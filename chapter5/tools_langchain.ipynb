{"cells": [{"cell_type": "markdown", "id": "XoS_YXhsRGQD", "metadata": {"id": "XoS_YXhsRGQD"}, "source": ["# Tools on LangChain"]}, {"cell_type": "code", "execution_count": 1, "id": "58fd7f64-0683-45d1-8a92-a86c5548c98c", "metadata": {}, "outputs": [], "source": ["# Cell for loading my environment variables from config.py,\n", "# which is located one directory above this notebook.\n", "import sys\n", "import os\n", "\n", "current_dir = os.getcwd()\n", "parent_dir = os.path.abspath(os.path.join(current_dir, '..'))\n", "sys.path.insert(0, parent_dir)\n", "\n", "from config import set_environment\n", "set_environment()"]}, {"cell_type": "code", "execution_count": 2, "id": "sNjI6aGKBzAJ0aIcO0YDYpWs", "metadata": {"id": "sNjI6aGKBzAJ0aIcO0YDYpWs", "tags": []}, "outputs": [], "source": ["# from langchain_google_vertexai import ChatVertexAI\n", "from langchain_openai import ChatOpenAI\n", "\n", "# llm = ChatVertexAI(model=\"gemini-2.0-flash-001\")\n", "llm = ChatOpenAI()\n", "question = \"how old is the US president?\""]}, {"cell_type": "markdown", "id": "d163ea1d", "metadata": {}, "source": ["Modern LLMs hide the need to construct a prompt from the user, you can define your tools as schemas instead and pass them as a separate argument:"]}, {"cell_type": "code", "execution_count": 3, "id": "JzrgWDIKy3Y2", "metadata": {"id": "JzrgWDIKy3Y2"}, "outputs": [], "source": ["search_tool = {\n", "    \"type\": \"function\",\n", "    \"function\": {\n", "        \"name\": \"google_search\",\n", "        \"description\": \"Returns about common facts, fresh events and news from Google Search engine based on a query.\",\n", "        \"parameters\": {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"query\": {\n", "                    \"type\": \"string\",\n", "                    \"title\": \"search_query\",\n", "                    \"description\": \"Search query to be sent to the search engine\"\n", "                }\n", "            },\n", "            \"required\": [\"query\"]\n", "        }\n", "    }\n", "}\n", "step1 = llm.invoke(question, tools=[search_tool])"]}, {"cell_type": "markdown", "id": "24518efd", "metadata": {}, "source": ["As we can see, now our outputs contains a special part called `tool_calls` - again, there's no need for us to parse the output any more, it's all done by an LLM:"]}, {"cell_type": "code", "execution_count": 4, "id": "-pvtbxJDSLvv", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 2, "status": "ok", "timestamp": 1738142152475, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "-pvtbxJDSLvv", "outputId": "8c7d65df-c333-4faa-9ee0-6063d607701d"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'name': 'google_search', 'args': {'query': 'current age of the US president'}, 'id': 'call_fNKQMstCvpceI7LNORZT4BLw', 'type': 'tool_call'}]\n"]}], "source": ["print(step1.tool_calls)"]}, {"cell_type": "markdown", "id": "63c4645a", "metadata": {}, "source": ["We can pass the tool calling result back to an LLM as a special `ToolMessage` message:"]}, {"cell_type": "code", "execution_count": 5, "id": "c3J3M4s3SQdm", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 927, "status": "ok", "timestamp": 1738142246299, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "c3J3M4s3SQdm", "outputId": "ae26ab65-b059-41fb-a091-7f23101fbd2a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The current age of the US President, <PERSON>, is 78 years old. He was born on June 14, 1946.\n"]}], "source": ["from langchain_core.messages import HumanMessage, ToolMessage\n", "\n", "tool_result = ToolMessage(content=\"<PERSON> › Age 78 years June 14, 1946\\n\", tool_call_id=step1.tool_calls[0][\"id\"])\n", "step2 = llm.invoke(\n", "    [HumanMessage(content=question), step1, tool_result],\n", "    tools=[search_tool]\n", ")\n", "assert len(step2.tool_calls) == 0\n", "\n", "print(step2.content)"]}, {"cell_type": "markdown", "id": "5c54c82b", "metadata": {}, "source": ["For the convinience, we can also `bind` tools to an LLM so that they would be auto-added to arguments on every invocation:"]}, {"cell_type": "code", "execution_count": 6, "id": "4uThVOXvVfQk", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 2038, "status": "ok", "timestamp": 1738142853415, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "4uThVOXvVfQk", "outputId": "5161dfd1-8d02-43ea-b433-1a189dfc4df9"}, "outputs": [{"data": {"text/plain": ["AIMessage(content='', additional_kwargs={'tool_calls': [{'id': 'call_E5TPwdGtjw6Y3RxcJoyuJHz7', 'function': {'arguments': '{\"query\":\"current age of US president\"}', 'name': 'google_search'}, 'type': 'function'}], 'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 18, 'prompt_tokens': 80, 'total_tokens': 98, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-3.5-turbo-0125', 'system_fingerprint': None, 'id': 'chatcmpl-BvPDtWCo9VxXcLqNkhFOvt05aMQzr', 'service_tier': 'default', 'finish_reason': 'tool_calls', 'logprobs': None}, id='run--89d2f620-b048-4c7e-9a83-14d66ea93f26-0', tool_calls=[{'name': 'google_search', 'args': {'query': 'current age of US president'}, 'id': 'call_E5TPwdGtjw6Y3RxcJoyuJHz7', 'type': 'tool_call'}], usage_metadata={'input_tokens': 80, 'output_tokens': 18, 'total_tokens': 98, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}})"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["llm_with_tools = llm.bind(tools=[search_tool])\n", "llm_with_tools.invoke(question)"]}, {"cell_type": "code", "execution_count": null, "id": "f8dfae93-7484-4eed-b086-58ea9d5d0289", "metadata": {}, "outputs": [], "source": []}], "metadata": {"colab": {"name": "Chapter2.5", "provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}