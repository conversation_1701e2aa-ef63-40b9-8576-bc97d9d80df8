{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1dfb0255-63ac-4f97-8625-6d05e3f4c52a", "metadata": {}, "outputs": [], "source": ["# Cell for loading environment variables from config.py,\n", "# which is located one directory above this notebook.\n", "import sys\n", "import os\n", "\n", "current_dir = os.getcwd()\n", "parent_dir = os.path.abspath(os.path.join(current_dir, '..'))\n", "sys.path.insert(0, parent_dir)\n", "\n", "from config import set_environment\n", "set_environment()"]}, {"cell_type": "code", "execution_count": 2, "id": "sNjI6aGKBzAJ0aIcO0YDYpWs", "metadata": {"id": "sNjI6aGKBzAJ0aIcO0YDYpWs", "tags": []}, "outputs": [], "source": ["from langchain_google_vertexai import ChatVertexAI\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langchain_openai import ChatOpenAI\n", "from langchain_core.prompts import PromptTemplate"]}, {"cell_type": "code", "execution_count": 3, "id": "93fc2830-cb96-4256-9487-160557f53cef", "metadata": {}, "outputs": [], "source": ["# llm = ChatVertexAI(model=\"gemini-2.0-flash-001\")\n", "# llm = ChatGoogleGenerativeAI(model=\"gemini-2.0-flash-001\")\n", "llm = ChatOpenAI(model=\"gpt-4\")"]}, {"cell_type": "markdown", "id": "1GKNegsPzgCs", "metadata": {"id": "1GKNegsPzgCs"}, "source": ["# ReACT example"]}, {"cell_type": "markdown", "id": "edec533f", "metadata": {}, "source": ["Let's deep dive into the ReACT pattern and implement it ourselves. We will mock tools as simple Python functions (without actual implementation) for now:"]}, {"cell_type": "code", "execution_count": 4, "id": "_v7ovZSy2JBD", "metadata": {"id": "_v7ovZSy2JBD"}, "outputs": [], "source": ["import math\n", "\n", "def mocked_google_search(query: str) -> str:\n", "  print(f\"CALLED GOOGLE_SEARCH with query={query}\")\n", "  return \"<PERSON> is a president of USA and he's 78 years old\"\n", "\n", "def mocked_calculator(expression: str) -> float:\n", "  print(f\"CALLED CALCULATOR with expression={expression}\")\n", "  if \"sqrt\" in expression:\n", "    return math.sqrt(78*132)\n", "  return 78*132"]}, {"cell_type": "markdown", "id": "71c9c2a0", "metadata": {}, "source": ["Let's define schemas for our tools and pass them to an LLM:"]}, {"cell_type": "code", "execution_count": 5, "id": "iK3DLt6tA6e0", "metadata": {"id": "iK3DLt6tA6e0"}, "outputs": [], "source": ["from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder\n", "\n", "calculator_tool = {\n", "    \"type\": \"function\",\n", "    \"function\": {\n", "        \"name\": \"calculator\",\n", "        \"description\": \"Computes mathematical expressions\",\n", "        \"parameters\": {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"expression\": {\n", "                    \"type\": \"string\",\n", "                    \"title\": \"expression\",\n", "                    \"description\": \"A mathematical expression to be evaluated by a calculator\"\n", "                }\n", "            },\n", "            \"required\": [\"expression\"]\n", "        }\n", "    }\n", "}\n", "\n", "search_tool = {\n", "    \"type\": \"function\",\n", "    \"function\": {\n", "        \"name\": \"google_search\",\n", "        \"description\": \"Returns about common facts, fresh events and news from Google Search engine based on a query.\",\n", "        \"parameters\": {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"query\": {\n", "                    \"type\": \"string\",\n", "                    \"title\": \"search_query\",\n", "                    \"description\": \"Search query to be sent to the search engine\"\n", "                }\n", "            },\n", "            \"required\": [\"query\"]\n", "        }\n", "    }\n", "}\n", "\n", "system_prompt = (\n", "    \"Always use a calculator for mathematical computations, and use Google Search \"\n", "    \"for information about common facts, fresh events and news. Do not assume anything, keep in \"\n", "    \"mind that things are changing and always \"\n", "    \"check yourself with external sources if possible.\"\n", ")\n", "\n", "prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", system_prompt),\n", "    MessagesPlaceholder(variable_name=\"messages\"),\n", "])"]}, {"cell_type": "code", "execution_count": 6, "id": "0aef9bcf-bd0e-4786-8dc3-330d78ae0b62", "metadata": {}, "outputs": [], "source": ["llm_with_tools = prompt | llm.bind_tools([search_tool, calculator_tool])"]}, {"cell_type": "markdown", "id": "361caf3e", "metadata": {}, "source": ["Now it's time to define the LangGraph workflow itself."]}, {"cell_type": "code", "execution_count": 7, "id": "Cy95GSiLzhiA", "metadata": {"id": "Cy95GSiLzhiA"}, "outputs": [], "source": ["from langchain_core.messages import HumanMessage, ToolMessage\n", "from langgraph.graph import MessagesState, StateGraph, START, END\n", "\n", "\n", "def invoke_llm(state: MessagesState):\n", "    return {\"messages\": [llm_with_tools.invoke(state[\"messages\"])]}\n", "\n", "\n", "def call_tools(state: MessagesState):\n", "    last_message = state[\"messages\"][-1]\n", "    tool_calls = last_message.tool_calls\n", "\n", "    new_messages = []\n", "\n", "    for tool_call in tool_calls:\n", "      if tool_call[\"name\"] == \"google_search\":\n", "        tool_result = mocked_google_search(**tool_call[\"args\"])\n", "        new_messages.append(ToolMessage(content=tool_result, tool_call_id=tool_call[\"id\"]))\n", "      elif tool_call[\"name\"] == \"calculator\":\n", "        tool_result = mocked_calculator(**tool_call[\"args\"])\n", "        new_messages.append(ToolMessage(content=tool_result, tool_call_id=tool_call[\"id\"]))\n", "      else:\n", "        raise ValueError(f\"Tool {tool_call['name']} is not defined!\")\n", "    return {\"messages\": new_messages}\n", "\n", "\n", "def should_run_tools(state: MessagesState):\n", "    last_message = state[\"messages\"][-1]\n", "    if last_message.tool_calls:\n", "      return \"call_tools\"\n", "    return END"]}, {"cell_type": "markdown", "id": "758fc223", "metadata": {}, "source": ["And we can put everything together as following:"]}, {"cell_type": "code", "execution_count": 8, "id": "TQhNe5Oi1iJ8", "metadata": {"id": "TQhNe5Oi1iJ8"}, "outputs": [], "source": ["builder = StateGraph(MessagesState)\n", "builder.add_node(\"invoke_llm\", invoke_llm)\n", "builder.add_node(\"call_tools\", call_tools)\n", "\n", "builder.add_edge(START, \"invoke_llm\")\n", "builder.add_conditional_edges(\"invoke_llm\", should_run_tools)\n", "builder.add_edge(\"call_tools\", \"invoke_llm\")\n", "graph = builder.compile()"]}, {"cell_type": "code", "execution_count": 9, "id": "NBpFQ8-I2-xU", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 2933, "status": "ok", "timestamp": 1738242542414, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "NBpFQ8-I2-xU", "outputId": "bb4cae74-84e2-4168-ea0f-3edee8201f3f"}, "outputs": [], "source": ["question = \"What is a square root of the current US president’s age multiplied by 132?\""]}, {"cell_type": "code", "execution_count": 10, "id": "ff37618f-12b4-4368-b60f-2b2345fa4f6b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CALLED GOOGLE_SEARCH with query=current US president age\n", "CALLED CALCULATOR with expression=sqrt(78*132)\n", "The square root of the current US president's age multiplied by 132 is approximately 101.47.\n"]}], "source": ["result = graph.invoke({\"messages\": [HumanMessage(content=question)]})\n", "print(result[\"messages\"][-1].content)"]}, {"cell_type": "markdown", "id": "3af963e9", "metadata": {}, "source": ["Now as you understand how it works under the hood, we can share good news with you. There's no need to implement it yourselves, you can use an customizable pre-built agent from LangGraph:"]}, {"cell_type": "code", "execution_count": 11, "id": "aHvnnNh_DwNP", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 306, "status": "ok", "timestamp": 1738238072999, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "aHvnnNh_DwNP", "outputId": "6131fe45-da06-4b92-b82a-8b3e0189b4e4"}, "outputs": [], "source": ["from langgraph.prebuilt import create_react_agent\n", "\n", "agent = create_react_agent(model=llm, tools=[search_tool, calculator_tool], prompt=system_prompt)"]}, {"cell_type": "code", "execution_count": null, "id": "fe9e558e-ff88-479f-a2a9-fb0fb6a145a4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"colab": {"name": "Chapter2.5", "provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}