{"cells": [{"cell_type": "markdown", "id": "74G6Ag42wXLU", "metadata": {"id": "74G6Ag42wXLU"}, "source": ["# Plan and solve agent"]}, {"cell_type": "markdown", "id": "61edf64c", "metadata": {"id": "61edf64c"}, "source": ["We'll begin with the planner functionality, and define the data structure and the corresponding instructions:"]}, {"cell_type": "code", "execution_count": 1, "id": "x8iszKXxwdLs", "metadata": {"id": "x8iszKXxwdLs", "executionInfo": {"status": "ok", "timestamp": 1741759001993, "user_tz": -60, "elapsed": 6192, "user": {"displayName": "", "userId": ""}}}, "outputs": [], "source": ["from pydantic import BaseModel, Field\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_google_vertexai import ChatVertexAI\n", "\n", "\n", "class Plan(BaseModel):\n", "    \"\"\"Plan to follow in future\"\"\"\n", "\n", "    steps: list[str] = Field(\n", "        description=\"different steps to follow, should be in sorted order\"\n", "    )\n", "\n", "\n", "system_prompt_template = (\n", "    \"For the given task, come up with a step by step plan.\\n\"\n", "    \"This plan should involve individual tasks, that if executed correctly will \"\n", "    \"yield the correct answer. Do not add any superfluous steps.\\n\"\n", "    \"The result of the final step should be the final answer. Make sure that each \"\n", "    \"step has all the information needed - do not skip steps.\"\n", ")\n", "planner_prompt = ChatPromptTemplate.from_messages(\n", "    [(\"system\", system_prompt_template),\n", "     (\"user\", \"Prepare a plan how to solve the following task:\\n{task}\\n\")])\n", "\n", "planner = planner_prompt | ChatVertexAI(\n", "    model_name=\"gemini-2.0-flash\", temperature=1.0\n", ").with_structured_output(Plan)"]}, {"cell_type": "markdown", "id": "14b74e0c", "metadata": {"id": "14b74e0c"}, "source": ["We will re-use tools we built in previous sections:"]}, {"cell_type": "code", "execution_count": 2, "id": "opTIbjUP7rSe", "metadata": {"id": "opTIbjUP7rSe", "executionInfo": {"status": "ok", "timestamp": 1741759001993, "user_tz": -60, "elapsed": 3, "user": {"displayName": "", "userId": ""}}}, "outputs": [], "source": ["from pydantic import BaseModel, Field\n", "from langchain_core.runnables import RunnableLambda, RunnableConfig\n", "from langchain_core.tools import tool, convert_runnable_to_tool\n", "\n", "\n", "class CalculatorArgs(BaseModel):\n", "    expression: str = Field(description=\"Mathematical expression to be evaluated\")\n", "\n", "def calculator(state: CalculatorArgs, config: RunnableConfig) -> str:\n", "    expression = state[\"expression\"]\n", "    math_constants = config[\"configurable\"].get(\"math_constants\", {})\n", "    result = ne.evaluate(expression.strip(), local_dict=math_constants)\n", "    return str(result)\n", "\n", "\n", "calculator_with_retry = RunnableLambda(calculator).with_retry(\n", "    wait_exponential_jitter=True,\n", "    stop_after_attempt=3,\n", ")\n", "\n", "calculator_tool = convert_runnable_to_tool(\n", "    calculator_with_retry,\n", "    name=\"calculator\",\n", "    description=(\n", "        \"Calculates a single mathematical expression, incl. complex numbers.\"\n", "        \"'\\nAlways add * to operations, examples:\\n73i -> 73*i\\n\"\n", "        \"7pi**2 -> 7*pi**2\"\n", "    ),\n", "    args_schema=CalculatorArgs,\n", "    arg_types={\"expression\": \"str\"},\n", ")"]}, {"cell_type": "code", "execution_count": 3, "id": "OILpC_PH7rU3", "metadata": {"id": "OILpC_PH7rU3", "executionInfo": {"status": "ok", "timestamp": 1741759003590, "user_tz": -60, "elapsed": 1600, "user": {"displayName": "", "userId": ""}}}, "outputs": [], "source": ["from langchain.agents import load_tools\n", "\n", "llm = ChatVertexAI(model=\"gemini-2.0-flash\")\n", "tools = load_tools(\n", "  tool_names=[\"ddg-search\", \"arxiv\", \"wikipedia\"],\n", "  llm=llm\n", ")"]}, {"cell_type": "markdown", "id": "b0025c8a", "metadata": {"id": "b0025c8a"}, "source": ["We will use these tools with a ReACT agent:"]}, {"cell_type": "code", "execution_count": 5, "id": "_9SNZrie8BPW", "metadata": {"id": "_9SNZrie8BPW", "executionInfo": {"status": "ok", "timestamp": 1741759091560, "user_tz": -60, "elapsed": 306, "user": {"displayName": "", "userId": ""}}}, "outputs": [], "source": ["from langgraph.prebuilt import create_react_agent\n", "from langgraph.prebuilt.chat_agent_executor import AgentState\n", "\n", "\n", "system_prompt = (\n", "    \"You're a smart assistant that carefully helps to solve complex tasks.\\n\"\n", "    \" Given a general plan to solve a task and a specific step, work on this step. \"\n", "    \" Don't assume anything, keep in minds things might change and always try to \"\n", "    \"use tools to double-check yourself.\\m\"\n", "    \" Use a calculator for mathematical computations, use Search to gather\"\n", "    \"for information about common facts, fresh events and news, use Arxiv to get \"\n", "    \"ideas on recent research and use Wikipedia for common knowledge.\"\n", ")\n", "\n", "step_template = (\n", "    \"Given the task and the plan, try to execute on a specific step of the plan.\\n\"\n", "    \"TASK:\\n{task}\\n\\nPLAN:\\n{plan}\\n\\nSTEP TO EXECUTE:\\n{step}\\n\"\n", ")\n", "\n", "prompt_template = ChatPromptTemplate.from_messages([\n", "    (\"system\", system_prompt),\n", "    (\"user\", step_template),\n", "])\n", "\n", "class StepState(AgentState):\n", "  plan: str\n", "  step: str\n", "  task: str\n", "\n", "execution_agent = create_react_agent(model=llm, tools=tools+[calculator_tool], state_schema=StepState, prompt=prompt_template)"]}, {"cell_type": "markdown", "id": "0efac21d", "metadata": {"id": "0efac21d"}, "source": ["Now it's time to define our agent's state:"]}, {"cell_type": "code", "execution_count": 6, "id": "BTF_oSu9345K", "metadata": {"id": "BTF_oSu9345K", "executionInfo": {"status": "ok", "timestamp": 1741759094235, "user_tz": -60, "elapsed": 308, "user": {"displayName": "", "userId": ""}}}, "outputs": [], "source": ["from typing import Annotated, TypedDict\n", "import operator\n", "\n", "\n", "task = \"Write a strategic one-pager of building an AI startup?\""]}, {"cell_type": "code", "execution_count": 7, "id": "Cxy7Qlyyxe6m", "metadata": {"id": "Cxy7Qlyyxe6m", "executionInfo": {"status": "ok", "timestamp": 1741759095478, "user_tz": -60, "elapsed": 2, "user": {"displayName": "", "userId": ""}}}, "outputs": [], "source": ["class PlanState(TypedDict):\n", "    task: str\n", "    plan: Plan\n", "    past_steps: Annotated[list[str], operator.add]\n", "    final_response: str\n", "\n", "\n", "def get_current_step(state: PlanState) -> int:\n", "  \"\"\"Returns the number of current step to be executed.\"\"\"\n", "  return len(state.get(\"past_steps\", []))\n", "\n", "def get_full_plan(state: PlanState) -> str:\n", "  \"\"\"Returns formatted plan with step numbers and past results.\"\"\"\n", "  full_plan = []\n", "  for i, step in enumerate(state[\"plan\"].steps):\n", "    full_step = f\"# {i+1}. Planned step: {step}\\n\"\n", "    if i < get_current_step(state):\n", "      full_step += f\"Result: {state['past_steps'][i]}\\n\"\n", "    full_plan.append(full_step)\n", "  return \"\\n\".join(full_plan)"]}, {"cell_type": "code", "execution_count": 8, "id": "HIZBG58K4XoN", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 449}, "executionInfo": {"elapsed": 1041, "status": "ok", "timestamp": 1741759138467, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "HIZBG58K4XoN", "outputId": "ff2835c1-98a6-4cc2-dcaf-d92df6604d4a"}, "outputs": [{"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}}], "source": ["from typing import Literal\n", "from langchain_core.prompts import PromptTemplate\n", "from langgraph.graph import StateGraph, START, END\n", "\n", "\n", "final_prompt = PromptTemplate.from_template(\n", "    \"You're a helpful assistant that has executed on a plan.\"\n", "    \"Given the results of the execution, prepare the final response.\\n\"\n", "    \"Don't assume anything\\nTASK:\\n{task}\\n\\nPLAN WITH RESUlTS:\\n{plan}\\n\"\n", "    \"FINAL RESPONSE:\\n\"\n", ")\n", "\n", "async def _build_initial_plan(state: PlanState) -> PlanState:\n", "  plan = await planner.ainvoke(state[\"task\"])\n", "  return {\"plan\": plan}\n", "\n", "async def _run_step(state: PlanState) -> PlanState:\n", "  plan = state[\"plan\"]\n", "  current_step = get_current_step(state)\n", "  step = await execution_agent.ainvoke({\"plan\": get_full_plan(state), \"step\": plan.steps[current_step], \"task\": state[\"task\"]})\n", "  return {\"past_steps\": [step[\"messages\"][-1].content]}\n", "\n", "async def _get_final_response(state: PlanState) -> PlanState:\n", "  final_response = await (final_prompt | llm).ainvoke({\"task\": state[\"task\"], \"plan\": get_full_plan(state)})\n", "  return {\"final_response\": final_response}\n", "\n", "\n", "def _should_continue(state: PlanState) -> Literal[\"run\", \"response\"]:\n", "  if get_current_step(state) < len(state[\"plan\"].steps):\n", "    return \"run\"\n", "  return \"response\"\n", "\n", "builder = StateGraph(PlanState)\n", "builder.add_node(\"initial_plan\", _build_initial_plan)\n", "builder.add_node(\"run\", _run_step)\n", "builder.add_node(\"response\", _get_final_response)\n", "\n", "builder.add_edge(START, \"initial_plan\")\n", "builder.add_edge(\"initial_plan\", \"run\")\n", "builder.add_conditional_edges(\"run\", _should_continue)\n", "builder.add_edge(\"response\", END)\n", "\n", "graph = builder.compile()\n", "\n", "from IPython.display import Image, display\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "source": ["Now it's time to run it!"], "metadata": {"id": "Lt8Zqefn9RT3"}, "id": "Lt8Zqefn9RT3"}, {"cell_type": "code", "execution_count": 9, "id": "W4UYAM62_iUR", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 119744, "status": "ok", "timestamp": 1741759260634, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "W4UYAM62_iUR", "outputId": "3e3c993e-a774-436e-a977-5c590804f46b"}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["WARNING:langchain_google_vertexai.chat_models:This model can reply with multiple function calls in one response. Please don't rely on `additional_kwargs.function_call` as only the last one will be saved.Use `tool_calls` instead.\n", "WARNING:langchain_google_vertexai.chat_models:This model can reply with multiple function calls in one response. Please don't rely on `additional_kwargs.function_call` as only the last one will be saved.Use `tool_calls` instead.\n", "WARNING:langchain_google_vertexai.chat_models:This model can reply with multiple function calls in one response. Please don't rely on `additional_kwargs.function_call` as only the last one will be saved.Use `tool_calls` instead.\n"]}], "source": ["task = \"Write a strategic one-pager of building an AI startup\"\n", "result = await graph.ainvoke({\"task\": task})"]}, {"cell_type": "code", "source": ["print(result[\"final_response\"].content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "tGTuCMEw9AYA", "executionInfo": {"status": "ok", "timestamp": 1741759479906, "user_tz": -60, "elapsed": 341, "user": {"displayName": "", "userId": ""}}, "outputId": "437f708b-3c97-487f-a35e-6ab498532921"}, "id": "tGTuCMEw9AYA", "execution_count": 13, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["## Strategic One-Pager: AI Startup for Accurate Content Generation\n", "\n", "**Problem:** Current language models hallucinate and contradict, undermining trust in AI-generated content for critical applications like research, journalism, and legal work.\n", "\n", "**Solution:** An AI-powered language model with real-time fact-checking and knowledge grounding, ensuring accuracy and consistency.\n", "\n", "**Target Audience:** Researchers, journalists, legal professionals, content creators, and organizations needing reliable information.\n", "\n", "**Value Proposition:** Increased trust, improved decision-making, reduced misinformation, and enhanced productivity through accurate AI-generated content.\n", "\n", "**Market Analysis:**\n", "\n", "* **Competition:** Existing LLMs (GPT-3, Bard) lack robust fact-checking. Fact-checking tools exist but aren't seamlessly integrated. Niche AI writing tools address specific needs but not comprehensive accuracy.\n", "* **Market Size:** The market for AI language models is substantial and growing rapidly (further research required to quantify). Demand for reliable AI-generated content is increasing across sectors.\n", "* **Customer Needs:** Researchers need accurate information and source citation. Journalists need credible, unbiased reporting tools. Legal professionals need accurate document generation. Content creators need reliable, plagiarism-free content.\n", "\n", "**Technology & IP:**\n", "\n", "* **Technology:** Transformer-based architecture with real-time fact-checking APIs, knowledge grounding, and reasoning capabilities.\n", "* **Data Sources:** Reputable sources like Wikipedia, journals, and news feeds.\n", "* **IP:** Proprietary algorithms for fact-checking, knowledge grounding, and reasoning. Unique data processing techniques.\n", "* **Competitive Advantages:** Enhanced accuracy, real-time fact-checking, advanced reasoning.\n", "* **Scalability:** Cloud-based infrastructure and modular design.\n", "\n", "**Business Model:**\n", "\n", "* **Revenue:** Tiered subscriptions, API access, premium features, and enterprise solutions.\n", "* **Pricing:** Tiered subscriptions, usage-based API pricing, value-based pricing for enterprise.\n", "* **Partnerships:** Data providers, technology companies, industry experts, marketing agencies.\n", "* **Customer Acquisition:** Content marketing, online advertising, free trials, community building.\n", "* **Financial Projections:**  (Detailed financial projections to be included in full business plan).\n", "\n", "**Team & Advisors:**\n", "\n", "* **[Your Name/CEO]:** Expertise in [Your Area of Expertise]. Focus: Strategy, vision, fundraising.\n", "* **[Co-founder/CTO]:** PhD in [Relevant Field]. Focus: Technology, algorithms.\n", "* **[Co-founder/Head of Business Development]:** MBA. Focus: Market analysis, customer acquisition.\n", "* **[Key Team Member/Lead AI Engineer]:** <PERSON>.Sc. in [Relevant Field]. Focus: Algorithm development.\n", "* **Advisors:** Experts in AI ethics, finance, and the target industry.\n", "\n", "**Funding Request:**\n", "\n", "* **Amount:** $5 million\n", "* **Use of Funds:** Team expansion (40%), infrastructure (30%), marketing (20%), working capital (10%).\n", "* **ROI:** Projected [Multiple]x return within [Timeframe] based on market growth and revenue projections.  Exit via acquisition or IPO.\n", "\n", "**Milestones & Roadmap:**\n", "\n", "* **Phase 1 (6 months):** MVP development with core features and private beta launch.\n", "* **Phase 2 (12 months):** Public beta, tiered subscriptions, API access, platform integrations, Series A funding.\n", "* **Phase 3 (18 months):** Advanced features, enterprise solutions, international expansion.\n", "* **Exit (36+ months):** Acquisition or IPO.\n", "\n", "**Exit Strategy:**\n", "\n", "* **Vision:** Become the leading provider of accurate and reliable AI-generated content.\n", "* **Exit:** Acquisition by a major technology company or IPO.\n", "\n", "\n", "This one-pager provides a concise overview of the AI startup, highlighting the problem, solution, market, technology, business model, team, funding needs, and roadmap.  A detailed business plan will expand on these points with in-depth analysis, financial projections, and supporting data.\n", "\n"]}]}, {"cell_type": "markdown", "source": ["We can also explore detailed steps:"], "metadata": {"id": "a0Tc-bMG9Mqd"}, "id": "a0Tc-bMG9Mqd"}, {"cell_type": "code", "execution_count": 14, "id": "lB-WdlFo_A12", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 139380, "status": "ok", "timestamp": 1741759624311, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "lB-WdlFo_A12", "outputId": "f7c4d2bb-9303-4719-9bec-a9390ca99ba8"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Output from node 'initial_plan':\n", "---\n", "{'plan': Plan(steps=['1. Define the problem and solution: Clearly articulate the problem your AI startup aims to solve and how your AI-powered solution addresses it. Be specific about the target market and the value proposition.', '2. Technology and IP: Describe your core AI technology, including algorithms, datasets, and any intellectual property (IP) you possess. Highlight its unique advantages and defensibility.', '3. Business model: Outline your revenue generation strategy, pricing model, and target customer segments. Explain how your AI solution creates value for customers and how you will capture that value.', \"4. Team and expertise: Showcase your team's expertise in AI, business development, and other relevant areas. Emphasize the experience and skills that make your team capable of executing the plan.\", '5. Market analysis: Analyze the target market size, competition, and potential barriers to entry. Provide data-driven insights to support your market assessment.', '6. Go-to-market strategy: Detail your plan for reaching target customers, including marketing channels, sales strategies, and partnership opportunities.', '7. Financial projections: Present realistic financial projections for the next 3-5 years, including revenue forecasts, cost estimates, and key performance indicators (KPIs).', '8. Funding request (if applicable): If seeking funding, clearly state the amount of funding needed, how it will be used, and the expected return on investment (ROI).', '9. Milestones and timelines: Set clear milestones and timelines for achieving key objectives. This demonstrates a well-structured plan and a commitment to execution.', '10. Exit strategy: Briefly describe your long-term vision for the startup and potential exit strategies, such as acquisition or initial public offering (IPO).'])}\n", "\n", "---\n", "\n", "Output from node 'run':\n", "---\n", "{'past_steps': ['Problem: Many companies, particularly small and medium-sized businesses (SMBs), struggle with efficient and personalized customer interactions.  They lack the resources and tools to provide 24/7 customer support, personalized recommendations, and proactive customer engagement, leading to lost sales and decreased customer satisfaction.  Additionally, current customer service solutions often involve complex and expensive software that requires extensive training and integration.\\n\\nSolution: An AI-powered customer interaction platform designed specifically for SMBs. This platform offers:\\n* **24/7 AI-powered chatbot:** Handles common customer inquiries, provides instant support, and gathers valuable customer data.\\n* **Personalized product recommendations:** Analyzes customer behavior and preferences to suggest relevant products, increasing sales conversion rates.\\n* **Proactive customer engagement:**  Identifies at-risk customers and proactively offers solutions, preventing churn and fostering loyalty.\\n* **Sentiment analysis:**  Tracks customer sentiment across different channels, enabling businesses to understand customer feedback and improve their offerings.\\n\\nTarget Market: Small and medium-sized businesses (SMBs) across various industries, particularly e-commerce, retail, and SaaS companies.  These businesses often lack the resources to implement complex customer service solutions but have a strong need for efficient and personalized customer interactions.\\n\\nValue Proposition: Our platform offers an affordable, easy-to-use, and scalable solution that empowers SMBs to enhance customer experience, increase sales, and improve customer retention. By automating routine tasks, providing personalized interactions, and offering proactive support, our AI-powered platform frees up valuable time and resources for business owners to focus on growth and innovation.\\n']}\n", "\n", "---\n", "\n", "Output from node 'run':\n", "---\n", "{'past_steps': [\"Core AI Technology and IP:\\n\\nOur AI-powered customer interaction platform leverages a combination of cutting-edge technologies to deliver a seamless and personalized experience for SMBs:\\n\\n* **Natural Language Processing (NLP):** We utilize advanced NLP models, including transformer networks, for accurate intent recognition, sentiment analysis, and entity extraction from customer conversations.  These models are trained on a vast dataset of customer service interactions and are continually refined through reinforcement learning techniques.  Our proprietary NLP pipeline allows us to understand the nuances of human language, enabling more natural and effective communication between the chatbot and customers.  We're exploring techniques like zero-shot and few-shot learning to adapt quickly to new industry-specific terminology and customer needs.  Furthermore, we are researching methods to incorporate multilingual support to cater to a broader customer base.  This focus on continuous improvement and adaptability sets us apart from competitors who rely on static, pre-trained models.\\n* **Machine Learning (ML) for Personalization:** Our personalization engine employs a combination of collaborative filtering and content-based filtering algorithms to recommend relevant products and services to customers.  We leverage customer purchase history, browsing behavior, demographics, and real-time interactions with the platform to generate personalized recommendations.  Our algorithms are constantly learning and adapting to changes in customer preferences, ensuring the accuracy and relevance of recommendations over time. The engine is designed to be flexible and can integrate with various SMB platforms and data sources, providing a seamless personalization experience regardless of the underlying infrastructure.\\n* **Deep Learning for Sentiment Analysis:** We utilize deep learning models, specifically recurrent neural networks (RNNs) and convolutional neural networks (CNNs), for sentiment analysis. This allows us to accurately gauge customer sentiment from text and voice interactions, providing valuable insights into customer satisfaction and potential issues. The model is trained on a diverse dataset of customer reviews, social media posts, and customer service transcripts to ensure robustness and accuracy. Our system can identify subtle shifts in sentiment, enabling businesses to proactively address customer concerns and prevent negative experiences from escalating.\\n* **Data Security and Privacy:** Data security and user privacy are paramount. We adhere to strict data privacy regulations and utilize robust security measures to protect sensitive customer information.  Our platform is designed with privacy by design principles, ensuring that customer data is anonymized and securely stored.  We are committed to transparent data handling practices and provide users with full control over their data.\\n\\nIntellectual Property (IP):\\n\\nWe are in the process of filing patents for our novel NLP pipeline, personalized recommendation engine, and sentiment analysis algorithms. These patents will protect our core technology and provide a significant competitive advantage. Our unique combination of NLP, ML, and deep learning, trained on a proprietary dataset, creates a defensible IP portfolio. We are also exploring partnerships with research institutions to further enhance our AI capabilities and expand our IP portfolio.\\n\\nUnique Advantages and Defensibility:\\n\\n* **Proprietary Algorithms and Data:** Our unique combination of in-house developed algorithms, trained on a proprietary dataset of customer service interactions, provides significant accuracy and performance advantages.\\n* **Focus on SMBs:**  Our platform is specifically designed for the needs and budget constraints of SMBs, a market often overlooked by larger AI solutions providers.\\n* **Scalability and Flexibility:** Our cloud-based architecture ensures scalability and flexibility, allowing businesses to easily integrate the platform into their existing workflows.\\n* **Continuous Improvement and Innovation:** We are committed to ongoing research and development, constantly improving our AI models and adding new features to stay ahead of the competition.  This dedication to innovation ensures our platform remains cutting-edge and continues to deliver value to our customers.\\n\"]}\n", "\n", "---\n", "\n", "Output from node 'run':\n", "---\n", "{'past_steps': ['Business Model:\\n\\nRevenue Generation Strategy:\\n\\nOur revenue generation strategy is based on a tiered subscription model, offering different levels of service and features to cater to the varying needs and budgets of SMBs.  We will also explore potential upselling opportunities for premium features and add-ons, such as advanced analytics and custom integrations.\\n\\nPricing Model:\\n\\n* **Basic Plan:**  This entry-level plan offers core features like the AI-powered chatbot for basic customer support, limited personalized product recommendations, and basic sentiment analysis.  Priced competitively to attract smaller businesses and startups.\\n* **Premium Plan:**  This mid-tier plan provides enhanced features, including advanced chatbot customization, more comprehensive personalized recommendations, proactive customer engagement capabilities, and detailed sentiment analysis reports.  Targeted towards growing businesses with higher customer interaction volumes.\\n* **Enterprise Plan:**  This top-tier plan offers a fully customized solution with dedicated support, advanced analytics, custom integrations, and premium features tailored to the specific needs of larger SMBs.\\n\\nTarget Customer Segments:\\n\\n* **E-commerce Businesses:** Online retailers can leverage our platform to provide 24/7 customer support, personalized product recommendations, and proactive customer engagement, leading to increased sales conversion rates and customer lifetime value.\\n* **Retail Businesses:** Brick-and-mortar stores can use the platform to enhance in-store and online customer experiences, offer personalized promotions, and gather valuable customer feedback.\\n* **SaaS Companies:**  Software-as-a-service providers can integrate our platform to improve customer onboarding, provide personalized support, and reduce churn.\\n\\nValue Creation and Capture:\\n\\nOur AI solution creates value for customers by:\\n\\n* **Enhancing Customer Experience:**  24/7 support, personalized recommendations, and proactive engagement create a seamless and positive customer experience.\\n* **Increasing Sales:** Personalized recommendations and targeted promotions drive sales conversion rates and increase revenue.\\n* **Improving Customer Retention:** Proactive support and personalized interactions foster customer loyalty and reduce churn.\\n* **Streamlining Operations:**  Automating routine tasks frees up valuable time and resources for business owners.\\n* **Gaining Customer Insights:** Sentiment analysis and data analytics provide valuable insights into customer behavior and preferences.\\n\\n\\nWe capture this value through our tiered subscription model, ensuring that our pricing aligns with the value delivered to customers.  By offering different pricing tiers with varying features, we cater to a wide range of SMBs, maximizing market penetration and revenue potential.  Furthermore, by continuously improving our AI capabilities and adding new features, we enhance the value proposition of our platform, justifying premium pricing and increasing customer lifetime value.\\n']}\n", "\n", "---\n", "\n", "Output from node 'run':\n", "---\n", "{'past_steps': ['Team and Expertise:\\n\\nOur team comprises experienced professionals with a proven track record in AI, business development, and startup leadership.  We combine deep technical expertise with a strong understanding of the SMB market, enabling us to effectively develop and deliver our AI-powered solution.\\n\\n**Key Team Members:**\\n\\n* **[Founder Name & Title]:**  [Brief description of the founder\\'s experience and expertise in AI and entrepreneurship.  Quantify achievements where possible, e.g., \"Led the development of an AI-powered recommendation engine that increased sales by 20%,\" or \"Successfully launched and scaled two tech startups.\"]  Focus on relevant skills like strategic thinking, leadership, and technical expertise.  Mention any relevant publications or awards to establish credibility. Search for \"[Founder Name]\" to find more information and add it.\\n* **[Co-founder Name & Title]:** [Similar description of the co-founder\\'s background and expertise in business development, marketing, or sales.  Quantify achievements related to business growth and market penetration, e.g., \"Increased customer acquisition by 30% through targeted marketing campaigns,\" or \"Secured strategic partnerships with key industry players.\"]  Emphasize skills like market analysis, sales strategy, and customer relationship management.\\n* **[Lead AI Engineer Name & Title]:** [Describe the lead AI engineer\\'s technical expertise in areas relevant to the project, such as NLP, machine learning, and deep learning.  Mention specific projects and achievements that demonstrate their technical skills, e.g., \"Developed a state-of-the-art NLP model for sentiment analysis with 90% accuracy,\" or \"Published research on deep learning applications in customer service.\"] Include information about relevant publications, patents, or open-source contributions.\\n* **[Other Key Team Members and their expertise]:** Briefly describe other key team members and their relevant expertise, such as software engineering, data science, product management, or customer support.  Highlight skills and experience that contribute to the overall success of the project.\\n\\n\\n**Advisory Board (if applicable):**\\n\\nIf you have an advisory board, briefly list the members and their relevant expertise. This adds credibility and demonstrates access to valuable guidance and resources.\\n\\n\\n**Team\\'s Capabilities:**\\n\\nOur team\\'s combined skills and experience make us uniquely capable of executing our plan:\\n\\n* **Deep AI Expertise:** Our AI engineers possess advanced knowledge in NLP, machine learning, and deep learning, enabling us to develop cutting-edge AI solutions.\\n* **Business Acumen:** Our business development team has a proven track record in market analysis, sales strategy, and customer acquisition.\\n* **SMB Market Understanding:** We understand the specific needs and challenges of SMBs, allowing us to tailor our solutions effectively.\\n* **Agile Development:** We embrace agile methodologies, enabling us to adapt quickly to changing market conditions and customer feedback.\\n* **Passion and Commitment:** We are a team of highly motivated individuals with a shared passion for building innovative AI solutions that empower businesses.\\n\\nThis combination of technical expertise, business acumen, and market understanding positions us for success in the competitive AI landscape. We are confident in our ability to execute our plan, deliver exceptional value to our customers, and achieve our long-term vision.\\n']}\n", "\n", "---\n", "\n", "Output from node 'run':\n", "---\n", "{'past_steps': ['Market Analysis:\\n\\nTarget Market Size:\\n\\nTo estimate the target market size for our AI-powered customer interaction platform, focusing on SMBs in e-commerce, retail, and SaaS, we can use the following approach:\\n\\n1.  **Define SMBs:** We\\'ll use the Small Business Administration (SBA) definition of SMBs as a starting point. Note that definitions can vary across countries.  I\\'ll check Wikipedia and other sources for standard definitions of SMBs.\\n2.  **Industry Focus:**  We are targeting e-commerce, retail, and SaaS businesses. We need to determine the number of SMBs in each of these sectors.  We can use market research reports, industry associations, and government data to find these figures. Search and Wikipedia might be helpful here.\\n3.  **Geographic Focus:** Initially, we will target [Specify your target geographic area, e.g., the United States, North America, specific countries]. This will help refine our market size estimation.  We can use country-specific data from government sources and statistical databases.\\n4.  **Data Sources:** Potential data sources include:\\n    *   U.S. Census Bureau (if targeting the US)\\n    *   Statista\\n    *   IBISWorld\\n    *   Industry-specific reports and publications\\n    *   Wikipedia for general industry overviews\\n    *   Search for recent reports on SMB market size.\\n\\nExample Search Queries:\\n\\n*   \"number of SMBs in e-commerce United States\"\\n*   \"market size retail SMBs North America\"\\n*   \"SaaS SMB market share Europe\"\\n*   \"definition of small and medium-sized businesses\"\\n\\n\\nCompetition:\\n\\nWe need to identify and analyze our main competitors in the AI-powered customer interaction platform space.  We\\'ll consider both direct and indirect competitors.\\n\\n1.  **Direct Competitors:** Companies offering similar AI-powered solutions for SMB customer interaction.\\n2.  **Indirect Competitors:** Companies offering alternative solutions for customer service, such as traditional CRM software, helpdesk platforms, and live chat solutions.\\n\\nAnalysis will include:\\n\\n*   **Competitor Profiling:**  Understanding their strengths, weaknesses, pricing, target market, and value proposition. We can use their websites, marketing materials, and industry reports to gather this information.\\n*   **Competitive Differentiation:** Highlighting our unique advantages and how we differentiate ourselves from the competition.  This is crucial for positioning our product in the market.\\n*   **Search Queries:**\\n    *   \"AI customer interaction platform for SMBs\"\\n    *   \"competitors Intercom\" (if Intercom is considered a competitor)\\n    *   \"best AI chatbots for small business\"\\n\\n\\n\\nPotential Barriers to Entry:\\n\\nWe\\'ll explore potential barriers to entry for our startup, such as:\\n\\n*   **Technology Development:** The complexity of developing and maintaining advanced AI algorithms.\\n*   **Data Acquisition:** Access to high-quality data for training our AI models.\\n*   **Talent Acquisition:** Hiring and retaining skilled AI engineers and other technical professionals.\\n*   **Funding:** Securing sufficient funding to support research, development, and marketing efforts.\\n*   **Competition:** Establishing a foothold in a competitive market with established players.\\n*   **Regulations:** Compliance with data privacy and security regulations.\\n\\nWe can research industry reports and use Search to find information on typical barriers to entry in the AI software market.\\n\\n\\nBy thoroughly analyzing the market size, competition, and potential barriers to entry, we can develop a robust go-to-market strategy and make informed decisions about our product development and business operations.\\n']}\n", "\n", "---\n", "\n", "Output from node 'run':\n", "---\n", "{'past_steps': ['Go-to-Market Strategy:\\n\\nOur go-to-market strategy will focus on reaching SMBs in our target industries (e-commerce, retail, and SaaS) through a multi-pronged approach that combines digital marketing, content marketing, strategic partnerships, and direct sales.\\n\\n**Marketing Channels:**\\n\\n*   **Digital Marketing:** We\\'ll utilize targeted advertising campaigns on platforms like Google Ads, LinkedIn Ads, and social media channels relevant to our target audience.  We will focus on keywords and demographics relevant to SMBs in e-commerce, retail, and SaaS.  A/B testing will be crucial for optimizing ad spend and maximizing conversion rates.\\n*   **Content Marketing:** Creating valuable content, such as blog posts, case studies, white papers, and webinars, that addresses the specific needs and challenges of our target market.  SEO optimization will be key to attracting organic traffic.  Content will focus on topics like improving customer experience, increasing sales conversion rates, and leveraging AI for business growth.  We can use tools like Google Keyword Planner to identify relevant keywords.\\n*   **Social Media Marketing:** Building a strong social media presence on platforms like LinkedIn, Twitter, and potentially Facebook or Instagram depending on the specific target audience.  We\\'ll share engaging content, participate in relevant online communities, and run targeted social media advertising campaigns.\\n*   **Email Marketing:** Building an email list through lead magnets and website sign-ups and using targeted email campaigns to nurture leads, promote our platform, and announce new features.\\n*   **Search Engine Optimization (SEO):** Optimizing our website and content for relevant keywords to improve organic search rankings and attract potential customers actively searching for solutions like ours.  We\\'ll use tools like Google Search Console and SEMrush to track our SEO performance.\\n\\n**Sales Strategies:**\\n\\n*   **Inside Sales:**  A dedicated inside sales team will focus on qualifying leads generated through marketing efforts, conducting product demos, and closing deals. We\\'ll use CRM software to manage leads, track sales activity, and measure performance.\\n*   **Freemium/Trial Model:**  Offering a freemium version of our platform or a free trial period to allow potential customers to experience the value proposition firsthand.  This can be an effective way to generate leads and convert them into paying customers.\\n*   **Online Demos and Webinars:** Hosting regular online product demos and webinars to showcase the platform\\'s features and benefits and answer questions from potential customers.\\n\\n**Partnership Opportunities:**\\n\\n*   **Strategic Partnerships:** Collaborating with complementary technology providers, industry associations, and influencers in the SMB market.  Examples might include e-commerce platform providers, point-of-sale (POS) system vendors, or small business consulting firms. Search for \"e-commerce platforms for small business\" or \"small business consulting firms\" to find potential partners.\\n*   **Affiliate Marketing:** Partnering with affiliates who can promote our platform to their audience in exchange for a commission on sales.\\n*   **Channel Partners:**  Exploring potential partnerships with value-added resellers (VARs) or system integrators who can help us reach a wider audience and provide localized support.\\n\\n**Key Performance Indicators (KPIs):**\\n\\nWe will track key performance indicators to measure the success of our go-to-market strategy:\\n\\n*   **Website traffic and conversion rates:**  Monitoring the number of visitors to our website, lead generation rates, and conversion rates from free trials or freemium users to paying customers.\\n*   **Customer acquisition cost (CAC):** Measuring the cost of acquiring a new customer through different marketing channels.\\n*   **Customer lifetime value (CLTV):**  Estimating the total revenue generated by a customer over their relationship with our company.\\n*   **Churn rate:** Tracking the percentage of customers who cancel their subscription.\\n*   **Monthly recurring revenue (MRR):**  Monitoring the predictable revenue generated from subscriptions.\\n\\nBy implementing this comprehensive go-to-market strategy and closely tracking key performance indicators, we aim to effectively reach our target market, acquire customers, and achieve sustainable growth.  We will regularly review and adjust our strategy based on market feedback and performance data.\\n']}\n", "\n", "---\n", "\n", "Output from node 'run':\n", "---\n", "{'past_steps': ['Financial Projections (3-5 Years):\\n\\nCreating realistic financial projections requires making assumptions about key variables like customer acquisition cost (CAC), customer lifetime value (CLTV), churn rate, and operating expenses.  We\\'ll start with a bottom-up approach based on our pricing model and go-to-market strategy, then refine it with market research and competitor analysis.\\n\\n**Key Assumptions:**\\n\\n*   **Pricing:**  Based on our tiered subscription model (Basic, Premium, Enterprise), we\\'ll estimate the average revenue per user (ARPU) for each tier.  Researching competitor pricing and market benchmarks for similar SaaS products can help refine these estimates.  Search for \"pricing SaaS customer service software\" or \"average revenue per user SaaS\" for relevant information.\\n*   **Customer Acquisition Cost (CAC):**  We\\'ll estimate CAC based on our planned marketing and sales efforts.  This will vary depending on the channel (e.g., paid advertising, content marketing, partnerships).  We\\'ll track CAC closely and refine our estimates as we gather real-world data.  Search for \"calculate customer acquisition cost SaaS\" for helpful resources.\\n*   **Churn Rate:**  We\\'ll estimate churn rate based on industry averages for SaaS businesses.  We\\'ll strive to minimize churn through excellent customer service and continuous product improvement.  Search for \"average churn rate SaaS\" for benchmark data.\\n*   **Customer Lifetime Value (CLTV):**  We\\'ll calculate CLTV based on ARPU and churn rate.  A higher CLTV indicates a more sustainable business model. Search for \"calculate customer lifetime value\" for relevant formulas.\\n\\n\\n**Revenue Forecasts:**\\n\\nWe\\'ll project revenue for each year based on the number of customers acquired in each tier and their respective ARPU.  We\\'ll use conservative estimates initially and adjust as needed.  We\\'ll also factor in potential upselling opportunities and additional revenue streams.\\n\\n**Cost Estimates:**\\n\\n*   **Operating Expenses:**  We\\'ll project operating expenses, including salaries, marketing and sales costs, infrastructure costs (cloud hosting, software), and administrative overhead.  We can use industry benchmarks and our own internal estimates.\\n*   **Research & Development (R&D):**  Allocate budget for ongoing R&D to improve our AI models, develop new features, and stay ahead of the competition.\\n\\n**Key Performance Indicators (KPIs):**\\n\\nIn addition to the KPIs mentioned in the go-to-market strategy, we\\'ll track:\\n\\n*   **Monthly Recurring Revenue (MRR):**  A crucial metric for subscription-based businesses.\\n*   **Gross Margin:**  Indicates the profitability of our product after accounting for direct costs.\\n*   **Customer Lifetime Value (CLTV) to CAC Ratio:**  A key indicator of the health of our business model.\\n\\n**Financial Statements:**\\n\\nWe\\'ll create projected income statements, balance sheets, and cash flow statements for the next 3-5 years.  These statements will provide a comprehensive overview of our projected financial performance.\\n\\n**Example Spreadsheet Structure:**\\n\\n| Year | Metric                  | Tier 1 | Tier 2 | Tier 3 | Total    |\\n|------|--------------------------|--------|--------|--------|----------|\\n| 1    | Customers                |        |        |        |          |\\n|      | ARPU                    |        |        |        |          |\\n|      | Revenue                  |        |        |        |          |\\n|      | CAC                     |        |        |        |          |\\n|      | Total CAC                |        |        |        |          |\\n|      | Churn Rate               |        |        |        |          |\\n|      | CLTV                    |        |        |        |          |\\n|      | Operating Expenses      |        |        |        |          |\\n|      | Gross Profit             |        |        |        |          |\\n|      | Net Income/Loss         |        |        |        |          |\\n\\n\\n**Next Steps:**\\n\\n1.  **Gather Data:**  Use Search, market research reports, and industry benchmarks to refine our assumptions about pricing, CAC, churn rate, and operating expenses.\\n2.  **Build Model:** Create a spreadsheet model to project our financials based on the gathered data.\\n3.  **Sensitivity Analysis:** Perform sensitivity analysis to understand the impact of changes in key assumptions on our projections.\\n4.  **Refine and Iterate:** Continuously refine our projections as we gather more data and make progress.\\n\\nBy following this structured approach and leveraging available tools and resources, we can create realistic financial projections that inform our business decisions and attract potential investors.\\n']}\n", "\n", "---\n", "\n", "Output from node 'run':\n", "---\n", "{'past_steps': ['Funding Request:\\n\\nWe are seeking \\\\$5 million in Series A funding to accelerate the growth of our AI-powered customer interaction platform.  This funding will be used to:\\n\\n* **Expand our engineering team:** Hire additional AI engineers and software developers to accelerate product development and introduce new features, such as advanced analytics, multilingual support, and enhanced integrations.  This will allow us to address a wider range of customer needs and stay ahead of the competition.\\n* **Scale our marketing and sales efforts:** Increase investment in digital marketing, content marketing, and sales resources to reach a larger audience of SMBs and drive customer acquisition.  This includes expanding our sales team, optimizing our marketing campaigns, and building strategic partnerships.\\n* **Enhance our AI capabilities:** Invest in research and development to further improve our core AI algorithms, expand our training dataset, and explore new AI technologies. This will ensure our platform remains cutting-edge and continues to deliver exceptional value to our customers.  We will investigate emerging areas like generative AI and personalized learning models.\\n* **Build out our customer success team:** Invest in customer support and success resources to provide exceptional onboarding, training, and ongoing support to our growing customer base.  This will help us minimize churn and maximize customer lifetime value.\\n* **Expand our infrastructure:** Invest in cloud infrastructure and other necessary resources to support our growing user base and ensure platform scalability and reliability.\\n\\nExpected Return on Investment (ROI):\\n\\nWe project a significant return on investment for our investors based on our strong growth potential and the large market opportunity in the SMB customer interaction space.  Our financial projections indicate that we can achieve profitability within [ timeframe, e.g., 2-3 years] and generate a substantial return for our investors through a potential acquisition or IPO within [ timeframe, e.g., 5-7 years].\\n\\n**Data to Support ROI Projections:**\\n\\nWe will provide detailed financial projections, including revenue forecasts, cost estimates, and key performance indicators (KPIs), to support our ROI projections.  These projections will be based on a realistic assessment of our market opportunity, competitive landscape, and operating expenses.  We will use conservative assumptions and conduct sensitivity analysis to account for potential risks and uncertainties.\\n\\n**Exit Strategy:**\\n\\nOur long-term vision is to become the leading provider of AI-powered customer interaction solutions for SMBs. We anticipate several potential exit strategies, including:\\n\\n* **Acquisition by a larger technology company:**  Several established players in the CRM, marketing automation, or customer service space could be potential acquirers. We can research and identify potential acquirers in this space using Search.\\n* **Initial Public Offering (IPO):**  If we achieve significant scale and market leadership, an IPO could be a viable option to provide liquidity for our investors and fuel further growth.\\n\\n**Investor Benefits:**\\n\\nBy investing in our startup, investors will gain:\\n\\n* **Exposure to the rapidly growing AI market:** The AI market is experiencing exponential growth, and our platform is well-positioned to capitalize on this trend.\\n* **Opportunity to disrupt the SMB customer interaction space:**  Our innovative AI-powered solution has the potential to transform how SMBs interact with their customers.\\n* **Strong financial returns:**  Our projections indicate a significant return on investment for our investors.\\n* **Experienced and passionate team:**  Our team has a proven track record in AI, business development, and startup leadership.\\n\\nWe are confident that our team, technology, and go-to-market strategy position us for success in the competitive AI landscape. We believe this funding round will enable us to achieve our ambitious goals and deliver substantial value to our investors.\\n']}\n", "\n", "---\n", "\n", "Output from node 'run':\n", "---\n", "{'past_steps': ['Milestones and Timelines:\\n\\nOur milestones are designed to be SMART: Specific, Measurable, Achievable, Relevant, and Time-bound. They are tied to our key objectives and provide a clear roadmap for execution.\\n\\n**Phase 1: Product Development and Beta Launch (0-6 months)**\\n\\n*   **Milestone 1:** Complete core AI model development for chatbot, personalization engine, and sentiment analysis.  **Timeline:** 3 months\\n*   **Milestone 2:** Develop and launch a beta version of the platform with core features. **Timeline:** 6 months\\n*   **Milestone 3:** Secure initial beta users for testing and feedback. **Timeline:** 6 months\\n\\n**Phase 2: Market Entry and Customer Acquisition (6-12 months)**\\n\\n*   **Milestone 4:** Officially launch the platform to the public. **Timeline:** 9 months\\n*   **Milestone 5:** Acquire [Number] paying customers. **Timeline:** 12 months\\n*   **Milestone 6:** Secure key strategic partnerships.  **Timeline:** 12 months\\n\\n**Phase 3: Growth and Expansion (12-24 months)**\\n\\n*   **Milestone 7:** Achieve [Target Monthly Recurring Revenue (MRR)]. **Timeline:** 18 months\\n*   **Milestone 8:** Expand into new target markets or geographic regions. **Timeline:** 24 months\\n*   **Milestone 9:** Introduce new features based on customer feedback and market demand (e.g., advanced analytics, integrations). **Timeline:** Ongoing\\n\\n**Phase 4: Scaling and Long-Term Growth (24-36 months)**\\n\\n*   **Milestone 10:** Achieve profitability. **Timeline:** 30 months\\n*   **Milestone 11:** Secure Series B funding (if needed) to fuel further expansion.  **Timeline:** As needed\\n*   **Milestone 12:**  [Define specific long-term growth milestones, e.g., achieve a specific market share, user base, or revenue target]. **Timeline:** 36 months\\n\\n**Tools and Tracking:**\\n\\n*   We will use project management software (e.g., Jira, Asana) to track progress against milestones, manage tasks, and collaborate effectively.\\n*   Regular progress meetings and performance reviews will ensure accountability and allow us to adapt our plan as needed.\\n*   Key performance indicators (KPIs) will be tracked and analyzed regularly to measure success and identify areas for improvement.\\n\\n\\nThis timeline provides a framework for our development and growth. We understand that flexibility is important in the startup environment, and we will adapt our plans as needed based on market feedback, performance data, and unforeseen circumstances.  Regularly reviewing and updating our milestones and timelines will be crucial for staying on track and achieving our long-term vision.\\n']}\n", "\n", "---\n", "\n", "Output from node 'run':\n", "---\n", "{'past_steps': ['Exit Strategy:\\n\\nOur long-term vision is to become the leading provider of AI-powered customer interaction solutions for SMBs globally. We aim to empower businesses of all sizes to enhance customer experiences, improve operational efficiency, and achieve sustainable growth through our innovative AI platform.  We envision a future where intelligent, personalized interactions are the norm, not the exception, and we aim to be at the forefront of this transformation.\\n\\nPotential exit strategies include:\\n\\n* **Acquisition by a larger technology company:**  Several established players in the CRM, marketing automation, or customer service space could be potential acquirers.  Companies like Salesforce, HubSpot, Zendesk, or Intercom might be interested in acquiring our platform to expand their AI capabilities and reach the SMB market.  We can also research other potential acquirers, such as large e-commerce platforms or payment processors, who could integrate our technology into their existing offerings.  I\\'ll use Search to identify potential acquirers: \"CRM companies\", \"marketing automation companies\", \"customer service platforms\", \"e-commerce platforms\", \"payment processors\".\\n* **Initial Public Offering (IPO):** If we achieve significant scale, market leadership, and strong financial performance, an IPO could be a viable option. This would provide liquidity for our investors and fuel further growth through access to public markets.  We will carefully evaluate market conditions and our financial performance to determine the optimal timing for a potential IPO.\\n* **Strategic Partnership or Merger:**  While not a traditional exit, a strategic partnership or merger with a complementary technology provider could create significant value and accelerate our growth.  This could involve partnering with a company that offers complementary products or services, such as marketing automation software, e-commerce platforms, or payment gateways.  This would expand our reach and provide our customers with a more comprehensive suite of tools. I\\'ll use Search to identify potential strategic partners: \"marketing automation software\", \"e-commerce platforms\", \"payment gateways\".\\n\\n\\n\\nWe will continuously evaluate these options and make strategic decisions based on market conditions, our financial performance, and the best interests of our stakeholders.  Our primary focus will be on building a successful and sustainable business that delivers exceptional value to our customers.\\n']}\n", "\n", "---\n", "\n", "Output from node 'response':\n", "---\n", "{'final_response': AIMessage(content='## AI Startup One-Pager: Revolutionizing Customer Interaction for SMBs\\n\\n**Problem:** Small and medium-sized businesses (SMBs) struggle with efficient and personalized customer interactions, leading to lost sales and decreased satisfaction.  They lack resources for 24/7 support, personalized recommendations, and proactive engagement.\\n\\n**Solution:** An AI-powered customer interaction platform offering:\\n* 24/7 AI chatbot for instant support and data gathering.\\n* Personalized product recommendations to increase sales.\\n* Proactive customer engagement to prevent churn.\\n* Sentiment analysis to understand customer feedback.\\n\\n**Target Market:** SMBs in e-commerce, retail, and SaaS.\\n\\n**Value Proposition:** Affordable, easy-to-use, scalable solution enhancing customer experience, increasing sales, and improving retention.\\n\\n\\n**Technology & IP:**\\n\\n* **NLP:** Advanced models (transformer networks) for intent recognition, sentiment analysis, and entity extraction, trained on a vast dataset and refined through reinforcement learning.  Proprietary NLP pipeline and focus on continuous improvement.\\n* **ML for Personalization:** Collaborative and content-based filtering algorithms leveraging customer data for personalized recommendations. Flexible engine integrates with various SMB platforms.\\n* **Deep Learning for Sentiment Analysis:** RNNs and CNNs trained on diverse datasets for accurate sentiment gauging.  Identifies subtle shifts enabling proactive issue addressing.\\n* **Data Security:**  Strict adherence to data privacy regulations and robust security measures with privacy by design principles.\\n\\n**IP:** Patents pending for NLP pipeline, recommendation engine, and sentiment analysis algorithms.\\n\\n\\n**Business Model:**\\n\\n* **Revenue:** Tiered subscription model (Basic, Premium, Enterprise) with potential upselling opportunities.\\n* **Value Creation:** Enhances customer experience, increases sales, improves retention, streamlines operations, and provides customer insights.\\n* **Value Capture:** Tiered subscription model aligned with value delivered, continuous improvement justifies premium pricing.\\n\\n\\n**Team:** Experienced professionals in AI, business development, and startup leadership. Key members include [Founder Name] (AI and entrepreneurship), [Co-founder Name] (business development/marketing), [Lead AI Engineer Name] (NLP, ML, Deep Learning), and [Other Key Team Members].  [Advisory Board information if applicable].\\n\\n\\n**Market Analysis:**\\n\\n* **Market Size:** Data will be gathered using SBA definitions and industry reports focusing on SMBs in e-commerce, retail, and SaaS within our target geographic area. Search queries will include: \"number of SMBs in e-commerce [Target Geography]\", etc.\\n* **Competition:** Analysis of direct and indirect competitors, focusing on strengths, weaknesses, pricing, and differentiation.  Search queries will include:  \"AI customer interaction platform for SMBs,\" \"competitors [Competitor Name]\", etc.\\n* **Barriers to Entry:** Assessment of challenges like technology development, data acquisition, talent, funding, competition, and regulations.\\n\\n\\n**Go-to-Market Strategy:**\\n\\n* **Marketing:** Digital marketing (Google Ads, LinkedIn Ads, social media), content marketing (blog, case studies, webinars), social media marketing, email marketing, and SEO.\\n* **Sales:** Inside sales team, freemium/trial model, online demos, and webinars.\\n* **Partnerships:** Strategic partnerships with complementary technology providers, industry associations, affiliates, and channel partners.\\n* **KPIs:** Website traffic, conversion rates, CAC, CLTV, churn rate, and MRR.\\n\\n\\n**Financial Projections:**\\n\\n* **Assumptions:** Based on tiered pricing, estimated CAC, churn rate, and operating expenses.  Market research and competitor analysis will refine these assumptions.\\n* **Projections:** 3-5 year projections of revenue, costs, and KPIs, including income statements, balance sheets, and cash flow statements.\\n\\n\\n**Funding Request:**\\n\\n* **Amount:** \\\\$5 million Series A\\n* **Use of Funds:** Expand engineering, sales & marketing, enhance AI, build customer success team, and expand infrastructure.\\n* **Expected ROI:** Profitability within [Timeframe] and potential acquisition/IPO within [Timeframe]. Detailed financial projections will be provided.\\n\\n\\n**Milestones & Timelines:**\\n\\n* **Phase 1 (0-6 months):** Product development and beta launch.\\n* **Phase 2 (6-12 months):** Market entry and customer acquisition.\\n* **Phase 3 (12-24 months):** Growth and expansion.\\n* **Phase 4 (24-36 months):** Scaling and long-term growth.\\n\\n\\n**Exit Strategy:**\\n\\n* **Long-Term Vision:** Become the leading provider of AI-powered customer interaction solutions for SMBs globally.\\n* **Potential Exits:** Acquisition by a larger technology company (Salesforce, HubSpot, Zendesk, Intercom, etc.), IPO, or strategic partnership/merger.\\n', additional_kwargs={}, response_metadata={'is_blocked': False, 'safety_ratings': [], 'usage_metadata': {'prompt_token_count': 7151, 'candidates_token_count': 1003, 'total_token_count': 8154, 'cached_content_token_count': 0}, 'finish_reason': 'STOP', 'avg_logprobs': -0.12822153656218843}, id='run-447d51a9-e8a9-4075-9094-3cbc220a677e-0', usage_metadata={'input_tokens': 7151, 'output_tokens': 1003, 'total_tokens': 8154})}\n", "\n", "---\n", "\n"]}], "source": ["async for output in graph.astream({\"task\": task}, stream_mode=\"updates\"):\n", "    for key, value in output.items():\n", "        print(f\"Output from node '{key}':\")\n", "        print(\"---\")\n", "        print(value)\n", "    print(\"\\n---\\n\")"]}], "metadata": {"colab": {"name": "plan_and_solve.ipynb", "provenance": []}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 5}