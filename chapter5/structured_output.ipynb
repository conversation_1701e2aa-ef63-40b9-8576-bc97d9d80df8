{"cells": [{"cell_type": "code", "execution_count": null, "id": "sNjI6aGKBzAJ0aIcO0YDYpWs", "metadata": {"id": "sNjI6aGKBzAJ0aIcO0YDYpWs", "tags": []}, "outputs": [], "source": ["from langchain_google_vertexai import ChatVertexAI\n", "import os\n", "\n", "# 设置你的 Google Cloud 项目ID\n", "# 请将 'your-project-id' 替换为你的实际项目ID\n", "os.environ['GOOGLE_CLOUD_PROJECT'] = 'your-project-id'\n", "\n", "llm = ChatVertexAI(model=\"gemini-2.0-flash-001\")"]}, {"cell_type": "markdown", "id": "uXb2Mbky3cgU", "metadata": {"id": "uXb2Mbky3cgU"}, "source": ["# Structured output"]}, {"cell_type": "markdown", "id": "6a334c71", "metadata": {}, "source": ["Let's define the data structure that describes a plan to solve a complex task:"]}, {"cell_type": "code", "execution_count": null, "id": "hCCBqpWu3dpf", "metadata": {"id": "hCCBqpWu3dpf"}, "outputs": [], "source": ["from pydantic import BaseModel, Field\n", "from langchain_core.prompts import PromptTemplate\n", "\n", "\n", "class Step(BaseModel):\n", "    \"\"\"A step that is a part of the plan to solve the task.\"\"\"\n", "    step: str = Field(description=\"Description of the step\")\n", "\n", "\n", "class Plan(BaseModel):\n", "    \"\"\"A plan to solve the task.\"\"\"\n", "    steps: list[Step]\n", "\n", "\n", "prompt = PromptTemplate.from_template(\n", "    \"Prepare a step-by-step plan to solve the given task.\\n\"\n", "    \"TASK:\\n{task}\\n\"\n", ")"]}, {"cell_type": "markdown", "id": "5a36a781", "metadata": {}, "source": ["We can explore that the model has succcessfully generated a complex _Pydantic_ structure, and all we needed to do was using a `with_structured_output` method:"]}, {"cell_type": "code", "execution_count": null, "id": "AAnMWXKS4cW6", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 15920, "status": "ok", "timestamp": 1738682792461, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "AAnMWXKS4cW6", "outputId": "8c948d8d-d275-46a6-ead8-3f159c4a0994"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:langchain_google_vertexai.functions_utils:Key '$defs' is not supported in schema, ignoring\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Amount of steps: 5\n", "**1. Idea Generation and Validation:**\n", "*   **Brainstorm Unique Angles:**  Don't just rehash existing content. Find a fresh perspective on generative AI. Consider focusing on specific applications (e.g., AI in art, business, healthcare), ethical implications, future trends, or personal stories of transformation through AI.\n", "*   **Keyword Research:** Use tools like Ahrefs, SEMrush, or even Amazon's search bar to identify relevant keywords and search volume. This will help you understand market demand and optimize your book for discoverability.\n", "*   **Competitive Analysis:** Study existing bestsellers on Amazon about AI. What are their strengths and weaknesses? What gaps can you fill? How can you differentiate your book?\n", "*   **Target Audience Definition:** Clearly define your ideal reader. What are their interests, needs, and pain points?  Knowing your audience will guide your writing style and content.\n"]}], "source": ["result = (prompt | llm.with_structured_output(Plan)).invoke(\"How to write a bestseller on Amazon about generative AI?\")\n", "assert isinstance(result, Plan)\n", "print(f\"Amount of steps: {len(result.steps)}\")\n", "for step in result.steps:\n", "  print(step.step)\n", "  break"]}, {"cell_type": "markdown", "id": "fd3d4069", "metadata": {}, "source": ["We can also use a `json_mode` and pass a custom schema to an LLM. "]}, {"cell_type": "code", "execution_count": null, "id": "1Boay6jf0Nbr", "metadata": {"id": "1Boay6jf0Nbr"}, "outputs": [], "source": ["plan_schema = {\n", "    \"type\": \"ARRAY\",\n", "    \"items\": {\n", "        \"type\": \"OBJECT\",\n", "          \"properties\": {\n", "              \"step\": {\"type\": \"STRING\"},\n", "          },\n", "      },\n", "}\n", "\n", "query = \"How to write a bestseller on Amazon about generative AI?\"\n", "result = (prompt | llm.with_structured_output(schema=plan_schema, method=\"json_mode\")).invoke(query)"]}, {"cell_type": "code", "execution_count": null, "id": "taN02FVe070o", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 482, "status": "ok", "timestamp": 1738670370302, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "taN02FVe070o", "outputId": "db0eefb8-3a82-404d-ad49-f84f34927824"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Amount of steps: 13\n", "[{'step': 'Market Research: Analyze the existing books on generative AI on Amazon. Identify gaps, underserved niches, and potential angles.  Analyze best-selling books in similar categories (e.g., tech, AI, business) to understand successful marketing strategies and reader preferences.'}]\n"]}], "source": ["assert(isinstance(result, list))\n", "print(f\"Amount of steps: {len(result)}\")\n", "print(result[0])"]}, {"cell_type": "markdown", "id": "0ca6bf83", "metadata": {}, "source": ["As an alternative, we can use custom arguments supported by the LLM provider. Please, note that these options are vendor-specific, and you need to check the corresponding documentatino for supported extra arguments and formats:"]}, {"cell_type": "code", "execution_count": null, "id": "gGwDhLeX0Bj2", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 14036, "status": "ok", "timestamp": 1738682823912, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "gGwDhLeX0Bj2", "outputId": "ddc67da7-372a-4052-80a1-95ca2d5639c5"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Amount of steps: 9\n", "{'step': '1. Understand the Generative AI Landscape: Research the current state of generative AI, key players, emerging trends, and potential future developments. Identify the gaps in existing literature and target a specific niche within the generative AI field (e.g., creative writing, image generation, code generation, etc.).'}\n"]}], "source": ["from langchain_core.output_parsers import JsonOutputParser\n", "\n", "llm_json = ChatVertexAI(model_name=\"gemini-2.0-flash\", response_mime_type=\"application/json\", response_schema=plan_schema)\n", "result = (prompt | llm_json | JsonOutputParser()).invoke(query)\n", "assert(isinstance(result, list))\n", "print(f\"Amount of steps: {len(result)}\")\n", "print(result[0])"]}, {"cell_type": "markdown", "id": "de04ae3d", "metadata": {}, "source": ["We can also generated a enum (again, it's a vendor-dependent feature):"]}, {"cell_type": "code", "execution_count": null, "id": "xvWsxTtS1pkB", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 831, "status": "ok", "timestamp": 1738682834210, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "xvWsxTtS1pkB", "outputId": "6542a957-9c57-4437-b72b-bd630fa19423"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["positive\n"]}], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "\n", "response_schema = {\"type\": \"STRING\", \"enum\": [\"positive\", \"negative\", \"neutral\"]}\n", "\n", "prompt = PromptTemplate.from_template(\n", "    \"Classify the tone of the following customer's review:\"\n", "    \"\\n{review}\\n\"\n", ")\n", "\n", "review = \"I like this movie!\"\n", "llm_enum = ChatVertexAI(model_name=\"gemini-2.0-flash\", response_mime_type=\"text/x.enum\", response_schema=response_schema)\n", "result = (prompt | llm_enum | StrOutputParser()).invoke(review)\n", "print(result)"]}], "metadata": {"colab": {"name": "Chapter2.5", "provenance": []}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 5}