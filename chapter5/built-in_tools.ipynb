{"cells": [{"cell_type": "code", "execution_count": 1, "id": "sNjI6aGKBzAJ0aIcO0YDYpWs", "metadata": {"executionInfo": {"elapsed": 4176, "status": "ok", "timestamp": 1741671166847, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "sNjI6aGKBzAJ0aIcO0YDYpWs", "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/langchain_ai/lib/python3.10/site-packages/google/auth/_default.py:76: UserWarning: Your application has authenticated using end user credentials from Google Cloud SDK without a quota project. You might receive a \"quota exceeded\" or \"API not enabled\" error. See the following page for troubleshooting: https://cloud.google.com/docs/authentication/adc-troubleshooting/user-creds. \n", "  warnings.warn(_CLOUD_SDK_CREDENTIALS_WARNING)\n"]}], "source": ["from langchain_google_vertexai import ChatVertexAI\n", "\n", "llm = ChatVertexAI(model=\"gemini-2.0-flash-001\")"]}, {"cell_type": "markdown", "id": "KdEC7GQhCFHF", "metadata": {"id": "KdEC7GQhCFHF"}, "source": ["# Built-in tools"]}, {"cell_type": "markdown", "id": "1f2d3d10", "metadata": {"id": "1f2d3d10"}, "source": ["An example of using a search engine as a tool. Let's create one based on DuckDuckGo search and explore its name, description and arguments' schema:"]}, {"cell_type": "code", "execution_count": 2, "id": "3yNkoTHzCGxB", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 2, "status": "ok", "timestamp": 1741671173941, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "3yNkoTHzCGxB", "outputId": "40605938-02ea-48d1-a437-d8382263e858"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON>'s name = duckduckgo_search\n", "Tool's name = A wrapper around DuckDuckGo Search. Useful for when you need to answer questions about current events. Input should be a search query.\n", "Tool's arg schema = <class 'langchain_community.tools.ddg_search.tool.DDGInput'>\n"]}], "source": ["from langchain_community.tools import DuckDuckGoSearchRun\n", "\n", "search = DuckDuckGoSearchRun(api_wrapper_kwargs={\"backend\": \"api\"})\n", "print(f\"<PERSON><PERSON>'s name = {search.name}\")\n", "print(f\"<PERSON><PERSON>'s name = {search.description}\")\n", "print(f\"Too<PERSON>'s arg schema = {search.args_schema}\")"]}, {"cell_type": "code", "execution_count": 3, "id": "z_x1y9BmDZin", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 3, "status": "ok", "timestamp": 1741671174895, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "z_x1y9BmDZin", "outputId": "4dd22b69-e69f-41b8-b952-35282c982496"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'query': FieldInfo(annotation=str, required=True, description='search query to look up')}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1798/572309269.py:3: PydanticDeprecatedSince20: The `__fields__` attribute is deprecated, use `model_fields` instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/\n", "  print(DDGInput.__fields__)\n"]}], "source": ["from langchain_community.tools.ddg_search.tool import DDGInput\n", "\n", "print(DDGInput.__fields__)"]}, {"cell_type": "markdown", "id": "691b4e5f", "metadata": {"id": "691b4e5f"}, "source": ["Let's run the tool:"]}, {"cell_type": "code", "execution_count": 4, "id": "D0nSfgwqCLNz", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 1120, "status": "ok", "timestamp": *************, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "D0nSfgwqCLNz", "outputId": "79fb6d5f-6915-42d5-ef04-2811ac896975"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The temperature in Munich tomorrow in the early morning is 14 ° C.. If you take into account factors such as wind, humidity and other weather conditions, the temperatures can feel like 14 ° C.. The chance of rain in Munich in the morning is 0%, and the wind will blow at 5 km/h. Munich 7 day weather forecast including weather warnings, temperature, rain, wind, visibility, humidity and UV Munich Weather Forecast. Access detailed hourly and 14 day forecasts, current conditions, maps, warnings, meteograms, historical data and more for Munich ... Weather in Munich tomorrow, 20 June. 03:00 : 16° Clear skies Feels Like 16 ... Munich Weather Forecasts. Weather Underground provides local & long-range weather forecasts, weatherreports, maps & tropical weather conditions for the Munich area. ... like 68 ° Clear. N. 3 ... München ☀ Weather forecast for 10 days, information from meteorological stations, webcams, sunrise and sunset, wind and precipitation maps for this place ... Weather report from station Munich, Distance: 2 km (12:30 2025/06/13) Weather for the next 24 hours . 13:00 14:00 15:00 16:00 17:00 18:00 19:00 20:00 21:00 22:00 23:00 00:00 tomorrow 01 ...\n"]}], "source": ["query = \"What is the weather in Munich like tomorrow?\"\n", "search_input = DDGInput(query=query)\n", "result = search.invoke(search_input.model_dump())\n", "print(result)"]}, {"cell_type": "markdown", "id": "7c40978b", "metadata": {"id": "7c40978b"}, "source": ["Now let's enhance an LLM with this tool:"]}, {"cell_type": "code", "execution_count": 5, "id": "r3d4kLcNbSna", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 642, "status": "ok", "timestamp": 1741671295697, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "r3d4kLcNbSna", "outputId": "d60a0fe5-19ed-42dd-a2cf-9f661b7b8b78"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/langchain_ai/lib/python3.10/site-packages/google/auth/_default.py:76: UserWarning: Your application has authenticated using end user credentials from Google Cloud SDK without a quota project. You might receive a \"quota exceeded\" or \"API not enabled\" error. See the following page for troubleshooting: https://cloud.google.com/docs/authentication/adc-troubleshooting/user-creds. \n", "  warnings.warn(_CLOUD_SDK_CREDENTIALS_WARNING)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'name': 'duckduckgo_search', 'args': {'query': 'weather in Munich tomorrow'}, 'id': '135e676c-63fa-4cc7-ad00-e26485a86458', 'type': 'tool_call'}\n"]}], "source": ["result = llm.invoke([(\"system\", \"Always use a duckduckgo_search tool for queries that require a fresh information\"), (\"user\", query)], tools=[search])\n", "print(result.tool_calls[0])"]}, {"cell_type": "markdown", "id": "9pVVmvArs9aY", "metadata": {"id": "9pVVmvArs9aY"}, "source": ["And we can double-check that the model invokes a search tool only when needed:"]}, {"cell_type": "code", "execution_count": 6, "id": "jNiCHnb8sykM", "metadata": {"executionInfo": {"elapsed": 693, "status": "ok", "timestamp": 1741671352990, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "jNiCHnb8sykM"}, "outputs": [], "source": ["result = llm.invoke([(\"system\", \"Always use a duckduckgo_search tool for queries that require a fresh information\"), (\"user\", \"How much is 2+2?\")], tools=[search])\n", "assert not result.tool_calls"]}, {"cell_type": "markdown", "id": "7e218660", "metadata": {"id": "7e218660"}, "source": ["And we can create a ReACT agent that uses this tool, and explore its workflow graph:"]}, {"cell_type": "code", "execution_count": 7, "id": "2uYnZPmgCehC", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 266}, "executionInfo": {"elapsed": 5, "status": "ok", "timestamp": 1741671420509, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "2uYnZPmgCehC", "outputId": "68e50780-87a1-497a-be22-906fc64bfb81"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from langgraph.prebuilt import create_react_agent\n", "from IPython.display import Image, display\n", "\n", "agent = create_react_agent(\n", "  model=llm,\n", "  tools=[search],\n", "  state_modifier=\"Always use a duckduckgo_search tool for queries that require a fresh information\"\n", ")\n", "\n", "display(Image(agent.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "32bfc4ee", "metadata": {"id": "32bfc4ee"}, "source": ["If we run it, we can see the tool calls appearing during execution:"]}, {"cell_type": "code", "execution_count": 8, "id": "gI7XZBU-0FZJ", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 2010, "status": "ok", "timestamp": 1741671425384, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "gI7XZBU-0FZJ", "outputId": "2d6511fd-fb40-4868-945d-4b835c50d497"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "I'm sorry, I'm unable to provide weather forecasts. Please try using a dedicated weather service for that information.\n"]}], "source": ["for event in agent.stream({\"messages\": [(\"user\", query)]}):\n", "  messages = event.get(\"agent\", event.get(\"tools\", {})).get(\"messages\", [])\n", "  for m in messages:\n", "     m.pretty_print()\n"]}, {"cell_type": "markdown", "id": "a4a9827a", "metadata": {"id": "a4a9827a"}, "source": ["We can also create a tool ourselves from other building blocks:"]}, {"cell_type": "code", "execution_count": 9, "id": "e0-AG1w0CUVR", "metadata": {"executionInfo": {"elapsed": 300, "status": "ok", "timestamp": 1741671429909, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "e0-AG1w0CUVR"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Python REPL can execute arbitrary code. Use with caution.\n"]}], "source": ["from langchain_core.tools import Tool\n", "from langchain_experimental.utilities import PythonREPL\n", "\n", "python_repl = PythonREPL()\n", "python_repl.run(\"print(2**4)\")\n", "\n", "code_interpreter_tool = Tool(\n", "    name=\"python_repl\",\n", "    description=\"A Python shell. Use this to execute python commands. Input should be a valid python command. If you want to see the output of a value, you should print it out with `print(...)`.\",\n", "    func=python_repl.run,\n", ")"]}, {"cell_type": "markdown", "id": "e1dcd841", "metadata": {"id": "e1dcd841"}, "source": ["This is a famous failure of an LLM:"]}, {"cell_type": "code", "execution_count": 10, "id": "UBcZAqiEtUKX", "metadata": {"executionInfo": {"elapsed": 308, "status": "ok", "timestamp": 1741671510650, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "UBcZAqiEtUKX"}, "outputs": [], "source": ["llm = ChatVertexAI(model=\"gemini-2.0-flash-001\", temperature=2.0)"]}, {"cell_type": "code", "execution_count": 11, "id": "fITV0-OOIWtm", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 358, "status": "ok", "timestamp": 1741671539609, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "fITV0-OOIWtm", "outputId": "d1216a9d-4aba-4f5c-cc9d-b8c3c5abc6b1"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/langchain_ai/lib/python3.10/site-packages/google/auth/_default.py:76: UserWarning: Your application has authenticated using end user credentials from Google Cloud SDK without a quota project. You might receive a \"quota exceeded\" or \"API not enabled\" error. See the following page for troubleshooting: https://cloud.google.com/docs/authentication/adc-troubleshooting/user-creds. \n", "  warnings.warn(_CLOUD_SDK_CREDENTIALS_WARNING)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["There are three \"r\"s in the word \"strawberry\".\n", "\n"]}], "source": ["query_strawberry = \"How many r are in the word strawberry?\"\n", "print(llm.invoke(query_strawberry).content)"]}, {"cell_type": "markdown", "id": "4IyjlM1SttXk", "metadata": {"id": "4IyjlM1SttXk"}, "source": ["But we can see that ReACT agent easily solved this problem by invoking an external tool:"]}, {"cell_type": "code", "execution_count": 12, "id": "M0DROwarIWwk", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 866, "status": "ok", "timestamp": 1741671544045, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "M0DROwarIWwk", "outputId": "6b61a27a-09a0-4308-ae04-bca4caf305ef"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  python_repl (d54b9bf2-27c1-4398-9a16-ca17d021e7f9)\n", " Call ID: d54b9bf2-27c1-4398-9a16-ca17d021e7f9\n", "  Args:\n", "    __arg1: print('strawberry'.count('r'))\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: python_repl\n", "\n", "3\n", "\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "There are 3 r's in the word strawberry.\n"]}], "source": ["agent = create_react_agent(\n", "  model=llm,\n", "  tools=[code_interpreter_tool])\n", "\n", "for event in agent.stream({\"messages\": [(\"user\", query_strawberry)]}):\n", "  messages = event.get(\"agent\", event.get(\"tools\", {})).get(\"messages\", [])\n", "  for m in messages:\n", "     m.pretty_print()"]}, {"cell_type": "markdown", "id": "af8f33c7", "metadata": {"id": "af8f33c7"}, "source": ["We can also use any third-pary API by wrapping it a tool. But first, we need to define a schema (below is an example for an open-sourced [exchange rates API](https://api.frankfurter.dev/v1)):"]}, {"cell_type": "code", "execution_count": 13, "id": "5MorOS6SNSWa", "metadata": {"executionInfo": {"elapsed": 323, "status": "ok", "timestamp": 1741671574007, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "5MorOS6SNSWa"}, "outputs": [], "source": ["api_spec = \"\"\"\n", "openapi: 3.0.0\n", "info:\n", "  title: Frankfurter Currency Exchange API\n", "  version: v1\n", "  description: API for retrieving currency exchange rates. Pay attention to the base currency and change it if needed.\n", "\n", "servers:\n", "  - url: https://api.frankfurter.dev/v1\n", "\n", "paths:\n", "  /v1/{date}:\n", "    get:\n", "      summary: Get exchange rates for a specific date.\n", "      parameters:\n", "        - in: path\n", "          name: date\n", "          schema:\n", "            type: string\n", "            pattern: '^\\d{4}-\\d{2}-\\d{2}$' # YYYY-MM-DD format\n", "          required: true\n", "          description: The date for which to retrieve exchange rates.  Use YYYY-MM-DD format.  Example: 2009-01-04\n", "        - in: query\n", "          name: symbols\n", "          schema:\n", "            type: string\n", "          description: Comma-separated list of currency symbols to retrieve rates for. Example: GBP,USD,EUR\n", "\n", "  /v1/latest:\n", "    get:\n", "      summary: Get the latest exchange rates.\n", "      parameters:\n", "        - in: query\n", "          name: symbols\n", "          schema:\n", "            type: string\n", "          description: Comma-separated list of currency symbols to retrieve rates for. Example: CHF,GBP\n", "        - in: query\n", "          name: base\n", "          schema:\n", "            type: string\n", "          description: The base currency for the exchange rates. If not provided, EUR is used as a base currency. Example: USD\n", "\"\"\""]}, {"cell_type": "markdown", "id": "0766a1af", "metadata": {"id": "0766a1af"}, "source": ["We can wrap it with an out-of-the-box `TextRequestsWrapper`:"]}, {"cell_type": "code", "execution_count": 14, "id": "-SRXlQqkORD9", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 621, "status": "ok", "timestamp": 1741671576573, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "-SRXlQqkORD9", "outputId": "314621a3-7133-4a22-d7ee-90437637bad9"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["requests_get\n", "requests_post\n", "requests_patch\n", "requests_put\n", "requests_delete\n"]}], "source": ["from langchain_community.agent_toolkits.openapi.toolkit import RequestsToolkit\n", "from langchain_community.utilities.requests import TextRequestsWrapper\n", "\n", "toolkit = RequestsToolkit(\n", "    requests_wrapper=TextRequestsWrapper(headers={}),\n", "    allow_dangerous_requests=True,\n", ")\n", "\n", "for tool in toolkit.get_tools():\n", "  print(tool.name)"]}, {"cell_type": "markdown", "id": "62cdd65d", "metadata": {"id": "62cdd65d"}, "source": ["Our ReACT agent now looks like this (note, that we have modified the system message):"]}, {"cell_type": "code", "execution_count": 51, "id": "bA7uaYWGNhz0", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 1567, "status": "ok", "timestamp": 1741671580658, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "bA7uaYWGNhz0", "outputId": "b5f2d1f9-3585-4eec-c6c3-bfd976012992"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "What is the swiss franc to US dollar exchange rate?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  requests_get (0978a563-9a08-4b82-a2cf-82f272601267)\n", " Call ID: 0978a563-9a08-4b82-a2cf-82f272601267\n", "  Args:\n", "    url: https://api.frankfurter.dev/v1/latest?symbols=USD&base=CHF\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: requests_get\n", "\n", "{\"amount\":1.0,\"base\":\"CHF\",\"date\":\"2025-03-10\",\"rates\":{\"USD\":1.1401}}\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "The Swiss Franc to US Dollar exchange rate is 1.1401.\n"]}], "source": ["system_message = (\n", "  \"You're given the API spec:\\n{api_spec}\\n\"\n", "  \"Use the API to answer users' queries if possible. \"\n", ")\n", "\n", "agent = create_react_agent(llm, toolkit.get_tools(), state_modifier=system_message.format(api_spec=api_spec))\n", "\n", "query = \"What is the swiss franc to US dollar exchange rate?\"\n", "\n", "events = agent.stream(\n", "    {\"messages\": [(\"user\", query)]},\n", "    stream_mode=\"values\",\n", ")\n", "for event in events:\n", "    event[\"messages\"][-1].pretty_print()"]}], "metadata": {"colab": {"name": "built-in_tools.ipynb", "provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}