{"cells": [{"cell_type": "code", "execution_count": 7, "id": "sNjI6aGKBzAJ0aIcO0YDYpWs", "metadata": {"id": "sNjI6aGKBzAJ0aIcO0YDYpWs", "tags": [], "executionInfo": {"status": "ok", "timestamp": 1741672072718, "user_tz": -60, "elapsed": 4423, "user": {"displayName": "", "userId": ""}}}, "outputs": [], "source": ["from langchain_google_vertexai import ChatVertexAI\n", "from langchain_core.prompts import PromptTemplate\n", "import os\n", "\n", "# 设置你的 Google Cloud 项目ID\n", "# 请将 'your-project-id' 替换为你的实际项目ID\n", "os.environ['GOOGLE_CLOUD_PROJECT'] = 'your-project-id'\n", "\n", "llm = ChatVertexAI(model=\"gemini-2.0-flash-001\")"]}, {"cell_type": "markdown", "id": "i9Q3FOwbDgCo", "metadata": {"id": "i9Q3FOwbDgCo"}, "source": ["# Custom tools"]}, {"cell_type": "markdown", "id": "7320bac8", "metadata": {"id": "7320bac8"}, "source": ["We will use `numexpr` library to evaluate math expressions:"]}, {"cell_type": "code", "execution_count": 2, "id": "AAua4df7D0EB", "metadata": {"executionInfo": {"elapsed": 306, "status": "ok", "timestamp": 1741672033679, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "AAua4df7D0EB"}, "outputs": [], "source": ["import numexpr as ne\n", "import math\n", "\n", "math_constants = {\"pi\": math.pi, \"i\": 1j, \"e\": math.exp},\n", "c = ne.evaluate((\"2+2\"), local_dict=math_constants)\n"]}, {"cell_type": "code", "execution_count": 3, "id": "8euz4lp-D9le", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 300, "status": "ok", "timestamp": 1741672036570, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "8euz4lp-D9le", "outputId": "585dfe6f-d3a7-4af5-9e1e-c6302d156318"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array(-5.+12.j)"]}, "metadata": {}, "execution_count": 3}], "source": ["math_constants = {\"pi\": math.pi, \"i\": 1j, \"e\": math.exp}\n", "ne.evaluate(\"(2+3*i)**2\", local_dict=math_constants)"]}, {"cell_type": "markdown", "id": "f0f637aa", "metadata": {"id": "f0f637aa"}, "source": ["We define our calculator as a Python function and wrap it with a built-in `@tool` decorator to create a tool from it:"]}, {"cell_type": "code", "execution_count": 4, "id": "FD8AZdXJDhEc", "metadata": {"id": "FD8AZdXJDhEc", "executionInfo": {"status": "ok", "timestamp": 1741672042364, "user_tz": -60, "elapsed": 1236, "user": {"displayName": "", "userId": ""}}}, "outputs": [], "source": ["from langchain_core.tools import tool\n", "\n", "\n", "@tool\n", "def calculator(expression: str) -> str:\n", "    \"\"\"Calculates a single mathematical expression, incl. complex numbers.\n", "\n", "    Always add * to operations, examples:\n", "      73i -> 73*i\n", "      7pi**2 -> 7*pi**2\n", "    \"\"\"\n", "    math_constants = {\"pi\": math.pi, \"i\": 1j, \"e\": math.exp}\n", "    result = ne.evaluate(expression.strip(), local_dict=math_constants)\n", "    return str(result)\n"]}, {"cell_type": "code", "execution_count": 5, "id": "x6H6IKjTFigf", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 3, "status": "ok", "timestamp": 1741672042364, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "x6H6IKjTFigf", "outputId": "f6a49c6b-93d6-4d75-fdd1-652f4155de8c"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Tool name: calculator\n", "Tool name: Calculates a single mathematical expression, incl. complex numbers.\n", "\n", "    Always add * to operations, examples:\n", "      73i -> 73*i\n", "      7pi**2 -> 7*pi**2\n", "Tool schema: {'description': 'Calculates a single mathematical expression, incl. complex numbers.\\n\\nAlways add * to operations, examples:\\n  73i -> 73*i\\n  7pi**2 -> 7*pi**2', 'properties': {'expression': {'title': 'Expression', 'type': 'string'}}, 'required': ['expression'], 'title': 'calculator', 'type': 'object'}\n"]}], "source": ["from langchain_core.tools import BaseTool\n", "\n", "assert isinstance(calculator, BaseTool)\n", "print(f\"Tool name: {calculator.name}\")\n", "print(f\"Tool name: {calculator.description}\")\n", "print(f\"Tool schema: {calculator.args_schema.model_json_schema()}\")"]}, {"cell_type": "code", "execution_count": 8, "id": "-acaMT4nE9qa", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 663, "status": "ok", "timestamp": 1741672073377, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "-acaMT4nE9qa", "outputId": "eb8f5010-b1ba-4310-9be1-a467de82f18b"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "How much is 2+3i squared?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  calculator (799f8b33-03cf-4920-bdbe-52680662fb5f)\n", " Call ID: 799f8b33-03cf-4920-bdbe-52680662fb5f\n", "  Args:\n", "    expression: (2+3*i)**2\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: calculator\n", "\n", "(-5+12j)\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "(2+3i)^2 is -5+12j.\n"]}], "source": ["from langgraph.prebuilt import create_react_agent\n", "\n", "query = \"How much is 2+3i squared?\"\n", "\n", "agent = create_react_agent(llm, [calculator])\n", "\n", "for event in agent.stream({\"messages\": [(\"user\", query)]}, stream_mode=\"values\"):\n", "    event[\"messages\"][-1].pretty_print()"]}, {"cell_type": "markdown", "source": ["We will re-use the `search` tool we created in the previous section:"], "metadata": {"id": "7NKS080hv7Lm"}, "id": "7NKS080hv7Lm"}, {"cell_type": "code", "source": ["from langchain_community.tools import DuckDuckGoSearchRun\n", "\n", "search = DuckDuckGoSearchRun()"], "metadata": {"id": "tNkk4a49v_F9", "executionInfo": {"status": "ok", "timestamp": 1741672190303, "user_tz": -60, "elapsed": 4, "user": {"displayName": "", "userId": ""}}}, "id": "tNkk4a49v_F9", "execution_count": 9, "outputs": []}, {"cell_type": "code", "execution_count": 11, "id": "J6FafmMHLJ35", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 4861, "status": "ok", "timestamp": 1741683655632, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "J6FafmMHLJ35", "outputId": "afc9d469-7cd0-4496-dbc0-612e6bf33f5d"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "What is a square root of the current US president’s age multiplied by 132?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  duckduckgo_search (364bee40-efd2-4f08-96c6-84f2bd1f4ca0)\n", " Call ID: 364bee40-efd2-4f08-96c6-84f2bd1f4ca0\n", "  Args:\n", "    query: current US president age\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: duckduckgo_search\n", "\n", "Following <PERSON>'s death at the age of 100, there are five living former or current U.S. presidents. At 82 years old, <PERSON> is the eldest of the group. He's closely followed by ... The White House, official residence of the president of the United States, in July 2008. The president of the United States is the head of state and head of government of the United States, [1] indirectly elected to a four-year term via the Electoral College. [2] Under the U.S. Constitution, the officeholder leads the executive branch of the federal government and is the commander-in-chief of ... Here are all 44 US presidents, ordered by their age at the start of their presidencies. While the minimum age requirement for president is 35 years old, the median age of presidents when elected to office is 55 years old. In 2017, President <PERSON> became the oldest elected president at 70 years old. Only two presidents were younger than <PERSON> when they were sworn in, <PERSON> was 42 years of age and <PERSON> was 43. How old is <PERSON>? <PERSON>, 63, was born on Aug. 4, 1961. The 2024 presidential race has finally ended with <PERSON> being voted the 47th U.S. President. The 2 most recent Presidents were among the oldest Presidents the United States has seen, but how do they compare to past Presidents and current world leaders in age? LandGate provided visualizations for age vs the average lifespan for historical and contemporary figures. The Age Factor One of ...\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "According to the search results, <PERSON> was voted the 47th U.S. President in the 2024 presidential race. I will now search for his age.\n", "Tool Calls:\n", "  duckduckgo_search (1dabbfaf-9f7b-4c31-bb5e-09552c293776)\n", " Call ID: 1dabbfaf-9f7b-4c31-bb5e-09552c293776\n", "  Args:\n", "    query: how old is <PERSON>\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: duckduckgo_search\n", "\n", "How old is <PERSON>? <PERSON> was born on June 14, 1946. On the day of his inauguration, he will be 78 years, 7 months, 6 days, excluding Jan. 20, according to TimeandDate's age calculator. In a rare speechless moment Sunday, <PERSON> stepped back from the microphone as his Las Vegas supporters spontaneously broke into a disjointed rendition of the world's most recognizable song. <PERSON>, a 13-year-old battling brain cancer, visited <PERSON> in the Oval Office. His visit comes after he was made an honorary Secret Service member during <PERSON>'s joint address to ... How old is <PERSON>? <PERSON> is 78, the same age <PERSON><PERSON> when we was sworn in 2021. But, <PERSON> has a June 14 birthday, making him a few months older than <PERSON><PERSON> was he was sworn in. <PERSON> specifically said that SSA records indicated that 4.7 million people 100 to 109 were getting checks, that 3.6 million 110 to 119 were, that 3.47 million 120 to 129 were, that 3.9 million 130 ...\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "<PERSON> is 78 years old.\n", "Tool Calls:\n", "  calculator (a57211ff-a98e-4f44-9ba5-37ea9e7af63e)\n", " Call ID: a57211ff-a98e-4f44-9ba5-37ea9e7af63e\n", "  Args:\n", "    expression: 78*132\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: calculator\n", "\n", "10296\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  calculator (48859a64-d974-455c-b0be-61284d001c38)\n", " Call ID: 48859a64-d974-455c-b0be-61284d001c38\n", "  Args:\n", "    expression: sqrt(10296)\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: calculator\n", "\n", "101.46920715172658\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "The square root of the current US president's age multiplied by 132 is approximately 101.47.\n"]}], "source": ["question = \"What is a square root of the current US president’s age multiplied by 132?\"\n", "\n", "system_hint = \"Think step-by-step. Always use search to get the fresh information about events or public facts that can change over time. Now is 2025 and remember president elections in the US recently happened.\"\n", "\n", "agent = create_react_agent(\n", "    llm, [calculator, search],\n", "    state_modifier=system_hint)\n", "\n", "for event in agent.stream({\"messages\": [(\"user\", question)]}, stream_mode=\"values\"):\n", "    event[\"messages\"][-1].pretty_print()"]}, {"cell_type": "markdown", "source": ["We can explore the final answer the model produced:"], "metadata": {"id": "G-HejN1xvwVO"}, "id": "G-HejN1xvwVO"}, {"cell_type": "code", "execution_count": 12, "id": "DNSYgZ9uNOJR", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 298, "status": "ok", "timestamp": 1741683659109, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "DNSYgZ9uNOJR", "outputId": "1516ec92-8f47-4a5e-d10a-5a19b5a5d2d5"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["The square root of the current US president's age multiplied by 132 is approximately 101.47.\n", "\n"]}], "source": ["print(event[\"messages\"][-1].content)"]}, {"cell_type": "markdown", "id": "1d739d28", "metadata": {"id": "1d739d28"}, "source": ["We can also create a tool from a `Runnable` (and again, we can convery any Python function to a `Runnable` by using `RunnableLambda`):"]}, {"cell_type": "code", "execution_count": 13, "id": "DpCOBFCxbfio", "metadata": {"id": "DpCOBFCxbfio", "executionInfo": {"status": "ok", "timestamp": 1741683671106, "user_tz": -60, "elapsed": 327, "user": {"displayName": "", "userId": ""}}}, "outputs": [], "source": ["from langchain_core.runnables import RunnableLambda\n", "from langchain_core.tools import convert_runnable_to_tool\n", "\n", "\n", "def calculator(expression: str) -> str:\n", "    math_constants = {\"pi\": math.pi, \"i\": 1j, \"e\": math.exp}\n", "    result = ne.evaluate(expression.strip(), local_dict=math_constants)\n", "    return str(result)\n", "\n", "\n", "calculator_with_retry = RunnableLambda(calculator).with_retry(\n", "    wait_exponential_jitter=True,\n", "    stop_after_attempt=3,\n", ")\n", "\n", "\n", "calculator_tool = convert_runnable_to_tool(\n", "    calculator_with_retry,\n", "    name=\"calculator\",\n", "    description=(\n", "        \"Calculates a single mathematical expression, incl. complex numbers.\"\n", "        \"'\\nAlways add * to operations, examples:\\n73i -> 73*i\\n\"\n", "        \"7pi**2 -> 7*pi**2\"\n", "    ),\n", "    arg_types={\"expression\": \"str\"},\n", ")"]}, {"cell_type": "code", "execution_count": 15, "id": "Wi5ei3L_bflH", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "executionInfo": {"elapsed": 312, "status": "ok", "timestamp": 1741683681843, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "Wi5ei3L_bflH", "outputId": "ab2e4137-c9ad-4202-8e27-5e6dba032290"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'(-5+12j)'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 15}], "source": ["calculator_tool.invoke({\"expression\": \"(2+3*i)**2\"})"]}, {"cell_type": "code", "execution_count": 16, "id": "SKIDo37EcN8t", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 645, "status": "ok", "timestamp": 1741683684900, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "SKIDo37EcN8t", "outputId": "4db93338-3650-4e33-ecc1-5fa0c06fefa5"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'name': 'calculator',\n", " 'args': {'__arg1': '(2+3*i)**2'},\n", " 'id': '3ca39b86-695b-4b21-a058-5d564e705743',\n", " 'type': 'tool_call'}"]}, "metadata": {}, "execution_count": 16}], "source": ["llm.invoke(\"How much is (2+3i)**2\", tools=[calculator_tool]).tool_calls[0]"]}, {"cell_type": "markdown", "source": ["We can also pass a custom arguments schema when we create a tool with `convert_runnable_to_tool`:"], "metadata": {"id": "sl7I6l6ocCPc"}, "id": "sl7I6l6ocCPc"}, {"cell_type": "code", "execution_count": 18, "id": "nr9ZHTlxWHTP", "metadata": {"id": "nr9ZHTlxWHTP", "executionInfo": {"status": "ok", "timestamp": 1741683750269, "user_tz": -60, "elapsed": 313, "user": {"displayName": "", "userId": ""}}}, "outputs": [], "source": ["from pydantic import BaseModel, Field\n", "from langchain_core.runnables import RunnableConfig\n", "\n", "\n", "class CalculatorArgs(BaseModel):\n", "    expression: str = Field(description=\"Mathematical expression to be evaluated\")\n", "\n", "\n", "def calculator(state: CalculatorArgs, config: RunnableConfig) -> str:\n", "    expression = state[\"expression\"]\n", "    math_constants = config[\"configurable\"].get(\"math_constants\", {})\n", "    result = ne.evaluate(expression.strip(), local_dict=math_constants)\n", "    return str(result)\n", "\n", "\n", "calculator_with_retry = RunnableLambda(calculator).with_retry(\n", "    wait_exponential_jitter=True,\n", "    stop_after_attempt=3,\n", ")\n", "\n", "calculator_tool = convert_runnable_to_tool(\n", "    calculator_with_retry,\n", "    name=\"calculator\",\n", "    description=(\n", "        \"Calculates a single mathematical expression, incl. complex numbers.\"\n", "        \"'\\nAlways add * to operations, examples:\\n73i -> 73*i\\n\"\n", "        \"7pi**2 -> 7*pi**2\"\n", "    ),\n", "    args_schema=CalculatorArgs,\n", "    arg_types={\"expression\": \"str\"},\n", ")"]}, {"cell_type": "code", "execution_count": 19, "id": "b2Oz-xRfdonW", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 5, "status": "ok", "timestamp": 1741683751202, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "b2Oz-xRfdonW", "outputId": "4785f128-451b-400d-c713-4021b89d38b0"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Tool name: calculator\n", "Tool name: Calculates a single mathematical expression, incl. complex numbers.'\n", "Always add * to operations, examples:\n", "73i -> 73*i\n", "7pi**2 -> 7*pi**2\n", "Args schema: {'properties': {'expression': {'title': 'Expression', 'type': 'string'}}, 'required': ['expression'], 'title': 'calculator', 'type': 'object'}\n"]}], "source": ["assert isinstance(calculator_tool, BaseTool)\n", "print(f\"Tool name: {calculator_tool.name}\")\n", "print(f\"Tool name: {calculator_tool.description}\")\n", "print(f\"Args schema: {calculator_tool.args_schema.model_json_schema()}\")"]}, {"cell_type": "markdown", "source": ["That's how we pass configuration to our new tool:"], "metadata": {"id": "NZNCd-z0cXQy"}, "id": "NZNCd-z0cXQy"}, {"cell_type": "code", "execution_count": 20, "id": "j33wWz4dYK_n", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 330, "status": "ok", "timestamp": 1741683779216, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "j33wWz4dYK_n", "outputId": "a7a9bcaa-0372-4de9-c71c-f69ce52b9746"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["{'name': 'calculator', 'args': {'expression': '(2+3*i)**2'}, 'id': '1d032f30-8e22-4f4d-885f-174b5c23a7bc', 'type': 'tool_call'}\n"]}], "source": ["math_constants = {\"pi\": math.pi, \"i\": 1j, \"e\": math.exp}\n", "config = {\"configurable\": {\"math_constants\": math_constants}}\n", "\n", "tool_call = llm.invoke(\"How much is (2+3i)**2\", tools=[calculator_tool]).tool_calls[0]\n", "print(tool_call)"]}, {"cell_type": "code", "execution_count": 21, "id": "x-94yBEKePI9", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "executionInfo": {"elapsed": 319, "status": "ok", "timestamp": 1741683798609, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "x-94yBEKePI9", "outputId": "e92ee9b1-6e40-4f01-f644-51d92f6ad46b"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'(-5+12j)'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 21}], "source": ["calculator_tool.invoke(tool_call[\"args\"], config=config)"]}, {"cell_type": "code", "execution_count": 23, "id": "QUTrwh24yHUn", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 622, "status": "ok", "timestamp": 1741683830955, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "QUTrwh24yHUn", "outputId": "50b82e64-ec1f-4718-821e-83f5b2064ea1"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["{'name': 'calculator', 'args': {'expression': '(2+3i)**2'}, 'id': '5e48037b-5ae8-4b5d-832e-aef8a2ee5fd1', 'type': 'tool_call'}\n"]}], "source": ["from langchain_core.tools import StructuredTool\n", "\n", "def calculator(expression: str) -> str:\n", "    math_constants = {\"pi\": math.pi, \"i\": 1j, \"e\": math.exp}\n", "    result = ne.evaluate(expression.strip(), local_dict=math_constants)\n", "    return str(result)\n", "\n", "calculator_tool = StructuredTool.from_function(\n", "    name=\"calculator\",\n", "    description=(\n", "        \"Calculates a single mathematical expression, incl. complex numbers.\"),\n", "    func=calculator,\n", "    args_schema=CalculatorArgs\n", ")\n", "\n", "tool_call = llm.invoke(\"How much is (2+3i)**2\", tools=[calculator_tool]).tool_calls[0]\n", "print(tool_call)"]}, {"cell_type": "markdown", "source": ["We can also take a look at how our agents adapts to the feedback from the environment and how an LLM corrects the input sent to the tool:"], "metadata": {"id": "GJ39w8RAc2AL"}, "id": "GJ39w8RAc2AL"}, {"cell_type": "code", "execution_count": 27, "id": "ZMxUl2GI9x5l", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 2508, "status": "ok", "timestamp": 1741683881774, "user": {"displayName": "", "userId": ""}, "user_tz": -60}, "id": "ZMxUl2GI9x5l", "outputId": "37350aef-8380-42d1-cdfd-5b8dca3cb56d"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "How much is (2+3i)^2\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  calculator (98fc1fae-d5a6-4d32-9fe9-b3017e5e8bf8)\n", " Call ID: 98fc1fae-d5a6-4d32-9fe9-b3017e5e8bf8\n", "  Args:\n", "    expression: (2+3i)^2\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: calculator\n", "\n", "Error: SyntaxError('invalid decimal literal', ('<expr>', 1, 4, '(2+3i)^2', 1, 4))\n", " Please fix your mistakes.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "I am sorry, I encountered an error. I will try again.\n", "Tool Calls:\n", "  calculator (1332e6bc-d6a3-4649-8992-f7bb8da77d11)\n", " Call ID: 1332e6bc-d6a3-4649-8992-f7bb8da77d11\n", "  Args:\n", "    expression: (2+3*i)^2\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: calculator\n", "\n", "Error: TypeError(\"unsupported operand type(s) for ^: 'OpNode' and 'int'\")\n", " Please fix your mistakes.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "I am sorry, I encountered an error. I will try again.\n", "Tool Calls:\n", "  calculator (204b107d-aa90-430b-810c-f3973002fa32)\n", " Call ID: 204b107d-aa90-430b-810c-f3973002fa32\n", "  Args:\n", "    expression: (2+3j)^2\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: calculator\n", "\n", "Error: TypeError(\"unsupported operand type(s) for ^: 'complex' and 'int'\")\n", " Please fix your mistakes.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "I am sorry, I encountered an error. I will try again.\n", "Tool Calls:\n", "  calculator (14643440-9057-407f-9b24-0386889962f9)\n", " Call ID: 14643440-9057-407f-9b24-0386889962f9\n", "  Args:\n", "    expression: (2+3j)*(2+3j)\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: calculator\n", "\n", "(-5+12j)\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "(2+3i)^2 is -5+12j\n"]}], "source": ["from langchain_core.tools import StructuredTool\n", "\n", "def calculator(expression: str) -> str:\n", "    \"\"\"Calculates a single mathematical expression, incl. complex numbers.\"\"\"\n", "    return str(ne.evaluate(expression.strip(), local_dict={}))\n", "\n", "calculator_tool = StructuredTool.from_function(\n", "    func=calculator,\n", "    handle_tool_error=True\n", ")\n", "\n", "agent = create_react_agent(\n", "    llm, [calculator_tool])\n", "\n", "for event in agent.stream({\"messages\": [(\"user\", \"How much is (2+3i)^2\")]}, stream_mode=\"values\", config=config):\n", "    event[\"messages\"][-1].pretty_print()"]}], "metadata": {"colab": {"name": "custom_tools.ipynb", "provenance": []}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 5}