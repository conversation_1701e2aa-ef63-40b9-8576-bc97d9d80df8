{"components": [{"data": {"contents_checksum": "266dfaaf7e1083085e27196f449ea63fbd0aa547665d17d8d248895d4b197301", "source": "", "type": "tar"}, "dependencies": ["gcloud-crc32c"], "details": {"description": "Command line tool that calculates CRC32C hashes on local files.", "display_name": "Google Cloud CRC32C Hash Tool (Platform Specific)"}, "gdu_only": false, "id": "gcloud-crc32c-linux-x86_64", "is_configuration": false, "is_hidden": true, "is_required": false, "platform": {"architectures": ["x86_64"], "operating_systems": ["LINUX"]}, "platform_required": false, "version": {"build_number": 20250613150750, "version_string": "1.0.0"}}], "revision": 20250725161220, "schema_version": {"no_update": false, "url": "https://dl.google.com/dl/cloudsdk/channels/rapid/google-cloud-sdk.tar.gz", "version": 3}, "version": "532.0.0"}