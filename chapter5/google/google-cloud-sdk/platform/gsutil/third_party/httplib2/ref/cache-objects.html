<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
<head>
<link rel="STYLESHEET" href="ref.css" type='text/css' />
<link rel="first" href="ref.html" title='The httplib2 Library' />
<link rel='contents' href='contents.html' title="Contents" />
<link rel='last' href='about.html' title='About this document...' />
<link rel='help' href='about.html' title='About this document...' />
<link rel="next" href="response-objects.html" />
<link rel="prev" href="http-objects.html" />
<link rel="parent" href="module-httplib2.html" />
<link rel="next" href="response-objects.html" />
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name='aesop' content='information' />
<title>1.1.2 Cache Objects</title>
</head>
<body>
<div class="navigation">
<div id='top-navigation-panel' xml:id='top-navigation-panel'>
<table align="center" width="100%" cellpadding="0" cellspacing="2">
<tr>
<td class='online-navigation'><a rel="prev" title="1.1.1 http Objects"
  href="http-objects.html"><img src='previous.png'
  border='0' height='32'  alt='Previous Page' width='32' /></a></td>
<td class='online-navigation'><a rel="parent" title="1.1 httplib2 A comprehensive"
  href="module-httplib2.html"><img src='up.png'
  border='0' height='32'  alt='Up one Level' width='32' /></a></td>
<td class='online-navigation'><a rel="next" title="1.1.3 response Objects"
  href="response-objects.html"><img src='next.png'
  border='0' height='32'  alt='Next Page' width='32' /></a></td>
<td align="center" width="100%">The httplib2 Library</td>
<td class='online-navigation'><a rel="contents" title="Table of Contents"
  href="contents.html"><img src='contents.png'
  border='0' height='32'  alt='Contents' width='32' /></a></td>
<td class='online-navigation'><img src='blank.png'
  border='0' height='32'  alt='' width='32' /></td>
<td class='online-navigation'><img src='blank.png'
  border='0' height='32'  alt='' width='32' /></td>
</tr></table>
<div class='online-navigation'>
<b class="navlabel">Previous:</b>
<a class="sectref" rel="prev" href="http-objects.html">1.1.1 Http Objects</a>
<b class="navlabel">Up:</b>
<a class="sectref" rel="parent" href="module-httplib2.html">1.1 httplib2 A comprehensive</a>
<b class="navlabel">Next:</b>
<a class="sectref" rel="next" href="response-objects.html">1.1.3 Response Objects</a>
</div>
<hr /></div>
</div>
<!--End of Navigation Panel-->

<h2><a name="SECTION002120000000000000000"></a>
<a name="cache-objects"></a>
<br>
1.1.2 Cache Objects
</h2>

<p>
If you wish to supply your own caching implementation
then you will need to pass in an object that supports the
following methods. Note that the <tt class="module">memcache</tt> module
supports this interface natively.

<p>
<dl><dt><table cellpadding="0" cellspacing="0"><tr valign="baseline">
  <td><nobr><b><tt id='l2h-23' xml:id='l2h-23' class="method">get</tt></b>(</nobr></td>
  <td><var>key</var>)</td></tr></table></dt>
<dd>
Takes a string <var>key</var> and returns the value as a string.
</dl>

<p>
<dl><dt><table cellpadding="0" cellspacing="0"><tr valign="baseline">
  <td><nobr><b><tt id='l2h-24' xml:id='l2h-24' class="method">set</tt></b>(</nobr></td>
  <td><var>key, value</var>)</td></tr></table></dt>
<dd>
Takes a string <var>key</var> and <var>value</var> and stores it in the cache.
</dl>

<p>
<dl><dt><table cellpadding="0" cellspacing="0"><tr valign="baseline">
  <td><nobr><b><tt id='l2h-25' xml:id='l2h-25' class="method">delete</tt></b>(</nobr></td>
  <td><var>key</var>)</td></tr></table></dt>
<dd>
Deletes the cached value stored at <var>key</var>. The value
of <var>key</var> is a string.
</dl>

<p>

<div class="navigation">
<div class='online-navigation'>
<p></p><hr />
<table align="center" width="100%" cellpadding="0" cellspacing="2">
<tr>
<td class='online-navigation'><a rel="prev" title="1.1.1 http Objects"
  href="http-objects.html"><img src='previous.png'
  border='0' height='32'  alt='Previous Page' width='32' /></a></td>
<td class='online-navigation'><a rel="parent" title="1.1 httplib2 A comprehensive"
  href="module-httplib2.html"><img src='up.png'
  border='0' height='32'  alt='Up one Level' width='32' /></a></td>
<td class='online-navigation'><a rel="next" title="1.1.3 response Objects"
  href="response-objects.html"><img src='next.png'
  border='0' height='32'  alt='Next Page' width='32' /></a></td>
<td align="center" width="100%">The httplib2 Library</td>
<td class='online-navigation'><a rel="contents" title="Table of Contents"
  href="contents.html"><img src='contents.png'
  border='0' height='32'  alt='Contents' width='32' /></a></td>
<td class='online-navigation'><img src='blank.png'
  border='0' height='32'  alt='' width='32' /></td>
<td class='online-navigation'><img src='blank.png'
  border='0' height='32'  alt='' width='32' /></td>
</tr></table>
<div class='online-navigation'>
<b class="navlabel">Previous:</b>
<a class="sectref" rel="prev" href="http-objects.html">1.1.1 Http Objects</a>
<b class="navlabel">Up:</b>
<a class="sectref" rel="parent" href="module-httplib2.html">1.1 httplib2 A comprehensive</a>
<b class="navlabel">Next:</b>
<a class="sectref" rel="next" href="response-objects.html">1.1.3 Response Objects</a>
</div>
</div>
<hr />
<span class="release-info">Release 0.3, documentation updated on Mar 8, 2007.</span>
</div>
<!--End of Navigation Panel-->

</body>
</html>
