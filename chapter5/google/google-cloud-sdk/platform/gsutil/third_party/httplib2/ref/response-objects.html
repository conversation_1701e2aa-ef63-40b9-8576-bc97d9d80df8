<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
<head>
<link rel="STYLESHEET" href="ref.css" type='text/css' />
<link rel="first" href="ref.html" title='The httplib2 Library' />
<link rel='contents' href='contents.html' title="Contents" />
<link rel='last' href='about.html' title='About this document...' />
<link rel='help' href='about.html' title='About this document...' />
<link rel="next" href="httplib2-example.html" />
<link rel="prev" href="cache-objects.html" />
<link rel="parent" href="module-httplib2.html" />
<link rel="next" href="httplib2-example.html" />
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name='aesop' content='information' />
<title>1.1.3 Response Objects</title>
</head>
<body>
<div class="navigation">
<div id='top-navigation-panel' xml:id='top-navigation-panel'>
<table align="center" width="100%" cellpadding="0" cellspacing="2">
<tr>
<td class='online-navigation'><a rel="prev" title="1.1.2 cache Objects"
  href="cache-objects.html"><img src='previous.png'
  border='0' height='32'  alt='Previous Page' width='32' /></a></td>
<td class='online-navigation'><a rel="parent" title="1.1 httplib2 A comprehensive"
  href="module-httplib2.html"><img src='up.png'
  border='0' height='32'  alt='Up one Level' width='32' /></a></td>
<td class='online-navigation'><a rel="next" title="1.1.4 Examples"
  href="httplib2-example.html"><img src='next.png'
  border='0' height='32'  alt='Next Page' width='32' /></a></td>
<td align="center" width="100%">The httplib2 Library</td>
<td class='online-navigation'><a rel="contents" title="Table of Contents"
  href="contents.html"><img src='contents.png'
  border='0' height='32'  alt='Contents' width='32' /></a></td>
<td class='online-navigation'><img src='blank.png'
  border='0' height='32'  alt='' width='32' /></td>
<td class='online-navigation'><img src='blank.png'
  border='0' height='32'  alt='' width='32' /></td>
</tr></table>
<div class='online-navigation'>
<b class="navlabel">Previous:</b>
<a class="sectref" rel="prev" href="cache-objects.html">1.1.2 Cache Objects</a>
<b class="navlabel">Up:</b>
<a class="sectref" rel="parent" href="module-httplib2.html">1.1 httplib2 A comprehensive</a>
<b class="navlabel">Next:</b>
<a class="sectref" rel="next" href="httplib2-example.html">1.1.4 Examples</a>
</div>
<hr /></div>
</div>
<!--End of Navigation Panel-->

<h2><a name="SECTION002130000000000000000"></a>
<a name="response-objects"></a>
<br>
1.1.3 Response Objects
</h2>

<p>
Response objects are derived from <tt class="class">dict</tt> and map
header names (lower case with the trailing colon removed)
to header values. In addition to the dict methods
a Response object also has:

<p>
<dl><dt><b><tt id='l2h-26' xml:id='l2h-26' class="member">fromcache</tt></b></dt>
<dd>
If <code>true</code> the the response was returned from the cache.
</dl>

<p>
<dl><dt><b><tt id='l2h-27' xml:id='l2h-27' class="member">version</tt></b></dt>
<dd>
The version of HTTP that the server supports. A value
of 11 means '1.1'.
</dl>

<p>
<dl><dt><b><tt id='l2h-28' xml:id='l2h-28' class="member">status</tt></b></dt>
<dd>
The numerical HTTP status code returned in the response.
</dl>

<p>
<dl><dt><b><tt id='l2h-29' xml:id='l2h-29' class="member">reason</tt></b></dt>
<dd>
The human readable component of the HTTP response status code.
</dl>

<p>
<dl><dt><b><tt id='l2h-30' xml:id='l2h-30' class="member">previous</tt></b></dt>
<dd>
If redirects are followed then the <tt class="class">Response</tt> object returned
is just for the very last HTTP request and <var>previous</var> points to
the previous <tt class="class">Response</tt> object. In this manner they form a chain
going back through the responses to the very first response.
Will be <code>None</code> if there are no previous respones.
</dl>

<p>
The Response object also populates the header <code>content-location</code>, that
contains the URI that was ultimately requested. This is useful if
redirects were encountered, you can determine the ultimate URI that
the request was sent to. All Response objects contain this key value,
including <code>previous</code> responses so you can determine the entire
chain of redirects. If <tt class="member">Http.force_exception_to_status_code</tt> is <code>True</code>
and the number of redirects has exceeded the number of allowed number
of redirects then the <tt class="class">Response</tt> object will report the error
in the status code, but the complete chain of previous responses will
still be in tact.

<p>

<div class="navigation">
<div class='online-navigation'>
<p></p><hr />
<table align="center" width="100%" cellpadding="0" cellspacing="2">
<tr>
<td class='online-navigation'><a rel="prev" title="1.1.2 cache Objects"
  href="cache-objects.html"><img src='previous.png'
  border='0' height='32'  alt='Previous Page' width='32' /></a></td>
<td class='online-navigation'><a rel="parent" title="1.1 httplib2 A comprehensive"
  href="module-httplib2.html"><img src='up.png'
  border='0' height='32'  alt='Up one Level' width='32' /></a></td>
<td class='online-navigation'><a rel="next" title="1.1.4 Examples"
  href="httplib2-example.html"><img src='next.png'
  border='0' height='32'  alt='Next Page' width='32' /></a></td>
<td align="center" width="100%">The httplib2 Library</td>
<td class='online-navigation'><a rel="contents" title="Table of Contents"
  href="contents.html"><img src='contents.png'
  border='0' height='32'  alt='Contents' width='32' /></a></td>
<td class='online-navigation'><img src='blank.png'
  border='0' height='32'  alt='' width='32' /></td>
<td class='online-navigation'><img src='blank.png'
  border='0' height='32'  alt='' width='32' /></td>
</tr></table>
<div class='online-navigation'>
<b class="navlabel">Previous:</b>
<a class="sectref" rel="prev" href="cache-objects.html">1.1.2 Cache Objects</a>
<b class="navlabel">Up:</b>
<a class="sectref" rel="parent" href="module-httplib2.html">1.1 httplib2 A comprehensive</a>
<b class="navlabel">Next:</b>
<a class="sectref" rel="next" href="httplib2-example.html">1.1.4 Examples</a>
</div>
</div>
<hr />
<span class="release-info">Release 0.3, documentation updated on Mar 8, 2007.</span>
</div>
<!--End of Navigation Panel-->

</body>
</html>
