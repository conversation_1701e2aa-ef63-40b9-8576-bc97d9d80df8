<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
<head>
<link rel="STYLESHEET" href="ref.css" type='text/css' />
<link rel="first" href="ref.html" title='The httplib2 Library' />
<link rel='contents' href='contents.html' title="Contents" />
<link rel='last' href='about.html' title='About this document...' />
<link rel='help' href='about.html' title='About this document...' />
<link rel="next" href="contents.html" />
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name='aesop' content='information' />
<title>The httplib2 Library</title>
</head>
<body>
<div class="navigation">
<div id='top-navigation-panel' xml:id='top-navigation-panel'>
<table align="center" width="100%" cellpadding="0" cellspacing="2">
<tr>
<td class='online-navigation'><img src='previous.png'
  border='0' height='32'  alt='Previous Page' width='32' /></td>
<td class='online-navigation'><img src='up.png'
  border='0' height='32'  alt='Up one Level' width='32' /></td>
<td class='online-navigation'><a rel="next" title="Contents"
  href="contents.html"><img src='next.png'
  border='0' height='32'  alt='Next Page' width='32' /></a></td>
<td align="center" width="100%">The httplib2 Library</td>
<td class='online-navigation'><a rel="contents" title="Table of Contents"
  href="contents.html"><img src='contents.png'
  border='0' height='32'  alt='Contents' width='32' /></a></td>
<td class='online-navigation'><img src='blank.png'
  border='0' height='32'  alt='' width='32' /></td>
<td class='online-navigation'><img src='blank.png'
  border='0' height='32'  alt='' width='32' /></td>
</tr></table>
<div class='online-navigation'>
<b class="navlabel">Next:</b>
<a class="sectref" rel="next" href="contents.html">Contents</a>
</div>
<hr /></div>
</div>
<!--End of Navigation Panel-->

<p>

<div class="titlepage">
<div class='center'>
<h1>The httplib2 Library</h1>
<p><b><font size="+2">Joe Gregorio</font></b></p>
<p>
Email: <span class="email"><EMAIL></span>
</p>
<p><strong>Release 0.3</strong><br />
<strong>Mar 8, 2007</strong></p>
<p></p>
</div>
</div>

<p>

<h3>Abstract:</h3>
<div class="ABSTRACT">

<p>
The <tt class="module">httplib2</tt> module is a comprehensive HTTP client library
that handles caching, keep-alive, compression, redirects and
many kinds of authentication.

<p>
</div>
<p>

<p>

<p><br /></p><hr class='online-navigation' />
<div class='online-navigation'>
<!--Table of Child-Links-->
<a name="CHILD_LINKS"></a>

<ul class="ChildLinks">
<li><a href="contents.html">Contents</a>
<li><a href="node2.html">1. Reference</a>
<ul>
<li><a href="module-httplib2.html">1.1 <tt class="module">httplib2</tt>
A comprehensive HTTP client library.</a>
<ul>
<li><a href="http-objects.html">1.1.1 Http Objects</a>
<li><a href="cache-objects.html">1.1.2 Cache Objects</a>
<li><a href="response-objects.html">1.1.3 Response Objects</a>
<li><a href="httplib2-example.html">1.1.4 Examples</a>
</ul>
</ul>
<li><a href="about.html">About this document ...</a>
</ul>
<!--End of Table of Child-Links-->
</div>

<div class="navigation">
<div class='online-navigation'>
<p></p><hr />
<table align="center" width="100%" cellpadding="0" cellspacing="2">
<tr>
<td class='online-navigation'><img src='previous.png'
  border='0' height='32'  alt='Previous Page' width='32' /></td>
<td class='online-navigation'><img src='up.png'
  border='0' height='32'  alt='Up one Level' width='32' /></td>
<td class='online-navigation'><a rel="next" title="Contents"
  href="contents.html"><img src='next.png'
  border='0' height='32'  alt='Next Page' width='32' /></a></td>
<td align="center" width="100%">The httplib2 Library</td>
<td class='online-navigation'><a rel="contents" title="Table of Contents"
  href="contents.html"><img src='contents.png'
  border='0' height='32'  alt='Contents' width='32' /></a></td>
<td class='online-navigation'><img src='blank.png'
  border='0' height='32'  alt='' width='32' /></td>
<td class='online-navigation'><img src='blank.png'
  border='0' height='32'  alt='' width='32' /></td>
</tr></table>
<div class='online-navigation'>
<b class="navlabel">Next:</b>
<a class="sectref" rel="next" href="contents.html">Contents</a>
</div>
</div>
<hr />
<span class="release-info">Release 0.3, documentation updated on Mar 8, 2007.</span>
</div>
<!--End of Navigation Panel-->

</body>
</html>
