<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
<head>
<link rel="STYLESHEET" href="ref.css" type='text/css' />
<link rel="first" href="ref.html" title='The httplib2 Library' />
<link rel='contents' href='contents.html' title="Contents" />
<link rel='last' href='about.html' title='About this document...' />
<link rel='help' href='about.html' title='About this document...' />
<link rel="prev" href="node2.html" />
<link rel="parent" href="node2.html" />
<link rel="next" href="http-objects.html" />
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name='aesop' content='information' />
<title>1.1 httplib2 A comprehensive HTTP client library. </title>
</head>
<body>
<div class="navigation">
<div id='top-navigation-panel' xml:id='top-navigation-panel'>
<table align="center" width="100%" cellpadding="0" cellspacing="2">
<tr>
<td class='online-navigation'><a rel="prev" title="1. Reference"
  href="node2.html"><img src='previous.png'
  border='0' height='32'  alt='Previous Page' width='32' /></a></td>
<td class='online-navigation'><a rel="parent" title="1. Reference"
  href="node2.html"><img src='up.png'
  border='0' height='32'  alt='Up one Level' width='32' /></a></td>
<td class='online-navigation'><a rel="next" title="1.1.1 http Objects"
  href="http-objects.html"><img src='next.png'
  border='0' height='32'  alt='Next Page' width='32' /></a></td>
<td align="center" width="100%">The httplib2 Library</td>
<td class='online-navigation'><a rel="contents" title="Table of Contents"
  href="contents.html"><img src='contents.png'
  border='0' height='32'  alt='Contents' width='32' /></a></td>
<td class='online-navigation'><img src='blank.png'
  border='0' height='32'  alt='' width='32' /></td>
<td class='online-navigation'><img src='blank.png'
  border='0' height='32'  alt='' width='32' /></td>
</tr></table>
<div class='online-navigation'>
<b class="navlabel">Previous:</b>
<a class="sectref" rel="prev" href="node2.html">1. Reference</a>
<b class="navlabel">Up:</b>
<a class="sectref" rel="parent" href="node2.html">1. Reference</a>
<b class="navlabel">Next:</b>
<a class="sectref" rel="next" href="http-objects.html">1.1.1 Http Objects</a>
</div>
<hr /></div>
</div>
<!--End of Navigation Panel-->

<h1><a name="SECTION002100000000000000000">
1.1 <tt class="module">httplib2</tt>
         A comprehensive HTTP client library.  </a>
</h1>

<p>
<a name="module-httplib2"></a>
<p>

<p>

<p>
The <tt class="module">httplib2</tt> module is a comprehensive HTTP client library with the following features:

<p>
<dl>
<dt><strong>HTTP and HTTPS</strong></dt>
<dd>HTTPS support is only available if the socket module was compiled with SSL support.
</dd>
<dt><strong>Keep-Alive</strong></dt>
<dd>Supports HTTP 1.1 Keep-Alive, keeping the socket open and performing multiple requests over the same connection if possible.
</dd>
<dt><strong>Authentication</strong></dt>
<dd>The following three types of HTTP Authentication are supported. These can be used over both HTTP and HTTPS.

<ul>
<li>Digest
</li>
<li>Basic
</li>
<li>WSSE

</li>
</ul>
</dd>
<dt><strong>Caching</strong></dt>
<dd>The module can optionally operate with a private cache that understands the Cache-Control: header and uses both the ETag and Last-Modified cache validators.
</dd>
<dt><strong>All Methods</strong></dt>
<dd>The module can handle any HTTP request method, not just GET and POST.
</dd>
<dt><strong>Redirects</strong></dt>
<dd>Automatically follows 3XX redirects on GETs.
</dd>
<dt><strong>Compression</strong></dt>
<dd>Handles both 'deflate' and 'gzip' types of compression.
</dd>
<dt><strong>Proxies</strong></dt>
<dd>If the Socksipy module is installed then httplib2 can handle sock4, sock5 and http proxies.
</dd>
<dt><strong>Lost update support</strong></dt>
<dd>Automatically adds back ETags into PUT requests to resources we have already cached. This implements Section 3.2 of Detecting the Lost Update Problem Using Unreserved Checkout
</dd>
</dl>

<p>
The <tt class="module">httplib2</tt> module defines the following variables:

<p>
<dl><dt><b><tt id='l2h-2' xml:id='l2h-2'>debuglevel</tt></b></dt>
<dd>
The amount of debugging information to print. The default is 0.
</dd></dl>

<p>
The <tt class="module">httplib2</tt> module may raise the following Exceptions. Note that
there is an option that turns exceptions into
normal responses with an HTTP status code indicating
an error occured. See <tt class="member">Http.force_exception_to_status_code</tt>

<p>
<dl><dt><b><span class="typelabel">exception</span>&nbsp;<tt id='l2h-3' xml:id='l2h-3' class="exception">HttpLib2Error</tt></b></dt>
<dd>
The Base Exception for all exceptions raised by httplib2.
</dd></dl>

<p>
<dl><dt><b><span class="typelabel">exception</span>&nbsp;<tt id='l2h-4' xml:id='l2h-4' class="exception">RedirectMissingLocation</tt></b></dt>
<dd>
A 3xx redirect response code was provided but no Location: header
was provided to point to the new location.
</dd></dl>

<p>
<dl><dt><b><span class="typelabel">exception</span>&nbsp;<tt id='l2h-5' xml:id='l2h-5' class="exception">RedirectLimit</tt></b></dt>
<dd>
The maximum number of redirections was reached without coming to a final URI.
</dd></dl>

<p>
<dl><dt><b><span class="typelabel">exception</span>&nbsp;<tt id='l2h-6' xml:id='l2h-6' class="exception">ServerNotFoundError</tt></b></dt>
<dd>
Unable to resolve the host name given.
</dd></dl>

<p>
<dl><dt><b><span class="typelabel">exception</span>&nbsp;<tt id='l2h-7' xml:id='l2h-7' class="exception">RelativeURIError</tt></b></dt>
<dd>
A relative, as opposed to an absolute URI, was passed into request().
</dd></dl>

<p>
<dl><dt><b><span class="typelabel">exception</span>&nbsp;<tt id='l2h-8' xml:id='l2h-8' class="exception">FailedToDecompressContent</tt></b></dt>
<dd>
The headers claimed that the content of the response was compressed but the
decompression algorithm applied to the content failed.
</dd></dl>

<p>
<dl><dt><b><span class="typelabel">exception</span>&nbsp;<tt id='l2h-9' xml:id='l2h-9' class="exception">UnimplementedDigestAuthOptionError</tt></b></dt>
<dd>
The server requested a type of Digest authentication that we
are unfamiliar with.
</dd></dl>

<p>
<dl><dt><b><span class="typelabel">exception</span>&nbsp;<tt id='l2h-10' xml:id='l2h-10' class="exception">UnimplementedHmacDigestAuthOptionError</tt></b></dt>
<dd>
The server requested a type of HMACDigest authentication that we
are unfamiliar with.
</dd></dl>

<p>
<dl><dt><table cellpadding="0" cellspacing="0"><tr valign="baseline">
  <td><nobr><b><span class="typelabel">class</span>&nbsp;<tt id='l2h-11' xml:id='l2h-11' class="class">Http</tt></b>(</nobr></td>
  <td><var></var><big>[</big><var>cache=None</var><big>]</big><var>, </var><big>[</big><var>timeout=None</var><big>]</big><var>, </var><big>[</big><var>proxy_info=None</var><big>]</big><var></var>)</td></tr></table></dt>
<dd>
The class that represents a client HTTP interface.
The <var>cache</var> parameter is either the name of a directory
to be used as a flat file cache, or it must an object that
implements the required caching interface.
The <var>timeout</var> parameter is the socket level timeout.
The <var>proxy_info</var> is an instance of <tt class="class">ProxyInfo</tt> and is supplied
if a proxy is to be used. Note that the Socksipy module must be
installed for proxy support to work.
</dl>

<p>
<dl><dt><table cellpadding="0" cellspacing="0"><tr valign="baseline">
  <td><nobr><b><span class="typelabel">class</span>&nbsp;<tt id='l2h-12' xml:id='l2h-12' class="class">Response</tt></b>(</nobr></td>
  <td><var>info</var>)</td></tr></table></dt>
<dd>
Response is a subclass of <tt class="class">dict</tt> and instances of this
class are returned from calls
to Http.request. The <var>info</var> parameter is either
an <tt class="class">rfc822.Message</tt> or an <tt class="class">httplib.HTTPResponse</tt> object.
</dl>

<p>
<dl><dt><table cellpadding="0" cellspacing="0"><tr valign="baseline">
  <td><nobr><b><span class="typelabel">class</span>&nbsp;<tt id='l2h-13' xml:id='l2h-13' class="class">FileCache</tt></b>(</nobr></td>
  <td><var>dir_name, </var><big>[</big><var>safe=safename</var><big>]</big><var></var>)</td></tr></table></dt>
<dd>
FileCache implements a Cache as a directory of files.
The <var>dir_name</var> parameter is
the name of the directory to use. If the directory does
not exist then FileCache attempts to create the directory.
The optional <var>safe</var> parameter is a funtion which generates
the cache filename for each URI. A FileCache object is
constructed and used for caching when you pass a directory name
into the constructor of <tt class="class">Http</tt>.
</dl>

<p>
<dl><dt><table cellpadding="0" cellspacing="0"><tr valign="baseline">
  <td><nobr><b><span class="typelabel">class</span>&nbsp;<tt id='l2h-14' xml:id='l2h-14' class="class">ProxyInfo</tt></b>(</nobr></td>
  <td><var>proxy_type, proxy_host, proxy_port, </var><big>[</big><var>proxy_rdns=None</var><big>]</big><var>, </var><big>[</big><var>proxy_user=None</var><big>]</big><var>, </var><big>[</big><var>proxy_pass=None</var><big>]</big><var></var>)</td></tr></table></dt>
<dd>
The parameter <var>proxy_type</var> must be set to one of socks.PROXY_TYPE_XXX
constants. The <var>proxy_host</var> and <var>proxy_port</var> must be set to the location
of the proxy. The optional <var>proxy_rdns</var> should be set to True if
the DNS server on the proxy should be used. The <var>proxy_user</var> and
<var>proxy_pass</var> are supplied when the proxy is protected by authentication.
</dl>

<p>

<p><br /></p><hr class='online-navigation' />
<div class='online-navigation'>
<!--Table of Child-Links-->
<a name="CHILD_LINKS"><strong>Subsections</strong></a>

<ul class="ChildLinks">
<li><a href="http-objects.html">1.1.1 Http Objects</a>
<li><a href="cache-objects.html">1.1.2 Cache Objects</a>
<li><a href="response-objects.html">1.1.3 Response Objects</a>
<li><a href="httplib2-example.html">1.1.4 Examples</a>
</ul>
<!--End of Table of Child-Links-->
</div>

<div class="navigation">
<div class='online-navigation'>
<p></p><hr />
<table align="center" width="100%" cellpadding="0" cellspacing="2">
<tr>
<td class='online-navigation'><a rel="prev" title="1. Reference"
  href="node2.html"><img src='previous.png'
  border='0' height='32'  alt='Previous Page' width='32' /></a></td>
<td class='online-navigation'><a rel="parent" title="1. Reference"
  href="node2.html"><img src='up.png'
  border='0' height='32'  alt='Up one Level' width='32' /></a></td>
<td class='online-navigation'><a rel="next" title="1.1.1 http Objects"
  href="http-objects.html"><img src='next.png'
  border='0' height='32'  alt='Next Page' width='32' /></a></td>
<td align="center" width="100%">The httplib2 Library</td>
<td class='online-navigation'><a rel="contents" title="Table of Contents"
  href="contents.html"><img src='contents.png'
  border='0' height='32'  alt='Contents' width='32' /></a></td>
<td class='online-navigation'><img src='blank.png'
  border='0' height='32'  alt='' width='32' /></td>
<td class='online-navigation'><img src='blank.png'
  border='0' height='32'  alt='' width='32' /></td>
</tr></table>
<div class='online-navigation'>
<b class="navlabel">Previous:</b>
<a class="sectref" rel="prev" href="node2.html">1. Reference</a>
<b class="navlabel">Up:</b>
<a class="sectref" rel="parent" href="node2.html">1. Reference</a>
<b class="navlabel">Next:</b>
<a class="sectref" rel="next" href="http-objects.html">1.1.1 Http Objects</a>
</div>
</div>
<hr />
<span class="release-info">Release 0.3, documentation updated on Mar 8, 2007.</span>
</div>
<!--End of Navigation Panel-->

</body>
</html>
