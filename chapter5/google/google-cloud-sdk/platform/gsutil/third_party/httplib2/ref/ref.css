/*
 * The first part of this is the standard CSS generated by LaTeX2HTML,
 * with the "empty" declarations removed.
 */

/* Century Schoolbook font is very similar to Computer Modern Math: cmmi */
.math                   { font-family: "Century Schoolbook", serif; }
.math i                 { font-family: "Century Schoolbook", serif;
                          font-weight: bold }
.boldmath               { font-family: "Century Schoolbook", serif;
                          font-weight: bold }

/*
 * Implement both fixed-size and relative sizes.
 *
 * I think these can be safely removed, as it doesn't appear that
 * LaTeX2HTML ever generates these, even though these are carried
 * over from the LaTeX2HTML stylesheet.
 */
small.xtiny             { font-size : xx-small; }
small.tiny              { font-size : x-small; }
small.scriptsize        { font-size : smaller; }
small.footnotesize      { font-size : small; }
big.xlarge              { font-size : large; }
big.xxlarge             { font-size : x-large; }
big.huge                { font-size : larger; }
big.xhuge               { font-size : xx-large; }

/*
 * Document-specific styles come next;
 * these are added for the Python documentation.
 *
 * Note that the size specifications for the H* elements are because
 * Netscape on Solaris otherwise doesn't get it right; they all end up
 * the normal text size.
 */

body                    { color: #000000;
                          background-color: #ffffff; }

a:link:active           { color: #ff0000; }
a:link:hover            { background-color: #bbeeff; }
a:visited:hover         { background-color: #bbeeff; }
a:visited               { color: #551a8b; }
a:link                  { color: #0000bb; }

h1, h2, h3, h4, h5, h6  { font-family: avantgarde, sans-serif;
                          font-weight: bold; }
h1                      { font-size: 180%; }
h2                      { font-size: 150%; }
h3, h4                  { font-size: 120%; }

/* These are section titles used in navigation links, so make sure we
 * match the section header font here, even it not the weight.
 */
.sectref                { font-family: avantgarde, sans-serif; }
/* And the label before the titles in navigation: */
.navlabel               { font-size: 85%; }


/* LaTeX2HTML insists on inserting <br> elements into headers which
 * are marked with \label.  This little bit of CSS magic ensures that
 * these elements don't cause spurious whitespace to be added.
 */
h1>br, h2>br, h3>br,
h4>br, h5>br, h6>br     { display: none; }

code, tt                { font-family: "lucida typewriter", lucidatypewriter,
                                       monospace; }
var                     { font-family: times, serif;
                          font-style: italic;
                          font-weight: normal; }

.Unix                   { font-variant: small-caps; }

.typelabel              { font-family: lucida, sans-serif; }

.navigation td          { background-color: #99ccff;
                          font-weight: bold;
                          font-family: avantgarde, sans-serif;
                          font-size: 110%; }

div.warning             { background-color: #fffaf0;
                          border: thin solid black;
                          padding: 1em;
                          margin-left: 2em;
                          margin-right: 2em; }

div.warning .label      { font-family: sans-serif;
                          font-size: 110%;
                          margin-right: 0.5em; }

div.note                { background-color: #fffaf0;
                          border: thin solid black;
                          padding: 1em;
                          margin-left: 2em;
                          margin-right: 2em; }

div.note .label         { margin-right: 0.5em;
                          font-family: sans-serif; }

address                 { font-size: 80%; }
.release-info           { font-style: italic;
                          font-size: 80%; }

.titlegraphic           { vertical-align: top; }

.verbatim pre           { color: #00008b;
                          font-family: "lucida typewriter", lucidatypewriter,
                                       monospace;
                          font-size: 90%; }
.verbatim               { margin-left: 2em; }
.verbatim .footer       { padding: 0.05in;
                          font-size: 85%;
                          background-color: #99ccff;
                          margin-right: 0.5in; }

.grammar                { background-color: #99ccff;
                          margin-right: 0.5in;
                          padding: 0.05in; }
.grammar-footer         { padding: 0.05in;
                          font-size: 85%; }
.grammartoken           { font-family: "lucida typewriter", lucidatypewriter,
                                       monospace; }

.productions                  { background-color: #bbeeff; }
.productions a:active         { color: #ff0000; }
.productions a:link:hover     { background-color: #99ccff; }
.productions a:visited:hover  { background-color: #99ccff; }
.productions a:visited        { color: #551a8b; }
.productions a:link           { color: #0000bb; }
.productions table            { vertical-align: baseline;
                                empty-cells: show; }
.productions > table td,
.productions > table th       { padding: 2px; }
.productions > table td:first-child,
.productions > table td:last-child {
                                font-family: "lucida typewriter",
                                             lucidatypewriter,
                                             monospace;
                                }
/* same as the second selector above, but expressed differently for Opera */
.productions > table td:first-child + td + td {
                                font-family: "lucida typewriter",
                                             lucidatypewriter,
                                             monospace;
                                vertical-align: baseline;
                                }
.productions > table td:first-child + td {
                                padding-left: 1em;
                                padding-right: 1em;
                                }
.productions > table tr       { vertical-align: baseline; }

.email                  { font-family: avantgarde, sans-serif; }
.mailheader             { font-family: avantgarde, sans-serif; }
.mimetype               { font-family: avantgarde, sans-serif; }
.newsgroup              { font-family: avantgarde, sans-serif; }
.url                    { font-family: avantgarde, sans-serif; }
.file                   { font-family: avantgarde, sans-serif; }
.guilabel               { font-family: avantgarde, sans-serif; }

.realtable              { border-collapse: collapse;
                          border-color: black;
                          border-style: solid;
                          border-width: 0px 0px 2px 0px;
                          empty-cells: show;
                          margin-left: auto;
                          margin-right: auto;
                          padding-left: 0.4em;
                          padding-right: 0.4em;
                          }
.realtable tbody        { vertical-align: baseline; }
.realtable tfoot        { display: table-footer-group; }
.realtable thead        { background-color: #99ccff;
                          border-width: 0px 0px 2px 1px;
                          display: table-header-group;
                          font-family: avantgarde, sans-serif;
                          font-weight: bold;
                          vertical-align: baseline;
                          }
.realtable thead :first-child {
                          border-width: 0px 0px 2px 0px;
                          }
.realtable thead th     { border-width: 0px 0px 2px 1px }
.realtable td,
.realtable th           { border-color: black;
                          border-style: solid;
                          border-width: 0px 0px 1px 1px;
                          padding-left: 0.4em;
                          padding-right: 0.4em;
                          }
.realtable td:first-child,
.realtable th:first-child {
                          border-left-width: 0px;
                          vertical-align: baseline;
                          }
.center                 { text-align: center; }
.left                   { text-align: left; }
.right                  { text-align: right; }

.refcount-info          { font-style: italic; }
.refcount-info .value   { font-weight: bold;
                          color: #006600; }

/*
 * Some decoration for the "See also:" blocks, in part inspired by some of
 * the styling on Lars Marius Garshol's XSA pages.
 * (The blue in the navigation bars is #99CCFF.)
 */
.seealso                { background-color: #fffaf0;
                          border: thin solid black;
                          padding: 0pt 1em 4pt 1em; }

.seealso > .heading     { font-size: 110%;
                          font-weight: bold; }

/*
 * Class 'availability' is used for module availability statements at
 * the top of modules.
 */
.availability .platform { font-weight: bold; }


/*
 * Additional styles for the distutils package.
 */
.du-command             { font-family: monospace; }
.du-option              { font-family: avantgarde, sans-serif; }
.du-filevar             { font-family: avantgarde, sans-serif;
                          font-style: italic; }
.du-xxx:before          { content: "** ";
                          font-weight: bold; }
.du-xxx:after           { content: " **";
                          font-weight: bold; }


/*
 * Some specialization for printed output.
 */
@media print {
  .online-navigation    { display: none; }
  }
