name: publish
on:
  # on pull request, run build step to catch errors earlier, but do not publish
  pull_request:
  # on push to master without a version tag, publish to test PyPI registry
  # ... with version tag, publish to production PyPI registry.
  push:
    branches: [master]
    tags: ["v[0-9]*"]
jobs:
  build:
    # https://github.community/t/duplicate-checks-on-push-and-pull-request-simultaneous-event/18012/5
    if: ${{ github.event_name == 'push' || github.event.pull_request.head.repo.full_name != 'httplib2/httplib2' }}
    runs-on: ubuntu-latest
    timeout-minutes: 5

    steps:
      - uses: actions/checkout@v2

      - name: cache pip
        uses: actions/cache@v2
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-publish
          restore-keys: |
            ${{ runner.os }}-pip-
            ${{ runner.os }}-

      - name: setup python
        uses: actions/setup-python@v2
        with:
          python-version: 3.x
      - name: install tools
        run: pip install --upgrade build check-manifest setuptools twine wheel

      - name: build package
        run: python -m build
      - name: check manifest
        run: check-manifest
      - name: twine check
        run: twine check dist/*

      - uses: actions/upload-artifact@v2
        with:
          name: dist
          path: dist/
          if-no-files-found: error
          retention-days: 1

  publish-prod:
    if: ${{ github.repository == 'httplib2/httplib2' && startsWith(github.ref, 'refs/tags/') }}
    needs: [build]
    environment: pypi-public
    runs-on: ubuntu-latest
    timeout-minutes: 5

    steps:
      - name: cache pip
        uses: actions/cache@v2
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-publish
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: setup python
        uses: actions/setup-python@v2
        with:
          python-version: 3.x
      - name: install tools
        run: pip install twine

      - uses: actions/download-artifact@v2
        with:
          name: dist
          path: dist/

      - name: twine check
        run: twine check dist/*

      - name: twine upload (prod)
        if: startsWith(github.ref, 'refs/tags/')
        env:
          TWINE_USERNAME: "__token__"
          TWINE_PASSWORD: ${{ secrets.PYPI_TOKEN }}
        run: twine upload --non-interactive --verbose dist/*

  publish-test:
    if: ${{ github.repository == 'httplib2/httplib2' && github.ref == 'refs/heads/master' }}
    needs: [build]
    environment: pypi-test
    runs-on: ubuntu-latest
    timeout-minutes: 5

    steps:
      - name: cache pip
        uses: actions/cache@v2
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-publish
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: setup python
        uses: actions/setup-python@v2
        with:
          python-version: 3.x
      - name: install tools
        run: pip install twine

      - uses: actions/download-artifact@v2
        with:
          name: dist
          path: dist/

      - name: twine upload (test)
        if: github.ref == 'refs/heads/master'
        env:
          TWINE_USERNAME: "__token__"
          TWINE_PASSWORD: ${{ secrets.PYPI_TOKEN }}
          TWINE_REPOSITORY: "testpypi"
        run: echo twine upload --non-interactive --skip-existing --verbose dist/*
