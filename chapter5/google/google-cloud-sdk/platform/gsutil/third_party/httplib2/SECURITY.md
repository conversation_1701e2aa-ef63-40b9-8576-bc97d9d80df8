# Security Policy

## Supported Versions

master branch and latest release get priority support. You should expect all known problems fixed in master.

All other released versions receive security updates per request.
If you use some old version and can not upgrade for any or no reason, ask for security update release, most likely you will get it.

## Reporting a Vulnerability

Contact current maintainers. At 2020-05: <EMAIL> or https://t.me/temotor
If that doesn't work, open Github issue just asking for private communication channel.

This is volunteer maintained project, all issues are processed on best effort basis, no deadlines promised. Of course, security vulnerabilities get priority over regular issues.

You can expect fame in history or maybe you prefer anonymity - say what you prefer.

Thank you for responsible handling of security problems. Your attention and effort are appreciated.
