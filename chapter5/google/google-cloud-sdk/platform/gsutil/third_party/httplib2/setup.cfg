# [bdist_wheel]
# universal = 1
# FIXME universal wheels don't roll well with our 2/3 split code base
# https://github.com/httplib2/httplib2/pull/29

[coverage:run]

[flake8]
exclude = *.egg*,.env,.git,.tox,_*,build*,dist*,venv*,python2/,python3/
ignore = E261,E731,W503
max-line-length = 121

[tool:pytest]
minversion = 3.2
addopts =
  # --fulltrace
  # -n auto
  --cov-config=setup.cfg
  --cov=httplib2
  --ignore=tests/_TODO_legacy/
  --noconftest
  --showlocals
  --strict-markers
  --tb=short
  --timeout=17
  --verbose
  -ra
