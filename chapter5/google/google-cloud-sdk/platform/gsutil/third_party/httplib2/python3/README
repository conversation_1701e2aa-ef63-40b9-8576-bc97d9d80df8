httplib2 for Python 3

This directory contains a port of httplib2 to Python 3. As you may
know, Python 3 is not backward-compatible with Python 2. The biggest
change in Python 3 (that affects httplib2) is the distinction between
bytes and strings.

To successfully use http2lib for Python 3, you absolutely must
understand the following sentence:

** THE RESPONSE HEADERS ARE STRINGS, BUT THE CONTENT BODY IS BYTES **


Example:

>>> import httplib2, pprint
>>> h = httplib2.Http(".cache")
>>> (resp_headers, content) = h.request("http://example.org/", "GET")
>>> pprint.pprint(resp_headers)
{'accept-ranges': 'bytes',
 'connection': 'close',
 'content-length': '438',
 'content-location': 'http://example.org/',
 'content-type': 'text/html; charset=UTF-8',
 'date': 'Fri, 29 May 2009 03:57:29 GMT',
 'etag': '"b80f4-1b6-80bfd280"',
 'last-modified': '<PERSON><PERSON>, 15 Nov 2005 13:24:10 GMT',
 'server': 'Apache/2.2.3 (CentOS)',
 'status': '200'}
>>> type(content)
<class 'bytes'>
>>> content[:49]
b'<HTML>\r\n<HEAD>\r\n  <TITLE>Example Web Page</TITLE>'


Further reading:

  * http://diveintopython3.org/strings.html
  * http://docs.python.org/3.0/whatsnew/3.0.html#text-vs-data-instead-of-unicode-vs-8-bit
  * http://docs.python.org/3.0/howto/unicode.html


--------------------------------------------------------------------
Httplib2 Software License

Copyright (c) 2006 by Joe Gregorio
Copyright (c) 2009 by Mark Pilgrim

Permission is hereby granted, free of charge, to any person 
obtaining a copy of this software and associated documentation 
files (the "Software"), to deal in the Software without restriction, 
including without limitation the rights to use, copy, modify, merge, 
publish, distribute, sublicense, and/or sell copies of the Software, 
and to permit persons to whom the Software is furnished to do so, 
subject to the following conditions:

The above copyright notice and this permission notice shall be 
included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, 
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES 
OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND 
NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS 
BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN 
ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN 
CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE 
SOFTWARE.

