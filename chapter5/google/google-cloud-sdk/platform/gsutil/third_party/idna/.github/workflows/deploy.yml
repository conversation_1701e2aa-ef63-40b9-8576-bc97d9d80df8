# Adapted from https://packaging.python.org/en/latest/guides/publishing-package-distribution-releases-using-github-actions-ci-cd-workflows/

name: Publish to Py<PERSON>
on: push
permissions:
  contents: read

jobs:

  build:
    name: Build distribution
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4
    - name: Set up Python
      uses: actions/setup-python@39cd14951b08e74b54015e9e001cdefcf80e669f # v5
      with:
        python-version: "3.x"
    - name: Install pypa/build
      run: python3 -m pip install build --user
    - name: Build a binary wheel and a source tarball
      run: python3 -m build
    - name: Store the distribution packages
      uses: actions/upload-artifact@834a144ee995460fba8ed112a2fc961b36a5ec5a # v4
      with:
        name: python-package-distributions
        path: dist/

  publish-to-pypi:
    name: >-
      Publish to Py<PERSON>
    if: startsWith(github.ref, 'refs/tags/')  # only publish to PyPI on tag pushes
    needs:
    - build
    runs-on: ubuntu-latest
    environment:
      name: pypi
      url: https://pypi.org/p/idna  # Replace <package-name> with your PyPI project name
    permissions:
      id-token: write  # IMPORTANT: mandatory for trusted publishing

    steps:
    - name: Download all the dists
      uses: actions/download-artifact@fa0a91b85d4f404e444e00e005971372dc801d16 # v4
      with:
        name: python-package-distributions
        path: dist/
    - name: Publish distribution to PyPI
      uses: pypa/gh-action-pypi-publish@ec4db0b4ddc65acdf4bff5fa45ac92d78b56bdf0 # release/v1

  github-release:
    name: Sign and upload GitHub Release
    needs:
    - publish-to-pypi
    runs-on: ubuntu-latest

    permissions:
      contents: write  # IMPORTANT: mandatory for making GitHub Releases
      id-token: write  # IMPORTANT: mandatory for sigstore

    steps:
    - name: Download the dists
      uses: actions/download-artifact@fa0a91b85d4f404e444e00e005971372dc801d16 # v4
      with:
        name: python-package-distributions
        path: dist/
    - name: Sign with Sigstore
      uses: sigstore/gh-action-sigstore-python@f514d46b907ebcd5bedc05145c03b69c1edd8b46 # v3.0.0
      with:
        inputs: >-
          ./dist/*.tar.gz
          ./dist/*.whl
    - name: Create GitHub Release
      env:
        GITHUB_TOKEN: ${{ github.token }}
      run: >-
        gh release create
        '${{ github.ref_name }}'
        --repo '${{ github.repository }}'
        --notes ""
    - name: Upload artifact signatures to GitHub Release
      env:
        GITHUB_TOKEN: ${{ github.token }}
      # Upload to GitHub Release using the `gh` CLI.
      # `dist/` contains the built packages, and the
      # sigstore-produced signatures and certificates.
      run: >-
        gh release upload
        '${{ github.ref_name }}' dist/**
        --repo '${{ github.repository }}'

#  publish-to-testpypi:
#    name: Publish to Test PyPI
#    needs:
#    - build
#    runs-on: ubuntu-latest

#    environment:
#      name: testpypi
#      url: https://test.pypi.org/p/idna

#    permissions:
#      id-token: write  # IMPORTANT: mandatory for trusted publishing

#    steps:
#    - name: Download all the dists
#      uses: actions/download-artifact@v4
#      with:
#        name: python-package-distributions
#        path: dist/
#    - name: Publish distribution to TestPyPI
#      uses: pypa/gh-action-pypi-publish@ec4db0b4ddc65acdf4bff5fa45ac92d78b56bdf0 # release/v1
#      with:
#        verbose: true
#        print-hash: true
#        repository-url: https://test.pypi.org/legacy/
