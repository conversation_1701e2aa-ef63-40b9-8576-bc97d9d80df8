# This workflow will install Python dependencies, run tests and lint with a variety of Python versions
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions

name: Python package

on:
  - push
  - pull_request

permissions:
  contents: read

jobs:
  mypy:
    strategy:
      fail-fast: false
      matrix:
        python-version:
          - '3.8'
          - '3.9'
          - '3.10'
          - '3.11'
          - '3.12'
          - '3.13'
    runs-on: ${{ matrix.os || 'ubuntu-latest' }}
    steps:
      - uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@39cd14951b08e74b54015e9e001cdefcf80e669f # v5.1.1
        with:
          python-version: ${{ matrix.python-version }}
          allow-prereleases: true
      - name: Install dependencies
        run: python -m pip install ruff mypy
      - name: Check code style with ruff
        run: |
          ruff format --check idna tests
          ruff check idna tests
      - name: Check types with mypy
        run: mypy --strict idna

  build:
    strategy:
      fail-fast: false
      matrix:
        python-version:
          - '3.7'
          - '3.8'
          - '3.9'
          - '3.10'
          - '3.11'
          - '3.12'
          - '3.13'
          - 'pypy-3.9'
        include:
          - python-version: 3.6
            os: ubuntu-20.04
    runs-on: ${{ matrix.os || 'ubuntu-latest' }}
    steps:
    - uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@39cd14951b08e74b54015e9e001cdefcf80e669f # v5.1.1
      with:
        python-version: ${{ matrix.python-version }}
        allow-prereleases: true
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        python -m pip install flake8 pytest
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
    - name: Lint with flake8
      run: |
        # stop the build if there are Python syntax errors or undefined names
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
    - name: Test with pytest
      run: |
        pytest
