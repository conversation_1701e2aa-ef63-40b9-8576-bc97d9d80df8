[build-system]
requires = ["flit_core >=3.2,<4"]
build-backend = "flit_core.buildapi"

[project]
name = "idna"
description = "Internationalized Domain Names in Applications (IDNA)"
readme = "README.rst"
license = {file = "LICENSE.md"}
authors = [
  {name = "<PERSON>", email = "<EMAIL>"}
]
classifiers = [
  "Development Status :: 5 - Production/Stable",
  "Intended Audience :: Developers",
  "Intended Audience :: System Administrators",
  "License :: OSI Approved :: BSD License",
  "Operating System :: OS Independent",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3 :: Only",
  "Programming Language :: Python :: 3.6",
  "Programming Language :: Python :: 3.7",
  "Programming Language :: Python :: 3.8",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Programming Language :: Python :: 3.13",
  "Programming Language :: Python :: Implementation :: CPython",
  "Programming Language :: Python :: Implementation :: PyPy",
  "Topic :: Internet :: Name Service (DNS)",
  "Topic :: Software Development :: Libraries :: Python Modules",
  "Topic :: Utilities",
]
requires-python = ">=3.6"
dynamic = ["version"]

[project.urls]
"Source" = "https://github.com/kjd/idna"
"Changelog" = "https://github.com/kjd/idna/blob/master/HISTORY.rst"
"Issue tracker" = "https://github.com/kjd/idna/issues"

[tool.flit.sdist]
exclude = [".gitignore", ".github/"]
include = ["tests", "tools", "HISTORY.rst"]

[project.optional-dependencies]
all = [
    "ruff >= 0.6.2",
    "mypy >= 1.11.2",
    "pytest >= 8.3.2",
    "flake8 >= 7.1.1",
]

[tool.ruff]
line-length = 127
[tool.ruff.lint]
extend-select = [
    "I",            # isort
]
