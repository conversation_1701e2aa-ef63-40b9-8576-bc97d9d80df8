Featured projects
=================

Did you liked how ``charset-normalizer`` perform? and its quality?
You may be interested in those other project maintained by the same authors.
We aim to serve the opensource community the best and as inclusively as we can, no matter
your level or opinions.

Niquests
--------

Started as a simple though.. IE 11 has built-in HTTP/2 support while Requests 2.32 does not!

Most of our programs that interact with HTTP server are built with ``requests`` and
we aren't likely to switch without a substantial effort.

We just might die at any moment, no notice whatsoever, knowingly that as a Python developer,
we never interacted with a HTTP/2 over TCP or HTTP/3 over QUIC capable server in 2023...

.. image:: https://dabuttonfactory.com/button.png?t=Get+Niquests+Now&f=Ubuntu-Bold&ts=26&tc=fff&hp=45&vp=20&c=11&bgt=unicolored&bgc=15d798&be=1
   :target: https://github.com/jawah/niquests

It is a fork of ``requests`` and no breaking changes are to be expected. We made sure that
your migration is effortless and safe.

httpie-next
-----------

Easy solution are cool, let us introduce you to HTTPie but with built-in support
for HTTP/2 and HTTP/3.
It is made available as a plugin, no effort required beside installing the said plugin.

Enjoy HTTPie refreshed!

.. image:: https://dabuttonfactory.com/button.png?t=Get+HTTPie-Next+Now&f=Ubuntu-Bold&ts=26&tc=fff&hp=45&vp=20&c=11&bgt=unicolored&bgc=15d798&be=1
   :target: https://github.com/Ousret/httpie-next

Wassima
-------

Did you ever wonder how would it feel like to leave the headache with root CAs (certificate authority)?
Well, you may, starting today, use your operating system trusted root CAs to verify
peer certificates with the at most comfort.

It is enabled by default in Niquests, but you can use that awesome feature by itself.

.. image:: https://dabuttonfactory.com/button.png?t=OS+root+CAs+for+Python&f=Ubuntu-Bold&ts=26&tc=fff&hp=45&vp=20&c=11&bgt=unicolored&bgc=15d798&be=1
   :target: https://github.com/jawah/wassima

The solution is universal and served for (almost) every possible case.
You may remove the certifi package, let it rest in peace.
