---
name: Bug report
about: Create a report to help us fix something bad like an exception
title: "[BUG]"
labels: bug, help wanted
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug/exception is.

**To Reproduce**
Give us the target text file. Host it somewhere with untouched encoding.

**Expected behavior**
A clear and concise description of what you expected to happen.

**Logs**
If applicable, add console outputs to help explain your problem.

**Desktop (please complete the following information):**
 - OS: [e.g. Linux, Windows or Mac]
 - Python version [e.g. 3.5]
 - Package version [eg. 2.0.0]

**Additional context**
Add any other context about the problem here.
