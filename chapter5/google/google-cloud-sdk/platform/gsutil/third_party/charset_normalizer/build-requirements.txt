# in the meantime we migrate to pyproject.toml
# this represent the minimum requirement to build (for the optional speedup)
--find-links https://github.com/mypyc/mypy_mypyc-wheels/releases/expanded_assets/v1.12.0+dev.b2deaaecf1a11e13bc962558992b5f2d5701f295
mypy==1.11.2; python_version >= '3.8' and python_version < '3.13'
mypy==1.12.0; python_version >= '3.13'
mypy==1.4.1; python_version < '3.8'
build>=0.10.0,<2
wheel==0.42.0
setuptools>=68,<76
