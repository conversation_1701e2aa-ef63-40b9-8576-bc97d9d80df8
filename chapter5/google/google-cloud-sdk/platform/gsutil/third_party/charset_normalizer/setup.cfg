[metadata]
name = charset-normalizer
description = The Real First Universal Charset Detector. Open, modern and actively maintained alternative to Chardet.
long_description = file: README.md, CHANGELOG.md, LICENSE
long_description_content_type = text/markdown
keywords = encoding, charset, charset-detector, detector, normalization, unicode, chardet, detect
url = https://github.com/Ousret/charset_normalizer
license = MIT
author_email = <EMAIL>
author = Ahmed TAHRI
project_urls =
    Bug Reports = https://github.com/Ousret/charset_normalizer/issues
    Documentation = https://charset-normalizer.readthedocs.io/en/latest
classifiers =
    Development Status :: 5 - Production/Stable
    License :: OSI Approved :: MIT License
    Intended Audience :: Developers
    Topic :: Software Development :: Libraries :: Python Modules
    Operating System :: OS Independent
    Programming Language :: Python
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3.7
    Programming Language :: Python :: 3.8
    Programming Language :: Python :: 3.9
    Programming Language :: Python :: 3.10
    Programming Language :: Python :: 3.11
    Programming Language :: Python :: 3.12
    Programming Language :: Python :: 3.13
    Programming Language :: Python :: Implementation :: PyPy
    Topic :: Text Processing :: Linguistic
    Topic :: Utilities
    Typing :: Typed

[options.packages.find]
exclude =
    tests
    *.tests
    *.tests.*
    tests.*
    docs*
    data*

[options.extras_require]
unicode_backport =

[options.entry_points]
console_scripts =
    normalizer = charset_normalizer.cli:cli_detect

[options]
packages = find:
include_package_data = True
python_requires = >=3.7.0

[options.package_data]
charset_normalizer = py.typed

[tool:pytest]
addopts = --cov=charset_normalizer --cov-report=term-missing -rxXs

[flake8]
ignore = W503, E203, B305
max-line-length = 120

[mypy]
disallow_untyped_defs = True
ignore_missing_imports = True

[tool:isort]
profile = black
combine_as_imports = True
