0.14:
  - <PERSON>ow providing a custom exception logger
    to 'locked' decorator
  - <PERSON><PERSON> providing a custom logger to process
    lock class
  - Fix issue #12
0.13:
  - Fix 'ensure_tree' check on freebsd
0.12:
  - Use a tiny retry util helper class
    for performing process locking
    retries.
0.11:
  - Directly use monotonic.monotonic.
  - Use BLATHER level for previously
    INFO/DEBUG statements.
0.10:
  - Add LICENSE in generated source tarballs
  - Add a version.py file that can be used to
    extract the current version.
0.9:
  - Allow providing a non-standard (eventlet or
    other condition class) to the r/w lock for
    cases where it is useful to do so.
  - Instead of having the r/w lock take a find
    eventlet keyword argument, allow for it to
    be provided a function that will be later
    called to get the current thread. This allows
    for the current *hack* to be easily removed
    by users (if they so desire).
0.8:
  - Add fastener logo (from openclipart).
  - Ensure r/w writer -> reader -> writer
    lock acquisition.
  - Attempt to use the monotonic pypi module
    if its installed for monotonically increasing
    time on python versions where this is not
    built-in.
0.7:
  - Add helpful `locked` decorator that can
    lock a method using a found attribute (a lock
    object or list of lock objects) in the
    instance the method is attached to.
  - Expose top level `try_lock` function.
0.6:
  - Allow the sleep function to be provided (so that
    various alternatives other than time.sleep can
    be used), ie eventlet.sleep (or other).
  - Remove dependency on oslo.utils (replace with
    small utility code that achieves the same effect).
0.5:
  - Make it possible to provide a acquisition timeout
    to the interprocess lock (which when acquisition
    can not complete in the desired time will return
    false).
0.4:
  - Have the interprocess lock acquire take a blocking
    keyword argument (defaulting to true) that can avoid
    blocking trying to acquire the lock
0.3:
  - Renamed from 'shared_lock' to 'fasteners'
0.2.1
  - Fix delay not working as expected
0.2:
  - Add a interprocess lock
0.1:
  - Add travis yaml file
  - Initial commit/import
