# Blunderbuss config
#
# This file controls who is assigned for pull requests and issues.
# Note: This file is autogenerated. To make changes to the assignee
# team, please update `codeowner_team` in `.repo-metadata.json`.
assign_issues:
  - googleapis/googleapis-auth

assign_issues_by:
  - labels:
      - "samples"
    to:
      - googleapis/python-samples-reviewers
      - googleapis/googleapis-auth

assign_prs:
  - googleapis/googleapis-auth
