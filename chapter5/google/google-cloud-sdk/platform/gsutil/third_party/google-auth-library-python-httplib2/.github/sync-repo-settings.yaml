# https://github.com/googleapis/repo-automation-bots/tree/main/packages/sync-repo-settings
# Rules for main branch protection
branchProtectionRules:
# Identifies the protection rule pattern. Name of the branch to be protected.
# Defaults to `main`
- pattern: main
  requiresCodeOwnerReviews: true
  requiresStrictStatusChecks: true
  requiredStatusCheckContexts:
    - 'cla/google'
    - 'OwlBot Post Processor'
    - 'Kokoro'
permissionRules:
  - team: actools-python
    permission: admin
  - team: actools
    permission: admin
  - team: yoshi-python
    permission: push
