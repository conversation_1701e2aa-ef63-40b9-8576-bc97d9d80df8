# Changelog

## [0.2.0](https://github.com/googleapis/google-auth-library-python-httplib2/compare/v0.1.1...v0.2.0) (2023-12-12)


### Features

* Add support for Python 3.12 ([#126](https://github.com/googleapis/google-auth-library-python-httplib2/issues/126)) ([9a110a6](https://github.com/googleapis/google-auth-library-python-httplib2/commit/9a110a6b509f44cfd359211094a6f609f47bd6ce))


### Dependencies

* Remove third-party mock library ([#124](https://github.com/googleapis/google-auth-library-python-httplib2/issues/124)) ([ea0c7c6](https://github.com/googleapis/google-auth-library-python-httplib2/commit/ea0c7c699f45846346fa11597e353d6afed7c829))

## [0.1.1](https://github.com/googleapis/google-auth-library-python-httplib2/compare/v0.1.0...v0.1.1) (2023-08-21)


### Bug Fixes

* Update setup.py ([#114](https://github.com/googleapis/google-auth-library-python-httplib2/issues/114)) ([d6a0e3d](https://github.com/googleapis/google-auth-library-python-httplib2/commit/d6a0e3d1afb43f95d9de18f25100d32c7303c1fd))

## [0.1.0](https://www.github.com/googleapis/google-auth-library-python-httplib2/compare/v0.0.3...v0.1.0) (2021-03-01)


### Features

* add close method ([#14](https://www.github.com/googleapis/google-auth-library-python-httplib2/issues/14)) ([feda187](https://www.github.com/googleapis/google-auth-library-python-httplib2/commit/feda187133beeb656fdd7f30ed124ed1e428a74a))
* expose a few httplib2 properties and a method ([#9](https://www.github.com/googleapis/google-auth-library-python-httplib2/issues/9)) ([e3aa44e](https://www.github.com/googleapis/google-auth-library-python-httplib2/commit/e3aa44e01e2987989671467c7a022ea33829eb2f))
