[tox]
envlist = lint,py27,py34,py35,py36,pypy,cover

[testenv]
deps =
  flask
  pytest
  pytest-cov
  pytest-localserver
  httplib2
commands =
  py.test --cov=google_auth_httplib2 --cov=tests {posargs:tests}

[testenv:cover]
basepython = python3.6
commands =
  py.test --cov=google_auth_httplib2 --cov=tests --cov-report= tests
  coverage report --show-missing --fail-under=100
deps =
  {[testenv]deps}

[testenv:lint]
basepython = python3.5
commands =
  python setup.py check --metadata --restructuredtext --strict
  flake8 \
    --import-order-style=google \
    --application-import-names="google_auth_httplib2,tests" \
    google_auth_httplib2.py tests
  gcp-devrel-py-tools run-pylint \
    --config pylint.config.py \
    --library-filesets google_auth_httplib2.py \
    --test-filesets tests
deps =
  flake8
  flake8-import-order
  pylint
  docutils
  gcp-devrel-py-tools
