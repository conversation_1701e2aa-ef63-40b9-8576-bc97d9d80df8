# Build artifacts
*.py[cod]
__pycache__
*.egg-info/
build/
dist/

# Documentation-related
docs/_build

# Test files
.nox/
.tox/
.cache/

# Django test database
db.sqlite3

# Coverage files
.coverage
coverage.xml
nosetests.xml
htmlcov/

# Files with private / local data
scripts/local_test_setup
tests/data/key.json
tests/data/key.p12
tests/data/user-key.json

# PyCharm configuration:
.idea

# Generated files
pylintrc
pylintrc.test

# Test logs
coverage.xml
*sponge_log.xml
