# -*- coding: utf-8 -*-
#
# Copyright 2024 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Generated by synthtool. DO NOT EDIT!
[flake8]
ignore = E203, E231, E266, E501, W503
exclude =
  # Exclude generated code.
  **/proto/**
  **/gapic/**
  **/services/**
  **/types/**
  *_pb2.py

  # Standard linting exemptions.
  **/.nox/**
  __pycache__,
  .git,
  *.pyc,
  conf.py
