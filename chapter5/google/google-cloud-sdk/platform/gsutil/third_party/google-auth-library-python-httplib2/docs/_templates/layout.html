
{% extends "!layout.html" %}
{%- block content %}
{%- if theme_fixed_sidebar|lower == 'true' %}
  <div class="document">
    {{ sidebar() }}
    {%- block document %}
      <div class="documentwrapper">
      {%- if render_sidebar %}
        <div class="bodywrapper">
      {%- endif %}

          {%- block relbar_top %}
            {%- if theme_show_relbar_top|tobool %}
              <div class="related top">
                &nbsp;
                {{- rellink_markup () }}
              </div>
            {%- endif %}
          {% endblock %}

          <div class="body" role="main">
          	<div class="admonition" id="python2-eol"> 
          	 As of January 1, 2020 this library no longer supports Python 2 on the latest released version. 
          	 Library versions released prior to that date will continue to be available. For more information please
          	 visit <a href="https://cloud.google.com/python/docs/python2-sunset/">Python 2 support on Google Cloud</a>.
          	</div>
            {% block body %} {% endblock %}
          </div>

          {%- block relbar_bottom %}
            {%- if theme_show_relbar_bottom|tobool %}
              <div class="related bottom">
                &nbsp;
                {{- rellink_markup () }}
              </div>
            {%- endif %}
          {% endblock %}

      {%- if render_sidebar %}
        </div>
      {%- endif %}
      </div>
    {%- endblock %}
    <div class="clearer"></div>
  </div>
{%- else %}
{{ super() }}
{%- endif %}
{%- endblock %}
