====================================
 Mock - Mocking and Testing Library
====================================

:Version: |release|
:Date: |today|
:Homepage: `Mock Homepage`_
:Download: `Mock on PyPI`_
:Documentation: `Python Docs`_
:License: `BSD License`_
:Support: `Mailing list (<EMAIL>)
 <http://lists.idyll.org/listinfo/testing-in-python>`_
:Issue tracker: `Github Issues
 <https://github.com/testing-cabal/mock/issues>`_
:Last sync: cb6aab1248c4aec4dd578bea717854505a6fb55d

.. _Mock Homepage: https://github.com/testing-cabal/mock
.. _BSD License: http://github.com/testing-cabal/mock/blob/master/LICENSE.txt
.. _Python Docs: https://docs.python.org/dev/library/unittest.mock.html

.. module:: mock
   :synopsis: Mock object and testing library.

.. index:: introduction

TOC
+++

.. toctree::
   :maxdepth: 2

   changelog

Introduction
++++++++++++

mock is a library for testing in Python. It allows you to replace parts of
your system under test with mock objects and make assertions about how they
have been used.

mock is now part of the Python standard library, available as
``unittest.mock`` in Python 3.3 onwards. However, if you are writing code that
runs on multiple versions of Python the ``mock`` package is better, as you get
the newest features from the latest release of Python available for all
Pythons.

The ``mock`` package contains a rolling backport of the standard library mock
code compatible with Python 2.6 and up, and 3.3 and up. Python 3.2 is supported
by mock 1.3.0 and below - with pip no longer supporting 3.2, we cannot test
against that version anymore.

Please see the standard library documentation for usage details.

.. index:: installing
.. _installing:

Installing
++++++++++

The current version is |release|. Mock is stable and widely used.

* `mock on PyPI <http://pypi.python.org/pypi/mock>`_

.. index:: repository
.. index:: git

You can checkout the latest development version from Github
repository with the following command:

    ``git clone https://github.com/testing-cabal/mock``


.. index:: pip

You can install mock with pip:

    | ``pip install -U mock``

Alternatively you can download the mock distribution from PyPI and after
unpacking run:

   ``python setup.py install``


.. index:: bug reports

Bug Reports
+++++++++++

Mock uses `unittest2 <http://pypi.python.org/pypi/unittest2>`_ for its own
Issues with the backport process, such as compatibility with a particular
Python, should be reported to the `bug tracker
<https://github.com/testing-cabal/mock/issues>`_. Feature requests and issues
with Mock functionality should be reported to the `Python bug tracker
<https://bugs.python.org>`_.

.. index:: python changes

Python Changes
++++++++++++++

Python NEWS entries from cPython:

.. include:: ../NEWS

.. index:: older versions

Older Versions of Python
++++++++++++++++++++++++

Version 1.0.1 is the last version compatible with Python < 2.6.

.. index:: maintainer notes

Maintainer Notes
++++++++++++++++

Development
===========

Checkout from git (see :ref:`installing`) and submit pull requests.

Committers can just push as desired: since all semantic development takes
place in cPython, the backport process is as lightweight as we can make it.

mock is CI tested using Travis-CI on Python versions 2.6, 2.7, 3.3, 3.4,
3.5, nightly Python 3 builds, pypy, pypy3. Jython support is desired, if
someone could contribute a patch to .travis.yml to support it that would be
excellent.

Releasing
=========

NB: please use semver. Bump the major component on API breaks, minor on all
non-bugfix changes, patch on bugfix only changes.

1. tag -s, push --tags origin master
2. setup.py sdist bdist_wheel upload -s


Backporting rules
=================

isinstance checks in cPython to ``type`` need to check ``ClassTypes``.
Code calling ``obj.isidentifier`` needs to change to ``_isidentifier(obj)``.

Backporting process
===================

1. Patch your git am with `my patch <https://github.com/rbtcollins/git>`_.
2. Install the applypatch-transform hook from tools/ to your .git hooks dir.
3. Configure a pre-applypatch hook to test at least all the cPython versions
   we support on each patch that is applied. I use containers, and a sample
   script is in tools/pre-applypatch.
4. Pull down the cPython git mirror: https://github.com/python/cpython.git
5. Export the new revisions since the ``Last sync`` at the top of this
   document::

     revs=${lastsync}
     rm migrate-export
     git log --pretty="format:%H " $revs.. -- Lib/unittest/mock.py \
       Lib/unittest/test/testmock/ > migrate-revs
     tac migrate-revs > migrate-sorted-revs
     for rev in $(< migrate-sorted-revs); do
           git format-patch -1 $rev -k --stdout >> migrate-export;
           done
     echo NEW SYNC POINT: $(git rev-parse HEAD)

6. Import into mock::

     git am -k --reject $path-to-cpython/migrate-export

   This will transform the patches automatically. Currently it will error
   on every NEWS change as I haven't gotten around to making those patches
   automatic. Fixup any errors that occur. When the patch is ready, do a ``git
   add -u`` to update the index and then ``git am --continue`` to move onto
   the next patch. If the patch is inappropriate e.g. the patch removing
   __ne__ which would break older pythons, then either do ``git reset --hard;
   git am --skip`` to discard any partially applied changes and skip over it,
   or, if it has a NEWS entry thats worth preserving, edit it down to just
   that, with a note such as we have for the ``__ne__`` patch, and continue on
   from there.

   The goal is that every patch work at all times.

7. After the import is complete, update this document with the new sync point.

8. Push to a personal branch and propose a PR to the main repo. This will make
   Travis-CI test it. If it works, push to the main repo.
