mock is a library for testing in Python. It allows you to replace parts of
your system under test with mock objects and make assertions about how they
have been used.

mock is now part of the Python standard library, available as `unittest.mock
<https://docs.python.org/dev/library/unittest.mock.html>`_ in Python 3.3
onwards.

This package contains a rolling backport of the standard library mock code
compatible with Python 2.6 and up, and 3.3 and up.

Please see the standard library documentation for more details.

:Homepage: `<PERSON>ck Homepage`_
:Download: `Mock on PyPI`_
:Documentation: `Python Docs`_
:License: `BSD License`_
:Support: `Mailing list (<EMAIL>)
 <http://lists.idyll.org/listinfo/testing-in-python>`_
:Issue tracker: `Github Issues
 <https://github.com/testing-cabal/mock/issues>`_
:Build status:
  .. image:: https://travis-ci.org/testing-cabal/mock.svg?branch=master
      :target: https://travis-ci.org/testing-cabal/mock

.. _Mock Homepage: https://github.com/testing-cabal/mock
.. _BSD License: http://github.com/testing-cabal/mock/blob/master/LICENSE.txt
.. _Python Docs: https://docs.python.org/dev/library/unittest.mock.html
.. _mock on PyPI: http://pypi.python.org/pypi/mock
