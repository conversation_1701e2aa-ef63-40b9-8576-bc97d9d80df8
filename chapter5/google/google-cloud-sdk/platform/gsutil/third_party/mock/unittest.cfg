
[unittest]
plugins = 
    unittest2.plugins.debugger
    unittest2.plugins.checker
    unittest2.plugins.doctestloader
    unittest2.plugins.matchregexp
    unittest2.plugins.moduleloading
    unittest2.plugins.testcoverage
    unittest2.plugins.growl
    unittest2.plugins.filtertests
    unittest2.plugins.junitxml
    unittest2.plugins.timed
    unittest2.plugins.counttests
    unittest2.plugins.logchannels

excluded-plugins =

# 0, 1 or 2 (default is 1)
# quiet, normal or verbose
# can be overriden at command line
verbosity = normal

# true or false
# even if false can be switched on at command line
catch =
buffer =
failfast =


[matchregexp]
always-on = False
full-path = True

[debugger]
always-on = False
errors-only = True

[coverage]
always-on = False
config =
report-html = False
# only used if report-html is false
annotate = False
# defaults to './htmlcov/'
html-directory =
# if unset will output to console
text-file =
branch = False
timid = False
cover-pylib = False
exclude-lines = 
    # Have to re-enable the standard pragma
    pragma: no cover

    # Don't complain about missing debug-only code:
    def __repr__
    if self\.debug

    # Don't complain if tests don't hit defensive assertion code:
    raise AssertionError
    raise NotImplementedError

    # Don't complain if non-runnable code isn't run:
    if 0:
    if __name__ == .__main__.
    
ignore-errors = False
modules =

[growl]
always-on = False

[doctest]
always-on = False

[module-loading]
always-on = False

[checker]
always-on = False
pep8 = False
pyflakes = True

[junit-xml]
always-on = False
path = junit.xml

[timed]
always-on = True
threshold = 0.01

[count]
always-on = True
enhanced = False
