#!/bin/bash
#
# An example hook script to verify what is about to be committed
# by applypatch from an e-mail message.
#
# The hook should exit with non-zero status after issuing an
# appropriate message if it wants to stop the commit.
#
# To enable this hook, rename this file to "pre-applypatch".

set -eu

#. git-sh-setup
echo "** in hook **"

function test_version {
  version=$1
  host=$(ls ~/.virtualenvs/mock-$version-* -d | sed -e "s/^.*mock-$version-//")
  if [ -z "$host" ]; then
    echo "No host found for $version"
    return 1
  fi
  echo testing $version in virtualenv mock-$version-$host on ssh host $host
  ssh $host "cd work/mock && . ~/.virtualenvs/mock-$version-$host/bin/activate && pip install .[test] && unit2"
}

find . -name "*.pyc" -exec rm "{}" \;

test_version 2.6
test_version 2.7
test_version 3.3
test_version 3.4
test_version 3.5
test_version cpython
test_version pypy

echo '** pre-apply complete and successful **'
