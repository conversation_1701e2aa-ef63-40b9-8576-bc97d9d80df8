[metadata]
name = mock
summary = Rolling backport of unittest.mock for all Pythons
home-page = https://github.com/testing-cabal/mock
description-file = README.rst
author = Testing Cabal
author-email = <EMAIL>
classifier = 
    Development Status :: 5 - Production/Stable
    Environment :: Console
    Intended Audience :: Developers
    License :: OSI Approved :: BSD License
    Operating System :: OS Independent
    Programming Language :: Python
    Programming Language :: Python :: 2
    Programming Language :: Python :: 2.6
    Programming Language :: Python :: 2.7
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3.2
    Programming Language :: Python :: 3.3
    Programming Language :: Python :: 3.4
    Programming Language :: Python :: 3.5
    Programming Language :: Python :: Implementation :: CPython
    Programming Language :: Python :: Implementation :: Jython
    Programming Language :: Python :: Implementation :: PyPy
    Topic :: Software Development :: Libraries
    Topic :: Software Development :: Libraries :: Python Modules
    Topic :: Software Development :: Testing
keyword =
    testing, test, mock, mocking, unittest, patching, stubs, fakes, doubles

[extras]
test =
  unittest2>=1.1.0
docs =
  jinja2<2.7:python_version<"3.3" and python_version>="3"
  Pygments<2:python_version<"3.3" and python_version>="3"
  sphinx<1.3:python_version<"3.3" and python_version>="3"
  sphinx:python_version<"3" or python_version>="3.3"

[files]
packages = mock

[bdist_wheel]
universal = 1
