sudo: false
language: python
python:
  - "2.6"
  - "2.7"
  - "3.3"
  - "3.4"
  - pypy
  - pypy3
matrix:
  include:
# Travis nightly look to be 3.5.0a4, b3 is out and the syntax error we see
# doesn't happen in trunk.
    - python: "nightly"
      env: SKIP_DOCS=1
install:
 - pip install -U pip
 - pip install -U wheel setuptools
 - pip install -U .[docs,test]
 - pip list
 - python --version
script:
 - unit2
 - if [ -z "$SKIP_DOCS" ]; then python setup.py build_sphinx; fi
 - rst2html.py --strict README.rst README.html
