[tox]
envlist = py25,py26,py27,py31,pypy,py32,py33,jython

[testenv]
deps=unittest2
commands={envbindir}/unit2 discover []

[testenv:py26]
commands=
    {envbindir}/unit2 discover []
    {envbindir}/sphinx-build -E -b doctest docs html
    {envbindir}/sphinx-build -E docs html
deps =
    unittest2
    sphinx

[testenv:py27]
commands=
    {envbindir}/unit2 discover []
    {envbindir}/sphinx-build -E -b doctest docs html
deps =
    unittest2
    sphinx

[testenv:py31]
deps =
    unittest2py3k

[testenv:py32]
commands=
    {envbindir}/python -m unittest discover []
deps =

[testenv:py33]
commands=
    {envbindir}/python -m unittest discover []
deps =

# note for jython. Execute in tests directory:
# rm `find . -name '*$py.class'`