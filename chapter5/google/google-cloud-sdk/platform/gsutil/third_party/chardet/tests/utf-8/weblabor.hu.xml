<?xml version="1.0" encoding="utf-8"?>
<!--
Source: http://weblabor.hu/rss
Expect: UTF-8
-->
<!DOCTYPE rss [<!ENTITY % HTMLlat1 PUBLIC "-//W3C//ENTITIES Latin 1 for XHTML//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml-lat1.ent">]>
<rss version="2.0" xml:base="http://weblabor.hu">
<channel>
 <title>Weblabor - a fejlesztői forrás</title>
 <link>http://weblabor.hu</link>
 <description></description>
 <language>hu</language>
<item>
 <title>Webfejlesztőt keresünk</title>
 <link>http://weblabor.hu/forumok/temak/11167</link>
 <description>Az Antikkönyv.hu Kft. webfejlesztőt keres:&lt;br /&gt;
&lt;br /&gt;
Feladat:A cég saját és ügyfelei részére webes alkalmazások fejlesztése, technikai támogatás nyújtása.&lt;br /&gt;
&lt;br /&gt;
Elvárások:&lt;br /&gt;
    * tapasztalattal rendelkező webes megoldás fejlesztő&lt;br /&gt;
    * HTML, Javascript, PHP, MySQL, Dreamweaver, Flash, Photoshop ismeretek&lt;br /&gt;
&lt;br /&gt;
Előny: munkabírás, ügyfélorientált magatartás, csapatmunka, jó kommunikációs készség&lt;br /&gt;
&lt;br /&gt;
Előnyt jelent:Delphi ismeretek&lt;br /&gt;
&lt;br /&gt;
Amit kínálunk:Sok munka, kihívás, dinamikus és fiatalos környezet&lt;br /&gt;
&lt;br /&gt;
&lt;br /&gt;
További információért keress minket az alábbi elérhetőségeken:&lt;br /&gt;
Kovács Adrienn&lt;br /&gt;
Tel.: 362-1286&lt;br /&gt;
Fax: 362-5355&lt;br /&gt;
&lt;br /&gt;
Jelentkezéshez küldd el önéletrajzodat az info##kukac##antikkonyv.hu címre!&lt;br /&gt;
&lt;br /&gt;
Várjuk jelentkezésedet!</description>
 <category domain="http://weblabor.hu/forumok/vegyes/munkaallas">Munka / Állás</category>
 <pubDate>Wed, 04 Jan 2006 17:19:44 +0100</pubDate>
</item>
<item>
 <title>Hányadik héten van egy dátum (PHP-ben)?</title>
 <link>http://weblabor.hu/forumok/temak/11166</link>
 <description>Sziasztok,&lt;br /&gt;
&lt;br /&gt;
PHP-ban hogyan lehet megkapni, hogy egy adott dátum a hónap hányadik hetére esik? Van erre valamilyen egyszerű módszer? Súgóban nem találtam semmit erre.&lt;br /&gt;
&lt;br /&gt;
Köszi előre is!!</description>
 <category domain="http://weblabor.hu/forumok/php/php">PHP</category>
 <pubDate>Wed, 04 Jan 2006 17:41:40 +0100</pubDate>
</item>
<item>
 <title>Surface level tips for good API writing</title>
 <link>http://weblabor.hu/linkek/11133</link>
 <description>Hogyan írjunk API-t</description>
 <category domain="http://weblabor.hu/linkek/blogmarkok">Blogmarkok</category>
 <pubDate>Wed, 04 Jan 2006 16:19:17 +0100</pubDate>
</item>
<item>
 <title>Basic webstandards Workshop</title>
 <link>http://weblabor.hu/linkek/11117</link>
 <description>A Max Design prezentációja a szabványok okos használatáról</description>
 <category domain="http://weblabor.hu/linkek/blogmarkok">Blogmarkok</category>
 <pubDate>Wed, 04 Jan 2006 15:17:28 +0100</pubDate>
</item>
<item>
 <title>textarea nem küld több mint 22 sort</title>
 <link>http://weblabor.hu/forumok/temak/11165</link>
 <description>Sziasztok!&lt;br /&gt;
&lt;br /&gt;
Van egy visszajelzési ürlapom de a textarea csak k.b 2000 karaktert küld el többet nem hajlandó. Lehet hogy a www.uw.hu szereverén korlátozva van a szövegmenyiség?&lt;br /&gt;
&lt;br /&gt;
A leveélküldés egy mailer.php es egy index.html ből áll.&lt;br /&gt;
 Mi lehet az oka?</description>
 <category domain="http://weblabor.hu/forumok/php/levelkuldes">Levélküldés és fogadás PHP-ben</category>
 <pubDate>Wed, 04 Jan 2006 15:14:13 +0100</pubDate>
</item>
<item>
 <title>Kép méretarányos átméretezése PHP-vel</title>
 <link>http://weblabor.hu/forumok/temak/11164</link>
 <description>Sziasztok! &lt;br /&gt;
nekem olyan kérdésem lenne, hogy hogyan lehet php-vel kép méretét méretarányosan csökkenteni? lehet-e ilyet? és ha igen, hogyan?&lt;br /&gt;
előre is thx.</description>
 <category domain="http://weblabor.hu/forumok/php/php">PHP</category>
 <pubDate>Wed, 04 Jan 2006 14:43:22 +0100</pubDate>
</item>
<item>
 <title>The Anatomy of Web Fonts</title>
 <link>http://weblabor.hu/linkek/11151</link>
 <description>Typográfiai és színkeverési alapok.</description>
 <category domain="http://weblabor.hu/linkek/blogmarkok">Blogmarkok</category>
 <pubDate>Wed, 04 Jan 2006 14:38:56 +0100</pubDate>
</item>
<item>
 <title>Változók értékének összeadása for ciklusban</title>
 <link>http://weblabor.hu/forumok/temak/11163</link>
 <description>Sziasztok!&lt;br /&gt;
&lt;br /&gt;
A következő problémába futottam bele:&lt;br /&gt;
&lt;br /&gt;
Van mondjuk három változóm, ami egész számot tartalmaz, ha ezeket szeretném összeadni, ami alaphelyzetben nem is jelent gondot:&amp;lt;?php$x1=1;$x2=2;$x3=3;$x_full=$x1+$x2+$x3;és akkor a $x_full értéke 6 lesz. &lt;br /&gt;
&lt;br /&gt;
Igen ám, de az x-ek száma változó (űrlapból érkeznek, lehet háromnál több is, kevesebb is), így arra gondoltam, hogy megoldom for ciklussal:&amp;lt;?php$x1=1;$x2=2;$x3=3;//x-ek száma:$x_num=3;for($i=1; $i&amp;lt;=$x_num; $i++) { $x=&quot;x&quot;.$i; $x_full.=+$$x; }Viszont így nem összeadja, hanem összefűzi őket, tehát $x_full értéke 123. Próbálkoztam azzal is, hogy a $$x értékét átalakítom számmá, hátha ez a gond, de ez sem segített. &lt;br /&gt;
&lt;br /&gt;
Előre is köszi a tippeket, hogy mit ronthattam el.</description>
 <category domain="http://weblabor.hu/forumok/php/php">PHP</category>
 <pubDate>Wed, 04 Jan 2006 14:44:29 +0100</pubDate>
</item>
<item>
 <title>HTML kód fájl letöltéshez fa struktúrában?</title>
 <link>http://weblabor.hu/forumok/temak/11162</link>
 <description>sziasztok.&lt;br /&gt;
&lt;br /&gt;
tudnatok irni egy szintaxist ami lehetove teszi a html en keresztul a tarhelyrol valo file letoltest? mondjuk ugy hogy megjelennek a filenevek fa-strukturaban?&lt;br /&gt;
&lt;br /&gt;
vagy tudtok tippet adni hol keresgeljek?&lt;br /&gt;
&lt;br /&gt;
koszi&lt;br /&gt;
&lt;br /&gt;
Balazs</description>
 <category domain="http://weblabor.hu/forumok/web/xhtmlajax">[X]HTML / AJAX / Flash</category>
 <pubDate>Wed, 04 Jan 2006 14:14:33 +0100</pubDate>
</item>
<item>
 <title>Cool URIs don&#039;t change</title>
 <link>http://weblabor.hu/linkek/11156</link>
 <description>Az aggregátorokat megzavarhatja a link változása</description>
 <category domain="http://weblabor.hu/linkek/blogmarkok">Blogmarkok</category>
 <pubDate>Wed, 04 Jan 2006 13:36:17 +0100</pubDate>
</item>
<item>
 <title>Nem kerül be a tartományok táblámba az adat (PHP, MySQL)</title>
 <link>http://weblabor.hu/forumok/temak/11161</link>
 <description>hali lenne egy ilyen php scriptem. az a baj hogy nem írja be az adatokat az adatbázisba. A kapcsolat az létrejön de nem írja be valamiért az adatokat valaki segítheten !&amp;lt;?php$felhasznalo=&quot;******&quot;;$jelszo=&quot;******&quot;;$adatbazis=&quot;adatbazis1&quot;;$kapcsolat=mysql_connect(&quot;localhost&quot;,&quot;$felhasznalo&quot;,&quot;$jelszo&quot;);mysql_select_db(&#039;adatbazis1&#039;);$query = &#039;SELECT * FROM tartomanyok&#039;;$result = mysql_query($query) or die(&#039;Query failed: &#039; . mysql_error());$parancs=&quot;insert into tartomanyok (tartomany,nem,email)values (&#039;123xyz.com&#039;,&#039;F&#039;,&#039;dfc##kukac##dfg.hu&#039;)&quot;;mysql_close( $kapcsolat )?&amp;gt;</description>
 <category domain="http://weblabor.hu/forumok/php/php">PHP</category>
 <pubDate>Wed, 04 Jan 2006 14:00:49 +0100</pubDate>
</item>
<item>
 <title>text input mezőben összeg tagolt megjelenítése</title>
 <link>http://weblabor.hu/forumok/temak/11159</link>
 <description>Sziasztok!&lt;br /&gt;
&lt;br /&gt;
Kedvenc megrendelőm előállt egy olyan kéréssel, hogy egy input mezőben, ahova egy Ft összeget kell beírni, már beírás közben tagolva jelenjen meg, tehát pl. így: 1.584.744, mondván, hogy áttekinthetőbb az űrlapot kitöltő számára. Lehetséges ez egyáltalán? Ráadásul, hogy csavarjak még egyet rajta, jó lenne ha az űrlap elküldésekor ez ennek ellenére tagolás nélkül menne tovább, mert számként tárolnám le az adatbázisban (műveleteket kell vele később végeznem), bár ezt utóbbit még lehet, hogy meg tudom oldani php-vel, ha egyszerűbb megoldás nincs.&lt;br /&gt;
&lt;br /&gt;
Előre is köszi a tippeket, ha van rá megoldás, vagy a megrendelőm felé továbbítható megerősítést, ha nincs.</description>
 <category domain="http://weblabor.hu/forumok/web/xhtmlajax">[X]HTML / AJAX / Flash</category>
 <pubDate>Wed, 04 Jan 2006 12:47:08 +0100</pubDate>
</item>
<item>
 <title>Graphic design plays a minor role on the Web</title>
 <link>http://weblabor.hu/linkek/11110</link>
 <description>Még mindig sokan nem értik meg, hogy a weblapok nem tv hirdetések</description>
 <category domain="http://weblabor.hu/linkek/blogmarkok">Blogmarkok</category>
 <pubDate>Wed, 04 Jan 2006 14:23:13 +0100</pubDate>
</item>
<item>
 <title>The Web 2.0 Quiz</title>
 <link>http://weblabor.hu/linkek/11143</link>
 <description>Szerinted ismered a Web 2.0-t?</description>
 <category domain="http://weblabor.hu/linkek/blogmarkok">Blogmarkok</category>
 <pubDate>Wed, 04 Jan 2006 11:31:01 +0100</pubDate>
</item>
<item>
 <title>Három aktív PHP verzió lehet használatban 2006-ban</title>
 <link>http://weblabor.hu/hirek/20060104/haromphpverzio</link>
 <description>Anonim beküldőnk hívta fel a figyelmünket az Internetnews PHP: Three Versions, One Promise című cikkére, mely felveti a kérdést: problémát jelent-e majd 2006-ban a megjelentetni tervezett PHP 6 illetve a még el sem terjedt PHP 5 és a széles körben használt PHP 4 együttélése. Könnyen előfordulhat az is, hogy a PHP 5-re frissítést tervezők kivárják a modern karakterkódolást és dátum kezelést is biztosító PHP 6-os érkezését. Az is érdekes kérdés, hogy miként tudják majd biztosítani a hoszting szolgáltatók mindhárom verziót felhasználóik számára.</description>
 <category domain="http://weblabor.hu/hirek/rovatok/php">PHP</category>
 <pubDate>Wed, 04 Jan 2006 11:15:54 +0100</pubDate>
</item>
</channel>
</rss>
