<?xml version="1.0" encoding="utf-8"?>
<!--
Source: http://weblabor.hu/node/feed
Expect: UTF-8
-->
<!DOCTYPE rss [<!ENTITY % HTMLlat1 PUBLIC "-//W3C//ENTITIES Latin 1 for XHTML//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml-lat1.ent">]>
<rss version="2.0" xml:base="http://weblabor.hu">
<channel>
 <title>Weblabor - a fejlesztői forrás</title>
 <link>http://weblabor.hu</link>
 <description></description>
 <language>hu</language>
<item>
 <title>Három aktív PHP verzió lehet használatban 2006-ban</title>
 <link>http://weblabor.hu/hirek/20060104/haromphpverzio</link>
 <description>Anonim beküldőnk hívta fel a figyelmünket az Internetnews PHP: Three Versions, One Promise című cikkére, mely felveti a kérdést: problémát jelent-e majd 2006-ban a megjelentetni tervezett PHP 6 illetve a még el sem terjedt PHP 5 és a széles körben használt PHP 4 együttélése. Könnyen előfordulhat az is, hogy a PHP 5-re frissítést tervezők kivárják a modern karakterkódolást és dátum kezelést is biztosító PHP 6-os érkezését. Az is érdekes kérdés, hogy miként tudják majd biztosítani a hoszting szolgáltatók mindhárom verziót felhasználóik számára.</description>
 <category domain="http://weblabor.hu/hirek/rovatok/php">PHP</category>
 <pubDate>Wed, 04 Jan 2006 11:15:54 +0100</pubDate>
</item>
<item>
 <title>Hijax: tervezés AJAX-ra gondolva</title>
 <link>http://weblabor.hu/hirek/20060104/hijax</link>
 <description>Az AJAX immár mindennapos varázsszó, a legújabb technológiák zászlóshajója. Jeremy Keith azonban úgy gondolja, hogy nem egy új technológiaként kellene rá tekintenünk, hanem csupán egy kiegészítésre, amely valamivel több lehetőséget ad a webes alkalmazások kialakításában. Véleménye szerint az a megközelítés eleve elhibázott, hogy alkalmazásokra gondolva az AJAX nélküli működést teljesen el is felejtjük, a dokumentum alapú web eszméjét mintegy a szőnyeg alá söpörve. Nem mehetünk el amellett, hogy a web még mindig dokumentumokból áll, még ha azok néha alkalmazásnak tűnnek is.</description>
 <category domain="http://weblabor.hu/hirek/rovatok/felulet">Felület</category>
 <pubDate>Wed, 04 Jan 2006 09:21:33 +0100</pubDate>
</item>
<item>
 <title>Új év a NetVibes-nál</title>
 <link>http://weblabor.hu/hirek/20060103/ujevnetvibes</link>
 <description>Novemberi beszámolónkat követően csönd övezte a NetVibes házatáját, azonban az utóbbi időkben ismét felpezsdült az élet a projekt körül. Ahogy már korábban blogmarkban jeleztük, a Todo modul segítéségével  teendőinket tarthatjuk nyilván, míg a Yahoo mail komponenssel a GMail-rivális web 2.0 levezőszoftvert használóknak járt a kedvükben a fejlesztőgárda.</description>
 <category domain="http://weblabor.hu/hirek/rovatok/felulet">Felület</category>
 <pubDate>Tue, 03 Jan 2006 18:37:22 +0100</pubDate>
</item>
<item>
 <title>Bevezetés a keresőoptimalizálásba</title>
 <link>http://weblabor.hu/hirek/20060103/seointro</link>
 <description>A napokban bukkantam rá a seomoz Beginner&amp;#039;s Guide to SEO című dokumentumára, ami egy kivételesen összeszedett és részletes bevezető a keresőoptimalizálásba. Véleményem szerint nagyon jó megközelítést ad a témához, a szokványos kereső kifejezésekre optimalizálás mellett jól kiemelve a felhasználókra optimalizálás fontosságát, az értékes tartalom szerepét. Különösen érdekes a webhely népszerűsítésre vonatkozó tippek listája.</description>
 <category domain="http://weblabor.hu/hirek/rovatok/kereso">Kereső</category>
 <pubDate>Tue, 03 Jan 2006 17:07:10 +0100</pubDate>
</item>
<item>
 <title>Közös rajztábla</title>
 <link>http://weblabor.hu/hirek/20060102/kozosrajztabla</link>
 <description>Goba küldte be nemrégiben a linket, mely egy érdekes alkalmazását mutatta be a közös alkotásnak, egy weblapba ágyazott SVG részlet segítségével. Érdekes kísérlet, láttunk már hasonlót, mégsem láttam még azt, ami itt bekövetkezett: előtört mindenkiből a gyermeki véna, és hihetetlen alkotások születtek percek alatt...</description>
 <category domain="http://weblabor.hu/hirek/rovatok/web">Web</category>
 <pubDate>Tue, 03 Jan 2006 11:00:28 +0100</pubDate>
</item>
<item>
 <title>Klikkelés követés AJAX segítségével</title>
 <link>http://weblabor.hu/hirek/20060102/ajaxlinktracker</link>
 <description>Sokak számára okoz gondot, hogy a webhelyhez kapcsolódó statisztikát nem tudják használható stratégiává formálni a tartalom javítását illetően. A link kattintások rögzítésével ugyanis azok weblaphoz kapcsolódó jelentősége elvész. Nem tudhatjuk pontosan, hogy a megfelelő prezentáció, a link több helyen történő ismétlése, esetleg egy jól sikerült cikk vezetett ahhoz, hogy többen klikkeltek egy adott linkre. Glenn Jones egy ötletes megoldást adott erre.</description>
 <category domain="http://weblabor.hu/hirek/rovatok/felulet">Felület</category>
 <pubDate>Mon, 02 Jan 2006 09:16:23 +0100</pubDate>
</item>
<item>
 <title>Firefox memória felszabadítása ablak minimalizáláskor</title>
 <link>http://weblabor.hu/hirek/20060102/firefoxmemmin</link>
 <description>A Firefoxnak nagy tudása és sok funkciója mellett van egy elég rossz tulajdonsága is: viszonylag sok memóriát képes lefoglalni magának. Manapság már a böngésző folyamatos használatban van, így gondolom hozzám hasonlóan mások se zárják be a programot, amíg más alkalmazással dolgoznak, legfeljebb minimalizálják az ablakát. Más programokkal ellentétben azonban a Firefox ilyenkor az általa foglalt fizikai memóriát nem szabadítja fel.</description>
 <category domain="http://weblabor.hu/hirek/rovatok/bongeszo">Böngésző</category>
 <pubDate>Mon, 02 Jan 2006 14:46:59 +0100</pubDate>
</item>
<item>
 <title>Canvas támogatás Internet Explorerben?</title>
 <link>http://weblabor.hu/hirek/20060101/canvasie</link>
 <description>A &amp;lt;canvas&amp;gt; sokak szerint az egyik legfontosabb böngésző oldali innováció, ami az utóbbi időben született. Láttuk, hogy a kiszámított visszatükröződéstől a rajzolóprogramon keresztül a lövöldözős játékig sokminden megvalósítható segítségével. Sajnos azonban a Safari és a Firefox 1.5 kivételével más böngészőben még nem érhető el a támogatása, a leendő Opera 9-es kiadásban várható a megjelenése. De mit tehetünk, ha Internet Explorerben szeretnénk a &amp;lt;canvas&amp;gt; elemet használni?</description>
 <category domain="http://weblabor.hu/hirek/rovatok/felulet">Felület</category>
 <pubDate>Sun, 01 Jan 2006 22:15:58 +0100</pubDate>
</item>
<item>
 <title>Weblabor karcsúsítás az új évre</title>
 <link>http://weblabor.hu/hirek/20051231/karcsusitas</link>
 <description>Ilyenkor szilveszter idején az emberek hajlamosak mindenféle ígéretet tenni arra vonatkozóan, hogy mit tesznek majd az új évben. Állítólag a leggyakoribb ilyen ígéret a fogyásról szól. Nos a Weblaboron egy kicsit előrehoztuk az ígéret megvalósítását, nehogy az újévi fogadalmak sorsára jusson az elképzelés. Akik napi szinten használják a Weblabort, talán észrevettek számos változást az utóbbi hetekben.</description>
 <category domain="http://weblabor.hu/hirek/rovatok/weblabor">Weblabor</category>
 <pubDate>Sat, 31 Dec 2005 16:39:36 +0100</pubDate>
</item>
<item>
 <title>Megjelent a WordPress 2.0</title>
 <link>http://weblabor.hu/hirek/20051231/wordpress2</link>
 <description>A WordPress blogmotor életének újabb mérföldkövéhez érkezett. Noha hivatalos bejelentés még nincs, ahogy már biztos sokan hallottátok, megjelent a 2.0-s verzió. A WordPress az elmúlt időkben igen nagy népszerűségre tett szert, nézzünk csak szét a háztáji keresőben. Mind a csupán blogolni vágyók, mind a fejlesztők számára kedves eszközről van szó. Az 1.5-ös széria megjelentével is már az élvonalba került az alkalmazás, a 2-es kiadás pedig rendesen feladja a leckét a konkurenciának. No de, lássuk a medvét!</description>
 <category domain="http://weblabor.hu/hirek/rovatok/tartalomkezelo">Tartalomkezelő</category>
 <pubDate>Sat, 31 Dec 2005 21:22:28 +0100</pubDate>
</item>
<item>
 <title>Hírforrás ikonok, &quot;szabványosítva&quot;</title>
 <link>http://weblabor.hu/hirek/20051230/feedikonszabvany</link>
 <description>Minden a narancssárga RSS ikonnal kezdődött - de szerencsére nem ezzel lett vége. Sok oldalon lelhető fel ma már (így nálunk is) az RSS hírforrást hirdető narancssárga &amp;quot;RSS&amp;quot; feliratú kis ikonka, mely jelzi a látogatók számára, hogy az adott oldal ebben a formátumban is elérhetővé tette híreit. Igen ám, de egyrészt ezekre nincsen kialakult ikon (bár azért hasonlítani szoktak egymásra ezek az ikonok), másrészt pedig nem csak ebben a formátumban létezik hírforrás. De itt a megoldás!</description>
 <category domain="http://weblabor.hu/hirek/rovatok/szabvany">Szabvány</category>
 <pubDate>Fri, 30 Dec 2005 22:16:52 +0100</pubDate>
</item>
<item>
 <title>Firefox 1.5 és Weblabor a Fix.tv műsorán</title>
 <link>http://weblabor.hu/hirek/20051225/mediasztar</link>
 <description>Sajnos a Weblaboron nem volt idő(m) reklámozni, de az utóbbi hetekben a Fix.tv LinuxPortál műsorának több adásában is szerepeltem. Akik nem kapták el az adásokat élőben vagy ismétlésben, immáron letölthető állapotban is elérhetnek két műsort: az egyikben a Firefox 1.5-ös verziójáról, a másikban pedig a Weblaborról társalogtunk a műsorvezetővel.</description>
 <category domain="http://weblabor.hu/hirek/rovatok/margo">Margó</category>
 <pubDate>Mon, 26 Dec 2005 13:02:51 +0100</pubDate>
</item>
<item>
 <title>Keresztplatformos turbó textarea a Jézuskától</title>
 <link>http://weblabor.hu/hirek/20051224/crossbrowserturbotextarea</link>
 <description>Szeretnék a karácsonykor is minket olvasóknak egy kis meglepetéssel szolgálni, aminek némileg érdekes története van. Egy levelező listás szállal indult, melynek keretén belül szerettem volna egy minél inkább böngésző független módszert találni arra, hogy egy textarea belső területén le tudjam kérdezni illetve be tudjam állítani a kurzor pozícióját, valamint bővíteni tudjam annak szerkesztő funkcióit. Sajnos ezekben a napokban idő hiányában esélyem sem volt, hogy utána járjak a témának. De tegnap jött a váratlan fordulat.</description>
 <category domain="http://weblabor.hu/hirek/rovatok/web">Web</category>
 <pubDate>Sun, 25 Dec 2005 23:47:10 +0100</pubDate>
</item>
<item>
 <title>Békés karácsonyt kívánunk!</title>
 <link>http://weblabor.hu/hirek/20051224/karacsony</link>
 <description>A szeretet ünnepe idén is lehetőséget ad arra, hogy egy kicsit visszahúzódjunk a világtól, a napi munka helyett családunkkal, szeretteinkkel töltsük az időt.&lt;br /&gt;
&lt;br /&gt;
A Weblabor szerkesztősége és az NJSZT WFSZ nevében ezúton szeretnénk átadni jókívánságainkat. Reméljük közösségünk aktív tagjainak és olvasóinknak is megadatik az ünnepben való elmélyülés lehetősége.</description>
 <category domain="http://weblabor.hu/hirek/rovatok/margo">Margó</category>
 <pubDate>Sat, 24 Dec 2005 16:46:20 +0100</pubDate>
</item>
<item>
 <title>MySQL kompatibilitási réteg készül PostreSQL-hez</title>
 <link>http://weblabor.hu/hirek/20051218/mysql2pgsql</link>
 <description>Sok nyílt forrású program csak MySQL adatbáziskezelővel működik jól, illetve még ha az alap programot sikeresen be is lehet üzemelni PostgreSQL használatával, a gondok rögtön feltűnnek, amint kiegészítőket szeretnénk telepíteni. Christopher Kings-Lynne úgy gondolta, hogy ezen változtatni kellene, és ha az összes nyílt forrású rendszer nem is javítható ki egyszerre, arra lehet törekedni, hogy a PostgreSQL is képes legyen a MySQL felületét biztosítani a kapcsolódó programok számára.</description>
 <category domain="http://weblabor.hu/hirek/rovatok/adatbazis">Adatbázis</category>
 <pubDate>Mon, 19 Dec 2005 00:12:29 +0100</pubDate>
</item>
</channel>
</rss>
