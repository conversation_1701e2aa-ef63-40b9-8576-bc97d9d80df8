repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.3.0
    hooks:
      - id: check-case-conflict
      - id: check-executables-have-shebangs
      - id: check-merge-conflict
  - repo: https://github.com/pycqa/isort
    rev: 5.10.1
    hooks:
      - id: isort
        args: ["--profile", "black"]
        name: isort (python)
  - repo: https://github.com/psf/black
    rev: 22.6.0
    hooks:
      - id: black
        args: ["--target-version", "py36"]
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v0.961
    hooks:
      - id: mypy
        files: ^chardet/
        args: ["--strict", "--pretty", "--show-error-codes"]
  - repo: https://github.com/PyCQA/prospector
    rev: 1.7.7 # The version of Prospector to use, if not 'master' for latest
    hooks:
      - id: prospector
