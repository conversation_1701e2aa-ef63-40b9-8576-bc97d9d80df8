[metadata]
name = chardet
version = attr: chardet.version.__version__
description = Universal encoding detector for Python 3
long_description = file: README.rst
author = <PERSON>
author_email = <EMAIL>
maintainer = <PERSON>
maintainer_email = <EMAIL>
url = https://github.com/chardet/chardet
license = LGPL
keywords = encoding, i18n, xml
project_urls =
    Documentation = https://chardet.readthedocs.io/
    GitHub Project = https://github.com/chardet/chardet
    Issue Tracker = https://github.com/chardet/chardet/issues
classifiers =
    Development Status :: 5 - Production/Stable
    Intended Audience :: Developers
    License :: OSI Approved :: GNU Lesser General Public License v2 or later (LGPLv2+)
    Operating System :: OS Independent
    Programming Language :: Python
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3.7
    Programming Language :: Python :: 3.8
    Programming Language :: Python :: 3.9
    Programming Language :: Python :: 3.10
    Programming Language :: Python :: 3.11
    Programming Language :: Python :: Implementation :: CPython
    Programming Language :: Python :: Implementation :: PyPy
    Topic :: Software Development :: Libraries :: Python Modules
    Topic :: Text Processing :: Linguistic

[options]
include_package_data = true
python_requires = >=3.7
packages = find:

[options.package_data]
chardet = py.typed

[options.entry_points]
console_scripts =
    chardetect = chardet.cli.chardetect:main

[tool:pytest]
addopts = -v
python_files = test.py
norecursedirs = *
