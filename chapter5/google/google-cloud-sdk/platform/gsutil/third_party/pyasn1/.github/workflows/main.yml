---
name: CI

on:
  push:
    branches: [ "main", "v*" ]
  pull_request:
    branches: [ "main", "v*" ]
  workflow_dispatch:
    inputs:
      tag:
        description: tag to build
        required: false
        type: string
  workflow_call:
    inputs:
      tag:
        description: tag to build
        required: true
        type: string

permissions:
  contents: read

env:
  FORCE_COLOR: 1

jobs:
  tests:
    name: "Python ${{ matrix.python-version }}"
    runs-on: "ubuntu-22.04"
    strategy:
      fail-fast: false
      matrix:
        python-version:
          - "3.8"
          - "3.9"
          - "3.10"
          - "3.11"
          - "3.12"
          - "3.13"
          - "pypy-3.8"
          - "pypy-3.9"
          - "pypy-3.10"
    steps:
      - uses: "actions/checkout@v4"
        with:
          ref: ${{ inputs.tag || github.ref }}
      - uses: "actions/setup-python@v5"
        with:
          python-version: "${{ matrix.python-version }}"
          allow-prereleases: true
          cache: "pip"
      - name: "Update pip"
        run: python -m pip install --upgrade pip setuptools wheel
      - name: "Install tox dependencies"
        run: python -m pip install --upgrade tox tox-gh-actions
      - name: "Run tox for ${{ matrix.python-version }}"
        run: "python -m tox"

  sdist:
    name: "Build sdist and wheel"
    runs-on: "ubuntu-latest"
    needs: tests
    steps:
      - uses: "actions/checkout@v4"
        with:
          ref: ${{ inputs.tag || github.ref }}
      - uses: "actions/setup-python@v5"
        with:
          python-version: "3.x"
          cache: "pip"
      - name: "Update pip"
        run: python -m pip install --upgrade pip setuptools wheel
      - name: "Install 'build'"
        run: python -m pip install --upgrade build
      - name: "Run 'build'"
        run: "python -m build"
      - name: "Upload sdist artifact"
        uses: actions/upload-artifact@v4
        with:
          name: sdist
          path: |
            dist/pyasn1*.tar.gz
          if-no-files-found: error
      - name: "Upload wheel artifact"
        uses: actions/upload-artifact@v4
        with:
          name: wheel
          path: |
            dist/pyasn1*.whl
          if-no-files-found: error
