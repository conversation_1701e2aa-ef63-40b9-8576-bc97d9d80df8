[metadata]
name = pyasn1
version = attr: pyasn1.__version__
description = Pure-Python implementation of ASN.1 types and DER/BER/CER codecs (X.208)
long_description = file: README.md
long_description_content_type = text/markdown
license = BSD-2-Clause
license_files = LICENSE.rst
url = https://github.com/pyasn1/pyasn1
author = <PERSON><PERSON>
author_email = <EMAIL>
maintainer = pyasn1 maintenance organization
maintainer_email = <PERSON> <<EMAIL>>
project_urls =
    Documentation=https://pyasn1.readthedocs.io
    Source=https://github.com/pyasn1/pyasn1
    Issues=https://github.com/pyasn1/pyasn1/issues
    Changelog=https://pyasn1.readthedocs.io/en/latest/changelog.html
platforms = any
classifiers =
    Development Status :: 5 - Production/Stable
    Environment :: Console
    Intended Audience :: Developers
    Intended Audience :: Education
    Intended Audience :: Information Technology
    Intended Audience :: System Administrators
    Intended Audience :: Telecommunications Industry
    License :: OSI Approved :: BSD License
    Natural Language :: English
    Operating System :: OS Independent
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3.8
    Programming Language :: Python :: 3.9
    Programming Language :: Python :: 3.10
    Programming Language :: Python :: 3.11
    Programming Language :: Python :: 3.12
    Programming Language :: Python :: 3.13
    Programming Language :: Python :: Implementation :: CPython
    Programming Language :: Python :: Implementation :: PyPy
    Topic :: Communications
    Topic :: Software Development :: Libraries :: Python Modules

[options]
python_requires = >=3.8
zip_safe = True
setup_requires = setuptools
packages =
    pyasn1
    pyasn1.type
    pyasn1.compat
    pyasn1.codec
    pyasn1.codec.ber
    pyasn1.codec.cer
    pyasn1.codec.der
    pyasn1.codec.native
