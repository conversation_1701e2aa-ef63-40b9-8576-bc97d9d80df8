
.. _tag.tag:

Solitary tag
------------

.. automodule:: pyasn1.type.tag
   :members: tagClassUniversal, tagClassApplication, tagClassContext,
             tagClassPrivate, tagFormatSimple, tagFormatConstructed

.. autoclass:: pyasn1.type.tag.Tag
   :members:

   .. note::

       The *Tag* objects are normally used by the
       :ref:`TagSet <tag.TagSet>`, objects to model a collection
       of ASN.1 tags.
