
Download & Install
==================

The *pyasn1* library is a pure-Python package with no external
dependencies. It works with Python 3.8+.

The best way to obtain PyASN1 is by running `pip`:

.. code-block:: bash

   $ virtualenv venv
   $ source venv/bin/activate
   $ pip install pyasn1

You may also want to use `pyasn1-modules`:

.. code-block:: bash

   $ pip install pyasn1-modules

Alternatively, you can download the latest release from
`GitHub <https://github.com/pyasn1/pyasn1/releases>`_
or `PyPI <https://pypi.org/project/pyasn1>`_.
