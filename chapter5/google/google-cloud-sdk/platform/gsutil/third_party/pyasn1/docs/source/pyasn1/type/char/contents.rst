
.. _type.char:

Character types
---------------

Besides :ref:`universal types <type.univ>` also defines a collection
of text types. Most of these types come from the past trying to capture
the fragments of long-forgotten technologies.

These *character* types are all scalars. They are similar to
:ref:`OctetString <univ.OctetString>` except that they all operate on
text, not bytes.

.. toctree::
   :maxdepth: 2

   /pyasn1/type/char/numericstring
   /pyasn1/type/char/printablestring
   /pyasn1/type/char/teletexstring
   /pyasn1/type/char/t61string
   /pyasn1/type/char/videotexstring
   /pyasn1/type/char/ia5string
   /pyasn1/type/char/graphicstring
   /pyasn1/type/char/visiblestring
   /pyasn1/type/char/iso646string
   /pyasn1/type/char/generalstring
   /pyasn1/type/char/universalstring
   /pyasn1/type/char/bmpstring
   /pyasn1/type/char/utf8string
