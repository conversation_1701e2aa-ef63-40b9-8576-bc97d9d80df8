[tox]
minversion = 3.18.0
envlist =
    py38, py39, py310, py311, py312, py313, pypy38, pypy39, pypy310
    cover, docs, bandit, build
isolated_build = true
skip_missing_interpreters = true

[testenv]
commands =
    {envpython} -Werror -m unittest discover -s tests

[testenv:cover]
basepython = python3
deps =
    coverage
commands = coverage erase
           coverage run --source pyasn1 -m unittest discover {posargs}
           coverage report --fail-under 80

[testenv:bandit]
skip_install = true
deps =
    bandit
commands =
    bandit -r pyasn1 -c .bandit.yml

[testenv:docs]
allowlist_externals = make
deps =
    sphinx
commands = make -C docs html SPHINXOPTS="-W --keep-going"

[testenv:build]
skip_install = true
deps =
    build
    twine
commands =
    {envpython} -m build
    {envpython} -m twine check --strict dist/pyasn1*.whl
    {envpython} -m twine check --strict dist/pyasn1*.tar.gz

[gh-actions]
python =
    3.8: py38
    3.9: py39, docs
    3.10: py310, cover, build, bandit
    3.11: py311
    3.12: py312
    3.13: py313
    pypy-3.8: pypy38
    pypy-3.9: pypy39
    pypy-3.10: pypy310
