{"delegates": ["<EMAIL>"], "service_account_impersonation_url": "https://iamcredentials.googleapis.com/v1/projects/-/serviceAccounts/<EMAIL>:generateAccessToken", "source_credentials": {"type": "external_account_authorized_user", "audience": "//iam.googleapis.com/locations/global/workforcePools/$WORKFORCE_POOL_ID/providers/$PROVIDER_ID", "refresh_token": "refreshToken", "token_url": "https://sts.googleapis.com/v1/oauth/token", "token_info_url": "https://sts.googleapis.com/v1/instrospect", "client_id": "clientId", "client_secret": "clientSecret"}, "type": "impersonated_service_account"}