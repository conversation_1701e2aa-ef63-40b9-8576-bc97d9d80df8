# Build artifacts
*.py[cod]
__pycache__
*.egg-info/
build/
dist/

# Documentation-related
docs/_build

# Test files
.nox/
.tox/
.cache/
.pytest_cache/
cert_path
key_path

# Django test database
db.sqlite3

# Coverage files
.coverage
coverage.xml
*sponge_log.xml
nosetests.xml
htmlcov/

# Files with private / local data
scripts/local_test_setup
tests/data/key.json
tests/data/key.p12
tests/data/user-key.json
system_tests/data/

# PyCharm configuration:
.idea
venv/

# Generated files
pylintrc
pylintrc.test
pytype_output/

.python-version
.DS_Store
cert_path
key_path
env/
.vscode/