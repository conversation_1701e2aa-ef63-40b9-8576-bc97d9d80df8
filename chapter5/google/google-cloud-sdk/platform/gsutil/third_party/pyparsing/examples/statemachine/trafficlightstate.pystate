#
# trafficlightstate.pystate
#
# state machine model of the states and associated behaviors and properties for each
# different state of a traffic light


# define state machine with transitions
# (states will be implemented as Python classes, so use name case appropriate for class names)
statemachine TrafficLightState:
    Red -> Green
    Green -> Yellow
    Yellow -> Red


# statemachine only defines the state->state transitions - actual behavior and properties
# must be added separately


# define some class level constants
Red.cars_can_go = False
Yellow.cars_can_go = True
Green.cars_can_go = True


# setup some class level methods
def flash_crosswalk(s):
    def flash():
        print(f"{s}...{s}...{s}")

    return flash

Red.crossing_signal = staticmethod(flash_crosswalk("WALK"))
Yellow.crossing_signal = staticmethod(flash_crosswalk("DONT WALK"))
Green.crossing_signal = staticmethod(flash_crosswalk("DONT WALK"))


# setup some instance methods
def wait(nSeconds):
    def waitFn(self):
        print("<wait %d seconds>" % nSeconds)

    return waitFn

Red.delay = wait(20)
Yellow.delay = wait(3)
Green.delay = wait(15)
