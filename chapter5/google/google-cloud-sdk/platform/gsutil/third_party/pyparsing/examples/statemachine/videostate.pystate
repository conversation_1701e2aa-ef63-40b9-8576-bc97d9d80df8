#
# videostate.pystate
#
# Statemachine describing the playing of a video
#   [] = stop
#    > = play
#   || = pause
#   >> = fast forward
#   << = rewind

statemachine VideoState:
    # basic >, [], and || controls
    Stopped-(play)->Playing
    Playing-(pause)-> Paused
    Playing-(stop)-> Stopped
    Paused-(stop)-> Stopped
    Paused-(play)->Playing

    # add >> and << controls - different meanings if occur while playing or stopped
    Playing-(fast_forward)->FastForward
    FastForward-(play)->Playing
    FastForward-(pause)->Paused
    FastForward-(stop)->Stopped
    Stopped-(fast_forward)->Forwardwinding
    Forwardwinding-(stop)->Stopped

    Playing-(rewind)->ReversePlaying
    ReversePlaying-(play)->Playing
    ReversePlaying-(pause)->Paused
    ReversePlaying-(stop)->Stopped
    Stopped-(rewind)->Rewinding
    Rewinding-(stop)->Stopped
