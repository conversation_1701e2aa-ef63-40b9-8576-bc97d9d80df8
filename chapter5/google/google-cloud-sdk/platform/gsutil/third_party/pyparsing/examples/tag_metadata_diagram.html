
<!DOCTYPE html>
<html>
<head>

    
        <style>
            .railroad-heading {
                font-family: monospace;
            }
        </style>
    

</head>
<body>

<meta charset="UTF-8"/>


    <div class="railroad-group">
        <h1 class="railroad-heading" id="z-0001"></h1>
        <div class="railroad-description"></div>
        <div class="railroad-svg">
            <svg class="railroad-diagram" height="261" viewBox="0 0 585.5 261" width="585.5" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g transform="translate(.5 .5)">
<g>
<path d="M20 21v20m10 -20v20m-10 -10h20" /></g><path d="M40 31h10" /><g>
<path d="M50 31h0.0" /><path d="M50.0 31h10" /><g class="terminal ">
<path d="M60.0 31h188.75" /><path d="M336.75 31h188.75" /><rect height="22" rx="10" ry="10" width="88" x="248.75" y="20"></rect><text x="292.75" y="35">&apos;Hello,&apos;</text></g><path d="M525.5 31a10 10 0 0 1 10 10v0a10 10 0 0 1 -10 10h-465.5a10 10 0 0 0 -10 10v0a10 10 0 0 0 10 10" /><g>
<path d="M60.0 71h0.0" /><path d="M525.5 71h0.0" /><path d="M60.0 71h20" /><g>
<path d="M80.0 71h12.75" /><path d="M492.75 71h12.75" /><g class="terminal ">
<path d="M92.75 71h0.0" /><path d="M282.75 71h0.0" /><rect height="22" rx="10" ry="10" width="190" x="92.75" y="60"></rect><text x="187.75" y="75">W:(A-Za-zªµºÀ-ÖØ...)</text></g><path d="M282.75 71h10" /><path d="M292.75 71h10" /><g class="terminal ">
<path d="M302.75 71h0.0" /><path d="M492.75 71h0.0" /><rect height="22" rx="10" ry="10" width="190" x="302.75" y="60"></rect><text x="397.75" y="75">Tag:alphabet=&apos;Latin&apos;</text></g></g><path d="M505.5 71h20" /><path d="M60.0 71a10 10 0 0 1 10 10v10a10 10 0 0 0 10 10" /><g>
<path d="M80.0 101h12.75" /><path d="M492.75 101h12.75" /><g class="terminal ">
<path d="M92.75 101h0.0" /><path d="M282.75 101h0.0" /><rect height="22" rx="10" ry="10" width="190" x="92.75" y="90"></rect><text x="187.75" y="105">W:(Ͱ-ʹͶͷͺ-ͽͿΆΈ-Ί...)</text></g><path d="M282.75 101h10" /><path d="M292.75 101h10" /><g class="terminal ">
<path d="M302.75 101h0.0" /><path d="M492.75 101h0.0" /><rect height="22" rx="10" ry="10" width="190" x="302.75" y="90"></rect><text x="397.75" y="105">Tag:alphabet=&apos;Greek&apos;</text></g></g><path d="M505.5 101a10 10 0 0 0 10 -10v-10a10 10 0 0 1 10 -10" /><path d="M60.0 71a10 10 0 0 1 10 10v40a10 10 0 0 0 10 10" /><g>
<path d="M80.0 131h0.0" /><path d="M505.5 131h0.0" /><g class="terminal ">
<path d="M80.0 131h0.0" /><path d="M270.0 131h0.0" /><rect height="22" rx="10" ry="10" width="190" x="80" y="120"></rect><text x="175" y="135">W:(々〆〱-〵〻〼ぁ-ゖゝ-ゟ...)</text></g><path d="M270.0 131h10" /><path d="M280.0 131h10" /><g class="terminal ">
<path d="M290.0 131h0.0" /><path d="M505.5 131h0.0" /><rect height="22" rx="10" ry="10" width="215.5" x="290" y="120"></rect><text x="397.75" y="135">Tag:alphabet=&apos;Japanese&apos;</text></g></g><path d="M505.5 131a10 10 0 0 0 10 -10v-40a10 10 0 0 1 10 -10" /></g><path d="M525.5 71a10 10 0 0 1 10 10v59a10 10 0 0 1 -10 10h-465.5a10 10 0 0 0 -10 10v0a10 10 0 0 0 10 10" /><g>
<path d="M60.0 170h93.5" /><path d="M432.0 170h93.5" /><path d="M153.5 170h20" /><g>
<path d="M173.5 170h4.25" /><path d="M407.75 170h4.25" /><g class="terminal ">
<path d="M177.75 170h0.0" /><path d="M223.25 170h0.0" /><rect height="22" rx="10" ry="10" width="45.5" x="177.75" y="159"></rect><text x="200.5" y="174">&apos;.&apos;</text></g><path d="M223.25 170h10" /><path d="M233.25 170h10" /><g class="terminal ">
<path d="M243.25 170h0.0" /><path d="M407.75 170h0.0" /><rect height="22" rx="10" ry="10" width="164.5" x="243.25" y="159"></rect><text x="325.5" y="174">Tag:mood=&apos;normal&apos;</text></g></g><path d="M412.0 170h20" /><path d="M153.5 170a10 10 0 0 1 10 10v10a10 10 0 0 0 10 10" /><g>
<path d="M173.5 200h0.0" /><path d="M412.0 200h0.0" /><g class="terminal ">
<path d="M173.5 200h0.0" /><path d="M219.0 200h0.0" /><rect height="22" rx="10" ry="10" width="45.5" x="173.5" y="189"></rect><text x="196.25" y="204">&apos;!&apos;</text></g><path d="M219.0 200h10" /><path d="M229.0 200h10" /><g class="terminal ">
<path d="M239.0 200h0.0" /><path d="M412.0 200h0.0" /><rect height="22" rx="10" ry="10" width="173" x="239" y="189"></rect><text x="325.5" y="204">Tag:mood=&apos;excited&apos;</text></g></g><path d="M412.0 200a10 10 0 0 0 10 -10v-10a10 10 0 0 1 10 -10" /><path d="M153.5 170a10 10 0 0 1 10 10v40a10 10 0 0 0 10 10" /><g>
<path d="M173.5 230h0.0" /><path d="M412.0 230h0.0" /><g class="terminal ">
<path d="M173.5 230h0.0" /><path d="M219.0 230h0.0" /><rect height="22" rx="10" ry="10" width="45.5" x="173.5" y="219"></rect><text x="196.25" y="234">&apos;?&apos;</text></g><path d="M219.0 230h10" /><path d="M229.0 230h10" /><g class="terminal ">
<path d="M239.0 230h0.0" /><path d="M412.0 230h0.0" /><rect height="22" rx="10" ry="10" width="173" x="239" y="219"></rect><text x="325.5" y="234">Tag:mood=&apos;curious&apos;</text></g></g><path d="M412.0 230a10 10 0 0 0 10 -10v-40a10 10 0 0 1 10 -10" /></g><path d="M525.5 170h10" /><path d="M535.5 170h0.0" /></g><path d="M535.5 170h10" /><path d="M 545.5 170 h 20 m -10 -10 v 20 m 10 -20 v 20"></path></g><style>/* <![CDATA[ */
	svg.railroad-diagram {
		background-color:hsl(30,20%,95%);
	}
	svg.railroad-diagram path {
		stroke-width:3;
		stroke:black;
		fill:rgba(0,0,0,0);
	}
	svg.railroad-diagram text {
		font:bold 14px monospace;
		text-anchor:middle;
	}
	svg.railroad-diagram text.label{
		text-anchor:start;
	}
	svg.railroad-diagram text.comment{
		font:italic 12px monospace;
	}
	svg.railroad-diagram rect{
		stroke-width:3;
		stroke:black;
		fill:hsl(120,100%,90%);
	}
	svg.railroad-diagram rect.group-box {
		stroke: gray;
		stroke-dasharray: 10 5;
		fill: none;
	}

/* ]]> */
</style></svg>
        </div>
    </div>


</body>
</html>
