<!DOCTYPE html>
<html>
<head>
    
        <style type="text/css">
            .railroad-heading {
                font-family: monospace;
            }
        </style>
    
</head>
<body>


    <div class="railroad-group">
        <h1 class="railroad-heading">Forward</h1>
        <div class="railroad-description"></div>
        <div class="railroad-svg">
            <svg class="railroad-diagram" height="172" viewBox="0 0 1344.0 172" width="1344.0" xmlns="http://www.w3.org/2000/svg">
<g transform="translate(.5 .5)">
<style>/* <![CDATA[ */
	svg.railroad-diagram {
		background-color:hsl(30,20%,95%);
	}
	svg.railroad-diagram path {
		stroke-width:3;
		stroke:black;
		fill:rgba(0,0,0,0);
	}
	svg.railroad-diagram text {
		font:bold 14px monospace;
		text-anchor:middle;
	}
	svg.railroad-diagram text.label{
		text-anchor:start;
	}
	svg.railroad-diagram text.comment{
		font:italic 12px monospace;
	}
	svg.railroad-diagram rect{
		stroke-width:3;
		stroke:black;
		fill:hsl(120,100%,90%);
	}
	svg.railroad-diagram rect.group-box {
		stroke: gray;
		stroke-dasharray: 10 5;
		fill: none;
	}

/* ]]> */
</style><g>
<path d="M20 53v20m10 -20v20m-10 -10h20"></path></g><path d="M40 63h10"></path><g>
<path d="M50 63h0.0"></path><path d="M1294.0 63h0.0"></path><g>
<path d="M50.0 63h0.0"></path><path d="M1048.0 63h0.0"></path><g>
<path d="M50.0 63h0.0"></path><path d="M662.0 63h0.0"></path><g>
<path d="M50.0 63h0.0"></path><path d="M571.0 63h0.0"></path><g class="terminal">
<path d="M50.0 63h0.0"></path><path d="M138.0 63h0.0"></path><rect height="22" rx="10" ry="10" width="88.0" x="50.0" y="52"></rect><text x="94.0" y="67">'select'</text></g><path d="M138.0 63h10"></path><g>
<path d="M148.0 63h0.0"></path><path d="M571.0 63h0.0"></path><path d="M148.0 63h20"></path><g class="terminal">
<path d="M168.0 63h168.75"></path><path d="M382.25 63h168.75"></path><rect height="22" rx="10" ry="10" width="45.5" x="336.75" y="52"></rect><text x="359.5" y="67">'&#42;'</text></g><path d="M551.0 63h20"></path><path d="M148.0 63a10 10 0 0 1 10 10v42a10 10 0 0 0 10 10"></path><g>
<path d="M168.0 125h0.0"></path><path d="M551.0 125h0.0"></path><g class="non-terminal">
<path d="M168.0 125h0.0"></path><path d="M281.5 125h0.0"></path><rect height="22" width="113.5" x="168.0" y="114"></rect><text x="224.75" y="129">column name</text></g><path d="M281.5 125h10"></path><g>
<path d="M291.5 125h0.0"></path><path d="M551.0 125h0.0"></path><path d="M291.5 125a10 10 0 0 0 10 -10v-23a10 10 0 0 1 10 -10"></path><g>
<path d="M311.5 82h219.5"></path></g><path d="M531.0 82a10 10 0 0 1 10 10v23a10 10 0 0 0 10 10"></path><path d="M291.5 125h20"></path><g>
<path d="M311.5 125h0.0"></path><path d="M531.0 125h0.0"></path><path d="M311.5 125h10"></path><g>
<path d="M321.5 125h0.0"></path><path d="M521.0 125h0.0"></path><g>
<path d="M321.5 125h0.0"></path><path d="M387.5 125h0.0"></path><rect class="group-box" height="38" rx="10" ry="10" width="66" x="321.5" y="106"></rect><g class="terminal">
<path d="M321.5 125h10.25"></path><path d="M377.25 125h10.25"></path><rect height="22" rx="10" ry="10" width="45.5" x="331.75" y="114"></rect><text x="354.5" y="129">','</text></g><g>
<path d="M321.5 98h0.0"></path><path d="M387.5 98h0.0"></path><text class="comment" x="354.5" y="103">Suppress</text></g></g><path d="M387.5 125h10"></path><path d="M397.5 125h10"></path><g class="non-terminal">
<path d="M407.5 125h0.0"></path><path d="M521.0 125h0.0"></path><rect height="22" width="113.5" x="407.5" y="114"></rect><text x="464.25" y="129">column name</text></g></g><path d="M521.0 125h10"></path><path d="M321.5 125a10 10 0 0 0 -10 10v7a10 10 0 0 0 10 10"></path><g>
<path d="M321.5 152h199.5"></path></g><path d="M521.0 152a10 10 0 0 0 10 -10v-7a10 10 0 0 0 -10 -10"></path></g><path d="M531.0 125h20"></path></g></g><path d="M551.0 125a10 10 0 0 0 10 -10v-42a10 10 0 0 1 10 -10"></path></g></g><path d="M571.0 63h10"></path><path d="M581.0 63h10"></path><g class="terminal">
<path d="M591.0 63h0.0"></path><path d="M662.0 63h0.0"></path><rect height="22" rx="10" ry="10" width="71.0" x="591.0" y="52"></rect><text x="626.5" y="67">'from'</text></g></g><path d="M662.0 63h10"></path><path d="M672.0 63h10"></path><g>
<path d="M682.0 63h0.0"></path><path d="M1048.0 63h0.0"></path><g class="non-terminal">
<path d="M682.0 63h0.0"></path><path d="M787.0 63h0.0"></path><rect height="22" width="105.0" x="682.0" y="52"></rect><text x="734.5" y="67">table name</text></g><path d="M787.0 63h10"></path><g>
<path d="M797.0 63h0.0"></path><path d="M1048.0 63h0.0"></path><path d="M797.0 63a10 10 0 0 0 10 -10v-23a10 10 0 0 1 10 -10"></path><g>
<path d="M817.0 20h211.0"></path></g><path d="M1028.0 20a10 10 0 0 1 10 10v23a10 10 0 0 0 10 10"></path><path d="M797.0 63h20"></path><g>
<path d="M817.0 63h0.0"></path><path d="M1028.0 63h0.0"></path><path d="M817.0 63h10"></path><g>
<path d="M827.0 63h0.0"></path><path d="M1018.0 63h0.0"></path><g>
<path d="M827.0 63h0.0"></path><path d="M893.0 63h0.0"></path><rect class="group-box" height="38" rx="10" ry="10" width="66" x="827.0" y="44"></rect><g class="terminal">
<path d="M827.0 63h10.25"></path><path d="M882.75 63h10.25"></path><rect height="22" rx="10" ry="10" width="45.5" x="837.25" y="52"></rect><text x="860.0" y="67">','</text></g><g>
<path d="M827.0 36h0.0"></path><path d="M893.0 36h0.0"></path><text class="comment" x="860.0" y="41">Suppress</text></g></g><path d="M893.0 63h10"></path><path d="M903.0 63h10"></path><g class="non-terminal">
<path d="M913.0 63h0.0"></path><path d="M1018.0 63h0.0"></path><rect height="22" width="105.0" x="913.0" y="52"></rect><text x="965.5" y="67">table name</text></g></g><path d="M1018.0 63h10"></path><path d="M827.0 63a10 10 0 0 0 -10 10v7a10 10 0 0 0 10 10"></path><g>
<path d="M827.0 90h191.0"></path></g><path d="M1018.0 90a10 10 0 0 0 10 -10v-7a10 10 0 0 0 -10 -10"></path></g><path d="M1028.0 63h20"></path></g></g></g><path d="M1048.0 63h10"></path><g>
<path d="M1058.0 63h0.0"></path><path d="M1294.0 63h0.0"></path><path d="M1058.0 63a10 10 0 0 0 10 -10v0a10 10 0 0 1 10 -10"></path><g>
<path d="M1078.0 43h196.0"></path></g><path d="M1274.0 43a10 10 0 0 1 10 10v0a10 10 0 0 0 10 10"></path><path d="M1058.0 63h20"></path><g>
<path d="M1078.0 63h0.0"></path><path d="M1274.0 63h0.0"></path><g class="terminal">
<path d="M1078.0 63h0.0"></path><path d="M1157.5 63h0.0"></path><rect height="22" rx="10" ry="10" width="79.5" x="1078.0" y="52"></rect><text x="1117.75" y="67">'where'</text></g><path d="M1157.5 63h10"></path><path d="M1167.5 63h10"></path><g class="non-terminal">
<path d="M1177.5 63h0.0"></path><path d="M1274.0 63h0.0"></path><rect height="22" width="96.5" x="1177.5" y="52"></rect><text x="1225.75" y="67">'or' term</text></g></g><path d="M1274.0 63h20"></path></g></g><path d="M1294.0 63h10"></path><path d="M 1304.0 63 h 20 m -10 -10 v 20 m 10 -20 v 20"></path></g></svg>
        </div>
    </div>

    <div class="railroad-group">
        <h1 class="railroad-heading">column name</h1>
        <div class="railroad-description"></div>
        <div class="railroad-svg">
            <svg class="railroad-diagram" height="110" viewBox="0 0 706.5 110" width="706.5" xmlns="http://www.w3.org/2000/svg">
<g transform="translate(.5 .5)">
<style>/* <![CDATA[ */
	svg.railroad-diagram {
		background-color:hsl(30,20%,95%);
	}
	svg.railroad-diagram path {
		stroke-width:3;
		stroke:black;
		fill:rgba(0,0,0,0);
	}
	svg.railroad-diagram text {
		font:bold 14px monospace;
		text-anchor:middle;
	}
	svg.railroad-diagram text.label{
		text-anchor:start;
	}
	svg.railroad-diagram text.comment{
		font:italic 12px monospace;
	}
	svg.railroad-diagram rect{
		stroke-width:3;
		stroke:black;
		fill:hsl(120,100%,90%);
	}
	svg.railroad-diagram rect.group-box {
		stroke: gray;
		stroke-dasharray: 10 5;
		fill: none;
	}

/* ]]> */
</style><g>
<path d="M20 53v20m10 -20v20m-10 -10h20"></path></g><path d="M40 63h10"></path><g>
<path d="M50 63h0.0"></path><path d="M656.5 63h0.0"></path><g>
<path d="M50.0 63h0.0"></path><path d="M285.5 63h0.0"></path><rect class="group-box" height="38" rx="10" ry="10" width="235.5" x="50.0" y="44"></rect><g class="terminal">
<path d="M50.0 63h10.0"></path><path d="M275.5 63h10.0"></path><rect height="22" rx="10" ry="10" width="215.5" x="60.0" y="52"></rect><text x="167.75" y="67">W:(A-Za-z, $0-9A-Z&#95;a-z)</text></g><g>
<path d="M50.0 36h0.0"></path><path d="M130.0 36h0.0"></path><text class="comment" x="90.0" y="41">identifier</text></g></g><path d="M285.5 63h10"></path><g>
<path d="M295.5 63h0.0"></path><path d="M656.5 63h0.0"></path><path d="M295.5 63a10 10 0 0 0 10 -10v-23a10 10 0 0 1 10 -10"></path><g>
<path d="M315.5 20h321.0"></path></g><path d="M636.5 20a10 10 0 0 1 10 10v23a10 10 0 0 0 10 10"></path><path d="M295.5 63h20"></path><g>
<path d="M315.5 63h0.0"></path><path d="M636.5 63h0.0"></path><path d="M315.5 63h10"></path><g>
<path d="M325.5 63h0.0"></path><path d="M626.5 63h0.0"></path><g class="terminal">
<path d="M325.5 63h0.0"></path><path d="M371.0 63h0.0"></path><rect height="22" rx="10" ry="10" width="45.5" x="325.5" y="52"></rect><text x="348.25" y="67">'.'</text></g><path d="M371.0 63h10"></path><path d="M381.0 63h10"></path><g>
<path d="M391.0 63h0.0"></path><path d="M626.5 63h0.0"></path><rect class="group-box" height="38" rx="10" ry="10" width="235.5" x="391.0" y="44"></rect><g class="terminal">
<path d="M391.0 63h10.0"></path><path d="M616.5 63h10.0"></path><rect height="22" rx="10" ry="10" width="215.5" x="401.0" y="52"></rect><text x="508.75" y="67">W:(A-Za-z, $0-9A-Z&#95;a-z)</text></g><g>
<path d="M391.0 36h0.0"></path><path d="M471.0 36h0.0"></path><text class="comment" x="431.0" y="41">identifier</text></g></g></g><path d="M626.5 63h10"></path><path d="M325.5 63a10 10 0 0 0 -10 10v7a10 10 0 0 0 10 10"></path><g>
<path d="M325.5 90h301.0"></path></g><path d="M626.5 90a10 10 0 0 0 10 -10v-7a10 10 0 0 0 -10 -10"></path></g><path d="M636.5 63h20"></path></g></g><path d="M656.5 63h10"></path><path d="M 666.5 63 h 20 m -10 -10 v 20 m 10 -20 v 20"></path></g></svg>
        </div>
    </div>

    <div class="railroad-group">
        <h1 class="railroad-heading">table name</h1>
        <div class="railroad-description"></div>
        <div class="railroad-svg">
            <svg class="railroad-diagram" height="110" viewBox="0 0 706.5 110" width="706.5" xmlns="http://www.w3.org/2000/svg">
<g transform="translate(.5 .5)">
<style>/* <![CDATA[ */
	svg.railroad-diagram {
		background-color:hsl(30,20%,95%);
	}
	svg.railroad-diagram path {
		stroke-width:3;
		stroke:black;
		fill:rgba(0,0,0,0);
	}
	svg.railroad-diagram text {
		font:bold 14px monospace;
		text-anchor:middle;
	}
	svg.railroad-diagram text.label{
		text-anchor:start;
	}
	svg.railroad-diagram text.comment{
		font:italic 12px monospace;
	}
	svg.railroad-diagram rect{
		stroke-width:3;
		stroke:black;
		fill:hsl(120,100%,90%);
	}
	svg.railroad-diagram rect.group-box {
		stroke: gray;
		stroke-dasharray: 10 5;
		fill: none;
	}

/* ]]> */
</style><g>
<path d="M20 53v20m10 -20v20m-10 -10h20"></path></g><path d="M40 63h10"></path><g>
<path d="M50 63h0.0"></path><path d="M656.5 63h0.0"></path><g>
<path d="M50.0 63h0.0"></path><path d="M285.5 63h0.0"></path><rect class="group-box" height="38" rx="10" ry="10" width="235.5" x="50.0" y="44"></rect><g class="terminal">
<path d="M50.0 63h10.0"></path><path d="M275.5 63h10.0"></path><rect height="22" rx="10" ry="10" width="215.5" x="60.0" y="52"></rect><text x="167.75" y="67">W:(A-Za-z, $0-9A-Z&#95;a-z)</text></g><g>
<path d="M50.0 36h0.0"></path><path d="M130.0 36h0.0"></path><text class="comment" x="90.0" y="41">identifier</text></g></g><path d="M285.5 63h10"></path><g>
<path d="M295.5 63h0.0"></path><path d="M656.5 63h0.0"></path><path d="M295.5 63a10 10 0 0 0 10 -10v-23a10 10 0 0 1 10 -10"></path><g>
<path d="M315.5 20h321.0"></path></g><path d="M636.5 20a10 10 0 0 1 10 10v23a10 10 0 0 0 10 10"></path><path d="M295.5 63h20"></path><g>
<path d="M315.5 63h0.0"></path><path d="M636.5 63h0.0"></path><path d="M315.5 63h10"></path><g>
<path d="M325.5 63h0.0"></path><path d="M626.5 63h0.0"></path><g class="terminal">
<path d="M325.5 63h0.0"></path><path d="M371.0 63h0.0"></path><rect height="22" rx="10" ry="10" width="45.5" x="325.5" y="52"></rect><text x="348.25" y="67">'.'</text></g><path d="M371.0 63h10"></path><path d="M381.0 63h10"></path><g>
<path d="M391.0 63h0.0"></path><path d="M626.5 63h0.0"></path><rect class="group-box" height="38" rx="10" ry="10" width="235.5" x="391.0" y="44"></rect><g class="terminal">
<path d="M391.0 63h10.0"></path><path d="M616.5 63h10.0"></path><rect height="22" rx="10" ry="10" width="215.5" x="401.0" y="52"></rect><text x="508.75" y="67">W:(A-Za-z, $0-9A-Z&#95;a-z)</text></g><g>
<path d="M391.0 36h0.0"></path><path d="M471.0 36h0.0"></path><text class="comment" x="431.0" y="41">identifier</text></g></g></g><path d="M626.5 63h10"></path><path d="M325.5 63a10 10 0 0 0 -10 10v7a10 10 0 0 0 10 10"></path><g>
<path d="M325.5 90h301.0"></path></g><path d="M626.5 90a10 10 0 0 0 10 -10v-7a10 10 0 0 0 -10 -10"></path></g><path d="M636.5 63h20"></path></g></g><path d="M656.5 63h10"></path><path d="M 666.5 63 h 20 m -10 -10 v 20 m 10 -20 v 20"></path></g></svg>
        </div>
    </div>

    <div class="railroad-group">
        <h1 class="railroad-heading">'or' term</h1>
        <div class="railroad-description"></div>
        <div class="railroad-svg">
            <svg class="railroad-diagram" height="125" viewBox="0 0 788.0 125" width="788.0" xmlns="http://www.w3.org/2000/svg">
<g transform="translate(.5 .5)">
<style>/* <![CDATA[ */
	svg.railroad-diagram {
		background-color:hsl(30,20%,95%);
	}
	svg.railroad-diagram path {
		stroke-width:3;
		stroke:black;
		fill:rgba(0,0,0,0);
	}
	svg.railroad-diagram text {
		font:bold 14px monospace;
		text-anchor:middle;
	}
	svg.railroad-diagram text.label{
		text-anchor:start;
	}
	svg.railroad-diagram text.comment{
		font:italic 12px monospace;
	}
	svg.railroad-diagram rect{
		stroke-width:3;
		stroke:black;
		fill:hsl(120,100%,90%);
	}
	svg.railroad-diagram rect.group-box {
		stroke: gray;
		stroke-dasharray: 10 5;
		fill: none;
	}

/* ]]> */
</style><g>
<path d="M20 45v20m10 -20v20m-10 -10h20"></path></g><g>
<path d="M40 55h0.0"></path><path d="M748.0 55h0.0"></path><path d="M40.0 55h20"></path><g>
<path d="M60.0 55h0.0"></path><path d="M728.0 55h0.0"></path><g>
<path d="M60.0 55h0.0"></path><path d="M384.0 55h0.0"></path><rect class="group-box" height="38" rx="10" ry="10" width="324.0" x="60.0" y="36"></rect><g>
<path d="M60.0 55h10.0"></path><path d="M374.0 55h10.0"></path><g>
<path d="M70.0 55h0.0"></path><path d="M249.0 55h0.0"></path><g class="non-terminal">
<path d="M70.0 55h0.0"></path><path d="M175.0 55h0.0"></path><rect height="22" width="105.0" x="70.0" y="44"></rect><text x="122.5" y="59">'and' term</text></g><path d="M175.0 55h10"></path><path d="M185.0 55h10"></path><g class="terminal">
<path d="M195.0 55h0.0"></path><path d="M249.0 55h0.0"></path><rect height="22" rx="10" ry="10" width="54.0" x="195.0" y="44"></rect><text x="222.0" y="59">'or'</text></g></g><path d="M249.0 55h10"></path><path d="M259.0 55h10"></path><g class="non-terminal">
<path d="M269.0 55h0.0"></path><path d="M374.0 55h0.0"></path><rect height="22" width="105.0" x="269.0" y="44"></rect><text x="321.5" y="59">'and' term</text></g></g><g>
<path d="M60.0 28h0.0"></path><path d="M91.0 28h0.0"></path><text class="comment" x="75.5" y="33">&#95;FB</text></g></g><path d="M384.0 55h10"></path><path d="M394.0 55h10"></path><g>
<path d="M404.0 55h0.0"></path><path d="M728.0 55h0.0"></path><g class="non-terminal">
<path d="M404.0 55h0.0"></path><path d="M509.0 55h0.0"></path><rect height="22" width="105.0" x="404.0" y="44"></rect><text x="456.5" y="59">'and' term</text></g><path d="M509.0 55h10"></path><path d="M519.0 55h10"></path><g>
<path d="M529.0 55h0.0"></path><path d="M728.0 55h0.0"></path><path d="M529.0 55h10"></path><g>
<path d="M539.0 55h0.0"></path><path d="M718.0 55h0.0"></path><g class="terminal">
<path d="M539.0 55h0.0"></path><path d="M593.0 55h0.0"></path><rect height="22" rx="10" ry="10" width="54.0" x="539.0" y="44"></rect><text x="566.0" y="59">'or'</text></g><path d="M593.0 55h10"></path><path d="M603.0 55h10"></path><g class="non-terminal">
<path d="M613.0 55h0.0"></path><path d="M718.0 55h0.0"></path><rect height="22" width="105.0" x="613.0" y="44"></rect><text x="665.5" y="59">'and' term</text></g></g><path d="M718.0 55h10"></path><path d="M539.0 55a10 10 0 0 0 -10 10v0a10 10 0 0 0 10 10"></path><g>
<path d="M539.0 75h179.0"></path></g><path d="M718.0 75a10 10 0 0 0 10 -10v0a10 10 0 0 0 -10 -10"></path></g></g></g><path d="M728.0 55h20"></path><path d="M40.0 55a10 10 0 0 1 10 10v19a10 10 0 0 0 10 10"></path><g class="non-terminal">
<path d="M60.0 94h281.5"></path><path d="M446.5 94h281.5"></path><rect height="22" width="105.0" x="341.5" y="83"></rect><text x="394.0" y="98">'and' term</text></g><path d="M728.0 94a10 10 0 0 0 10 -10v-19a10 10 0 0 1 10 -10"></path></g><path d="M 748.0 55 h 20 m -10 -10 v 20 m 10 -20 v 20"></path></g></svg>
        </div>
    </div>

    <div class="railroad-group">
        <h1 class="railroad-heading">'and' term</h1>
        <div class="railroad-description"></div>
        <div class="railroad-svg">
            <svg class="railroad-diagram" height="125" viewBox="0 0 805.0 125" width="805.0" xmlns="http://www.w3.org/2000/svg">
<g transform="translate(.5 .5)">
<style>/* <![CDATA[ */
	svg.railroad-diagram {
		background-color:hsl(30,20%,95%);
	}
	svg.railroad-diagram path {
		stroke-width:3;
		stroke:black;
		fill:rgba(0,0,0,0);
	}
	svg.railroad-diagram text {
		font:bold 14px monospace;
		text-anchor:middle;
	}
	svg.railroad-diagram text.label{
		text-anchor:start;
	}
	svg.railroad-diagram text.comment{
		font:italic 12px monospace;
	}
	svg.railroad-diagram rect{
		stroke-width:3;
		stroke:black;
		fill:hsl(120,100%,90%);
	}
	svg.railroad-diagram rect.group-box {
		stroke: gray;
		stroke-dasharray: 10 5;
		fill: none;
	}

/* ]]> */
</style><g>
<path d="M20 45v20m10 -20v20m-10 -10h20"></path></g><g>
<path d="M40 55h0.0"></path><path d="M765.0 55h0.0"></path><path d="M40.0 55h20"></path><g>
<path d="M60.0 55h0.0"></path><path d="M745.0 55h0.0"></path><g>
<path d="M60.0 55h0.0"></path><path d="M392.5 55h0.0"></path><rect class="group-box" height="38" rx="10" ry="10" width="332.5" x="60.0" y="36"></rect><g>
<path d="M60.0 55h10.0"></path><path d="M382.5 55h10.0"></path><g>
<path d="M70.0 55h0.0"></path><path d="M257.5 55h0.0"></path><g class="non-terminal">
<path d="M70.0 55h0.0"></path><path d="M175.0 55h0.0"></path><rect height="22" width="105.0" x="70.0" y="44"></rect><text x="122.5" y="59">'not' term</text></g><path d="M175.0 55h10"></path><path d="M185.0 55h10"></path><g class="terminal">
<path d="M195.0 55h0.0"></path><path d="M257.5 55h0.0"></path><rect height="22" rx="10" ry="10" width="62.5" x="195.0" y="44"></rect><text x="226.25" y="59">'and'</text></g></g><path d="M257.5 55h10"></path><path d="M267.5 55h10"></path><g class="non-terminal">
<path d="M277.5 55h0.0"></path><path d="M382.5 55h0.0"></path><rect height="22" width="105.0" x="277.5" y="44"></rect><text x="330.0" y="59">'not' term</text></g></g><g>
<path d="M60.0 28h0.0"></path><path d="M91.0 28h0.0"></path><text class="comment" x="75.5" y="33">&#95;FB</text></g></g><path d="M392.5 55h10"></path><path d="M402.5 55h10"></path><g>
<path d="M412.5 55h0.0"></path><path d="M745.0 55h0.0"></path><g class="non-terminal">
<path d="M412.5 55h0.0"></path><path d="M517.5 55h0.0"></path><rect height="22" width="105.0" x="412.5" y="44"></rect><text x="465.0" y="59">'not' term</text></g><path d="M517.5 55h10"></path><path d="M527.5 55h10"></path><g>
<path d="M537.5 55h0.0"></path><path d="M745.0 55h0.0"></path><path d="M537.5 55h10"></path><g>
<path d="M547.5 55h0.0"></path><path d="M735.0 55h0.0"></path><g class="terminal">
<path d="M547.5 55h0.0"></path><path d="M610.0 55h0.0"></path><rect height="22" rx="10" ry="10" width="62.5" x="547.5" y="44"></rect><text x="578.75" y="59">'and'</text></g><path d="M610.0 55h10"></path><path d="M620.0 55h10"></path><g class="non-terminal">
<path d="M630.0 55h0.0"></path><path d="M735.0 55h0.0"></path><rect height="22" width="105.0" x="630.0" y="44"></rect><text x="682.5" y="59">'not' term</text></g></g><path d="M735.0 55h10"></path><path d="M547.5 55a10 10 0 0 0 -10 10v0a10 10 0 0 0 10 10"></path><g>
<path d="M547.5 75h187.5"></path></g><path d="M735.0 75a10 10 0 0 0 10 -10v0a10 10 0 0 0 -10 -10"></path></g></g></g><path d="M745.0 55h20"></path><path d="M40.0 55a10 10 0 0 1 10 10v19a10 10 0 0 0 10 10"></path><g class="non-terminal">
<path d="M60.0 94h290.0"></path><path d="M455.0 94h290.0"></path><rect height="22" width="105.0" x="350.0" y="83"></rect><text x="402.5" y="98">'not' term</text></g><path d="M745.0 94a10 10 0 0 0 10 -10v-19a10 10 0 0 1 10 -10"></path></g><path d="M 765.0 55 h 20 m -10 -10 v 20 m 10 -20 v 20"></path></g></svg>
        </div>
    </div>

    <div class="railroad-group">
        <h1 class="railroad-heading">'not' term</h1>
        <div class="railroad-description"></div>
        <div class="railroad-svg">
            <svg class="railroad-diagram" height="372" viewBox="0 0 1622.5 372" width="1622.5" xmlns="http://www.w3.org/2000/svg">
<g transform="translate(.5 .5)">
<style>/* <![CDATA[ */
	svg.railroad-diagram {
		background-color:hsl(30,20%,95%);
	}
	svg.railroad-diagram path {
		stroke-width:3;
		stroke:black;
		fill:rgba(0,0,0,0);
	}
	svg.railroad-diagram text {
		font:bold 14px monospace;
		text-anchor:middle;
	}
	svg.railroad-diagram text.label{
		text-anchor:start;
	}
	svg.railroad-diagram text.comment{
		font:italic 12px monospace;
	}
	svg.railroad-diagram rect{
		stroke-width:3;
		stroke:black;
		fill:hsl(120,100%,90%);
	}
	svg.railroad-diagram rect.group-box {
		stroke: gray;
		stroke-dasharray: 10 5;
		fill: none;
	}

/* ]]> */
</style><g>
<path d="M20 45v20m10 -20v20m-10 -10h20"></path></g><g>
<path d="M40 55h0.0"></path><path d="M1582.5 55h0.0"></path><path d="M40.0 55h20"></path><g>
<path d="M60.0 55h528.75"></path><path d="M1033.75 55h528.75"></path><g>
<path d="M588.75 55h0.0"></path><path d="M796.25 55h0.0"></path><rect class="group-box" height="38" rx="10" ry="10" width="207.5" x="588.75" y="36"></rect><g>
<path d="M588.75 55h10.0"></path><path d="M786.25 55h10.0"></path><g class="terminal">
<path d="M598.75 55h0.0"></path><path d="M661.25 55h0.0"></path><rect height="22" rx="10" ry="10" width="62.5" x="598.75" y="44"></rect><text x="630.0" y="59">'not'</text></g><path d="M661.25 55h10"></path><path d="M671.25 55h10"></path><g class="non-terminal">
<path d="M681.25 55h0.0"></path><path d="M786.25 55h0.0"></path><rect height="22" width="105.0" x="681.25" y="44"></rect><text x="733.75" y="59">'not' term</text></g></g><g>
<path d="M588.75 28h0.0"></path><path d="M619.75 28h0.0"></path><text class="comment" x="604.25" y="33">&#95;FB</text></g></g><path d="M796.25 55h10"></path><path d="M806.25 55h10"></path><g>
<path d="M816.25 55h0.0"></path><path d="M1033.75 55h0.0"></path><g>
<path d="M816.25 55h0.0"></path><path d="M918.75 55h0.0"></path><path d="M816.25 55a10 10 0 0 0 10 -10v0a10 10 0 0 1 10 -10"></path><g>
<path d="M836.25 35h62.5"></path></g><path d="M898.75 35a10 10 0 0 1 10 10v0a10 10 0 0 0 10 10"></path><path d="M816.25 55h20"></path><g class="terminal">
<path d="M836.25 55h0.0"></path><path d="M898.75 55h0.0"></path><rect height="22" rx="10" ry="10" width="62.5" x="836.25" y="44"></rect><text x="867.5" y="59">'not'</text></g><path d="M898.75 55h20"></path></g><path d="M918.75 55h10"></path><g class="non-terminal">
<path d="M928.75 55h0.0"></path><path d="M1033.75 55h0.0"></path><rect height="22" width="105.0" x="928.75" y="44"></rect><text x="981.25" y="59">'not' term</text></g></g></g><path d="M1562.5 55h20"></path><path d="M40.0 55a10 10 0 0 1 10 10v27a10 10 0 0 0 10 10"></path><g>
<path d="M60.0 102h0.0"></path><path d="M1562.5 102h0.0"></path><path d="M60.0 102h20"></path><g>
<path d="M80.0 102h0.0"></path><path d="M1542.5 102h0.0"></path><path d="M80.0 102h20"></path><g>
<path d="M100.0 102h0.0"></path><path d="M1522.5 102h0.0"></path><path d="M100.0 102h20"></path><g>
<path d="M120.0 102h0.0"></path><path d="M1502.5 102h0.0"></path><path d="M120.0 102h20"></path><g>
<path d="M140.0 102h0.0"></path><path d="M1482.5 102h0.0"></path><g>
<path d="M140.0 102h0.0"></path><path d="M1366.0 102h0.0"></path><g class="non-terminal">
<path d="M140.0 102h0.0"></path><path d="M253.5 102h0.0"></path><rect height="22" width="113.5" x="140.0" y="91"></rect><text x="196.75" y="106">column name</text></g><path d="M253.5 102h10"></path><g>
<path d="M263.5 102h0.0"></path><path d="M1366.0 102h0.0"></path><path d="M263.5 102a10 10 0 0 0 10 -10v0a10 10 0 0 1 10 -10h978.5"></path><path d="M359.0 122h987.0a10 10 0 0 0 10 -10v0a10 10 0 0 1 10 -10"></path><path d="M263.5 102h10"></path><g class="terminal">
<path d="M273.5 102h10.0"></path><path d="M329.0 102h10.0"></path><rect height="22" rx="10" ry="10" width="45.5" x="283.5" y="91"></rect><text x="306.25" y="106">'='</text></g><path d="M339.0 102a10 10 0 0 1 10 10v0a10 10 0 0 0 10 10"></path><path d="M339.0 82a10 10 0 0 1 10 10v0a10 10 0 0 0 10 10"></path><g class="terminal">
<path d="M359.0 102h10.0"></path><path d="M423.0 102h10.0"></path><rect height="22" rx="10" ry="10" width="54.0" x="369.0" y="91"></rect><text x="396.0" y="106">'!='</text></g><path d="M433.0 102a10 10 0 0 1 10 10v0a10 10 0 0 0 10 10"></path><path d="M433.0 82a10 10 0 0 1 10 10v0a10 10 0 0 0 10 10"></path><g class="terminal">
<path d="M453.0 102h10.0"></path><path d="M517.0 102h10.0"></path><rect height="22" rx="10" ry="10" width="54.0" x="463.0" y="91"></rect><text x="490.0" y="106">'&#60;='</text></g><path d="M527.0 102a10 10 0 0 1 10 10v0a10 10 0 0 0 10 10"></path><path d="M527.0 82a10 10 0 0 1 10 10v0a10 10 0 0 0 10 10"></path><g class="terminal">
<path d="M547.0 102h10.0"></path><path d="M602.5 102h10.0"></path><rect height="22" rx="10" ry="10" width="45.5" x="557.0" y="91"></rect><text x="579.75" y="106">'&#60;'</text></g><path d="M612.5 102a10 10 0 0 1 10 10v0a10 10 0 0 0 10 10"></path><path d="M612.5 82a10 10 0 0 1 10 10v0a10 10 0 0 0 10 10"></path><g class="terminal">
<path d="M632.5 102h10.0"></path><path d="M696.5 102h10.0"></path><rect height="22" rx="10" ry="10" width="54.0" x="642.5" y="91"></rect><text x="669.5" y="106">'>='</text></g><path d="M706.5 102a10 10 0 0 1 10 10v0a10 10 0 0 0 10 10"></path><path d="M706.5 82a10 10 0 0 1 10 10v0a10 10 0 0 0 10 10"></path><g class="terminal">
<path d="M726.5 102h10.0"></path><path d="M782.0 102h10.0"></path><rect height="22" rx="10" ry="10" width="45.5" x="736.5" y="91"></rect><text x="759.25" y="106">'>'</text></g><path d="M792.0 102a10 10 0 0 1 10 10v0a10 10 0 0 0 10 10"></path><path d="M792.0 82a10 10 0 0 1 10 10v0a10 10 0 0 0 10 10"></path><g class="terminal">
<path d="M812.0 102h10.0"></path><path d="M876.0 102h10.0"></path><rect height="22" rx="10" ry="10" width="54.0" x="822.0" y="91"></rect><text x="849.0" y="106">'EQ'</text></g><path d="M886.0 102a10 10 0 0 1 10 10v0a10 10 0 0 0 10 10"></path><path d="M886.0 82a10 10 0 0 1 10 10v0a10 10 0 0 0 10 10"></path><g class="terminal">
<path d="M906.0 102h10.0"></path><path d="M970.0 102h10.0"></path><rect height="22" rx="10" ry="10" width="54.0" x="916.0" y="91"></rect><text x="943.0" y="106">'NE'</text></g><path d="M980.0 102a10 10 0 0 1 10 10v0a10 10 0 0 0 10 10"></path><path d="M980.0 82a10 10 0 0 1 10 10v0a10 10 0 0 0 10 10"></path><g class="terminal">
<path d="M1000.0 102h10.0"></path><path d="M1064.0 102h10.0"></path><rect height="22" rx="10" ry="10" width="54.0" x="1010.0" y="91"></rect><text x="1037.0" y="106">'LT'</text></g><path d="M1074.0 102a10 10 0 0 1 10 10v0a10 10 0 0 0 10 10"></path><path d="M1074.0 82a10 10 0 0 1 10 10v0a10 10 0 0 0 10 10"></path><g class="terminal">
<path d="M1094.0 102h10.0"></path><path d="M1158.0 102h10.0"></path><rect height="22" rx="10" ry="10" width="54.0" x="1104.0" y="91"></rect><text x="1131.0" y="106">'LE'</text></g><path d="M1168.0 102a10 10 0 0 1 10 10v0a10 10 0 0 0 10 10"></path><path d="M1168.0 82a10 10 0 0 1 10 10v0a10 10 0 0 0 10 10"></path><g class="terminal">
<path d="M1188.0 102h10.0"></path><path d="M1252.0 102h10.0"></path><rect height="22" rx="10" ry="10" width="54.0" x="1198.0" y="91"></rect><text x="1225.0" y="106">'GT'</text></g><path d="M1262.0 102a10 10 0 0 1 10 10v0a10 10 0 0 0 10 10"></path><path d="M1262.0 82a10 10 0 0 1 10 10v0a10 10 0 0 0 10 10"></path><g class="terminal">
<path d="M1282.0 102h10.0"></path><path d="M1346.0 102h10.0"></path><rect height="22" rx="10" ry="10" width="54.0" x="1292.0" y="91"></rect><text x="1319.0" y="106">'GE'</text></g><path d="M1356.0 102h10"></path></g></g><path d="M1366.0 102h10"></path><path d="M1376.0 102h10"></path><g class="non-terminal">
<path d="M1386.0 102h0.0"></path><path d="M1482.5 102h0.0"></path><rect height="22" width="96.5" x="1386.0" y="91"></rect><text x="1434.25" y="106">Unnamed 2</text></g></g><path d="M1482.5 102h20"></path><path d="M120.0 102a10 10 0 0 1 10 10v51a10 10 0 0 0 10 10"></path><g>
<path d="M140.0 173h327.5"></path><path d="M1155.0 173h327.5"></path><g>
<path d="M467.5 173h0.0"></path><path d="M655.0 173h0.0"></path><g class="non-terminal">
<path d="M467.5 173h0.0"></path><path d="M581.0 173h0.0"></path><rect height="22" width="113.5" x="467.5" y="162"></rect><text x="524.25" y="177">column name</text></g><path d="M581.0 173h10"></path><path d="M591.0 173h10"></path><g class="terminal">
<path d="M601.0 173h0.0"></path><path d="M655.0 173h0.0"></path><rect height="22" rx="10" ry="10" width="54.0" x="601.0" y="162"></rect><text x="628.0" y="177">'in'</text></g></g><path d="M655.0 173h10"></path><path d="M665.0 173h10"></path><g>
<path d="M675.0 173h0.0"></path><path d="M1155.0 173h0.0"></path><g>
<path d="M675.0 173h0.0"></path><path d="M1089.5 173h0.0"></path><g class="terminal">
<path d="M675.0 173h0.0"></path><path d="M720.5 173h0.0"></path><rect height="22" rx="10" ry="10" width="45.5" x="675.0" y="162"></rect><text x="697.75" y="177">'('</text></g><path d="M720.5 173h10"></path><path d="M730.5 173h10"></path><g>
<path d="M740.5 173h0.0"></path><path d="M1089.5 173h0.0"></path><g class="non-terminal">
<path d="M740.5 173h0.0"></path><path d="M837.0 173h0.0"></path><rect height="22" width="96.5" x="740.5" y="162"></rect><text x="788.75" y="177">Unnamed 2</text></g><path d="M837.0 173h10"></path><g>
<path d="M847.0 173h0.0"></path><path d="M1089.5 173h0.0"></path><path d="M847.0 173a10 10 0 0 0 10 -10v-23a10 10 0 0 1 10 -10"></path><g>
<path d="M867.0 130h202.5"></path></g><path d="M1069.5 130a10 10 0 0 1 10 10v23a10 10 0 0 0 10 10"></path><path d="M847.0 173h20"></path><g>
<path d="M867.0 173h0.0"></path><path d="M1069.5 173h0.0"></path><path d="M867.0 173h10"></path><g>
<path d="M877.0 173h0.0"></path><path d="M1059.5 173h0.0"></path><g>
<path d="M877.0 173h0.0"></path><path d="M943.0 173h0.0"></path><rect class="group-box" height="38" rx="10" ry="10" width="66" x="877.0" y="154"></rect><g class="terminal">
<path d="M877.0 173h10.25"></path><path d="M932.75 173h10.25"></path><rect height="22" rx="10" ry="10" width="45.5" x="887.25" y="162"></rect><text x="910.0" y="177">','</text></g><g>
<path d="M877.0 146h0.0"></path><path d="M943.0 146h0.0"></path><text class="comment" x="910.0" y="151">Suppress</text></g></g><path d="M943.0 173h10"></path><path d="M953.0 173h10"></path><g class="non-terminal">
<path d="M963.0 173h0.0"></path><path d="M1059.5 173h0.0"></path><rect height="22" width="96.5" x="963.0" y="162"></rect><text x="1011.25" y="177">Unnamed 2</text></g></g><path d="M1059.5 173h10"></path><path d="M877.0 173a10 10 0 0 0 -10 10v7a10 10 0 0 0 10 10"></path><g>
<path d="M877.0 200h182.5"></path></g><path d="M1059.5 200a10 10 0 0 0 10 -10v-7a10 10 0 0 0 -10 -10"></path></g><path d="M1069.5 173h20"></path></g></g></g><path d="M1089.5 173h10"></path><path d="M1099.5 173h10"></path><g class="terminal">
<path d="M1109.5 173h0.0"></path><path d="M1155.0 173h0.0"></path><rect height="22" rx="10" ry="10" width="45.5" x="1109.5" y="162"></rect><text x="1132.25" y="177">')'</text></g></g></g><path d="M1482.5 173a10 10 0 0 0 10 -10v-51a10 10 0 0 1 10 -10"></path></g><path d="M1502.5 102h20"></path><path d="M100.0 102a10 10 0 0 1 10 10v97a10 10 0 0 0 10 10"></path><g>
<path d="M120.0 219h482.25"></path><path d="M1020.25 219h482.25"></path><g>
<path d="M602.25 219h0.0"></path><path d="M789.75 219h0.0"></path><g class="non-terminal">
<path d="M602.25 219h0.0"></path><path d="M715.75 219h0.0"></path><rect height="22" width="113.5" x="602.25" y="208"></rect><text x="659.0" y="223">column name</text></g><path d="M715.75 219h10"></path><path d="M725.75 219h10"></path><g class="terminal">
<path d="M735.75 219h0.0"></path><path d="M789.75 219h0.0"></path><rect height="22" rx="10" ry="10" width="54.0" x="735.75" y="208"></rect><text x="762.75" y="223">'in'</text></g></g><path d="M789.75 219h10"></path><path d="M799.75 219h10"></path><g>
<path d="M809.75 219h0.0"></path><path d="M1020.25 219h0.0"></path><g>
<path d="M809.75 219h0.0"></path><path d="M954.75 219h0.0"></path><g class="terminal">
<path d="M809.75 219h0.0"></path><path d="M855.25 219h0.0"></path><rect height="22" rx="10" ry="10" width="45.5" x="809.75" y="208"></rect><text x="832.5" y="223">'('</text></g><path d="M855.25 219h10"></path><path d="M865.25 219h10"></path><g class="non-terminal">
<path d="M875.25 219h0.0"></path><path d="M954.75 219h0.0"></path><rect height="22" width="79.5" x="875.25" y="208"></rect><text x="915.0" y="223">Forward</text></g></g><path d="M954.75 219h10"></path><path d="M964.75 219h10"></path><g class="terminal">
<path d="M974.75 219h0.0"></path><path d="M1020.25 219h0.0"></path><rect height="22" rx="10" ry="10" width="45.5" x="974.75" y="208"></rect><text x="997.5" y="223">')'</text></g></g></g><path d="M1502.5 219a10 10 0 0 0 10 -10v-97a10 10 0 0 1 10 -10"></path></g><path d="M1522.5 102h20"></path><path d="M80.0 102a10 10 0 0 1 10 10v127a10 10 0 0 0 10 10"></path><g>
<path d="M100.0 249h515.75"></path><path d="M1006.75 249h515.75"></path><g>
<path d="M615.75 249h0.0"></path><path d="M803.25 249h0.0"></path><g class="non-terminal">
<path d="M615.75 249h0.0"></path><path d="M729.25 249h0.0"></path><rect height="22" width="113.5" x="615.75" y="238"></rect><text x="672.5" y="253">column name</text></g><path d="M729.25 249h10"></path><path d="M739.25 249h10"></path><g class="terminal">
<path d="M749.25 249h0.0"></path><path d="M803.25 249h0.0"></path><rect height="22" rx="10" ry="10" width="54.0" x="749.25" y="238"></rect><text x="776.25" y="253">'is'</text></g></g><path d="M803.25 249h10"></path><g>
<path d="M813.25 249h0.0"></path><path d="M1006.75 249h0.0"></path><path d="M813.25 249h20"></path><g class="terminal">
<path d="M833.25 249h41.25"></path><path d="M945.5 249h41.25"></path><rect height="22" rx="10" ry="10" width="71.0" x="874.5" y="238"></rect><text x="910.0" y="253">'null'</text></g><path d="M986.75 249h20"></path><path d="M813.25 249a10 10 0 0 1 10 10v10a10 10 0 0 0 10 10"></path><g>
<path d="M833.25 279h0.0"></path><path d="M986.75 279h0.0"></path><g class="terminal">
<path d="M833.25 279h0.0"></path><path d="M895.75 279h0.0"></path><rect height="22" rx="10" ry="10" width="62.5" x="833.25" y="268"></rect><text x="864.5" y="283">'not'</text></g><path d="M895.75 279h10"></path><path d="M905.75 279h10"></path><g class="terminal">
<path d="M915.75 279h0.0"></path><path d="M986.75 279h0.0"></path><rect height="22" rx="10" ry="10" width="71.0" x="915.75" y="268"></rect><text x="951.25" y="283">'null'</text></g></g><path d="M986.75 279a10 10 0 0 0 10 -10v-10a10 10 0 0 1 10 -10"></path></g></g><path d="M1522.5 249a10 10 0 0 0 10 -10v-127a10 10 0 0 1 10 -10"></path></g><path d="M1542.5 102h20"></path><path d="M60.0 102a10 10 0 0 1 10 10v211a10 10 0 0 0 10 10"></path><g>
<path d="M80.0 333h597.0"></path><path d="M945.5 333h597.0"></path><g>
<path d="M677.0 333h0.0"></path><path d="M859.5 333h0.0"></path><g>
<path d="M677.0 333h0.0"></path><path d="M743.0 333h0.0"></path><rect class="group-box" height="38" rx="10" ry="10" width="66" x="677.0" y="314"></rect><g class="terminal">
<path d="M677.0 333h10.25"></path><path d="M732.75 333h10.25"></path><rect height="22" rx="10" ry="10" width="45.5" x="687.25" y="322"></rect><text x="710.0" y="337">'('</text></g><g>
<path d="M677.0 306h0.0"></path><path d="M743.0 306h0.0"></path><text class="comment" x="710.0" y="311">Suppress</text></g></g><path d="M743.0 333h10"></path><path d="M753.0 333h10"></path><g class="non-terminal">
<path d="M763.0 333h0.0"></path><path d="M859.5 333h0.0"></path><rect height="22" width="96.5" x="763.0" y="322"></rect><text x="811.25" y="337">'or' term</text></g></g><path d="M859.5 333h10"></path><path d="M869.5 333h10"></path><g>
<path d="M879.5 333h0.0"></path><path d="M945.5 333h0.0"></path><rect class="group-box" height="38" rx="10" ry="10" width="66" x="879.5" y="314"></rect><g class="terminal">
<path d="M879.5 333h10.25"></path><path d="M935.25 333h10.25"></path><rect height="22" rx="10" ry="10" width="45.5" x="889.75" y="322"></rect><text x="912.5" y="337">')'</text></g><g>
<path d="M879.5 306h0.0"></path><path d="M945.5 306h0.0"></path><text class="comment" x="912.5" y="311">Suppress</text></g></g></g><path d="M1542.5 333a10 10 0 0 0 10 -10v-211a10 10 0 0 1 10 -10"></path></g><path d="M1562.5 102a10 10 0 0 0 10 -10v-27a10 10 0 0 1 10 -10"></path></g><path d="M 1582.5 55 h 20 m -10 -10 v 20 m 10 -20 v 20"></path></g></svg>
        </div>
    </div>

    <div class="railroad-group">
        <h1 class="railroad-heading">Unnamed 2</h1>
        <div class="railroad-description"></div>
        <div class="railroad-svg">
            <svg class="railroad-diagram" height="278" viewBox="0 0 787.0 278" width="787.0" xmlns="http://www.w3.org/2000/svg">
<g transform="translate(.5 .5)">
<style>/* <![CDATA[ */
	svg.railroad-diagram {
		background-color:hsl(30,20%,95%);
	}
	svg.railroad-diagram path {
		stroke-width:3;
		stroke:black;
		fill:rgba(0,0,0,0);
	}
	svg.railroad-diagram text {
		font:bold 14px monospace;
		text-anchor:middle;
	}
	svg.railroad-diagram text.label{
		text-anchor:start;
	}
	svg.railroad-diagram text.comment{
		font:italic 12px monospace;
	}
	svg.railroad-diagram rect{
		stroke-width:3;
		stroke:black;
		fill:hsl(120,100%,90%);
	}
	svg.railroad-diagram rect.group-box {
		stroke: gray;
		stroke-dasharray: 10 5;
		fill: none;
	}

/* ]]> */
</style><g>
<path d="M20 45v20m10 -20v20m-10 -10h20"></path></g><g>
<path d="M40 55h0.0"></path><path d="M747.0 55h0.0"></path><path d="M40.0 55h20"></path><g>
<path d="M60.0 55h0.0"></path><path d="M727.0 55h0.0"></path><path d="M60.0 55h20"></path><g>
<path d="M80.0 55h146.0"></path><path d="M561.0 55h146.0"></path><path d="M226.0 55h20"></path><g>
<path d="M246.0 55h0.0"></path><path d="M541.0 55h0.0"></path><rect class="group-box" height="38" rx="10" ry="10" width="295.0" x="246.0" y="36"></rect><g class="terminal">
<path d="M246.0 55h10.0"></path><path d="M531.0 55h10.0"></path><rect height="22" rx="10" ry="10" width="275.0" x="256.0" y="44"></rect><text x="393.5" y="59">Re:('&#91;+-&#93;?(?:\d+\.\d&#42;|\.\d+)')</text></g><g>
<path d="M246.0 28h0.0"></path><path d="M333.0 28h0.0"></path><text class="comment" x="289.5" y="33">real number</text></g></g><path d="M541.0 55h20"></path><path d="M226.0 55a10 10 0 0 1 10 10v42a10 10 0 0 0 10 10"></path><g>
<path d="M246.0 117h63.75"></path><path d="M477.25 117h63.75"></path><rect class="group-box" height="38" rx="10" ry="10" width="167.5" x="309.75" y="98"></rect><g class="terminal">
<path d="M309.75 117h10.0"></path><path d="M467.25 117h10.0"></path><rect height="22" rx="10" ry="10" width="147.5" x="319.75" y="106"></rect><text x="393.5" y="121">Re:('&#91;+-&#93;?\d+')</text></g><g>
<path d="M309.75 90h0.0"></path><path d="M417.75 90h0.0"></path><text class="comment" x="363.75" y="95">signed integer</text></g></g><path d="M541.0 117a10 10 0 0 0 10 -10v-42a10 10 0 0 1 10 -10"></path></g><path d="M707.0 55h20"></path><path d="M60.0 55a10 10 0 0 1 10 10v104a10 10 0 0 0 10 10"></path><g>
<path d="M80.0 179h0.0"></path><path d="M707.0 179h0.0"></path><rect class="group-box" height="68" rx="10" ry="10" width="627.0" x="80.0" y="160"></rect><g>
<path d="M80.0 179h0.0"></path><path d="M707.0 179h0.0"></path><path d="M80.0 179h20"></path><g>
<path d="M100.0 179h0.0"></path><path d="M687.0 179h0.0"></path><g class="terminal">
<path d="M100.0 179h0.0"></path><path d="M621.5 179h0.0"></path><rect height="22" rx="10" ry="10" width="521.5" x="100.0" y="168"></rect><text x="360.75" y="183">Re:('"(?:&#91;^"\n\r\\&#93;|(?:"")|(?:\\(?:&#91;^x&#93;|x&#91;0-9a-fA-F&#93;+)))&#42;')</text></g><path d="M621.5 179h10"></path><path d="M631.5 179h10"></path><g class="terminal">
<path d="M641.5 179h0.0"></path><path d="M687.0 179h0.0"></path><rect height="22" rx="10" ry="10" width="45.5" x="641.5" y="168"></rect><text x="664.25" y="183">'"'</text></g></g><path d="M687.0 179h20"></path><path d="M80.0 179a10 10 0 0 1 10 10v10a10 10 0 0 0 10 10"></path><g>
<path d="M100.0 209h0.0"></path><path d="M687.0 209h0.0"></path><g class="terminal">
<path d="M100.0 209h0.0"></path><path d="M621.5 209h0.0"></path><rect height="22" rx="10" ry="10" width="521.5" x="100.0" y="198"></rect><text x="360.75" y="213">Re:("'(?:&#91;^'\n\r\\&#93;|(?:'')|(?:\\(?:&#91;^x&#93;|x&#91;0-9a-fA-F&#93;+)))&#42;")</text></g><path d="M621.5 209h10"></path><path d="M631.5 209h10"></path><g class="terminal">
<path d="M641.5 209h0.0"></path><path d="M687.0 209h0.0"></path><rect height="22" rx="10" ry="10" width="45.5" x="641.5" y="198"></rect><text x="664.25" y="213">"'"</text></g></g><path d="M687.0 209a10 10 0 0 0 10 -10v-10a10 10 0 0 1 10 -10"></path></g><g>
<path d="M80.0 152h0.0"></path><path d="M384.0 152h0.0"></path><text class="comment" x="232.0" y="157">quotedString using single or double quotes</text></g></g><path d="M707.0 179a10 10 0 0 0 10 -10v-104a10 10 0 0 1 10 -10"></path></g><path d="M727.0 55h20"></path><path d="M40.0 55a10 10 0 0 1 10 10v172a10 10 0 0 0 10 10"></path><g class="non-terminal">
<path d="M60.0 247h276.75"></path><path d="M450.25 247h276.75"></path><rect height="22" width="113.5" x="336.75" y="236"></rect><text x="393.5" y="251">column name</text></g><path d="M727.0 247a10 10 0 0 0 10 -10v-172a10 10 0 0 1 10 -10"></path></g><path d="M 747.0 55 h 20 m -10 -10 v 20 m 10 -20 v 20"></path></g></svg>
        </div>
    </div>

</body>
</html>