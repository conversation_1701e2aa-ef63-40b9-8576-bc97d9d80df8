@startuml
'https://plantuml.com/class-diagram

top to bottom direction
hide circle
hide empty members
'hide empty methods
skinparam groupInheritance 3

note as N1
Class Diagram
---
<size 18>pyparsing 3.0.9
<size 18>May, 2022
end note

N1 <-[hidden]- unicode

package core {

class globals {
quoted_string
sgl_quoted_string
dbl_quoted_string
counted_array()
match_previous_literal()
match_previous_expr()
one_of()
dict_of()
original_text_for()
ungroup()
nested_expr()
make_html_tags()
make_xml_tags()
common_html_entity
replace_html_entity()
class OpAssoc
infix_notation()
class IndentedBlock
c_style_comment
html_comment
rest_of_line
dbl_slash_comment
cpp_style_comment
java_style_comment
python_style_comment
match_only_at_col()
replace_with()
remove_quotes()
with_attribute()
with_class()
trace_parse_action()
condition_as_parse_action()
srange()
token_map()
autoname_elements()
}

class ParseResults {
class List
{static}from_dict()
__getitem__()
__setitem__()
__contains__()
__len__()
__bool__()
__iter__()
__reversed__()
__getattr__()
__add__()
__getstate__()
__setstate__()
__getnewargs__()
__dir__()
as_dict()
as_list()
dump()
get_name()
items()
keys()
values()
haskeys()
pop()
get()
insert()
append()
extend()
clear()
copy()
get_name()
pprint()
}

class ParseBaseException #ffffff {
{static} explain_exception()
explain()
mark_input_line()
line
lineno
column
parser_element
}
class ParseException
class ParseFatalException
class ParseSyntaxException

ParseBaseException <|-- ParseException
ParseBaseException <|-- ParseFatalException
ParseFatalException <|-- ParseSyntaxException

class ParserElement {
name: str
results_name: str
---
{classifier} enable_packrat()
{classifier} enable_left_recursion()
{classifier} disable_memoization()
{classifier} set_default_whitespace_chars()
{classifier} inline_literals_using()
{classifier} reset_cache()

{static} verbose_stacktrace
suppress_warning()

operator + () -> And
operator - () -> And.ErrorStop
operator | () -> MatchFirst
operator ^ () -> Or
operator & () -> Each
operator ~ () -> NotAny
operator [] () -> _MultipleMatch
operator () () [set_results_name()]

add_condition()
add_parse_action()
set_parse_action()
copy()
ignore(expr)
leave_whitespace()
parse_with_tabs()
suppress()
set_break()
set_debug()
set_debug_actions()
set_name()
set_results_name()
parse_string()
scan_string()
search_string()
transform_string()
split()
run_tests()
recurse()
create_diagram()
}
class Token #ffffff
class ParseExpression #ffffff {
exprs: list[ParserElement]
}
class ParseElementEnhance #ffffff {
expr: ParserElement
}
class _PositionToken  #ffffff
class Char
class White
class Word {
'Word(init_chars: str, body_chars: str, min: int, \nmax: int, exact: int, as_keyword: bool, exclude_chars: str)
}
class Keyword {
{static} set_default_keyword_chars(chars: str)
}
class CaselessKeyword
class Empty
class Literal
class Regex
class NoMatch
class CharsNotIn
class QuotedString

class And
class Or
class MatchFirst
class Each

class OneOrMore
class ZeroOrMore
class DelimitedList
class SkipTo
class Group
class Forward {
operator <<= ()
}

class LineStart
class LineEnd
class StringStart
class StringEnd
class WordStart
class WordEnd
class _MultipleMatch #ffffff
class FollowedBy
class PrecededBy
class AtLineStart
class AtStringStart

class TokenConverter #ffffff
class Located
class Opt

class Combine
class Group
class Dict
class Suppress

ParserElement <|-- Token
ParserElement <|----- ParseExpression
Token <|-- _PositionToken
ParserElement <|----- ParseElementEnhance

'ParseElementEnhance ---> ParserElement
'ParseExpression ---> "*" ParserElement


Token <|-- Empty
Token <|-- CloseMatch
Token <|-- NoMatch
Token <|-- Literal
Token <|-- Word
Token <|---- Keyword
Token <|--- Regex
Token <|--- CharsNotIn
Token <|-- White
Token <|---- QuotedString
Word <|-- Char
Literal <|-- CaselessLiteral
Keyword <|-- CaselessKeyword

ParseExpression <|-- And
ParseExpression <|-- Or
ParseExpression <|-- MatchFirst
ParseExpression <|-- Each

ParseElementEnhance <|-- SkipTo
ParseElementEnhance <|--- Forward
ParseElementEnhance <|-- Located
ParseElementEnhance <|--- _MultipleMatch
_MultipleMatch <|-- OneOrMore
_MultipleMatch <|-- ZeroOrMore
ParseElementEnhance <|-- DelimitedList
ParseElementEnhance <|--- NotAny
ParseElementEnhance <|--- FollowedBy
ParseElementEnhance <|--- PrecededBy
ParseElementEnhance <|-- Opt
ParseElementEnhance <|--- TokenConverter
ParseElementEnhance <|-- AtStringStart
ParseElementEnhance <|-- AtLineStart
TokenConverter <|-- Group
TokenConverter <|-- Dict
TokenConverter <|-- Suppress
TokenConverter <|-- Combine

_PositionToken <|-- LineStart
_PositionToken <|-- LineEnd
_PositionToken <|-- WordStart
_PositionToken <|-- WordEnd
_PositionToken <|-- StringStart
_PositionToken <|-- StringEnd

}

package common {
class " " {
comma_separated_list
convert_to_integer()
convert_to_float()
integer
hex_integer
signed_integer
fraction
mixed_integer
real
sci_real
number
fnumber
identifier
ipv4_address
ipv6_address
mac_address
convert_to_date()
convert_to_datetime()
iso8601_date
iso8601_datetime
uuid
strip_html_tags()
upcase_tokens()
downcase_tokens()
url
}

}
package unicode {
class unicode_set {
printables: str
alphas: str
nums: str
alphanums: str
identchars: str
identbodychars: str
}
class Latin1
class LatinA
class LatinB
class BasicMultilingualPlane
class Chinese
class Thai
class Japanese {
class Kanji
class Hiragana
class Katakana
}
class Greek
class Hangul
class Arabic
class Devanagari
class Hebrew
class Cyrillic

unicode_set <|-- Latin1
unicode_set <|--- LatinA
unicode_set <|-- LatinB
unicode_set <|---- BasicMultilingualPlane
unicode_set <|-- Greek
unicode_set <|--- Cyrillic
unicode_set <|--- Chinese
unicode_set <|--- Japanese
unicode_set <|--- Hangul
Chinese <|-- CJK
Japanese <|-- CJK
Hangul <|-- CJK
unicode_set <|-- Thai
unicode_set <|-- Arabic
unicode_set <|-- Hebrew
unicode_set <|--- Devanagari

}

ParserElement <-[hidden] ParseBaseException
'ParseBaseException <-[hidden] globals
'globals <-[hidden] ParserElement
CJK <-[hidden]-- common

@enduml