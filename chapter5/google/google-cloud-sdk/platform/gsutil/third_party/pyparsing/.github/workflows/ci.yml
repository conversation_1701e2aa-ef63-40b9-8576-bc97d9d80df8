name: Continuous Integration
on:
  push:
    branches:
      - master

  pull_request:
    paths:
      - .github/workflows/ci.yml
      - pyparsing/*
      - pyproject.toml
      - tox.ini

permissions:
  contents: read

jobs:
  tests:
    name: Unit tests
    runs-on: ${{ matrix.os || 'ubuntu-latest' }}
    strategy:
      matrix:
        os: ["ubuntu-latest"]
        toxenv: [py]
        python-version: ["3.9", "3.10", "3.11", "3.12", "3.13.0-rc.2"]
        include:
          - python-version: "3.12"
            os: macos-latest
          - python-version: "3.11"
            toxenv: mypy-check
          - python-version: "pypy-3.9"
    env:
      TOXENV: ${{ matrix.toxenv || 'py' }}
    steps:
      - uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          python -m pip install tox railroad-diagrams Jinja2

      - name: Test
        run: tox
