
ASN.1 modules for Python
------------------------
[![PyPI](https://img.shields.io/pypi/v/pyasn1-modules.svg?maxAge=2592000)](https://pypi.python.org/pypi/pyasn1-modules)
[![Python Versions](https://img.shields.io/pypi/pyversions/pyasn1-modules.svg)](https://pypi.python.org/pypi/pyasn1-modules/)
[![Build status](https://travis-ci.org/etingof/pyasn1-modules.svg?branch=master)](https://secure.travis-ci.org/etingof/pyasn1-modules)
[![Coverage Status](https://img.shields.io/codecov/c/github/etingof/pyasn1-modules.svg)](https://codecov.io/github/etingof/pyasn1-modules/)
[![GitHub license](https://img.shields.io/badge/license-BSD-blue.svg)](https://raw.githubusercontent.com/etingof/pyasn1-modules/master/LICENSE.txt)

This is a small but growing collection of 
[ASN.1](https://www.itu.int/rec/dologin_pub.asp?lang=e&id=T-REC-X.208-198811-W!!PDF-E&type=items)
data structures expressed in Python terms using [pyasn1](https://github.com/etingof/pyasn1) data model.

If ASN.1 module you need is not present in this collection, try using
[Asn1ate](https://github.com/kimgr/asn1ate) tool that compiles ASN.1 documents
into pyasn1 code. 

Feedback
--------

If something does not work as expected, try browsing pyasn1
[mailing list archives](https://sourceforge.net/p/pyasn1/mailman/pyasn1-users/)
or post your question
[to Stack Overflow](http://stackoverflow.com/questions/ask).
If you want to contribute ASN.1 modules you have converted into pyasn1,
please send me a pull request.

Copyright (c) 2005-2017, [Ilya Etingof](mailto:<EMAIL>).
All rights reserved.

