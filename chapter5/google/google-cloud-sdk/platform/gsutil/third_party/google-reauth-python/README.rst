Google Reauth Python Library
============================

|build| |docs| |pypi|

This library provides Reauth support to Google's authentication libraries for
Python. Reauth allows using two-factor authentication for end-user credentials.

.. |build| image:: https://travis-ci.org/Google/google-reauth-python.svg?branch=master
   :target: https://travis-ci.org/Google/google-reauth-python
.. |pypi| image:: https://img.shields.io/pypi/v/google-reauth.svg
   :target: https://pypi.python.org/pypi/google-reauth

Installing
----------

You can install using `pip`_::

    $ pip install google-reauth

.. _pip: https://pip.pypa.io/en/stable/

For more information on setting up your Python development environment, please refer to `Python Development Environment Setup Guide`_ for Google Cloud Platform.

.. _`Python Development Environment Setup Guide`: https://cloud.google.com/python/setup

Contributing
------------

See `CONTRIBUTING.rst`_ for more information on how to get started.

.. _CONTRIBUTING.rst: https://github.com/GoogleCloudPlatform/google-auth-library-python/blob/master/CONTRIBUTING.rst

License
-------

Apache 2.0 - See `the LICENSE`_ for more information.

.. _the LICENSE: https://github.com/GoogleCloudPlatform/google-auth-library-python/blob/master/LICENSE
