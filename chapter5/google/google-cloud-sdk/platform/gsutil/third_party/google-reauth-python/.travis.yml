language: python
sudo: false
matrix:
  include:
  - python: 3.6
    env: NOX_SESSION=lint
  - python: 2.7
    env: NOX_SESSION="tests(py='2.7')"
  - python: 3.4
    env: NOX_SESSION="tests(py='3.4')"
  - python: 3.5
    env: NOX_SESSION="tests(py='3.5')"
  - python: 3.6
    env: NOX_SESSION="tests(py='3.6')"
cache:
  directories:
  - ${HOME}/.cache
install:
- pip install --upgrade nox-automation
script:
- nox -s "$NOX_SESSION"
