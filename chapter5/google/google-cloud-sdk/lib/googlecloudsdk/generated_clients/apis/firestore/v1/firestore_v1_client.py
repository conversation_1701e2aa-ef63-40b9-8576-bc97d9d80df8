"""Generated client library for firestore version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.firestore.v1 import firestore_v1_messages as messages


class FirestoreV1(base_api.BaseApiClient):
  """Generated client library for service firestore version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://firestore.googleapis.com/'
  MTLS_BASE_URL = 'https://firestore.mtls.googleapis.com/'

  _PACKAGE = 'firestore'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform', 'https://www.googleapis.com/auth/datastore']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'FirestoreV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new firestore handle."""
    url = url or self.BASE_URL
    super(FirestoreV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_databases_backupSchedules = self.ProjectsDatabasesBackupSchedulesService(self)
    self.projects_databases_collectionGroups_fields = self.ProjectsDatabasesCollectionGroupsFieldsService(self)
    self.projects_databases_collectionGroups_indexes = self.ProjectsDatabasesCollectionGroupsIndexesService(self)
    self.projects_databases_collectionGroups = self.ProjectsDatabasesCollectionGroupsService(self)
    self.projects_databases_documents = self.ProjectsDatabasesDocumentsService(self)
    self.projects_databases_operations = self.ProjectsDatabasesOperationsService(self)
    self.projects_databases_userCreds = self.ProjectsDatabasesUserCredsService(self)
    self.projects_databases = self.ProjectsDatabasesService(self)
    self.projects_locations_backups = self.ProjectsLocationsBackupsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsDatabasesBackupSchedulesService(base_api.BaseApiService):
    """Service class for the projects_databases_backupSchedules resource."""

    _NAME = 'projects_databases_backupSchedules'

    def __init__(self, client):
      super(FirestoreV1.ProjectsDatabasesBackupSchedulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a backup schedule on a database. At most two backup schedules can be configured on a database, one daily backup schedule and one weekly backup schedule.

      Args:
        request: (FirestoreProjectsDatabasesBackupSchedulesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleFirestoreAdminV1BackupSchedule) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/backupSchedules',
        http_method='POST',
        method_id='firestore.projects.databases.backupSchedules.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/backupSchedules',
        request_field='googleFirestoreAdminV1BackupSchedule',
        request_type_name='FirestoreProjectsDatabasesBackupSchedulesCreateRequest',
        response_type_name='GoogleFirestoreAdminV1BackupSchedule',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a backup schedule.

      Args:
        request: (FirestoreProjectsDatabasesBackupSchedulesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/backupSchedules/{backupSchedulesId}',
        http_method='DELETE',
        method_id='firestore.projects.databases.backupSchedules.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='FirestoreProjectsDatabasesBackupSchedulesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about a backup schedule.

      Args:
        request: (FirestoreProjectsDatabasesBackupSchedulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleFirestoreAdminV1BackupSchedule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/backupSchedules/{backupSchedulesId}',
        http_method='GET',
        method_id='firestore.projects.databases.backupSchedules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='FirestoreProjectsDatabasesBackupSchedulesGetRequest',
        response_type_name='GoogleFirestoreAdminV1BackupSchedule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List backup schedules.

      Args:
        request: (FirestoreProjectsDatabasesBackupSchedulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleFirestoreAdminV1ListBackupSchedulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/backupSchedules',
        http_method='GET',
        method_id='firestore.projects.databases.backupSchedules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/backupSchedules',
        request_field='',
        request_type_name='FirestoreProjectsDatabasesBackupSchedulesListRequest',
        response_type_name='GoogleFirestoreAdminV1ListBackupSchedulesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a backup schedule.

      Args:
        request: (FirestoreProjectsDatabasesBackupSchedulesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleFirestoreAdminV1BackupSchedule) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/backupSchedules/{backupSchedulesId}',
        http_method='PATCH',
        method_id='firestore.projects.databases.backupSchedules.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleFirestoreAdminV1BackupSchedule',
        request_type_name='FirestoreProjectsDatabasesBackupSchedulesPatchRequest',
        response_type_name='GoogleFirestoreAdminV1BackupSchedule',
        supports_download=False,
    )

  class ProjectsDatabasesCollectionGroupsFieldsService(base_api.BaseApiService):
    """Service class for the projects_databases_collectionGroups_fields resource."""

    _NAME = 'projects_databases_collectionGroups_fields'

    def __init__(self, client):
      super(FirestoreV1.ProjectsDatabasesCollectionGroupsFieldsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the metadata and configuration for a Field.

      Args:
        request: (FirestoreProjectsDatabasesCollectionGroupsFieldsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleFirestoreAdminV1Field) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/collectionGroups/{collectionGroupsId}/fields/{fieldsId}',
        http_method='GET',
        method_id='firestore.projects.databases.collectionGroups.fields.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='FirestoreProjectsDatabasesCollectionGroupsFieldsGetRequest',
        response_type_name='GoogleFirestoreAdminV1Field',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the field configuration and metadata for this database. Currently, FirestoreAdmin.ListFields only supports listing fields that have been explicitly overridden. To issue this query, call FirestoreAdmin.ListFields with the filter set to `indexConfig.usesAncestorConfig:false` or `ttlConfig:*`.

      Args:
        request: (FirestoreProjectsDatabasesCollectionGroupsFieldsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleFirestoreAdminV1ListFieldsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/collectionGroups/{collectionGroupsId}/fields',
        http_method='GET',
        method_id='firestore.projects.databases.collectionGroups.fields.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/fields',
        request_field='',
        request_type_name='FirestoreProjectsDatabasesCollectionGroupsFieldsListRequest',
        response_type_name='GoogleFirestoreAdminV1ListFieldsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a field configuration. Currently, field updates apply only to single field index configuration. However, calls to FirestoreAdmin.UpdateField should provide a field mask to avoid changing any configuration that the caller isn't aware of. The field mask should be specified as: `{ paths: "index_config" }`. This call returns a google.longrunning.Operation which may be used to track the status of the field update. The metadata for the operation will be the type FieldOperationMetadata. To configure the default field settings for the database, use the special `Field` with resource name: `projects/{project_id}/databases/{database_id}/collectionGroups/__default__/fields/*`.

      Args:
        request: (FirestoreProjectsDatabasesCollectionGroupsFieldsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/collectionGroups/{collectionGroupsId}/fields/{fieldsId}',
        http_method='PATCH',
        method_id='firestore.projects.databases.collectionGroups.fields.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleFirestoreAdminV1Field',
        request_type_name='FirestoreProjectsDatabasesCollectionGroupsFieldsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsDatabasesCollectionGroupsIndexesService(base_api.BaseApiService):
    """Service class for the projects_databases_collectionGroups_indexes resource."""

    _NAME = 'projects_databases_collectionGroups_indexes'

    def __init__(self, client):
      super(FirestoreV1.ProjectsDatabasesCollectionGroupsIndexesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a composite index. This returns a google.longrunning.Operation which may be used to track the status of the creation. The metadata for the operation will be the type IndexOperationMetadata.

      Args:
        request: (FirestoreProjectsDatabasesCollectionGroupsIndexesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/collectionGroups/{collectionGroupsId}/indexes',
        http_method='POST',
        method_id='firestore.projects.databases.collectionGroups.indexes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/indexes',
        request_field='googleFirestoreAdminV1Index',
        request_type_name='FirestoreProjectsDatabasesCollectionGroupsIndexesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a composite index.

      Args:
        request: (FirestoreProjectsDatabasesCollectionGroupsIndexesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/collectionGroups/{collectionGroupsId}/indexes/{indexesId}',
        http_method='DELETE',
        method_id='firestore.projects.databases.collectionGroups.indexes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='FirestoreProjectsDatabasesCollectionGroupsIndexesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a composite index.

      Args:
        request: (FirestoreProjectsDatabasesCollectionGroupsIndexesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleFirestoreAdminV1Index) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/collectionGroups/{collectionGroupsId}/indexes/{indexesId}',
        http_method='GET',
        method_id='firestore.projects.databases.collectionGroups.indexes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='FirestoreProjectsDatabasesCollectionGroupsIndexesGetRequest',
        response_type_name='GoogleFirestoreAdminV1Index',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists composite indexes.

      Args:
        request: (FirestoreProjectsDatabasesCollectionGroupsIndexesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleFirestoreAdminV1ListIndexesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/collectionGroups/{collectionGroupsId}/indexes',
        http_method='GET',
        method_id='firestore.projects.databases.collectionGroups.indexes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/indexes',
        request_field='',
        request_type_name='FirestoreProjectsDatabasesCollectionGroupsIndexesListRequest',
        response_type_name='GoogleFirestoreAdminV1ListIndexesResponse',
        supports_download=False,
    )

  class ProjectsDatabasesCollectionGroupsService(base_api.BaseApiService):
    """Service class for the projects_databases_collectionGroups resource."""

    _NAME = 'projects_databases_collectionGroups'

    def __init__(self, client):
      super(FirestoreV1.ProjectsDatabasesCollectionGroupsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsDatabasesDocumentsService(base_api.BaseApiService):
    """Service class for the projects_databases_documents resource."""

    _NAME = 'projects_databases_documents'

    def __init__(self, client):
      super(FirestoreV1.ProjectsDatabasesDocumentsService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchGet(self, request, global_params=None):
      r"""Gets multiple documents. Documents returned by this method are not guaranteed to be returned in the same order that they were requested.

      Args:
        request: (FirestoreProjectsDatabasesDocumentsBatchGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BatchGetDocumentsResponse) The response message.
      """
      config = self.GetMethodConfig('BatchGet')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchGet.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/documents:batchGet',
        http_method='POST',
        method_id='firestore.projects.databases.documents.batchGet',
        ordered_params=['database'],
        path_params=['database'],
        query_params=[],
        relative_path='v1/{+database}/documents:batchGet',
        request_field='batchGetDocumentsRequest',
        request_type_name='FirestoreProjectsDatabasesDocumentsBatchGetRequest',
        response_type_name='BatchGetDocumentsResponse',
        supports_download=False,
    )

    def BatchWrite(self, request, global_params=None):
      r"""Applies a batch of write operations. The BatchWrite method does not apply the write operations atomically and can apply them out of order. Method does not allow more than one write per document. Each write succeeds or fails independently. See the BatchWriteResponse for the success status of each write. If you require an atomically applied set of writes, use Commit instead.

      Args:
        request: (FirestoreProjectsDatabasesDocumentsBatchWriteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BatchWriteResponse) The response message.
      """
      config = self.GetMethodConfig('BatchWrite')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchWrite.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/documents:batchWrite',
        http_method='POST',
        method_id='firestore.projects.databases.documents.batchWrite',
        ordered_params=['database'],
        path_params=['database'],
        query_params=[],
        relative_path='v1/{+database}/documents:batchWrite',
        request_field='batchWriteRequest',
        request_type_name='FirestoreProjectsDatabasesDocumentsBatchWriteRequest',
        response_type_name='BatchWriteResponse',
        supports_download=False,
    )

    def BeginTransaction(self, request, global_params=None):
      r"""Starts a new transaction.

      Args:
        request: (FirestoreProjectsDatabasesDocumentsBeginTransactionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BeginTransactionResponse) The response message.
      """
      config = self.GetMethodConfig('BeginTransaction')
      return self._RunMethod(
          config, request, global_params=global_params)

    BeginTransaction.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/documents:beginTransaction',
        http_method='POST',
        method_id='firestore.projects.databases.documents.beginTransaction',
        ordered_params=['database'],
        path_params=['database'],
        query_params=[],
        relative_path='v1/{+database}/documents:beginTransaction',
        request_field='beginTransactionRequest',
        request_type_name='FirestoreProjectsDatabasesDocumentsBeginTransactionRequest',
        response_type_name='BeginTransactionResponse',
        supports_download=False,
    )

    def Commit(self, request, global_params=None):
      r"""Commits a transaction, while optionally updating documents.

      Args:
        request: (FirestoreProjectsDatabasesDocumentsCommitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CommitResponse) The response message.
      """
      config = self.GetMethodConfig('Commit')
      return self._RunMethod(
          config, request, global_params=global_params)

    Commit.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/documents:commit',
        http_method='POST',
        method_id='firestore.projects.databases.documents.commit',
        ordered_params=['database'],
        path_params=['database'],
        query_params=[],
        relative_path='v1/{+database}/documents:commit',
        request_field='commitRequest',
        request_type_name='FirestoreProjectsDatabasesDocumentsCommitRequest',
        response_type_name='CommitResponse',
        supports_download=False,
    )

    def CreateDocument(self, request, global_params=None):
      r"""Creates a new document.

      Args:
        request: (FirestoreProjectsDatabasesDocumentsCreateDocumentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Document) The response message.
      """
      config = self.GetMethodConfig('CreateDocument')
      return self._RunMethod(
          config, request, global_params=global_params)

    CreateDocument.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/documents/{documentsId}/{collectionId}',
        http_method='POST',
        method_id='firestore.projects.databases.documents.createDocument',
        ordered_params=['parent', 'collectionId'],
        path_params=['collectionId', 'parent'],
        query_params=['documentId', 'mask_fieldPaths'],
        relative_path='v1/{+parent}/{collectionId}',
        request_field='document',
        request_type_name='FirestoreProjectsDatabasesDocumentsCreateDocumentRequest',
        response_type_name='Document',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a document.

      Args:
        request: (FirestoreProjectsDatabasesDocumentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/documents/{documentsId}/{documentsId1}',
        http_method='DELETE',
        method_id='firestore.projects.databases.documents.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['currentDocument_exists', 'currentDocument_updateTime'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='FirestoreProjectsDatabasesDocumentsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a single document.

      Args:
        request: (FirestoreProjectsDatabasesDocumentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Document) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/documents/{documentsId}/{documentsId1}',
        http_method='GET',
        method_id='firestore.projects.databases.documents.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['mask_fieldPaths', 'readTime', 'transaction'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='FirestoreProjectsDatabasesDocumentsGetRequest',
        response_type_name='Document',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists documents.

      Args:
        request: (FirestoreProjectsDatabasesDocumentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDocumentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/documents/{documentsId}/{documentsId1}/{collectionId}',
        http_method='GET',
        method_id='firestore.projects.databases.documents.list',
        ordered_params=['parent', 'collectionId'],
        path_params=['collectionId', 'parent'],
        query_params=['mask_fieldPaths', 'orderBy', 'pageSize', 'pageToken', 'readTime', 'showMissing', 'transaction'],
        relative_path='v1/{+parent}/{collectionId}',
        request_field='',
        request_type_name='FirestoreProjectsDatabasesDocumentsListRequest',
        response_type_name='ListDocumentsResponse',
        supports_download=False,
    )

    def ListCollectionIds(self, request, global_params=None):
      r"""Lists all the collection IDs underneath a document.

      Args:
        request: (FirestoreProjectsDatabasesDocumentsListCollectionIdsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListCollectionIdsResponse) The response message.
      """
      config = self.GetMethodConfig('ListCollectionIds')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListCollectionIds.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/documents/{documentsId}/{documentsId1}:listCollectionIds',
        http_method='POST',
        method_id='firestore.projects.databases.documents.listCollectionIds',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}:listCollectionIds',
        request_field='listCollectionIdsRequest',
        request_type_name='FirestoreProjectsDatabasesDocumentsListCollectionIdsRequest',
        response_type_name='ListCollectionIdsResponse',
        supports_download=False,
    )

    def ListDocuments(self, request, global_params=None):
      r"""Lists documents.

      Args:
        request: (FirestoreProjectsDatabasesDocumentsListDocumentsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDocumentsResponse) The response message.
      """
      config = self.GetMethodConfig('ListDocuments')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListDocuments.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/documents/{collectionId}',
        http_method='GET',
        method_id='firestore.projects.databases.documents.listDocuments',
        ordered_params=['parent', 'collectionId'],
        path_params=['collectionId', 'parent'],
        query_params=['mask_fieldPaths', 'orderBy', 'pageSize', 'pageToken', 'readTime', 'showMissing', 'transaction'],
        relative_path='v1/{+parent}/{collectionId}',
        request_field='',
        request_type_name='FirestoreProjectsDatabasesDocumentsListDocumentsRequest',
        response_type_name='ListDocumentsResponse',
        supports_download=False,
    )

    def Listen(self, request, global_params=None):
      r"""Listens to changes. This method is only available via gRPC or WebChannel (not REST).

      Args:
        request: (FirestoreProjectsDatabasesDocumentsListenRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListenResponse) The response message.
      """
      config = self.GetMethodConfig('Listen')
      return self._RunMethod(
          config, request, global_params=global_params)

    Listen.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/documents:listen',
        http_method='POST',
        method_id='firestore.projects.databases.documents.listen',
        ordered_params=['database'],
        path_params=['database'],
        query_params=[],
        relative_path='v1/{+database}/documents:listen',
        request_field='listenRequest',
        request_type_name='FirestoreProjectsDatabasesDocumentsListenRequest',
        response_type_name='ListenResponse',
        supports_download=False,
    )

    def PartitionQuery(self, request, global_params=None):
      r"""Partitions a query by returning partition cursors that can be used to run the query in parallel. The returned partition cursors are split points that can be used by RunQuery as starting/end points for the query results.

      Args:
        request: (FirestoreProjectsDatabasesDocumentsPartitionQueryRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PartitionQueryResponse) The response message.
      """
      config = self.GetMethodConfig('PartitionQuery')
      return self._RunMethod(
          config, request, global_params=global_params)

    PartitionQuery.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/documents/{documentsId}/{documentsId1}:partitionQuery',
        http_method='POST',
        method_id='firestore.projects.databases.documents.partitionQuery',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}:partitionQuery',
        request_field='partitionQueryRequest',
        request_type_name='FirestoreProjectsDatabasesDocumentsPartitionQueryRequest',
        response_type_name='PartitionQueryResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates or inserts a document.

      Args:
        request: (FirestoreProjectsDatabasesDocumentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Document) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/documents/{documentsId}/{documentsId1}',
        http_method='PATCH',
        method_id='firestore.projects.databases.documents.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['currentDocument_exists', 'currentDocument_updateTime', 'mask_fieldPaths', 'updateMask_fieldPaths'],
        relative_path='v1/{+name}',
        request_field='document',
        request_type_name='FirestoreProjectsDatabasesDocumentsPatchRequest',
        response_type_name='Document',
        supports_download=False,
    )

    def Rollback(self, request, global_params=None):
      r"""Rolls back a transaction.

      Args:
        request: (FirestoreProjectsDatabasesDocumentsRollbackRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Rollback')
      return self._RunMethod(
          config, request, global_params=global_params)

    Rollback.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/documents:rollback',
        http_method='POST',
        method_id='firestore.projects.databases.documents.rollback',
        ordered_params=['database'],
        path_params=['database'],
        query_params=[],
        relative_path='v1/{+database}/documents:rollback',
        request_field='rollbackRequest',
        request_type_name='FirestoreProjectsDatabasesDocumentsRollbackRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def RunAggregationQuery(self, request, global_params=None):
      r"""Runs an aggregation query. Rather than producing Document results like Firestore.RunQuery, this API allows running an aggregation to produce a series of AggregationResult server-side. High-Level Example: ``` -- Return the number of documents in table given a filter. SELECT COUNT(*) FROM ( SELECT * FROM k where a = true ); ```.

      Args:
        request: (FirestoreProjectsDatabasesDocumentsRunAggregationQueryRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RunAggregationQueryResponse) The response message.
      """
      config = self.GetMethodConfig('RunAggregationQuery')
      return self._RunMethod(
          config, request, global_params=global_params)

    RunAggregationQuery.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/documents/{documentsId}/{documentsId1}:runAggregationQuery',
        http_method='POST',
        method_id='firestore.projects.databases.documents.runAggregationQuery',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}:runAggregationQuery',
        request_field='runAggregationQueryRequest',
        request_type_name='FirestoreProjectsDatabasesDocumentsRunAggregationQueryRequest',
        response_type_name='RunAggregationQueryResponse',
        supports_download=False,
    )

    def RunQuery(self, request, global_params=None):
      r"""Runs a query.

      Args:
        request: (FirestoreProjectsDatabasesDocumentsRunQueryRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RunQueryResponse) The response message.
      """
      config = self.GetMethodConfig('RunQuery')
      return self._RunMethod(
          config, request, global_params=global_params)

    RunQuery.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/documents/{documentsId}/{documentsId1}:runQuery',
        http_method='POST',
        method_id='firestore.projects.databases.documents.runQuery',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}:runQuery',
        request_field='runQueryRequest',
        request_type_name='FirestoreProjectsDatabasesDocumentsRunQueryRequest',
        response_type_name='RunQueryResponse',
        supports_download=False,
    )

    def Write(self, request, global_params=None):
      r"""Streams batches of document updates and deletes, in order. This method is only available via gRPC or WebChannel (not REST).

      Args:
        request: (FirestoreProjectsDatabasesDocumentsWriteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WriteResponse) The response message.
      """
      config = self.GetMethodConfig('Write')
      return self._RunMethod(
          config, request, global_params=global_params)

    Write.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/documents:write',
        http_method='POST',
        method_id='firestore.projects.databases.documents.write',
        ordered_params=['database'],
        path_params=['database'],
        query_params=[],
        relative_path='v1/{+database}/documents:write',
        request_field='writeRequest',
        request_type_name='FirestoreProjectsDatabasesDocumentsWriteRequest',
        response_type_name='WriteResponse',
        supports_download=False,
    )

  class ProjectsDatabasesOperationsService(base_api.BaseApiService):
    """Service class for the projects_databases_operations resource."""

    _NAME = 'projects_databases_operations'

    def __init__(self, client):
      super(FirestoreV1.ProjectsDatabasesOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (FirestoreProjectsDatabasesOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='firestore.projects.databases.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='googleLongrunningCancelOperationRequest',
        request_type_name='FirestoreProjectsDatabasesOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (FirestoreProjectsDatabasesOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='firestore.projects.databases.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='FirestoreProjectsDatabasesOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (FirestoreProjectsDatabasesOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/operations/{operationsId}',
        http_method='GET',
        method_id='firestore.projects.databases.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='FirestoreProjectsDatabasesOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (FirestoreProjectsDatabasesOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/operations',
        http_method='GET',
        method_id='firestore.projects.databases.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='FirestoreProjectsDatabasesOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

  class ProjectsDatabasesUserCredsService(base_api.BaseApiService):
    """Service class for the projects_databases_userCreds resource."""

    _NAME = 'projects_databases_userCreds'

    def __init__(self, client):
      super(FirestoreV1.ProjectsDatabasesUserCredsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a user creds.

      Args:
        request: (FirestoreProjectsDatabasesUserCredsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleFirestoreAdminV1UserCreds) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/userCreds',
        http_method='POST',
        method_id='firestore.projects.databases.userCreds.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['userCredsId'],
        relative_path='v1/{+parent}/userCreds',
        request_field='googleFirestoreAdminV1UserCreds',
        request_type_name='FirestoreProjectsDatabasesUserCredsCreateRequest',
        response_type_name='GoogleFirestoreAdminV1UserCreds',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a user creds.

      Args:
        request: (FirestoreProjectsDatabasesUserCredsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/userCreds/{userCredsId}',
        http_method='DELETE',
        method_id='firestore.projects.databases.userCreds.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='FirestoreProjectsDatabasesUserCredsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Disable(self, request, global_params=None):
      r"""Disables a user creds. No-op if the user creds are already disabled.

      Args:
        request: (FirestoreProjectsDatabasesUserCredsDisableRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleFirestoreAdminV1UserCreds) The response message.
      """
      config = self.GetMethodConfig('Disable')
      return self._RunMethod(
          config, request, global_params=global_params)

    Disable.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/userCreds/{userCredsId}:disable',
        http_method='POST',
        method_id='firestore.projects.databases.userCreds.disable',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:disable',
        request_field='googleFirestoreAdminV1DisableUserCredsRequest',
        request_type_name='FirestoreProjectsDatabasesUserCredsDisableRequest',
        response_type_name='GoogleFirestoreAdminV1UserCreds',
        supports_download=False,
    )

    def Enable(self, request, global_params=None):
      r"""Enables a user creds. No-op if the user creds are already enabled.

      Args:
        request: (FirestoreProjectsDatabasesUserCredsEnableRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleFirestoreAdminV1UserCreds) The response message.
      """
      config = self.GetMethodConfig('Enable')
      return self._RunMethod(
          config, request, global_params=global_params)

    Enable.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/userCreds/{userCredsId}:enable',
        http_method='POST',
        method_id='firestore.projects.databases.userCreds.enable',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:enable',
        request_field='googleFirestoreAdminV1EnableUserCredsRequest',
        request_type_name='FirestoreProjectsDatabasesUserCredsEnableRequest',
        response_type_name='GoogleFirestoreAdminV1UserCreds',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a user creds resource. Note that the returned resource does not contain the secret value itself.

      Args:
        request: (FirestoreProjectsDatabasesUserCredsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleFirestoreAdminV1UserCreds) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/userCreds/{userCredsId}',
        http_method='GET',
        method_id='firestore.projects.databases.userCreds.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='FirestoreProjectsDatabasesUserCredsGetRequest',
        response_type_name='GoogleFirestoreAdminV1UserCreds',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List all user creds in the database. Note that the returned resource does not contain the secret value itself.

      Args:
        request: (FirestoreProjectsDatabasesUserCredsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleFirestoreAdminV1ListUserCredsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/userCreds',
        http_method='GET',
        method_id='firestore.projects.databases.userCreds.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/userCreds',
        request_field='',
        request_type_name='FirestoreProjectsDatabasesUserCredsListRequest',
        response_type_name='GoogleFirestoreAdminV1ListUserCredsResponse',
        supports_download=False,
    )

    def ResetPassword(self, request, global_params=None):
      r"""Resets the password of a user creds.

      Args:
        request: (FirestoreProjectsDatabasesUserCredsResetPasswordRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleFirestoreAdminV1UserCreds) The response message.
      """
      config = self.GetMethodConfig('ResetPassword')
      return self._RunMethod(
          config, request, global_params=global_params)

    ResetPassword.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}/userCreds/{userCredsId}:resetPassword',
        http_method='POST',
        method_id='firestore.projects.databases.userCreds.resetPassword',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:resetPassword',
        request_field='googleFirestoreAdminV1ResetUserPasswordRequest',
        request_type_name='FirestoreProjectsDatabasesUserCredsResetPasswordRequest',
        response_type_name='GoogleFirestoreAdminV1UserCreds',
        supports_download=False,
    )

  class ProjectsDatabasesService(base_api.BaseApiService):
    """Service class for the projects_databases resource."""

    _NAME = 'projects_databases'

    def __init__(self, client):
      super(FirestoreV1.ProjectsDatabasesService, self).__init__(client)
      self._upload_configs = {
          }

    def BulkDeleteDocuments(self, request, global_params=None):
      r"""Bulk deletes a subset of documents from Google Cloud Firestore. Documents created or updated after the underlying system starts to process the request will not be deleted. The bulk delete occurs in the background and its progress can be monitored and managed via the Operation resource that is created. For more details on bulk delete behavior, refer to: https://cloud.google.com/firestore/docs/manage-data/bulk-delete.

      Args:
        request: (FirestoreProjectsDatabasesBulkDeleteDocumentsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BulkDeleteDocuments')
      return self._RunMethod(
          config, request, global_params=global_params)

    BulkDeleteDocuments.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}:bulkDeleteDocuments',
        http_method='POST',
        method_id='firestore.projects.databases.bulkDeleteDocuments',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:bulkDeleteDocuments',
        request_field='googleFirestoreAdminV1BulkDeleteDocumentsRequest',
        request_type_name='FirestoreProjectsDatabasesBulkDeleteDocumentsRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Clone(self, request, global_params=None):
      r"""Creates a new database by cloning an existing one. The new database must be in the same cloud region or multi-region location as the existing database. This behaves similar to FirestoreAdmin.CreateDatabase except instead of creating a new empty database, a new database is created with the database type, index configuration, and documents from an existing database. The long-running operation can be used to track the progress of the clone, with the Operation's metadata field type being the CloneDatabaseMetadata. The response type is the Database if the clone was successful. The new database is not readable or writeable until the LRO has completed.

      Args:
        request: (FirestoreProjectsDatabasesCloneRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Clone')
      return self._RunMethod(
          config, request, global_params=global_params)

    Clone.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases:clone',
        http_method='POST',
        method_id='firestore.projects.databases.clone',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/databases:clone',
        request_field='googleFirestoreAdminV1CloneDatabaseRequest',
        request_type_name='FirestoreProjectsDatabasesCloneRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Create a database.

      Args:
        request: (FirestoreProjectsDatabasesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases',
        http_method='POST',
        method_id='firestore.projects.databases.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['databaseId'],
        relative_path='v1/{+parent}/databases',
        request_field='googleFirestoreAdminV1Database',
        request_type_name='FirestoreProjectsDatabasesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a database.

      Args:
        request: (FirestoreProjectsDatabasesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}',
        http_method='DELETE',
        method_id='firestore.projects.databases.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='FirestoreProjectsDatabasesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def ExportDocuments(self, request, global_params=None):
      r"""Exports a copy of all or a subset of documents from Google Cloud Firestore to another storage system, such as Google Cloud Storage. Recent updates to documents may not be reflected in the export. The export occurs in the background and its progress can be monitored and managed via the Operation resource that is created. The output of an export may only be used once the associated operation is done. If an export operation is cancelled before completion it may leave partial data behind in Google Cloud Storage. For more details on export behavior and output format, refer to: https://cloud.google.com/firestore/docs/manage-data/export-import.

      Args:
        request: (FirestoreProjectsDatabasesExportDocumentsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('ExportDocuments')
      return self._RunMethod(
          config, request, global_params=global_params)

    ExportDocuments.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}:exportDocuments',
        http_method='POST',
        method_id='firestore.projects.databases.exportDocuments',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:exportDocuments',
        request_field='googleFirestoreAdminV1ExportDocumentsRequest',
        request_type_name='FirestoreProjectsDatabasesExportDocumentsRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about a database.

      Args:
        request: (FirestoreProjectsDatabasesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleFirestoreAdminV1Database) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}',
        http_method='GET',
        method_id='firestore.projects.databases.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='FirestoreProjectsDatabasesGetRequest',
        response_type_name='GoogleFirestoreAdminV1Database',
        supports_download=False,
    )

    def ImportDocuments(self, request, global_params=None):
      r"""Imports documents into Google Cloud Firestore. Existing documents with the same name are overwritten. The import occurs in the background and its progress can be monitored and managed via the Operation resource that is created. If an ImportDocuments operation is cancelled, it is possible that a subset of the data has already been imported to Cloud Firestore.

      Args:
        request: (FirestoreProjectsDatabasesImportDocumentsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('ImportDocuments')
      return self._RunMethod(
          config, request, global_params=global_params)

    ImportDocuments.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}:importDocuments',
        http_method='POST',
        method_id='firestore.projects.databases.importDocuments',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:importDocuments',
        request_field='googleFirestoreAdminV1ImportDocumentsRequest',
        request_type_name='FirestoreProjectsDatabasesImportDocumentsRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List all the databases in the project.

      Args:
        request: (FirestoreProjectsDatabasesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleFirestoreAdminV1ListDatabasesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases',
        http_method='GET',
        method_id='firestore.projects.databases.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['showDeleted'],
        relative_path='v1/{+parent}/databases',
        request_field='',
        request_type_name='FirestoreProjectsDatabasesListRequest',
        response_type_name='GoogleFirestoreAdminV1ListDatabasesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a database.

      Args:
        request: (FirestoreProjectsDatabasesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases/{databasesId}',
        http_method='PATCH',
        method_id='firestore.projects.databases.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleFirestoreAdminV1Database',
        request_type_name='FirestoreProjectsDatabasesPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Restore(self, request, global_params=None):
      r"""Creates a new database by restoring from an existing backup. The new database must be in the same cloud region or multi-region location as the existing backup. This behaves similar to FirestoreAdmin.CreateDatabase except instead of creating a new empty database, a new database is created with the database type, index configuration, and documents from an existing backup. The long-running operation can be used to track the progress of the restore, with the Operation's metadata field type being the RestoreDatabaseMetadata. The response type is the Database if the restore was successful. The new database is not readable or writeable until the LRO has completed.

      Args:
        request: (FirestoreProjectsDatabasesRestoreRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Restore')
      return self._RunMethod(
          config, request, global_params=global_params)

    Restore.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/databases:restore',
        http_method='POST',
        method_id='firestore.projects.databases.restore',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/databases:restore',
        request_field='googleFirestoreAdminV1RestoreDatabaseRequest',
        request_type_name='FirestoreProjectsDatabasesRestoreRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsBackupsService(base_api.BaseApiService):
    """Service class for the projects_locations_backups resource."""

    _NAME = 'projects_locations_backups'

    def __init__(self, client):
      super(FirestoreV1.ProjectsLocationsBackupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a backup.

      Args:
        request: (FirestoreProjectsLocationsBackupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backups/{backupsId}',
        http_method='DELETE',
        method_id='firestore.projects.locations.backups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='FirestoreProjectsLocationsBackupsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about a backup.

      Args:
        request: (FirestoreProjectsLocationsBackupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleFirestoreAdminV1Backup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backups/{backupsId}',
        http_method='GET',
        method_id='firestore.projects.locations.backups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='FirestoreProjectsLocationsBackupsGetRequest',
        response_type_name='GoogleFirestoreAdminV1Backup',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all the backups.

      Args:
        request: (FirestoreProjectsLocationsBackupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleFirestoreAdminV1ListBackupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/backups',
        http_method='GET',
        method_id='firestore.projects.locations.backups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter'],
        relative_path='v1/{+parent}/backups',
        request_field='',
        request_type_name='FirestoreProjectsLocationsBackupsListRequest',
        response_type_name='GoogleFirestoreAdminV1ListBackupsResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(FirestoreV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (FirestoreProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='firestore.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='FirestoreProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (FirestoreProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='firestore.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/locations',
        request_field='',
        request_type_name='FirestoreProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(FirestoreV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
