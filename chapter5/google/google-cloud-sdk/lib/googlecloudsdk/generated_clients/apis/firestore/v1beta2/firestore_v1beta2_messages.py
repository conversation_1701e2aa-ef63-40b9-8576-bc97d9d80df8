"""Generated message classes for firestore version v1beta2.

Accesses the NoSQL document database built for automatic scaling, high
performance, and ease of application development.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'firestore'


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class FirestoreProjectsDatabasesCollectionGroupsFieldsGetRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesCollectionGroupsFieldsGetRequest object.

  Fields:
    name: A name of the form `projects/{project_id}/databases/{database_id}/co
      llectionGroups/{collection_id}/fields/{field_id}`
  """

  name = _messages.StringField(1, required=True)


class FirestoreProjectsDatabasesCollectionGroupsFieldsListRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesCollectionGroupsFieldsListRequest object.

  Fields:
    filter: The filter to apply to list results. Currently,
      FirestoreAdmin.ListFields only supports listing fields that have been
      explicitly overridden. To issue this query, call
      FirestoreAdmin.ListFields with the filter set to
      `indexConfig.usesAncestorConfig:false`.
    pageSize: The number of results to return.
    pageToken: A page token, returned from a previous call to
      FirestoreAdmin.ListFields, that may be used to get the next page of
      results.
    parent: A parent name of the form `projects/{project_id}/databases/{databa
      se_id}/collectionGroups/{collection_id}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class FirestoreProjectsDatabasesCollectionGroupsFieldsPatchRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesCollectionGroupsFieldsPatchRequest object.

  Fields:
    googleFirestoreAdminV1beta2Field: A GoogleFirestoreAdminV1beta2Field
      resource to be passed as the request body.
    name: A field name of the form `projects/{project_id}/databases/{database_
      id}/collectionGroups/{collection_id}/fields/{field_path}` A field path
      may be a simple field name, e.g. `address` or a path to fields within
      map_value , e.g. `address.city`, or a special field path. The only valid
      special field is `*`, which represents any field. Field paths may be
      quoted using ` (backtick). The only character that needs to be escaped
      within a quoted field path is the backtick character itself, escaped
      using a backslash. Special characters in field paths that must be quoted
      include: `*`, `.`, ``` (backtick), `[`, `]`, as well as any ascii
      symbolic characters. Examples: (Note: Comments here are written in
      markdown syntax, so there is an additional layer of backticks to
      represent a code block) `\`address.city\`` represents a field named
      `address.city`, not the map key `city` in the field `address`. `\`*\``
      represents a field named `*`, not any field. A special `Field` contains
      the default indexing settings for all fields. This field's resource name
      is: `projects/{project_id}/databases/{database_id}/collectionGroups/__de
      fault__/fields/*` Indexes defined on this `Field` will be applied to all
      fields which do not have their own `Field` index configuration.
    updateMask: A mask, relative to the field. If specified, only
      configuration specified by this field_mask will be updated in the field.
  """

  googleFirestoreAdminV1beta2Field = _messages.MessageField('GoogleFirestoreAdminV1beta2Field', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class FirestoreProjectsDatabasesCollectionGroupsIndexesCreateRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesCollectionGroupsIndexesCreateRequest object.

  Fields:
    googleFirestoreAdminV1beta2Index: A GoogleFirestoreAdminV1beta2Index
      resource to be passed as the request body.
    parent: A parent name of the form `projects/{project_id}/databases/{databa
      se_id}/collectionGroups/{collection_id}`
  """

  googleFirestoreAdminV1beta2Index = _messages.MessageField('GoogleFirestoreAdminV1beta2Index', 1)
  parent = _messages.StringField(2, required=True)


class FirestoreProjectsDatabasesCollectionGroupsIndexesDeleteRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesCollectionGroupsIndexesDeleteRequest object.

  Fields:
    name: A name of the form `projects/{project_id}/databases/{database_id}/co
      llectionGroups/{collection_id}/indexes/{index_id}`
  """

  name = _messages.StringField(1, required=True)


class FirestoreProjectsDatabasesCollectionGroupsIndexesGetRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesCollectionGroupsIndexesGetRequest object.

  Fields:
    name: A name of the form `projects/{project_id}/databases/{database_id}/co
      llectionGroups/{collection_id}/indexes/{index_id}`
  """

  name = _messages.StringField(1, required=True)


class FirestoreProjectsDatabasesCollectionGroupsIndexesListRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesCollectionGroupsIndexesListRequest object.

  Fields:
    filter: The filter to apply to list results.
    pageSize: The number of results to return.
    pageToken: A page token, returned from a previous call to
      FirestoreAdmin.ListIndexes, that may be used to get the next page of
      results.
    parent: A parent name of the form `projects/{project_id}/databases/{databa
      se_id}/collectionGroups/{collection_id}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class FirestoreProjectsDatabasesExportDocumentsRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesExportDocumentsRequest object.

  Fields:
    googleFirestoreAdminV1beta2ExportDocumentsRequest: A
      GoogleFirestoreAdminV1beta2ExportDocumentsRequest resource to be passed
      as the request body.
    name: Database to export. Should be of the form:
      `projects/{project_id}/databases/{database_id}`.
  """

  googleFirestoreAdminV1beta2ExportDocumentsRequest = _messages.MessageField('GoogleFirestoreAdminV1beta2ExportDocumentsRequest', 1)
  name = _messages.StringField(2, required=True)


class FirestoreProjectsDatabasesImportDocumentsRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesImportDocumentsRequest object.

  Fields:
    googleFirestoreAdminV1beta2ImportDocumentsRequest: A
      GoogleFirestoreAdminV1beta2ImportDocumentsRequest resource to be passed
      as the request body.
    name: Database to import into. Should be of the form:
      `projects/{project_id}/databases/{database_id}`.
  """

  googleFirestoreAdminV1beta2ImportDocumentsRequest = _messages.MessageField('GoogleFirestoreAdminV1beta2ImportDocumentsRequest', 1)
  name = _messages.StringField(2, required=True)


class GoogleFirestoreAdminV1BulkDeleteDocumentsMetadata(_messages.Message):
  r"""Metadata for google.longrunning.Operation results from
  FirestoreAdmin.BulkDeleteDocuments.

  Enums:
    OperationStateValueValuesEnum: The state of the operation.

  Fields:
    collectionIds: The IDs of the collection groups that are being deleted.
    endTime: The time this operation completed. Will be unset if operation
      still in progress.
    namespaceIds: Which namespace IDs are being deleted.
    operationState: The state of the operation.
    progressBytes: The progress, in bytes, of this operation.
    progressDocuments: The progress, in documents, of this operation.
    snapshotTime: The timestamp that corresponds to the version of the
      database that is being read to get the list of documents to delete. This
      time can also be used as the timestamp of PITR in case of disaster
      recovery (subject to PITR window limit).
    startTime: The time this operation started.
  """

  class OperationStateValueValuesEnum(_messages.Enum):
    r"""The state of the operation.

    Values:
      OPERATION_STATE_UNSPECIFIED: Unspecified.
      INITIALIZING: Request is being prepared for processing.
      PROCESSING: Request is actively being processed.
      CANCELLING: Request is in the process of being cancelled after user
        called google.longrunning.Operations.CancelOperation on the operation.
      FINALIZING: Request has been processed and is in its finalization stage.
      SUCCESSFUL: Request has completed successfully.
      FAILED: Request has finished being processed, but encountered an error.
      CANCELLED: Request has finished being cancelled after user called
        google.longrunning.Operations.CancelOperation.
    """
    OPERATION_STATE_UNSPECIFIED = 0
    INITIALIZING = 1
    PROCESSING = 2
    CANCELLING = 3
    FINALIZING = 4
    SUCCESSFUL = 5
    FAILED = 6
    CANCELLED = 7

  collectionIds = _messages.StringField(1, repeated=True)
  endTime = _messages.StringField(2)
  namespaceIds = _messages.StringField(3, repeated=True)
  operationState = _messages.EnumField('OperationStateValueValuesEnum', 4)
  progressBytes = _messages.MessageField('GoogleFirestoreAdminV1Progress', 5)
  progressDocuments = _messages.MessageField('GoogleFirestoreAdminV1Progress', 6)
  snapshotTime = _messages.StringField(7)
  startTime = _messages.StringField(8)


class GoogleFirestoreAdminV1CloneDatabaseMetadata(_messages.Message):
  r"""Metadata for the long-running operation from the CloneDatabase request.

  Enums:
    OperationStateValueValuesEnum: The operation state of the clone.

  Fields:
    database: The name of the database being cloned to.
    endTime: The time the clone finished, unset for ongoing clones.
    operationState: The operation state of the clone.
    pitrSnapshot: The snapshot from which this database was cloned.
    progressPercentage: How far along the clone is as an estimated percentage
      of remaining time.
    startTime: The time the clone was started.
  """

  class OperationStateValueValuesEnum(_messages.Enum):
    r"""The operation state of the clone.

    Values:
      OPERATION_STATE_UNSPECIFIED: Unspecified.
      INITIALIZING: Request is being prepared for processing.
      PROCESSING: Request is actively being processed.
      CANCELLING: Request is in the process of being cancelled after user
        called google.longrunning.Operations.CancelOperation on the operation.
      FINALIZING: Request has been processed and is in its finalization stage.
      SUCCESSFUL: Request has completed successfully.
      FAILED: Request has finished being processed, but encountered an error.
      CANCELLED: Request has finished being cancelled after user called
        google.longrunning.Operations.CancelOperation.
    """
    OPERATION_STATE_UNSPECIFIED = 0
    INITIALIZING = 1
    PROCESSING = 2
    CANCELLING = 3
    FINALIZING = 4
    SUCCESSFUL = 5
    FAILED = 6
    CANCELLED = 7

  database = _messages.StringField(1)
  endTime = _messages.StringField(2)
  operationState = _messages.EnumField('OperationStateValueValuesEnum', 3)
  pitrSnapshot = _messages.MessageField('GoogleFirestoreAdminV1PitrSnapshot', 4)
  progressPercentage = _messages.MessageField('GoogleFirestoreAdminV1Progress', 5)
  startTime = _messages.StringField(6)


class GoogleFirestoreAdminV1CreateDatabaseMetadata(_messages.Message):
  r"""Metadata related to the create database operation."""


class GoogleFirestoreAdminV1DeleteDatabaseMetadata(_messages.Message):
  r"""Metadata related to the delete database operation."""


class GoogleFirestoreAdminV1PitrSnapshot(_messages.Message):
  r"""A consistent snapshot of a database at a specific point in time. A PITR
  (Point-in-time recovery) snapshot with previous versions of a database's
  data is available for every minute up to the associated database's data
  retention period. If the PITR feature is enabled, the retention period is 7
  days; otherwise, it is one hour.

  Fields:
    database: Required. The name of the database that this was a snapshot of.
      Format: `projects/{project}/databases/{database}`.
    databaseUid: Output only. Public UUID of the database the snapshot was
      associated with.
    snapshotTime: Required. Snapshot time of the database.
  """

  database = _messages.StringField(1)
  databaseUid = _messages.BytesField(2)
  snapshotTime = _messages.StringField(3)


class GoogleFirestoreAdminV1Progress(_messages.Message):
  r"""Describes the progress of the operation. Unit of work is generic and
  must be interpreted based on where Progress is used.

  Fields:
    completedWork: The amount of work completed.
    estimatedWork: The amount of work estimated.
  """

  completedWork = _messages.IntegerField(1)
  estimatedWork = _messages.IntegerField(2)


class GoogleFirestoreAdminV1RestoreDatabaseMetadata(_messages.Message):
  r"""Metadata for the long-running operation from the RestoreDatabase
  request.

  Enums:
    OperationStateValueValuesEnum: The operation state of the restore.

  Fields:
    backup: The name of the backup restoring from.
    database: The name of the database being restored to.
    endTime: The time the restore finished, unset for ongoing restores.
    operationState: The operation state of the restore.
    progressPercentage: How far along the restore is as an estimated
      percentage of remaining time.
    startTime: The time the restore was started.
  """

  class OperationStateValueValuesEnum(_messages.Enum):
    r"""The operation state of the restore.

    Values:
      OPERATION_STATE_UNSPECIFIED: Unspecified.
      INITIALIZING: Request is being prepared for processing.
      PROCESSING: Request is actively being processed.
      CANCELLING: Request is in the process of being cancelled after user
        called google.longrunning.Operations.CancelOperation on the operation.
      FINALIZING: Request has been processed and is in its finalization stage.
      SUCCESSFUL: Request has completed successfully.
      FAILED: Request has finished being processed, but encountered an error.
      CANCELLED: Request has finished being cancelled after user called
        google.longrunning.Operations.CancelOperation.
    """
    OPERATION_STATE_UNSPECIFIED = 0
    INITIALIZING = 1
    PROCESSING = 2
    CANCELLING = 3
    FINALIZING = 4
    SUCCESSFUL = 5
    FAILED = 6
    CANCELLED = 7

  backup = _messages.StringField(1)
  database = _messages.StringField(2)
  endTime = _messages.StringField(3)
  operationState = _messages.EnumField('OperationStateValueValuesEnum', 4)
  progressPercentage = _messages.MessageField('GoogleFirestoreAdminV1Progress', 5)
  startTime = _messages.StringField(6)


class GoogleFirestoreAdminV1UpdateDatabaseMetadata(_messages.Message):
  r"""Metadata related to the update database operation."""


class GoogleFirestoreAdminV1beta2ExportDocumentsMetadata(_messages.Message):
  r"""Metadata for google.longrunning.Operation results from
  FirestoreAdmin.ExportDocuments.

  Enums:
    OperationStateValueValuesEnum: The state of the export operation.

  Fields:
    collectionIds: Which collection ids are being exported.
    endTime: The time this operation completed. Will be unset if operation
      still in progress.
    operationState: The state of the export operation.
    outputUriPrefix: Where the entities are being exported to.
    progressBytes: The progress, in bytes, of this operation.
    progressDocuments: The progress, in documents, of this operation.
    startTime: The time this operation started.
  """

  class OperationStateValueValuesEnum(_messages.Enum):
    r"""The state of the export operation.

    Values:
      OPERATION_STATE_UNSPECIFIED: Unspecified.
      INITIALIZING: Request is being prepared for processing.
      PROCESSING: Request is actively being processed.
      CANCELLING: Request is in the process of being cancelled after user
        called google.longrunning.Operations.CancelOperation on the operation.
      FINALIZING: Request has been processed and is in its finalization stage.
      SUCCESSFUL: Request has completed successfully.
      FAILED: Request has finished being processed, but encountered an error.
      CANCELLED: Request has finished being cancelled after user called
        google.longrunning.Operations.CancelOperation.
    """
    OPERATION_STATE_UNSPECIFIED = 0
    INITIALIZING = 1
    PROCESSING = 2
    CANCELLING = 3
    FINALIZING = 4
    SUCCESSFUL = 5
    FAILED = 6
    CANCELLED = 7

  collectionIds = _messages.StringField(1, repeated=True)
  endTime = _messages.StringField(2)
  operationState = _messages.EnumField('OperationStateValueValuesEnum', 3)
  outputUriPrefix = _messages.StringField(4)
  progressBytes = _messages.MessageField('GoogleFirestoreAdminV1beta2Progress', 5)
  progressDocuments = _messages.MessageField('GoogleFirestoreAdminV1beta2Progress', 6)
  startTime = _messages.StringField(7)


class GoogleFirestoreAdminV1beta2ExportDocumentsRequest(_messages.Message):
  r"""The request for FirestoreAdmin.ExportDocuments.

  Fields:
    collectionIds: Which collection ids to export. Unspecified means all
      collections.
    outputUriPrefix: The output URI. Currently only supports Google Cloud
      Storage URIs of the form: `gs://BUCKET_NAME[/NAMESPACE_PATH]`, where
      `BUCKET_NAME` is the name of the Google Cloud Storage bucket and
      `NAMESPACE_PATH` is an optional Google Cloud Storage namespace path.
      When choosing a name, be sure to consider Google Cloud Storage naming
      guidelines: https://cloud.google.com/storage/docs/naming. If the URI is
      a bucket (without a namespace path), a prefix will be generated based on
      the start time.
  """

  collectionIds = _messages.StringField(1, repeated=True)
  outputUriPrefix = _messages.StringField(2)


class GoogleFirestoreAdminV1beta2ExportDocumentsResponse(_messages.Message):
  r"""Returned in the google.longrunning.Operation response field.

  Fields:
    outputUriPrefix: Location of the output files. This can be used to begin
      an import into Cloud Firestore (this project or another project) after
      the operation completes successfully.
  """

  outputUriPrefix = _messages.StringField(1)


class GoogleFirestoreAdminV1beta2Field(_messages.Message):
  r"""Represents a single field in the database. Fields are grouped by their
  "Collection Group", which represent all collections in the database with the
  same id.

  Fields:
    indexConfig: The index configuration for this field. If unset, field
      indexing will revert to the configuration defined by the
      `ancestor_field`. To explicitly remove all indexes for this field,
      specify an index config with an empty list of indexes.
    name: A field name of the form `projects/{project_id}/databases/{database_
      id}/collectionGroups/{collection_id}/fields/{field_path}` A field path
      may be a simple field name, e.g. `address` or a path to fields within
      map_value , e.g. `address.city`, or a special field path. The only valid
      special field is `*`, which represents any field. Field paths may be
      quoted using ` (backtick). The only character that needs to be escaped
      within a quoted field path is the backtick character itself, escaped
      using a backslash. Special characters in field paths that must be quoted
      include: `*`, `.`, ``` (backtick), `[`, `]`, as well as any ascii
      symbolic characters. Examples: (Note: Comments here are written in
      markdown syntax, so there is an additional layer of backticks to
      represent a code block) `\`address.city\`` represents a field named
      `address.city`, not the map key `city` in the field `address`. `\`*\``
      represents a field named `*`, not any field. A special `Field` contains
      the default indexing settings for all fields. This field's resource name
      is: `projects/{project_id}/databases/{database_id}/collectionGroups/__de
      fault__/fields/*` Indexes defined on this `Field` will be applied to all
      fields which do not have their own `Field` index configuration.
  """

  indexConfig = _messages.MessageField('GoogleFirestoreAdminV1beta2IndexConfig', 1)
  name = _messages.StringField(2)


class GoogleFirestoreAdminV1beta2FieldOperationMetadata(_messages.Message):
  r"""Metadata for google.longrunning.Operation results from
  FirestoreAdmin.UpdateField.

  Enums:
    StateValueValuesEnum: The state of the operation.

  Fields:
    bytesProgress: The progress, in bytes, of this operation.
    documentProgress: The progress, in documents, of this operation.
    endTime: The time this operation completed. Will be unset if operation
      still in progress.
    field: The field resource that this operation is acting on. For example: `
      projects/{project_id}/databases/{database_id}/collectionGroups/{collecti
      on_id}/fields/{field_path}`
    indexConfigDeltas: A list of IndexConfigDelta, which describe the intent
      of this operation.
    startTime: The time this operation started.
    state: The state of the operation.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of the operation.

    Values:
      OPERATION_STATE_UNSPECIFIED: Unspecified.
      INITIALIZING: Request is being prepared for processing.
      PROCESSING: Request is actively being processed.
      CANCELLING: Request is in the process of being cancelled after user
        called google.longrunning.Operations.CancelOperation on the operation.
      FINALIZING: Request has been processed and is in its finalization stage.
      SUCCESSFUL: Request has completed successfully.
      FAILED: Request has finished being processed, but encountered an error.
      CANCELLED: Request has finished being cancelled after user called
        google.longrunning.Operations.CancelOperation.
    """
    OPERATION_STATE_UNSPECIFIED = 0
    INITIALIZING = 1
    PROCESSING = 2
    CANCELLING = 3
    FINALIZING = 4
    SUCCESSFUL = 5
    FAILED = 6
    CANCELLED = 7

  bytesProgress = _messages.MessageField('GoogleFirestoreAdminV1beta2Progress', 1)
  documentProgress = _messages.MessageField('GoogleFirestoreAdminV1beta2Progress', 2)
  endTime = _messages.StringField(3)
  field = _messages.StringField(4)
  indexConfigDeltas = _messages.MessageField('GoogleFirestoreAdminV1beta2IndexConfigDelta', 5, repeated=True)
  startTime = _messages.StringField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)


class GoogleFirestoreAdminV1beta2ImportDocumentsMetadata(_messages.Message):
  r"""Metadata for google.longrunning.Operation results from
  FirestoreAdmin.ImportDocuments.

  Enums:
    OperationStateValueValuesEnum: The state of the import operation.

  Fields:
    collectionIds: Which collection ids are being imported.
    endTime: The time this operation completed. Will be unset if operation
      still in progress.
    inputUriPrefix: The location of the documents being imported.
    operationState: The state of the import operation.
    progressBytes: The progress, in bytes, of this operation.
    progressDocuments: The progress, in documents, of this operation.
    startTime: The time this operation started.
  """

  class OperationStateValueValuesEnum(_messages.Enum):
    r"""The state of the import operation.

    Values:
      OPERATION_STATE_UNSPECIFIED: Unspecified.
      INITIALIZING: Request is being prepared for processing.
      PROCESSING: Request is actively being processed.
      CANCELLING: Request is in the process of being cancelled after user
        called google.longrunning.Operations.CancelOperation on the operation.
      FINALIZING: Request has been processed and is in its finalization stage.
      SUCCESSFUL: Request has completed successfully.
      FAILED: Request has finished being processed, but encountered an error.
      CANCELLED: Request has finished being cancelled after user called
        google.longrunning.Operations.CancelOperation.
    """
    OPERATION_STATE_UNSPECIFIED = 0
    INITIALIZING = 1
    PROCESSING = 2
    CANCELLING = 3
    FINALIZING = 4
    SUCCESSFUL = 5
    FAILED = 6
    CANCELLED = 7

  collectionIds = _messages.StringField(1, repeated=True)
  endTime = _messages.StringField(2)
  inputUriPrefix = _messages.StringField(3)
  operationState = _messages.EnumField('OperationStateValueValuesEnum', 4)
  progressBytes = _messages.MessageField('GoogleFirestoreAdminV1beta2Progress', 5)
  progressDocuments = _messages.MessageField('GoogleFirestoreAdminV1beta2Progress', 6)
  startTime = _messages.StringField(7)


class GoogleFirestoreAdminV1beta2ImportDocumentsRequest(_messages.Message):
  r"""The request for FirestoreAdmin.ImportDocuments.

  Fields:
    collectionIds: Which collection ids to import. Unspecified means all
      collections included in the import.
    inputUriPrefix: Location of the exported files. This must match the
      output_uri_prefix of an ExportDocumentsResponse from an export that has
      completed successfully. See: google.firestore.admin.v1beta2.ExportDocume
      ntsResponse.output_uri_prefix.
  """

  collectionIds = _messages.StringField(1, repeated=True)
  inputUriPrefix = _messages.StringField(2)


class GoogleFirestoreAdminV1beta2Index(_messages.Message):
  r"""Cloud Firestore indexes enable simple and complex queries against
  documents in a database.

  Enums:
    QueryScopeValueValuesEnum: Indexes with a collection query scope specified
      allow queries against a collection that is the child of a specific
      document, specified at query time, and that has the same collection id.
      Indexes with a collection group query scope specified allow queries
      against all collections descended from a specific document, specified at
      query time, and that have the same collection id as this index.
    StateValueValuesEnum: Output only. The serving state of the index.

  Fields:
    fields: The fields supported by this index. For composite indexes, this is
      always 2 or more fields. The last field entry is always for the field
      path `__name__`. If, on creation, `__name__` was not specified as the
      last field, it will be added automatically with the same direction as
      that of the last field defined. If the final field in a composite index
      is not directional, the `__name__` will be ordered ASCENDING (unless
      explicitly specified). For single field indexes, this will always be
      exactly one entry with a field path equal to the field path of the
      associated field.
    name: Output only. A server defined name for this index. The form of this
      name for composite indexes will be: `projects/{project_id}/databases/{da
      tabase_id}/collectionGroups/{collection_id}/indexes/{composite_index_id}
      ` For single field indexes, this field will be empty.
    queryScope: Indexes with a collection query scope specified allow queries
      against a collection that is the child of a specific document, specified
      at query time, and that has the same collection id. Indexes with a
      collection group query scope specified allow queries against all
      collections descended from a specific document, specified at query time,
      and that have the same collection id as this index.
    state: Output only. The serving state of the index.
  """

  class QueryScopeValueValuesEnum(_messages.Enum):
    r"""Indexes with a collection query scope specified allow queries against
    a collection that is the child of a specific document, specified at query
    time, and that has the same collection id. Indexes with a collection group
    query scope specified allow queries against all collections descended from
    a specific document, specified at query time, and that have the same
    collection id as this index.

    Values:
      QUERY_SCOPE_UNSPECIFIED: The query scope is unspecified. Not a valid
        option.
      COLLECTION: Indexes with a collection query scope specified allow
        queries against a collection that is the child of a specific document,
        specified at query time, and that has the collection id specified by
        the index.
      COLLECTION_GROUP: Indexes with a collection group query scope specified
        allow queries against all collections that has the collection id
        specified by the index.
    """
    QUERY_SCOPE_UNSPECIFIED = 0
    COLLECTION = 1
    COLLECTION_GROUP = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The serving state of the index.

    Values:
      STATE_UNSPECIFIED: The state is unspecified.
      CREATING: The index is being created. There is an active long-running
        operation for the index. The index is updated when writing a document.
        Some index data may exist.
      READY: The index is ready to be used. The index is updated when writing
        a document. The index is fully populated from all stored documents it
        applies to.
      NEEDS_REPAIR: The index was being created, but something went wrong.
        There is no active long-running operation for the index, and the most
        recently finished long-running operation failed. The index is not
        updated when writing a document. Some index data may exist. Use the
        google.longrunning.Operations API to determine why the operation that
        last attempted to create this index failed, then re-create the index.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    NEEDS_REPAIR = 3

  fields = _messages.MessageField('GoogleFirestoreAdminV1beta2IndexField', 1, repeated=True)
  name = _messages.StringField(2)
  queryScope = _messages.EnumField('QueryScopeValueValuesEnum', 3)
  state = _messages.EnumField('StateValueValuesEnum', 4)


class GoogleFirestoreAdminV1beta2IndexConfig(_messages.Message):
  r"""The index configuration for this field.

  Fields:
    ancestorField: Output only. Specifies the resource name of the `Field`
      from which this field's index configuration is set (when
      `uses_ancestor_config` is true), or from which it *would* be set if this
      field had no index configuration (when `uses_ancestor_config` is false).
    indexes: The indexes supported for this field.
    reverting: Output only When true, the `Field`'s index configuration is in
      the process of being reverted. Once complete, the index config will
      transition to the same state as the field specified by `ancestor_field`,
      at which point `uses_ancestor_config` will be `true` and `reverting`
      will be `false`.
    usesAncestorConfig: Output only. When true, the `Field`'s index
      configuration is set from the configuration specified by the
      `ancestor_field`. When false, the `Field`'s index configuration is
      defined explicitly.
  """

  ancestorField = _messages.StringField(1)
  indexes = _messages.MessageField('GoogleFirestoreAdminV1beta2Index', 2, repeated=True)
  reverting = _messages.BooleanField(3)
  usesAncestorConfig = _messages.BooleanField(4)


class GoogleFirestoreAdminV1beta2IndexConfigDelta(_messages.Message):
  r"""Information about an index configuration change.

  Enums:
    ChangeTypeValueValuesEnum: Specifies how the index is changing.

  Fields:
    changeType: Specifies how the index is changing.
    index: The index being changed.
  """

  class ChangeTypeValueValuesEnum(_messages.Enum):
    r"""Specifies how the index is changing.

    Values:
      CHANGE_TYPE_UNSPECIFIED: The type of change is not specified or known.
      ADD: The single field index is being added.
      REMOVE: The single field index is being removed.
    """
    CHANGE_TYPE_UNSPECIFIED = 0
    ADD = 1
    REMOVE = 2

  changeType = _messages.EnumField('ChangeTypeValueValuesEnum', 1)
  index = _messages.MessageField('GoogleFirestoreAdminV1beta2Index', 2)


class GoogleFirestoreAdminV1beta2IndexField(_messages.Message):
  r"""A field in an index. The field_path describes which field is indexed,
  the value_mode describes how the field value is indexed.

  Enums:
    ArrayConfigValueValuesEnum: Indicates that this field supports operations
      on `array_value`s.
    OrderValueValuesEnum: Indicates that this field supports ordering by the
      specified order or comparing using =, <, <=, >, >=.

  Fields:
    arrayConfig: Indicates that this field supports operations on
      `array_value`s.
    fieldPath: Can be __name__. For single field indexes, this must match the
      name of the field or may be omitted.
    order: Indicates that this field supports ordering by the specified order
      or comparing using =, <, <=, >, >=.
  """

  class ArrayConfigValueValuesEnum(_messages.Enum):
    r"""Indicates that this field supports operations on `array_value`s.

    Values:
      ARRAY_CONFIG_UNSPECIFIED: The index does not support additional array
        queries.
      CONTAINS: The index supports array containment queries.
    """
    ARRAY_CONFIG_UNSPECIFIED = 0
    CONTAINS = 1

  class OrderValueValuesEnum(_messages.Enum):
    r"""Indicates that this field supports ordering by the specified order or
    comparing using =, <, <=, >, >=.

    Values:
      ORDER_UNSPECIFIED: The ordering is unspecified. Not a valid option.
      ASCENDING: The field is ordered by ascending field value.
      DESCENDING: The field is ordered by descending field value.
    """
    ORDER_UNSPECIFIED = 0
    ASCENDING = 1
    DESCENDING = 2

  arrayConfig = _messages.EnumField('ArrayConfigValueValuesEnum', 1)
  fieldPath = _messages.StringField(2)
  order = _messages.EnumField('OrderValueValuesEnum', 3)


class GoogleFirestoreAdminV1beta2IndexOperationMetadata(_messages.Message):
  r"""Metadata for google.longrunning.Operation results from
  FirestoreAdmin.CreateIndex.

  Enums:
    StateValueValuesEnum: The state of the operation.

  Fields:
    endTime: The time this operation completed. Will be unset if operation
      still in progress.
    index: The index resource that this operation is acting on. For example: `
      projects/{project_id}/databases/{database_id}/collectionGroups/{collecti
      on_id}/indexes/{index_id}`
    progressBytes: The progress, in bytes, of this operation.
    progressDocuments: The progress, in documents, of this operation.
    startTime: The time this operation started.
    state: The state of the operation.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of the operation.

    Values:
      OPERATION_STATE_UNSPECIFIED: Unspecified.
      INITIALIZING: Request is being prepared for processing.
      PROCESSING: Request is actively being processed.
      CANCELLING: Request is in the process of being cancelled after user
        called google.longrunning.Operations.CancelOperation on the operation.
      FINALIZING: Request has been processed and is in its finalization stage.
      SUCCESSFUL: Request has completed successfully.
      FAILED: Request has finished being processed, but encountered an error.
      CANCELLED: Request has finished being cancelled after user called
        google.longrunning.Operations.CancelOperation.
    """
    OPERATION_STATE_UNSPECIFIED = 0
    INITIALIZING = 1
    PROCESSING = 2
    CANCELLING = 3
    FINALIZING = 4
    SUCCESSFUL = 5
    FAILED = 6
    CANCELLED = 7

  endTime = _messages.StringField(1)
  index = _messages.StringField(2)
  progressBytes = _messages.MessageField('GoogleFirestoreAdminV1beta2Progress', 3)
  progressDocuments = _messages.MessageField('GoogleFirestoreAdminV1beta2Progress', 4)
  startTime = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)


class GoogleFirestoreAdminV1beta2ListFieldsResponse(_messages.Message):
  r"""The response for FirestoreAdmin.ListFields.

  Fields:
    fields: The requested fields.
    nextPageToken: A page token that may be used to request another page of
      results. If blank, this is the last page.
  """

  fields = _messages.MessageField('GoogleFirestoreAdminV1beta2Field', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleFirestoreAdminV1beta2ListIndexesResponse(_messages.Message):
  r"""The response for FirestoreAdmin.ListIndexes.

  Fields:
    indexes: The requested indexes.
    nextPageToken: A page token that may be used to request another page of
      results. If blank, this is the last page.
  """

  indexes = _messages.MessageField('GoogleFirestoreAdminV1beta2Index', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleFirestoreAdminV1beta2Progress(_messages.Message):
  r"""Describes the progress of the operation. Unit of work is generic and
  must be interpreted based on where Progress is used.

  Fields:
    completedWork: The amount of work completed.
    estimatedWork: The amount of work estimated.
  """

  completedWork = _messages.IntegerField(1)
  estimatedWork = _messages.IntegerField(2)


class GoogleLongrunningOperation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
