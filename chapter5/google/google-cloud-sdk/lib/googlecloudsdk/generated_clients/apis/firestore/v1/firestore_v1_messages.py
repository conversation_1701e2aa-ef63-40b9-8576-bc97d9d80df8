"""Generated message classes for firestore version v1.

Accesses the NoSQL document database built for automatic scaling, high
performance, and ease of application development.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'firestore'


class Aggregation(_messages.Message):
  r"""Defines an aggregation that produces a single result.

  Fields:
    alias: Optional. Optional name of the field to store the result of the
      aggregation into. If not provided, Firestore will pick a default name
      following the format `field_`. For example: ``` AGGREGATE COUNT_UP_TO(1)
      AS count_up_to_1, COUNT_UP_TO(2), COUNT_UP_TO(3) AS count_up_to_3,
      COUNT(*) OVER ( ... ); ``` becomes: ``` AGGREGATE COUNT_UP_TO(1) AS
      count_up_to_1, COUNT_UP_TO(2) AS field_1, COUNT_UP_TO(3) AS
      count_up_to_3, COUNT(*) AS field_2 OVER ( ... ); ``` Requires: * Must be
      unique across all aggregation aliases. * Conform to document field name
      limitations.
    avg: Average aggregator.
    count: Count aggregator.
    sum: Sum aggregator.
  """

  alias = _messages.StringField(1)
  avg = _messages.MessageField('Avg', 2)
  count = _messages.MessageField('Count', 3)
  sum = _messages.MessageField('Sum', 4)


class AggregationResult(_messages.Message):
  r"""The result of a single bucket from a Firestore aggregation query. The
  keys of `aggregate_fields` are the same for all results in an aggregation
  query, unlike document queries which can have different fields present for
  each result.

  Messages:
    AggregateFieldsValue: The result of the aggregation functions, ex:
      `COUNT(*) AS total_docs`. The key is the alias assigned to the
      aggregation function on input and the size of this map equals the number
      of aggregation functions in the query.

  Fields:
    aggregateFields: The result of the aggregation functions, ex: `COUNT(*) AS
      total_docs`. The key is the alias assigned to the aggregation function
      on input and the size of this map equals the number of aggregation
      functions in the query.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AggregateFieldsValue(_messages.Message):
    r"""The result of the aggregation functions, ex: `COUNT(*) AS total_docs`.
    The key is the alias assigned to the aggregation function on input and the
    size of this map equals the number of aggregation functions in the query.

    Messages:
      AdditionalProperty: An additional property for a AggregateFieldsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AggregateFieldsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AggregateFieldsValue object.

      Fields:
        key: Name of the additional property.
        value: A Value attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Value', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  aggregateFields = _messages.MessageField('AggregateFieldsValue', 1)


class ArrayValue(_messages.Message):
  r"""An array value.

  Fields:
    values: Values in the array.
  """

  values = _messages.MessageField('Value', 1, repeated=True)


class Avg(_messages.Message):
  r"""Average of the values of the requested field. * Only numeric values will
  be aggregated. All non-numeric values including `NULL` are skipped. * If the
  aggregated values contain `NaN`, returns `NaN`. Infinity math follows
  IEEE-754 standards. * If the aggregated value set is empty, returns `NULL`.
  * Always returns the result as a double.

  Fields:
    field: The field to aggregate on.
  """

  field = _messages.MessageField('FieldReference', 1)


class BatchGetDocumentsRequest(_messages.Message):
  r"""The request for Firestore.BatchGetDocuments.

  Fields:
    documents: The names of the documents to retrieve. In the format:
      `projects/{project_id}/databases/{database_id}/documents/{document_path}
      `. The request will fail if any of the document is not a child resource
      of the given `database`. Duplicate names will be elided.
    mask: The fields to return. If not set, returns all fields. If a document
      has a field that is not present in this mask, that field will not be
      returned in the response.
    newTransaction: Starts a new transaction and reads the documents. Defaults
      to a read-only transaction. The new transaction ID will be returned as
      the first response in the stream.
    readTime: Reads documents as they were at the given time. This must be a
      microsecond precision timestamp within the past one hour, or if Point-
      in-Time Recovery is enabled, can additionally be a whole minute
      timestamp within the past 7 days.
    transaction: Reads documents in a transaction.
  """

  documents = _messages.StringField(1, repeated=True)
  mask = _messages.MessageField('DocumentMask', 2)
  newTransaction = _messages.MessageField('TransactionOptions', 3)
  readTime = _messages.StringField(4)
  transaction = _messages.BytesField(5)


class BatchGetDocumentsResponse(_messages.Message):
  r"""The streamed response for Firestore.BatchGetDocuments.

  Fields:
    found: A document that was requested.
    missing: A document name that was requested but does not exist. In the
      format: `projects/{project_id}/databases/{database_id}/documents/{docume
      nt_path}`.
    readTime: The time at which the document was read. This may be monotically
      increasing, in this case the previous documents in the result stream are
      guaranteed not to have changed between their read_time and this one.
    transaction: The transaction that was started as part of this request.
      Will only be set in the first response, and only if
      BatchGetDocumentsRequest.new_transaction was set in the request.
  """

  found = _messages.MessageField('Document', 1)
  missing = _messages.StringField(2)
  readTime = _messages.StringField(3)
  transaction = _messages.BytesField(4)


class BatchWriteRequest(_messages.Message):
  r"""The request for Firestore.BatchWrite.

  Messages:
    LabelsValue: Labels associated with this batch write.

  Fields:
    labels: Labels associated with this batch write.
    writes: The writes to apply. Method does not apply writes atomically and
      does not guarantee ordering. Each write succeeds or fails independently.
      You cannot write to the same document more than once per request.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels associated with this batch write.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)
  writes = _messages.MessageField('Write', 2, repeated=True)


class BatchWriteResponse(_messages.Message):
  r"""The response from Firestore.BatchWrite.

  Fields:
    status: The status of applying the writes. This i-th write status
      corresponds to the i-th write in the request.
    writeResults: The result of applying the writes. This i-th write result
      corresponds to the i-th write in the request.
  """

  status = _messages.MessageField('Status', 1, repeated=True)
  writeResults = _messages.MessageField('WriteResult', 2, repeated=True)


class BeginTransactionRequest(_messages.Message):
  r"""The request for Firestore.BeginTransaction.

  Fields:
    options: The options for the transaction. Defaults to a read-write
      transaction.
  """

  options = _messages.MessageField('TransactionOptions', 1)


class BeginTransactionResponse(_messages.Message):
  r"""The response for Firestore.BeginTransaction.

  Fields:
    transaction: The transaction that was started.
  """

  transaction = _messages.BytesField(1)


class BitSequence(_messages.Message):
  r"""A sequence of bits, encoded in a byte array. Each byte in the `bitmap`
  byte array stores 8 bits of the sequence. The only exception is the last
  byte, which may store 8 _or fewer_ bits. The `padding` defines the number of
  bits of the last byte to be ignored as "padding". The values of these
  "padding" bits are unspecified and must be ignored. To retrieve the first
  bit, bit 0, calculate: `(bitmap[0] & 0x01) != 0`. To retrieve the second
  bit, bit 1, calculate: `(bitmap[0] & 0x02) != 0`. To retrieve the third bit,
  bit 2, calculate: `(bitmap[0] & 0x04) != 0`. To retrieve the fourth bit, bit
  3, calculate: `(bitmap[0] & 0x08) != 0`. To retrieve bit n, calculate:
  `(bitmap[n / 8] & (0x01 << (n % 8))) != 0`. The "size" of a `BitSequence`
  (the number of bits it contains) is calculated by this formula:
  `(bitmap.length * 8) - padding`.

  Fields:
    bitmap: The bytes that encode the bit sequence. May have a length of zero.
    padding: The number of bits of the last byte in `bitmap` to ignore as
      "padding". If the length of `bitmap` is zero, then this value must be
      `0`. Otherwise, this value must be between 0 and 7, inclusive.
  """

  bitmap = _messages.BytesField(1)
  padding = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class BloomFilter(_messages.Message):
  r"""A bloom filter (https://en.wikipedia.org/wiki/Bloom_filter). The bloom
  filter hashes the entries with MD5 and treats the resulting 128-bit hash as
  2 distinct 64-bit hash values, interpreted as unsigned integers using 2's
  complement encoding. These two hash values, named `h1` and `h2`, are then
  used to compute the `hash_count` hash values using the formula, starting at
  `i=0`: h(i) = h1 + (i * h2) These resulting values are then taken modulo the
  number of bits in the bloom filter to get the bits of the bloom filter to
  test for the given entry.

  Fields:
    bits: The bloom filter data.
    hashCount: The number of hashes used by the algorithm.
  """

  bits = _messages.MessageField('BitSequence', 1)
  hashCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class CollectionSelector(_messages.Message):
  r"""A selection of a collection, such as `messages as m1`.

  Fields:
    allDescendants: When false, selects only collections that are immediate
      children of the `parent` specified in the containing `RunQueryRequest`.
      When true, selects all descendant collections.
    collectionId: The collection ID. When set, selects only collections with
      this ID.
  """

  allDescendants = _messages.BooleanField(1)
  collectionId = _messages.StringField(2)


class CommitRequest(_messages.Message):
  r"""The request for Firestore.Commit.

  Fields:
    transaction: If set, applies all writes in this transaction, and commits
      it.
    writes: The writes to apply. Always executed atomically and in order.
  """

  transaction = _messages.BytesField(1)
  writes = _messages.MessageField('Write', 2, repeated=True)


class CommitResponse(_messages.Message):
  r"""The response for Firestore.Commit.

  Fields:
    commitTime: The time at which the commit occurred. Any read with an equal
      or greater `read_time` is guaranteed to see the effects of the commit.
    writeResults: The result of applying the writes. This i-th write result
      corresponds to the i-th write in the request.
  """

  commitTime = _messages.StringField(1)
  writeResults = _messages.MessageField('WriteResult', 2, repeated=True)


class CompositeFilter(_messages.Message):
  r"""A filter that merges multiple other filters using the given operator.

  Enums:
    OpValueValuesEnum: The operator for combining multiple filters.

  Fields:
    filters: The list of filters to combine. Requires: * At least one filter
      is present.
    op: The operator for combining multiple filters.
  """

  class OpValueValuesEnum(_messages.Enum):
    r"""The operator for combining multiple filters.

    Values:
      OPERATOR_UNSPECIFIED: Unspecified. This value must not be used.
      AND: Documents are required to satisfy all of the combined filters.
      OR: Documents are required to satisfy at least one of the combined
        filters.
    """
    OPERATOR_UNSPECIFIED = 0
    AND = 1
    OR = 2

  filters = _messages.MessageField('Filter', 1, repeated=True)
  op = _messages.EnumField('OpValueValuesEnum', 2)


class Count(_messages.Message):
  r"""Count of documents that match the query. The `COUNT(*)` aggregation
  function operates on the entire document so it does not require a field
  reference.

  Fields:
    upTo: Optional. Optional constraint on the maximum number of documents to
      count. This provides a way to set an upper bound on the number of
      documents to scan, limiting latency, and cost. Unspecified is
      interpreted as no bound. High-Level Example: ``` AGGREGATE
      COUNT_UP_TO(1000) OVER ( SELECT * FROM k ); ``` Requires: * Must be
      greater than zero when present.
  """

  upTo = _messages.IntegerField(1)


class Cursor(_messages.Message):
  r"""A position in a query result set.

  Fields:
    before: If the position is just before or just after the given values,
      relative to the sort order defined by the query.
    values: The values that represent a position, in the order they appear in
      the order by clause of a query. Can contain fewer values than specified
      in the order by clause.
  """

  before = _messages.BooleanField(1)
  values = _messages.MessageField('Value', 2, repeated=True)


class Document(_messages.Message):
  r"""A Firestore document. Must not exceed 1 MiB - 4 bytes.

  Messages:
    FieldsValue: The document's fields. The map keys represent field names.
      Field names matching the regular expression `__.*__` are reserved.
      Reserved field names are forbidden except in certain documented
      contexts. The field names, represented as UTF-8, must not exceed 1,500
      bytes and cannot be empty. Field paths may be used in other contexts to
      refer to structured fields defined here. For `map_value`, the field path
      is represented by a dot-delimited (`.`) string of segments. Each segment
      is either a simple field name (defined below) or a quoted field name.
      For example, the structured field `"foo" : { map_value: { "x&y" : {
      string_value: "hello" }}}` would be represented by the field path ``
      foo.`x&y` ``. A simple field name contains only characters `a` to `z`,
      `A` to `Z`, `0` to `9`, or `_`, and must not start with `0` to `9`. For
      example, `foo_bar_17`. A quoted field name starts and ends with `` ` ``
      and may contain any character. Some characters, including `` ` ``, must
      be escaped using a `\`. For example, `` `x&y` `` represents `x&y` and ``
      `bak\`tik` `` represents `` bak`tik ``.

  Fields:
    createTime: Output only. The time at which the document was created. This
      value increases monotonically when a document is deleted then recreated.
      It can also be compared to values from other documents and the
      `read_time` of a query.
    fields: The document's fields. The map keys represent field names. Field
      names matching the regular expression `__.*__` are reserved. Reserved
      field names are forbidden except in certain documented contexts. The
      field names, represented as UTF-8, must not exceed 1,500 bytes and
      cannot be empty. Field paths may be used in other contexts to refer to
      structured fields defined here. For `map_value`, the field path is
      represented by a dot-delimited (`.`) string of segments. Each segment is
      either a simple field name (defined below) or a quoted field name. For
      example, the structured field `"foo" : { map_value: { "x&y" : {
      string_value: "hello" }}}` would be represented by the field path ``
      foo.`x&y` ``. A simple field name contains only characters `a` to `z`,
      `A` to `Z`, `0` to `9`, or `_`, and must not start with `0` to `9`. For
      example, `foo_bar_17`. A quoted field name starts and ends with `` ` ``
      and may contain any character. Some characters, including `` ` ``, must
      be escaped using a `\`. For example, `` `x&y` `` represents `x&y` and ``
      `bak\`tik` `` represents `` bak`tik ``.
    name: The resource name of the document, for example
      `projects/{project_id}/databases/{database_id}/documents/{document_path}
      `.
    updateTime: Output only. The time at which the document was last changed.
      This value is initially set to the `create_time` then increases
      monotonically with each change to the document. It can also be compared
      to values from other documents and the `read_time` of a query.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FieldsValue(_messages.Message):
    r"""The document's fields. The map keys represent field names. Field names
    matching the regular expression `__.*__` are reserved. Reserved field
    names are forbidden except in certain documented contexts. The field
    names, represented as UTF-8, must not exceed 1,500 bytes and cannot be
    empty. Field paths may be used in other contexts to refer to structured
    fields defined here. For `map_value`, the field path is represented by a
    dot-delimited (`.`) string of segments. Each segment is either a simple
    field name (defined below) or a quoted field name. For example, the
    structured field `"foo" : { map_value: { "x&y" : { string_value: "hello"
    }}}` would be represented by the field path `` foo.`x&y` ``. A simple
    field name contains only characters `a` to `z`, `A` to `Z`, `0` to `9`, or
    `_`, and must not start with `0` to `9`. For example, `foo_bar_17`. A
    quoted field name starts and ends with `` ` `` and may contain any
    character. Some characters, including `` ` ``, must be escaped using a
    `\`. For example, `` `x&y` `` represents `x&y` and `` `bak\`tik` ``
    represents `` bak`tik ``.

    Messages:
      AdditionalProperty: An additional property for a FieldsValue object.

    Fields:
      additionalProperties: Additional properties of type FieldsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FieldsValue object.

      Fields:
        key: Name of the additional property.
        value: A Value attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Value', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  fields = _messages.MessageField('FieldsValue', 2)
  name = _messages.StringField(3)
  updateTime = _messages.StringField(4)


class DocumentChange(_messages.Message):
  r"""A Document has changed. May be the result of multiple writes, including
  deletes, that ultimately resulted in a new value for the Document. Multiple
  DocumentChange messages may be returned for the same logical change, if
  multiple targets are affected.

  Fields:
    document: The new state of the Document. If `mask` is set, contains only
      fields that were updated or added.
    removedTargetIds: A set of target IDs for targets that no longer match
      this document.
    targetIds: A set of target IDs of targets that match this document.
  """

  document = _messages.MessageField('Document', 1)
  removedTargetIds = _messages.IntegerField(2, repeated=True, variant=_messages.Variant.INT32)
  targetIds = _messages.IntegerField(3, repeated=True, variant=_messages.Variant.INT32)


class DocumentDelete(_messages.Message):
  r"""A Document has been deleted. May be the result of multiple writes,
  including updates, the last of which deleted the Document. Multiple
  DocumentDelete messages may be returned for the same logical delete, if
  multiple targets are affected.

  Fields:
    document: The resource name of the Document that was deleted.
    readTime: The read timestamp at which the delete was observed. Greater or
      equal to the `commit_time` of the delete.
    removedTargetIds: A set of target IDs for targets that previously matched
      this entity.
  """

  document = _messages.StringField(1)
  readTime = _messages.StringField(2)
  removedTargetIds = _messages.IntegerField(3, repeated=True, variant=_messages.Variant.INT32)


class DocumentMask(_messages.Message):
  r"""A set of field paths on a document. Used to restrict a get or update
  operation on a document to a subset of its fields. This is different from
  standard field masks, as this is always scoped to a Document, and takes in
  account the dynamic nature of Value.

  Fields:
    fieldPaths: The list of field paths in the mask. See Document.fields for a
      field path syntax reference.
  """

  fieldPaths = _messages.StringField(1, repeated=True)


class DocumentRemove(_messages.Message):
  r"""A Document has been removed from the view of the targets. Sent if the
  document is no longer relevant to a target and is out of view. Can be sent
  instead of a DocumentDelete or a DocumentChange if the server can not send
  the new value of the document. Multiple DocumentRemove messages may be
  returned for the same logical write or delete, if multiple targets are
  affected.

  Fields:
    document: The resource name of the Document that has gone out of view.
    readTime: The read timestamp at which the remove was observed. Greater or
      equal to the `commit_time` of the change/delete/remove.
    removedTargetIds: A set of target IDs for targets that previously matched
      this document.
  """

  document = _messages.StringField(1)
  readTime = _messages.StringField(2)
  removedTargetIds = _messages.IntegerField(3, repeated=True, variant=_messages.Variant.INT32)


class DocumentTransform(_messages.Message):
  r"""A transformation of a document.

  Fields:
    document: The name of the document to transform.
    fieldTransforms: The list of transformations to apply to the fields of the
      document, in order. This must not be empty.
  """

  document = _messages.StringField(1)
  fieldTransforms = _messages.MessageField('FieldTransform', 2, repeated=True)


class DocumentsTarget(_messages.Message):
  r"""A target specified by a set of documents names.

  Fields:
    documents: The names of the documents to retrieve. In the format:
      `projects/{project_id}/databases/{database_id}/documents/{document_path}
      `. The request will fail if any of the document is not a child resource
      of the given `database`. Duplicate names will be elided.
  """

  documents = _messages.StringField(1, repeated=True)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class ExecutionStats(_messages.Message):
  r"""Execution statistics for the query.

  Messages:
    DebugStatsValue: Debugging statistics from the execution of the query.
      Note that the debugging stats are subject to change as Firestore
      evolves. It could include: { "indexes_entries_scanned": "1000",
      "documents_scanned": "20", "billing_details" : { "documents_billable":
      "20", "index_entries_billable": "1000", "min_query_cost": "0" } }

  Fields:
    debugStats: Debugging statistics from the execution of the query. Note
      that the debugging stats are subject to change as Firestore evolves. It
      could include: { "indexes_entries_scanned": "1000", "documents_scanned":
      "20", "billing_details" : { "documents_billable": "20",
      "index_entries_billable": "1000", "min_query_cost": "0" } }
    executionDuration: Total time to execute the query in the backend.
    readOperations: Total billable read operations.
    resultsReturned: Total number of results returned, including documents,
      projections, aggregation results, keys.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DebugStatsValue(_messages.Message):
    r"""Debugging statistics from the execution of the query. Note that the
    debugging stats are subject to change as Firestore evolves. It could
    include: { "indexes_entries_scanned": "1000", "documents_scanned": "20",
    "billing_details" : { "documents_billable": "20",
    "index_entries_billable": "1000", "min_query_cost": "0" } }

    Messages:
      AdditionalProperty: An additional property for a DebugStatsValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DebugStatsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  debugStats = _messages.MessageField('DebugStatsValue', 1)
  executionDuration = _messages.StringField(2)
  readOperations = _messages.IntegerField(3)
  resultsReturned = _messages.IntegerField(4)


class ExistenceFilter(_messages.Message):
  r"""A digest of all the documents that match a given target.

  Fields:
    count: The total count of documents that match target_id. If different
      from the count of documents in the client that match, the client must
      manually determine which documents no longer match the target. The
      client can use the `unchanged_names` bloom filter to assist with this
      determination by testing ALL the document names against the filter; if
      the document name is NOT in the filter, it means the document no longer
      matches the target.
    targetId: The target ID to which this filter applies.
    unchangedNames: A bloom filter that, despite its name, contains the UTF-8
      byte encodings of the resource names of ALL the documents that match
      target_id, in the form `projects/{project_id}/databases/{database_id}/do
      cuments/{document_path}`. This bloom filter may be omitted at the
      server's discretion, such as if it is deemed that the client will not
      make use of it or if it is too computationally expensive to calculate or
      transmit. Clients must gracefully handle this field being absent by
      falling back to the logic used before this field existed; that is, re-
      add the target without a resume token to figure out which documents in
      the client's cache are out of sync.
  """

  count = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  targetId = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  unchangedNames = _messages.MessageField('BloomFilter', 3)


class ExplainMetrics(_messages.Message):
  r"""Explain metrics for the query.

  Fields:
    executionStats: Aggregated stats from the execution of the query. Only
      present when ExplainOptions.analyze is set to true.
    planSummary: Planning phase information for the query.
  """

  executionStats = _messages.MessageField('ExecutionStats', 1)
  planSummary = _messages.MessageField('PlanSummary', 2)


class ExplainOptions(_messages.Message):
  r"""Explain options for the query.

  Fields:
    analyze: Optional. Whether to execute this query. When false (the
      default), the query will be planned, returning only metrics from the
      planning stages. When true, the query will be planned and executed,
      returning the full query results along with both planning and execution
      stage metrics.
  """

  analyze = _messages.BooleanField(1)


class FieldFilter(_messages.Message):
  r"""A filter on a specific field.

  Enums:
    OpValueValuesEnum: The operator to filter by.

  Fields:
    field: The field to filter by.
    op: The operator to filter by.
    value: The value to compare to.
  """

  class OpValueValuesEnum(_messages.Enum):
    r"""The operator to filter by.

    Values:
      OPERATOR_UNSPECIFIED: Unspecified. This value must not be used.
      LESS_THAN: The given `field` is less than the given `value`. Requires: *
        That `field` come first in `order_by`.
      LESS_THAN_OR_EQUAL: The given `field` is less than or equal to the given
        `value`. Requires: * That `field` come first in `order_by`.
      GREATER_THAN: The given `field` is greater than the given `value`.
        Requires: * That `field` come first in `order_by`.
      GREATER_THAN_OR_EQUAL: The given `field` is greater than or equal to the
        given `value`. Requires: * That `field` come first in `order_by`.
      EQUAL: The given `field` is equal to the given `value`.
      NOT_EQUAL: The given `field` is not equal to the given `value`.
        Requires: * No other `NOT_EQUAL`, `NOT_IN`, `IS_NOT_NULL`, or
        `IS_NOT_NAN`. * That `field` comes first in the `order_by`.
      ARRAY_CONTAINS: The given `field` is an array that contains the given
        `value`.
      IN: The given `field` is equal to at least one value in the given array.
        Requires: * That `value` is a non-empty `ArrayValue`, subject to
        disjunction limits. * No `NOT_IN` filters in the same query.
      ARRAY_CONTAINS_ANY: The given `field` is an array that contains any of
        the values in the given array. Requires: * That `value` is a non-empty
        `ArrayValue`, subject to disjunction limits. * No other
        `ARRAY_CONTAINS_ANY` filters within the same disjunction. * No
        `NOT_IN` filters in the same query.
      NOT_IN: The value of the `field` is not in the given array. Requires: *
        That `value` is a non-empty `ArrayValue` with at most 10 values. * No
        other `OR`, `IN`, `ARRAY_CONTAINS_ANY`, `NOT_IN`, `NOT_EQUAL`,
        `IS_NOT_NULL`, or `IS_NOT_NAN`. * That `field` comes first in the
        `order_by`.
    """
    OPERATOR_UNSPECIFIED = 0
    LESS_THAN = 1
    LESS_THAN_OR_EQUAL = 2
    GREATER_THAN = 3
    GREATER_THAN_OR_EQUAL = 4
    EQUAL = 5
    NOT_EQUAL = 6
    ARRAY_CONTAINS = 7
    IN = 8
    ARRAY_CONTAINS_ANY = 9
    NOT_IN = 10

  field = _messages.MessageField('FieldReference', 1)
  op = _messages.EnumField('OpValueValuesEnum', 2)
  value = _messages.MessageField('Value', 3)


class FieldReference(_messages.Message):
  r"""A reference to a field in a document, ex: `stats.operations`.

  Fields:
    fieldPath: A reference to a field in a document. Requires: * MUST be a
      dot-delimited (`.`) string of segments, where each segment conforms to
      document field name limitations.
  """

  fieldPath = _messages.StringField(1)


class FieldTransform(_messages.Message):
  r"""A transformation of a field of the document.

  Enums:
    SetToServerValueValueValuesEnum: Sets the field to the given server value.

  Fields:
    appendMissingElements: Append the given elements in order if they are not
      already present in the current field value. If the field is not an
      array, or if the field does not yet exist, it is first set to the empty
      array. Equivalent numbers of different types (e.g. 3L and 3.0) are
      considered equal when checking if a value is missing. NaN is equal to
      NaN, and Null is equal to Null. If the input contains multiple
      equivalent values, only the first will be considered. The corresponding
      transform_result will be the null value.
    fieldPath: The path of the field. See Document.fields for the field path
      syntax reference.
    increment: Adds the given value to the field's current value. This must be
      an integer or a double value. If the field is not an integer or double,
      or if the field does not yet exist, the transformation will set the
      field to the given value. If either of the given value or the current
      field value are doubles, both values will be interpreted as doubles.
      Double arithmetic and representation of double values follow IEEE 754
      semantics. If there is positive/negative integer overflow, the field is
      resolved to the largest magnitude positive/negative integer.
    maximum: Sets the field to the maximum of its current value and the given
      value. This must be an integer or a double value. If the field is not an
      integer or double, or if the field does not yet exist, the
      transformation will set the field to the given value. If a maximum
      operation is applied where the field and the input value are of mixed
      types (that is - one is an integer and one is a double) the field takes
      on the type of the larger operand. If the operands are equivalent (e.g.
      3 and 3.0), the field does not change. 0, 0.0, and -0.0 are all zero.
      The maximum of a zero stored value and zero input value is always the
      stored value. The maximum of any numeric value x and NaN is NaN.
    minimum: Sets the field to the minimum of its current value and the given
      value. This must be an integer or a double value. If the field is not an
      integer or double, or if the field does not yet exist, the
      transformation will set the field to the input value. If a minimum
      operation is applied where the field and the input value are of mixed
      types (that is - one is an integer and one is a double) the field takes
      on the type of the smaller operand. If the operands are equivalent (e.g.
      3 and 3.0), the field does not change. 0, 0.0, and -0.0 are all zero.
      The minimum of a zero stored value and zero input value is always the
      stored value. The minimum of any numeric value x and NaN is NaN.
    removeAllFromArray: Remove all of the given elements from the array in the
      field. If the field is not an array, or if the field does not yet exist,
      it is set to the empty array. Equivalent numbers of the different types
      (e.g. 3L and 3.0) are considered equal when deciding whether an element
      should be removed. NaN is equal to NaN, and Null is equal to Null. This
      will remove all equivalent values if there are duplicates. The
      corresponding transform_result will be the null value.
    setToServerValue: Sets the field to the given server value.
  """

  class SetToServerValueValueValuesEnum(_messages.Enum):
    r"""Sets the field to the given server value.

    Values:
      SERVER_VALUE_UNSPECIFIED: Unspecified. This value must not be used.
      REQUEST_TIME: The time at which the server processed the request, with
        millisecond precision. If used on multiple fields (same or different
        documents) in a transaction, all the fields will get the same server
        timestamp.
    """
    SERVER_VALUE_UNSPECIFIED = 0
    REQUEST_TIME = 1

  appendMissingElements = _messages.MessageField('ArrayValue', 1)
  fieldPath = _messages.StringField(2)
  increment = _messages.MessageField('Value', 3)
  maximum = _messages.MessageField('Value', 4)
  minimum = _messages.MessageField('Value', 5)
  removeAllFromArray = _messages.MessageField('ArrayValue', 6)
  setToServerValue = _messages.EnumField('SetToServerValueValueValuesEnum', 7)


class Filter(_messages.Message):
  r"""A filter.

  Fields:
    compositeFilter: A composite filter.
    fieldFilter: A filter on a document field.
    unaryFilter: A filter that takes exactly one argument.
  """

  compositeFilter = _messages.MessageField('CompositeFilter', 1)
  fieldFilter = _messages.MessageField('FieldFilter', 2)
  unaryFilter = _messages.MessageField('UnaryFilter', 3)


class FindNearest(_messages.Message):
  r"""Nearest Neighbors search config. The ordering provided by FindNearest
  supersedes the order_by stage. If multiple documents have the same vector
  distance, the returned document order is not guaranteed to be stable between
  queries.

  Enums:
    DistanceMeasureValueValuesEnum: Required. The distance measure to use,
      required.

  Fields:
    distanceMeasure: Required. The distance measure to use, required.
    distanceResultField: Optional. Optional name of the field to output the
      result of the vector distance calculation. Must conform to document
      field name limitations.
    distanceThreshold: Optional. Option to specify a threshold for which no
      less similar documents will be returned. The behavior of the specified
      `distance_measure` will affect the meaning of the distance threshold.
      Since DOT_PRODUCT distances increase when the vectors are more similar,
      the comparison is inverted. * For EUCLIDEAN, COSINE: `WHERE distance <=
      distance_threshold` * For DOT_PRODUCT: `WHERE distance >=
      distance_threshold`
    limit: Required. The number of nearest neighbors to return. Must be a
      positive integer of no more than 1000.
    queryVector: Required. The query vector that we are searching on. Must be
      a vector of no more than 2048 dimensions.
    vectorField: Required. An indexed vector field to search upon. Only
      documents which contain vectors whose dimensionality match the
      query_vector can be returned.
  """

  class DistanceMeasureValueValuesEnum(_messages.Enum):
    r"""Required. The distance measure to use, required.

    Values:
      DISTANCE_MEASURE_UNSPECIFIED: Should not be set.
      EUCLIDEAN: Measures the EUCLIDEAN distance between the vectors. See
        [Euclidean](https://en.wikipedia.org/wiki/Euclidean_distance) to learn
        more. The resulting distance decreases the more similar two vectors
        are.
      COSINE: COSINE distance compares vectors based on the angle between
        them, which allows you to measure similarity that isn't based on the
        vectors magnitude. We recommend using DOT_PRODUCT with unit normalized
        vectors instead of COSINE distance, which is mathematically equivalent
        with better performance. See [Cosine
        Similarity](https://en.wikipedia.org/wiki/Cosine_similarity) to learn
        more about COSINE similarity and COSINE distance. The resulting COSINE
        distance decreases the more similar two vectors are.
      DOT_PRODUCT: Similar to cosine but is affected by the magnitude of the
        vectors. See [Dot Product](https://en.wikipedia.org/wiki/Dot_product)
        to learn more. The resulting distance increases the more similar two
        vectors are.
    """
    DISTANCE_MEASURE_UNSPECIFIED = 0
    EUCLIDEAN = 1
    COSINE = 2
    DOT_PRODUCT = 3

  distanceMeasure = _messages.EnumField('DistanceMeasureValueValuesEnum', 1)
  distanceResultField = _messages.StringField(2)
  distanceThreshold = _messages.FloatField(3)
  limit = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  queryVector = _messages.MessageField('Value', 5)
  vectorField = _messages.MessageField('FieldReference', 6)


class FirestoreProjectsDatabasesBackupSchedulesCreateRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesBackupSchedulesCreateRequest object.

  Fields:
    googleFirestoreAdminV1BackupSchedule: A
      GoogleFirestoreAdminV1BackupSchedule resource to be passed as the
      request body.
    parent: Required. The parent database. Format
      `projects/{project}/databases/{database}`
  """

  googleFirestoreAdminV1BackupSchedule = _messages.MessageField('GoogleFirestoreAdminV1BackupSchedule', 1)
  parent = _messages.StringField(2, required=True)


class FirestoreProjectsDatabasesBackupSchedulesDeleteRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesBackupSchedulesDeleteRequest object.

  Fields:
    name: Required. The name of the backup schedule. Format `projects/{project
      }/databases/{database}/backupSchedules/{backup_schedule}`
  """

  name = _messages.StringField(1, required=True)


class FirestoreProjectsDatabasesBackupSchedulesGetRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesBackupSchedulesGetRequest object.

  Fields:
    name: Required. The name of the backup schedule. Format `projects/{project
      }/databases/{database}/backupSchedules/{backup_schedule}`
  """

  name = _messages.StringField(1, required=True)


class FirestoreProjectsDatabasesBackupSchedulesListRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesBackupSchedulesListRequest object.

  Fields:
    parent: Required. The parent database. Format is
      `projects/{project}/databases/{database}`.
  """

  parent = _messages.StringField(1, required=True)


class FirestoreProjectsDatabasesBackupSchedulesPatchRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesBackupSchedulesPatchRequest object.

  Fields:
    googleFirestoreAdminV1BackupSchedule: A
      GoogleFirestoreAdminV1BackupSchedule resource to be passed as the
      request body.
    name: Output only. The unique backup schedule identifier across all
      locations and databases for the given project. This will be auto-
      assigned. Format is `projects/{project}/databases/{database}/backupSched
      ules/{backup_schedule}`
    updateMask: The list of fields to be updated.
  """

  googleFirestoreAdminV1BackupSchedule = _messages.MessageField('GoogleFirestoreAdminV1BackupSchedule', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class FirestoreProjectsDatabasesBulkDeleteDocumentsRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesBulkDeleteDocumentsRequest object.

  Fields:
    googleFirestoreAdminV1BulkDeleteDocumentsRequest: A
      GoogleFirestoreAdminV1BulkDeleteDocumentsRequest resource to be passed
      as the request body.
    name: Required. Database to operate. Should be of the form:
      `projects/{project_id}/databases/{database_id}`.
  """

  googleFirestoreAdminV1BulkDeleteDocumentsRequest = _messages.MessageField('GoogleFirestoreAdminV1BulkDeleteDocumentsRequest', 1)
  name = _messages.StringField(2, required=True)


class FirestoreProjectsDatabasesCloneRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesCloneRequest object.

  Fields:
    googleFirestoreAdminV1CloneDatabaseRequest: A
      GoogleFirestoreAdminV1CloneDatabaseRequest resource to be passed as the
      request body.
    parent: Required. The project to clone the database in. Format is
      `projects/{project_id}`.
  """

  googleFirestoreAdminV1CloneDatabaseRequest = _messages.MessageField('GoogleFirestoreAdminV1CloneDatabaseRequest', 1)
  parent = _messages.StringField(2, required=True)


class FirestoreProjectsDatabasesCollectionGroupsFieldsGetRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesCollectionGroupsFieldsGetRequest object.

  Fields:
    name: Required. A name of the form `projects/{project_id}/databases/{datab
      ase_id}/collectionGroups/{collection_id}/fields/{field_id}`
  """

  name = _messages.StringField(1, required=True)


class FirestoreProjectsDatabasesCollectionGroupsFieldsListRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesCollectionGroupsFieldsListRequest object.

  Fields:
    filter: The filter to apply to list results. Currently,
      FirestoreAdmin.ListFields only supports listing fields that have been
      explicitly overridden. To issue this query, call
      FirestoreAdmin.ListFields with a filter that includes
      `indexConfig.usesAncestorConfig:false` or `ttlConfig:*`.
    pageSize: The number of results to return.
    pageToken: A page token, returned from a previous call to
      FirestoreAdmin.ListFields, that may be used to get the next page of
      results.
    parent: Required. A parent name of the form `projects/{project_id}/databas
      es/{database_id}/collectionGroups/{collection_id}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class FirestoreProjectsDatabasesCollectionGroupsFieldsPatchRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesCollectionGroupsFieldsPatchRequest object.

  Fields:
    googleFirestoreAdminV1Field: A GoogleFirestoreAdminV1Field resource to be
      passed as the request body.
    name: Required. A field name of the form: `projects/{project_id}/databases
      /{database_id}/collectionGroups/{collection_id}/fields/{field_path}` A
      field path can be a simple field name, e.g. `address` or a path to
      fields within `map_value` , e.g. `address.city`, or a special field
      path. The only valid special field is `*`, which represents any field.
      Field paths can be quoted using `` ` `` (backtick). The only character
      that must be escaped within a quoted field path is the backtick
      character itself, escaped using a backslash. Special characters in field
      paths that must be quoted include: `*`, `.`, `` ` `` (backtick), `[`,
      `]`, as well as any ascii symbolic characters. Examples: ``
      `address.city` `` represents a field named `address.city`, not the map
      key `city` in the field `address`. `` `*` `` represents a field named
      `*`, not any field. A special `Field` contains the default indexing
      settings for all fields. This field's resource name is: `projects/{proje
      ct_id}/databases/{database_id}/collectionGroups/__default__/fields/*`
      Indexes defined on this `Field` will be applied to all fields which do
      not have their own `Field` index configuration.
    updateMask: A mask, relative to the field. If specified, only
      configuration specified by this field_mask will be updated in the field.
  """

  googleFirestoreAdminV1Field = _messages.MessageField('GoogleFirestoreAdminV1Field', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class FirestoreProjectsDatabasesCollectionGroupsIndexesCreateRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesCollectionGroupsIndexesCreateRequest object.

  Fields:
    googleFirestoreAdminV1Index: A GoogleFirestoreAdminV1Index resource to be
      passed as the request body.
    parent: Required. A parent name of the form `projects/{project_id}/databas
      es/{database_id}/collectionGroups/{collection_id}`
  """

  googleFirestoreAdminV1Index = _messages.MessageField('GoogleFirestoreAdminV1Index', 1)
  parent = _messages.StringField(2, required=True)


class FirestoreProjectsDatabasesCollectionGroupsIndexesDeleteRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesCollectionGroupsIndexesDeleteRequest object.

  Fields:
    name: Required. A name of the form `projects/{project_id}/databases/{datab
      ase_id}/collectionGroups/{collection_id}/indexes/{index_id}`
  """

  name = _messages.StringField(1, required=True)


class FirestoreProjectsDatabasesCollectionGroupsIndexesGetRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesCollectionGroupsIndexesGetRequest object.

  Fields:
    name: Required. A name of the form `projects/{project_id}/databases/{datab
      ase_id}/collectionGroups/{collection_id}/indexes/{index_id}`
  """

  name = _messages.StringField(1, required=True)


class FirestoreProjectsDatabasesCollectionGroupsIndexesListRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesCollectionGroupsIndexesListRequest object.

  Fields:
    filter: The filter to apply to list results.
    pageSize: The number of results to return.
    pageToken: A page token, returned from a previous call to
      FirestoreAdmin.ListIndexes, that may be used to get the next page of
      results.
    parent: Required. A parent name of the form `projects/{project_id}/databas
      es/{database_id}/collectionGroups/{collection_id}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class FirestoreProjectsDatabasesCreateRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesCreateRequest object.

  Fields:
    databaseId: Required. The ID to use for the database, which will become
      the final component of the database's resource name. This value should
      be 4-63 characters. Valid characters are /a-z-/ with first character a
      letter and the last a letter or a number. Must not be UUID-like
      /[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}/. "(default)" database ID is
      also valid.
    googleFirestoreAdminV1Database: A GoogleFirestoreAdminV1Database resource
      to be passed as the request body.
    parent: Required. A parent name of the form `projects/{project_id}`
  """

  databaseId = _messages.StringField(1)
  googleFirestoreAdminV1Database = _messages.MessageField('GoogleFirestoreAdminV1Database', 2)
  parent = _messages.StringField(3, required=True)


class FirestoreProjectsDatabasesDeleteRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDeleteRequest object.

  Fields:
    etag: The current etag of the Database. If an etag is provided and does
      not match the current etag of the database, deletion will be blocked and
      a FAILED_PRECONDITION error will be returned.
    name: Required. A name of the form
      `projects/{project_id}/databases/{database_id}`
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class FirestoreProjectsDatabasesDocumentsBatchGetRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsBatchGetRequest object.

  Fields:
    batchGetDocumentsRequest: A BatchGetDocumentsRequest resource to be passed
      as the request body.
    database: Required. The database name. In the format:
      `projects/{project_id}/databases/{database_id}`.
  """

  batchGetDocumentsRequest = _messages.MessageField('BatchGetDocumentsRequest', 1)
  database = _messages.StringField(2, required=True)


class FirestoreProjectsDatabasesDocumentsBatchWriteRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsBatchWriteRequest object.

  Fields:
    batchWriteRequest: A BatchWriteRequest resource to be passed as the
      request body.
    database: Required. The database name. In the format:
      `projects/{project_id}/databases/{database_id}`.
  """

  batchWriteRequest = _messages.MessageField('BatchWriteRequest', 1)
  database = _messages.StringField(2, required=True)


class FirestoreProjectsDatabasesDocumentsBeginTransactionRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsBeginTransactionRequest object.

  Fields:
    beginTransactionRequest: A BeginTransactionRequest resource to be passed
      as the request body.
    database: Required. The database name. In the format:
      `projects/{project_id}/databases/{database_id}`.
  """

  beginTransactionRequest = _messages.MessageField('BeginTransactionRequest', 1)
  database = _messages.StringField(2, required=True)


class FirestoreProjectsDatabasesDocumentsCommitRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsCommitRequest object.

  Fields:
    commitRequest: A CommitRequest resource to be passed as the request body.
    database: Required. The database name. In the format:
      `projects/{project_id}/databases/{database_id}`.
  """

  commitRequest = _messages.MessageField('CommitRequest', 1)
  database = _messages.StringField(2, required=True)


class FirestoreProjectsDatabasesDocumentsCreateDocumentRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsCreateDocumentRequest object.

  Fields:
    collectionId: Required. The collection ID, relative to `parent`, to list.
      For example: `chatrooms`.
    document: A Document resource to be passed as the request body.
    documentId: The client-assigned document ID to use for this document.
      Optional. If not specified, an ID will be assigned by the service.
    mask_fieldPaths: The list of field paths in the mask. See Document.fields
      for a field path syntax reference.
    parent: Required. The parent resource. For example:
      `projects/{project_id}/databases/{database_id}/documents` or `projects/{
      project_id}/databases/{database_id}/documents/chatrooms/{chatroom_id}`
  """

  collectionId = _messages.StringField(1, required=True)
  document = _messages.MessageField('Document', 2)
  documentId = _messages.StringField(3)
  mask_fieldPaths = _messages.StringField(4, repeated=True)
  parent = _messages.StringField(5, required=True)


class FirestoreProjectsDatabasesDocumentsDeleteRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsDeleteRequest object.

  Fields:
    currentDocument_exists: When set to `true`, the target document must
      exist. When set to `false`, the target document must not exist.
    currentDocument_updateTime: When set, the target document must exist and
      have been last updated at that time. Timestamp must be microsecond
      aligned.
    name: Required. The resource name of the Document to delete. In the
      format: `projects/{project_id}/databases/{database_id}/documents/{docume
      nt_path}`.
  """

  currentDocument_exists = _messages.BooleanField(1)
  currentDocument_updateTime = _messages.StringField(2)
  name = _messages.StringField(3, required=True)


class FirestoreProjectsDatabasesDocumentsGetRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsGetRequest object.

  Fields:
    mask_fieldPaths: The list of field paths in the mask. See Document.fields
      for a field path syntax reference.
    name: Required. The resource name of the Document to get. In the format:
      `projects/{project_id}/databases/{database_id}/documents/{document_path}
      `.
    readTime: Reads the version of the document at the given time. This must
      be a microsecond precision timestamp within the past one hour, or if
      Point-in-Time Recovery is enabled, can additionally be a whole minute
      timestamp within the past 7 days.
    transaction: Reads the document in a transaction.
  """

  mask_fieldPaths = _messages.StringField(1, repeated=True)
  name = _messages.StringField(2, required=True)
  readTime = _messages.StringField(3)
  transaction = _messages.BytesField(4)


class FirestoreProjectsDatabasesDocumentsListCollectionIdsRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsListCollectionIdsRequest object.

  Fields:
    listCollectionIdsRequest: A ListCollectionIdsRequest resource to be passed
      as the request body.
    parent: Required. The parent document. In the format:
      `projects/{project_id}/databases/{database_id}/documents/{document_path}
      `. For example: `projects/my-project/databases/my-
      database/documents/chatrooms/my-chatroom`
  """

  listCollectionIdsRequest = _messages.MessageField('ListCollectionIdsRequest', 1)
  parent = _messages.StringField(2, required=True)


class FirestoreProjectsDatabasesDocumentsListDocumentsRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsListDocumentsRequest object.

  Fields:
    collectionId: Optional. The collection ID, relative to `parent`, to list.
      For example: `chatrooms` or `messages`. This is optional, and when not
      provided, Firestore will list documents from all collections under the
      provided `parent`.
    mask_fieldPaths: The list of field paths in the mask. See Document.fields
      for a field path syntax reference.
    orderBy: Optional. The optional ordering of the documents to return. For
      example: `priority desc, __name__ desc`. This mirrors the `ORDER BY`
      used in Firestore queries but in a string representation. When absent,
      documents are ordered based on `__name__ ASC`.
    pageSize: Optional. The maximum number of documents to return in a single
      response. Firestore may return fewer than this value.
    pageToken: Optional. A page token, received from a previous
      `ListDocuments` response. Provide this to retrieve the subsequent page.
      When paginating, all other parameters (with the exception of
      `page_size`) must match the values set in the request that generated the
      page token.
    parent: Required. The parent resource name. In the format:
      `projects/{project_id}/databases/{database_id}/documents` or `projects/{
      project_id}/databases/{database_id}/documents/{document_path}`. For
      example: `projects/my-project/databases/my-database/documents` or
      `projects/my-project/databases/my-database/documents/chatrooms/my-
      chatroom`
    readTime: Perform the read at the provided time. This must be a
      microsecond precision timestamp within the past one hour, or if Point-
      in-Time Recovery is enabled, can additionally be a whole minute
      timestamp within the past 7 days.
    showMissing: If the list should show missing documents. A document is
      missing if it does not exist, but there are sub-documents nested
      underneath it. When true, such missing documents will be returned with a
      key but will not have fields, `create_time`, or `update_time` set.
      Requests with `show_missing` may not specify `where` or `order_by`.
    transaction: Perform the read as part of an already active transaction.
  """

  collectionId = _messages.StringField(1, required=True)
  mask_fieldPaths = _messages.StringField(2, repeated=True)
  orderBy = _messages.StringField(3)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)
  parent = _messages.StringField(6, required=True)
  readTime = _messages.StringField(7)
  showMissing = _messages.BooleanField(8)
  transaction = _messages.BytesField(9)


class FirestoreProjectsDatabasesDocumentsListRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsListRequest object.

  Fields:
    collectionId: Optional. The collection ID, relative to `parent`, to list.
      For example: `chatrooms` or `messages`. This is optional, and when not
      provided, Firestore will list documents from all collections under the
      provided `parent`.
    mask_fieldPaths: The list of field paths in the mask. See Document.fields
      for a field path syntax reference.
    orderBy: Optional. The optional ordering of the documents to return. For
      example: `priority desc, __name__ desc`. This mirrors the `ORDER BY`
      used in Firestore queries but in a string representation. When absent,
      documents are ordered based on `__name__ ASC`.
    pageSize: Optional. The maximum number of documents to return in a single
      response. Firestore may return fewer than this value.
    pageToken: Optional. A page token, received from a previous
      `ListDocuments` response. Provide this to retrieve the subsequent page.
      When paginating, all other parameters (with the exception of
      `page_size`) must match the values set in the request that generated the
      page token.
    parent: Required. The parent resource name. In the format:
      `projects/{project_id}/databases/{database_id}/documents` or `projects/{
      project_id}/databases/{database_id}/documents/{document_path}`. For
      example: `projects/my-project/databases/my-database/documents` or
      `projects/my-project/databases/my-database/documents/chatrooms/my-
      chatroom`
    readTime: Perform the read at the provided time. This must be a
      microsecond precision timestamp within the past one hour, or if Point-
      in-Time Recovery is enabled, can additionally be a whole minute
      timestamp within the past 7 days.
    showMissing: If the list should show missing documents. A document is
      missing if it does not exist, but there are sub-documents nested
      underneath it. When true, such missing documents will be returned with a
      key but will not have fields, `create_time`, or `update_time` set.
      Requests with `show_missing` may not specify `where` or `order_by`.
    transaction: Perform the read as part of an already active transaction.
  """

  collectionId = _messages.StringField(1, required=True)
  mask_fieldPaths = _messages.StringField(2, repeated=True)
  orderBy = _messages.StringField(3)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)
  parent = _messages.StringField(6, required=True)
  readTime = _messages.StringField(7)
  showMissing = _messages.BooleanField(8)
  transaction = _messages.BytesField(9)


class FirestoreProjectsDatabasesDocumentsListenRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsListenRequest object.

  Fields:
    database: Required. The database name. In the format:
      `projects/{project_id}/databases/{database_id}`.
    listenRequest: A ListenRequest resource to be passed as the request body.
  """

  database = _messages.StringField(1, required=True)
  listenRequest = _messages.MessageField('ListenRequest', 2)


class FirestoreProjectsDatabasesDocumentsPartitionQueryRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsPartitionQueryRequest object.

  Fields:
    parent: Required. The parent resource name. In the format:
      `projects/{project_id}/databases/{database_id}/documents`. Document
      resource names are not supported; only database resource names can be
      specified.
    partitionQueryRequest: A PartitionQueryRequest resource to be passed as
      the request body.
  """

  parent = _messages.StringField(1, required=True)
  partitionQueryRequest = _messages.MessageField('PartitionQueryRequest', 2)


class FirestoreProjectsDatabasesDocumentsPatchRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsPatchRequest object.

  Fields:
    currentDocument_exists: When set to `true`, the target document must
      exist. When set to `false`, the target document must not exist.
    currentDocument_updateTime: When set, the target document must exist and
      have been last updated at that time. Timestamp must be microsecond
      aligned.
    document: A Document resource to be passed as the request body.
    mask_fieldPaths: The list of field paths in the mask. See Document.fields
      for a field path syntax reference.
    name: The resource name of the document, for example
      `projects/{project_id}/databases/{database_id}/documents/{document_path}
      `.
    updateMask_fieldPaths: The list of field paths in the mask. See
      Document.fields for a field path syntax reference.
  """

  currentDocument_exists = _messages.BooleanField(1)
  currentDocument_updateTime = _messages.StringField(2)
  document = _messages.MessageField('Document', 3)
  mask_fieldPaths = _messages.StringField(4, repeated=True)
  name = _messages.StringField(5, required=True)
  updateMask_fieldPaths = _messages.StringField(6, repeated=True)


class FirestoreProjectsDatabasesDocumentsRollbackRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsRollbackRequest object.

  Fields:
    database: Required. The database name. In the format:
      `projects/{project_id}/databases/{database_id}`.
    rollbackRequest: A RollbackRequest resource to be passed as the request
      body.
  """

  database = _messages.StringField(1, required=True)
  rollbackRequest = _messages.MessageField('RollbackRequest', 2)


class FirestoreProjectsDatabasesDocumentsRunAggregationQueryRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsRunAggregationQueryRequest object.

  Fields:
    parent: Required. The parent resource name. In the format:
      `projects/{project_id}/databases/{database_id}/documents` or `projects/{
      project_id}/databases/{database_id}/documents/{document_path}`. For
      example: `projects/my-project/databases/my-database/documents` or
      `projects/my-project/databases/my-database/documents/chatrooms/my-
      chatroom`
    runAggregationQueryRequest: A RunAggregationQueryRequest resource to be
      passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  runAggregationQueryRequest = _messages.MessageField('RunAggregationQueryRequest', 2)


class FirestoreProjectsDatabasesDocumentsRunQueryRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsRunQueryRequest object.

  Fields:
    parent: Required. The parent resource name. In the format:
      `projects/{project_id}/databases/{database_id}/documents` or `projects/{
      project_id}/databases/{database_id}/documents/{document_path}`. For
      example: `projects/my-project/databases/my-database/documents` or
      `projects/my-project/databases/my-database/documents/chatrooms/my-
      chatroom`
    runQueryRequest: A RunQueryRequest resource to be passed as the request
      body.
  """

  parent = _messages.StringField(1, required=True)
  runQueryRequest = _messages.MessageField('RunQueryRequest', 2)


class FirestoreProjectsDatabasesDocumentsWriteRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsWriteRequest object.

  Fields:
    database: Required. The database name. In the format:
      `projects/{project_id}/databases/{database_id}`. This is only required
      in the first message.
    writeRequest: A WriteRequest resource to be passed as the request body.
  """

  database = _messages.StringField(1, required=True)
  writeRequest = _messages.MessageField('WriteRequest', 2)


class FirestoreProjectsDatabasesExportDocumentsRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesExportDocumentsRequest object.

  Fields:
    googleFirestoreAdminV1ExportDocumentsRequest: A
      GoogleFirestoreAdminV1ExportDocumentsRequest resource to be passed as
      the request body.
    name: Required. Database to export. Should be of the form:
      `projects/{project_id}/databases/{database_id}`.
  """

  googleFirestoreAdminV1ExportDocumentsRequest = _messages.MessageField('GoogleFirestoreAdminV1ExportDocumentsRequest', 1)
  name = _messages.StringField(2, required=True)


class FirestoreProjectsDatabasesGetRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesGetRequest object.

  Fields:
    name: Required. A name of the form
      `projects/{project_id}/databases/{database_id}`
  """

  name = _messages.StringField(1, required=True)


class FirestoreProjectsDatabasesImportDocumentsRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesImportDocumentsRequest object.

  Fields:
    googleFirestoreAdminV1ImportDocumentsRequest: A
      GoogleFirestoreAdminV1ImportDocumentsRequest resource to be passed as
      the request body.
    name: Required. Database to import into. Should be of the form:
      `projects/{project_id}/databases/{database_id}`.
  """

  googleFirestoreAdminV1ImportDocumentsRequest = _messages.MessageField('GoogleFirestoreAdminV1ImportDocumentsRequest', 1)
  name = _messages.StringField(2, required=True)


class FirestoreProjectsDatabasesListRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesListRequest object.

  Fields:
    parent: Required. A parent name of the form `projects/{project_id}`
    showDeleted: If true, also returns deleted resources.
  """

  parent = _messages.StringField(1, required=True)
  showDeleted = _messages.BooleanField(2)


class FirestoreProjectsDatabasesOperationsCancelRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesOperationsCancelRequest object.

  Fields:
    googleLongrunningCancelOperationRequest: A
      GoogleLongrunningCancelOperationRequest resource to be passed as the
      request body.
    name: The name of the operation resource to be cancelled.
  """

  googleLongrunningCancelOperationRequest = _messages.MessageField('GoogleLongrunningCancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class FirestoreProjectsDatabasesOperationsDeleteRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class FirestoreProjectsDatabasesOperationsGetRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class FirestoreProjectsDatabasesOperationsListRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class FirestoreProjectsDatabasesPatchRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesPatchRequest object.

  Fields:
    googleFirestoreAdminV1Database: A GoogleFirestoreAdminV1Database resource
      to be passed as the request body.
    name: The resource name of the Database. Format:
      `projects/{project}/databases/{database}`
    updateMask: The list of fields to be updated.
  """

  googleFirestoreAdminV1Database = _messages.MessageField('GoogleFirestoreAdminV1Database', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class FirestoreProjectsDatabasesRestoreRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesRestoreRequest object.

  Fields:
    googleFirestoreAdminV1RestoreDatabaseRequest: A
      GoogleFirestoreAdminV1RestoreDatabaseRequest resource to be passed as
      the request body.
    parent: Required. The project to restore the database in. Format is
      `projects/{project_id}`.
  """

  googleFirestoreAdminV1RestoreDatabaseRequest = _messages.MessageField('GoogleFirestoreAdminV1RestoreDatabaseRequest', 1)
  parent = _messages.StringField(2, required=True)


class FirestoreProjectsDatabasesUserCredsCreateRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesUserCredsCreateRequest object.

  Fields:
    googleFirestoreAdminV1UserCreds: A GoogleFirestoreAdminV1UserCreds
      resource to be passed as the request body.
    parent: Required. A parent name of the form
      `projects/{project_id}/databases/{database_id}`
    userCredsId: Required. The ID to use for the user creds, which will become
      the final component of the user creds's resource name. This value should
      be 4-63 characters. Valid characters are /a-z-/ with first character a
      letter and the last a letter or a number. Must not be UUID-like
      /[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}/.
  """

  googleFirestoreAdminV1UserCreds = _messages.MessageField('GoogleFirestoreAdminV1UserCreds', 1)
  parent = _messages.StringField(2, required=True)
  userCredsId = _messages.StringField(3)


class FirestoreProjectsDatabasesUserCredsDeleteRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesUserCredsDeleteRequest object.

  Fields:
    name: Required. A name of the form
      `projects/{project_id}/databases/{database_id}/userCreds/{user_creds_id}
      `
  """

  name = _messages.StringField(1, required=True)


class FirestoreProjectsDatabasesUserCredsDisableRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesUserCredsDisableRequest object.

  Fields:
    googleFirestoreAdminV1DisableUserCredsRequest: A
      GoogleFirestoreAdminV1DisableUserCredsRequest resource to be passed as
      the request body.
    name: Required. A name of the form
      `projects/{project_id}/databases/{database_id}/userCreds/{user_creds_id}
      `
  """

  googleFirestoreAdminV1DisableUserCredsRequest = _messages.MessageField('GoogleFirestoreAdminV1DisableUserCredsRequest', 1)
  name = _messages.StringField(2, required=True)


class FirestoreProjectsDatabasesUserCredsEnableRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesUserCredsEnableRequest object.

  Fields:
    googleFirestoreAdminV1EnableUserCredsRequest: A
      GoogleFirestoreAdminV1EnableUserCredsRequest resource to be passed as
      the request body.
    name: Required. A name of the form
      `projects/{project_id}/databases/{database_id}/userCreds/{user_creds_id}
      `
  """

  googleFirestoreAdminV1EnableUserCredsRequest = _messages.MessageField('GoogleFirestoreAdminV1EnableUserCredsRequest', 1)
  name = _messages.StringField(2, required=True)


class FirestoreProjectsDatabasesUserCredsGetRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesUserCredsGetRequest object.

  Fields:
    name: Required. A name of the form
      `projects/{project_id}/databases/{database_id}/userCreds/{user_creds_id}
      `
  """

  name = _messages.StringField(1, required=True)


class FirestoreProjectsDatabasesUserCredsListRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesUserCredsListRequest object.

  Fields:
    parent: Required. A parent database name of the form
      `projects/{project_id}/databases/{database_id}`
  """

  parent = _messages.StringField(1, required=True)


class FirestoreProjectsDatabasesUserCredsResetPasswordRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesUserCredsResetPasswordRequest object.

  Fields:
    googleFirestoreAdminV1ResetUserPasswordRequest: A
      GoogleFirestoreAdminV1ResetUserPasswordRequest resource to be passed as
      the request body.
    name: Required. A name of the form
      `projects/{project_id}/databases/{database_id}/userCreds/{user_creds_id}
      `
  """

  googleFirestoreAdminV1ResetUserPasswordRequest = _messages.MessageField('GoogleFirestoreAdminV1ResetUserPasswordRequest', 1)
  name = _messages.StringField(2, required=True)


class FirestoreProjectsLocationsBackupsDeleteRequest(_messages.Message):
  r"""A FirestoreProjectsLocationsBackupsDeleteRequest object.

  Fields:
    name: Required. Name of the backup to delete. format is
      `projects/{project}/locations/{location}/backups/{backup}`.
  """

  name = _messages.StringField(1, required=True)


class FirestoreProjectsLocationsBackupsGetRequest(_messages.Message):
  r"""A FirestoreProjectsLocationsBackupsGetRequest object.

  Fields:
    name: Required. Name of the backup to fetch. Format is
      `projects/{project}/locations/{location}/backups/{backup}`.
  """

  name = _messages.StringField(1, required=True)


class FirestoreProjectsLocationsBackupsListRequest(_messages.Message):
  r"""A FirestoreProjectsLocationsBackupsListRequest object.

  Fields:
    filter: An expression that filters the list of returned backups. A filter
      expression consists of a field name, a comparison operator, and a value
      for filtering. The value must be a string, a number, or a boolean. The
      comparison operator must be one of: `<`, `>`, `<=`, `>=`, `!=`, `=`, or
      `:`. Colon `:` is the contains operator. Filter rules are not case
      sensitive. The following fields in the Backup are eligible for
      filtering: * `database_uid` (supports `=` only)
    parent: Required. The location to list backups from. Format is
      `projects/{project}/locations/{location}`. Use `{location} = '-'` to
      list backups from all locations for the given project. This allows
      listing backups from a single location or from all locations.
  """

  filter = _messages.StringField(1)
  parent = _messages.StringField(2, required=True)


class FirestoreProjectsLocationsGetRequest(_messages.Message):
  r"""A FirestoreProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class FirestoreProjectsLocationsListRequest(_messages.Message):
  r"""A FirestoreProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class GoogleFirestoreAdminV1Backup(_messages.Message):
  r"""A Backup of a Cloud Firestore Database. The backup contains all
  documents and index configurations for the given database at a specific
  point in time.

  Enums:
    StateValueValuesEnum: Output only. The current state of the backup.

  Fields:
    database: Output only. Name of the Firestore database that the backup is
      from. Format is `projects/{project}/databases/{database}`.
    databaseUid: Output only. The system-generated UUID4 for the Firestore
      database that the backup is from.
    expireTime: Output only. The timestamp at which this backup expires.
    name: Output only. The unique resource name of the Backup. Format is
      `projects/{project}/locations/{location}/backups/{backup}`.
    snapshotTime: Output only. The backup contains an externally consistent
      copy of the database at this time.
    state: Output only. The current state of the backup.
    stats: Output only. Statistics about the backup. This data only becomes
      available after the backup is fully materialized to secondary storage.
      This field will be empty till then.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the backup.

    Values:
      STATE_UNSPECIFIED: The state is unspecified.
      CREATING: The pending backup is still being created. Operations on the
        backup will be rejected in this state.
      READY: The backup is complete and ready to use.
      NOT_AVAILABLE: The backup is not available at this moment.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    NOT_AVAILABLE = 3

  database = _messages.StringField(1)
  databaseUid = _messages.StringField(2)
  expireTime = _messages.StringField(3)
  name = _messages.StringField(4)
  snapshotTime = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  stats = _messages.MessageField('GoogleFirestoreAdminV1Stats', 7)


class GoogleFirestoreAdminV1BackupSchedule(_messages.Message):
  r"""A backup schedule for a Cloud Firestore Database. This resource is owned
  by the database it is backing up, and is deleted along with the database.
  The actual backups are not though.

  Fields:
    createTime: Output only. The timestamp at which this backup schedule was
      created and effective since. No backups will be created for this
      schedule before this time.
    dailyRecurrence: For a schedule that runs daily.
    name: Output only. The unique backup schedule identifier across all
      locations and databases for the given project. This will be auto-
      assigned. Format is `projects/{project}/databases/{database}/backupSched
      ules/{backup_schedule}`
    retention: At what relative time in the future, compared to its creation
      time, the backup should be deleted, e.g. keep backups for 7 days. The
      maximum supported retention period is 14 weeks.
    updateTime: Output only. The timestamp at which this backup schedule was
      most recently updated. When a backup schedule is first created, this is
      the same as create_time.
    weeklyRecurrence: For a schedule that runs weekly on a specific day.
  """

  createTime = _messages.StringField(1)
  dailyRecurrence = _messages.MessageField('GoogleFirestoreAdminV1DailyRecurrence', 2)
  name = _messages.StringField(3)
  retention = _messages.StringField(4)
  updateTime = _messages.StringField(5)
  weeklyRecurrence = _messages.MessageField('GoogleFirestoreAdminV1WeeklyRecurrence', 6)


class GoogleFirestoreAdminV1BackupSource(_messages.Message):
  r"""Information about a backup that was used to restore a database.

  Fields:
    backup: The resource name of the backup that was used to restore this
      database. Format:
      `projects/{project}/locations/{location}/backups/{backup}`.
  """

  backup = _messages.StringField(1)


class GoogleFirestoreAdminV1BulkDeleteDocumentsMetadata(_messages.Message):
  r"""Metadata for google.longrunning.Operation results from
  FirestoreAdmin.BulkDeleteDocuments.

  Enums:
    OperationStateValueValuesEnum: The state of the operation.

  Fields:
    collectionIds: The IDs of the collection groups that are being deleted.
    endTime: The time this operation completed. Will be unset if operation
      still in progress.
    namespaceIds: Which namespace IDs are being deleted.
    operationState: The state of the operation.
    progressBytes: The progress, in bytes, of this operation.
    progressDocuments: The progress, in documents, of this operation.
    snapshotTime: The timestamp that corresponds to the version of the
      database that is being read to get the list of documents to delete. This
      time can also be used as the timestamp of PITR in case of disaster
      recovery (subject to PITR window limit).
    startTime: The time this operation started.
  """

  class OperationStateValueValuesEnum(_messages.Enum):
    r"""The state of the operation.

    Values:
      OPERATION_STATE_UNSPECIFIED: Unspecified.
      INITIALIZING: Request is being prepared for processing.
      PROCESSING: Request is actively being processed.
      CANCELLING: Request is in the process of being cancelled after user
        called google.longrunning.Operations.CancelOperation on the operation.
      FINALIZING: Request has been processed and is in its finalization stage.
      SUCCESSFUL: Request has completed successfully.
      FAILED: Request has finished being processed, but encountered an error.
      CANCELLED: Request has finished being cancelled after user called
        google.longrunning.Operations.CancelOperation.
    """
    OPERATION_STATE_UNSPECIFIED = 0
    INITIALIZING = 1
    PROCESSING = 2
    CANCELLING = 3
    FINALIZING = 4
    SUCCESSFUL = 5
    FAILED = 6
    CANCELLED = 7

  collectionIds = _messages.StringField(1, repeated=True)
  endTime = _messages.StringField(2)
  namespaceIds = _messages.StringField(3, repeated=True)
  operationState = _messages.EnumField('OperationStateValueValuesEnum', 4)
  progressBytes = _messages.MessageField('GoogleFirestoreAdminV1Progress', 5)
  progressDocuments = _messages.MessageField('GoogleFirestoreAdminV1Progress', 6)
  snapshotTime = _messages.StringField(7)
  startTime = _messages.StringField(8)


class GoogleFirestoreAdminV1BulkDeleteDocumentsRequest(_messages.Message):
  r"""The request for FirestoreAdmin.BulkDeleteDocuments. When both
  collection_ids and namespace_ids are set, only documents satisfying both
  conditions will be deleted. Requests with namespace_ids and collection_ids
  both empty will be rejected. Please use FirestoreAdmin.DeleteDatabase
  instead.

  Fields:
    collectionIds: Optional. IDs of the collection groups to delete.
      Unspecified means all collection groups. Each collection group in this
      list must be unique.
    namespaceIds: Optional. Namespaces to delete. An empty list means all
      namespaces. This is the recommended usage for databases that don't use
      namespaces. An empty string element represents the default namespace.
      This should be used if the database has data in non-default namespaces,
      but doesn't want to delete from them. Each namespace in this list must
      be unique.
  """

  collectionIds = _messages.StringField(1, repeated=True)
  namespaceIds = _messages.StringField(2, repeated=True)


class GoogleFirestoreAdminV1CloneDatabaseMetadata(_messages.Message):
  r"""Metadata for the long-running operation from the CloneDatabase request.

  Enums:
    OperationStateValueValuesEnum: The operation state of the clone.

  Fields:
    database: The name of the database being cloned to.
    endTime: The time the clone finished, unset for ongoing clones.
    operationState: The operation state of the clone.
    pitrSnapshot: The snapshot from which this database was cloned.
    progressPercentage: How far along the clone is as an estimated percentage
      of remaining time.
    startTime: The time the clone was started.
  """

  class OperationStateValueValuesEnum(_messages.Enum):
    r"""The operation state of the clone.

    Values:
      OPERATION_STATE_UNSPECIFIED: Unspecified.
      INITIALIZING: Request is being prepared for processing.
      PROCESSING: Request is actively being processed.
      CANCELLING: Request is in the process of being cancelled after user
        called google.longrunning.Operations.CancelOperation on the operation.
      FINALIZING: Request has been processed and is in its finalization stage.
      SUCCESSFUL: Request has completed successfully.
      FAILED: Request has finished being processed, but encountered an error.
      CANCELLED: Request has finished being cancelled after user called
        google.longrunning.Operations.CancelOperation.
    """
    OPERATION_STATE_UNSPECIFIED = 0
    INITIALIZING = 1
    PROCESSING = 2
    CANCELLING = 3
    FINALIZING = 4
    SUCCESSFUL = 5
    FAILED = 6
    CANCELLED = 7

  database = _messages.StringField(1)
  endTime = _messages.StringField(2)
  operationState = _messages.EnumField('OperationStateValueValuesEnum', 3)
  pitrSnapshot = _messages.MessageField('GoogleFirestoreAdminV1PitrSnapshot', 4)
  progressPercentage = _messages.MessageField('GoogleFirestoreAdminV1Progress', 5)
  startTime = _messages.StringField(6)


class GoogleFirestoreAdminV1CloneDatabaseRequest(_messages.Message):
  r"""The request message for FirestoreAdmin.CloneDatabase.

  Messages:
    TagsValue: Optional. Immutable. Tags to be bound to the cloned database.
      The tags should be provided in the format of `tagKeys/{tag_key_id} ->
      tagValues/{tag_value_id}`.

  Fields:
    databaseId: Required. The ID to use for the database, which will become
      the final component of the database's resource name. This database ID
      must not be associated with an existing database. This value should be
      4-63 characters. Valid characters are /a-z-/ with first character a
      letter and the last a letter or a number. Must not be UUID-like
      /[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}/. "(default)" database ID is
      also valid.
    encryptionConfig: Optional. Encryption configuration for the cloned
      database. If this field is not specified, the cloned database will use
      the same encryption configuration as the source database, namely
      use_source_encryption.
    pitrSnapshot: Required. Specification of the PITR data to clone from. The
      source database must exist. The cloned database will be created in the
      same location as the source database.
    tags: Optional. Immutable. Tags to be bound to the cloned database. The
      tags should be provided in the format of `tagKeys/{tag_key_id} ->
      tagValues/{tag_value_id}`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""Optional. Immutable. Tags to be bound to the cloned database. The tags
    should be provided in the format of `tagKeys/{tag_key_id} ->
    tagValues/{tag_value_id}`.

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  databaseId = _messages.StringField(1)
  encryptionConfig = _messages.MessageField('GoogleFirestoreAdminV1EncryptionConfig', 2)
  pitrSnapshot = _messages.MessageField('GoogleFirestoreAdminV1PitrSnapshot', 3)
  tags = _messages.MessageField('TagsValue', 4)


class GoogleFirestoreAdminV1CmekConfig(_messages.Message):
  r"""The CMEK (Customer Managed Encryption Key) configuration for a Firestore
  database. If not present, the database is secured by the default Google
  encryption key.

  Fields:
    activeKeyVersion: Output only. Currently in-use [KMS key
      versions](https://cloud.google.com/kms/docs/resource-
      hierarchy#key_versions). During [key
      rotation](https://cloud.google.com/kms/docs/key-rotation), there can be
      multiple in-use key versions. The expected format is `projects/{project_
      id}/locations/{kms_location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}
      /cryptoKeyVersions/{key_version}`.
    kmsKeyName: Required. Only keys in the same location as this database are
      allowed to be used for encryption. For Firestore's nam5 multi-region,
      this corresponds to Cloud KMS multi-region us. For Firestore's eur3
      multi-region, this corresponds to Cloud KMS multi-region europe. See
      https://cloud.google.com/kms/docs/locations. The expected format is `pro
      jects/{project_id}/locations/{kms_location}/keyRings/{key_ring}/cryptoKe
      ys/{crypto_key}`.
  """

  activeKeyVersion = _messages.StringField(1, repeated=True)
  kmsKeyName = _messages.StringField(2)


class GoogleFirestoreAdminV1CreateDatabaseMetadata(_messages.Message):
  r"""Metadata related to the create database operation."""


class GoogleFirestoreAdminV1CustomerManagedEncryptionOptions(_messages.Message):
  r"""The configuration options for using CMEK (Customer Managed Encryption
  Key) encryption.

  Fields:
    kmsKeyName: Required. Only keys in the same location as the database are
      allowed to be used for encryption. For Firestore's nam5 multi-region,
      this corresponds to Cloud KMS multi-region us. For Firestore's eur3
      multi-region, this corresponds to Cloud KMS multi-region europe. See
      https://cloud.google.com/kms/docs/locations. The expected format is `pro
      jects/{project_id}/locations/{kms_location}/keyRings/{key_ring}/cryptoKe
      ys/{crypto_key}`.
  """

  kmsKeyName = _messages.StringField(1)


class GoogleFirestoreAdminV1DailyRecurrence(_messages.Message):
  r"""Represents a recurring schedule that runs every day. The time zone is
  UTC.
  """



class GoogleFirestoreAdminV1Database(_messages.Message):
  r"""A Cloud Firestore Database.

  Enums:
    AppEngineIntegrationModeValueValuesEnum: The App Engine integration mode
      to use for this database.
    ConcurrencyModeValueValuesEnum: The concurrency control mode to use for
      this database.
    DatabaseEditionValueValuesEnum: Immutable. The edition of the database.
    DeleteProtectionStateValueValuesEnum: State of delete protection for the
      database.
    PointInTimeRecoveryEnablementValueValuesEnum: Whether to enable the PITR
      feature on this database.
    TypeValueValuesEnum: The type of the database. See
      https://cloud.google.com/datastore/docs/firestore-or-datastore for
      information about how to choose.

  Messages:
    TagsValue: Optional. Input only. Immutable. Tag keys/values directly bound
      to this resource. For example: "123/environment": "production",
      "123/costCenter": "marketing"

  Fields:
    appEngineIntegrationMode: The App Engine integration mode to use for this
      database.
    cmekConfig: Optional. Presence indicates CMEK is enabled for this
      database.
    concurrencyMode: The concurrency control mode to use for this database.
    createTime: Output only. The timestamp at which this database was created.
      Databases created before 2016 do not populate create_time.
    databaseEdition: Immutable. The edition of the database.
    deleteProtectionState: State of delete protection for the database.
    deleteTime: Output only. The timestamp at which this database was deleted.
      Only set if the database has been deleted.
    earliestVersionTime: Output only. The earliest timestamp at which older
      versions of the data can be read from the database. See
      [version_retention_period] above; this field is populated with `now -
      version_retention_period`. This value is continuously updated, and
      becomes stale the moment it is queried. If you are using this value to
      recover data, make sure to account for the time from the moment when the
      value is queried to the moment when you initiate the recovery.
    etag: This checksum is computed by the server based on the value of other
      fields, and may be sent on update and delete requests to ensure the
      client has an up-to-date value before proceeding.
    freeTier: Output only. Background: Free tier is the ability of a Firestore
      database to use a small amount of resources every day without being
      charged. Once usage exceeds the free tier limit further usage is
      charged. Whether this database can make use of the free tier. Only one
      database per project can be eligible for the free tier. The first (or
      next) database that is created in a project without a free tier database
      will be marked as eligible for the free tier. Databases that are created
      while there is a free tier database will not be eligible for the free
      tier.
    keyPrefix: Output only. The key_prefix for this database. This key_prefix
      is used, in combination with the project ID ("~") to construct the
      application ID that is returned from the Cloud Datastore APIs in Google
      App Engine first generation runtimes. This value may be empty in which
      case the appid to use for URL-encoded keys is the project_id (eg: foo
      instead of v~foo).
    locationId: The location of the database. Available locations are listed
      at https://cloud.google.com/firestore/docs/locations.
    name: The resource name of the Database. Format:
      `projects/{project}/databases/{database}`
    pointInTimeRecoveryEnablement: Whether to enable the PITR feature on this
      database.
    previousId: Output only. The database resource's prior database ID. This
      field is only populated for deleted databases.
    sourceInfo: Output only. Information about the provenance of this
      database.
    tags: Optional. Input only. Immutable. Tag keys/values directly bound to
      this resource. For example: "123/environment": "production",
      "123/costCenter": "marketing"
    type: The type of the database. See
      https://cloud.google.com/datastore/docs/firestore-or-datastore for
      information about how to choose.
    uid: Output only. The system-generated UUID4 for this Database.
    updateTime: Output only. The timestamp at which this database was most
      recently updated. Note this only includes updates to the database
      resource and not data contained by the database.
    versionRetentionPeriod: Output only. The period during which past versions
      of data are retained in the database. Any read or query can specify a
      `read_time` within this window, and will read the state of the database
      at that time. If the PITR feature is enabled, the retention period is 7
      days. Otherwise, the retention period is 1 hour.
  """

  class AppEngineIntegrationModeValueValuesEnum(_messages.Enum):
    r"""The App Engine integration mode to use for this database.

    Values:
      APP_ENGINE_INTEGRATION_MODE_UNSPECIFIED: Not used.
      ENABLED: If an App Engine application exists in the same region as this
        database, App Engine configuration will impact this database. This
        includes disabling of the application & database, as well as disabling
        writes to the database.
      DISABLED: App Engine has no effect on the ability of this database to
        serve requests. This is the default setting for databases created with
        the Firestore API.
    """
    APP_ENGINE_INTEGRATION_MODE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  class ConcurrencyModeValueValuesEnum(_messages.Enum):
    r"""The concurrency control mode to use for this database.

    Values:
      CONCURRENCY_MODE_UNSPECIFIED: Not used.
      OPTIMISTIC: Use optimistic concurrency control by default. This mode is
        available for Cloud Firestore databases.
      PESSIMISTIC: Use pessimistic concurrency control by default. This mode
        is available for Cloud Firestore databases. This is the default
        setting for Cloud Firestore.
      OPTIMISTIC_WITH_ENTITY_GROUPS: Use optimistic concurrency control with
        entity groups by default. This is the only available mode for Cloud
        Datastore. This mode is also available for Cloud Firestore with
        Datastore Mode but is not recommended.
    """
    CONCURRENCY_MODE_UNSPECIFIED = 0
    OPTIMISTIC = 1
    PESSIMISTIC = 2
    OPTIMISTIC_WITH_ENTITY_GROUPS = 3

  class DatabaseEditionValueValuesEnum(_messages.Enum):
    r"""Immutable. The edition of the database.

    Values:
      DATABASE_EDITION_UNSPECIFIED: Not used.
      STANDARD: Standard edition. This is the default setting if not
        specified.
      ENTERPRISE: Enterprise edition.
    """
    DATABASE_EDITION_UNSPECIFIED = 0
    STANDARD = 1
    ENTERPRISE = 2

  class DeleteProtectionStateValueValuesEnum(_messages.Enum):
    r"""State of delete protection for the database.

    Values:
      DELETE_PROTECTION_STATE_UNSPECIFIED: The default value. Delete
        protection type is not specified
      DELETE_PROTECTION_DISABLED: Delete protection is disabled
      DELETE_PROTECTION_ENABLED: Delete protection is enabled
    """
    DELETE_PROTECTION_STATE_UNSPECIFIED = 0
    DELETE_PROTECTION_DISABLED = 1
    DELETE_PROTECTION_ENABLED = 2

  class PointInTimeRecoveryEnablementValueValuesEnum(_messages.Enum):
    r"""Whether to enable the PITR feature on this database.

    Values:
      POINT_IN_TIME_RECOVERY_ENABLEMENT_UNSPECIFIED: Not used.
      POINT_IN_TIME_RECOVERY_ENABLED: Reads are supported on selected versions
        of the data from within the past 7 days: * Reads against any timestamp
        within the past hour * Reads against 1-minute snapshots beyond 1 hour
        and within 7 days `version_retention_period` and
        `earliest_version_time` can be used to determine the supported
        versions.
      POINT_IN_TIME_RECOVERY_DISABLED: Reads are supported on any version of
        the data from within the past 1 hour.
    """
    POINT_IN_TIME_RECOVERY_ENABLEMENT_UNSPECIFIED = 0
    POINT_IN_TIME_RECOVERY_ENABLED = 1
    POINT_IN_TIME_RECOVERY_DISABLED = 2

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of the database. See
    https://cloud.google.com/datastore/docs/firestore-or-datastore for
    information about how to choose.

    Values:
      DATABASE_TYPE_UNSPECIFIED: Not used.
      FIRESTORE_NATIVE: Firestore Native Mode
      DATASTORE_MODE: Firestore in Datastore Mode.
    """
    DATABASE_TYPE_UNSPECIFIED = 0
    FIRESTORE_NATIVE = 1
    DATASTORE_MODE = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""Optional. Input only. Immutable. Tag keys/values directly bound to
    this resource. For example: "123/environment": "production",
    "123/costCenter": "marketing"

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  appEngineIntegrationMode = _messages.EnumField('AppEngineIntegrationModeValueValuesEnum', 1)
  cmekConfig = _messages.MessageField('GoogleFirestoreAdminV1CmekConfig', 2)
  concurrencyMode = _messages.EnumField('ConcurrencyModeValueValuesEnum', 3)
  createTime = _messages.StringField(4)
  databaseEdition = _messages.EnumField('DatabaseEditionValueValuesEnum', 5)
  deleteProtectionState = _messages.EnumField('DeleteProtectionStateValueValuesEnum', 6)
  deleteTime = _messages.StringField(7)
  earliestVersionTime = _messages.StringField(8)
  etag = _messages.StringField(9)
  freeTier = _messages.BooleanField(10)
  keyPrefix = _messages.StringField(11)
  locationId = _messages.StringField(12)
  name = _messages.StringField(13)
  pointInTimeRecoveryEnablement = _messages.EnumField('PointInTimeRecoveryEnablementValueValuesEnum', 14)
  previousId = _messages.StringField(15)
  sourceInfo = _messages.MessageField('GoogleFirestoreAdminV1SourceInfo', 16)
  tags = _messages.MessageField('TagsValue', 17)
  type = _messages.EnumField('TypeValueValuesEnum', 18)
  uid = _messages.StringField(19)
  updateTime = _messages.StringField(20)
  versionRetentionPeriod = _messages.StringField(21)


class GoogleFirestoreAdminV1DeleteDatabaseMetadata(_messages.Message):
  r"""Metadata related to the delete database operation."""


class GoogleFirestoreAdminV1DisableUserCredsRequest(_messages.Message):
  r"""The request for FirestoreAdmin.DisableUserCreds."""


class GoogleFirestoreAdminV1EnableUserCredsRequest(_messages.Message):
  r"""The request for FirestoreAdmin.EnableUserCreds."""


class GoogleFirestoreAdminV1EncryptionConfig(_messages.Message):
  r"""Encryption configuration for a new database being created from another
  source. The source could be a Backup .

  Fields:
    customerManagedEncryption: Use Customer Managed Encryption Keys (CMEK) for
      encryption.
    googleDefaultEncryption: Use Google default encryption.
    useSourceEncryption: The database will use the same encryption
      configuration as the source.
  """

  customerManagedEncryption = _messages.MessageField('GoogleFirestoreAdminV1CustomerManagedEncryptionOptions', 1)
  googleDefaultEncryption = _messages.MessageField('GoogleFirestoreAdminV1GoogleDefaultEncryptionOptions', 2)
  useSourceEncryption = _messages.MessageField('GoogleFirestoreAdminV1SourceEncryptionOptions', 3)


class GoogleFirestoreAdminV1ExportDocumentsMetadata(_messages.Message):
  r"""Metadata for google.longrunning.Operation results from
  FirestoreAdmin.ExportDocuments.

  Enums:
    OperationStateValueValuesEnum: The state of the export operation.

  Fields:
    collectionIds: Which collection IDs are being exported.
    endTime: The time this operation completed. Will be unset if operation
      still in progress.
    namespaceIds: Which namespace IDs are being exported.
    operationState: The state of the export operation.
    outputUriPrefix: Where the documents are being exported to.
    progressBytes: The progress, in bytes, of this operation.
    progressDocuments: The progress, in documents, of this operation.
    snapshotTime: The timestamp that corresponds to the version of the
      database that is being exported. If unspecified, there are no guarantees
      about the consistency of the documents being exported.
    startTime: The time this operation started.
  """

  class OperationStateValueValuesEnum(_messages.Enum):
    r"""The state of the export operation.

    Values:
      OPERATION_STATE_UNSPECIFIED: Unspecified.
      INITIALIZING: Request is being prepared for processing.
      PROCESSING: Request is actively being processed.
      CANCELLING: Request is in the process of being cancelled after user
        called google.longrunning.Operations.CancelOperation on the operation.
      FINALIZING: Request has been processed and is in its finalization stage.
      SUCCESSFUL: Request has completed successfully.
      FAILED: Request has finished being processed, but encountered an error.
      CANCELLED: Request has finished being cancelled after user called
        google.longrunning.Operations.CancelOperation.
    """
    OPERATION_STATE_UNSPECIFIED = 0
    INITIALIZING = 1
    PROCESSING = 2
    CANCELLING = 3
    FINALIZING = 4
    SUCCESSFUL = 5
    FAILED = 6
    CANCELLED = 7

  collectionIds = _messages.StringField(1, repeated=True)
  endTime = _messages.StringField(2)
  namespaceIds = _messages.StringField(3, repeated=True)
  operationState = _messages.EnumField('OperationStateValueValuesEnum', 4)
  outputUriPrefix = _messages.StringField(5)
  progressBytes = _messages.MessageField('GoogleFirestoreAdminV1Progress', 6)
  progressDocuments = _messages.MessageField('GoogleFirestoreAdminV1Progress', 7)
  snapshotTime = _messages.StringField(8)
  startTime = _messages.StringField(9)


class GoogleFirestoreAdminV1ExportDocumentsRequest(_messages.Message):
  r"""The request for FirestoreAdmin.ExportDocuments.

  Fields:
    collectionIds: Which collection IDs to export. Unspecified means all
      collections. Each collection ID in this list must be unique.
    namespaceIds: An empty list represents all namespaces. This is the
      preferred usage for databases that don't use namespaces. An empty string
      element represents the default namespace. This should be used if the
      database has data in non-default namespaces, but doesn't want to include
      them. Each namespace in this list must be unique.
    outputUriPrefix: The output URI. Currently only supports Google Cloud
      Storage URIs of the form: `gs://BUCKET_NAME[/NAMESPACE_PATH]`, where
      `BUCKET_NAME` is the name of the Google Cloud Storage bucket and
      `NAMESPACE_PATH` is an optional Google Cloud Storage namespace path.
      When choosing a name, be sure to consider Google Cloud Storage naming
      guidelines: https://cloud.google.com/storage/docs/naming. If the URI is
      a bucket (without a namespace path), a prefix will be generated based on
      the start time.
    snapshotTime: The timestamp that corresponds to the version of the
      database to be exported. The timestamp must be in the past, rounded to
      the minute and not older than earliestVersionTime. If specified, then
      the exported documents will represent a consistent view of the database
      at the provided time. Otherwise, there are no guarantees about the
      consistency of the exported documents.
  """

  collectionIds = _messages.StringField(1, repeated=True)
  namespaceIds = _messages.StringField(2, repeated=True)
  outputUriPrefix = _messages.StringField(3)
  snapshotTime = _messages.StringField(4)


class GoogleFirestoreAdminV1ExportDocumentsResponse(_messages.Message):
  r"""Returned in the google.longrunning.Operation response field.

  Fields:
    outputUriPrefix: Location of the output files. This can be used to begin
      an import into Cloud Firestore (this project or another project) after
      the operation completes successfully.
  """

  outputUriPrefix = _messages.StringField(1)


class GoogleFirestoreAdminV1Field(_messages.Message):
  r"""Represents a single field in the database. Fields are grouped by their
  "Collection Group", which represent all collections in the database with the
  same ID.

  Fields:
    indexConfig: The index configuration for this field. If unset, field
      indexing will revert to the configuration defined by the
      `ancestor_field`. To explicitly remove all indexes for this field,
      specify an index config with an empty list of indexes.
    name: Required. A field name of the form: `projects/{project_id}/databases
      /{database_id}/collectionGroups/{collection_id}/fields/{field_path}` A
      field path can be a simple field name, e.g. `address` or a path to
      fields within `map_value` , e.g. `address.city`, or a special field
      path. The only valid special field is `*`, which represents any field.
      Field paths can be quoted using `` ` `` (backtick). The only character
      that must be escaped within a quoted field path is the backtick
      character itself, escaped using a backslash. Special characters in field
      paths that must be quoted include: `*`, `.`, `` ` `` (backtick), `[`,
      `]`, as well as any ascii symbolic characters. Examples: ``
      `address.city` `` represents a field named `address.city`, not the map
      key `city` in the field `address`. `` `*` `` represents a field named
      `*`, not any field. A special `Field` contains the default indexing
      settings for all fields. This field's resource name is: `projects/{proje
      ct_id}/databases/{database_id}/collectionGroups/__default__/fields/*`
      Indexes defined on this `Field` will be applied to all fields which do
      not have their own `Field` index configuration.
    ttlConfig: The TTL configuration for this `Field`. Setting or unsetting
      this will enable or disable the TTL for documents that have this
      `Field`.
  """

  indexConfig = _messages.MessageField('GoogleFirestoreAdminV1IndexConfig', 1)
  name = _messages.StringField(2)
  ttlConfig = _messages.MessageField('GoogleFirestoreAdminV1TtlConfig', 3)


class GoogleFirestoreAdminV1FieldOperationMetadata(_messages.Message):
  r"""Metadata for google.longrunning.Operation results from
  FirestoreAdmin.UpdateField.

  Enums:
    StateValueValuesEnum: The state of the operation.

  Fields:
    endTime: The time this operation completed. Will be unset if operation
      still in progress.
    field: The field resource that this operation is acting on. For example: `
      projects/{project_id}/databases/{database_id}/collectionGroups/{collecti
      on_id}/fields/{field_path}`
    indexConfigDeltas: A list of IndexConfigDelta, which describe the intent
      of this operation.
    progressBytes: The progress, in bytes, of this operation.
    progressDocuments: The progress, in documents, of this operation.
    startTime: The time this operation started.
    state: The state of the operation.
    ttlConfigDelta: Describes the deltas of TTL configuration.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of the operation.

    Values:
      OPERATION_STATE_UNSPECIFIED: Unspecified.
      INITIALIZING: Request is being prepared for processing.
      PROCESSING: Request is actively being processed.
      CANCELLING: Request is in the process of being cancelled after user
        called google.longrunning.Operations.CancelOperation on the operation.
      FINALIZING: Request has been processed and is in its finalization stage.
      SUCCESSFUL: Request has completed successfully.
      FAILED: Request has finished being processed, but encountered an error.
      CANCELLED: Request has finished being cancelled after user called
        google.longrunning.Operations.CancelOperation.
    """
    OPERATION_STATE_UNSPECIFIED = 0
    INITIALIZING = 1
    PROCESSING = 2
    CANCELLING = 3
    FINALIZING = 4
    SUCCESSFUL = 5
    FAILED = 6
    CANCELLED = 7

  endTime = _messages.StringField(1)
  field = _messages.StringField(2)
  indexConfigDeltas = _messages.MessageField('GoogleFirestoreAdminV1IndexConfigDelta', 3, repeated=True)
  progressBytes = _messages.MessageField('GoogleFirestoreAdminV1Progress', 4)
  progressDocuments = _messages.MessageField('GoogleFirestoreAdminV1Progress', 5)
  startTime = _messages.StringField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  ttlConfigDelta = _messages.MessageField('GoogleFirestoreAdminV1TtlConfigDelta', 8)


class GoogleFirestoreAdminV1FlatIndex(_messages.Message):
  r"""An index that stores vectors in a flat data structure, and supports
  exhaustive search.
  """



class GoogleFirestoreAdminV1GoogleDefaultEncryptionOptions(_messages.Message):
  r"""The configuration options for using Google default encryption."""


class GoogleFirestoreAdminV1ImportDocumentsMetadata(_messages.Message):
  r"""Metadata for google.longrunning.Operation results from
  FirestoreAdmin.ImportDocuments.

  Enums:
    OperationStateValueValuesEnum: The state of the import operation.

  Fields:
    collectionIds: Which collection IDs are being imported.
    endTime: The time this operation completed. Will be unset if operation
      still in progress.
    inputUriPrefix: The location of the documents being imported.
    namespaceIds: Which namespace IDs are being imported.
    operationState: The state of the import operation.
    progressBytes: The progress, in bytes, of this operation.
    progressDocuments: The progress, in documents, of this operation.
    startTime: The time this operation started.
  """

  class OperationStateValueValuesEnum(_messages.Enum):
    r"""The state of the import operation.

    Values:
      OPERATION_STATE_UNSPECIFIED: Unspecified.
      INITIALIZING: Request is being prepared for processing.
      PROCESSING: Request is actively being processed.
      CANCELLING: Request is in the process of being cancelled after user
        called google.longrunning.Operations.CancelOperation on the operation.
      FINALIZING: Request has been processed and is in its finalization stage.
      SUCCESSFUL: Request has completed successfully.
      FAILED: Request has finished being processed, but encountered an error.
      CANCELLED: Request has finished being cancelled after user called
        google.longrunning.Operations.CancelOperation.
    """
    OPERATION_STATE_UNSPECIFIED = 0
    INITIALIZING = 1
    PROCESSING = 2
    CANCELLING = 3
    FINALIZING = 4
    SUCCESSFUL = 5
    FAILED = 6
    CANCELLED = 7

  collectionIds = _messages.StringField(1, repeated=True)
  endTime = _messages.StringField(2)
  inputUriPrefix = _messages.StringField(3)
  namespaceIds = _messages.StringField(4, repeated=True)
  operationState = _messages.EnumField('OperationStateValueValuesEnum', 5)
  progressBytes = _messages.MessageField('GoogleFirestoreAdminV1Progress', 6)
  progressDocuments = _messages.MessageField('GoogleFirestoreAdminV1Progress', 7)
  startTime = _messages.StringField(8)


class GoogleFirestoreAdminV1ImportDocumentsRequest(_messages.Message):
  r"""The request for FirestoreAdmin.ImportDocuments.

  Fields:
    collectionIds: Which collection IDs to import. Unspecified means all
      collections included in the import. Each collection ID in this list must
      be unique.
    inputUriPrefix: Location of the exported files. This must match the
      output_uri_prefix of an ExportDocumentsResponse from an export that has
      completed successfully. See:
      google.firestore.admin.v1.ExportDocumentsResponse.output_uri_prefix.
    namespaceIds: An empty list represents all namespaces. This is the
      preferred usage for databases that don't use namespaces. An empty string
      element represents the default namespace. This should be used if the
      database has data in non-default namespaces, but doesn't want to include
      them. Each namespace in this list must be unique.
  """

  collectionIds = _messages.StringField(1, repeated=True)
  inputUriPrefix = _messages.StringField(2)
  namespaceIds = _messages.StringField(3, repeated=True)


class GoogleFirestoreAdminV1Index(_messages.Message):
  r"""Cloud Firestore indexes enable simple and complex queries against
  documents in a database.

  Enums:
    ApiScopeValueValuesEnum: The API scope supported by this index.
    DensityValueValuesEnum: Immutable. The density configuration of the index.
    QueryScopeValueValuesEnum: Indexes with a collection query scope specified
      allow queries against a collection that is the child of a specific
      document, specified at query time, and that has the same collection ID.
      Indexes with a collection group query scope specified allow queries
      against all collections descended from a specific document, specified at
      query time, and that have the same collection ID as this index.
    StateValueValuesEnum: Output only. The serving state of the index.

  Fields:
    apiScope: The API scope supported by this index.
    density: Immutable. The density configuration of the index.
    fields: The fields supported by this index. For composite indexes, this
      requires a minimum of 2 and a maximum of 100 fields. The last field
      entry is always for the field path `__name__`. If, on creation,
      `__name__` was not specified as the last field, it will be added
      automatically with the same direction as that of the last field defined.
      If the final field in a composite index is not directional, the
      `__name__` will be ordered ASCENDING (unless explicitly specified). For
      single field indexes, this will always be exactly one entry with a field
      path equal to the field path of the associated field.
    multikey: Optional. Whether the index is multikey. By default, the index
      is not multikey. For non-multikey indexes, none of the paths in the
      index definition reach or traverse an array, except via an explicit
      array index. For multikey indexes, at most one of the paths in the index
      definition reach or traverse an array, except via an explicit array
      index. Violations will result in errors. Note this field only applies to
      index with MONGODB_COMPATIBLE_API ApiScope.
    name: Output only. A server defined name for this index. The form of this
      name for composite indexes will be: `projects/{project_id}/databases/{da
      tabase_id}/collectionGroups/{collection_id}/indexes/{composite_index_id}
      ` For single field indexes, this field will be empty.
    queryScope: Indexes with a collection query scope specified allow queries
      against a collection that is the child of a specific document, specified
      at query time, and that has the same collection ID. Indexes with a
      collection group query scope specified allow queries against all
      collections descended from a specific document, specified at query time,
      and that have the same collection ID as this index.
    shardCount: Optional. The number of shards for the index.
    state: Output only. The serving state of the index.
  """

  class ApiScopeValueValuesEnum(_messages.Enum):
    r"""The API scope supported by this index.

    Values:
      ANY_API: The index can only be used by the Firestore Native query API.
        This is the default.
      DATASTORE_MODE_API: The index can only be used by the Firestore in
        Datastore Mode query API.
      MONGODB_COMPATIBLE_API: The index can only be used by the
        MONGODB_COMPATIBLE_API.
    """
    ANY_API = 0
    DATASTORE_MODE_API = 1
    MONGODB_COMPATIBLE_API = 2

  class DensityValueValuesEnum(_messages.Enum):
    r"""Immutable. The density configuration of the index.

    Values:
      DENSITY_UNSPECIFIED: Unspecified. It will use database default setting.
        This value is input only.
      SPARSE_ALL: In order for an index entry to be added, the document must
        contain all fields specified in the index. This is the only allowed
        value for indexes having ApiScope `ANY_API` and `DATASTORE_MODE_API`.
      SPARSE_ANY: In order for an index entry to be added, the document must
        contain at least one of the fields specified in the index. Non-
        existent fields are treated as having a NULL value when generating
        index entries.
      DENSE: An index entry will be added regardless of whether the document
        contains any of the fields specified in the index. Non-existent fields
        are treated as having a NULL value when generating index entries.
    """
    DENSITY_UNSPECIFIED = 0
    SPARSE_ALL = 1
    SPARSE_ANY = 2
    DENSE = 3

  class QueryScopeValueValuesEnum(_messages.Enum):
    r"""Indexes with a collection query scope specified allow queries against
    a collection that is the child of a specific document, specified at query
    time, and that has the same collection ID. Indexes with a collection group
    query scope specified allow queries against all collections descended from
    a specific document, specified at query time, and that have the same
    collection ID as this index.

    Values:
      QUERY_SCOPE_UNSPECIFIED: The query scope is unspecified. Not a valid
        option.
      COLLECTION: Indexes with a collection query scope specified allow
        queries against a collection that is the child of a specific document,
        specified at query time, and that has the collection ID specified by
        the index.
      COLLECTION_GROUP: Indexes with a collection group query scope specified
        allow queries against all collections that has the collection ID
        specified by the index.
      COLLECTION_RECURSIVE: Include all the collections's ancestor in the
        index. Only available for Datastore Mode databases.
    """
    QUERY_SCOPE_UNSPECIFIED = 0
    COLLECTION = 1
    COLLECTION_GROUP = 2
    COLLECTION_RECURSIVE = 3

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The serving state of the index.

    Values:
      STATE_UNSPECIFIED: The state is unspecified.
      CREATING: The index is being created. There is an active long-running
        operation for the index. The index is updated when writing a document.
        Some index data may exist.
      READY: The index is ready to be used. The index is updated when writing
        a document. The index is fully populated from all stored documents it
        applies to.
      NEEDS_REPAIR: The index was being created, but something went wrong.
        There is no active long-running operation for the index, and the most
        recently finished long-running operation failed. The index is not
        updated when writing a document. Some index data may exist. Use the
        google.longrunning.Operations API to determine why the operation that
        last attempted to create this index failed, then re-create the index.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    NEEDS_REPAIR = 3

  apiScope = _messages.EnumField('ApiScopeValueValuesEnum', 1)
  density = _messages.EnumField('DensityValueValuesEnum', 2)
  fields = _messages.MessageField('GoogleFirestoreAdminV1IndexField', 3, repeated=True)
  multikey = _messages.BooleanField(4)
  name = _messages.StringField(5)
  queryScope = _messages.EnumField('QueryScopeValueValuesEnum', 6)
  shardCount = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  state = _messages.EnumField('StateValueValuesEnum', 8)


class GoogleFirestoreAdminV1IndexConfig(_messages.Message):
  r"""The index configuration for this field.

  Fields:
    ancestorField: Output only. Specifies the resource name of the `Field`
      from which this field's index configuration is set (when
      `uses_ancestor_config` is true), or from which it *would* be set if this
      field had no index configuration (when `uses_ancestor_config` is false).
    indexes: The indexes supported for this field.
    reverting: Output only When true, the `Field`'s index configuration is in
      the process of being reverted. Once complete, the index config will
      transition to the same state as the field specified by `ancestor_field`,
      at which point `uses_ancestor_config` will be `true` and `reverting`
      will be `false`.
    usesAncestorConfig: Output only. When true, the `Field`'s index
      configuration is set from the configuration specified by the
      `ancestor_field`. When false, the `Field`'s index configuration is
      defined explicitly.
  """

  ancestorField = _messages.StringField(1)
  indexes = _messages.MessageField('GoogleFirestoreAdminV1Index', 2, repeated=True)
  reverting = _messages.BooleanField(3)
  usesAncestorConfig = _messages.BooleanField(4)


class GoogleFirestoreAdminV1IndexConfigDelta(_messages.Message):
  r"""Information about an index configuration change.

  Enums:
    ChangeTypeValueValuesEnum: Specifies how the index is changing.

  Fields:
    changeType: Specifies how the index is changing.
    index: The index being changed.
  """

  class ChangeTypeValueValuesEnum(_messages.Enum):
    r"""Specifies how the index is changing.

    Values:
      CHANGE_TYPE_UNSPECIFIED: The type of change is not specified or known.
      ADD: The single field index is being added.
      REMOVE: The single field index is being removed.
    """
    CHANGE_TYPE_UNSPECIFIED = 0
    ADD = 1
    REMOVE = 2

  changeType = _messages.EnumField('ChangeTypeValueValuesEnum', 1)
  index = _messages.MessageField('GoogleFirestoreAdminV1Index', 2)


class GoogleFirestoreAdminV1IndexField(_messages.Message):
  r"""A field in an index. The field_path describes which field is indexed,
  the value_mode describes how the field value is indexed.

  Enums:
    ArrayConfigValueValuesEnum: Indicates that this field supports operations
      on `array_value`s.
    OrderValueValuesEnum: Indicates that this field supports ordering by the
      specified order or comparing using =, !=, <, <=, >, >=.

  Fields:
    arrayConfig: Indicates that this field supports operations on
      `array_value`s.
    fieldPath: Can be __name__. For single field indexes, this must match the
      name of the field or may be omitted.
    order: Indicates that this field supports ordering by the specified order
      or comparing using =, !=, <, <=, >, >=.
    vectorConfig: Indicates that this field supports nearest neighbor and
      distance operations on vector.
  """

  class ArrayConfigValueValuesEnum(_messages.Enum):
    r"""Indicates that this field supports operations on `array_value`s.

    Values:
      ARRAY_CONFIG_UNSPECIFIED: The index does not support additional array
        queries.
      CONTAINS: The index supports array containment queries.
    """
    ARRAY_CONFIG_UNSPECIFIED = 0
    CONTAINS = 1

  class OrderValueValuesEnum(_messages.Enum):
    r"""Indicates that this field supports ordering by the specified order or
    comparing using =, !=, <, <=, >, >=.

    Values:
      ORDER_UNSPECIFIED: The ordering is unspecified. Not a valid option.
      ASCENDING: The field is ordered by ascending field value.
      DESCENDING: The field is ordered by descending field value.
    """
    ORDER_UNSPECIFIED = 0
    ASCENDING = 1
    DESCENDING = 2

  arrayConfig = _messages.EnumField('ArrayConfigValueValuesEnum', 1)
  fieldPath = _messages.StringField(2)
  order = _messages.EnumField('OrderValueValuesEnum', 3)
  vectorConfig = _messages.MessageField('GoogleFirestoreAdminV1VectorConfig', 4)


class GoogleFirestoreAdminV1IndexOperationMetadata(_messages.Message):
  r"""Metadata for google.longrunning.Operation results from
  FirestoreAdmin.CreateIndex.

  Enums:
    StateValueValuesEnum: The state of the operation.

  Fields:
    endTime: The time this operation completed. Will be unset if operation
      still in progress.
    index: The index resource that this operation is acting on. For example: `
      projects/{project_id}/databases/{database_id}/collectionGroups/{collecti
      on_id}/indexes/{index_id}`
    progressBytes: The progress, in bytes, of this operation.
    progressDocuments: The progress, in documents, of this operation.
    startTime: The time this operation started.
    state: The state of the operation.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of the operation.

    Values:
      OPERATION_STATE_UNSPECIFIED: Unspecified.
      INITIALIZING: Request is being prepared for processing.
      PROCESSING: Request is actively being processed.
      CANCELLING: Request is in the process of being cancelled after user
        called google.longrunning.Operations.CancelOperation on the operation.
      FINALIZING: Request has been processed and is in its finalization stage.
      SUCCESSFUL: Request has completed successfully.
      FAILED: Request has finished being processed, but encountered an error.
      CANCELLED: Request has finished being cancelled after user called
        google.longrunning.Operations.CancelOperation.
    """
    OPERATION_STATE_UNSPECIFIED = 0
    INITIALIZING = 1
    PROCESSING = 2
    CANCELLING = 3
    FINALIZING = 4
    SUCCESSFUL = 5
    FAILED = 6
    CANCELLED = 7

  endTime = _messages.StringField(1)
  index = _messages.StringField(2)
  progressBytes = _messages.MessageField('GoogleFirestoreAdminV1Progress', 3)
  progressDocuments = _messages.MessageField('GoogleFirestoreAdminV1Progress', 4)
  startTime = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)


class GoogleFirestoreAdminV1ListBackupSchedulesResponse(_messages.Message):
  r"""The response for FirestoreAdmin.ListBackupSchedules.

  Fields:
    backupSchedules: List of all backup schedules.
  """

  backupSchedules = _messages.MessageField('GoogleFirestoreAdminV1BackupSchedule', 1, repeated=True)


class GoogleFirestoreAdminV1ListBackupsResponse(_messages.Message):
  r"""The response for FirestoreAdmin.ListBackups.

  Fields:
    backups: List of all backups for the project.
    unreachable: List of locations that existing backups were not able to be
      fetched from. Instead of failing the entire requests when a single
      location is unreachable, this response returns a partial result set and
      list of locations unable to be reached here. The request can be retried
      against a single location to get a concrete error.
  """

  backups = _messages.MessageField('GoogleFirestoreAdminV1Backup', 1, repeated=True)
  unreachable = _messages.StringField(2, repeated=True)


class GoogleFirestoreAdminV1ListDatabasesResponse(_messages.Message):
  r"""The list of databases for a project.

  Fields:
    databases: The databases in the project.
    unreachable: In the event that data about individual databases cannot be
      listed they will be recorded here. An example entry might be:
      projects/some_project/locations/some_location This can happen if the
      Cloud Region that the Database resides in is currently unavailable. In
      this case we can't fetch all the details about the database. You may be
      able to get a more detailed error message (or possibly fetch the
      resource) by sending a 'Get' request for the resource or a 'List'
      request for the specific location.
  """

  databases = _messages.MessageField('GoogleFirestoreAdminV1Database', 1, repeated=True)
  unreachable = _messages.StringField(2, repeated=True)


class GoogleFirestoreAdminV1ListFieldsResponse(_messages.Message):
  r"""The response for FirestoreAdmin.ListFields.

  Fields:
    fields: The requested fields.
    nextPageToken: A page token that may be used to request another page of
      results. If blank, this is the last page.
  """

  fields = _messages.MessageField('GoogleFirestoreAdminV1Field', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleFirestoreAdminV1ListIndexesResponse(_messages.Message):
  r"""The response for FirestoreAdmin.ListIndexes.

  Fields:
    indexes: The requested indexes.
    nextPageToken: A page token that may be used to request another page of
      results. If blank, this is the last page.
  """

  indexes = _messages.MessageField('GoogleFirestoreAdminV1Index', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleFirestoreAdminV1ListUserCredsResponse(_messages.Message):
  r"""The response for FirestoreAdmin.ListUserCreds.

  Fields:
    userCreds: The user creds for the database.
  """

  userCreds = _messages.MessageField('GoogleFirestoreAdminV1UserCreds', 1, repeated=True)


class GoogleFirestoreAdminV1LocationMetadata(_messages.Message):
  r"""The metadata message for google.cloud.location.Location.metadata."""


class GoogleFirestoreAdminV1PitrSnapshot(_messages.Message):
  r"""A consistent snapshot of a database at a specific point in time. A PITR
  (Point-in-time recovery) snapshot with previous versions of a database's
  data is available for every minute up to the associated database's data
  retention period. If the PITR feature is enabled, the retention period is 7
  days; otherwise, it is one hour.

  Fields:
    database: Required. The name of the database that this was a snapshot of.
      Format: `projects/{project}/databases/{database}`.
    databaseUid: Output only. Public UUID of the database the snapshot was
      associated with.
    snapshotTime: Required. Snapshot time of the database.
  """

  database = _messages.StringField(1)
  databaseUid = _messages.BytesField(2)
  snapshotTime = _messages.StringField(3)


class GoogleFirestoreAdminV1Progress(_messages.Message):
  r"""Describes the progress of the operation. Unit of work is generic and
  must be interpreted based on where Progress is used.

  Fields:
    completedWork: The amount of work completed.
    estimatedWork: The amount of work estimated.
  """

  completedWork = _messages.IntegerField(1)
  estimatedWork = _messages.IntegerField(2)


class GoogleFirestoreAdminV1ResetUserPasswordRequest(_messages.Message):
  r"""The request for FirestoreAdmin.ResetUserPassword."""


class GoogleFirestoreAdminV1ResourceIdentity(_messages.Message):
  r"""Describes a Resource Identity principal.

  Fields:
    principal: Output only. Principal identifier string. See:
      https://cloud.google.com/iam/docs/principal-identifiers
  """

  principal = _messages.StringField(1)


class GoogleFirestoreAdminV1RestoreDatabaseMetadata(_messages.Message):
  r"""Metadata for the long-running operation from the RestoreDatabase
  request.

  Enums:
    OperationStateValueValuesEnum: The operation state of the restore.

  Fields:
    backup: The name of the backup restoring from.
    database: The name of the database being restored to.
    endTime: The time the restore finished, unset for ongoing restores.
    operationState: The operation state of the restore.
    progressPercentage: How far along the restore is as an estimated
      percentage of remaining time.
    startTime: The time the restore was started.
  """

  class OperationStateValueValuesEnum(_messages.Enum):
    r"""The operation state of the restore.

    Values:
      OPERATION_STATE_UNSPECIFIED: Unspecified.
      INITIALIZING: Request is being prepared for processing.
      PROCESSING: Request is actively being processed.
      CANCELLING: Request is in the process of being cancelled after user
        called google.longrunning.Operations.CancelOperation on the operation.
      FINALIZING: Request has been processed and is in its finalization stage.
      SUCCESSFUL: Request has completed successfully.
      FAILED: Request has finished being processed, but encountered an error.
      CANCELLED: Request has finished being cancelled after user called
        google.longrunning.Operations.CancelOperation.
    """
    OPERATION_STATE_UNSPECIFIED = 0
    INITIALIZING = 1
    PROCESSING = 2
    CANCELLING = 3
    FINALIZING = 4
    SUCCESSFUL = 5
    FAILED = 6
    CANCELLED = 7

  backup = _messages.StringField(1)
  database = _messages.StringField(2)
  endTime = _messages.StringField(3)
  operationState = _messages.EnumField('OperationStateValueValuesEnum', 4)
  progressPercentage = _messages.MessageField('GoogleFirestoreAdminV1Progress', 5)
  startTime = _messages.StringField(6)


class GoogleFirestoreAdminV1RestoreDatabaseRequest(_messages.Message):
  r"""The request message for FirestoreAdmin.RestoreDatabase.

  Messages:
    TagsValue: Optional. Immutable. Tags to be bound to the restored database.
      The tags should be provided in the format of `tagKeys/{tag_key_id} ->
      tagValues/{tag_value_id}`.

  Fields:
    backup: Required. Backup to restore from. Must be from the same project as
      the parent. The restored database will be created in the same location
      as the source backup. Format is:
      `projects/{project_id}/locations/{location}/backups/{backup}`
    databaseId: Required. The ID to use for the database, which will become
      the final component of the database's resource name. This database ID
      must not be associated with an existing database. This value should be
      4-63 characters. Valid characters are /a-z-/ with first character a
      letter and the last a letter or a number. Must not be UUID-like
      /[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}/. "(default)" database ID is
      also valid.
    encryptionConfig: Optional. Encryption configuration for the restored
      database. If this field is not specified, the restored database will use
      the same encryption configuration as the backup, namely
      use_source_encryption.
    tags: Optional. Immutable. Tags to be bound to the restored database. The
      tags should be provided in the format of `tagKeys/{tag_key_id} ->
      tagValues/{tag_value_id}`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""Optional. Immutable. Tags to be bound to the restored database. The
    tags should be provided in the format of `tagKeys/{tag_key_id} ->
    tagValues/{tag_value_id}`.

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  backup = _messages.StringField(1)
  databaseId = _messages.StringField(2)
  encryptionConfig = _messages.MessageField('GoogleFirestoreAdminV1EncryptionConfig', 3)
  tags = _messages.MessageField('TagsValue', 4)


class GoogleFirestoreAdminV1SourceEncryptionOptions(_messages.Message):
  r"""The configuration options for using the same encryption method as the
  source.
  """



class GoogleFirestoreAdminV1SourceInfo(_messages.Message):
  r"""Information about the provenance of this database.

  Fields:
    backup: If set, this database was restored from the specified backup (or a
      snapshot thereof).
    operation: The associated long-running operation. This field may not be
      set after the operation has completed. Format:
      `projects/{project}/databases/{database}/operations/{operation}`.
  """

  backup = _messages.MessageField('GoogleFirestoreAdminV1BackupSource', 1)
  operation = _messages.StringField(2)


class GoogleFirestoreAdminV1Stats(_messages.Message):
  r"""Backup specific statistics.

  Fields:
    documentCount: Output only. The total number of documents contained in the
      backup.
    indexCount: Output only. The total number of index entries contained in
      the backup.
    sizeBytes: Output only. Summation of the size of all documents and index
      entries in the backup, measured in bytes.
  """

  documentCount = _messages.IntegerField(1)
  indexCount = _messages.IntegerField(2)
  sizeBytes = _messages.IntegerField(3)


class GoogleFirestoreAdminV1TtlConfig(_messages.Message):
  r"""The TTL (time-to-live) configuration for documents that have this
  `Field` set. Storing a timestamp value into a TTL-enabled field will be
  treated as the document's absolute expiration time. Timestamp values in the
  past indicate that the document is eligible for immediate expiration. Using
  any other data type or leaving the field absent will disable expiration for
  the individual document.

  Enums:
    StateValueValuesEnum: Output only. The state of the TTL configuration.

  Fields:
    state: Output only. The state of the TTL configuration.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the TTL configuration.

    Values:
      STATE_UNSPECIFIED: The state is unspecified or unknown.
      CREATING: The TTL is being applied. There is an active long-running
        operation to track the change. Newly written documents will have TTLs
        applied as requested. Requested TTLs on existing documents are still
        being processed. When TTLs on all existing documents have been
        processed, the state will move to 'ACTIVE'.
      ACTIVE: The TTL is active for all documents.
      NEEDS_REPAIR: The TTL configuration could not be enabled for all
        existing documents. Newly written documents will continue to have
        their TTL applied. The LRO returned when last attempting to enable TTL
        for this `Field` has failed, and may have more details.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    NEEDS_REPAIR = 3

  state = _messages.EnumField('StateValueValuesEnum', 1)


class GoogleFirestoreAdminV1TtlConfigDelta(_messages.Message):
  r"""Information about a TTL configuration change.

  Enums:
    ChangeTypeValueValuesEnum: Specifies how the TTL configuration is
      changing.

  Fields:
    changeType: Specifies how the TTL configuration is changing.
  """

  class ChangeTypeValueValuesEnum(_messages.Enum):
    r"""Specifies how the TTL configuration is changing.

    Values:
      CHANGE_TYPE_UNSPECIFIED: The type of change is not specified or known.
      ADD: The TTL config is being added.
      REMOVE: The TTL config is being removed.
    """
    CHANGE_TYPE_UNSPECIFIED = 0
    ADD = 1
    REMOVE = 2

  changeType = _messages.EnumField('ChangeTypeValueValuesEnum', 1)


class GoogleFirestoreAdminV1UpdateDatabaseMetadata(_messages.Message):
  r"""Metadata related to the update database operation."""


class GoogleFirestoreAdminV1UserCreds(_messages.Message):
  r"""A Cloud Firestore User Creds.

  Enums:
    StateValueValuesEnum: Output only. Whether the user creds are enabled or
      disabled. Defaults to ENABLED on creation.

  Fields:
    createTime: Output only. The time the user creds were created.
    name: Identifier. The resource name of the UserCreds. Format:
      `projects/{project}/databases/{database}/userCreds/{user_creds}`
    resourceIdentity: Resource Identity descriptor.
    securePassword: Output only. The plaintext server-generated password for
      the user creds. Only populated in responses for CreateUserCreds and
      ResetUserPassword.
    state: Output only. Whether the user creds are enabled or disabled.
      Defaults to ENABLED on creation.
    updateTime: Output only. The time the user creds were last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Whether the user creds are enabled or disabled. Defaults
    to ENABLED on creation.

    Values:
      STATE_UNSPECIFIED: The default value. Should not be used.
      ENABLED: The user creds are enabled.
      DISABLED: The user creds are disabled.
    """
    STATE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  createTime = _messages.StringField(1)
  name = _messages.StringField(2)
  resourceIdentity = _messages.MessageField('GoogleFirestoreAdminV1ResourceIdentity', 3)
  securePassword = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)
  updateTime = _messages.StringField(6)


class GoogleFirestoreAdminV1VectorConfig(_messages.Message):
  r"""The index configuration to support vector search operations

  Fields:
    dimension: Required. The vector dimension this configuration applies to.
      The resulting index will only include vectors of this dimension, and can
      be used for vector search with the same dimension.
    flat: Indicates the vector index is a flat index.
  """

  dimension = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  flat = _messages.MessageField('GoogleFirestoreAdminV1FlatIndex', 2)


class GoogleFirestoreAdminV1WeeklyRecurrence(_messages.Message):
  r"""Represents a recurring schedule that runs on a specified day of the
  week. The time zone is UTC.

  Enums:
    DayValueValuesEnum: The day of week to run. DAY_OF_WEEK_UNSPECIFIED is not
      allowed.

  Fields:
    day: The day of week to run. DAY_OF_WEEK_UNSPECIFIED is not allowed.
  """

  class DayValueValuesEnum(_messages.Enum):
    r"""The day of week to run. DAY_OF_WEEK_UNSPECIFIED is not allowed.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  day = _messages.EnumField('DayValueValuesEnum', 1)


class GoogleLongrunningCancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class GoogleLongrunningListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('GoogleLongrunningOperation', 2, repeated=True)


class GoogleLongrunningOperation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class LatLng(_messages.Message):
  r"""An object that represents a latitude/longitude pair. This is expressed
  as a pair of doubles to represent degrees latitude and degrees longitude.
  Unless specified otherwise, this object must conform to the WGS84 standard.
  Values must be within normalized ranges.

  Fields:
    latitude: The latitude in degrees. It must be in the range [-90.0, +90.0].
    longitude: The longitude in degrees. It must be in the range [-180.0,
      +180.0].
  """

  latitude = _messages.FloatField(1)
  longitude = _messages.FloatField(2)


class ListCollectionIdsRequest(_messages.Message):
  r"""The request for Firestore.ListCollectionIds.

  Fields:
    pageSize: The maximum number of results to return.
    pageToken: A page token. Must be a value from ListCollectionIdsResponse.
    readTime: Reads documents as they were at the given time. This must be a
      microsecond precision timestamp within the past one hour, or if Point-
      in-Time Recovery is enabled, can additionally be a whole minute
      timestamp within the past 7 days.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  readTime = _messages.StringField(3)


class ListCollectionIdsResponse(_messages.Message):
  r"""The response from Firestore.ListCollectionIds.

  Fields:
    collectionIds: The collection ids.
    nextPageToken: A page token that may be used to continue the list.
  """

  collectionIds = _messages.StringField(1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListDocumentsResponse(_messages.Message):
  r"""The response for Firestore.ListDocuments.

  Fields:
    documents: The Documents found.
    nextPageToken: A token to retrieve the next page of documents. If this
      field is omitted, there are no subsequent pages.
  """

  documents = _messages.MessageField('Document', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListenRequest(_messages.Message):
  r"""A request for Firestore.Listen

  Messages:
    LabelsValue: Labels associated with this target change.

  Fields:
    addTarget: A target to add to this stream.
    labels: Labels associated with this target change.
    removeTarget: The ID of a target to remove from this stream.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels associated with this target change.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  addTarget = _messages.MessageField('Target', 1)
  labels = _messages.MessageField('LabelsValue', 2)
  removeTarget = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class ListenResponse(_messages.Message):
  r"""The response for Firestore.Listen.

  Fields:
    documentChange: A Document has changed.
    documentDelete: A Document has been deleted.
    documentRemove: A Document has been removed from a target (because it is
      no longer relevant to that target).
    filter: A filter to apply to the set of documents previously returned for
      the given target. Returned when documents may have been removed from the
      given target, but the exact documents are unknown.
    targetChange: Targets have changed.
  """

  documentChange = _messages.MessageField('DocumentChange', 1)
  documentDelete = _messages.MessageField('DocumentDelete', 2)
  documentRemove = _messages.MessageField('DocumentRemove', 3)
  filter = _messages.MessageField('ExistenceFilter', 4)
  targetChange = _messages.MessageField('TargetChange', 5)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class MapValue(_messages.Message):
  r"""A map value.

  Messages:
    FieldsValue: The map's fields. The map keys represent field names. Field
      names matching the regular expression `__.*__` are reserved. Reserved
      field names are forbidden except in certain documented contexts. The map
      keys, represented as UTF-8, must not exceed 1,500 bytes and cannot be
      empty.

  Fields:
    fields: The map's fields. The map keys represent field names. Field names
      matching the regular expression `__.*__` are reserved. Reserved field
      names are forbidden except in certain documented contexts. The map keys,
      represented as UTF-8, must not exceed 1,500 bytes and cannot be empty.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FieldsValue(_messages.Message):
    r"""The map's fields. The map keys represent field names. Field names
    matching the regular expression `__.*__` are reserved. Reserved field
    names are forbidden except in certain documented contexts. The map keys,
    represented as UTF-8, must not exceed 1,500 bytes and cannot be empty.

    Messages:
      AdditionalProperty: An additional property for a FieldsValue object.

    Fields:
      additionalProperties: Additional properties of type FieldsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FieldsValue object.

      Fields:
        key: Name of the additional property.
        value: A Value attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Value', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  fields = _messages.MessageField('FieldsValue', 1)


class Order(_messages.Message):
  r"""An order on a field.

  Enums:
    DirectionValueValuesEnum: The direction to order by. Defaults to
      `ASCENDING`.

  Fields:
    direction: The direction to order by. Defaults to `ASCENDING`.
    field: The field to order by.
  """

  class DirectionValueValuesEnum(_messages.Enum):
    r"""The direction to order by. Defaults to `ASCENDING`.

    Values:
      DIRECTION_UNSPECIFIED: Unspecified.
      ASCENDING: Ascending.
      DESCENDING: Descending.
    """
    DIRECTION_UNSPECIFIED = 0
    ASCENDING = 1
    DESCENDING = 2

  direction = _messages.EnumField('DirectionValueValuesEnum', 1)
  field = _messages.MessageField('FieldReference', 2)


class PartitionQueryRequest(_messages.Message):
  r"""The request for Firestore.PartitionQuery.

  Fields:
    pageSize: The maximum number of partitions to return in this call, subject
      to `partition_count`. For example, if `partition_count` = 10 and
      `page_size` = 8, the first call to PartitionQuery will return up to 8
      partitions and a `next_page_token` if more results exist. A second call
      to PartitionQuery will return up to 2 partitions, to complete the total
      of 10 specified in `partition_count`.
    pageToken: The `next_page_token` value returned from a previous call to
      PartitionQuery that may be used to get an additional set of results.
      There are no ordering guarantees between sets of results. Thus, using
      multiple sets of results will require merging the different result sets.
      For example, two subsequent calls using a page_token may return: *
      cursor B, cursor M, cursor Q * cursor A, cursor U, cursor W To obtain a
      complete result set ordered with respect to the results of the query
      supplied to PartitionQuery, the results sets should be merged: cursor A,
      cursor B, cursor M, cursor Q, cursor U, cursor W
    partitionCount: The desired maximum number of partition points. The
      partitions may be returned across multiple pages of results. The number
      must be positive. The actual number of partitions returned may be fewer.
      For example, this may be set to one fewer than the number of parallel
      queries to be run, or in running a data pipeline job, one fewer than the
      number of workers or compute instances available.
    readTime: Reads documents as they were at the given time. This must be a
      microsecond precision timestamp within the past one hour, or if Point-
      in-Time Recovery is enabled, can additionally be a whole minute
      timestamp within the past 7 days.
    structuredQuery: A structured query. Query must specify collection with
      all descendants and be ordered by name ascending. Other filters, order
      bys, limits, offsets, and start/end cursors are not supported.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  partitionCount = _messages.IntegerField(3)
  readTime = _messages.StringField(4)
  structuredQuery = _messages.MessageField('StructuredQuery', 5)


class PartitionQueryResponse(_messages.Message):
  r"""The response for Firestore.PartitionQuery.

  Fields:
    nextPageToken: A page token that may be used to request an additional set
      of results, up to the number specified by `partition_count` in the
      PartitionQuery request. If blank, there are no more results.
    partitions: Partition results. Each partition is a split point that can be
      used by RunQuery as a starting or end point for the query results. The
      RunQuery requests must be made with the same query supplied to this
      PartitionQuery request. The partition cursors will be ordered according
      to same ordering as the results of the query supplied to PartitionQuery.
      For example, if a PartitionQuery request returns partition cursors A and
      B, running the following three queries will return the entire result set
      of the original query: * query, end_at A * query, start_at A, end_at B *
      query, start_at B An empty result may indicate that the query has too
      few results to be partitioned, or that the query is not yet supported
      for partitioning.
  """

  nextPageToken = _messages.StringField(1)
  partitions = _messages.MessageField('Cursor', 2, repeated=True)


class PlanSummary(_messages.Message):
  r"""Planning phase information for the query.

  Messages:
    IndexesUsedValueListEntry: A IndexesUsedValueListEntry object.

  Fields:
    indexesUsed: The indexes selected for the query. For example: [
      {"query_scope": "Collection", "properties": "(foo ASC, __name__ ASC)"},
      {"query_scope": "Collection", "properties": "(bar ASC, __name__ ASC)"} ]
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class IndexesUsedValueListEntry(_messages.Message):
    r"""A IndexesUsedValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a
        IndexesUsedValueListEntry object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a IndexesUsedValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  indexesUsed = _messages.MessageField('IndexesUsedValueListEntry', 1, repeated=True)


class Precondition(_messages.Message):
  r"""A precondition on a document, used for conditional operations.

  Fields:
    exists: When set to `true`, the target document must exist. When set to
      `false`, the target document must not exist.
    updateTime: When set, the target document must exist and have been last
      updated at that time. Timestamp must be microsecond aligned.
  """

  exists = _messages.BooleanField(1)
  updateTime = _messages.StringField(2)


class Projection(_messages.Message):
  r"""The projection of document's fields to return.

  Fields:
    fields: The fields to return. If empty, all fields are returned. To only
      return the name of the document, use `['__name__']`.
  """

  fields = _messages.MessageField('FieldReference', 1, repeated=True)


class QueryTarget(_messages.Message):
  r"""A target specified by a query.

  Fields:
    parent: The parent resource name. In the format:
      `projects/{project_id}/databases/{database_id}/documents` or `projects/{
      project_id}/databases/{database_id}/documents/{document_path}`. For
      example: `projects/my-project/databases/my-database/documents` or
      `projects/my-project/databases/my-database/documents/chatrooms/my-
      chatroom`
    structuredQuery: A structured query.
  """

  parent = _messages.StringField(1)
  structuredQuery = _messages.MessageField('StructuredQuery', 2)


class ReadOnly(_messages.Message):
  r"""Options for a transaction that can only be used to read documents.

  Fields:
    readTime: Reads documents at the given time. This must be a microsecond
      precision timestamp within the past one hour, or if Point-in-Time
      Recovery is enabled, can additionally be a whole minute timestamp within
      the past 7 days.
  """

  readTime = _messages.StringField(1)


class ReadWrite(_messages.Message):
  r"""Options for a transaction that can be used to read and write documents.
  Firestore does not allow 3rd party auth requests to create read-write.
  transactions.

  Fields:
    retryTransaction: An optional transaction to retry.
  """

  retryTransaction = _messages.BytesField(1)


class RollbackRequest(_messages.Message):
  r"""The request for Firestore.Rollback.

  Fields:
    transaction: Required. The transaction to roll back.
  """

  transaction = _messages.BytesField(1)


class RunAggregationQueryRequest(_messages.Message):
  r"""The request for Firestore.RunAggregationQuery.

  Fields:
    explainOptions: Optional. Explain options for the query. If set,
      additional query statistics will be returned. If not, only query results
      will be returned.
    newTransaction: Starts a new transaction as part of the query, defaulting
      to read-only. The new transaction ID will be returned as the first
      response in the stream.
    readTime: Executes the query at the given timestamp. This must be a
      microsecond precision timestamp within the past one hour, or if Point-
      in-Time Recovery is enabled, can additionally be a whole minute
      timestamp within the past 7 days.
    structuredAggregationQuery: An aggregation query.
    transaction: Run the aggregation within an already active transaction. The
      value here is the opaque transaction ID to execute the query in.
  """

  explainOptions = _messages.MessageField('ExplainOptions', 1)
  newTransaction = _messages.MessageField('TransactionOptions', 2)
  readTime = _messages.StringField(3)
  structuredAggregationQuery = _messages.MessageField('StructuredAggregationQuery', 4)
  transaction = _messages.BytesField(5)


class RunAggregationQueryResponse(_messages.Message):
  r"""The response for Firestore.RunAggregationQuery.

  Fields:
    explainMetrics: Query explain metrics. This is only present when the
      RunAggregationQueryRequest.explain_options is provided, and it is sent
      only once with the last response in the stream.
    readTime: The time at which the aggregate result was computed. This is
      always monotonically increasing; in this case, the previous
      AggregationResult in the result stream are guaranteed not to have
      changed between their `read_time` and this one. If the query returns no
      results, a response with `read_time` and no `result` will be sent, and
      this represents the time at which the query was run.
    result: A single aggregation result. Not present when reporting partial
      progress.
    transaction: The transaction that was started as part of this request.
      Only present on the first response when the request requested to start a
      new transaction.
  """

  explainMetrics = _messages.MessageField('ExplainMetrics', 1)
  readTime = _messages.StringField(2)
  result = _messages.MessageField('AggregationResult', 3)
  transaction = _messages.BytesField(4)


class RunQueryRequest(_messages.Message):
  r"""The request for Firestore.RunQuery.

  Fields:
    explainOptions: Optional. Explain options for the query. If set,
      additional query statistics will be returned. If not, only query results
      will be returned.
    newTransaction: Starts a new transaction and reads the documents. Defaults
      to a read-only transaction. The new transaction ID will be returned as
      the first response in the stream.
    readTime: Reads documents as they were at the given time. This must be a
      microsecond precision timestamp within the past one hour, or if Point-
      in-Time Recovery is enabled, can additionally be a whole minute
      timestamp within the past 7 days.
    structuredQuery: A structured query.
    transaction: Run the query within an already active transaction. The value
      here is the opaque transaction ID to execute the query in.
  """

  explainOptions = _messages.MessageField('ExplainOptions', 1)
  newTransaction = _messages.MessageField('TransactionOptions', 2)
  readTime = _messages.StringField(3)
  structuredQuery = _messages.MessageField('StructuredQuery', 4)
  transaction = _messages.BytesField(5)


class RunQueryResponse(_messages.Message):
  r"""The response for Firestore.RunQuery.

  Fields:
    document: A query result, not set when reporting partial progress.
    done: If present, Firestore has completely finished the request and no
      more documents will be returned.
    explainMetrics: Query explain metrics. This is only present when the
      RunQueryRequest.explain_options is provided, and it is sent only once
      with the last response in the stream.
    readTime: The time at which the document was read. This may be
      monotonically increasing; in this case, the previous documents in the
      result stream are guaranteed not to have changed between their
      `read_time` and this one. If the query returns no results, a response
      with `read_time` and no `document` will be sent, and this represents the
      time at which the query was run.
    skippedResults: The number of results that have been skipped due to an
      offset between the last response and the current response.
    transaction: The transaction that was started as part of this request. Can
      only be set in the first response, and only if
      RunQueryRequest.new_transaction was set in the request. If set, no other
      fields will be set in this response.
  """

  document = _messages.MessageField('Document', 1)
  done = _messages.BooleanField(2)
  explainMetrics = _messages.MessageField('ExplainMetrics', 3)
  readTime = _messages.StringField(4)
  skippedResults = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  transaction = _messages.BytesField(6)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StructuredAggregationQuery(_messages.Message):
  r"""Firestore query for running an aggregation over a StructuredQuery.

  Fields:
    aggregations: Optional. Series of aggregations to apply over the results
      of the `structured_query`. Requires: * A minimum of one and maximum of
      five aggregations per query.
    structuredQuery: Nested structured query.
  """

  aggregations = _messages.MessageField('Aggregation', 1, repeated=True)
  structuredQuery = _messages.MessageField('StructuredQuery', 2)


class StructuredQuery(_messages.Message):
  r"""A Firestore query. The query stages are executed in the following order:
  1. from 2. where 3. select 4. order_by + start_at + end_at 5. offset 6.
  limit 7. find_nearest

  Fields:
    endAt: A potential prefix of a position in the result set to end the query
      at. This is similar to `START_AT` but with it controlling the end
      position rather than the start position. Requires: * The number of
      values cannot be greater than the number of fields specified in the
      `ORDER BY` clause.
    findNearest: Optional. A potential nearest neighbors search. Applies after
      all other filters and ordering. Finds the closest vector embeddings to
      the given query vector.
    from_: The collections to query.
    limit: The maximum number of results to return. Applies after all other
      constraints. Requires: * The value must be greater than or equal to zero
      if specified.
    offset: The number of documents to skip before returning the first result.
      This applies after the constraints specified by the `WHERE`, `START AT`,
      & `END AT` but before the `LIMIT` clause. Requires: * The value must be
      greater than or equal to zero if specified.
    orderBy: The order to apply to the query results. Firestore allows callers
      to provide a full ordering, a partial ordering, or no ordering at all.
      In all cases, Firestore guarantees a stable ordering through the
      following rules: * The `order_by` is required to reference all fields
      used with an inequality filter. * All fields that are required to be in
      the `order_by` but are not already present are appended in
      lexicographical ordering of the field name. * If an order on `__name__`
      is not specified, it is appended by default. Fields are appended with
      the same sort direction as the last order specified, or 'ASCENDING' if
      no order was specified. For example: * `ORDER BY a` becomes `ORDER BY a
      ASC, __name__ ASC` * `ORDER BY a DESC` becomes `ORDER BY a DESC,
      __name__ DESC` * `WHERE a > 1` becomes `WHERE a > 1 ORDER BY a ASC,
      __name__ ASC` * `WHERE __name__ > ... AND a > 1` becomes `WHERE __name__
      > ... AND a > 1 ORDER BY a ASC, __name__ ASC`
    select: Optional sub-set of the fields to return. This acts as a
      DocumentMask over the documents returned from a query. When not set,
      assumes that the caller wants all fields returned.
    startAt: A potential prefix of a position in the result set to start the
      query at. The ordering of the result set is based on the `ORDER BY`
      clause of the original query. ``` SELECT * FROM k WHERE a = 1 AND b > 2
      ORDER BY b ASC, __name__ ASC; ``` This query's results are ordered by
      `(b ASC, __name__ ASC)`. Cursors can reference either the full ordering
      or a prefix of the location, though it cannot reference more fields than
      what are in the provided `ORDER BY`. Continuing off the example above,
      attaching the following start cursors will have varying impact: - `START
      BEFORE (2, /k/123)`: start the query right before `a = 1 AND b > 2 AND
      __name__ > /k/123`. - `START AFTER (10)`: start the query right after `a
      = 1 AND b > 10`. Unlike `OFFSET` which requires scanning over the first
      N results to skip, a start cursor allows the query to begin at a logical
      position. This position is not required to match an actual result, it
      will scan forward from this position to find the next document.
      Requires: * The number of values cannot be greater than the number of
      fields specified in the `ORDER BY` clause.
    where: The filter to apply.
  """

  endAt = _messages.MessageField('Cursor', 1)
  findNearest = _messages.MessageField('FindNearest', 2)
  from_ = _messages.MessageField('CollectionSelector', 3, repeated=True)
  limit = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  offset = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  orderBy = _messages.MessageField('Order', 6, repeated=True)
  select = _messages.MessageField('Projection', 7)
  startAt = _messages.MessageField('Cursor', 8)
  where = _messages.MessageField('Filter', 9)


class Sum(_messages.Message):
  r"""Sum of the values of the requested field. * Only numeric values will be
  aggregated. All non-numeric values including `NULL` are skipped. * If the
  aggregated values contain `NaN`, returns `NaN`. Infinity math follows
  IEEE-754 standards. * If the aggregated value set is empty, returns 0. *
  Returns a 64-bit integer if all aggregated numbers are integers and the sum
  result does not overflow. Otherwise, the result is returned as a double.
  Note that even if all the aggregated values are integers, the result is
  returned as a double if it cannot fit within a 64-bit signed integer. When
  this occurs, the returned value will lose precision. * When underflow
  occurs, floating-point aggregation is non-deterministic. This means that
  running the same query repeatedly without any changes to the underlying
  values could produce slightly different results each time. In those cases,
  values should be stored as integers over floating-point numbers.

  Fields:
    field: The field to aggregate on.
  """

  field = _messages.MessageField('FieldReference', 1)


class Target(_messages.Message):
  r"""A specification of a set of documents to listen to.

  Fields:
    documents: A target specified by a set of document names.
    expectedCount: The number of documents that last matched the query at the
      resume token or read time. This value is only relevant when a
      `resume_type` is provided. This value being present and greater than
      zero signals that the client wants `ExistenceFilter.unchanged_names` to
      be included in the response.
    once: If the target should be removed once it is current and consistent.
    query: A target specified by a query.
    readTime: Start listening after a specific `read_time`. The client must
      know the state of matching documents at this time.
    resumeToken: A resume token from a prior TargetChange for an identical
      target. Using a resume token with a different target is unsupported and
      may fail.
    targetId: The target ID that identifies the target on the stream. Must be
      a positive number and non-zero. If `target_id` is 0 (or unspecified),
      the server will assign an ID for this target and return that in a
      `TargetChange::ADD` event. Once a target with `target_id=0` is added,
      all subsequent targets must also have `target_id=0`. If an `AddTarget`
      request with `target_id != 0` is sent to the server after a target with
      `target_id=0` is added, the server will immediately send a response with
      a `TargetChange::Remove` event. Note that if the client sends multiple
      `AddTarget` requests without an ID, the order of IDs returned in
      `TargetChange.target_ids` are undefined. Therefore, clients should
      provide a target ID instead of relying on the server to assign one. If
      `target_id` is non-zero, there must not be an existing active target on
      this stream with the same ID.
  """

  documents = _messages.MessageField('DocumentsTarget', 1)
  expectedCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  once = _messages.BooleanField(3)
  query = _messages.MessageField('QueryTarget', 4)
  readTime = _messages.StringField(5)
  resumeToken = _messages.BytesField(6)
  targetId = _messages.IntegerField(7, variant=_messages.Variant.INT32)


class TargetChange(_messages.Message):
  r"""Targets being watched have changed.

  Enums:
    TargetChangeTypeValueValuesEnum: The type of change that occurred.

  Fields:
    cause: The error that resulted in this change, if applicable.
    readTime: The consistent `read_time` for the given `target_ids` (omitted
      when the target_ids are not at a consistent snapshot). The stream is
      guaranteed to send a `read_time` with `target_ids` empty whenever the
      entire stream reaches a new consistent snapshot. ADD, CURRENT, and RESET
      messages are guaranteed to (eventually) result in a new consistent
      snapshot (while NO_CHANGE and REMOVE messages are not). For a given
      stream, `read_time` is guaranteed to be monotonically increasing.
    resumeToken: A token that can be used to resume the stream for the given
      `target_ids`, or all targets if `target_ids` is empty. Not set on every
      target change.
    targetChangeType: The type of change that occurred.
    targetIds: The target IDs of targets that have changed. If empty, the
      change applies to all targets. The order of the target IDs is not
      defined.
  """

  class TargetChangeTypeValueValuesEnum(_messages.Enum):
    r"""The type of change that occurred.

    Values:
      NO_CHANGE: No change has occurred. Used only to send an updated
        `resume_token`.
      ADD: The targets have been added.
      REMOVE: The targets have been removed.
      CURRENT: The targets reflect all changes committed before the targets
        were added to the stream. This will be sent after or with a
        `read_time` that is greater than or equal to the time at which the
        targets were added. Listeners can wait for this change if read-after-
        write semantics are desired.
      RESET: The targets have been reset, and a new initial state for the
        targets will be returned in subsequent changes. After the initial
        state is complete, `CURRENT` will be returned even if the target was
        previously indicated to be `CURRENT`.
    """
    NO_CHANGE = 0
    ADD = 1
    REMOVE = 2
    CURRENT = 3
    RESET = 4

  cause = _messages.MessageField('Status', 1)
  readTime = _messages.StringField(2)
  resumeToken = _messages.BytesField(3)
  targetChangeType = _messages.EnumField('TargetChangeTypeValueValuesEnum', 4)
  targetIds = _messages.IntegerField(5, repeated=True, variant=_messages.Variant.INT32)


class TransactionOptions(_messages.Message):
  r"""Options for creating a new transaction.

  Fields:
    readOnly: The transaction can only be used for read operations.
    readWrite: The transaction can be used for both read and write operations.
  """

  readOnly = _messages.MessageField('ReadOnly', 1)
  readWrite = _messages.MessageField('ReadWrite', 2)


class UnaryFilter(_messages.Message):
  r"""A filter with a single operand.

  Enums:
    OpValueValuesEnum: The unary operator to apply.

  Fields:
    field: The field to which to apply the operator.
    op: The unary operator to apply.
  """

  class OpValueValuesEnum(_messages.Enum):
    r"""The unary operator to apply.

    Values:
      OPERATOR_UNSPECIFIED: Unspecified. This value must not be used.
      IS_NAN: The given `field` is equal to `NaN`.
      IS_NULL: The given `field` is equal to `NULL`.
      IS_NOT_NAN: The given `field` is not equal to `NaN`. Requires: * No
        other `NOT_EQUAL`, `NOT_IN`, `IS_NOT_NULL`, or `IS_NOT_NAN`. * That
        `field` comes first in the `order_by`.
      IS_NOT_NULL: The given `field` is not equal to `NULL`. Requires: * A
        single `NOT_EQUAL`, `NOT_IN`, `IS_NOT_NULL`, or `IS_NOT_NAN`. * That
        `field` comes first in the `order_by`.
    """
    OPERATOR_UNSPECIFIED = 0
    IS_NAN = 1
    IS_NULL = 2
    IS_NOT_NAN = 3
    IS_NOT_NULL = 4

  field = _messages.MessageField('FieldReference', 1)
  op = _messages.EnumField('OpValueValuesEnum', 2)


class Value(_messages.Message):
  r"""A message that can hold any of the supported value types.

  Enums:
    NullValueValueValuesEnum: A null value.

  Fields:
    arrayValue: An array value. Cannot directly contain another array value,
      though can contain a map which contains another array.
    booleanValue: A boolean value.
    bytesValue: A bytes value. Must not exceed 1 MiB - 89 bytes. Only the
      first 1,500 bytes are considered by queries.
    doubleValue: A double value.
    geoPointValue: A geo point value representing a point on the surface of
      Earth.
    integerValue: An integer value.
    mapValue: A map value.
    nullValue: A null value.
    referenceValue: A reference to a document. For example:
      `projects/{project_id}/databases/{database_id}/documents/{document_path}
      `.
    stringValue: A string value. The string, represented as UTF-8, must not
      exceed 1 MiB - 89 bytes. Only the first 1,500 bytes of the UTF-8
      representation are considered by queries.
    timestampValue: A timestamp value. Precise only to microseconds. When
      stored, any additional precision is rounded down.
  """

  class NullValueValueValuesEnum(_messages.Enum):
    r"""A null value.

    Values:
      NULL_VALUE: Null value.
    """
    NULL_VALUE = 0

  arrayValue = _messages.MessageField('ArrayValue', 1)
  booleanValue = _messages.BooleanField(2)
  bytesValue = _messages.BytesField(3)
  doubleValue = _messages.FloatField(4)
  geoPointValue = _messages.MessageField('LatLng', 5)
  integerValue = _messages.IntegerField(6)
  mapValue = _messages.MessageField('MapValue', 7)
  nullValue = _messages.EnumField('NullValueValueValuesEnum', 8)
  referenceValue = _messages.StringField(9)
  stringValue = _messages.StringField(10)
  timestampValue = _messages.StringField(11)


class Write(_messages.Message):
  r"""A write on a document.

  Fields:
    currentDocument: An optional precondition on the document. The write will
      fail if this is set and not met by the target document.
    delete: A document name to delete. In the format:
      `projects/{project_id}/databases/{database_id}/documents/{document_path}
      `.
    transform: Applies a transformation to a document.
    update: A document to write.
    updateMask: The fields to update in this write. This field can be set only
      when the operation is `update`. If the mask is not set for an `update`
      and the document exists, any existing data will be overwritten. If the
      mask is set and the document on the server has fields not covered by the
      mask, they are left unchanged. Fields referenced in the mask, but not
      present in the input document, are deleted from the document on the
      server. The field paths in this mask must not contain a reserved field
      name.
    updateTransforms: The transforms to perform after update. This field can
      be set only when the operation is `update`. If present, this write is
      equivalent to performing `update` and `transform` to the same document
      atomically and in order.
  """

  currentDocument = _messages.MessageField('Precondition', 1)
  delete = _messages.StringField(2)
  transform = _messages.MessageField('DocumentTransform', 3)
  update = _messages.MessageField('Document', 4)
  updateMask = _messages.MessageField('DocumentMask', 5)
  updateTransforms = _messages.MessageField('FieldTransform', 6, repeated=True)


class WriteRequest(_messages.Message):
  r"""The request for Firestore.Write. The first request creates a stream, or
  resumes an existing one from a token. When creating a new stream, the server
  replies with a response containing only an ID and a token, to use in the
  next request. When resuming a stream, the server first streams any responses
  later than the given token, then a response containing only an up-to-date
  token, to use in the next request.

  Messages:
    LabelsValue: Labels associated with this write request.

  Fields:
    labels: Labels associated with this write request.
    streamId: The ID of the write stream to resume. This may only be set in
      the first message. When left empty, a new write stream will be created.
    streamToken: A stream token that was previously sent by the server. The
      client should set this field to the token from the most recent
      WriteResponse it has received. This acknowledges that the client has
      received responses up to this token. After sending this token, earlier
      tokens may not be used anymore. The server may close the stream if there
      are too many unacknowledged responses. Leave this field unset when
      creating a new stream. To resume a stream at a specific point, set this
      field and the `stream_id` field. Leave this field unset when creating a
      new stream.
    writes: The writes to apply. Always executed atomically and in order. This
      must be empty on the first request. This may be empty on the last
      request. This must not be empty on all other requests.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels associated with this write request.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)
  streamId = _messages.StringField(2)
  streamToken = _messages.BytesField(3)
  writes = _messages.MessageField('Write', 4, repeated=True)


class WriteResponse(_messages.Message):
  r"""The response for Firestore.Write.

  Fields:
    commitTime: The time at which the commit occurred. Any read with an equal
      or greater `read_time` is guaranteed to see the effects of the write.
    streamId: The ID of the stream. Only set on the first message, when a new
      stream was created.
    streamToken: A token that represents the position of this response in the
      stream. This can be used by a client to resume the stream at this point.
      This field is always set.
    writeResults: The result of applying the writes. This i-th write result
      corresponds to the i-th write in the request.
  """

  commitTime = _messages.StringField(1)
  streamId = _messages.StringField(2)
  streamToken = _messages.BytesField(3)
  writeResults = _messages.MessageField('WriteResult', 4, repeated=True)


class WriteResult(_messages.Message):
  r"""The result of applying a write.

  Fields:
    transformResults: The results of applying each
      DocumentTransform.FieldTransform, in the same order.
    updateTime: The last update time of the document after applying the write.
      Not set after a `delete`. If the write did not actually change the
      document, this will be the previous update_time.
  """

  transformResults = _messages.MessageField('Value', 1, repeated=True)
  updateTime = _messages.StringField(2)


encoding.AddCustomJsonFieldMapping(
    StructuredQuery, 'from_', 'from')
encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    FirestoreProjectsDatabasesDocumentsCreateDocumentRequest, 'mask_fieldPaths', 'mask.fieldPaths')
encoding.AddCustomJsonFieldMapping(
    FirestoreProjectsDatabasesDocumentsDeleteRequest, 'currentDocument_exists', 'currentDocument.exists')
encoding.AddCustomJsonFieldMapping(
    FirestoreProjectsDatabasesDocumentsDeleteRequest, 'currentDocument_updateTime', 'currentDocument.updateTime')
encoding.AddCustomJsonFieldMapping(
    FirestoreProjectsDatabasesDocumentsGetRequest, 'mask_fieldPaths', 'mask.fieldPaths')
encoding.AddCustomJsonFieldMapping(
    FirestoreProjectsDatabasesDocumentsListRequest, 'mask_fieldPaths', 'mask.fieldPaths')
encoding.AddCustomJsonFieldMapping(
    FirestoreProjectsDatabasesDocumentsListDocumentsRequest, 'mask_fieldPaths', 'mask.fieldPaths')
encoding.AddCustomJsonFieldMapping(
    FirestoreProjectsDatabasesDocumentsPatchRequest, 'currentDocument_exists', 'currentDocument.exists')
encoding.AddCustomJsonFieldMapping(
    FirestoreProjectsDatabasesDocumentsPatchRequest, 'currentDocument_updateTime', 'currentDocument.updateTime')
encoding.AddCustomJsonFieldMapping(
    FirestoreProjectsDatabasesDocumentsPatchRequest, 'mask_fieldPaths', 'mask.fieldPaths')
encoding.AddCustomJsonFieldMapping(
    FirestoreProjectsDatabasesDocumentsPatchRequest, 'updateMask_fieldPaths', 'updateMask.fieldPaths')
