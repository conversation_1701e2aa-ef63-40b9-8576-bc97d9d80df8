"""Generated message classes for looker version v1alpha1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'looker'


class AdminSettings(_messages.Message):
  r"""Looker instance Admin settings fields.

  Fields:
    allowedEmailDomains: Email domain allowlist for the instance.
  """

  allowedEmailDomains = _messages.StringField(1, repeated=True)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CustomDomain(_messages.Message):
  r"""Custom domain information.

  Enums:
    StateValueValuesEnum: Domain state.

  Fields:
    domain: Domain name.
    state: Domain state.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Domain state.

    Values:
      CUSTOM_DOMAIN_STATE_UNSPECIFIED: Unspecified state.
      UNVERIFIED: DNS record is not created.
      VERIFIED: DNS record is created.
      MODIFYING: Calling SLM to update.
      AVAILABLE: ManagedCertificate is ready.
      UNAVAILABLE: ManagedCertificate is not ready.
      UNKNOWN: Status is not known.
    """
    CUSTOM_DOMAIN_STATE_UNSPECIFIED = 0
    UNVERIFIED = 1
    VERIFIED = 2
    MODIFYING = 3
    AVAILABLE = 4
    UNAVAILABLE = 5
    UNKNOWN = 6

  domain = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class Date(_messages.Message):
  r"""Represents a whole or partial calendar date, such as a birthday. The
  time of day and time zone are either specified elsewhere or are
  insignificant. The date is relative to the Gregorian Calendar. This can
  represent one of the following: * A full date, with non-zero year, month,
  and day values. * A month and day, with a zero year (for example, an
  anniversary). * A year on its own, with a zero month and a zero day. * A
  year and month, with a zero day (for example, a credit card expiration
  date). Related types: * google.type.TimeOfDay * google.type.DateTime *
  google.protobuf.Timestamp

  Fields:
    day: Day of a month. Must be from 1 to 31 and valid for the year and
      month, or 0 to specify a year by itself or a year and month where the
      day isn't significant.
    month: Month of a year. Must be from 1 to 12, or 0 to specify a year
      without a month and day.
    year: Year of the date. Must be from 1 to 9999, or 0 to specify a date
      without a year.
  """

  day = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  month = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  year = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class DenyMaintenancePeriod(_messages.Message):
  r"""Specifies the maintenance denial period.

  Fields:
    endDate: Required. End date of the deny maintenance period.
    startDate: Required. Start date of the deny maintenance period.
    time: Required. Time in UTC when the period starts and ends.
  """

  endDate = _messages.MessageField('Date', 1)
  startDate = _messages.MessageField('Date', 2)
  time = _messages.MessageField('TimeOfDay', 3)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EncryptionConfig(_messages.Message):
  r"""Encryption configuration (i.e. CMEK).

  Enums:
    KmsKeyStateValueValuesEnum: Output only. Status of the CMEK key.

  Fields:
    kmsKeyName: Name of the CMEK key in KMS (input parameter).
    kmsKeyNameVersion: Output only. Full name and version of the CMEK key
      currently in use to encrypt Looker data. Format: `projects/{project}/loc
      ations/{location}/keyRings/{ring}/cryptoKeys/{key}/cryptoKeyVersions/{ve
      rsion}`. Empty if CMEK is not configured in this instance.
    kmsKeyState: Output only. Status of the CMEK key.
  """

  class KmsKeyStateValueValuesEnum(_messages.Enum):
    r"""Output only. Status of the CMEK key.

    Values:
      KMS_KEY_STATE_UNSPECIFIED: CMEK status not specified.
      VALID: CMEK key is currently valid.
      REVOKED: CMEK key is currently revoked (instance should in restricted
        mode).
    """
    KMS_KEY_STATE_UNSPECIFIED = 0
    VALID = 1
    REVOKED = 2

  kmsKeyName = _messages.StringField(1)
  kmsKeyNameVersion = _messages.StringField(2)
  kmsKeyState = _messages.EnumField('KmsKeyStateValueValuesEnum', 3)


class ExportEncryptionConfig(_messages.Message):
  r"""Configuration for Encryption - e.g. CMEK.

  Fields:
    kmsKeyName: Required. Name of the CMEK key in KMS.
  """

  kmsKeyName = _messages.StringField(1)


class ExportInstanceRequest(_messages.Message):
  r"""Request options for exporting data of an Instance.

  Fields:
    encryptionConfig: Required. Encryption configuration (CMEK). For CMEK
      enabled instances it should be same as looker CMEK.
    gcsUri: The path to the folder in Google Cloud Storage where the export
      will be stored. The URI is in the form `gs://bucketName/folderName`.
  """

  encryptionConfig = _messages.MessageField('ExportEncryptionConfig', 1)
  gcsUri = _messages.StringField(2)


class ExportMetadata(_messages.Message):
  r"""ExportMetadata represents the metadata of the exported artifacts. The
  metadata.json file in export artifact can be parsed as this message

  Enums:
    SourceValueValuesEnum: The source type of the migration.

  Fields:
    exportEncryptionKey: Encryption key that was used to encrypt the export
      artifacts.
    filePaths: List of files created as part of export artifact (excluding the
      metadata). The paths are relative to the folder containing the metadata.
    lookerEncryptionKey: Looker encryption key, encrypted with the provided
      export encryption key. This value will only be populated if the looker
      instance uses Looker managed encryption instead of CMEK.
    lookerInstance: Name of the exported instance. Format:
      projects/{project}/locations/{location}/instances/{instance}
    lookerPlatformEdition: Platform edition of the exported instance.
    lookerVersion: Version of instance when the export was created.
    source: The source type of the migration.
  """

  class SourceValueValuesEnum(_messages.Enum):
    r"""The source type of the migration.

    Values:
      SOURCE_UNSPECIFIED: Source not specified
      LOOKER_CORE: Source of export is Looker Core
      LOOKER_ORIGINAL: Source of export is Looker Original
    """
    SOURCE_UNSPECIFIED = 0
    LOOKER_CORE = 1
    LOOKER_ORIGINAL = 2

  exportEncryptionKey = _messages.MessageField('ExportMetadataEncryptionKey', 1)
  filePaths = _messages.StringField(2, repeated=True)
  lookerEncryptionKey = _messages.StringField(3)
  lookerInstance = _messages.StringField(4)
  lookerPlatformEdition = _messages.StringField(5)
  lookerVersion = _messages.StringField(6)
  source = _messages.EnumField('SourceValueValuesEnum', 7)


class ExportMetadataEncryptionKey(_messages.Message):
  r"""Encryption key details for the exported artifact.

  Fields:
    cmek: Name of the CMEK.
    version: Version of the CMEK.
  """

  cmek = _messages.StringField(1)
  version = _messages.StringField(2)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GeminiAiConfig(_messages.Message):
  r"""Information for Gemini AI setup for a Looker instance.

  Fields:
    promptLogging: Optional. Whether to enable prompt logging for Gemini AI.
    trustedTester: Optional. Whether customer opt in for Gemini AI public
      preview.
  """

  promptLogging = _messages.BooleanField(1)
  trustedTester = _messages.BooleanField(2)


class ImportInstanceRequest(_messages.Message):
  r"""Requestion options for importing looker data to an Instance

  Fields:
    gcsUri: Path to the import folder in Google Cloud Storage, in the form
      `gs://bucketName/folderName`.
  """

  gcsUri = _messages.StringField(1)


class Instance(_messages.Message):
  r"""A Looker instance.

  Enums:
    PlatformEditionValueValuesEnum: Platform edition.
    StateValueValuesEnum: Output only. The state of the instance.
    TierValueValuesEnum: Optional. Tier

  Fields:
    adminSettings: Looker Instance Admin settings.
    consumerNetwork: Network name in the consumer project. Format:
      `projects/{project}/global/networks/{network}`. Note that the consumer
      network may be in a different GCP project than the consumer project that
      is hosting the Looker Instance.
    createTime: Output only. The time when the Looker instance provisioning
      was first requested.
    customDomain: A CustomDomain attribute.
    denyMaintenancePeriod: Maintenance denial period for this instance.
    egressPublicIp: Output only. Public Egress IP (IPv4).
    enablePrivateIp: Whether private IP is enabled on the Looker instance.
    enablePublicIp: Whether public IP is enabled on the Looker instance.
    encryptionConfig: Encryption configuration (CMEK). Only set if CMEK has
      been enabled on the instance.
    fipsEnabled: Optional. Whether FIPS is enabled on the Looker instance.
    geminiAiConfig: Optional. Duet AI configuration
    geminiEnabled: Optional. Whether Gemini feature is enabled on the Looker
      instance or not.
    ingressPrivateIp: Output only. Private Ingress IP (IPv4).
    ingressPublicIp: Output only. Public Ingress IP (IPv4).
    lastDenyMaintenancePeriod: Output only. Last computed maintenance denial
      period for this instance.
    linkedLspProjectNumber: Optional. Linked Google Cloud Project Number for
      Looker Studio Pro.
    lookerUri: Output only. Looker instance URI which can be used to access
      the Looker Instance UI.
    lookerVersion: Output only. The Looker version that the instance is using.
    maintenanceSchedule: Maintenance schedule for this instance.
    maintenanceWindow: Maintenance window for this instance.
    name: Output only. Format:
      `projects/{project}/locations/{location}/instances/{instance}`.
    oauthConfig: Looker instance OAuth login settings.
    platformEdition: Platform edition.
    pscConfig: Optional. PSC configuration. Used when `psc_enabled` is true.
    pscEnabled: Optional. Whether to use Private Service Connect (PSC) for
      private IP connectivity. If true, neither `public_ip_enabled` nor
      `private_ip_enabled` can be true.
    reservedRange: Name of a reserved IP address range within the
      Instance.consumer_network, to be used for private services access
      connection. May or may not be specified in a create request.
    state: Output only. The state of the instance.
    tier: Optional. Tier
    updateTime: Output only. The time when the Looker instance was last
      updated.
    users: User metadata.
  """

  class PlatformEditionValueValuesEnum(_messages.Enum):
    r"""Platform edition.

    Values:
      PLATFORM_EDITION_UNSPECIFIED: Platform edition is unspecified.
      STANDARD: Standard.
      ADVANCED: Advanced.
      ELITE: Elite.
      LOOKER_CORE_TRIAL: Trial.
    """
    PLATFORM_EDITION_UNSPECIFIED = 0
    STANDARD = 1
    ADVANCED = 2
    ELITE = 3
    LOOKER_CORE_TRIAL = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the instance.

    Values:
      STATE_UNSPECIFIED: State is unspecified.
      ACTIVE: Instance is active and ready for use.
      CREATING: Instance provisioning is in progress.
      FAILED: Instance is in a failed state.
      SUSPENDED: Instance was suspended.
      UPDATING: Instance update is in progress.
      DELETING: Instance delete is in progress.
      EXPORTING: Instance is being exported.
      IMPORTING: Instance is importing data.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    FAILED = 3
    SUSPENDED = 4
    UPDATING = 5
    DELETING = 6
    EXPORTING = 7
    IMPORTING = 8

  class TierValueValuesEnum(_messages.Enum):
    r"""Optional. Tier

    Values:
      TIER_UNSPECIFIED: Unspecified tier.
      XS: X-Small
      S: Small
      M: Medium
      L: Large
      XL: X-Large
    """
    TIER_UNSPECIFIED = 0
    XS = 1
    S = 2
    M = 3
    L = 4
    XL = 5

  adminSettings = _messages.MessageField('AdminSettings', 1)
  consumerNetwork = _messages.StringField(2)
  createTime = _messages.StringField(3)
  customDomain = _messages.MessageField('CustomDomain', 4)
  denyMaintenancePeriod = _messages.MessageField('DenyMaintenancePeriod', 5)
  egressPublicIp = _messages.StringField(6)
  enablePrivateIp = _messages.BooleanField(7)
  enablePublicIp = _messages.BooleanField(8)
  encryptionConfig = _messages.MessageField('EncryptionConfig', 9)
  fipsEnabled = _messages.BooleanField(10)
  geminiAiConfig = _messages.MessageField('GeminiAiConfig', 11)
  geminiEnabled = _messages.BooleanField(12)
  ingressPrivateIp = _messages.StringField(13)
  ingressPublicIp = _messages.StringField(14)
  lastDenyMaintenancePeriod = _messages.MessageField('DenyMaintenancePeriod', 15)
  linkedLspProjectNumber = _messages.IntegerField(16)
  lookerUri = _messages.StringField(17)
  lookerVersion = _messages.StringField(18)
  maintenanceSchedule = _messages.MessageField('MaintenanceSchedule', 19)
  maintenanceWindow = _messages.MessageField('MaintenanceWindow', 20)
  name = _messages.StringField(21)
  oauthConfig = _messages.MessageField('OAuthConfig', 22)
  platformEdition = _messages.EnumField('PlatformEditionValueValuesEnum', 23)
  pscConfig = _messages.MessageField('PscConfig', 24)
  pscEnabled = _messages.BooleanField(25)
  reservedRange = _messages.StringField(26)
  state = _messages.EnumField('StateValueValuesEnum', 27)
  tier = _messages.EnumField('TierValueValuesEnum', 28)
  updateTime = _messages.StringField(29)
  users = _messages.MessageField('Users', 30)


class InstanceBackup(_messages.Message):
  r"""The details of a backup resource.

  Enums:
    StateValueValuesEnum: Output only. The current state of the backup.

  Fields:
    createTime: Output only. The time when the backup was started.
    encryptionConfig: Output only. Current status of the CMEK encryption
    expireTime: Output only. The time when the backup will be deleted.
    name: Immutable. The relative resource name of the backup, in the
      following form: `projects/{project_number}/locations/{location_id}/insta
      nces/{instance_id}/backups/{backup}`
    state: Output only. The current state of the backup.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the backup.

    Values:
      STATE_UNSPECIFIED: The state of the backup is unknown.
      CREATING: The backup is being created.
      DELETING: The backup is being deleted.
      ACTIVE: The backup is active and ready to use.
      FAILED: The backup failed.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    DELETING = 2
    ACTIVE = 3
    FAILED = 4

  createTime = _messages.StringField(1)
  encryptionConfig = _messages.MessageField('EncryptionConfig', 2)
  expireTime = _messages.StringField(3)
  name = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)


class ListInstanceBackupsResponse(_messages.Message):
  r"""Response from listing Looker instance backups.

  Fields:
    instanceBackups: The list of instances matching the request filters, up to
      the requested `page_size`.
    nextPageToken: If provided, a page token that can look up the next
      `page_size` results. If empty, the results list is exhausted.
    unreachable: Locations that could not be reached.
  """

  instanceBackups = _messages.MessageField('InstanceBackup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListInstancesResponse(_messages.Message):
  r"""Response from ListInstances.

  Fields:
    instances: The list of instances matching the request filters, up to the
      requested ListInstancesRequest.pageSize.
    nextPageToken: If provided, a page token that can look up the next
      ListInstancesRequest.pageSize results. If empty, the results list is
      exhausted.
    unreachable: Locations that could not be reached.
  """

  instances = _messages.MessageField('Instance', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class LookerProjectsLocationsGetRequest(_messages.Message):
  r"""A LookerProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class LookerProjectsLocationsInstancesBackupsCreateRequest(_messages.Message):
  r"""A LookerProjectsLocationsInstancesBackupsCreateRequest object.

  Fields:
    instanceBackup: A InstanceBackup resource to be passed as the request
      body.
    parent: Required. Format:
      projects/{project}/locations/{location}/instances/{instance}
  """

  instanceBackup = _messages.MessageField('InstanceBackup', 1)
  parent = _messages.StringField(2, required=True)


class LookerProjectsLocationsInstancesBackupsDeleteRequest(_messages.Message):
  r"""A LookerProjectsLocationsInstancesBackupsDeleteRequest object.

  Fields:
    name: Required. Format: projects/{project}/locations/{location}/instances/
      {instance}/backups/{backup}
  """

  name = _messages.StringField(1, required=True)


class LookerProjectsLocationsInstancesBackupsGetIamPolicyRequest(_messages.Message):
  r"""A LookerProjectsLocationsInstancesBackupsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class LookerProjectsLocationsInstancesBackupsGetRequest(_messages.Message):
  r"""A LookerProjectsLocationsInstancesBackupsGetRequest object.

  Fields:
    name: Required. Format: `projects/{project}/locations/{location}/instances
      /{instance}/backups/{backup}`.
  """

  name = _messages.StringField(1, required=True)


class LookerProjectsLocationsInstancesBackupsListRequest(_messages.Message):
  r"""A LookerProjectsLocationsInstancesBackupsListRequest object.

  Fields:
    orderBy: Sort results. Default order is "create_time desc". Other
      supported fields are "state" and "expire_time".
      https://google.aip.dev/132#ordering
    pageSize: The maximum number of instances to return.
    pageToken: A page token received from a previous ListInstances request.
    parent: Required. Format:
      projects/{project}/locations/{location}/instances/{instance}.
  """

  orderBy = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class LookerProjectsLocationsInstancesBackupsSetIamPolicyRequest(_messages.Message):
  r"""A LookerProjectsLocationsInstancesBackupsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class LookerProjectsLocationsInstancesBackupsTestIamPermissionsRequest(_messages.Message):
  r"""A LookerProjectsLocationsInstancesBackupsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class LookerProjectsLocationsInstancesCreateRequest(_messages.Message):
  r"""A LookerProjectsLocationsInstancesCreateRequest object.

  Fields:
    instance: A Instance resource to be passed as the request body.
    instanceId: Required. The unique instance identifier. Must contain only
      lowercase letters, numbers, or hyphens, with the first character a
      letter and the last a letter or a number. 63 characters maximum.
    parent: Required. Format: `projects/{project}/locations/{location}`.
  """

  instance = _messages.MessageField('Instance', 1)
  instanceId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LookerProjectsLocationsInstancesDeleteRequest(_messages.Message):
  r"""A LookerProjectsLocationsInstancesDeleteRequest object.

  Fields:
    force: Whether to force cascading delete.
    name: Required. Format:
      `projects/{project}/locations/{location}/instances/{instance}`.
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class LookerProjectsLocationsInstancesExportRequest(_messages.Message):
  r"""A LookerProjectsLocationsInstancesExportRequest object.

  Fields:
    exportInstanceRequest: A ExportInstanceRequest resource to be passed as
      the request body.
    name: Required. Format:
      `projects/{project}/locations/{location}/instances/{instance}`.
  """

  exportInstanceRequest = _messages.MessageField('ExportInstanceRequest', 1)
  name = _messages.StringField(2, required=True)


class LookerProjectsLocationsInstancesGetIamPolicyRequest(_messages.Message):
  r"""A LookerProjectsLocationsInstancesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class LookerProjectsLocationsInstancesGetRequest(_messages.Message):
  r"""A LookerProjectsLocationsInstancesGetRequest object.

  Fields:
    name: Required. Format:
      `projects/{project}/locations/{location}/instances/{instance}`.
  """

  name = _messages.StringField(1, required=True)


class LookerProjectsLocationsInstancesImportRequest(_messages.Message):
  r"""A LookerProjectsLocationsInstancesImportRequest object.

  Fields:
    importInstanceRequest: A ImportInstanceRequest resource to be passed as
      the request body.
    name: Required. Format:
      `projects/{project}/locations/{location}/instances/{instance}`.
  """

  importInstanceRequest = _messages.MessageField('ImportInstanceRequest', 1)
  name = _messages.StringField(2, required=True)


class LookerProjectsLocationsInstancesListRequest(_messages.Message):
  r"""A LookerProjectsLocationsInstancesListRequest object.

  Fields:
    pageSize: The maximum number of instances to return. If unspecified at
      most 256 will be returned. The maximum possible value is 2048.
    pageToken: A page token received from a previous ListInstancesRequest.
    parent: Required. Format: `projects/{project}/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LookerProjectsLocationsInstancesPatchRequest(_messages.Message):
  r"""A LookerProjectsLocationsInstancesPatchRequest object.

  Fields:
    instance: A Instance resource to be passed as the request body.
    name: Output only. Format:
      `projects/{project}/locations/{location}/instances/{instance}`.
    updateMask: Required. Field mask used to specify the fields to be
      overwritten in the Instance resource by the update. The fields specified
      in the mask are relative to the resource, not the full request. A field
      will be overwritten if it is in the mask.
  """

  instance = _messages.MessageField('Instance', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class LookerProjectsLocationsInstancesRestartRequest(_messages.Message):
  r"""A LookerProjectsLocationsInstancesRestartRequest object.

  Fields:
    name: Required. Format:
      `projects/{project}/locations/{location}/instances/{instance}`.
    restartInstanceRequest: A RestartInstanceRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  restartInstanceRequest = _messages.MessageField('RestartInstanceRequest', 2)


class LookerProjectsLocationsInstancesRestoreRequest(_messages.Message):
  r"""A LookerProjectsLocationsInstancesRestoreRequest object.

  Fields:
    name: Required. Instance being restored Format:
      projects/{project}/locations/{location}/instances/{instance}
    restoreInstanceRequest: A RestoreInstanceRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  restoreInstanceRequest = _messages.MessageField('RestoreInstanceRequest', 2)


class LookerProjectsLocationsInstancesSetIamPolicyRequest(_messages.Message):
  r"""A LookerProjectsLocationsInstancesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class LookerProjectsLocationsInstancesTestIamPermissionsRequest(_messages.Message):
  r"""A LookerProjectsLocationsInstancesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class LookerProjectsLocationsListRequest(_messages.Message):
  r"""A LookerProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class LookerProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A LookerProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class LookerProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A LookerProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class LookerProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A LookerProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class LookerProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A LookerProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class MaintenanceSchedule(_messages.Message):
  r"""Published upcoming future maintenance schedule.

  Fields:
    endTime: The scheduled end time for the maintenance.
    startTime: The scheduled start time for the maintenance.
  """

  endTime = _messages.StringField(1)
  startTime = _messages.StringField(2)


class MaintenanceWindow(_messages.Message):
  r"""Specifies the recurring maintenance window.

  Enums:
    DayOfWeekValueValuesEnum: Required. Day of the week for this
      MaintenanceWindow (in UTC).

  Fields:
    dayOfWeek: Required. Day of the week for this MaintenanceWindow (in UTC).
    startTime: Required. Time in UTC when the period starts. Maintenance will
      be scheduled within 60 minutes.
  """

  class DayOfWeekValueValuesEnum(_messages.Enum):
    r"""Required. Day of the week for this MaintenanceWindow (in UTC).

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  dayOfWeek = _messages.EnumField('DayOfWeekValueValuesEnum', 1)
  startTime = _messages.MessageField('TimeOfDay', 2)


class OAuthConfig(_messages.Message):
  r"""Looker instance OAuth login settings.

  Fields:
    clientId: Input only. Client ID from an external OAuth application. This
      is an input-only field, and thus will not be set in any responses.
    clientSecret: Input only. Client secret from an external OAuth
      application. This is an input-only field, and thus will not be set in
      any responses.
  """

  clientId = _messages.StringField(1)
  clientSecret = _messages.StringField(2)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: API version used to start the operation.
    createTime: The time the operation was created.
    endTime: The time the operation finished running.
    requestedCancellation: Identifies whether the user has requested
      cancellation of the operation. Operations that have successfully been
      cancelled have Operation.error value with a google.rpc.Status.code of 1,
      corresponding to `Code.CANCELLED`.
    statusMessage: Human-readable status of the operation, if any.
    target: Server-defined resource path for the target of the operation.
    verb: Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class PscConfig(_messages.Message):
  r"""Information for Private Service Connect (PSC) setup for a Looker
  instance.

  Fields:
    allowedVpcs: Optional. List of VPCs that are allowed ingress into looker.
      Format: projects/{project}/global/networks/{network}
    lookerServiceAttachmentUri: Output only. URI of the Looker service
      attachment.
    serviceAttachments: Optional. List of egress service attachment
      configurations.
  """

  allowedVpcs = _messages.StringField(1, repeated=True)
  lookerServiceAttachmentUri = _messages.StringField(2)
  serviceAttachments = _messages.MessageField('ServiceAttachment', 3, repeated=True)


class RestartInstanceRequest(_messages.Message):
  r"""Request options for restarting an instance."""


class RestoreInstanceRequest(_messages.Message):
  r"""Request options for restoring an instance

  Fields:
    backup: Required. Backup being used to restore the instance Format: projec
      ts/{project}/locations/{location}/instances/{instance}/backups/{backup}
  """

  backup = _messages.StringField(1)


class ServiceAttachment(_messages.Message):
  r"""Service attachment configuration.

  Enums:
    ConnectionStatusValueValuesEnum: Output only. Connection status.

  Fields:
    connectionStatus: Output only. Connection status.
    localFqdn: Required. Fully qualified domain name that will be used in the
      private DNS record created for the service attachment.
    targetServiceAttachmentUri: Required. URI of the service attachment to
      connect to. Format: projects/{project}/regions/{region}/serviceAttachmen
      ts/{service_attachment}
  """

  class ConnectionStatusValueValuesEnum(_messages.Enum):
    r"""Output only. Connection status.

    Values:
      UNKNOWN: Connection status is unspecified.
      ACCEPTED: Connection is established and functioning normally.
      PENDING: Connection is not established (Looker tenant project hasn't
        been allowlisted).
      REJECTED: Connection is not established (Looker tenant project is
        explicitly in reject list).
      NEEDS_ATTENTION: Issue with target service attachment, e.g. NAT subnet
        is exhausted.
      CLOSED: Target service attachment does not exist. This status is a
        terminal state.
    """
    UNKNOWN = 0
    ACCEPTED = 1
    PENDING = 2
    REJECTED = 3
    NEEDS_ATTENTION = 4
    CLOSED = 5

  connectionStatus = _messages.EnumField('ConnectionStatusValueValuesEnum', 1)
  localFqdn = _messages.StringField(2)
  targetServiceAttachmentUri = _messages.StringField(3)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TimeOfDay(_messages.Message):
  r"""Represents a time of day. The date and time zone are either not
  significant or are specified elsewhere. An API may choose to allow leap
  seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.

  Fields:
    hours: Hours of day in 24 hour format. Should be from 0 to 23. An API may
      choose to allow the value "24:00:00" for scenarios like business closing
      time.
    minutes: Minutes of hour of day. Must be from 0 to 59.
    nanos: Fractions of seconds in nanoseconds. Must be from 0 to 999,999,999.
    seconds: Seconds of minutes of the time. Must normally be from 0 to 59. An
      API may allow the value 60 if it allows leap-seconds.
  """

  hours = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minutes = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  nanos = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  seconds = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class Users(_messages.Message):
  r"""Metadata about users for a Looker instance.

  Fields:
    additionalDeveloperUsers: Optional. The number of additional developer
      users the instance owner has purchased.
    additionalStandardUsers: Optional. The number of additional standard users
      the instance owner has purchased.
    additionalViewerUsers: Optional. The number of additional viewer users the
      instance owner has purchased.
  """

  additionalDeveloperUsers = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  additionalStandardUsers = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  additionalViewerUsers = _messages.IntegerField(3, variant=_messages.Variant.INT32)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    LookerProjectsLocationsInstancesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    LookerProjectsLocationsInstancesBackupsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
