"""Generated message classes for recaptchaenterprise version v1.

Help protect your website from fraudulent activity, spam, and abuse without
creating friction.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'recaptchaenterprise'


class GoogleCloudRecaptchaenterpriseV1AccountDefenderAssessment(_messages.Message):
  r"""Account defender risk assessment.

  Enums:
    LabelsValueListEntryValuesEnum:

  Fields:
    labels: Output only. Labels for this request.
  """

  class LabelsValueListEntryValuesEnum(_messages.Enum):
    r"""LabelsValueListEntryValuesEnum enum type.

    Values:
      ACCOUNT_DEFENDER_LABEL_UNSPECIFIED: Default unspecified type.
      PROFILE_MATCH: The request matches a known good profile for the user.
      SUSPICIOUS_LOGIN_ACTIVITY: The request is potentially a suspicious login
        event and must be further verified either through multi-factor
        authentication or another system.
      SUSPICIOUS_ACCOUNT_CREATION: The request matched a profile that
        previously had suspicious account creation behavior. This can mean
        that this is a fake account.
      RELATED_ACCOUNTS_NUMBER_HIGH: The account in the request has a high
        number of related accounts. It does not necessarily imply that the
        account is bad but can require further investigation.
    """
    ACCOUNT_DEFENDER_LABEL_UNSPECIFIED = 0
    PROFILE_MATCH = 1
    SUSPICIOUS_LOGIN_ACTIVITY = 2
    SUSPICIOUS_ACCOUNT_CREATION = 3
    RELATED_ACCOUNTS_NUMBER_HIGH = 4

  labels = _messages.EnumField('LabelsValueListEntryValuesEnum', 1, repeated=True)


class GoogleCloudRecaptchaenterpriseV1AccountVerificationInfo(_messages.Message):
  r"""Information about account verification, used for identity verification.

  Enums:
    LatestVerificationResultValueValuesEnum: Output only. Result of the latest
      account verification challenge.

  Fields:
    endpoints: Optional. Endpoints that can be used for identity verification.
    languageCode: Optional. Language code preference for the verification
      message, set as a IETF BCP 47 language code.
    latestVerificationResult: Output only. Result of the latest account
      verification challenge.
    username: Username of the account that is being verified. Deprecated.
      Customers should now provide the `account_id` field in
      `event.user_info`.
  """

  class LatestVerificationResultValueValuesEnum(_messages.Enum):
    r"""Output only. Result of the latest account verification challenge.

    Values:
      RESULT_UNSPECIFIED: No information about the latest account
        verification.
      SUCCESS_USER_VERIFIED: The user was successfully verified. This means
        the account verification challenge was successfully completed.
      ERROR_USER_NOT_VERIFIED: The user failed the verification challenge.
      ERROR_SITE_ONBOARDING_INCOMPLETE: The site is not properly onboarded to
        use the account verification feature.
      ERROR_RECIPIENT_NOT_ALLOWED: The recipient is not allowed for account
        verification. This can occur during integration but should not occur
        in production.
      ERROR_RECIPIENT_ABUSE_LIMIT_EXHAUSTED: The recipient has already been
        sent too many verification codes in a short amount of time.
      ERROR_CRITICAL_INTERNAL: The verification flow could not be completed
        due to a critical internal error.
      ERROR_CUSTOMER_QUOTA_EXHAUSTED: The client has exceeded their two factor
        request quota for this period of time.
      ERROR_VERIFICATION_BYPASSED: The request cannot be processed at the time
        because of an incident. This bypass can be restricted to a problematic
        destination email domain, a customer, or could affect the entire
        service.
      ERROR_VERDICT_MISMATCH: The request parameters do not match with the
        token provided and cannot be processed.
    """
    RESULT_UNSPECIFIED = 0
    SUCCESS_USER_VERIFIED = 1
    ERROR_USER_NOT_VERIFIED = 2
    ERROR_SITE_ONBOARDING_INCOMPLETE = 3
    ERROR_RECIPIENT_NOT_ALLOWED = 4
    ERROR_RECIPIENT_ABUSE_LIMIT_EXHAUSTED = 5
    ERROR_CRITICAL_INTERNAL = 6
    ERROR_CUSTOMER_QUOTA_EXHAUSTED = 7
    ERROR_VERIFICATION_BYPASSED = 8
    ERROR_VERDICT_MISMATCH = 9

  endpoints = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1EndpointVerificationInfo', 1, repeated=True)
  languageCode = _messages.StringField(2)
  latestVerificationResult = _messages.EnumField('LatestVerificationResultValueValuesEnum', 3)
  username = _messages.StringField(4)


class GoogleCloudRecaptchaenterpriseV1AddIpOverrideRequest(_messages.Message):
  r"""The AddIpOverride request message.

  Fields:
    ipOverrideData: Required. IP override added to the key.
  """

  ipOverrideData = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1IpOverrideData', 1)


class GoogleCloudRecaptchaenterpriseV1AddIpOverrideResponse(_messages.Message):
  r"""Response for AddIpOverride."""


class GoogleCloudRecaptchaenterpriseV1AndroidKeySettings(_messages.Message):
  r"""Settings specific to keys that can be used by Android apps.

  Fields:
    allowAllPackageNames: Optional. If set to true, allowed_package_names are
      not enforced.
    allowedPackageNames: Optional. Android package names of apps allowed to
      use the key. Example: 'com.companyname.appname'
    supportNonGoogleAppStoreDistribution: Optional. Set to true for keys that
      are used in an Android application that is available for download in app
      stores in addition to the Google Play Store.
  """

  allowAllPackageNames = _messages.BooleanField(1)
  allowedPackageNames = _messages.StringField(2, repeated=True)
  supportNonGoogleAppStoreDistribution = _messages.BooleanField(3)


class GoogleCloudRecaptchaenterpriseV1AnnotateAssessmentRequest(_messages.Message):
  r"""The request message to annotate an Assessment.

  Enums:
    AnnotationValueValuesEnum: Optional. The annotation that is assigned to
      the Event. This field can be left empty to provide reasons that apply to
      an event without concluding whether the event is legitimate or
      fraudulent.
    ReasonsValueListEntryValuesEnum:

  Fields:
    accountId: Optional. A stable account identifier to apply to the
      assessment. This is an alternative to setting `account_id` in
      `CreateAssessment`, for example when a stable account identifier is not
      yet known in the initial request.
    annotation: Optional. The annotation that is assigned to the Event. This
      field can be left empty to provide reasons that apply to an event
      without concluding whether the event is legitimate or fraudulent.
    hashedAccountId: Optional. A stable hashed account identifier to apply to
      the assessment. This is an alternative to setting `hashed_account_id` in
      `CreateAssessment`, for example when a stable account identifier is not
      yet known in the initial request.
    reasons: Optional. Reasons for the annotation that are assigned to the
      event.
    transactionEvent: Optional. If the assessment is part of a payment
      transaction, provide details on payment lifecycle events that occur in
      the transaction.
  """

  class AnnotationValueValuesEnum(_messages.Enum):
    r"""Optional. The annotation that is assigned to the Event. This field can
    be left empty to provide reasons that apply to an event without concluding
    whether the event is legitimate or fraudulent.

    Values:
      ANNOTATION_UNSPECIFIED: Default unspecified type.
      LEGITIMATE: Provides information that the event turned out to be
        legitimate.
      FRAUDULENT: Provides information that the event turned out to be
        fraudulent.
      PASSWORD_CORRECT: Provides information that the event was related to a
        login event in which the user typed the correct password. Deprecated,
        prefer indicating CORRECT_PASSWORD through the reasons field instead.
      PASSWORD_INCORRECT: Provides information that the event was related to a
        login event in which the user typed the incorrect password.
        Deprecated, prefer indicating INCORRECT_PASSWORD through the reasons
        field instead.
    """
    ANNOTATION_UNSPECIFIED = 0
    LEGITIMATE = 1
    FRAUDULENT = 2
    PASSWORD_CORRECT = 3
    PASSWORD_INCORRECT = 4

  class ReasonsValueListEntryValuesEnum(_messages.Enum):
    r"""ReasonsValueListEntryValuesEnum enum type.

    Values:
      REASON_UNSPECIFIED: Unspecified reason. Do not use.
      CHARGEBACK: Indicates that the transaction had a chargeback issued with
        no other details. When possible, specify the type by using
        CHARGEBACK_FRAUD or CHARGEBACK_DISPUTE instead.
      CHARGEBACK_FRAUD: Indicates that the transaction had a chargeback issued
        related to an alleged unauthorized transaction from the cardholder's
        perspective (for example, the card number was stolen).
      CHARGEBACK_DISPUTE: Indicates that the transaction had a chargeback
        issued related to the cardholder having provided their card details
        but allegedly not being satisfied with the purchase (for example,
        misrepresentation, attempted cancellation).
      REFUND: Indicates that the completed payment transaction was refunded by
        the seller.
      REFUND_FRAUD: Indicates that the completed payment transaction was
        determined to be fraudulent by the seller, and was cancelled and
        refunded as a result.
      TRANSACTION_ACCEPTED: Indicates that the payment transaction was
        accepted, and the user was charged.
      TRANSACTION_DECLINED: Indicates that the payment transaction was
        declined, for example due to invalid card details.
      PAYMENT_HEURISTICS: Indicates the transaction associated with the
        assessment is suspected of being fraudulent based on the payment
        method, billing details, shipping address or other transaction
        information.
      INITIATED_TWO_FACTOR: Indicates that the user was served a 2FA
        challenge. An old assessment with `ENUM_VALUES.INITIATED_TWO_FACTOR`
        reason that has not been overwritten with `PASSED_TWO_FACTOR` is
        treated as an abandoned 2FA flow. This is equivalent to
        `FAILED_TWO_FACTOR`.
      PASSED_TWO_FACTOR: Indicates that the user passed a 2FA challenge.
      FAILED_TWO_FACTOR: Indicates that the user failed a 2FA challenge.
      CORRECT_PASSWORD: Indicates the user provided the correct password.
      INCORRECT_PASSWORD: Indicates the user provided an incorrect password.
      SOCIAL_SPAM: Indicates that the user sent unwanted and abusive messages
        to other users of the platform, such as spam, scams, phishing, or
        social engineering.
    """
    REASON_UNSPECIFIED = 0
    CHARGEBACK = 1
    CHARGEBACK_FRAUD = 2
    CHARGEBACK_DISPUTE = 3
    REFUND = 4
    REFUND_FRAUD = 5
    TRANSACTION_ACCEPTED = 6
    TRANSACTION_DECLINED = 7
    PAYMENT_HEURISTICS = 8
    INITIATED_TWO_FACTOR = 9
    PASSED_TWO_FACTOR = 10
    FAILED_TWO_FACTOR = 11
    CORRECT_PASSWORD = 12
    INCORRECT_PASSWORD = 13
    SOCIAL_SPAM = 14

  accountId = _messages.StringField(1)
  annotation = _messages.EnumField('AnnotationValueValuesEnum', 2)
  hashedAccountId = _messages.BytesField(3)
  reasons = _messages.EnumField('ReasonsValueListEntryValuesEnum', 4, repeated=True)
  transactionEvent = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1TransactionEvent', 5)


class GoogleCloudRecaptchaenterpriseV1AnnotateAssessmentResponse(_messages.Message):
  r"""Empty response for AnnotateAssessment."""


class GoogleCloudRecaptchaenterpriseV1AppleDeveloperId(_messages.Message):
  r"""Contains fields that are required to perform Apple-specific integrity
  checks.

  Fields:
    keyId: Required. The Apple developer key ID (10-character string).
    privateKey: Required. Input only. A private key (downloaded as a text file
      with a .p8 file extension) generated for your Apple Developer account.
      Ensure that Apple DeviceCheck is enabled for the private key.
    teamId: Required. The Apple team ID (10-character string) owning the
      provisioning profile used to build your application.
  """

  keyId = _messages.StringField(1)
  privateKey = _messages.StringField(2)
  teamId = _messages.StringField(3)


class GoogleCloudRecaptchaenterpriseV1Assessment(_messages.Message):
  r"""A reCAPTCHA Enterprise assessment resource.

  Fields:
    accountDefenderAssessment: Output only. Assessment returned by account
      defender when an account identifier is provided.
    accountVerification: Optional. Account verification information for
      identity verification. The assessment event must include a token and
      site key to use this feature.
    assessmentEnvironment: Optional. The environment creating the assessment.
      This describes your environment (the system invoking CreateAssessment),
      NOT the environment of your user.
    event: Optional. The event being assessed.
    firewallPolicyAssessment: Output only. Assessment returned when firewall
      policies belonging to the project are evaluated using the field
      firewall_policy_evaluation.
    fraudPreventionAssessment: Output only. Assessment returned by Fraud
      Prevention when TransactionData is provided.
    fraudSignals: Output only. Fraud Signals specific to the users involved in
      a payment transaction.
    name: Output only. Identifier. The resource name for the Assessment in the
      format `projects/{project}/assessments/{assessment}`.
    phoneFraudAssessment: Output only. Assessment returned when a site key, a
      token, and a phone number as `user_id` are provided. Account defender
      and SMS toll fraud protection need to be enabled.
    privatePasswordLeakVerification: Optional. The private password leak
      verification field contains the parameters that are used to to check for
      leaks privately without sharing user credentials.
    riskAnalysis: Output only. The risk analysis result for the event being
      assessed.
    tokenProperties: Output only. Properties of the provided event token.
  """

  accountDefenderAssessment = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1AccountDefenderAssessment', 1)
  accountVerification = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1AccountVerificationInfo', 2)
  assessmentEnvironment = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1AssessmentEnvironment', 3)
  event = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1Event', 4)
  firewallPolicyAssessment = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1FirewallPolicyAssessment', 5)
  fraudPreventionAssessment = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1FraudPreventionAssessment', 6)
  fraudSignals = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1FraudSignals', 7)
  name = _messages.StringField(8)
  phoneFraudAssessment = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1PhoneFraudAssessment', 9)
  privatePasswordLeakVerification = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1PrivatePasswordLeakVerification', 10)
  riskAnalysis = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1RiskAnalysis', 11)
  tokenProperties = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1TokenProperties', 12)


class GoogleCloudRecaptchaenterpriseV1AssessmentEnvironment(_messages.Message):
  r"""The environment creating the assessment. This describes your environment
  (the system invoking CreateAssessment), NOT the environment of your user.

  Fields:
    client: Optional. Identifies the client module initiating the
      CreateAssessment request. This can be the link to the client module's
      project. Examples include: - "github.com/GoogleCloudPlatform/recaptcha-
      enterprise-google-tag-manager" -
      "cloud.google.com/recaptcha/docs/implement-waf-akamai" -
      "cloud.google.com/recaptcha/docs/implement-waf-cloudflare" -
      "wordpress.org/plugins/recaptcha-something"
    version: Optional. The version of the client module. For example, "1.0.0".
  """

  client = _messages.StringField(1)
  version = _messages.StringField(2)


class GoogleCloudRecaptchaenterpriseV1Bot(_messages.Message):
  r"""Bot information and metadata.

  Enums:
    BotTypeValueValuesEnum: Optional. Enumerated field representing the type
      of bot.

  Fields:
    botType: Optional. Enumerated field representing the type of bot.
    name: Optional. Enumerated string value that indicates the identity of the
      bot, formatted in kebab-case.
  """

  class BotTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Enumerated field representing the type of bot.

    Values:
      BOT_TYPE_UNSPECIFIED: Default unspecified type.
      AI_AGENT: Software program that interacts with a site and performs tasks
        autonomously.
      CONTENT_SCRAPER: Software that extracts specific data from sites for
        use.
      SEARCH_INDEXER: Software that crawls sites and stores content for the
        purpose of efficient retrieval, likely as part of a search engine.
    """
    BOT_TYPE_UNSPECIFIED = 0
    AI_AGENT = 1
    CONTENT_SCRAPER = 2
    SEARCH_INDEXER = 3

  botType = _messages.EnumField('BotTypeValueValuesEnum', 1)
  name = _messages.StringField(2)


class GoogleCloudRecaptchaenterpriseV1ChallengeMetrics(_messages.Message):
  r"""Metrics related to challenges.

  Fields:
    failedCount: Count of submitted challenge solutions that were incorrect or
      otherwise deemed suspicious such that a subsequent challenge was
      triggered.
    nocaptchaCount: Count of nocaptchas (successful verification without a
      challenge) issued.
    pageloadCount: Count of reCAPTCHA checkboxes or badges rendered. This is
      mostly equivalent to a count of pageloads for pages that include
      reCAPTCHA.
    passedCount: Count of nocaptchas (successful verification without a
      challenge) plus submitted challenge solutions that were correct and
      resulted in verification.
  """

  failedCount = _messages.IntegerField(1)
  nocaptchaCount = _messages.IntegerField(2)
  pageloadCount = _messages.IntegerField(3)
  passedCount = _messages.IntegerField(4)


class GoogleCloudRecaptchaenterpriseV1EndpointVerificationInfo(_messages.Message):
  r"""Information about a verification endpoint that can be used for 2FA.

  Fields:
    emailAddress: Email address for which to trigger a verification request.
    lastVerificationTime: Output only. Timestamp of the last successful
      verification for the endpoint, if any.
    phoneNumber: Phone number for which to trigger a verification request.
      Should be given in E.164 format.
    requestToken: Output only. Token to provide to the client to trigger
      endpoint verification. It must be used within 15 minutes.
  """

  emailAddress = _messages.StringField(1)
  lastVerificationTime = _messages.StringField(2)
  phoneNumber = _messages.StringField(3)
  requestToken = _messages.StringField(4)


class GoogleCloudRecaptchaenterpriseV1Event(_messages.Message):
  r"""The event being assessed.

  Enums:
    FraudPreventionValueValuesEnum: Optional. The Fraud Prevention setting for
      this assessment.

  Fields:
    expectedAction: Optional. The expected action for this type of event. This
      should be the same action provided at token generation time on client-
      side platforms already integrated with recaptcha enterprise.
    express: Optional. Flag for a reCAPTCHA express request for an assessment
      without a token. If enabled, `site_key` must reference an Express site
      key.
    firewallPolicyEvaluation: Optional. Flag for enabling firewall policy
      config assessment. If this flag is enabled, the firewall policy is
      evaluated and a suggested firewall action is returned in the response.
    fraudPrevention: Optional. The Fraud Prevention setting for this
      assessment.
    hashedAccountId: Optional. Deprecated: use `user_info.account_id` instead.
      Unique stable hashed user identifier for the request. The identifier
      must be hashed using hmac-sha256 with stable secret.
    headers: Optional. HTTP header information about the request.
    ja3: Optional. JA3 fingerprint for SSL clients. To learn how to compute
      this fingerprint, please refer to https://github.com/salesforce/ja3.
    ja4: Optional. JA4 fingerprint for SSL clients. To learn how to compute
      this fingerprint, please refer to https://github.com/FoxIO-LLC/ja4.
    requestedUri: Optional. The URI resource the user requested that triggered
      an assessment.
    siteKey: Optional. The site key that was used to invoke reCAPTCHA
      Enterprise on your site and generate the token.
    token: Optional. The user response token provided by the reCAPTCHA
      Enterprise client-side integration on your site.
    transactionData: Optional. Data describing a payment transaction to be
      assessed. Sending this data enables reCAPTCHA Enterprise Fraud
      Prevention and the FraudPreventionAssessment component in the response.
    userAgent: Optional. The user agent present in the request from the user's
      device related to this event.
    userInfo: Optional. Information about the user that generates this event,
      when they can be identified. They are often identified through the use
      of an account for logged-in requests or login/registration requests, or
      by providing user identifiers for guest actions like checkout.
    userIpAddress: Optional. The IP address in the request from the user's
      device related to this event.
    wafTokenAssessment: Optional. Flag for running WAF token assessment. If
      enabled, the token must be specified, and have been created by a WAF-
      enabled key.
  """

  class FraudPreventionValueValuesEnum(_messages.Enum):
    r"""Optional. The Fraud Prevention setting for this assessment.

    Values:
      FRAUD_PREVENTION_UNSPECIFIED: Default, unspecified setting.
        `fraud_prevention_assessment` is returned if `transaction_data` is
        present in `Event` and Fraud Prevention is enabled in the Google Cloud
        console.
      ENABLED: Enable Fraud Prevention for this assessment, if Fraud
        Prevention is enabled in the Google Cloud console.
      DISABLED: Disable Fraud Prevention for this assessment, regardless of
        the Google Cloud console settings.
    """
    FRAUD_PREVENTION_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  expectedAction = _messages.StringField(1)
  express = _messages.BooleanField(2)
  firewallPolicyEvaluation = _messages.BooleanField(3)
  fraudPrevention = _messages.EnumField('FraudPreventionValueValuesEnum', 4)
  hashedAccountId = _messages.BytesField(5)
  headers = _messages.StringField(6, repeated=True)
  ja3 = _messages.StringField(7)
  ja4 = _messages.StringField(8)
  requestedUri = _messages.StringField(9)
  siteKey = _messages.StringField(10)
  token = _messages.StringField(11)
  transactionData = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1TransactionData', 12)
  userAgent = _messages.StringField(13)
  userInfo = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1UserInfo', 14)
  userIpAddress = _messages.StringField(15)
  wafTokenAssessment = _messages.BooleanField(16)


class GoogleCloudRecaptchaenterpriseV1ExpressKeySettings(_messages.Message):
  r"""Settings specific to keys that can be used for reCAPTCHA Express."""


class GoogleCloudRecaptchaenterpriseV1FirewallAction(_messages.Message):
  r"""An individual action. Each action represents what to do if a policy
  matches.

  Fields:
    allow: The user request did not match any policy and should be allowed
      access to the requested resource.
    block: This action denies access to a given page. The user gets an HTTP
      error code.
    includeRecaptchaScript: This action injects reCAPTCHA JavaScript code into
      the HTML page returned by the site backend.
    redirect: This action redirects the request to a reCAPTCHA interstitial to
      attach a token.
    setHeader: This action sets a custom header but allow the request to
      continue to the customer backend.
    substitute: This action transparently serves a different page to an
      offending user.
  """

  allow = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1FirewallActionAllowAction', 1)
  block = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1FirewallActionBlockAction', 2)
  includeRecaptchaScript = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1FirewallActionIncludeRecaptchaScriptAction', 3)
  redirect = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1FirewallActionRedirectAction', 4)
  setHeader = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1FirewallActionSetHeaderAction', 5)
  substitute = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1FirewallActionSubstituteAction', 6)


class GoogleCloudRecaptchaenterpriseV1FirewallActionAllowAction(_messages.Message):
  r"""An allow action continues processing a request unimpeded."""


class GoogleCloudRecaptchaenterpriseV1FirewallActionBlockAction(_messages.Message):
  r"""A block action serves an HTTP error code a prevents the request from
  hitting the backend.
  """



class GoogleCloudRecaptchaenterpriseV1FirewallActionIncludeRecaptchaScriptAction(_messages.Message):
  r"""An include reCAPTCHA script action involves injecting reCAPTCHA
  JavaScript code into the HTML returned by the site backend. This reCAPTCHA
  script is tasked with collecting user signals on the requested web page,
  issuing tokens as a cookie within the site domain, and enabling their
  utilization in subsequent page requests.
  """



class GoogleCloudRecaptchaenterpriseV1FirewallActionRedirectAction(_messages.Message):
  r"""A redirect action returns a 307 (temporary redirect) response, pointing
  the user to a reCAPTCHA interstitial page to attach a token.
  """



class GoogleCloudRecaptchaenterpriseV1FirewallActionSetHeaderAction(_messages.Message):
  r"""A set header action sets a header and forwards the request to the
  backend. This can be used to trigger custom protection implemented on the
  backend.

  Fields:
    key: Optional. The header key to set in the request to the backend server.
    value: Optional. The header value to set in the request to the backend
      server.
  """

  key = _messages.StringField(1)
  value = _messages.StringField(2)


class GoogleCloudRecaptchaenterpriseV1FirewallActionSubstituteAction(_messages.Message):
  r"""A substitute action transparently serves a different page than the one
  requested.

  Fields:
    path: Optional. The address to redirect to. The target is a relative path
      in the current host. Example: "/blog/404.html".
  """

  path = _messages.StringField(1)


class GoogleCloudRecaptchaenterpriseV1FirewallPolicy(_messages.Message):
  r"""A FirewallPolicy represents a single matching pattern and resulting
  actions to take.

  Fields:
    actions: Optional. The actions that the caller should take regarding user
      access. There should be at most one terminal action. A terminal action
      is any action that forces a response, such as `AllowAction`,
      `BlockAction` or `SubstituteAction`. Zero or more non-terminal actions
      such as `SetHeader` might be specified. A single policy can contain up
      to 16 actions.
    condition: Optional. A CEL (Common Expression Language) conditional
      expression that specifies if this policy applies to an incoming user
      request. If this condition evaluates to true and the requested path
      matched the path pattern, the associated actions should be executed by
      the caller. The condition string is checked for CEL syntax correctness
      on creation. For more information, see the [CEL
      spec](https://github.com/google/cel-spec) and its [language
      definition](https://github.com/google/cel-
      spec/blob/master/doc/langdef.md). A condition has a max length of 500
      characters.
    description: Optional. A description of what this policy aims to achieve,
      for convenience purposes. The description can at most include 256 UTF-8
      characters.
    name: Identifier. The resource name for the FirewallPolicy in the format
      `projects/{project}/firewallpolicies/{firewallpolicy}`.
    path: Optional. The path for which this policy applies, specified as a
      glob pattern. For more information on glob, see the [manual
      page](https://man7.org/linux/man-pages/man7/glob.7.html). A path has a
      max length of 200 characters.
  """

  actions = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1FirewallAction', 1, repeated=True)
  condition = _messages.StringField(2)
  description = _messages.StringField(3)
  name = _messages.StringField(4)
  path = _messages.StringField(5)


class GoogleCloudRecaptchaenterpriseV1FirewallPolicyAssessment(_messages.Message):
  r"""Policy config assessment.

  Fields:
    error: Output only. If the processing of a policy config fails, an error
      is populated and the firewall_policy is left empty.
    firewallPolicy: Output only. The policy that matched the request. If more
      than one policy may match, this is the first match. If no policy matches
      the incoming request, the policy field is left empty.
  """

  error = _messages.MessageField('GoogleRpcStatus', 1)
  firewallPolicy = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1FirewallPolicy', 2)


class GoogleCloudRecaptchaenterpriseV1FraudPreventionAssessment(_messages.Message):
  r"""Assessment for Fraud Prevention.

  Fields:
    behavioralTrustVerdict: Output only. Assessment of this transaction for
      behavioral trust.
    cardTestingVerdict: Output only. Assessment of this transaction for risk
      of being part of a card testing attack.
    riskReasons: Output only. Reasons why the transaction is probably
      fraudulent and received a high transaction risk score.
    stolenInstrumentVerdict: Output only. Assessment of this transaction for
      risk of a stolen instrument.
    transactionRisk: Output only. Probability of this transaction being
      fraudulent. Summarizes the combined risk of attack vectors below. Values
      are from 0.0 (lowest) to 1.0 (highest).
  """

  behavioralTrustVerdict = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1FraudPreventionAssessmentBehavioralTrustVerdict', 1)
  cardTestingVerdict = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1FraudPreventionAssessmentCardTestingVerdict', 2)
  riskReasons = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1FraudPreventionAssessmentRiskReason', 3, repeated=True)
  stolenInstrumentVerdict = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1FraudPreventionAssessmentStolenInstrumentVerdict', 4)
  transactionRisk = _messages.FloatField(5, variant=_messages.Variant.FLOAT)


class GoogleCloudRecaptchaenterpriseV1FraudPreventionAssessmentBehavioralTrustVerdict(_messages.Message):
  r"""Information about behavioral trust of the transaction.

  Fields:
    trust: Output only. Probability of this transaction attempt being executed
      in a behaviorally trustworthy way. Values are from 0.0 (lowest) to 1.0
      (highest).
  """

  trust = _messages.FloatField(1, variant=_messages.Variant.FLOAT)


class GoogleCloudRecaptchaenterpriseV1FraudPreventionAssessmentCardTestingVerdict(_messages.Message):
  r"""Information about card testing fraud, where an adversary is testing
  fraudulently obtained cards or brute forcing their details.

  Fields:
    risk: Output only. Probability of this transaction attempt being part of a
      card testing attack. Values are from 0.0 (lowest) to 1.0 (highest).
  """

  risk = _messages.FloatField(1, variant=_messages.Variant.FLOAT)


class GoogleCloudRecaptchaenterpriseV1FraudPreventionAssessmentRiskReason(_messages.Message):
  r"""Risk reasons applicable to the Fraud Prevention assessment.

  Enums:
    ReasonValueValuesEnum: Output only. Risk reasons applicable to the Fraud
      Prevention assessment.

  Fields:
    reason: Output only. Risk reasons applicable to the Fraud Prevention
      assessment.
  """

  class ReasonValueValuesEnum(_messages.Enum):
    r"""Output only. Risk reasons applicable to the Fraud Prevention
    assessment.

    Values:
      REASON_UNSPECIFIED: Default unspecified type.
      HIGH_TRANSACTION_VELOCITY: A suspiciously high number of recent
        transactions have used identifiers present in this transaction.
      EXCESSIVE_ENUMERATION_PATTERN: User is cycling through a suspiciously
        large number of identifiers, suggesting enumeration or validation
        attacks within a potential fraud network.
      SHORT_IDENTITY_HISTORY: User has a short history or no history in the
        reCAPTCHA network, suggesting the possibility of synthetic identity
        generation.
      GEOLOCATION_DISCREPANCY: Identifiers used in this transaction originate
        from an unusual or conflicting set of geolocations.
      ASSOCIATED_WITH_FRAUD_CLUSTER: This transaction is linked to a cluster
        of known fraudulent activity.
    """
    REASON_UNSPECIFIED = 0
    HIGH_TRANSACTION_VELOCITY = 1
    EXCESSIVE_ENUMERATION_PATTERN = 2
    SHORT_IDENTITY_HISTORY = 3
    GEOLOCATION_DISCREPANCY = 4
    ASSOCIATED_WITH_FRAUD_CLUSTER = 5

  reason = _messages.EnumField('ReasonValueValuesEnum', 1)


class GoogleCloudRecaptchaenterpriseV1FraudPreventionAssessmentStolenInstrumentVerdict(_messages.Message):
  r"""Information about stolen instrument fraud, where the user is not the
  legitimate owner of the instrument being used for the purchase.

  Fields:
    risk: Output only. Probability of this transaction being executed with a
      stolen instrument. Values are from 0.0 (lowest) to 1.0 (highest).
  """

  risk = _messages.FloatField(1, variant=_messages.Variant.FLOAT)


class GoogleCloudRecaptchaenterpriseV1FraudSignals(_messages.Message):
  r"""Fraud signals describing users and cards involved in the transaction.

  Fields:
    cardSignals: Output only. Signals describing the payment card or cards
      used in this transaction.
    userSignals: Output only. Signals describing the end user in this
      transaction.
  """

  cardSignals = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1FraudSignalsCardSignals', 1)
  userSignals = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1FraudSignalsUserSignals', 2)


class GoogleCloudRecaptchaenterpriseV1FraudSignalsCardSignals(_messages.Message):
  r"""Signals describing the payment card used in this transaction.

  Enums:
    CardLabelsValueListEntryValuesEnum:

  Fields:
    cardLabels: Output only. The labels for the payment card in this
      transaction.
  """

  class CardLabelsValueListEntryValuesEnum(_messages.Enum):
    r"""CardLabelsValueListEntryValuesEnum enum type.

    Values:
      CARD_LABEL_UNSPECIFIED: No label specified.
      PREPAID: This card has been detected as prepaid.
      VIRTUAL: This card has been detected as virtual, such as a card number
        generated for a single transaction or merchant.
      UNEXPECTED_LOCATION: This card has been detected as being used in an
        unexpected geographic location.
    """
    CARD_LABEL_UNSPECIFIED = 0
    PREPAID = 1
    VIRTUAL = 2
    UNEXPECTED_LOCATION = 3

  cardLabels = _messages.EnumField('CardLabelsValueListEntryValuesEnum', 1, repeated=True)


class GoogleCloudRecaptchaenterpriseV1FraudSignalsUserSignals(_messages.Message):
  r"""Signals describing the user involved in this transaction.

  Fields:
    activeDaysLowerBound: Output only. This user (based on email, phone, and
      other identifiers) has been seen on the internet for at least this
      number of days.
    syntheticRisk: Output only. Likelihood (from 0.0 to 1.0) this user
      includes synthetic components in their identity, such as a randomly
      generated email address, temporary phone number, or fake shipping
      address.
  """

  activeDaysLowerBound = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  syntheticRisk = _messages.FloatField(2, variant=_messages.Variant.FLOAT)


class GoogleCloudRecaptchaenterpriseV1IOSKeySettings(_messages.Message):
  r"""Settings specific to keys that can be used by iOS apps.

  Fields:
    allowAllBundleIds: Optional. If set to true, allowed_bundle_ids are not
      enforced.
    allowedBundleIds: Optional. iOS bundle ids of apps allowed to use the key.
      Example: 'com.companyname.productname.appname'
    appleDeveloperId: Optional. Apple Developer account details for the app
      that is protected by the reCAPTCHA Key. reCAPTCHA leverages platform-
      specific checks like Apple App Attest and Apple DeviceCheck to protect
      your app from abuse. Providing these fields allows reCAPTCHA to get a
      better assessment of the integrity of your app.
  """

  allowAllBundleIds = _messages.BooleanField(1)
  allowedBundleIds = _messages.StringField(2, repeated=True)
  appleDeveloperId = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1AppleDeveloperId', 3)


class GoogleCloudRecaptchaenterpriseV1IpOverrideData(_messages.Message):
  r"""Information about the IP or IP range override.

  Enums:
    OverrideTypeValueValuesEnum: Required. Describes the type of IP override.

  Fields:
    ip: Required. The IP address to override (can be IPv4, IPv6 or CIDR). The
      IP override must be a valid IPv4 or IPv6 address, or a CIDR range. The
      IP override must be a public IP address. Example of IPv4: ***********
      Example of IPv6: 2001:0000:130F:0000:0000:09C0:876A:130B Example of IPv4
      with CIDR: ***********/24 Example of IPv6 with CIDR: 2001:0DB8:1234::/48
    overrideType: Required. Describes the type of IP override.
  """

  class OverrideTypeValueValuesEnum(_messages.Enum):
    r"""Required. Describes the type of IP override.

    Values:
      OVERRIDE_TYPE_UNSPECIFIED: Default override type that indicates this
        enum hasn't been specified.
      ALLOW: Allowlist the IP address; i.e. give a `risk_analysis.score` of
        0.9 for all valid assessments.
    """
    OVERRIDE_TYPE_UNSPECIFIED = 0
    ALLOW = 1

  ip = _messages.StringField(1)
  overrideType = _messages.EnumField('OverrideTypeValueValuesEnum', 2)


class GoogleCloudRecaptchaenterpriseV1Key(_messages.Message):
  r"""A key used to identify and configure applications (web and/or mobile)
  that use reCAPTCHA Enterprise.

  Messages:
    LabelsValue: Optional. See [Creating and managing labels]
      (https://cloud.google.com/recaptcha/docs/labels).

  Fields:
    androidSettings: Settings for keys that can be used by Android apps.
    createTime: Output only. The timestamp corresponding to the creation of
      this key.
    displayName: Required. Human-readable display name of this key. Modifiable
      by user.
    expressSettings: Settings for keys that can be used by reCAPTCHA Express.
    iosSettings: Settings for keys that can be used by iOS apps.
    labels: Optional. See [Creating and managing labels]
      (https://cloud.google.com/recaptcha/docs/labels).
    name: Identifier. The resource name for the Key in the format
      `projects/{project}/keys/{key}`.
    testingOptions: Optional. Options for user acceptance testing.
    wafSettings: Optional. Settings for WAF
    webSettings: Settings for keys that can be used by websites.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. See [Creating and managing labels]
    (https://cloud.google.com/recaptcha/docs/labels).

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  androidSettings = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1AndroidKeySettings', 1)
  createTime = _messages.StringField(2)
  displayName = _messages.StringField(3)
  expressSettings = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1ExpressKeySettings', 4)
  iosSettings = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1IOSKeySettings', 5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  testingOptions = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1TestingOptions', 8)
  wafSettings = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1WafSettings', 9)
  webSettings = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1WebKeySettings', 10)


class GoogleCloudRecaptchaenterpriseV1ListFirewallPoliciesResponse(_messages.Message):
  r"""Response to request to list firewall policies belonging to a project.

  Fields:
    firewallPolicies: Policy details.
    nextPageToken: Token to retrieve the next page of results. It is set to
      empty if no policies remain in results.
  """

  firewallPolicies = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1FirewallPolicy', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudRecaptchaenterpriseV1ListIpOverridesResponse(_messages.Message):
  r"""Response for ListIpOverrides.

  Fields:
    ipOverrides: IP Overrides details.
    nextPageToken: Token to retrieve the next page of results. If this field
      is empty, no keys remain in the results.
  """

  ipOverrides = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1IpOverrideData', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudRecaptchaenterpriseV1ListKeysResponse(_messages.Message):
  r"""Response to request to list keys in a project.

  Fields:
    keys: Key details.
    nextPageToken: Token to retrieve the next page of results. It is set to
      empty if no keys remain in results.
  """

  keys = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1Key', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudRecaptchaenterpriseV1ListRelatedAccountGroupMembershipsResponse(_messages.Message):
  r"""The response to a `ListRelatedAccountGroupMemberships` call.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    relatedAccountGroupMemberships: The memberships listed by the query.
  """

  nextPageToken = _messages.StringField(1)
  relatedAccountGroupMemberships = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1RelatedAccountGroupMembership', 2, repeated=True)


class GoogleCloudRecaptchaenterpriseV1ListRelatedAccountGroupsResponse(_messages.Message):
  r"""The response to a `ListRelatedAccountGroups` call.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    relatedAccountGroups: The groups of related accounts listed by the query.
  """

  nextPageToken = _messages.StringField(1)
  relatedAccountGroups = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1RelatedAccountGroup', 2, repeated=True)


class GoogleCloudRecaptchaenterpriseV1Metrics(_messages.Message):
  r"""Metrics for a single Key.

  Fields:
    challengeMetrics: Metrics are continuous and in order by dates, and in the
      granularity of day. Only challenge-based keys (CHECKBOX, INVISIBLE) have
      challenge-based data.
    name: Output only. Identifier. The name of the metrics, in the format
      `projects/{project}/keys/{key}/metrics`.
    scoreMetrics: Metrics are continuous and in order by dates, and in the
      granularity of day. All Key types should have score-based data.
    startTime: Inclusive start time aligned to a day in the
      America/Los_Angeles (Pacific) timezone.
  """

  challengeMetrics = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1ChallengeMetrics', 1, repeated=True)
  name = _messages.StringField(2)
  scoreMetrics = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1ScoreMetrics', 3, repeated=True)
  startTime = _messages.StringField(4)


class GoogleCloudRecaptchaenterpriseV1MigrateKeyRequest(_messages.Message):
  r"""The migrate key request message.

  Fields:
    skipBillingCheck: Optional. If true, skips the billing check. A reCAPTCHA
      Enterprise key or migrated key behaves differently than a reCAPTCHA
      (non-Enterprise version) key when you reach a quota limit (see
      https://cloud.google.com/recaptcha/quotas#quota_limit). To avoid any
      disruption of your usage, we check that a billing account is present. If
      your usage of reCAPTCHA is under the free quota, you can safely skip the
      billing check and proceed with the migration. See
      https://cloud.google.com/recaptcha/docs/billing-information.
  """

  skipBillingCheck = _messages.BooleanField(1)


class GoogleCloudRecaptchaenterpriseV1PhoneFraudAssessment(_messages.Message):
  r"""Assessment for Phone Fraud

  Fields:
    smsTollFraudVerdict: Output only. Assessment of this phone event for risk
      of SMS toll fraud.
  """

  smsTollFraudVerdict = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1SmsTollFraudVerdict', 1)


class GoogleCloudRecaptchaenterpriseV1PrivatePasswordLeakVerification(_messages.Message):
  r"""Private password leak verification info.

  Fields:
    encryptedLeakMatchPrefixes: Output only. List of prefixes of the encrypted
      potential password leaks that matched the given parameters. They must be
      compared with the client-side decryption prefix of
      `reencrypted_user_credentials_hash`
    encryptedUserCredentialsHash: Optional. Encrypted Scrypt hash of the
      canonicalized username+password. It is re-encrypted by the server and
      returned through `reencrypted_user_credentials_hash`.
    lookupHashPrefix: Required. Exactly 26-bit prefix of the SHA-256 hash of
      the canonicalized username. It is used to look up password leaks
      associated with that hash prefix.
    reencryptedUserCredentialsHash: Output only. Corresponds to the re-
      encryption of the `encrypted_user_credentials_hash` field. It is used to
      match potential password leaks within `encrypted_leak_match_prefixes`.
  """

  encryptedLeakMatchPrefixes = _messages.BytesField(1, repeated=True)
  encryptedUserCredentialsHash = _messages.BytesField(2)
  lookupHashPrefix = _messages.BytesField(3)
  reencryptedUserCredentialsHash = _messages.BytesField(4)


class GoogleCloudRecaptchaenterpriseV1RelatedAccountGroup(_messages.Message):
  r"""A group of related accounts.

  Fields:
    name: Required. Identifier. The resource name for the related account
      group in the format
      `projects/{project}/relatedaccountgroups/{related_account_group}`.
  """

  name = _messages.StringField(1)


class GoogleCloudRecaptchaenterpriseV1RelatedAccountGroupMembership(_messages.Message):
  r"""A membership in a group of related accounts.

  Fields:
    accountId: The unique stable account identifier of the member. The
      identifier corresponds to an `account_id` provided in a previous
      `CreateAssessment` or `AnnotateAssessment` call.
    hashedAccountId: Deprecated: use `account_id` instead. The unique stable
      hashed account identifier of the member. The identifier corresponds to a
      `hashed_account_id` provided in a previous `CreateAssessment` or
      `AnnotateAssessment` call.
    name: Required. Identifier. The resource name for this membership in the
      format `projects/{project}/relatedaccountgroups/{relatedaccountgroup}/me
      mberships/{membership}`.
  """

  accountId = _messages.StringField(1)
  hashedAccountId = _messages.BytesField(2)
  name = _messages.StringField(3)


class GoogleCloudRecaptchaenterpriseV1RemoveIpOverrideRequest(_messages.Message):
  r"""The RemoveIpOverride request message.

  Fields:
    ipOverrideData: Required. IP override to be removed from the key.
  """

  ipOverrideData = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1IpOverrideData', 1)


class GoogleCloudRecaptchaenterpriseV1RemoveIpOverrideResponse(_messages.Message):
  r"""Response for RemoveIpOverride."""


class GoogleCloudRecaptchaenterpriseV1ReorderFirewallPoliciesRequest(_messages.Message):
  r"""The reorder firewall policies request message.

  Fields:
    names: Required. A list containing all policy names, in the new order.
      Each name is in the format
      `projects/{project}/firewallpolicies/{firewallpolicy}`.
  """

  names = _messages.StringField(1, repeated=True)


class GoogleCloudRecaptchaenterpriseV1ReorderFirewallPoliciesResponse(_messages.Message):
  r"""The reorder firewall policies response message."""


class GoogleCloudRecaptchaenterpriseV1RetrieveLegacySecretKeyResponse(_messages.Message):
  r"""Secret key is used only in legacy reCAPTCHA. It must be used in a 3rd
  party integration with legacy reCAPTCHA.

  Fields:
    legacySecretKey: The secret key (also known as shared secret) authorizes
      communication between your application backend and the reCAPTCHA
      Enterprise server to create an assessment. The secret key needs to be
      kept safe for security purposes.
  """

  legacySecretKey = _messages.StringField(1)


class GoogleCloudRecaptchaenterpriseV1RiskAnalysis(_messages.Message):
  r"""Risk analysis result for an event.

  Enums:
    ChallengeValueValuesEnum: Output only. Challenge information for
      POLICY_BASED_CHALLENGE and INVISIBLE keys
    ReasonsValueListEntryValuesEnum:

  Fields:
    challenge: Output only. Challenge information for POLICY_BASED_CHALLENGE
      and INVISIBLE keys
    extendedVerdictReasons: Output only. Extended verdict reasons to be used
      for experimentation only. The set of possible reasons is subject to
      change.
    reasons: Output only. Reasons contributing to the risk analysis verdict.
    score: Output only. Legitimate event score from 0.0 to 1.0. (1.0 means
      very likely legitimate traffic while 0.0 means very likely non-
      legitimate traffic).
    verifiedBots: Output only. Bots with identities that have been verified by
      reCAPTCHA and detected in the event.
  """

  class ChallengeValueValuesEnum(_messages.Enum):
    r"""Output only. Challenge information for POLICY_BASED_CHALLENGE and
    INVISIBLE keys

    Values:
      CHALLENGE_UNSPECIFIED: Default unspecified type.
      NOCAPTCHA: No challenge was presented for solving.
      PASSED: A solution was submitted that was correct.
      FAILED: A solution was submitted that was incorrect or otherwise deemed
        suspicious.
    """
    CHALLENGE_UNSPECIFIED = 0
    NOCAPTCHA = 1
    PASSED = 2
    FAILED = 3

  class ReasonsValueListEntryValuesEnum(_messages.Enum):
    r"""ReasonsValueListEntryValuesEnum enum type.

    Values:
      CLASSIFICATION_REASON_UNSPECIFIED: Default unspecified type.
      AUTOMATION: Interactions matched the behavior of an automated agent.
      UNEXPECTED_ENVIRONMENT: The event originated from an illegitimate
        environment.
      TOO_MUCH_TRAFFIC: Traffic volume from the event source is higher than
        normal.
      UNEXPECTED_USAGE_PATTERNS: Interactions with the site were significantly
        different than expected patterns.
      LOW_CONFIDENCE_SCORE: Too little traffic has been received from this
        site thus far to generate quality risk analysis.
      SUSPECTED_CARDING: The request matches behavioral characteristics of a
        carding attack.
      SUSPECTED_CHARGEBACK: The request matches behavioral characteristics of
        chargebacks for fraud.
    """
    CLASSIFICATION_REASON_UNSPECIFIED = 0
    AUTOMATION = 1
    UNEXPECTED_ENVIRONMENT = 2
    TOO_MUCH_TRAFFIC = 3
    UNEXPECTED_USAGE_PATTERNS = 4
    LOW_CONFIDENCE_SCORE = 5
    SUSPECTED_CARDING = 6
    SUSPECTED_CHARGEBACK = 7

  challenge = _messages.EnumField('ChallengeValueValuesEnum', 1)
  extendedVerdictReasons = _messages.StringField(2, repeated=True)
  reasons = _messages.EnumField('ReasonsValueListEntryValuesEnum', 3, repeated=True)
  score = _messages.FloatField(4, variant=_messages.Variant.FLOAT)
  verifiedBots = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1Bot', 5, repeated=True)


class GoogleCloudRecaptchaenterpriseV1ScoreDistribution(_messages.Message):
  r"""Score distribution.

  Messages:
    ScoreBucketsValue: Map key is score value multiplied by 100. The scores
      are discrete values between [0, 1]. The maximum number of buckets is on
      order of a few dozen, but typically much lower (ie. 10).

  Fields:
    scoreBuckets: Map key is score value multiplied by 100. The scores are
      discrete values between [0, 1]. The maximum number of buckets is on
      order of a few dozen, but typically much lower (ie. 10).
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ScoreBucketsValue(_messages.Message):
    r"""Map key is score value multiplied by 100. The scores are discrete
    values between [0, 1]. The maximum number of buckets is on order of a few
    dozen, but typically much lower (ie. 10).

    Messages:
      AdditionalProperty: An additional property for a ScoreBucketsValue
        object.

    Fields:
      additionalProperties: Additional properties of type ScoreBucketsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ScoreBucketsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.IntegerField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  scoreBuckets = _messages.MessageField('ScoreBucketsValue', 1)


class GoogleCloudRecaptchaenterpriseV1ScoreMetrics(_messages.Message):
  r"""Metrics related to scoring.

  Messages:
    ActionMetricsValue: Action-based metrics. The map key is the action name
      which specified by the site owners at time of the "execute" client-side
      call.

  Fields:
    actionMetrics: Action-based metrics. The map key is the action name which
      specified by the site owners at time of the "execute" client-side call.
    overallMetrics: Aggregated score metrics for all traffic.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ActionMetricsValue(_messages.Message):
    r"""Action-based metrics. The map key is the action name which specified
    by the site owners at time of the "execute" client-side call.

    Messages:
      AdditionalProperty: An additional property for a ActionMetricsValue
        object.

    Fields:
      additionalProperties: Additional properties of type ActionMetricsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ActionMetricsValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudRecaptchaenterpriseV1ScoreDistribution attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1ScoreDistribution', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  actionMetrics = _messages.MessageField('ActionMetricsValue', 1)
  overallMetrics = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1ScoreDistribution', 2)


class GoogleCloudRecaptchaenterpriseV1SearchRelatedAccountGroupMembershipsRequest(_messages.Message):
  r"""The request message to search related account group memberships.

  Fields:
    accountId: Optional. The unique stable account identifier used to search
      connections. The identifier should correspond to an `account_id`
      provided in a previous `CreateAssessment` or `AnnotateAssessment` call.
      Either hashed_account_id or account_id must be set, but not both.
    hashedAccountId: Optional. Deprecated: use `account_id` instead. The
      unique stable hashed account identifier used to search connections. The
      identifier should correspond to a `hashed_account_id` provided in a
      previous `CreateAssessment` or `AnnotateAssessment` call. Either
      hashed_account_id or account_id must be set, but not both.
    pageSize: Optional. The maximum number of groups to return. The service
      might return fewer than this value. If unspecified, at most 50 groups
      are returned. The maximum value is 1000; values above 1000 are coerced
      to 1000.
    pageToken: Optional. A page token, received from a previous
      `SearchRelatedAccountGroupMemberships` call. Provide this to retrieve
      the subsequent page. When paginating, all other parameters provided to
      `SearchRelatedAccountGroupMemberships` must match the call that provided
      the page token.
  """

  accountId = _messages.StringField(1)
  hashedAccountId = _messages.BytesField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class GoogleCloudRecaptchaenterpriseV1SearchRelatedAccountGroupMembershipsResponse(_messages.Message):
  r"""The response to a `SearchRelatedAccountGroupMemberships` call.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    relatedAccountGroupMemberships: The queried memberships.
  """

  nextPageToken = _messages.StringField(1)
  relatedAccountGroupMemberships = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1RelatedAccountGroupMembership', 2, repeated=True)


class GoogleCloudRecaptchaenterpriseV1SmsTollFraudVerdict(_messages.Message):
  r"""Information about SMS toll fraud.

  Enums:
    ReasonsValueListEntryValuesEnum:

  Fields:
    reasons: Output only. Reasons contributing to the SMS toll fraud verdict.
    risk: Output only. Probability of an SMS event being fraudulent. Values
      are from 0.0 (lowest) to 1.0 (highest).
  """

  class ReasonsValueListEntryValuesEnum(_messages.Enum):
    r"""ReasonsValueListEntryValuesEnum enum type.

    Values:
      SMS_TOLL_FRAUD_REASON_UNSPECIFIED: Default unspecified reason
      INVALID_PHONE_NUMBER: The provided phone number was invalid
    """
    SMS_TOLL_FRAUD_REASON_UNSPECIFIED = 0
    INVALID_PHONE_NUMBER = 1

  reasons = _messages.EnumField('ReasonsValueListEntryValuesEnum', 1, repeated=True)
  risk = _messages.FloatField(2, variant=_messages.Variant.FLOAT)


class GoogleCloudRecaptchaenterpriseV1TestingOptions(_messages.Message):
  r"""Options for user acceptance testing.

  Enums:
    TestingChallengeValueValuesEnum: Optional. For challenge-based keys only
      (CHECKBOX, INVISIBLE), all challenge requests for this site return
      nocaptcha if NOCAPTCHA, or an unsolvable challenge if CHALLENGE.

  Fields:
    testingChallenge: Optional. For challenge-based keys only (CHECKBOX,
      INVISIBLE), all challenge requests for this site return nocaptcha if
      NOCAPTCHA, or an unsolvable challenge if CHALLENGE.
    testingScore: Optional. All assessments for this Key return this score.
      Must be between 0 (likely not legitimate) and 1 (likely legitimate)
      inclusive.
  """

  class TestingChallengeValueValuesEnum(_messages.Enum):
    r"""Optional. For challenge-based keys only (CHECKBOX, INVISIBLE), all
    challenge requests for this site return nocaptcha if NOCAPTCHA, or an
    unsolvable challenge if CHALLENGE.

    Values:
      TESTING_CHALLENGE_UNSPECIFIED: Perform the normal risk analysis and
        return either nocaptcha or a challenge depending on risk and trust
        factors.
      NOCAPTCHA: Challenge requests for this key always return a nocaptcha,
        which does not require a solution.
      UNSOLVABLE_CHALLENGE: Challenge requests for this key always return an
        unsolvable challenge.
    """
    TESTING_CHALLENGE_UNSPECIFIED = 0
    NOCAPTCHA = 1
    UNSOLVABLE_CHALLENGE = 2

  testingChallenge = _messages.EnumField('TestingChallengeValueValuesEnum', 1)
  testingScore = _messages.FloatField(2, variant=_messages.Variant.FLOAT)


class GoogleCloudRecaptchaenterpriseV1TokenProperties(_messages.Message):
  r"""Properties of the provided event token.

  Enums:
    InvalidReasonValueValuesEnum: Output only. Reason associated with the
      response when valid = false.

  Fields:
    action: Output only. Action name provided at token generation.
    androidPackageName: Output only. The name of the Android package with
      which the token was generated (Android keys only).
    createTime: Output only. The timestamp corresponding to the generation of
      the token.
    hostname: Output only. The hostname of the page on which the token was
      generated (Web keys only).
    invalidReason: Output only. Reason associated with the response when valid
      = false.
    iosBundleId: Output only. The ID of the iOS bundle with which the token
      was generated (iOS keys only).
    valid: Output only. Whether the provided user response token is valid.
      When valid = false, the reason could be specified in invalid_reason or
      it could also be due to a user failing to solve a challenge or a sitekey
      mismatch (i.e the sitekey used to generate the token was different than
      the one specified in the assessment).
  """

  class InvalidReasonValueValuesEnum(_messages.Enum):
    r"""Output only. Reason associated with the response when valid = false.

    Values:
      INVALID_REASON_UNSPECIFIED: Default unspecified type.
      UNKNOWN_INVALID_REASON: If the failure reason was not accounted for.
      MALFORMED: The provided user verification token was malformed.
      EXPIRED: The user verification token had expired.
      DUPE: The user verification had already been seen.
      MISSING: The user verification token was not present.
      BROWSER_ERROR: A retriable error (such as network failure) occurred on
        the browser. Could easily be simulated by an attacker.
    """
    INVALID_REASON_UNSPECIFIED = 0
    UNKNOWN_INVALID_REASON = 1
    MALFORMED = 2
    EXPIRED = 3
    DUPE = 4
    MISSING = 5
    BROWSER_ERROR = 6

  action = _messages.StringField(1)
  androidPackageName = _messages.StringField(2)
  createTime = _messages.StringField(3)
  hostname = _messages.StringField(4)
  invalidReason = _messages.EnumField('InvalidReasonValueValuesEnum', 5)
  iosBundleId = _messages.StringField(6)
  valid = _messages.BooleanField(7)


class GoogleCloudRecaptchaenterpriseV1TransactionData(_messages.Message):
  r"""Transaction data associated with a payment protected by reCAPTCHA
  Enterprise.

  Fields:
    billingAddress: Optional. Address associated with the payment method when
      applicable.
    cardBin: Optional. The Bank Identification Number - generally the first 6
      or 8 digits of the card.
    cardLastFour: Optional. The last four digits of the card.
    currencyCode: Optional. The currency code in ISO-4217 format.
    gatewayInfo: Optional. Information about the payment gateway's response to
      the transaction.
    items: Optional. Items purchased in this transaction.
    merchants: Optional. Information about the user or users fulfilling the
      transaction.
    paymentMethod: Optional. The payment method for the transaction. The
      allowed values are: * credit-card * debit-card * gift-card *
      processor-{name} (If a third-party is used, for example, processor-
      paypal) * custom-{name} (If an alternative method is used, for example,
      custom-crypto)
    shippingAddress: Optional. Destination address if this transaction
      involves shipping a physical item.
    shippingValue: Optional. The value of shipping in the specified currency.
      0 for free or no shipping.
    transactionId: Unique identifier for the transaction. This custom
      identifier can be used to reference this transaction in the future, for
      example, labeling a refund or chargeback event. Two attempts at the same
      transaction should use the same transaction id.
    user: Optional. Information about the user paying/initiating the
      transaction.
    value: Optional. The decimal value of the transaction in the specified
      currency.
  """

  billingAddress = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1TransactionDataAddress', 1)
  cardBin = _messages.StringField(2)
  cardLastFour = _messages.StringField(3)
  currencyCode = _messages.StringField(4)
  gatewayInfo = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1TransactionDataGatewayInfo', 5)
  items = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1TransactionDataItem', 6, repeated=True)
  merchants = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1TransactionDataUser', 7, repeated=True)
  paymentMethod = _messages.StringField(8)
  shippingAddress = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1TransactionDataAddress', 9)
  shippingValue = _messages.FloatField(10)
  transactionId = _messages.StringField(11)
  user = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1TransactionDataUser', 12)
  value = _messages.FloatField(13)


class GoogleCloudRecaptchaenterpriseV1TransactionDataAddress(_messages.Message):
  r"""Structured address format for billing and shipping addresses.

  Fields:
    address: Optional. The first lines of the address. The first line
      generally contains the street name and number, and further lines may
      include information such as an apartment number.
    administrativeArea: Optional. The state, province, or otherwise
      administrative area of the address.
    locality: Optional. The town/city of the address.
    postalCode: Optional. The postal or ZIP code of the address.
    recipient: Optional. The recipient name, potentially including information
      such as "care of".
    regionCode: Optional. The CLDR country/region of the address.
  """

  address = _messages.StringField(1, repeated=True)
  administrativeArea = _messages.StringField(2)
  locality = _messages.StringField(3)
  postalCode = _messages.StringField(4)
  recipient = _messages.StringField(5)
  regionCode = _messages.StringField(6)


class GoogleCloudRecaptchaenterpriseV1TransactionDataGatewayInfo(_messages.Message):
  r"""Details about the transaction from the gateway.

  Fields:
    avsResponseCode: Optional. AVS response code from the gateway (available
      only when reCAPTCHA Enterprise is called after authorization).
    cvvResponseCode: Optional. CVV response code from the gateway (available
      only when reCAPTCHA Enterprise is called after authorization).
    gatewayResponseCode: Optional. Gateway response code describing the state
      of the transaction.
    name: Optional. Name of the gateway service (for example, stripe, square,
      paypal).
  """

  avsResponseCode = _messages.StringField(1)
  cvvResponseCode = _messages.StringField(2)
  gatewayResponseCode = _messages.StringField(3)
  name = _messages.StringField(4)


class GoogleCloudRecaptchaenterpriseV1TransactionDataItem(_messages.Message):
  r"""Line items being purchased in this transaction.

  Fields:
    merchantAccountId: Optional. When a merchant is specified, its
      corresponding account_id. Necessary to populate marketplace-style
      transactions.
    name: Optional. The full name of the item.
    quantity: Optional. The quantity of this item that is being purchased.
    value: Optional. The value per item that the user is paying, in the
      transaction currency, after discounts.
  """

  merchantAccountId = _messages.StringField(1)
  name = _messages.StringField(2)
  quantity = _messages.IntegerField(3)
  value = _messages.FloatField(4)


class GoogleCloudRecaptchaenterpriseV1TransactionDataUser(_messages.Message):
  r"""Details about a user's account involved in the transaction.

  Fields:
    accountId: Optional. Unique account identifier for this user. If using
      account defender, this should match the hashed_account_id field.
      Otherwise, a unique and persistent identifier for this account.
    creationMs: Optional. The epoch milliseconds of the user's account
      creation.
    email: Optional. The email address of the user.
    emailVerified: Optional. Whether the email has been verified to be
      accessible by the user (OTP or similar).
    phoneNumber: Optional. The phone number of the user, with country code.
    phoneVerified: Optional. Whether the phone number has been verified to be
      accessible by the user (OTP or similar).
  """

  accountId = _messages.StringField(1)
  creationMs = _messages.IntegerField(2)
  email = _messages.StringField(3)
  emailVerified = _messages.BooleanField(4)
  phoneNumber = _messages.StringField(5)
  phoneVerified = _messages.BooleanField(6)


class GoogleCloudRecaptchaenterpriseV1TransactionEvent(_messages.Message):
  r"""Describes an event in the lifecycle of a payment transaction.

  Enums:
    EventTypeValueValuesEnum: Optional. The type of this transaction event.

  Fields:
    eventTime: Optional. Timestamp when this transaction event occurred;
      otherwise assumed to be the time of the API call.
    eventType: Optional. The type of this transaction event.
    reason: Optional. The reason or standardized code that corresponds with
      this transaction event, if one exists. For example, a CHARGEBACK event
      with code 6005.
    value: Optional. The value that corresponds with this transaction event,
      if one exists. For example, a refund event where $5.00 was refunded.
      Currency is obtained from the original transaction data.
  """

  class EventTypeValueValuesEnum(_messages.Enum):
    r"""Optional. The type of this transaction event.

    Values:
      TRANSACTION_EVENT_TYPE_UNSPECIFIED: Default, unspecified event type.
      MERCHANT_APPROVE: Indicates that the transaction is approved by the
        merchant. The accompanying reasons can include terms such as
        'INHOUSE', 'ACCERTIFY', 'CYBERSOURCE', or 'MANUAL_REVIEW'.
      MERCHANT_DENY: Indicates that the transaction is denied and concluded
        due to risks detected by the merchant. The accompanying reasons can
        include terms such as 'INHOUSE', 'ACCERTIFY', 'CYBERSOURCE', or
        'MANUAL_REVIEW'.
      MANUAL_REVIEW: Indicates that the transaction is being evaluated by a
        human, due to suspicion or risk.
      AUTHORIZATION: Indicates that the authorization attempt with the card
        issuer succeeded.
      AUTHORIZATION_DECLINE: Indicates that the authorization attempt with the
        card issuer failed. The accompanying reasons can include Visa's '54'
        indicating that the card is expired, or '82' indicating that the CVV
        is incorrect.
      PAYMENT_CAPTURE: Indicates that the transaction is completed because the
        funds were settled.
      PAYMENT_CAPTURE_DECLINE: Indicates that the transaction could not be
        completed because the funds were not settled.
      CANCEL: Indicates that the transaction has been canceled. Specify the
        reason for the cancellation. For example, 'INSUFFICIENT_INVENTORY'.
      CHARGEBACK_INQUIRY: Indicates that the merchant has received a
        chargeback inquiry due to fraud for the transaction, requesting
        additional information before a fraud chargeback is officially issued
        and a formal chargeback notification is sent.
      CHARGEBACK_ALERT: Indicates that the merchant has received a chargeback
        alert due to fraud for the transaction. The process of resolving the
        dispute without involving the payment network is started.
      FRAUD_NOTIFICATION: Indicates that a fraud notification is issued for
        the transaction, sent by the payment instrument's issuing bank because
        the transaction appears to be fraudulent. We recommend including TC40
        or SAFE data in the `reason` field for this event type. For partial
        chargebacks, we recommend that you include an amount in the `value`
        field.
      CHARGEBACK: Indicates that the merchant is informed by the payment
        network that the transaction has entered the chargeback process due to
        fraud. Reason code examples include Discover's '6005' and '6041'. For
        partial chargebacks, we recommend that you include an amount in the
        `value` field.
      CHARGEBACK_REPRESENTMENT: Indicates that the transaction has entered the
        chargeback process due to fraud, and that the merchant has chosen to
        enter representment. Reason examples include Discover's '6005' and
        '6041'. For partial chargebacks, we recommend that you include an
        amount in the `value` field.
      CHARGEBACK_REVERSE: Indicates that the transaction has had a fraud
        chargeback which was illegitimate and was reversed as a result. For
        partial chargebacks, we recommend that you include an amount in the
        `value` field.
      REFUND_REQUEST: Indicates that the merchant has received a refund for a
        completed transaction. For partial refunds, we recommend that you
        include an amount in the `value` field. Reason example: 'TAX_EXEMPT'
        (partial refund of exempt tax)
      REFUND_DECLINE: Indicates that the merchant has received a refund
        request for this transaction, but that they have declined it. For
        partial refunds, we recommend that you include an amount in the
        `value` field. Reason example: 'TAX_EXEMPT' (partial refund of exempt
        tax)
      REFUND: Indicates that the completed transaction was refunded by the
        merchant. For partial refunds, we recommend that you include an amount
        in the `value` field. Reason example: 'TAX_EXEMPT' (partial refund of
        exempt tax)
      REFUND_REVERSE: Indicates that the completed transaction was refunded by
        the merchant, and that this refund was reversed. For partial refunds,
        we recommend that you include an amount in the `value` field.
    """
    TRANSACTION_EVENT_TYPE_UNSPECIFIED = 0
    MERCHANT_APPROVE = 1
    MERCHANT_DENY = 2
    MANUAL_REVIEW = 3
    AUTHORIZATION = 4
    AUTHORIZATION_DECLINE = 5
    PAYMENT_CAPTURE = 6
    PAYMENT_CAPTURE_DECLINE = 7
    CANCEL = 8
    CHARGEBACK_INQUIRY = 9
    CHARGEBACK_ALERT = 10
    FRAUD_NOTIFICATION = 11
    CHARGEBACK = 12
    CHARGEBACK_REPRESENTMENT = 13
    CHARGEBACK_REVERSE = 14
    REFUND_REQUEST = 15
    REFUND_DECLINE = 16
    REFUND = 17
    REFUND_REVERSE = 18

  eventTime = _messages.StringField(1)
  eventType = _messages.EnumField('EventTypeValueValuesEnum', 2)
  reason = _messages.StringField(3)
  value = _messages.FloatField(4)


class GoogleCloudRecaptchaenterpriseV1UserId(_messages.Message):
  r"""An identifier associated with a user.

  Fields:
    email: Optional. An email address.
    phoneNumber: Optional. A phone number. Should use the E.164 format.
    username: Optional. A unique username, if different from all the other
      identifiers and `account_id` that are provided. Can be a unique login
      handle or display name for a user.
  """

  email = _messages.StringField(1)
  phoneNumber = _messages.StringField(2)
  username = _messages.StringField(3)


class GoogleCloudRecaptchaenterpriseV1UserInfo(_messages.Message):
  r"""User information associated with a request protected by reCAPTCHA
  Enterprise.

  Fields:
    accountId: Optional. For logged-in requests or login/registration
      requests, the unique account identifier associated with this user. You
      can use the username if it is stable (meaning it is the same for every
      request associated with the same user), or any stable user ID of your
      choice. Leave blank for non logged-in actions or guest checkout.
    createAccountTime: Optional. Creation time for this account associated
      with this user. Leave blank for non logged-in actions, guest checkout,
      or when there is no account associated with the current user.
    userIds: Optional. Identifiers associated with this user or request.
  """

  accountId = _messages.StringField(1)
  createAccountTime = _messages.StringField(2)
  userIds = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1UserId', 3, repeated=True)


class GoogleCloudRecaptchaenterpriseV1WafSettings(_messages.Message):
  r"""Settings specific to keys that can be used for WAF (Web Application
  Firewall).

  Enums:
    WafFeatureValueValuesEnum: Required. The WAF feature for which this key is
      enabled.
    WafServiceValueValuesEnum: Required. The WAF service that uses this key.

  Fields:
    wafFeature: Required. The WAF feature for which this key is enabled.
    wafService: Required. The WAF service that uses this key.
  """

  class WafFeatureValueValuesEnum(_messages.Enum):
    r"""Required. The WAF feature for which this key is enabled.

    Values:
      WAF_FEATURE_UNSPECIFIED: Undefined feature.
      CHALLENGE_PAGE: Redirects suspicious traffic to reCAPTCHA.
      SESSION_TOKEN: Use reCAPTCHA session-tokens to protect the whole user
        session on the site's domain.
      ACTION_TOKEN: Use reCAPTCHA action-tokens to protect user actions.
      EXPRESS: Deprecated: Use `express_settings` instead.
    """
    WAF_FEATURE_UNSPECIFIED = 0
    CHALLENGE_PAGE = 1
    SESSION_TOKEN = 2
    ACTION_TOKEN = 3
    EXPRESS = 4

  class WafServiceValueValuesEnum(_messages.Enum):
    r"""Required. The WAF service that uses this key.

    Values:
      WAF_SERVICE_UNSPECIFIED: Undefined WAF
      CA: Cloud Armor
      FASTLY: Fastly
      CLOUDFLARE: Cloudflare
      AKAMAI: Akamai
    """
    WAF_SERVICE_UNSPECIFIED = 0
    CA = 1
    FASTLY = 2
    CLOUDFLARE = 3
    AKAMAI = 4

  wafFeature = _messages.EnumField('WafFeatureValueValuesEnum', 1)
  wafService = _messages.EnumField('WafServiceValueValuesEnum', 2)


class GoogleCloudRecaptchaenterpriseV1WebKeySettings(_messages.Message):
  r"""Settings specific to keys that can be used by websites.

  Enums:
    ChallengeSecurityPreferenceValueValuesEnum: Optional. Settings for the
      frequency and difficulty at which this key triggers captcha challenges.
      This should only be specified for `IntegrationType` CHECKBOX, INVISIBLE
      or POLICY_BASED_CHALLENGE.
    IntegrationTypeValueValuesEnum: Required. Describes how this key is
      integrated with the website.

  Fields:
    allowAllDomains: Optional. If set to true, it means allowed_domains are
      not enforced.
    allowAmpTraffic: Optional. If set to true, the key can be used on AMP
      (Accelerated Mobile Pages) websites. This is supported only for the
      SCORE integration type.
    allowedDomains: Optional. Domains or subdomains of websites allowed to use
      the key. All subdomains of an allowed domain are automatically allowed.
      A valid domain requires a host and must not include any path, port,
      query or fragment. Examples: 'example.com' or 'subdomain.example.com'
    challengeSecurityPreference: Optional. Settings for the frequency and
      difficulty at which this key triggers captcha challenges. This should
      only be specified for `IntegrationType` CHECKBOX, INVISIBLE or
      POLICY_BASED_CHALLENGE.
    challengeSettings: Optional. Challenge settings.
    integrationType: Required. Describes how this key is integrated with the
      website.
  """

  class ChallengeSecurityPreferenceValueValuesEnum(_messages.Enum):
    r"""Optional. Settings for the frequency and difficulty at which this key
    triggers captcha challenges. This should only be specified for
    `IntegrationType` CHECKBOX, INVISIBLE or POLICY_BASED_CHALLENGE.

    Values:
      CHALLENGE_SECURITY_PREFERENCE_UNSPECIFIED: Default type that indicates
        this enum hasn't been specified.
      USABILITY: Key tends to show fewer and easier challenges.
      BALANCE: Key tends to show balanced (in amount and difficulty)
        challenges.
      SECURITY: Key tends to show more and harder challenges.
    """
    CHALLENGE_SECURITY_PREFERENCE_UNSPECIFIED = 0
    USABILITY = 1
    BALANCE = 2
    SECURITY = 3

  class IntegrationTypeValueValuesEnum(_messages.Enum):
    r"""Required. Describes how this key is integrated with the website.

    Values:
      INTEGRATION_TYPE_UNSPECIFIED: Default type that indicates this enum
        hasn't been specified. This is not a valid IntegrationType, one of the
        other types must be specified instead.
      SCORE: Only used to produce scores. It doesn't display the "I'm not a
        robot" checkbox and never shows captcha challenges.
      CHECKBOX: Displays the "I'm not a robot" checkbox and may show captcha
        challenges after it is checked.
      INVISIBLE: Doesn't display the "I'm not a robot" checkbox, but may show
        captcha challenges after risk analysis.
      SCORE_AND_CHALLENGE: Displays a visual challenge or not depending on the
        user risk analysis score.
      POLICY_BASED_CHALLENGE: Displays a visual challenge or not depending on
        the user risk analysis score.
    """
    INTEGRATION_TYPE_UNSPECIFIED = 0
    SCORE = 1
    CHECKBOX = 2
    INVISIBLE = 3
    SCORE_AND_CHALLENGE = 4
    POLICY_BASED_CHALLENGE = 5

  allowAllDomains = _messages.BooleanField(1)
  allowAmpTraffic = _messages.BooleanField(2)
  allowedDomains = _messages.StringField(3, repeated=True)
  challengeSecurityPreference = _messages.EnumField('ChallengeSecurityPreferenceValueValuesEnum', 4)
  challengeSettings = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1WebKeySettingsChallengeSettings', 5)
  integrationType = _messages.EnumField('IntegrationTypeValueValuesEnum', 6)


class GoogleCloudRecaptchaenterpriseV1WebKeySettingsActionSettings(_messages.Message):
  r"""Per-action challenge settings.

  Fields:
    scoreThreshold: Required. A challenge is triggered if the end-user score
      is below that threshold. Value must be between 0 and 1 (inclusive).
  """

  scoreThreshold = _messages.FloatField(1, variant=_messages.Variant.FLOAT)


class GoogleCloudRecaptchaenterpriseV1WebKeySettingsChallengeSettings(_messages.Message):
  r"""Settings for POLICY_BASED_CHALLENGE keys to control when a challenge is
  triggered.

  Messages:
    ActionSettingsValue: Optional. The action to score threshold map. The
      action name should be the same as the action name passed in the `data-
      action` attribute (see https://cloud.google.com/recaptcha/docs/actions-
      website). Action names are case-insensitive. There is a maximum of 100
      action settings. An action name has a maximum length of 100.

  Fields:
    actionSettings: Optional. The action to score threshold map. The action
      name should be the same as the action name passed in the `data-action`
      attribute (see https://cloud.google.com/recaptcha/docs/actions-website).
      Action names are case-insensitive. There is a maximum of 100 action
      settings. An action name has a maximum length of 100.
    defaultSettings: Required. Defines when a challenge is triggered (unless
      the default threshold is overridden for the given action, see
      `action_settings`).
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ActionSettingsValue(_messages.Message):
    r"""Optional. The action to score threshold map. The action name should be
    the same as the action name passed in the `data-action` attribute (see
    https://cloud.google.com/recaptcha/docs/actions-website). Action names are
    case-insensitive. There is a maximum of 100 action settings. An action
    name has a maximum length of 100.

    Messages:
      AdditionalProperty: An additional property for a ActionSettingsValue
        object.

    Fields:
      additionalProperties: Additional properties of type ActionSettingsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ActionSettingsValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudRecaptchaenterpriseV1WebKeySettingsActionSettings
          attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1WebKeySettingsActionSettings', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  actionSettings = _messages.MessageField('ActionSettingsValue', 1)
  defaultSettings = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1WebKeySettingsActionSettings', 2)


class GoogleProtobufEmpty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class GoogleRpcStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class RecaptchaenterpriseProjectsAssessmentsAnnotateRequest(_messages.Message):
  r"""A RecaptchaenterpriseProjectsAssessmentsAnnotateRequest object.

  Fields:
    googleCloudRecaptchaenterpriseV1AnnotateAssessmentRequest: A
      GoogleCloudRecaptchaenterpriseV1AnnotateAssessmentRequest resource to be
      passed as the request body.
    name: Required. The resource name of the Assessment, in the format
      `projects/{project}/assessments/{assessment}`.
  """

  googleCloudRecaptchaenterpriseV1AnnotateAssessmentRequest = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1AnnotateAssessmentRequest', 1)
  name = _messages.StringField(2, required=True)


class RecaptchaenterpriseProjectsAssessmentsCreateRequest(_messages.Message):
  r"""A RecaptchaenterpriseProjectsAssessmentsCreateRequest object.

  Fields:
    googleCloudRecaptchaenterpriseV1Assessment: A
      GoogleCloudRecaptchaenterpriseV1Assessment resource to be passed as the
      request body.
    parent: Required. The name of the project in which the assessment is
      created, in the format `projects/{project}`.
  """

  googleCloudRecaptchaenterpriseV1Assessment = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1Assessment', 1)
  parent = _messages.StringField(2, required=True)


class RecaptchaenterpriseProjectsFirewallpoliciesCreateRequest(_messages.Message):
  r"""A RecaptchaenterpriseProjectsFirewallpoliciesCreateRequest object.

  Fields:
    googleCloudRecaptchaenterpriseV1FirewallPolicy: A
      GoogleCloudRecaptchaenterpriseV1FirewallPolicy resource to be passed as
      the request body.
    parent: Required. The name of the project this policy applies to, in the
      format `projects/{project}`.
  """

  googleCloudRecaptchaenterpriseV1FirewallPolicy = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1FirewallPolicy', 1)
  parent = _messages.StringField(2, required=True)


class RecaptchaenterpriseProjectsFirewallpoliciesDeleteRequest(_messages.Message):
  r"""A RecaptchaenterpriseProjectsFirewallpoliciesDeleteRequest object.

  Fields:
    name: Required. The name of the policy to be deleted, in the format
      `projects/{project}/firewallpolicies/{firewallpolicy}`.
  """

  name = _messages.StringField(1, required=True)


class RecaptchaenterpriseProjectsFirewallpoliciesGetRequest(_messages.Message):
  r"""A RecaptchaenterpriseProjectsFirewallpoliciesGetRequest object.

  Fields:
    name: Required. The name of the requested policy, in the format
      `projects/{project}/firewallpolicies/{firewallpolicy}`.
  """

  name = _messages.StringField(1, required=True)


class RecaptchaenterpriseProjectsFirewallpoliciesListRequest(_messages.Message):
  r"""A RecaptchaenterpriseProjectsFirewallpoliciesListRequest object.

  Fields:
    pageSize: Optional. The maximum number of policies to return. Default is
      10. Max limit is 1000.
    pageToken: Optional. The next_page_token value returned from a previous.
      ListFirewallPoliciesRequest, if any.
    parent: Required. The name of the project to list the policies for, in the
      format `projects/{project}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class RecaptchaenterpriseProjectsFirewallpoliciesPatchRequest(_messages.Message):
  r"""A RecaptchaenterpriseProjectsFirewallpoliciesPatchRequest object.

  Fields:
    googleCloudRecaptchaenterpriseV1FirewallPolicy: A
      GoogleCloudRecaptchaenterpriseV1FirewallPolicy resource to be passed as
      the request body.
    name: Identifier. The resource name for the FirewallPolicy in the format
      `projects/{project}/firewallpolicies/{firewallpolicy}`.
    updateMask: Optional. The mask to control which fields of the policy get
      updated. If the mask is not present, all fields are updated.
  """

  googleCloudRecaptchaenterpriseV1FirewallPolicy = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1FirewallPolicy', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class RecaptchaenterpriseProjectsFirewallpoliciesReorderRequest(_messages.Message):
  r"""A RecaptchaenterpriseProjectsFirewallpoliciesReorderRequest object.

  Fields:
    googleCloudRecaptchaenterpriseV1ReorderFirewallPoliciesRequest: A
      GoogleCloudRecaptchaenterpriseV1ReorderFirewallPoliciesRequest resource
      to be passed as the request body.
    parent: Required. The name of the project to list the policies for, in the
      format `projects/{project}`.
  """

  googleCloudRecaptchaenterpriseV1ReorderFirewallPoliciesRequest = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1ReorderFirewallPoliciesRequest', 1)
  parent = _messages.StringField(2, required=True)


class RecaptchaenterpriseProjectsKeysAddIpOverrideRequest(_messages.Message):
  r"""A RecaptchaenterpriseProjectsKeysAddIpOverrideRequest object.

  Fields:
    googleCloudRecaptchaenterpriseV1AddIpOverrideRequest: A
      GoogleCloudRecaptchaenterpriseV1AddIpOverrideRequest resource to be
      passed as the request body.
    name: Required. The name of the key to which the IP override is added, in
      the format `projects/{project}/keys/{key}`.
  """

  googleCloudRecaptchaenterpriseV1AddIpOverrideRequest = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1AddIpOverrideRequest', 1)
  name = _messages.StringField(2, required=True)


class RecaptchaenterpriseProjectsKeysCreateRequest(_messages.Message):
  r"""A RecaptchaenterpriseProjectsKeysCreateRequest object.

  Fields:
    googleCloudRecaptchaenterpriseV1Key: A GoogleCloudRecaptchaenterpriseV1Key
      resource to be passed as the request body.
    parent: Required. The name of the project in which the key is created, in
      the format `projects/{project}`.
  """

  googleCloudRecaptchaenterpriseV1Key = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1Key', 1)
  parent = _messages.StringField(2, required=True)


class RecaptchaenterpriseProjectsKeysDeleteRequest(_messages.Message):
  r"""A RecaptchaenterpriseProjectsKeysDeleteRequest object.

  Fields:
    name: Required. The name of the key to be deleted, in the format
      `projects/{project}/keys/{key}`.
  """

  name = _messages.StringField(1, required=True)


class RecaptchaenterpriseProjectsKeysGetMetricsRequest(_messages.Message):
  r"""A RecaptchaenterpriseProjectsKeysGetMetricsRequest object.

  Fields:
    name: Required. The name of the requested metrics, in the format
      `projects/{project}/keys/{key}/metrics`.
  """

  name = _messages.StringField(1, required=True)


class RecaptchaenterpriseProjectsKeysGetRequest(_messages.Message):
  r"""A RecaptchaenterpriseProjectsKeysGetRequest object.

  Fields:
    name: Required. The name of the requested key, in the format
      `projects/{project}/keys/{key}`.
  """

  name = _messages.StringField(1, required=True)


class RecaptchaenterpriseProjectsKeysListIpOverridesRequest(_messages.Message):
  r"""A RecaptchaenterpriseProjectsKeysListIpOverridesRequest object.

  Fields:
    pageSize: Optional. The maximum number of overrides to return. Default is
      10. Max limit is 100. If the number of overrides is less than the
      page_size, all overrides are returned. If the page size is more than
      100, it is coerced to 100.
    pageToken: Optional. The next_page_token value returned from a previous
      ListIpOverridesRequest, if any.
    parent: Required. The parent key for which the IP overrides are listed, in
      the format `projects/{project}/keys/{key}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class RecaptchaenterpriseProjectsKeysListRequest(_messages.Message):
  r"""A RecaptchaenterpriseProjectsKeysListRequest object.

  Fields:
    pageSize: Optional. The maximum number of keys to return. Default is 10.
      Max limit is 1000.
    pageToken: Optional. The next_page_token value returned from a previous.
      ListKeysRequest, if any.
    parent: Required. The name of the project that contains the keys that is
      listed, in the format `projects/{project}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class RecaptchaenterpriseProjectsKeysMigrateRequest(_messages.Message):
  r"""A RecaptchaenterpriseProjectsKeysMigrateRequest object.

  Fields:
    googleCloudRecaptchaenterpriseV1MigrateKeyRequest: A
      GoogleCloudRecaptchaenterpriseV1MigrateKeyRequest resource to be passed
      as the request body.
    name: Required. The name of the key to be migrated, in the format
      `projects/{project}/keys/{key}`.
  """

  googleCloudRecaptchaenterpriseV1MigrateKeyRequest = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1MigrateKeyRequest', 1)
  name = _messages.StringField(2, required=True)


class RecaptchaenterpriseProjectsKeysPatchRequest(_messages.Message):
  r"""A RecaptchaenterpriseProjectsKeysPatchRequest object.

  Fields:
    googleCloudRecaptchaenterpriseV1Key: A GoogleCloudRecaptchaenterpriseV1Key
      resource to be passed as the request body.
    name: Identifier. The resource name for the Key in the format
      `projects/{project}/keys/{key}`.
    updateMask: Optional. The mask to control which fields of the key get
      updated. If the mask is not present, all fields are updated.
  """

  googleCloudRecaptchaenterpriseV1Key = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1Key', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class RecaptchaenterpriseProjectsKeysRemoveIpOverrideRequest(_messages.Message):
  r"""A RecaptchaenterpriseProjectsKeysRemoveIpOverrideRequest object.

  Fields:
    googleCloudRecaptchaenterpriseV1RemoveIpOverrideRequest: A
      GoogleCloudRecaptchaenterpriseV1RemoveIpOverrideRequest resource to be
      passed as the request body.
    name: Required. The name of the key from which the IP override is removed,
      in the format `projects/{project}/keys/{key}`.
  """

  googleCloudRecaptchaenterpriseV1RemoveIpOverrideRequest = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1RemoveIpOverrideRequest', 1)
  name = _messages.StringField(2, required=True)


class RecaptchaenterpriseProjectsKeysRetrieveLegacySecretKeyRequest(_messages.Message):
  r"""A RecaptchaenterpriseProjectsKeysRetrieveLegacySecretKeyRequest object.

  Fields:
    key: Required. The public key name linked to the requested secret key in
      the format `projects/{project}/keys/{key}`.
  """

  key = _messages.StringField(1, required=True)


class RecaptchaenterpriseProjectsRelatedaccountgroupmembershipsSearchRequest(_messages.Message):
  r"""A RecaptchaenterpriseProjectsRelatedaccountgroupmembershipsSearchRequest
  object.

  Fields:
    googleCloudRecaptchaenterpriseV1SearchRelatedAccountGroupMembershipsReques
      t: A GoogleCloudRecaptchaenterpriseV1SearchRelatedAccountGroupMembership
      sRequest resource to be passed as the request body.
    project: Required. The name of the project to search related account group
      memberships from. Specify the project name in the following format:
      `projects/{project}`.
  """

  googleCloudRecaptchaenterpriseV1SearchRelatedAccountGroupMembershipsRequest = _messages.MessageField('GoogleCloudRecaptchaenterpriseV1SearchRelatedAccountGroupMembershipsRequest', 1)
  project = _messages.StringField(2, required=True)


class RecaptchaenterpriseProjectsRelatedaccountgroupsListRequest(_messages.Message):
  r"""A RecaptchaenterpriseProjectsRelatedaccountgroupsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of groups to return. The service
      might return fewer than this value. If unspecified, at most 50 groups
      are returned. The maximum value is 1000; values above 1000 are coerced
      to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListRelatedAccountGroups` call. Provide this to retrieve the subsequent
      page. When paginating, all other parameters provided to
      `ListRelatedAccountGroups` must match the call that provided the page
      token.
    parent: Required. The name of the project to list related account groups
      from, in the format `projects/{project}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class RecaptchaenterpriseProjectsRelatedaccountgroupsMembershipsListRequest(_messages.Message):
  r"""A RecaptchaenterpriseProjectsRelatedaccountgroupsMembershipsListRequest
  object.

  Fields:
    pageSize: Optional. The maximum number of accounts to return. The service
      might return fewer than this value. If unspecified, at most 50 accounts
      are returned. The maximum value is 1000; values above 1000 are coerced
      to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListRelatedAccountGroupMemberships` call. When paginating, all other
      parameters provided to `ListRelatedAccountGroupMemberships` must match
      the call that provided the page token.
    parent: Required. The resource name for the related account group in the
      format `projects/{project}/relatedaccountgroups/{relatedaccountgroup}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
