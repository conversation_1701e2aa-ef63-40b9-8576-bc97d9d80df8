"""Generated client library for vmwareengine version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.vmwareengine.v1 import vmwareengine_v1_messages as messages


class VmwareengineV1(base_api.BaseApiClient):
  """Generated client library for service vmwareengine version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://vmwareengine.googleapis.com/'
  MTLS_BASE_URL = 'https://vmwareengine.mtls.googleapis.com/'

  _PACKAGE = 'vmwareengine'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'VmwareengineV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new vmwareengine handle."""
    url = url or self.BASE_URL
    super(VmwareengineV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_announcements = self.ProjectsLocationsAnnouncementsService(self)
    self.projects_locations_dnsBindPermission = self.ProjectsLocationsDnsBindPermissionService(self)
    self.projects_locations_networkPeerings_peeringRoutes = self.ProjectsLocationsNetworkPeeringsPeeringRoutesService(self)
    self.projects_locations_networkPeerings = self.ProjectsLocationsNetworkPeeringsService(self)
    self.projects_locations_networkPolicies_externalAccessRules = self.ProjectsLocationsNetworkPoliciesExternalAccessRulesService(self)
    self.projects_locations_networkPolicies = self.ProjectsLocationsNetworkPoliciesService(self)
    self.projects_locations_nodeTypes = self.ProjectsLocationsNodeTypesService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_privateClouds_clusters_nodes = self.ProjectsLocationsPrivateCloudsClustersNodesService(self)
    self.projects_locations_privateClouds_clusters = self.ProjectsLocationsPrivateCloudsClustersService(self)
    self.projects_locations_privateClouds_externalAddresses = self.ProjectsLocationsPrivateCloudsExternalAddressesService(self)
    self.projects_locations_privateClouds_hcxActivationKeys = self.ProjectsLocationsPrivateCloudsHcxActivationKeysService(self)
    self.projects_locations_privateClouds_identitySources = self.ProjectsLocationsPrivateCloudsIdentitySourcesService(self)
    self.projects_locations_privateClouds_loggingServers = self.ProjectsLocationsPrivateCloudsLoggingServersService(self)
    self.projects_locations_privateClouds_managementDnsZoneBindings = self.ProjectsLocationsPrivateCloudsManagementDnsZoneBindingsService(self)
    self.projects_locations_privateClouds_subnets = self.ProjectsLocationsPrivateCloudsSubnetsService(self)
    self.projects_locations_privateClouds_upgradeJobs = self.ProjectsLocationsPrivateCloudsUpgradeJobsService(self)
    self.projects_locations_privateClouds_upgrades = self.ProjectsLocationsPrivateCloudsUpgradesService(self)
    self.projects_locations_privateClouds = self.ProjectsLocationsPrivateCloudsService(self)
    self.projects_locations_privateConnections_peeringRoutes = self.ProjectsLocationsPrivateConnectionsPeeringRoutesService(self)
    self.projects_locations_privateConnections = self.ProjectsLocationsPrivateConnectionsService(self)
    self.projects_locations_vmwareEngineNetworks = self.ProjectsLocationsVmwareEngineNetworksService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsAnnouncementsService(base_api.BaseApiService):
    """Service class for the projects_locations_announcements resource."""

    _NAME = 'projects_locations_announcements'

    def __init__(self, client):
      super(VmwareengineV1.ProjectsLocationsAnnouncementsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Retrieves a `Announcement` by its resource name.

      Args:
        request: (VmwareengineProjectsLocationsAnnouncementsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Announcement) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/announcements/{announcementsId}',
        http_method='GET',
        method_id='vmwareengine.projects.locations.announcements.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsAnnouncementsGetRequest',
        response_type_name='Announcement',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists `Announcements` for a given region and project.

      Args:
        request: (VmwareengineProjectsLocationsAnnouncementsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAnnouncementsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/announcements',
        http_method='GET',
        method_id='vmwareengine.projects.locations.announcements.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/announcements',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsAnnouncementsListRequest',
        response_type_name='ListAnnouncementsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDnsBindPermissionService(base_api.BaseApiService):
    """Service class for the projects_locations_dnsBindPermission resource."""

    _NAME = 'projects_locations_dnsBindPermission'

    def __init__(self, client):
      super(VmwareengineV1.ProjectsLocationsDnsBindPermissionService, self).__init__(client)
      self._upload_configs = {
          }

    def Grant(self, request, global_params=None):
      r"""Grants the bind permission to the customer provided principal(user / service account) to bind their DNS zone with the intranet VPC associated with the project. DnsBindPermission is a global resource and location can only be global.

      Args:
        request: (VmwareengineProjectsLocationsDnsBindPermissionGrantRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Grant')
      return self._RunMethod(
          config, request, global_params=global_params)

    Grant.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/dnsBindPermission:grant',
        http_method='POST',
        method_id='vmwareengine.projects.locations.dnsBindPermission.grant',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:grant',
        request_field='grantDnsBindPermissionRequest',
        request_type_name='VmwareengineProjectsLocationsDnsBindPermissionGrantRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Revoke(self, request, global_params=None):
      r"""Revokes the bind permission from the customer provided principal(user / service account) on the intranet VPC associated with the consumer project. DnsBindPermission is a global resource and location can only be global.

      Args:
        request: (VmwareengineProjectsLocationsDnsBindPermissionRevokeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Revoke')
      return self._RunMethod(
          config, request, global_params=global_params)

    Revoke.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/dnsBindPermission:revoke',
        http_method='POST',
        method_id='vmwareengine.projects.locations.dnsBindPermission.revoke',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:revoke',
        request_field='revokeDnsBindPermissionRequest',
        request_type_name='VmwareengineProjectsLocationsDnsBindPermissionRevokeRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsNetworkPeeringsPeeringRoutesService(base_api.BaseApiService):
    """Service class for the projects_locations_networkPeerings_peeringRoutes resource."""

    _NAME = 'projects_locations_networkPeerings_peeringRoutes'

    def __init__(self, client):
      super(VmwareengineV1.ProjectsLocationsNetworkPeeringsPeeringRoutesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the network peering routes exchanged over a peering connection. NetworkPeering is a global resource and location can only be global.

      Args:
        request: (VmwareengineProjectsLocationsNetworkPeeringsPeeringRoutesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPeeringRoutesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/networkPeerings/{networkPeeringsId}/peeringRoutes',
        http_method='GET',
        method_id='vmwareengine.projects.locations.networkPeerings.peeringRoutes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/peeringRoutes',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsNetworkPeeringsPeeringRoutesListRequest',
        response_type_name='ListPeeringRoutesResponse',
        supports_download=False,
    )

  class ProjectsLocationsNetworkPeeringsService(base_api.BaseApiService):
    """Service class for the projects_locations_networkPeerings resource."""

    _NAME = 'projects_locations_networkPeerings'

    def __init__(self, client):
      super(VmwareengineV1.ProjectsLocationsNetworkPeeringsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new network peering between the peer network and VMware Engine network provided in a `NetworkPeering` resource. NetworkPeering is a global resource and location can only be global.

      Args:
        request: (VmwareengineProjectsLocationsNetworkPeeringsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/networkPeerings',
        http_method='POST',
        method_id='vmwareengine.projects.locations.networkPeerings.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['networkPeeringId', 'requestId'],
        relative_path='v1/{+parent}/networkPeerings',
        request_field='networkPeering',
        request_type_name='VmwareengineProjectsLocationsNetworkPeeringsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a `NetworkPeering` resource. When a network peering is deleted for a VMware Engine network, the peer network becomes inaccessible to that VMware Engine network. NetworkPeering is a global resource and location can only be global.

      Args:
        request: (VmwareengineProjectsLocationsNetworkPeeringsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/networkPeerings/{networkPeeringsId}',
        http_method='DELETE',
        method_id='vmwareengine.projects.locations.networkPeerings.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsNetworkPeeringsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a `NetworkPeering` resource by its resource name. The resource contains details of the network peering, such as peered networks, import and export custom route configurations, and peering state. NetworkPeering is a global resource and location can only be global.

      Args:
        request: (VmwareengineProjectsLocationsNetworkPeeringsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NetworkPeering) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/networkPeerings/{networkPeeringsId}',
        http_method='GET',
        method_id='vmwareengine.projects.locations.networkPeerings.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsNetworkPeeringsGetRequest',
        response_type_name='NetworkPeering',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists `NetworkPeering` resources in a given project. NetworkPeering is a global resource and location can only be global.

      Args:
        request: (VmwareengineProjectsLocationsNetworkPeeringsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListNetworkPeeringsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/networkPeerings',
        http_method='GET',
        method_id='vmwareengine.projects.locations.networkPeerings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/networkPeerings',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsNetworkPeeringsListRequest',
        response_type_name='ListNetworkPeeringsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Modifies a `NetworkPeering` resource. Only the `description` field can be updated. Only fields specified in `updateMask` are applied. NetworkPeering is a global resource and location can only be global.

      Args:
        request: (VmwareengineProjectsLocationsNetworkPeeringsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/networkPeerings/{networkPeeringsId}',
        http_method='PATCH',
        method_id='vmwareengine.projects.locations.networkPeerings.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='networkPeering',
        request_type_name='VmwareengineProjectsLocationsNetworkPeeringsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsNetworkPoliciesExternalAccessRulesService(base_api.BaseApiService):
    """Service class for the projects_locations_networkPolicies_externalAccessRules resource."""

    _NAME = 'projects_locations_networkPolicies_externalAccessRules'

    def __init__(self, client):
      super(VmwareengineV1.ProjectsLocationsNetworkPoliciesExternalAccessRulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new external access rule in a given network policy.

      Args:
        request: (VmwareengineProjectsLocationsNetworkPoliciesExternalAccessRulesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/networkPolicies/{networkPoliciesId}/externalAccessRules',
        http_method='POST',
        method_id='vmwareengine.projects.locations.networkPolicies.externalAccessRules.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['externalAccessRuleId', 'requestId'],
        relative_path='v1/{+parent}/externalAccessRules',
        request_field='externalAccessRule',
        request_type_name='VmwareengineProjectsLocationsNetworkPoliciesExternalAccessRulesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single external access rule.

      Args:
        request: (VmwareengineProjectsLocationsNetworkPoliciesExternalAccessRulesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/networkPolicies/{networkPoliciesId}/externalAccessRules/{externalAccessRulesId}',
        http_method='DELETE',
        method_id='vmwareengine.projects.locations.networkPolicies.externalAccessRules.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsNetworkPoliciesExternalAccessRulesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single external access rule.

      Args:
        request: (VmwareengineProjectsLocationsNetworkPoliciesExternalAccessRulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ExternalAccessRule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/networkPolicies/{networkPoliciesId}/externalAccessRules/{externalAccessRulesId}',
        http_method='GET',
        method_id='vmwareengine.projects.locations.networkPolicies.externalAccessRules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsNetworkPoliciesExternalAccessRulesGetRequest',
        response_type_name='ExternalAccessRule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists `ExternalAccessRule` resources in the specified network policy.

      Args:
        request: (VmwareengineProjectsLocationsNetworkPoliciesExternalAccessRulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListExternalAccessRulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/networkPolicies/{networkPoliciesId}/externalAccessRules',
        http_method='GET',
        method_id='vmwareengine.projects.locations.networkPolicies.externalAccessRules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/externalAccessRules',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsNetworkPoliciesExternalAccessRulesListRequest',
        response_type_name='ListExternalAccessRulesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single external access rule. Only fields specified in `update_mask` are applied.

      Args:
        request: (VmwareengineProjectsLocationsNetworkPoliciesExternalAccessRulesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/networkPolicies/{networkPoliciesId}/externalAccessRules/{externalAccessRulesId}',
        http_method='PATCH',
        method_id='vmwareengine.projects.locations.networkPolicies.externalAccessRules.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='externalAccessRule',
        request_type_name='VmwareengineProjectsLocationsNetworkPoliciesExternalAccessRulesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsNetworkPoliciesService(base_api.BaseApiService):
    """Service class for the projects_locations_networkPolicies resource."""

    _NAME = 'projects_locations_networkPolicies'

    def __init__(self, client):
      super(VmwareengineV1.ProjectsLocationsNetworkPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new network policy in a given VMware Engine network of a project and location (region). A new network policy cannot be created if another network policy already exists in the same scope.

      Args:
        request: (VmwareengineProjectsLocationsNetworkPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/networkPolicies',
        http_method='POST',
        method_id='vmwareengine.projects.locations.networkPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['networkPolicyId', 'requestId'],
        relative_path='v1/{+parent}/networkPolicies',
        request_field='networkPolicy',
        request_type_name='VmwareengineProjectsLocationsNetworkPoliciesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a `NetworkPolicy` resource. A network policy cannot be deleted when `NetworkService.state` is set to `RECONCILING` for either its external IP or internet access service.

      Args:
        request: (VmwareengineProjectsLocationsNetworkPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/networkPolicies/{networkPoliciesId}',
        http_method='DELETE',
        method_id='vmwareengine.projects.locations.networkPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsNetworkPoliciesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def FetchExternalAddresses(self, request, global_params=None):
      r"""Lists external IP addresses assigned to VMware workload VMs within the scope of the given network policy.

      Args:
        request: (VmwareengineProjectsLocationsNetworkPoliciesFetchExternalAddressesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FetchNetworkPolicyExternalAddressesResponse) The response message.
      """
      config = self.GetMethodConfig('FetchExternalAddresses')
      return self._RunMethod(
          config, request, global_params=global_params)

    FetchExternalAddresses.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/networkPolicies/{networkPoliciesId}:fetchExternalAddresses',
        http_method='GET',
        method_id='vmwareengine.projects.locations.networkPolicies.fetchExternalAddresses',
        ordered_params=['networkPolicy'],
        path_params=['networkPolicy'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+networkPolicy}:fetchExternalAddresses',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsNetworkPoliciesFetchExternalAddressesRequest',
        response_type_name='FetchNetworkPolicyExternalAddressesResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a `NetworkPolicy` resource by its resource name.

      Args:
        request: (VmwareengineProjectsLocationsNetworkPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NetworkPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/networkPolicies/{networkPoliciesId}',
        http_method='GET',
        method_id='vmwareengine.projects.locations.networkPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsNetworkPoliciesGetRequest',
        response_type_name='NetworkPolicy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists `NetworkPolicy` resources in a specified project and location.

      Args:
        request: (VmwareengineProjectsLocationsNetworkPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListNetworkPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/networkPolicies',
        http_method='GET',
        method_id='vmwareengine.projects.locations.networkPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/networkPolicies',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsNetworkPoliciesListRequest',
        response_type_name='ListNetworkPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Modifies a `NetworkPolicy` resource. Only the following fields can be updated: `internet_access`, `external_ip`, `edge_services_cidr`. Only fields specified in `updateMask` are applied. When updating a network policy, the external IP network service can only be disabled if there are no external IP addresses present in the scope of the policy. Also, a `NetworkService` cannot be updated when `NetworkService.state` is set to `RECONCILING`. During operation processing, the resource is temporarily in the `ACTIVE` state before the operation fully completes. For that period of time, you can't update the resource. Use the operation status to determine when the processing fully completes.

      Args:
        request: (VmwareengineProjectsLocationsNetworkPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/networkPolicies/{networkPoliciesId}',
        http_method='PATCH',
        method_id='vmwareengine.projects.locations.networkPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='networkPolicy',
        request_type_name='VmwareengineProjectsLocationsNetworkPoliciesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsNodeTypesService(base_api.BaseApiService):
    """Service class for the projects_locations_nodeTypes resource."""

    _NAME = 'projects_locations_nodeTypes'

    def __init__(self, client):
      super(VmwareengineV1.ProjectsLocationsNodeTypesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details of a single `NodeType`.

      Args:
        request: (VmwareengineProjectsLocationsNodeTypesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NodeType) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/nodeTypes/{nodeTypesId}',
        http_method='GET',
        method_id='vmwareengine.projects.locations.nodeTypes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsNodeTypesGetRequest',
        response_type_name='NodeType',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists node types.

      Args:
        request: (VmwareengineProjectsLocationsNodeTypesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListNodeTypesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/nodeTypes',
        http_method='GET',
        method_id='vmwareengine.projects.locations.nodeTypes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/nodeTypes',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsNodeTypesListRequest',
        response_type_name='ListNodeTypesResponse',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(VmwareengineV1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (VmwareengineProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='vmwareengine.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (VmwareengineProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='vmwareengine.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (VmwareengineProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='vmwareengine.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsPrivateCloudsClustersNodesService(base_api.BaseApiService):
    """Service class for the projects_locations_privateClouds_clusters_nodes resource."""

    _NAME = 'projects_locations_privateClouds_clusters_nodes'

    def __init__(self, client):
      super(VmwareengineV1.ProjectsLocationsPrivateCloudsClustersNodesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details of a single node.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsClustersNodesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Node) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/clusters/{clustersId}/nodes/{nodesId}',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.clusters.nodes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsClustersNodesGetRequest',
        response_type_name='Node',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists nodes in a given cluster.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsClustersNodesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListNodesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/clusters/{clustersId}/nodes',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.clusters.nodes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/nodes',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsClustersNodesListRequest',
        response_type_name='ListNodesResponse',
        supports_download=False,
    )

  class ProjectsLocationsPrivateCloudsClustersService(base_api.BaseApiService):
    """Service class for the projects_locations_privateClouds_clusters resource."""

    _NAME = 'projects_locations_privateClouds_clusters'

    def __init__(self, client):
      super(VmwareengineV1.ProjectsLocationsPrivateCloudsClustersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new cluster in a given private cloud. Creating a new cluster provides additional nodes for use in the parent private cloud and requires sufficient [node quota](https://cloud.google.com/vmware-engine/quotas).

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsClustersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/clusters',
        http_method='POST',
        method_id='vmwareengine.projects.locations.privateClouds.clusters.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['clusterId', 'requestId', 'validateOnly'],
        relative_path='v1/{+parent}/clusters',
        request_field='cluster',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsClustersCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a `Cluster` resource. To avoid unintended data loss, migrate or gracefully shut down any workloads running on the cluster before deletion. You cannot delete the management cluster of a private cloud using this method.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsClustersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/clusters/{clustersId}',
        http_method='DELETE',
        method_id='vmwareengine.projects.locations.privateClouds.clusters.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsClustersDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a `Cluster` resource by its resource name.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsClustersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Cluster) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/clusters/{clustersId}',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.clusters.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsClustersGetRequest',
        response_type_name='Cluster',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsClustersGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/clusters/{clustersId}:getIamPolicy',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.clusters.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsClustersGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists `Cluster` resources in a given private cloud.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsClustersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListClustersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/clusters',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.clusters.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/clusters',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsClustersListRequest',
        response_type_name='ListClustersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Modifies a `Cluster` resource. Only fields specified in `updateMask` are applied. During operation processing, the resource is temporarily in the `ACTIVE` state before the operation fully completes. For that period of time, you can't update the resource. Use the operation status to determine when the processing fully completes.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsClustersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/clusters/{clustersId}',
        http_method='PATCH',
        method_id='vmwareengine.projects.locations.privateClouds.clusters.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='cluster',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsClustersPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsClustersSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/clusters/{clustersId}:setIamPolicy',
        http_method='POST',
        method_id='vmwareengine.projects.locations.privateClouds.clusters.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsClustersSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsClustersTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/clusters/{clustersId}:testIamPermissions',
        http_method='POST',
        method_id='vmwareengine.projects.locations.privateClouds.clusters.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsClustersTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsPrivateCloudsExternalAddressesService(base_api.BaseApiService):
    """Service class for the projects_locations_privateClouds_externalAddresses resource."""

    _NAME = 'projects_locations_privateClouds_externalAddresses'

    def __init__(self, client):
      super(VmwareengineV1.ProjectsLocationsPrivateCloudsExternalAddressesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new `ExternalAddress` resource in a given private cloud. The network policy that corresponds to the private cloud must have the external IP address network service enabled (`NetworkPolicy.external_ip`).

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsExternalAddressesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/externalAddresses',
        http_method='POST',
        method_id='vmwareengine.projects.locations.privateClouds.externalAddresses.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['externalAddressId', 'requestId'],
        relative_path='v1/{+parent}/externalAddresses',
        request_field='externalAddress',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsExternalAddressesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single external IP address. When you delete an external IP address, connectivity between the external IP address and the corresponding internal IP address is lost.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsExternalAddressesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/externalAddresses/{externalAddressesId}',
        http_method='DELETE',
        method_id='vmwareengine.projects.locations.privateClouds.externalAddresses.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsExternalAddressesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single external IP address.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsExternalAddressesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ExternalAddress) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/externalAddresses/{externalAddressesId}',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.externalAddresses.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsExternalAddressesGetRequest',
        response_type_name='ExternalAddress',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists external IP addresses assigned to VMware workload VMs in a given private cloud.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsExternalAddressesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListExternalAddressesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/externalAddresses',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.externalAddresses.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/externalAddresses',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsExternalAddressesListRequest',
        response_type_name='ListExternalAddressesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single external IP address. Only fields specified in `update_mask` are applied. During operation processing, the resource is temporarily in the `ACTIVE` state before the operation fully completes. For that period of time, you can't update the resource. Use the operation status to determine when the processing fully completes.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsExternalAddressesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/externalAddresses/{externalAddressesId}',
        http_method='PATCH',
        method_id='vmwareengine.projects.locations.privateClouds.externalAddresses.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='externalAddress',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsExternalAddressesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsPrivateCloudsHcxActivationKeysService(base_api.BaseApiService):
    """Service class for the projects_locations_privateClouds_hcxActivationKeys resource."""

    _NAME = 'projects_locations_privateClouds_hcxActivationKeys'

    def __init__(self, client):
      super(VmwareengineV1.ProjectsLocationsPrivateCloudsHcxActivationKeysService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new HCX activation key in a given private cloud.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsHcxActivationKeysCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/hcxActivationKeys',
        http_method='POST',
        method_id='vmwareengine.projects.locations.privateClouds.hcxActivationKeys.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['hcxActivationKeyId', 'requestId'],
        relative_path='v1/{+parent}/hcxActivationKeys',
        request_field='hcxActivationKey',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsHcxActivationKeysCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a `HcxActivationKey` resource by its resource name.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsHcxActivationKeysGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HcxActivationKey) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/hcxActivationKeys/{hcxActivationKeysId}',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.hcxActivationKeys.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsHcxActivationKeysGetRequest',
        response_type_name='HcxActivationKey',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsHcxActivationKeysGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/hcxActivationKeys/{hcxActivationKeysId}:getIamPolicy',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.hcxActivationKeys.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsHcxActivationKeysGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists `HcxActivationKey` resources in a given private cloud.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsHcxActivationKeysListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListHcxActivationKeysResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/hcxActivationKeys',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.hcxActivationKeys.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/hcxActivationKeys',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsHcxActivationKeysListRequest',
        response_type_name='ListHcxActivationKeysResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsHcxActivationKeysSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/hcxActivationKeys/{hcxActivationKeysId}:setIamPolicy',
        http_method='POST',
        method_id='vmwareengine.projects.locations.privateClouds.hcxActivationKeys.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsHcxActivationKeysSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsHcxActivationKeysTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/hcxActivationKeys/{hcxActivationKeysId}:testIamPermissions',
        http_method='POST',
        method_id='vmwareengine.projects.locations.privateClouds.hcxActivationKeys.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsHcxActivationKeysTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsPrivateCloudsIdentitySourcesService(base_api.BaseApiService):
    """Service class for the projects_locations_privateClouds_identitySources resource."""

    _NAME = 'projects_locations_privateClouds_identitySources'

    def __init__(self, client):
      super(VmwareengineV1.ProjectsLocationsPrivateCloudsIdentitySourcesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new identity source in a given private cloud.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsIdentitySourcesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/identitySources',
        http_method='POST',
        method_id='vmwareengine.projects.locations.privateClouds.identitySources.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['identitySourceId', 'requestId', 'validateOnly'],
        relative_path='v1/{+parent}/identitySources',
        request_field='identitySource',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsIdentitySourcesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a `IdentitySource` resource.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsIdentitySourcesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/identitySources/{identitySourcesId}',
        http_method='DELETE',
        method_id='vmwareengine.projects.locations.privateClouds.identitySources.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsIdentitySourcesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the `IdentitySource` resource by its resource name.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsIdentitySourcesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (IdentitySource) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/identitySources/{identitySourcesId}',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.identitySources.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsIdentitySourcesGetRequest',
        response_type_name='IdentitySource',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists identity sources in the private cloud.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsIdentitySourcesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListIdentitySourcesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/identitySources',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.identitySources.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/identitySources',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsIdentitySourcesListRequest',
        response_type_name='ListIdentitySourcesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Modifies an `IdentitySource` resource. Only the following fields can be updated: `base_users_dn`, `base_groups_dn`, `domain_user`, `domain_password` and `ssl_certificates`. Only fields specified in `update_mask` are applied. When updating identity source with LDAPS protocol, update mask must include `ssl_certificates`. When updating identity source with LDAP protocol, update mask must not include `ssl_certificates`. When updating `domain_user`, `domain_password` must be updated as well, and the other way around.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsIdentitySourcesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/identitySources/{identitySourcesId}',
        http_method='PATCH',
        method_id='vmwareengine.projects.locations.privateClouds.identitySources.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='identitySource',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsIdentitySourcesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsPrivateCloudsLoggingServersService(base_api.BaseApiService):
    """Service class for the projects_locations_privateClouds_loggingServers resource."""

    _NAME = 'projects_locations_privateClouds_loggingServers'

    def __init__(self, client):
      super(VmwareengineV1.ProjectsLocationsPrivateCloudsLoggingServersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new logging server for a given private cloud.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsLoggingServersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/loggingServers',
        http_method='POST',
        method_id='vmwareengine.projects.locations.privateClouds.loggingServers.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['loggingServerId', 'requestId'],
        relative_path='v1/{+parent}/loggingServers',
        request_field='loggingServer',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsLoggingServersCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single logging server.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsLoggingServersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/loggingServers/{loggingServersId}',
        http_method='DELETE',
        method_id='vmwareengine.projects.locations.privateClouds.loggingServers.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsLoggingServersDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a logging server.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsLoggingServersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LoggingServer) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/loggingServers/{loggingServersId}',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.loggingServers.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsLoggingServersGetRequest',
        response_type_name='LoggingServer',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists logging servers configured for a given private cloud.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsLoggingServersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLoggingServersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/loggingServers',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.loggingServers.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/loggingServers',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsLoggingServersListRequest',
        response_type_name='ListLoggingServersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single logging server. Only fields specified in `update_mask` are applied.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsLoggingServersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/loggingServers/{loggingServersId}',
        http_method='PATCH',
        method_id='vmwareengine.projects.locations.privateClouds.loggingServers.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='loggingServer',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsLoggingServersPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsPrivateCloudsManagementDnsZoneBindingsService(base_api.BaseApiService):
    """Service class for the projects_locations_privateClouds_managementDnsZoneBindings resource."""

    _NAME = 'projects_locations_privateClouds_managementDnsZoneBindings'

    def __init__(self, client):
      super(VmwareengineV1.ProjectsLocationsPrivateCloudsManagementDnsZoneBindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new `ManagementDnsZoneBinding` resource in a private cloud. This RPC creates the DNS binding and the resource that represents the DNS binding of the consumer VPC network to the management DNS zone. A management DNS zone is the Cloud DNS cross-project binding zone that VMware Engine creates for each private cloud. It contains FQDNs and corresponding IP addresses for the private cloud's ESXi hosts and management VM appliances like vCenter and NSX Manager.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsManagementDnsZoneBindingsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/managementDnsZoneBindings',
        http_method='POST',
        method_id='vmwareengine.projects.locations.privateClouds.managementDnsZoneBindings.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['managementDnsZoneBindingId', 'requestId'],
        relative_path='v1/{+parent}/managementDnsZoneBindings',
        request_field='managementDnsZoneBinding',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsManagementDnsZoneBindingsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a `ManagementDnsZoneBinding` resource. When a management DNS zone binding is deleted, the corresponding consumer VPC network is no longer bound to the management DNS zone.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsManagementDnsZoneBindingsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/managementDnsZoneBindings/{managementDnsZoneBindingsId}',
        http_method='DELETE',
        method_id='vmwareengine.projects.locations.privateClouds.managementDnsZoneBindings.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsManagementDnsZoneBindingsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a 'ManagementDnsZoneBinding' resource by its resource name.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsManagementDnsZoneBindingsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ManagementDnsZoneBinding) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/managementDnsZoneBindings/{managementDnsZoneBindingsId}',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.managementDnsZoneBindings.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsManagementDnsZoneBindingsGetRequest',
        response_type_name='ManagementDnsZoneBinding',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Consumer VPCs bound to Management DNS Zone of a given private cloud.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsManagementDnsZoneBindingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListManagementDnsZoneBindingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/managementDnsZoneBindings',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.managementDnsZoneBindings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/managementDnsZoneBindings',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsManagementDnsZoneBindingsListRequest',
        response_type_name='ListManagementDnsZoneBindingsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a `ManagementDnsZoneBinding` resource. Only fields specified in `update_mask` are applied.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsManagementDnsZoneBindingsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/managementDnsZoneBindings/{managementDnsZoneBindingsId}',
        http_method='PATCH',
        method_id='vmwareengine.projects.locations.privateClouds.managementDnsZoneBindings.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='managementDnsZoneBinding',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsManagementDnsZoneBindingsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Repair(self, request, global_params=None):
      r"""Retries to create a `ManagementDnsZoneBinding` resource that is in failed state.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsManagementDnsZoneBindingsRepairRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Repair')
      return self._RunMethod(
          config, request, global_params=global_params)

    Repair.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/managementDnsZoneBindings/{managementDnsZoneBindingsId}:repair',
        http_method='POST',
        method_id='vmwareengine.projects.locations.privateClouds.managementDnsZoneBindings.repair',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:repair',
        request_field='repairManagementDnsZoneBindingRequest',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsManagementDnsZoneBindingsRepairRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsPrivateCloudsSubnetsService(base_api.BaseApiService):
    """Service class for the projects_locations_privateClouds_subnets resource."""

    _NAME = 'projects_locations_privateClouds_subnets'

    def __init__(self, client):
      super(VmwareengineV1.ProjectsLocationsPrivateCloudsSubnetsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details of a single subnet.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsSubnetsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Subnet) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/subnets/{subnetsId}',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.subnets.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsSubnetsGetRequest',
        response_type_name='Subnet',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists subnets in a given private cloud.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsSubnetsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSubnetsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/subnets',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.subnets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/subnets',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsSubnetsListRequest',
        response_type_name='ListSubnetsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single subnet. Only fields specified in `update_mask` are applied. *Note*: This API is synchronous and always returns a successful `google.longrunning.Operation` (LRO). The returned LRO will only have `done` and `response` fields.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsSubnetsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/subnets/{subnetsId}',
        http_method='PATCH',
        method_id='vmwareengine.projects.locations.privateClouds.subnets.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='subnet',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsSubnetsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsPrivateCloudsUpgradeJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_privateClouds_upgradeJobs resource."""

    _NAME = 'projects_locations_privateClouds_upgradeJobs'

    def __init__(self, client):
      super(VmwareengineV1.ProjectsLocationsPrivateCloudsUpgradeJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Retrieves a Private Cloud `UpgradeJob` resource by its resource name.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsUpgradeJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (UpgradeJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/upgradeJobs/{upgradeJobsId}',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.upgradeJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsUpgradeJobsGetRequest',
        response_type_name='UpgradeJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists `UpgradeJob` recources for a given private cloud.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsUpgradeJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListUpgradeJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/upgradeJobs',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.upgradeJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/upgradeJobs',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsUpgradeJobsListRequest',
        response_type_name='ListUpgradeJobsResponse',
        supports_download=False,
    )

  class ProjectsLocationsPrivateCloudsUpgradesService(base_api.BaseApiService):
    """Service class for the projects_locations_privateClouds_upgrades resource."""

    _NAME = 'projects_locations_privateClouds_upgrades'

    def __init__(self, client):
      super(VmwareengineV1.ProjectsLocationsPrivateCloudsUpgradesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Retrieves a private cloud `Upgrade` resource by its resource name.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsUpgradesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Upgrade) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/upgrades/{upgradesId}',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.upgrades.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsUpgradesGetRequest',
        response_type_name='Upgrade',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists past, ongoing and upcoming `Upgrades` for the given private cloud.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsUpgradesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListUpgradesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/upgrades',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.upgrades.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/upgrades',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsUpgradesListRequest',
        response_type_name='ListUpgradesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update the private cloud `Upgrade` resource. Only `schedule` field can updated. The schedule can only be updated when the upgrade has not started and schedule edit window is open. Only fields specified in `update_mask` are considered.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsUpgradesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/upgrades/{upgradesId}',
        http_method='PATCH',
        method_id='vmwareengine.projects.locations.privateClouds.upgrades.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='upgrade',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsUpgradesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsPrivateCloudsService(base_api.BaseApiService):
    """Service class for the projects_locations_privateClouds resource."""

    _NAME = 'projects_locations_privateClouds'

    def __init__(self, client):
      super(VmwareengineV1.ProjectsLocationsPrivateCloudsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new `PrivateCloud` resource in a given project and location. Private clouds of type `STANDARD` and `TIME_LIMITED` are zonal resources, `STRETCHED` private clouds are regional. Creating a private cloud also creates a [management cluster](https://cloud.google.com/vmware-engine/docs/concepts-vmware-components) for that private cloud.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds',
        http_method='POST',
        method_id='vmwareengine.projects.locations.privateClouds.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['privateCloudId', 'requestId', 'validateOnly'],
        relative_path='v1/{+parent}/privateClouds',
        request_field='privateCloud',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Schedules a `PrivateCloud` resource for deletion. A `PrivateCloud` resource scheduled for deletion has `PrivateCloud.state` set to `DELETED` and `expireTime` set to the time when deletion is final and can no longer be reversed. The delete operation is marked as done as soon as the `PrivateCloud` is successfully scheduled for deletion (this also applies when `delayHours` is set to zero), and the operation is not kept in pending state until `PrivateCloud` is purged. `PrivateCloud` can be restored using `UndeletePrivateCloud` method before the `expireTime` elapses. When `expireTime` is reached, deletion is final and all private cloud resources are irreversibly removed and billing stops. During the final removal process, `PrivateCloud.state` is set to `PURGING`. `PrivateCloud` can be polled using standard `GET` method for the whole period of deletion and purging. It will not be returned only when it is completely purged.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}',
        http_method='DELETE',
        method_id='vmwareengine.projects.locations.privateClouds.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['delayHours', 'force', 'requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a `PrivateCloud` resource by its resource name.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PrivateCloud) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsGetRequest',
        response_type_name='PrivateCloud',
        supports_download=False,
    )

    def GetDnsForwarding(self, request, global_params=None):
      r"""Gets details of the `DnsForwarding` config.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsGetDnsForwardingRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DnsForwarding) The response message.
      """
      config = self.GetMethodConfig('GetDnsForwarding')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetDnsForwarding.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/dnsForwarding',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.getDnsForwarding',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsGetDnsForwardingRequest',
        response_type_name='DnsForwarding',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}:getIamPolicy',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists `PrivateCloud` resources in a given project and location.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPrivateCloudsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/privateClouds',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsListRequest',
        response_type_name='ListPrivateCloudsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Modifies a `PrivateCloud` resource. Only the following fields can be updated: `description`. Only fields specified in `updateMask` are applied. During operation processing, the resource is temporarily in the `ACTIVE` state before the operation fully completes. For that period of time, you can't update the resource. Use the operation status to determine when the processing fully completes.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}',
        http_method='PATCH',
        method_id='vmwareengine.projects.locations.privateClouds.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='privateCloud',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def PrivateCloudDeletionNow(self, request, global_params=None):
      r"""Accelerates the deletion of a private cloud that is currently in soft deletion A `PrivateCloud` resource in soft deletion has `PrivateCloud.state` set to `SOFT_DELETED` and `PrivateCloud.expireTime` set to the time when deletion can no longer be reversed.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsPrivateCloudDeletionNowRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('PrivateCloudDeletionNow')
      return self._RunMethod(
          config, request, global_params=global_params)

    PrivateCloudDeletionNow.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}:privateCloudDeletionNow',
        http_method='POST',
        method_id='vmwareengine.projects.locations.privateClouds.privateCloudDeletionNow',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:privateCloudDeletionNow',
        request_field='acceleratePrivateCloudDeletionRequest',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsPrivateCloudDeletionNowRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def ResetNsxCredentials(self, request, global_params=None):
      r"""Resets credentials of the NSX appliance.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsResetNsxCredentialsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('ResetNsxCredentials')
      return self._RunMethod(
          config, request, global_params=global_params)

    ResetNsxCredentials.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}:resetNsxCredentials',
        http_method='POST',
        method_id='vmwareengine.projects.locations.privateClouds.resetNsxCredentials',
        ordered_params=['privateCloud'],
        path_params=['privateCloud'],
        query_params=[],
        relative_path='v1/{+privateCloud}:resetNsxCredentials',
        request_field='resetNsxCredentialsRequest',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsResetNsxCredentialsRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def ResetVcenterCredentials(self, request, global_params=None):
      r"""Resets credentials of the Vcenter appliance.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsResetVcenterCredentialsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('ResetVcenterCredentials')
      return self._RunMethod(
          config, request, global_params=global_params)

    ResetVcenterCredentials.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}:resetVcenterCredentials',
        http_method='POST',
        method_id='vmwareengine.projects.locations.privateClouds.resetVcenterCredentials',
        ordered_params=['privateCloud'],
        path_params=['privateCloud'],
        query_params=[],
        relative_path='v1/{+privateCloud}:resetVcenterCredentials',
        request_field='resetVcenterCredentialsRequest',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsResetVcenterCredentialsRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}:setIamPolicy',
        http_method='POST',
        method_id='vmwareengine.projects.locations.privateClouds.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def ShowNsxCredentials(self, request, global_params=None):
      r"""Gets details of credentials for NSX appliance.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsShowNsxCredentialsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Credentials) The response message.
      """
      config = self.GetMethodConfig('ShowNsxCredentials')
      return self._RunMethod(
          config, request, global_params=global_params)

    ShowNsxCredentials.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}:showNsxCredentials',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.showNsxCredentials',
        ordered_params=['privateCloud'],
        path_params=['privateCloud'],
        query_params=[],
        relative_path='v1/{+privateCloud}:showNsxCredentials',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsShowNsxCredentialsRequest',
        response_type_name='Credentials',
        supports_download=False,
    )

    def ShowVcenterCredentials(self, request, global_params=None):
      r"""Gets details of credentials for Vcenter appliance.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsShowVcenterCredentialsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Credentials) The response message.
      """
      config = self.GetMethodConfig('ShowVcenterCredentials')
      return self._RunMethod(
          config, request, global_params=global_params)

    ShowVcenterCredentials.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}:showVcenterCredentials',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateClouds.showVcenterCredentials',
        ordered_params=['privateCloud'],
        path_params=['privateCloud'],
        query_params=['username'],
        relative_path='v1/{+privateCloud}:showVcenterCredentials',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsShowVcenterCredentialsRequest',
        response_type_name='Credentials',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}:testIamPermissions',
        http_method='POST',
        method_id='vmwareengine.projects.locations.privateClouds.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def Undelete(self, request, global_params=None):
      r"""Restores a private cloud that was previously scheduled for deletion by `DeletePrivateCloud`. A `PrivateCloud` resource scheduled for deletion has `PrivateCloud.state` set to `DELETED` and `PrivateCloud.expireTime` set to the time when deletion can no longer be reversed.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsUndeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Undelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}:undelete',
        http_method='POST',
        method_id='vmwareengine.projects.locations.privateClouds.undelete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:undelete',
        request_field='undeletePrivateCloudRequest',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsUndeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def UpdateDnsForwarding(self, request, global_params=None):
      r"""Updates the parameters of the `DnsForwarding` config, like associated domains. Only fields specified in `update_mask` are applied.

      Args:
        request: (VmwareengineProjectsLocationsPrivateCloudsUpdateDnsForwardingRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('UpdateDnsForwarding')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateDnsForwarding.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateClouds/{privateCloudsId}/dnsForwarding',
        http_method='PATCH',
        method_id='vmwareengine.projects.locations.privateClouds.updateDnsForwarding',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='dnsForwarding',
        request_type_name='VmwareengineProjectsLocationsPrivateCloudsUpdateDnsForwardingRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsPrivateConnectionsPeeringRoutesService(base_api.BaseApiService):
    """Service class for the projects_locations_privateConnections_peeringRoutes resource."""

    _NAME = 'projects_locations_privateConnections_peeringRoutes'

    def __init__(self, client):
      super(VmwareengineV1.ProjectsLocationsPrivateConnectionsPeeringRoutesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the private connection routes exchanged over a peering connection.

      Args:
        request: (VmwareengineProjectsLocationsPrivateConnectionsPeeringRoutesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPrivateConnectionPeeringRoutesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateConnections/{privateConnectionsId}/peeringRoutes',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateConnections.peeringRoutes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/peeringRoutes',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateConnectionsPeeringRoutesListRequest',
        response_type_name='ListPrivateConnectionPeeringRoutesResponse',
        supports_download=False,
    )

  class ProjectsLocationsPrivateConnectionsService(base_api.BaseApiService):
    """Service class for the projects_locations_privateConnections resource."""

    _NAME = 'projects_locations_privateConnections'

    def __init__(self, client):
      super(VmwareengineV1.ProjectsLocationsPrivateConnectionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new private connection that can be used for accessing private Clouds.

      Args:
        request: (VmwareengineProjectsLocationsPrivateConnectionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateConnections',
        http_method='POST',
        method_id='vmwareengine.projects.locations.privateConnections.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['privateConnectionId', 'requestId'],
        relative_path='v1/{+parent}/privateConnections',
        request_field='privateConnection',
        request_type_name='VmwareengineProjectsLocationsPrivateConnectionsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a `PrivateConnection` resource. When a private connection is deleted for a VMware Engine network, the connected network becomes inaccessible to that VMware Engine network.

      Args:
        request: (VmwareengineProjectsLocationsPrivateConnectionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateConnections/{privateConnectionsId}',
        http_method='DELETE',
        method_id='vmwareengine.projects.locations.privateConnections.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateConnectionsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a `PrivateConnection` resource by its resource name. The resource contains details of the private connection, such as connected network, routing mode and state.

      Args:
        request: (VmwareengineProjectsLocationsPrivateConnectionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PrivateConnection) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateConnections/{privateConnectionsId}',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateConnections.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateConnectionsGetRequest',
        response_type_name='PrivateConnection',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists `PrivateConnection` resources in a given project and location.

      Args:
        request: (VmwareengineProjectsLocationsPrivateConnectionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPrivateConnectionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateConnections',
        http_method='GET',
        method_id='vmwareengine.projects.locations.privateConnections.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/privateConnections',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsPrivateConnectionsListRequest',
        response_type_name='ListPrivateConnectionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Modifies a `PrivateConnection` resource. Only `description` and `routing_mode` fields can be updated. Only fields specified in `updateMask` are applied.

      Args:
        request: (VmwareengineProjectsLocationsPrivateConnectionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/privateConnections/{privateConnectionsId}',
        http_method='PATCH',
        method_id='vmwareengine.projects.locations.privateConnections.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='privateConnection',
        request_type_name='VmwareengineProjectsLocationsPrivateConnectionsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsVmwareEngineNetworksService(base_api.BaseApiService):
    """Service class for the projects_locations_vmwareEngineNetworks resource."""

    _NAME = 'projects_locations_vmwareEngineNetworks'

    def __init__(self, client):
      super(VmwareengineV1.ProjectsLocationsVmwareEngineNetworksService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new VMware Engine network that can be used by a private cloud.

      Args:
        request: (VmwareengineProjectsLocationsVmwareEngineNetworksCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareEngineNetworks',
        http_method='POST',
        method_id='vmwareengine.projects.locations.vmwareEngineNetworks.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'vmwareEngineNetworkId'],
        relative_path='v1/{+parent}/vmwareEngineNetworks',
        request_field='vmwareEngineNetwork',
        request_type_name='VmwareengineProjectsLocationsVmwareEngineNetworksCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a `VmwareEngineNetwork` resource. You can only delete a VMware Engine network after all resources that refer to it are deleted. For example, a private cloud, a network peering, and a network policy can all refer to the same VMware Engine network.

      Args:
        request: (VmwareengineProjectsLocationsVmwareEngineNetworksDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareEngineNetworks/{vmwareEngineNetworksId}',
        http_method='DELETE',
        method_id='vmwareengine.projects.locations.vmwareEngineNetworks.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsVmwareEngineNetworksDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a `VmwareEngineNetwork` resource by its resource name. The resource contains details of the VMware Engine network, such as its VMware Engine network type, peered networks in a service project, and state (for example, `CREATING`, `ACTIVE`, `DELETING`).

      Args:
        request: (VmwareengineProjectsLocationsVmwareEngineNetworksGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (VmwareEngineNetwork) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareEngineNetworks/{vmwareEngineNetworksId}',
        http_method='GET',
        method_id='vmwareengine.projects.locations.vmwareEngineNetworks.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsVmwareEngineNetworksGetRequest',
        response_type_name='VmwareEngineNetwork',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists `VmwareEngineNetwork` resources in a given project and location.

      Args:
        request: (VmwareengineProjectsLocationsVmwareEngineNetworksListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListVmwareEngineNetworksResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareEngineNetworks',
        http_method='GET',
        method_id='vmwareengine.projects.locations.vmwareEngineNetworks.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/vmwareEngineNetworks',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsVmwareEngineNetworksListRequest',
        response_type_name='ListVmwareEngineNetworksResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Modifies a VMware Engine network resource. Only the following fields can be updated: `description`. Only fields specified in `updateMask` are applied.

      Args:
        request: (VmwareengineProjectsLocationsVmwareEngineNetworksPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareEngineNetworks/{vmwareEngineNetworksId}',
        http_method='PATCH',
        method_id='vmwareengine.projects.locations.vmwareEngineNetworks.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='vmwareEngineNetwork',
        request_type_name='VmwareengineProjectsLocationsVmwareEngineNetworksPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(VmwareengineV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (VmwareengineProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='vmwareengine.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def GetDnsBindPermission(self, request, global_params=None):
      r"""Gets all the principals having bind permission on the intranet VPC associated with the consumer project granted by the Grant API. DnsBindPermission is a global resource and location can only be global.

      Args:
        request: (VmwareengineProjectsLocationsGetDnsBindPermissionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DnsBindPermission) The response message.
      """
      config = self.GetMethodConfig('GetDnsBindPermission')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetDnsBindPermission.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/dnsBindPermission',
        http_method='GET',
        method_id='vmwareengine.projects.locations.getDnsBindPermission',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsGetDnsBindPermissionRequest',
        response_type_name='DnsBindPermission',
        supports_download=False,
    )

    def GetProjectState(self, request, global_params=None):
      r"""Gets state of a single `Project`.

      Args:
        request: (VmwareengineProjectsLocationsGetProjectStateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ProjectState) The response message.
      """
      config = self.GetMethodConfig('GetProjectState')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetProjectState.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/projectState',
        http_method='GET',
        method_id='vmwareengine.projects.locations.getProjectState',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsGetProjectStateRequest',
        response_type_name='ProjectState',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (VmwareengineProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='vmwareengine.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/locations',
        request_field='',
        request_type_name='VmwareengineProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(VmwareengineV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
