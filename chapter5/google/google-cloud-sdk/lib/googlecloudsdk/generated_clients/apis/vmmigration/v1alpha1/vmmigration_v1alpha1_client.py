"""Generated client library for vmmigration version v1alpha1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.vmmigration.v1alpha1 import vmmigration_v1alpha1_messages as messages


class VmmigrationV1alpha1(base_api.BaseApiClient):
  """Generated client library for service vmmigration version v1alpha1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://vmmigration.googleapis.com/'
  MTLS_BASE_URL = 'https://vmmigration.mtls.googleapis.com/'

  _PACKAGE = 'vmmigration'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1alpha1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'VmmigrationV1alpha1'
  _URL_VERSION = 'v1alpha1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new vmmigration handle."""
    url = url or self.BASE_URL
    super(VmmigrationV1alpha1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_groups = self.ProjectsLocationsGroupsService(self)
    self.projects_locations_imageImports_imageImportJobs = self.ProjectsLocationsImageImportsImageImportJobsService(self)
    self.projects_locations_imageImports = self.ProjectsLocationsImageImportsService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_sources_datacenterConnectors = self.ProjectsLocationsSourcesDatacenterConnectorsService(self)
    self.projects_locations_sources_diskMigrationJobs = self.ProjectsLocationsSourcesDiskMigrationJobsService(self)
    self.projects_locations_sources_migratingVms_cloneJobs = self.ProjectsLocationsSourcesMigratingVmsCloneJobsService(self)
    self.projects_locations_sources_migratingVms_cutoverJobs = self.ProjectsLocationsSourcesMigratingVmsCutoverJobsService(self)
    self.projects_locations_sources_migratingVms_replicationCycles = self.ProjectsLocationsSourcesMigratingVmsReplicationCyclesService(self)
    self.projects_locations_sources_migratingVms = self.ProjectsLocationsSourcesMigratingVmsService(self)
    self.projects_locations_sources_utilizationReports = self.ProjectsLocationsSourcesUtilizationReportsService(self)
    self.projects_locations_sources = self.ProjectsLocationsSourcesService(self)
    self.projects_locations_targetProjects = self.ProjectsLocationsTargetProjectsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsGroupsService(base_api.BaseApiService):
    """Service class for the projects_locations_groups resource."""

    _NAME = 'projects_locations_groups'

    def __init__(self, client):
      super(VmmigrationV1alpha1.ProjectsLocationsGroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def AddGroupMigration(self, request, global_params=None):
      r"""Adds a MigratingVm to a Group.

      Args:
        request: (VmmigrationProjectsLocationsGroupsAddGroupMigrationRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('AddGroupMigration')
      return self._RunMethod(
          config, request, global_params=global_params)

    AddGroupMigration.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/groups/{groupsId}:addGroupMigration',
        http_method='POST',
        method_id='vmmigration.projects.locations.groups.addGroupMigration',
        ordered_params=['group'],
        path_params=['group'],
        query_params=[],
        relative_path='v1alpha1/{+group}:addGroupMigration',
        request_field='addGroupMigrationRequest',
        request_type_name='VmmigrationProjectsLocationsGroupsAddGroupMigrationRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new Group in a given project and location.

      Args:
        request: (VmmigrationProjectsLocationsGroupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/groups',
        http_method='POST',
        method_id='vmmigration.projects.locations.groups.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['groupId', 'requestId'],
        relative_path='v1alpha1/{+parent}/groups',
        request_field='group',
        request_type_name='VmmigrationProjectsLocationsGroupsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Group.

      Args:
        request: (VmmigrationProjectsLocationsGroupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/groups/{groupsId}',
        http_method='DELETE',
        method_id='vmmigration.projects.locations.groups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsGroupsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Group.

      Args:
        request: (VmmigrationProjectsLocationsGroupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Group) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/groups/{groupsId}',
        http_method='GET',
        method_id='vmmigration.projects.locations.groups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsGroupsGetRequest',
        response_type_name='Group',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Groups in a given project and location.

      Args:
        request: (VmmigrationProjectsLocationsGroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/groups',
        http_method='GET',
        method_id='vmmigration.projects.locations.groups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/groups',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsGroupsListRequest',
        response_type_name='ListGroupsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Group.

      Args:
        request: (VmmigrationProjectsLocationsGroupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/groups/{groupsId}',
        http_method='PATCH',
        method_id='vmmigration.projects.locations.groups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='group',
        request_type_name='VmmigrationProjectsLocationsGroupsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def RemoveGroupMigration(self, request, global_params=None):
      r"""Removes a MigratingVm from a Group.

      Args:
        request: (VmmigrationProjectsLocationsGroupsRemoveGroupMigrationRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('RemoveGroupMigration')
      return self._RunMethod(
          config, request, global_params=global_params)

    RemoveGroupMigration.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/groups/{groupsId}:removeGroupMigration',
        http_method='POST',
        method_id='vmmigration.projects.locations.groups.removeGroupMigration',
        ordered_params=['group'],
        path_params=['group'],
        query_params=[],
        relative_path='v1alpha1/{+group}:removeGroupMigration',
        request_field='removeGroupMigrationRequest',
        request_type_name='VmmigrationProjectsLocationsGroupsRemoveGroupMigrationRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsImageImportsImageImportJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_imageImports_imageImportJobs resource."""

    _NAME = 'projects_locations_imageImports_imageImportJobs'

    def __init__(self, client):
      super(VmmigrationV1alpha1.ProjectsLocationsImageImportsImageImportJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Initiates the cancellation of a running clone job.

      Args:
        request: (VmmigrationProjectsLocationsImageImportsImageImportJobsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/imageImports/{imageImportsId}/imageImportJobs/{imageImportJobsId}:cancel',
        http_method='POST',
        method_id='vmmigration.projects.locations.imageImports.imageImportJobs.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='cancelImageImportJobRequest',
        request_type_name='VmmigrationProjectsLocationsImageImportsImageImportJobsCancelRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single ImageImportJob.

      Args:
        request: (VmmigrationProjectsLocationsImageImportsImageImportJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ImageImportJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/imageImports/{imageImportsId}/imageImportJobs/{imageImportJobsId}',
        http_method='GET',
        method_id='vmmigration.projects.locations.imageImports.imageImportJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsImageImportsImageImportJobsGetRequest',
        response_type_name='ImageImportJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ImageImportJobs in a given project.

      Args:
        request: (VmmigrationProjectsLocationsImageImportsImageImportJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListImageImportJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/imageImports/{imageImportsId}/imageImportJobs',
        http_method='GET',
        method_id='vmmigration.projects.locations.imageImports.imageImportJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/imageImportJobs',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsImageImportsImageImportJobsListRequest',
        response_type_name='ListImageImportJobsResponse',
        supports_download=False,
    )

  class ProjectsLocationsImageImportsService(base_api.BaseApiService):
    """Service class for the projects_locations_imageImports resource."""

    _NAME = 'projects_locations_imageImports'

    def __init__(self, client):
      super(VmmigrationV1alpha1.ProjectsLocationsImageImportsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new ImageImport in a given project.

      Args:
        request: (VmmigrationProjectsLocationsImageImportsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/imageImports',
        http_method='POST',
        method_id='vmmigration.projects.locations.imageImports.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['imageImportId', 'requestId'],
        relative_path='v1alpha1/{+parent}/imageImports',
        request_field='imageImport',
        request_type_name='VmmigrationProjectsLocationsImageImportsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single ImageImport.

      Args:
        request: (VmmigrationProjectsLocationsImageImportsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/imageImports/{imageImportsId}',
        http_method='DELETE',
        method_id='vmmigration.projects.locations.imageImports.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsImageImportsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single ImageImport.

      Args:
        request: (VmmigrationProjectsLocationsImageImportsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ImageImport) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/imageImports/{imageImportsId}',
        http_method='GET',
        method_id='vmmigration.projects.locations.imageImports.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsImageImportsGetRequest',
        response_type_name='ImageImport',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ImageImports in a given project.

      Args:
        request: (VmmigrationProjectsLocationsImageImportsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListImageImportsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/imageImports',
        http_method='GET',
        method_id='vmmigration.projects.locations.imageImports.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/imageImports',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsImageImportsListRequest',
        response_type_name='ListImageImportsResponse',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(VmmigrationV1alpha1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (VmmigrationProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='vmmigration.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='VmmigrationProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (VmmigrationProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='vmmigration.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (VmmigrationProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='vmmigration.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (VmmigrationProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='vmmigration.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsSourcesDatacenterConnectorsService(base_api.BaseApiService):
    """Service class for the projects_locations_sources_datacenterConnectors resource."""

    _NAME = 'projects_locations_sources_datacenterConnectors'

    def __init__(self, client):
      super(VmmigrationV1alpha1.ProjectsLocationsSourcesDatacenterConnectorsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new DatacenterConnector in a given Source.

      Args:
        request: (VmmigrationProjectsLocationsSourcesDatacenterConnectorsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/datacenterConnectors',
        http_method='POST',
        method_id='vmmigration.projects.locations.sources.datacenterConnectors.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['datacenterConnectorId', 'requestId'],
        relative_path='v1alpha1/{+parent}/datacenterConnectors',
        request_field='datacenterConnector',
        request_type_name='VmmigrationProjectsLocationsSourcesDatacenterConnectorsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single DatacenterConnector.

      Args:
        request: (VmmigrationProjectsLocationsSourcesDatacenterConnectorsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/datacenterConnectors/{datacenterConnectorsId}',
        http_method='DELETE',
        method_id='vmmigration.projects.locations.sources.datacenterConnectors.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsSourcesDatacenterConnectorsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single DatacenterConnector.

      Args:
        request: (VmmigrationProjectsLocationsSourcesDatacenterConnectorsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DatacenterConnector) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/datacenterConnectors/{datacenterConnectorsId}',
        http_method='GET',
        method_id='vmmigration.projects.locations.sources.datacenterConnectors.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsSourcesDatacenterConnectorsGetRequest',
        response_type_name='DatacenterConnector',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists DatacenterConnectors in a given Source.

      Args:
        request: (VmmigrationProjectsLocationsSourcesDatacenterConnectorsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDatacenterConnectorsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/datacenterConnectors',
        http_method='GET',
        method_id='vmmigration.projects.locations.sources.datacenterConnectors.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/datacenterConnectors',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsSourcesDatacenterConnectorsListRequest',
        response_type_name='ListDatacenterConnectorsResponse',
        supports_download=False,
    )

    def UpgradeAppliance(self, request, global_params=None):
      r"""Upgrades the appliance relate to this DatacenterConnector to the in-place updateable version.

      Args:
        request: (VmmigrationProjectsLocationsSourcesDatacenterConnectorsUpgradeApplianceRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('UpgradeAppliance')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpgradeAppliance.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/datacenterConnectors/{datacenterConnectorsId}:upgradeAppliance',
        http_method='POST',
        method_id='vmmigration.projects.locations.sources.datacenterConnectors.upgradeAppliance',
        ordered_params=['datacenterConnector'],
        path_params=['datacenterConnector'],
        query_params=[],
        relative_path='v1alpha1/{+datacenterConnector}:upgradeAppliance',
        request_field='upgradeApplianceRequest',
        request_type_name='VmmigrationProjectsLocationsSourcesDatacenterConnectorsUpgradeApplianceRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsSourcesDiskMigrationJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_sources_diskMigrationJobs resource."""

    _NAME = 'projects_locations_sources_diskMigrationJobs'

    def __init__(self, client):
      super(VmmigrationV1alpha1.ProjectsLocationsSourcesDiskMigrationJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancels the disk migration job.

      Args:
        request: (VmmigrationProjectsLocationsSourcesDiskMigrationJobsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/diskMigrationJobs/{diskMigrationJobsId}:cancel',
        http_method='POST',
        method_id='vmmigration.projects.locations.sources.diskMigrationJobs.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='cancelDiskMigrationJobRequest',
        request_type_name='VmmigrationProjectsLocationsSourcesDiskMigrationJobsCancelRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new disk migration job in a given Source.

      Args:
        request: (VmmigrationProjectsLocationsSourcesDiskMigrationJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/diskMigrationJobs',
        http_method='POST',
        method_id='vmmigration.projects.locations.sources.diskMigrationJobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['diskMigrationJobId', 'requestId'],
        relative_path='v1alpha1/{+parent}/diskMigrationJobs',
        request_field='diskMigrationJob',
        request_type_name='VmmigrationProjectsLocationsSourcesDiskMigrationJobsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single DiskMigrationJob.

      Args:
        request: (VmmigrationProjectsLocationsSourcesDiskMigrationJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/diskMigrationJobs/{diskMigrationJobsId}',
        http_method='DELETE',
        method_id='vmmigration.projects.locations.sources.diskMigrationJobs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsSourcesDiskMigrationJobsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single DiskMigrationJob.

      Args:
        request: (VmmigrationProjectsLocationsSourcesDiskMigrationJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DiskMigrationJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/diskMigrationJobs/{diskMigrationJobsId}',
        http_method='GET',
        method_id='vmmigration.projects.locations.sources.diskMigrationJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsSourcesDiskMigrationJobsGetRequest',
        response_type_name='DiskMigrationJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists DiskMigrationJobs in a given Source.

      Args:
        request: (VmmigrationProjectsLocationsSourcesDiskMigrationJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDiskMigrationJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/diskMigrationJobs',
        http_method='GET',
        method_id='vmmigration.projects.locations.sources.diskMigrationJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/diskMigrationJobs',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsSourcesDiskMigrationJobsListRequest',
        response_type_name='ListDiskMigrationJobsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single DiskMigrationJob.

      Args:
        request: (VmmigrationProjectsLocationsSourcesDiskMigrationJobsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/diskMigrationJobs/{diskMigrationJobsId}',
        http_method='PATCH',
        method_id='vmmigration.projects.locations.sources.diskMigrationJobs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='diskMigrationJob',
        request_type_name='VmmigrationProjectsLocationsSourcesDiskMigrationJobsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Run(self, request, global_params=None):
      r"""Runs the disk migration job.

      Args:
        request: (VmmigrationProjectsLocationsSourcesDiskMigrationJobsRunRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Run')
      return self._RunMethod(
          config, request, global_params=global_params)

    Run.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/diskMigrationJobs/{diskMigrationJobsId}:run',
        http_method='POST',
        method_id='vmmigration.projects.locations.sources.diskMigrationJobs.run',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:run',
        request_field='runDiskMigrationJobRequest',
        request_type_name='VmmigrationProjectsLocationsSourcesDiskMigrationJobsRunRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsSourcesMigratingVmsCloneJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_sources_migratingVms_cloneJobs resource."""

    _NAME = 'projects_locations_sources_migratingVms_cloneJobs'

    def __init__(self, client):
      super(VmmigrationV1alpha1.ProjectsLocationsSourcesMigratingVmsCloneJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Initiates the cancellation of a running clone job.

      Args:
        request: (VmmigrationProjectsLocationsSourcesMigratingVmsCloneJobsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/migratingVms/{migratingVmsId}/cloneJobs/{cloneJobsId}:cancel',
        http_method='POST',
        method_id='vmmigration.projects.locations.sources.migratingVms.cloneJobs.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='cancelCloneJobRequest',
        request_type_name='VmmigrationProjectsLocationsSourcesMigratingVmsCloneJobsCancelRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Initiates a Clone of a specific migrating VM.

      Args:
        request: (VmmigrationProjectsLocationsSourcesMigratingVmsCloneJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/migratingVms/{migratingVmsId}/cloneJobs',
        http_method='POST',
        method_id='vmmigration.projects.locations.sources.migratingVms.cloneJobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['cloneJobId', 'requestId'],
        relative_path='v1alpha1/{+parent}/cloneJobs',
        request_field='cloneJob',
        request_type_name='VmmigrationProjectsLocationsSourcesMigratingVmsCloneJobsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single CloneJob.

      Args:
        request: (VmmigrationProjectsLocationsSourcesMigratingVmsCloneJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CloneJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/migratingVms/{migratingVmsId}/cloneJobs/{cloneJobsId}',
        http_method='GET',
        method_id='vmmigration.projects.locations.sources.migratingVms.cloneJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsSourcesMigratingVmsCloneJobsGetRequest',
        response_type_name='CloneJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the CloneJobs of a migrating VM. Only 25 most recent CloneJobs are listed.

      Args:
        request: (VmmigrationProjectsLocationsSourcesMigratingVmsCloneJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListCloneJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/migratingVms/{migratingVmsId}/cloneJobs',
        http_method='GET',
        method_id='vmmigration.projects.locations.sources.migratingVms.cloneJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/cloneJobs',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsSourcesMigratingVmsCloneJobsListRequest',
        response_type_name='ListCloneJobsResponse',
        supports_download=False,
    )

  class ProjectsLocationsSourcesMigratingVmsCutoverJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_sources_migratingVms_cutoverJobs resource."""

    _NAME = 'projects_locations_sources_migratingVms_cutoverJobs'

    def __init__(self, client):
      super(VmmigrationV1alpha1.ProjectsLocationsSourcesMigratingVmsCutoverJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Initiates the cancellation of a running cutover job.

      Args:
        request: (VmmigrationProjectsLocationsSourcesMigratingVmsCutoverJobsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/migratingVms/{migratingVmsId}/cutoverJobs/{cutoverJobsId}:cancel',
        http_method='POST',
        method_id='vmmigration.projects.locations.sources.migratingVms.cutoverJobs.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='cancelCutoverJobRequest',
        request_type_name='VmmigrationProjectsLocationsSourcesMigratingVmsCutoverJobsCancelRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Initiates a Cutover of a specific migrating VM. The returned LRO is completed when the cutover job resource is created and the job is initiated.

      Args:
        request: (VmmigrationProjectsLocationsSourcesMigratingVmsCutoverJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/migratingVms/{migratingVmsId}/cutoverJobs',
        http_method='POST',
        method_id='vmmigration.projects.locations.sources.migratingVms.cutoverJobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['cutoverJobId', 'requestId'],
        relative_path='v1alpha1/{+parent}/cutoverJobs',
        request_field='cutoverJob',
        request_type_name='VmmigrationProjectsLocationsSourcesMigratingVmsCutoverJobsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single CutoverJob.

      Args:
        request: (VmmigrationProjectsLocationsSourcesMigratingVmsCutoverJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CutoverJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/migratingVms/{migratingVmsId}/cutoverJobs/{cutoverJobsId}',
        http_method='GET',
        method_id='vmmigration.projects.locations.sources.migratingVms.cutoverJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsSourcesMigratingVmsCutoverJobsGetRequest',
        response_type_name='CutoverJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the CutoverJobs of a migrating VM. Only 25 most recent CutoverJobs are listed.

      Args:
        request: (VmmigrationProjectsLocationsSourcesMigratingVmsCutoverJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListCutoverJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/migratingVms/{migratingVmsId}/cutoverJobs',
        http_method='GET',
        method_id='vmmigration.projects.locations.sources.migratingVms.cutoverJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/cutoverJobs',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsSourcesMigratingVmsCutoverJobsListRequest',
        response_type_name='ListCutoverJobsResponse',
        supports_download=False,
    )

  class ProjectsLocationsSourcesMigratingVmsReplicationCyclesService(base_api.BaseApiService):
    """Service class for the projects_locations_sources_migratingVms_replicationCycles resource."""

    _NAME = 'projects_locations_sources_migratingVms_replicationCycles'

    def __init__(self, client):
      super(VmmigrationV1alpha1.ProjectsLocationsSourcesMigratingVmsReplicationCyclesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details of a single ReplicationCycle.

      Args:
        request: (VmmigrationProjectsLocationsSourcesMigratingVmsReplicationCyclesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ReplicationCycle) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/migratingVms/{migratingVmsId}/replicationCycles/{replicationCyclesId}',
        http_method='GET',
        method_id='vmmigration.projects.locations.sources.migratingVms.replicationCycles.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsSourcesMigratingVmsReplicationCyclesGetRequest',
        response_type_name='ReplicationCycle',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ReplicationCycles in a given MigratingVM.

      Args:
        request: (VmmigrationProjectsLocationsSourcesMigratingVmsReplicationCyclesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListReplicationCyclesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/migratingVms/{migratingVmsId}/replicationCycles',
        http_method='GET',
        method_id='vmmigration.projects.locations.sources.migratingVms.replicationCycles.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/replicationCycles',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsSourcesMigratingVmsReplicationCyclesListRequest',
        response_type_name='ListReplicationCyclesResponse',
        supports_download=False,
    )

  class ProjectsLocationsSourcesMigratingVmsService(base_api.BaseApiService):
    """Service class for the projects_locations_sources_migratingVms resource."""

    _NAME = 'projects_locations_sources_migratingVms'

    def __init__(self, client):
      super(VmmigrationV1alpha1.ProjectsLocationsSourcesMigratingVmsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new MigratingVm in a given Source.

      Args:
        request: (VmmigrationProjectsLocationsSourcesMigratingVmsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/migratingVms',
        http_method='POST',
        method_id='vmmigration.projects.locations.sources.migratingVms.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['migratingVmId', 'requestId'],
        relative_path='v1alpha1/{+parent}/migratingVms',
        request_field='migratingVm',
        request_type_name='VmmigrationProjectsLocationsSourcesMigratingVmsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single MigratingVm.

      Args:
        request: (VmmigrationProjectsLocationsSourcesMigratingVmsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/migratingVms/{migratingVmsId}',
        http_method='DELETE',
        method_id='vmmigration.projects.locations.sources.migratingVms.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsSourcesMigratingVmsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def FinalizeMigration(self, request, global_params=None):
      r"""Marks a migration as completed, deleting migration resources that are no longer being used. Only applicable after cutover is done.

      Args:
        request: (VmmigrationProjectsLocationsSourcesMigratingVmsFinalizeMigrationRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('FinalizeMigration')
      return self._RunMethod(
          config, request, global_params=global_params)

    FinalizeMigration.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/migratingVms/{migratingVmsId}:finalizeMigration',
        http_method='POST',
        method_id='vmmigration.projects.locations.sources.migratingVms.finalizeMigration',
        ordered_params=['migratingVm'],
        path_params=['migratingVm'],
        query_params=[],
        relative_path='v1alpha1/{+migratingVm}:finalizeMigration',
        request_field='finalizeMigrationRequest',
        request_type_name='VmmigrationProjectsLocationsSourcesMigratingVmsFinalizeMigrationRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single MigratingVm.

      Args:
        request: (VmmigrationProjectsLocationsSourcesMigratingVmsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MigratingVm) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/migratingVms/{migratingVmsId}',
        http_method='GET',
        method_id='vmmigration.projects.locations.sources.migratingVms.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsSourcesMigratingVmsGetRequest',
        response_type_name='MigratingVm',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists MigratingVms in a given Source.

      Args:
        request: (VmmigrationProjectsLocationsSourcesMigratingVmsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMigratingVmsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/migratingVms',
        http_method='GET',
        method_id='vmmigration.projects.locations.sources.migratingVms.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'view'],
        relative_path='v1alpha1/{+parent}/migratingVms',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsSourcesMigratingVmsListRequest',
        response_type_name='ListMigratingVmsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single MigratingVm.

      Args:
        request: (VmmigrationProjectsLocationsSourcesMigratingVmsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/migratingVms/{migratingVmsId}',
        http_method='PATCH',
        method_id='vmmigration.projects.locations.sources.migratingVms.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='migratingVm',
        request_type_name='VmmigrationProjectsLocationsSourcesMigratingVmsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def PauseMigration(self, request, global_params=None):
      r"""Pauses a migration for a VM. If cycle tasks are running they will be cancelled, preserving source task data. Further replication cycles will not be triggered while the VM is paused.

      Args:
        request: (VmmigrationProjectsLocationsSourcesMigratingVmsPauseMigrationRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('PauseMigration')
      return self._RunMethod(
          config, request, global_params=global_params)

    PauseMigration.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/migratingVms/{migratingVmsId}:pauseMigration',
        http_method='POST',
        method_id='vmmigration.projects.locations.sources.migratingVms.pauseMigration',
        ordered_params=['migratingVm'],
        path_params=['migratingVm'],
        query_params=[],
        relative_path='v1alpha1/{+migratingVm}:pauseMigration',
        request_field='pauseMigrationRequest',
        request_type_name='VmmigrationProjectsLocationsSourcesMigratingVmsPauseMigrationRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def ResumeMigration(self, request, global_params=None):
      r"""Resumes a migration for a VM. When called on a paused migration, will start the process of uploading data and creating snapshots; when called on a completed cut-over migration, will update the migration to active state and start the process of uploading data and creating snapshots.

      Args:
        request: (VmmigrationProjectsLocationsSourcesMigratingVmsResumeMigrationRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('ResumeMigration')
      return self._RunMethod(
          config, request, global_params=global_params)

    ResumeMigration.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/migratingVms/{migratingVmsId}:resumeMigration',
        http_method='POST',
        method_id='vmmigration.projects.locations.sources.migratingVms.resumeMigration',
        ordered_params=['migratingVm'],
        path_params=['migratingVm'],
        query_params=[],
        relative_path='v1alpha1/{+migratingVm}:resumeMigration',
        request_field='resumeMigrationRequest',
        request_type_name='VmmigrationProjectsLocationsSourcesMigratingVmsResumeMigrationRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def StartMigration(self, request, global_params=None):
      r"""Starts migration for a VM. Starts the process of uploading data and creating snapshots, in replication cycles scheduled by the policy.

      Args:
        request: (VmmigrationProjectsLocationsSourcesMigratingVmsStartMigrationRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('StartMigration')
      return self._RunMethod(
          config, request, global_params=global_params)

    StartMigration.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/migratingVms/{migratingVmsId}:startMigration',
        http_method='POST',
        method_id='vmmigration.projects.locations.sources.migratingVms.startMigration',
        ordered_params=['migratingVm'],
        path_params=['migratingVm'],
        query_params=[],
        relative_path='v1alpha1/{+migratingVm}:startMigration',
        request_field='startMigrationRequest',
        request_type_name='VmmigrationProjectsLocationsSourcesMigratingVmsStartMigrationRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsSourcesUtilizationReportsService(base_api.BaseApiService):
    """Service class for the projects_locations_sources_utilizationReports resource."""

    _NAME = 'projects_locations_sources_utilizationReports'

    def __init__(self, client):
      super(VmmigrationV1alpha1.ProjectsLocationsSourcesUtilizationReportsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new UtilizationReport.

      Args:
        request: (VmmigrationProjectsLocationsSourcesUtilizationReportsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/utilizationReports',
        http_method='POST',
        method_id='vmmigration.projects.locations.sources.utilizationReports.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'utilizationReportId'],
        relative_path='v1alpha1/{+parent}/utilizationReports',
        request_field='utilizationReport',
        request_type_name='VmmigrationProjectsLocationsSourcesUtilizationReportsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Utilization Report.

      Args:
        request: (VmmigrationProjectsLocationsSourcesUtilizationReportsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/utilizationReports/{utilizationReportsId}',
        http_method='DELETE',
        method_id='vmmigration.projects.locations.sources.utilizationReports.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsSourcesUtilizationReportsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a single Utilization Report.

      Args:
        request: (VmmigrationProjectsLocationsSourcesUtilizationReportsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (UtilizationReport) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/utilizationReports/{utilizationReportsId}',
        http_method='GET',
        method_id='vmmigration.projects.locations.sources.utilizationReports.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsSourcesUtilizationReportsGetRequest',
        response_type_name='UtilizationReport',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Utilization Reports of the given Source.

      Args:
        request: (VmmigrationProjectsLocationsSourcesUtilizationReportsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListUtilizationReportsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/utilizationReports',
        http_method='GET',
        method_id='vmmigration.projects.locations.sources.utilizationReports.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'view'],
        relative_path='v1alpha1/{+parent}/utilizationReports',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsSourcesUtilizationReportsListRequest',
        response_type_name='ListUtilizationReportsResponse',
        supports_download=False,
    )

  class ProjectsLocationsSourcesService(base_api.BaseApiService):
    """Service class for the projects_locations_sources resource."""

    _NAME = 'projects_locations_sources'

    def __init__(self, client):
      super(VmmigrationV1alpha1.ProjectsLocationsSourcesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Source in a given project and location.

      Args:
        request: (VmmigrationProjectsLocationsSourcesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources',
        http_method='POST',
        method_id='vmmigration.projects.locations.sources.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'sourceId'],
        relative_path='v1alpha1/{+parent}/sources',
        request_field='source',
        request_type_name='VmmigrationProjectsLocationsSourcesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Source.

      Args:
        request: (VmmigrationProjectsLocationsSourcesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}',
        http_method='DELETE',
        method_id='vmmigration.projects.locations.sources.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsSourcesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def FetchInventory(self, request, global_params=None):
      r"""List remote source's inventory of VMs. The remote source is the onprem vCenter (remote in the sense it's not in Compute Engine). The inventory describes the list of existing VMs in that source. Note that this operation lists the VMs on the remote source, as opposed to listing the MigratingVms resources in the vmmigration service.

      Args:
        request: (VmmigrationProjectsLocationsSourcesFetchInventoryRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FetchInventoryResponse) The response message.
      """
      config = self.GetMethodConfig('FetchInventory')
      return self._RunMethod(
          config, request, global_params=global_params)

    FetchInventory.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}:fetchInventory',
        http_method='GET',
        method_id='vmmigration.projects.locations.sources.fetchInventory',
        ordered_params=['source'],
        path_params=['source'],
        query_params=['forceRefresh', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+source}:fetchInventory',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsSourcesFetchInventoryRequest',
        response_type_name='FetchInventoryResponse',
        supports_download=False,
    )

    def FetchStorageInventory(self, request, global_params=None):
      r"""List remote source's inventory of storage resources. The remote source is another cloud vendor (e.g. AWS, Azure). The inventory describes the list of existing storage resources in that source. Note that this operation lists the resources on the remote source, as opposed to listing the MigratingVms resources in the vmmigration service.

      Args:
        request: (VmmigrationProjectsLocationsSourcesFetchStorageInventoryRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FetchStorageInventoryResponse) The response message.
      """
      config = self.GetMethodConfig('FetchStorageInventory')
      return self._RunMethod(
          config, request, global_params=global_params)

    FetchStorageInventory.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}:fetchStorageInventory',
        http_method='GET',
        method_id='vmmigration.projects.locations.sources.fetchStorageInventory',
        ordered_params=['source'],
        path_params=['source'],
        query_params=['forceRefresh', 'pageSize', 'pageToken', 'type'],
        relative_path='v1alpha1/{+source}:fetchStorageInventory',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsSourcesFetchStorageInventoryRequest',
        response_type_name='FetchStorageInventoryResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Source.

      Args:
        request: (VmmigrationProjectsLocationsSourcesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Source) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}',
        http_method='GET',
        method_id='vmmigration.projects.locations.sources.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsSourcesGetRequest',
        response_type_name='Source',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Sources in a given project and location.

      Args:
        request: (VmmigrationProjectsLocationsSourcesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSourcesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources',
        http_method='GET',
        method_id='vmmigration.projects.locations.sources.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/sources',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsSourcesListRequest',
        response_type_name='ListSourcesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Source.

      Args:
        request: (VmmigrationProjectsLocationsSourcesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}',
        http_method='PATCH',
        method_id='vmmigration.projects.locations.sources.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='source',
        request_type_name='VmmigrationProjectsLocationsSourcesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsTargetProjectsService(base_api.BaseApiService):
    """Service class for the projects_locations_targetProjects resource."""

    _NAME = 'projects_locations_targetProjects'

    def __init__(self, client):
      super(VmmigrationV1alpha1.ProjectsLocationsTargetProjectsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new TargetProject in a given project. NOTE: TargetProject is a global resource; hence the only supported value for location is `global`.

      Args:
        request: (VmmigrationProjectsLocationsTargetProjectsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/targetProjects',
        http_method='POST',
        method_id='vmmigration.projects.locations.targetProjects.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'targetProjectId'],
        relative_path='v1alpha1/{+parent}/targetProjects',
        request_field='targetProject',
        request_type_name='VmmigrationProjectsLocationsTargetProjectsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single TargetProject. NOTE: TargetProject is a global resource; hence the only supported value for location is `global`.

      Args:
        request: (VmmigrationProjectsLocationsTargetProjectsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/targetProjects/{targetProjectsId}',
        http_method='DELETE',
        method_id='vmmigration.projects.locations.targetProjects.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsTargetProjectsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single TargetProject. NOTE: TargetProject is a global resource; hence the only supported value for location is `global`.

      Args:
        request: (VmmigrationProjectsLocationsTargetProjectsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TargetProject) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/targetProjects/{targetProjectsId}',
        http_method='GET',
        method_id='vmmigration.projects.locations.targetProjects.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsTargetProjectsGetRequest',
        response_type_name='TargetProject',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists TargetProjects in a given project. NOTE: TargetProject is a global resource; hence the only supported value for location is `global`.

      Args:
        request: (VmmigrationProjectsLocationsTargetProjectsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListTargetProjectsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/targetProjects',
        http_method='GET',
        method_id='vmmigration.projects.locations.targetProjects.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/targetProjects',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsTargetProjectsListRequest',
        response_type_name='ListTargetProjectsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single TargetProject. NOTE: TargetProject is a global resource; hence the only supported value for location is `global`.

      Args:
        request: (VmmigrationProjectsLocationsTargetProjectsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/targetProjects/{targetProjectsId}',
        http_method='PATCH',
        method_id='vmmigration.projects.locations.targetProjects.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='targetProject',
        request_type_name='VmmigrationProjectsLocationsTargetProjectsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(VmmigrationV1alpha1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (VmmigrationProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='vmmigration.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (VmmigrationProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='vmmigration.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/locations',
        request_field='',
        request_type_name='VmmigrationProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(VmmigrationV1alpha1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
