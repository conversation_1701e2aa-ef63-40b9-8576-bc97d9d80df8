"""Generated message classes for vmmigration version v1alpha1.

Use the Migrate to Virtual Machines API to programmatically migrate workloads.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'vmmigration'


class AccessKeyCredentials(_messages.Message):
  r"""Message describing AWS Credentials using access key id and secret.

  Fields:
    accessKeyId: AWS access key ID.
    secretAccessKey: Input only. AWS secret access key.
    sessionToken: Input only. AWS session token. Used only when AWS security
      token service (STS) is responsible for creating the temporary
      credentials.
  """

  accessKeyId = _messages.StringField(1)
  secretAccessKey = _messages.StringField(2)
  sessionToken = _messages.StringField(3)


class AdaptationModifier(_messages.Message):
  r"""AdaptationModifier a modifier to be used for configuration of the OS
  adaptation process.

  Fields:
    name: Optional. The name of the modifier.
    value: Optional. The value of the modifier. The actual value depends on
      the modifier and can also be empty.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class AdaptingOSStep(_messages.Message):
  r"""AdaptingOSStep contains specific step details."""


class AddGroupMigrationRequest(_messages.Message):
  r"""Request message for 'AddGroupMigration' request.

  Fields:
    migratingVm: The full path name of the MigratingVm to add.
  """

  migratingVm = _messages.StringField(1)


class ApplianceVersion(_messages.Message):
  r"""Describes an appliance version.

  Fields:
    critical: Determine whether it's critical to upgrade the appliance to this
      version.
    releaseNotesUri: Link to a page that contains the version release notes.
    uri: A link for downloading the version.
    version: The appliance version.
  """

  critical = _messages.BooleanField(1)
  releaseNotesUri = _messages.StringField(2)
  uri = _messages.StringField(3)
  version = _messages.StringField(4)


class AppliedLicense(_messages.Message):
  r"""AppliedLicense holds the license data returned by adaptation module
  report.

  Enums:
    TypeValueValuesEnum: The license type that was used in OS adaptation.

  Fields:
    osLicense: The OS license returned from the adaptation module's report.
    type: The license type that was used in OS adaptation.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The license type that was used in OS adaptation.

    Values:
      TYPE_UNSPECIFIED: Unspecified license for the OS.
      NONE: No license available for the OS.
      PAYG: The license type is Pay As You Go license type.
      BYOL: The license type is Bring Your Own License type.
    """
    TYPE_UNSPECIFIED = 0
    NONE = 1
    PAYG = 2
    BYOL = 3

  osLicense = _messages.StringField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class AvailableUpdates(_messages.Message):
  r"""Holds information about the available versions for upgrade.

  Fields:
    inPlaceUpdate: The latest version for in place update. The current
      appliance can be updated to this version using the API or m4c CLI.
    newDeployableAppliance: The newest deployable version of the appliance.
      The current appliance can't be updated into this version, and the owner
      must manually deploy this OVA to a new appliance.
  """

  inPlaceUpdate = _messages.MessageField('ApplianceVersion', 1)
  newDeployableAppliance = _messages.MessageField('ApplianceVersion', 2)


class AwsDiskDetails(_messages.Message):
  r"""The details of an AWS instance disk.

  Fields:
    diskNumber: Output only. The ordinal number of the disk.
    sizeGb: Output only. Size in GB.
    volumeId: Output only. AWS volume ID.
  """

  diskNumber = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  sizeGb = _messages.IntegerField(2)
  volumeId = _messages.StringField(3)


class AwsSecurityGroup(_messages.Message):
  r"""AwsSecurityGroup describes a security group of an AWS VM.

  Fields:
    id: The AWS security group id.
    name: The AWS security group name.
  """

  id = _messages.StringField(1)
  name = _messages.StringField(2)


class AwsSourceDetails(_messages.Message):
  r"""AwsSourceDetails message describes a specific source details for the AWS
  source type.

  Enums:
    StateValueValuesEnum: Output only. State of the source as determined by
      the health check.

  Messages:
    MigrationResourcesUserTagsValue: User specified tags to add to every M2VM
      generated resource in AWS. These tags will be set in addition to the
      default tags that are set as part of the migration process. The tags
      must not begin with the reserved prefix `m2vm`.

  Fields:
    accessKeyCreds: AWS Credentials using access key id and secret.
    awsRegion: Immutable. The AWS region that the source VMs will be migrated
      from.
    error: Output only. Provides details on the state of the Source in case of
      an error.
    inventorySecurityGroupNames: AWS security group names to limit the scope
      of the source inventory.
    inventoryTagList: AWS resource tags to limit the scope of the source
      inventory.
    migrationResourcesUserTags: User specified tags to add to every M2VM
      generated resource in AWS. These tags will be set in addition to the
      default tags that are set as part of the migration process. The tags
      must not begin with the reserved prefix `m2vm`.
    publicIp: Output only. The source's public IP. All communication initiated
      by this source will originate from this IP.
    state: Output only. State of the source as determined by the health check.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the source as determined by the health check.

    Values:
      STATE_UNSPECIFIED: The state is unknown. This is used for API
        compatibility only and is not used by the system.
      PENDING: The state was not sampled by the health checks yet.
      FAILED: The source is available but might not be usable yet due to
        invalid credentials or another reason. The error message will contain
        further details.
      ACTIVE: The source exists and its credentials were verified.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    FAILED = 2
    ACTIVE = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MigrationResourcesUserTagsValue(_messages.Message):
    r"""User specified tags to add to every M2VM generated resource in AWS.
    These tags will be set in addition to the default tags that are set as
    part of the migration process. The tags must not begin with the reserved
    prefix `m2vm`.

    Messages:
      AdditionalProperty: An additional property for a
        MigrationResourcesUserTagsValue object.

    Fields:
      additionalProperties: Additional properties of type
        MigrationResourcesUserTagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MigrationResourcesUserTagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  accessKeyCreds = _messages.MessageField('AccessKeyCredentials', 1)
  awsRegion = _messages.StringField(2)
  error = _messages.MessageField('Status', 3)
  inventorySecurityGroupNames = _messages.StringField(4, repeated=True)
  inventoryTagList = _messages.MessageField('Tag', 5, repeated=True)
  migrationResourcesUserTags = _messages.MessageField('MigrationResourcesUserTagsValue', 6)
  publicIp = _messages.StringField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)


class AwsSourceDiskDetails(_messages.Message):
  r"""Represents the source AWS Disk details.

  Enums:
    DiskTypeValueValuesEnum: Optional. Output only. Disk type.

  Messages:
    TagsValue: Optional. Output only. A map of AWS volume tags.

  Fields:
    diskType: Optional. Output only. Disk type.
    sizeGib: Output only. Size in GiB.
    tags: Optional. Output only. A map of AWS volume tags.
    volumeId: Required. AWS volume ID.
  """

  class DiskTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Output only. Disk type.

    Values:
      TYPE_UNSPECIFIED: Unspecified AWS disk type. Should not be used.
      GP2: GP2 disk type.
      GP3: GP3 disk type.
      IO1: IO1 disk type.
      IO2: IO2 disk type.
      ST1: ST1 disk type.
      SC1: SC1 disk type.
      STANDARD: Standard disk type.
    """
    TYPE_UNSPECIFIED = 0
    GP2 = 1
    GP3 = 2
    IO1 = 3
    IO2 = 4
    ST1 = 5
    SC1 = 6
    STANDARD = 7

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""Optional. Output only. A map of AWS volume tags.

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  diskType = _messages.EnumField('DiskTypeValueValuesEnum', 1)
  sizeGib = _messages.IntegerField(2)
  tags = _messages.MessageField('TagsValue', 3)
  volumeId = _messages.StringField(4)


class AwsSourceVmDetails(_messages.Message):
  r"""Represent the source AWS VM details.

  Enums:
    ArchitectureValueValuesEnum: Output only. The VM architecture.
    FirmwareValueValuesEnum: Output only. The firmware type of the source VM.

  Fields:
    architecture: Output only. The VM architecture.
    committedStorageBytes: Output only. The total size of the disks being
      migrated in bytes.
    disks: Output only. The disks attached to the source VM.
    firmware: Output only. The firmware type of the source VM.
    vmCapabilitiesInfo: Output only. Information about VM capabilities needed
      for some Compute Engine features.
  """

  class ArchitectureValueValuesEnum(_messages.Enum):
    r"""Output only. The VM architecture.

    Values:
      VM_ARCHITECTURE_UNSPECIFIED: The architecture is unknown.
      VM_ARCHITECTURE_X86_FAMILY: The architecture is one of the x86
        architectures.
      VM_ARCHITECTURE_ARM64: The architecture is ARM64.
    """
    VM_ARCHITECTURE_UNSPECIFIED = 0
    VM_ARCHITECTURE_X86_FAMILY = 1
    VM_ARCHITECTURE_ARM64 = 2

  class FirmwareValueValuesEnum(_messages.Enum):
    r"""Output only. The firmware type of the source VM.

    Values:
      FIRMWARE_UNSPECIFIED: The firmware is unknown.
      EFI: The firmware is EFI.
      BIOS: The firmware is BIOS.
    """
    FIRMWARE_UNSPECIFIED = 0
    EFI = 1
    BIOS = 2

  architecture = _messages.EnumField('ArchitectureValueValuesEnum', 1)
  committedStorageBytes = _messages.IntegerField(2)
  disks = _messages.MessageField('AwsDiskDetails', 3, repeated=True)
  firmware = _messages.EnumField('FirmwareValueValuesEnum', 4)
  vmCapabilitiesInfo = _messages.MessageField('VmCapabilities', 5)


class AwsVmDetails(_messages.Message):
  r"""AwsVmDetails describes a VM in AWS.

  Enums:
    ArchitectureValueValuesEnum: The CPU architecture.
    BootOptionValueValuesEnum: The VM Boot Option.
    PowerStateValueValuesEnum: Output only. The power state of the VM at the
      moment list was taken.
    VirtualizationTypeValueValuesEnum: The virtualization type.

  Messages:
    TagsValue: The tags of the VM.

  Fields:
    architecture: The CPU architecture.
    bootOption: The VM Boot Option.
    committedStorageMb: The total size of the storage allocated to the VM in
      MB.
    cpuCount: The number of cpus the VM has.
    diskCount: The number of disks the VM has.
    displayName: The display name of the VM. Note that this value is not
      necessarily unique.
    instanceType: The instance type of the VM.
    memoryMb: The memory size of the VM in MB.
    osDescription: The VM's OS.
    powerState: Output only. The power state of the VM at the moment list was
      taken.
    securityGroups: The security groups the VM belongs to.
    sourceDescription: The descriptive name of the AWS's source this VM is
      connected to.
    sourceId: The id of the AWS's source this VM is connected to.
    tags: The tags of the VM.
    virtualizationType: The virtualization type.
    vmId: The VM ID in AWS.
    vpcId: The VPC ID the VM belongs to.
    zone: The AWS zone of the VM.
  """

  class ArchitectureValueValuesEnum(_messages.Enum):
    r"""The CPU architecture.

    Values:
      VM_ARCHITECTURE_UNSPECIFIED: The architecture is unknown.
      I386: The architecture is I386.
      X86_64: The architecture is X86_64.
      ARM64: The architecture is ARM64.
      X86_64_MAC: The architecture is X86_64_MAC.
    """
    VM_ARCHITECTURE_UNSPECIFIED = 0
    I386 = 1
    X86_64 = 2
    ARM64 = 3
    X86_64_MAC = 4

  class BootOptionValueValuesEnum(_messages.Enum):
    r"""The VM Boot Option.

    Values:
      BOOT_OPTION_UNSPECIFIED: The boot option is unknown.
      EFI: The boot option is UEFI.
      BIOS: The boot option is LEGACY-BIOS.
    """
    BOOT_OPTION_UNSPECIFIED = 0
    EFI = 1
    BIOS = 2

  class PowerStateValueValuesEnum(_messages.Enum):
    r"""Output only. The power state of the VM at the moment list was taken.

    Values:
      POWER_STATE_UNSPECIFIED: Power state is not specified.
      ON: The VM is turned on.
      OFF: The VM is turned off.
      SUSPENDED: The VM is suspended. This is similar to hibernation or sleep
        mode.
      PENDING: The VM is starting.
    """
    POWER_STATE_UNSPECIFIED = 0
    ON = 1
    OFF = 2
    SUSPENDED = 3
    PENDING = 4

  class VirtualizationTypeValueValuesEnum(_messages.Enum):
    r"""The virtualization type.

    Values:
      VM_VIRTUALIZATION_TYPE_UNSPECIFIED: The virtualization type is unknown.
      HVM: The virtualziation type is HVM.
      PARAVIRTUAL: The virtualziation type is PARAVIRTUAL.
    """
    VM_VIRTUALIZATION_TYPE_UNSPECIFIED = 0
    HVM = 1
    PARAVIRTUAL = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""The tags of the VM.

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  architecture = _messages.EnumField('ArchitectureValueValuesEnum', 1)
  bootOption = _messages.EnumField('BootOptionValueValuesEnum', 2)
  committedStorageMb = _messages.IntegerField(3)
  cpuCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  diskCount = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  displayName = _messages.StringField(6)
  instanceType = _messages.StringField(7)
  memoryMb = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  osDescription = _messages.StringField(9)
  powerState = _messages.EnumField('PowerStateValueValuesEnum', 10)
  securityGroups = _messages.MessageField('AwsSecurityGroup', 11, repeated=True)
  sourceDescription = _messages.StringField(12)
  sourceId = _messages.StringField(13)
  tags = _messages.MessageField('TagsValue', 14)
  virtualizationType = _messages.EnumField('VirtualizationTypeValueValuesEnum', 15)
  vmId = _messages.StringField(16)
  vpcId = _messages.StringField(17)
  zone = _messages.StringField(18)


class AwsVmsDetails(_messages.Message):
  r"""AWSVmsDetails describes VMs in AWS.

  Fields:
    details: The details of the AWS VMs.
  """

  details = _messages.MessageField('AwsVmDetails', 1, repeated=True)


class AzureDiskDetails(_messages.Message):
  r"""The details of an Azure VM disk.

  Fields:
    diskId: Output only. Azure disk ID.
    diskNumber: Output only. The ordinal number of the disk.
    sizeGb: Output only. Size in GB.
  """

  diskId = _messages.StringField(1)
  diskNumber = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  sizeGb = _messages.IntegerField(3)


class AzureSourceDetails(_messages.Message):
  r"""AzureSourceDetails message describes a specific source details for the
  Azure source type.

  Enums:
    StateValueValuesEnum: Output only. State of the source as determined by
      the health check.

  Messages:
    MigrationResourcesUserTagsValue: User specified tags to add to every M2VM
      generated resource in Azure. These tags will be set in addition to the
      default tags that are set as part of the migration process. The tags
      must not begin with the reserved prefix `m4ce` or `m2vm`.

  Fields:
    azureLocation: Immutable. The Azure location (region) that the source VMs
      will be migrated from.
    clientSecretCreds: Azure Credentials using tenant ID, client ID and
      secret.
    error: Output only. Provides details on the state of the Source in case of
      an error.
    migrationResourcesUserTags: User specified tags to add to every M2VM
      generated resource in Azure. These tags will be set in addition to the
      default tags that are set as part of the migration process. The tags
      must not begin with the reserved prefix `m4ce` or `m2vm`.
    resourceGroupId: Output only. The ID of the Azure resource group that
      contains all resources related to the migration process of this source.
    state: Output only. State of the source as determined by the health check.
    subscriptionId: Immutable. Azure subscription ID.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the source as determined by the health check.

    Values:
      STATE_UNSPECIFIED: The state is unknown. This is used for API
        compatibility only and is not used by the system.
      PENDING: The state was not sampled by the health checks yet.
      FAILED: The source is available but might not be usable yet due to
        invalid credentials or another reason. The error message will contain
        further details.
      ACTIVE: The source exists and its credentials were verified.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    FAILED = 2
    ACTIVE = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MigrationResourcesUserTagsValue(_messages.Message):
    r"""User specified tags to add to every M2VM generated resource in Azure.
    These tags will be set in addition to the default tags that are set as
    part of the migration process. The tags must not begin with the reserved
    prefix `m4ce` or `m2vm`.

    Messages:
      AdditionalProperty: An additional property for a
        MigrationResourcesUserTagsValue object.

    Fields:
      additionalProperties: Additional properties of type
        MigrationResourcesUserTagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MigrationResourcesUserTagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  azureLocation = _messages.StringField(1)
  clientSecretCreds = _messages.MessageField('ClientSecretCredentials', 2)
  error = _messages.MessageField('Status', 3)
  migrationResourcesUserTags = _messages.MessageField('MigrationResourcesUserTagsValue', 4)
  resourceGroupId = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  subscriptionId = _messages.StringField(7)


class AzureSourceVmDetails(_messages.Message):
  r"""Represent the source Azure VM details.

  Enums:
    ArchitectureValueValuesEnum: Output only. The VM architecture.
    FirmwareValueValuesEnum: Output only. The firmware type of the source VM.

  Fields:
    architecture: Output only. The VM architecture.
    committedStorageBytes: Output only. The total size of the disks being
      migrated in bytes.
    disks: Output only. The disks attached to the source VM.
    firmware: Output only. The firmware type of the source VM.
    vmCapabilitiesInfo: Output only. Information about VM capabilities needed
      for some Compute Engine features.
  """

  class ArchitectureValueValuesEnum(_messages.Enum):
    r"""Output only. The VM architecture.

    Values:
      VM_ARCHITECTURE_UNSPECIFIED: The architecture is unknown.
      VM_ARCHITECTURE_X86_FAMILY: The architecture is one of the x86
        architectures.
      VM_ARCHITECTURE_ARM64: The architecture is ARM64.
    """
    VM_ARCHITECTURE_UNSPECIFIED = 0
    VM_ARCHITECTURE_X86_FAMILY = 1
    VM_ARCHITECTURE_ARM64 = 2

  class FirmwareValueValuesEnum(_messages.Enum):
    r"""Output only. The firmware type of the source VM.

    Values:
      FIRMWARE_UNSPECIFIED: The firmware is unknown.
      EFI: The firmware is EFI.
      BIOS: The firmware is BIOS.
    """
    FIRMWARE_UNSPECIFIED = 0
    EFI = 1
    BIOS = 2

  architecture = _messages.EnumField('ArchitectureValueValuesEnum', 1)
  committedStorageBytes = _messages.IntegerField(2)
  disks = _messages.MessageField('AzureDiskDetails', 3, repeated=True)
  firmware = _messages.EnumField('FirmwareValueValuesEnum', 4)
  vmCapabilitiesInfo = _messages.MessageField('VmCapabilities', 5)


class AzureVmDetails(_messages.Message):
  r"""AzureVmDetails describes a VM in Azure.

  Enums:
    ArchitectureValueValuesEnum: The CPU architecture.
    BootOptionValueValuesEnum: The VM Boot Option.
    PowerStateValueValuesEnum: The power state of the VM at the moment list
      was taken.

  Messages:
    TagsValue: The tags of the VM.

  Fields:
    architecture: The CPU architecture.
    bootOption: The VM Boot Option.
    committedStorageMb: The total size of the storage allocated to the VM in
      MB.
    computerName: The VM's ComputerName.
    cpuCount: The number of cpus the VM has.
    diskCount: The number of disks the VM has, including OS disk.
    disks: Description of the data disks.
    memoryMb: The memory size of the VM in MB.
    osDescription: Description of the OS.
    osDisk: Description of the OS disk.
    powerState: The power state of the VM at the moment list was taken.
    tags: The tags of the VM.
    vmId: The VM full path in Azure.
    vmSize: VM size as configured in Azure. Determines the VM's hardware spec.
  """

  class ArchitectureValueValuesEnum(_messages.Enum):
    r"""The CPU architecture.

    Values:
      VM_ARCHITECTURE_UNSPECIFIED: The architecture is unknown.
      VM_ARCHITECTURE_X86_FAMILY: The architecture is one of the x86
        architectures.
      VM_ARCHITECTURE_ARM64: The architecture is ARM64.
    """
    VM_ARCHITECTURE_UNSPECIFIED = 0
    VM_ARCHITECTURE_X86_FAMILY = 1
    VM_ARCHITECTURE_ARM64 = 2

  class BootOptionValueValuesEnum(_messages.Enum):
    r"""The VM Boot Option.

    Values:
      BOOT_OPTION_UNSPECIFIED: The boot option is unknown.
      EFI: The boot option is UEFI.
      BIOS: The boot option is BIOS.
    """
    BOOT_OPTION_UNSPECIFIED = 0
    EFI = 1
    BIOS = 2

  class PowerStateValueValuesEnum(_messages.Enum):
    r"""The power state of the VM at the moment list was taken.

    Values:
      POWER_STATE_UNSPECIFIED: Power state is not specified.
      STARTING: The VM is starting.
      RUNNING: The VM is running.
      STOPPING: The VM is stopping.
      STOPPED: The VM is stopped.
      DEALLOCATING: The VM is deallocating.
      DEALLOCATED: The VM is deallocated.
      UNKNOWN: The VM's power state is unknown.
    """
    POWER_STATE_UNSPECIFIED = 0
    STARTING = 1
    RUNNING = 2
    STOPPING = 3
    STOPPED = 4
    DEALLOCATING = 5
    DEALLOCATED = 6
    UNKNOWN = 7

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""The tags of the VM.

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  architecture = _messages.EnumField('ArchitectureValueValuesEnum', 1)
  bootOption = _messages.EnumField('BootOptionValueValuesEnum', 2)
  committedStorageMb = _messages.IntegerField(3)
  computerName = _messages.StringField(4)
  cpuCount = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  diskCount = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  disks = _messages.MessageField('Disk', 7, repeated=True)
  memoryMb = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  osDescription = _messages.MessageField('OSDescription', 9)
  osDisk = _messages.MessageField('OSDisk', 10)
  powerState = _messages.EnumField('PowerStateValueValuesEnum', 11)
  tags = _messages.MessageField('TagsValue', 12)
  vmId = _messages.StringField(13)
  vmSize = _messages.StringField(14)


class AzureVmsDetails(_messages.Message):
  r"""AzureVmsDetails describes VMs in Azure.

  Fields:
    details: The details of the Azure VMs.
  """

  details = _messages.MessageField('AzureVmDetails', 1, repeated=True)


class BootDiskDefaults(_messages.Message):
  r"""BootDiskDefaults hold information about the boot disk of a VM.

  Enums:
    DiskTypeValueValuesEnum: Optional. The type of disk provisioning to use
      for the VM.

  Fields:
    deviceName: Optional. Specifies a unique device name of your choice that
      is reflected into the /dev/disk/by-id/google-* tree of a Linux operating
      system running within the instance. If not specified, the server chooses
      a default device name to apply to this disk, in the form persistent-
      disk-x, where x is a number assigned by Google Compute Engine. This
      field is only applicable for persistent disks.
    diskName: Optional. The name of the disk.
    diskType: Optional. The type of disk provisioning to use for the VM.
    encryption: Optional. The encryption to apply to the boot disk.
    image: The image to use when creating the disk.
  """

  class DiskTypeValueValuesEnum(_messages.Enum):
    r"""Optional. The type of disk provisioning to use for the VM.

    Values:
      COMPUTE_ENGINE_DISK_TYPE_UNSPECIFIED: An unspecified disk type. Will be
        used as STANDARD.
      COMPUTE_ENGINE_DISK_TYPE_STANDARD: A Standard disk type.
      COMPUTE_ENGINE_DISK_TYPE_SSD: SSD hard disk type.
      COMPUTE_ENGINE_DISK_TYPE_BALANCED: An alternative to SSD persistent
        disks that balance performance and cost.
      COMPUTE_ENGINE_DISK_TYPE_HYPERDISK_BALANCED: Hyperdisk balanced disk
        type.
    """
    COMPUTE_ENGINE_DISK_TYPE_UNSPECIFIED = 0
    COMPUTE_ENGINE_DISK_TYPE_STANDARD = 1
    COMPUTE_ENGINE_DISK_TYPE_SSD = 2
    COMPUTE_ENGINE_DISK_TYPE_BALANCED = 3
    COMPUTE_ENGINE_DISK_TYPE_HYPERDISK_BALANCED = 4

  deviceName = _messages.StringField(1)
  diskName = _messages.StringField(2)
  diskType = _messages.EnumField('DiskTypeValueValuesEnum', 3)
  encryption = _messages.MessageField('Encryption', 4)
  image = _messages.MessageField('DiskImageDefaults', 5)


class CancelCloneJobRequest(_messages.Message):
  r"""Request message for 'CancelCloneJob' request."""


class CancelCutoverJobRequest(_messages.Message):
  r"""Request message for 'CancelCutoverJob' request."""


class CancelDiskMigrationJobRequest(_messages.Message):
  r"""Request message for 'CancelDiskMigrationJob' request."""


class CancelImageImportJobRequest(_messages.Message):
  r"""Request message for 'CancelImageImportJob' request."""


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class ClientSecretCredentials(_messages.Message):
  r"""Message describing Azure Credentials using tenant ID, client ID and
  secret.

  Fields:
    clientId: Azure client ID.
    clientSecret: Input only. Azure client secret.
    tenantId: Azure tenant ID.
  """

  clientId = _messages.StringField(1)
  clientSecret = _messages.StringField(2)
  tenantId = _messages.StringField(3)


class CloneJob(_messages.Message):
  r"""CloneJob describes the process of creating a clone of a MigratingVM to
  the requested target based on the latest successful uploaded snapshots.
  While the migration cycles of a MigratingVm take place, it is possible to
  verify the uploaded VM can be started in the cloud, by creating a clone. The
  clone can be created without any downtime, and it is created using the
  latest snapshots which are already in the cloud. The cloneJob is only
  responsible for its work, not its products, which means once it is finished,
  it will never touch the instance it created. It will only delete it in case
  of the CloneJob being cancelled or upon failure to clone.

  Enums:
    StateValueValuesEnum: Output only. State of the clone job.

  Fields:
    computeEngineDisksTargetDetails: Output only. Details of the target
      Persistent Disks in Compute Engine.
    computeEngineTargetDetails: Output only. Details of the target VM in
      Compute Engine.
    computeEngineVmDetails: Output only. Details of the VM in Compute Engine.
      Deprecated: Use compute_engine_target_details instead.
    createTime: Output only. The time the clone job was created (as an API
      call, not when it was actually created in the target).
    endTime: Output only. The time the clone job was ended.
    error: Output only. Provides details for the errors that led to the Clone
      Job's state.
    name: Output only. The name of the clone.
    state: Output only. State of the clone job.
    stateTime: Output only. The time the state was last updated.
    steps: Output only. The clone steps list representing its progress.
    targetDetails: Output only. Details of the VM to create as the target of
      this clone job. Deprecated: Use compute_engine_target_details instead.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the clone job.

    Values:
      STATE_UNSPECIFIED: The state is unknown. This is used for API
        compatibility only and is not used by the system.
      PENDING: The clone job has not yet started.
      ACTIVE: The clone job is active and running.
      FAILED: The clone job finished with errors.
      SUCCEEDED: The clone job finished successfully.
      CANCELLED: The clone job was cancelled.
      CANCELLING: The clone job is being cancelled.
      ADAPTING_OS: OS adaptation is running as part of the clone job to
        generate license.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    ACTIVE = 2
    FAILED = 3
    SUCCEEDED = 4
    CANCELLED = 5
    CANCELLING = 6
    ADAPTING_OS = 7

  computeEngineDisksTargetDetails = _messages.MessageField('ComputeEngineDisksTargetDetails', 1)
  computeEngineTargetDetails = _messages.MessageField('ComputeEngineTargetDetails', 2)
  computeEngineVmDetails = _messages.MessageField('TargetVMDetails', 3)
  createTime = _messages.StringField(4)
  endTime = _messages.StringField(5)
  error = _messages.MessageField('Status', 6)
  name = _messages.StringField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  stateTime = _messages.StringField(9)
  steps = _messages.MessageField('CloneStep', 10, repeated=True)
  targetDetails = _messages.MessageField('TargetVMDetails', 11)


class CloneStep(_messages.Message):
  r"""CloneStep holds information about the clone step progress.

  Fields:
    adaptingOs: Adapting OS step.
    endTime: The time the step has ended.
    instantiatingMigratedVm: Instantiating migrated VM step.
    preparingVmDisks: Preparing VM disks step.
    startTime: The time the step has started.
  """

  adaptingOs = _messages.MessageField('AdaptingOSStep', 1)
  endTime = _messages.StringField(2)
  instantiatingMigratedVm = _messages.MessageField('InstantiatingMigratedVMStep', 3)
  preparingVmDisks = _messages.MessageField('PreparingVMDisksStep', 4)
  startTime = _messages.StringField(5)


class ComputeEngineDisk(_messages.Message):
  r"""Compute Engine disk target details.

  Enums:
    DiskTypeValueValuesEnum: Required. The disk type to use.

  Fields:
    diskId: Optional. Target Compute Engine Disk ID. This is the resource ID
      segment of the Compute Engine Disk to create. In the resource name
      compute/v1/projects/{project}/zones/{zone}/disks/disk1 "disk1" is the
      resource ID for the disk.
    diskType: Required. The disk type to use.
    replicaZones: Optional. Replication zones of the regional disk. Should be
      of the form: projects/{target-project}/locations/{replica-zone}
      Currently only one replica zone is supported.
    zone: Required. The Compute Engine zone in which to create the disk.
      Should be of the form: projects/{target-project}/locations/{zone}
  """

  class DiskTypeValueValuesEnum(_messages.Enum):
    r"""Required. The disk type to use.

    Values:
      COMPUTE_ENGINE_DISK_TYPE_UNSPECIFIED: An unspecified disk type. Will be
        used as STANDARD.
      COMPUTE_ENGINE_DISK_TYPE_STANDARD: A Standard disk type.
      COMPUTE_ENGINE_DISK_TYPE_SSD: SSD hard disk type.
      COMPUTE_ENGINE_DISK_TYPE_BALANCED: An alternative to SSD persistent
        disks that balance performance and cost.
      COMPUTE_ENGINE_DISK_TYPE_HYPERDISK_BALANCED: Hyperdisk balanced disk
        type.
    """
    COMPUTE_ENGINE_DISK_TYPE_UNSPECIFIED = 0
    COMPUTE_ENGINE_DISK_TYPE_STANDARD = 1
    COMPUTE_ENGINE_DISK_TYPE_SSD = 2
    COMPUTE_ENGINE_DISK_TYPE_BALANCED = 3
    COMPUTE_ENGINE_DISK_TYPE_HYPERDISK_BALANCED = 4

  diskId = _messages.StringField(1)
  diskType = _messages.EnumField('DiskTypeValueValuesEnum', 2)
  replicaZones = _messages.StringField(3, repeated=True)
  zone = _messages.StringField(4)


class ComputeEngineDisksTargetDefaults(_messages.Message):
  r"""ComputeEngineDisksTargetDefaults is a collection of details for creating
  Persistent Disks in a target Compute Engine project.

  Fields:
    disks: The details of each Persistent Disk to create.
    disksTargetDefaults: Details of the disk only migration target.
    targetProject: The full path of the resource of type TargetProject which
      represents the Compute Engine project in which to create the Persistent
      Disks.
    vmTargetDefaults: Details of the VM migration target.
    zone: The zone in which to create the Persistent Disks.
  """

  disks = _messages.MessageField('PersistentDiskDefaults', 1, repeated=True)
  disksTargetDefaults = _messages.MessageField('DisksMigrationDisksTargetDefaults', 2)
  targetProject = _messages.StringField(3)
  vmTargetDefaults = _messages.MessageField('DisksMigrationVmTargetDefaults', 4)
  zone = _messages.StringField(5)


class ComputeEngineDisksTargetDetails(_messages.Message):
  r"""ComputeEngineDisksTargetDetails is a collection of created Persistent
  Disks details.

  Fields:
    disks: The details of each created Persistent Disk.
    disksTargetDetails: Details of the disks-only migration target.
    vmTargetDetails: Details for the VM the migrated data disks are attached
      to.
  """

  disks = _messages.MessageField('PersistentDisk', 1, repeated=True)
  disksTargetDetails = _messages.MessageField('DisksMigrationDisksTargetDetails', 2)
  vmTargetDetails = _messages.MessageField('DisksMigrationVmTargetDetails', 3)


class ComputeEngineTargetDefaults(_messages.Message):
  r"""ComputeEngineTargetDefaults is a collection of details for creating a VM
  in a target Compute Engine project.

  Enums:
    BootConversionValueValuesEnum: Optional. By default the virtual machine
      will keep its existing boot option. Setting this property will trigger
      an internal process which will convert the virtual machine from using
      the existing boot option to another.
    BootOptionValueValuesEnum: Output only. The VM Boot Option, as set in the
      source VM.
    DiskTypeValueValuesEnum: The disk type to use in the VM.
    LicenseTypeValueValuesEnum: The license type to use in OS adaptation.

  Messages:
    LabelsValue: A map of labels to associate with the VM.
    MetadataValue: The metadata key/value pairs to assign to the VM.

  Fields:
    adaptationModifiers: Optional. AdaptationModifiers are the set of
      modifiers used during OS adaptation.
    additionalLicenses: Additional licenses to assign to the VM.
    appliedLicense: Output only. The OS license returned from the adaptation
      module report.
    bootConversion: Optional. By default the virtual machine will keep its
      existing boot option. Setting this property will trigger an internal
      process which will convert the virtual machine from using the existing
      boot option to another.
    bootOption: Output only. The VM Boot Option, as set in the source VM.
    computeScheduling: Compute instance scheduling information (if empty
      default is used).
    diskType: The disk type to use in the VM.
    enableIntegrityMonitoring: Optional. Defines whether the instance has
      integrity monitoring enabled. This can be set to true only if the VM
      boot option is EFI, and vTPM is enabled.
    enableVtpm: Optional. Defines whether the instance has vTPM enabled. This
      can be set to true only if the VM boot option is EFI.
    encryption: Optional. Immutable. The encryption to apply to the VM disks.
    hostname: The hostname to assign to the VM.
    labels: A map of labels to associate with the VM.
    licenseType: The license type to use in OS adaptation.
    machineType: The machine type to create the VM with.
    machineTypeSeries: The machine type series to create the VM with.
    metadata: The metadata key/value pairs to assign to the VM.
    networkInterfaces: List of NICs connected to this VM.
    networkTags: A list of network tags to associate with the VM.
    secureBoot: Defines whether the instance has Secure Boot enabled. This can
      be set to true only if the VM boot option is EFI.
    serviceAccount: The service account to associate the VM with.
    targetProject: The full path of the resource of type TargetProject which
      represents the Compute Engine project in which to create this VM.
    vmName: The name of the VM to create.
    zone: The zone in which to create the VM.
  """

  class BootConversionValueValuesEnum(_messages.Enum):
    r"""Optional. By default the virtual machine will keep its existing boot
    option. Setting this property will trigger an internal process which will
    convert the virtual machine from using the existing boot option to
    another.

    Values:
      BOOT_CONVERSION_UNSPECIFIED: Unspecified conversion type.
      NONE: No conversion.
      BIOS_TO_EFI: Convert from BIOS to EFI.
    """
    BOOT_CONVERSION_UNSPECIFIED = 0
    NONE = 1
    BIOS_TO_EFI = 2

  class BootOptionValueValuesEnum(_messages.Enum):
    r"""Output only. The VM Boot Option, as set in the source VM.

    Values:
      COMPUTE_ENGINE_BOOT_OPTION_UNSPECIFIED: The boot option is unknown.
      COMPUTE_ENGINE_BOOT_OPTION_EFI: The boot option is EFI.
      COMPUTE_ENGINE_BOOT_OPTION_BIOS: The boot option is BIOS.
    """
    COMPUTE_ENGINE_BOOT_OPTION_UNSPECIFIED = 0
    COMPUTE_ENGINE_BOOT_OPTION_EFI = 1
    COMPUTE_ENGINE_BOOT_OPTION_BIOS = 2

  class DiskTypeValueValuesEnum(_messages.Enum):
    r"""The disk type to use in the VM.

    Values:
      COMPUTE_ENGINE_DISK_TYPE_UNSPECIFIED: An unspecified disk type. Will be
        used as STANDARD.
      COMPUTE_ENGINE_DISK_TYPE_STANDARD: A Standard disk type.
      COMPUTE_ENGINE_DISK_TYPE_SSD: SSD hard disk type.
      COMPUTE_ENGINE_DISK_TYPE_BALANCED: An alternative to SSD persistent
        disks that balance performance and cost.
      COMPUTE_ENGINE_DISK_TYPE_HYPERDISK_BALANCED: Hyperdisk balanced disk
        type.
    """
    COMPUTE_ENGINE_DISK_TYPE_UNSPECIFIED = 0
    COMPUTE_ENGINE_DISK_TYPE_STANDARD = 1
    COMPUTE_ENGINE_DISK_TYPE_SSD = 2
    COMPUTE_ENGINE_DISK_TYPE_BALANCED = 3
    COMPUTE_ENGINE_DISK_TYPE_HYPERDISK_BALANCED = 4

  class LicenseTypeValueValuesEnum(_messages.Enum):
    r"""The license type to use in OS adaptation.

    Values:
      COMPUTE_ENGINE_LICENSE_TYPE_DEFAULT: The license type is the default for
        the OS.
      COMPUTE_ENGINE_LICENSE_TYPE_PAYG: The license type is Pay As You Go
        license type.
      COMPUTE_ENGINE_LICENSE_TYPE_BYOL: The license type is Bring Your Own
        License type.
    """
    COMPUTE_ENGINE_LICENSE_TYPE_DEFAULT = 0
    COMPUTE_ENGINE_LICENSE_TYPE_PAYG = 1
    COMPUTE_ENGINE_LICENSE_TYPE_BYOL = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""A map of labels to associate with the VM.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""The metadata key/value pairs to assign to the VM.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Additional properties of type MetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  adaptationModifiers = _messages.MessageField('AdaptationModifier', 1, repeated=True)
  additionalLicenses = _messages.StringField(2, repeated=True)
  appliedLicense = _messages.MessageField('AppliedLicense', 3)
  bootConversion = _messages.EnumField('BootConversionValueValuesEnum', 4)
  bootOption = _messages.EnumField('BootOptionValueValuesEnum', 5)
  computeScheduling = _messages.MessageField('ComputeScheduling', 6)
  diskType = _messages.EnumField('DiskTypeValueValuesEnum', 7)
  enableIntegrityMonitoring = _messages.BooleanField(8)
  enableVtpm = _messages.BooleanField(9)
  encryption = _messages.MessageField('Encryption', 10)
  hostname = _messages.StringField(11)
  labels = _messages.MessageField('LabelsValue', 12)
  licenseType = _messages.EnumField('LicenseTypeValueValuesEnum', 13)
  machineType = _messages.StringField(14)
  machineTypeSeries = _messages.StringField(15)
  metadata = _messages.MessageField('MetadataValue', 16)
  networkInterfaces = _messages.MessageField('NetworkInterface', 17, repeated=True)
  networkTags = _messages.StringField(18, repeated=True)
  secureBoot = _messages.BooleanField(19)
  serviceAccount = _messages.StringField(20)
  targetProject = _messages.StringField(21)
  vmName = _messages.StringField(22)
  zone = _messages.StringField(23)


class ComputeEngineTargetDetails(_messages.Message):
  r"""ComputeEngineTargetDetails is a collection of details for creating a VM
  in a target Compute Engine project.

  Enums:
    BootConversionValueValuesEnum: Optional. By default the virtual machine
      will keep its existing boot option. Setting this property will trigger
      an internal process which will convert the virtual machine from using
      the existing boot option to another.
    BootOptionValueValuesEnum: The VM Boot Option, as set in the source VM.
    DiskTypeValueValuesEnum: The disk type to use in the VM.
    LicenseTypeValueValuesEnum: The license type to use in OS adaptation.

  Messages:
    LabelsValue: A map of labels to associate with the VM.
    MetadataValue: The metadata key/value pairs to assign to the VM.

  Fields:
    adaptationModifiers: Optional. Modifiers to be used as configuration of
      the OS adaptation process.
    additionalLicenses: Additional licenses to assign to the VM.
    appliedLicense: The OS license returned from the adaptation module report.
    bootConversion: Optional. By default the virtual machine will keep its
      existing boot option. Setting this property will trigger an internal
      process which will convert the virtual machine from using the existing
      boot option to another.
    bootOption: The VM Boot Option, as set in the source VM.
    computeScheduling: Compute instance scheduling information (if empty
      default is used).
    diskType: The disk type to use in the VM.
    enableIntegrityMonitoring: Optional. Defines whether the instance has
      integrity monitoring enabled.
    enableVtpm: Optional. Defines whether the instance has vTPM enabled.
    encryption: Optional. The encryption to apply to the VM disks.
    hostname: The hostname to assign to the VM.
    labels: A map of labels to associate with the VM.
    licenseType: The license type to use in OS adaptation.
    machineType: The machine type to create the VM with.
    machineTypeSeries: The machine type series to create the VM with.
    metadata: The metadata key/value pairs to assign to the VM.
    networkInterfaces: List of NICs connected to this VM.
    networkTags: A list of network tags to associate with the VM.
    project: The Google Cloud target project ID or project name.
    secureBoot: Defines whether the instance has Secure Boot enabled. This can
      be set to true only if the VM boot option is EFI.
    serviceAccount: The service account to associate the VM with.
    vmName: The name of the VM to create.
    zone: The zone in which to create the VM.
  """

  class BootConversionValueValuesEnum(_messages.Enum):
    r"""Optional. By default the virtual machine will keep its existing boot
    option. Setting this property will trigger an internal process which will
    convert the virtual machine from using the existing boot option to
    another.

    Values:
      BOOT_CONVERSION_UNSPECIFIED: Unspecified conversion type.
      NONE: No conversion.
      BIOS_TO_EFI: Convert from BIOS to EFI.
    """
    BOOT_CONVERSION_UNSPECIFIED = 0
    NONE = 1
    BIOS_TO_EFI = 2

  class BootOptionValueValuesEnum(_messages.Enum):
    r"""The VM Boot Option, as set in the source VM.

    Values:
      COMPUTE_ENGINE_BOOT_OPTION_UNSPECIFIED: The boot option is unknown.
      COMPUTE_ENGINE_BOOT_OPTION_EFI: The boot option is EFI.
      COMPUTE_ENGINE_BOOT_OPTION_BIOS: The boot option is BIOS.
    """
    COMPUTE_ENGINE_BOOT_OPTION_UNSPECIFIED = 0
    COMPUTE_ENGINE_BOOT_OPTION_EFI = 1
    COMPUTE_ENGINE_BOOT_OPTION_BIOS = 2

  class DiskTypeValueValuesEnum(_messages.Enum):
    r"""The disk type to use in the VM.

    Values:
      COMPUTE_ENGINE_DISK_TYPE_UNSPECIFIED: An unspecified disk type. Will be
        used as STANDARD.
      COMPUTE_ENGINE_DISK_TYPE_STANDARD: A Standard disk type.
      COMPUTE_ENGINE_DISK_TYPE_SSD: SSD hard disk type.
      COMPUTE_ENGINE_DISK_TYPE_BALANCED: An alternative to SSD persistent
        disks that balance performance and cost.
      COMPUTE_ENGINE_DISK_TYPE_HYPERDISK_BALANCED: Hyperdisk balanced disk
        type.
    """
    COMPUTE_ENGINE_DISK_TYPE_UNSPECIFIED = 0
    COMPUTE_ENGINE_DISK_TYPE_STANDARD = 1
    COMPUTE_ENGINE_DISK_TYPE_SSD = 2
    COMPUTE_ENGINE_DISK_TYPE_BALANCED = 3
    COMPUTE_ENGINE_DISK_TYPE_HYPERDISK_BALANCED = 4

  class LicenseTypeValueValuesEnum(_messages.Enum):
    r"""The license type to use in OS adaptation.

    Values:
      COMPUTE_ENGINE_LICENSE_TYPE_DEFAULT: The license type is the default for
        the OS.
      COMPUTE_ENGINE_LICENSE_TYPE_PAYG: The license type is Pay As You Go
        license type.
      COMPUTE_ENGINE_LICENSE_TYPE_BYOL: The license type is Bring Your Own
        License type.
    """
    COMPUTE_ENGINE_LICENSE_TYPE_DEFAULT = 0
    COMPUTE_ENGINE_LICENSE_TYPE_PAYG = 1
    COMPUTE_ENGINE_LICENSE_TYPE_BYOL = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""A map of labels to associate with the VM.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""The metadata key/value pairs to assign to the VM.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Additional properties of type MetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  adaptationModifiers = _messages.MessageField('AdaptationModifier', 1, repeated=True)
  additionalLicenses = _messages.StringField(2, repeated=True)
  appliedLicense = _messages.MessageField('AppliedLicense', 3)
  bootConversion = _messages.EnumField('BootConversionValueValuesEnum', 4)
  bootOption = _messages.EnumField('BootOptionValueValuesEnum', 5)
  computeScheduling = _messages.MessageField('ComputeScheduling', 6)
  diskType = _messages.EnumField('DiskTypeValueValuesEnum', 7)
  enableIntegrityMonitoring = _messages.BooleanField(8)
  enableVtpm = _messages.BooleanField(9)
  encryption = _messages.MessageField('Encryption', 10)
  hostname = _messages.StringField(11)
  labels = _messages.MessageField('LabelsValue', 12)
  licenseType = _messages.EnumField('LicenseTypeValueValuesEnum', 13)
  machineType = _messages.StringField(14)
  machineTypeSeries = _messages.StringField(15)
  metadata = _messages.MessageField('MetadataValue', 16)
  networkInterfaces = _messages.MessageField('NetworkInterface', 17, repeated=True)
  networkTags = _messages.StringField(18, repeated=True)
  project = _messages.StringField(19)
  secureBoot = _messages.BooleanField(20)
  serviceAccount = _messages.StringField(21)
  vmName = _messages.StringField(22)
  zone = _messages.StringField(23)


class ComputeScheduling(_messages.Message):
  r"""Scheduling information for VM on maintenance/restart behaviour and node
  allocation in sole tenant nodes. Options for instance behavior when the host
  machine undergoes maintenance that may temporarily impact instance
  performance.

  Enums:
    OnHostMaintenanceValueValuesEnum: How the instance should behave when the
      host machine undergoes maintenance that may temporarily impact instance
      performance.
    RestartTypeValueValuesEnum: Whether the Instance should be automatically
      restarted whenever it is terminated by Compute Engine (not terminated by
      user). This configuration is identical to `automaticRestart` field in
      Compute Engine create instance under scheduling. It was changed to an
      enum (instead of a boolean) to match the default value in Compute Engine
      which is automatic restart.

  Fields:
    automaticRestart: A boolean attribute.
    minNodeCpus: The minimum number of virtual CPUs this instance will consume
      when running on a sole-tenant node. Ignored if no node_affinites are
      configured.
    nodeAffinities: A set of node affinity and anti-affinity configurations
      for sole tenant nodes.
    onHostMaintenance: How the instance should behave when the host machine
      undergoes maintenance that may temporarily impact instance performance.
    restartType: Whether the Instance should be automatically restarted
      whenever it is terminated by Compute Engine (not terminated by user).
      This configuration is identical to `automaticRestart` field in Compute
      Engine create instance under scheduling. It was changed to an enum
      (instead of a boolean) to match the default value in Compute Engine
      which is automatic restart.
  """

  class OnHostMaintenanceValueValuesEnum(_messages.Enum):
    r"""How the instance should behave when the host machine undergoes
    maintenance that may temporarily impact instance performance.

    Values:
      ON_HOST_MAINTENANCE_UNSPECIFIED: An unknown, unexpected behavior.
      TERMINATE: Terminate the instance when the host machine undergoes
        maintenance.
      MIGRATE: Migrate the instance when the host machine undergoes
        maintenance.
    """
    ON_HOST_MAINTENANCE_UNSPECIFIED = 0
    TERMINATE = 1
    MIGRATE = 2

  class RestartTypeValueValuesEnum(_messages.Enum):
    r"""Whether the Instance should be automatically restarted whenever it is
    terminated by Compute Engine (not terminated by user). This configuration
    is identical to `automaticRestart` field in Compute Engine create instance
    under scheduling. It was changed to an enum (instead of a boolean) to
    match the default value in Compute Engine which is automatic restart.

    Values:
      RESTART_TYPE_UNSPECIFIED: Unspecified behavior. This will use the
        default.
      AUTOMATIC_RESTART: The Instance should be automatically restarted
        whenever it is terminated by Compute Engine.
      NO_AUTOMATIC_RESTART: The Instance isn't automatically restarted
        whenever it is terminated by Compute Engine.
    """
    RESTART_TYPE_UNSPECIFIED = 0
    AUTOMATIC_RESTART = 1
    NO_AUTOMATIC_RESTART = 2

  automaticRestart = _messages.BooleanField(1)
  minNodeCpus = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  nodeAffinities = _messages.MessageField('SchedulingNodeAffinity', 3, repeated=True)
  onHostMaintenance = _messages.EnumField('OnHostMaintenanceValueValuesEnum', 4)
  restartType = _messages.EnumField('RestartTypeValueValuesEnum', 5)


class CopyingSourceDiskSnapshotStep(_messages.Message):
  r"""CopyingSourceDiskSnapshotStep contains specific step details."""


class CreatingImageStep(_messages.Message):
  r"""CreatingImageStep contains specific step details."""


class CreatingSourceDiskSnapshotStep(_messages.Message):
  r"""CreatingSourceDiskSnapshotStep contains specific step details."""


class CutoverForecast(_messages.Message):
  r"""CutoverForecast holds information about future CutoverJobs of a
  MigratingVm.

  Fields:
    estimatedCutoverJobDuration: Output only. Estimation of the CutoverJob
      duration.
  """

  estimatedCutoverJobDuration = _messages.StringField(1)


class CutoverJob(_messages.Message):
  r"""CutoverJob message describes a cutover of a migrating VM. The CutoverJob
  is the operation of shutting down the VM, creating a snapshot and cloning
  the VM using the replicated snapshot.

  Enums:
    StateValueValuesEnum: Output only. State of the cutover job.

  Fields:
    computeEngineDisksTargetDetails: Output only. Details of the target
      Persistent Disks in Compute Engine.
    computeEngineTargetDetails: Output only. Details of the target VM in
      Compute Engine.
    computeEngineVmDetails: Output only. Details of the VM in Compute Engine.
      Deprecated: Use compute_engine_target_details instead.
    createTime: Output only. The time the cutover job was created (as an API
      call, not when it was actually created in the target).
    endTime: Output only. The time the cutover job had finished.
    error: Output only. Provides details for the errors that led to the
      Cutover Job's state.
    name: Output only. The name of the cutover job.
    progress: Output only. The current progress in percentage of the cutover
      job.
    progressPercent: Output only. The current progress in percentage of the
      cutover job.
    state: Output only. State of the cutover job.
    stateMessage: Output only. A message providing possible extra details
      about the current state.
    stateTime: Output only. The time the state was last updated.
    steps: Output only. The cutover steps list representing its progress.
    targetDetails: Output only. Details of the VM to create as the target of
      this cutover job. Deprecated: Use compute_engine_target_details instead.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the cutover job.

    Values:
      STATE_UNSPECIFIED: The state is unknown. This is used for API
        compatibility only and is not used by the system.
      PENDING: The cutover job has not yet started.
      FAILED: The cutover job finished with errors.
      SUCCEEDED: The cutover job finished successfully.
      CANCELLED: The cutover job was cancelled.
      CANCELLING: The cutover job is being cancelled.
      ACTIVE: The cutover job is active and running.
      ADAPTING_OS: OS adaptation is running as part of the cutover job to
        generate license.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    FAILED = 2
    SUCCEEDED = 3
    CANCELLED = 4
    CANCELLING = 5
    ACTIVE = 6
    ADAPTING_OS = 7

  computeEngineDisksTargetDetails = _messages.MessageField('ComputeEngineDisksTargetDetails', 1)
  computeEngineTargetDetails = _messages.MessageField('ComputeEngineTargetDetails', 2)
  computeEngineVmDetails = _messages.MessageField('TargetVMDetails', 3)
  createTime = _messages.StringField(4)
  endTime = _messages.StringField(5)
  error = _messages.MessageField('Status', 6)
  name = _messages.StringField(7)
  progress = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  progressPercent = _messages.IntegerField(9, variant=_messages.Variant.INT32)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  stateMessage = _messages.StringField(11)
  stateTime = _messages.StringField(12)
  steps = _messages.MessageField('CutoverStep', 13, repeated=True)
  targetDetails = _messages.MessageField('TargetVMDetails', 14)


class CutoverStep(_messages.Message):
  r"""CutoverStep holds information about the cutover step progress.

  Fields:
    endTime: The time the step has ended.
    finalSync: Final sync step.
    instantiatingMigratedVm: Instantiating migrated VM step.
    preparingVmDisks: Preparing VM disks step.
    previousReplicationCycle: A replication cycle prior cutover step.
    shuttingDownSourceVm: Shutting down VM step.
    startTime: The time the step has started.
  """

  endTime = _messages.StringField(1)
  finalSync = _messages.MessageField('ReplicationCycle', 2)
  instantiatingMigratedVm = _messages.MessageField('InstantiatingMigratedVMStep', 3)
  preparingVmDisks = _messages.MessageField('PreparingVMDisksStep', 4)
  previousReplicationCycle = _messages.MessageField('ReplicationCycle', 5)
  shuttingDownSourceVm = _messages.MessageField('ShuttingDownSourceVMStep', 6)
  startTime = _messages.StringField(7)


class CycleStep(_messages.Message):
  r"""CycleStep holds information about a step progress.

  Fields:
    endTime: The time the cycle step has ended.
    initializingReplication: Initializing replication step.
    postProcessing: Post processing step.
    replicating: Replicating step.
    startTime: The time the cycle step has started.
  """

  endTime = _messages.StringField(1)
  initializingReplication = _messages.MessageField('InitializingReplicationStep', 2)
  postProcessing = _messages.MessageField('PostProcessingStep', 3)
  replicating = _messages.MessageField('ReplicatingStep', 4)
  startTime = _messages.StringField(5)


class DataDiskImageImport(_messages.Message):
  r"""Mentions that the image import is not using OS adaptation process."""


class DatacenterConnector(_messages.Message):
  r"""DatacenterConnector message describes a connector between the Source and
  Google Cloud, which is installed on a vmware datacenter (an OVA vm installed
  by the user) to connect the Datacenter to Google Cloud and support vm
  migration data transfer.

  Enums:
    StateValueValuesEnum: Output only. State of the DatacenterConnector, as
      determined by the health checks.

  Fields:
    applianceInfrastructureVersion: Output only. Appliance OVA version. This
      is the OVA which is manually installed by the user and contains the
      infrastructure for the automatically updatable components on the
      appliance.
    applianceSoftwareVersion: Output only. Appliance last installed update
      bundle version. This is the version of the automatically updatable
      components on the appliance.
    availableVersions: Output only. The available versions for updating this
      appliance.
    bucket: Output only. The communication channel between the datacenter
      connector and Google Cloud.
    createTime: Output only. The time the connector was created (as an API
      call, not when it was actually installed).
    error: Output only. Provides details on the state of the Datacenter
      Connector in case of an error.
    name: Output only. The connector's name.
    registrationId: Immutable. A unique key for this connector. This key is
      internal to the OVA connector and is supplied with its creation during
      the registration process and can not be modified.
    serviceAccount: The service account to use in the connector when
      communicating with the cloud.
    state: Output only. State of the DatacenterConnector, as determined by the
      health checks.
    stateTime: Output only. The time the state was last set.
    updateTime: Output only. The last time the connector was updated with an
      API call.
    upgradeStatus: Output only. The status of the current / last
      upgradeAppliance operation.
    version: The version running in the DatacenterConnector. This is supplied
      by the OVA connector during the registration process and can not be
      modified.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the DatacenterConnector, as determined by the
    health checks.

    Values:
      STATE_UNSPECIFIED: The state is unknown. This is used for API
        compatibility only and is not used by the system.
      PENDING: The state was not sampled by the health checks yet.
      OFFLINE: The source was sampled by health checks and is not available.
      FAILED: The source is available but might not be usable yet due to
        unvalidated credentials or another reason. The credentials referred to
        are the ones to the Source. The error message will contain further
        details.
      ACTIVE: The source exists and its credentials were verified.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    OFFLINE = 2
    FAILED = 3
    ACTIVE = 4

  applianceInfrastructureVersion = _messages.StringField(1)
  applianceSoftwareVersion = _messages.StringField(2)
  availableVersions = _messages.MessageField('AvailableUpdates', 3)
  bucket = _messages.StringField(4)
  createTime = _messages.StringField(5)
  error = _messages.MessageField('Status', 6)
  name = _messages.StringField(7)
  registrationId = _messages.StringField(8)
  serviceAccount = _messages.StringField(9)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  stateTime = _messages.StringField(11)
  updateTime = _messages.StringField(12)
  upgradeStatus = _messages.MessageField('UpgradeStatus', 13)
  version = _messages.StringField(14)


class Disk(_messages.Message):
  r"""A message describing a data disk.

  Fields:
    lun: The disk's Logical Unit Number (LUN).
    name: The disk name.
    sizeGb: The disk size in GB.
  """

  lun = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  name = _messages.StringField(2)
  sizeGb = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class DiskImageDefaults(_messages.Message):
  r"""Contains details about the image source used to create the disk.

  Fields:
    sourceImage: Required. The Image resource used when creating the disk.
  """

  sourceImage = _messages.StringField(1)


class DiskImageTargetDetails(_messages.Message):
  r"""The target details of the image resource that will be created by the
  import job.

  Messages:
    LabelsValue: Optional. A map of labels to associate with the image.

  Fields:
    additionalLicenses: Optional. Additional licenses to assign to the image.
      Format: https://www.googleapis.com/compute/v1/projects/PROJECT_ID/global
      /licenses/LICENSE_NAME Or https://www.googleapis.com/compute/beta/projec
      ts/PROJECT_ID/global/licenses/LICENSE_NAME
    dataDiskImageImport: Optional. Use to skip OS adaptation process.
    description: Optional. An optional description of the image.
    encryption: Immutable. The encryption to apply to the image.
    familyName: Optional. The name of the image family to which the new image
      belongs.
    imageName: Required. The name of the image to be created.
    labels: Optional. A map of labels to associate with the image.
    osAdaptationParameters: Optional. Use to set the parameters relevant for
      the OS adaptation process.
    singleRegionStorage: Optional. Set to true to set the image
      storageLocations to the single region of the import job. When false, the
      closest multi-region is selected.
    targetProject: Required. Reference to the TargetProject resource that
      represents the target project in which the imported image will be
      created.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. A map of labels to associate with the image.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  additionalLicenses = _messages.StringField(1, repeated=True)
  dataDiskImageImport = _messages.MessageField('DataDiskImageImport', 2)
  description = _messages.StringField(3)
  encryption = _messages.MessageField('Encryption', 4)
  familyName = _messages.StringField(5)
  imageName = _messages.StringField(6)
  labels = _messages.MessageField('LabelsValue', 7)
  osAdaptationParameters = _messages.MessageField('ImageImportOsAdaptationParameters', 8)
  singleRegionStorage = _messages.BooleanField(9)
  targetProject = _messages.StringField(10)


class DiskMigrationJob(_messages.Message):
  r"""Describes the disk which will be migrated from the source environment.
  The source disk has to be unattached.

  Enums:
    StateValueValuesEnum: Output only. State of the DiskMigrationJob.

  Fields:
    awsSourceDiskDetails: Details of the unattached AWS source disk.
    createTime: Output only. The time the DiskMigrationJob resource was
      created.
    errors: Output only. Provides details on the errors that led to the disk
      migration job's state in case of an error.
    name: Output only. Identifier. The identifier of the DiskMigrationJob.
    state: Output only. State of the DiskMigrationJob.
    steps: Output only. The disk migration steps list representing its
      progress.
    targetDetails: Required. Details of the target Disk in Compute Engine.
    updateTime: Output only. The last time the DiskMigrationJob resource was
      updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the DiskMigrationJob.

    Values:
      STATE_UNSPECIFIED: The state is unspecified. This is not in use.
      READY: The initial state of the disk migration. In this state the
        customers can update the target details.
      RUNNING: The migration is active, and it's running or scheduled to run.
      SUCCEEDED: The migration completed successfully.
      CANCELLING: Migration cancellation was initiated.
      CANCELLED: The migration was cancelled.
      FAILED: The migration process encountered an unrecoverable error and was
        aborted.
    """
    STATE_UNSPECIFIED = 0
    READY = 1
    RUNNING = 2
    SUCCEEDED = 3
    CANCELLING = 4
    CANCELLED = 5
    FAILED = 6

  awsSourceDiskDetails = _messages.MessageField('AwsSourceDiskDetails', 1)
  createTime = _messages.StringField(2)
  errors = _messages.MessageField('Status', 3, repeated=True)
  name = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)
  steps = _messages.MessageField('DiskMigrationStep', 6, repeated=True)
  targetDetails = _messages.MessageField('DiskMigrationJobTargetDetails', 7)
  updateTime = _messages.StringField(8)


class DiskMigrationJobTargetDetails(_messages.Message):
  r"""Details of the target disk in Compute Engine.

  Messages:
    LabelsValue: Optional. A map of labels to associate with the disk.

  Fields:
    encryption: Optional. The encryption to apply to the disk.
    labels: Optional. A map of labels to associate with the disk.
    targetDisk: Required. The target disk.
    targetProject: Required. The name of the resource of type TargetProject
      which represents the Compute Engine project in which to create the disk.
      Should be of the form:
      projects/{project}/locations/global/targetProjects/{target-project}
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. A map of labels to associate with the disk.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  encryption = _messages.MessageField('Encryption', 1)
  labels = _messages.MessageField('LabelsValue', 2)
  targetDisk = _messages.MessageField('ComputeEngineDisk', 3)
  targetProject = _messages.StringField(4)


class DiskMigrationStep(_messages.Message):
  r"""DiskMigrationStep holds information about the disk migration step
  progress.

  Fields:
    copyingSourceDiskSnapshot: Copying source disk snapshot step.
    creatingSourceDiskSnapshot: Creating source disk snapshot step.
    endTime: Output only. The time the step has ended.
    provisioningTargetDisk: Creating target disk step.
    startTime: Output only. The time the step has started.
  """

  copyingSourceDiskSnapshot = _messages.MessageField('CopyingSourceDiskSnapshotStep', 1)
  creatingSourceDiskSnapshot = _messages.MessageField('CreatingSourceDiskSnapshotStep', 2)
  endTime = _messages.StringField(3)
  provisioningTargetDisk = _messages.MessageField('ProvisioningTargetDiskStep', 4)
  startTime = _messages.StringField(5)


class DisksMigrationDisksTargetDefaults(_messages.Message):
  r"""Details for a disk only migration."""


class DisksMigrationDisksTargetDetails(_messages.Message):
  r"""Details for a disks-only migration."""


class DisksMigrationVmTargetDefaults(_messages.Message):
  r"""Details for creation of a VM that migrated data disks will be attached
  to.

  Messages:
    LabelsValue: Optional. A map of labels to associate with the VM.
    MetadataValue: Optional. The metadata key/value pairs to assign to the VM.

  Fields:
    additionalLicenses: Optional. Additional licenses to assign to the VM.
    bootDiskDefaults: Optional. Details of the boot disk of the VM.
    computeScheduling: Optional. Compute instance scheduling information (if
      empty default is used).
    enableIntegrityMonitoring: Optional. Defines whether the instance has
      integrity monitoring enabled.
    enableVtpm: Optional. Defines whether the instance has vTPM enabled.
    encryption: Optional. The encryption to apply to the VM.
    hostname: Optional. The hostname to assign to the VM.
    labels: Optional. A map of labels to associate with the VM.
    machineType: Required. The machine type to create the VM with.
    machineTypeSeries: Optional. The machine type series to create the VM
      with. For presentation only.
    metadata: Optional. The metadata key/value pairs to assign to the VM.
    networkInterfaces: Optional. NICs to attach to the VM.
    networkTags: Optional. A list of network tags to associate with the VM.
    secureBoot: Optional. Defines whether the instance has Secure Boot
      enabled. This can be set to true only if the VM boot option is EFI.
    serviceAccount: Optional. The service account to associate the VM with.
    vmName: Required. The name of the VM to create.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. A map of labels to associate with the VM.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Optional. The metadata key/value pairs to assign to the VM.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Additional properties of type MetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  additionalLicenses = _messages.StringField(1, repeated=True)
  bootDiskDefaults = _messages.MessageField('BootDiskDefaults', 2)
  computeScheduling = _messages.MessageField('ComputeScheduling', 3)
  enableIntegrityMonitoring = _messages.BooleanField(4)
  enableVtpm = _messages.BooleanField(5)
  encryption = _messages.MessageField('Encryption', 6)
  hostname = _messages.StringField(7)
  labels = _messages.MessageField('LabelsValue', 8)
  machineType = _messages.StringField(9)
  machineTypeSeries = _messages.StringField(10)
  metadata = _messages.MessageField('MetadataValue', 11)
  networkInterfaces = _messages.MessageField('NetworkInterface', 12, repeated=True)
  networkTags = _messages.StringField(13, repeated=True)
  secureBoot = _messages.BooleanField(14)
  serviceAccount = _messages.StringField(15)
  vmName = _messages.StringField(16)


class DisksMigrationVmTargetDetails(_messages.Message):
  r"""Details for the VM created VM as part of disks migration.

  Fields:
    vmUri: Output only. The URI of the Compute Engine VM.
  """

  vmUri = _messages.StringField(1)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Encryption(_messages.Message):
  r"""Encryption message describes the details of the applied encryption.

  Fields:
    kmsKey: Required. The name of the encryption key that is stored in Google
      Cloud KMS.
  """

  kmsKey = _messages.StringField(1)


class FetchInventoryResponse(_messages.Message):
  r"""Response message for fetchInventory.

  Fields:
    awsVms: The description of the VMs in a Source of type AWS.
    azureVms: The description of the VMs in a Source of type Azure.
    nextPageToken: Output only. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
    updateTime: Output only. The timestamp when the source was last queried
      (if the result is from the cache).
    vmwareVms: The description of the VMs in a Source of type Vmware.
  """

  awsVms = _messages.MessageField('AwsVmsDetails', 1)
  azureVms = _messages.MessageField('AzureVmsDetails', 2)
  nextPageToken = _messages.StringField(3)
  updateTime = _messages.StringField(4)
  vmwareVms = _messages.MessageField('VmwareVmsDetails', 5)


class FetchStorageInventoryResponse(_messages.Message):
  r"""Response message for fetchStorageInventory.

  Fields:
    nextPageToken: Output only. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
    resources: The list of storage resources in the source.
    updateTime: Output only. The timestamp when the source was last queried
      (if the result is from the cache).
  """

  nextPageToken = _messages.StringField(1)
  resources = _messages.MessageField('SourceStorageResource', 2, repeated=True)
  updateTime = _messages.StringField(3)


class FinalizeMigrationRequest(_messages.Message):
  r"""Request message for 'FinalizeMigration' request."""


class Group(_messages.Message):
  r"""Describes message for 'Group' resource. The Group is a collections of
  several MigratingVms.

  Enums:
    MigrationTargetTypeValueValuesEnum: Immutable. The target type of this
      group.

  Fields:
    createTime: Output only. The create time timestamp.
    description: User-provided description of the group.
    displayName: Display name is a user defined name for this group which can
      be updated.
    migrationTargetType: Immutable. The target type of this group.
    name: Output only. The Group name.
    updateTime: Output only. The update time timestamp.
  """

  class MigrationTargetTypeValueValuesEnum(_messages.Enum):
    r"""Immutable. The target type of this group.

    Values:
      MIGRATION_TARGET_TYPE_UNSPECIFIED: Group type is not specified. This
        defaults to Compute Engine targets.
      MIGRATION_TARGET_TYPE_GCE: All MigratingVMs in the group must have
        Compute Engine targets.
      MIGRATION_TARGET_TYPE_DISKS: All MigratingVMs in the group must have
        Compute Engine Disks targets.
    """
    MIGRATION_TARGET_TYPE_UNSPECIFIED = 0
    MIGRATION_TARGET_TYPE_GCE = 1
    MIGRATION_TARGET_TYPE_DISKS = 2

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  migrationTargetType = _messages.EnumField('MigrationTargetTypeValueValuesEnum', 4)
  name = _messages.StringField(5)
  updateTime = _messages.StringField(6)


class ImageImport(_messages.Message):
  r"""ImageImport describes the configuration of the image import to run.

  Fields:
    cloudStorageUri: Immutable. The path to the Cloud Storage file from which
      the image should be imported.
    createTime: Output only. The time the image import was created.
    diskImageTargetDefaults: Immutable. Target details for importing a disk
      image, will be used by ImageImportJob.
    encryption: Immutable. The encryption details used by the image import
      process during the image adaptation for Compute Engine.
    machineImageTargetDefaults: Immutable. Target details for importing a
      machine image, will be used by ImageImportJob.
    name: Output only. The resource path of the ImageImport.
    recentImageImportJobs: Output only. The result of the most recent runs for
      this ImageImport. All jobs for this ImageImport can be listed via
      ListImageImportJobs.
  """

  cloudStorageUri = _messages.StringField(1)
  createTime = _messages.StringField(2)
  diskImageTargetDefaults = _messages.MessageField('DiskImageTargetDetails', 3)
  encryption = _messages.MessageField('Encryption', 4)
  machineImageTargetDefaults = _messages.MessageField('MachineImageTargetDetails', 5)
  name = _messages.StringField(6)
  recentImageImportJobs = _messages.MessageField('ImageImportJob', 7, repeated=True)


class ImageImportJob(_messages.Message):
  r"""ImageImportJob describes the progress and result of an image import.

  Enums:
    StateValueValuesEnum: Output only. The state of the image import.

  Fields:
    cloudStorageUri: Output only. The path to the Cloud Storage file from
      which the image should be imported.
    createTime: Output only. The time the image import was created (as an API
      call, not when it was actually created in the target).
    createdResources: Output only. The resource paths of the resources created
      by the image import job.
    diskImageTargetDetails: Output only. Target details used to import a disk
      image.
    endTime: Output only. The time the image import was ended.
    errors: Output only. Provides details on the error that led to the image
      import state in case of an error.
    machineImageTargetDetails: Output only. Target details used to import a
      machine image.
    name: Output only. The resource path of the ImageImportJob.
    state: Output only. The state of the image import.
    steps: Output only. The image import steps list representing its progress.
    warnings: Output only. Warnings that occurred during the image import.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the image import.

    Values:
      STATE_UNSPECIFIED: The state is unknown.
      PENDING: The image import has not yet started.
      RUNNING: The image import is active and running.
      SUCCEEDED: The image import has finished successfully.
      FAILED: The image import has finished with errors.
      CANCELLING: The image import is being cancelled.
      CANCELLED: The image import was cancelled.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    RUNNING = 2
    SUCCEEDED = 3
    FAILED = 4
    CANCELLING = 5
    CANCELLED = 6

  cloudStorageUri = _messages.StringField(1)
  createTime = _messages.StringField(2)
  createdResources = _messages.StringField(3, repeated=True)
  diskImageTargetDetails = _messages.MessageField('DiskImageTargetDetails', 4)
  endTime = _messages.StringField(5)
  errors = _messages.MessageField('Status', 6, repeated=True)
  machineImageTargetDetails = _messages.MessageField('MachineImageTargetDetails', 7)
  name = _messages.StringField(8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  steps = _messages.MessageField('ImageImportStep', 10, repeated=True)
  warnings = _messages.MessageField('MigrationWarning', 11, repeated=True)


class ImageImportOsAdaptationParameters(_messages.Message):
  r"""Parameters affecting the OS adaptation process.

  Enums:
    BootConversionValueValuesEnum: Optional. By default the image will keep
      its existing boot option. Setting this property will trigger an internal
      process which will convert the image from using the existing boot option
      to another. The size of the boot disk might be increased to allow the
      conversion
    LicenseTypeValueValuesEnum: Optional. Choose which type of license to
      apply to the imported image.

  Fields:
    adaptationModifiers: Optional. Modifiers to be used as configuration of
      the OS adaptation process.
    bootConversion: Optional. By default the image will keep its existing boot
      option. Setting this property will trigger an internal process which
      will convert the image from using the existing boot option to another.
      The size of the boot disk might be increased to allow the conversion
    generalize: Optional. Set to true in order to generalize the imported
      image. The generalization process enables co-existence of multiple VMs
      created from the same image. For Windows, generalizing the image removes
      computer-specific information such as installed drivers and the computer
      security identifier (SID).
    licenseType: Optional. Choose which type of license to apply to the
      imported image.
  """

  class BootConversionValueValuesEnum(_messages.Enum):
    r"""Optional. By default the image will keep its existing boot option.
    Setting this property will trigger an internal process which will convert
    the image from using the existing boot option to another. The size of the
    boot disk might be increased to allow the conversion

    Values:
      BOOT_CONVERSION_UNSPECIFIED: Unspecified conversion type.
      NONE: No conversion.
      BIOS_TO_EFI: Convert from BIOS to EFI.
    """
    BOOT_CONVERSION_UNSPECIFIED = 0
    NONE = 1
    BIOS_TO_EFI = 2

  class LicenseTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Choose which type of license to apply to the imported image.

    Values:
      COMPUTE_ENGINE_LICENSE_TYPE_DEFAULT: The license type is the default for
        the OS.
      COMPUTE_ENGINE_LICENSE_TYPE_PAYG: The license type is Pay As You Go
        license type.
      COMPUTE_ENGINE_LICENSE_TYPE_BYOL: The license type is Bring Your Own
        License type.
    """
    COMPUTE_ENGINE_LICENSE_TYPE_DEFAULT = 0
    COMPUTE_ENGINE_LICENSE_TYPE_PAYG = 1
    COMPUTE_ENGINE_LICENSE_TYPE_BYOL = 2

  adaptationModifiers = _messages.MessageField('AdaptationModifier', 1, repeated=True)
  bootConversion = _messages.EnumField('BootConversionValueValuesEnum', 2)
  generalize = _messages.BooleanField(3)
  licenseType = _messages.EnumField('LicenseTypeValueValuesEnum', 4)


class ImageImportStep(_messages.Message):
  r"""ImageImportStep holds information about the image import step progress.

  Fields:
    adaptingOs: Adapting OS step.
    creatingImage: Creating image step.
    endTime: Output only. The time the step has ended.
    initializing: Initializing step.
    loadingSourceFiles: Loading source files step.
    startTime: Output only. The time the step has started.
  """

  adaptingOs = _messages.MessageField('AdaptingOSStep', 1)
  creatingImage = _messages.MessageField('CreatingImageStep', 2)
  endTime = _messages.StringField(3)
  initializing = _messages.MessageField('InitializingImageImportStep', 4)
  loadingSourceFiles = _messages.MessageField('LoadingImageSourceFilesStep', 5)
  startTime = _messages.StringField(6)


class InitializingImageImportStep(_messages.Message):
  r"""InitializingImageImportStep contains specific step details."""


class InitializingReplicationStep(_messages.Message):
  r"""InitializingReplicationStep contains specific step details."""


class InstantiatingMigratedVMStep(_messages.Message):
  r"""InstantiatingMigratedVMStep contains specific step details."""


class Link(_messages.Message):
  r"""Describes a URL link.

  Fields:
    description: Describes what the link offers.
    url: The URL of the link.
  """

  description = _messages.StringField(1)
  url = _messages.StringField(2)


class ListCloneJobsResponse(_messages.Message):
  r"""Response message for 'ListCloneJobs' request.

  Fields:
    cloneJobs: Output only. The list of clone jobs response.
    nextPageToken: Output only. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
    unreachable: Output only. Locations that could not be reached.
  """

  cloneJobs = _messages.MessageField('CloneJob', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListCutoverJobsResponse(_messages.Message):
  r"""Response message for 'ListCutoverJobs' request.

  Fields:
    cutoverJobs: Output only. The list of cutover jobs response.
    nextPageToken: Output only. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
    unreachable: Output only. Locations that could not be reached.
  """

  cutoverJobs = _messages.MessageField('CutoverJob', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListDatacenterConnectorsResponse(_messages.Message):
  r"""Response message for 'ListDatacenterConnectors' request.

  Fields:
    datacenterConnectors: Output only. The list of sources response.
    nextPageToken: Output only. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
    unreachable: Output only. Locations that could not be reached.
  """

  datacenterConnectors = _messages.MessageField('DatacenterConnector', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListDiskMigrationJobsResponse(_messages.Message):
  r"""Response message for 'ListDiskMigrationJobs' request.

  Fields:
    diskMigrationJobs: Output only. The list of the disk migration jobs.
    nextPageToken: Optional. Output only. A token, which can be sent as
      `page_token` to retrieve the next page. If this field is omitted, there
      are no subsequent pages.
    unreachable: Output only. Locations that could not be reached.
  """

  diskMigrationJobs = _messages.MessageField('DiskMigrationJob', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListGroupsResponse(_messages.Message):
  r"""Response message for 'ListGroups' request.

  Fields:
    groups: Output only. The list of groups response.
    nextPageToken: Output only. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
    unreachable: Output only. Locations that could not be reached.
  """

  groups = _messages.MessageField('Group', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListImageImportJobsResponse(_messages.Message):
  r"""Response message for 'ListImageImportJobs' call.

  Fields:
    imageImportJobs: Output only. The list of target response.
    nextPageToken: Output only. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
    unreachable: Output only. Locations that could not be reached.
  """

  imageImportJobs = _messages.MessageField('ImageImportJob', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListImageImportsResponse(_messages.Message):
  r"""Response message for 'ListImageImports' call.

  Fields:
    imageImports: Output only. The list of target response.
    nextPageToken: Output only. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
    unreachable: Output only. Locations that could not be reached.
  """

  imageImports = _messages.MessageField('ImageImport', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListMigratingVmsResponse(_messages.Message):
  r"""Response message for 'ListMigratingVms' request.

  Fields:
    migratingVms: Output only. The list of Migrating VMs response.
    nextPageToken: Output only. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
    unreachable: Output only. Locations that could not be reached.
  """

  migratingVms = _messages.MessageField('MigratingVm', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListReplicationCyclesResponse(_messages.Message):
  r"""Response message for 'ListReplicationCycles' request.

  Fields:
    nextPageToken: Output only. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
    replicationCycles: Output only. The list of replication cycles response.
    unreachable: Output only. Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  replicationCycles = _messages.MessageField('ReplicationCycle', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListSourcesResponse(_messages.Message):
  r"""Response message for 'ListSources' request.

  Fields:
    nextPageToken: Output only. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
    sources: Output only. The list of sources response.
    unreachable: Output only. Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  sources = _messages.MessageField('Source', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListTargetProjectsResponse(_messages.Message):
  r"""Response message for 'ListTargetProjects' call.

  Fields:
    nextPageToken: Output only. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
    targetProjects: Output only. The list of target response.
    unreachable: Output only. Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  targetProjects = _messages.MessageField('TargetProject', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListUtilizationReportsResponse(_messages.Message):
  r"""Response message for 'ListUtilizationReports' request.

  Fields:
    nextPageToken: Output only. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
    unreachable: Output only. Locations that could not be reached.
    utilizationReports: Output only. The list of reports.
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  utilizationReports = _messages.MessageField('UtilizationReport', 3, repeated=True)


class LoadingImageSourceFilesStep(_messages.Message):
  r"""LoadingImageSourceFilesStep contains specific step details."""


class LocalizedMessage(_messages.Message):
  r"""Provides a localized error message that is safe to return to the user
  which can be attached to an RPC error.

  Fields:
    locale: The locale used following the specification defined at
      https://www.rfc-editor.org/rfc/bcp/bcp47.txt. Examples are: "en-US",
      "fr-CH", "es-MX"
    message: The localized error message in the above locale.
  """

  locale = _messages.StringField(1)
  message = _messages.StringField(2)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class MachineImageParametersOverrides(_messages.Message):
  r"""Parameters overriding decisions based on the source machine image
  configurations.

  Fields:
    machineType: Optional. The machine type to create the MachineImage with.
      If empty, the service will choose a relevant machine type based on the
      information from the source image. For more information about machine
      types, please refer to https://cloud.google.com/compute/docs/machine-
      resource.
  """

  machineType = _messages.StringField(1)


class MachineImageTargetDetails(_messages.Message):
  r"""The target details of the machine image resource that will be created by
  the image import job.

  Messages:
    LabelsValue: Optional. The labels to apply to the instance created by the
      machine image.

  Fields:
    additionalLicenses: Optional. Additional licenses to assign to the
      instance created by the machine image. Format: https://www.googleapis.co
      m/compute/v1/projects/PROJECT_ID/global/licenses/LICENSE_NAME Or https:/
      /www.googleapis.com/compute/beta/projects/PROJECT_ID/global/licenses/LIC
      ENSE_NAME
    description: Optional. An optional description of the machine image.
    encryption: Immutable. The encryption to apply to the machine image.
    labels: Optional. The labels to apply to the instance created by the
      machine image.
    machineImageName: Required. The name of the machine image to be created.
    machineImageParametersOverrides: Optional. Parameters overriding decisions
      based on the source machine image configurations.
    networkInterfaces: Optional. The network interfaces to create with the
      instance created by the machine image. Internal and external IP
      addresses are ignored for machine image import.
    osAdaptationParameters: Optional. Use to set the parameters relevant for
      the OS adaptation process.
    serviceAccount: Optional. The service account to assign to the instance
      created by the machine image.
    shieldedInstanceConfig: Optional. Shielded instance configuration.
    singleRegionStorage: Optional. Set to true to set the machine image
      storageLocations to the single region of the import job. When false, the
      closest multi-region is selected.
    skipOsAdaptation: Optional. Use to skip OS adaptation process.
    tags: Optional. The tags to apply to the instance created by the machine
      image.
    targetProject: Required. Reference to the TargetProject resource that
      represents the target project in which the imported machine image will
      be created.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels to apply to the instance created by the machine
    image.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  additionalLicenses = _messages.StringField(1, repeated=True)
  description = _messages.StringField(2)
  encryption = _messages.MessageField('Encryption', 3)
  labels = _messages.MessageField('LabelsValue', 4)
  machineImageName = _messages.StringField(5)
  machineImageParametersOverrides = _messages.MessageField('MachineImageParametersOverrides', 6)
  networkInterfaces = _messages.MessageField('NetworkInterface', 7, repeated=True)
  osAdaptationParameters = _messages.MessageField('ImageImportOsAdaptationParameters', 8)
  serviceAccount = _messages.MessageField('ServiceAccount', 9)
  shieldedInstanceConfig = _messages.MessageField('ShieldedInstanceConfig', 10)
  singleRegionStorage = _messages.BooleanField(11)
  skipOsAdaptation = _messages.MessageField('SkipOsAdaptation', 12)
  tags = _messages.StringField(13, repeated=True)
  targetProject = _messages.StringField(14)


class MigratingVm(_messages.Message):
  r"""MigratingVm describes the VM that will be migrated from a Source
  environment and its replication state.

  Enums:
    StateValueValuesEnum: Output only. State of the MigratingVm.

  Messages:
    LabelsValue: The labels of the migrating VM.

  Fields:
    awsSourceVmDetails: Output only. Details of the VM from an AWS source.
    azureSourceVmDetails: Output only. Details of the VM from an Azure source.
    computeEngineDisksTargetDefaults: Details of the target Persistent Disks
      in Compute Engine.
    computeEngineTargetDefaults: Details of the target VM in Compute Engine.
    computeEngineVmDefaults: Details of the VM in Compute Engine. Deprecated:
      Use compute_engine_target_defaults instead.
    createTime: Output only. The time the migrating VM was created (this
      refers to this resource and not to the time it was installed in the
      source).
    currentSyncInfo: Output only. Details of the current running replication
      cycle.
    cutoverForecast: Output only. Provides details of future CutoverJobs of a
      MigratingVm. Set to empty when cutover forecast is unavailable.
    description: The description attached to the migrating VM by the user.
    displayName: The display name attached to the MigratingVm by the user.
    error: Output only. Provides details on the state of the Migrating VM in
      case of an error in replication.
    group: Output only. The group this migrating vm is included in, if any.
      The group is represented by the full path of the appropriate Group
      resource.
    labels: The labels of the migrating VM.
    lastReplicationCycle: Output only. Details of the last replication cycle.
      This will be updated whenever a replication cycle is finished and is not
      to be confused with last_sync which is only updated on successful
      replication cycles.
    lastSync: Output only. The most updated snapshot created time in the
      source that finished replication.
    name: Output only. The identifier of the MigratingVm.
    policy: The replication schedule policy.
    recentCloneJobs: Output only. The recent clone jobs performed on the
      migrating VM. This field holds the vm's last completed clone job and the
      vm's running clone job, if one exists. Note: To have this field
      populated you need to explicitly request it via the "view" parameter of
      the Get/List request.
    recentCutoverJobs: Output only. The recent cutover jobs performed on the
      migrating VM. This field holds the vm's last completed cutover job and
      the vm's running cutover job, if one exists. Note: To have this field
      populated you need to explicitly request it via the "view" parameter of
      the Get/List request.
    sourceVmId: The unique ID of the VM in the source. The VM's name in
      vSphere can be changed, so this is not the VM's name but rather its
      moRef id. This id is of the form vm-.
    state: Output only. State of the MigratingVm.
    stateTime: Output only. The last time the migrating VM state was updated.
    targetDefaults: The default configuration of the target VM that will be
      created in Google Cloud as a result of the migration. Deprecated: Use
      compute_engine_target_defaults instead.
    updateTime: Output only. The last time the migrating VM resource was
      updated.
    vmwareSourceVmDetails: Output only. Details of the VM from a Vmware
      source.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the MigratingVm.

    Values:
      STATE_UNSPECIFIED: The state was not sampled by the health checks yet.
      PENDING: The VM in the source is being verified.
      READY: The source VM was verified, and it's ready to start replication.
      FIRST_SYNC: Migration is going through the first sync cycle.
      ACTIVE: The replication is active, and it's running or scheduled to run.
      CUTTING_OVER: The source VM is being turned off, and a final replication
        is currently running.
      CUTOVER: The source VM was stopped and replicated. The replication is
        currently paused.
      FINAL_SYNC: A cutover job is active and replication cycle is running the
        final sync.
      PAUSED: The replication was paused by the user and no cycles are
        scheduled to run.
      FINALIZING: The migrating VM is being finalized and migration resources
        are being removed.
      FINALIZED: The replication process is done. The migrating VM is
        finalized and no longer consumes billable resources.
      ERROR: The replication process encountered an unrecoverable error and
        was aborted.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    READY = 2
    FIRST_SYNC = 3
    ACTIVE = 4
    CUTTING_OVER = 5
    CUTOVER = 6
    FINAL_SYNC = 7
    PAUSED = 8
    FINALIZING = 9
    FINALIZED = 10
    ERROR = 11

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels of the migrating VM.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  awsSourceVmDetails = _messages.MessageField('AwsSourceVmDetails', 1)
  azureSourceVmDetails = _messages.MessageField('AzureSourceVmDetails', 2)
  computeEngineDisksTargetDefaults = _messages.MessageField('ComputeEngineDisksTargetDefaults', 3)
  computeEngineTargetDefaults = _messages.MessageField('ComputeEngineTargetDefaults', 4)
  computeEngineVmDefaults = _messages.MessageField('TargetVMDetails', 5)
  createTime = _messages.StringField(6)
  currentSyncInfo = _messages.MessageField('ReplicationCycle', 7)
  cutoverForecast = _messages.MessageField('CutoverForecast', 8)
  description = _messages.StringField(9)
  displayName = _messages.StringField(10)
  error = _messages.MessageField('Status', 11)
  group = _messages.StringField(12)
  labels = _messages.MessageField('LabelsValue', 13)
  lastReplicationCycle = _messages.MessageField('ReplicationCycle', 14)
  lastSync = _messages.MessageField('ReplicationSync', 15)
  name = _messages.StringField(16)
  policy = _messages.MessageField('SchedulePolicy', 17)
  recentCloneJobs = _messages.MessageField('CloneJob', 18, repeated=True)
  recentCutoverJobs = _messages.MessageField('CutoverJob', 19, repeated=True)
  sourceVmId = _messages.StringField(20)
  state = _messages.EnumField('StateValueValuesEnum', 21)
  stateTime = _messages.StringField(22)
  targetDefaults = _messages.MessageField('TargetVMDetails', 23)
  updateTime = _messages.StringField(24)
  vmwareSourceVmDetails = _messages.MessageField('VmwareSourceVmDetails', 25)


class MigrationError(_messages.Message):
  r"""Represents migration resource error information that can be used with
  google.rpc.Status message. MigrationError is used to present the user with
  error information in migration operations.

  Enums:
    CodeValueValuesEnum: Output only. The error code.

  Fields:
    actionItem: Output only. Suggested action for solving the error.
    code: Output only. The error code.
    errorMessage: Output only. The localized error message.
    errorTime: Output only. The time the error occurred.
    helpLinks: Output only. URL(s) pointing to additional information on
      handling the current error.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Output only. The error code.

    Values:
      ERROR_CODE_UNSPECIFIED: Default value. This value is not used.
      UNKNOWN_ERROR: Migrate to Virtual Machines encountered an unknown error.
      SOURCE_VALIDATION_ERROR: Migrate to Virtual Machines encountered an
        error while validating replication source health.
      SOURCE_REPLICATION_ERROR: Migrate to Virtual Machines encountered an
        error during source data operation.
      TARGET_REPLICATION_ERROR: Migrate to Virtual Machines encountered an
        error during target data operation.
      OS_ADAPTATION_ERROR: Migrate to Virtual Machines encountered an error
        during OS adaptation.
      CLONE_ERROR: Migrate to Virtual Machines encountered an error in clone
        operation.
      CUTOVER_ERROR: Migrate to Virtual Machines encountered an error in
        cutover operation.
      UTILIZATION_REPORT_ERROR: Migrate to Virtual Machines encountered an
        error during utilization report creation.
      APPLIANCE_UPGRADE_ERROR: Migrate to Virtual Machines encountered an
        error during appliance upgrade.
      IMAGE_IMPORT_ERROR: Migrate to Virtual Machines encountered an error in
        image import operation.
      DISK_MIGRATION_ERROR: Migrate to Virtual Machines encountered an error
        in disk migration operation.
    """
    ERROR_CODE_UNSPECIFIED = 0
    UNKNOWN_ERROR = 1
    SOURCE_VALIDATION_ERROR = 2
    SOURCE_REPLICATION_ERROR = 3
    TARGET_REPLICATION_ERROR = 4
    OS_ADAPTATION_ERROR = 5
    CLONE_ERROR = 6
    CUTOVER_ERROR = 7
    UTILIZATION_REPORT_ERROR = 8
    APPLIANCE_UPGRADE_ERROR = 9
    IMAGE_IMPORT_ERROR = 10
    DISK_MIGRATION_ERROR = 11

  actionItem = _messages.MessageField('LocalizedMessage', 1)
  code = _messages.EnumField('CodeValueValuesEnum', 2)
  errorMessage = _messages.MessageField('LocalizedMessage', 3)
  errorTime = _messages.StringField(4)
  helpLinks = _messages.MessageField('Link', 5, repeated=True)


class MigrationWarning(_messages.Message):
  r"""Represents migration resource warning information that can be used with
  google.rpc.Status message. MigrationWarning is used to present the user with
  warning information in migration operations.

  Enums:
    CodeValueValuesEnum: The warning code.

  Fields:
    actionItem: Output only. Suggested action for solving the warning.
    code: The warning code.
    helpLinks: Output only. URL(s) pointing to additional information on
      handling the current warning.
    warningMessage: Output only. The localized warning message.
    warningTime: The time the warning occurred.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""The warning code.

    Values:
      WARNING_CODE_UNSPECIFIED: Default value. This value is not used.
      ADAPTATION_WARNING: A warning originated from OS Adaptation.
    """
    WARNING_CODE_UNSPECIFIED = 0
    ADAPTATION_WARNING = 1

  actionItem = _messages.MessageField('LocalizedMessage', 1)
  code = _messages.EnumField('CodeValueValuesEnum', 2)
  helpLinks = _messages.MessageField('Link', 3, repeated=True)
  warningMessage = _messages.MessageField('LocalizedMessage', 4)
  warningTime = _messages.StringField(5)


class NetworkInterface(_messages.Message):
  r"""NetworkInterface represents a NIC of a VM.

  Enums:
    NetworkTierValueValuesEnum: Optional. The networking tier used for
      optimizing connectivity between instances and systems on the internet.
      Applies only for external ephemeral IP addresses. If left empty, will
      default to PREMIUM.

  Fields:
    externalIp: Optional. The external IP to define in the NIC.
    internalIp: Optional. The internal IP to define in the NIC. The formats
      accepted are: `ephemeral` \ ipv4 address \ a named address resource full
      path.
    network: The network to connect the NIC to.
    networkTier: Optional. The networking tier used for optimizing
      connectivity between instances and systems on the internet. Applies only
      for external ephemeral IP addresses. If left empty, will default to
      PREMIUM.
    subnetwork: Optional. The subnetwork to connect the NIC to.
  """

  class NetworkTierValueValuesEnum(_messages.Enum):
    r"""Optional. The networking tier used for optimizing connectivity between
    instances and systems on the internet. Applies only for external ephemeral
    IP addresses. If left empty, will default to PREMIUM.

    Values:
      COMPUTE_ENGINE_NETWORK_TIER_UNSPECIFIED: An unspecified network tier.
        Will be used as PREMIUM.
      NETWORK_TIER_STANDARD: A standard network tier.
      NETWORK_TIER_PREMIUM: A premium network tier.
    """
    COMPUTE_ENGINE_NETWORK_TIER_UNSPECIFIED = 0
    NETWORK_TIER_STANDARD = 1
    NETWORK_TIER_PREMIUM = 2

  externalIp = _messages.StringField(1)
  internalIp = _messages.StringField(2)
  network = _messages.StringField(3)
  networkTier = _messages.EnumField('NetworkTierValueValuesEnum', 4)
  subnetwork = _messages.StringField(5)


class OSDescription(_messages.Message):
  r"""A message describing the VM's OS. Including OS, Publisher, Offer and
  Plan if applicable.

  Fields:
    offer: OS offer.
    plan: OS plan.
    publisher: OS publisher.
    type: OS type.
  """

  offer = _messages.StringField(1)
  plan = _messages.StringField(2)
  publisher = _messages.StringField(3)
  type = _messages.StringField(4)


class OSDisk(_messages.Message):
  r"""A message describing the OS disk.

  Fields:
    name: The disk's full name.
    sizeGb: The disk's size in GB.
    type: The disk's type.
  """

  name = _messages.StringField(1)
  sizeGb = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  type = _messages.StringField(3)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class PauseMigrationRequest(_messages.Message):
  r"""Request message for 'PauseMigration' request."""


class PersistentDisk(_messages.Message):
  r"""Details of a created Persistent Disk.

  Fields:
    diskUri: The URI of the Persistent Disk.
    sourceDiskNumber: The ordinal number of the source VM disk.
  """

  diskUri = _messages.StringField(1)
  sourceDiskNumber = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class PersistentDiskDefaults(_messages.Message):
  r"""Details for creation of a Persistent Disk.

  Enums:
    DiskTypeValueValuesEnum: The disk type to use.

  Messages:
    AdditionalLabelsValue: A map of labels to associate with the Persistent
      Disk.

  Fields:
    additionalLabels: A map of labels to associate with the Persistent Disk.
    diskName: Optional. The name of the Persistent Disk to create.
    diskType: The disk type to use.
    encryption: Optional. The encryption to apply to the disk.
    sourceDiskNumber: Required. The ordinal number of the source VM disk.
    vmAttachmentDetails: Optional. Details for attachment of the disk to a VM.
      Used when the disk is set to be attached to a target VM.
  """

  class DiskTypeValueValuesEnum(_messages.Enum):
    r"""The disk type to use.

    Values:
      COMPUTE_ENGINE_DISK_TYPE_UNSPECIFIED: An unspecified disk type. Will be
        used as STANDARD.
      COMPUTE_ENGINE_DISK_TYPE_STANDARD: A Standard disk type.
      COMPUTE_ENGINE_DISK_TYPE_SSD: SSD hard disk type.
      COMPUTE_ENGINE_DISK_TYPE_BALANCED: An alternative to SSD persistent
        disks that balance performance and cost.
      COMPUTE_ENGINE_DISK_TYPE_HYPERDISK_BALANCED: Hyperdisk balanced disk
        type.
    """
    COMPUTE_ENGINE_DISK_TYPE_UNSPECIFIED = 0
    COMPUTE_ENGINE_DISK_TYPE_STANDARD = 1
    COMPUTE_ENGINE_DISK_TYPE_SSD = 2
    COMPUTE_ENGINE_DISK_TYPE_BALANCED = 3
    COMPUTE_ENGINE_DISK_TYPE_HYPERDISK_BALANCED = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AdditionalLabelsValue(_messages.Message):
    r"""A map of labels to associate with the Persistent Disk.

    Messages:
      AdditionalProperty: An additional property for a AdditionalLabelsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        AdditionalLabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AdditionalLabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  additionalLabels = _messages.MessageField('AdditionalLabelsValue', 1)
  diskName = _messages.StringField(2)
  diskType = _messages.EnumField('DiskTypeValueValuesEnum', 3)
  encryption = _messages.MessageField('Encryption', 4)
  sourceDiskNumber = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  vmAttachmentDetails = _messages.MessageField('VmAttachmentDetails', 6)


class PostProcessingStep(_messages.Message):
  r"""PostProcessingStep contains specific step details."""


class PreparingVMDisksStep(_messages.Message):
  r"""PreparingVMDisksStep contains specific step details."""


class ProvisioningTargetDiskStep(_messages.Message):
  r"""ProvisioningTargetDiskStep contains specific step details."""


class RemoveGroupMigrationRequest(_messages.Message):
  r"""Request message for 'RemoveMigration' request.

  Fields:
    migratingVm: The MigratingVm to remove.
  """

  migratingVm = _messages.StringField(1)


class ReplicatingStep(_messages.Message):
  r"""ReplicatingStep contains specific step details.

  Fields:
    lastThirtyMinutesAverageBytesPerSecond: The source disks replication rate
      for the last 30 minutes in bytes per second.
    lastTwoMinutesAverageBytesPerSecond: The source disks replication rate for
      the last 2 minutes in bytes per second.
    replicatedBytes: Replicated bytes in the step.
    totalBytes: Total bytes to be handled in the step.
  """

  lastThirtyMinutesAverageBytesPerSecond = _messages.IntegerField(1)
  lastTwoMinutesAverageBytesPerSecond = _messages.IntegerField(2)
  replicatedBytes = _messages.IntegerField(3)
  totalBytes = _messages.IntegerField(4)


class ReplicationCycle(_messages.Message):
  r"""ReplicationCycle contains information about the current replication
  cycle status.

  Enums:
    StateValueValuesEnum: State of the ReplicationCycle.

  Fields:
    cycleNumber: The cycle's ordinal number.
    endTime: The time the replication cycle has ended.
    error: Output only. Provides details on the state of the cycle in case of
      an error.
    name: The identifier of the ReplicationCycle.
    progress: The current progress in percentage of this cycle.
    progressPercent: The current progress in percentage of this cycle. Was
      replaced by 'steps' field, which breaks down the cycle progression more
      accurately.
    startTime: The time the replication cycle has started.
    state: State of the ReplicationCycle.
    steps: The cycle's steps list representing its progress.
    totalPauseDuration: The accumulated duration the replication cycle was
      paused.
    warnings: Output only. Warnings that occurred during the cycle.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""State of the ReplicationCycle.

    Values:
      STATE_UNSPECIFIED: The state is unknown. This is used for API
        compatibility only and is not used by the system.
      RUNNING: The replication cycle is running.
      PAUSED: The replication cycle is paused.
      FAILED: The replication cycle finished with errors.
      SUCCEEDED: The replication cycle finished successfully.
    """
    STATE_UNSPECIFIED = 0
    RUNNING = 1
    PAUSED = 2
    FAILED = 3
    SUCCEEDED = 4

  cycleNumber = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  endTime = _messages.StringField(2)
  error = _messages.MessageField('Status', 3)
  name = _messages.StringField(4)
  progress = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  progressPercent = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  startTime = _messages.StringField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  steps = _messages.MessageField('CycleStep', 9, repeated=True)
  totalPauseDuration = _messages.StringField(10)
  warnings = _messages.MessageField('MigrationWarning', 11, repeated=True)


class ReplicationSync(_messages.Message):
  r"""ReplicationSync contain information about the last replica sync to the
  cloud.

  Fields:
    lastSyncTime: The most updated snapshot created time in the source that
      finished replication.
  """

  lastSyncTime = _messages.StringField(1)


class ResumeMigrationRequest(_messages.Message):
  r"""Request message for 'ResumeMigration' request."""


class RunDiskMigrationJobRequest(_messages.Message):
  r"""Request message for 'RunDiskMigrationJobRequest' request."""


class SchedulePolicy(_messages.Message):
  r"""A policy for scheduling replications.

  Fields:
    idleDuration: The idle duration between replication stages.
    skipOsAdaptation: A flag to indicate whether to skip OS adaptation during
      the replication sync. OS adaptation is a process where the VM's
      operating system undergoes changes and adaptations to fully function on
      Compute Engine.
  """

  idleDuration = _messages.StringField(1)
  skipOsAdaptation = _messages.BooleanField(2)


class SchedulingNodeAffinity(_messages.Message):
  r"""Node Affinity: the configuration of desired nodes onto which this
  Instance could be scheduled. Based on https://cloud.google.com/compute/docs/
  reference/rest/v1/instances/setScheduling

  Enums:
    OperatorValueValuesEnum: The operator to use for the node resources
      specified in the `values` parameter.

  Fields:
    key: The label key of Node resource to reference.
    operator: The operator to use for the node resources specified in the
      `values` parameter.
    values: Corresponds to the label values of Node resource.
  """

  class OperatorValueValuesEnum(_messages.Enum):
    r"""The operator to use for the node resources specified in the `values`
    parameter.

    Values:
      OPERATOR_UNSPECIFIED: An unknown, unexpected behavior.
      IN: The node resource group should be in these resources affinity.
      NOT_IN: The node resource group should not be in these resources
        affinity.
    """
    OPERATOR_UNSPECIFIED = 0
    IN = 1
    NOT_IN = 2

  key = _messages.StringField(1)
  operator = _messages.EnumField('OperatorValueValuesEnum', 2)
  values = _messages.StringField(3, repeated=True)


class ServiceAccount(_messages.Message):
  r"""Service account to assign to the instance created by the machine image.

  Fields:
    email: Required. The email address of the service account.
    scopes: Optional. The list of scopes to be made available for this service
      account.
  """

  email = _messages.StringField(1)
  scopes = _messages.StringField(2, repeated=True)


class ShieldedInstanceConfig(_messages.Message):
  r"""Shielded instance configuration.

  Enums:
    SecureBootValueValuesEnum: Optional. Defines whether the instance created
      by the machine image has Secure Boot enabled. This can be set to true
      only if the image boot option is EFI.

  Fields:
    enableIntegrityMonitoring: Optional. Defines whether the instance created
      by the machine image has integrity monitoring enabled. This can be set
      to true only if the image boot option is EFI, and vTPM is enabled.
    enableVtpm: Optional. Defines whether the instance created by the machine
      image has vTPM enabled. This can be set to true only if the image boot
      option is EFI.
    secureBoot: Optional. Defines whether the instance created by the machine
      image has Secure Boot enabled. This can be set to true only if the image
      boot option is EFI.
  """

  class SecureBootValueValuesEnum(_messages.Enum):
    r"""Optional. Defines whether the instance created by the machine image
    has Secure Boot enabled. This can be set to true only if the image boot
    option is EFI.

    Values:
      SECURE_BOOT_UNSPECIFIED: No explicit value is selected. Will use the
        configuration of the source (if exists, otherwise the default will be
        false).
      TRUE: Use secure boot. This can be set to true only if the image boot
        option is EFI.
      FALSE: Do not use secure boot.
    """
    SECURE_BOOT_UNSPECIFIED = 0
    TRUE = 1
    FALSE = 2

  enableIntegrityMonitoring = _messages.BooleanField(1)
  enableVtpm = _messages.BooleanField(2)
  secureBoot = _messages.EnumField('SecureBootValueValuesEnum', 3)


class ShuttingDownSourceVMStep(_messages.Message):
  r"""ShuttingDownSourceVMStep contains specific step details."""


class SkipOsAdaptation(_messages.Message):
  r"""Mentions that the machine image import is not using OS adaptation
  process.
  """



class Source(_messages.Message):
  r"""Source message describes a specific vm migration Source resource. It
  contains the source environment information.

  Messages:
    LabelsValue: The labels of the source.

  Fields:
    aws: AWS type source details.
    azure: Azure type source details.
    createTime: Output only. The create time timestamp.
    description: User-provided description of the source.
    encryption: Optional. Immutable. The encryption details of the source data
      stored by the service.
    error: Output only. Provides details on the state of the Source in case of
      an error.
    labels: The labels of the source.
    name: Output only. The Source name.
    updateTime: Output only. The update time timestamp.
    vmware: Vmware type source details.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels of the source.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  aws = _messages.MessageField('AwsSourceDetails', 1)
  azure = _messages.MessageField('AzureSourceDetails', 2)
  createTime = _messages.StringField(3)
  description = _messages.StringField(4)
  encryption = _messages.MessageField('Encryption', 5)
  error = _messages.MessageField('Status', 6)
  labels = _messages.MessageField('LabelsValue', 7)
  name = _messages.StringField(8)
  updateTime = _messages.StringField(9)
  vmware = _messages.MessageField('VmwareSourceDetails', 10)


class SourceStorageResource(_messages.Message):
  r"""SourceStorageResource describes a storage resource in the source.

  Fields:
    awsDiskDetails: Source AWS volume details.
  """

  awsDiskDetails = _messages.MessageField('AwsSourceDiskDetails', 1)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StartMigrationRequest(_messages.Message):
  r"""Request message for 'StartMigrationRequest' request."""


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class Tag(_messages.Message):
  r"""Tag is an AWS tag representation.

  Fields:
    key: Required. Key of tag.
    value: Required. Value of tag.
  """

  key = _messages.StringField(1)
  value = _messages.StringField(2)


class TargetProject(_messages.Message):
  r"""TargetProject message represents a target Compute Engine project for a
  migration or a clone.

  Fields:
    createTime: Output only. The time this target project resource was created
      (not related to when the Compute Engine project it points to was
      created).
    description: The target project's description.
    name: Output only. The name of the target project.
    project: Required. The target project ID (number) or project name.
    updateTime: Output only. The last time the target project resource was
      updated.
  """

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  name = _messages.StringField(3)
  project = _messages.StringField(4)
  updateTime = _messages.StringField(5)


class TargetVMDetails(_messages.Message):
  r"""TargetVMDetails is a collection of details for creating a VM in a target
  Compute Engine project.

  Enums:
    BootOptionValueValuesEnum: Output only. The VM Boot Option, as set in the
      source VM.
    DiskTypeValueValuesEnum: The disk type to use in the VM.
    LicenseTypeValueValuesEnum: The license type to use in OS adaptation.

  Messages:
    LabelsValue: A map of labels to associate with the VM.
    MetadataValue: The metadata key/value pairs to assign to the VM.

  Fields:
    appliedLicense: Output only. The OS license returned from the adaptation
      module report.
    bootOption: Output only. The VM Boot Option, as set in the source VM.
    computeScheduling: Compute instance scheduling information (if empty
      default is used).
    diskType: The disk type to use in the VM.
    externalIp: The external IP to define in the VM.
    internalIp: The internal IP to define in the VM. The formats accepted are:
      `ephemeral` \ ipv4 address \ a named address resource full path.
    labels: A map of labels to associate with the VM.
    licenseType: The license type to use in OS adaptation.
    machineType: The machine type to create the VM with.
    machineTypeSeries: The machine type series to create the VM with.
    metadata: The metadata key/value pairs to assign to the VM.
    name: The name of the VM to create.
    network: The network to connect the VM to.
    networkInterfaces: List of NICs connected to this VM.
    networkTags: A list of network tags to associate with the VM.
    project: Output only. The project in which to create the VM.
    secureBoot: Defines whether the instance has Secure Boot enabled. This can
      be set to true only if the vm boot option is EFI.
    serviceAccount: The service account to associate the VM with.
    subnetwork: The subnetwork to connect the VM to.
    targetProject: The full path of the resource of type TargetProject which
      represents the Compute Engine project in which to create this VM.
    zone: The zone in which to create the VM.
  """

  class BootOptionValueValuesEnum(_messages.Enum):
    r"""Output only. The VM Boot Option, as set in the source VM.

    Values:
      BOOT_OPTION_UNSPECIFIED: The boot option is unknown.
      EFI: The boot option is EFI.
      BIOS: The boot option is BIOS.
    """
    BOOT_OPTION_UNSPECIFIED = 0
    EFI = 1
    BIOS = 2

  class DiskTypeValueValuesEnum(_messages.Enum):
    r"""The disk type to use in the VM.

    Values:
      DISK_TYPE_UNSPECIFIED: An unspecified disk type. Will be used as
        STANDARD.
      STANDARD: A Standard disk type.
      BALANCED: An alternative to SSD persistent disks that balance
        performance and cost.
      SSD: SSD hard disk type.
      HYPERDISK_BALANCED: Hyperdisk balanced disk type.
    """
    DISK_TYPE_UNSPECIFIED = 0
    STANDARD = 1
    BALANCED = 2
    SSD = 3
    HYPERDISK_BALANCED = 4

  class LicenseTypeValueValuesEnum(_messages.Enum):
    r"""The license type to use in OS adaptation.

    Values:
      DEFAULT: The license type is the default for the OS.
      PAYG: The license type is Pay As You Go license type.
      BYOL: The license type is Bring Your Own License type.
    """
    DEFAULT = 0
    PAYG = 1
    BYOL = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""A map of labels to associate with the VM.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""The metadata key/value pairs to assign to the VM.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Additional properties of type MetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  appliedLicense = _messages.MessageField('AppliedLicense', 1)
  bootOption = _messages.EnumField('BootOptionValueValuesEnum', 2)
  computeScheduling = _messages.MessageField('ComputeScheduling', 3)
  diskType = _messages.EnumField('DiskTypeValueValuesEnum', 4)
  externalIp = _messages.StringField(5)
  internalIp = _messages.StringField(6)
  labels = _messages.MessageField('LabelsValue', 7)
  licenseType = _messages.EnumField('LicenseTypeValueValuesEnum', 8)
  machineType = _messages.StringField(9)
  machineTypeSeries = _messages.StringField(10)
  metadata = _messages.MessageField('MetadataValue', 11)
  name = _messages.StringField(12)
  network = _messages.StringField(13)
  networkInterfaces = _messages.MessageField('NetworkInterface', 14, repeated=True)
  networkTags = _messages.StringField(15, repeated=True)
  project = _messages.StringField(16)
  secureBoot = _messages.BooleanField(17)
  serviceAccount = _messages.StringField(18)
  subnetwork = _messages.StringField(19)
  targetProject = _messages.StringField(20)
  zone = _messages.StringField(21)


class UpgradeApplianceRequest(_messages.Message):
  r"""Request message for 'UpgradeAppliance' request.

  Fields:
    requestId: A request ID to identify requests. Specify a unique request ID
      so that if you must retry your request, the server will know to ignore
      the request if it has already been completed. The server will guarantee
      that for at least 60 minutes after the first request. For example,
      consider a situation where you make an initial request and the request
      times out. If you make the request again with the same request ID, the
      server can check if original operation with the same request ID was
      received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  requestId = _messages.StringField(1)


class UpgradeStatus(_messages.Message):
  r"""UpgradeStatus contains information about upgradeAppliance operation.

  Enums:
    StateValueValuesEnum: The state of the upgradeAppliance operation.

  Fields:
    error: Output only. Provides details on the state of the upgrade operation
      in case of an error.
    previousVersion: The version from which we upgraded.
    startTime: The time the operation was started.
    state: The state of the upgradeAppliance operation.
    version: The version to upgrade to.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of the upgradeAppliance operation.

    Values:
      STATE_UNSPECIFIED: The state was not sampled by the health checks yet.
      RUNNING: The upgrade has started.
      FAILED: The upgrade failed.
      SUCCEEDED: The upgrade finished successfully.
    """
    STATE_UNSPECIFIED = 0
    RUNNING = 1
    FAILED = 2
    SUCCEEDED = 3

  error = _messages.MessageField('Status', 1)
  previousVersion = _messages.StringField(2)
  startTime = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)
  version = _messages.StringField(5)


class UtilizationReport(_messages.Message):
  r"""Utilization report details the utilization (CPU, memory, etc.) of
  selected source VMs.

  Enums:
    StateValueValuesEnum: Output only. Current state of the report.
    TimeFrameValueValuesEnum: Time frame of the report.

  Fields:
    createTime: Output only. The time the report was created (this refers to
      the time of the request, not the time the report creation completed).
    displayName: The report display name, as assigned by the user.
    error: Output only. Provides details on the state of the report in case of
      an error.
    frameEndTime: Output only. The point in time when the time frame ends.
      Notice that the time frame is counted backwards. For instance if the
      "frame_end_time" value is 2021/01/20 and the time frame is WEEK then the
      report covers the week between 2021/01/20 and 2021/01/14.
    name: Output only. The report unique name.
    state: Output only. Current state of the report.
    stateTime: Output only. The time the state was last set.
    timeFrame: Time frame of the report.
    vmCount: Output only. Total number of VMs included in the report.
    vms: List of utilization information per VM. When sent as part of the
      request, the "vm_id" field is used in order to specify which VMs to
      include in the report. In that case all other fields are ignored.
    vmsCount: Output only. Total number of VMs included in the report.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the report.

    Values:
      STATE_UNSPECIFIED: The state is unknown. This value is not in use.
      CREATING: The report is in the making.
      SUCCEEDED: Report creation completed successfully.
      FAILED: Report creation failed.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    SUCCEEDED = 2
    FAILED = 3

  class TimeFrameValueValuesEnum(_messages.Enum):
    r"""Time frame of the report.

    Values:
      TIME_FRAME_UNSPECIFIED: The time frame was not specified and will
        default to WEEK.
      WEEK: One week.
      MONTH: One month.
      YEAR: One year.
    """
    TIME_FRAME_UNSPECIFIED = 0
    WEEK = 1
    MONTH = 2
    YEAR = 3

  createTime = _messages.StringField(1)
  displayName = _messages.StringField(2)
  error = _messages.MessageField('Status', 3)
  frameEndTime = _messages.StringField(4)
  name = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  stateTime = _messages.StringField(7)
  timeFrame = _messages.EnumField('TimeFrameValueValuesEnum', 8)
  vmCount = _messages.IntegerField(9, variant=_messages.Variant.INT32)
  vms = _messages.MessageField('VmUtilizationInfo', 10, repeated=True)
  vmsCount = _messages.IntegerField(11, variant=_messages.Variant.INT32)


class VmAttachmentDetails(_messages.Message):
  r"""Details for attachment of the disk to a VM.

  Fields:
    deviceName: Optional. Specifies a unique device name of your choice that
      is reflected into the /dev/disk/by-id/google-* tree of a Linux operating
      system running within the instance. If not specified, the server chooses
      a default device name to apply to this disk, in the form persistent-
      disk-x, where x is a number assigned by Google Compute Engine. This
      field is only applicable for persistent disks.
  """

  deviceName = _messages.StringField(1)


class VmCapabilities(_messages.Message):
  r"""Migrating VM source information about the VM capabilities needed for
  some Compute Engine features.

  Enums:
    OsCapabilitiesValueListEntryValuesEnum:

  Fields:
    lastOsCapabilitiesUpdateTime: Output only. The last time OS capabilities
      list was updated.
    osCapabilities: Output only. Unordered list. List of certain VM OS
      capabilities needed for some Compute Engine features.
  """

  class OsCapabilitiesValueListEntryValuesEnum(_messages.Enum):
    r"""OsCapabilitiesValueListEntryValuesEnum enum type.

    Values:
      OS_CAPABILITY_UNSPECIFIED: This is for API compatibility only and is not
        in use.
      OS_CAPABILITY_NVME_STORAGE_ACCESS: NVMe driver installed and the VM can
        use NVMe PD or local SSD.
      OS_CAPABILITY_GVNIC_NETWORK_INTERFACE: gVNIC virtual NIC driver
        supported.
      OS_CAPABILITY_IDPF_NETWORK_INTERFACE: IDPF virtual NIC driver supported.
    """
    OS_CAPABILITY_UNSPECIFIED = 0
    OS_CAPABILITY_NVME_STORAGE_ACCESS = 1
    OS_CAPABILITY_GVNIC_NETWORK_INTERFACE = 2
    OS_CAPABILITY_IDPF_NETWORK_INTERFACE = 3

  lastOsCapabilitiesUpdateTime = _messages.StringField(1)
  osCapabilities = _messages.EnumField('OsCapabilitiesValueListEntryValuesEnum', 2, repeated=True)


class VmUtilizationInfo(_messages.Message):
  r"""Utilization information of a single VM.

  Fields:
    utilization: Utilization metrics for this VM.
    vmId: The VM's ID in the source.
    vmwareVmDetails: The description of the VM in a Source of type Vmware.
  """

  utilization = _messages.MessageField('VmUtilizationMetrics', 1)
  vmId = _messages.StringField(2)
  vmwareVmDetails = _messages.MessageField('VmwareVmDetails', 3)


class VmUtilizationMetrics(_messages.Message):
  r"""Utilization metrics values for a single VM.

  Fields:
    cpuAverage: Average CPU usage, percent.
    cpuAveragePercent: Average CPU usage, percent.
    cpuMax: Max CPU usage, percent.
    cpuMaxPercent: Max CPU usage, percent.
    diskIoRateAverage: Average disk IO rate, in kilobytes per second.
    diskIoRateAverageKbps: Average disk IO rate, in kilobytes per second.
    diskIoRateMax: Max disk IO rate, in kilobytes per second.
    diskIoRateMaxKbps: Max disk IO rate, in kilobytes per second.
    memoryAverage: Average memory usage, percent.
    memoryAveragePercent: Average memory usage, percent.
    memoryMax: Max memory usage, percent.
    memoryMaxPercent: Max memory usage, percent.
    networkThroughputAverage: Average network throughput (combined transmit-
      rates and receive-rates), in kilobytes per second.
    networkThroughputAverageKbps: Average network throughput (combined
      transmit-rates and receive-rates), in kilobytes per second.
    networkThroughputMax: Max network throughput (combined transmit-rates and
      receive-rates), in kilobytes per second.
    networkThroughputMaxKbps: Max network throughput (combined transmit-rates
      and receive-rates), in kilobytes per second.
  """

  cpuAverage = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  cpuAveragePercent = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  cpuMax = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  cpuMaxPercent = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  diskIoRateAverage = _messages.IntegerField(5)
  diskIoRateAverageKbps = _messages.IntegerField(6)
  diskIoRateMax = _messages.IntegerField(7)
  diskIoRateMaxKbps = _messages.IntegerField(8)
  memoryAverage = _messages.IntegerField(9, variant=_messages.Variant.INT32)
  memoryAveragePercent = _messages.IntegerField(10, variant=_messages.Variant.INT32)
  memoryMax = _messages.IntegerField(11, variant=_messages.Variant.INT32)
  memoryMaxPercent = _messages.IntegerField(12, variant=_messages.Variant.INT32)
  networkThroughputAverage = _messages.IntegerField(13)
  networkThroughputAverageKbps = _messages.IntegerField(14)
  networkThroughputMax = _messages.IntegerField(15)
  networkThroughputMaxKbps = _messages.IntegerField(16)


class VmmigrationProjectsLocationsGetRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class VmmigrationProjectsLocationsGroupsAddGroupMigrationRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsGroupsAddGroupMigrationRequest object.

  Fields:
    addGroupMigrationRequest: A AddGroupMigrationRequest resource to be passed
      as the request body.
    group: Required. The full path name of the Group to add to.
  """

  addGroupMigrationRequest = _messages.MessageField('AddGroupMigrationRequest', 1)
  group = _messages.StringField(2, required=True)


class VmmigrationProjectsLocationsGroupsCreateRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsGroupsCreateRequest object.

  Fields:
    group: A Group resource to be passed as the request body.
    groupId: Required. The group identifier.
    parent: Required. The Group's parent.
    requestId: A request ID to identify requests. Specify a unique request ID
      so that if you must retry your request, the server will know to ignore
      the request if it has already been completed. The server will guarantee
      that for at least 60 minutes since the first request. For example,
      consider a situation where you make an initial request and the request
      times out. If you make the request again with the same request ID, the
      server can check if original operation with the same request ID was
      received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  group = _messages.MessageField('Group', 1)
  groupId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class VmmigrationProjectsLocationsGroupsDeleteRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsGroupsDeleteRequest object.

  Fields:
    name: Required. The Group name.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class VmmigrationProjectsLocationsGroupsGetRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsGroupsGetRequest object.

  Fields:
    name: Required. The group name.
  """

  name = _messages.StringField(1, required=True)


class VmmigrationProjectsLocationsGroupsListRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsGroupsListRequest object.

  Fields:
    filter: Optional. The filter request.
    orderBy: Optional. the order by fields for the result.
    pageSize: Optional. The maximum number of groups to return. The service
      may return fewer than this value. If unspecified, at most 500 groups
      will be returned. The maximum value is 1000; values above 1000 will be
      coerced to 1000.
    pageToken: Required. A page token, received from a previous `ListGroups`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListGroups` must match the call that
      provided the page token.
    parent: Required. The parent, which owns this collection of groups.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class VmmigrationProjectsLocationsGroupsPatchRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsGroupsPatchRequest object.

  Fields:
    group: A Group resource to be passed as the request body.
    name: Output only. The Group name.
    requestId: A request ID to identify requests. Specify a unique request ID
      so that if you must retry your request, the server will know to ignore
      the request if it has already been completed. The server will guarantee
      that for at least 60 minutes since the first request. For example,
      consider a situation where you make an initial request and the request
      times out. If you make the request again with the same request ID, the
      server can check if original operation with the same request ID was
      received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Field mask is used to specify the fields to be overwritten in
      the Group resource by the update. The fields specified in the
      update_mask are relative to the resource, not the full request. A field
      will be overwritten if it is in the mask. If the user does not provide a
      mask then all fields will be overwritten.
  """

  group = _messages.MessageField('Group', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class VmmigrationProjectsLocationsGroupsRemoveGroupMigrationRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsGroupsRemoveGroupMigrationRequest object.

  Fields:
    group: Required. The name of the Group.
    removeGroupMigrationRequest: A RemoveGroupMigrationRequest resource to be
      passed as the request body.
  """

  group = _messages.StringField(1, required=True)
  removeGroupMigrationRequest = _messages.MessageField('RemoveGroupMigrationRequest', 2)


class VmmigrationProjectsLocationsImageImportsCreateRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsImageImportsCreateRequest object.

  Fields:
    imageImport: A ImageImport resource to be passed as the request body.
    imageImportId: Required. The image import identifier. This value maximum
      length is 63 characters, and valid characters are /a-z-/. It must start
      with an english letter and must not end with a hyphen.
    parent: Required. The ImageImport's parent.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  imageImport = _messages.MessageField('ImageImport', 1)
  imageImportId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class VmmigrationProjectsLocationsImageImportsDeleteRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsImageImportsDeleteRequest object.

  Fields:
    name: Required. The ImageImport name.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and t he
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class VmmigrationProjectsLocationsImageImportsGetRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsImageImportsGetRequest object.

  Fields:
    name: Required. The ImageImport name.
  """

  name = _messages.StringField(1, required=True)


class VmmigrationProjectsLocationsImageImportsImageImportJobsCancelRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsImageImportsImageImportJobsCancelRequest
  object.

  Fields:
    cancelImageImportJobRequest: A CancelImageImportJobRequest resource to be
      passed as the request body.
    name: Required. The image import job id.
  """

  cancelImageImportJobRequest = _messages.MessageField('CancelImageImportJobRequest', 1)
  name = _messages.StringField(2, required=True)


class VmmigrationProjectsLocationsImageImportsImageImportJobsGetRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsImageImportsImageImportJobsGetRequest
  object.

  Fields:
    name: Required. The ImageImportJob name.
  """

  name = _messages.StringField(1, required=True)


class VmmigrationProjectsLocationsImageImportsImageImportJobsListRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsImageImportsImageImportJobsListRequest
  object.

  Fields:
    filter: Optional. The filter request (according to AIP-160).
    orderBy: Optional. The order by fields for the result (according to
      AIP-132). Currently ordering is only possible by "name" field.
    pageSize: Optional. The maximum number of targets to return. The service
      may return fewer than this value. If unspecified, at most 500 targets
      will be returned. The maximum value is 1000; values above 1000 will be
      coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListImageImportJobs` call. Provide this to retrieve the subsequent
      page. When paginating, all other parameters provided to
      `ListImageImportJobs` must match the call that provided the page token.
    parent: Required. The parent, which owns this collection of targets.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class VmmigrationProjectsLocationsImageImportsListRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsImageImportsListRequest object.

  Fields:
    filter: Optional. The filter request (according to AIP-160).
    orderBy: Optional. The order by fields for the result (according to
      AIP-132). Currently ordering is only possible by "name" field.
    pageSize: Optional. The maximum number of targets to return. The service
      may return fewer than this value. If unspecified, at most 500 targets
      will be returned. The maximum value is 1000; values above 1000 will be
      coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListImageImports` call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters provided to `ListImageImports`
      must match the call that provided the page token.
    parent: Required. The parent, which owns this collection of targets.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class VmmigrationProjectsLocationsListRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class VmmigrationProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class VmmigrationProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class VmmigrationProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class VmmigrationProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class VmmigrationProjectsLocationsSourcesCreateRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesCreateRequest object.

  Fields:
    parent: Required. The Source's parent.
    requestId: A request ID to identify requests. Specify a unique request ID
      so that if you must retry your request, the server will know to ignore
      the request if it has already been completed. The server will guarantee
      that for at least 60 minutes since the first request. For example,
      consider a situation where you make an initial request and the request
      times out. If you make the request again with the same request ID, the
      server can check if original operation with the same request ID was
      received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    source: A Source resource to be passed as the request body.
    sourceId: Required. The source identifier.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  source = _messages.MessageField('Source', 3)
  sourceId = _messages.StringField(4)


class VmmigrationProjectsLocationsSourcesDatacenterConnectorsCreateRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesDatacenterConnectorsCreateRequest
  object.

  Fields:
    datacenterConnector: A DatacenterConnector resource to be passed as the
      request body.
    datacenterConnectorId: Required. The datacenterConnector identifier.
    parent: Required. The DatacenterConnector's parent. Required. The Source
      in where the new DatacenterConnector will be created. For example:
      `projects/my-project/locations/us-central1/sources/my-source`
    requestId: A request ID to identify requests. Specify a unique request ID
      so that if you must retry your request, the server will know to ignore
      the request if it has already been completed. The server will guarantee
      that for at least 60 minutes since the first request. For example,
      consider a situation where you make an initial request and the request
      times out. If you make the request again with the same request ID, the
      server can check if original operation with the same request ID was
      received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  datacenterConnector = _messages.MessageField('DatacenterConnector', 1)
  datacenterConnectorId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class VmmigrationProjectsLocationsSourcesDatacenterConnectorsDeleteRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesDatacenterConnectorsDeleteRequest
  object.

  Fields:
    name: Required. The DatacenterConnector name.
    requestId: A request ID to identify requests. Specify a unique request ID
      so that if you must retry your request, the server will know to ignore
      the request if it has already been completed. The server will guarantee
      that for at least 60 minutes after the first request. For example,
      consider a situation where you make an initial request and the request
      times out. If you make the request again with the same request ID, the
      server can check if original operation with the same request ID was
      received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class VmmigrationProjectsLocationsSourcesDatacenterConnectorsGetRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesDatacenterConnectorsGetRequest
  object.

  Fields:
    name: Required. The name of the DatacenterConnector.
  """

  name = _messages.StringField(1, required=True)


class VmmigrationProjectsLocationsSourcesDatacenterConnectorsListRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesDatacenterConnectorsListRequest
  object.

  Fields:
    filter: Optional. The filter request.
    orderBy: Optional. the order by fields for the result.
    pageSize: Optional. The maximum number of connectors to return. The
      service may return fewer than this value. If unspecified, at most 500
      sources will be returned. The maximum value is 1000; values above 1000
      will be coerced to 1000.
    pageToken: Required. A page token, received from a previous
      `ListDatacenterConnectors` call. Provide this to retrieve the subsequent
      page. When paginating, all other parameters provided to
      `ListDatacenterConnectors` must match the call that provided the page
      token.
    parent: Required. The parent, which owns this collection of connectors.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class VmmigrationProjectsLocationsSourcesDatacenterConnectorsUpgradeApplianceRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesDatacenterConnectorsUpgradeApplianc
  eRequest object.

  Fields:
    datacenterConnector: Required. The DatacenterConnector name.
    upgradeApplianceRequest: A UpgradeApplianceRequest resource to be passed
      as the request body.
  """

  datacenterConnector = _messages.StringField(1, required=True)
  upgradeApplianceRequest = _messages.MessageField('UpgradeApplianceRequest', 2)


class VmmigrationProjectsLocationsSourcesDeleteRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesDeleteRequest object.

  Fields:
    name: Required. The Source name.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class VmmigrationProjectsLocationsSourcesDiskMigrationJobsCancelRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesDiskMigrationJobsCancelRequest
  object.

  Fields:
    cancelDiskMigrationJobRequest: A CancelDiskMigrationJobRequest resource to
      be passed as the request body.
    name: Required. The name of the DiskMigrationJob.
  """

  cancelDiskMigrationJobRequest = _messages.MessageField('CancelDiskMigrationJobRequest', 1)
  name = _messages.StringField(2, required=True)


class VmmigrationProjectsLocationsSourcesDiskMigrationJobsCreateRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesDiskMigrationJobsCreateRequest
  object.

  Fields:
    diskMigrationJob: A DiskMigrationJob resource to be passed as the request
      body.
    diskMigrationJobId: Required. The DiskMigrationJob identifier. The maximum
      length of this value is 63 characters. Valid characters are lower case
      Latin letters, digits and hyphen. It must start with a Latin letter and
      must not end with a hyphen.
    parent: Required. The DiskMigrationJob's parent.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request timed out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  diskMigrationJob = _messages.MessageField('DiskMigrationJob', 1)
  diskMigrationJobId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class VmmigrationProjectsLocationsSourcesDiskMigrationJobsDeleteRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesDiskMigrationJobsDeleteRequest
  object.

  Fields:
    name: Required. The name of the DiskMigrationJob.
  """

  name = _messages.StringField(1, required=True)


class VmmigrationProjectsLocationsSourcesDiskMigrationJobsGetRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesDiskMigrationJobsGetRequest object.

  Fields:
    name: Required. The name of the DiskMigrationJob.
  """

  name = _messages.StringField(1, required=True)


class VmmigrationProjectsLocationsSourcesDiskMigrationJobsListRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesDiskMigrationJobsListRequest
  object.

  Fields:
    filter: Optional. The filter request (according to AIP-160).
    orderBy: Optional. Ordering of the result list.
    pageSize: Optional. The maximum number of disk migration jobs to return.
      The service may return fewer than this value. If unspecified, at most
      500 disk migration jobs will be returned. The maximum value is 1000;
      values above 1000 will be coerced to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListDiskMigrationJobs` call. Provide this to retrieve the subsequent
      page. When paginating, all parameters provided to
      `ListDiskMigrationJobs` except `page_size` must match the call that
      provided the page token.
    parent: Required. The parent, which owns this collection of
      DiskMigrationJobs.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class VmmigrationProjectsLocationsSourcesDiskMigrationJobsPatchRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesDiskMigrationJobsPatchRequest
  object.

  Fields:
    diskMigrationJob: A DiskMigrationJob resource to be passed as the request
      body.
    name: Output only. Identifier. The identifier of the DiskMigrationJob.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request timed out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the DiskMigrationJob resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask, then a mask equivalent to all fields that are
      populated (have a non-empty value), will be implied.
  """

  diskMigrationJob = _messages.MessageField('DiskMigrationJob', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class VmmigrationProjectsLocationsSourcesDiskMigrationJobsRunRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesDiskMigrationJobsRunRequest object.

  Fields:
    name: Required. The name of the DiskMigrationJob.
    runDiskMigrationJobRequest: A RunDiskMigrationJobRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  runDiskMigrationJobRequest = _messages.MessageField('RunDiskMigrationJobRequest', 2)


class VmmigrationProjectsLocationsSourcesFetchInventoryRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesFetchInventoryRequest object.

  Fields:
    forceRefresh: If this flag is set to true, the source will be queried
      instead of using cached results. Using this flag will make the call
      slower.
    pageSize: The maximum number of VMs to return. The service may return
      fewer than this value. For AWS source: If unspecified, at most 500 VMs
      will be returned. The maximum value is 1000; values above 1000 will be
      coerced to 1000. For VMWare source: If unspecified, all VMs will be
      returned. There is no limit for maximum value.
    pageToken: A page token, received from a previous `FetchInventory` call.
      Provide this to retrieve the subsequent page. When paginating, all other
      parameters provided to `FetchInventory` must match the call that
      provided the page token.
    source: Required. The name of the Source.
  """

  forceRefresh = _messages.BooleanField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  source = _messages.StringField(4, required=True)


class VmmigrationProjectsLocationsSourcesFetchStorageInventoryRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesFetchStorageInventoryRequest
  object.

  Enums:
    TypeValueValuesEnum: Required. The type of the storage inventory to fetch.

  Fields:
    forceRefresh: Optional. If this flag is set to true, the source will be
      queried instead of using cached results. Using this flag will make the
      call slower.
    pageSize: Optional. The maximum number of VMs to return. The service may
      return fewer than this value.
    pageToken: Optional. A page token, received from a previous
      `FetchStorageInventory` call. Provide this to retrieve the subsequent
      page. When paginating, all other parameters provided to
      `FetchStorageInventory` must match the call that provided the page
      token.
    source: Required. The name of the Source.
    type: Required. The type of the storage inventory to fetch.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of the storage inventory to fetch.

    Values:
      STORAGE_TYPE_UNSPECIFIED: The type is unspecified.
      DISKS: The type is disks.
      SNAPSHOTS: The type is snapshots.
    """
    STORAGE_TYPE_UNSPECIFIED = 0
    DISKS = 1
    SNAPSHOTS = 2

  forceRefresh = _messages.BooleanField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  source = _messages.StringField(4, required=True)
  type = _messages.EnumField('TypeValueValuesEnum', 5)


class VmmigrationProjectsLocationsSourcesGetRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesGetRequest object.

  Fields:
    name: Required. The Source name.
  """

  name = _messages.StringField(1, required=True)


class VmmigrationProjectsLocationsSourcesListRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesListRequest object.

  Fields:
    filter: Optional. The filter request.
    orderBy: Optional. the order by fields for the result.
    pageSize: Optional. The maximum number of sources to return. The service
      may return fewer than this value. If unspecified, at most 500 sources
      will be returned. The maximum value is 1000; values above 1000 will be
      coerced to 1000.
    pageToken: Required. A page token, received from a previous `ListSources`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListSources` must match the call that
      provided the page token.
    parent: Required. The parent, which owns this collection of sources.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class VmmigrationProjectsLocationsSourcesMigratingVmsCloneJobsCancelRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesMigratingVmsCloneJobsCancelRequest
  object.

  Fields:
    cancelCloneJobRequest: A CancelCloneJobRequest resource to be passed as
      the request body.
    name: Required. The clone job id
  """

  cancelCloneJobRequest = _messages.MessageField('CancelCloneJobRequest', 1)
  name = _messages.StringField(2, required=True)


class VmmigrationProjectsLocationsSourcesMigratingVmsCloneJobsCreateRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesMigratingVmsCloneJobsCreateRequest
  object.

  Fields:
    cloneJob: A CloneJob resource to be passed as the request body.
    cloneJobId: Required. The clone job identifier.
    parent: Required. The Clone's parent.
    requestId: A request ID to identify requests. Specify a unique request ID
      so that if you must retry your request, the server will know to ignore
      the request if it has already been completed. The server will guarantee
      that for at least 60 minutes since the first request. For example,
      consider a situation where you make an initial request and the request
      times out. If you make the request again with the same request ID, the
      server can check if original operation with the same request ID was
      received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  cloneJob = _messages.MessageField('CloneJob', 1)
  cloneJobId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class VmmigrationProjectsLocationsSourcesMigratingVmsCloneJobsGetRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesMigratingVmsCloneJobsGetRequest
  object.

  Fields:
    name: Required. The name of the CloneJob.
  """

  name = _messages.StringField(1, required=True)


class VmmigrationProjectsLocationsSourcesMigratingVmsCloneJobsListRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesMigratingVmsCloneJobsListRequest
  object.

  Fields:
    filter: Optional. The filter request.
    orderBy: Optional. the order by fields for the result.
    pageSize: Optional. The maximum number of clone jobs to return. The
      service may return fewer than this value. If unspecified, at most 500
      clone jobs will be returned. The maximum value is 1000; values above
      1000 will be coerced to 1000.
    pageToken: Required. A page token, received from a previous
      `ListCloneJobs` call. Provide this to retrieve the subsequent page. When
      paginating, all other parameters provided to `ListCloneJobs` must match
      the call that provided the page token.
    parent: Required. The parent, which owns this collection of source VMs.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class VmmigrationProjectsLocationsSourcesMigratingVmsCreateRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesMigratingVmsCreateRequest object.

  Fields:
    migratingVm: A MigratingVm resource to be passed as the request body.
    migratingVmId: Required. The migratingVm identifier.
    parent: Required. The MigratingVm's parent.
    requestId: A request ID to identify requests. Specify a unique request ID
      so that if you must retry your request, the server will know to ignore
      the request if it has already been completed. The server will guarantee
      that for at least 60 minutes since the first request. For example,
      consider a situation where you make an initial request and the request
      times out. If you make the request again with the same request ID, the
      server can check if original operation with the same request ID was
      received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  migratingVm = _messages.MessageField('MigratingVm', 1)
  migratingVmId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class VmmigrationProjectsLocationsSourcesMigratingVmsCutoverJobsCancelRequest(_messages.Message):
  r"""A
  VmmigrationProjectsLocationsSourcesMigratingVmsCutoverJobsCancelRequest
  object.

  Fields:
    cancelCutoverJobRequest: A CancelCutoverJobRequest resource to be passed
      as the request body.
    name: Required. The cutover job id
  """

  cancelCutoverJobRequest = _messages.MessageField('CancelCutoverJobRequest', 1)
  name = _messages.StringField(2, required=True)


class VmmigrationProjectsLocationsSourcesMigratingVmsCutoverJobsCreateRequest(_messages.Message):
  r"""A
  VmmigrationProjectsLocationsSourcesMigratingVmsCutoverJobsCreateRequest
  object.

  Fields:
    cutoverJob: A CutoverJob resource to be passed as the request body.
    cutoverJobId: Required. The cutover job identifier.
    parent: Required. The Cutover's parent.
    requestId: A request ID to identify requests. Specify a unique request ID
      so that if you must retry your request, the server will know to ignore
      the request if it has already been completed. The server will guarantee
      that for at least 60 minutes since the first request. For example,
      consider a situation where you make an initial request and the request
      times out. If you make the request again with the same request ID, the
      server can check if original operation with the same request ID was
      received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  cutoverJob = _messages.MessageField('CutoverJob', 1)
  cutoverJobId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class VmmigrationProjectsLocationsSourcesMigratingVmsCutoverJobsGetRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesMigratingVmsCutoverJobsGetRequest
  object.

  Fields:
    name: Required. The name of the CutoverJob.
  """

  name = _messages.StringField(1, required=True)


class VmmigrationProjectsLocationsSourcesMigratingVmsCutoverJobsListRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesMigratingVmsCutoverJobsListRequest
  object.

  Fields:
    filter: Optional. The filter request.
    orderBy: Optional. the order by fields for the result.
    pageSize: Optional. The maximum number of cutover jobs to return. The
      service may return fewer than this value. If unspecified, at most 500
      cutover jobs will be returned. The maximum value is 1000; values above
      1000 will be coerced to 1000.
    pageToken: Required. A page token, received from a previous
      `ListCutoverJobs` call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters provided to `ListCutoverJobs` must
      match the call that provided the page token.
    parent: Required. The parent, which owns this collection of migrating VMs.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class VmmigrationProjectsLocationsSourcesMigratingVmsDeleteRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesMigratingVmsDeleteRequest object.

  Fields:
    name: Required. The name of the MigratingVm.
  """

  name = _messages.StringField(1, required=True)


class VmmigrationProjectsLocationsSourcesMigratingVmsFinalizeMigrationRequest(_messages.Message):
  r"""A
  VmmigrationProjectsLocationsSourcesMigratingVmsFinalizeMigrationRequest
  object.

  Fields:
    finalizeMigrationRequest: A FinalizeMigrationRequest resource to be passed
      as the request body.
    migratingVm: Required. The name of the MigratingVm.
  """

  finalizeMigrationRequest = _messages.MessageField('FinalizeMigrationRequest', 1)
  migratingVm = _messages.StringField(2, required=True)


class VmmigrationProjectsLocationsSourcesMigratingVmsGetRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesMigratingVmsGetRequest object.

  Enums:
    ViewValueValuesEnum: Optional. The level of details of the migrating VM.

  Fields:
    name: Required. The name of the MigratingVm.
    view: Optional. The level of details of the migrating VM.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional. The level of details of the migrating VM.

    Values:
      MIGRATING_VM_VIEW_UNSPECIFIED: View is unspecified. The API will
        fallback to the default value.
      MIGRATING_VM_VIEW_BASIC: Get the migrating VM basic details. The basic
        details do not include the recent clone jobs and recent cutover jobs
        lists.
      MIGRATING_VM_VIEW_FULL: Include everything.
    """
    MIGRATING_VM_VIEW_UNSPECIFIED = 0
    MIGRATING_VM_VIEW_BASIC = 1
    MIGRATING_VM_VIEW_FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class VmmigrationProjectsLocationsSourcesMigratingVmsListRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesMigratingVmsListRequest object.

  Enums:
    ViewValueValuesEnum: Optional. The level of details of each migrating VM.

  Fields:
    filter: Optional. The filter request.
    orderBy: Optional. the order by fields for the result.
    pageSize: Optional. The maximum number of migrating VMs to return. The
      service may return fewer than this value. If unspecified, at most 500
      migrating VMs will be returned. The maximum value is 1000; values above
      1000 will be coerced to 1000.
    pageToken: Required. A page token, received from a previous
      `ListMigratingVms` call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters provided to `ListMigratingVms`
      must match the call that provided the page token.
    parent: Required. The parent, which owns this collection of MigratingVms.
    view: Optional. The level of details of each migrating VM.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional. The level of details of each migrating VM.

    Values:
      MIGRATING_VM_VIEW_UNSPECIFIED: View is unspecified. The API will
        fallback to the default value.
      MIGRATING_VM_VIEW_BASIC: Get the migrating VM basic details. The basic
        details do not include the recent clone jobs and recent cutover jobs
        lists.
      MIGRATING_VM_VIEW_FULL: Include everything.
    """
    MIGRATING_VM_VIEW_UNSPECIFIED = 0
    MIGRATING_VM_VIEW_BASIC = 1
    MIGRATING_VM_VIEW_FULL = 2

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 6)


class VmmigrationProjectsLocationsSourcesMigratingVmsPatchRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesMigratingVmsPatchRequest object.

  Fields:
    migratingVm: A MigratingVm resource to be passed as the request body.
    name: Output only. The identifier of the MigratingVm.
    requestId: A request ID to identify requests. Specify a unique request ID
      so that if you must retry your request, the server will know to ignore
      the request if it has already been completed. The server will guarantee
      that for at least 60 minutes since the first request. For example,
      consider a situation where you make an initial request and the request
      times out. If you make the request again with the same request ID, the
      server can check if original operation with the same request ID was
      received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Field mask is used to specify the fields to be overwritten in
      the MigratingVm resource by the update. The fields specified in the
      update_mask are relative to the resource, not the full request. A field
      will be overwritten if it is in the mask. If the user does not provide a
      mask then all fields will be overwritten.
  """

  migratingVm = _messages.MessageField('MigratingVm', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class VmmigrationProjectsLocationsSourcesMigratingVmsPauseMigrationRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesMigratingVmsPauseMigrationRequest
  object.

  Fields:
    migratingVm: Required. The name of the MigratingVm.
    pauseMigrationRequest: A PauseMigrationRequest resource to be passed as
      the request body.
  """

  migratingVm = _messages.StringField(1, required=True)
  pauseMigrationRequest = _messages.MessageField('PauseMigrationRequest', 2)


class VmmigrationProjectsLocationsSourcesMigratingVmsReplicationCyclesGetRequest(_messages.Message):
  r"""A
  VmmigrationProjectsLocationsSourcesMigratingVmsReplicationCyclesGetRequest
  object.

  Fields:
    name: Required. The name of the ReplicationCycle.
  """

  name = _messages.StringField(1, required=True)


class VmmigrationProjectsLocationsSourcesMigratingVmsReplicationCyclesListRequest(_messages.Message):
  r"""A
  VmmigrationProjectsLocationsSourcesMigratingVmsReplicationCyclesListRequest
  object.

  Fields:
    filter: Optional. The filter request.
    orderBy: Optional. the order by fields for the result.
    pageSize: Optional. The maximum number of replication cycles to return.
      The service may return fewer than this value. If unspecified, at most
      100 migrating VMs will be returned. The maximum value is 100; values
      above 100 will be coerced to 100.
    pageToken: Required. A page token, received from a previous
      `ListReplicationCycles` call. Provide this to retrieve the subsequent
      page. When paginating, all other parameters provided to
      `ListReplicationCycles` must match the call that provided the page
      token.
    parent: Required. The parent, which owns this collection of
      ReplicationCycles.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class VmmigrationProjectsLocationsSourcesMigratingVmsResumeMigrationRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesMigratingVmsResumeMigrationRequest
  object.

  Fields:
    migratingVm: Required. The name of the MigratingVm.
    resumeMigrationRequest: A ResumeMigrationRequest resource to be passed as
      the request body.
  """

  migratingVm = _messages.StringField(1, required=True)
  resumeMigrationRequest = _messages.MessageField('ResumeMigrationRequest', 2)


class VmmigrationProjectsLocationsSourcesMigratingVmsStartMigrationRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesMigratingVmsStartMigrationRequest
  object.

  Fields:
    migratingVm: Required. The name of the MigratingVm.
    startMigrationRequest: A StartMigrationRequest resource to be passed as
      the request body.
  """

  migratingVm = _messages.StringField(1, required=True)
  startMigrationRequest = _messages.MessageField('StartMigrationRequest', 2)


class VmmigrationProjectsLocationsSourcesPatchRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesPatchRequest object.

  Fields:
    name: Output only. The Source name.
    requestId: A request ID to identify requests. Specify a unique request ID
      so that if you must retry your request, the server will know to ignore
      the request if it has already been completed. The server will guarantee
      that for at least 60 minutes since the first request. For example,
      consider a situation where you make an initial request and the request
      times out. If you make the request again with the same request ID, the
      server can check if original operation with the same request ID was
      received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    source: A Source resource to be passed as the request body.
    updateMask: Field mask is used to specify the fields to be overwritten in
      the Source resource by the update. The fields specified in the
      update_mask are relative to the resource, not the full request. A field
      will be overwritten if it is in the mask. If the user does not provide a
      mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  source = _messages.MessageField('Source', 3)
  updateMask = _messages.StringField(4)


class VmmigrationProjectsLocationsSourcesUtilizationReportsCreateRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesUtilizationReportsCreateRequest
  object.

  Fields:
    parent: Required. The Utilization Report's parent.
    requestId: A request ID to identify requests. Specify a unique request ID
      so that if you must retry your request, the server will know to ignore
      the request if it has already been completed. The server will guarantee
      that for at least 60 minutes since the first request. For example,
      consider a situation where you make an initial request and the request
      times out. If you make the request again with the same request ID, the
      server can check if original operation with the same request ID was
      received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    utilizationReport: A UtilizationReport resource to be passed as the
      request body.
    utilizationReportId: Required. The ID to use for the report, which will
      become the final component of the reports's resource name. This value
      maximum length is 63 characters, and valid characters are /a-z-/. It
      must start with an english letter and must not end with a hyphen.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  utilizationReport = _messages.MessageField('UtilizationReport', 3)
  utilizationReportId = _messages.StringField(4)


class VmmigrationProjectsLocationsSourcesUtilizationReportsDeleteRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesUtilizationReportsDeleteRequest
  object.

  Fields:
    name: Required. The Utilization Report name.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class VmmigrationProjectsLocationsSourcesUtilizationReportsGetRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesUtilizationReportsGetRequest
  object.

  Enums:
    ViewValueValuesEnum: Optional. The level of details of the report.
      Defaults to FULL

  Fields:
    name: Required. The Utilization Report name.
    view: Optional. The level of details of the report. Defaults to FULL
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional. The level of details of the report. Defaults to FULL

    Values:
      UTILIZATION_REPORT_VIEW_UNSPECIFIED: The default / unset value. The API
        will default to FULL on single report request and BASIC for multiple
        reports request.
      BASIC: Get the report metadata, without the list of VMs and their
        utilization info.
      FULL: Include everything.
    """
    UTILIZATION_REPORT_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class VmmigrationProjectsLocationsSourcesUtilizationReportsListRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsSourcesUtilizationReportsListRequest
  object.

  Enums:
    ViewValueValuesEnum: Optional. The level of details of each report.
      Defaults to BASIC.

  Fields:
    filter: Optional. The filter request.
    orderBy: Optional. the order by fields for the result.
    pageSize: Optional. The maximum number of reports to return. The service
      may return fewer than this value. If unspecified, at most 500 reports
      will be returned. The maximum value is 1000; values above 1000 will be
      coerced to 1000.
    pageToken: Required. A page token, received from a previous
      `ListUtilizationReports` call. Provide this to retrieve the subsequent
      page. When paginating, all other parameters provided to
      `ListUtilizationReports` must match the call that provided the page
      token.
    parent: Required. The Utilization Reports parent.
    view: Optional. The level of details of each report. Defaults to BASIC.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional. The level of details of each report. Defaults to BASIC.

    Values:
      UTILIZATION_REPORT_VIEW_UNSPECIFIED: The default / unset value. The API
        will default to FULL on single report request and BASIC for multiple
        reports request.
      BASIC: Get the report metadata, without the list of VMs and their
        utilization info.
      FULL: Include everything.
    """
    UTILIZATION_REPORT_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 6)


class VmmigrationProjectsLocationsTargetProjectsCreateRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsTargetProjectsCreateRequest object.

  Fields:
    parent: Required. The TargetProject's parent.
    requestId: A request ID to identify requests. Specify a unique request ID
      so that if you must retry your request, the server will know to ignore
      the request if it has already been completed. The server will guarantee
      that for at least 60 minutes since the first request. For example,
      consider a situation where you make an initial request and the request
      times out. If you make the request again with the same request ID, the
      server can check if original operation with the same request ID was
      received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    targetProject: A TargetProject resource to be passed as the request body.
    targetProjectId: Required. The target_project identifier.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  targetProject = _messages.MessageField('TargetProject', 3)
  targetProjectId = _messages.StringField(4)


class VmmigrationProjectsLocationsTargetProjectsDeleteRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsTargetProjectsDeleteRequest object.

  Fields:
    name: Required. The TargetProject name.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class VmmigrationProjectsLocationsTargetProjectsGetRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsTargetProjectsGetRequest object.

  Fields:
    name: Required. The TargetProject name.
  """

  name = _messages.StringField(1, required=True)


class VmmigrationProjectsLocationsTargetProjectsListRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsTargetProjectsListRequest object.

  Fields:
    filter: Optional. The filter request.
    orderBy: Optional. the order by fields for the result.
    pageSize: Optional. The maximum number of targets to return. The service
      may return fewer than this value. If unspecified, at most 500 targets
      will be returned. The maximum value is 1000; values above 1000 will be
      coerced to 1000.
    pageToken: Required. A page token, received from a previous `ListTargets`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListTargets` must match the call that
      provided the page token.
    parent: Required. The parent, which owns this collection of targets.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class VmmigrationProjectsLocationsTargetProjectsPatchRequest(_messages.Message):
  r"""A VmmigrationProjectsLocationsTargetProjectsPatchRequest object.

  Fields:
    name: Output only. The name of the target project.
    requestId: A request ID to identify requests. Specify a unique request ID
      so that if you must retry your request, the server will know to ignore
      the request if it has already been completed. The server will guarantee
      that for at least 60 minutes since the first request. For example,
      consider a situation where you make an initial request and the request
      times out. If you make the request again with the same request ID, the
      server can check if original operation with the same request ID was
      received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    targetProject: A TargetProject resource to be passed as the request body.
    updateMask: Field mask is used to specify the fields to be overwritten in
      the TargetProject resource by the update. The fields specified in the
      update_mask are relative to the resource, not the full request. A field
      will be overwritten if it is in the mask. If the user does not provide a
      mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  targetProject = _messages.MessageField('TargetProject', 3)
  updateMask = _messages.StringField(4)


class VmwareDiskDetails(_messages.Message):
  r"""The details of a Vmware VM disk.

  Fields:
    diskNumber: Output only. The ordinal number of the disk.
    label: Output only. The disk label.
    sizeGb: Output only. Size in GB.
  """

  diskNumber = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  label = _messages.StringField(2)
  sizeGb = _messages.IntegerField(3)


class VmwareSourceDetails(_messages.Message):
  r"""VmwareSourceDetails message describes a specific source details for the
  vmware source type.

  Fields:
    password: Input only. The credentials password. This is write only and can
      not be read in a GET operation.
    resolvedVcenterHost: The hostname of the vcenter.
    thumbprint: The thumbprint representing the certificate for the vcenter.
    username: The credentials username.
    vcenterIp: The ip address of the vcenter this Source represents.
  """

  password = _messages.StringField(1)
  resolvedVcenterHost = _messages.StringField(2)
  thumbprint = _messages.StringField(3)
  username = _messages.StringField(4)
  vcenterIp = _messages.StringField(5)


class VmwareSourceVmDetails(_messages.Message):
  r"""Represent the source Vmware VM details.

  Enums:
    ArchitectureValueValuesEnum: Output only. The VM architecture.
    FirmwareValueValuesEnum: Output only. The firmware type of the source VM.

  Fields:
    architecture: Output only. The VM architecture.
    committedStorageBytes: Output only. The total size of the disks being
      migrated in bytes.
    disks: Output only. The disks attached to the source VM.
    firmware: Output only. The firmware type of the source VM.
    vmCapabilitiesInfo: Output only. Information about VM capabilities needed
      for some Compute Engine features.
  """

  class ArchitectureValueValuesEnum(_messages.Enum):
    r"""Output only. The VM architecture.

    Values:
      VM_ARCHITECTURE_UNSPECIFIED: The architecture is unknown.
      VM_ARCHITECTURE_X86_FAMILY: The architecture is one of the x86
        architectures.
      VM_ARCHITECTURE_ARM64: The architecture is ARM64.
    """
    VM_ARCHITECTURE_UNSPECIFIED = 0
    VM_ARCHITECTURE_X86_FAMILY = 1
    VM_ARCHITECTURE_ARM64 = 2

  class FirmwareValueValuesEnum(_messages.Enum):
    r"""Output only. The firmware type of the source VM.

    Values:
      FIRMWARE_UNSPECIFIED: The firmware is unknown.
      EFI: The firmware is EFI.
      BIOS: The firmware is BIOS.
    """
    FIRMWARE_UNSPECIFIED = 0
    EFI = 1
    BIOS = 2

  architecture = _messages.EnumField('ArchitectureValueValuesEnum', 1)
  committedStorageBytes = _messages.IntegerField(2)
  disks = _messages.MessageField('VmwareDiskDetails', 3, repeated=True)
  firmware = _messages.EnumField('FirmwareValueValuesEnum', 4)
  vmCapabilitiesInfo = _messages.MessageField('VmCapabilities', 5)


class VmwareVmDetails(_messages.Message):
  r"""VmwareVmDetails describes a VM in vCenter.

  Enums:
    ArchitectureValueValuesEnum: Output only. The CPU architecture.
    BootOptionValueValuesEnum: Output only. The VM Boot Option.
    PowerStateValueValuesEnum: The power state of the VM at the moment list
      was taken.

  Fields:
    architecture: Output only. The CPU architecture.
    bootOption: Output only. The VM Boot Option.
    committedStorage: The total size of the storage allocated to the VM in MB.
    committedStorageMb: The total size of the storage allocated to the VM in
      MB.
    cpuCount: The number of cpus in the VM.
    datacenterDescription: The descriptive name of the vCenter's datacenter
      this VM is contained in.
    datacenterId: The id of the vCenter's datacenter this VM is contained in.
    diskCount: The number of disks the VM has.
    displayName: The display name of the VM. Note that this is not necessarily
      unique.
    guestDescription: The VM's OS. See for example https://vdc-
      repo.vmware.com/vmwb-repository/dcr-public/da47f910-60ac-438b-8b9b-
      6122f4d14524/16b7274a-bf8b-4b4c-a05e-
      746f2aa93c8c/doc/vim.vm.GuestOsDescriptor.GuestOsIdentifier.html for
      types of strings this might hold.
    memoryMb: The size of the memory of the VM in MB.
    powerState: The power state of the VM at the moment list was taken.
    uuid: The unique identifier of the VM in vCenter.
    vmId: The VM's id in the source (note that this is not the MigratingVm's
      id). This is the moref id of the VM.
  """

  class ArchitectureValueValuesEnum(_messages.Enum):
    r"""Output only. The CPU architecture.

    Values:
      VM_ARCHITECTURE_UNSPECIFIED: The architecture is unknown.
      VM_ARCHITECTURE_X86_FAMILY: The architecture is one of the x86
        architectures.
      VM_ARCHITECTURE_ARM64: The architecture is ARM64.
    """
    VM_ARCHITECTURE_UNSPECIFIED = 0
    VM_ARCHITECTURE_X86_FAMILY = 1
    VM_ARCHITECTURE_ARM64 = 2

  class BootOptionValueValuesEnum(_messages.Enum):
    r"""Output only. The VM Boot Option.

    Values:
      BOOT_OPTION_UNSPECIFIED: The boot option is unknown.
      EFI: The boot option is EFI.
      BIOS: The boot option is BIOS.
    """
    BOOT_OPTION_UNSPECIFIED = 0
    EFI = 1
    BIOS = 2

  class PowerStateValueValuesEnum(_messages.Enum):
    r"""The power state of the VM at the moment list was taken.

    Values:
      POWER_STATE_UNSPECIFIED: Power state is not specified.
      ON: The VM is turned ON.
      OFF: The VM is turned OFF.
      SUSPENDED: The VM is suspended. This is similar to hibernation or sleep
        mode.
    """
    POWER_STATE_UNSPECIFIED = 0
    ON = 1
    OFF = 2
    SUSPENDED = 3

  architecture = _messages.EnumField('ArchitectureValueValuesEnum', 1)
  bootOption = _messages.EnumField('BootOptionValueValuesEnum', 2)
  committedStorage = _messages.IntegerField(3)
  committedStorageMb = _messages.IntegerField(4)
  cpuCount = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  datacenterDescription = _messages.StringField(6)
  datacenterId = _messages.StringField(7)
  diskCount = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  displayName = _messages.StringField(9)
  guestDescription = _messages.StringField(10)
  memoryMb = _messages.IntegerField(11, variant=_messages.Variant.INT32)
  powerState = _messages.EnumField('PowerStateValueValuesEnum', 12)
  uuid = _messages.StringField(13)
  vmId = _messages.StringField(14)


class VmwareVmsDetails(_messages.Message):
  r"""VmwareVmsDetails describes VMs in vCenter.

  Fields:
    details: The details of the vmware VMs.
  """

  details = _messages.MessageField('VmwareVmDetails', 1, repeated=True)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
