"""Generated message classes for file version v1.

The Cloud Filestore API is used for creating and managing cloud file servers.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'file'


class Backup(_messages.Message):
  r"""A Filestore backup.

  Enums:
    FileSystemProtocolValueValuesEnum: Output only. The file system protocol
      of the source Filestore instance that this backup is created from.
    SourceInstanceTierValueValuesEnum: Output only. The service tier of the
      source Filestore instance that this backup is created from.
    StateValueValuesEnum: Output only. The backup state.

  Messages:
    LabelsValue: Resource labels to represent user provided metadata.
    TagsValue: Optional. Input only. Immutable. Tag key-value pairs bound to
      this resource. Each key must be a namespaced name and each value a short
      name. Example: "123456789012/environment" : "production",
      "123456789013/costCenter" : "marketing" See the documentation for more
      information: - Namespaced name: https://cloud.google.com/resource-
      manager/docs/tags/tags-creating-and-managing#retrieving_tag_key - Short
      name: https://cloud.google.com/resource-manager/docs/tags/tags-creating-
      and-managing#retrieving_tag_value

  Fields:
    capacityGb: Output only. Capacity of the source file share when the backup
      was created.
    createTime: Output only. The time when the backup was created.
    description: A description of the backup with 2048 characters or less.
      Requests with longer descriptions will be rejected.
    downloadBytes: Output only. Amount of bytes that will be downloaded if the
      backup is restored. This may be different than storage bytes, since
      sequential backups of the same disk will share storage.
    fileSystemProtocol: Output only. The file system protocol of the source
      Filestore instance that this backup is created from.
    kmsKey: Immutable. KMS key name used for data encryption.
    labels: Resource labels to represent user provided metadata.
    name: Output only. The resource name of the backup, in the format
      `projects/{project_number}/locations/{location_id}/backups/{backup_id}`.
    satisfiesPzi: Output only. Reserved for future use.
    satisfiesPzs: Output only. Reserved for future use.
    sourceFileShare: Name of the file share in the source Filestore instance
      that the backup is created from.
    sourceInstance: The resource name of the source Filestore instance, in the
      format `projects/{project_number}/locations/{location_id}/instances/{ins
      tance_id}`, used to create this backup.
    sourceInstanceTier: Output only. The service tier of the source Filestore
      instance that this backup is created from.
    state: Output only. The backup state.
    storageBytes: Output only. The size of the storage used by the backup. As
      backups share storage, this number is expected to change with backup
      creation/deletion.
    tags: Optional. Input only. Immutable. Tag key-value pairs bound to this
      resource. Each key must be a namespaced name and each value a short
      name. Example: "123456789012/environment" : "production",
      "123456789013/costCenter" : "marketing" See the documentation for more
      information: - Namespaced name: https://cloud.google.com/resource-
      manager/docs/tags/tags-creating-and-managing#retrieving_tag_key - Short
      name: https://cloud.google.com/resource-manager/docs/tags/tags-creating-
      and-managing#retrieving_tag_value
  """

  class FileSystemProtocolValueValuesEnum(_messages.Enum):
    r"""Output only. The file system protocol of the source Filestore instance
    that this backup is created from.

    Values:
      FILE_PROTOCOL_UNSPECIFIED: FILE_PROTOCOL_UNSPECIFIED serves a "not set"
        default value when a FileProtocol is a separate field in a message.
      NFS_V3: NFS 3.0.
      NFS_V4_1: NFS 4.1.
    """
    FILE_PROTOCOL_UNSPECIFIED = 0
    NFS_V3 = 1
    NFS_V4_1 = 2

  class SourceInstanceTierValueValuesEnum(_messages.Enum):
    r"""Output only. The service tier of the source Filestore instance that
    this backup is created from.

    Values:
      TIER_UNSPECIFIED: Not set.
      STANDARD: STANDARD tier. BASIC_HDD is the preferred term for this tier.
      PREMIUM: PREMIUM tier. BASIC_SSD is the preferred term for this tier.
      BASIC_HDD: BASIC instances offer a maximum capacity of 63.9 TB.
        BASIC_HDD is an alias for STANDARD Tier, offering economical
        performance backed by HDD.
      BASIC_SSD: BASIC instances offer a maximum capacity of 63.9 TB.
        BASIC_SSD is an alias for PREMIUM Tier, and offers improved
        performance backed by SSD.
      HIGH_SCALE_SSD: HIGH_SCALE instances offer expanded capacity and
        performance scaling capabilities.
      ENTERPRISE: ENTERPRISE instances offer the features and availability
        needed for mission-critical workloads.
      ZONAL: ZONAL instances offer expanded capacity and performance scaling
        capabilities.
      REGIONAL: REGIONAL instances offer the features and availability needed
        for mission-critical workloads.
    """
    TIER_UNSPECIFIED = 0
    STANDARD = 1
    PREMIUM = 2
    BASIC_HDD = 3
    BASIC_SSD = 4
    HIGH_SCALE_SSD = 5
    ENTERPRISE = 6
    ZONAL = 7
    REGIONAL = 8

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The backup state.

    Values:
      STATE_UNSPECIFIED: State not set.
      CREATING: Backup is being created.
      FINALIZING: Backup has been taken and the operation is being finalized.
        At this point, changes to the file share will not be reflected in the
        backup.
      READY: Backup is available for use.
      DELETING: Backup is being deleted.
      INVALID: Backup is not valid and cannot be used for creating new
        instances or restoring existing instances.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    FINALIZING = 2
    READY = 3
    DELETING = 4
    INVALID = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Resource labels to represent user provided metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""Optional. Input only. Immutable. Tag key-value pairs bound to this
    resource. Each key must be a namespaced name and each value a short name.
    Example: "123456789012/environment" : "production",
    "123456789013/costCenter" : "marketing" See the documentation for more
    information: - Namespaced name: https://cloud.google.com/resource-
    manager/docs/tags/tags-creating-and-managing#retrieving_tag_key - Short
    name: https://cloud.google.com/resource-manager/docs/tags/tags-creating-
    and-managing#retrieving_tag_value

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  capacityGb = _messages.IntegerField(1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  downloadBytes = _messages.IntegerField(4)
  fileSystemProtocol = _messages.EnumField('FileSystemProtocolValueValuesEnum', 5)
  kmsKey = _messages.StringField(6)
  labels = _messages.MessageField('LabelsValue', 7)
  name = _messages.StringField(8)
  satisfiesPzi = _messages.BooleanField(9)
  satisfiesPzs = _messages.BooleanField(10)
  sourceFileShare = _messages.StringField(11)
  sourceInstance = _messages.StringField(12)
  sourceInstanceTier = _messages.EnumField('SourceInstanceTierValueValuesEnum', 13)
  state = _messages.EnumField('StateValueValuesEnum', 14)
  storageBytes = _messages.IntegerField(15)
  tags = _messages.MessageField('TagsValue', 16)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class DailyCycle(_messages.Message):
  r"""Time window specified for daily operations.

  Fields:
    duration: Output only. Duration of the time window, set by service
      producer.
    startTime: Time within the day to start the operations.
  """

  duration = _messages.StringField(1)
  startTime = _messages.MessageField('TimeOfDay', 2)


class Date(_messages.Message):
  r"""Represents a whole or partial calendar date, such as a birthday. The
  time of day and time zone are either specified elsewhere or are
  insignificant. The date is relative to the Gregorian Calendar. This can
  represent one of the following: * A full date, with non-zero year, month,
  and day values. * A month and day, with a zero year (for example, an
  anniversary). * A year on its own, with a zero month and a zero day. * A
  year and month, with a zero day (for example, a credit card expiration
  date). Related types: * google.type.TimeOfDay * google.type.DateTime *
  google.protobuf.Timestamp

  Fields:
    day: Day of a month. Must be from 1 to 31 and valid for the year and
      month, or 0 to specify a year by itself or a year and month where the
      day isn't significant.
    month: Month of a year. Must be from 1 to 12, or 0 to specify a year
      without a month and day.
    year: Year of the date. Must be from 1 to 9999, or 0 to specify a date
      without a year.
  """

  day = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  month = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  year = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class DenyMaintenancePeriod(_messages.Message):
  r"""DenyMaintenancePeriod definition. Maintenance is forbidden within the
  deny period. The start_date must be less than the end_date.

  Fields:
    endDate: Deny period end date. This can be: * A full date, with non-zero
      year, month and day values. * A month and day value, with a zero year.
      Allows recurring deny periods each year. Date matching this period will
      have to be before the end.
    startDate: Deny period start date. This can be: * A full date, with non-
      zero year, month and day values. * A month and day value, with a zero
      year. Allows recurring deny periods each year. Date matching this period
      will have to be the same or after the start.
    time: Time in UTC when the Blackout period starts on start_date and ends
      on end_date. This can be: * Full time. * All zeros for 00:00:00 UTC
  """

  endDate = _messages.MessageField('Date', 1)
  startDate = _messages.MessageField('Date', 2)
  time = _messages.MessageField('TimeOfDay', 3)


class DirectoryServicesConfig(_messages.Message):
  r"""Directory Services configuration for Kerberos-based authentication.

  Fields:
    ldap: Configuration for LDAP servers.
    managedActiveDirectory: Configuration for Managed Service for Microsoft
      Active Directory.
  """

  ldap = _messages.MessageField('LdapConfig', 1)
  managedActiveDirectory = _messages.MessageField('ManagedActiveDirectoryConfig', 2)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class FileProjectsLocationsBackupsCreateRequest(_messages.Message):
  r"""A FileProjectsLocationsBackupsCreateRequest object.

  Fields:
    backup: A Backup resource to be passed as the request body.
    backupId: Required. The ID to use for the backup. The ID must be unique
      within the specified project and location. This value must start with a
      lowercase letter followed by up to 62 lowercase letters, numbers, or
      hyphens, and cannot end with a hyphen. Values that do not match this
      pattern will trigger an INVALID_ARGUMENT error.
    parent: Required. The backup's project and location, in the format
      `projects/{project_number}/locations/{location}`. In Filestore, backup
      locations map to Google Cloud regions, for example **us-west1**.
  """

  backup = _messages.MessageField('Backup', 1)
  backupId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class FileProjectsLocationsBackupsDeleteRequest(_messages.Message):
  r"""A FileProjectsLocationsBackupsDeleteRequest object.

  Fields:
    name: Required. The backup resource name, in the format
      `projects/{project_number}/locations/{location}/backups/{backup_id}`
  """

  name = _messages.StringField(1, required=True)


class FileProjectsLocationsBackupsGetRequest(_messages.Message):
  r"""A FileProjectsLocationsBackupsGetRequest object.

  Fields:
    name: Required. The backup resource name, in the format
      `projects/{project_number}/locations/{location}/backups/{backup_id}`.
  """

  name = _messages.StringField(1, required=True)


class FileProjectsLocationsBackupsListRequest(_messages.Message):
  r"""A FileProjectsLocationsBackupsListRequest object.

  Fields:
    filter: List filter.
    orderBy: Sort results. Supported values are "name", "name desc" or ""
      (unsorted).
    pageSize: The maximum number of items to return.
    pageToken: The next_page_token value to use if there are additional
      results to retrieve for this list request.
    parent: Required. The project and location for which to retrieve backup
      information, in the format
      `projects/{project_number}/locations/{location}`. In Filestore, backup
      locations map to Google Cloud regions, for example **us-west1**. To
      retrieve backup information for all locations, use "-" for the
      `{location}` value.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class FileProjectsLocationsBackupsPatchRequest(_messages.Message):
  r"""A FileProjectsLocationsBackupsPatchRequest object.

  Fields:
    backup: A Backup resource to be passed as the request body.
    name: Output only. The resource name of the backup, in the format
      `projects/{project_number}/locations/{location_id}/backups/{backup_id}`.
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field.
  """

  backup = _messages.MessageField('Backup', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class FileProjectsLocationsGetRequest(_messages.Message):
  r"""A FileProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class FileProjectsLocationsInstancesCreateRequest(_messages.Message):
  r"""A FileProjectsLocationsInstancesCreateRequest object.

  Fields:
    instance: A Instance resource to be passed as the request body.
    instanceId: Required. The name of the instance to create. The name must be
      unique for the specified project and location.
    parent: Required. The instance's project and location, in the format
      `projects/{project_id}/locations/{location}`. In Filestore, locations
      map to Google Cloud zones, for example **us-west1-b**.
  """

  instance = _messages.MessageField('Instance', 1)
  instanceId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class FileProjectsLocationsInstancesDeleteRequest(_messages.Message):
  r"""A FileProjectsLocationsInstancesDeleteRequest object.

  Fields:
    force: If set to true, all snapshots of the instance will also be deleted.
      (Otherwise, the request will only work if the instance has no
      snapshots.)
    name: Required. The instance resource name, in the format
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class FileProjectsLocationsInstancesGetRequest(_messages.Message):
  r"""A FileProjectsLocationsInstancesGetRequest object.

  Fields:
    name: Required. The instance resource name, in the format
      `projects/{project_id}/locations/{location}/instances/{instance_id}`.
  """

  name = _messages.StringField(1, required=True)


class FileProjectsLocationsInstancesListRequest(_messages.Message):
  r"""A FileProjectsLocationsInstancesListRequest object.

  Fields:
    filter: List filter.
    orderBy: Sort results. Supported values are "name", "name desc" or ""
      (unsorted).
    pageSize: The maximum number of items to return.
    pageToken: The next_page_token value to use if there are additional
      results to retrieve for this list request.
    parent: Required. The project and location for which to retrieve instance
      information, in the format `projects/{project_id}/locations/{location}`.
      In Cloud Filestore, locations map to Google Cloud zones, for example
      **us-west1-b**. To retrieve instance information for all locations, use
      "-" for the `{location}` value.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class FileProjectsLocationsInstancesPatchRequest(_messages.Message):
  r"""A FileProjectsLocationsInstancesPatchRequest object.

  Fields:
    instance: A Instance resource to be passed as the request body.
    name: Output only. The resource name of the instance, in the format
      `projects/{project}/locations/{location}/instances/{instance}`.
    updateMask: Mask of fields to update. At least one path must be supplied
      in this field. The elements of the repeated paths field may only include
      these fields: * "description" * "file_shares" * "labels" *
      "performance_config" * "deletion_protection_enabled" *
      "deletion_protection_reason"
  """

  instance = _messages.MessageField('Instance', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class FileProjectsLocationsInstancesPromoteReplicaRequest(_messages.Message):
  r"""A FileProjectsLocationsInstancesPromoteReplicaRequest object.

  Fields:
    name: Required. The resource name of the instance, in the format
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`.
    promoteReplicaRequest: A PromoteReplicaRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  promoteReplicaRequest = _messages.MessageField('PromoteReplicaRequest', 2)


class FileProjectsLocationsInstancesRestoreRequest(_messages.Message):
  r"""A FileProjectsLocationsInstancesRestoreRequest object.

  Fields:
    name: Required. The resource name of the instance, in the format `projects
      /{project_number}/locations/{location_id}/instances/{instance_id}`.
    restoreInstanceRequest: A RestoreInstanceRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  restoreInstanceRequest = _messages.MessageField('RestoreInstanceRequest', 2)


class FileProjectsLocationsInstancesRevertRequest(_messages.Message):
  r"""A FileProjectsLocationsInstancesRevertRequest object.

  Fields:
    name: Required. The resource name of the instance, in the format
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`.
    revertInstanceRequest: A RevertInstanceRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  revertInstanceRequest = _messages.MessageField('RevertInstanceRequest', 2)


class FileProjectsLocationsInstancesSnapshotsCreateRequest(_messages.Message):
  r"""A FileProjectsLocationsInstancesSnapshotsCreateRequest object.

  Fields:
    parent: Required. The Filestore Instance to create the snapshots of, in
      the format
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    snapshot: A Snapshot resource to be passed as the request body.
    snapshotId: Required. The ID to use for the snapshot. The ID must be
      unique within the specified instance. This value must start with a
      lowercase letter followed by up to 62 lowercase letters, numbers, or
      hyphens, and cannot end with a hyphen.
  """

  parent = _messages.StringField(1, required=True)
  snapshot = _messages.MessageField('Snapshot', 2)
  snapshotId = _messages.StringField(3)


class FileProjectsLocationsInstancesSnapshotsDeleteRequest(_messages.Message):
  r"""A FileProjectsLocationsInstancesSnapshotsDeleteRequest object.

  Fields:
    name: Required. The snapshot resource name, in the format `projects/{proje
      ct_id}/locations/{location}/instances/{instance_id}/snapshots/{snapshot_
      id}`
  """

  name = _messages.StringField(1, required=True)


class FileProjectsLocationsInstancesSnapshotsGetRequest(_messages.Message):
  r"""A FileProjectsLocationsInstancesSnapshotsGetRequest object.

  Fields:
    name: Required. The snapshot resource name, in the format `projects/{proje
      ct_id}/locations/{location}/instances/{instance_id}/snapshots/{snapshot_
      id}`
  """

  name = _messages.StringField(1, required=True)


class FileProjectsLocationsInstancesSnapshotsListRequest(_messages.Message):
  r"""A FileProjectsLocationsInstancesSnapshotsListRequest object.

  Fields:
    filter: List filter.
    orderBy: Sort results. Supported values are "name", "name desc" or ""
      (unsorted).
    pageSize: The maximum number of items to return.
    pageToken: The next_page_token value to use if there are additional
      results to retrieve for this list request.
    parent: Required. The instance for which to retrieve snapshot information,
      in the format
      `projects/{project_id}/locations/{location}/instances/{instance_id}`.
    returnPartialSuccess: Optional. If true, allow partial responses for
      multi-regional Aggregated List requests.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)
  returnPartialSuccess = _messages.BooleanField(6)


class FileProjectsLocationsInstancesSnapshotsPatchRequest(_messages.Message):
  r"""A FileProjectsLocationsInstancesSnapshotsPatchRequest object.

  Fields:
    name: Output only. The resource name of the snapshot, in the format `proje
      cts/{project_id}/locations/{location_id}/instances/{instance_id}/snapsho
      ts/{snapshot_id}`.
    snapshot: A Snapshot resource to be passed as the request body.
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field.
  """

  name = _messages.StringField(1, required=True)
  snapshot = _messages.MessageField('Snapshot', 2)
  updateMask = _messages.StringField(3)


class FileProjectsLocationsListRequest(_messages.Message):
  r"""A FileProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class FileProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A FileProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class FileProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A FileProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class FileProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A FileProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class FileProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A FileProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class FileShareConfig(_messages.Message):
  r"""File share configuration for the instance.

  Fields:
    capacityGb: File share capacity in gigabytes (GB). Filestore defines 1 GB
      as 1024^3 bytes.
    name: Required. The name of the file share. Must use 1-16 characters for
      the basic service tier and 1-63 characters for all other service tiers.
      Must use lowercase letters, numbers, or underscores `[a-z0-9_]`. Must
      start with a letter. Immutable.
    nfsExportOptions: Nfs Export Options. There is a limit of 10 export
      options per file share.
    sourceBackup: The resource name of the backup, in the format
      `projects/{project_number}/locations/{location_id}/backups/{backup_id}`,
      that this file share has been restored from.
  """

  capacityGb = _messages.IntegerField(1)
  name = _messages.StringField(2)
  nfsExportOptions = _messages.MessageField('NfsExportOptions', 3, repeated=True)
  sourceBackup = _messages.StringField(4)


class FixedIOPS(_messages.Message):
  r"""Fixed IOPS (input/output operations per second) parameters.

  Fields:
    maxIops: Required. Maximum IOPS.
  """

  maxIops = _messages.IntegerField(1)


class GoogleCloudSaasacceleratorManagementProvidersV1Instance(_messages.Message):
  r"""Instance represents the interface for SLM services to actuate the state
  of control plane resources. Example Instance in JSON, where consumer-
  project-number=123456, producer-project-id=cloud-sql: ```json Instance: {
  "name": "projects/123456/locations/us-east1/instances/prod-instance",
  "create_time": { "seconds": **********, }, "labels": { "env": "prod", "foo":
  "bar" }, "state": READY, "software_versions": { "software_update": "cloud-
  sql-09-28-2018", }, "maintenance_policy_names": { "UpdatePolicy":
  "projects/123456/locations/us-east1/maintenancePolicies/prod-update-policy",
  } "tenant_project_id": "cloud-sql-test-tenant", "producer_metadata": {
  "cloud-sql-tier": "basic", "cloud-sql-instance-size": "1G", },
  "provisioned_resources": [ { "resource-type": "compute-instance", "resource-
  url": "https://www.googleapis.com/compute/v1/projects/cloud-sql/zones/us-
  east1-b/instances/vm-1", } ], "maintenance_schedules": { "csa_rollout": {
  "start_time": { "seconds": **********, }, "end_time": { "seconds":
  1535406431, }, }, "ncsa_rollout": { "start_time": { "seconds": **********,
  }, "end_time": { "seconds": 1535406431, }, } }, "consumer_defined_name":
  "my-sql-instance1", } ``` LINT.IfChange

  Enums:
    StateValueValuesEnum: Output only. Current lifecycle state of the resource
      (e.g. if it's being created or ready to use).

  Messages:
    LabelsValue: Optional. Resource labels to represent user provided
      metadata. Each label is a key-value pair, where both the key and the
      value are arbitrary strings provided by the user.
    MaintenancePolicyNamesValue: Optional. The MaintenancePolicies that have
      been attached to the instance. The key must be of the type name of the
      oneof policy name defined in MaintenancePolicy, and the referenced
      policy must define the same policy type. For details, please refer to
      go/mr-user-guide. Should not be set if
      maintenance_settings.maintenance_policies is set.
    MaintenanceSchedulesValue: The MaintenanceSchedule contains the scheduling
      information of published maintenance schedule with same key as
      software_versions.
    NotificationParametersValue: Optional. notification_parameter are
      information that service producers may like to include that is not
      relevant to Rollout. This parameter will only be passed to Gamma and
      Cloud Logging for notification/logging purpose.
    ProducerMetadataValue: Output only. Custom string attributes used
      primarily to expose producer-specific information in monitoring
      dashboards. See go/get-instance-metadata.
    SoftwareVersionsValue: Software versions that are used to deploy this
      instance. This can be mutated by rollout services.

  Fields:
    consumerDefinedName: consumer_defined_name is the name of the instance set
      by the service consumers. Generally this is different from the `name`
      field which reperesents the system-assigned id of the instance which the
      service consumers do not recognize. This is a required field for tenants
      onboarding to Maintenance Window notifications (go/slm-rollout-
      maintenance-policies#prerequisites).
    consumerProjectNumber: Optional. The consumer_project_number associated
      with this Apigee instance. This field is added specifically to support
      Apigee integration with SLM Rollout and UMM. It represents the numerical
      project ID of the GCP project that consumes this Apigee instance. It is
      used for SLM rollout notifications and UMM integration, enabling proper
      mapping to customer projects and log delivery for Apigee instances. This
      field complements consumer_project_id and may be used for specific
      Apigee scenarios where the numerical ID is required.
    createTime: Output only. Timestamp when the resource was created.
    instanceType: Optional. The instance_type of this instance of format: proj
      ects/{project_number}/locations/{location_id}/instanceTypes/{instance_ty
      pe_id}. Instance Type represents a high-level tier or SKU of the service
      that this instance belong to. When enabled(eg: Maintenance Rollout),
      Rollout uses 'instance_type' along with 'software_versions' to determine
      whether instance needs an update or not.
    labels: Optional. Resource labels to represent user provided metadata.
      Each label is a key-value pair, where both the key and the value are
      arbitrary strings provided by the user.
    maintenancePolicyNames: Optional. The MaintenancePolicies that have been
      attached to the instance. The key must be of the type name of the oneof
      policy name defined in MaintenancePolicy, and the referenced policy must
      define the same policy type. For details, please refer to go/mr-user-
      guide. Should not be set if maintenance_settings.maintenance_policies is
      set.
    maintenanceSchedules: The MaintenanceSchedule contains the scheduling
      information of published maintenance schedule with same key as
      software_versions.
    maintenanceSettings: Optional. The MaintenanceSettings associated with
      instance.
    name: Unique name of the resource. It uses the form: `projects/{project_nu
      mber}/locations/{location_id}/instances/{instance_id}` Note: This name
      is passed, stored and logged across the rollout system. So use of
      consumer project_id or any other consumer PII in the name is strongly
      discouraged for wipeout (go/wipeout) compliance. See
      go/elysium/project_ids#storage-guidance for more details.
    notificationParameters: Optional. notification_parameter are information
      that service producers may like to include that is not relevant to
      Rollout. This parameter will only be passed to Gamma and Cloud Logging
      for notification/logging purpose.
    producerMetadata: Output only. Custom string attributes used primarily to
      expose producer-specific information in monitoring dashboards. See
      go/get-instance-metadata.
    provisionedResources: Output only. The list of data plane resources
      provisioned for this instance, e.g. compute VMs. See go/get-instance-
      metadata.
    slmInstanceTemplate: Link to the SLM instance template. Only populated
      when updating SLM instances via SSA's Actuation service adaptor. Service
      producers with custom control plane (e.g. Cloud SQL) doesn't need to
      populate this field. Instead they should use software_versions.
    sloMetadata: Output only. SLO metadata for instance classification in the
      Standardized dataplane SLO platform. See go/cloud-ssa-standard-slo for
      feature description.
    softwareVersions: Software versions that are used to deploy this instance.
      This can be mutated by rollout services.
    state: Output only. Current lifecycle state of the resource (e.g. if it's
      being created or ready to use).
    tenantProjectId: Output only. ID of the associated GCP tenant project. See
      go/get-instance-metadata.
    updateTime: Output only. Timestamp when the resource was last modified.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current lifecycle state of the resource (e.g. if it's
    being created or ready to use).

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      CREATING: Instance is being created.
      READY: Instance has been created and is ready to use.
      UPDATING: Instance is being updated.
      REPAIRING: Instance is unheathy and under repair.
      DELETING: Instance is being deleted.
      ERROR: Instance encountered an error and is in indeterministic state.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    UPDATING = 3
    REPAIRING = 4
    DELETING = 5
    ERROR = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels to represent user provided metadata. Each
    label is a key-value pair, where both the key and the value are arbitrary
    strings provided by the user.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MaintenancePolicyNamesValue(_messages.Message):
    r"""Optional. The MaintenancePolicies that have been attached to the
    instance. The key must be of the type name of the oneof policy name
    defined in MaintenancePolicy, and the referenced policy must define the
    same policy type. For details, please refer to go/mr-user-guide. Should
    not be set if maintenance_settings.maintenance_policies is set.

    Messages:
      AdditionalProperty: An additional property for a
        MaintenancePolicyNamesValue object.

    Fields:
      additionalProperties: Additional properties of type
        MaintenancePolicyNamesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MaintenancePolicyNamesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MaintenanceSchedulesValue(_messages.Message):
    r"""The MaintenanceSchedule contains the scheduling information of
    published maintenance schedule with same key as software_versions.

    Messages:
      AdditionalProperty: An additional property for a
        MaintenanceSchedulesValue object.

    Fields:
      additionalProperties: Additional properties of type
        MaintenanceSchedulesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MaintenanceSchedulesValue object.

      Fields:
        key: Name of the additional property.
        value: A
          GoogleCloudSaasacceleratorManagementProvidersV1MaintenanceSchedule
          attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1MaintenanceSchedule', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class NotificationParametersValue(_messages.Message):
    r"""Optional. notification_parameter are information that service
    producers may like to include that is not relevant to Rollout. This
    parameter will only be passed to Gamma and Cloud Logging for
    notification/logging purpose.

    Messages:
      AdditionalProperty: An additional property for a
        NotificationParametersValue object.

    Fields:
      additionalProperties: Additional properties of type
        NotificationParametersValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a NotificationParametersValue object.

      Fields:
        key: Name of the additional property.
        value: A
          GoogleCloudSaasacceleratorManagementProvidersV1NotificationParameter
          attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1NotificationParameter', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ProducerMetadataValue(_messages.Message):
    r"""Output only. Custom string attributes used primarily to expose
    producer-specific information in monitoring dashboards. See go/get-
    instance-metadata.

    Messages:
      AdditionalProperty: An additional property for a ProducerMetadataValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        ProducerMetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ProducerMetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SoftwareVersionsValue(_messages.Message):
    r"""Software versions that are used to deploy this instance. This can be
    mutated by rollout services.

    Messages:
      AdditionalProperty: An additional property for a SoftwareVersionsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        SoftwareVersionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SoftwareVersionsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  consumerDefinedName = _messages.StringField(1)
  consumerProjectNumber = _messages.StringField(2)
  createTime = _messages.StringField(3)
  instanceType = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  maintenancePolicyNames = _messages.MessageField('MaintenancePolicyNamesValue', 6)
  maintenanceSchedules = _messages.MessageField('MaintenanceSchedulesValue', 7)
  maintenanceSettings = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1MaintenanceSettings', 8)
  name = _messages.StringField(9)
  notificationParameters = _messages.MessageField('NotificationParametersValue', 10)
  producerMetadata = _messages.MessageField('ProducerMetadataValue', 11)
  provisionedResources = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1ProvisionedResource', 12, repeated=True)
  slmInstanceTemplate = _messages.StringField(13)
  sloMetadata = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1SloMetadata', 14)
  softwareVersions = _messages.MessageField('SoftwareVersionsValue', 15)
  state = _messages.EnumField('StateValueValuesEnum', 16)
  tenantProjectId = _messages.StringField(17)
  updateTime = _messages.StringField(18)


class GoogleCloudSaasacceleratorManagementProvidersV1MaintenanceSchedule(_messages.Message):
  r"""Maintenance schedule which is exposed to customer and potentially end
  user, indicating published upcoming future maintenance schedule

  Fields:
    canReschedule: This field is deprecated, and will be always set to true
      since reschedule can happen multiple times now. This field should not be
      removed until all service producers remove this for their customers.
    endTime: The scheduled end time for the maintenance.
    rolloutManagementPolicy: The rollout management policy this maintenance
      schedule is associated with. When doing reschedule update request, the
      reschedule should be against this given policy.
    scheduleDeadlineTime: schedule_deadline_time is the time deadline any
      schedule start time cannot go beyond, including reschedule. It's
      normally the initial schedule start time plus maintenance window length
      (1 day or 1 week). Maintenance cannot be scheduled to start beyond this
      deadline.
    startTime: The scheduled start time for the maintenance.
  """

  canReschedule = _messages.BooleanField(1)
  endTime = _messages.StringField(2)
  rolloutManagementPolicy = _messages.StringField(3)
  scheduleDeadlineTime = _messages.StringField(4)
  startTime = _messages.StringField(5)


class GoogleCloudSaasacceleratorManagementProvidersV1MaintenanceSettings(_messages.Message):
  r"""Maintenance settings associated with instance. Allows service producers
  and end users to assign settings that controls maintenance on this instance.

  Messages:
    MaintenancePoliciesValue: Optional. The MaintenancePolicies that have been
      attached to the instance. The key must be of the type name of the oneof
      policy name defined in MaintenancePolicy, and the embedded policy must
      define the same policy type. For details, please refer to go/mr-user-
      guide. Should not be set if maintenance_policy_names is set. If only the
      name is needed, then only populate MaintenancePolicy.name.

  Fields:
    exclude: Optional. Exclude instance from maintenance. When true, rollout
      service will not attempt maintenance on the instance. Rollout service
      will include the instance in reported rollout progress as not attempted.
    isRollback: Optional. If the update call is triggered from rollback, set
      the value as true.
    maintenancePolicies: Optional. The MaintenancePolicies that have been
      attached to the instance. The key must be of the type name of the oneof
      policy name defined in MaintenancePolicy, and the embedded policy must
      define the same policy type. For details, please refer to go/mr-user-
      guide. Should not be set if maintenance_policy_names is set. If only the
      name is needed, then only populate MaintenancePolicy.name.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MaintenancePoliciesValue(_messages.Message):
    r"""Optional. The MaintenancePolicies that have been attached to the
    instance. The key must be of the type name of the oneof policy name
    defined in MaintenancePolicy, and the embedded policy must define the same
    policy type. For details, please refer to go/mr-user-guide. Should not be
    set if maintenance_policy_names is set. If only the name is needed, then
    only populate MaintenancePolicy.name.

    Messages:
      AdditionalProperty: An additional property for a
        MaintenancePoliciesValue object.

    Fields:
      additionalProperties: Additional properties of type
        MaintenancePoliciesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MaintenancePoliciesValue object.

      Fields:
        key: Name of the additional property.
        value: A MaintenancePolicy attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('MaintenancePolicy', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  exclude = _messages.BooleanField(1)
  isRollback = _messages.BooleanField(2)
  maintenancePolicies = _messages.MessageField('MaintenancePoliciesValue', 3)


class GoogleCloudSaasacceleratorManagementProvidersV1NodeSloMetadata(_messages.Message):
  r"""Node information for custom per-node SLO implementations. SSA does not
  support per-node SLO, but producers can populate per-node information in
  SloMetadata for custom precomputations. SSA Eligibility Exporter will emit
  per-node metric based on this information.

  Fields:
    location: The location of the node, if different from instance location.
    nodeId: The id of the node. This should be equal to
      SaasInstanceNode.node_id.
    perSliEligibility: If present, this will override eligibility for the node
      coming from instance or exclusions for specified SLIs.
  """

  location = _messages.StringField(1)
  nodeId = _messages.StringField(2)
  perSliEligibility = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1PerSliSloEligibility', 3)


class GoogleCloudSaasacceleratorManagementProvidersV1NotificationParameter(_messages.Message):
  r"""Contains notification related data.

  Fields:
    values: Optional. Array of string values. e.g. instance's replica
      information.
  """

  values = _messages.StringField(1, repeated=True)


class GoogleCloudSaasacceleratorManagementProvidersV1PerSliSloEligibility(_messages.Message):
  r"""PerSliSloEligibility is a mapping from an SLI name to eligibility.

  Messages:
    EligibilitiesValue: An entry in the eligibilities map specifies an
      eligibility for a particular SLI for the given instance. The SLI key in
      the name must be a valid SLI name specified in the Eligibility Exporter
      binary flags otherwise an error will be emitted by Eligibility Exporter
      and the oncaller will be alerted. If an SLI has been defined in the
      binary flags but the eligibilities map does not contain it, the
      corresponding SLI time series will not be emitted by the Eligibility
      Exporter. This ensures a smooth rollout and compatibility between the
      data produced by different versions of the Eligibility Exporters. If
      eligibilities map contains a key for an SLI which has not been declared
      in the binary flags, there will be an error message emitted in the
      Eligibility Exporter log and the metric for the SLI in question will not
      be emitted.

  Fields:
    eligibilities: An entry in the eligibilities map specifies an eligibility
      for a particular SLI for the given instance. The SLI key in the name
      must be a valid SLI name specified in the Eligibility Exporter binary
      flags otherwise an error will be emitted by Eligibility Exporter and the
      oncaller will be alerted. If an SLI has been defined in the binary flags
      but the eligibilities map does not contain it, the corresponding SLI
      time series will not be emitted by the Eligibility Exporter. This
      ensures a smooth rollout and compatibility between the data produced by
      different versions of the Eligibility Exporters. If eligibilities map
      contains a key for an SLI which has not been declared in the binary
      flags, there will be an error message emitted in the Eligibility
      Exporter log and the metric for the SLI in question will not be emitted.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EligibilitiesValue(_messages.Message):
    r"""An entry in the eligibilities map specifies an eligibility for a
    particular SLI for the given instance. The SLI key in the name must be a
    valid SLI name specified in the Eligibility Exporter binary flags
    otherwise an error will be emitted by Eligibility Exporter and the
    oncaller will be alerted. If an SLI has been defined in the binary flags
    but the eligibilities map does not contain it, the corresponding SLI time
    series will not be emitted by the Eligibility Exporter. This ensures a
    smooth rollout and compatibility between the data produced by different
    versions of the Eligibility Exporters. If eligibilities map contains a key
    for an SLI which has not been declared in the binary flags, there will be
    an error message emitted in the Eligibility Exporter log and the metric
    for the SLI in question will not be emitted.

    Messages:
      AdditionalProperty: An additional property for a EligibilitiesValue
        object.

    Fields:
      additionalProperties: Additional properties of type EligibilitiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EligibilitiesValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudSaasacceleratorManagementProvidersV1SloEligibility
          attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1SloEligibility', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  eligibilities = _messages.MessageField('EligibilitiesValue', 1)


class GoogleCloudSaasacceleratorManagementProvidersV1ProvisionedResource(_messages.Message):
  r"""Describes provisioned dataplane resources.

  Fields:
    resourceType: Type of the resource. This can be either a GCP resource or a
      custom one (e.g. another cloud provider's VM). For GCP compute resources
      use singular form of the names listed in GCP compute API documentation
      (https://cloud.google.com/compute/docs/reference/rest/v1/), prefixed
      with 'compute-', for example: 'compute-instance', 'compute-disk',
      'compute-autoscaler'.
    resourceUrl: URL identifying the resource, e.g.
      "https://www.googleapis.com/compute/v1/projects/...)".
  """

  resourceType = _messages.StringField(1)
  resourceUrl = _messages.StringField(2)


class GoogleCloudSaasacceleratorManagementProvidersV1SloEligibility(_messages.Message):
  r"""SloEligibility is a tuple containing eligibility value: true if an
  instance is eligible for SLO calculation or false if it should be excluded
  from all SLO-related calculations along with a user-defined reason.

  Fields:
    eligible: Whether an instance is eligible or ineligible.
    reason: User-defined reason for the current value of instance eligibility.
      Usually, this can be directly mapped to the internal state. An empty
      reason is allowed.
  """

  eligible = _messages.BooleanField(1)
  reason = _messages.StringField(2)


class GoogleCloudSaasacceleratorManagementProvidersV1SloMetadata(_messages.Message):
  r"""SloMetadata contains resources required for proper SLO classification of
  the instance.

  Fields:
    nodes: Optional. List of nodes. Some producers need to use per-node
      metadata to calculate SLO. This field allows such producers to publish
      per-node SLO meta data, which will be consumed by SSA Eligibility
      Exporter and published in the form of per node metric to Monarch.
    perSliEligibility: Optional. Multiple per-instance SLI eligibilities which
      apply for individual SLIs.
    tier: Name of the SLO tier the Instance belongs to. This name will be
      expected to match the tiers specified in the service SLO configuration.
      Field is mandatory and must not be empty.
  """

  nodes = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1NodeSloMetadata', 1, repeated=True)
  perSliEligibility = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1PerSliSloEligibility', 2)
  tier = _messages.StringField(3)


class IOPSPerTB(_messages.Message):
  r"""IOPS per TB. Filestore defines TB as 1024^4 bytes (TiB).

  Fields:
    maxIopsPerTb: Required. Maximum IOPS per TiB.
  """

  maxIopsPerTb = _messages.IntegerField(1)


class Instance(_messages.Message):
  r"""A Filestore instance.

  Enums:
    ProtocolValueValuesEnum: Immutable. The protocol indicates the access
      protocol for all shares in the instance. This field is immutable and it
      cannot be changed after the instance has been created. Default value:
      `NFS_V3`.
    StateValueValuesEnum: Output only. The instance state.
    SuspensionReasonsValueListEntryValuesEnum:
    TierValueValuesEnum: The service tier of the instance.

  Messages:
    LabelsValue: Resource labels to represent user provided metadata.
    TagsValue: Optional. Input only. Immutable. Tag key-value pairs bound to
      this resource. Each key must be a namespaced name and each value a short
      name. Example: "123456789012/environment" : "production",
      "123456789013/costCenter" : "marketing" See the documentation for more
      information: - Namespaced name: https://cloud.google.com/resource-
      manager/docs/tags/tags-creating-and-managing#retrieving_tag_key - Short
      name: https://cloud.google.com/resource-manager/docs/tags/tags-creating-
      and-managing#retrieving_tag_value

  Fields:
    createTime: Output only. The time when the instance was created.
    customPerformanceSupported: Output only. Indicates whether this instance
      supports configuring its performance. If true, the user can configure
      the instance's performance by using the 'performance_config' field.
    deletionProtectionEnabled: Optional. Indicates whether the instance is
      protected against deletion.
    deletionProtectionReason: Optional. The reason for enabling deletion
      protection.
    description: The description of the instance (2048 characters or less).
    directoryServices: Optional. Directory Services configuration for
      Kerberos-based authentication. Should only be set if protocol is
      "NFS_V4_1".
    etag: Server-specified ETag for the instance resource to prevent
      simultaneous updates from overwriting each other.
    fileShares: File system shares on the instance. For this version, only a
      single file share is supported.
    kmsKeyName: KMS key name used for data encryption.
    labels: Resource labels to represent user provided metadata.
    name: Output only. The resource name of the instance, in the format
      `projects/{project}/locations/{location}/instances/{instance}`.
    networks: VPC networks to which the instance is connected. For this
      version, only a single network is supported.
    performanceConfig: Optional. Used to configure performance.
    performanceLimits: Output only. Used for getting performance limits.
    protocol: Immutable. The protocol indicates the access protocol for all
      shares in the instance. This field is immutable and it cannot be changed
      after the instance has been created. Default value: `NFS_V3`.
    replication: Optional. Replication configuration.
    satisfiesPzi: Output only. Reserved for future use.
    satisfiesPzs: Output only. Reserved for future use.
    state: Output only. The instance state.
    statusMessage: Output only. Additional information about the instance
      state, if available.
    suspensionReasons: Output only. Field indicates all the reasons the
      instance is in "SUSPENDED" state.
    tags: Optional. Input only. Immutable. Tag key-value pairs bound to this
      resource. Each key must be a namespaced name and each value a short
      name. Example: "123456789012/environment" : "production",
      "123456789013/costCenter" : "marketing" See the documentation for more
      information: - Namespaced name: https://cloud.google.com/resource-
      manager/docs/tags/tags-creating-and-managing#retrieving_tag_key - Short
      name: https://cloud.google.com/resource-manager/docs/tags/tags-creating-
      and-managing#retrieving_tag_value
    tier: The service tier of the instance.
  """

  class ProtocolValueValuesEnum(_messages.Enum):
    r"""Immutable. The protocol indicates the access protocol for all shares
    in the instance. This field is immutable and it cannot be changed after
    the instance has been created. Default value: `NFS_V3`.

    Values:
      FILE_PROTOCOL_UNSPECIFIED: FILE_PROTOCOL_UNSPECIFIED serves a "not set"
        default value when a FileProtocol is a separate field in a message.
      NFS_V3: NFS 3.0.
      NFS_V4_1: NFS 4.1.
    """
    FILE_PROTOCOL_UNSPECIFIED = 0
    NFS_V3 = 1
    NFS_V4_1 = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The instance state.

    Values:
      STATE_UNSPECIFIED: State not set.
      CREATING: The instance is being created.
      READY: The instance is available for use.
      REPAIRING: Work is being done on the instance. You can get further
        details from the `statusMessage` field of the `Instance` resource.
      DELETING: The instance is shutting down.
      ERROR: The instance is experiencing an issue and might be unusable. You
        can get further details from the `statusMessage` field of the
        `Instance` resource.
      RESTORING: The instance is restoring a backup to an existing file share
        and may be unusable during this time.
      SUSPENDED: The instance is suspended. You can get further details from
        the `suspension_reasons` field of the `Instance` resource.
      SUSPENDING: The instance is in the process of becoming suspended.
      RESUMING: The instance is in the process of becoming active.
      REVERTING: The instance is reverting to a snapshot.
      PROMOTING: The replica instance is being promoted.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    REPAIRING = 3
    DELETING = 4
    ERROR = 5
    RESTORING = 6
    SUSPENDED = 7
    SUSPENDING = 8
    RESUMING = 9
    REVERTING = 10
    PROMOTING = 11

  class SuspensionReasonsValueListEntryValuesEnum(_messages.Enum):
    r"""SuspensionReasonsValueListEntryValuesEnum enum type.

    Values:
      SUSPENSION_REASON_UNSPECIFIED: Not set.
      KMS_KEY_ISSUE: The KMS key used by the instance is either revoked or
        denied access to.
    """
    SUSPENSION_REASON_UNSPECIFIED = 0
    KMS_KEY_ISSUE = 1

  class TierValueValuesEnum(_messages.Enum):
    r"""The service tier of the instance.

    Values:
      TIER_UNSPECIFIED: Not set.
      STANDARD: STANDARD tier. BASIC_HDD is the preferred term for this tier.
      PREMIUM: PREMIUM tier. BASIC_SSD is the preferred term for this tier.
      BASIC_HDD: BASIC instances offer a maximum capacity of 63.9 TB.
        BASIC_HDD is an alias for STANDARD Tier, offering economical
        performance backed by HDD.
      BASIC_SSD: BASIC instances offer a maximum capacity of 63.9 TB.
        BASIC_SSD is an alias for PREMIUM Tier, and offers improved
        performance backed by SSD.
      HIGH_SCALE_SSD: HIGH_SCALE instances offer expanded capacity and
        performance scaling capabilities.
      ENTERPRISE: ENTERPRISE instances offer the features and availability
        needed for mission-critical workloads.
      ZONAL: ZONAL instances offer expanded capacity and performance scaling
        capabilities.
      REGIONAL: REGIONAL instances offer the features and availability needed
        for mission-critical workloads.
    """
    TIER_UNSPECIFIED = 0
    STANDARD = 1
    PREMIUM = 2
    BASIC_HDD = 3
    BASIC_SSD = 4
    HIGH_SCALE_SSD = 5
    ENTERPRISE = 6
    ZONAL = 7
    REGIONAL = 8

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Resource labels to represent user provided metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""Optional. Input only. Immutable. Tag key-value pairs bound to this
    resource. Each key must be a namespaced name and each value a short name.
    Example: "123456789012/environment" : "production",
    "123456789013/costCenter" : "marketing" See the documentation for more
    information: - Namespaced name: https://cloud.google.com/resource-
    manager/docs/tags/tags-creating-and-managing#retrieving_tag_key - Short
    name: https://cloud.google.com/resource-manager/docs/tags/tags-creating-
    and-managing#retrieving_tag_value

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  customPerformanceSupported = _messages.BooleanField(2)
  deletionProtectionEnabled = _messages.BooleanField(3)
  deletionProtectionReason = _messages.StringField(4)
  description = _messages.StringField(5)
  directoryServices = _messages.MessageField('DirectoryServicesConfig', 6)
  etag = _messages.StringField(7)
  fileShares = _messages.MessageField('FileShareConfig', 8, repeated=True)
  kmsKeyName = _messages.StringField(9)
  labels = _messages.MessageField('LabelsValue', 10)
  name = _messages.StringField(11)
  networks = _messages.MessageField('NetworkConfig', 12, repeated=True)
  performanceConfig = _messages.MessageField('PerformanceConfig', 13)
  performanceLimits = _messages.MessageField('PerformanceLimits', 14)
  protocol = _messages.EnumField('ProtocolValueValuesEnum', 15)
  replication = _messages.MessageField('Replication', 16)
  satisfiesPzi = _messages.BooleanField(17)
  satisfiesPzs = _messages.BooleanField(18)
  state = _messages.EnumField('StateValueValuesEnum', 19)
  statusMessage = _messages.StringField(20)
  suspensionReasons = _messages.EnumField('SuspensionReasonsValueListEntryValuesEnum', 21, repeated=True)
  tags = _messages.MessageField('TagsValue', 22)
  tier = _messages.EnumField('TierValueValuesEnum', 23)


class LdapConfig(_messages.Message):
  r"""LdapConfig contains all the parameters for connecting to LDAP servers.

  Fields:
    domain: Required. The LDAP domain name in the format of `my-domain.com`.
    groupsOu: Optional. The groups Organizational Unit (OU) is optional. This
      parameter is a hint to allow faster lookup in the LDAP namespace. In
      case that this parameter is not provided, Filestore instance will query
      the whole LDAP namespace.
    servers: Required. The servers names are used for specifying the LDAP
      servers names. The LDAP servers names can come with two formats: 1. DNS
      name, for example: `ldap.example1.com`, `ldap.example2.com`. 2. IP
      address, for example: `********`, `********`, `********`. All servers
      names must be in the same format: either all DNS names or all IP
      addresses.
    usersOu: Optional. The users Organizational Unit (OU) is optional. This
      parameter is a hint to allow faster lookup in the LDAP namespace. In
      case that this parameter is not provided, Filestore instance will query
      the whole LDAP namespace.
  """

  domain = _messages.StringField(1)
  groupsOu = _messages.StringField(2)
  servers = _messages.StringField(3, repeated=True)
  usersOu = _messages.StringField(4)


class ListBackupsResponse(_messages.Message):
  r"""ListBackupsResponse is the result of ListBackupsRequest.

  Fields:
    backups: A list of backups in the project for the specified location. If
      the `{location}` value in the request is "-", the response contains a
      list of backups from all locations. If any location is unreachable, the
      response will only return backups in reachable locations and the
      "unreachable" field will be populated with a list of unreachable
      locations.
    nextPageToken: The token you can use to retrieve the next page of results.
      Not returned if there are no more results in the list.
    unreachable: Unordered list. Locations that could not be reached.
  """

  backups = _messages.MessageField('Backup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListInstancesResponse(_messages.Message):
  r"""ListInstancesResponse is the result of ListInstancesRequest.

  Fields:
    instances: A list of instances in the project for the specified location.
      If the `{location}` value in the request is "-", the response contains a
      list of instances from all locations. If any location is unreachable,
      the response will only return instances in reachable locations and the
      "unreachable" field will be populated with a list of unreachable
      locations.
    nextPageToken: The token you can use to retrieve the next page of results.
      Not returned if there are no more results in the list.
    unreachable: Unordered list. Locations that could not be reached.
  """

  instances = _messages.MessageField('Instance', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListSnapshotsResponse(_messages.Message):
  r"""ListSnapshotsResponse is the result of ListSnapshotsRequest.

  Fields:
    nextPageToken: The token you can use to retrieve the next page of results.
      Not returned if there are no more results in the list.
    snapshots: A list of snapshots in the project for the specified instance.
    unreachable: Unordered list. Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  snapshots = _messages.MessageField('Snapshot', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class MaintenancePolicy(_messages.Message):
  r"""Defines policies to service maintenance events.

  Enums:
    StateValueValuesEnum: Optional. The state of the policy.

  Messages:
    LabelsValue: Optional. Resource labels to represent user provided
      metadata. Each label is a key-value pair, where both the key and the
      value are arbitrary strings provided by the user.

  Fields:
    createTime: Output only. The time when the resource was created.
    description: Optional. Description of what this policy is for.
      Create/Update methods return INVALID_ARGUMENT if the length is greater
      than 512.
    labels: Optional. Resource labels to represent user provided metadata.
      Each label is a key-value pair, where both the key and the value are
      arbitrary strings provided by the user.
    name: Required. MaintenancePolicy name using the form: `projects/{project_
      id}/locations/{location_id}/maintenancePolicies/{maintenance_policy_id}`
      where {project_id} refers to a GCP consumer project ID, {location_id}
      refers to a GCP region/zone, {maintenance_policy_id} must be 1-63
      characters long and match the regular expression
      `[a-z0-9]([-a-z0-9]*[a-z0-9])?`.
    state: Optional. The state of the policy.
    updatePolicy: Maintenance policy applicable to instance update.
    updateTime: Output only. The time when the resource was updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Optional. The state of the policy.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      READY: Resource is ready to be used.
      DELETING: Resource is being deleted. It can no longer be attached to
        instances.
    """
    STATE_UNSPECIFIED = 0
    READY = 1
    DELETING = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels to represent user provided metadata. Each
    label is a key-value pair, where both the key and the value are arbitrary
    strings provided by the user.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)
  updatePolicy = _messages.MessageField('UpdatePolicy', 6)
  updateTime = _messages.StringField(7)


class MaintenanceWindow(_messages.Message):
  r"""MaintenanceWindow definition.

  Fields:
    dailyCycle: Daily cycle.
    weeklyCycle: Weekly cycle.
  """

  dailyCycle = _messages.MessageField('DailyCycle', 1)
  weeklyCycle = _messages.MessageField('WeeklyCycle', 2)


class ManagedActiveDirectoryConfig(_messages.Message):
  r"""ManagedActiveDirectoryConfig contains all the parameters for connecting
  to Managed Service for Microsoft Active Directory (Managed Microsoft AD).

  Fields:
    computer: Required. The computer name is used as a prefix in the command
      to mount the remote target. For example: if the computer is `my-
      computer`, the mount command will look like: `$mount -o
      vers=4.1,sec=krb5 my-computer.filestore.: `.
    domain: Required. The domain resource name, in the format
      `projects/{project_id}/locations/global/domains/{domain}`.
  """

  computer = _messages.StringField(1)
  domain = _messages.StringField(2)


class NetworkConfig(_messages.Message):
  r"""Network configuration for the instance.

  Enums:
    ConnectModeValueValuesEnum: The network connect mode of the Filestore
      instance. If not provided, the connect mode defaults to DIRECT_PEERING.
    ModesValueListEntryValuesEnum:

  Fields:
    connectMode: The network connect mode of the Filestore instance. If not
      provided, the connect mode defaults to DIRECT_PEERING.
    ipAddresses: Output only. IPv4 addresses in the format
      `{octet1}.{octet2}.{octet3}.{octet4}` or IPv6 addresses in the format `{
      block1}:{block2}:{block3}:{block4}:{block5}:{block6}:{block7}:{block8}`.
    modes: Internet protocol versions for which the instance has IP addresses
      assigned. For this version, only MODE_IPV4 is supported.
    network: The name of the Google Compute Engine [VPC
      network](https://cloud.google.com/vpc/docs/vpc) to which the instance is
      connected.
    pscConfig: Optional. Private Service Connect configuration. Should only be
      set when connect_mode is PRIVATE_SERVICE_CONNECT.
    reservedIpRange: Optional, reserved_ip_range can have one of the following
      two types of values. * CIDR range value when using DIRECT_PEERING
      connect mode. * [Allocated IP address
      range](https://cloud.google.com/compute/docs/ip-addresses/reserve-
      static-internal-ip-address) when using PRIVATE_SERVICE_ACCESS connect
      mode. When the name of an allocated IP address range is specified, it
      must be one of the ranges associated with the private service access
      connection. When specified as a direct CIDR value, it must be a /29 CIDR
      block for Basic tier, a /24 CIDR block for High Scale tier, or a /26
      CIDR block for Enterprise tier in one of the [internal IP address ranges
      ](https://www.arin.net/reference/research/statistics/address_filters/)
      that identifies the range of IP addresses reserved for this instance.
      For example, 10.0.0.0/29, ***********/24 or ***********/26,
      respectively. The range you specify can't overlap with either existing
      subnets or assigned IP address ranges for other Filestore instances in
      the selected VPC network.
  """

  class ConnectModeValueValuesEnum(_messages.Enum):
    r"""The network connect mode of the Filestore instance. If not provided,
    the connect mode defaults to DIRECT_PEERING.

    Values:
      CONNECT_MODE_UNSPECIFIED: Not set.
      DIRECT_PEERING: Connect via direct peering to the Filestore service.
      PRIVATE_SERVICE_ACCESS: Connect to your Filestore instance using Private
        Service Access. Private services access provides an IP address range
        for multiple Google Cloud services, including Filestore.
      PRIVATE_SERVICE_CONNECT: Connect to your Filestore instance using
        Private Service Connect. A connection policy must exist in the region
        for the VPC network and the google-cloud-filestore service class.
    """
    CONNECT_MODE_UNSPECIFIED = 0
    DIRECT_PEERING = 1
    PRIVATE_SERVICE_ACCESS = 2
    PRIVATE_SERVICE_CONNECT = 3

  class ModesValueListEntryValuesEnum(_messages.Enum):
    r"""ModesValueListEntryValuesEnum enum type.

    Values:
      ADDRESS_MODE_UNSPECIFIED: Internet protocol not set.
      MODE_IPV4: Use the IPv4 internet protocol.
    """
    ADDRESS_MODE_UNSPECIFIED = 0
    MODE_IPV4 = 1

  connectMode = _messages.EnumField('ConnectModeValueValuesEnum', 1)
  ipAddresses = _messages.StringField(2, repeated=True)
  modes = _messages.EnumField('ModesValueListEntryValuesEnum', 3, repeated=True)
  network = _messages.StringField(4)
  pscConfig = _messages.MessageField('PscConfig', 5)
  reservedIpRange = _messages.StringField(6)


class NfsExportOptions(_messages.Message):
  r"""NFS export options specifications.

  Enums:
    AccessModeValueValuesEnum: Either READ_ONLY, for allowing only read
      requests on the exported directory, or READ_WRITE, for allowing both
      read and write requests. The default is READ_WRITE.
    SquashModeValueValuesEnum: Either NO_ROOT_SQUASH, for allowing root access
      on the exported directory, or ROOT_SQUASH, for not allowing root access.
      The default is NO_ROOT_SQUASH.

  Fields:
    accessMode: Either READ_ONLY, for allowing only read requests on the
      exported directory, or READ_WRITE, for allowing both read and write
      requests. The default is READ_WRITE.
    anonGid: An integer representing the anonymous group id with a default
      value of 65534. Anon_gid may only be set with squash_mode of
      ROOT_SQUASH. An error will be returned if this field is specified for
      other squash_mode settings.
    anonUid: An integer representing the anonymous user id with a default
      value of 65534. Anon_uid may only be set with squash_mode of
      ROOT_SQUASH. An error will be returned if this field is specified for
      other squash_mode settings.
    ipRanges: List of either an IPv4 addresses in the format
      `{octet1}.{octet2}.{octet3}.{octet4}` or CIDR ranges in the format
      `{octet1}.{octet2}.{octet3}.{octet4}/{mask size}` which may mount the
      file share. Overlapping IP ranges are not allowed, both within and
      across NfsExportOptions. An error will be returned. The limit is 64 IP
      ranges/addresses for each FileShareConfig among all NfsExportOptions.
    network: Optional. The source VPC network for ip_ranges. Required for
      instances using Private Service Connect, optional otherwise. If
      provided, must be the same network specified in the
      `NetworkConfig.network` field.
    squashMode: Either NO_ROOT_SQUASH, for allowing root access on the
      exported directory, or ROOT_SQUASH, for not allowing root access. The
      default is NO_ROOT_SQUASH.
  """

  class AccessModeValueValuesEnum(_messages.Enum):
    r"""Either READ_ONLY, for allowing only read requests on the exported
    directory, or READ_WRITE, for allowing both read and write requests. The
    default is READ_WRITE.

    Values:
      ACCESS_MODE_UNSPECIFIED: AccessMode not set.
      READ_ONLY: The client can only read the file share.
      READ_WRITE: The client can read and write the file share (default).
    """
    ACCESS_MODE_UNSPECIFIED = 0
    READ_ONLY = 1
    READ_WRITE = 2

  class SquashModeValueValuesEnum(_messages.Enum):
    r"""Either NO_ROOT_SQUASH, for allowing root access on the exported
    directory, or ROOT_SQUASH, for not allowing root access. The default is
    NO_ROOT_SQUASH.

    Values:
      SQUASH_MODE_UNSPECIFIED: SquashMode not set.
      NO_ROOT_SQUASH: The Root user has root access to the file share
        (default).
      ROOT_SQUASH: The Root user has squashed access to the anonymous uid/gid.
    """
    SQUASH_MODE_UNSPECIFIED = 0
    NO_ROOT_SQUASH = 1
    ROOT_SQUASH = 2

  accessMode = _messages.EnumField('AccessModeValueValuesEnum', 1)
  anonGid = _messages.IntegerField(2)
  anonUid = _messages.IntegerField(3)
  ipRanges = _messages.StringField(4, repeated=True)
  network = _messages.StringField(5)
  squashMode = _messages.EnumField('SquashModeValueValuesEnum', 6)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    cancelRequested: Output only. Identifies whether the user has requested
      cancellation of the operation. Operations that have been cancelled
      successfully have google.longrunning.Operation.error value with a
      google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    statusDetail: Output only. Human-readable status of the operation, if any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  cancelRequested = _messages.BooleanField(2)
  createTime = _messages.StringField(3)
  endTime = _messages.StringField(4)
  statusDetail = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class PerformanceConfig(_messages.Message):
  r"""Used for setting the performance configuration. If the user doesn't
  specify PerformanceConfig, automatically provision the default performance
  settings as described in
  https://cloud.google.com/filestore/docs/performance. Larger instances will
  be linearly set to more IOPS. If the instance's capacity is increased or
  decreased, its performance will be automatically adjusted upwards or
  downwards accordingly (respectively).

  Fields:
    fixedIops: Choose a fixed provisioned IOPS value for the instance, which
      will remain constant regardless of instance capacity. Value must be a
      multiple of 1000. If the chosen value is outside the supported range for
      the instance's capacity during instance creation, instance creation will
      fail with an `InvalidArgument` error. Similarly, if an instance capacity
      update would result in a value outside the supported range, the update
      will fail with an `InvalidArgument` error.
    iopsPerTb: Provision IOPS dynamically based on the capacity of the
      instance. Provisioned IOPS will be calculated by multiplying the
      capacity of the instance in TiB by the `iops_per_tb` value. For example,
      for a 2 TiB instance with an `iops_per_tb` value of 17000 the
      provisioned IOPS will be 34000. If the calculated value is outside the
      supported range for the instance's capacity during instance creation,
      instance creation will fail with an `InvalidArgument` error. Similarly,
      if an instance capacity update would result in a value outside the
      supported range, the update will fail with an `InvalidArgument` error.
  """

  fixedIops = _messages.MessageField('FixedIOPS', 1)
  iopsPerTb = _messages.MessageField('IOPSPerTB', 2)


class PerformanceLimits(_messages.Message):
  r"""The enforced performance limits, calculated from the instance's
  performance configuration.

  Fields:
    maxIops: Output only. The max IOPS.
    maxReadIops: Output only. The max read IOPS.
    maxReadThroughputBps: Output only. The max read throughput in bytes per
      second.
    maxWriteIops: Output only. The max write IOPS.
    maxWriteThroughputBps: Output only. The max write throughput in bytes per
      second.
  """

  maxIops = _messages.IntegerField(1)
  maxReadIops = _messages.IntegerField(2)
  maxReadThroughputBps = _messages.IntegerField(3)
  maxWriteIops = _messages.IntegerField(4)
  maxWriteThroughputBps = _messages.IntegerField(5)


class PromoteReplicaRequest(_messages.Message):
  r"""PromoteReplicaRequest promotes a Filestore standby instance (replica).

  Fields:
    peerInstance: Optional. The resource name of the peer instance to promote,
      in the format
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`.
      The peer instance is required if the operation is called on an active
      instance.
  """

  peerInstance = _messages.StringField(1)


class PscConfig(_messages.Message):
  r"""Private Service Connect configuration.

  Fields:
    endpointProject: Optional. Consumer service project in which the Private
      Service Connect endpoint would be set up. This is optional, and only
      relevant in case the network is a shared VPC. If this is not specified,
      the endpoint would be setup in the VPC host project.
  """

  endpointProject = _messages.StringField(1)


class ReplicaConfig(_messages.Message):
  r"""Replica configuration for the instance.

  Enums:
    StateValueValuesEnum: Output only. The replica state.
    StateReasonsValueListEntryValuesEnum:

  Fields:
    lastActiveSyncTime: Output only. The timestamp of the latest replication
      snapshot taken on the active instance and is already replicated safely.
    peerInstance: Optional. The peer instance.
    state: Output only. The replica state.
    stateReasons: Output only. Additional information about the replication
      state, if available.
    stateUpdateTime: Output only. The time when the replica state was updated.
  """

  class StateReasonsValueListEntryValuesEnum(_messages.Enum):
    r"""StateReasonsValueListEntryValuesEnum enum type.

    Values:
      STATE_REASON_UNSPECIFIED: Reason not specified.
      PEER_INSTANCE_UNREACHABLE: The peer instance is unreachable.
      REMOVE_FAILED: The remove replica peer instance operation failed.
    """
    STATE_REASON_UNSPECIFIED = 0
    PEER_INSTANCE_UNREACHABLE = 1
    REMOVE_FAILED = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The replica state.

    Values:
      STATE_UNSPECIFIED: State not set.
      CREATING: The replica is being created.
      READY: The replica is ready.
      REMOVING: The replica is being removed.
      FAILED: The replica is experiencing an issue and might be unusable. You
        can get further details from the `stateReasons` field of the
        `ReplicaConfig` object.
      PROMOTING: The replica is being promoted.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    REMOVING = 3
    FAILED = 4
    PROMOTING = 5

  lastActiveSyncTime = _messages.StringField(1)
  peerInstance = _messages.StringField(2)
  state = _messages.EnumField('StateValueValuesEnum', 3)
  stateReasons = _messages.EnumField('StateReasonsValueListEntryValuesEnum', 4, repeated=True)
  stateUpdateTime = _messages.StringField(5)


class Replication(_messages.Message):
  r"""Replication specifications.

  Enums:
    RoleValueValuesEnum: Optional. The replication role.

  Fields:
    replicas: Optional. Replication configuration for the replica instance
      associated with this instance. Only a single replica is supported.
    role: Optional. The replication role.
  """

  class RoleValueValuesEnum(_messages.Enum):
    r"""Optional. The replication role.

    Values:
      ROLE_UNSPECIFIED: Role not set.
      ACTIVE: The instance is the `ACTIVE` replication member, functions as
        the replication source instance.
      STANDBY: The instance is the `STANDBY` replication member, functions as
        the replication destination instance.
    """
    ROLE_UNSPECIFIED = 0
    ACTIVE = 1
    STANDBY = 2

  replicas = _messages.MessageField('ReplicaConfig', 1, repeated=True)
  role = _messages.EnumField('RoleValueValuesEnum', 2)


class RestoreInstanceRequest(_messages.Message):
  r"""RestoreInstanceRequest restores an existing instance's file share from a
  backup.

  Fields:
    fileShare: Required. Name of the file share in the Filestore instance that
      the backup is being restored to.
    sourceBackup: The resource name of the backup, in the format
      `projects/{project_number}/locations/{location_id}/backups/{backup_id}`.
  """

  fileShare = _messages.StringField(1)
  sourceBackup = _messages.StringField(2)


class RevertInstanceRequest(_messages.Message):
  r"""RevertInstanceRequest reverts the given instance's file share to the
  specified snapshot.

  Fields:
    targetSnapshotId: Required. The snapshot resource ID, in the format 'my-
      snapshot', where the specified ID is the {snapshot_id} of the fully
      qualified name like `projects/{project_id}/locations/{location_id}/insta
      nces/{instance_id}/snapshots/{snapshot_id}`
  """

  targetSnapshotId = _messages.StringField(1)


class Schedule(_messages.Message):
  r"""Configure the schedule.

  Enums:
    DayValueValuesEnum: Allows to define schedule that runs specified day of
      the week.

  Fields:
    day: Allows to define schedule that runs specified day of the week.
    duration: Output only. Duration of the time window, set by service
      producer.
    startTime: Time within the window to start the operations.
  """

  class DayValueValuesEnum(_messages.Enum):
    r"""Allows to define schedule that runs specified day of the week.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  day = _messages.EnumField('DayValueValuesEnum', 1)
  duration = _messages.StringField(2)
  startTime = _messages.MessageField('TimeOfDay', 3)


class Snapshot(_messages.Message):
  r"""A Filestore snapshot.

  Enums:
    StateValueValuesEnum: Output only. The snapshot state.

  Messages:
    LabelsValue: Resource labels to represent user provided metadata.
    TagsValue: Optional. Input only. Immutable. Tag key-value pairs bound to
      this resource. Each key must be a namespaced name and each value a short
      name. Example: "123456789012/environment" : "production",
      "123456789013/costCenter" : "marketing" See the documentation for more
      information: - Namespaced name: https://cloud.google.com/resource-
      manager/docs/tags/tags-creating-and-managing#retrieving_tag_key - Short
      name: https://cloud.google.com/resource-manager/docs/tags/tags-creating-
      and-managing#retrieving_tag_value

  Fields:
    createTime: Output only. The time when the snapshot was created.
    description: A description of the snapshot with 2048 characters or less.
      Requests with longer descriptions will be rejected.
    filesystemUsedBytes: Output only. The amount of bytes needed to allocate a
      full copy of the snapshot content
    labels: Resource labels to represent user provided metadata.
    name: Output only. The resource name of the snapshot, in the format `proje
      cts/{project_id}/locations/{location_id}/instances/{instance_id}/snapsho
      ts/{snapshot_id}`.
    state: Output only. The snapshot state.
    tags: Optional. Input only. Immutable. Tag key-value pairs bound to this
      resource. Each key must be a namespaced name and each value a short
      name. Example: "123456789012/environment" : "production",
      "123456789013/costCenter" : "marketing" See the documentation for more
      information: - Namespaced name: https://cloud.google.com/resource-
      manager/docs/tags/tags-creating-and-managing#retrieving_tag_key - Short
      name: https://cloud.google.com/resource-manager/docs/tags/tags-creating-
      and-managing#retrieving_tag_value
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The snapshot state.

    Values:
      STATE_UNSPECIFIED: State not set.
      CREATING: Snapshot is being created.
      READY: Snapshot is available for use.
      DELETING: Snapshot is being deleted.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    DELETING = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Resource labels to represent user provided metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""Optional. Input only. Immutable. Tag key-value pairs bound to this
    resource. Each key must be a namespaced name and each value a short name.
    Example: "123456789012/environment" : "production",
    "123456789013/costCenter" : "marketing" See the documentation for more
    information: - Namespaced name: https://cloud.google.com/resource-
    manager/docs/tags/tags-creating-and-managing#retrieving_tag_key - Short
    name: https://cloud.google.com/resource-manager/docs/tags/tags-creating-
    and-managing#retrieving_tag_value

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  filesystemUsedBytes = _messages.IntegerField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  tags = _messages.MessageField('TagsValue', 7)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TimeOfDay(_messages.Message):
  r"""Represents a time of day. The date and time zone are either not
  significant or are specified elsewhere. An API may choose to allow leap
  seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.

  Fields:
    hours: Hours of a day in 24 hour format. Must be greater than or equal to
      0 and typically must be less than or equal to 23. An API may choose to
      allow the value "24:00:00" for scenarios like business closing time.
    minutes: Minutes of an hour. Must be greater than or equal to 0 and less
      than or equal to 59.
    nanos: Fractions of seconds, in nanoseconds. Must be greater than or equal
      to 0 and less than or equal to 999,999,999.
    seconds: Seconds of a minute. Must be greater than or equal to 0 and
      typically must be less than or equal to 59. An API may allow the value
      60 if it allows leap-seconds.
  """

  hours = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minutes = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  nanos = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  seconds = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class UpdatePolicy(_messages.Message):
  r"""Maintenance policy applicable to instance updates.

  Enums:
    ChannelValueValuesEnum: Optional. Relative scheduling channel applied to
      resource.

  Fields:
    channel: Optional. Relative scheduling channel applied to resource.
    denyMaintenancePeriods: Deny Maintenance Period that is applied to
      resource to indicate when maintenance is forbidden. The protocol
      supports zero-to-many such periods, but the current SLM Rollout
      implementation only supports zero-to-one.
    window: Optional. Maintenance window that is applied to resources covered
      by this policy.
  """

  class ChannelValueValuesEnum(_messages.Enum):
    r"""Optional. Relative scheduling channel applied to resource.

    Values:
      UPDATE_CHANNEL_UNSPECIFIED: Unspecified channel.
      EARLIER: Early channel within a customer project.
      LATER: Later channel within a customer project.
      WEEK1: ! ! The follow channels can ONLY be used if you adopt the new MW
        system! ! ! NOTE: all WEEK channels are assumed to be under a weekly
        window. ! There is currently no dedicated channel definitions for
        Daily windows. ! If you use Daily window, the system will assume a 1d
        (24Hours) advanced ! notification period b/w EARLY and LATER. ! We may
        consider support more flexible daily channel specifications in ! the
        future. WEEK1 == EARLIER with minimum 7d advanced notification. {7d,
        14d} The system will treat them equally and will use WEEK1 whenever it
        can. New customers are encouraged to use this channel annotation.
      WEEK2: WEEK2 == LATER with minimum 14d advanced notification {14d, 21d}.
      WEEK5: WEEK5 == 40d support. minimum 35d advanced notification {35d,
        42d}.
    """
    UPDATE_CHANNEL_UNSPECIFIED = 0
    EARLIER = 1
    LATER = 2
    WEEK1 = 3
    WEEK2 = 4
    WEEK5 = 5

  channel = _messages.EnumField('ChannelValueValuesEnum', 1)
  denyMaintenancePeriods = _messages.MessageField('DenyMaintenancePeriod', 2, repeated=True)
  window = _messages.MessageField('MaintenanceWindow', 3)


class WeeklyCycle(_messages.Message):
  r"""Time window specified for weekly operations.

  Fields:
    schedule: User can specify multiple windows in a week. Minimum of 1
      window.
  """

  schedule = _messages.MessageField('Schedule', 1, repeated=True)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
