"""Generated message classes for workstations version v1.

Allows administrators to create managed developer environments in the cloud.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'workstations'


class Accelerator(_messages.Message):
  r"""An accelerator card attached to the instance.

  Fields:
    count: Optional. Number of accelerator cards exposed to the instance.
    type: Optional. Type of accelerator resource to attach to the instance,
      for example, `"nvidia-tesla-p100"`.
  """

  count = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  type = _messages.StringField(2)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class BoostConfig(_messages.Message):
  r"""A boost configuration is a set of resources that a workstation can use
  to increase its performance. If you specify a boost configuration, upon
  startup, workstation users can choose to use a VM provisioned under the
  boost config by passing the boost config ID in the start request. If the
  workstation user does not provide a boost config ID in the start request,
  the system will choose a VM from the pool provisioned under the default
  config.

  Fields:
    accelerators: Optional. A list of the type and count of accelerator cards
      attached to the boost instance. Defaults to `none`.
    bootDiskSizeGb: Optional. The size of the boot disk for the VM in
      gigabytes (GB). The minimum boot disk size is `30` GB. Defaults to `50`
      GB.
    enableNestedVirtualization: Optional. Whether to enable nested
      virtualization on boosted Cloud Workstations VMs running using this
      boost configuration. Defaults to false. Nested virtualization lets you
      run virtual machine (VM) instances inside your workstation. Before
      enabling nested virtualization, consider the following important
      considerations. Cloud Workstations instances are subject to the [same
      restrictions as Compute Engine
      instances](https://cloud.google.com/compute/docs/instances/nested-
      virtualization/overview#restrictions): * **Organization policy**:
      projects, folders, or organizations may be restricted from creating
      nested VMs if the **Disable VM nested virtualization** constraint is
      enforced in the organization policy. For more information, see the
      Compute Engine section, [Checking whether nested virtualization is
      allowed](https://cloud.google.com/compute/docs/instances/nested-
      virtualization/managing-
      constraint#checking_whether_nested_virtualization_is_allowed). *
      **Performance**: nested VMs might experience a 10% or greater decrease
      in performance for workloads that are CPU-bound and possibly greater
      than a 10% decrease for workloads that are input/output bound. *
      **Machine Type**: nested virtualization can only be enabled on boost
      configurations that specify a machine_type in the N1 or N2 machine
      series.
    id: Required. The ID to be used for the boost configuration.
    machineType: Optional. The type of machine that boosted VM instances will
      use-for example, `e2-standard-4`. For more information about machine
      types that Cloud Workstations supports, see the list of [available
      machine types](https://cloud.google.com/workstations/docs/available-
      machine-types). Defaults to `e2-standard-4`.
    poolSize: Optional. The number of boost VMs that the system should keep
      idle so that workstations can be boosted quickly. Defaults to `0`.
  """

  accelerators = _messages.MessageField('Accelerator', 1, repeated=True)
  bootDiskSizeGb = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  enableNestedVirtualization = _messages.BooleanField(3)
  id = _messages.StringField(4)
  machineType = _messages.StringField(5)
  poolSize = _messages.IntegerField(6, variant=_messages.Variant.INT32)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class Container(_messages.Message):
  r"""A Docker container.

  Messages:
    EnvValue: Optional. Environment variables passed to the container's
      entrypoint.

  Fields:
    args: Optional. Arguments passed to the entrypoint.
    command: Optional. If set, overrides the default ENTRYPOINT specified by
      the image.
    env: Optional. Environment variables passed to the container's entrypoint.
    image: Optional. A Docker container image that defines a custom
      environment. Cloud Workstations provides a number of [preconfigured
      images](https://cloud.google.com/workstations/docs/preconfigured-base-
      images), but you can create your own [custom container
      images](https://cloud.google.com/workstations/docs/custom-container-
      images). If using a private image, the `host.gceInstance.serviceAccount`
      field must be specified in the workstation configuration. If using a
      custom container image, the service account must have [Artifact Registry
      Reader](https://cloud.google.com/artifact-registry/docs/access-
      control#roles) permission to pull the specified image. Otherwise, the
      image must be publicly accessible.
    runAsUser: Optional. If set, overrides the USER specified in the image
      with the given uid.
    workingDir: Optional. If set, overrides the default DIR specified by the
      image.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvValue(_messages.Message):
    r"""Optional. Environment variables passed to the container's entrypoint.

    Messages:
      AdditionalProperty: An additional property for a EnvValue object.

    Fields:
      additionalProperties: Additional properties of type EnvValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  args = _messages.StringField(1, repeated=True)
  command = _messages.StringField(2, repeated=True)
  env = _messages.MessageField('EnvValue', 3)
  image = _messages.StringField(4)
  runAsUser = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  workingDir = _messages.StringField(6)


class CustomerEncryptionKey(_messages.Message):
  r"""A customer-managed encryption key (CMEK) for the Compute Engine
  resources of the associated workstation configuration. Specify the name of
  your Cloud KMS encryption key and the default service account. We recommend
  that you use a separate service account and follow [Cloud KMS best
  practices](https://cloud.google.com/kms/docs/separation-of-duties).

  Fields:
    kmsKey: Immutable. The name of the Google Cloud KMS encryption key. For
      example, `"projects/PROJECT_ID/locations/REGION/keyRings/KEY_RING/crypto
      Keys/KEY_NAME"`. The key must be in the same region as the workstation
      configuration.
    kmsKeyServiceAccount: Immutable. The service account to use with the
      specified KMS key. We recommend that you use a separate service account
      and follow KMS best practices. For more information, see [Separation of
      duties](https://cloud.google.com/kms/docs/separation-of-duties) and
      `gcloud kms keys add-iam-policy-binding`
      [`--member`](https://cloud.google.com/sdk/gcloud/reference/kms/keys/add-
      iam-policy-binding#--member).
  """

  kmsKey = _messages.StringField(1)
  kmsKeyServiceAccount = _messages.StringField(2)


class DomainConfig(_messages.Message):
  r"""Configuration options for a custom domain.

  Fields:
    domain: Immutable. Domain used by Workstations for HTTP ingress.
  """

  domain = _messages.StringField(1)


class EphemeralDirectory(_messages.Message):
  r"""An ephemeral directory which won't persist across workstation sessions.
  It is freshly created on every workstation start operation.

  Fields:
    gcePd: An EphemeralDirectory backed by a Compute Engine persistent disk.
    mountPath: Required. Location of this directory in the running
      workstation.
  """

  gcePd = _messages.MessageField('GcePersistentDisk', 1)
  mountPath = _messages.StringField(2)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GatewayConfig(_messages.Message):
  r"""Configuration options for Cluster HTTP Gateway.

  Fields:
    http2Enabled: Optional. Whether HTTP/2 is enabled for this workstation
      cluster. Defaults to false.
  """

  http2Enabled = _messages.BooleanField(1)


class GceConfidentialInstanceConfig(_messages.Message):
  r"""A set of Compute Engine Confidential VM instance options.

  Fields:
    enableConfidentialCompute: Optional. Whether the instance has confidential
      compute enabled.
  """

  enableConfidentialCompute = _messages.BooleanField(1)


class GceInstance(_messages.Message):
  r"""A runtime using a Compute Engine instance.

  Messages:
    VmTagsValue: Optional. Resource manager tags to be bound to this instance.
      Tag keys and values have the same definition as [resource manager
      tags](https://cloud.google.com/resource-manager/docs/tags/tags-
      overview). Keys must be in the format `tagKeys/{tag_key_id}`, and values
      are in the format `tagValues/456`.

  Fields:
    accelerators: Optional. A list of the type and count of accelerator cards
      attached to the instance.
    boostConfigs: Optional. A list of the boost configurations that
      workstations created using this workstation configuration are allowed to
      use. If specified, users will have the option to choose from the list of
      boost configs when starting a workstation.
    bootDiskSizeGb: Optional. The size of the boot disk for the VM in
      gigabytes (GB). The minimum boot disk size is `30` GB. Defaults to `50`
      GB.
    confidentialInstanceConfig: Optional. A set of Compute Engine Confidential
      VM instance options.
    disablePublicIpAddresses: Optional. When set to true, disables public IP
      addresses for VMs. If you disable public IP addresses, you must set up
      Private Google Access or Cloud NAT on your network. If you use Private
      Google Access and you use `private.googleapis.com` or
      `restricted.googleapis.com` for Container Registry and Artifact
      Registry, make sure that you set up DNS records for domains `*.gcr.io`
      and `*.pkg.dev`. Defaults to false (VMs have public IP addresses).
    disableSsh: Optional. Whether to disable SSH access to the VM.
    enableNestedVirtualization: Optional. Whether to enable nested
      virtualization on Cloud Workstations VMs created using this workstation
      configuration. Defaults to false. Nested virtualization lets you run
      virtual machine (VM) instances inside your workstation. Before enabling
      nested virtualization, consider the following important considerations.
      Cloud Workstations instances are subject to the [same restrictions as
      Compute Engine
      instances](https://cloud.google.com/compute/docs/instances/nested-
      virtualization/overview#restrictions): * **Organization policy**:
      projects, folders, or organizations may be restricted from creating
      nested VMs if the **Disable VM nested virtualization** constraint is
      enforced in the organization policy. For more information, see the
      Compute Engine section, [Checking whether nested virtualization is
      allowed](https://cloud.google.com/compute/docs/instances/nested-
      virtualization/managing-
      constraint#checking_whether_nested_virtualization_is_allowed). *
      **Performance**: nested VMs might experience a 10% or greater decrease
      in performance for workloads that are CPU-bound and possibly greater
      than a 10% decrease for workloads that are input/output bound. *
      **Machine Type**: nested virtualization can only be enabled on
      workstation configurations that specify a machine_type in the N1 or N2
      machine series.
    machineType: Optional. The type of machine to use for VM instances-for
      example, `"e2-standard-4"`. For more information about machine types
      that Cloud Workstations supports, see the list of [available machine
      types](https://cloud.google.com/workstations/docs/available-machine-
      types).
    poolSize: Optional. The number of VMs that the system should keep idle so
      that new workstations can be started quickly for new users. Defaults to
      `0` in the API.
    pooledInstances: Output only. Number of instances currently available in
      the pool for faster workstation startup.
    serviceAccount: Optional. The email address of the service account for
      Cloud Workstations VMs created with this configuration. When specified,
      be sure that the service account has `logging.logEntries.create` and
      `monitoring.timeSeries.create` permissions on the project so it can
      write logs out to Cloud Logging. If using a custom container image, the
      service account must have [Artifact Registry
      Reader](https://cloud.google.com/artifact-registry/docs/access-
      control#roles) permission to pull the specified image. If you as the
      administrator want to be able to `ssh` into the underlying VM, you need
      to set this value to a service account for which you have the
      `iam.serviceAccounts.actAs` permission. Conversely, if you don't want
      anyone to be able to `ssh` into the underlying VM, use a service account
      where no one has that permission. If not set, VMs run with a service
      account provided by the Cloud Workstations service, and the image must
      be publicly accessible.
    serviceAccountScopes: Optional. Scopes to grant to the service_account.
      When specified, users of workstations under this configuration must have
      `iam.serviceAccounts.actAs` on the service account.
    shieldedInstanceConfig: Optional. A set of Compute Engine Shielded
      instance options.
    tags: Optional. Network tags to add to the Compute Engine VMs backing the
      workstations. This option applies [network
      tags](https://cloud.google.com/vpc/docs/add-remove-network-tags) to VMs
      created with this configuration. These network tags enable the creation
      of [firewall
      rules](https://cloud.google.com/workstations/docs/configure-firewall-
      rules).
    vmTags: Optional. Resource manager tags to be bound to this instance. Tag
      keys and values have the same definition as [resource manager
      tags](https://cloud.google.com/resource-manager/docs/tags/tags-
      overview). Keys must be in the format `tagKeys/{tag_key_id}`, and values
      are in the format `tagValues/456`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class VmTagsValue(_messages.Message):
    r"""Optional. Resource manager tags to be bound to this instance. Tag keys
    and values have the same definition as [resource manager
    tags](https://cloud.google.com/resource-manager/docs/tags/tags-overview).
    Keys must be in the format `tagKeys/{tag_key_id}`, and values are in the
    format `tagValues/456`.

    Messages:
      AdditionalProperty: An additional property for a VmTagsValue object.

    Fields:
      additionalProperties: Additional properties of type VmTagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a VmTagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  accelerators = _messages.MessageField('Accelerator', 1, repeated=True)
  boostConfigs = _messages.MessageField('BoostConfig', 2, repeated=True)
  bootDiskSizeGb = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  confidentialInstanceConfig = _messages.MessageField('GceConfidentialInstanceConfig', 4)
  disablePublicIpAddresses = _messages.BooleanField(5)
  disableSsh = _messages.BooleanField(6)
  enableNestedVirtualization = _messages.BooleanField(7)
  machineType = _messages.StringField(8)
  poolSize = _messages.IntegerField(9, variant=_messages.Variant.INT32)
  pooledInstances = _messages.IntegerField(10, variant=_messages.Variant.INT32)
  serviceAccount = _messages.StringField(11)
  serviceAccountScopes = _messages.StringField(12, repeated=True)
  shieldedInstanceConfig = _messages.MessageField('GceShieldedInstanceConfig', 13)
  tags = _messages.StringField(14, repeated=True)
  vmTags = _messages.MessageField('VmTagsValue', 15)


class GceInstanceHost(_messages.Message):
  r"""The Compute Engine instance host.

  Fields:
    id: Optional. Output only. The ID of the Compute Engine instance.
    name: Optional. Output only. The name of the Compute Engine instance.
    zone: Optional. Output only. The zone of the Compute Engine instance.
  """

  id = _messages.StringField(1)
  name = _messages.StringField(2)
  zone = _messages.StringField(3)


class GcePersistentDisk(_messages.Message):
  r"""An EphemeralDirectory is backed by a Compute Engine persistent disk.

  Fields:
    diskType: Optional. Type of the disk to use. Defaults to `"pd-standard"`.
    readOnly: Optional. Whether the disk is read only. If true, the disk may
      be shared by multiple VMs and source_snapshot must be set.
    sourceImage: Optional. Name of the disk image to use as the source for the
      disk. Must be empty if source_snapshot is set. Updating source_image
      will update content in the ephemeral directory after the workstation is
      restarted. Only file systems supported by Container-Optimized OS (COS)
      are explicitly supported. For a list of supported file systems, please
      refer to the [COS documentation](https://cloud.google.com/container-
      optimized-os/docs/concepts/supported-filesystems). This field is
      mutable.
    sourceSnapshot: Optional. Name of the snapshot to use as the source for
      the disk. Must be empty if source_image is set. Must be empty if
      read_only is false. Updating source_snapshot will update content in the
      ephemeral directory after the workstation is restarted. Only file
      systems supported by Container-Optimized OS (COS) are explicitly
      supported. For a list of supported file systems, see [the filesystems
      available in Container-Optimized OS](https://cloud.google.com/container-
      optimized-os/docs/concepts/supported-filesystems). This field is
      mutable.
  """

  diskType = _messages.StringField(1)
  readOnly = _messages.BooleanField(2)
  sourceImage = _messages.StringField(3)
  sourceSnapshot = _messages.StringField(4)


class GceRegionalPersistentDisk(_messages.Message):
  r"""A Persistent Directory backed by a Compute Engine regional persistent
  disk. The persistent_directories field is repeated, but it may contain only
  one entry. It creates a [persistent
  disk](https://cloud.google.com/compute/docs/disks/persistent-disks) that
  mounts to the workstation VM at `/home` when the session starts and detaches
  when the session ends. If this field is empty, workstations created with
  this configuration do not have a persistent home directory.

  Enums:
    ReclaimPolicyValueValuesEnum: Optional. Whether the persistent disk should
      be deleted when the workstation is deleted. Valid values are `DELETE`
      and `RETAIN`. Defaults to `DELETE`.

  Fields:
    diskType: Optional. The [type of the persistent
      disk](https://cloud.google.com/compute/docs/disks#disk-types) for the
      home directory. Defaults to `"pd-standard"`.
    fsType: Optional. Type of file system that the disk should be formatted
      with. The workstation image must support this file system type. Must be
      empty if source_snapshot is set. Defaults to `"ext4"`.
    reclaimPolicy: Optional. Whether the persistent disk should be deleted
      when the workstation is deleted. Valid values are `DELETE` and `RETAIN`.
      Defaults to `DELETE`.
    sizeGb: Optional. The GB capacity of a persistent home directory for each
      workstation created with this configuration. Must be empty if
      source_snapshot is set. Valid values are `10`, `50`, `100`, `200`,
      `500`, or `1000`. Defaults to `200`. If less than `200` GB, the
      disk_type must be `"pd-balanced"` or `"pd-ssd"`.
    sourceSnapshot: Optional. Name of the snapshot to use as the source for
      the disk. If set, size_gb and fs_type must be empty. Must be formatted
      as ext4 file system with no partitions.
  """

  class ReclaimPolicyValueValuesEnum(_messages.Enum):
    r"""Optional. Whether the persistent disk should be deleted when the
    workstation is deleted. Valid values are `DELETE` and `RETAIN`. Defaults
    to `DELETE`.

    Values:
      RECLAIM_POLICY_UNSPECIFIED: Do not use.
      DELETE: Delete the persistent disk when deleting the workstation.
      RETAIN: Keep the persistent disk when deleting the workstation. An
        administrator must manually delete the disk.
    """
    RECLAIM_POLICY_UNSPECIFIED = 0
    DELETE = 1
    RETAIN = 2

  diskType = _messages.StringField(1)
  fsType = _messages.StringField(2)
  reclaimPolicy = _messages.EnumField('ReclaimPolicyValueValuesEnum', 3)
  sizeGb = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  sourceSnapshot = _messages.StringField(5)


class GceShieldedInstanceConfig(_messages.Message):
  r"""A set of Compute Engine Shielded instance options.

  Fields:
    enableIntegrityMonitoring: Optional. Whether the instance has integrity
      monitoring enabled.
    enableSecureBoot: Optional. Whether the instance has Secure Boot enabled.
    enableVtpm: Optional. Whether the instance has the vTPM enabled.
  """

  enableIntegrityMonitoring = _messages.BooleanField(1)
  enableSecureBoot = _messages.BooleanField(2)
  enableVtpm = _messages.BooleanField(3)


class GenerateAccessTokenRequest(_messages.Message):
  r"""Request message for GenerateAccessToken.

  Fields:
    expireTime: Desired expiration time of the access token. This value must
      be at most 24 hours in the future. If a value is not specified, the
      token's expiration time will be set to a default value of 1 hour in the
      future.
    port: Optional. Port for which the access token should be generated. If
      specified, the generated access token grants access only to the
      specified port of the workstation. If specified, values must be within
      the range [1 - 65535]. If not specified, the generated access token
      grants access to all ports of the workstation.
    ttl: Desired lifetime duration of the access token. This value must be at
      most 24 hours. If a value is not specified, the token's lifetime will be
      set to a default value of 1 hour.
  """

  expireTime = _messages.StringField(1)
  port = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  ttl = _messages.StringField(3)


class GenerateAccessTokenResponse(_messages.Message):
  r"""Response message for GenerateAccessToken.

  Fields:
    accessToken: The generated bearer access token. To use this token, include
      it in an Authorization header of an HTTP request sent to the associated
      workstation's hostname-for example, `Authorization: Bearer `.
    expireTime: Time at which the generated token will expire.
  """

  accessToken = _messages.StringField(1)
  expireTime = _messages.StringField(2)


class GoogleProtobufEmpty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Host(_messages.Message):
  r"""Runtime host for a workstation.

  Fields:
    gceInstance: Specifies a Compute Engine instance as the host.
  """

  gceInstance = _messages.MessageField('GceInstance', 1)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListUsableWorkstationConfigsResponse(_messages.Message):
  r"""Response message for ListUsableWorkstationConfigs.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: Unreachable resources.
    workstationConfigs: The requested configs.
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  workstationConfigs = _messages.MessageField('WorkstationConfig', 3, repeated=True)


class ListUsableWorkstationsResponse(_messages.Message):
  r"""Response message for ListUsableWorkstations.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: Unreachable resources.
    workstations: The requested workstations.
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  workstations = _messages.MessageField('Workstation', 3, repeated=True)


class ListWorkstationClustersResponse(_messages.Message):
  r"""Response message for ListWorkstationClusters.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: Unreachable resources.
    workstationClusters: The requested workstation clusters.
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  workstationClusters = _messages.MessageField('WorkstationCluster', 3, repeated=True)


class ListWorkstationConfigsResponse(_messages.Message):
  r"""Response message for ListWorkstationConfigs.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: Unreachable resources.
    workstationConfigs: The requested configs.
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  workstationConfigs = _messages.MessageField('WorkstationConfig', 3, repeated=True)


class ListWorkstationsResponse(_messages.Message):
  r"""Response message for ListWorkstations.

  Fields:
    nextPageToken: Optional. Token to retrieve the next page of results, or
      empty if there are no more results in the list.
    unreachable: Optional. Unreachable resources.
    workstations: The requested workstations.
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  workstations = _messages.MessageField('Workstation', 3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Metadata for long-running operations.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. Time that the operation was created.
    endTime: Output only. Time that the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class PersistentDirectory(_messages.Message):
  r"""A directory to persist across workstation sessions. Updates to this
  field will not update existing workstations and will only take effect on new
  workstations.

  Fields:
    gcePd: A PersistentDirectory backed by a Compute Engine persistent disk.
    mountPath: Optional. Location of this directory in the running
      workstation.
  """

  gcePd = _messages.MessageField('GceRegionalPersistentDisk', 1)
  mountPath = _messages.StringField(2)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class PortRange(_messages.Message):
  r"""A PortRange defines a range of ports. Both first and last are inclusive.
  To specify a single port, both first and last should be the same.

  Fields:
    first: Required. Starting port number for the current range of ports.
      Valid ports are 22, 80, and ports within the range 1024-65535.
    last: Required. Ending port number for the current range of ports. Valid
      ports are 22, 80, and ports within the range 1024-65535.
  """

  first = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  last = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class PrivateClusterConfig(_messages.Message):
  r"""Configuration options for private workstation clusters.

  Fields:
    allowedProjects: Optional. Additional projects that are allowed to attach
      to the workstation cluster's service attachment. By default, the
      workstation cluster's project and the VPC host project (if different)
      are allowed.
    clusterHostname: Output only. Hostname for the workstation cluster. This
      field will be populated only when private endpoint is enabled. To access
      workstations in the workstation cluster, create a new DNS zone mapping
      this domain name to an internal IP address and a forwarding rule mapping
      that address to the service attachment.
    enablePrivateEndpoint: Immutable. Whether Workstations endpoint is
      private.
    serviceAttachmentUri: Output only. Service attachment URI for the
      workstation cluster. The service attachment is created when private
      endpoint is enabled. To access workstations in the workstation cluster,
      configure access to the managed service using [Private Service
      Connect](https://cloud.google.com/vpc/docs/configure-private-service-
      connect-services).
  """

  allowedProjects = _messages.StringField(1, repeated=True)
  clusterHostname = _messages.StringField(2)
  enablePrivateEndpoint = _messages.BooleanField(3)
  serviceAttachmentUri = _messages.StringField(4)


class ReadinessCheck(_messages.Message):
  r"""A readiness check to be performed on a workstation.

  Fields:
    path: Optional. Path to which the request should be sent.
    port: Optional. Port to which the request should be sent.
  """

  path = _messages.StringField(1)
  port = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class RuntimeHost(_messages.Message):
  r"""Runtime host for the workstation.

  Fields:
    gceInstanceHost: Specifies a Compute Engine instance as the host.
  """

  gceInstanceHost = _messages.MessageField('GceInstanceHost', 1)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StartWorkstationRequest(_messages.Message):
  r"""Request message for StartWorkstation.

  Fields:
    boostConfig: Optional. If set, the workstation starts using the boost
      configuration with the specified ID.
    etag: Optional. If set, the request will be rejected if the latest version
      of the workstation on the server does not have this ETag.
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not actually apply it.
  """

  boostConfig = _messages.StringField(1)
  etag = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StopWorkstationRequest(_messages.Message):
  r"""Request message for StopWorkstation.

  Fields:
    etag: Optional. If set, the request will be rejected if the latest version
      of the workstation on the server does not have this ETag.
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not actually apply it.
  """

  etag = _messages.StringField(1)
  validateOnly = _messages.BooleanField(2)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class Workstation(_messages.Message):
  r"""A single instance of a developer workstation with its own persistent
  storage.

  Enums:
    StateValueValuesEnum: Output only. Current state of the workstation.

  Messages:
    AnnotationsValue: Optional. Client-specified annotations.
    EnvValue: Optional. Environment variables passed to the workstation
      container's entrypoint.
    LabelsValue: Optional.
      [Labels](https://cloud.google.com/workstations/docs/label-resources)
      that are applied to the workstation and that are also propagated to the
      underlying Compute Engine resources.

  Fields:
    annotations: Optional. Client-specified annotations.
    createTime: Output only. Time when this workstation was created.
    deleteTime: Output only. Time when this workstation was soft-deleted.
    displayName: Optional. Human-readable name for this workstation.
    env: Optional. Environment variables passed to the workstation container's
      entrypoint.
    etag: Optional. Checksum computed by the server. May be sent on update and
      delete requests to make sure that the client has an up-to-date value
      before proceeding.
    host: Output only. Host to which clients can send HTTPS traffic that will
      be received by the workstation. Authorized traffic will be received to
      the workstation as HTTP on port 80. To send traffic to a different port,
      clients may prefix the host with the destination port in the format
      `{port}-{host}`.
    kmsKey: Output only. The name of the Google Cloud KMS encryption key used
      to encrypt this workstation. The KMS key can only be configured in the
      WorkstationConfig. The expected format is
      `projects/*/locations/*/keyRings/*/cryptoKeys/*`.
    labels: Optional.
      [Labels](https://cloud.google.com/workstations/docs/label-resources)
      that are applied to the workstation and that are also propagated to the
      underlying Compute Engine resources.
    name: Identifier. Full name of this workstation.
    reconciling: Output only. Indicates whether this workstation is currently
      being updated to match its intended state.
    runtimeHost: Optional. Output only. Runtime host for the workstation when
      in STATE_RUNNING.
    sourceWorkstation: Optional. The source workstation from which this
      workstation's persistent directories were cloned on creation.
    startTime: Output only. Time when this workstation was most recently
      successfully started, regardless of the workstation's initial state.
    state: Output only. Current state of the workstation.
    uid: Output only. A system-assigned unique identifier for this
      workstation.
    updateTime: Output only. Time when this workstation was most recently
      updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the workstation.

    Values:
      STATE_UNSPECIFIED: Do not use.
      STATE_STARTING: The workstation is not yet ready to accept requests from
        users but will be soon.
      STATE_RUNNING: The workstation is ready to accept requests from users.
      STATE_STOPPING: The workstation is being stopped.
      STATE_STOPPED: The workstation is stopped and will not be able to
        receive requests until it is started.
    """
    STATE_UNSPECIFIED = 0
    STATE_STARTING = 1
    STATE_RUNNING = 2
    STATE_STOPPING = 3
    STATE_STOPPED = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Client-specified annotations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvValue(_messages.Message):
    r"""Optional. Environment variables passed to the workstation container's
    entrypoint.

    Messages:
      AdditionalProperty: An additional property for a EnvValue object.

    Fields:
      additionalProperties: Additional properties of type EnvValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. [Labels](https://cloud.google.com/workstations/docs/label-
    resources) that are applied to the workstation and that are also
    propagated to the underlying Compute Engine resources.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  deleteTime = _messages.StringField(3)
  displayName = _messages.StringField(4)
  env = _messages.MessageField('EnvValue', 5)
  etag = _messages.StringField(6)
  host = _messages.StringField(7)
  kmsKey = _messages.StringField(8)
  labels = _messages.MessageField('LabelsValue', 9)
  name = _messages.StringField(10)
  reconciling = _messages.BooleanField(11)
  runtimeHost = _messages.MessageField('RuntimeHost', 12)
  sourceWorkstation = _messages.StringField(13)
  startTime = _messages.StringField(14)
  state = _messages.EnumField('StateValueValuesEnum', 15)
  uid = _messages.StringField(16)
  updateTime = _messages.StringField(17)


class WorkstationCluster(_messages.Message):
  r"""A workstation cluster resource in the Cloud Workstations API. Defines a
  group of workstations in a particular region and the VPC network they're
  attached to.

  Messages:
    AnnotationsValue: Optional. Client-specified annotations.
    LabelsValue: Optional.
      [Labels](https://cloud.google.com/workstations/docs/label-resources)
      that are applied to the workstation cluster and that are also propagated
      to the underlying Compute Engine resources.
    TagsValue: Optional. Input only. Immutable. Tag keys/values directly bound
      to this resource. For example: "123/environment": "production",
      "123/costCenter": "marketing"

  Fields:
    annotations: Optional. Client-specified annotations.
    conditions: Output only. Status conditions describing the workstation
      cluster's current state.
    controlPlaneIp: Output only. The private IP address of the control plane
      for this workstation cluster. Workstation VMs need access to this IP
      address to work with the service, so make sure that your firewall rules
      allow egress from the workstation VMs to this address.
    createTime: Output only. Time when this workstation cluster was created.
    degraded: Output only. Whether this workstation cluster is in degraded
      mode, in which case it may require user action to restore full
      functionality. The conditions field contains detailed information about
      the status of the cluster.
    deleteTime: Output only. Time when this workstation cluster was soft-
      deleted.
    displayName: Optional. Human-readable name for this workstation cluster.
    domainConfig: Optional. Configuration options for a custom domain.
    etag: Optional. Checksum computed by the server. May be sent on update and
      delete requests to make sure that the client has an up-to-date value
      before proceeding.
    gatewayConfig: Optional. Configuration options for Cluster HTTP Gateway.
    labels: Optional.
      [Labels](https://cloud.google.com/workstations/docs/label-resources)
      that are applied to the workstation cluster and that are also propagated
      to the underlying Compute Engine resources.
    name: Identifier. Full name of this workstation cluster.
    network: Immutable. Name of the Compute Engine network in which instances
      associated with this workstation cluster will be created.
    privateClusterConfig: Optional. Configuration for private workstation
      cluster.
    reconciling: Output only. Indicates whether this workstation cluster is
      currently being updated to match its intended state.
    subnetwork: Immutable. Name of the Compute Engine subnetwork in which
      instances associated with this workstation cluster will be created. Must
      be part of the subnetwork specified for this workstation cluster.
    tags: Optional. Input only. Immutable. Tag keys/values directly bound to
      this resource. For example: "123/environment": "production",
      "123/costCenter": "marketing"
    uid: Output only. A system-assigned unique identifier for this workstation
      cluster.
    updateTime: Output only. Time when this workstation cluster was most
      recently updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Client-specified annotations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. [Labels](https://cloud.google.com/workstations/docs/label-
    resources) that are applied to the workstation cluster and that are also
    propagated to the underlying Compute Engine resources.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""Optional. Input only. Immutable. Tag keys/values directly bound to
    this resource. For example: "123/environment": "production",
    "123/costCenter": "marketing"

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  conditions = _messages.MessageField('Status', 2, repeated=True)
  controlPlaneIp = _messages.StringField(3)
  createTime = _messages.StringField(4)
  degraded = _messages.BooleanField(5)
  deleteTime = _messages.StringField(6)
  displayName = _messages.StringField(7)
  domainConfig = _messages.MessageField('DomainConfig', 8)
  etag = _messages.StringField(9)
  gatewayConfig = _messages.MessageField('GatewayConfig', 10)
  labels = _messages.MessageField('LabelsValue', 11)
  name = _messages.StringField(12)
  network = _messages.StringField(13)
  privateClusterConfig = _messages.MessageField('PrivateClusterConfig', 14)
  reconciling = _messages.BooleanField(15)
  subnetwork = _messages.StringField(16)
  tags = _messages.MessageField('TagsValue', 17)
  uid = _messages.StringField(18)
  updateTime = _messages.StringField(19)


class WorkstationConfig(_messages.Message):
  r"""A workstation configuration resource in the Cloud Workstations API.
  Workstation configurations act as templates for workstations. The
  workstation configuration defines details such as the workstation virtual
  machine (VM) instance type, persistent storage, container image defining
  environment, which IDE or Code Editor to use, and more. Administrators and
  platform teams can also use [Identity and Access Management
  (IAM)](https://cloud.google.com/iam/docs/overview) rules to grant access to
  teams or to individual developers.

  Messages:
    AnnotationsValue: Optional. Client-specified annotations.
    LabelsValue: Optional.
      [Labels](https://cloud.google.com/workstations/docs/label-resources)
      that are applied to the workstation configuration and that are also
      propagated to the underlying Compute Engine resources.

  Fields:
    allowedPorts: Optional. A list of PortRanges specifying single ports or
      ranges of ports that are externally accessible in the workstation.
      Allowed ports must be one of 22, 80, or within range 1024-65535. If not
      specified defaults to ports 22, 80, and ports 1024-65535.
    annotations: Optional. Client-specified annotations.
    conditions: Output only. Status conditions describing the workstation
      configuration's current state.
    container: Optional. Container that runs upon startup for each workstation
      using this workstation configuration.
    createTime: Output only. Time when this workstation configuration was
      created.
    degraded: Output only. Whether this workstation configuration is in
      degraded mode, in which case it may require user action to restore full
      functionality. The conditions field contains detailed information about
      the status of the configuration.
    deleteTime: Output only. Time when this workstation configuration was
      soft-deleted.
    disableTcpConnections: Optional. Disables support for plain TCP
      connections in the workstation. By default the service supports TCP
      connections through a websocket relay. Setting this option to true
      disables that relay, which prevents the usage of services that require
      plain TCP connections, such as SSH. When enabled, all communication must
      occur over HTTPS or WSS.
    displayName: Optional. Human-readable name for this workstation
      configuration.
    enableAuditAgent: Optional. Whether to enable Linux `auditd` logging on
      the workstation. When enabled, a service_account must also be specified
      that has `roles/logging.logWriter` and `roles/monitoring.metricWriter`
      on the project. Operating system audit logging is distinct from [Cloud
      Audit Logs](https://cloud.google.com/workstations/docs/audit-logging)
      and [Container output
      logging](https://cloud.google.com/workstations/docs/container-output-
      logging#overview). Operating system audit logs are available in the
      [Cloud Logging](https://cloud.google.com/logging/docs) console by
      querying: resource.type="gce_instance" log_name:"/logs/linux-auditd"
    encryptionKey: Immutable. Encrypts resources of this workstation
      configuration using a customer-managed encryption key (CMEK). If
      specified, the boot disk of the Compute Engine instance and the
      persistent disk are encrypted using this encryption key. If this field
      is not set, the disks are encrypted using a generated key. Customer-
      managed encryption keys do not protect disk metadata. If the customer-
      managed encryption key is rotated, when the workstation instance is
      stopped, the system attempts to recreate the persistent disk with the
      new version of the key. Be sure to keep older versions of the key until
      the persistent disk is recreated. Otherwise, data on the persistent disk
      might be lost. If the encryption key is revoked, the workstation session
      automatically stops within 7 hours. Immutable after the workstation
      configuration is created.
    ephemeralDirectories: Optional. Ephemeral directories which won't persist
      across workstation sessions.
    etag: Optional. Checksum computed by the server. May be sent on update and
      delete requests to make sure that the client has an up-to-date value
      before proceeding.
    grantWorkstationAdminRoleOnCreate: Optional. Grant creator of a
      workstation `roles/workstations.policyAdmin` role along with
      `roles/workstations.user` role on the workstation created by them. This
      allows workstation users to share access to either their entire
      workstation, or individual ports. Defaults to false.
    host: Optional. Runtime host for the workstation.
    idleTimeout: Optional. Number of seconds to wait before automatically
      stopping a workstation after it last received user traffic. A value of
      `"0s"` indicates that Cloud Workstations VMs created with this
      configuration should never time out due to idleness. Provide
      [duration](https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#duration) terminated by `s` for
      seconds-for example, `"7200s"` (2 hours). The default is `"1200s"` (20
      minutes).
    labels: Optional.
      [Labels](https://cloud.google.com/workstations/docs/label-resources)
      that are applied to the workstation configuration and that are also
      propagated to the underlying Compute Engine resources.
    maxUsableWorkstations: Optional. Maximum number of workstations under this
      configuration a user can have `workstations.workstation.use` permission
      on. Only enforced on CreateWorkstation API calls on the user issuing the
      API request. Can be overridden by: - granting a user
      workstations.workstationConfigs.exemptMaxUsableWorkstationLimit
      permission, or - having a user with that permission create a workstation
      and granting another user `workstations.workstation.use` permission on
      that workstation. If not specified, defaults to `0`, which indicates
      unlimited.
    name: Identifier. Full name of this workstation configuration.
    persistentDirectories: Optional. Directories to persist across workstation
      sessions.
    readinessChecks: Optional. Readiness checks to perform when starting a
      workstation using this workstation configuration. Mark a workstation as
      running only after all specified readiness checks return 200 status
      codes.
    reconciling: Output only. Indicates whether this workstation configuration
      is currently being updated to match its intended state.
    replicaZones: Optional. Immutable. Specifies the zones used to replicate
      the VM and disk resources within the region. If set, exactly two zones
      within the workstation cluster's region must be specified-for example,
      `['us-central1-a', 'us-central1-f']`. If this field is empty, two
      default zones within the region are used. Immutable after the
      workstation configuration is created.
    runningTimeout: Optional. Number of seconds that a workstation can run
      until it is automatically shut down. We recommend that workstations be
      shut down daily to reduce costs and so that security updates can be
      applied upon restart. The idle_timeout and running_timeout fields are
      independent of each other. Note that the running_timeout field shuts
      down VMs after the specified time, regardless of whether or not the VMs
      are idle. Provide duration terminated by `s` for seconds-for example,
      `"54000s"` (15 hours). Defaults to `"43200s"` (12 hours). A value of
      `"0s"` indicates that workstations using this configuration should never
      time out. If encryption_key is set, it must be greater than `"0s"` and
      less than `"86400s"` (24 hours). Warning: A value of `"0s"` indicates
      that Cloud Workstations VMs created with this configuration have no
      maximum running time. This is strongly discouraged because you incur
      costs and will not pick up security updates.
    uid: Output only. A system-assigned unique identifier for this workstation
      configuration.
    updateTime: Output only. Time when this workstation configuration was most
      recently updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Client-specified annotations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. [Labels](https://cloud.google.com/workstations/docs/label-
    resources) that are applied to the workstation configuration and that are
    also propagated to the underlying Compute Engine resources.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  allowedPorts = _messages.MessageField('PortRange', 1, repeated=True)
  annotations = _messages.MessageField('AnnotationsValue', 2)
  conditions = _messages.MessageField('Status', 3, repeated=True)
  container = _messages.MessageField('Container', 4)
  createTime = _messages.StringField(5)
  degraded = _messages.BooleanField(6)
  deleteTime = _messages.StringField(7)
  disableTcpConnections = _messages.BooleanField(8)
  displayName = _messages.StringField(9)
  enableAuditAgent = _messages.BooleanField(10)
  encryptionKey = _messages.MessageField('CustomerEncryptionKey', 11)
  ephemeralDirectories = _messages.MessageField('EphemeralDirectory', 12, repeated=True)
  etag = _messages.StringField(13)
  grantWorkstationAdminRoleOnCreate = _messages.BooleanField(14)
  host = _messages.MessageField('Host', 15)
  idleTimeout = _messages.StringField(16)
  labels = _messages.MessageField('LabelsValue', 17)
  maxUsableWorkstations = _messages.IntegerField(18, variant=_messages.Variant.INT32)
  name = _messages.StringField(19)
  persistentDirectories = _messages.MessageField('PersistentDirectory', 20, repeated=True)
  readinessChecks = _messages.MessageField('ReadinessCheck', 21, repeated=True)
  reconciling = _messages.BooleanField(22)
  replicaZones = _messages.StringField(23, repeated=True)
  runningTimeout = _messages.StringField(24)
  uid = _messages.StringField(25)
  updateTime = _messages.StringField(26)


class WorkstationsProjectsLocationsGetRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class WorkstationsProjectsLocationsListRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class WorkstationsProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class WorkstationsProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class WorkstationsProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class WorkstationsProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class WorkstationsProjectsLocationsWorkstationClustersCreateRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsWorkstationClustersCreateRequest object.

  Fields:
    parent: Required. Parent resource name.
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not actually apply it.
    workstationCluster: A WorkstationCluster resource to be passed as the
      request body.
    workstationClusterId: Required. ID to use for the workstation cluster.
  """

  parent = _messages.StringField(1, required=True)
  validateOnly = _messages.BooleanField(2)
  workstationCluster = _messages.MessageField('WorkstationCluster', 3)
  workstationClusterId = _messages.StringField(4)


class WorkstationsProjectsLocationsWorkstationClustersDeleteRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsWorkstationClustersDeleteRequest object.

  Fields:
    etag: Optional. If set, the request will be rejected if the latest version
      of the workstation cluster on the server does not have this ETag.
    force: Optional. If set, any workstation configurations and workstations
      in the workstation cluster are also deleted. Otherwise, the request only
      works if the workstation cluster has no configurations or workstations.
    name: Required. Name of the workstation cluster to delete.
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not apply it.
  """

  etag = _messages.StringField(1)
  force = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class WorkstationsProjectsLocationsWorkstationClustersGetRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsWorkstationClustersGetRequest object.

  Fields:
    name: Required. Name of the requested resource.
  """

  name = _messages.StringField(1, required=True)


class WorkstationsProjectsLocationsWorkstationClustersListRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsWorkstationClustersListRequest object.

  Fields:
    filter: Optional. Filter the WorkstationClusters to be listed. Possible
      filters are described in https://google.aip.dev/160.
    pageSize: Optional. Maximum number of items to return.
    pageToken: Optional. next_page_token value returned from a previous List
      request, if any.
    parent: Required. Parent resource name.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class WorkstationsProjectsLocationsWorkstationClustersPatchRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsWorkstationClustersPatchRequest object.

  Fields:
    allowMissing: Optional. If set, and the workstation cluster is not found,
      a new workstation cluster will be created. In this situation,
      update_mask is ignored.
    name: Identifier. Full name of this workstation cluster.
    updateMask: Required. Mask that specifies which fields in the workstation
      cluster should be updated.
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not actually apply it.
    workstationCluster: A WorkstationCluster resource to be passed as the
      request body.
  """

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)
  workstationCluster = _messages.MessageField('WorkstationCluster', 5)


class WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsCreateRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsCrea
  teRequest object.

  Fields:
    parent: Required. Parent resource name.
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not actually apply it.
    workstationConfig: A WorkstationConfig resource to be passed as the
      request body.
    workstationConfigId: Required. ID to use for the workstation
      configuration.
  """

  parent = _messages.StringField(1, required=True)
  validateOnly = _messages.BooleanField(2)
  workstationConfig = _messages.MessageField('WorkstationConfig', 3)
  workstationConfigId = _messages.StringField(4)


class WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsDeleteRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsDele
  teRequest object.

  Fields:
    etag: Optional. If set, the request is rejected if the latest version of
      the workstation configuration on the server does not have this ETag.
    force: Optional. If set, any workstations in the workstation configuration
      are also deleted. Otherwise, the request works only if the workstation
      configuration has no workstations.
    name: Required. Name of the workstation configuration to delete.
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not actually apply it.
  """

  etag = _messages.StringField(1)
  force = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsGetIamPolicyRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsGetI
  amPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsGetRequest(_messages.Message):
  r"""A
  WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsGetRequest
  object.

  Fields:
    name: Required. Name of the requested resource.
  """

  name = _messages.StringField(1, required=True)


class WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsListRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsList
  Request object.

  Fields:
    filter: Optional. Filter the WorkstationConfigs to be listed. Possible
      filters are described in https://google.aip.dev/160.
    pageSize: Optional. Maximum number of items to return.
    pageToken: Optional. next_page_token value returned from a previous List
      request, if any.
    parent: Required. Parent resource name.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsListUsableRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsList
  UsableRequest object.

  Fields:
    pageSize: Optional. Maximum number of items to return.
    pageToken: Optional. next_page_token value returned from a previous List
      request, if any.
    parent: Required. Parent resource name.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsPatchRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsPatc
  hRequest object.

  Fields:
    allowMissing: Optional. If set and the workstation configuration is not
      found, a new workstation configuration will be created. In this
      situation, update_mask is ignored.
    name: Identifier. Full name of this workstation configuration.
    updateMask: Required. Mask specifying which fields in the workstation
      configuration should be updated.
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not actually apply it.
    workstationConfig: A WorkstationConfig resource to be passed as the
      request body.
  """

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)
  workstationConfig = _messages.MessageField('WorkstationConfig', 5)


class WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsSetIamPolicyRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsSetI
  amPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsTestIamPermissionsRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsTest
  IamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsCreateRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWork
  stationsCreateRequest object.

  Fields:
    parent: Required. Parent resource name.
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not actually apply it.
    workstation: A Workstation resource to be passed as the request body.
    workstationId: Required. ID to use for the workstation.
  """

  parent = _messages.StringField(1, required=True)
  validateOnly = _messages.BooleanField(2)
  workstation = _messages.MessageField('Workstation', 3)
  workstationId = _messages.StringField(4)


class WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsDeleteRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWork
  stationsDeleteRequest object.

  Fields:
    etag: Optional. If set, the request will be rejected if the latest version
      of the workstation on the server does not have this ETag.
    name: Required. Name of the workstation to delete.
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not actually apply it.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsGenerateAccessTokenRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWork
  stationsGenerateAccessTokenRequest object.

  Fields:
    generateAccessTokenRequest: A GenerateAccessTokenRequest resource to be
      passed as the request body.
    workstation: Required. Name of the workstation for which the access token
      should be generated.
  """

  generateAccessTokenRequest = _messages.MessageField('GenerateAccessTokenRequest', 1)
  workstation = _messages.StringField(2, required=True)


class WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsGetIamPolicyRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWork
  stationsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsGetRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWork
  stationsGetRequest object.

  Fields:
    name: Required. Name of the requested resource.
  """

  name = _messages.StringField(1, required=True)


class WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsListRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWork
  stationsListRequest object.

  Fields:
    filter: Optional. Filter the Workstations to be listed. Possible filters
      are described in https://google.aip.dev/160.
    pageSize: Optional. Maximum number of items to return.
    pageToken: Optional. next_page_token value returned from a previous List
      request, if any.
    parent: Required. Parent resource name.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsListUsableRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWork
  stationsListUsableRequest object.

  Fields:
    pageSize: Optional. Maximum number of items to return.
    pageToken: Optional. next_page_token value returned from a previous List
      request, if any.
    parent: Required. Parent resource name.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsPatchRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWork
  stationsPatchRequest object.

  Fields:
    allowMissing: Optional. If set and the workstation configuration is not
      found, a new workstation configuration is created. In this situation,
      update_mask is ignored.
    name: Identifier. Full name of this workstation.
    updateMask: Required. Mask specifying which fields in the workstation
      configuration should be updated.
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not actually apply it.
    workstation: A Workstation resource to be passed as the request body.
  """

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)
  workstation = _messages.MessageField('Workstation', 5)


class WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsSetIamPolicyRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWork
  stationsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsStartRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWork
  stationsStartRequest object.

  Fields:
    name: Required. Name of the workstation to start.
    startWorkstationRequest: A StartWorkstationRequest resource to be passed
      as the request body.
  """

  name = _messages.StringField(1, required=True)
  startWorkstationRequest = _messages.MessageField('StartWorkstationRequest', 2)


class WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsStopRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWork
  stationsStopRequest object.

  Fields:
    name: Required. Name of the workstation to stop.
    stopWorkstationRequest: A StopWorkstationRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  stopWorkstationRequest = _messages.MessageField('StopWorkstationRequest', 2)


class WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsTestIamPermissionsRequest(_messages.Message):
  r"""A WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWork
  stationsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
