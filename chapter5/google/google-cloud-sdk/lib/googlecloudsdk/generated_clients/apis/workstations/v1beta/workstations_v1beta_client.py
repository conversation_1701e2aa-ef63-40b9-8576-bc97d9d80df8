"""Generated client library for workstations version v1beta."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.workstations.v1beta import workstations_v1beta_messages as messages


class WorkstationsV1beta(base_api.BaseApiClient):
  """Generated client library for service workstations version v1beta."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://workstations.googleapis.com/'
  MTLS_BASE_URL = 'https://workstations.mtls.googleapis.com/'

  _PACKAGE = 'workstations'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1beta'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'WorkstationsV1beta'
  _URL_VERSION = 'v1beta'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new workstations handle."""
    url = url or self.BASE_URL
    super(WorkstationsV1beta, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_workstationClusters_workstationConfigs_workstations = self.ProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsService(self)
    self.projects_locations_workstationClusters_workstationConfigs = self.ProjectsLocationsWorkstationClustersWorkstationConfigsService(self)
    self.projects_locations_workstationClusters = self.ProjectsLocationsWorkstationClustersService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(WorkstationsV1beta.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (WorkstationsProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='workstations.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='WorkstationsProjectsLocationsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (WorkstationsProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='workstations.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='WorkstationsProjectsLocationsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (WorkstationsProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='workstations.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='WorkstationsProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (WorkstationsProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='workstations.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta/{+name}/operations',
        request_field='',
        request_type_name='WorkstationsProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsService(base_api.BaseApiService):
    """Service class for the projects_locations_workstationClusters_workstationConfigs_workstations resource."""

    _NAME = 'projects_locations_workstationClusters_workstationConfigs_workstations'

    def __init__(self, client):
      super(WorkstationsV1beta.ProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new workstation.

      Args:
        request: (WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}/workstations',
        http_method='POST',
        method_id='workstations.projects.locations.workstationClusters.workstationConfigs.workstations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['validateOnly', 'workstationId'],
        relative_path='v1beta/{+parent}/workstations',
        request_field='workstation',
        request_type_name='WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified workstation.

      Args:
        request: (WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}/workstations/{workstationsId}',
        http_method='DELETE',
        method_id='workstations.projects.locations.workstationClusters.workstationConfigs.workstations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'validateOnly'],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def GenerateAccessToken(self, request, global_params=None):
      r"""Returns a short-lived credential that can be used to send authenticated and authorized traffic to a workstation. Once generated this token cannot be revoked and is good for the lifetime of the token.

      Args:
        request: (WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsGenerateAccessTokenRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GenerateAccessTokenResponse) The response message.
      """
      config = self.GetMethodConfig('GenerateAccessToken')
      return self._RunMethod(
          config, request, global_params=global_params)

    GenerateAccessToken.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}/workstations/{workstationsId}:generateAccessToken',
        http_method='POST',
        method_id='workstations.projects.locations.workstationClusters.workstationConfigs.workstations.generateAccessToken',
        ordered_params=['workstation'],
        path_params=['workstation'],
        query_params=[],
        relative_path='v1beta/{+workstation}:generateAccessToken',
        request_field='generateAccessTokenRequest',
        request_type_name='WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsGenerateAccessTokenRequest',
        response_type_name='GenerateAccessTokenResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the requested workstation.

      Args:
        request: (WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Workstation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}/workstations/{workstationsId}',
        http_method='GET',
        method_id='workstations.projects.locations.workstationClusters.workstationConfigs.workstations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsGetRequest',
        response_type_name='Workstation',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}/workstations/{workstationsId}:getIamPolicy',
        http_method='GET',
        method_id='workstations.projects.locations.workstationClusters.workstationConfigs.workstations.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1beta/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns all Workstations using the specified workstation configuration.

      Args:
        request: (WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWorkstationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}/workstations',
        http_method='GET',
        method_id='workstations.projects.locations.workstationClusters.workstationConfigs.workstations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta/{+parent}/workstations',
        request_field='',
        request_type_name='WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsListRequest',
        response_type_name='ListWorkstationsResponse',
        supports_download=False,
    )

    def ListUsable(self, request, global_params=None):
      r"""Returns all workstations using the specified workstation configuration on which the caller has the "workstations.workstations.use" permission.

      Args:
        request: (WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsListUsableRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListUsableWorkstationsResponse) The response message.
      """
      config = self.GetMethodConfig('ListUsable')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListUsable.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}/workstations:listUsable',
        http_method='GET',
        method_id='workstations.projects.locations.workstationClusters.workstationConfigs.workstations.listUsable',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1beta/{+parent}/workstations:listUsable',
        request_field='',
        request_type_name='WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsListUsableRequest',
        response_type_name='ListUsableWorkstationsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing workstation.

      Args:
        request: (WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}/workstations/{workstationsId}',
        http_method='PATCH',
        method_id='workstations.projects.locations.workstationClusters.workstationConfigs.workstations.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask', 'validateOnly'],
        relative_path='v1beta/{+name}',
        request_field='workstation',
        request_type_name='WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}/workstations/{workstationsId}:setIamPolicy',
        http_method='POST',
        method_id='workstations.projects.locations.workstationClusters.workstationConfigs.workstations.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1beta/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def Start(self, request, global_params=None):
      r"""Starts running a workstation so that users can connect to it.

      Args:
        request: (WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsStartRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Start')
      return self._RunMethod(
          config, request, global_params=global_params)

    Start.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}/workstations/{workstationsId}:start',
        http_method='POST',
        method_id='workstations.projects.locations.workstationClusters.workstationConfigs.workstations.start',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}:start',
        request_field='startWorkstationRequest',
        request_type_name='WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsStartRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Stop(self, request, global_params=None):
      r"""Stops running a workstation, reducing costs.

      Args:
        request: (WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsStopRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Stop')
      return self._RunMethod(
          config, request, global_params=global_params)

    Stop.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}/workstations/{workstationsId}:stop',
        http_method='POST',
        method_id='workstations.projects.locations.workstationClusters.workstationConfigs.workstations.stop',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}:stop',
        request_field='stopWorkstationRequest',
        request_type_name='WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsStopRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}/workstations/{workstationsId}:testIamPermissions',
        http_method='POST',
        method_id='workstations.projects.locations.workstationClusters.workstationConfigs.workstations.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1beta/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsWorkstationsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsWorkstationClustersWorkstationConfigsService(base_api.BaseApiService):
    """Service class for the projects_locations_workstationClusters_workstationConfigs resource."""

    _NAME = 'projects_locations_workstationClusters_workstationConfigs'

    def __init__(self, client):
      super(WorkstationsV1beta.ProjectsLocationsWorkstationClustersWorkstationConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new workstation configuration.

      Args:
        request: (WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs',
        http_method='POST',
        method_id='workstations.projects.locations.workstationClusters.workstationConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['validateOnly', 'workstationConfigId'],
        relative_path='v1beta/{+parent}/workstationConfigs',
        request_field='workstationConfig',
        request_type_name='WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified workstation configuration.

      Args:
        request: (WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}',
        http_method='DELETE',
        method_id='workstations.projects.locations.workstationClusters.workstationConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'force', 'validateOnly'],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the requested workstation configuration.

      Args:
        request: (WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkstationConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}',
        http_method='GET',
        method_id='workstations.projects.locations.workstationClusters.workstationConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsGetRequest',
        response_type_name='WorkstationConfig',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}:getIamPolicy',
        http_method='GET',
        method_id='workstations.projects.locations.workstationClusters.workstationConfigs.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1beta/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns all workstation configurations in the specified cluster.

      Args:
        request: (WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWorkstationConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs',
        http_method='GET',
        method_id='workstations.projects.locations.workstationClusters.workstationConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta/{+parent}/workstationConfigs',
        request_field='',
        request_type_name='WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsListRequest',
        response_type_name='ListWorkstationConfigsResponse',
        supports_download=False,
    )

    def ListUsable(self, request, global_params=None):
      r"""Returns all workstation configurations in the specified cluster on which the caller has the "workstations.workstation.create" permission.

      Args:
        request: (WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsListUsableRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListUsableWorkstationConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('ListUsable')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListUsable.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs:listUsable',
        http_method='GET',
        method_id='workstations.projects.locations.workstationClusters.workstationConfigs.listUsable',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1beta/{+parent}/workstationConfigs:listUsable',
        request_field='',
        request_type_name='WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsListUsableRequest',
        response_type_name='ListUsableWorkstationConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing workstation configuration.

      Args:
        request: (WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}',
        http_method='PATCH',
        method_id='workstations.projects.locations.workstationClusters.workstationConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask', 'validateOnly'],
        relative_path='v1beta/{+name}',
        request_field='workstationConfig',
        request_type_name='WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}:setIamPolicy',
        http_method='POST',
        method_id='workstations.projects.locations.workstationClusters.workstationConfigs.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1beta/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}/workstationConfigs/{workstationConfigsId}:testIamPermissions',
        http_method='POST',
        method_id='workstations.projects.locations.workstationClusters.workstationConfigs.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1beta/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='WorkstationsProjectsLocationsWorkstationClustersWorkstationConfigsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsWorkstationClustersService(base_api.BaseApiService):
    """Service class for the projects_locations_workstationClusters resource."""

    _NAME = 'projects_locations_workstationClusters'

    def __init__(self, client):
      super(WorkstationsV1beta.ProjectsLocationsWorkstationClustersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new workstation cluster.

      Args:
        request: (WorkstationsProjectsLocationsWorkstationClustersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters',
        http_method='POST',
        method_id='workstations.projects.locations.workstationClusters.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['validateOnly', 'workstationClusterId'],
        relative_path='v1beta/{+parent}/workstationClusters',
        request_field='workstationCluster',
        request_type_name='WorkstationsProjectsLocationsWorkstationClustersCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified workstation cluster.

      Args:
        request: (WorkstationsProjectsLocationsWorkstationClustersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}',
        http_method='DELETE',
        method_id='workstations.projects.locations.workstationClusters.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'force', 'validateOnly'],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='WorkstationsProjectsLocationsWorkstationClustersDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the requested workstation cluster.

      Args:
        request: (WorkstationsProjectsLocationsWorkstationClustersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkstationCluster) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}',
        http_method='GET',
        method_id='workstations.projects.locations.workstationClusters.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='WorkstationsProjectsLocationsWorkstationClustersGetRequest',
        response_type_name='WorkstationCluster',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns all workstation clusters in the specified location.

      Args:
        request: (WorkstationsProjectsLocationsWorkstationClustersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWorkstationClustersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters',
        http_method='GET',
        method_id='workstations.projects.locations.workstationClusters.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta/{+parent}/workstationClusters',
        request_field='',
        request_type_name='WorkstationsProjectsLocationsWorkstationClustersListRequest',
        response_type_name='ListWorkstationClustersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing workstation cluster.

      Args:
        request: (WorkstationsProjectsLocationsWorkstationClustersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/workstationClusters/{workstationClustersId}',
        http_method='PATCH',
        method_id='workstations.projects.locations.workstationClusters.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask', 'validateOnly'],
        relative_path='v1beta/{+name}',
        request_field='workstationCluster',
        request_type_name='WorkstationsProjectsLocationsWorkstationClustersPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(WorkstationsV1beta.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(WorkstationsV1beta.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
