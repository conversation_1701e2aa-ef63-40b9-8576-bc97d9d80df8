"""Generated message classes for redis version v1beta1.

Creates and manages Redis instances on the Google Cloud Platform.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'redis'


class AOFConfig(_messages.Message):
  r"""Configuration of the AOF based persistence.

  Enums:
    AppendFsyncValueValuesEnum: Optional. fsync configuration.

  Fields:
    appendFsync: Optional. fsync configuration.
  """

  class AppendFsyncValueValuesEnum(_messages.Enum):
    r"""Optional. fsync configuration.

    Values:
      APPEND_FSYNC_UNSPECIFIED: Not set. Default: EVERYSEC
      NO: Never fsync. Normally Linux will flush data every 30 seconds with
        this configuration, but it's up to the kernel's exact tuning.
      EVERYSEC: fsync every second. Fast enough, and you may lose 1 second of
        data if there is a disaster
      ALWAYS: fsync every time new write commands are appended to the AOF. It
        has the best data loss protection at the cost of performance
    """
    APPEND_FSYNC_UNSPECIFIED = 0
    NO = 1
    EVERYSEC = 2
    ALWAYS = 3

  appendFsync = _messages.EnumField('AppendFsyncValueValuesEnum', 1)


class AutomatedBackupConfig(_messages.Message):
  r"""The automated backup config for a cluster.

  Enums:
    AutomatedBackupModeValueValuesEnum: Optional. The automated backup mode.
      If the mode is disabled, the other fields will be ignored.

  Fields:
    automatedBackupMode: Optional. The automated backup mode. If the mode is
      disabled, the other fields will be ignored.
    fixedFrequencySchedule: Optional. Trigger automated backups at a fixed
      frequency.
    retention: Optional. How long to keep automated backups before the backups
      are deleted. The value should be between 1 day and 365 days. If not
      specified, the default value is 35 days.
  """

  class AutomatedBackupModeValueValuesEnum(_messages.Enum):
    r"""Optional. The automated backup mode. If the mode is disabled, the
    other fields will be ignored.

    Values:
      AUTOMATED_BACKUP_MODE_UNSPECIFIED: Default value. Automated backup
        config is not specified.
      DISABLED: Automated backup config disabled.
      ENABLED: Automated backup config enabled.
    """
    AUTOMATED_BACKUP_MODE_UNSPECIFIED = 0
    DISABLED = 1
    ENABLED = 2

  automatedBackupMode = _messages.EnumField('AutomatedBackupModeValueValuesEnum', 1)
  fixedFrequencySchedule = _messages.MessageField('FixedFrequencySchedule', 2)
  retention = _messages.StringField(3)


class AvailabilityConfiguration(_messages.Message):
  r"""Configuration for availability of database instance

  Enums:
    AvailabilityTypeValueValuesEnum: Availability type. Potential values: *
      `ZONAL`: The instance serves data from only one zone. Outages in that
      zone affect data accessibility. * `REGIONAL`: The instance can serve
      data from more than one zone in a region (it is highly available).

  Fields:
    automaticFailoverRoutingConfigured: Checks for existence of (multi-
      cluster) routing configuration that allows automatic failover to a
      different zone/region in case of an outage. Applicable to Bigtable
      resources.
    availabilityType: Availability type. Potential values: * `ZONAL`: The
      instance serves data from only one zone. Outages in that zone affect
      data accessibility. * `REGIONAL`: The instance can serve data from more
      than one zone in a region (it is highly available).
    crossRegionReplicaConfigured: Checks for resources that are configured to
      have redundancy, and ongoing replication across regions
    externalReplicaConfigured: A boolean attribute.
    promotableReplicaConfigured: A boolean attribute.
  """

  class AvailabilityTypeValueValuesEnum(_messages.Enum):
    r"""Availability type. Potential values: * `ZONAL`: The instance serves
    data from only one zone. Outages in that zone affect data accessibility. *
    `REGIONAL`: The instance can serve data from more than one zone in a
    region (it is highly available).

    Values:
      AVAILABILITY_TYPE_UNSPECIFIED: <no description>
      ZONAL: Zonal available instance.
      REGIONAL: Regional available instance.
      MULTI_REGIONAL: Multi regional instance
      AVAILABILITY_TYPE_OTHER: For rest of the other category
    """
    AVAILABILITY_TYPE_UNSPECIFIED = 0
    ZONAL = 1
    REGIONAL = 2
    MULTI_REGIONAL = 3
    AVAILABILITY_TYPE_OTHER = 4

  automaticFailoverRoutingConfigured = _messages.BooleanField(1)
  availabilityType = _messages.EnumField('AvailabilityTypeValueValuesEnum', 2)
  crossRegionReplicaConfigured = _messages.BooleanField(3)
  externalReplicaConfigured = _messages.BooleanField(4)
  promotableReplicaConfigured = _messages.BooleanField(5)


class Backup(_messages.Message):
  r"""Backup of a cluster.

  Enums:
    BackupTypeValueValuesEnum: Output only. Type of the backup.
    NodeTypeValueValuesEnum: Output only. Node type of the cluster.
    StateValueValuesEnum: Output only. State of the backup.

  Fields:
    backupFiles: Output only. List of backup files of the backup.
    backupType: Output only. Type of the backup.
    cluster: Output only. Cluster resource path of this backup.
    clusterUid: Output only. Cluster uid of this backup.
    createTime: Output only. The time when the backup was created.
    encryptionInfo: Output only. Encryption information of the backup.
    engineVersion: Output only. redis-7.2, valkey-7.5
    expireTime: Output only. The time when the backup will expire.
    name: Identifier. Full resource path of the backup. the last part of the
      name is the backup id with the following format:
      [YYYYMMDDHHMMSS]_[Shorted Cluster UID] OR customer specified while
      backup cluster. Example: 20240515123000_1234
    nodeType: Output only. Node type of the cluster.
    replicaCount: Output only. Number of replicas for the cluster.
    shardCount: Output only. Number of shards for the cluster.
    state: Output only. State of the backup.
    totalSizeBytes: Output only. Total size of the backup in bytes.
    uid: Output only. System assigned unique identifier of the backup.
  """

  class BackupTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of the backup.

    Values:
      BACKUP_TYPE_UNSPECIFIED: The default value, not set.
      ON_DEMAND: On-demand backup.
      AUTOMATED: Automated backup.
    """
    BACKUP_TYPE_UNSPECIFIED = 0
    ON_DEMAND = 1
    AUTOMATED = 2

  class NodeTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Node type of the cluster.

    Values:
      NODE_TYPE_UNSPECIFIED: Node type unspecified
      REDIS_SHARED_CORE_NANO: Redis shared core nano node_type.
      REDIS_HIGHMEM_MEDIUM: Redis highmem medium node_type.
      REDIS_HIGHMEM_XLARGE: Redis highmem xlarge node_type.
      REDIS_STANDARD_SMALL: Redis standard small node_type.
    """
    NODE_TYPE_UNSPECIFIED = 0
    REDIS_SHARED_CORE_NANO = 1
    REDIS_HIGHMEM_MEDIUM = 2
    REDIS_HIGHMEM_XLARGE = 3
    REDIS_STANDARD_SMALL = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the backup.

    Values:
      STATE_UNSPECIFIED: The default value, not set.
      CREATING: The backup is being created.
      ACTIVE: The backup is active to be used.
      DELETING: The backup is being deleted.
      SUSPENDED: The backup is currently suspended due to reasons like project
        deletion, billing account closure, etc.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    SUSPENDED = 4

  backupFiles = _messages.MessageField('BackupFile', 1, repeated=True)
  backupType = _messages.EnumField('BackupTypeValueValuesEnum', 2)
  cluster = _messages.StringField(3)
  clusterUid = _messages.StringField(4)
  createTime = _messages.StringField(5)
  encryptionInfo = _messages.MessageField('EncryptionInfo', 6)
  engineVersion = _messages.StringField(7)
  expireTime = _messages.StringField(8)
  name = _messages.StringField(9)
  nodeType = _messages.EnumField('NodeTypeValueValuesEnum', 10)
  replicaCount = _messages.IntegerField(11, variant=_messages.Variant.INT32)
  shardCount = _messages.IntegerField(12, variant=_messages.Variant.INT32)
  state = _messages.EnumField('StateValueValuesEnum', 13)
  totalSizeBytes = _messages.IntegerField(14)
  uid = _messages.StringField(15)


class BackupClusterRequest(_messages.Message):
  r"""Request for [BackupCluster].

  Fields:
    backupId: Optional. The id of the backup to be created. If not specified,
      the default value ([YYYYMMDDHHMMSS]_[Shortened Cluster UID] is used.
    ttl: Optional. TTL for the backup to expire. Value range is 1 day to 100
      years. If not specified, the default value is 100 years.
  """

  backupId = _messages.StringField(1)
  ttl = _messages.StringField(2)


class BackupCollection(_messages.Message):
  r"""BackupCollection of a cluster.

  Fields:
    cluster: Output only. The full resource path of the cluster the backup
      collection belongs to. Example:
      projects/{project}/locations/{location}/clusters/{cluster}
    clusterUid: Output only. The cluster uid of the backup collection.
    createTime: Output only. The time when the backup collection was created.
    kmsKey: Output only. The KMS key used to encrypt the backups under this
      backup collection.
    name: Identifier. Full resource path of the backup collection.
    uid: Output only. System assigned unique identifier of the backup
      collection.
  """

  cluster = _messages.StringField(1)
  clusterUid = _messages.StringField(2)
  createTime = _messages.StringField(3)
  kmsKey = _messages.StringField(4)
  name = _messages.StringField(5)
  uid = _messages.StringField(6)


class BackupConfiguration(_messages.Message):
  r"""Configuration for automatic backups

  Fields:
    automatedBackupEnabled: Whether customer visible automated backups are
      enabled on the instance.
    backupRetentionSettings: Backup retention settings.
    pointInTimeRecoveryEnabled: Whether point-in-time recovery is enabled.
      This is optional field, if the database service does not have this
      feature or metadata is not available in control plane, this can be
      omitted.
  """

  automatedBackupEnabled = _messages.BooleanField(1)
  backupRetentionSettings = _messages.MessageField('RetentionSettings', 2)
  pointInTimeRecoveryEnabled = _messages.BooleanField(3)


class BackupDRConfiguration(_messages.Message):
  r"""BackupDRConfiguration to capture the backup and disaster recovery
  details of database resource.

  Fields:
    backupdrManaged: Indicates if the resource is managed by BackupDR.
  """

  backupdrManaged = _messages.BooleanField(1)


class BackupFile(_messages.Message):
  r"""Backup is consisted of multiple backup files.

  Fields:
    createTime: Output only. The time when the backup file was created.
    fileName: Output only. e.g: .rdb
    sizeBytes: Output only. Size of the backup file in bytes.
  """

  createTime = _messages.StringField(1)
  fileName = _messages.StringField(2)
  sizeBytes = _messages.IntegerField(3)


class BackupRun(_messages.Message):
  r"""A backup run.

  Enums:
    StatusValueValuesEnum: The status of this run. REQUIRED

  Fields:
    endTime: The time the backup operation completed. REQUIRED
    error: Information about why the backup operation failed. This is only
      present if the run has the FAILED status. OPTIONAL
    startTime: The time the backup operation started. REQUIRED
    status: The status of this run. REQUIRED
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""The status of this run. REQUIRED

    Values:
      STATUS_UNSPECIFIED: <no description>
      SUCCESSFUL: The backup was successful.
      FAILED: The backup was unsuccessful.
    """
    STATUS_UNSPECIFIED = 0
    SUCCESSFUL = 1
    FAILED = 2

  endTime = _messages.StringField(1)
  error = _messages.MessageField('OperationError', 2)
  startTime = _messages.StringField(3)
  status = _messages.EnumField('StatusValueValuesEnum', 4)


class CertChain(_messages.Message):
  r"""A CertChain object.

  Fields:
    certificates: The certificates that form the CA chain, from leaf to root
      order.
  """

  certificates = _messages.StringField(1, repeated=True)


class CertificateAuthority(_messages.Message):
  r"""Redis cluster certificate authority

  Fields:
    managedServerCa: A ManagedCertificateAuthority attribute.
    name: Identifier. Unique name of the resource in this scope including
      project, location and cluster using the form: `projects/{project}/locati
      ons/{location}/clusters/{cluster}/certificateAuthority`
  """

  managedServerCa = _messages.MessageField('ManagedCertificateAuthority', 1)
  name = _messages.StringField(2)


class Cluster(_messages.Message):
  r"""A cluster instance.

  Enums:
    AuthorizationModeValueValuesEnum: Optional. The authorization mode of the
      Redis cluster. If not provided, auth feature is disabled for the
      cluster.
    NodeTypeValueValuesEnum: Optional. The type of a redis node in the
      cluster. NodeType determines the underlying machine-type of a redis
      node.
    StateValueValuesEnum: Output only. The current state of this cluster. Can
      be CREATING, READY, UPDATING, DELETING and SUSPENDED
    TransitEncryptionModeValueValuesEnum: Optional. The in-transit encryption
      for the Redis cluster. If not provided, encryption is disabled for the
      cluster.

  Messages:
    RedisConfigsValue: Optional. Key/Value pairs of customer overrides for
      mutable Redis Configs

  Fields:
    allowFewerZonesDeployment: Optional. Immutable. Allows customers to
      specify if they are okay with deploying a multi-zone cluster in less
      than 3 zones. Once set, if there is a zonal outage during the cluster
      creation, the cluster will only be deployed in 2 zones, and stay within
      the 2 zones for its lifecycle.
    asyncClusterEndpointsDeletionEnabled: Optional. If true, cluster endpoints
      that are created and registered by customers can be deleted
      asynchronously. That is, such a cluster endpoint can be de-registered
      before the forwarding rules in the cluster endpoint are deleted.
    authorizationMode: Optional. The authorization mode of the Redis cluster.
      If not provided, auth feature is disabled for the cluster.
    automatedBackupConfig: Optional. The automated backup config for the
      cluster.
    availableMaintenanceVersions: Output only. This field is used to determine
      the available maintenance versions for the self service update.
    backupCollection: Optional. Output only. The backup collection full
      resource name. Example:
      projects/{project}/locations/{location}/backupCollections/{collection}
    clusterEndpoints: Optional. A list of cluster endpoints.
    createTime: Output only. The timestamp associated with the cluster
      creation request.
    crossClusterReplicationConfig: Optional. Cross cluster replication config.
    deletionProtectionEnabled: Optional. The delete operation will fail when
      the value is set to true.
    discoveryEndpoints: Output only. Endpoints created on each given network,
      for Redis clients to connect to the cluster. Currently only one
      discovery endpoint is supported.
    effectiveMaintenanceVersion: Output only. This field represents the actual
      maintenance version of the cluster.
    encryptionInfo: Output only. Encryption information of the data at rest of
      the cluster.
    gcsSource: Optional. Backups stored in Cloud Storage buckets. The Cloud
      Storage buckets need to be the same region as the clusters. Read
      permission is required to import from the provided Cloud Storage
      objects.
    kmsKey: Optional. The KMS key used to encrypt the at-rest data of the
      cluster.
    maintenancePolicy: Optional. ClusterMaintenancePolicy determines when to
      allow or deny updates.
    maintenanceSchedule: Output only. ClusterMaintenanceSchedule Output only
      Published maintenance schedule.
    maintenanceVersion: Optional. This field can be used to trigger self
      service update to indicate the desired maintenance version. The input to
      this field can be determined by the available_maintenance_versions
      field.
    managedBackupSource: Optional. Backups generated and managed by
      memorystore service.
    name: Required. Identifier. Unique name of the resource in this scope
      including project and location using the form:
      `projects/{project_id}/locations/{location_id}/clusters/{cluster_id}`
    nodeType: Optional. The type of a redis node in the cluster. NodeType
      determines the underlying machine-type of a redis node.
    ondemandMaintenance: Optional. Input only. Ondemand maintenance for the
      cluster. This field can be used to trigger ondemand critical update on
      the cluster.
    persistenceConfig: Optional. Persistence config (RDB, AOF) for the
      cluster.
    preciseSizeGb: Output only. Precise value of redis memory size in GB for
      the entire cluster.
    primaryZones: Optional. Immutable. Set of zones to be used for cluster
      primary nodes.
    pscConfigs: Optional. Each PscConfig configures the consumer network where
      IPs will be designated to the cluster for client access through Private
      Service Connect Automation. Currently, only one PscConfig is supported.
    pscConnections: Output only. The list of PSC connections that are auto-
      created through service connectivity automation.
    pscServiceAttachments: Output only. Service attachment details to
      configure Psc connections
    redisConfigs: Optional. Key/Value pairs of customer overrides for mutable
      Redis Configs
    replicaCount: Optional. The number of replica nodes per shard.
    satisfiesPzi: Optional. Output only. Reserved for future use.
    satisfiesPzs: Optional. Output only. Reserved for future use.
    shardCount: Optional. Number of shards for the Redis cluster.
    simulateMaintenanceEvent: Optional. Input only. Simulate a maintenance
      event.
    sizeGb: Output only. Redis memory size in GB for the entire cluster
      rounded up to the next integer.
    state: Output only. The current state of this cluster. Can be CREATING,
      READY, UPDATING, DELETING and SUSPENDED
    stateInfo: Output only. Additional information about the current state of
      the cluster.
    transitEncryptionMode: Optional. The in-transit encryption for the Redis
      cluster. If not provided, encryption is disabled for the cluster.
    uid: Output only. System assigned, unique identifier for the cluster.
    zoneDistributionConfig: Optional. This config will be used to determine
      how the customer wants us to distribute cluster resources within the
      region.
    zones: Optional. Immutable. Set of zones to be used for cluster nodes.
  """

  class AuthorizationModeValueValuesEnum(_messages.Enum):
    r"""Optional. The authorization mode of the Redis cluster. If not
    provided, auth feature is disabled for the cluster.

    Values:
      AUTH_MODE_UNSPECIFIED: Not set.
      AUTH_MODE_IAM_AUTH: IAM basic authorization mode
      AUTH_MODE_DISABLED: Authorization disabled mode
    """
    AUTH_MODE_UNSPECIFIED = 0
    AUTH_MODE_IAM_AUTH = 1
    AUTH_MODE_DISABLED = 2

  class NodeTypeValueValuesEnum(_messages.Enum):
    r"""Optional. The type of a redis node in the cluster. NodeType determines
    the underlying machine-type of a redis node.

    Values:
      NODE_TYPE_UNSPECIFIED: Node type unspecified
      REDIS_SHARED_CORE_NANO: Redis shared core nano node_type.
      REDIS_HIGHMEM_MEDIUM: Redis highmem medium node_type.
      REDIS_HIGHMEM_XLARGE: Redis highmem xlarge node_type.
      REDIS_STANDARD_SMALL: Redis standard small node_type.
    """
    NODE_TYPE_UNSPECIFIED = 0
    REDIS_SHARED_CORE_NANO = 1
    REDIS_HIGHMEM_MEDIUM = 2
    REDIS_HIGHMEM_XLARGE = 3
    REDIS_STANDARD_SMALL = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of this cluster. Can be CREATING,
    READY, UPDATING, DELETING and SUSPENDED

    Values:
      STATE_UNSPECIFIED: Not set.
      CREATING: Redis cluster is being created.
      ACTIVE: Redis cluster has been created and is fully usable.
      UPDATING: Redis cluster configuration is being updated.
      DELETING: Redis cluster is being deleted.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    UPDATING = 3
    DELETING = 4

  class TransitEncryptionModeValueValuesEnum(_messages.Enum):
    r"""Optional. The in-transit encryption for the Redis cluster. If not
    provided, encryption is disabled for the cluster.

    Values:
      TRANSIT_ENCRYPTION_MODE_UNSPECIFIED: In-transit encryption not set.
      TRANSIT_ENCRYPTION_MODE_DISABLED: In-transit encryption disabled.
      TRANSIT_ENCRYPTION_MODE_SERVER_AUTHENTICATION: Use server managed
        encryption for in-transit encryption.
    """
    TRANSIT_ENCRYPTION_MODE_UNSPECIFIED = 0
    TRANSIT_ENCRYPTION_MODE_DISABLED = 1
    TRANSIT_ENCRYPTION_MODE_SERVER_AUTHENTICATION = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class RedisConfigsValue(_messages.Message):
    r"""Optional. Key/Value pairs of customer overrides for mutable Redis
    Configs

    Messages:
      AdditionalProperty: An additional property for a RedisConfigsValue
        object.

    Fields:
      additionalProperties: Additional properties of type RedisConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a RedisConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  allowFewerZonesDeployment = _messages.BooleanField(1)
  asyncClusterEndpointsDeletionEnabled = _messages.BooleanField(2)
  authorizationMode = _messages.EnumField('AuthorizationModeValueValuesEnum', 3)
  automatedBackupConfig = _messages.MessageField('AutomatedBackupConfig', 4)
  availableMaintenanceVersions = _messages.StringField(5, repeated=True)
  backupCollection = _messages.StringField(6)
  clusterEndpoints = _messages.MessageField('ClusterEndpoint', 7, repeated=True)
  createTime = _messages.StringField(8)
  crossClusterReplicationConfig = _messages.MessageField('CrossClusterReplicationConfig', 9)
  deletionProtectionEnabled = _messages.BooleanField(10)
  discoveryEndpoints = _messages.MessageField('DiscoveryEndpoint', 11, repeated=True)
  effectiveMaintenanceVersion = _messages.StringField(12)
  encryptionInfo = _messages.MessageField('EncryptionInfo', 13)
  gcsSource = _messages.MessageField('GcsBackupSource', 14)
  kmsKey = _messages.StringField(15)
  maintenancePolicy = _messages.MessageField('ClusterMaintenancePolicy', 16)
  maintenanceSchedule = _messages.MessageField('ClusterMaintenanceSchedule', 17)
  maintenanceVersion = _messages.StringField(18)
  managedBackupSource = _messages.MessageField('ManagedBackupSource', 19)
  name = _messages.StringField(20)
  nodeType = _messages.EnumField('NodeTypeValueValuesEnum', 21)
  ondemandMaintenance = _messages.BooleanField(22)
  persistenceConfig = _messages.MessageField('ClusterPersistenceConfig', 23)
  preciseSizeGb = _messages.FloatField(24)
  primaryZones = _messages.StringField(25, repeated=True)
  pscConfigs = _messages.MessageField('PscConfig', 26, repeated=True)
  pscConnections = _messages.MessageField('PscConnection', 27, repeated=True)
  pscServiceAttachments = _messages.MessageField('PscServiceAttachment', 28, repeated=True)
  redisConfigs = _messages.MessageField('RedisConfigsValue', 29)
  replicaCount = _messages.IntegerField(30, variant=_messages.Variant.INT32)
  satisfiesPzi = _messages.BooleanField(31)
  satisfiesPzs = _messages.BooleanField(32)
  shardCount = _messages.IntegerField(33, variant=_messages.Variant.INT32)
  simulateMaintenanceEvent = _messages.BooleanField(34)
  sizeGb = _messages.IntegerField(35, variant=_messages.Variant.INT32)
  state = _messages.EnumField('StateValueValuesEnum', 36)
  stateInfo = _messages.MessageField('StateInfo', 37)
  transitEncryptionMode = _messages.EnumField('TransitEncryptionModeValueValuesEnum', 38)
  uid = _messages.StringField(39)
  zoneDistributionConfig = _messages.MessageField('ZoneDistributionConfig', 40)
  zones = _messages.StringField(41, repeated=True)


class ClusterDenyMaintenancePeriod(_messages.Message):
  r"""ClusterDenyMaintenancePeriod definition. Except emergencies, maintenance
  will not be scheduled to start within this deny period. The start_date must
  be less than the end_date.

  Fields:
    endDate: Deny period end date. This can be: * A full date, with non-zero
      year, month and day values. * A month and day value, with a zero year
      for recurring. Date matching this period will have to be before the end.
    startDate: Deny period start date. This can be: * A full date, with non-
      zero year, month and day values. * A month and day value, with a zero
      year for recurring. Date matching this period will have to be the same
      or after the start.
    time: Time in UTC when the deny period starts on start_date and ends on
      end_date. This can be: * Full time.
  """

  endDate = _messages.MessageField('Date', 1)
  startDate = _messages.MessageField('Date', 2)
  time = _messages.MessageField('TimeOfDay', 3)


class ClusterEndpoint(_messages.Message):
  r"""ClusterEndpoint consists of PSC connections that are created as a group
  in each VPC network for accessing the cluster. In each group, there shall be
  one connection for each service attachment in the cluster.

  Fields:
    connections: Required. A group of PSC connections. They are created in the
      same VPC network, one for each service attachment in the cluster.
  """

  connections = _messages.MessageField('ConnectionDetail', 1, repeated=True)


class ClusterMaintenancePolicy(_messages.Message):
  r"""Maintenance policy per cluster.

  Fields:
    createTime: Output only. The time when the policy was created i.e.
      Maintenance Window or Deny Period was assigned.
    denyMaintenancePeriods: Optional. Deny maintenance periods
    updateTime: Output only. The time when the policy was updated i.e.
      Maintenance Window or Deny Period was updated.
    weeklyMaintenanceWindow: Optional. Maintenance window that is applied to
      resources covered by this policy. Minimum 1. For the current version,
      the maximum number of weekly_maintenance_window is expected to be one.
  """

  createTime = _messages.StringField(1)
  denyMaintenancePeriods = _messages.MessageField('ClusterDenyMaintenancePeriod', 2, repeated=True)
  updateTime = _messages.StringField(3)
  weeklyMaintenanceWindow = _messages.MessageField('ClusterWeeklyMaintenanceWindow', 4, repeated=True)


class ClusterMaintenanceSchedule(_messages.Message):
  r"""Upcoming maintenance schedule.

  Fields:
    endTime: Output only. The end time of any upcoming scheduled maintenance
      for this instance.
    startTime: Output only. The start time of any upcoming scheduled
      maintenance for this instance.
  """

  endTime = _messages.StringField(1)
  startTime = _messages.StringField(2)


class ClusterPersistenceConfig(_messages.Message):
  r"""Configuration of the persistence functionality.

  Enums:
    ModeValueValuesEnum: Optional. The mode of persistence.

  Fields:
    aofConfig: Optional. AOF configuration. This field will be ignored if mode
      is not AOF.
    mode: Optional. The mode of persistence.
    rdbConfig: Optional. RDB configuration. This field will be ignored if mode
      is not RDB.
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""Optional. The mode of persistence.

    Values:
      PERSISTENCE_MODE_UNSPECIFIED: Not set.
      DISABLED: Persistence is disabled, and any snapshot data is deleted.
      RDB: RDB based persistence is enabled.
      AOF: AOF based persistence is enabled.
    """
    PERSISTENCE_MODE_UNSPECIFIED = 0
    DISABLED = 1
    RDB = 2
    AOF = 3

  aofConfig = _messages.MessageField('AOFConfig', 1)
  mode = _messages.EnumField('ModeValueValuesEnum', 2)
  rdbConfig = _messages.MessageField('RDBConfig', 3)


class ClusterWeeklyMaintenanceWindow(_messages.Message):
  r"""Time window specified for weekly operations.

  Enums:
    DayValueValuesEnum: Optional. Allows to define schedule that runs
      specified day of the week.

  Fields:
    day: Optional. Allows to define schedule that runs specified day of the
      week.
    startTime: Optional. Start time of the window in UTC.
  """

  class DayValueValuesEnum(_messages.Enum):
    r"""Optional. Allows to define schedule that runs specified day of the
    week.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  day = _messages.EnumField('DayValueValuesEnum', 1)
  startTime = _messages.MessageField('TimeOfDay', 2)


class Compliance(_messages.Message):
  r"""Contains compliance information about a security standard indicating
  unmet recommendations.

  Fields:
    standard: Industry-wide compliance standards or benchmarks, such as CIS,
      PCI, and OWASP.
    version: Version of the standard or benchmark, for example, 1.1
  """

  standard = _messages.StringField(1)
  version = _messages.StringField(2)


class ConfigBasedSignalData(_messages.Message):
  r"""Config based signal data. This is used to send signals to Condor which
  are based on the DB level configurations. These will be used to send signals
  for self managed databases.

  Enums:
    SignalTypeValueValuesEnum: Required. Signal type of the signal

  Fields:
    fullResourceName: Required. Full Resource name of the source resource.
    lastRefreshTime: Required. Last time signal was refreshed
    resourceId: Database resource id.
    signalBoolValue: Signal data for boolean signals.
    signalType: Required. Signal type of the signal
  """

  class SignalTypeValueValuesEnum(_messages.Enum):
    r"""Required. Signal type of the signal

    Values:
      SIGNAL_TYPE_UNSPECIFIED: Unspecified signal type.
      SIGNAL_TYPE_OUTDATED_MINOR_VERSION: Outdated Minor Version
      SIGNAL_TYPE_DATABASE_AUDITING_DISABLED: Represents database auditing is
        disabled.
      SIGNAL_TYPE_NO_ROOT_PASSWORD: Represents if a database has a password
        configured for the root account or not.
      SIGNAL_TYPE_EXPOSED_TO_PUBLIC_ACCESS: Represents if a resource is
        exposed to public access.
      SIGNAL_TYPE_UNENCRYPTED_CONNECTIONS: Represents if a resources requires
        all incoming connections to use SSL or not.
    """
    SIGNAL_TYPE_UNSPECIFIED = 0
    SIGNAL_TYPE_OUTDATED_MINOR_VERSION = 1
    SIGNAL_TYPE_DATABASE_AUDITING_DISABLED = 2
    SIGNAL_TYPE_NO_ROOT_PASSWORD = 3
    SIGNAL_TYPE_EXPOSED_TO_PUBLIC_ACCESS = 4
    SIGNAL_TYPE_UNENCRYPTED_CONNECTIONS = 5

  fullResourceName = _messages.StringField(1)
  lastRefreshTime = _messages.StringField(2)
  resourceId = _messages.MessageField('DatabaseResourceId', 3)
  signalBoolValue = _messages.BooleanField(4)
  signalType = _messages.EnumField('SignalTypeValueValuesEnum', 5)


class ConnectionDetail(_messages.Message):
  r"""Detailed information of each PSC connection.

  Fields:
    pscAutoConnection: Detailed information of a PSC connection that is
      created through service connectivity automation.
    pscConnection: Detailed information of a PSC connection that is created by
      the customer who owns the cluster.
  """

  pscAutoConnection = _messages.MessageField('PscAutoConnection', 1)
  pscConnection = _messages.MessageField('PscConnection', 2)


class CrossClusterReplicationConfig(_messages.Message):
  r"""Cross cluster replication config.

  Enums:
    ClusterRoleValueValuesEnum: Output only. The role of the cluster in cross
      cluster replication.

  Fields:
    clusterRole: Output only. The role of the cluster in cross cluster
      replication.
    membership: Output only. An output only view of all the member clusters
      participating in the cross cluster replication. This view will be
      provided by every member cluster irrespective of its cluster
      role(primary or secondary). A primary cluster can provide information
      about all the secondary clusters replicating from it. However, a
      secondary cluster only knows about the primary cluster from which it is
      replicating. However, for scenarios, where the primary cluster is
      unavailable(e.g. regional outage), a GetCluster request can be sent to
      any other member cluster and this field will list all the member
      clusters participating in cross cluster replication.
    primaryCluster: Details of the primary cluster that is used as the
      replication source for this secondary cluster. This field is only set
      for a secondary cluster.
    secondaryClusters: List of secondary clusters that are replicating from
      this primary cluster. This field is only set for a primary cluster.
    updateTime: Output only. The last time cross cluster replication config
      was updated.
  """

  class ClusterRoleValueValuesEnum(_messages.Enum):
    r"""Output only. The role of the cluster in cross cluster replication.

    Values:
      CLUSTER_ROLE_UNSPECIFIED: Cluster role is not set. The behavior is
        equivalent to NONE.
      NONE: This cluster does not participate in cross cluster replication. It
        is an independent cluster and does not replicate to or from any other
        clusters.
      PRIMARY: A cluster that allows both reads and writes. Any data written
        to this cluster is also replicated to the attached secondary clusters.
      SECONDARY: A cluster that allows only reads and replicates data from a
        primary cluster.
    """
    CLUSTER_ROLE_UNSPECIFIED = 0
    NONE = 1
    PRIMARY = 2
    SECONDARY = 3

  clusterRole = _messages.EnumField('ClusterRoleValueValuesEnum', 1)
  membership = _messages.MessageField('Membership', 2)
  primaryCluster = _messages.MessageField('RemoteCluster', 3)
  secondaryClusters = _messages.MessageField('RemoteCluster', 4, repeated=True)
  updateTime = _messages.StringField(5)


class CustomMetadataData(_messages.Message):
  r"""Any custom metadata associated with the resource. e.g. A spanner
  instance can have multiple databases with its own unique metadata.
  Information for these individual databases can be captured in custom
  metadata data

  Fields:
    internalResourceMetadata: Metadata for individual internal resources in an
      instance. e.g. spanner instance can have multiple databases with unique
      configuration.
  """

  internalResourceMetadata = _messages.MessageField('InternalResourceMetadata', 1, repeated=True)


class DatabaseResourceFeed(_messages.Message):
  r"""DatabaseResourceFeed is the top level proto to be used to ingest
  different database resource level events into Condor platform. Next ID: 9

  Enums:
    FeedTypeValueValuesEnum: Required. Type feed to be ingested into condor

  Fields:
    configBasedSignalData: Config based signal data is used to ingest signals
      that are generated based on the configuration of the database resource.
    feedTimestamp: Required. Timestamp when feed is generated.
    feedType: Required. Type feed to be ingested into condor
    observabilityMetricData: A ObservabilityMetricData attribute.
    recommendationSignalData: A DatabaseResourceRecommendationSignalData
      attribute.
    resourceHealthSignalData: A DatabaseResourceHealthSignalData attribute.
    resourceId: Primary key associated with the Resource. resource_id is
      available in individual feed level as well.
    resourceMetadata: A DatabaseResourceMetadata attribute.
  """

  class FeedTypeValueValuesEnum(_messages.Enum):
    r"""Required. Type feed to be ingested into condor

    Values:
      FEEDTYPE_UNSPECIFIED: <no description>
      RESOURCE_METADATA: Database resource metadata feed from control plane
      OBSERVABILITY_DATA: Database resource monitoring data
      SECURITY_FINDING_DATA: Database resource security health signal data
      RECOMMENDATION_SIGNAL_DATA: Database resource recommendation signal data
      CONFIG_BASED_SIGNAL_DATA: Database config based signal data
    """
    FEEDTYPE_UNSPECIFIED = 0
    RESOURCE_METADATA = 1
    OBSERVABILITY_DATA = 2
    SECURITY_FINDING_DATA = 3
    RECOMMENDATION_SIGNAL_DATA = 4
    CONFIG_BASED_SIGNAL_DATA = 5

  configBasedSignalData = _messages.MessageField('ConfigBasedSignalData', 1)
  feedTimestamp = _messages.StringField(2)
  feedType = _messages.EnumField('FeedTypeValueValuesEnum', 3)
  observabilityMetricData = _messages.MessageField('ObservabilityMetricData', 4)
  recommendationSignalData = _messages.MessageField('DatabaseResourceRecommendationSignalData', 5)
  resourceHealthSignalData = _messages.MessageField('DatabaseResourceHealthSignalData', 6)
  resourceId = _messages.MessageField('DatabaseResourceId', 7)
  resourceMetadata = _messages.MessageField('DatabaseResourceMetadata', 8)


class DatabaseResourceHealthSignalData(_messages.Message):
  r"""Common model for database resource health signal data.

  Enums:
    ProviderValueValuesEnum: Cloud provider name. Ex:
      GCP/AWS/Azure/OnPrem/SelfManaged
    SignalClassValueValuesEnum: Required. The class of the signal, such as if
      it's a THREAT or VULNERABILITY.
    SignalSeverityValueValuesEnum: The severity of the signal, such as if it's
      a HIGH or LOW severity.
    SignalTypeValueValuesEnum: Required. Type of signal, for example,
      `AVAILABLE_IN_MULTIPLE_ZONES`, `LOGGING_MOST_ERRORS`, etc.
    StateValueValuesEnum:

  Messages:
    AdditionalMetadataValue: Any other additional metadata

  Fields:
    additionalMetadata: Any other additional metadata
    compliance: Industry standards associated with this signal; if this signal
      is an issue, that could be a violation of the associated industry
      standard(s). For example, AUTO_BACKUP_DISABLED signal is associated with
      CIS GCP 1.1, CIS GCP 1.2, CIS GCP 1.3, NIST 800-53 and ISO-27001
      compliance standards. If a database resource does not have automated
      backup enable, it will violate these following industry standards.
    description: Description associated with signal
    eventTime: Required. The last time at which the event described by this
      signal took place
    externalUri: The external-uri of the signal, using which more information
      about this signal can be obtained. In GCP, this will take user to SCC
      page to get more details about signals.
    location: This is used to identify the location of the resource. Example:
      "us-central1"
    name: Required. The name of the signal, ex: PUBLIC_SQL_INSTANCE,
      SQL_LOG_ERROR_VERBOSITY etc.
    provider: Cloud provider name. Ex: GCP/AWS/Azure/OnPrem/SelfManaged
    resourceContainer: Closest parent container of this resource. In GCP,
      'container' refers to a Cloud Resource Manager project. It must be
      resource name of a Cloud Resource Manager project with the format of
      "provider//", such as "projects/123". For GCP provided resources, number
      should be project number.
    resourceName: Required. Database resource name associated with the signal.
      Resource name to follow CAIS resource_name format as noted here
      go/condor-common-datamodel
    signalClass: Required. The class of the signal, such as if it's a THREAT
      or VULNERABILITY.
    signalId: Required. Unique identifier for the signal. This is an unique id
      which would be mainatined by partner to identify a signal.
    signalSeverity: The severity of the signal, such as if it's a HIGH or LOW
      severity.
    signalType: Required. Type of signal, for example,
      `AVAILABLE_IN_MULTIPLE_ZONES`, `LOGGING_MOST_ERRORS`, etc.
    state: A StateValueValuesEnum attribute.
  """

  class ProviderValueValuesEnum(_messages.Enum):
    r"""Cloud provider name. Ex: GCP/AWS/Azure/OnPrem/SelfManaged

    Values:
      PROVIDER_UNSPECIFIED: <no description>
      GCP: Google cloud platform provider
      AWS: Amazon web service
      AZURE: Azure web service
      ONPREM: On-prem database resources.
      SELFMANAGED: Self-managed database provider. These are resources on a
        cloud platform, e.g., database resource installed in a GCE VM, but not
        a managed database service.
      PROVIDER_OTHER: For the rest of the other categories. Other refers to
        the rest of other database service providers, this could be smaller
        cloud provider. This needs to be provided when the provider is known,
        but it is not present in the existing set of enum values.
    """
    PROVIDER_UNSPECIFIED = 0
    GCP = 1
    AWS = 2
    AZURE = 3
    ONPREM = 4
    SELFMANAGED = 5
    PROVIDER_OTHER = 6

  class SignalClassValueValuesEnum(_messages.Enum):
    r"""Required. The class of the signal, such as if it's a THREAT or
    VULNERABILITY.

    Values:
      CLASS_UNSPECIFIED: Unspecified signal class.
      THREAT: Describes unwanted or malicious activity.
      VULNERABILITY: Describes a potential weakness in software that increases
        risk to Confidentiality & Integrity & Availability.
      MISCONFIGURATION: Describes a potential weakness in cloud resource/asset
        configuration that increases risk.
      OBSERVATION: Describes a security observation that is for informational
        purposes.
      ERROR: Describes an error that prevents some SCC functionality.
    """
    CLASS_UNSPECIFIED = 0
    THREAT = 1
    VULNERABILITY = 2
    MISCONFIGURATION = 3
    OBSERVATION = 4
    ERROR = 5

  class SignalSeverityValueValuesEnum(_messages.Enum):
    r"""The severity of the signal, such as if it's a HIGH or LOW severity.

    Values:
      SIGNAL_SEVERITY_UNSPECIFIED: This value is used for findings when a
        source doesn't write a severity value.
      CRITICAL: A critical vulnerability is easily discoverable by an external
        actor, exploitable.
      HIGH: A high risk vulnerability can be easily discovered and exploited
        in combination with other vulnerabilities.
      MEDIUM: A medium risk vulnerability could be used by an actor to gain
        access to resources or privileges that enable them to eventually gain
        access and the ability to execute arbitrary code or exfiltrate data.
      LOW: A low risk vulnerability hampers a security organization's ability
        to detect vulnerabilities or active threats in their deployment.
    """
    SIGNAL_SEVERITY_UNSPECIFIED = 0
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4

  class SignalTypeValueValuesEnum(_messages.Enum):
    r"""Required. Type of signal, for example, `AVAILABLE_IN_MULTIPLE_ZONES`,
    `LOGGING_MOST_ERRORS`, etc.

    Values:
      SIGNAL_TYPE_UNSPECIFIED: Unspecified.
      SIGNAL_TYPE_NOT_PROTECTED_BY_AUTOMATIC_FAILOVER: Represents if a
        resource is protected by automatic failover. Checks for resources that
        are configured to have redundancy within a region that enables
        automatic failover.
      SIGNAL_TYPE_GROUP_NOT_REPLICATING_ACROSS_REGIONS: Represents if a group
        is replicating across regions. Checks for resources that are
        configured to have redundancy, and ongoing replication, across
        regions.
      SIGNAL_TYPE_NOT_AVAILABLE_IN_MULTIPLE_ZONES: Represents if the resource
        is available in multiple zones or not.
      SIGNAL_TYPE_NOT_AVAILABLE_IN_MULTIPLE_REGIONS: Represents if a resource
        is available in multiple regions.
      SIGNAL_TYPE_NO_PROMOTABLE_REPLICA: Represents if a resource has a
        promotable replica.
      SIGNAL_TYPE_NO_AUTOMATED_BACKUP_POLICY: Represents if a resource has an
        automated backup policy.
      SIGNAL_TYPE_SHORT_BACKUP_RETENTION: Represents if a resources has a
        short backup retention period.
      SIGNAL_TYPE_LAST_BACKUP_FAILED: Represents if the last backup of a
        resource failed.
      SIGNAL_TYPE_LAST_BACKUP_OLD: Represents if the last backup of a resource
        is older than some threshold value.
      SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_2_0: Represents if a resource
        violates CIS GCP Foundation 2.0.
      SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_3: Represents if a resource
        violates CIS GCP Foundation 1.3.
      SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_2: Represents if a resource
        violates CIS GCP Foundation 1.2.
      SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_1: Represents if a resource
        violates CIS GCP Foundation 1.1.
      SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_0: Represents if a resource
        violates CIS GCP Foundation 1.0.
      SIGNAL_TYPE_VIOLATES_CIS_CONTROLS_V8_0: Represents if a resource
        violates CIS Controls 8.0.
      SIGNAL_TYPE_VIOLATES_NIST_800_53: Represents if a resource violates NIST
        800-53.
      SIGNAL_TYPE_VIOLATES_NIST_800_53_R5: Represents if a resource violates
        NIST 800-53 R5.
      SIGNAL_TYPE_VIOLATES_NIST_CYBERSECURITY_FRAMEWORK_V1_0: Represents if a
        resource violates NIST Cybersecurity Framework 1.0.
      SIGNAL_TYPE_VIOLATES_ISO_27001: Represents if a resource violates
        ISO-27001.
      SIGNAL_TYPE_VIOLATES_ISO_27001_V2022: Represents if a resource violates
        ISO 27001 2022.
      SIGNAL_TYPE_VIOLATES_PCI_DSS_V3_2_1: Represents if a resource violates
        PCI-DSS v3.2.1.
      SIGNAL_TYPE_VIOLATES_PCI_DSS_V4_0: Represents if a resource violates
        PCI-DSS v4.0.
      SIGNAL_TYPE_VIOLATES_CLOUD_CONTROLS_MATRIX_V4: Represents if a resource
        violates Cloud Controls Matrix v4.0.
      SIGNAL_TYPE_VIOLATES_HIPAA: Represents if a resource violates HIPAA.
      SIGNAL_TYPE_VIOLATES_SOC2_V2017: Represents if a resource violates SOC2
        v2017.
      SIGNAL_TYPE_LOGS_NOT_OPTIMIZED_FOR_TROUBLESHOOTING: Represents if
        log_checkpoints database flag for a Cloud SQL for PostgreSQL instance
        is not set to on.
      SIGNAL_TYPE_QUERY_DURATIONS_NOT_LOGGED: Represents if the log_duration
        database flag for a Cloud SQL for PostgreSQL instance is not set to
        on.
      SIGNAL_TYPE_VERBOSE_ERROR_LOGGING: Represents if the log_error_verbosity
        database flag for a Cloud SQL for PostgreSQL instance is not set to
        default or stricter (default or terse).
      SIGNAL_TYPE_QUERY_LOCK_WAITS_NOT_LOGGED: Represents if the
        log_lock_waits database flag for a Cloud SQL for PostgreSQL instance
        is not set to on.
      SIGNAL_TYPE_LOGGING_MOST_ERRORS: Represents if the
        log_min_error_statement database flag for a Cloud SQL for PostgreSQL
        instance is not set appropriately.
      SIGNAL_TYPE_LOGGING_ONLY_CRITICAL_ERRORS: Represents if the
        log_min_error_statement database flag for a Cloud SQL for PostgreSQL
        instance does not have an appropriate severity level.
      SIGNAL_TYPE_MINIMAL_ERROR_LOGGING: Represents if the log_min_messages
        database flag for a Cloud SQL for PostgreSQL instance is not set to
        warning or another recommended value.
      SIGNAL_TYPE_QUERY_STATISTICS_LOGGED: Represents if the databaseFlags
        property of instance metadata for the log_executor_status field is set
        to on.
      SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_CLIENT_HOSTNAME: Represents if the
        log_hostname database flag for a Cloud SQL for PostgreSQL instance is
        not set to off.
      SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_PARSER_STATISTICS: Represents if the
        log_parser_stats database flag for a Cloud SQL for PostgreSQL instance
        is not set to off.
      SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_PLANNER_STATISTICS: Represents if the
        log_planner_stats database flag for a Cloud SQL for PostgreSQL
        instance is not set to off.
      SIGNAL_TYPE_NOT_LOGGING_ONLY_DDL_STATEMENTS: Represents if the
        log_statement database flag for a Cloud SQL for PostgreSQL instance is
        not set to DDL (all data definition statements).
      SIGNAL_TYPE_LOGGING_QUERY_STATISTICS: Represents if the
        log_statement_stats database flag for a Cloud SQL for PostgreSQL
        instance is not set to off.
      SIGNAL_TYPE_NOT_LOGGING_TEMPORARY_FILES: Represents if the
        log_temp_files database flag for a Cloud SQL for PostgreSQL instance
        is not set to "0". (NOTE: 0 = ON)
      SIGNAL_TYPE_CONNECTION_MAX_NOT_CONFIGURED: Represents if the user
        connections database flag for a Cloud SQL for SQL Server instance is
        configured.
      SIGNAL_TYPE_USER_OPTIONS_CONFIGURED: Represents if the user options
        database flag for Cloud SQL SQL Server instance is configured or not.
      SIGNAL_TYPE_EXPOSED_TO_PUBLIC_ACCESS: Represents if a resource is
        exposed to public access.
      SIGNAL_TYPE_UNENCRYPTED_CONNECTIONS: Represents if a resources requires
        all incoming connections to use SSL or not.
      SIGNAL_TYPE_NO_ROOT_PASSWORD: Represents if a Cloud SQL database has a
        password configured for the root account or not.
      SIGNAL_TYPE_WEAK_ROOT_PASSWORD: Represents if a Cloud SQL database has a
        weak password configured for the root account.
      SIGNAL_TYPE_ENCRYPTION_KEY_NOT_CUSTOMER_MANAGED: Represents if a SQL
        database instance is not encrypted with customer-managed encryption
        keys (CMEK).
      SIGNAL_TYPE_SERVER_AUTHENTICATION_NOT_REQUIRED: Represents if The
        contained database authentication database flag for a Cloud SQL for
        SQL Server instance is not set to off.
      SIGNAL_TYPE_EXPOSED_BY_OWNERSHIP_CHAINING: Represents if the
        cross_db_ownership_chaining database flag for a Cloud SQL for SQL
        Server instance is not set to off.
      SIGNAL_TYPE_EXPOSED_TO_EXTERNAL_SCRIPTS: Represents if he external
        scripts enabled database flag for a Cloud SQL for SQL Server instance
        is not set to off.
      SIGNAL_TYPE_EXPOSED_TO_LOCAL_DATA_LOADS: Represents if the local_infile
        database flag for a Cloud SQL for MySQL instance is not set to off.
      SIGNAL_TYPE_CONNECTION_ATTEMPTS_NOT_LOGGED: Represents if the
        log_connections database flag for a Cloud SQL for PostgreSQL instance
        is not set to on.
      SIGNAL_TYPE_DISCONNECTIONS_NOT_LOGGED: Represents if the
        log_disconnections database flag for a Cloud SQL for PostgreSQL
        instance is not set to on.
      SIGNAL_TYPE_LOGGING_EXCESSIVE_STATEMENT_INFO: Represents if the
        log_min_duration_statement database flag for a Cloud SQL for
        PostgreSQL instance is not set to -1.
      SIGNAL_TYPE_EXPOSED_TO_REMOTE_ACCESS: Represents if the remote access
        database flag for a Cloud SQL for SQL Server instance is not set to
        off.
      SIGNAL_TYPE_DATABASE_NAMES_EXPOSED: Represents if the skip_show_database
        database flag for a Cloud SQL for MySQL instance is not set to on.
      SIGNAL_TYPE_SENSITIVE_TRACE_INFO_NOT_MASKED: Represents if the 3625
        (trace flag) database flag for a Cloud SQL for SQL Server instance is
        not set to on.
      SIGNAL_TYPE_PUBLIC_IP_ENABLED: Represents if public IP is enabled.
      SIGNAL_TYPE_IDLE: Represents Idle instance helps to reduce costs.
      SIGNAL_TYPE_OVERPROVISIONED: Represents instances that are unnecessarily
        large for given workload.
      SIGNAL_TYPE_HIGH_NUMBER_OF_OPEN_TABLES: Represents high number of
        concurrently opened tables.
      SIGNAL_TYPE_HIGH_NUMBER_OF_TABLES: Represents high table count close to
        SLA limit.
      SIGNAL_TYPE_HIGH_TRANSACTION_ID_UTILIZATION: Represents high number of
        unvacuumed transactions
      SIGNAL_TYPE_UNDERPROVISIONED: Represents need for more CPU and/or memory
      SIGNAL_TYPE_OUT_OF_DISK: Represents out of disk.
      SIGNAL_TYPE_SERVER_CERTIFICATE_NEAR_EXPIRY: Represents server
        certificate is near expiry.
      SIGNAL_TYPE_DATABASE_AUDITING_DISABLED: Represents database auditing is
        disabled.
      SIGNAL_TYPE_RESTRICT_AUTHORIZED_NETWORKS: Represents not restricted to
        authorized networks.
      SIGNAL_TYPE_VIOLATE_POLICY_RESTRICT_PUBLIC_IP: Represents violate org
        policy restrict public ip.
      SIGNAL_TYPE_QUOTA_LIMIT: Cluster nearing quota limit
      SIGNAL_TYPE_NO_PASSWORD_POLICY: No password policy set on resources
      SIGNAL_TYPE_CONNECTIONS_PERFORMANCE_IMPACT: Performance impact of
        connections settings
      SIGNAL_TYPE_TMP_TABLES_PERFORMANCE_IMPACT: Performance impact of
        temporary tables settings
      SIGNAL_TYPE_TRANS_LOGS_PERFORMANCE_IMPACT: Performance impact of
        transaction logs settings
      SIGNAL_TYPE_HIGH_JOINS_WITHOUT_INDEXES: Performance impact of high joins
        without indexes
      SIGNAL_TYPE_SUPERUSER_WRITING_TO_USER_TABLES: Detects events where a
        Cloud SQL superuser (postgres for PostgreSQL servers or root for MySQL
        users) writes to non-system tables.
      SIGNAL_TYPE_USER_GRANTED_ALL_PERMISSIONS: Detects events where a
        database user or role has been granted all privileges to a database,
        or to all tables, procedures, or functions in a schema.
      SIGNAL_TYPE_DATA_EXPORT_TO_EXTERNAL_CLOUD_STORAGE_BUCKET: Detects if
        database instance data exported to a Cloud Storage bucket outside of
        the organization.
      SIGNAL_TYPE_DATA_EXPORT_TO_PUBLIC_CLOUD_STORAGE_BUCKET: Detects if
        database instance data exported to a Cloud Storage bucket that is
        owned by the organization and is publicly accessible.
      SIGNAL_TYPE_WEAK_PASSWORD_HASH_ALGORITHM: Detects if a database instance
        is using a weak password hash algorithm.
      SIGNAL_TYPE_NO_USER_PASSWORD_POLICY: Detects if a database instance has
        no user password policy set.
      SIGNAL_TYPE_HOT_NODE: Detects if a database instance/cluster has a hot
        node.
      SIGNAL_TYPE_NO_POINT_IN_TIME_RECOVERY: Detects if a database instance
        has no point in time recovery enabled.
      SIGNAL_TYPE_RESOURCE_SUSPENDED: Detects if a database instance/cluster
        is suspended.
      SIGNAL_TYPE_EXPENSIVE_COMMANDS: Detects that expensive commands are
        being run on a database instance impacting overall performance.
      SIGNAL_TYPE_NO_MAINTENANCE_POLICY_CONFIGURED: Indicates that the
        instance does not have a maintenance policy configured.
      SIGNAL_TYPE_NO_DELETION_PROTECTION: Deletion Protection Disabled for the
        resource
      SIGNAL_TYPE_INEFFICIENT_QUERY: Indicates that the instance has
        inefficient queries detected.
      SIGNAL_TYPE_READ_INTENSIVE_WORKLOAD: Indicates that the instance has
        read intensive workload.
      SIGNAL_TYPE_MEMORY_LIMIT: Indicates that the instance is nearing memory
        limit.
      SIGNAL_TYPE_MAX_SERVER_MEMORY: Indicates that the instance's max server
        memory is configured higher than the recommended value.
      SIGNAL_TYPE_LARGE_ROWS: Indicates that the database has large rows
        beyond the recommended limit.
      SIGNAL_TYPE_HIGH_WRITE_PRESSURE: Heavy write pressure on the database
        rows.
      SIGNAL_TYPE_HIGH_READ_PRESSURE: Heavy read pressure on the database
        rows.
      SIGNAL_TYPE_ENCRYPTION_ORG_POLICY_NOT_SATISFIED: Encryption org policy
        not satisfied.
      SIGNAL_TYPE_LOCATION_ORG_POLICY_NOT_SATISFIED: Location org policy not
        satisfied.
      SIGNAL_TYPE_OUTDATED_MINOR_VERSION: Outdated DB minor version.
      SIGNAL_TYPE_SCHEMA_NOT_OPTIMIZED: Schema not optimized.
      SIGNAL_TYPE_MANY_IDLE_CONNECTIONS: High number of idle connections.
      SIGNAL_TYPE_REPLICATION_LAG: Replication delay.
      SIGNAL_TYPE_OUTDATED_VERSION: Outdated version.
    """
    SIGNAL_TYPE_UNSPECIFIED = 0
    SIGNAL_TYPE_NOT_PROTECTED_BY_AUTOMATIC_FAILOVER = 1
    SIGNAL_TYPE_GROUP_NOT_REPLICATING_ACROSS_REGIONS = 2
    SIGNAL_TYPE_NOT_AVAILABLE_IN_MULTIPLE_ZONES = 3
    SIGNAL_TYPE_NOT_AVAILABLE_IN_MULTIPLE_REGIONS = 4
    SIGNAL_TYPE_NO_PROMOTABLE_REPLICA = 5
    SIGNAL_TYPE_NO_AUTOMATED_BACKUP_POLICY = 6
    SIGNAL_TYPE_SHORT_BACKUP_RETENTION = 7
    SIGNAL_TYPE_LAST_BACKUP_FAILED = 8
    SIGNAL_TYPE_LAST_BACKUP_OLD = 9
    SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_2_0 = 10
    SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_3 = 11
    SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_2 = 12
    SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_1 = 13
    SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_0 = 14
    SIGNAL_TYPE_VIOLATES_CIS_CONTROLS_V8_0 = 15
    SIGNAL_TYPE_VIOLATES_NIST_800_53 = 16
    SIGNAL_TYPE_VIOLATES_NIST_800_53_R5 = 17
    SIGNAL_TYPE_VIOLATES_NIST_CYBERSECURITY_FRAMEWORK_V1_0 = 18
    SIGNAL_TYPE_VIOLATES_ISO_27001 = 19
    SIGNAL_TYPE_VIOLATES_ISO_27001_V2022 = 20
    SIGNAL_TYPE_VIOLATES_PCI_DSS_V3_2_1 = 21
    SIGNAL_TYPE_VIOLATES_PCI_DSS_V4_0 = 22
    SIGNAL_TYPE_VIOLATES_CLOUD_CONTROLS_MATRIX_V4 = 23
    SIGNAL_TYPE_VIOLATES_HIPAA = 24
    SIGNAL_TYPE_VIOLATES_SOC2_V2017 = 25
    SIGNAL_TYPE_LOGS_NOT_OPTIMIZED_FOR_TROUBLESHOOTING = 26
    SIGNAL_TYPE_QUERY_DURATIONS_NOT_LOGGED = 27
    SIGNAL_TYPE_VERBOSE_ERROR_LOGGING = 28
    SIGNAL_TYPE_QUERY_LOCK_WAITS_NOT_LOGGED = 29
    SIGNAL_TYPE_LOGGING_MOST_ERRORS = 30
    SIGNAL_TYPE_LOGGING_ONLY_CRITICAL_ERRORS = 31
    SIGNAL_TYPE_MINIMAL_ERROR_LOGGING = 32
    SIGNAL_TYPE_QUERY_STATISTICS_LOGGED = 33
    SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_CLIENT_HOSTNAME = 34
    SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_PARSER_STATISTICS = 35
    SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_PLANNER_STATISTICS = 36
    SIGNAL_TYPE_NOT_LOGGING_ONLY_DDL_STATEMENTS = 37
    SIGNAL_TYPE_LOGGING_QUERY_STATISTICS = 38
    SIGNAL_TYPE_NOT_LOGGING_TEMPORARY_FILES = 39
    SIGNAL_TYPE_CONNECTION_MAX_NOT_CONFIGURED = 40
    SIGNAL_TYPE_USER_OPTIONS_CONFIGURED = 41
    SIGNAL_TYPE_EXPOSED_TO_PUBLIC_ACCESS = 42
    SIGNAL_TYPE_UNENCRYPTED_CONNECTIONS = 43
    SIGNAL_TYPE_NO_ROOT_PASSWORD = 44
    SIGNAL_TYPE_WEAK_ROOT_PASSWORD = 45
    SIGNAL_TYPE_ENCRYPTION_KEY_NOT_CUSTOMER_MANAGED = 46
    SIGNAL_TYPE_SERVER_AUTHENTICATION_NOT_REQUIRED = 47
    SIGNAL_TYPE_EXPOSED_BY_OWNERSHIP_CHAINING = 48
    SIGNAL_TYPE_EXPOSED_TO_EXTERNAL_SCRIPTS = 49
    SIGNAL_TYPE_EXPOSED_TO_LOCAL_DATA_LOADS = 50
    SIGNAL_TYPE_CONNECTION_ATTEMPTS_NOT_LOGGED = 51
    SIGNAL_TYPE_DISCONNECTIONS_NOT_LOGGED = 52
    SIGNAL_TYPE_LOGGING_EXCESSIVE_STATEMENT_INFO = 53
    SIGNAL_TYPE_EXPOSED_TO_REMOTE_ACCESS = 54
    SIGNAL_TYPE_DATABASE_NAMES_EXPOSED = 55
    SIGNAL_TYPE_SENSITIVE_TRACE_INFO_NOT_MASKED = 56
    SIGNAL_TYPE_PUBLIC_IP_ENABLED = 57
    SIGNAL_TYPE_IDLE = 58
    SIGNAL_TYPE_OVERPROVISIONED = 59
    SIGNAL_TYPE_HIGH_NUMBER_OF_OPEN_TABLES = 60
    SIGNAL_TYPE_HIGH_NUMBER_OF_TABLES = 61
    SIGNAL_TYPE_HIGH_TRANSACTION_ID_UTILIZATION = 62
    SIGNAL_TYPE_UNDERPROVISIONED = 63
    SIGNAL_TYPE_OUT_OF_DISK = 64
    SIGNAL_TYPE_SERVER_CERTIFICATE_NEAR_EXPIRY = 65
    SIGNAL_TYPE_DATABASE_AUDITING_DISABLED = 66
    SIGNAL_TYPE_RESTRICT_AUTHORIZED_NETWORKS = 67
    SIGNAL_TYPE_VIOLATE_POLICY_RESTRICT_PUBLIC_IP = 68
    SIGNAL_TYPE_QUOTA_LIMIT = 69
    SIGNAL_TYPE_NO_PASSWORD_POLICY = 70
    SIGNAL_TYPE_CONNECTIONS_PERFORMANCE_IMPACT = 71
    SIGNAL_TYPE_TMP_TABLES_PERFORMANCE_IMPACT = 72
    SIGNAL_TYPE_TRANS_LOGS_PERFORMANCE_IMPACT = 73
    SIGNAL_TYPE_HIGH_JOINS_WITHOUT_INDEXES = 74
    SIGNAL_TYPE_SUPERUSER_WRITING_TO_USER_TABLES = 75
    SIGNAL_TYPE_USER_GRANTED_ALL_PERMISSIONS = 76
    SIGNAL_TYPE_DATA_EXPORT_TO_EXTERNAL_CLOUD_STORAGE_BUCKET = 77
    SIGNAL_TYPE_DATA_EXPORT_TO_PUBLIC_CLOUD_STORAGE_BUCKET = 78
    SIGNAL_TYPE_WEAK_PASSWORD_HASH_ALGORITHM = 79
    SIGNAL_TYPE_NO_USER_PASSWORD_POLICY = 80
    SIGNAL_TYPE_HOT_NODE = 81
    SIGNAL_TYPE_NO_POINT_IN_TIME_RECOVERY = 82
    SIGNAL_TYPE_RESOURCE_SUSPENDED = 83
    SIGNAL_TYPE_EXPENSIVE_COMMANDS = 84
    SIGNAL_TYPE_NO_MAINTENANCE_POLICY_CONFIGURED = 85
    SIGNAL_TYPE_NO_DELETION_PROTECTION = 86
    SIGNAL_TYPE_INEFFICIENT_QUERY = 87
    SIGNAL_TYPE_READ_INTENSIVE_WORKLOAD = 88
    SIGNAL_TYPE_MEMORY_LIMIT = 89
    SIGNAL_TYPE_MAX_SERVER_MEMORY = 90
    SIGNAL_TYPE_LARGE_ROWS = 91
    SIGNAL_TYPE_HIGH_WRITE_PRESSURE = 92
    SIGNAL_TYPE_HIGH_READ_PRESSURE = 93
    SIGNAL_TYPE_ENCRYPTION_ORG_POLICY_NOT_SATISFIED = 94
    SIGNAL_TYPE_LOCATION_ORG_POLICY_NOT_SATISFIED = 95
    SIGNAL_TYPE_OUTDATED_MINOR_VERSION = 96
    SIGNAL_TYPE_SCHEMA_NOT_OPTIMIZED = 97
    SIGNAL_TYPE_MANY_IDLE_CONNECTIONS = 98
    SIGNAL_TYPE_REPLICATION_LAG = 99
    SIGNAL_TYPE_OUTDATED_VERSION = 100

  class StateValueValuesEnum(_messages.Enum):
    r"""StateValueValuesEnum enum type.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      ACTIVE: The signal requires attention and has not been addressed yet.
      RESOLVED: The signal has been fixed, triaged as a non-issue or otherwise
        addressed and is no longer active.
      MUTED: The signal has been muted.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    RESOLVED = 2
    MUTED = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AdditionalMetadataValue(_messages.Message):
    r"""Any other additional metadata

    Messages:
      AdditionalProperty: An additional property for a AdditionalMetadataValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AdditionalMetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  additionalMetadata = _messages.MessageField('AdditionalMetadataValue', 1)
  compliance = _messages.MessageField('Compliance', 2, repeated=True)
  description = _messages.StringField(3)
  eventTime = _messages.StringField(4)
  externalUri = _messages.StringField(5)
  location = _messages.StringField(6)
  name = _messages.StringField(7)
  provider = _messages.EnumField('ProviderValueValuesEnum', 8)
  resourceContainer = _messages.StringField(9)
  resourceName = _messages.StringField(10)
  signalClass = _messages.EnumField('SignalClassValueValuesEnum', 11)
  signalId = _messages.StringField(12)
  signalSeverity = _messages.EnumField('SignalSeverityValueValuesEnum', 13)
  signalType = _messages.EnumField('SignalTypeValueValuesEnum', 14)
  state = _messages.EnumField('StateValueValuesEnum', 15)


class DatabaseResourceId(_messages.Message):
  r"""DatabaseResourceId will serve as primary key for any resource ingestion
  event.

  Enums:
    ProviderValueValuesEnum: Required. Cloud provider name. Ex:
      GCP/AWS/Azure/OnPrem/SelfManaged

  Fields:
    provider: Required. Cloud provider name. Ex:
      GCP/AWS/Azure/OnPrem/SelfManaged
    providerDescription: Optional. Needs to be used only when the provider is
      PROVIDER_OTHER.
    resourceType: Required. The type of resource this ID is identifying. Ex
      go/keep-sorted start alloydb.googleapis.com/Cluster,
      alloydb.googleapis.com/Instance, bigtableadmin.googleapis.com/Cluster,
      bigtableadmin.googleapis.com/Instance compute.googleapis.com/Instance
      firestore.googleapis.com/Database, redis.googleapis.com/Instance,
      redis.googleapis.com/Cluster,
      oracledatabase.googleapis.com/CloudExadataInfrastructure
      oracledatabase.googleapis.com/CloudVmCluster
      oracledatabase.googleapis.com/AutonomousDatabase
      spanner.googleapis.com/Instance, spanner.googleapis.com/Database,
      sqladmin.googleapis.com/Instance, go/keep-sorted end REQUIRED Please
      refer go/condor-common-datamodel
    uniqueId: Required. A service-local token that distinguishes this resource
      from other resources within the same service.
  """

  class ProviderValueValuesEnum(_messages.Enum):
    r"""Required. Cloud provider name. Ex: GCP/AWS/Azure/OnPrem/SelfManaged

    Values:
      PROVIDER_UNSPECIFIED: <no description>
      GCP: Google cloud platform provider
      AWS: Amazon web service
      AZURE: Azure web service
      ONPREM: On-prem database resources.
      SELFMANAGED: Self-managed database provider. These are resources on a
        cloud platform, e.g., database resource installed in a GCE VM, but not
        a managed database service.
      PROVIDER_OTHER: For the rest of the other categories. Other refers to
        the rest of other database service providers, this could be smaller
        cloud provider. This needs to be provided when the provider is known,
        but it is not present in the existing set of enum values.
    """
    PROVIDER_UNSPECIFIED = 0
    GCP = 1
    AWS = 2
    AZURE = 3
    ONPREM = 4
    SELFMANAGED = 5
    PROVIDER_OTHER = 6

  provider = _messages.EnumField('ProviderValueValuesEnum', 1)
  providerDescription = _messages.StringField(2)
  resourceType = _messages.StringField(3)
  uniqueId = _messages.StringField(4)


class DatabaseResourceMetadata(_messages.Message):
  r"""Common model for database resource instance metadata. Next ID: 26

  Enums:
    CurrentStateValueValuesEnum: Current state of the instance.
    EditionValueValuesEnum: Optional. Edition represents whether the instance
      is ENTERPRISE or ENTERPRISE_PLUS. This information is core to Cloud SQL
      only and is used to identify the edition of the instance.
    ExpectedStateValueValuesEnum: The state that the instance is expected to
      be in. For example, an instance state can transition to UNHEALTHY due to
      wrong patch update, while the expected state will remain at the HEALTHY.
    InstanceTypeValueValuesEnum: The type of the instance. Specified at
      creation time.
    SuspensionReasonValueValuesEnum: Optional. Suspension reason for the
      resource.

  Fields:
    availabilityConfiguration: Availability configuration for this instance
    backupConfiguration: Backup configuration for this instance
    backupRun: Latest backup run information for this instance
    backupdrConfiguration: Optional. BackupDR Configuration for the resource.
    creationTime: The creation time of the resource, i.e. the time when
      resource is created and recorded in partner service.
    currentState: Current state of the instance.
    customMetadata: Any custom metadata associated with the resource
    edition: Optional. Edition represents whether the instance is ENTERPRISE
      or ENTERPRISE_PLUS. This information is core to Cloud SQL only and is
      used to identify the edition of the instance.
    entitlements: Entitlements associated with the resource
    expectedState: The state that the instance is expected to be in. For
      example, an instance state can transition to UNHEALTHY due to wrong
      patch update, while the expected state will remain at the HEALTHY.
    gcbdrConfiguration: GCBDR configuration for the resource.
    id: Required. Unique identifier for a Database resource
    instanceType: The type of the instance. Specified at creation time.
    location: The resource location. REQUIRED
    machineConfiguration: Machine configuration for this resource.
    primaryResourceId: Identifier for this resource's immediate parent/primary
      resource if the current resource is a replica or derived form of another
      Database resource. Else it would be NULL. REQUIRED if the immediate
      parent exists when first time resource is getting ingested, otherwise
      optional.
    primaryResourceLocation: Primary resource location. REQUIRED if the
      immediate parent exists when first time resource is getting ingested,
      otherwise optional.
    product: The product this resource represents.
    resourceContainer: Closest parent Cloud Resource Manager container of this
      resource. It must be resource name of a Cloud Resource Manager project
      with the format of "/", such as "projects/123". For GCP provided
      resources, number should be project number.
    resourceName: Required. Different from DatabaseResourceId.unique_id, a
      resource name can be reused over time. That is, after a resource named
      "ABC" is deleted, the name "ABC" can be used to to create a new resource
      within the same source. Resource name to follow CAIS resource_name
      format as noted here go/condor-common-datamodel
    suspensionReason: Optional. Suspension reason for the resource.
    tagsSet: Optional. Tags associated with this resources.
    updationTime: The time at which the resource was updated and recorded at
      partner service.
    userLabelSet: User-provided labels associated with the resource
  """

  class CurrentStateValueValuesEnum(_messages.Enum):
    r"""Current state of the instance.

    Values:
      STATE_UNSPECIFIED: <no description>
      HEALTHY: The instance is running.
      UNHEALTHY: Instance being created, updated, deleted or under maintenance
      SUSPENDED: When instance is suspended
      DELETED: Instance is deleted.
      STATE_OTHER: For rest of the other category
    """
    STATE_UNSPECIFIED = 0
    HEALTHY = 1
    UNHEALTHY = 2
    SUSPENDED = 3
    DELETED = 4
    STATE_OTHER = 5

  class EditionValueValuesEnum(_messages.Enum):
    r"""Optional. Edition represents whether the instance is ENTERPRISE or
    ENTERPRISE_PLUS. This information is core to Cloud SQL only and is used to
    identify the edition of the instance.

    Values:
      EDITION_UNSPECIFIED: Default, to make it consistent with instance
        edition enum.
      EDITION_ENTERPRISE: Represents the enterprise edition.
      EDITION_ENTERPRISE_PLUS: Represents the enterprise plus edition.
    """
    EDITION_UNSPECIFIED = 0
    EDITION_ENTERPRISE = 1
    EDITION_ENTERPRISE_PLUS = 2

  class ExpectedStateValueValuesEnum(_messages.Enum):
    r"""The state that the instance is expected to be in. For example, an
    instance state can transition to UNHEALTHY due to wrong patch update,
    while the expected state will remain at the HEALTHY.

    Values:
      STATE_UNSPECIFIED: <no description>
      HEALTHY: The instance is running.
      UNHEALTHY: Instance being created, updated, deleted or under maintenance
      SUSPENDED: When instance is suspended
      DELETED: Instance is deleted.
      STATE_OTHER: For rest of the other category
    """
    STATE_UNSPECIFIED = 0
    HEALTHY = 1
    UNHEALTHY = 2
    SUSPENDED = 3
    DELETED = 4
    STATE_OTHER = 5

  class InstanceTypeValueValuesEnum(_messages.Enum):
    r"""The type of the instance. Specified at creation time.

    Values:
      INSTANCE_TYPE_UNSPECIFIED: Unspecified.
      SUB_RESOURCE_TYPE_UNSPECIFIED: For rest of the other categories.
      PRIMARY: A regular primary database instance.
      SECONDARY: A cluster or an instance acting as a secondary.
      READ_REPLICA: An instance acting as a read-replica.
      OTHER: For rest of the other categories.
      SUB_RESOURCE_TYPE_PRIMARY: A regular primary database instance.
      SUB_RESOURCE_TYPE_SECONDARY: A cluster or an instance acting as a
        secondary.
      SUB_RESOURCE_TYPE_READ_REPLICA: An instance acting as a read-replica.
      SUB_RESOURCE_TYPE_EXTERNAL_PRIMARY: An instance acting as an external
        primary.
      SUB_RESOURCE_TYPE_OTHER: For rest of the other categories.
    """
    INSTANCE_TYPE_UNSPECIFIED = 0
    SUB_RESOURCE_TYPE_UNSPECIFIED = 1
    PRIMARY = 2
    SECONDARY = 3
    READ_REPLICA = 4
    OTHER = 5
    SUB_RESOURCE_TYPE_PRIMARY = 6
    SUB_RESOURCE_TYPE_SECONDARY = 7
    SUB_RESOURCE_TYPE_READ_REPLICA = 8
    SUB_RESOURCE_TYPE_EXTERNAL_PRIMARY = 9
    SUB_RESOURCE_TYPE_OTHER = 10

  class SuspensionReasonValueValuesEnum(_messages.Enum):
    r"""Optional. Suspension reason for the resource.

    Values:
      SUSPENSION_REASON_UNSPECIFIED: Suspension reason is unspecified.
      WIPEOUT_HIDE_EVENT: Wipeout hide event.
      WIPEOUT_PURGE_EVENT: Wipeout purge event.
      BILLING_DISABLED: Billing disabled for project
      ABUSER_DETECTED: Abuse detected for resource
      ENCRYPTION_KEY_INACCESSIBLE: Encryption key inaccessible.
      REPLICATED_CLUSTER_ENCRYPTION_KEY_INACCESSIBLE: Replicated cluster
        encryption key inaccessible.
    """
    SUSPENSION_REASON_UNSPECIFIED = 0
    WIPEOUT_HIDE_EVENT = 1
    WIPEOUT_PURGE_EVENT = 2
    BILLING_DISABLED = 3
    ABUSER_DETECTED = 4
    ENCRYPTION_KEY_INACCESSIBLE = 5
    REPLICATED_CLUSTER_ENCRYPTION_KEY_INACCESSIBLE = 6

  availabilityConfiguration = _messages.MessageField('AvailabilityConfiguration', 1)
  backupConfiguration = _messages.MessageField('BackupConfiguration', 2)
  backupRun = _messages.MessageField('BackupRun', 3)
  backupdrConfiguration = _messages.MessageField('BackupDRConfiguration', 4)
  creationTime = _messages.StringField(5)
  currentState = _messages.EnumField('CurrentStateValueValuesEnum', 6)
  customMetadata = _messages.MessageField('CustomMetadataData', 7)
  edition = _messages.EnumField('EditionValueValuesEnum', 8)
  entitlements = _messages.MessageField('Entitlement', 9, repeated=True)
  expectedState = _messages.EnumField('ExpectedStateValueValuesEnum', 10)
  gcbdrConfiguration = _messages.MessageField('GCBDRConfiguration', 11)
  id = _messages.MessageField('DatabaseResourceId', 12)
  instanceType = _messages.EnumField('InstanceTypeValueValuesEnum', 13)
  location = _messages.StringField(14)
  machineConfiguration = _messages.MessageField('MachineConfiguration', 15)
  primaryResourceId = _messages.MessageField('DatabaseResourceId', 16)
  primaryResourceLocation = _messages.StringField(17)
  product = _messages.MessageField('Product', 18)
  resourceContainer = _messages.StringField(19)
  resourceName = _messages.StringField(20)
  suspensionReason = _messages.EnumField('SuspensionReasonValueValuesEnum', 21)
  tagsSet = _messages.MessageField('Tags', 22)
  updationTime = _messages.StringField(23)
  userLabelSet = _messages.MessageField('UserLabels', 24)


class DatabaseResourceRecommendationSignalData(_messages.Message):
  r"""Common model for database resource recommendation signal data.

  Enums:
    RecommendationStateValueValuesEnum: Required. Recommendation state
    SignalTypeValueValuesEnum: Required. Type of signal, for example,
      `SIGNAL_TYPE_IDLE`, `SIGNAL_TYPE_HIGH_NUMBER_OF_TABLES`, etc.

  Messages:
    AdditionalMetadataValue: Optional. Any other additional metadata specific
      to recommendation

  Fields:
    additionalMetadata: Optional. Any other additional metadata specific to
      recommendation
    lastRefreshTime: Required. last time recommendationw as refreshed
    recommendationState: Required. Recommendation state
    recommender: Required. Name of recommendation. Examples:
      organizations/1234/locations/us-central1/recommenders/google.cloudsql.in
      stance.PerformanceRecommender/recommendations/9876
    recommenderId: Required. ID of recommender. Examples:
      "google.cloudsql.instance.PerformanceRecommender"
    recommenderSubtype: Required. Contains an identifier for a subtype of
      recommendations produced for the same recommender. Subtype is a function
      of content and impact, meaning a new subtype might be added when
      significant changes to `content` or `primary_impact.category` are
      introduced. See the Recommenders section to see a list of subtypes for a
      given Recommender. Examples: For recommender =
      "google.cloudsql.instance.PerformanceRecommender", recommender_subtype
      can be "MYSQL_HIGH_NUMBER_OF_OPEN_TABLES_BEST_PRACTICE"/"POSTGRES_HIGH_T
      RANSACTION_ID_UTILIZATION_BEST_PRACTICE"
    resourceName: Required. Database resource name associated with the signal.
      Resource name to follow CAIS resource_name format as noted here
      go/condor-common-datamodel
    signalType: Required. Type of signal, for example, `SIGNAL_TYPE_IDLE`,
      `SIGNAL_TYPE_HIGH_NUMBER_OF_TABLES`, etc.
  """

  class RecommendationStateValueValuesEnum(_messages.Enum):
    r"""Required. Recommendation state

    Values:
      UNSPECIFIED: <no description>
      ACTIVE: Recommendation is active and can be applied. ACTIVE
        recommendations can be marked as CLAIMED, SUCCEEDED, or FAILED.
      CLAIMED: Recommendation is in claimed state. Recommendations content is
        immutable and cannot be updated by Google. CLAIMED recommendations can
        be marked as CLAIMED, SUCCEEDED, or FAILED.
      SUCCEEDED: Recommendation is in succeeded state. Recommendations content
        is immutable and cannot be updated by Google. SUCCEEDED
        recommendations can be marked as SUCCEEDED, or FAILED.
      FAILED: Recommendation is in failed state. Recommendations content is
        immutable and cannot be updated by Google. FAILED recommendations can
        be marked as SUCCEEDED, or FAILED.
      DISMISSED: Recommendation is in dismissed state. Recommendation content
        can be updated by Google. DISMISSED recommendations can be marked as
        ACTIVE.
    """
    UNSPECIFIED = 0
    ACTIVE = 1
    CLAIMED = 2
    SUCCEEDED = 3
    FAILED = 4
    DISMISSED = 5

  class SignalTypeValueValuesEnum(_messages.Enum):
    r"""Required. Type of signal, for example, `SIGNAL_TYPE_IDLE`,
    `SIGNAL_TYPE_HIGH_NUMBER_OF_TABLES`, etc.

    Values:
      SIGNAL_TYPE_UNSPECIFIED: Unspecified.
      SIGNAL_TYPE_NOT_PROTECTED_BY_AUTOMATIC_FAILOVER: Represents if a
        resource is protected by automatic failover. Checks for resources that
        are configured to have redundancy within a region that enables
        automatic failover.
      SIGNAL_TYPE_GROUP_NOT_REPLICATING_ACROSS_REGIONS: Represents if a group
        is replicating across regions. Checks for resources that are
        configured to have redundancy, and ongoing replication, across
        regions.
      SIGNAL_TYPE_NOT_AVAILABLE_IN_MULTIPLE_ZONES: Represents if the resource
        is available in multiple zones or not.
      SIGNAL_TYPE_NOT_AVAILABLE_IN_MULTIPLE_REGIONS: Represents if a resource
        is available in multiple regions.
      SIGNAL_TYPE_NO_PROMOTABLE_REPLICA: Represents if a resource has a
        promotable replica.
      SIGNAL_TYPE_NO_AUTOMATED_BACKUP_POLICY: Represents if a resource has an
        automated backup policy.
      SIGNAL_TYPE_SHORT_BACKUP_RETENTION: Represents if a resources has a
        short backup retention period.
      SIGNAL_TYPE_LAST_BACKUP_FAILED: Represents if the last backup of a
        resource failed.
      SIGNAL_TYPE_LAST_BACKUP_OLD: Represents if the last backup of a resource
        is older than some threshold value.
      SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_2_0: Represents if a resource
        violates CIS GCP Foundation 2.0.
      SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_3: Represents if a resource
        violates CIS GCP Foundation 1.3.
      SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_2: Represents if a resource
        violates CIS GCP Foundation 1.2.
      SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_1: Represents if a resource
        violates CIS GCP Foundation 1.1.
      SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_0: Represents if a resource
        violates CIS GCP Foundation 1.0.
      SIGNAL_TYPE_VIOLATES_CIS_CONTROLS_V8_0: Represents if a resource
        violates CIS Controls 8.0.
      SIGNAL_TYPE_VIOLATES_NIST_800_53: Represents if a resource violates NIST
        800-53.
      SIGNAL_TYPE_VIOLATES_NIST_800_53_R5: Represents if a resource violates
        NIST 800-53 R5.
      SIGNAL_TYPE_VIOLATES_NIST_CYBERSECURITY_FRAMEWORK_V1_0: Represents if a
        resource violates NIST Cybersecurity Framework 1.0.
      SIGNAL_TYPE_VIOLATES_ISO_27001: Represents if a resource violates
        ISO-27001.
      SIGNAL_TYPE_VIOLATES_ISO_27001_V2022: Represents if a resource violates
        ISO 27001 2022.
      SIGNAL_TYPE_VIOLATES_PCI_DSS_V3_2_1: Represents if a resource violates
        PCI-DSS v3.2.1.
      SIGNAL_TYPE_VIOLATES_PCI_DSS_V4_0: Represents if a resource violates
        PCI-DSS v4.0.
      SIGNAL_TYPE_VIOLATES_CLOUD_CONTROLS_MATRIX_V4: Represents if a resource
        violates Cloud Controls Matrix v4.0.
      SIGNAL_TYPE_VIOLATES_HIPAA: Represents if a resource violates HIPAA.
      SIGNAL_TYPE_VIOLATES_SOC2_V2017: Represents if a resource violates SOC2
        v2017.
      SIGNAL_TYPE_LOGS_NOT_OPTIMIZED_FOR_TROUBLESHOOTING: Represents if
        log_checkpoints database flag for a Cloud SQL for PostgreSQL instance
        is not set to on.
      SIGNAL_TYPE_QUERY_DURATIONS_NOT_LOGGED: Represents if the log_duration
        database flag for a Cloud SQL for PostgreSQL instance is not set to
        on.
      SIGNAL_TYPE_VERBOSE_ERROR_LOGGING: Represents if the log_error_verbosity
        database flag for a Cloud SQL for PostgreSQL instance is not set to
        default or stricter (default or terse).
      SIGNAL_TYPE_QUERY_LOCK_WAITS_NOT_LOGGED: Represents if the
        log_lock_waits database flag for a Cloud SQL for PostgreSQL instance
        is not set to on.
      SIGNAL_TYPE_LOGGING_MOST_ERRORS: Represents if the
        log_min_error_statement database flag for a Cloud SQL for PostgreSQL
        instance is not set appropriately.
      SIGNAL_TYPE_LOGGING_ONLY_CRITICAL_ERRORS: Represents if the
        log_min_error_statement database flag for a Cloud SQL for PostgreSQL
        instance does not have an appropriate severity level.
      SIGNAL_TYPE_MINIMAL_ERROR_LOGGING: Represents if the log_min_messages
        database flag for a Cloud SQL for PostgreSQL instance is not set to
        warning or another recommended value.
      SIGNAL_TYPE_QUERY_STATISTICS_LOGGED: Represents if the databaseFlags
        property of instance metadata for the log_executor_status field is set
        to on.
      SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_CLIENT_HOSTNAME: Represents if the
        log_hostname database flag for a Cloud SQL for PostgreSQL instance is
        not set to off.
      SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_PARSER_STATISTICS: Represents if the
        log_parser_stats database flag for a Cloud SQL for PostgreSQL instance
        is not set to off.
      SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_PLANNER_STATISTICS: Represents if the
        log_planner_stats database flag for a Cloud SQL for PostgreSQL
        instance is not set to off.
      SIGNAL_TYPE_NOT_LOGGING_ONLY_DDL_STATEMENTS: Represents if the
        log_statement database flag for a Cloud SQL for PostgreSQL instance is
        not set to DDL (all data definition statements).
      SIGNAL_TYPE_LOGGING_QUERY_STATISTICS: Represents if the
        log_statement_stats database flag for a Cloud SQL for PostgreSQL
        instance is not set to off.
      SIGNAL_TYPE_NOT_LOGGING_TEMPORARY_FILES: Represents if the
        log_temp_files database flag for a Cloud SQL for PostgreSQL instance
        is not set to "0". (NOTE: 0 = ON)
      SIGNAL_TYPE_CONNECTION_MAX_NOT_CONFIGURED: Represents if the user
        connections database flag for a Cloud SQL for SQL Server instance is
        configured.
      SIGNAL_TYPE_USER_OPTIONS_CONFIGURED: Represents if the user options
        database flag for Cloud SQL SQL Server instance is configured or not.
      SIGNAL_TYPE_EXPOSED_TO_PUBLIC_ACCESS: Represents if a resource is
        exposed to public access.
      SIGNAL_TYPE_UNENCRYPTED_CONNECTIONS: Represents if a resources requires
        all incoming connections to use SSL or not.
      SIGNAL_TYPE_NO_ROOT_PASSWORD: Represents if a Cloud SQL database has a
        password configured for the root account or not.
      SIGNAL_TYPE_WEAK_ROOT_PASSWORD: Represents if a Cloud SQL database has a
        weak password configured for the root account.
      SIGNAL_TYPE_ENCRYPTION_KEY_NOT_CUSTOMER_MANAGED: Represents if a SQL
        database instance is not encrypted with customer-managed encryption
        keys (CMEK).
      SIGNAL_TYPE_SERVER_AUTHENTICATION_NOT_REQUIRED: Represents if The
        contained database authentication database flag for a Cloud SQL for
        SQL Server instance is not set to off.
      SIGNAL_TYPE_EXPOSED_BY_OWNERSHIP_CHAINING: Represents if the
        cross_db_ownership_chaining database flag for a Cloud SQL for SQL
        Server instance is not set to off.
      SIGNAL_TYPE_EXPOSED_TO_EXTERNAL_SCRIPTS: Represents if he external
        scripts enabled database flag for a Cloud SQL for SQL Server instance
        is not set to off.
      SIGNAL_TYPE_EXPOSED_TO_LOCAL_DATA_LOADS: Represents if the local_infile
        database flag for a Cloud SQL for MySQL instance is not set to off.
      SIGNAL_TYPE_CONNECTION_ATTEMPTS_NOT_LOGGED: Represents if the
        log_connections database flag for a Cloud SQL for PostgreSQL instance
        is not set to on.
      SIGNAL_TYPE_DISCONNECTIONS_NOT_LOGGED: Represents if the
        log_disconnections database flag for a Cloud SQL for PostgreSQL
        instance is not set to on.
      SIGNAL_TYPE_LOGGING_EXCESSIVE_STATEMENT_INFO: Represents if the
        log_min_duration_statement database flag for a Cloud SQL for
        PostgreSQL instance is not set to -1.
      SIGNAL_TYPE_EXPOSED_TO_REMOTE_ACCESS: Represents if the remote access
        database flag for a Cloud SQL for SQL Server instance is not set to
        off.
      SIGNAL_TYPE_DATABASE_NAMES_EXPOSED: Represents if the skip_show_database
        database flag for a Cloud SQL for MySQL instance is not set to on.
      SIGNAL_TYPE_SENSITIVE_TRACE_INFO_NOT_MASKED: Represents if the 3625
        (trace flag) database flag for a Cloud SQL for SQL Server instance is
        not set to on.
      SIGNAL_TYPE_PUBLIC_IP_ENABLED: Represents if public IP is enabled.
      SIGNAL_TYPE_IDLE: Represents Idle instance helps to reduce costs.
      SIGNAL_TYPE_OVERPROVISIONED: Represents instances that are unnecessarily
        large for given workload.
      SIGNAL_TYPE_HIGH_NUMBER_OF_OPEN_TABLES: Represents high number of
        concurrently opened tables.
      SIGNAL_TYPE_HIGH_NUMBER_OF_TABLES: Represents high table count close to
        SLA limit.
      SIGNAL_TYPE_HIGH_TRANSACTION_ID_UTILIZATION: Represents high number of
        unvacuumed transactions
      SIGNAL_TYPE_UNDERPROVISIONED: Represents need for more CPU and/or memory
      SIGNAL_TYPE_OUT_OF_DISK: Represents out of disk.
      SIGNAL_TYPE_SERVER_CERTIFICATE_NEAR_EXPIRY: Represents server
        certificate is near expiry.
      SIGNAL_TYPE_DATABASE_AUDITING_DISABLED: Represents database auditing is
        disabled.
      SIGNAL_TYPE_RESTRICT_AUTHORIZED_NETWORKS: Represents not restricted to
        authorized networks.
      SIGNAL_TYPE_VIOLATE_POLICY_RESTRICT_PUBLIC_IP: Represents violate org
        policy restrict public ip.
      SIGNAL_TYPE_QUOTA_LIMIT: Cluster nearing quota limit
      SIGNAL_TYPE_NO_PASSWORD_POLICY: No password policy set on resources
      SIGNAL_TYPE_CONNECTIONS_PERFORMANCE_IMPACT: Performance impact of
        connections settings
      SIGNAL_TYPE_TMP_TABLES_PERFORMANCE_IMPACT: Performance impact of
        temporary tables settings
      SIGNAL_TYPE_TRANS_LOGS_PERFORMANCE_IMPACT: Performance impact of
        transaction logs settings
      SIGNAL_TYPE_HIGH_JOINS_WITHOUT_INDEXES: Performance impact of high joins
        without indexes
      SIGNAL_TYPE_SUPERUSER_WRITING_TO_USER_TABLES: Detects events where a
        Cloud SQL superuser (postgres for PostgreSQL servers or root for MySQL
        users) writes to non-system tables.
      SIGNAL_TYPE_USER_GRANTED_ALL_PERMISSIONS: Detects events where a
        database user or role has been granted all privileges to a database,
        or to all tables, procedures, or functions in a schema.
      SIGNAL_TYPE_DATA_EXPORT_TO_EXTERNAL_CLOUD_STORAGE_BUCKET: Detects if
        database instance data exported to a Cloud Storage bucket outside of
        the organization.
      SIGNAL_TYPE_DATA_EXPORT_TO_PUBLIC_CLOUD_STORAGE_BUCKET: Detects if
        database instance data exported to a Cloud Storage bucket that is
        owned by the organization and is publicly accessible.
      SIGNAL_TYPE_WEAK_PASSWORD_HASH_ALGORITHM: Detects if a database instance
        is using a weak password hash algorithm.
      SIGNAL_TYPE_NO_USER_PASSWORD_POLICY: Detects if a database instance has
        no user password policy set.
      SIGNAL_TYPE_HOT_NODE: Detects if a database instance/cluster has a hot
        node.
      SIGNAL_TYPE_NO_POINT_IN_TIME_RECOVERY: Detects if a database instance
        has no point in time recovery enabled.
      SIGNAL_TYPE_RESOURCE_SUSPENDED: Detects if a database instance/cluster
        is suspended.
      SIGNAL_TYPE_EXPENSIVE_COMMANDS: Detects that expensive commands are
        being run on a database instance impacting overall performance.
      SIGNAL_TYPE_NO_MAINTENANCE_POLICY_CONFIGURED: Indicates that the
        instance does not have a maintenance policy configured.
      SIGNAL_TYPE_NO_DELETION_PROTECTION: Deletion Protection Disabled for the
        resource
      SIGNAL_TYPE_INEFFICIENT_QUERY: Indicates that the instance has
        inefficient queries detected.
      SIGNAL_TYPE_READ_INTENSIVE_WORKLOAD: Indicates that the instance has
        read intensive workload.
      SIGNAL_TYPE_MEMORY_LIMIT: Indicates that the instance is nearing memory
        limit.
      SIGNAL_TYPE_MAX_SERVER_MEMORY: Indicates that the instance's max server
        memory is configured higher than the recommended value.
      SIGNAL_TYPE_LARGE_ROWS: Indicates that the database has large rows
        beyond the recommended limit.
      SIGNAL_TYPE_HIGH_WRITE_PRESSURE: Heavy write pressure on the database
        rows.
      SIGNAL_TYPE_HIGH_READ_PRESSURE: Heavy read pressure on the database
        rows.
      SIGNAL_TYPE_ENCRYPTION_ORG_POLICY_NOT_SATISFIED: Encryption org policy
        not satisfied.
      SIGNAL_TYPE_LOCATION_ORG_POLICY_NOT_SATISFIED: Location org policy not
        satisfied.
      SIGNAL_TYPE_OUTDATED_MINOR_VERSION: Outdated DB minor version.
      SIGNAL_TYPE_SCHEMA_NOT_OPTIMIZED: Schema not optimized.
      SIGNAL_TYPE_MANY_IDLE_CONNECTIONS: High number of idle connections.
      SIGNAL_TYPE_REPLICATION_LAG: Replication delay.
      SIGNAL_TYPE_OUTDATED_VERSION: Outdated version.
    """
    SIGNAL_TYPE_UNSPECIFIED = 0
    SIGNAL_TYPE_NOT_PROTECTED_BY_AUTOMATIC_FAILOVER = 1
    SIGNAL_TYPE_GROUP_NOT_REPLICATING_ACROSS_REGIONS = 2
    SIGNAL_TYPE_NOT_AVAILABLE_IN_MULTIPLE_ZONES = 3
    SIGNAL_TYPE_NOT_AVAILABLE_IN_MULTIPLE_REGIONS = 4
    SIGNAL_TYPE_NO_PROMOTABLE_REPLICA = 5
    SIGNAL_TYPE_NO_AUTOMATED_BACKUP_POLICY = 6
    SIGNAL_TYPE_SHORT_BACKUP_RETENTION = 7
    SIGNAL_TYPE_LAST_BACKUP_FAILED = 8
    SIGNAL_TYPE_LAST_BACKUP_OLD = 9
    SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_2_0 = 10
    SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_3 = 11
    SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_2 = 12
    SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_1 = 13
    SIGNAL_TYPE_VIOLATES_CIS_GCP_FOUNDATION_1_0 = 14
    SIGNAL_TYPE_VIOLATES_CIS_CONTROLS_V8_0 = 15
    SIGNAL_TYPE_VIOLATES_NIST_800_53 = 16
    SIGNAL_TYPE_VIOLATES_NIST_800_53_R5 = 17
    SIGNAL_TYPE_VIOLATES_NIST_CYBERSECURITY_FRAMEWORK_V1_0 = 18
    SIGNAL_TYPE_VIOLATES_ISO_27001 = 19
    SIGNAL_TYPE_VIOLATES_ISO_27001_V2022 = 20
    SIGNAL_TYPE_VIOLATES_PCI_DSS_V3_2_1 = 21
    SIGNAL_TYPE_VIOLATES_PCI_DSS_V4_0 = 22
    SIGNAL_TYPE_VIOLATES_CLOUD_CONTROLS_MATRIX_V4 = 23
    SIGNAL_TYPE_VIOLATES_HIPAA = 24
    SIGNAL_TYPE_VIOLATES_SOC2_V2017 = 25
    SIGNAL_TYPE_LOGS_NOT_OPTIMIZED_FOR_TROUBLESHOOTING = 26
    SIGNAL_TYPE_QUERY_DURATIONS_NOT_LOGGED = 27
    SIGNAL_TYPE_VERBOSE_ERROR_LOGGING = 28
    SIGNAL_TYPE_QUERY_LOCK_WAITS_NOT_LOGGED = 29
    SIGNAL_TYPE_LOGGING_MOST_ERRORS = 30
    SIGNAL_TYPE_LOGGING_ONLY_CRITICAL_ERRORS = 31
    SIGNAL_TYPE_MINIMAL_ERROR_LOGGING = 32
    SIGNAL_TYPE_QUERY_STATISTICS_LOGGED = 33
    SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_CLIENT_HOSTNAME = 34
    SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_PARSER_STATISTICS = 35
    SIGNAL_TYPE_EXCESSIVE_LOGGING_OF_PLANNER_STATISTICS = 36
    SIGNAL_TYPE_NOT_LOGGING_ONLY_DDL_STATEMENTS = 37
    SIGNAL_TYPE_LOGGING_QUERY_STATISTICS = 38
    SIGNAL_TYPE_NOT_LOGGING_TEMPORARY_FILES = 39
    SIGNAL_TYPE_CONNECTION_MAX_NOT_CONFIGURED = 40
    SIGNAL_TYPE_USER_OPTIONS_CONFIGURED = 41
    SIGNAL_TYPE_EXPOSED_TO_PUBLIC_ACCESS = 42
    SIGNAL_TYPE_UNENCRYPTED_CONNECTIONS = 43
    SIGNAL_TYPE_NO_ROOT_PASSWORD = 44
    SIGNAL_TYPE_WEAK_ROOT_PASSWORD = 45
    SIGNAL_TYPE_ENCRYPTION_KEY_NOT_CUSTOMER_MANAGED = 46
    SIGNAL_TYPE_SERVER_AUTHENTICATION_NOT_REQUIRED = 47
    SIGNAL_TYPE_EXPOSED_BY_OWNERSHIP_CHAINING = 48
    SIGNAL_TYPE_EXPOSED_TO_EXTERNAL_SCRIPTS = 49
    SIGNAL_TYPE_EXPOSED_TO_LOCAL_DATA_LOADS = 50
    SIGNAL_TYPE_CONNECTION_ATTEMPTS_NOT_LOGGED = 51
    SIGNAL_TYPE_DISCONNECTIONS_NOT_LOGGED = 52
    SIGNAL_TYPE_LOGGING_EXCESSIVE_STATEMENT_INFO = 53
    SIGNAL_TYPE_EXPOSED_TO_REMOTE_ACCESS = 54
    SIGNAL_TYPE_DATABASE_NAMES_EXPOSED = 55
    SIGNAL_TYPE_SENSITIVE_TRACE_INFO_NOT_MASKED = 56
    SIGNAL_TYPE_PUBLIC_IP_ENABLED = 57
    SIGNAL_TYPE_IDLE = 58
    SIGNAL_TYPE_OVERPROVISIONED = 59
    SIGNAL_TYPE_HIGH_NUMBER_OF_OPEN_TABLES = 60
    SIGNAL_TYPE_HIGH_NUMBER_OF_TABLES = 61
    SIGNAL_TYPE_HIGH_TRANSACTION_ID_UTILIZATION = 62
    SIGNAL_TYPE_UNDERPROVISIONED = 63
    SIGNAL_TYPE_OUT_OF_DISK = 64
    SIGNAL_TYPE_SERVER_CERTIFICATE_NEAR_EXPIRY = 65
    SIGNAL_TYPE_DATABASE_AUDITING_DISABLED = 66
    SIGNAL_TYPE_RESTRICT_AUTHORIZED_NETWORKS = 67
    SIGNAL_TYPE_VIOLATE_POLICY_RESTRICT_PUBLIC_IP = 68
    SIGNAL_TYPE_QUOTA_LIMIT = 69
    SIGNAL_TYPE_NO_PASSWORD_POLICY = 70
    SIGNAL_TYPE_CONNECTIONS_PERFORMANCE_IMPACT = 71
    SIGNAL_TYPE_TMP_TABLES_PERFORMANCE_IMPACT = 72
    SIGNAL_TYPE_TRANS_LOGS_PERFORMANCE_IMPACT = 73
    SIGNAL_TYPE_HIGH_JOINS_WITHOUT_INDEXES = 74
    SIGNAL_TYPE_SUPERUSER_WRITING_TO_USER_TABLES = 75
    SIGNAL_TYPE_USER_GRANTED_ALL_PERMISSIONS = 76
    SIGNAL_TYPE_DATA_EXPORT_TO_EXTERNAL_CLOUD_STORAGE_BUCKET = 77
    SIGNAL_TYPE_DATA_EXPORT_TO_PUBLIC_CLOUD_STORAGE_BUCKET = 78
    SIGNAL_TYPE_WEAK_PASSWORD_HASH_ALGORITHM = 79
    SIGNAL_TYPE_NO_USER_PASSWORD_POLICY = 80
    SIGNAL_TYPE_HOT_NODE = 81
    SIGNAL_TYPE_NO_POINT_IN_TIME_RECOVERY = 82
    SIGNAL_TYPE_RESOURCE_SUSPENDED = 83
    SIGNAL_TYPE_EXPENSIVE_COMMANDS = 84
    SIGNAL_TYPE_NO_MAINTENANCE_POLICY_CONFIGURED = 85
    SIGNAL_TYPE_NO_DELETION_PROTECTION = 86
    SIGNAL_TYPE_INEFFICIENT_QUERY = 87
    SIGNAL_TYPE_READ_INTENSIVE_WORKLOAD = 88
    SIGNAL_TYPE_MEMORY_LIMIT = 89
    SIGNAL_TYPE_MAX_SERVER_MEMORY = 90
    SIGNAL_TYPE_LARGE_ROWS = 91
    SIGNAL_TYPE_HIGH_WRITE_PRESSURE = 92
    SIGNAL_TYPE_HIGH_READ_PRESSURE = 93
    SIGNAL_TYPE_ENCRYPTION_ORG_POLICY_NOT_SATISFIED = 94
    SIGNAL_TYPE_LOCATION_ORG_POLICY_NOT_SATISFIED = 95
    SIGNAL_TYPE_OUTDATED_MINOR_VERSION = 96
    SIGNAL_TYPE_SCHEMA_NOT_OPTIMIZED = 97
    SIGNAL_TYPE_MANY_IDLE_CONNECTIONS = 98
    SIGNAL_TYPE_REPLICATION_LAG = 99
    SIGNAL_TYPE_OUTDATED_VERSION = 100

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AdditionalMetadataValue(_messages.Message):
    r"""Optional. Any other additional metadata specific to recommendation

    Messages:
      AdditionalProperty: An additional property for a AdditionalMetadataValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AdditionalMetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  additionalMetadata = _messages.MessageField('AdditionalMetadataValue', 1)
  lastRefreshTime = _messages.StringField(2)
  recommendationState = _messages.EnumField('RecommendationStateValueValuesEnum', 3)
  recommender = _messages.StringField(4)
  recommenderId = _messages.StringField(5)
  recommenderSubtype = _messages.StringField(6)
  resourceName = _messages.StringField(7)
  signalType = _messages.EnumField('SignalTypeValueValuesEnum', 8)


class Date(_messages.Message):
  r"""Represents a whole or partial calendar date, such as a birthday. The
  time of day and time zone are either specified elsewhere or are
  insignificant. The date is relative to the Gregorian Calendar. This can
  represent one of the following: * A full date, with non-zero year, month,
  and day values. * A month and day, with a zero year (for example, an
  anniversary). * A year on its own, with a zero month and a zero day. * A
  year and month, with a zero day (for example, a credit card expiration
  date). Related types: * google.type.TimeOfDay * google.type.DateTime *
  google.protobuf.Timestamp

  Fields:
    day: Day of a month. Must be from 1 to 31 and valid for the year and
      month, or 0 to specify a year by itself or a year and month where the
      day isn't significant.
    month: Month of a year. Must be from 1 to 12, or 0 to specify a year
      without a month and day.
    year: Year of the date. Must be from 1 to 9999, or 0 to specify a date
      without a year.
  """

  day = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  month = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  year = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class DiscoveryEndpoint(_messages.Message):
  r"""Endpoints on each network, for Redis clients to connect to the cluster.

  Fields:
    address: Output only. Address of the exposed Redis endpoint used by
      clients to connect to the service. The address could be either IP or
      hostname.
    port: Output only. The port number of the exposed Redis endpoint.
    pscConfig: Output only. Customer configuration for where the endpoint is
      created and accessed from.
  """

  address = _messages.StringField(1)
  port = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pscConfig = _messages.MessageField('PscConfig', 3)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EncryptionInfo(_messages.Message):
  r"""EncryptionInfo describes the encryption information of a cluster or a
  backup.

  Enums:
    EncryptionTypeValueValuesEnum: Output only. Type of encryption.
    KmsKeyPrimaryStateValueValuesEnum: Output only. The state of the primary
      version of the KMS key perceived by the system. This field is not
      populated in backups.

  Fields:
    encryptionType: Output only. Type of encryption.
    kmsKeyPrimaryState: Output only. The state of the primary version of the
      KMS key perceived by the system. This field is not populated in backups.
    kmsKeyVersions: Output only. KMS key versions that are being used to
      protect the data at-rest.
    lastUpdateTime: Output only. The most recent time when the encryption info
      was updated.
  """

  class EncryptionTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of encryption.

    Values:
      TYPE_UNSPECIFIED: Encryption type not specified. Defaults to
        GOOGLE_DEFAULT_ENCRYPTION.
      GOOGLE_DEFAULT_ENCRYPTION: The data is encrypted at rest with a key that
        is fully managed by Google. No key version will be populated. This is
        the default state.
      CUSTOMER_MANAGED_ENCRYPTION: The data is encrypted at rest with a key
        that is managed by the customer. KMS key versions will be populated.
    """
    TYPE_UNSPECIFIED = 0
    GOOGLE_DEFAULT_ENCRYPTION = 1
    CUSTOMER_MANAGED_ENCRYPTION = 2

  class KmsKeyPrimaryStateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the primary version of the KMS key perceived
    by the system. This field is not populated in backups.

    Values:
      KMS_KEY_STATE_UNSPECIFIED: The default value. This value is unused.
      ENABLED: The KMS key is enabled and correctly configured.
      PERMISSION_DENIED: Permission denied on the KMS key.
      DISABLED: The KMS key is disabled.
      DESTROYED: The KMS key is destroyed.
      DESTROY_SCHEDULED: The KMS key is scheduled to be destroyed.
      EKM_KEY_UNREACHABLE_DETECTED: The EKM key is unreachable.
      BILLING_DISABLED: Billing is disabled for the project.
      UNKNOWN_FAILURE: All other unknown failures.
    """
    KMS_KEY_STATE_UNSPECIFIED = 0
    ENABLED = 1
    PERMISSION_DENIED = 2
    DISABLED = 3
    DESTROYED = 4
    DESTROY_SCHEDULED = 5
    EKM_KEY_UNREACHABLE_DETECTED = 6
    BILLING_DISABLED = 7
    UNKNOWN_FAILURE = 8

  encryptionType = _messages.EnumField('EncryptionTypeValueValuesEnum', 1)
  kmsKeyPrimaryState = _messages.EnumField('KmsKeyPrimaryStateValueValuesEnum', 2)
  kmsKeyVersions = _messages.StringField(3, repeated=True)
  lastUpdateTime = _messages.StringField(4)


class Entitlement(_messages.Message):
  r"""Proto representing the access that a user has to a specific
  feature/service. NextId: 3.

  Enums:
    EntitlementStateValueValuesEnum: The current state of user's accessibility
      to a feature/benefit.
    TypeValueValuesEnum: An enum that represents the type of this entitlement.

  Fields:
    entitlementState: The current state of user's accessibility to a
      feature/benefit.
    type: An enum that represents the type of this entitlement.
  """

  class EntitlementStateValueValuesEnum(_messages.Enum):
    r"""The current state of user's accessibility to a feature/benefit.

    Values:
      ENTITLEMENT_STATE_UNSPECIFIED: <no description>
      ENTITLED: User is entitled to a feature/benefit, but whether it has been
        successfully provisioned is decided by provisioning state.
      REVOKED: User is entitled to a feature/benefit, but it was requested to
        be revoked. Whether the revoke has been successful is decided by
        provisioning state.
    """
    ENTITLEMENT_STATE_UNSPECIFIED = 0
    ENTITLED = 1
    REVOKED = 2

  class TypeValueValuesEnum(_messages.Enum):
    r"""An enum that represents the type of this entitlement.

    Values:
      ENTITLEMENT_TYPE_UNSPECIFIED: The entitlement type is unspecified.
      GEMINI: The root entitlement representing Gemini package ownership.This
        will no longer be supported in the future.
      NATIVE: The entitlement representing Native Tier, This will be the
        default Entitlement going forward with GCA Enablement.
      GCA_STANDARD: The entitlement representing GCA-Standard Tier.
    """
    ENTITLEMENT_TYPE_UNSPECIFIED = 0
    GEMINI = 1
    NATIVE = 2
    GCA_STANDARD = 3

  entitlementState = _messages.EnumField('EntitlementStateValueValuesEnum', 1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class ExportBackupRequest(_messages.Message):
  r"""Request for [ExportBackup].

  Fields:
    gcsBucket: Google Cloud Storage bucket, like "my-bucket".
  """

  gcsBucket = _messages.StringField(1)


class ExportInstanceRequest(_messages.Message):
  r"""Request for Export.

  Fields:
    outputConfig: Required. Specify data to be exported.
  """

  outputConfig = _messages.MessageField('OutputConfig', 1)


class FailoverInstanceRequest(_messages.Message):
  r"""Request for Failover.

  Enums:
    DataProtectionModeValueValuesEnum: Optional. Available data protection
      modes that the user can choose. If it's unspecified, data protection
      mode will be LIMITED_DATA_LOSS by default.

  Fields:
    dataProtectionMode: Optional. Available data protection modes that the
      user can choose. If it's unspecified, data protection mode will be
      LIMITED_DATA_LOSS by default.
  """

  class DataProtectionModeValueValuesEnum(_messages.Enum):
    r"""Optional. Available data protection modes that the user can choose. If
    it's unspecified, data protection mode will be LIMITED_DATA_LOSS by
    default.

    Values:
      DATA_PROTECTION_MODE_UNSPECIFIED: Defaults to LIMITED_DATA_LOSS if a
        data protection mode is not specified.
      LIMITED_DATA_LOSS: Instance failover will be protected with data loss
        control. More specifically, the failover will only be performed if the
        current replication offset diff between primary and replica is under a
        certain threshold.
      FORCE_DATA_LOSS: Instance failover will be performed without data loss
        control.
    """
    DATA_PROTECTION_MODE_UNSPECIFIED = 0
    LIMITED_DATA_LOSS = 1
    FORCE_DATA_LOSS = 2

  dataProtectionMode = _messages.EnumField('DataProtectionModeValueValuesEnum', 1)


class FixedFrequencySchedule(_messages.Message):
  r"""This schedule allows the backup to be triggered at a fixed frequency
  (currently only daily is supported).

  Fields:
    startTime: Required. The start time of every automated backup in UTC. It
      must be set to the start of an hour. This field is required.
  """

  startTime = _messages.MessageField('TimeOfDay', 1)


class GCBDRConfiguration(_messages.Message):
  r"""GCBDR Configuration for the resource.

  Fields:
    gcbdrManaged: Whether the resource is managed by GCBDR.
  """

  gcbdrManaged = _messages.BooleanField(1)


class GcsBackupSource(_messages.Message):
  r"""Backups stored in Cloud Storage buckets. The Cloud Storage buckets need
  to be the same region as the clusters.

  Fields:
    uris: Optional. URIs of the Cloud Storage objects to import. Example:
      gs://bucket1/object1, gs://bucket2/folder2/object2
  """

  uris = _messages.StringField(1, repeated=True)


class GcsDestination(_messages.Message):
  r"""The Cloud Storage location for the output content

  Fields:
    uri: Required. Data destination URI (e.g. 'gs://my_bucket/my_object').
      Existing files will be overwritten.
  """

  uri = _messages.StringField(1)


class GcsSource(_messages.Message):
  r"""The Cloud Storage location for the input content

  Fields:
    uri: Required. Source data URI. (e.g. 'gs://my_bucket/my_object').
  """

  uri = _messages.StringField(1)


class GoogleCloudCommonOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    cancelRequested: Output only. Identifies whether the user has requested
      cancellation of the operation. Operations that have been cancelled
      successfully have google.longrunning.Operation.error value with a
      google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    statusDetail: Output only. Human-readable status of the operation, if any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  cancelRequested = _messages.BooleanField(2)
  createTime = _messages.StringField(3)
  endTime = _messages.StringField(4)
  statusDetail = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudRedisV1beta1LocationMetadata(_messages.Message):
  r"""This location metadata represents additional configuration options for a
  given location where a Redis instance may be created. All fields are output
  only. It is returned as content of the
  `google.cloud.location.Location.metadata` field.

  Messages:
    AvailableZonesValue: Output only. The set of available zones in the
      location. The map is keyed by the lowercase ID of each zone, as defined
      by GCE. These keys can be specified in `location_id` or
      `alternative_location_id` fields when creating a Redis instance.

  Fields:
    availableZones: Output only. The set of available zones in the location.
      The map is keyed by the lowercase ID of each zone, as defined by GCE.
      These keys can be specified in `location_id` or
      `alternative_location_id` fields when creating a Redis instance.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AvailableZonesValue(_messages.Message):
    r"""Output only. The set of available zones in the location. The map is
    keyed by the lowercase ID of each zone, as defined by GCE. These keys can
    be specified in `location_id` or `alternative_location_id` fields when
    creating a Redis instance.

    Messages:
      AdditionalProperty: An additional property for a AvailableZonesValue
        object.

    Fields:
      additionalProperties: Additional properties of type AvailableZonesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AvailableZonesValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudRedisV1beta1ZoneMetadata attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudRedisV1beta1ZoneMetadata', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  availableZones = _messages.MessageField('AvailableZonesValue', 1)


class GoogleCloudRedisV1beta1ZoneMetadata(_messages.Message):
  r"""Defines specific information for a particular zone. Currently empty and
  reserved for future use only.
  """



class ImportInstanceRequest(_messages.Message):
  r"""Request for Import.

  Fields:
    inputConfig: Required. Specify data to be imported.
  """

  inputConfig = _messages.MessageField('InputConfig', 1)


class InputConfig(_messages.Message):
  r"""The input content

  Fields:
    gcsSource: Google Cloud Storage location where input content is located.
  """

  gcsSource = _messages.MessageField('GcsSource', 1)


class Instance(_messages.Message):
  r"""A Memorystore for Redis instance.

  Enums:
    ConnectModeValueValuesEnum: Optional. The network connect mode of the
      Redis instance. If not provided, the connect mode defaults to
      DIRECT_PEERING.
    ReadReplicasModeValueValuesEnum: Optional. Read replicas mode for the
      instance. Defaults to READ_REPLICAS_DISABLED.
    StateValueValuesEnum: Output only. The current state of this instance.
    SuspensionReasonsValueListEntryValuesEnum:
    TierValueValuesEnum: Required. The service tier of the instance.
    TransitEncryptionModeValueValuesEnum: Optional. The TLS mode of the Redis
      instance. If not provided, TLS is disabled for the instance.

  Messages:
    LabelsValue: Resource labels to represent user provided metadata
    RedisConfigsValue: Optional. Redis configuration parameters, according to
      http://redis.io/topics/config. Currently, the only supported parameters
      are: Redis version 3.2 and newer: * maxmemory-policy * notify-keyspace-
      events Redis version 4.0 and newer: * activedefrag * lfu-decay-time *
      lfu-log-factor * maxmemory-gb Redis version 5.0 and newer: * stream-
      node-max-bytes * stream-node-max-entries
    TagsValue: Optional. Input only. Immutable. Tag keys/values directly bound
      to this resource. For example: "123/environment": "production",
      "123/costCenter": "marketing"

  Fields:
    alternativeLocationId: Optional. If specified, at least one node will be
      provisioned in this zone in addition to the zone specified in
      location_id. Only applicable to standard tier. If provided, it must be a
      different zone from the one provided in [location_id]. Additional nodes
      beyond the first 2 will be placed in zones selected by the service.
    authEnabled: Optional. Indicates whether OSS Redis AUTH is enabled for the
      instance. If set to "true" AUTH is enabled on the instance. Default
      value is "false" meaning AUTH is disabled.
    authorizedNetwork: Optional. The full name of the Google Compute Engine
      [network](https://cloud.google.com/vpc/docs/vpc) to which the instance
      is connected. If left unspecified, the `default` network will be used.
    availableMaintenanceVersions: Optional. The available maintenance versions
      that an instance could update to.
    connectMode: Optional. The network connect mode of the Redis instance. If
      not provided, the connect mode defaults to DIRECT_PEERING.
    createTime: Output only. The time the instance was created.
    currentLocationId: Output only. The current zone where the Redis primary
      node is located. In basic tier, this will always be the same as
      [location_id]. In standard tier, this can be the zone of any node in the
      instance.
    customerManagedKey: Optional. The KMS key reference that the customer
      provides when trying to create the instance.
    displayName: An arbitrary and optional user-provided name for the
      instance.
    host: Output only. Hostname or IP address of the exposed Redis endpoint
      used by clients to connect to the service.
    labels: Resource labels to represent user provided metadata
    locationId: Optional. The zone where the instance will be provisioned. If
      not provided, the service will choose a zone from the specified region
      for the instance. For standard tier, additional nodes will be added
      across multiple zones for protection against zonal failures. If
      specified, at least one node will be provisioned in this zone.
    maintenancePolicy: Optional. The maintenance policy for the instance. If
      not provided, maintenance events can be performed at any time.
    maintenanceSchedule: Output only. Date and time of upcoming maintenance
      events which have been scheduled.
    maintenanceVersion: Optional. The self service update maintenance version.
      The version is date based such as "20210712_00_00".
    memorySizeGb: Required. Redis memory size in GiB.
    name: Required. Unique name of the resource in this scope including
      project and location using the form:
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
      Note: Redis instances are managed and addressed at regional level so
      location_id here refers to a GCP region; however, users may choose which
      specific zone (or collection of zones for cross-zone instances) an
      instance should be provisioned in. Refer to location_id and
      alternative_location_id fields for more details.
    nodes: Output only. Info per node.
    persistenceConfig: Optional. Persistence configuration parameters
    persistenceIamIdentity: Output only. Cloud IAM identity used by import /
      export operations to transfer data to/from Cloud Storage. Format is
      "serviceAccount:". The value may change over time for a given instance
      so should be checked before each import/export operation.
    port: Output only. The port number of the exposed Redis endpoint.
    readEndpoint: Output only. Hostname or IP address of the exposed readonly
      Redis endpoint. Standard tier only. Targets all healthy replica nodes in
      instance. Replication is asynchronous and replica nodes will exhibit
      some lag behind the primary. Write requests must target 'host'.
    readEndpointPort: Output only. The port number of the exposed readonly
      redis endpoint. Standard tier only. Write requests should target 'port'.
    readReplicasMode: Optional. Read replicas mode for the instance. Defaults
      to READ_REPLICAS_DISABLED.
    redisConfigs: Optional. Redis configuration parameters, according to
      http://redis.io/topics/config. Currently, the only supported parameters
      are: Redis version 3.2 and newer: * maxmemory-policy * notify-keyspace-
      events Redis version 4.0 and newer: * activedefrag * lfu-decay-time *
      lfu-log-factor * maxmemory-gb Redis version 5.0 and newer: * stream-
      node-max-bytes * stream-node-max-entries
    redisVersion: Optional. The version of Redis software. If not provided,
      latest supported version will be used. Currently, the supported values
      are: * `REDIS_3_2` for Redis 3.2 compatibility * `REDIS_4_0` for Redis
      4.0 compatibility (default) * `REDIS_5_0` for Redis 5.0 compatibility *
      `REDIS_6_X` for Redis 6.x compatibility * `REDIS_7_0` for Redis 7.0
      compatibility
    replicaCount: Optional. The number of replica nodes. The valid range for
      the Standard Tier with read replicas enabled is [1-5] and defaults to 2.
      If read replicas are not enabled for a Standard Tier instance, the only
      valid value is 1 and the default is 1. The valid value for basic tier is
      0 and the default is also 0.
    reservedIpRange: Optional. For DIRECT_PEERING mode, the CIDR range of
      internal addresses that are reserved for this instance. Range must be
      unique and non-overlapping with existing subnets in an authorized
      network. For PRIVATE_SERVICE_ACCESS mode, the name of one allocated IP
      address ranges associated with this private service access connection.
      If not provided, the service will choose an unused /29 block, for
      example, 10.0.0.0/29 or ***********/29. For READ_REPLICAS_ENABLED the
      default block size is /28.
    satisfiesPzi: Optional. Output only. Reserved for future use.
    satisfiesPzs: Optional. Output only. Reserved for future use.
    secondaryIpRange: Optional. Additional IP range for node placement.
      Required when enabling read replicas on an existing instance. For
      DIRECT_PEERING mode value must be a CIDR range of size /28, or "auto".
      For PRIVATE_SERVICE_ACCESS mode value must be the name of an allocated
      address range associated with the private service access connection, or
      "auto".
    serverCaCerts: Output only. List of server CA certificates for the
      instance.
    state: Output only. The current state of this instance.
    statusMessage: Output only. Additional information about the current
      status of this instance, if available.
    suspensionReasons: Optional. reasons that causes instance in "SUSPENDED"
      state.
    tags: Optional. Input only. Immutable. Tag keys/values directly bound to
      this resource. For example: "123/environment": "production",
      "123/costCenter": "marketing"
    tier: Required. The service tier of the instance.
    transitEncryptionMode: Optional. The TLS mode of the Redis instance. If
      not provided, TLS is disabled for the instance.
  """

  class ConnectModeValueValuesEnum(_messages.Enum):
    r"""Optional. The network connect mode of the Redis instance. If not
    provided, the connect mode defaults to DIRECT_PEERING.

    Values:
      CONNECT_MODE_UNSPECIFIED: Not set.
      DIRECT_PEERING: Connect via direct peering to the Memorystore for Redis
        hosted service.
      PRIVATE_SERVICE_ACCESS: Connect your Memorystore for Redis instance
        using Private Service Access. Private services access provides an IP
        address range for multiple Google Cloud services, including
        Memorystore.
    """
    CONNECT_MODE_UNSPECIFIED = 0
    DIRECT_PEERING = 1
    PRIVATE_SERVICE_ACCESS = 2

  class ReadReplicasModeValueValuesEnum(_messages.Enum):
    r"""Optional. Read replicas mode for the instance. Defaults to
    READ_REPLICAS_DISABLED.

    Values:
      READ_REPLICAS_MODE_UNSPECIFIED: If not set, Memorystore Redis backend
        will default to READ_REPLICAS_DISABLED.
      READ_REPLICAS_DISABLED: If disabled, read endpoint will not be provided
        and the instance cannot scale up or down the number of replicas.
      READ_REPLICAS_ENABLED: If enabled, read endpoint will be provided and
        the instance can scale up and down the number of replicas. Not valid
        for basic tier.
    """
    READ_REPLICAS_MODE_UNSPECIFIED = 0
    READ_REPLICAS_DISABLED = 1
    READ_REPLICAS_ENABLED = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of this instance.

    Values:
      STATE_UNSPECIFIED: Not set.
      CREATING: Redis instance is being created.
      READY: Redis instance has been created and is fully usable.
      UPDATING: Redis instance configuration is being updated. Certain kinds
        of updates may cause the instance to become unusable while the update
        is in progress.
      DELETING: Redis instance is being deleted.
      REPAIRING: Redis instance is being repaired and may be unusable.
      MAINTENANCE: Maintenance is being performed on this Redis instance.
      IMPORTING: Redis instance is importing data (availability may be
        affected).
      FAILING_OVER: Redis instance is failing over (availability may be
        affected).
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    UPDATING = 3
    DELETING = 4
    REPAIRING = 5
    MAINTENANCE = 6
    IMPORTING = 7
    FAILING_OVER = 8

  class SuspensionReasonsValueListEntryValuesEnum(_messages.Enum):
    r"""SuspensionReasonsValueListEntryValuesEnum enum type.

    Values:
      SUSPENSION_REASON_UNSPECIFIED: Not set.
      CUSTOMER_MANAGED_KEY_ISSUE: Something wrong with the CMEK key provided
        by customer.
    """
    SUSPENSION_REASON_UNSPECIFIED = 0
    CUSTOMER_MANAGED_KEY_ISSUE = 1

  class TierValueValuesEnum(_messages.Enum):
    r"""Required. The service tier of the instance.

    Values:
      TIER_UNSPECIFIED: Not set.
      BASIC: BASIC tier: standalone instance
      STANDARD_HA: STANDARD_HA tier: highly available primary/replica
        instances
    """
    TIER_UNSPECIFIED = 0
    BASIC = 1
    STANDARD_HA = 2

  class TransitEncryptionModeValueValuesEnum(_messages.Enum):
    r"""Optional. The TLS mode of the Redis instance. If not provided, TLS is
    disabled for the instance.

    Values:
      TRANSIT_ENCRYPTION_MODE_UNSPECIFIED: Not set.
      SERVER_AUTHENTICATION: Client to Server traffic encryption enabled with
        server authentication.
      DISABLED: TLS is disabled for the instance.
    """
    TRANSIT_ENCRYPTION_MODE_UNSPECIFIED = 0
    SERVER_AUTHENTICATION = 1
    DISABLED = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Resource labels to represent user provided metadata

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class RedisConfigsValue(_messages.Message):
    r"""Optional. Redis configuration parameters, according to
    http://redis.io/topics/config. Currently, the only supported parameters
    are: Redis version 3.2 and newer: * maxmemory-policy * notify-keyspace-
    events Redis version 4.0 and newer: * activedefrag * lfu-decay-time * lfu-
    log-factor * maxmemory-gb Redis version 5.0 and newer: * stream-node-max-
    bytes * stream-node-max-entries

    Messages:
      AdditionalProperty: An additional property for a RedisConfigsValue
        object.

    Fields:
      additionalProperties: Additional properties of type RedisConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a RedisConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""Optional. Input only. Immutable. Tag keys/values directly bound to
    this resource. For example: "123/environment": "production",
    "123/costCenter": "marketing"

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  alternativeLocationId = _messages.StringField(1)
  authEnabled = _messages.BooleanField(2)
  authorizedNetwork = _messages.StringField(3)
  availableMaintenanceVersions = _messages.StringField(4, repeated=True)
  connectMode = _messages.EnumField('ConnectModeValueValuesEnum', 5)
  createTime = _messages.StringField(6)
  currentLocationId = _messages.StringField(7)
  customerManagedKey = _messages.StringField(8)
  displayName = _messages.StringField(9)
  host = _messages.StringField(10)
  labels = _messages.MessageField('LabelsValue', 11)
  locationId = _messages.StringField(12)
  maintenancePolicy = _messages.MessageField('MaintenancePolicy', 13)
  maintenanceSchedule = _messages.MessageField('MaintenanceSchedule', 14)
  maintenanceVersion = _messages.StringField(15)
  memorySizeGb = _messages.IntegerField(16, variant=_messages.Variant.INT32)
  name = _messages.StringField(17)
  nodes = _messages.MessageField('NodeInfo', 18, repeated=True)
  persistenceConfig = _messages.MessageField('PersistenceConfig', 19)
  persistenceIamIdentity = _messages.StringField(20)
  port = _messages.IntegerField(21, variant=_messages.Variant.INT32)
  readEndpoint = _messages.StringField(22)
  readEndpointPort = _messages.IntegerField(23, variant=_messages.Variant.INT32)
  readReplicasMode = _messages.EnumField('ReadReplicasModeValueValuesEnum', 24)
  redisConfigs = _messages.MessageField('RedisConfigsValue', 25)
  redisVersion = _messages.StringField(26)
  replicaCount = _messages.IntegerField(27, variant=_messages.Variant.INT32)
  reservedIpRange = _messages.StringField(28)
  satisfiesPzi = _messages.BooleanField(29)
  satisfiesPzs = _messages.BooleanField(30)
  secondaryIpRange = _messages.StringField(31)
  serverCaCerts = _messages.MessageField('TlsCertificate', 32, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 33)
  statusMessage = _messages.StringField(34)
  suspensionReasons = _messages.EnumField('SuspensionReasonsValueListEntryValuesEnum', 35, repeated=True)
  tags = _messages.MessageField('TagsValue', 36)
  tier = _messages.EnumField('TierValueValuesEnum', 37)
  transitEncryptionMode = _messages.EnumField('TransitEncryptionModeValueValuesEnum', 38)


class InstanceAuthString(_messages.Message):
  r"""Instance AUTH string details.

  Fields:
    authString: AUTH string set on the instance.
  """

  authString = _messages.StringField(1)


class InternalResourceMetadata(_messages.Message):
  r"""Metadata for individual internal resources in an instance. e.g. spanner
  instance can have multiple databases with unique configuration settings.
  Similarly bigtable can have multiple clusters within same bigtable instance.

  Fields:
    backupConfiguration: Backup configuration for this database
    backupRun: Information about the last backup attempt for this database
    isDeletionProtectionEnabled: Whether deletion protection is enabled for
      this internal resource.
    product: A Product attribute.
    resourceId: A DatabaseResourceId attribute.
    resourceName: Required. internal resource name for spanner this will be
      database name e.g."spanner.googleapis.com/projects/123/abc/instances/ins
      t1/databases/db1"
  """

  backupConfiguration = _messages.MessageField('BackupConfiguration', 1)
  backupRun = _messages.MessageField('BackupRun', 2)
  isDeletionProtectionEnabled = _messages.BooleanField(3)
  product = _messages.MessageField('Product', 4)
  resourceId = _messages.MessageField('DatabaseResourceId', 5)
  resourceName = _messages.StringField(6)


class ListBackupCollectionsResponse(_messages.Message):
  r"""Response for [ListBackupCollections].

  Fields:
    backupCollections: A list of backupCollections in the project. If the
      `location_id` in the parent field of the request is "-", all regions
      available to the project are queried, and the results aggregated. If in
      such an aggregated query a location is unavailable, a placeholder
      backupCollection entry is included in the response with the `name` field
      set to a value of the form
      `projects/{project_id}/locations/{location_id}/backupCollections/`- and
      the `status` field set to ERROR and `status_message` field set to
      "location not available for ListBackupCollections".
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: Locations that could not be reached.
  """

  backupCollections = _messages.MessageField('BackupCollection', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListBackupsResponse(_messages.Message):
  r"""Response for [ListBackups].

  Fields:
    backups: A list of backups in the project.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: Backups that could not be reached.
  """

  backups = _messages.MessageField('Backup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListClustersResponse(_messages.Message):
  r"""Response for ListClusters.

  Fields:
    clusters: A list of Redis clusters in the project in the specified
      location, or across all locations. If the `location_id` in the parent
      field of the request is "-", all regions available to the project are
      queried, and the results aggregated. If in such an aggregated query a
      location is unavailable, a placeholder Redis entry is included in the
      response with the `name` field set to a value of the form
      `projects/{project_id}/locations/{location_id}/clusters/`- and the
      `status` field set to ERROR and `status_message` field set to "location
      not available for ListClusters".
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: Locations that could not be reached.
  """

  clusters = _messages.MessageField('Cluster', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListInstancesResponse(_messages.Message):
  r"""Response for ListInstances.

  Fields:
    instances: A list of Redis instances in the project in the specified
      location, or across all locations. If the `location_id` in the parent
      field of the request is "-", all regions available to the project are
      queried, and the results aggregated. If in such an aggregated query a
      location is unavailable, a placeholder Redis entry is included in the
      response with the `name` field set to a value of the form
      `projects/{project_id}/locations/{location_id}/instances/`- and the
      `status` field set to ERROR and `status_message` field set to "location
      not available for ListInstances".
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: Locations that could not be reached.
  """

  instances = _messages.MessageField('Instance', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Output only. The set of available zones in the location.
      The map is keyed by the lowercase ID of each zone, as defined by Compute
      Engine. These keys can be specified in `location_id` or
      `alternative_location_id` fields when creating a Redis instance.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: Resource ID for the region. For example: "us-east1".
    metadata: Output only. The set of available zones in the location. The map
      is keyed by the lowercase ID of each zone, as defined by Compute Engine.
      These keys can be specified in `location_id` or
      `alternative_location_id` fields when creating a Redis instance.
    name: Full resource name for the region. For example: "projects/example-
      project/locations/us-east1".
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Output only. The set of available zones in the location. The map is
    keyed by the lowercase ID of each zone, as defined by Compute Engine.
    These keys can be specified in `location_id` or `alternative_location_id`
    fields when creating a Redis instance.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class MachineConfiguration(_messages.Message):
  r"""MachineConfiguration describes the configuration of a machine specific
  to Database Resource.

  Fields:
    cpuCount: The number of CPUs. Deprecated. Use vcpu_count instead.
      TODO(b/342344482) add proto validations again after bug fix.
    memorySizeInBytes: Memory size in bytes. TODO(b/342344482) add proto
      validations again after bug fix.
    shardCount: Optional. Number of shards (if applicable).
    vcpuCount: Optional. The number of vCPUs. TODO(b/342344482) add proto
      validations again after bug fix.
  """

  cpuCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  memorySizeInBytes = _messages.IntegerField(2)
  shardCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  vcpuCount = _messages.FloatField(4)


class MaintenancePolicy(_messages.Message):
  r"""Maintenance policy for an instance.

  Enums:
    NotificationLeadtimeValueValuesEnum: Optional. Notification scheduling
      lead time.

  Fields:
    createTime: Output only. The time when the policy was created.
    description: Optional. Description of what this policy is for.
      Create/Update methods return INVALID_ARGUMENT if the length is greater
      than 512.
    notificationLeadtime: Optional. Notification scheduling lead time.
    updateTime: Output only. The time when the policy was last updated.
    weeklyMaintenanceWindow: Optional. Maintenance window that is applied to
      resources covered by this policy. Minimum 1. For the current version,
      the maximum number of weekly_window is expected to be one.
  """

  class NotificationLeadtimeValueValuesEnum(_messages.Enum):
    r"""Optional. Notification scheduling lead time.

    Values:
      NOTIFICATION_LEAD_TIME_UNSPECIFIED: Not set.
      WEEK1: WEEK1 == EARLIER with minimum 7d advanced notification. {7d, 14d}
      WEEK2: WEEK2 == LATER with minimum 14d advanced notification {14d, 21d}.
      WEEK5: WEEK5 == 40d support. minimum 35d advanced notification {35d,
        42d}.
    """
    NOTIFICATION_LEAD_TIME_UNSPECIFIED = 0
    WEEK1 = 1
    WEEK2 = 2
    WEEK5 = 3

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  notificationLeadtime = _messages.EnumField('NotificationLeadtimeValueValuesEnum', 3)
  updateTime = _messages.StringField(4)
  weeklyMaintenanceWindow = _messages.MessageField('WeeklyMaintenanceWindow', 5, repeated=True)


class MaintenanceSchedule(_messages.Message):
  r"""Upcoming maintenance schedule. If no maintenance is scheduled, fields
  are not populated.

  Fields:
    canReschedule: If the scheduled maintenance can be rescheduled, default is
      true.
    endTime: Output only. The end time of any upcoming scheduled maintenance
      for this instance.
    scheduleDeadlineTime: Output only. The deadline that the maintenance
      schedule start time can not go beyond, including reschedule.
    startTime: Output only. The start time of any upcoming scheduled
      maintenance for this instance.
  """

  canReschedule = _messages.BooleanField(1)
  endTime = _messages.StringField(2)
  scheduleDeadlineTime = _messages.StringField(3)
  startTime = _messages.StringField(4)


class ManagedBackupSource(_messages.Message):
  r"""Backups that generated and managed by memorystore.

  Fields:
    backup: Optional. Example: //redis.googleapis.com/projects/{project}/locat
      ions/{location}/backupCollections/{collection}/backups/{backup} A
      shorter version (without the prefix) of the backup name is also
      supported, like projects/{project}/locations/{location}/backupCollection
      s/{collection}/backups/{backup_id} In this case, it assumes the backup
      is under redis.googleapis.com.
  """

  backup = _messages.StringField(1)


class ManagedCertificateAuthority(_messages.Message):
  r"""A ManagedCertificateAuthority object.

  Fields:
    caCerts: The PEM encoded CA certificate chains for redis managed server
      authentication
  """

  caCerts = _messages.MessageField('CertChain', 1, repeated=True)


class Membership(_messages.Message):
  r"""An output only view of all the member clusters participating in the
  cross cluster replication.

  Fields:
    primaryCluster: Output only. The primary cluster that acts as the source
      of replication for the secondary clusters.
    secondaryClusters: Output only. The list of secondary clusters replicating
      from the primary cluster.
  """

  primaryCluster = _messages.MessageField('RemoteCluster', 1)
  secondaryClusters = _messages.MessageField('RemoteCluster', 2, repeated=True)


class NodeInfo(_messages.Message):
  r"""Node specific properties.

  Fields:
    id: Output only. Node identifying string. e.g. 'node-0', 'node-1'
    zone: Output only. Location of the node.
  """

  id = _messages.StringField(1)
  zone = _messages.StringField(2)


class ObservabilityMetricData(_messages.Message):
  r"""A ObservabilityMetricData object.

  Enums:
    AggregationTypeValueValuesEnum: Required. Type of aggregation performed on
      the metric.
    MetricTypeValueValuesEnum: Required. Type of metric like CPU, Memory, etc.

  Fields:
    aggregationType: Required. Type of aggregation performed on the metric.
    metricType: Required. Type of metric like CPU, Memory, etc.
    observationTime: Required. The time the metric value was observed.
    resourceName: Required. Database resource name associated with the signal.
      Resource name to follow CAIS resource_name format as noted here
      go/condor-common-datamodel
    value: Required. Value of the metric type.
  """

  class AggregationTypeValueValuesEnum(_messages.Enum):
    r"""Required. Type of aggregation performed on the metric.

    Values:
      AGGREGATION_TYPE_UNSPECIFIED: Unspecified aggregation type.
      PEAK: PEAK aggregation type.
      P99: P99 aggregation type.
      P95: P95 aggregation type.
      CURRENT: current aggregation type.
    """
    AGGREGATION_TYPE_UNSPECIFIED = 0
    PEAK = 1
    P99 = 2
    P95 = 3
    CURRENT = 4

  class MetricTypeValueValuesEnum(_messages.Enum):
    r"""Required. Type of metric like CPU, Memory, etc.

    Values:
      METRIC_TYPE_UNSPECIFIED: Unspecified metric type.
      CPU_UTILIZATION: CPU utilization for a resource. The value is a fraction
        between 0.0 and 1.0 (may momentarily exceed 1.0 in some cases).
      MEMORY_UTILIZATION: Memory utilization for a resource. The value is a
        fraction between 0.0 and 1.0 (may momentarily exceed 1.0 in some
        cases).
      NETWORK_CONNECTIONS: Number of network connections for a resource.
      STORAGE_UTILIZATION: Storage utilization for a resource. The value is a
        fraction between 0.0 and 1.0 (may momentarily exceed 1.0 in some
        cases).
      STORAGE_USED_BYTES: Sotrage used by a resource.
      NODE_COUNT: Node count for a resource. It represents the number of node
        units in a bigtable/spanner instance.
      MEMORY_USED_BYTES: Memory used by a resource (in bytes).
      PROCESSING_UNIT_COUNT: Processing units used by a resource. It
        represents the number of processing units in a spanner instance.
    """
    METRIC_TYPE_UNSPECIFIED = 0
    CPU_UTILIZATION = 1
    MEMORY_UTILIZATION = 2
    NETWORK_CONNECTIONS = 3
    STORAGE_UTILIZATION = 4
    STORAGE_USED_BYTES = 5
    NODE_COUNT = 6
    MEMORY_USED_BYTES = 7
    PROCESSING_UNIT_COUNT = 8

  aggregationType = _messages.EnumField('AggregationTypeValueValuesEnum', 1)
  metricType = _messages.EnumField('MetricTypeValueValuesEnum', 2)
  observationTime = _messages.StringField(3)
  resourceName = _messages.StringField(4)
  value = _messages.MessageField('TypedValue', 5)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: { `createTime`: The time the operation was created.
      `endTime`: The time the operation finished running. `target`: Server-
      defined resource path for the target of the operation. `verb`: Name of
      the verb executed by the operation. `statusDetail`: Human-readable
      status of the operation, if any. `cancelRequested`: Identifies whether
      the user has requested cancellation of the operation. Operations that
      have successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
      `apiVersion`: API version used to start the operation. }
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: { `createTime`: The time the operation was created. `endTime`:
      The time the operation finished running. `target`: Server-defined
      resource path for the target of the operation. `verb`: Name of the verb
      executed by the operation. `statusDetail`: Human-readable status of the
      operation, if any. `cancelRequested`: Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
      `apiVersion`: API version used to start the operation. }
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""{ `createTime`: The time the operation was created. `endTime`: The
    time the operation finished running. `target`: Server-defined resource
    path for the target of the operation. `verb`: Name of the verb executed by
    the operation. `statusDetail`: Human-readable status of the operation, if
    any. `cancelRequested`: Identifies whether the user has requested
    cancellation of the operation. Operations that have successfully been
    cancelled have Operation.error value with a google.rpc.Status.code of 1,
    corresponding to `Code.CANCELLED`. `apiVersion`: API version used to start
    the operation. }

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationError(_messages.Message):
  r"""An error that occurred during a backup creation operation.

  Enums:
    ErrorTypeValueValuesEnum:

  Fields:
    code: Identifies the specific error that occurred. REQUIRED
    errorType: A ErrorTypeValueValuesEnum attribute.
    message: Additional information about the error encountered. REQUIRED
  """

  class ErrorTypeValueValuesEnum(_messages.Enum):
    r"""ErrorTypeValueValuesEnum enum type.

    Values:
      OPERATION_ERROR_TYPE_UNSPECIFIED: UNSPECIFIED means product type is not
        known or available.
      KMS_KEY_ERROR: key destroyed, expired, not found, unreachable or
        permission denied.
      DATABASE_ERROR: Database is not accessible
      STOCKOUT_ERROR: The zone or region does not have sufficient resources to
        handle the request at the moment
      CANCELLATION_ERROR: User initiated cancellation
      SQLSERVER_ERROR: SQL server specific error
      INTERNAL_ERROR: Any other internal error.
    """
    OPERATION_ERROR_TYPE_UNSPECIFIED = 0
    KMS_KEY_ERROR = 1
    DATABASE_ERROR = 2
    STOCKOUT_ERROR = 3
    CANCELLATION_ERROR = 4
    SQLSERVER_ERROR = 5
    INTERNAL_ERROR = 6

  code = _messages.StringField(1)
  errorType = _messages.EnumField('ErrorTypeValueValuesEnum', 2)
  message = _messages.StringField(3)


class OperationMetadata(_messages.Message):
  r"""Pre-defined metadata fields.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class OutputConfig(_messages.Message):
  r"""The output content

  Fields:
    gcsDestination: Google Cloud Storage destination for output content.
  """

  gcsDestination = _messages.MessageField('GcsDestination', 1)


class PersistenceConfig(_messages.Message):
  r"""Configuration of the persistence functionality.

  Enums:
    PersistenceModeValueValuesEnum: Optional. Controls whether Persistence
      features are enabled. If not provided, the existing value will be used.
    RdbSnapshotPeriodValueValuesEnum: Optional. Period between RDB snapshots.
      Snapshots will be attempted every period starting from the provided
      snapshot start time. For example, a start time of 01/01/2033 06:45 and
      SIX_HOURS snapshot period will do nothing until 01/01/2033, and then
      trigger snapshots every day at 06:45, 12:45, 18:45, and 00:45 the next
      day, and so on. If not provided, TWENTY_FOUR_HOURS will be used as
      default.

  Fields:
    persistenceMode: Optional. Controls whether Persistence features are
      enabled. If not provided, the existing value will be used.
    rdbNextSnapshotTime: Output only. The next time that a snapshot attempt is
      scheduled to occur.
    rdbSnapshotPeriod: Optional. Period between RDB snapshots. Snapshots will
      be attempted every period starting from the provided snapshot start
      time. For example, a start time of 01/01/2033 06:45 and SIX_HOURS
      snapshot period will do nothing until 01/01/2033, and then trigger
      snapshots every day at 06:45, 12:45, 18:45, and 00:45 the next day, and
      so on. If not provided, TWENTY_FOUR_HOURS will be used as default.
    rdbSnapshotStartTime: Optional. Date and time that the first snapshot
      was/will be attempted, and to which future snapshots will be aligned. If
      not provided, the current time will be used.
  """

  class PersistenceModeValueValuesEnum(_messages.Enum):
    r"""Optional. Controls whether Persistence features are enabled. If not
    provided, the existing value will be used.

    Values:
      PERSISTENCE_MODE_UNSPECIFIED: Not set.
      DISABLED: Persistence is disabled for the instance, and any existing
        snapshots are deleted.
      RDB: RDB based Persistence is enabled.
    """
    PERSISTENCE_MODE_UNSPECIFIED = 0
    DISABLED = 1
    RDB = 2

  class RdbSnapshotPeriodValueValuesEnum(_messages.Enum):
    r"""Optional. Period between RDB snapshots. Snapshots will be attempted
    every period starting from the provided snapshot start time. For example,
    a start time of 01/01/2033 06:45 and SIX_HOURS snapshot period will do
    nothing until 01/01/2033, and then trigger snapshots every day at 06:45,
    12:45, 18:45, and 00:45 the next day, and so on. If not provided,
    TWENTY_FOUR_HOURS will be used as default.

    Values:
      SNAPSHOT_PERIOD_UNSPECIFIED: Not set.
      FIFTEEN_MINUTES: Snapshot every 15 minutes.
      THIRTY_MINUTES: Snapshot every 30 minutes.
      ONE_HOUR: Snapshot every 1 hour.
      SIX_HOURS: Snapshot every 6 hours.
      TWELVE_HOURS: Snapshot every 12 hours.
      TWENTY_FOUR_HOURS: Snapshot every 24 hours.
    """
    SNAPSHOT_PERIOD_UNSPECIFIED = 0
    FIFTEEN_MINUTES = 1
    THIRTY_MINUTES = 2
    ONE_HOUR = 3
    SIX_HOURS = 4
    TWELVE_HOURS = 5
    TWENTY_FOUR_HOURS = 6

  persistenceMode = _messages.EnumField('PersistenceModeValueValuesEnum', 1)
  rdbNextSnapshotTime = _messages.StringField(2)
  rdbSnapshotPeriod = _messages.EnumField('RdbSnapshotPeriodValueValuesEnum', 3)
  rdbSnapshotStartTime = _messages.StringField(4)


class Product(_messages.Message):
  r"""Product specification for Condor resources.

  Enums:
    EngineValueValuesEnum: The specific engine that the underlying database is
      running.
    TypeValueValuesEnum: Type of specific database product. It could be
      CloudSQL, AlloyDB etc..

  Fields:
    engine: The specific engine that the underlying database is running.
    minorVersion: Minor version of the underlying database engine. Example
      values: For MySQL, it could be "8.0.32", "5.7.32" etc.. For Postgres, it
      could be "14.3", "15.3" etc..
    type: Type of specific database product. It could be CloudSQL, AlloyDB
      etc..
    version: Version of the underlying database engine. Example values: For
      MySQL, it could be "8.0", "5.7" etc.. For Postgres, it could be "14",
      "15" etc..
  """

  class EngineValueValuesEnum(_messages.Enum):
    r"""The specific engine that the underlying database is running.

    Values:
      ENGINE_UNSPECIFIED: UNSPECIFIED means engine type is not known or
        available.
      ENGINE_MYSQL: MySQL binary running as an engine in the database
        instance.
      MYSQL: MySQL binary running as engine in database instance.
      ENGINE_POSTGRES: Postgres binary running as engine in database instance.
      POSTGRES: Postgres binary running as engine in database instance.
      ENGINE_SQL_SERVER: SQLServer binary running as engine in database
        instance.
      SQL_SERVER: SQLServer binary running as engine in database instance.
      ENGINE_NATIVE: Native database binary running as engine in instance.
      NATIVE: Native database binary running as engine in instance.
      ENGINE_CLOUD_SPANNER_WITH_POSTGRES_DIALECT: Cloud Spanner with
        PostgreSQL dialect.
      ENGINE_CLOUD_SPANNER_WITH_GOOGLESQL_DIALECT: Cloud Spanner with Google
        SQL dialect.
      ENGINE_MEMORYSTORE_FOR_REDIS: Memorystore with Redis dialect.
      ENGINE_MEMORYSTORE_FOR_REDIS_CLUSTER: Memorystore with Redis cluster
        dialect.
      ENGINE_OTHER: Other refers to rest of other database engine. This is to
        be when engine is known, but it is not present in this enum.
      ENGINE_FIRESTORE_WITH_NATIVE_MODE: Firestore with native mode.
      ENGINE_FIRESTORE_WITH_DATASTORE_MODE: Firestore with datastore mode.
      ENGINE_FIRESTORE_WITH_MONGODB_COMPATIBILITY_MODE: Firestore with MongoDB
        compatibility mode.
      ENGINE_EXADATA_ORACLE: Oracle Exadata engine.
      ENGINE_ADB_SERVERLESS_ORACLE: Oracle Autonomous DB Serverless engine.
    """
    ENGINE_UNSPECIFIED = 0
    ENGINE_MYSQL = 1
    MYSQL = 2
    ENGINE_POSTGRES = 3
    POSTGRES = 4
    ENGINE_SQL_SERVER = 5
    SQL_SERVER = 6
    ENGINE_NATIVE = 7
    NATIVE = 8
    ENGINE_CLOUD_SPANNER_WITH_POSTGRES_DIALECT = 9
    ENGINE_CLOUD_SPANNER_WITH_GOOGLESQL_DIALECT = 10
    ENGINE_MEMORYSTORE_FOR_REDIS = 11
    ENGINE_MEMORYSTORE_FOR_REDIS_CLUSTER = 12
    ENGINE_OTHER = 13
    ENGINE_FIRESTORE_WITH_NATIVE_MODE = 14
    ENGINE_FIRESTORE_WITH_DATASTORE_MODE = 15
    ENGINE_FIRESTORE_WITH_MONGODB_COMPATIBILITY_MODE = 16
    ENGINE_EXADATA_ORACLE = 17
    ENGINE_ADB_SERVERLESS_ORACLE = 18

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of specific database product. It could be CloudSQL, AlloyDB etc..

    Values:
      PRODUCT_TYPE_UNSPECIFIED: UNSPECIFIED means product type is not known or
        available.
      PRODUCT_TYPE_CLOUD_SQL: Cloud SQL product area in GCP
      CLOUD_SQL: Cloud SQL product area in GCP
      PRODUCT_TYPE_ALLOYDB: AlloyDB product area in GCP
      ALLOYDB: AlloyDB product area in GCP
      PRODUCT_TYPE_SPANNER: Spanner product area in GCP
      PRODUCT_TYPE_ON_PREM: On premises database product.
      ON_PREM: On premises database product.
      PRODUCT_TYPE_MEMORYSTORE: Memorystore product area in GCP
      PRODUCT_TYPE_BIGTABLE: Bigtable product area in GCP
      PRODUCT_TYPE_FIRESTORE: Firestore product area in GCP.
      PRODUCT_TYPE_COMPUTE_ENGINE: Compute Engine self managed databases
      PRODUCT_TYPE_ORACLE_ON_GCP: Oracle product area in GCP
      PRODUCT_TYPE_OTHER: Other refers to rest of other product type. This is
        to be when product type is known, but it is not present in this enum.
    """
    PRODUCT_TYPE_UNSPECIFIED = 0
    PRODUCT_TYPE_CLOUD_SQL = 1
    CLOUD_SQL = 2
    PRODUCT_TYPE_ALLOYDB = 3
    ALLOYDB = 4
    PRODUCT_TYPE_SPANNER = 5
    PRODUCT_TYPE_ON_PREM = 6
    ON_PREM = 7
    PRODUCT_TYPE_MEMORYSTORE = 8
    PRODUCT_TYPE_BIGTABLE = 9
    PRODUCT_TYPE_FIRESTORE = 10
    PRODUCT_TYPE_COMPUTE_ENGINE = 11
    PRODUCT_TYPE_ORACLE_ON_GCP = 12
    PRODUCT_TYPE_OTHER = 13

  engine = _messages.EnumField('EngineValueValuesEnum', 1)
  minorVersion = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)
  version = _messages.StringField(4)


class PscAutoConnection(_messages.Message):
  r"""Details of consumer resources in a PSC connection that is created
  through Service Connectivity Automation.

  Enums:
    ConnectionTypeValueValuesEnum: Output only. Type of the PSC connection.
    PscConnectionStatusValueValuesEnum: Output only. The status of the PSC
      connection. Please note that this value is updated periodically. Please
      use Private Service Connect APIs for the latest status.

  Fields:
    address: Output only. The IP allocated on the consumer network for the PSC
      forwarding rule.
    connectionType: Output only. Type of the PSC connection.
    forwardingRule: Output only. The URI of the consumer side forwarding rule.
      Example: projects/{projectNumOrId}/regions/us-
      east1/forwardingRules/{resourceId}.
    network: Required. The consumer network where the IP address resides, in
      the form of projects/{project_id}/global/networks/{network_id}.
    projectId: Required. The consumer project_id where the forwarding rule is
      created from.
    pscConnectionId: Output only. The PSC connection id of the forwarding rule
      connected to the service attachment.
    pscConnectionStatus: Output only. The status of the PSC connection. Please
      note that this value is updated periodically. Please use Private Service
      Connect APIs for the latest status.
    serviceAttachment: Output only. The service attachment which is the target
      of the PSC connection, in the form of projects/{project-
      id}/regions/{region}/serviceAttachments/{service-attachment-id}.
  """

  class ConnectionTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of the PSC connection.

    Values:
      CONNECTION_TYPE_UNSPECIFIED: Cluster endpoint Type is not set
      CONNECTION_TYPE_DISCOVERY: Cluster endpoint that will be used as for
        cluster topology discovery.
      CONNECTION_TYPE_PRIMARY: Cluster endpoint that will be used as primary
        endpoint to access primary.
      CONNECTION_TYPE_READER: Cluster endpoint that will be used as reader
        endpoint to access replicas.
    """
    CONNECTION_TYPE_UNSPECIFIED = 0
    CONNECTION_TYPE_DISCOVERY = 1
    CONNECTION_TYPE_PRIMARY = 2
    CONNECTION_TYPE_READER = 3

  class PscConnectionStatusValueValuesEnum(_messages.Enum):
    r"""Output only. The status of the PSC connection. Please note that this
    value is updated periodically. Please use Private Service Connect APIs for
    the latest status.

    Values:
      PSC_CONNECTION_STATUS_UNSPECIFIED: PSC connection status is not
        specified.
      PSC_CONNECTION_STATUS_ACTIVE: The connection is active
      PSC_CONNECTION_STATUS_NOT_FOUND: Connection not found
    """
    PSC_CONNECTION_STATUS_UNSPECIFIED = 0
    PSC_CONNECTION_STATUS_ACTIVE = 1
    PSC_CONNECTION_STATUS_NOT_FOUND = 2

  address = _messages.StringField(1)
  connectionType = _messages.EnumField('ConnectionTypeValueValuesEnum', 2)
  forwardingRule = _messages.StringField(3)
  network = _messages.StringField(4)
  projectId = _messages.StringField(5)
  pscConnectionId = _messages.StringField(6)
  pscConnectionStatus = _messages.EnumField('PscConnectionStatusValueValuesEnum', 7)
  serviceAttachment = _messages.StringField(8)


class PscConfig(_messages.Message):
  r"""A PscConfig object.

  Fields:
    network: Required. The network where the IP address of the discovery
      endpoint will be reserved, in the form of
      projects/{network_project}/global/networks/{network_id}.
  """

  network = _messages.StringField(1)


class PscConnection(_messages.Message):
  r"""Details of consumer resources in a PSC connection.

  Enums:
    ConnectionTypeValueValuesEnum: Output only. Type of the PSC connection.
    PscConnectionStatusValueValuesEnum: Output only. The status of the PSC
      connection. Please note that this value is updated periodically. To get
      the latest status of a PSC connection, follow
      https://cloud.google.com/vpc/docs/configure-private-service-connect-
      services#endpoint-details.

  Fields:
    address: Required. The IP allocated on the consumer network for the PSC
      forwarding rule.
    connectionType: Output only. Type of the PSC connection.
    forwardingRule: Required. The URI of the consumer side forwarding rule.
      Example: projects/{projectNumOrId}/regions/us-
      east1/forwardingRules/{resourceId}.
    network: Required. The consumer network where the IP address resides, in
      the form of projects/{project_id}/global/networks/{network_id}.
    port: Output only. port will only be set for Primary/Reader or Discovery
      endpoint.
    projectId: Optional. Project ID of the consumer project where the
      forwarding rule is created in.
    pscConnectionId: Required. The PSC connection id of the forwarding rule
      connected to the service attachment.
    pscConnectionStatus: Output only. The status of the PSC connection. Please
      note that this value is updated periodically. To get the latest status
      of a PSC connection, follow https://cloud.google.com/vpc/docs/configure-
      private-service-connect-services#endpoint-details.
    serviceAttachment: Required. The service attachment which is the target of
      the PSC connection, in the form of projects/{project-
      id}/regions/{region}/serviceAttachments/{service-attachment-id}.
  """

  class ConnectionTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of the PSC connection.

    Values:
      CONNECTION_TYPE_UNSPECIFIED: Cluster endpoint Type is not set
      CONNECTION_TYPE_DISCOVERY: Cluster endpoint that will be used as for
        cluster topology discovery.
      CONNECTION_TYPE_PRIMARY: Cluster endpoint that will be used as primary
        endpoint to access primary.
      CONNECTION_TYPE_READER: Cluster endpoint that will be used as reader
        endpoint to access replicas.
    """
    CONNECTION_TYPE_UNSPECIFIED = 0
    CONNECTION_TYPE_DISCOVERY = 1
    CONNECTION_TYPE_PRIMARY = 2
    CONNECTION_TYPE_READER = 3

  class PscConnectionStatusValueValuesEnum(_messages.Enum):
    r"""Output only. The status of the PSC connection. Please note that this
    value is updated periodically. To get the latest status of a PSC
    connection, follow https://cloud.google.com/vpc/docs/configure-private-
    service-connect-services#endpoint-details.

    Values:
      PSC_CONNECTION_STATUS_UNSPECIFIED: PSC connection status is not
        specified.
      PSC_CONNECTION_STATUS_ACTIVE: The connection is active
      PSC_CONNECTION_STATUS_NOT_FOUND: Connection not found
    """
    PSC_CONNECTION_STATUS_UNSPECIFIED = 0
    PSC_CONNECTION_STATUS_ACTIVE = 1
    PSC_CONNECTION_STATUS_NOT_FOUND = 2

  address = _messages.StringField(1)
  connectionType = _messages.EnumField('ConnectionTypeValueValuesEnum', 2)
  forwardingRule = _messages.StringField(3)
  network = _messages.StringField(4)
  port = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  projectId = _messages.StringField(6)
  pscConnectionId = _messages.StringField(7)
  pscConnectionStatus = _messages.EnumField('PscConnectionStatusValueValuesEnum', 8)
  serviceAttachment = _messages.StringField(9)


class PscServiceAttachment(_messages.Message):
  r"""Configuration of a service attachment of the cluster, for creating PSC
  connections.

  Enums:
    ConnectionTypeValueValuesEnum: Output only. Type of a PSC connection
      targeting this service attachment.

  Fields:
    connectionType: Output only. Type of a PSC connection targeting this
      service attachment.
    serviceAttachment: Output only. Service attachment URI which your self-
      created PscConnection should use as target
  """

  class ConnectionTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of a PSC connection targeting this service
    attachment.

    Values:
      CONNECTION_TYPE_UNSPECIFIED: Cluster endpoint Type is not set
      CONNECTION_TYPE_DISCOVERY: Cluster endpoint that will be used as for
        cluster topology discovery.
      CONNECTION_TYPE_PRIMARY: Cluster endpoint that will be used as primary
        endpoint to access primary.
      CONNECTION_TYPE_READER: Cluster endpoint that will be used as reader
        endpoint to access replicas.
    """
    CONNECTION_TYPE_UNSPECIFIED = 0
    CONNECTION_TYPE_DISCOVERY = 1
    CONNECTION_TYPE_PRIMARY = 2
    CONNECTION_TYPE_READER = 3

  connectionType = _messages.EnumField('ConnectionTypeValueValuesEnum', 1)
  serviceAttachment = _messages.StringField(2)


class RDBConfig(_messages.Message):
  r"""Configuration of the RDB based persistence.

  Enums:
    RdbSnapshotPeriodValueValuesEnum: Optional. Period between RDB snapshots.

  Fields:
    rdbSnapshotPeriod: Optional. Period between RDB snapshots.
    rdbSnapshotStartTime: Optional. The time that the first snapshot was/will
      be attempted, and to which future snapshots will be aligned. If not
      provided, the current time will be used.
  """

  class RdbSnapshotPeriodValueValuesEnum(_messages.Enum):
    r"""Optional. Period between RDB snapshots.

    Values:
      SNAPSHOT_PERIOD_UNSPECIFIED: Not set.
      ONE_HOUR: One hour.
      SIX_HOURS: Six hours.
      TWELVE_HOURS: Twelve hours.
      TWENTY_FOUR_HOURS: Twenty four hours.
    """
    SNAPSHOT_PERIOD_UNSPECIFIED = 0
    ONE_HOUR = 1
    SIX_HOURS = 2
    TWELVE_HOURS = 3
    TWENTY_FOUR_HOURS = 4

  rdbSnapshotPeriod = _messages.EnumField('RdbSnapshotPeriodValueValuesEnum', 1)
  rdbSnapshotStartTime = _messages.StringField(2)


class ReconciliationOperationMetadata(_messages.Message):
  r"""Operation metadata returned by the CLH during resource state
  reconciliation.

  Enums:
    ExclusiveActionValueValuesEnum: Excluisive action returned by the CLH.

  Fields:
    deleteResource: DEPRECATED. Use exclusive_action instead.
    exclusiveAction: Excluisive action returned by the CLH.
  """

  class ExclusiveActionValueValuesEnum(_messages.Enum):
    r"""Excluisive action returned by the CLH.

    Values:
      UNKNOWN_REPAIR_ACTION: Unknown repair action.
      DELETE: The resource has to be deleted. When using this bit, the CLH
        should fail the operation. DEPRECATED. Instead use DELETE_RESOURCE
        OperationSignal in SideChannel.
      RETRY: This resource could not be repaired but the repair should be
        tried again at a later time. This can happen if there is a dependency
        that needs to be resolved first- e.g. if a parent resource must be
        repaired before a child resource.
    """
    UNKNOWN_REPAIR_ACTION = 0
    DELETE = 1
    RETRY = 2

  deleteResource = _messages.BooleanField(1)
  exclusiveAction = _messages.EnumField('ExclusiveActionValueValuesEnum', 2)


class RedisProjectsLocationsBackupCollectionsBackupsDeleteRequest(_messages.Message):
  r"""A RedisProjectsLocationsBackupCollectionsBackupsDeleteRequest object.

  Fields:
    name: Required. Redis backup resource name using the form: `projects/{proj
      ect_id}/locations/{location_id}/backupCollections/{backup_collection_id}
      /backups/{backup_id}`
    requestId: Optional. Idempotent request UUID.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class RedisProjectsLocationsBackupCollectionsBackupsExportRequest(_messages.Message):
  r"""A RedisProjectsLocationsBackupCollectionsBackupsExportRequest object.

  Fields:
    exportBackupRequest: A ExportBackupRequest resource to be passed as the
      request body.
    name: Required. Redis backup resource name using the form: `projects/{proj
      ect_id}/locations/{location_id}/backupCollections/{backup_collection_id}
      /backups/{backup_id}`
  """

  exportBackupRequest = _messages.MessageField('ExportBackupRequest', 1)
  name = _messages.StringField(2, required=True)


class RedisProjectsLocationsBackupCollectionsBackupsGetRequest(_messages.Message):
  r"""A RedisProjectsLocationsBackupCollectionsBackupsGetRequest object.

  Fields:
    name: Required. Redis backup resource name using the form: `projects/{proj
      ect_id}/locations/{location_id}/backupCollections/{backup_collection_id}
      /backups/{backup_id}`
  """

  name = _messages.StringField(1, required=True)


class RedisProjectsLocationsBackupCollectionsBackupsListRequest(_messages.Message):
  r"""A RedisProjectsLocationsBackupCollectionsBackupsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of items to return. If not
      specified, a default value of 1000 will be used by the service.
      Regardless of the page_size value, the response may include a partial
      list and a caller should only rely on response's `next_page_token` to
      determine if there are more clusters left to be queried.
    pageToken: Optional. The `next_page_token` value returned from a previous
      [ListBackupCollections] request, if any.
    parent: Required. The resource name of the backupCollection using the
      form: `projects/{project_id}/locations/{location_id}/backupCollections/{
      backup_collection_id}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class RedisProjectsLocationsBackupCollectionsGetRequest(_messages.Message):
  r"""A RedisProjectsLocationsBackupCollectionsGetRequest object.

  Fields:
    name: Required. Redis backupCollection resource name using the form: `proj
      ects/{project_id}/locations/{location_id}/backupCollections/{backup_coll
      ection_id}` where `location_id` refers to a Google Cloud region.
  """

  name = _messages.StringField(1, required=True)


class RedisProjectsLocationsBackupCollectionsListRequest(_messages.Message):
  r"""A RedisProjectsLocationsBackupCollectionsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of items to return. If not
      specified, a default value of 1000 will be used by the service.
      Regardless of the page_size value, the response may include a partial
      list and a caller should only rely on response's `next_page_token` to
      determine if there are more clusters left to be queried.
    pageToken: Optional. The `next_page_token` value returned from a previous
      [ListBackupCollections] request, if any.
    parent: Required. The resource name of the backupCollection location using
      the form: `projects/{project_id}/locations/{location_id}` where
      `location_id` refers to a Google Cloud region.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class RedisProjectsLocationsClustersBackupRequest(_messages.Message):
  r"""A RedisProjectsLocationsClustersBackupRequest object.

  Fields:
    backupClusterRequest: A BackupClusterRequest resource to be passed as the
      request body.
    name: Required. Redis cluster resource name using the form:
      `projects/{project_id}/locations/{location_id}/clusters/{cluster_id}`
      where `location_id` refers to a Google Cloud region.
  """

  backupClusterRequest = _messages.MessageField('BackupClusterRequest', 1)
  name = _messages.StringField(2, required=True)


class RedisProjectsLocationsClustersCreateRequest(_messages.Message):
  r"""A RedisProjectsLocationsClustersCreateRequest object.

  Fields:
    cluster: A Cluster resource to be passed as the request body.
    clusterId: Required. The logical name of the Redis cluster in the customer
      project with the following restrictions: * Must contain only lowercase
      letters, numbers, and hyphens. * Must start with a letter. * Must be
      between 1-63 characters. * Must end with a number or a letter. * Must be
      unique within the customer project / location
    parent: Required. The resource name of the cluster location using the
      form: `projects/{project_id}/locations/{location_id}` where
      `location_id` refers to a Google Cloud region.
    requestId: Optional. Idempotent request UUID.
  """

  cluster = _messages.MessageField('Cluster', 1)
  clusterId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class RedisProjectsLocationsClustersDeleteRequest(_messages.Message):
  r"""A RedisProjectsLocationsClustersDeleteRequest object.

  Fields:
    name: Required. Redis cluster resource name using the form:
      `projects/{project_id}/locations/{location_id}/clusters/{cluster_id}`
      where `location_id` refers to a Google Cloud region.
    requestId: Optional. Idempotent request UUID.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class RedisProjectsLocationsClustersGetCertificateAuthorityRequest(_messages.Message):
  r"""A RedisProjectsLocationsClustersGetCertificateAuthorityRequest object.

  Fields:
    name: Required. Redis cluster certificate authority resource name using
      the form: `projects/{project_id}/locations/{location_id}/clusters/{clust
      er_id}/certificateAuthority` where `location_id` refers to a Google
      Cloud region.
  """

  name = _messages.StringField(1, required=True)


class RedisProjectsLocationsClustersGetRequest(_messages.Message):
  r"""A RedisProjectsLocationsClustersGetRequest object.

  Fields:
    name: Required. Redis cluster resource name using the form:
      `projects/{project_id}/locations/{location_id}/clusters/{cluster_id}`
      where `location_id` refers to a Google Cloud region.
  """

  name = _messages.StringField(1, required=True)


class RedisProjectsLocationsClustersListRequest(_messages.Message):
  r"""A RedisProjectsLocationsClustersListRequest object.

  Fields:
    pageSize: The maximum number of items to return. If not specified, a
      default value of 1000 will be used by the service. Regardless of the
      page_size value, the response may include a partial list and a caller
      should only rely on response's `next_page_token` to determine if there
      are more clusters left to be queried.
    pageToken: The `next_page_token` value returned from a previous
      ListClusters request, if any.
    parent: Required. The resource name of the cluster location using the
      form: `projects/{project_id}/locations/{location_id}` where
      `location_id` refers to a Google Cloud region.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class RedisProjectsLocationsClustersPatchRequest(_messages.Message):
  r"""A RedisProjectsLocationsClustersPatchRequest object.

  Fields:
    cluster: A Cluster resource to be passed as the request body.
    name: Required. Identifier. Unique name of the resource in this scope
      including project and location using the form:
      `projects/{project_id}/locations/{location_id}/clusters/{cluster_id}`
    requestId: Optional. Idempotent request UUID.
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field. The elements of the repeated paths field may
      only include these fields from Cluster: * `size_gb` * `replica_count` *
      `cluster_endpoints`
  """

  cluster = _messages.MessageField('Cluster', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class RedisProjectsLocationsClustersRescheduleClusterMaintenanceRequest(_messages.Message):
  r"""A RedisProjectsLocationsClustersRescheduleClusterMaintenanceRequest
  object.

  Fields:
    name: Required. Redis Cluster instance resource name using the form:
      `projects/{project_id}/locations/{location_id}/clusters/{cluster_id}`
      where `location_id` refers to a Google Cloud region.
    rescheduleClusterMaintenanceRequest: A RescheduleClusterMaintenanceRequest
      resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  rescheduleClusterMaintenanceRequest = _messages.MessageField('RescheduleClusterMaintenanceRequest', 2)


class RedisProjectsLocationsGetRequest(_messages.Message):
  r"""A RedisProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class RedisProjectsLocationsInstancesCreateRequest(_messages.Message):
  r"""A RedisProjectsLocationsInstancesCreateRequest object.

  Fields:
    instance: A Instance resource to be passed as the request body.
    instanceId: Required. The logical name of the Redis instance in the
      customer project with the following restrictions: * Must contain only
      lowercase letters, numbers, and hyphens. * Must start with a letter. *
      Must be between 1-40 characters. * Must end with a number or a letter. *
      Must be unique within the customer project / location
    parent: Required. The resource name of the instance location using the
      form: `projects/{project_id}/locations/{location_id}` where
      `location_id` refers to a GCP region.
  """

  instance = _messages.MessageField('Instance', 1)
  instanceId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class RedisProjectsLocationsInstancesDeleteRequest(_messages.Message):
  r"""A RedisProjectsLocationsInstancesDeleteRequest object.

  Fields:
    name: Required. Redis instance resource name using the form:
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
      where `location_id` refers to a GCP region.
  """

  name = _messages.StringField(1, required=True)


class RedisProjectsLocationsInstancesExportRequest(_messages.Message):
  r"""A RedisProjectsLocationsInstancesExportRequest object.

  Fields:
    exportInstanceRequest: A ExportInstanceRequest resource to be passed as
      the request body.
    name: Required. Redis instance resource name using the form:
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
      where `location_id` refers to a GCP region.
  """

  exportInstanceRequest = _messages.MessageField('ExportInstanceRequest', 1)
  name = _messages.StringField(2, required=True)


class RedisProjectsLocationsInstancesFailoverRequest(_messages.Message):
  r"""A RedisProjectsLocationsInstancesFailoverRequest object.

  Fields:
    failoverInstanceRequest: A FailoverInstanceRequest resource to be passed
      as the request body.
    name: Required. Redis instance resource name using the form:
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
      where `location_id` refers to a GCP region.
  """

  failoverInstanceRequest = _messages.MessageField('FailoverInstanceRequest', 1)
  name = _messages.StringField(2, required=True)


class RedisProjectsLocationsInstancesGetAuthStringRequest(_messages.Message):
  r"""A RedisProjectsLocationsInstancesGetAuthStringRequest object.

  Fields:
    name: Required. Redis instance resource name using the form:
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
      where `location_id` refers to a GCP region.
  """

  name = _messages.StringField(1, required=True)


class RedisProjectsLocationsInstancesGetRequest(_messages.Message):
  r"""A RedisProjectsLocationsInstancesGetRequest object.

  Fields:
    name: Required. Redis instance resource name using the form:
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
      where `location_id` refers to a GCP region.
  """

  name = _messages.StringField(1, required=True)


class RedisProjectsLocationsInstancesImportRequest(_messages.Message):
  r"""A RedisProjectsLocationsInstancesImportRequest object.

  Fields:
    importInstanceRequest: A ImportInstanceRequest resource to be passed as
      the request body.
    name: Required. Redis instance resource name using the form:
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
      where `location_id` refers to a GCP region.
  """

  importInstanceRequest = _messages.MessageField('ImportInstanceRequest', 1)
  name = _messages.StringField(2, required=True)


class RedisProjectsLocationsInstancesListRequest(_messages.Message):
  r"""A RedisProjectsLocationsInstancesListRequest object.

  Fields:
    pageSize: The maximum number of items to return. If not specified, a
      default value of 1000 will be used by the service. Regardless of the
      page_size value, the response may include a partial list and a caller
      should only rely on response's `next_page_token` to determine if there
      are more instances left to be queried.
    pageToken: The `next_page_token` value returned from a previous
      ListInstances request, if any.
    parent: Required. The resource name of the instance location using the
      form: `projects/{project_id}/locations/{location_id}` where
      `location_id` refers to a GCP region.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class RedisProjectsLocationsInstancesPatchRequest(_messages.Message):
  r"""A RedisProjectsLocationsInstancesPatchRequest object.

  Fields:
    instance: A Instance resource to be passed as the request body.
    name: Required. Unique name of the resource in this scope including
      project and location using the form:
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
      Note: Redis instances are managed and addressed at regional level so
      location_id here refers to a GCP region; however, users may choose which
      specific zone (or collection of zones for cross-zone instances) an
      instance should be provisioned in. Refer to location_id and
      alternative_location_id fields for more details.
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field. The elements of the repeated paths field may
      only include these fields from Instance: * `displayName` * `labels` *
      `memorySizeGb` * `redisConfig` * `replica_count`
  """

  instance = _messages.MessageField('Instance', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class RedisProjectsLocationsInstancesRescheduleMaintenanceRequest(_messages.Message):
  r"""A RedisProjectsLocationsInstancesRescheduleMaintenanceRequest object.

  Fields:
    name: Required. Redis instance resource name using the form:
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
      where `location_id` refers to a GCP region.
    rescheduleMaintenanceRequest: A RescheduleMaintenanceRequest resource to
      be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  rescheduleMaintenanceRequest = _messages.MessageField('RescheduleMaintenanceRequest', 2)


class RedisProjectsLocationsInstancesUpgradeRequest(_messages.Message):
  r"""A RedisProjectsLocationsInstancesUpgradeRequest object.

  Fields:
    name: Required. Redis instance resource name using the form:
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
      where `location_id` refers to a GCP region.
    upgradeInstanceRequest: A UpgradeInstanceRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  upgradeInstanceRequest = _messages.MessageField('UpgradeInstanceRequest', 2)


class RedisProjectsLocationsListRequest(_messages.Message):
  r"""A RedisProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class RedisProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A RedisProjectsLocationsOperationsCancelRequest object.

  Fields:
    name: The name of the operation resource to be cancelled.
  """

  name = _messages.StringField(1, required=True)


class RedisProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A RedisProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class RedisProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A RedisProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class RedisProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A RedisProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class RemoteCluster(_messages.Message):
  r"""Details of the remote cluster associated with this cluster in a cross
  cluster replication setup.

  Fields:
    cluster: Output only. The full resource path of the remote cluster in the
      format: projects//locations//clusters/
    uid: Output only. The unique identifier of the remote cluster.
  """

  cluster = _messages.StringField(1)
  uid = _messages.StringField(2)


class RescheduleClusterMaintenanceRequest(_messages.Message):
  r"""Request for rescheduling a cluster maintenance.

  Enums:
    RescheduleTypeValueValuesEnum: Required. If reschedule type is
      SPECIFIC_TIME, must set up schedule_time as well.

  Fields:
    rescheduleType: Required. If reschedule type is SPECIFIC_TIME, must set up
      schedule_time as well.
    scheduleTime: Optional. Timestamp when the maintenance shall be
      rescheduled to if reschedule_type=SPECIFIC_TIME, in RFC 3339 format, for
      example `2012-11-15T16:19:00.094Z`.
  """

  class RescheduleTypeValueValuesEnum(_messages.Enum):
    r"""Required. If reschedule type is SPECIFIC_TIME, must set up
    schedule_time as well.

    Values:
      RESCHEDULE_TYPE_UNSPECIFIED: Not set.
      IMMEDIATE: If the user wants to schedule the maintenance to happen now.
      SPECIFIC_TIME: If the user wants to reschedule the maintenance to a
        specific time.
    """
    RESCHEDULE_TYPE_UNSPECIFIED = 0
    IMMEDIATE = 1
    SPECIFIC_TIME = 2

  rescheduleType = _messages.EnumField('RescheduleTypeValueValuesEnum', 1)
  scheduleTime = _messages.StringField(2)


class RescheduleMaintenanceRequest(_messages.Message):
  r"""Request for RescheduleMaintenance.

  Enums:
    RescheduleTypeValueValuesEnum: Required. If reschedule type is
      SPECIFIC_TIME, must set up schedule_time as well.

  Fields:
    rescheduleType: Required. If reschedule type is SPECIFIC_TIME, must set up
      schedule_time as well.
    scheduleTime: Optional. Timestamp when the maintenance shall be
      rescheduled to if reschedule_type=SPECIFIC_TIME, in RFC 3339 format, for
      example `2012-11-15T16:19:00.094Z`.
  """

  class RescheduleTypeValueValuesEnum(_messages.Enum):
    r"""Required. If reschedule type is SPECIFIC_TIME, must set up
    schedule_time as well.

    Values:
      RESCHEDULE_TYPE_UNSPECIFIED: Not set.
      IMMEDIATE: If the user wants to schedule the maintenance to happen now.
      NEXT_AVAILABLE_WINDOW: If the user wants to use the existing maintenance
        policy to find the next available window.
      SPECIFIC_TIME: If the user wants to reschedule the maintenance to a
        specific time.
    """
    RESCHEDULE_TYPE_UNSPECIFIED = 0
    IMMEDIATE = 1
    NEXT_AVAILABLE_WINDOW = 2
    SPECIFIC_TIME = 3

  rescheduleType = _messages.EnumField('RescheduleTypeValueValuesEnum', 1)
  scheduleTime = _messages.StringField(2)


class RetentionSettings(_messages.Message):
  r"""A RetentionSettings object.

  Enums:
    RetentionUnitValueValuesEnum: The unit that 'retained_backups' represents.

  Fields:
    durationBasedRetention: Duration based retention period i.e. 172800
      seconds (2 days)
    quantityBasedRetention: A integer attribute.
    retentionUnit: The unit that 'retained_backups' represents.
    timeBasedRetention: A string attribute.
    timestampBasedRetentionTime: Timestamp based retention period i.e.
      2024-05-01T00:00:00Z
  """

  class RetentionUnitValueValuesEnum(_messages.Enum):
    r"""The unit that 'retained_backups' represents.

    Values:
      RETENTION_UNIT_UNSPECIFIED: Backup retention unit is unspecified, will
        be treated as COUNT.
      COUNT: Retention will be by count, eg. "retain the most recent 7
        backups".
      TIME: Retention will be by Time, eg. "retain backups till a specific
        time" i.e. till 2024-05-01T00:00:00Z.
      DURATION: Retention will be by duration, eg. "retain the backups for
        172800 seconds (2 days)".
      RETENTION_UNIT_OTHER: For rest of the other category
    """
    RETENTION_UNIT_UNSPECIFIED = 0
    COUNT = 1
    TIME = 2
    DURATION = 3
    RETENTION_UNIT_OTHER = 4

  durationBasedRetention = _messages.StringField(1)
  quantityBasedRetention = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  retentionUnit = _messages.EnumField('RetentionUnitValueValuesEnum', 3)
  timeBasedRetention = _messages.StringField(4)
  timestampBasedRetentionTime = _messages.StringField(5)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StateInfo(_messages.Message):
  r"""Represents additional information about the state of the cluster.

  Fields:
    updateInfo: Describes ongoing update on the cluster when cluster state is
      UPDATING.
  """

  updateInfo = _messages.MessageField('UpdateInfo', 1)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class Tags(_messages.Message):
  r"""Message type for storing tags. Tags provide a way to create annotations
  for resources, and in some cases conditionally allow or deny policies based
  on whether a resource has a specific tag.

  Messages:
    TagsValue: The Tag key/value mappings.

  Fields:
    tags: The Tag key/value mappings.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""The Tag key/value mappings.

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  tags = _messages.MessageField('TagsValue', 1)


class TimeOfDay(_messages.Message):
  r"""Represents a time of day. The date and time zone are either not
  significant or are specified elsewhere. An API may choose to allow leap
  seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.

  Fields:
    hours: Hours of a day in 24 hour format. Must be greater than or equal to
      0 and typically must be less than or equal to 23. An API may choose to
      allow the value "24:00:00" for scenarios like business closing time.
    minutes: Minutes of an hour. Must be greater than or equal to 0 and less
      than or equal to 59.
    nanos: Fractions of seconds, in nanoseconds. Must be greater than or equal
      to 0 and less than or equal to 999,999,999.
    seconds: Seconds of a minute. Must be greater than or equal to 0 and
      typically must be less than or equal to 59. An API may allow the value
      60 if it allows leap-seconds.
  """

  hours = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minutes = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  nanos = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  seconds = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class TlsCertificate(_messages.Message):
  r"""TlsCertificate Resource

  Fields:
    cert: PEM representation.
    createTime: Output only. The time when the certificate was created in [RFC
      3339](https://tools.ietf.org/html/rfc3339) format, for example
      `2020-05-18T00:00:00.094Z`.
    expireTime: Output only. The time when the certificate expires in [RFC
      3339](https://tools.ietf.org/html/rfc3339) format, for example
      `2020-05-18T00:00:00.094Z`.
    serialNumber: Serial number, as extracted from the certificate.
    sha1Fingerprint: Sha1 Fingerprint of the certificate.
  """

  cert = _messages.StringField(1)
  createTime = _messages.StringField(2)
  expireTime = _messages.StringField(3)
  serialNumber = _messages.StringField(4)
  sha1Fingerprint = _messages.StringField(5)


class TypedValue(_messages.Message):
  r"""TypedValue represents the value of a metric type. It can either be a
  double, an int64, a string or a bool.

  Fields:
    boolValue: For boolean value
    doubleValue: For double value
    int64Value: For integer value
    stringValue: For string value
  """

  boolValue = _messages.BooleanField(1)
  doubleValue = _messages.FloatField(2)
  int64Value = _messages.IntegerField(3)
  stringValue = _messages.StringField(4)


class UpdateInfo(_messages.Message):
  r"""Represents information about an updating cluster.

  Enums:
    TargetNodeTypeValueValuesEnum: Target node type for redis cluster.

  Fields:
    targetNodeType: Target node type for redis cluster.
    targetReplicaCount: Target number of replica nodes per shard.
    targetShardCount: Target number of shards for redis cluster
  """

  class TargetNodeTypeValueValuesEnum(_messages.Enum):
    r"""Target node type for redis cluster.

    Values:
      NODE_TYPE_UNSPECIFIED: Node type unspecified
      REDIS_SHARED_CORE_NANO: Redis shared core nano node_type.
      REDIS_HIGHMEM_MEDIUM: Redis highmem medium node_type.
      REDIS_HIGHMEM_XLARGE: Redis highmem xlarge node_type.
      REDIS_STANDARD_SMALL: Redis standard small node_type.
    """
    NODE_TYPE_UNSPECIFIED = 0
    REDIS_SHARED_CORE_NANO = 1
    REDIS_HIGHMEM_MEDIUM = 2
    REDIS_HIGHMEM_XLARGE = 3
    REDIS_STANDARD_SMALL = 4

  targetNodeType = _messages.EnumField('TargetNodeTypeValueValuesEnum', 1)
  targetReplicaCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  targetShardCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class UpgradeInstanceRequest(_messages.Message):
  r"""Request for UpgradeInstance.

  Fields:
    redisVersion: Required. Specifies the target version of Redis software to
      upgrade to.
  """

  redisVersion = _messages.StringField(1)


class UserLabels(_messages.Message):
  r"""Message type for storing user labels. User labels are used to tag App
  Engine resources, allowing users to search for resources matching a set of
  labels and to aggregate usage data by labels.

  Messages:
    LabelsValue: A LabelsValue object.

  Fields:
    labels: A LabelsValue attribute.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""A LabelsValue object.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)


class WeeklyMaintenanceWindow(_messages.Message):
  r"""Time window in which disruptive maintenance updates occur. Non-
  disruptive updates can occur inside or outside this window.

  Enums:
    DayValueValuesEnum: Required. The day of week that maintenance updates
      occur.

  Fields:
    day: Required. The day of week that maintenance updates occur.
    duration: Output only. Duration of the maintenance window. The current
      window is fixed at 1 hour.
    startTime: Required. Start time of the window in UTC time.
  """

  class DayValueValuesEnum(_messages.Enum):
    r"""Required. The day of week that maintenance updates occur.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  day = _messages.EnumField('DayValueValuesEnum', 1)
  duration = _messages.StringField(2)
  startTime = _messages.MessageField('TimeOfDay', 3)


class ZoneDistributionConfig(_messages.Message):
  r"""Zone distribution config for allocation of cluster resources.

  Enums:
    ModeValueValuesEnum: Optional. The mode of zone distribution. Defaults to
      MULTI_ZONE, when not specified.

  Fields:
    mode: Optional. The mode of zone distribution. Defaults to MULTI_ZONE,
      when not specified.
    zone: Optional. When SINGLE ZONE distribution is selected, zone field
      would be used to allocate all resources in that zone. This is not
      applicable to MULTI_ZONE, and would be ignored for MULTI_ZONE clusters.
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""Optional. The mode of zone distribution. Defaults to MULTI_ZONE, when
    not specified.

    Values:
      ZONE_DISTRIBUTION_MODE_UNSPECIFIED: Not Set. Default: MULTI_ZONE
      MULTI_ZONE: Distribute all resources across 3 zones picked at random,
        within the region.
      SINGLE_ZONE: Distribute all resources in a single zone. The zone field
        must be specified, when this mode is selected.
    """
    ZONE_DISTRIBUTION_MODE_UNSPECIFIED = 0
    MULTI_ZONE = 1
    SINGLE_ZONE = 2

  mode = _messages.EnumField('ModeValueValuesEnum', 1)
  zone = _messages.StringField(2)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
