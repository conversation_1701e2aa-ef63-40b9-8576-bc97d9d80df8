"""Generated message classes for parallelstore version v1beta.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'parallelstore'


class DestinationGcsBucket(_messages.Message):
  r"""Cloud Storage as the destination of a data transfer.

  Fields:
    uri: Required. URI to a Cloud Storage bucket in the format: `gs:///`. The
      path inside the bucket is optional.
  """

  uri = _messages.StringField(1)


class DestinationParallelstore(_messages.Message):
  r"""Parallelstore as the destination of a data transfer.

  Fields:
    path: Optional. Root directory path to the Paralellstore filesystem,
      starting with `/`. Defaults to `/` if unset.
  """

  path = _messages.StringField(1)


class ExportDataRequest(_messages.Message):
  r"""Export data from Parallelstore to Cloud Storage.

  Fields:
    destinationGcsBucket: Cloud Storage destination.
    metadataOptions: Optional. The metadata options for the export data.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (********-0000-0000-0000-************).
    serviceAccount: Optional. User-specified Service Account (SA) credentials
      to be used when performing the transfer. Use one of the following
      formats: * `{EMAIL_ADDRESS_OR_UNIQUE_ID}` * `projects/{PROJECT_ID_OR_NUM
      BER}/serviceAccounts/{EMAIL_ADDRESS_OR_UNIQUE_ID}` *
      `projects/-/serviceAccounts/{EMAIL_ADDRESS_OR_UNIQUE_ID}` If
      unspecified, the Parallelstore service agent is used: `service-@gcp-sa-
      parallelstore.iam.gserviceaccount.com`
    sourceParallelstore: Parallelstore source.
  """

  destinationGcsBucket = _messages.MessageField('DestinationGcsBucket', 1)
  metadataOptions = _messages.MessageField('TransferMetadataOptions', 2)
  requestId = _messages.StringField(3)
  serviceAccount = _messages.StringField(4)
  sourceParallelstore = _messages.MessageField('SourceParallelstore', 5)


class GoogleProtobufEmpty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class ImportDataRequest(_messages.Message):
  r"""Import data from Cloud Storage into a Parallelstore instance.

  Fields:
    destinationParallelstore: Parallelstore destination.
    metadataOptions: Optional. The transfer metadata options for the import
      data.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (********-0000-0000-0000-************).
    serviceAccount: Optional. User-specified service account credentials to be
      used when performing the transfer. Use one of the following formats: *
      `{EMAIL_ADDRESS_OR_UNIQUE_ID}` * `projects/{PROJECT_ID_OR_NUMBER}/servic
      eAccounts/{EMAIL_ADDRESS_OR_UNIQUE_ID}` *
      `projects/-/serviceAccounts/{EMAIL_ADDRESS_OR_UNIQUE_ID}` If
      unspecified, the Parallelstore service agent is used: `service-@gcp-sa-
      parallelstore.iam.gserviceaccount.com`
    sourceGcsBucket: The Cloud Storage source bucket and, optionally, path
      inside the bucket.
  """

  destinationParallelstore = _messages.MessageField('DestinationParallelstore', 1)
  metadataOptions = _messages.MessageField('TransferMetadataOptions', 2)
  requestId = _messages.StringField(3)
  serviceAccount = _messages.StringField(4)
  sourceGcsBucket = _messages.MessageField('SourceGcsBucket', 5)


class Instance(_messages.Message):
  r"""A Parallelstore instance.

  Enums:
    DeploymentTypeValueValuesEnum: Optional. Immutable. The deployment type of
      the instance. Allowed values are: * `SCRATCH`: the instance is a scratch
      instance. * `PERSISTENT`: the instance is a persistent instance.
    DirectoryStripeLevelValueValuesEnum: Optional. Immutable. Stripe level for
      directories. Allowed values are: * `DIRECTORY_STRIPE_LEVEL_MIN`:
      recommended when directories contain a small number of files. *
      `DIRECTORY_STRIPE_LEVEL_BALANCED`: balances performance for workloads
      involving a mix of small and large directories. *
      `DIRECTORY_STRIPE_LEVEL_MAX`: recommended for directories with a large
      number of files.
    FileStripeLevelValueValuesEnum: Optional. Immutable. Stripe level for
      files. Allowed values are: * `FILE_STRIPE_LEVEL_MIN`: offers the best
      performance for small size files. * `FILE_STRIPE_LEVEL_BALANCED`:
      balances performance for workloads involving a mix of small and large
      files. * `FILE_STRIPE_LEVEL_MAX`: higher throughput performance for
      larger files.
    StateValueValuesEnum: Output only. The instance state.

  Messages:
    LabelsValue: Optional. Cloud Labels are a flexible and lightweight
      mechanism for organizing cloud resources into groups that reflect a
      customer's organizational needs and deployment strategies. See
      https://cloud.google.com/resource-manager/docs/labels-overview for
      details.

  Fields:
    accessPoints: Output only. A list of IPv4 addresses used for client side
      configuration.
    capacityGib: Required. Immutable. The instance's storage capacity in
      Gibibytes (GiB). Allowed values are between 12000 and 100000, in
      multiples of 4000; e.g., 12000, 16000, 20000, ...
    createTime: Output only. The time when the instance was created.
    daosVersion: Output only. Deprecated: The version of DAOS software running
      in the instance.
    deploymentType: Optional. Immutable. The deployment type of the instance.
      Allowed values are: * `SCRATCH`: the instance is a scratch instance. *
      `PERSISTENT`: the instance is a persistent instance.
    description: Optional. The description of the instance. 2048 characters or
      less.
    directoryStripeLevel: Optional. Immutable. Stripe level for directories.
      Allowed values are: * `DIRECTORY_STRIPE_LEVEL_MIN`: recommended when
      directories contain a small number of files. *
      `DIRECTORY_STRIPE_LEVEL_BALANCED`: balances performance for workloads
      involving a mix of small and large directories. *
      `DIRECTORY_STRIPE_LEVEL_MAX`: recommended for directories with a large
      number of files.
    effectiveReservedIpRange: Output only. Immutable. The ID of the IP address
      range being used by the instance's VPC network. This field is populated
      by the service and contains the value currently used by the service.
    fileStripeLevel: Optional. Immutable. Stripe level for files. Allowed
      values are: * `FILE_STRIPE_LEVEL_MIN`: offers the best performance for
      small size files. * `FILE_STRIPE_LEVEL_BALANCED`: balances performance
      for workloads involving a mix of small and large files. *
      `FILE_STRIPE_LEVEL_MAX`: higher throughput performance for larger files.
    labels: Optional. Cloud Labels are a flexible and lightweight mechanism
      for organizing cloud resources into groups that reflect a customer's
      organizational needs and deployment strategies. See
      https://cloud.google.com/resource-manager/docs/labels-overview for
      details.
    name: Identifier. The resource name of the instance, in the format
      `projects/{project}/locations/{location}/instances/{instance_id}`.
    network: Optional. Immutable. The name of the Compute Engine [VPC
      network](https://cloud.google.com/vpc/docs/vpc) to which the instance is
      connected.
    reservedIpRange: Optional. Immutable. The ID of the IP address range being
      used by the instance's VPC network. See [Configure a VPC network](https:
      //cloud.google.com/parallelstore/docs/vpc#create_and_configure_the_vpc).
      If no ID is provided, all ranges are considered.
    state: Output only. The instance state.
    updateTime: Output only. The time when the instance was updated.
  """

  class DeploymentTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Immutable. The deployment type of the instance. Allowed
    values are: * `SCRATCH`: the instance is a scratch instance. *
    `PERSISTENT`: the instance is a persistent instance.

    Values:
      DEPLOYMENT_TYPE_UNSPECIFIED: Default Deployment Type It is equivalent to
        SCRATCH
      SCRATCH: Scratch
      PERSISTENT: Persistent
    """
    DEPLOYMENT_TYPE_UNSPECIFIED = 0
    SCRATCH = 1
    PERSISTENT = 2

  class DirectoryStripeLevelValueValuesEnum(_messages.Enum):
    r"""Optional. Immutable. Stripe level for directories. Allowed values are:
    * `DIRECTORY_STRIPE_LEVEL_MIN`: recommended when directories contain a
    small number of files. * `DIRECTORY_STRIPE_LEVEL_BALANCED`: balances
    performance for workloads involving a mix of small and large directories.
    * `DIRECTORY_STRIPE_LEVEL_MAX`: recommended for directories with a large
    number of files.

    Values:
      DIRECTORY_STRIPE_LEVEL_UNSPECIFIED: If not set, DirectoryStripeLevel
        will default to DIRECTORY_STRIPE_LEVEL_MAX
      DIRECTORY_STRIPE_LEVEL_MIN: Minimum directory striping
      DIRECTORY_STRIPE_LEVEL_BALANCED: Medium directory striping
      DIRECTORY_STRIPE_LEVEL_MAX: Maximum directory striping
    """
    DIRECTORY_STRIPE_LEVEL_UNSPECIFIED = 0
    DIRECTORY_STRIPE_LEVEL_MIN = 1
    DIRECTORY_STRIPE_LEVEL_BALANCED = 2
    DIRECTORY_STRIPE_LEVEL_MAX = 3

  class FileStripeLevelValueValuesEnum(_messages.Enum):
    r"""Optional. Immutable. Stripe level for files. Allowed values are: *
    `FILE_STRIPE_LEVEL_MIN`: offers the best performance for small size files.
    * `FILE_STRIPE_LEVEL_BALANCED`: balances performance for workloads
    involving a mix of small and large files. * `FILE_STRIPE_LEVEL_MAX`:
    higher throughput performance for larger files.

    Values:
      FILE_STRIPE_LEVEL_UNSPECIFIED: If not set, FileStripeLevel will default
        to FILE_STRIPE_LEVEL_BALANCED
      FILE_STRIPE_LEVEL_MIN: Minimum file striping
      FILE_STRIPE_LEVEL_BALANCED: Medium file striping
      FILE_STRIPE_LEVEL_MAX: Maximum file striping
    """
    FILE_STRIPE_LEVEL_UNSPECIFIED = 0
    FILE_STRIPE_LEVEL_MIN = 1
    FILE_STRIPE_LEVEL_BALANCED = 2
    FILE_STRIPE_LEVEL_MAX = 3

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The instance state.

    Values:
      STATE_UNSPECIFIED: Not set.
      CREATING: The instance is being created.
      ACTIVE: The instance is available for use.
      DELETING: The instance is being deleted.
      FAILED: LINT.IfChange The instance is not usable. LINT.ThenChange(//depo
        t/google3/configs/monitoring/boq/daos_clh/cloud_precomputes_lib.py)
      UPGRADING: The instance is being upgraded.
      REPAIRING: The instance is being repaired. This should only be used by
        instances using the `PERSISTENT` deployment type.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    FAILED = 4
    UPGRADING = 5
    REPAIRING = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Cloud Labels are a flexible and lightweight mechanism for
    organizing cloud resources into groups that reflect a customer's
    organizational needs and deployment strategies. See
    https://cloud.google.com/resource-manager/docs/labels-overview for
    details.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  accessPoints = _messages.StringField(1, repeated=True)
  capacityGib = _messages.IntegerField(2)
  createTime = _messages.StringField(3)
  daosVersion = _messages.StringField(4)
  deploymentType = _messages.EnumField('DeploymentTypeValueValuesEnum', 5)
  description = _messages.StringField(6)
  directoryStripeLevel = _messages.EnumField('DirectoryStripeLevelValueValuesEnum', 7)
  effectiveReservedIpRange = _messages.StringField(8)
  fileStripeLevel = _messages.EnumField('FileStripeLevelValueValuesEnum', 9)
  labels = _messages.MessageField('LabelsValue', 10)
  name = _messages.StringField(11)
  network = _messages.StringField(12)
  reservedIpRange = _messages.StringField(13)
  state = _messages.EnumField('StateValueValuesEnum', 14)
  updateTime = _messages.StringField(15)


class ListInstancesResponse(_messages.Message):
  r"""Response from ListInstances.

  Fields:
    instances: The list of Parallelstore instances.
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  instances = _messages.MessageField('Instance', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Long-running operation metadata.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class ParallelstoreProjectsLocationsGetRequest(_messages.Message):
  r"""A ParallelstoreProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class ParallelstoreProjectsLocationsInstancesCreateRequest(_messages.Message):
  r"""A ParallelstoreProjectsLocationsInstancesCreateRequest object.

  Fields:
    instance: A Instance resource to be passed as the request body.
    instanceId: Required. The name of the Parallelstore instance. * Must
      contain only lowercase letters, numbers, and hyphens. * Must start with
      a letter. * Must be between 1-63 characters. * Must end with a number or
      a letter. * Must be unique within the customer project / location
    parent: Required. The instance's project and location, in the format
      `projects/{project}/locations/{location}`. Locations map to Google Cloud
      zones; for example, `us-west1-b`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (********-0000-0000-0000-************).
  """

  instance = _messages.MessageField('Instance', 1)
  instanceId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class ParallelstoreProjectsLocationsInstancesDeleteRequest(_messages.Message):
  r"""A ParallelstoreProjectsLocationsInstancesDeleteRequest object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (********-0000-0000-0000-************).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class ParallelstoreProjectsLocationsInstancesExportDataRequest(_messages.Message):
  r"""A ParallelstoreProjectsLocationsInstancesExportDataRequest object.

  Fields:
    exportDataRequest: A ExportDataRequest resource to be passed as the
      request body.
    name: Required. Name of the resource.
  """

  exportDataRequest = _messages.MessageField('ExportDataRequest', 1)
  name = _messages.StringField(2, required=True)


class ParallelstoreProjectsLocationsInstancesGetRequest(_messages.Message):
  r"""A ParallelstoreProjectsLocationsInstancesGetRequest object.

  Fields:
    name: Required. The instance resource name, in the format
      `projects/{project_id}/locations/{location}/instances/{instance_id}`.
  """

  name = _messages.StringField(1, required=True)


class ParallelstoreProjectsLocationsInstancesImportDataRequest(_messages.Message):
  r"""A ParallelstoreProjectsLocationsInstancesImportDataRequest object.

  Fields:
    importDataRequest: A ImportDataRequest resource to be passed as the
      request body.
    name: Required. Name of the resource.
  """

  importDataRequest = _messages.MessageField('ImportDataRequest', 1)
  name = _messages.StringField(2, required=True)


class ParallelstoreProjectsLocationsInstancesListRequest(_messages.Message):
  r"""A ParallelstoreProjectsLocationsInstancesListRequest object.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint for how to order the results.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, the server will pick an appropriate
      default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The project and location for which to retrieve instance
      information, in the format `projects/{project_id}/locations/{location}`.
      To retrieve instance information for all locations, use "-" as the value
      of `{location}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ParallelstoreProjectsLocationsInstancesPatchRequest(_messages.Message):
  r"""A ParallelstoreProjectsLocationsInstancesPatchRequest object.

  Fields:
    instance: A Instance resource to be passed as the request body.
    name: Identifier. The resource name of the instance, in the format
      `projects/{project}/locations/{location}/instances/{instance_id}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (********-0000-0000-0000-************).
    updateMask: Required. Mask of fields to update. Field mask is used to
      specify the fields to be overwritten in the Instance resource by the
      update. At least one path must be supplied in this field. The fields
      specified in the update_mask are relative to the resource, not the full
      request.
  """

  instance = _messages.MessageField('Instance', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class ParallelstoreProjectsLocationsListRequest(_messages.Message):
  r"""A ParallelstoreProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class ParallelstoreProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A ParallelstoreProjectsLocationsOperationsCancelRequest object.

  Fields:
    name: The name of the operation resource to be cancelled.
  """

  name = _messages.StringField(1, required=True)


class ParallelstoreProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A ParallelstoreProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class ParallelstoreProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A ParallelstoreProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class ParallelstoreProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A ParallelstoreProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class ReconciliationOperationMetadata(_messages.Message):
  r"""Operation metadata returned by the CLH during resource state
  reconciliation.

  Enums:
    ExclusiveActionValueValuesEnum: Excluisive action returned by the CLH.

  Fields:
    deleteResource: DEPRECATED. Use exclusive_action instead.
    exclusiveAction: Excluisive action returned by the CLH.
  """

  class ExclusiveActionValueValuesEnum(_messages.Enum):
    r"""Excluisive action returned by the CLH.

    Values:
      UNKNOWN_REPAIR_ACTION: Unknown repair action.
      DELETE: The resource has to be deleted. When using this bit, the CLH
        should fail the operation. DEPRECATED. Instead use DELETE_RESOURCE
        OperationSignal in SideChannel.
      RETRY: This resource could not be repaired but the repair should be
        tried again at a later time. This can happen if there is a dependency
        that needs to be resolved first- e.g. if a parent resource must be
        repaired before a child resource.
    """
    UNKNOWN_REPAIR_ACTION = 0
    DELETE = 1
    RETRY = 2

  deleteResource = _messages.BooleanField(1)
  exclusiveAction = _messages.EnumField('ExclusiveActionValueValuesEnum', 2)


class SourceGcsBucket(_messages.Message):
  r"""Cloud Storage as the source of a data transfer.

  Fields:
    uri: Required. URI to a Cloud Storage bucket in the format: `gs:///`. The
      path inside the bucket is optional.
  """

  uri = _messages.StringField(1)


class SourceParallelstore(_messages.Message):
  r"""Parallelstore as the source of a data transfer.

  Fields:
    path: Optional. Root directory path to the Paralellstore filesystem,
      starting with `/`. Defaults to `/` if unset.
  """

  path = _messages.StringField(1)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TransferMetadataOptions(_messages.Message):
  r"""Transfer metadata options for the instance.

  Enums:
    GidValueValuesEnum: Optional. The GID preservation behavior.
    ModeValueValuesEnum: Optional. The mode preservation behavior.
    UidValueValuesEnum: Optional. The UID preservation behavior.

  Fields:
    gid: Optional. The GID preservation behavior.
    mode: Optional. The mode preservation behavior.
    uid: Optional. The UID preservation behavior.
  """

  class GidValueValuesEnum(_messages.Enum):
    r"""Optional. The GID preservation behavior.

    Values:
      GID_UNSPECIFIED: default is GID_NUMBER_PRESERVE.
      GID_SKIP: Do not preserve GID during a transfer job.
      GID_NUMBER_PRESERVE: Preserve GID that is in number format during a
        transfer job.
    """
    GID_UNSPECIFIED = 0
    GID_SKIP = 1
    GID_NUMBER_PRESERVE = 2

  class ModeValueValuesEnum(_messages.Enum):
    r"""Optional. The mode preservation behavior.

    Values:
      MODE_UNSPECIFIED: default is MODE_PRESERVE.
      MODE_SKIP: Do not preserve mode during a transfer job.
      MODE_PRESERVE: Preserve mode during a transfer job.
    """
    MODE_UNSPECIFIED = 0
    MODE_SKIP = 1
    MODE_PRESERVE = 2

  class UidValueValuesEnum(_messages.Enum):
    r"""Optional. The UID preservation behavior.

    Values:
      UID_UNSPECIFIED: default is UID_NUMBER_PRESERVE.
      UID_SKIP: Do not preserve UID during a transfer job.
      UID_NUMBER_PRESERVE: Preserve UID that is in number format during a
        transfer job.
    """
    UID_UNSPECIFIED = 0
    UID_SKIP = 1
    UID_NUMBER_PRESERVE = 2

  gid = _messages.EnumField('GidValueValuesEnum', 1)
  mode = _messages.EnumField('ModeValueValuesEnum', 2)
  uid = _messages.EnumField('UidValueValuesEnum', 3)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
