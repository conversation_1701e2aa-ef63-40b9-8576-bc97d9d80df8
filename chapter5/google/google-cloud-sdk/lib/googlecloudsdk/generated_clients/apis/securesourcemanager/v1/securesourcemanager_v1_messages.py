"""Generated message classes for securesourcemanager version v1.

Regionally deployed, single-tenant managed source code repository hosted on
Google Cloud.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'securesourcemanager'


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class BatchCreatePullRequestCommentsRequest(_messages.Message):
  r"""The request to batch create pull request comments.

  Fields:
    requests: Required. The request message specifying the resources to
      create. There should be exactly one CreatePullRequestCommentRequest with
      CommentDetail being REVIEW in the list, and no more than 100
      CreatePullRequestCommentRequests with CommentDetail being CODE in the
      list
  """

  requests = _messages.MessageField('CreatePullRequestCommentRequest', 1, repeated=True)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class Branch(_messages.Message):
  r"""Branch represents a branch involved in a pull request.

  Fields:
    ref: Required. Name of the branch.
    sha: Output only. The commit at the tip of the branch.
  """

  ref = _messages.StringField(1)
  sha = _messages.StringField(2)


class BranchRule(_messages.Message):
  r"""Metadata of a BranchRule. BranchRule is the protection rule to enforce
  pre-defined rules on designated branches within a repository.

  Messages:
    AnnotationsValue: Optional. User annotations. These attributes can only be
      set and used by the user. See https://google.aip.dev/128#annotations for
      more details such as format and size limitations.

  Fields:
    allowStaleReviews: Optional. Determines if allow stale reviews or
      approvals before merging to the branch.
    annotations: Optional. User annotations. These attributes can only be set
      and used by the user. See https://google.aip.dev/128#annotations for
      more details such as format and size limitations.
    createTime: Output only. Create timestamp.
    disabled: Optional. Determines if the branch rule is disabled or not.
    etag: Optional. This checksum is computed by the server based on the value
      of other fields, and may be sent on update and delete requests to ensure
      the client has an up-to-date value before proceeding.
    includePattern: Optional. The pattern of the branch that can match to this
      BranchRule. Specified as regex. .* for all branches. Examples: main,
      (main|release.*). Current MVP phase only support `.*` for wildcard.
    minimumApprovalsCount: Optional. The minimum number of approvals required
      for the branch rule to be matched.
    minimumReviewsCount: Optional. The minimum number of reviews required for
      the branch rule to be matched.
    name: Optional. A unique identifier for a BranchRule. The name should be
      of the format: `projects/{project}/locations/{location}/repositories/{re
      pository}/branchRules/{branch_rule}`
    requireCommentsResolved: Optional. Determines if require comments resolved
      before merging to the branch.
    requireLinearHistory: Optional. Determines if require linear history
      before merging to the branch.
    requirePullRequest: Optional. Determines if the branch rule requires a
      pull request or not.
    requiredStatusChecks: Optional. List of required status checks before
      merging to the branch.
    uid: Output only. Unique identifier of the repository.
    updateTime: Output only. Update timestamp.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. User annotations. These attributes can only be set and used
    by the user. See https://google.aip.dev/128#annotations for more details
    such as format and size limitations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  allowStaleReviews = _messages.BooleanField(1)
  annotations = _messages.MessageField('AnnotationsValue', 2)
  createTime = _messages.StringField(3)
  disabled = _messages.BooleanField(4)
  etag = _messages.StringField(5)
  includePattern = _messages.StringField(6)
  minimumApprovalsCount = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  minimumReviewsCount = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  name = _messages.StringField(9)
  requireCommentsResolved = _messages.BooleanField(10)
  requireLinearHistory = _messages.BooleanField(11)
  requirePullRequest = _messages.BooleanField(12)
  requiredStatusChecks = _messages.MessageField('Check', 13, repeated=True)
  uid = _messages.StringField(14)
  updateTime = _messages.StringField(15)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class Check(_messages.Message):
  r"""Check is a type for status check.

  Fields:
    context: Required. The context of the check.
  """

  context = _messages.StringField(1)


class CloseIssueRequest(_messages.Message):
  r"""The request to close an issue.

  Fields:
    etag: Optional. The current etag of the issue. If the etag is provided and
      does not match the current etag of the issue, closing will be blocked
      and an ABORTED error will be returned.
  """

  etag = _messages.StringField(1)


class ClosePullRequestRequest(_messages.Message):
  r"""ClosePullRequestRequest is the request to close a pull request."""


class Code(_messages.Message):
  r"""The comment on a code line.

  Fields:
    body: Required. The comment body.
    effectiveCommitSha: Output only. The effective commit sha this code
      comment is pointing to.
    effectiveRootComment: Output only. The root comment of the conversation,
      derived from the reply field.
    position: Optional. The position of the comment.
    reply: Optional. Input only. The PullRequestComment resource name that
      this comment is replying to.
    resolved: Output only. Boolean indicator if the comment is resolved.
  """

  body = _messages.StringField(1)
  effectiveCommitSha = _messages.StringField(2)
  effectiveRootComment = _messages.StringField(3)
  position = _messages.MessageField('Position', 4)
  reply = _messages.StringField(5)
  resolved = _messages.BooleanField(6)


class Comment(_messages.Message):
  r"""The general pull request comment.

  Fields:
    body: Required. The comment body.
  """

  body = _messages.StringField(1)


class CreatePullRequestCommentRequest(_messages.Message):
  r"""The request to create a pull request comment.

  Fields:
    parent: Required. The pull request in which to create the pull request
      comment. Format: `projects/{project_number}/locations/{location_id}/repo
      sitories/{repository_id}/pullRequests/{pull_request_id}`
    pullRequestComment: Required. The pull request comment to create.
  """

  parent = _messages.StringField(1)
  pullRequestComment = _messages.MessageField('PullRequestComment', 2)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class FetchBlobResponse(_messages.Message):
  r"""Response message containing the content of a blob.

  Fields:
    content: The content of the blob, encoded as base64.
    sha: The SHA-1 hash of the blob.
  """

  content = _messages.StringField(1)
  sha = _messages.StringField(2)


class FetchTreeResponse(_messages.Message):
  r"""Response message containing a list of TreeEntry objects.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    treeEntries: The list of TreeEntry objects.
  """

  nextPageToken = _messages.StringField(1)
  treeEntries = _messages.MessageField('TreeEntry', 2, repeated=True)


class FileDiff(_messages.Message):
  r"""Metadata of a FileDiff. FileDiff represents a single file diff in a pull
  request.

  Enums:
    ActionValueValuesEnum: Output only. The action taken on the file (eg.
      added, modified, deleted).

  Fields:
    action: Output only. The action taken on the file (eg. added, modified,
      deleted).
    name: Output only. The name of the file.
    patch: Output only. The git patch containing the file changes.
    sha: Output only. The commit pointing to the file changes.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Output only. The action taken on the file (eg. added, modified,
    deleted).

    Values:
      ACTION_UNSPECIFIED: Unspecified.
      ADDED: The file was added.
      MODIFIED: The file was modified.
      DELETED: The file was deleted.
    """
    ACTION_UNSPECIFIED = 0
    ADDED = 1
    MODIFIED = 2
    DELETED = 3

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  name = _messages.StringField(2)
  patch = _messages.StringField(3)
  sha = _messages.StringField(4)


class Hook(_messages.Message):
  r"""Metadata of a Secure Source Manager Hook.

  Enums:
    EventsValueListEntryValuesEnum:

  Fields:
    createTime: Output only. Create timestamp.
    disabled: Optional. Determines if the hook disabled or not. Set to true to
      stop sending traffic.
    events: Optional. The events that trigger hook on.
    name: Identifier. A unique identifier for a Hook. The name should be of
      the format: `projects/{project}/locations/{location_id}/repositories/{re
      pository_id}/hooks/{hook_id}`
    pushOption: Optional. The trigger option for push events.
    sensitiveQueryString: Optional. The sensitive query string to be appended
      to the target URI.
    targetUri: Required. The target URI to which the payloads will be
      delivered.
    uid: Output only. Unique identifier of the hook.
    updateTime: Output only. Update timestamp.
  """

  class EventsValueListEntryValuesEnum(_messages.Enum):
    r"""EventsValueListEntryValuesEnum enum type.

    Values:
      UNSPECIFIED: Unspecified.
      PUSH: Push events are triggered when pushing to the repository.
      PULL_REQUEST: Pull request events are triggered when a pull request is
        opened, closed, reopened, or edited.
    """
    UNSPECIFIED = 0
    PUSH = 1
    PULL_REQUEST = 2

  createTime = _messages.StringField(1)
  disabled = _messages.BooleanField(2)
  events = _messages.EnumField('EventsValueListEntryValuesEnum', 3, repeated=True)
  name = _messages.StringField(4)
  pushOption = _messages.MessageField('PushOption', 5)
  sensitiveQueryString = _messages.StringField(6)
  targetUri = _messages.StringField(7)
  uid = _messages.StringField(8)
  updateTime = _messages.StringField(9)


class HostConfig(_messages.Message):
  r"""HostConfig has different instance endpoints.

  Fields:
    api: Output only. API hostname.
    gitHttp: Output only. Git HTTP hostname.
    gitSsh: Output only. Git SSH hostname.
    html: Output only. HTML hostname.
  """

  api = _messages.StringField(1)
  gitHttp = _messages.StringField(2)
  gitSsh = _messages.StringField(3)
  html = _messages.StringField(4)


class InitialConfig(_messages.Message):
  r"""Repository initialization configuration.

  Fields:
    defaultBranch: Default branch name of the repository.
    gitignores: List of gitignore template names user can choose from. Valid
      values: actionscript, ada, agda, android, anjuta, ansible, appcelerator-
      titanium, app-engine, archives, arch-linux-packages, atmel-studio,
      autotools, backup, bazaar, bazel, bitrix, bricx-cc, c, cake-php,
      calabash, cf-wheels, chef-cookbook, clojure, cloud9, c-make, code-
      igniter, code-kit, code-sniffer, common-lisp, composer, concrete5, coq,
      cordova, cpp, craft-cms, cuda, cvs, d, dart, dart-editor, delphi, diff,
      dm, dreamweaver, dropbox, drupal, drupal-7, eagle, eclipse, eiffel-
      studio, elisp, elixir, elm, emacs, ensime, epi-server, erlang, esp-idf,
      espresso, exercism, expression-engine, ext-js, fancy, finale, flex-
      builder, force-dot-com, fortran, fuel-php, gcov, git-book, gnome-shell-
      extension, go, godot, gpg, gradle, grails, gwt, haskell, hugo, iar-
      ewarm, idris, igor-pro, images, infor-cms, java, jboss, jboss-4,
      jboss-6, jdeveloper, jekyll, jenkins-home, jenv, jet-brains, jigsaw,
      joomla, julia, jupyter-notebooks, kate, kdevelop4, kentico, ki-cad,
      kohana, kotlin, lab-view, laravel, lazarus, leiningen, lemon-stand,
      libre-office, lilypond, linux, lithium, logtalk, lua, lyx, mac-os,
      magento, magento-1, magento-2, matlab, maven, mercurial, mercury,
      metals, meta-programming-system, meteor, microsoft-office, model-sim,
      momentics, mono-develop, nanoc, net-beans, nikola, nim, ninja, node,
      notepad-pp, nwjs, objective--c, ocaml, octave, opa, open-cart, openssl,
      oracle-forms, otto, packer, patch, perl, perl6, phalcon, phoenix,
      pimcore, play-framework, plone, prestashop, processing, psoc-creator,
      puppet, pure-script, putty, python, qooxdoo, qt, r, racket, rails, raku,
      red, redcar, redis, rhodes-rhomobile, ros, ruby, rust, sam, sass, sbt,
      scala, scheme, scons, scrivener, sdcc, seam-gen, sketch-up, slick-edit,
      smalltalk, snap, splunk, stata, stella, sublime-text, sugar-crm, svn,
      swift, symfony, symphony-cms, synopsys-vcs, tags, terraform, tex, text-
      mate, textpattern, think-php, tortoise-git, turbo-gears-2, typo3,
      umbraco, unity, unreal-engine, vagrant, vim, virtual-env, virtuoso,
      visual-studio, visual-studio-code, vue, vvvv, waf, web-methods, windows,
      word-press, xcode, xilinx, xilinx-ise, xojo, yeoman, yii, zend-
      framework, zephir.
    license: License template name user can choose from. Valid values:
      license-0bsd, license-389-exception, aal, abstyles, adobe-2006, adobe-
      glyph, adsl, afl-1-1, afl-1-2, afl-2-0, afl-2-1, afl-3-0, afmparse,
      agpl-1-0, agpl-1-0-only, agpl-1-0-or-later, agpl-3-0-only, agpl-3-0-or-
      later, aladdin, amdplpa, aml, ampas, antlr-pd, antlr-pd-fallback,
      apache-1-0, apache-1-1, apache-2-0, apafml, apl-1-0, apsl-1-0, apsl-1-1,
      apsl-1-2, apsl-2-0, artistic-1-0, artistic-1-0-cl8, artistic-1-0-perl,
      artistic-2-0, autoconf-exception-2-0, autoconf-exception-3-0, bahyph,
      barr, beerware, bison-exception-2-2, bittorrent-1-0, bittorrent-1-1,
      blessing, blueoak-1-0-0, bootloader-exception, borceux, bsd-1-clause,
      bsd-2-clause, bsd-2-clause-freebsd, bsd-2-clause-netbsd, bsd-2-clause-
      patent, bsd-2-clause-views, bsd-3-clause, bsd-3-clause-attribution,
      bsd-3-clause-clear, bsd-3-clause-lbnl, bsd-3-clause-modification,
      bsd-3-clause-no-nuclear-license, bsd-3-clause-no-nuclear-license-2014,
      bsd-3-clause-no-nuclear-warranty, bsd-3-clause-open-mpi, bsd-4-clause,
      bsd-4-clause-shortened, bsd-4-clause-uc, bsd-protection, bsd-source-
      code, bsl-1-0, busl-1-1, cal-1-0, cal-1-0-combined-work-exception,
      caldera, catosl-1-1, cc0-1-0, cc-by-1-0, cc-by-2-0, cc-by-3-0, cc-
      by-3-0-at, cc-by-3-0-us, cc-by-4-0, cc-by-nc-1-0, cc-by-nc-2-0, cc-by-
      nc-3-0, cc-by-nc-4-0, cc-by-nc-nd-1-0, cc-by-nc-nd-2-0, cc-by-nc-nd-3-0,
      cc-by-nc-nd-3-0-igo, cc-by-nc-nd-4-0, cc-by-nc-sa-1-0, cc-by-nc-sa-2-0,
      cc-by-nc-sa-3-0, cc-by-nc-sa-4-0, cc-by-nd-1-0, cc-by-nd-2-0, cc-by-
      nd-3-0, cc-by-nd-4-0, cc-by-sa-1-0, cc-by-sa-2-0, cc-by-sa-2-0-uk, cc-
      by-sa-2-1-jp, cc-by-sa-3-0, cc-by-sa-3-0-at, cc-by-sa-4-0, cc-pddc,
      cddl-1-0, cddl-1-1, cdla-permissive-1-0, cdla-sharing-1-0, cecill-1-0,
      cecill-1-1, cecill-2-0, cecill-2-1, cecill-b, cecill-c, cern-ohl-1-1,
      cern-ohl-1-2, cern-ohl-p-2-0, cern-ohl-s-2-0, cern-ohl-w-2-0,
      clartistic, classpath-exception-2-0, clisp-exception-2-0, cnri-jython,
      cnri-python, cnri-python-gpl-compatible, condor-1-1, copyleft-
      next-0-3-0, copyleft-next-0-3-1, cpal-1-0, cpl-1-0, cpol-1-02,
      crossword, crystal-stacker, cua-opl-1-0, cube, c-uda-1-0, curl,
      d-fsl-1-0, diffmark, digirule-foss-exception, doc, dotseqn, drl-1-0,
      dsdp, dvipdfm, ecl-1-0, ecl-2-0, ecos-exception-2-0, efl-1-0, efl-2-0,
      egenix, entessa, epics, epl-1-0, epl-2-0, erlpl-1-1, etalab-2-0, eu-
      datagrid, eupl-1-0, eupl-1-1, eupl-1-2, eurosym, fair, fawkes-runtime-
      exception, fltk-exception, font-exception-2-0, frameworx-1-0, freebsd-
      doc, freeimage, freertos-exception-2-0, fsfap, fsful, fsfullr, ftl, gcc-
      exception-2-0, gcc-exception-3-1, gd, gfdl-1-1-invariants-only,
      gfdl-1-1-invariants-or-later, gfdl-1-1-no-invariants-only, gfdl-1-1-no-
      invariants-or-later, gfdl-1-1-only, gfdl-1-1-or-later,
      gfdl-1-2-invariants-only, gfdl-1-2-invariants-or-later, gfdl-1-2-no-
      invariants-only, gfdl-1-2-no-invariants-or-later, gfdl-1-2-only,
      gfdl-1-2-or-later, gfdl-1-3-invariants-only, gfdl-1-3-invariants-or-
      later, gfdl-1-3-no-invariants-only, gfdl-1-3-no-invariants-or-later,
      gfdl-1-3-only, gfdl-1-3-or-later, giftware, gl2ps, glide, glulxe,
      glwtpl, gnu-javamail-exception, gnuplot, gpl-1-0-only, gpl-1-0-or-later,
      gpl-2-0-only, gpl-2-0-or-later, gpl-3-0-linking-exception,
      gpl-3-0-linking-source-exception, gpl-3-0-only, gpl-3-0-or-later, gpl-
      cc-1-0, gsoap-1-3b, haskell-report, hippocratic-2-1, hpnd, hpnd-sell-
      variant, htmltidy, i2p-gpl-java-exception, ibm-pibs, icu, ijg, image-
      magick, imatix, imlib2, info-zip, intel, intel-acpi, interbase-1-0, ipa,
      ipl-1-0, isc, jasper-2-0, jpnic, json, lal-1-2, lal-1-3, latex2e,
      leptonica, lgpl-2-0-only, lgpl-2-0-or-later, lgpl-2-1-only, lgpl-2-1-or-
      later, lgpl-3-0-linking-exception, lgpl-3-0-only, lgpl-3-0-or-later,
      lgpllr, libpng, libpng-2-0, libselinux-1-0, libtiff, libtool-exception,
      liliq-p-1-1, liliq-r-1-1, liliq-rplus-1-1, linux-openib, linux-syscall-
      note, llvm-exception, lpl-1-0, lpl-1-02, lppl-1-0, lppl-1-1, lppl-1-2,
      lppl-1-3a, lppl-1-3c, lzma-exception, make-index, mif-exception, miros,
      mit, mit-0, mit-advertising, mit-cmu, mit-enna, mit-feh, mit-modern-
      variant, mitnfa, mit-open-group, motosoto, mpich2, mpl-1-0, mpl-1-1,
      mpl-2-0, mpl-2-0-no-copyleft-exception, ms-pl, ms-rl, mtll,
      mulanpsl-1-0, mulanpsl-2-0, multics, mup, naist-2003, nasa-1-3, naumen,
      nbpl-1-0, ncgl-uk-2-0, ncsa, netcdf, net-snmp, newsletr, ngpl, nist-pd,
      nist-pd-fallback, nlod-1-0, nlpl, nokia, nokia-qt-exception-1-1, nosl,
      noweb, npl-1-0, npl-1-1, nposl-3-0, nrl, ntp, ntp-0, ocaml-lgpl-linking-
      exception, occt-exception-1-0, occt-pl, oclc-2-0, odbl-1-0, odc-by-1-0,
      ofl-1-0, ofl-1-0-no-rfn, ofl-1-0-rfn, ofl-1-1, ofl-1-1-no-rfn,
      ofl-1-1-rfn, ogc-1-0, ogdl-taiwan-1-0, ogl-canada-2-0, ogl-uk-1-0, ogl-
      uk-2-0, ogl-uk-3-0, ogtsl, oldap-1-1, oldap-1-2, oldap-1-3, oldap-1-4,
      oldap-2-0, oldap-2-0-1, oldap-2-1, oldap-2-2, oldap-2-2-1, oldap-2-2-2,
      oldap-2-3, oldap-2-4, oldap-2-7, oml, openjdk-assembly-exception-1-0,
      openssl, openvpn-openssl-exception, opl-1-0, oset-pl-2-1, osl-1-0,
      osl-1-1, osl-2-0, osl-2-1, osl-3-0, o-uda-1-0, parity-6-0-0,
      parity-7-0-0, pddl-1-0, php-3-0, php-3-01, plexus, polyform-
      noncommercial-1-0-0, polyform-small-business-1-0-0, postgresql, psf-2-0,
      psfrag, ps-or-pdf-font-exception-20170817, psutils, python-2-0, qhull,
      qpl-1-0, qt-gpl-exception-1-0, qt-lgpl-exception-1-1, qwt-exception-1-0,
      rdisc, rhecos-1-1, rpl-1-1, rpsl-1-0, rsa-md, rscpl, ruby, saxpath, sax-
      pd, scea, sendmail, sendmail-8-23, sgi-b-1-0, sgi-b-1-1, sgi-b-2-0,
      shl-0-51, shl-2-0, shl-2-1, simpl-2-0, sissl, sissl-1-2, sleepycat,
      smlnj, smppl, snia, spencer-86, spencer-94, spencer-99, spl-1-0, ssh-
      openssh, ssh-short, sspl-1-0, sugarcrm-1-1-3, swift-exception, swl,
      tapr-ohl-1-0, tcl, tcp-wrappers, tmate, torque-1-1, tosl, tu-berlin-1-0,
      tu-berlin-2-0, u-boot-exception-2-0, ucl-1-0, unicode-dfs-2015, unicode-
      dfs-2016, unicode-tou, universal-foss-exception-1-0, unlicense, upl-1-0,
      vim, vostrom, vsl-1-0, w3c, w3c-19980720, w3c-20150513, watcom-1-0,
      wsuipa, wtfpl, wxwindows-exception-3-1, x11, xerox, xfree86-1-1, xinetd,
      xnet, xpp, xskat, ypl-1-0, ypl-1-1, zed, zend-2-0, zimbra-1-3,
      zimbra-1-4, zlib, zlib-acknowledgement, zpl-1-1, zpl-2-0, zpl-2-1.
    readme: README template name. Valid template name(s) are: default.
  """

  defaultBranch = _messages.StringField(1)
  gitignores = _messages.StringField(2, repeated=True)
  license = _messages.StringField(3)
  readme = _messages.StringField(4)


class Instance(_messages.Message):
  r"""A resource that represents a Secure Source Manager instance.

  Enums:
    StateValueValuesEnum: Output only. Current state of the instance.
    StateNoteValueValuesEnum: Output only. An optional field providing
      information about the current instance state.

  Messages:
    LabelsValue: Optional. Labels as key value pairs.

  Fields:
    createTime: Output only. Create timestamp.
    hostConfig: Output only. A list of hostnames for this instance.
    kmsKey: Optional. Immutable. Customer-managed encryption key name, in the
      format projects/*/locations/*/keyRings/*/cryptoKeys/*.
    labels: Optional. Labels as key value pairs.
    name: Optional. A unique identifier for an instance. The name should be of
      the format: `projects/{project_number}/locations/{location_id}/instances
      /{instance_id}` `project_number`: Maps to a unique int64 id assigned to
      each project. `location_id`: Refers to the region where the instance
      will be deployed. Since Secure Source Manager is a regional service, it
      must be one of the valid GCP regions. `instance_id`: User provided name
      for the instance, must be unique for a project_number and location_id
      combination.
    privateConfig: Optional. Private settings for private instance.
    state: Output only. Current state of the instance.
    stateNote: Output only. An optional field providing information about the
      current instance state.
    updateTime: Output only. Update timestamp.
    workforceIdentityFederationConfig: Optional. Configuration for Workforce
      Identity Federation to support third party identity provider. If unset,
      defaults to the Google OIDC IdP.
  """

  class StateNoteValueValuesEnum(_messages.Enum):
    r"""Output only. An optional field providing information about the current
    instance state.

    Values:
      STATE_NOTE_UNSPECIFIED: STATE_NOTE_UNSPECIFIED as the first value of
        State.
      PAUSED_CMEK_UNAVAILABLE: CMEK access is unavailable.
      INSTANCE_RESUMING: INSTANCE_RESUMING indicates that the instance was
        previously paused and is under the process of being brought back.
    """
    STATE_NOTE_UNSPECIFIED = 0
    PAUSED_CMEK_UNAVAILABLE = 1
    INSTANCE_RESUMING = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the instance.

    Values:
      STATE_UNSPECIFIED: Not set. This should only be the case for incoming
        requests.
      CREATING: Instance is being created.
      ACTIVE: Instance is ready.
      DELETING: Instance is being deleted.
      PAUSED: Instance is paused.
      UNKNOWN: Instance is unknown, we are not sure if it's functioning.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    PAUSED = 4
    UNKNOWN = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  hostConfig = _messages.MessageField('HostConfig', 2)
  kmsKey = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  privateConfig = _messages.MessageField('PrivateConfig', 6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  stateNote = _messages.EnumField('StateNoteValueValuesEnum', 8)
  updateTime = _messages.StringField(9)
  workforceIdentityFederationConfig = _messages.MessageField('WorkforceIdentityFederationConfig', 10)


class Issue(_messages.Message):
  r"""Metadata of an Issue.

  Enums:
    StateValueValuesEnum: Output only. State of the issue.

  Fields:
    body: Optional. Issue body. Provides a detailed description of the issue.
    closeTime: Output only. Close timestamp (if closed). Cleared when is re-
      opened.
    createTime: Output only. Creation timestamp.
    etag: Optional. This checksum is computed by the server based on the value
      of other fields, and may be sent on update and delete requests to ensure
      the client has an up-to-date value before proceeding.
    name: Identifier. Unique identifier for an issue. The issue id is
      generated by the server. Format: `projects/{project}/locations/{location
      }/repositories/{repository}/issues/{issue_id}`
    state: Output only. State of the issue.
    title: Required. Issue title.
    updateTime: Output only. Last updated timestamp.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the issue.

    Values:
      STATE_UNSPECIFIED: Unspecified.
      OPEN: An open issue.
      CLOSED: A closed issue.
    """
    STATE_UNSPECIFIED = 0
    OPEN = 1
    CLOSED = 2

  body = _messages.StringField(1)
  closeTime = _messages.StringField(2)
  createTime = _messages.StringField(3)
  etag = _messages.StringField(4)
  name = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  title = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class IssueComment(_messages.Message):
  r"""IssueComment represents a comment on an issue.

  Fields:
    body: Required. The comment body.
    createTime: Output only. Creation timestamp.
    name: Identifier. Unique identifier for an issue comment. The comment id
      is generated by the server. Format: `projects/{project}/locations/{locat
      ion}/repositories/{repository}/issues/{issue}/issueComments/{comment_id}
      `
    updateTime: Output only. Last updated timestamp.
  """

  body = _messages.StringField(1)
  createTime = _messages.StringField(2)
  name = _messages.StringField(3)
  updateTime = _messages.StringField(4)


class IssueRedirectTicketInternalRequest(_messages.Message):
  r"""IssueRedirectTicketInternalRequest is the request to issue a redirect
  ticket for an instance. For internal use only.

  Fields:
    redirectUri: Required. URI to be used in the redirect.
  """

  redirectUri = _messages.StringField(1)


class IssueRedirectTicketInternalResponse(_messages.Message):
  r"""A IssueRedirectTicketInternalResponse object.

  Fields:
    ticketId: ID of the created redirect ticket.
  """

  ticketId = _messages.StringField(1)


class ListBranchRulesResponse(_messages.Message):
  r"""ListBranchRulesResponse is the response to listing branchRules.

  Fields:
    branchRules: The list of branch rules.
    nextPageToken: A token identifying a page of results the server should
      return.
  """

  branchRules = _messages.MessageField('BranchRule', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListHooksResponse(_messages.Message):
  r"""ListHooksResponse is response to list hooks.

  Fields:
    hooks: The list of hooks.
    nextPageToken: A token identifying a page of results the server should
      return.
  """

  hooks = _messages.MessageField('Hook', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListInstancesResponse(_messages.Message):
  r"""A ListInstancesResponse object.

  Fields:
    instances: The list of instances.
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  instances = _messages.MessageField('Instance', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListIssueCommentsResponse(_messages.Message):
  r"""The response to list issue comments.

  Fields:
    issueComments: The list of issue comments.
    nextPageToken: A token identifying a page of results the server should
      return.
  """

  issueComments = _messages.MessageField('IssueComment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListIssuesResponse(_messages.Message):
  r"""The response to list issues.

  Fields:
    issues: The list of issues.
    nextPageToken: A token identifying a page of results the server should
      return.
  """

  issues = _messages.MessageField('Issue', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListPullRequestCommentsResponse(_messages.Message):
  r"""The response to list pull request comments.

  Fields:
    nextPageToken: A token to set as page_token to retrieve the next page. If
      this field is omitted, there are no subsequent pages.
    pullRequestComments: The list of pull request comments.
  """

  nextPageToken = _messages.StringField(1)
  pullRequestComments = _messages.MessageField('PullRequestComment', 2, repeated=True)


class ListPullRequestFileDiffsResponse(_messages.Message):
  r"""ListPullRequestFileDiffsResponse is the response containing file diffs
  returned from ListPullRequestFileDiffs.

  Fields:
    fileDiffs: The list of pull request file diffs.
    nextPageToken: A token identifying a page of results the server should
      return.
  """

  fileDiffs = _messages.MessageField('FileDiff', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListPullRequestsResponse(_messages.Message):
  r"""ListPullRequestsResponse is the response to list pull requests.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    pullRequests: The list of pull requests.
  """

  nextPageToken = _messages.StringField(1)
  pullRequests = _messages.MessageField('PullRequest', 2, repeated=True)


class ListRepositoriesResponse(_messages.Message):
  r"""A ListRepositoriesResponse object.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    repositories: The list of repositories.
  """

  nextPageToken = _messages.StringField(1)
  repositories = _messages.MessageField('Repository', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class MergePullRequestRequest(_messages.Message):
  r"""MergePullRequestRequest is the request to merge a pull request."""


class OpenIssueRequest(_messages.Message):
  r"""The request to open an issue.

  Fields:
    etag: Optional. The current etag of the issue. If the etag is provided and
      does not match the current etag of the issue, opening will be blocked
      and an ABORTED error will be returned.
  """

  etag = _messages.StringField(1)


class OpenPullRequestRequest(_messages.Message):
  r"""OpenPullRequestRequest is the request to open a pull request."""


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class Position(_messages.Message):
  r"""The position of the code comment.

  Fields:
    line: Required. The line number of the comment. Positive value means it's
      on the new side of the diff, negative value means it's on the old side.
    path: Required. The path of the file.
  """

  line = _messages.IntegerField(1)
  path = _messages.StringField(2)


class PrivateConfig(_messages.Message):
  r"""PrivateConfig includes settings for private instance.

  Fields:
    caPool: Optional. Immutable. CA pool resource, resource must in the format
      of `projects/{project}/locations/{location}/caPools/{ca_pool}`.
    httpServiceAttachment: Output only. Service Attachment for HTTP, resource
      is in the format of `projects/{project}/regions/{region}/serviceAttachme
      nts/{service_attachment}`.
    isPrivate: Required. Immutable. Indicate if it's private instance.
    pscAllowedProjects: Optional. Additional allowed projects for setting up
      PSC connections. Instance host project is automatically allowed and does
      not need to be included in this list.
    sshServiceAttachment: Output only. Service Attachment for SSH, resource is
      in the format of `projects/{project}/regions/{region}/serviceAttachments
      /{service_attachment}`.
  """

  caPool = _messages.StringField(1)
  httpServiceAttachment = _messages.StringField(2)
  isPrivate = _messages.BooleanField(3)
  pscAllowedProjects = _messages.StringField(4, repeated=True)
  sshServiceAttachment = _messages.StringField(5)


class PullRequest(_messages.Message):
  r"""Metadata of a PullRequest. PullRequest is the request from a user to
  merge a branch (head) into another branch (base).

  Enums:
    StateValueValuesEnum: Output only. State of the pull request (open, closed
      or merged).

  Fields:
    base: Required. The branch to merge changes in.
    body: Optional. The pull request body. Provides a detailed description of
      the changes.
    closeTime: Output only. Close timestamp (if closed or merged). Cleared
      when pull request is re-opened.
    createTime: Output only. Creation timestamp.
    head: Immutable. The branch containing the changes to be merged.
    name: Output only. A unique identifier for a PullRequest. The number
      appended at the end is generated by the server. Format: `projects/{proje
      ct}/locations/{location}/repositories/{repository}/pullRequests/{pull_re
      quest_id}`
    state: Output only. State of the pull request (open, closed or merged).
    title: Required. The pull request title.
    updateTime: Output only. Last updated timestamp.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the pull request (open, closed or merged).

    Values:
      STATE_UNSPECIFIED: Unspecified.
      OPEN: An open pull request.
      CLOSED: A closed pull request.
      MERGED: A merged pull request.
    """
    STATE_UNSPECIFIED = 0
    OPEN = 1
    CLOSED = 2
    MERGED = 3

  base = _messages.MessageField('Branch', 1)
  body = _messages.StringField(2)
  closeTime = _messages.StringField(3)
  createTime = _messages.StringField(4)
  head = _messages.MessageField('Branch', 5)
  name = _messages.StringField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  title = _messages.StringField(8)
  updateTime = _messages.StringField(9)


class PullRequestComment(_messages.Message):
  r"""PullRequestComment represents a comment on a pull request.

  Fields:
    code: Optional. The comment on a code line.
    comment: Optional. The general pull request comment.
    createTime: Output only. Creation timestamp.
    name: Identifier. Unique identifier for the pull request comment. The
      comment id is generated by the server. Format: `projects/{project}/locat
      ions/{location}/repositories/{repository}/pullRequests/{pull_request}/pu
      llRequestComments/{comment_id}`
    review: Optional. The review summary comment.
    updateTime: Output only. Last updated timestamp.
  """

  code = _messages.MessageField('Code', 1)
  comment = _messages.MessageField('Comment', 2)
  createTime = _messages.StringField(3)
  name = _messages.StringField(4)
  review = _messages.MessageField('Review', 5)
  updateTime = _messages.StringField(6)


class PushOption(_messages.Message):
  r"""A PushOption object.

  Fields:
    branchFilter: Optional. Trigger hook for matching branches only. Specified
      as glob pattern. If empty or *, events for all branches are reported.
      Examples: main, {main,release*}. See
      https://pkg.go.dev/github.com/gobwas/glob documentation.
  """

  branchFilter = _messages.StringField(1)


class Repository(_messages.Message):
  r"""Metadata of a Secure Source Manager repository.

  Fields:
    createTime: Output only. Create timestamp.
    description: Optional. Description of the repository, which cannot exceed
      500 characters.
    etag: Optional. This checksum is computed by the server based on the value
      of other fields, and may be sent on update and delete requests to ensure
      the client has an up-to-date value before proceeding.
    initialConfig: Input only. Initial configurations for the repository.
    instance: Optional. The name of the instance in which the repository is
      hosted, formatted as `projects/{project_number}/locations/{location_id}/
      instances/{instance_id}` When creating repository via
      securesourcemanager.googleapis.com, this field is used as input. When
      creating repository via *.sourcemanager.dev, this field is output only.
    name: Optional. A unique identifier for a repository. The name should be
      of the format: `projects/{project}/locations/{location_id}/repositories/
      {repository_id}`
    uid: Output only. Unique identifier of the repository.
    updateTime: Output only. Update timestamp.
    uris: Output only. URIs for the repository.
  """

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  etag = _messages.StringField(3)
  initialConfig = _messages.MessageField('InitialConfig', 4)
  instance = _messages.StringField(5)
  name = _messages.StringField(6)
  uid = _messages.StringField(7)
  updateTime = _messages.StringField(8)
  uris = _messages.MessageField('URIs', 9)


class ResolvePullRequestCommentsRequest(_messages.Message):
  r"""The request to resolve multiple pull request comments.

  Fields:
    autoFill: Optional. If set, at least one comment in a thread is required,
      rest of the comments in the same thread will be automatically updated to
      resolved. If unset, all comments in the same thread need be present.
    names: Required. The names of the pull request comments to resolve.
      Format: `projects/{project_number}/locations/{location_id}/repositories/
      {repository_id}/pullRequests/{pull_request_id}/pullRequestComments/{comm
      ent_id}` Only comments from the same threads are allowed in the same
      request.
  """

  autoFill = _messages.BooleanField(1)
  names = _messages.StringField(2, repeated=True)


class Review(_messages.Message):
  r"""The review summary comment.

  Enums:
    ActionTypeValueValuesEnum: Required. The review action type.

  Fields:
    actionType: Required. The review action type.
    body: Optional. The comment body.
    effectiveCommitSha: Output only. The effective commit sha this review is
      pointing to.
  """

  class ActionTypeValueValuesEnum(_messages.Enum):
    r"""Required. The review action type.

    Values:
      ACTION_TYPE_UNSPECIFIED: Unspecified.
      COMMENT: A general review comment.
      CHANGE_REQUESTED: Change required from this review.
      APPROVED: Change approved from this review.
    """
    ACTION_TYPE_UNSPECIFIED = 0
    COMMENT = 1
    CHANGE_REQUESTED = 2
    APPROVED = 3

  actionType = _messages.EnumField('ActionTypeValueValuesEnum', 1)
  body = _messages.StringField(2)
  effectiveCommitSha = _messages.StringField(3)


class SecuresourcemanagerProjectsLocationsGetRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class SecuresourcemanagerProjectsLocationsInstancesCreateRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsInstancesCreateRequest object.

  Fields:
    instance: A Instance resource to be passed as the request body.
    instanceId: Required. ID of the instance to be created.
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  instance = _messages.MessageField('Instance', 1)
  instanceId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class SecuresourcemanagerProjectsLocationsInstancesDeleteRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsInstancesDeleteRequest object.

  Fields:
    name: Required. Name of the resource.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class SecuresourcemanagerProjectsLocationsInstancesGetIamPolicyRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsInstancesGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class SecuresourcemanagerProjectsLocationsInstancesGetRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsInstancesGetRequest object.

  Fields:
    name: Required. Name of the resource.
  """

  name = _messages.StringField(1, required=True)


class SecuresourcemanagerProjectsLocationsInstancesIssueRedirectTicketInternalRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsInstancesIssueRedirectTicketIntern
  alRequest object.

  Fields:
    instance: Required. The instance resource to issue a redirect ticket for.
    issueRedirectTicketInternalRequest: A IssueRedirectTicketInternalRequest
      resource to be passed as the request body.
  """

  instance = _messages.StringField(1, required=True)
  issueRedirectTicketInternalRequest = _messages.MessageField('IssueRedirectTicketInternalRequest', 2)


class SecuresourcemanagerProjectsLocationsInstancesListRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsInstancesListRequest object.

  Fields:
    filter: Filter for filtering results.
    orderBy: Hint for how to order the results.
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListInstancesRequest.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class SecuresourcemanagerProjectsLocationsInstancesSetIamPolicyRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsInstancesSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class SecuresourcemanagerProjectsLocationsInstancesTestIamPermissionsRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsInstancesTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class SecuresourcemanagerProjectsLocationsListRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class SecuresourcemanagerProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class SecuresourcemanagerProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class SecuresourcemanagerProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class SecuresourcemanagerProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class SecuresourcemanagerProjectsLocationsRepositoriesBranchRulesCreateRequest(_messages.Message):
  r"""A
  SecuresourcemanagerProjectsLocationsRepositoriesBranchRulesCreateRequest
  object.

  Fields:
    branchRule: A BranchRule resource to be passed as the request body.
    branchRuleId: A string attribute.
    parent: A string attribute.
  """

  branchRule = _messages.MessageField('BranchRule', 1)
  branchRuleId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesBranchRulesDeleteRequest(_messages.Message):
  r"""A
  SecuresourcemanagerProjectsLocationsRepositoriesBranchRulesDeleteRequest
  object.

  Fields:
    allowMissing: Optional. If set to true, and the branch rule is not found,
      the request will succeed but no action will be taken on the server.
    name: A string attribute.
  """

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesBranchRulesGetRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesBranchRulesGetRequest
  object.

  Fields:
    name: Required. Name of the repository to retrieve. The format is `project
      s/{project}/locations/{location}/repositories/{repository}/branchRules/{
      branch_rule}`.
  """

  name = _messages.StringField(1, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesBranchRulesListRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesBranchRulesListRequest
  object.

  Fields:
    pageSize: A integer attribute.
    pageToken: A string attribute.
    parent: A string attribute.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesBranchRulesPatchRequest(_messages.Message):
  r"""A
  SecuresourcemanagerProjectsLocationsRepositoriesBranchRulesPatchRequest
  object.

  Fields:
    branchRule: A BranchRule resource to be passed as the request body.
    name: Optional. A unique identifier for a BranchRule. The name should be
      of the format: `projects/{project}/locations/{location}/repositories/{re
      pository}/branchRules/{branch_rule}`
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the branchRule resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. The special
      value "*" means full replacement.
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not actually post it. (https://google.aip.dev/163, for
      declarative friendly)
  """

  branchRule = _messages.MessageField('BranchRule', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SecuresourcemanagerProjectsLocationsRepositoriesCreateRepositoryInternalRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesCreateRepositoryIntern
  alRequest object.

  Fields:
    parent: Required. The project in which to create the repository. Values
      are of the form `projects/{project_number}/locations/{location_id}`
    repository: A Repository resource to be passed as the request body.
    repositoryId: Required. The ID to use for the repository, which will
      become the final component of the repository's resource name. This value
      should be 4-63 characters, and valid characters are /a-z-_/.
  """

  parent = _messages.StringField(1, required=True)
  repository = _messages.MessageField('Repository', 2)
  repositoryId = _messages.StringField(3)


class SecuresourcemanagerProjectsLocationsRepositoriesCreateRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesCreateRequest object.

  Fields:
    parent: Required. The project in which to create the repository. Values
      are of the form `projects/{project_number}/locations/{location_id}`
    repository: A Repository resource to be passed as the request body.
    repositoryId: Required. The ID to use for the repository, which will
      become the final component of the repository's resource name. This value
      should be 4-63 characters, and valid characters are /a-z-/.
  """

  parent = _messages.StringField(1, required=True)
  repository = _messages.MessageField('Repository', 2)
  repositoryId = _messages.StringField(3)


class SecuresourcemanagerProjectsLocationsRepositoriesDeleteRepositoryInternalRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesDeleteRepositoryIntern
  alRequest object.

  Fields:
    name: Required. Name of the resource.
  """

  name = _messages.StringField(1, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesDeleteRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesDeleteRequest object.

  Fields:
    allowMissing: Optional. If set to true, and the repository is not found,
      the request will succeed but no action will be taken on the server.
    name: Required. Name of the repository to delete. The format is `projects/
      {project_number}/locations/{location_id}/repositories/{repository_id}`.
  """

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesFetchBlobRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesFetchBlobRequest
  object.

  Fields:
    repository: Required. The format is `projects/{project_number}/locations/{
      location_id}/repositories/{repository_id}`. Specifies the repository
      containing the blob.
    sha: Required. The SHA-1 hash of the blob to retrieve.
  """

  repository = _messages.StringField(1, required=True)
  sha = _messages.StringField(2)


class SecuresourcemanagerProjectsLocationsRepositoriesFetchTreeRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesFetchTreeRequest
  object.

  Fields:
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, at most 10,000 items will be returned.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    recursive: Optional. If true, include all subfolders and their files in
      the response. If false, only the immediate children are returned.
    ref: Optional. `ref` can be a SHA-1 hash, a branch name, or a tag.
      Specifies which tree to fetch. If not specified, the default branch will
      be used.
    repository: Required. The format is `projects/{project_number}/locations/{
      location_id}/repositories/{repository_id}`. Specifies the repository to
      fetch the tree from.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  recursive = _messages.BooleanField(3)
  ref = _messages.StringField(4)
  repository = _messages.StringField(5, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesGetIamPolicyRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesGetRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesGetRequest object.

  Fields:
    name: Required. Name of the repository to retrieve. The format is `project
      s/{project_number}/locations/{location_id}/repositories/{repository_id}`
      .
  """

  name = _messages.StringField(1, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesHooksCreateRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesHooksCreateRequest
  object.

  Fields:
    hook: A Hook resource to be passed as the request body.
    hookId: Required. The ID to use for the hook, which will become the final
      component of the hook's resource name. This value restricts to lower-
      case letters, numbers, and hyphen, with the first character a letter,
      the last a letter or a number, and a 63 character maximum.
    parent: Required. The repository in which to create the hook. Values are
      of the form `projects/{project_number}/locations/{location_id}/repositor
      ies/{repository_id}`
  """

  hook = _messages.MessageField('Hook', 1)
  hookId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesHooksDeleteRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesHooksDeleteRequest
  object.

  Fields:
    name: Required. Name of the hook to delete. The format is `projects/{proje
      ct_number}/locations/{location_id}/repositories/{repository_id}/hooks/{h
      ook_id}`.
  """

  name = _messages.StringField(1, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesHooksGetRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesHooksGetRequest
  object.

  Fields:
    name: Required. Name of the hook to retrieve. The format is `projects/{pro
      ject_number}/locations/{location_id}/repositories/{repository_id}/hooks/
      {hook_id}`.
  """

  name = _messages.StringField(1, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesHooksListRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesHooksListRequest
  object.

  Fields:
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListHooksRequest.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesHooksPatchRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesHooksPatchRequest
  object.

  Fields:
    hook: A Hook resource to be passed as the request body.
    name: Identifier. A unique identifier for a Hook. The name should be of
      the format: `projects/{project}/locations/{location_id}/repositories/{re
      pository_id}/hooks/{hook_id}`
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the hook resource by the update. The fields specified in
      the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. The special value "*"
      means full replacement.
  """

  hook = _messages.MessageField('Hook', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuresourcemanagerProjectsLocationsRepositoriesIssuesCloseRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesIssuesCloseRequest
  object.

  Fields:
    closeIssueRequest: A CloseIssueRequest resource to be passed as the
      request body.
    name: Required. Name of the issue to close. The format is `projects/{proje
      ct_number}/locations/{location_id}/repositories/{repository_id}/issues/{
      issue_id}`.
  """

  closeIssueRequest = _messages.MessageField('CloseIssueRequest', 1)
  name = _messages.StringField(2, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesIssuesCreateRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesIssuesCreateRequest
  object.

  Fields:
    issue: A Issue resource to be passed as the request body.
    parent: Required. The repository in which to create the issue. Format: `pr
      ojects/{project_number}/locations/{location_id}/repositories/{repository
      _id}`
  """

  issue = _messages.MessageField('Issue', 1)
  parent = _messages.StringField(2, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesIssuesDeleteRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesIssuesDeleteRequest
  object.

  Fields:
    etag: Optional. The current etag of the issue. If the etag is provided and
      does not match the current etag of the issue, deletion will be blocked
      and an ABORTED error will be returned.
    name: Required. Name of the issue to delete. The format is `projects/{proj
      ect_number}/locations/{location_id}/repositories/{repository_id}/issues/
      {issue_id}`.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesIssuesGetRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesIssuesGetRequest
  object.

  Fields:
    name: Required. Name of the issue to retrieve. The format is `projects/{pr
      oject}/locations/{location}/repositories/{repository}/issues/{issue_id}`
      .
  """

  name = _messages.StringField(1, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesIssuesIssueCommentsCreateRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesIssuesIssueCommentsCre
  ateRequest object.

  Fields:
    issueComment: A IssueComment resource to be passed as the request body.
    parent: Required. The issue in which to create the issue comment. Format:
      `projects/{project_number}/locations/{location_id}/repositories/{reposit
      ory_id}/issues/{issue_id}`
  """

  issueComment = _messages.MessageField('IssueComment', 1)
  parent = _messages.StringField(2, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesIssuesIssueCommentsDeleteRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesIssuesIssueCommentsDel
  eteRequest object.

  Fields:
    name: Required. Name of the issue comment to delete. The format is `projec
      ts/{project_number}/locations/{location_id}/repositories/{repository_id}
      /issues/{issue_id}/issueComments/{comment_id}`.
  """

  name = _messages.StringField(1, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesIssuesIssueCommentsGetRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesIssuesIssueCommentsGet
  Request object.

  Fields:
    name: Required. Name of the issue comment to retrieve. The format is `proj
      ects/{project}/locations/{location}/repositories/{repository}/issues/{is
      sue_id}/issueComments/{comment_id}`.
  """

  name = _messages.StringField(1, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesIssuesIssueCommentsListRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesIssuesIssueCommentsLis
  tRequest object.

  Fields:
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The issue in which to list the comments. Format: `projec
      ts/{project_number}/locations/{location_id}/repositories/{repository_id}
      /issues/{issue_id}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesIssuesIssueCommentsPatchRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesIssuesIssueCommentsPat
  chRequest object.

  Fields:
    issueComment: A IssueComment resource to be passed as the request body.
    name: Identifier. Unique identifier for an issue comment. The comment id
      is generated by the server. Format: `projects/{project}/locations/{locat
      ion}/repositories/{repository}/issues/{issue}/issueComments/{comment_id}
      `
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the issue comment resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. The special
      value "*" means full replacement.
  """

  issueComment = _messages.MessageField('IssueComment', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuresourcemanagerProjectsLocationsRepositoriesIssuesListRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesIssuesListRequest
  object.

  Fields:
    filter: Optional. Used to filter the resulting issues list.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The repository in which to list issues. Format: `project
      s/{project_number}/locations/{location_id}/repositories/{repository_id}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesIssuesOpenRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesIssuesOpenRequest
  object.

  Fields:
    name: Required. Name of the issue to open. The format is `projects/{projec
      t_number}/locations/{location_id}/repositories/{repository_id}/issues/{i
      ssue_id}`.
    openIssueRequest: A OpenIssueRequest resource to be passed as the request
      body.
  """

  name = _messages.StringField(1, required=True)
  openIssueRequest = _messages.MessageField('OpenIssueRequest', 2)


class SecuresourcemanagerProjectsLocationsRepositoriesIssuesPatchRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesIssuesPatchRequest
  object.

  Fields:
    issue: A Issue resource to be passed as the request body.
    name: Identifier. Unique identifier for an issue. The issue id is
      generated by the server. Format: `projects/{project}/locations/{location
      }/repositories/{repository}/issues/{issue_id}`
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the issue resource by the update. The fields specified in
      the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. The special value "*"
      means full replacement.
  """

  issue = _messages.MessageField('Issue', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuresourcemanagerProjectsLocationsRepositoriesListRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesListRequest object.

  Fields:
    filter: Optional. Filter results.
    instance: Optional. The name of the instance in which the repository is
      hosted, formatted as `projects/{project_number}/locations/{location_id}/
      instances/{instance_id}`. When listing repositories via
      securesourcemanager.googleapis.com, this field is required. When listing
      repositories via *.sourcemanager.dev, this field is ignored.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListRepositoriesRequest.
  """

  filter = _messages.StringField(1)
  instance = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesPatchRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesPatchRequest object.

  Fields:
    name: Optional. A unique identifier for a repository. The name should be
      of the format: `projects/{project}/locations/{location_id}/repositories/
      {repository_id}`
    repository: A Repository resource to be passed as the request body.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the repository resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
    validateOnly: Optional. False by default. If set to true, the request is
      validated and the user is provided with an expected result, but no
      actual change is made.
  """

  name = _messages.StringField(1, required=True)
  repository = _messages.MessageField('Repository', 2)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsCloseRequest(_messages.Message):
  r"""A
  SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsCloseRequest
  object.

  Fields:
    closePullRequestRequest: A ClosePullRequestRequest resource to be passed
      as the request body.
    name: Required. The pull request to close. Format: `projects/{project_numb
      er}/locations/{location_id}/repositories/{repository_id}/pullRequests/{p
      ull_request_id}`
  """

  closePullRequestRequest = _messages.MessageField('ClosePullRequestRequest', 1)
  name = _messages.StringField(2, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsCreateRequest(_messages.Message):
  r"""A
  SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsCreateRequest
  object.

  Fields:
    parent: Required. The repository that the pull request is created from.
      Format: `projects/{project_number}/locations/{location_id}/repositories/
      {repository_id}`
    pullRequest: A PullRequest resource to be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  pullRequest = _messages.MessageField('PullRequest', 2)


class SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsGetRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsGetRequest
  object.

  Fields:
    name: Required. Name of the pull request to retrieve. The format is `proje
      cts/{project}/locations/{location}/repositories/{repository}/pullRequest
      s/{pull_request}`.
  """

  name = _messages.StringField(1, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsListFileDiffsRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsListFileDi
  ffsRequest object.

  Fields:
    name: Required. The pull request to list file diffs for. Format: `projects
      /{project_number}/locations/{location_id}/repositories/{repository_id}/p
      ullRequests/{pull_request_id}`
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
  """

  name = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsListRequest(_messages.Message):
  r"""A
  SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsListRequest
  object.

  Fields:
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The repository in which to list pull requests. Format: `
      projects/{project_number}/locations/{location_id}/repositories/{reposito
      ry_id}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsMergeRequest(_messages.Message):
  r"""A
  SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsMergeRequest
  object.

  Fields:
    mergePullRequestRequest: A MergePullRequestRequest resource to be passed
      as the request body.
    name: Required. The pull request to merge. Format: `projects/{project_numb
      er}/locations/{location_id}/repositories/{repository_id}/pullRequests/{p
      ull_request_id}`
  """

  mergePullRequestRequest = _messages.MessageField('MergePullRequestRequest', 1)
  name = _messages.StringField(2, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsOpenRequest(_messages.Message):
  r"""A
  SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsOpenRequest
  object.

  Fields:
    name: Required. The pull request to open. Format: `projects/{project_numbe
      r}/locations/{location_id}/repositories/{repository_id}/pullRequests/{pu
      ll_request_id}`
    openPullRequestRequest: A OpenPullRequestRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  openPullRequestRequest = _messages.MessageField('OpenPullRequestRequest', 2)


class SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPatchRequest(_messages.Message):
  r"""A
  SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPatchRequest
  object.

  Fields:
    name: Output only. A unique identifier for a PullRequest. The number
      appended at the end is generated by the server. Format: `projects/{proje
      ct}/locations/{location}/repositories/{repository}/pullRequests/{pull_re
      quest_id}`
    pullRequest: A PullRequest resource to be passed as the request body.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the pull request resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. The special
      value "*" means full replacement.
  """

  name = _messages.StringField(1, required=True)
  pullRequest = _messages.MessageField('PullRequest', 2)
  updateMask = _messages.StringField(3)


class SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullRequestCommentsBatchCreateRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullReques
  tCommentsBatchCreateRequest object.

  Fields:
    batchCreatePullRequestCommentsRequest: A
      BatchCreatePullRequestCommentsRequest resource to be passed as the
      request body.
    parent: Required. The pull request in which to create the pull request
      comments. Format: `projects/{project_number}/locations/{location_id}/rep
      ositories/{repository_id}/pullRequests/{pull_request_id}`
  """

  batchCreatePullRequestCommentsRequest = _messages.MessageField('BatchCreatePullRequestCommentsRequest', 1)
  parent = _messages.StringField(2, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullRequestCommentsCreateRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullReques
  tCommentsCreateRequest object.

  Fields:
    parent: Required. The pull request in which to create the pull request
      comment. Format: `projects/{project_number}/locations/{location_id}/repo
      sitories/{repository_id}/pullRequests/{pull_request_id}`
    pullRequestComment: A PullRequestComment resource to be passed as the
      request body.
  """

  parent = _messages.StringField(1, required=True)
  pullRequestComment = _messages.MessageField('PullRequestComment', 2)


class SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullRequestCommentsDeleteRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullReques
  tCommentsDeleteRequest object.

  Fields:
    name: Required. Name of the pull request comment to delete. The format is
      `projects/{project_number}/locations/{location_id}/repositories/{reposit
      ory_id}/pullRequests/{pull_request_id}/pullRequestComments/{comment_id}`
      .
  """

  name = _messages.StringField(1, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullRequestCommentsGetRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullReques
  tCommentsGetRequest object.

  Fields:
    name: Required. Name of the pull request comment to retrieve. The format
      is `projects/{project_number}/locations/{location_id}/repositories/{repo
      sitory_id}/pullRequests/{pull_request_id}/pullRequestComments/{comment_i
      d}`.
  """

  name = _messages.StringField(1, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullRequestCommentsListRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullReques
  tCommentsListRequest object.

  Fields:
    pageSize: Optional. Requested page size. If unspecified, at most 100 pull
      request comments will be returned. The maximum value is 100; values
      above 100 will be coerced to 100.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The pull request in which to list pull request comments.
      Format: `projects/{project_number}/locations/{location_id}/repositories/
      {repository_id}/pullRequests/{pull_request_id}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullRequestCommentsPatchRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullReques
  tCommentsPatchRequest object.

  Fields:
    name: Identifier. Unique identifier for the pull request comment. The
      comment id is generated by the server. Format: `projects/{project}/locat
      ions/{location}/repositories/{repository}/pullRequests/{pull_request}/pu
      llRequestComments/{comment_id}`
    pullRequestComment: A PullRequestComment resource to be passed as the
      request body.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the pull request comment resource by the update.
      Updatable fields are `body`.
  """

  name = _messages.StringField(1, required=True)
  pullRequestComment = _messages.MessageField('PullRequestComment', 2)
  updateMask = _messages.StringField(3)


class SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullRequestCommentsResolveRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullReques
  tCommentsResolveRequest object.

  Fields:
    parent: Required. The pull request in which to resolve the pull request
      comments. Format: `projects/{project_number}/locations/{location_id}/rep
      ositories/{repository_id}/pullRequests/{pull_request_id}`
    resolvePullRequestCommentsRequest: A ResolvePullRequestCommentsRequest
      resource to be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  resolvePullRequestCommentsRequest = _messages.MessageField('ResolvePullRequestCommentsRequest', 2)


class SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullRequestCommentsUnresolveRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullReques
  tCommentsUnresolveRequest object.

  Fields:
    parent: Required. The pull request in which to resolve the pull request
      comments. Format: `projects/{project_number}/locations/{location_id}/rep
      ositories/{repository_id}/pullRequests/{pull_request_id}`
    unresolvePullRequestCommentsRequest: A UnresolvePullRequestCommentsRequest
      resource to be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  unresolvePullRequestCommentsRequest = _messages.MessageField('UnresolvePullRequestCommentsRequest', 2)


class SecuresourcemanagerProjectsLocationsRepositoriesSetIamPolicyRequest(_messages.Message):
  r"""A SecuresourcemanagerProjectsLocationsRepositoriesSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class SecuresourcemanagerProjectsLocationsRepositoriesTestIamPermissionsRequest(_messages.Message):
  r"""A
  SecuresourcemanagerProjectsLocationsRepositoriesTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TreeEntry(_messages.Message):
  r"""Represents an entry within a tree structure (like a Git tree).

  Enums:
    TypeValueValuesEnum: Output only. The type of the object (TREE, BLOB,
      COMMIT). Output-only.

  Fields:
    mode: Output only. The file mode as a string (e.g., "100644"). Indicates
      file type. Output-only.
    path: Output only. The path of the file or directory within the tree
      (e.g., "src/main/java/MyClass.java"). Output-only.
    sha: Output only. The SHA-1 hash of the object (unique identifier).
      Output-only.
    size: Output only. The size of the object in bytes (only for blobs).
      Output-only.
    type: Output only. The type of the object (TREE, BLOB, COMMIT). Output-
      only.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the object (TREE, BLOB, COMMIT). Output-only.

    Values:
      OBJECT_TYPE_UNSPECIFIED: Default value, indicating the object type is
        unspecified.
      TREE: Represents a directory (folder).
      BLOB: Represents a file (contains file data).
      COMMIT: Represents a pointer to another repository (submodule).
    """
    OBJECT_TYPE_UNSPECIFIED = 0
    TREE = 1
    BLOB = 2
    COMMIT = 3

  mode = _messages.StringField(1)
  path = _messages.StringField(2)
  sha = _messages.StringField(3)
  size = _messages.IntegerField(4)
  type = _messages.EnumField('TypeValueValuesEnum', 5)


class URIs(_messages.Message):
  r"""URIs for the repository.

  Fields:
    api: Output only. API is the URI for API access.
    gitHttps: Output only. git_https is the git HTTPS URI for git operations.
    html: Output only. HTML is the URI for user to view the repository in a
      browser.
  """

  api = _messages.StringField(1)
  gitHttps = _messages.StringField(2)
  html = _messages.StringField(3)


class UnresolvePullRequestCommentsRequest(_messages.Message):
  r"""The request to unresolve multiple pull request comments.

  Fields:
    autoFill: Optional. If set, at least one comment in a thread is required,
      rest of the comments in the same thread will be automatically updated to
      unresolved. If unset, all comments in the same thread need be present.
    names: Required. The names of the pull request comments to unresolve.
      Format: `projects/{project_number}/locations/{location_id}/repositories/
      {repository_id}/pullRequests/{pull_request_id}/pullRequestComments/{comm
      ent_id}` Only comments from the same threads are allowed in the same
      request.
  """

  autoFill = _messages.BooleanField(1)
  names = _messages.StringField(2, repeated=True)


class WorkforceIdentityFederationConfig(_messages.Message):
  r"""WorkforceIdentityFederationConfig allows this instance to support users
  from external identity providers.

  Fields:
    enabled: Optional. Immutable. Whether Workforce Identity Federation is
      enabled.
  """

  enabled = _messages.BooleanField(1)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    SecuresourcemanagerProjectsLocationsInstancesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    SecuresourcemanagerProjectsLocationsRepositoriesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
