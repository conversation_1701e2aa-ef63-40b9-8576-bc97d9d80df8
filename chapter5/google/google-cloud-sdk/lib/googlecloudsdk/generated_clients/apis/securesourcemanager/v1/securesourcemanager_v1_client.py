"""Generated client library for securesourcemanager version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.securesourcemanager.v1 import securesourcemanager_v1_messages as messages


class SecuresourcemanagerV1(base_api.BaseApiClient):
  """Generated client library for service securesourcemanager version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://securesourcemanager.googleapis.com/'
  MTLS_BASE_URL = 'https://securesourcemanager.mtls.googleapis.com/'

  _PACKAGE = 'securesourcemanager'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'SecuresourcemanagerV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new securesourcemanager handle."""
    url = url or self.BASE_URL
    super(SecuresourcemanagerV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_instances = self.ProjectsLocationsInstancesService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_repositories_branchRules = self.ProjectsLocationsRepositoriesBranchRulesService(self)
    self.projects_locations_repositories_hooks = self.ProjectsLocationsRepositoriesHooksService(self)
    self.projects_locations_repositories_issues_issueComments = self.ProjectsLocationsRepositoriesIssuesIssueCommentsService(self)
    self.projects_locations_repositories_issues = self.ProjectsLocationsRepositoriesIssuesService(self)
    self.projects_locations_repositories_pullRequests_pullRequestComments = self.ProjectsLocationsRepositoriesPullRequestsPullRequestCommentsService(self)
    self.projects_locations_repositories_pullRequests = self.ProjectsLocationsRepositoriesPullRequestsService(self)
    self.projects_locations_repositories = self.ProjectsLocationsRepositoriesService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsInstancesService(base_api.BaseApiService):
    """Service class for the projects_locations_instances resource."""

    _NAME = 'projects_locations_instances'

    def __init__(self, client):
      super(SecuresourcemanagerV1.ProjectsLocationsInstancesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new instance in a given project and location.

      Args:
        request: (SecuresourcemanagerProjectsLocationsInstancesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances',
        http_method='POST',
        method_id='securesourcemanager.projects.locations.instances.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['instanceId', 'requestId'],
        relative_path='v1/{+parent}/instances',
        request_field='instance',
        request_type_name='SecuresourcemanagerProjectsLocationsInstancesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single instance.

      Args:
        request: (SecuresourcemanagerProjectsLocationsInstancesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}',
        http_method='DELETE',
        method_id='securesourcemanager.projects.locations.instances.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsInstancesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single instance.

      Args:
        request: (SecuresourcemanagerProjectsLocationsInstancesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Instance) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}',
        http_method='GET',
        method_id='securesourcemanager.projects.locations.instances.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsInstancesGetRequest',
        response_type_name='Instance',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (SecuresourcemanagerProjectsLocationsInstancesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:getIamPolicy',
        http_method='GET',
        method_id='securesourcemanager.projects.locations.instances.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsInstancesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def IssueRedirectTicketInternal(self, request, global_params=None):
      r"""THIS METHOD IS FOR INTERNAL USE ONLY.

      Args:
        request: (SecuresourcemanagerProjectsLocationsInstancesIssueRedirectTicketInternalRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (IssueRedirectTicketInternalResponse) The response message.
      """
      config = self.GetMethodConfig('IssueRedirectTicketInternal')
      return self._RunMethod(
          config, request, global_params=global_params)

    IssueRedirectTicketInternal.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:issueRedirectTicketInternal',
        http_method='POST',
        method_id='securesourcemanager.projects.locations.instances.issueRedirectTicketInternal',
        ordered_params=['instance'],
        path_params=['instance'],
        query_params=[],
        relative_path='v1/{+instance}:issueRedirectTicketInternal',
        request_field='issueRedirectTicketInternalRequest',
        request_type_name='SecuresourcemanagerProjectsLocationsInstancesIssueRedirectTicketInternalRequest',
        response_type_name='IssueRedirectTicketInternalResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Instances in a given project and location.

      Args:
        request: (SecuresourcemanagerProjectsLocationsInstancesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListInstancesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances',
        http_method='GET',
        method_id='securesourcemanager.projects.locations.instances.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/instances',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsInstancesListRequest',
        response_type_name='ListInstancesResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (SecuresourcemanagerProjectsLocationsInstancesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:setIamPolicy',
        http_method='POST',
        method_id='securesourcemanager.projects.locations.instances.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='SecuresourcemanagerProjectsLocationsInstancesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (SecuresourcemanagerProjectsLocationsInstancesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:testIamPermissions',
        http_method='POST',
        method_id='securesourcemanager.projects.locations.instances.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='SecuresourcemanagerProjectsLocationsInstancesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(SecuresourcemanagerV1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (SecuresourcemanagerProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='securesourcemanager.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='SecuresourcemanagerProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (SecuresourcemanagerProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='securesourcemanager.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (SecuresourcemanagerProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='securesourcemanager.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (SecuresourcemanagerProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='securesourcemanager.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsRepositoriesBranchRulesService(base_api.BaseApiService):
    """Service class for the projects_locations_repositories_branchRules resource."""

    _NAME = 'projects_locations_repositories_branchRules'

    def __init__(self, client):
      super(SecuresourcemanagerV1.ProjectsLocationsRepositoriesBranchRulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""CreateBranchRule creates a branch rule in a given repository.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesBranchRulesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/branchRules',
        http_method='POST',
        method_id='securesourcemanager.projects.locations.repositories.branchRules.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['branchRuleId'],
        relative_path='v1/{+parent}/branchRules',
        request_field='branchRule',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesBranchRulesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""DeleteBranchRule deletes a branch rule.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesBranchRulesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/branchRules/{branchRulesId}',
        http_method='DELETE',
        method_id='securesourcemanager.projects.locations.repositories.branchRules.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesBranchRulesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""GetBranchRule gets a branch rule.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesBranchRulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BranchRule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/branchRules/{branchRulesId}',
        http_method='GET',
        method_id='securesourcemanager.projects.locations.repositories.branchRules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesBranchRulesGetRequest',
        response_type_name='BranchRule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""ListBranchRules lists branch rules in a given repository.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesBranchRulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBranchRulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/branchRules',
        http_method='GET',
        method_id='securesourcemanager.projects.locations.repositories.branchRules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/branchRules',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesBranchRulesListRequest',
        response_type_name='ListBranchRulesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""UpdateBranchRule updates a branch rule.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesBranchRulesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/branchRules/{branchRulesId}',
        http_method='PATCH',
        method_id='securesourcemanager.projects.locations.repositories.branchRules.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='branchRule',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesBranchRulesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsRepositoriesHooksService(base_api.BaseApiService):
    """Service class for the projects_locations_repositories_hooks resource."""

    _NAME = 'projects_locations_repositories_hooks'

    def __init__(self, client):
      super(SecuresourcemanagerV1.ProjectsLocationsRepositoriesHooksService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new hook in a given repository.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesHooksCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/hooks',
        http_method='POST',
        method_id='securesourcemanager.projects.locations.repositories.hooks.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['hookId'],
        relative_path='v1/{+parent}/hooks',
        request_field='hook',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesHooksCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Hook.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesHooksDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/hooks/{hooksId}',
        http_method='DELETE',
        method_id='securesourcemanager.projects.locations.repositories.hooks.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesHooksDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets metadata of a hook.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesHooksGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Hook) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/hooks/{hooksId}',
        http_method='GET',
        method_id='securesourcemanager.projects.locations.repositories.hooks.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesHooksGetRequest',
        response_type_name='Hook',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists hooks in a given repository.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesHooksListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListHooksResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/hooks',
        http_method='GET',
        method_id='securesourcemanager.projects.locations.repositories.hooks.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/hooks',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesHooksListRequest',
        response_type_name='ListHooksResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the metadata of a hook.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesHooksPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/hooks/{hooksId}',
        http_method='PATCH',
        method_id='securesourcemanager.projects.locations.repositories.hooks.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='hook',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesHooksPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsRepositoriesIssuesIssueCommentsService(base_api.BaseApiService):
    """Service class for the projects_locations_repositories_issues_issueComments resource."""

    _NAME = 'projects_locations_repositories_issues_issueComments'

    def __init__(self, client):
      super(SecuresourcemanagerV1.ProjectsLocationsRepositoriesIssuesIssueCommentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an issue comment.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesIssuesIssueCommentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/issues/{issuesId}/issueComments',
        http_method='POST',
        method_id='securesourcemanager.projects.locations.repositories.issues.issueComments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/issueComments',
        request_field='issueComment',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesIssuesIssueCommentsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an issue comment.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesIssuesIssueCommentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/issues/{issuesId}/issueComments/{issueCommentsId}',
        http_method='DELETE',
        method_id='securesourcemanager.projects.locations.repositories.issues.issueComments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesIssuesIssueCommentsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an issue comment.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesIssuesIssueCommentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (IssueComment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/issues/{issuesId}/issueComments/{issueCommentsId}',
        http_method='GET',
        method_id='securesourcemanager.projects.locations.repositories.issues.issueComments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesIssuesIssueCommentsGetRequest',
        response_type_name='IssueComment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists comments in an issue.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesIssuesIssueCommentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListIssueCommentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/issues/{issuesId}/issueComments',
        http_method='GET',
        method_id='securesourcemanager.projects.locations.repositories.issues.issueComments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/issueComments',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesIssuesIssueCommentsListRequest',
        response_type_name='ListIssueCommentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an issue comment.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesIssuesIssueCommentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/issues/{issuesId}/issueComments/{issueCommentsId}',
        http_method='PATCH',
        method_id='securesourcemanager.projects.locations.repositories.issues.issueComments.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='issueComment',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesIssuesIssueCommentsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsRepositoriesIssuesService(base_api.BaseApiService):
    """Service class for the projects_locations_repositories_issues resource."""

    _NAME = 'projects_locations_repositories_issues'

    def __init__(self, client):
      super(SecuresourcemanagerV1.ProjectsLocationsRepositoriesIssuesService, self).__init__(client)
      self._upload_configs = {
          }

    def Close(self, request, global_params=None):
      r"""Closes an issue.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesIssuesCloseRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Close')
      return self._RunMethod(
          config, request, global_params=global_params)

    Close.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/issues/{issuesId}:close',
        http_method='POST',
        method_id='securesourcemanager.projects.locations.repositories.issues.close',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:close',
        request_field='closeIssueRequest',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesIssuesCloseRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates an issue.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesIssuesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/issues',
        http_method='POST',
        method_id='securesourcemanager.projects.locations.repositories.issues.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/issues',
        request_field='issue',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesIssuesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an issue.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesIssuesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/issues/{issuesId}',
        http_method='DELETE',
        method_id='securesourcemanager.projects.locations.repositories.issues.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesIssuesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an issue.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesIssuesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Issue) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/issues/{issuesId}',
        http_method='GET',
        method_id='securesourcemanager.projects.locations.repositories.issues.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesIssuesGetRequest',
        response_type_name='Issue',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists issues in a repository.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesIssuesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListIssuesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/issues',
        http_method='GET',
        method_id='securesourcemanager.projects.locations.repositories.issues.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/issues',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesIssuesListRequest',
        response_type_name='ListIssuesResponse',
        supports_download=False,
    )

    def Open(self, request, global_params=None):
      r"""Opens an issue.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesIssuesOpenRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Open')
      return self._RunMethod(
          config, request, global_params=global_params)

    Open.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/issues/{issuesId}:open',
        http_method='POST',
        method_id='securesourcemanager.projects.locations.repositories.issues.open',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:open',
        request_field='openIssueRequest',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesIssuesOpenRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a issue.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesIssuesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/issues/{issuesId}',
        http_method='PATCH',
        method_id='securesourcemanager.projects.locations.repositories.issues.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='issue',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesIssuesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsRepositoriesPullRequestsPullRequestCommentsService(base_api.BaseApiService):
    """Service class for the projects_locations_repositories_pullRequests_pullRequestComments resource."""

    _NAME = 'projects_locations_repositories_pullRequests_pullRequestComments'

    def __init__(self, client):
      super(SecuresourcemanagerV1.ProjectsLocationsRepositoriesPullRequestsPullRequestCommentsService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchCreate(self, request, global_params=None):
      r"""Batch creates pull request comments.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullRequestCommentsBatchCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('BatchCreate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchCreate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/pullRequests/{pullRequestsId}/pullRequestComments:batchCreate',
        http_method='POST',
        method_id='securesourcemanager.projects.locations.repositories.pullRequests.pullRequestComments.batchCreate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/pullRequestComments:batchCreate',
        request_field='batchCreatePullRequestCommentsRequest',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullRequestCommentsBatchCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a pull request comment.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullRequestCommentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/pullRequests/{pullRequestsId}/pullRequestComments',
        http_method='POST',
        method_id='securesourcemanager.projects.locations.repositories.pullRequests.pullRequestComments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/pullRequestComments',
        request_field='pullRequestComment',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullRequestCommentsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a pull request comment.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullRequestCommentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/pullRequests/{pullRequestsId}/pullRequestComments/{pullRequestCommentsId}',
        http_method='DELETE',
        method_id='securesourcemanager.projects.locations.repositories.pullRequests.pullRequestComments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullRequestCommentsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a pull request comment.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullRequestCommentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PullRequestComment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/pullRequests/{pullRequestsId}/pullRequestComments/{pullRequestCommentsId}',
        http_method='GET',
        method_id='securesourcemanager.projects.locations.repositories.pullRequests.pullRequestComments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullRequestCommentsGetRequest',
        response_type_name='PullRequestComment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists pull request comments.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullRequestCommentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPullRequestCommentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/pullRequests/{pullRequestsId}/pullRequestComments',
        http_method='GET',
        method_id='securesourcemanager.projects.locations.repositories.pullRequests.pullRequestComments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/pullRequestComments',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullRequestCommentsListRequest',
        response_type_name='ListPullRequestCommentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a pull request comment.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullRequestCommentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/pullRequests/{pullRequestsId}/pullRequestComments/{pullRequestCommentsId}',
        http_method='PATCH',
        method_id='securesourcemanager.projects.locations.repositories.pullRequests.pullRequestComments.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='pullRequestComment',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullRequestCommentsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Resolve(self, request, global_params=None):
      r"""Resolves pull request comments.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullRequestCommentsResolveRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Resolve')
      return self._RunMethod(
          config, request, global_params=global_params)

    Resolve.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/pullRequests/{pullRequestsId}/pullRequestComments:resolve',
        http_method='POST',
        method_id='securesourcemanager.projects.locations.repositories.pullRequests.pullRequestComments.resolve',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/pullRequestComments:resolve',
        request_field='resolvePullRequestCommentsRequest',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullRequestCommentsResolveRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Unresolve(self, request, global_params=None):
      r"""Unresolves pull request comment.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullRequestCommentsUnresolveRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Unresolve')
      return self._RunMethod(
          config, request, global_params=global_params)

    Unresolve.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/pullRequests/{pullRequestsId}/pullRequestComments:unresolve',
        http_method='POST',
        method_id='securesourcemanager.projects.locations.repositories.pullRequests.pullRequestComments.unresolve',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/pullRequestComments:unresolve',
        request_field='unresolvePullRequestCommentsRequest',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPullRequestCommentsUnresolveRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsRepositoriesPullRequestsService(base_api.BaseApiService):
    """Service class for the projects_locations_repositories_pullRequests resource."""

    _NAME = 'projects_locations_repositories_pullRequests'

    def __init__(self, client):
      super(SecuresourcemanagerV1.ProjectsLocationsRepositoriesPullRequestsService, self).__init__(client)
      self._upload_configs = {
          }

    def Close(self, request, global_params=None):
      r"""Closes a pull request without merging.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsCloseRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Close')
      return self._RunMethod(
          config, request, global_params=global_params)

    Close.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/pullRequests/{pullRequestsId}:close',
        http_method='POST',
        method_id='securesourcemanager.projects.locations.repositories.pullRequests.close',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:close',
        request_field='closePullRequestRequest',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsCloseRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a pull request.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/pullRequests',
        http_method='POST',
        method_id='securesourcemanager.projects.locations.repositories.pullRequests.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/pullRequests',
        request_field='pullRequest',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a pull request.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PullRequest) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/pullRequests/{pullRequestsId}',
        http_method='GET',
        method_id='securesourcemanager.projects.locations.repositories.pullRequests.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsGetRequest',
        response_type_name='PullRequest',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists pull requests in a repository.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPullRequestsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/pullRequests',
        http_method='GET',
        method_id='securesourcemanager.projects.locations.repositories.pullRequests.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/pullRequests',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsListRequest',
        response_type_name='ListPullRequestsResponse',
        supports_download=False,
    )

    def ListFileDiffs(self, request, global_params=None):
      r"""Lists a pull request's file diffs.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsListFileDiffsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPullRequestFileDiffsResponse) The response message.
      """
      config = self.GetMethodConfig('ListFileDiffs')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListFileDiffs.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/pullRequests/{pullRequestsId}:listFileDiffs',
        http_method='GET',
        method_id='securesourcemanager.projects.locations.repositories.pullRequests.listFileDiffs',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+name}:listFileDiffs',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsListFileDiffsRequest',
        response_type_name='ListPullRequestFileDiffsResponse',
        supports_download=False,
    )

    def Merge(self, request, global_params=None):
      r"""Merges a pull request.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsMergeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Merge')
      return self._RunMethod(
          config, request, global_params=global_params)

    Merge.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/pullRequests/{pullRequestsId}:merge',
        http_method='POST',
        method_id='securesourcemanager.projects.locations.repositories.pullRequests.merge',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:merge',
        request_field='mergePullRequestRequest',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsMergeRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Open(self, request, global_params=None):
      r"""Opens a pull request.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsOpenRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Open')
      return self._RunMethod(
          config, request, global_params=global_params)

    Open.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/pullRequests/{pullRequestsId}:open',
        http_method='POST',
        method_id='securesourcemanager.projects.locations.repositories.pullRequests.open',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:open',
        request_field='openPullRequestRequest',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsOpenRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a pull request.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}/pullRequests/{pullRequestsId}',
        http_method='PATCH',
        method_id='securesourcemanager.projects.locations.repositories.pullRequests.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='pullRequest',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesPullRequestsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsRepositoriesService(base_api.BaseApiService):
    """Service class for the projects_locations_repositories resource."""

    _NAME = 'projects_locations_repositories'

    def __init__(self, client):
      super(SecuresourcemanagerV1.ProjectsLocationsRepositoriesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new repository in a given project and location. The Repository.Instance field is required in the request body for requests using the securesourcemanager.googleapis.com endpoint.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories',
        http_method='POST',
        method_id='securesourcemanager.projects.locations.repositories.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['repositoryId'],
        relative_path='v1/{+parent}/repositories',
        request_field='repository',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def CreateRepositoryInternal(self, request, global_params=None):
      r"""THIS METHOD IS FOR INTERNAL USE ONLY.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesCreateRepositoryInternalRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Repository) The response message.
      """
      config = self.GetMethodConfig('CreateRepositoryInternal')
      return self._RunMethod(
          config, request, global_params=global_params)

    CreateRepositoryInternal.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories:createRepositoryInternal',
        http_method='POST',
        method_id='securesourcemanager.projects.locations.repositories.createRepositoryInternal',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['repositoryId'],
        relative_path='v1/{+parent}/repositories:createRepositoryInternal',
        request_field='repository',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesCreateRepositoryInternalRequest',
        response_type_name='Repository',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Repository.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}',
        http_method='DELETE',
        method_id='securesourcemanager.projects.locations.repositories.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def DeleteRepositoryInternal(self, request, global_params=None):
      r"""THIS METHOD IS FOR INTERNAL USE ONLY.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesDeleteRepositoryInternalRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('DeleteRepositoryInternal')
      return self._RunMethod(
          config, request, global_params=global_params)

    DeleteRepositoryInternal.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}:deleteRepositoryInternal',
        http_method='DELETE',
        method_id='securesourcemanager.projects.locations.repositories.deleteRepositoryInternal',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:deleteRepositoryInternal',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesDeleteRepositoryInternalRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def FetchBlob(self, request, global_params=None):
      r"""Fetches a blob from a repository.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesFetchBlobRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FetchBlobResponse) The response message.
      """
      config = self.GetMethodConfig('FetchBlob')
      return self._RunMethod(
          config, request, global_params=global_params)

    FetchBlob.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}:fetchBlob',
        http_method='GET',
        method_id='securesourcemanager.projects.locations.repositories.fetchBlob',
        ordered_params=['repository'],
        path_params=['repository'],
        query_params=['sha'],
        relative_path='v1/{+repository}:fetchBlob',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesFetchBlobRequest',
        response_type_name='FetchBlobResponse',
        supports_download=False,
    )

    def FetchTree(self, request, global_params=None):
      r"""Fetches a tree from a repository.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesFetchTreeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FetchTreeResponse) The response message.
      """
      config = self.GetMethodConfig('FetchTree')
      return self._RunMethod(
          config, request, global_params=global_params)

    FetchTree.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}:fetchTree',
        http_method='GET',
        method_id='securesourcemanager.projects.locations.repositories.fetchTree',
        ordered_params=['repository'],
        path_params=['repository'],
        query_params=['pageSize', 'pageToken', 'recursive', 'ref'],
        relative_path='v1/{+repository}:fetchTree',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesFetchTreeRequest',
        response_type_name='FetchTreeResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets metadata of a repository.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Repository) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}',
        http_method='GET',
        method_id='securesourcemanager.projects.locations.repositories.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesGetRequest',
        response_type_name='Repository',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Get IAM policy for a repository.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}:getIamPolicy',
        http_method='GET',
        method_id='securesourcemanager.projects.locations.repositories.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Repositories in a given project and location. The instance field is required in the query parameter for requests using the securesourcemanager.googleapis.com endpoint.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRepositoriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories',
        http_method='GET',
        method_id='securesourcemanager.projects.locations.repositories.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'instance', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/repositories',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesListRequest',
        response_type_name='ListRepositoriesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the metadata of a repository.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}',
        http_method='PATCH',
        method_id='securesourcemanager.projects.locations.repositories.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='repository',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Set IAM policy on a repository.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}:setIamPolicy',
        http_method='POST',
        method_id='securesourcemanager.projects.locations.repositories.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Test IAM permissions on a repository. IAM permission checks are not required on this method.

      Args:
        request: (SecuresourcemanagerProjectsLocationsRepositoriesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/repositories/{repositoriesId}:testIamPermissions',
        http_method='POST',
        method_id='securesourcemanager.projects.locations.repositories.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='SecuresourcemanagerProjectsLocationsRepositoriesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(SecuresourcemanagerV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (SecuresourcemanagerProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='securesourcemanager.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (SecuresourcemanagerProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='securesourcemanager.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/locations',
        request_field='',
        request_type_name='SecuresourcemanagerProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(SecuresourcemanagerV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
