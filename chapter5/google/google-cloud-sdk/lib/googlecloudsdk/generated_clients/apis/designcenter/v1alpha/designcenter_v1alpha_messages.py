"""Generated message classes for designcenter version v1alpha.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'designcenter'


class AlternateDefault(_messages.Message):
  r"""Alternate default for a UI input.

  Enums:
    TypeValueValuesEnum: Output only. Type of alternate default.

  Fields:
    type: Output only. Type of alternate default.
    value: Output only. Value of the alternate default.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of alternate default.

    Values:
      DISPLAY_VARIABLE_ALTERNATE_TYPE_UNSPECIFIED: Default
      DISPLAY_VARIABLE_ALTERNATE_TYPE_SECURITY: A more secure default.
      DISPLAY_VARIABLE_ALTERNATE_TYPE_DC: A default specifically needed for
        Application Design center.
    """
    DISPLAY_VARIABLE_ALTERNATE_TYPE_UNSPECIFIED = 0
    DISPLAY_VARIABLE_ALTERNATE_TYPE_SECURITY = 1
    DISPLAY_VARIABLE_ALTERNATE_TYPE_DC = 2

  type = _messages.EnumField('TypeValueValuesEnum', 1)
  value = _messages.MessageField('extra_types.JsonValue', 2)


class AppHubApplicationParameters(_messages.Message):
  r"""App Hub application parameters.

  Fields:
    applicationId: Optional. The App Hub application ID.
    attributes: Optional. The App Hub application attributes.
    hostProjectId: Optional. The host project ID where the App Hub application
      is created.
    location: Optional. The App Hub application location. Only used for
      applications with REGIONAL scope.
    scope: Optional. The App Hub application scope.
  """

  applicationId = _messages.StringField(1)
  attributes = _messages.MessageField('Attributes', 2)
  hostProjectId = _messages.StringField(3)
  location = _messages.StringField(4)
  scope = _messages.MessageField('Scope', 5)


class Application(_messages.Message):
  r"""Represents the application resource.

  Enums:
    StateValueValuesEnum: Output only. Deployment state of the application.

  Fields:
    appParameters: Optional. A list of parameters to attach to the deployment
      source object, which is a catalog entry or application template
      snapshot.
    apphubApplication: Output only. The App Hub App associated with the
      application.
    attributes: Optional. Attributes of the application.
    componentParameters: Optional. A list of component parameters to associate
      with the application.
    connectionConfigs: Optional. Connection configuration for the application.
    createTime: Output only. Create timestamp.
    deploymentMetadata: Output only. Deployment metadata of the application.
    deploymentProject: Optional. Deployment project of the application.
    deploymentRegion: Optional. The region where the application is deployed.
    deploymentRevision: Output only. [Output only] Optional Infra Manager
      deployment Id with revision
    description: Optional. Description of the application.
    displayName: Optional. Display name of the application.
    importExistingResources: Optional. Import existing resources into the
      application.
    name: Identifier. The name of the application. Format: projects/{project}/
      locations/{location}/spaces/{space}/applications/{application}
    previewReference: Output only. Preview reference for the application.
    projectParameters: Output only. List of project parameters for the
      application.
    scope: Optional. Scope of the application.
    serializedApplicationTemplate: Output only. [Output only] Serialized
      application template.
    serviceAccount: Optional. Your own service account that you use to deploy
      an application.
    source: Optional. The application deployment source.
    state: Output only. Deployment state of the application.
    updateTime: Output only. Update timestamp.
    updatedTemplateRevision: Output only. The updated template revision
      because of which the application is outdated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Deployment state of the application.

    Values:
      STATE_UNSPECIFIED: Unspecified application deployment state.
      DRAFT: Application is in draft.
      CREATING_DEPLOYMENT: New application deployment is in progress.
      UPDATING_DEPLOYMENT: Update application deployment is in progress.
      DELETING: Delete application deployment is in progress.
      DEPLOYED: Application deployment is completed.
      DELETED: Application deployment is deleted.
      FAILED: Application deployment is failed.
      DEPLOYING: Application deployment is in progress.
    """
    STATE_UNSPECIFIED = 0
    DRAFT = 1
    CREATING_DEPLOYMENT = 2
    UPDATING_DEPLOYMENT = 3
    DELETING = 4
    DEPLOYED = 5
    DELETED = 6
    FAILED = 7
    DEPLOYING = 8

  appParameters = _messages.MessageField('Parameter', 1, repeated=True)
  apphubApplication = _messages.StringField(2)
  attributes = _messages.MessageField('Attributes', 3)
  componentParameters = _messages.MessageField('ComponentParameters', 4, repeated=True)
  connectionConfigs = _messages.MessageField('ConnectionConfig', 5, repeated=True)
  createTime = _messages.StringField(6)
  deploymentMetadata = _messages.MessageField('DeploymentMetadata', 7)
  deploymentProject = _messages.StringField(8)
  deploymentRegion = _messages.StringField(9)
  deploymentRevision = _messages.StringField(10)
  description = _messages.StringField(11)
  displayName = _messages.StringField(12)
  importExistingResources = _messages.BooleanField(13)
  name = _messages.StringField(14)
  previewReference = _messages.StringField(15)
  projectParameters = _messages.MessageField('ProjectParameters', 16, repeated=True)
  scope = _messages.MessageField('Scope', 17)
  serializedApplicationTemplate = _messages.MessageField('SerializedApplicationTemplate', 18)
  serviceAccount = _messages.StringField(19)
  source = _messages.MessageField('DeploymentSource', 20)
  state = _messages.EnumField('StateValueValuesEnum', 21)
  updateTime = _messages.StringField(22)
  updatedTemplateRevision = _messages.MessageField('UpdatedTemplateRevision', 23)


class ApplicationOperationMetadata(_messages.Message):
  r"""Ephemeral metadata depicting the state of the delete operation.

  Fields:
    build: Output only. Cloud Build instance UUID associated with the delete
      operation.
    step: Output only. The current step of the delete operation. Associated
      with Infrastructure Manager and App Hub.
  """

  build = _messages.StringField(1)
  step = _messages.StringField(2)


class ApplicationTemplate(_messages.Message):
  r"""Application template resource.

  Enums:
    IacFormatValueValuesEnum: Output only. The IaC format of the application
      template.

  Fields:
    applicationParameters: Optional. Parameters to apply to all components in
      an application. You can specify projectID and region.
    createTime: Output only. Application template creation timestamp.
    description: Optional. Application template description.
    displayName: Optional. Application template display name.
    iacFormat: Output only. The IaC format of the application template.
    latestRevision: Output only. The latest application template revision.
    name: Identifier. Application template name.
    serializedApplicationTemplate: Output only. The serialized application
      template.
    updateTime: Output only. Application template update timestamp.
  """

  class IacFormatValueValuesEnum(_messages.Enum):
    r"""Output only. The IaC format of the application template.

    Values:
      IAC_FORMAT_UNSPECIFIED: IaC format is unspecified.
      TERRAFORM: IaC format is Terraform.
    """
    IAC_FORMAT_UNSPECIFIED = 0
    TERRAFORM = 1

  applicationParameters = _messages.MessageField('Parameter', 1, repeated=True)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  displayName = _messages.StringField(4)
  iacFormat = _messages.EnumField('IacFormatValueValuesEnum', 5)
  latestRevision = _messages.StringField(6)
  name = _messages.StringField(7)
  serializedApplicationTemplate = _messages.MessageField('SerializedApplicationTemplate', 8)
  updateTime = _messages.StringField(9)


class ApplicationTemplateRevision(_messages.Message):
  r"""Application template revision resource.

  Fields:
    createTime: Output only. The application template revision creation
      timestamp.
    name: Identifier. The application template revision name.
    snapshot: Output only. The serialized application template.
  """

  createTime = _messages.StringField(1)
  name = _messages.StringField(2)
  snapshot = _messages.MessageField('SerializedApplicationTemplate', 3)


class Attributes(_messages.Message):
  r"""Consumer provided attributes.

  Fields:
    businessOwners: Optional. Business team that ensures user needs are met
      and value is delivered
    criticality: Optional. User-defined criticality information.
    developerOwners: Optional. Developer team that owns development and
      coding.
    environment: Optional. User-defined environment information.
    operatorOwners: Optional. Operator team that ensures runtime and
      operations.
  """

  businessOwners = _messages.MessageField('ContactInfo', 1, repeated=True)
  criticality = _messages.MessageField('Criticality', 2)
  developerOwners = _messages.MessageField('ContactInfo', 3, repeated=True)
  environment = _messages.MessageField('Environment', 4)
  operatorOwners = _messages.MessageField('ContactInfo', 5, repeated=True)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class Catalog(_messages.Message):
  r"""A collection of templates.

  Fields:
    createTime: Output only. The catalog creation timestamp.
    description: Optional. The catalog description.
    displayName: Optional. The catalog display name.
    name: Identifier. The catalog name in the following format:
      projects/$project/locations/$location/spaces/$space/catalogs/$catalog
    updateTime: Output only. The catalog update timestamp.
  """

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  name = _messages.StringField(4)
  updateTime = _messages.StringField(5)


class CatalogTemplate(_messages.Message):
  r"""A template inside a catalog.

  Fields:
    createTime: Output only. The catalog template creation timestamp.
    description: Optional. The catalog template description.
    displayName: Optional. The display name of a catalog template.
    latestRevisionId: Output only. Latest revision of the template.
    name: Identifier. The catalog template name in following format: projects/
      $project/locations/$location/spaces/$space/catalogs/$catalog/templates/$
      template
    updateTime: Output only. The catalog template update timestamp.
    uuid: Output only. The template revisions UUID.
  """

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  latestRevisionId = _messages.StringField(4)
  name = _messages.StringField(5)
  updateTime = _messages.StringField(6)
  uuid = _messages.StringField(7)


class CatalogTemplateRevision(_messages.Message):
  r"""Template revisions inside a catalog.

  Enums:
    StateValueValuesEnum: Output only. The template state
      (validating/ready/invalid).
    TypeValueValuesEnum: Required. The Application Design Center assembly
      template type.

  Fields:
    applicationTemplateRevision: Output only. The application template
      revision.
    applicationTemplateRevisionSource: Optional. The application template
      revision source.
    createTime: Output only. The catalog template creation timestamp.
    description: Optional. The catalog template revision description.
    gitSource: Optional. The git source.
    name: Identifier. The catalog template revision name. projects/$project/lo
      cations/$location/spaces/$space/catalogs/$catalog/templates/$template/re
      visions/$revision
    state: Output only. The template state (validating/ready/invalid).
    templateMetadata: Output only. Template metadata related to Terraform
      input and output.
    type: Required. The Application Design Center assembly template type.
    updateTime: Output only. The catalog template update timestamp.
    uuid: Output only. UUID of the template revision.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The template state (validating/ready/invalid).

    Values:
      STATE_UNSPECIFIED: Default.
      VALIDATING: The template is being validated.
      ACTIVE: The template is ready to be used.
      INVALID: The template is invalid.
    """
    STATE_UNSPECIFIED = 0
    VALIDATING = 1
    ACTIVE = 2
    INVALID = 3

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. The Application Design Center assembly template type.

    Values:
      TEMPLATE_TYPE_UNSPECIFIED: Default.
      SERVICE: A service template is an App Hub service.
      WORKLOAD: A workload template is an App Hub workload.
      ASSET: An asset template can be used to provision resources that are not
        services or workloads.
      APPLICATION: An application template is a composition of
        workload/service/asset templates.
      JSS_SOLUTION: A Jumpstart Solution template.
      SERVICE_DATA_SOURCE: A service data source template.
    """
    TEMPLATE_TYPE_UNSPECIFIED = 0
    SERVICE = 1
    WORKLOAD = 2
    ASSET = 3
    APPLICATION = 4
    JSS_SOLUTION = 5
    SERVICE_DATA_SOURCE = 6

  applicationTemplateRevision = _messages.MessageField('SerializedApplicationTemplate', 1)
  applicationTemplateRevisionSource = _messages.StringField(2)
  createTime = _messages.StringField(3)
  description = _messages.StringField(4)
  gitSource = _messages.MessageField('GitSource', 5)
  name = _messages.StringField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  templateMetadata = _messages.MessageField('TFBlueprintMetadata', 8)
  type = _messages.EnumField('TypeValueValuesEnum', 9)
  updateTime = _messages.StringField(10)
  uuid = _messages.StringField(11)


class Channel(_messages.Message):
  r"""Separate message to accommodate custom formats across IRC and Slack.

  Fields:
    uri: Required. URI of the channel.
  """

  uri = _messages.StringField(1)


class CommitApplicationTemplateRequest(_messages.Message):
  r"""Request message for CommitApplicationTemplate method."""


class Component(_messages.Message):
  r"""Component resource.

  Fields:
    apis: Output only. APIs required to be enabled to deploy the component, in
      the form of "*.googleapis.com".
    connectionsParameters: Output only. The connection parameters of the
      component.
    createTime: Output only.
    displayName: Optional. The component display name.
    name: Identifier. The component name.
    parameters: Optional. The component parameters.
    roles: Output only. IAM roles required by the service account to deploy
      the component.
    sharedTemplateRevisionUri: Required. The shared template used to generate
      the component.
    updateTime: Output only. The component update timestamp.
  """

  apis = _messages.StringField(1, repeated=True)
  connectionsParameters = _messages.MessageField('ConnectionParameters', 2, repeated=True)
  createTime = _messages.StringField(3)
  displayName = _messages.StringField(4)
  name = _messages.StringField(5)
  parameters = _messages.MessageField('Parameter', 6, repeated=True)
  roles = _messages.StringField(7, repeated=True)
  sharedTemplateRevisionUri = _messages.StringField(8)
  updateTime = _messages.StringField(9)


class ComponentOutputParameters(_messages.Message):
  r"""The component output parameters of the deployment.

  Fields:
    component: Output only. The component name of the output parameters.
    parameters: Output only. The output parameters of the component.
  """

  component = _messages.StringField(1)
  parameters = _messages.MessageField('Parameter', 2, repeated=True)


class ComponentParameters(_messages.Message):
  r"""Information about the component level parameters for an application.

  Enums:
    StateValueValuesEnum: Output only. Deployment state of the component.

  Fields:
    component: Required. The name of the component parameter.
    connectionsParameters: Output only.
    parameters: Optional. A list of parameters associated with the component.
    state: Output only. Deployment state of the component.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Deployment state of the component.

    Values:
      STATE_UNSPECIFIED: Unspecified component deployment state.
      DRAFT: Component is in draft.
      CREATING_DEPLOYMENT: New Component deployment is in progress.
      UPDATING_DEPLOYMENT: Update Component deployment is in progress.
      DELETING: Delete Component deployment is in progress.
      DEPLOYED: Component deployment is completed.
      DELETED: Component deployment is deleted.
      FAILED: Component deployment is failed.
    """
    STATE_UNSPECIFIED = 0
    DRAFT = 1
    CREATING_DEPLOYMENT = 2
    UPDATING_DEPLOYMENT = 3
    DELETING = 4
    DEPLOYED = 5
    DELETED = 6
    FAILED = 7

  component = _messages.StringField(1)
  connectionsParameters = _messages.MessageField('ConnectionParameters', 2, repeated=True)
  parameters = _messages.MessageField('Parameter', 3, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 4)


class Connection(_messages.Message):
  r"""Connection resource.

  Fields:
    createTime: Output only. The connection creation timestamp.
    destinationComponentParameters: Optional. The parameters of the connection
      associated with the destination component.
    destinationComponentUri: Required. The destination component URI used to
      generate the connection.
    name: Identifier. The connection name.
    sourceComponentParameters: Optional. The parameters of the connection
      associated with the source component.
    updateTime: Output only. The connection update timestamp.
  """

  createTime = _messages.StringField(1)
  destinationComponentParameters = _messages.MessageField('Parameter', 2, repeated=True)
  destinationComponentUri = _messages.StringField(3)
  name = _messages.StringField(4)
  sourceComponentParameters = _messages.MessageField('Parameter', 5, repeated=True)
  updateTime = _messages.StringField(6)


class ConnectionConfig(_messages.Message):
  r"""Connection configuration for the application.

  Fields:
    connectionUri: Required. The connection URI.
    destinationComponentParameters: Optional. The parameters of the connection
      associated with the destination component.
    sourceComponentParameters: Optional. The parameters of the connection
      associated with the source component.
  """

  connectionUri = _messages.StringField(1)
  destinationComponentParameters = _messages.MessageField('Parameter', 2, repeated=True)
  sourceComponentParameters = _messages.MessageField('Parameter', 3, repeated=True)


class ConnectionParameters(_messages.Message):
  r"""Connection level parameters associated with each component within an
  application.

  Fields:
    connection: Required. The name of the connection parameter.
    parameters: Optional. A list of parameters associated with the connection.
  """

  connection = _messages.StringField(1)
  parameters = _messages.MessageField('Parameter', 2, repeated=True)


class ContactInfo(_messages.Message):
  r"""Contact information of stakeholders.

  Fields:
    channel: Optional. Communication channel of the contacts.
    displayName: Optional. Contact's name. Can have a maximum length of 63
      characters.
    email: Required. Email address of the contacts.
  """

  channel = _messages.MessageField('Channel', 1)
  displayName = _messages.StringField(2)
  email = _messages.StringField(3)


class Criticality(_messages.Message):
  r"""Criticality of the Application, Service, or Workload

  Enums:
    TypeValueValuesEnum: Required. Criticality Type.

  Fields:
    level: Optional. Criticality level. Can contain only lowercase letters,
      numeric characters, underscores, and dashes. Can have a maximum length
      of 63 characters. Deprecated: Please refer to type instead.
    missionCritical: Optional. Indicates mission-critical Application,
      Service, or Workload. Deprecated: Please refer to type instead.
    type: Required. Criticality Type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. Criticality Type.

    Values:
      TYPE_UNSPECIFIED: Unspecified type.
      MISSION_CRITICAL: Mission critical service, application or workload.
      HIGH: High impact.
      MEDIUM: Medium impact.
      LOW: Low impact.
    """
    TYPE_UNSPECIFIED = 0
    MISSION_CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4

  level = _messages.StringField(1)
  missionCritical = _messages.BooleanField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class DeployApplicationRequest(_messages.Message):
  r"""Message for deploying an application.

  Fields:
    replace: Optional. Flag to update the existing deployment. If not set or
      false, deploy will fail if application `state` is in the `DEPLOYED`
      state.
    serviceAccount: Optional. The email address of the service account to use
      for this deployment. - If provided, this service account will be used to
      execute the deployment process, taking precedence over any
      service_account specified on the Application resource itself for this
      operation. - The caller MUST have the 'iam.serviceAccounts.actAs'
      permission on this service account. - If this field is omitted, the
      system will use the 'service_account' defined within the Application
      resource. - It is strongly RECOMMENDED to provide a service account
      either here or on the Application resource, as deployment will fail if
      no service account can be determined. Format:
      projects/{PROJECT}/serviceAccounts/{EMAIL_ADDRESS}
    workerPool: Optional. The user-specified Worker Pool resource in which the
      Cloud Build job will execute. Format
      projects/{project}/locations/{location}/workerPools/{workerPoolId} If
      this field is unspecified, the default Cloud Build worker pool will be
      used. If omitted and application resource ref provided has worker_pool
      defined, that worker pool is used.
  """

  replace = _messages.BooleanField(1)
  serviceAccount = _messages.StringField(2)
  workerPool = _messages.StringField(3)


class DeploymentAttemptMetadata(_messages.Message):
  r"""DeploymentAttemptMetadata represents a previous deployment attempt for
  an operation that failed due to a retryable error.

  Fields:
    attempt: The sequential number of the attempt (starting from 1).
    build: The build or execution ID associated with this specific attempt.
      This can be used to link back to logs or trace information.
    errorDetail: Human readable string that summarizes the deployment error
      issue.
  """

  attempt = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  build = _messages.StringField(2)
  errorDetail = _messages.StringField(3)


class DeploymentError(_messages.Message):
  r"""The error associated with the deployment.

  Enums:
    CodeValueValuesEnum: Output only. The deployment error code. Based on
      Infrastructure Manager error codes.
    TypeValueValuesEnum: Output only. The error type based on the deployment
      error code.

  Fields:
    code: Output only. The deployment error code. Based on Infrastructure
      Manager error codes.
    deploymentFailureResolutionInfo: Output only. The call to actions
      associated with the deployment issue.
    detail: Output only. Human readable string that summarizes the deployment
      error issue.
    errorMessage: Output only. Stores errors generated by Infra Manager, as
      well as all the non-internal errors (e.g., INVALID_ARGUMENT) that occur
      before initiating the deployment.
    tfErrors: Output only. The error message associated with the deployment.
    type: Output only. The error type based on the deployment error code.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Output only. The deployment error code. Based on Infrastructure
    Manager error codes.

    Values:
      ERROR_CODE_UNSPECIFIED: No error code was specified.
      REVISION_FAILED: The revision failed. See Revision for more details.
      CLOUD_BUILD_PERMISSION_DENIED: Cloud Build failed due to a permission
        issue.
      DELETE_BUILD_API_FAILED: Cloud Build job associated with a deployment
        deletion could not be started.
      DELETE_BUILD_RUN_FAILED: Cloud Build job associated with a deployment
        deletion was started but failed.
      BUCKET_CREATION_PERMISSION_DENIED: Cloud Storage bucket creation failed
        due to a permission issue.
      BUCKET_CREATION_FAILED: Cloud Storage bucket creation failed due to an
        issue unrelated to permissions.
    """
    ERROR_CODE_UNSPECIFIED = 0
    REVISION_FAILED = 1
    CLOUD_BUILD_PERMISSION_DENIED = 2
    DELETE_BUILD_API_FAILED = 3
    DELETE_BUILD_RUN_FAILED = 4
    BUCKET_CREATION_PERMISSION_DENIED = 5
    BUCKET_CREATION_FAILED = 6

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. The error type based on the deployment error code.

    Values:
      ERROR_TYPE_UNSPECIFIED: Unspecified error type.
      PERMISSION: Permission related error.
      QUOTA: Quota related error.
      ALREADY_EXISTS: Resource already exists.
      RESOURCE_UNAVAILABLE: Resource is unavailable.
      BILLING: Billing related error.
      GENERIC: Generic error if error does not fall under any of the above.
      TERRAFORM: Terraform related error.
      BAD_INPUT: Bad input error.
      PLATFORM: Platform related error.
      API_ENABLEMENT: API enablement error.
      BAD_REQUEST: Bad request error.
    """
    ERROR_TYPE_UNSPECIFIED = 0
    PERMISSION = 1
    QUOTA = 2
    ALREADY_EXISTS = 3
    RESOURCE_UNAVAILABLE = 4
    BILLING = 5
    GENERIC = 6
    TERRAFORM = 7
    BAD_INPUT = 8
    PLATFORM = 9
    API_ENABLEMENT = 10
    BAD_REQUEST = 11

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  deploymentFailureResolutionInfo = _messages.MessageField('DeploymentFailureResolutionInfo', 2, repeated=True)
  detail = _messages.StringField(3)
  errorMessage = _messages.StringField(4)
  tfErrors = _messages.MessageField('TerraformError', 5, repeated=True)
  type = _messages.EnumField('TypeValueValuesEnum', 6)


class DeploymentFailureResolutionInfo(_messages.Message):
  r"""The call to action associated with the deployment issue.

  Fields:
    resolutionLink: Output only. Link for the call to action.
    resolutionLinkText: Output only. Call to action link text.
    resolutionMessage: Output only. Elaborative error resolution message.
  """

  resolutionLink = _messages.StringField(1)
  resolutionLinkText = _messages.StringField(2)
  resolutionMessage = _messages.StringField(3)


class DeploymentMetadata(_messages.Message):
  r"""Deployment information for the application.

  Fields:
    build: Output only. Cloud Build instance UUID associated with this
      deployment.
    componentOutputParameters: Output only. The component output parameters of
      the deployment.
    error: Output only. The error associated with the deployment.
    retryAttempts: Output only. The attempted number of retry in the
      deployment.
    revision: Output only. The revision of the deployment associated with the
      Application.
    workerPool: Output only. The user-specified Cloud Build worker pool
      resource used for application deployment. Format:
      `projects/{project}/locations/{location}/workerPools/{workerPoolId}`.
  """

  build = _messages.StringField(1)
  componentOutputParameters = _messages.MessageField('ComponentOutputParameters', 2, repeated=True)
  error = _messages.MessageField('DeploymentError', 3)
  retryAttempts = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  revision = _messages.StringField(5)
  workerPool = _messages.StringField(6)


class DeploymentOperationMetadata(_messages.Message):
  r"""Ephemeral metadata depicting the state of the deployment operation.

  Fields:
    deployment: Output only. The name of the deployment associated with the
      application.
    deploymentBuild: Output only. Cloud Build instance UUID associated with
      this deployment.
    deploymentLogs: Output only. Location of the deployment operation logs in
      `gs://{bucket}/{object}` format.
    deploymentStep: Output only. The current step of the deployment. This will
      mimic the DeploymentStep enum in the deployment proto.
    previousAttempt: Previous deployment attempt metadata.
    resources: Details of the Resourcess in the deployment.
  """

  deployment = _messages.StringField(1)
  deploymentBuild = _messages.StringField(2)
  deploymentLogs = _messages.StringField(3)
  deploymentStep = _messages.StringField(4)
  previousAttempt = _messages.MessageField('DeploymentAttemptMetadata', 5)
  resources = _messages.MessageField('Resource', 6, repeated=True)


class DeploymentSource(_messages.Message):
  r"""Source template information for the deployment.

  Fields:
    applicationTemplateRevision: Application template revision URI.
    sharedTemplateRevisionUri: Shared template revision URI.
  """

  applicationTemplateRevision = _messages.StringField(1)
  sharedTemplateRevisionUri = _messages.StringField(2)


class DesigncenterProjectsLocationsGetRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class DesigncenterProjectsLocationsListRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class DesigncenterProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class DesigncenterProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class DesigncenterProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class DesigncenterProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class DesigncenterProjectsLocationsSpacesApplicationTemplatesCommitRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesApplicationTemplatesCommitRequest
  object.

  Fields:
    commitApplicationTemplateRequest: A CommitApplicationTemplateRequest
      resource to be passed as the request body.
    name: Required. The name of the application template.
  """

  commitApplicationTemplateRequest = _messages.MessageField('CommitApplicationTemplateRequest', 1)
  name = _messages.StringField(2, required=True)


class DesigncenterProjectsLocationsSpacesApplicationTemplatesComponentsConnectionsCreateRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesApplicationTemplatesComponentsConne
  ctionsCreateRequest object.

  Fields:
    connection: A Connection resource to be passed as the request body.
    connectionId: Required. The ID of the connection to create.
    parent: Required. The parent resource in which to create a connection.
  """

  connection = _messages.MessageField('Connection', 1)
  connectionId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class DesigncenterProjectsLocationsSpacesApplicationTemplatesComponentsConnectionsDeleteRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesApplicationTemplatesComponentsConne
  ctionsDeleteRequest object.

  Fields:
    name: Required. The connection name.
  """

  name = _messages.StringField(1, required=True)


class DesigncenterProjectsLocationsSpacesApplicationTemplatesComponentsConnectionsGetRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesApplicationTemplatesComponentsConne
  ctionsGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class DesigncenterProjectsLocationsSpacesApplicationTemplatesComponentsConnectionsListRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesApplicationTemplatesComponentsConne
  ctionsListRequest object.

  Fields:
    filter: Optional. A filter that chooses which connections to return.
    orderBy: Optional. How the results should be sorted.
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent resource from which to request a list of
      connections.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DesigncenterProjectsLocationsSpacesApplicationTemplatesComponentsConnectionsPatchRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesApplicationTemplatesComponentsConne
  ctionsPatchRequest object.

  Fields:
    connection: A Connection resource to be passed as the request body.
    name: Identifier. The connection name.
    updateMask: Optional. Fields to overwrite in the connection update. The
      fields specified in the update_mask are relative to the resource, not
      the full request. A field will be overwritten if it is in the mask. If
      you don't provide a mask, all fields are overwritten.
  """

  connection = _messages.MessageField('Connection', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class DesigncenterProjectsLocationsSpacesApplicationTemplatesComponentsCreateRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesApplicationTemplatesComponentsCreat
  eRequest object.

  Fields:
    component: A Component resource to be passed as the request body.
    componentId: Required. The ID of the component to create.
    parent: Required. The parent resource in which to create the component.
  """

  component = _messages.MessageField('Component', 1)
  componentId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class DesigncenterProjectsLocationsSpacesApplicationTemplatesComponentsDeleteRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesApplicationTemplatesComponentsDelet
  eRequest object.

  Fields:
    force: Optional. If set to true, the component's children are also
      deleted. If false, the component is only deleted if it has no children.
    name: Required. The component name.
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class DesigncenterProjectsLocationsSpacesApplicationTemplatesComponentsGetRequest(_messages.Message):
  r"""A
  DesigncenterProjectsLocationsSpacesApplicationTemplatesComponentsGetRequest
  object.

  Fields:
    name: Required. The component name.
  """

  name = _messages.StringField(1, required=True)


class DesigncenterProjectsLocationsSpacesApplicationTemplatesComponentsListRequest(_messages.Message):
  r"""A
  DesigncenterProjectsLocationsSpacesApplicationTemplatesComponentsListRequest
  object.

  Fields:
    filter: Optional. A filter that chooses which components to return.
    orderBy: Optional. How the results should be sorted.
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent resource from which the list of components is
      requested.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DesigncenterProjectsLocationsSpacesApplicationTemplatesComponentsPatchRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesApplicationTemplatesComponentsPatch
  Request object.

  Fields:
    component: A Component resource to be passed as the request body.
    name: Identifier. The component name.
    updateMask: Optional. The fields to overwrite in the component update. If
      you don't provide a mask, all fields are overwritten.
  """

  component = _messages.MessageField('Component', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class DesigncenterProjectsLocationsSpacesApplicationTemplatesCreateRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesApplicationTemplatesCreateRequest
  object.

  Fields:
    applicationTemplate: A ApplicationTemplate resource to be passed as the
      request body.
    applicationTemplateId: Required. The ID of the application template to
      create.
    parent: Required. The parent resource in which to create the application
      template.
  """

  applicationTemplate = _messages.MessageField('ApplicationTemplate', 1)
  applicationTemplateId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class DesigncenterProjectsLocationsSpacesApplicationTemplatesDeleteRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesApplicationTemplatesDeleteRequest
  object.

  Fields:
    force: Optional. If set to true, the application template's children are
      also deleted. If false, the application template is only deleted if it
      has no children.
    name: Required. The application template name.
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class DesigncenterProjectsLocationsSpacesApplicationTemplatesGenerateRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesApplicationTemplatesGenerateRequest
  object.

  Fields:
    generateApplicationTemplateIaCRequest: A
      GenerateApplicationTemplateIaCRequest resource to be passed as the
      request body.
    name: Required. The name of the application template.
  """

  generateApplicationTemplateIaCRequest = _messages.MessageField('GenerateApplicationTemplateIaCRequest', 1)
  name = _messages.StringField(2, required=True)


class DesigncenterProjectsLocationsSpacesApplicationTemplatesGetRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesApplicationTemplatesGetRequest
  object.

  Fields:
    name: Required. The application template name.
  """

  name = _messages.StringField(1, required=True)


class DesigncenterProjectsLocationsSpacesApplicationTemplatesImportRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesApplicationTemplatesImportRequest
  object.

  Fields:
    importApplicationTemplateRequest: A ImportApplicationTemplateRequest
      resource to be passed as the request body.
    name: Required. The name of the application template.
  """

  importApplicationTemplateRequest = _messages.MessageField('ImportApplicationTemplateRequest', 1)
  name = _messages.StringField(2, required=True)


class DesigncenterProjectsLocationsSpacesApplicationTemplatesListRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesApplicationTemplatesListRequest
  object.

  Fields:
    filter: Optional. A filter that chooses which shared template revisions to
      return.
    orderBy: Optional. How the results should be sorted.
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent resource from which to list application
      templates.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DesigncenterProjectsLocationsSpacesApplicationTemplatesPatchRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesApplicationTemplatesPatchRequest
  object.

  Fields:
    applicationTemplate: A ApplicationTemplate resource to be passed as the
      request body.
    name: Identifier. Application template name.
    updateMask: Optional. The fields to overwrite in the application template
      update. The fields specified in the update_mask are relative to the
      resource, not the full request. A field will be overwritten if it is in
      the mask. If you don't provide a mask, all fields are overwritten.
  """

  applicationTemplate = _messages.MessageField('ApplicationTemplate', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class DesigncenterProjectsLocationsSpacesApplicationTemplatesRevisionsDeleteRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesApplicationTemplatesRevisionsDelete
  Request object.

  Fields:
    name: Required. The application template revision name.
  """

  name = _messages.StringField(1, required=True)


class DesigncenterProjectsLocationsSpacesApplicationTemplatesRevisionsGetRequest(_messages.Message):
  r"""A
  DesigncenterProjectsLocationsSpacesApplicationTemplatesRevisionsGetRequest
  object.

  Fields:
    name: Required. The application template revision name.
  """

  name = _messages.StringField(1, required=True)


class DesigncenterProjectsLocationsSpacesApplicationTemplatesRevisionsListRequest(_messages.Message):
  r"""A
  DesigncenterProjectsLocationsSpacesApplicationTemplatesRevisionsListRequest
  object.

  Fields:
    filter: Optional. A filter that chooses which application template
      revisions to return.
    orderBy: Optional. How the results should be sorted.
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent from which to list application template
      revisions.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DesigncenterProjectsLocationsSpacesApplicationsCreateRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesApplicationsCreateRequest object.

  Fields:
    application: A Application resource to be passed as the request body.
    applicationId: Required. The ID of the application to create.
    parent: Required. The parent resource in which to create the application.
      Format: projects/$project/locations/$location/spaces/$space
  """

  application = _messages.MessageField('Application', 1)
  applicationId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class DesigncenterProjectsLocationsSpacesApplicationsDeleteRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesApplicationsDeleteRequest object.

  Fields:
    force: Optional. If set to true, the application's children are also
      deleted. If false, the application is only deleted if it has no
      children.
    name: Required. The application name. Format: projects/$project/locations/
      $location/spaces/$space/applications/$application
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class DesigncenterProjectsLocationsSpacesApplicationsDeployRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesApplicationsDeployRequest object.

  Fields:
    deployApplicationRequest: A DeployApplicationRequest resource to be passed
      as the request body.
    name: Required. The application name. Format: projects/$project/locations/
      $location/spaces/$space/applications/$application
  """

  deployApplicationRequest = _messages.MessageField('DeployApplicationRequest', 1)
  name = _messages.StringField(2, required=True)


class DesigncenterProjectsLocationsSpacesApplicationsGenerateRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesApplicationsGenerateRequest object.

  Fields:
    generateApplicationIaCRequest: A GenerateApplicationIaCRequest resource to
      be passed as the request body.
    name: Required. The name of the application.
  """

  generateApplicationIaCRequest = _messages.MessageField('GenerateApplicationIaCRequest', 1)
  name = _messages.StringField(2, required=True)


class DesigncenterProjectsLocationsSpacesApplicationsGetRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesApplicationsGetRequest object.

  Fields:
    name: Required. The application name. Format: projects/$project/locations/
      $location/spaces/$space/applications/$application
  """

  name = _messages.StringField(1, required=True)


class DesigncenterProjectsLocationsSpacesApplicationsListRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesApplicationsListRequest object.

  Fields:
    filter: Optional. A filter that chooses which applications to return.
    orderBy: Optional. How the results should be sorted.
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent resource from which to request a list of
      applications.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DesigncenterProjectsLocationsSpacesApplicationsPatchRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesApplicationsPatchRequest object.

  Fields:
    application: A Application resource to be passed as the request body.
    name: Identifier. The name of the application. Format: projects/{project}/
      locations/{location}/spaces/{space}/applications/{application}
    updateMask: Optional. The fields to overwrite in the application update.
      If you don't provide a mask, all fields are overwritten.
  """

  application = _messages.MessageField('Application', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class DesigncenterProjectsLocationsSpacesApplicationsPreviewRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesApplicationsPreviewRequest object.

  Fields:
    name: Required. The application name in the following format: projects/$pr
      oject/locations/$location/spaces/$space/applications/$application
    previewApplicationRequest: A PreviewApplicationRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  previewApplicationRequest = _messages.MessageField('PreviewApplicationRequest', 2)


class DesigncenterProjectsLocationsSpacesCatalogsCreateRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesCatalogsCreateRequest object.

  Fields:
    catalog: A Catalog resource to be passed as the request body.
    catalogId: Required. The ID of the catalog to create.
    parent: Required. The parent space in which a catalog is created in the
      following format: projects/$project/locations/$location/spaces/$space
  """

  catalog = _messages.MessageField('Catalog', 1)
  catalogId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class DesigncenterProjectsLocationsSpacesCatalogsDeleteRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesCatalogsDeleteRequest object.

  Fields:
    force: Optional. If set to true, the catalog's children are also deleted.
      If false, the catalog is only deleted if it has no children.
    name: Required. The catalog name in the following format:
      projects/$project/locations/$location/spaces/$space/catalogs/$catalog
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class DesigncenterProjectsLocationsSpacesCatalogsGetRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesCatalogsGetRequest object.

  Fields:
    name: Required. The catalog name in the following format:
      projects/$project/locations/$location/spaces/$space/catalogs/$catalog
  """

  name = _messages.StringField(1, required=True)


class DesigncenterProjectsLocationsSpacesCatalogsListRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesCatalogsListRequest object.

  Fields:
    filter: Optional. A filter that chooses which catalogs to return.
    orderBy: Optional. How the results should be sorted.
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, retrieve the next batch of results from
      the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent space from which catalogs are listed. Format:
      projects/$project/locations/$location/spaces/$space
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DesigncenterProjectsLocationsSpacesCatalogsPatchRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesCatalogsPatchRequest object.

  Fields:
    catalog: A Catalog resource to be passed as the request body.
    name: Identifier. The catalog name in the following format:
      projects/$project/locations/$location/spaces/$space/catalogs/$catalog
    updateMask: Optional. The fields to overwrite in the catalog update. If
      you don't provide a mask, all fields are overwritten.
  """

  catalog = _messages.MessageField('Catalog', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class DesigncenterProjectsLocationsSpacesCatalogsSharesCreateRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesCatalogsSharesCreateRequest object.

  Fields:
    parent: Required. The parent resource in which shares are created.
    share: A Share resource to be passed as the request body.
    shareId: Required. The ID of the share to create.
  """

  parent = _messages.StringField(1, required=True)
  share = _messages.MessageField('Share', 2)
  shareId = _messages.StringField(3)


class DesigncenterProjectsLocationsSpacesCatalogsSharesDeleteRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesCatalogsSharesDeleteRequest object.

  Fields:
    name: Required. The share name in the following format: projects/$project/
      locations/$location/spaces/$space/catalogs/$catalog/shares/$share
  """

  name = _messages.StringField(1, required=True)


class DesigncenterProjectsLocationsSpacesCatalogsSharesGetRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesCatalogsSharesGetRequest object.

  Fields:
    name: Required. The share name in the following format: projects/$project/
      locations/$location/spaces/$space/catalogs/$catalog/shares/$share
  """

  name = _messages.StringField(1, required=True)


class DesigncenterProjectsLocationsSpacesCatalogsSharesListRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesCatalogsSharesListRequest object.

  Fields:
    orderBy: Optional. How the results should be sorted.
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent resource from which a list of shares is
      requested.
  """

  orderBy = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class DesigncenterProjectsLocationsSpacesCatalogsSharesSyncRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesCatalogsSharesSyncRequest object.

  Fields:
    name: Required. The share name in the following format: projects/$project/
      locations/$location/spaces/$space/catalogs/$catalog/shares/$share
    syncShareRequest: A SyncShareRequest resource to be passed as the request
      body.
  """

  name = _messages.StringField(1, required=True)
  syncShareRequest = _messages.MessageField('SyncShareRequest', 2)


class DesigncenterProjectsLocationsSpacesCatalogsTemplatesCreateRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesCatalogsTemplatesCreateRequest
  object.

  Fields:
    catalogTemplate: A CatalogTemplate resource to be passed as the request
      body.
    catalogTemplateId: Required. The ID of the catalog template to create. The
      ID is appended to the catalog template's name.
    parent: Required. The parent resource in which the catalog template is
      created.
  """

  catalogTemplate = _messages.MessageField('CatalogTemplate', 1)
  catalogTemplateId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class DesigncenterProjectsLocationsSpacesCatalogsTemplatesDeleteRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesCatalogsTemplatesDeleteRequest
  object.

  Fields:
    force: Optional. If set to true, the catalog template's children are also
      deleted. If false, the catalog template is only deleted if it has no
      children.
    name: Required. The catalog template name. Format: projects/$project/locat
      ions/$location/spaces/$space/catalogs/$catalog/templates/$template
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class DesigncenterProjectsLocationsSpacesCatalogsTemplatesGetRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesCatalogsTemplatesGetRequest object.

  Fields:
    name: Required. The catalog template name in the following format: project
      s/$project/locations/$location/spaces/$space/catalogs/$catalog/templates
      /$template
  """

  name = _messages.StringField(1, required=True)


class DesigncenterProjectsLocationsSpacesCatalogsTemplatesListRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesCatalogsTemplatesListRequest
  object.

  Fields:
    filter: Optional. A filter that chooses the catalog templates to return.
    orderBy: Optional. How the results should be sorted.
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent from which the catalog templates are listed
      in the following format:
      projects/$project/locations/$location/spaces/$space/catalogs/$catalog
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DesigncenterProjectsLocationsSpacesCatalogsTemplatesPatchRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesCatalogsTemplatesPatchRequest
  object.

  Fields:
    catalogTemplate: A CatalogTemplate resource to be passed as the request
      body.
    name: Identifier. The catalog template name in following format: projects/
      $project/locations/$location/spaces/$space/catalogs/$catalog/templates/$
      template
    updateMask: Optional. The fields to overwrite in the catalog template
      update. The fields specified in the update_mask are relative to the
      resource, not the full request. A field will be overwritten if it is in
      the mask. If you don't provide a mask, all fields are overwritten.
  """

  catalogTemplate = _messages.MessageField('CatalogTemplate', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class DesigncenterProjectsLocationsSpacesCatalogsTemplatesRevisionsCreateRequest(_messages.Message):
  r"""A
  DesigncenterProjectsLocationsSpacesCatalogsTemplatesRevisionsCreateRequest
  object.

  Fields:
    catalogTemplateRevision: A CatalogTemplateRevision resource to be passed
      as the request body.
    catalogTemplateRevisionId: Required. The ID of the template revision to
      create.
    parent: Required. The parent in which the catalog template revisions
      request is created in following format:
      projects/$project/locations/$location/spaces/$space/templates/$template
  """

  catalogTemplateRevision = _messages.MessageField('CatalogTemplateRevision', 1)
  catalogTemplateRevisionId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class DesigncenterProjectsLocationsSpacesCatalogsTemplatesRevisionsDeleteRequest(_messages.Message):
  r"""A
  DesigncenterProjectsLocationsSpacesCatalogsTemplatesRevisionsDeleteRequest
  object.

  Fields:
    name: Required. The template revision name.
  """

  name = _messages.StringField(1, required=True)


class DesigncenterProjectsLocationsSpacesCatalogsTemplatesRevisionsGetRequest(_messages.Message):
  r"""A
  DesigncenterProjectsLocationsSpacesCatalogsTemplatesRevisionsGetRequest
  object.

  Fields:
    name: Required. The catalog template revisions name.
  """

  name = _messages.StringField(1, required=True)


class DesigncenterProjectsLocationsSpacesCatalogsTemplatesRevisionsListRequest(_messages.Message):
  r"""A
  DesigncenterProjectsLocationsSpacesCatalogsTemplatesRevisionsListRequest
  object.

  Fields:
    filter: Optional. A filter that chooses which catalog template revisions
      to return.
    orderBy: Optional. How the results should be sorted.
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent resource from which a list of catalog
      template revisions is requested.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DesigncenterProjectsLocationsSpacesCreateRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesCreateRequest object.

  Fields:
    parent: Required. The parent in which to create a space.
    space: A Space resource to be passed as the request body.
    spaceId: Required. The ID of the space to create.
  """

  parent = _messages.StringField(1, required=True)
  space = _messages.MessageField('Space', 2)
  spaceId = _messages.StringField(3)


class DesigncenterProjectsLocationsSpacesDeleteRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesDeleteRequest object.

  Fields:
    force: Optional. If set to true, the space's children are also deleted. If
      false, the space is only deleted if it has no children.
    name: Required. The space name.
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class DesigncenterProjectsLocationsSpacesGetIamPolicyRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class DesigncenterProjectsLocationsSpacesGetRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesGetRequest object.

  Fields:
    name: Required. The space name.
  """

  name = _messages.StringField(1, required=True)


class DesigncenterProjectsLocationsSpacesListRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesListRequest object.

  Fields:
    filter: Optional. A filter that chooses which spaces to return.
    orderBy: Optional. How the results are sorted.
    pageSize: Optional. The maximum number of pages to return from this
      request. If unspecified, the value automatically reflects the number of
      results returned.
    pageToken: Optional. If present, retrieve the next batch of results from
      the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The name of the parent resource, for which spaces are
      listed.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DesigncenterProjectsLocationsSpacesPatchRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesPatchRequest object.

  Fields:
    name: Identifier. The space name.
    space: A Space resource to be passed as the request body.
    updateMask: Optional. The fields to overwrite in the space update. If you
      don't provide a mask, all fields are overwritten.
  """

  name = _messages.StringField(1, required=True)
  space = _messages.MessageField('Space', 2)
  updateMask = _messages.StringField(3)


class DesigncenterProjectsLocationsSpacesSetIamPolicyRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class DesigncenterProjectsLocationsSpacesSharedTemplatesGetRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesSharedTemplatesGetRequest object.

  Fields:
    name: Required. The shared template name. Format: projects/$project/locati
      ons/$location/spaces/$space/sharedTemplates/$sharedTemplate
  """

  name = _messages.StringField(1, required=True)


class DesigncenterProjectsLocationsSpacesSharedTemplatesListRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesSharedTemplatesListRequest object.

  Fields:
    filter: Optional. A filter that chooses which shared templates to return.
    orderBy: Optional. How the results should be sorted.
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent space for which shared templates are listed
      in the following format:
      projects/$project/locations/$location/spaces/$space
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DesigncenterProjectsLocationsSpacesSharedTemplatesRevisionsGetRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesSharedTemplatesRevisionsGetRequest
  object.

  Fields:
    name: Required. The shared template revision name, in the following
      format: projects/$project/locations/$location/spaces/$space/sharedTempla
      tes/$sharedTemplate/revisions/$revision
  """

  name = _messages.StringField(1, required=True)


class DesigncenterProjectsLocationsSpacesSharedTemplatesRevisionsListRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesSharedTemplatesRevisionsListRequest
  object.

  Fields:
    filter: Optional. A filter that chooses which shared template revisions to
      return.
    orderBy: Optional. How the results should be sorted.
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent resource from which to list shared template
      revisions, in the following format: projects/$project/locations/$locatio
      n/spaces/$space/sharedTemplates/$sharedTemplate
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DesigncenterProjectsLocationsSpacesTestIamPermissionsRequest(_messages.Message):
  r"""A DesigncenterProjectsLocationsSpacesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class DeveloperConnectConfig(_messages.Message):
  r"""This config defines the location of a source through Developer Connect.

  Fields:
    gitRepositoryLink: Required. The Developer Connect Git repository link,
      formatted as `projects/*/locations/*/connections/*/gitRepositoryLink/*`.
  """

  gitRepositoryLink = _messages.StringField(1)


class DisplayVariableToggle(_messages.Message):
  r"""Display variable toggle.

  Enums:
    VariableTypeValueValuesEnum: Output only. The type of the variable used to
      toggle the display of another variable.

  Fields:
    variable: Output only. The name of the variable used to toggle the display
      of another variable.
    variableType: Output only. The type of the variable used to toggle the
      display of another variable.
    variableValues: Output only. The value of the variable used to toggle the
      display of another variable.
  """

  class VariableTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the variable used to toggle the display of
    another variable.

    Values:
      TOGGLE_VARIABLE_TYPE_UNSPECIFIED: Default
      TOGGLE_VARIABLE_TYPE_BOOLEAN: Boolean
      TOGGLE_VARIABLE_TYPE_STRING: String
      TOGGLE_VARIABLE_TYPE_INTEGER: Integer
    """
    TOGGLE_VARIABLE_TYPE_UNSPECIFIED = 0
    TOGGLE_VARIABLE_TYPE_BOOLEAN = 1
    TOGGLE_VARIABLE_TYPE_STRING = 2
    TOGGLE_VARIABLE_TYPE_INTEGER = 3

  variable = _messages.StringField(1)
  variableType = _messages.EnumField('VariableTypeValueValuesEnum', 2)
  variableValues = _messages.StringField(3, repeated=True)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Environment(_messages.Message):
  r"""Environment of the Application, Service, or Workload

  Enums:
    TypeValueValuesEnum: Required. Environment Type.

  Fields:
    environment: Optional. Environment name. Can contain only lowercase
      letters, numeric characters, underscores, and dashes. Can have a maximum
      length of 63 characters. Deprecated: Please refer to type instead.
    type: Required. Environment Type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. Environment Type.

    Values:
      TYPE_UNSPECIFIED: Unspecified type.
      PRODUCTION: Production environment.
      STAGING: Staging environment.
      TEST: Test environment.
      DEVELOPMENT: Development environment.
    """
    TYPE_UNSPECIFIED = 0
    PRODUCTION = 1
    STAGING = 2
    TEST = 3
    DEVELOPMENT = 4

  environment = _messages.StringField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GenerateApplicationIaCRequest(_messages.Message):
  r"""Request message for GenerateApplicationIaC method.

  Enums:
    IacFormatValueValuesEnum: Optional. The IaC format to generate.

  Fields:
    gcsUri: Optional. The Cloud Storage URI to write the generated IaC to.
    iacFormat: Optional. The IaC format to generate.
  """

  class IacFormatValueValuesEnum(_messages.Enum):
    r"""Optional. The IaC format to generate.

    Values:
      IAC_FORMAT_UNSPECIFIED: IaC format is unspecified.
      TERRAFORM: IaC format is Terraform.
    """
    IAC_FORMAT_UNSPECIFIED = 0
    TERRAFORM = 1

  gcsUri = _messages.StringField(1)
  iacFormat = _messages.EnumField('IacFormatValueValuesEnum', 2)


class GenerateApplicationIaCResponse(_messages.Message):
  r"""Response message for GenerateApplicationIaC method.

  Fields:
    gcsUri: Output only. The Cloud Storage URI of the generated IaC.
  """

  gcsUri = _messages.StringField(1)


class GenerateApplicationTemplateIaCRequest(_messages.Message):
  r"""Request message for GenerateApplicationTemplateIaC method.

  Enums:
    IacFormatValueValuesEnum: Optional. The IaC format to generate.

  Fields:
    gcsUri: Optional. The Cloud Storage URI to write the generated IaC to.
    iacFormat: Optional. The IaC format to generate.
  """

  class IacFormatValueValuesEnum(_messages.Enum):
    r"""Optional. The IaC format to generate.

    Values:
      IAC_FORMAT_UNSPECIFIED: IaC format is unspecified.
      TERRAFORM: IaC format is Terraform.
    """
    IAC_FORMAT_UNSPECIFIED = 0
    TERRAFORM = 1

  gcsUri = _messages.StringField(1)
  iacFormat = _messages.EnumField('IacFormatValueValuesEnum', 2)


class GenerateApplicationTemplateIaCResponse(_messages.Message):
  r"""Response message for GenerateApplicationTemplateIaC method.

  Fields:
    gcsUri: Output only. The Cloud Storage URI of the generated IaC.
  """

  gcsUri = _messages.StringField(1)


class GitSource(_messages.Message):
  r"""Defines how to access a Git source.

  Fields:
    commitSha: Output only. The commit SHA of the git repo deduced from given
      ref_tag.
    developerConnectConfig: Optional. This config defines the location of a
      source through Developer Connect. Used to access private git repos.
    dir: Optional. Directory, relative to the source root, in which to run the
      build. This must be a relative path. If a step's `dir` is specified and
      is an absolute path, this value is ignored for that step's execution.
    refTag: Required. The reference tag of the git repo.
    repo: Required. Location of the git repo to build.
  """

  commitSha = _messages.StringField(1)
  developerConnectConfig = _messages.MessageField('DeveloperConnectConfig', 2)
  dir = _messages.StringField(3)
  refTag = _messages.StringField(4)
  repo = _messages.StringField(5)


class ImportApplicationTemplateRequest(_messages.Message):
  r"""Request message for ImportApplicationTemplate method.

  Fields:
    applicationTemplateRevisionUri: Optional. The source URI for application
      template revision.
    sharedTemplateRevisionUri: Optional. The source URI for catalog template.
  """

  applicationTemplateRevisionUri = _messages.StringField(1)
  sharedTemplateRevisionUri = _messages.StringField(2)


class ListApplicationTemplateRevisionsResponse(_messages.Message):
  r"""Message for response to listing application template revisions.

  Fields:
    applicationTemplateRevisions: The list of application template revisions.
    nextPageToken: If there might be more results than those appearing in this
      response, then nextPageToken is included. To get the next set of
      results, call this method again using the value of nextPageToken as
      pageToken.
    unreachable: Locations that could not be reached.
  """

  applicationTemplateRevisions = _messages.MessageField('ApplicationTemplateRevision', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListApplicationTemplatesResponse(_messages.Message):
  r"""Message for response to listing application templates.

  Fields:
    applicationTemplates: The list of application templates.
    nextPageToken: If there might be more results than those appearing in this
      response, then nextPageToken is included. To get the next set of
      results, call this method again using the value of nextPageToken as
      pageToken.
    unreachable: Locations that could not be reached.
  """

  applicationTemplates = _messages.MessageField('ApplicationTemplate', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListApplicationsResponse(_messages.Message):
  r"""Message for response to listing applications.

  Fields:
    applications: The list of applications.
    nextPageToken: If there might be more results than those appearing in this
      response, then nextPageToken is included. To get the next set of
      results, call this method again using the value of nextPageToken as
      pageToken.
    unreachable: Locations that could not be reached.
  """

  applications = _messages.MessageField('Application', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListCatalogTemplateRevisionsResponse(_messages.Message):
  r"""Message for response to listing template revisions.

  Fields:
    catalogTemplateRevisions: The list of template revisions.
    nextPageToken: If there might be more results than those appearing in this
      response, then nextPageToken is included. To get the next set of
      results, call this method again using the value of nextPageToken as
      pageToken.
    unreachable: Locations that could not be reached.
  """

  catalogTemplateRevisions = _messages.MessageField('CatalogTemplateRevision', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListCatalogTemplatesResponse(_messages.Message):
  r"""Message for response to listing catalog templates.

  Fields:
    catalogTemplates: The list of catalog templates.
    nextPageToken: If there might be more results than those appearing in this
      response, then nextPageToken is included. To get the next set of
      results, call this method again using the value of nextPageToken as
      pageToken.
  """

  catalogTemplates = _messages.MessageField('CatalogTemplate', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListCatalogsResponse(_messages.Message):
  r"""Results returned from catalogs.list.

  Fields:
    catalogs: A list of catalogs
    nextPageToken: If there might be more results than those appearing in this
      response, nextPageToken is included. To get the next set of results,
      call this method again using the value of nextPageToken as pageToken.
  """

  catalogs = _messages.MessageField('Catalog', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListComponentsResponse(_messages.Message):
  r"""Message for response to listing components.

  Fields:
    components: The list of components.
    nextPageToken: If there might be more results than those appearing in this
      response, then nextPageToken is included. To get the next set of
      results, call this method again using the value of nextPageToken as
      pageToken.
    unreachable: Locations that could not be reached.
  """

  components = _messages.MessageField('Component', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListConnectionsResponse(_messages.Message):
  r"""Message for response to listing connections.

  Fields:
    connections: The list of connections.
    nextPageToken: If there might be more results than those appearing in this
      response, then nextPageToken is included. To get the next set of
      results, call this method again using the value of nextPageToken as
      pageToken.
    unreachable: Locations that could not be reached.
  """

  connections = _messages.MessageField('Connection', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListSharedTemplateRevisionsResponse(_messages.Message):
  r"""Message for response to listing shared templates.

  Fields:
    nextPageToken: If there might be more results than those appearing in this
      response, then nextPageToken is included. To get the next set of
      results, call this method again using the value of nextPageToken as
      pageToken.
    sharedTemplateRevisions: The list of shared templates.
  """

  nextPageToken = _messages.StringField(1)
  sharedTemplateRevisions = _messages.MessageField('SharedTemplateRevision', 2, repeated=True)


class ListSharedTemplatesResponse(_messages.Message):
  r"""Message for response to listing shared templates.

  Fields:
    nextPageToken: If there might be more results than those appearing in this
      response, then nextPageToken is included. To get the next set of
      results, call this method again using the value of nextPageToken as
      pageToken.
    sharedTemplates: The list of shared templates.
  """

  nextPageToken = _messages.StringField(1)
  sharedTemplates = _messages.MessageField('SharedTemplate', 2, repeated=True)


class ListSharesResponse(_messages.Message):
  r"""Message for response to listing shares of a catalog.

  Fields:
    nextPageToken: If there might be more results than those appearing in this
      response, then nextPageToken is included. To get the next set of
      results, call this method again using the value of nextPageToken as
      pageToken.
    shares: The list of shares.
  """

  nextPageToken = _messages.StringField(1)
  shares = _messages.MessageField('Share', 2, repeated=True)


class ListSpacesResponse(_messages.Message):
  r"""Results returned from projects.locations.spaces.list.

  Fields:
    nextPageToken: If there might be more results than those appearing in this
      response, nextPageToken is included. To get the next set of results,
      call this method again using the value of nextPageToken as pageToken.
    spaces: A list of spaces.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  spaces = _messages.MessageField('Space', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    applicationMetadata: Output only. The application delete metadata this
      operation is associated with.
    createTime: Output only. The time the operation was created.
    deploymentMetadata: Output only. The application deployment metadata this
      operation is associated with.
    endTime: Output only. The time the operation finished running.
    previewMetadata: Output only. The application preview metadata this
      operation is associated with.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human readable status of the operation, if
      any.
    target: Output only. The server defined resource path for the target of
      the operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  applicationMetadata = _messages.MessageField('ApplicationOperationMetadata', 2)
  createTime = _messages.StringField(3)
  deploymentMetadata = _messages.MessageField('DeploymentOperationMetadata', 4)
  endTime = _messages.StringField(5)
  previewMetadata = _messages.MessageField('PreviewOperationMetadata', 6)
  requestedCancellation = _messages.BooleanField(7)
  statusMessage = _messages.StringField(8)
  target = _messages.StringField(9)
  verb = _messages.StringField(10)


class Parameter(_messages.Message):
  r"""Parameter resource.

  Fields:
    key: Required. The key of the parameter.
    value: Required. The value of the parameter.
  """

  key = _messages.StringField(1)
  value = _messages.MessageField('extra_types.JsonValue', 2)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class PreviewApplicationRequest(_messages.Message):
  r"""Message for deploying an application.

  Fields:
    serviceAccount: Optional. The email address of the service account to use
      for this preview operation. - If provided, this service account will be
      used to execute the preview process, taking precedence over any
      service_account specified on the Application resource itself for this
      operation. - The caller MUST have the 'iam.serviceAccounts.actAs'
      permission on this service account. - If this field is omitted, the
      system will use the 'service_account' defined within the Application
      resource. - It is strongly RECOMMENDED to provide a service account
      either here or on the Application resource, as preview will fail if no
      service account can be determined. Format:
      projects/{PROJECT}/serviceAccounts/{EMAIL_ADDRESS}
    workerPool: Optional. The user-specified Worker Pool resource in which the
      Cloud Build job will execute. Format
      projects/{project}/locations/{location}/workerPools/{workerPoolId} If
      this field is unspecified, the default Cloud Build worker pool will be
      used. If omitted and application resource ref provided has worker_pool
      defined, that worker pool is used.
  """

  serviceAccount = _messages.StringField(1)
  workerPool = _messages.StringField(2)


class PreviewOperationMetadata(_messages.Message):
  r"""Ephemeral metadata depicting the state of the preview operation.

  Fields:
    previewBuild: Output only. Cloud Build instance UUID associated with the
      preview operation.
    previewLogs: Output only. Location of the preview operation logs in
      `gs://{bucket}/{object}` format.
    previewStep: Output only. The current step of the preview. The possible
      steps a preview may be running as returned by the Infrastructure.
      Manager.
    resources: Details of the Resourcess in the deployment.
  """

  previewBuild = _messages.StringField(1)
  previewLogs = _messages.StringField(2)
  previewStep = _messages.StringField(3)
  resources = _messages.MessageField('Resource', 4, repeated=True)


class ProjectParameters(_messages.Message):
  r"""The ProjectParameters provides the parameters needed to deploy the
  application in the given project.

  Fields:
    apis: Output only. APIs required to be enabled to deploy the application,
      in the form of "*.googleapis.com".
    projectId: Output only. The project ID of the project.
    roles: Output only. IAM roles required by the service account to deploy
      the application in given project.
  """

  apis = _messages.StringField(1, repeated=True)
  projectId = _messages.StringField(2)
  roles = _messages.StringField(3, repeated=True)


class ProviderVersion(_messages.Message):
  r"""ProviderVersion defines the required version for a provider.

  Fields:
    source: Output only. Provider source of form \[hostname]\namespace\name
      Hostname is optional defaulting to Terraform registry.
    version: Output only. Version constraint string.
  """

  source = _messages.StringField(1)
  version = _messages.StringField(2)


class Resource(_messages.Message):
  r"""Status of the Resourcess in the deployment.

  Enums:
    StateValueValuesEnum: Output only. Current state of the resource.

  Fields:
    name: Output only. Identifier. The name of the resource.
    state: Output only. Current state of the resource.
    type: Output only. The type of the resource.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the resource.

    Values:
      STATE_UNSPECIFIED: The default value. This value is used if the state is
        omitted.
      PLANNED: Resource has been planned for reconcile.
      IN_PROGRESS: Resource is actively reconciling into the intended state.
      RECONCILED: Resource has reconciled to intended state.
      FAILED: Resource failed to reconcile.
      DRIFTED: Drift is detected in the resourece state.
      INTENDED: Resource is intended state.
    """
    STATE_UNSPECIFIED = 0
    PLANNED = 1
    IN_PROGRESS = 2
    RECONCILED = 3
    FAILED = 4
    DRIFTED = 5
    INTENDED = 6

  name = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)
  type = _messages.StringField(3)


class Scope(_messages.Message):
  r"""Scope of an application.

  Enums:
    TypeValueValuesEnum: Required. Scope Type.

  Fields:
    type: Required. Scope Type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. Scope Type.

    Values:
      TYPE_UNSPECIFIED: Unspecified type.
      REGIONAL: Regional type.
      GLOBAL: Global type.
    """
    TYPE_UNSPECIFIED = 0
    REGIONAL = 1
    GLOBAL = 2

  type = _messages.EnumField('TypeValueValuesEnum', 1)


class SerializedApplicationTemplate(_messages.Message):
  r"""Serialized application template.

  Enums:
    IacFormatValueValuesEnum: Optional. The IaC format of the application
      template.

  Fields:
    apphubApplicationParameters: Optional. The App Hub application parameters.
    applicationParameters: Optional. Parameters to apply to all components in
      the application template.
    components: Optional. The application template components.
    description: Optional. The application template description.
    displayName: Optional. The application template display name.
    hasGlobalResource: Output only. Whether the application template is
      compatible with regional scope.
    iacFormat: Optional. The IaC format of the application template.
    uri: Optional. The application template URI.
  """

  class IacFormatValueValuesEnum(_messages.Enum):
    r"""Optional. The IaC format of the application template.

    Values:
      IAC_FORMAT_UNSPECIFIED: IaC format is unspecified.
      TERRAFORM: IaC format is Terraform.
    """
    IAC_FORMAT_UNSPECIFIED = 0
    TERRAFORM = 1

  apphubApplicationParameters = _messages.MessageField('AppHubApplicationParameters', 1)
  applicationParameters = _messages.MessageField('Parameter', 2, repeated=True)
  components = _messages.MessageField('SerializedComponent', 3, repeated=True)
  description = _messages.StringField(4)
  displayName = _messages.StringField(5)
  hasGlobalResource = _messages.BooleanField(6)
  iacFormat = _messages.EnumField('IacFormatValueValuesEnum', 7)
  uri = _messages.StringField(8)


class SerializedComponent(_messages.Message):
  r"""Serialized component.

  Fields:
    apis: Optional. APIs required to be enabled to deploy the component, in
      the form of "*.googleapis.com".
    connections: Optional. The component connections.
    connectionsParameters: Output only. The connection parameters of the
      component.
    displayName: Optional. The component display name.
    parameters: Optional. The component parameters.
    roles: Optional. IAM roles required by the service account to deploy the
      component.
    sharedTemplateRevisionUri: Optional. The shared template used to generate
      the component.
    uri: Optional. The component URI.
  """

  apis = _messages.StringField(1, repeated=True)
  connections = _messages.MessageField('SerializedConnection', 2, repeated=True)
  connectionsParameters = _messages.MessageField('ConnectionParameters', 3, repeated=True)
  displayName = _messages.StringField(4)
  parameters = _messages.MessageField('Parameter', 5, repeated=True)
  roles = _messages.StringField(6, repeated=True)
  sharedTemplateRevisionUri = _messages.StringField(7)
  uri = _messages.StringField(8)


class SerializedConnection(_messages.Message):
  r"""Serialized connection.

  Fields:
    destinationComponentParameters: Optional. The parameters of the connection
      associated with the destination component.
    destinationComponentUri: Optional. The destination component URI used to
      generate the connection.
    sourceComponentParameters: Optional. The parameters of the connection
      associated with the source component.
    uri: Optional. The connection URI.
  """

  destinationComponentParameters = _messages.MessageField('Parameter', 1, repeated=True)
  destinationComponentUri = _messages.StringField(2)
  sourceComponentParameters = _messages.MessageField('Parameter', 3, repeated=True)
  uri = _messages.StringField(4)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class Share(_messages.Message):
  r"""A catalog share between spaces.

  Enums:
    StateValueValuesEnum: Output only. The state of the share.

  Fields:
    createTime: Output only. The share creation timestamp.
    destinationSpace: Required. The space resource path to which the catalog
      is shared. Format is
      `projects/$project/locations/$location/spaces/$space`
    name: Identifier. The share name in the following format: projects/$projec
      t/locations/$location/spaces/$space/catalogs/$catalog/shares/$share
    state: Output only. The state of the share.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the share.

    Values:
      STATE_UNSPECIFIED: The state of the share is unknown.
      IN_SYNC: The share is in sync with the shared template catalog.
      OUT_OF_SYNC: The share is out of sync with the shared template catalog.
    """
    STATE_UNSPECIFIED = 0
    IN_SYNC = 1
    OUT_OF_SYNC = 2

  createTime = _messages.StringField(1)
  destinationSpace = _messages.StringField(2)
  name = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)


class SharedTemplate(_messages.Message):
  r"""A read-only template that is shared with a space.

  Fields:
    createTime: Output only. The shared template creation timestamp.
    description: Optional. The shared template description.
    displayName: Optional. The shared template display name.
    latestRevisionId: Output only. Latest revision of the shared template.
    name: Identifier. The shared template name. projects/$project/locations/$l
      ocation/spaces/$space/sharedTemplates/$sharedTemplate
    originTemplate: Output only. The origin template of the shared template.
    updateTime: Output only. The shared template update timestamp.
  """

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  latestRevisionId = _messages.StringField(4)
  name = _messages.StringField(5)
  originTemplate = _messages.StringField(6)
  updateTime = _messages.StringField(7)


class SharedTemplateRevision(_messages.Message):
  r"""A read-only template revision that is shared with a space.

  Enums:
    TypeValueValuesEnum: Required. The Application Design Center assembly
      template type.

  Fields:
    applicationTemplateRevision: Output only. The serialized application
      template revision.
    applicationTemplateRevisionSource: Optional. The application template
      revision source.
    createTime: Output only. The shared template revision creation timestamp.
    description: Optional. The shared template revision description.
    gitSource: Optional. The git source.
    name: Identifier. The shared template revision name. $sharedTemplate is a
      server-generated UUID. projects/$project/locations/$location/spaces/$spa
      ce/sharedTemplates/$sharedTemplate/revisions/$revision
    originTemplateRevision: Output only. The shared template revision refers
      to the following catalog template revision.
    sharedTemplateMetadata: Output only. The shared template metadata.
    type: Required. The Application Design Center assembly template type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. The Application Design Center assembly template type.

    Values:
      TEMPLATE_TYPE_UNSPECIFIED: Default.
      SERVICE: A service template is an App Hub service.
      WORKLOAD: A workload template is an App Hub workload.
      ASSET: An asset template can be used to provision resources that are not
        services or workloads.
      APPLICATION: An application template is a composition of
        workload/service/asset templates.
      JSS_SOLUTION: A Jumpstart Solution template.
      SERVICE_DATA_SOURCE: A service data source template.
    """
    TEMPLATE_TYPE_UNSPECIFIED = 0
    SERVICE = 1
    WORKLOAD = 2
    ASSET = 3
    APPLICATION = 4
    JSS_SOLUTION = 5
    SERVICE_DATA_SOURCE = 6

  applicationTemplateRevision = _messages.MessageField('SerializedApplicationTemplate', 1)
  applicationTemplateRevisionSource = _messages.StringField(2)
  createTime = _messages.StringField(3)
  description = _messages.StringField(4)
  gitSource = _messages.MessageField('GitSource', 5)
  name = _messages.StringField(6)
  originTemplateRevision = _messages.StringField(7)
  sharedTemplateMetadata = _messages.MessageField('TFBlueprintMetadata', 8)
  type = _messages.EnumField('TypeValueValuesEnum', 9)


class Space(_messages.Message):
  r"""Space is a top level resource for managing teams building applications
  through Application Design Center.

  Fields:
    createTime: Output only. Space creation timestamp.
    description: Optional. Description for the space.
    displayName: Optional. Display name for the space.
    enableGcpSharedTemplates: Optional. Flag to enable Google opinionated
      shared templates.
    gcsBucket: Optional. An existing Google Cloud Storage bucket that you want
      to use instead of creating a new bucket during ADC setup. If not
      provided, a default bucket is created during setup. The bucket must
      exist in the same project as the space. If the bucket name does not
      exist in the same project as the space, the request fails with an
      INVALID_ARGUMENT error. If you do not have access to the bucket, the
      request fails with a PERMISSION_DENIED error. Format: {$bucket_name} For
      example, if the Cloud Storage bucket URI is gs:\/\/{$bucket_name}, the
      format is {$bucket_name}.
    name: Identifier. The space name.
    updateTime: Output only. Space update timestamp
  """

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  enableGcpSharedTemplates = _messages.BooleanField(4)
  gcsBucket = _messages.StringField(5)
  name = _messages.StringField(6)
  updateTime = _messages.StringField(7)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class SyncShareRequest(_messages.Message):
  r"""Message for syncing a share of a catalog."""


class TFBlueprintMetadata(_messages.Message):
  r"""Terraform input and output metadata.

  Fields:
    apis: Output only. APIs that must be enabled to deploy the template, in
      the form of "compute.googleapis.com".
    connections: Output only. TODO b/********* - Check if connection field is
      required here. Union of all the Terraform input connections.
    deploymentDuration: Optional. The time estimate for deploying the
      blueprint.
    iconUri: Output only. The icon URI for the blueprint.
    providerVersions: Output only. Provider versions required to deploy the
      blueprint.
    roles: Output only. IAM roles required by the service account deploying
      the template, in the form of "roles/compute.admin".
    terraformInput: Output only. Terraform inputs.
    terraformOutput: Output only. Terraform outputs.
    uiMetadata: Output only. UI metadata for the blueprint.
  """

  apis = _messages.StringField(1, repeated=True)
  connections = _messages.MessageField('TerraformInputConnections', 2, repeated=True)
  deploymentDuration = _messages.StringField(3)
  iconUri = _messages.StringField(4)
  providerVersions = _messages.MessageField('ProviderVersion', 5, repeated=True)
  roles = _messages.StringField(6, repeated=True)
  terraformInput = _messages.MessageField('TerraformInput', 7, repeated=True)
  terraformOutput = _messages.MessageField('TerraformOutput', 8, repeated=True)
  uiMetadata = _messages.MessageField('TerraformBlueprintUiMetadata', 9)


class TerraformBlueprintUiMetadata(_messages.Message):
  r"""Terraform blueprint UI metadata.

  Fields:
    terraformUiInput: Output only. Terraform UI inputs.
    terraformUiOutput: Output only. Terraform UI outputs.
  """

  terraformUiInput = _messages.MessageField('TerraformUiInput', 1)
  terraformUiOutput = _messages.MessageField('TerraformUiOutput', 2)


class TerraformError(_messages.Message):
  r"""Errors encountered during actuation using Terraform

  Fields:
    error: Output only. Original error response from underlying Google API, if
      available.
    errorDescription: A human-readable error description.
    httpResponseCode: HTTP response code returned from Google Cloud Platform
      APIs when Terraform fails to provision the resource. If unset or 0, no
      HTTP response code was returned by Terraform.
    resourceAddress: Address of the resource associated with the error, e.g.
      `google_compute_network.vpc_network`.
  """

  error = _messages.MessageField('Status', 1)
  errorDescription = _messages.StringField(2)
  httpResponseCode = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  resourceAddress = _messages.StringField(4)


class TerraformInput(_messages.Message):
  r"""Terraform inputs.

  Fields:
    connections: Output only. Terraform input connections.
    defaultValue: Output only. The default value of the Terraform input
      variable.
    description: Output only. Terraform variable description.
    isRequired: Output only. Indicates if input is required.
    terraformInputVariable: Output only. Input variable name present in
      Terraform.
    type: Output only. The Terraform input data type.
  """

  connections = _messages.MessageField('TerraformInputConnections', 1, repeated=True)
  defaultValue = _messages.MessageField('extra_types.JsonValue', 2)
  description = _messages.StringField(3)
  isRequired = _messages.BooleanField(4)
  terraformInputVariable = _messages.StringField(5)
  type = _messages.StringField(6)


class TerraformInputConnections(_messages.Message):
  r"""TerraformInputConnections

  Fields:
    cftTemplateUri: Output only. List of other templates that can be connected
      to.
    cftTemplateVersion: Output only. Version of the other template that can be
      connected to.
    inputPath: Output only. Input path of the other template that can be
      connected to.
    outputVar: Output only. Output variable name present in Terraform.
  """

  cftTemplateUri = _messages.StringField(1)
  cftTemplateVersion = _messages.StringField(2)
  inputPath = _messages.StringField(3)
  outputVar = _messages.StringField(4)


class TerraformOutput(_messages.Message):
  r"""Terraform outputs.

  Fields:
    description: Output only. Description of the Terraform varaiable.
    terraformOutputVariable: Output only. Output variable name present in
      Terraform.
    type: Output only. Type of Terraform output.
  """

  description = _messages.StringField(1)
  terraformOutputVariable = _messages.StringField(2)
  type = _messages.MessageField('extra_types.JsonValue', 3)


class TerraformUiInput(_messages.Message):
  r"""Terraform blueprint UI input.

  Messages:
    TerraformUiInputsValue: Output only. Terraform UI inputs.

  Fields:
    terraformUiInputs: Output only. Terraform UI inputs.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TerraformUiInputsValue(_messages.Message):
    r"""Output only. Terraform UI inputs.

    Messages:
      AdditionalProperty: An additional property for a TerraformUiInputsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        TerraformUiInputsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TerraformUiInputsValue object.

      Fields:
        key: Name of the additional property.
        value: A TerraformUiInputDisplayVariable attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('TerraformUiInputDisplayVariable', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  terraformUiInputs = _messages.MessageField('TerraformUiInputsValue', 1)


class TerraformUiInputDisplayVariable(_messages.Message):
  r"""Terraform blueprint UI input display variable.

  Messages:
    PropertiesValue: Output only. properties is a map defining all the fields
      of the input variable.

  Fields:
    alternateDefaults: Output only. Alternate defaults for the input.
    displayName: Output only. Display name of the input.
    enumValueLabels: Output only. Labels for enum values. Values must be UTF-8
      text with no markup, and at most 64 characters.
    level: Output only. Indicates the "advanced" level of the input property.
      Level 0 (default) will always be shown. Level 1 corresponds to one
      expansion (user clicks "show advanced options" or "more options").
      Higher levels correspond to further expansions, or they may be collapsed
      to level 1 by the UI implementation. Optional.
    max: Output only. Maximum value for numeric types.
    min: Output only. Minimum value for numeric types.
    properties: Output only. properties is a map defining all the fields of
      the input variable.
    regexValidation: Output only. Regex based validation rules for the
      variable.
    subtext: Output only. Property subtext, displayed below the title.
    title: Output only. Title of the input.
    toggleUsingVariables: Output only. Variables used to toggle the display of
      another variable.
    validation: Output only. Text describing the validation rules for the
      property. Typically shown after an invalid input. Optional. UTF-8 text.
      No markup. At most 128 characters.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PropertiesValue(_messages.Message):
    r"""Output only. properties is a map defining all the fields of the input
    variable.

    Messages:
      AdditionalProperty: An additional property for a PropertiesValue object.

    Fields:
      additionalProperties: Additional properties of type PropertiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PropertiesValue object.

      Fields:
        key: Name of the additional property.
        value: A TerraformUiInputDisplayVariable attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('TerraformUiInputDisplayVariable', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  alternateDefaults = _messages.MessageField('AlternateDefault', 1, repeated=True)
  displayName = _messages.StringField(2)
  enumValueLabels = _messages.MessageField('ValueLabel', 3, repeated=True)
  level = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  max = _messages.FloatField(5, variant=_messages.Variant.FLOAT)
  min = _messages.FloatField(6, variant=_messages.Variant.FLOAT)
  properties = _messages.MessageField('PropertiesValue', 7)
  regexValidation = _messages.StringField(8)
  subtext = _messages.StringField(9)
  title = _messages.StringField(10)
  toggleUsingVariables = _messages.MessageField('DisplayVariableToggle', 11, repeated=True)
  validation = _messages.StringField(12)


class TerraformUiOutput(_messages.Message):
  r"""Terraform blueprint UI output.

  Messages:
    TerraformUiOutputsValue: Output only. Visibility of the output.

  Fields:
    outputMessage: Output only. Message to be displayed in the UI.
    terraformUiOutputs: Output only. Visibility of the output.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TerraformUiOutputsValue(_messages.Message):
    r"""Output only. Visibility of the output.

    Messages:
      AdditionalProperty: An additional property for a TerraformUiOutputsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        TerraformUiOutputsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TerraformUiOutputsValue object.

      Fields:
        key: Name of the additional property.
        value: A TerraformUiOutputDisplay attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('TerraformUiOutputDisplay', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  outputMessage = _messages.StringField(1)
  terraformUiOutputs = _messages.MessageField('TerraformUiOutputsValue', 2)


class TerraformUiOutputDisplay(_messages.Message):
  r"""Terraform blueprint UI output display.

  Enums:
    VisibilityValueValuesEnum: Output only. Visibility of the output.

  Fields:
    visibility: Output only. Visibility of the output.
  """

  class VisibilityValueValuesEnum(_messages.Enum):
    r"""Output only. Visibility of the output.

    Values:
      DISPLAY_VARIABLE_VISIBILITY_UNSPECIFIED: Default
      DISPLAY_VARIABLE_VISIBILITY_ROOT: Expose output as root module output.
    """
    DISPLAY_VARIABLE_VISIBILITY_UNSPECIFIED = 0
    DISPLAY_VARIABLE_VISIBILITY_ROOT = 1

  visibility = _messages.EnumField('VisibilityValueValuesEnum', 1)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class UpdatedTemplateRevision(_messages.Message):
  r"""The updated template revision, which results in an outdated application.

  Fields:
    createTime: Output only. The time when the template was created.
    templateDisplayName: Output only. The display name of the template. If the
      display name is not available, the template resource name is used.
    templateRevision: Output only. The details of the updated template
      revision.
  """

  createTime = _messages.StringField(1)
  templateDisplayName = _messages.StringField(2)
  templateRevision = _messages.StringField(3)


class ValueLabel(_messages.Message):
  r"""Value label for a UI input.

  Fields:
    label: Output only. Label of the enum.
    value: Output only. Value of the enum.
  """

  label = _messages.StringField(1)
  value = _messages.StringField(2)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    DesigncenterProjectsLocationsSpacesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
