"""Generated client library for cloudkms version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.cloudkms.v1 import cloudkms_v1_messages as messages


class CloudkmsV1(base_api.BaseApiClient):
  """Generated client library for service cloudkms version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://cloudkms.googleapis.com/'
  MTLS_BASE_URL = 'https://cloudkms.mtls.googleapis.com/'

  _PACKAGE = 'cloudkms'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform', 'https://www.googleapis.com/auth/cloudkms']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'CloudkmsV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new cloudkms handle."""
    url = url or self.BASE_URL
    super(CloudkmsV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.folders = self.FoldersService(self)
    self.organizations = self.OrganizationsService(self)
    self.projects_locations_ekmConfig = self.ProjectsLocationsEkmConfigService(self)
    self.projects_locations_ekmConnections = self.ProjectsLocationsEkmConnectionsService(self)
    self.projects_locations_keyHandles = self.ProjectsLocationsKeyHandlesService(self)
    self.projects_locations_keyRings_cryptoKeys_cryptoKeyVersions = self.ProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsService(self)
    self.projects_locations_keyRings_cryptoKeys = self.ProjectsLocationsKeyRingsCryptoKeysService(self)
    self.projects_locations_keyRings_importJobs = self.ProjectsLocationsKeyRingsImportJobsService(self)
    self.projects_locations_keyRings = self.ProjectsLocationsKeyRingsService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class FoldersService(base_api.BaseApiService):
    """Service class for the folders resource."""

    _NAME = 'folders'

    def __init__(self, client):
      super(CloudkmsV1.FoldersService, self).__init__(client)
      self._upload_configs = {
          }

    def GetAutokeyConfig(self, request, global_params=None):
      r"""Returns the AutokeyConfig for a folder.

      Args:
        request: (CloudkmsFoldersGetAutokeyConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AutokeyConfig) The response message.
      """
      config = self.GetMethodConfig('GetAutokeyConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetAutokeyConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/autokeyConfig',
        http_method='GET',
        method_id='cloudkms.folders.getAutokeyConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudkmsFoldersGetAutokeyConfigRequest',
        response_type_name='AutokeyConfig',
        supports_download=False,
    )

    def GetKajPolicyConfig(self, request, global_params=None):
      r"""Gets the KeyAccessJustificationsPolicyConfig for a given organization/folder/projects.

      Args:
        request: (CloudkmsFoldersGetKajPolicyConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (KeyAccessJustificationsPolicyConfig) The response message.
      """
      config = self.GetMethodConfig('GetKajPolicyConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetKajPolicyConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/kajPolicyConfig',
        http_method='GET',
        method_id='cloudkms.folders.getKajPolicyConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudkmsFoldersGetKajPolicyConfigRequest',
        response_type_name='KeyAccessJustificationsPolicyConfig',
        supports_download=False,
    )

    def UpdateAutokeyConfig(self, request, global_params=None):
      r"""Updates the AutokeyConfig for a folder. The caller must have both `cloudkms.autokeyConfigs.update` permission on the parent folder and `cloudkms.cryptoKeys.setIamPolicy` permission on the provided key project. A KeyHandle creation in the folder's descendant projects will use this configuration to determine where to create the resulting CryptoKey.

      Args:
        request: (CloudkmsFoldersUpdateAutokeyConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AutokeyConfig) The response message.
      """
      config = self.GetMethodConfig('UpdateAutokeyConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateAutokeyConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/autokeyConfig',
        http_method='PATCH',
        method_id='cloudkms.folders.updateAutokeyConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='autokeyConfig',
        request_type_name='CloudkmsFoldersUpdateAutokeyConfigRequest',
        response_type_name='AutokeyConfig',
        supports_download=False,
    )

    def UpdateKajPolicyConfig(self, request, global_params=None):
      r"""Updates the KeyAccessJustificationsPolicyConfig for a given organization/folder/projects.

      Args:
        request: (CloudkmsFoldersUpdateKajPolicyConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (KeyAccessJustificationsPolicyConfig) The response message.
      """
      config = self.GetMethodConfig('UpdateKajPolicyConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateKajPolicyConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/kajPolicyConfig',
        http_method='PATCH',
        method_id='cloudkms.folders.updateKajPolicyConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='keyAccessJustificationsPolicyConfig',
        request_type_name='CloudkmsFoldersUpdateKajPolicyConfigRequest',
        response_type_name='KeyAccessJustificationsPolicyConfig',
        supports_download=False,
    )

  class OrganizationsService(base_api.BaseApiService):
    """Service class for the organizations resource."""

    _NAME = 'organizations'

    def __init__(self, client):
      super(CloudkmsV1.OrganizationsService, self).__init__(client)
      self._upload_configs = {
          }

    def GetKajPolicyConfig(self, request, global_params=None):
      r"""Gets the KeyAccessJustificationsPolicyConfig for a given organization/folder/projects.

      Args:
        request: (CloudkmsOrganizationsGetKajPolicyConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (KeyAccessJustificationsPolicyConfig) The response message.
      """
      config = self.GetMethodConfig('GetKajPolicyConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetKajPolicyConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/kajPolicyConfig',
        http_method='GET',
        method_id='cloudkms.organizations.getKajPolicyConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudkmsOrganizationsGetKajPolicyConfigRequest',
        response_type_name='KeyAccessJustificationsPolicyConfig',
        supports_download=False,
    )

    def UpdateKajPolicyConfig(self, request, global_params=None):
      r"""Updates the KeyAccessJustificationsPolicyConfig for a given organization/folder/projects.

      Args:
        request: (CloudkmsOrganizationsUpdateKajPolicyConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (KeyAccessJustificationsPolicyConfig) The response message.
      """
      config = self.GetMethodConfig('UpdateKajPolicyConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateKajPolicyConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/kajPolicyConfig',
        http_method='PATCH',
        method_id='cloudkms.organizations.updateKajPolicyConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='keyAccessJustificationsPolicyConfig',
        request_type_name='CloudkmsOrganizationsUpdateKajPolicyConfigRequest',
        response_type_name='KeyAccessJustificationsPolicyConfig',
        supports_download=False,
    )

  class ProjectsLocationsEkmConfigService(base_api.BaseApiService):
    """Service class for the projects_locations_ekmConfig resource."""

    _NAME = 'projects_locations_ekmConfig'

    def __init__(self, client):
      super(CloudkmsV1.ProjectsLocationsEkmConfigService, self).__init__(client)
      self._upload_configs = {
          }

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (CloudkmsProjectsLocationsEkmConfigGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/ekmConfig:getIamPolicy',
        http_method='GET',
        method_id='cloudkms.projects.locations.ekmConfig.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='CloudkmsProjectsLocationsEkmConfigGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (CloudkmsProjectsLocationsEkmConfigSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/ekmConfig:setIamPolicy',
        http_method='POST',
        method_id='cloudkms.projects.locations.ekmConfig.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='CloudkmsProjectsLocationsEkmConfigSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (CloudkmsProjectsLocationsEkmConfigTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/ekmConfig:testIamPermissions',
        http_method='POST',
        method_id='cloudkms.projects.locations.ekmConfig.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='CloudkmsProjectsLocationsEkmConfigTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsEkmConnectionsService(base_api.BaseApiService):
    """Service class for the projects_locations_ekmConnections resource."""

    _NAME = 'projects_locations_ekmConnections'

    def __init__(self, client):
      super(CloudkmsV1.ProjectsLocationsEkmConnectionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new EkmConnection in a given Project and Location.

      Args:
        request: (CloudkmsProjectsLocationsEkmConnectionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EkmConnection) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/ekmConnections',
        http_method='POST',
        method_id='cloudkms.projects.locations.ekmConnections.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['ekmConnectionId'],
        relative_path='v1/{+parent}/ekmConnections',
        request_field='ekmConnection',
        request_type_name='CloudkmsProjectsLocationsEkmConnectionsCreateRequest',
        response_type_name='EkmConnection',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns metadata for a given EkmConnection.

      Args:
        request: (CloudkmsProjectsLocationsEkmConnectionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EkmConnection) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/ekmConnections/{ekmConnectionsId}',
        http_method='GET',
        method_id='cloudkms.projects.locations.ekmConnections.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudkmsProjectsLocationsEkmConnectionsGetRequest',
        response_type_name='EkmConnection',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (CloudkmsProjectsLocationsEkmConnectionsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/ekmConnections/{ekmConnectionsId}:getIamPolicy',
        http_method='GET',
        method_id='cloudkms.projects.locations.ekmConnections.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='CloudkmsProjectsLocationsEkmConnectionsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists EkmConnections.

      Args:
        request: (CloudkmsProjectsLocationsEkmConnectionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEkmConnectionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/ekmConnections',
        http_method='GET',
        method_id='cloudkms.projects.locations.ekmConnections.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/ekmConnections',
        request_field='',
        request_type_name='CloudkmsProjectsLocationsEkmConnectionsListRequest',
        response_type_name='ListEkmConnectionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an EkmConnection's metadata.

      Args:
        request: (CloudkmsProjectsLocationsEkmConnectionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EkmConnection) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/ekmConnections/{ekmConnectionsId}',
        http_method='PATCH',
        method_id='cloudkms.projects.locations.ekmConnections.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='ekmConnection',
        request_type_name='CloudkmsProjectsLocationsEkmConnectionsPatchRequest',
        response_type_name='EkmConnection',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (CloudkmsProjectsLocationsEkmConnectionsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/ekmConnections/{ekmConnectionsId}:setIamPolicy',
        http_method='POST',
        method_id='cloudkms.projects.locations.ekmConnections.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='CloudkmsProjectsLocationsEkmConnectionsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (CloudkmsProjectsLocationsEkmConnectionsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/ekmConnections/{ekmConnectionsId}:testIamPermissions',
        http_method='POST',
        method_id='cloudkms.projects.locations.ekmConnections.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='CloudkmsProjectsLocationsEkmConnectionsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def VerifyConnectivity(self, request, global_params=None):
      r"""Verifies that Cloud KMS can successfully connect to the external key manager specified by an EkmConnection. If there is an error connecting to the EKM, this method returns a FAILED_PRECONDITION status containing structured information as described at https://cloud.google.com/kms/docs/reference/ekm_errors.

      Args:
        request: (CloudkmsProjectsLocationsEkmConnectionsVerifyConnectivityRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (VerifyConnectivityResponse) The response message.
      """
      config = self.GetMethodConfig('VerifyConnectivity')
      return self._RunMethod(
          config, request, global_params=global_params)

    VerifyConnectivity.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/ekmConnections/{ekmConnectionsId}:verifyConnectivity',
        http_method='GET',
        method_id='cloudkms.projects.locations.ekmConnections.verifyConnectivity',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:verifyConnectivity',
        request_field='',
        request_type_name='CloudkmsProjectsLocationsEkmConnectionsVerifyConnectivityRequest',
        response_type_name='VerifyConnectivityResponse',
        supports_download=False,
    )

  class ProjectsLocationsKeyHandlesService(base_api.BaseApiService):
    """Service class for the projects_locations_keyHandles resource."""

    _NAME = 'projects_locations_keyHandles'

    def __init__(self, client):
      super(CloudkmsV1.ProjectsLocationsKeyHandlesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new KeyHandle, triggering the provisioning of a new CryptoKey for CMEK use with the given resource type in the configured key project and the same location. GetOperation should be used to resolve the resulting long-running operation and get the resulting KeyHandle and CryptoKey.

      Args:
        request: (CloudkmsProjectsLocationsKeyHandlesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyHandles',
        http_method='POST',
        method_id='cloudkms.projects.locations.keyHandles.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['keyHandleId'],
        relative_path='v1/{+parent}/keyHandles',
        request_field='keyHandle',
        request_type_name='CloudkmsProjectsLocationsKeyHandlesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the KeyHandle.

      Args:
        request: (CloudkmsProjectsLocationsKeyHandlesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (KeyHandle) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyHandles/{keyHandlesId}',
        http_method='GET',
        method_id='cloudkms.projects.locations.keyHandles.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudkmsProjectsLocationsKeyHandlesGetRequest',
        response_type_name='KeyHandle',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists KeyHandles.

      Args:
        request: (CloudkmsProjectsLocationsKeyHandlesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListKeyHandlesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyHandles',
        http_method='GET',
        method_id='cloudkms.projects.locations.keyHandles.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/keyHandles',
        request_field='',
        request_type_name='CloudkmsProjectsLocationsKeyHandlesListRequest',
        response_type_name='ListKeyHandlesResponse',
        supports_download=False,
    )

  class ProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsService(base_api.BaseApiService):
    """Service class for the projects_locations_keyRings_cryptoKeys_cryptoKeyVersions resource."""

    _NAME = 'projects_locations_keyRings_cryptoKeys_cryptoKeyVersions'

    def __init__(self, client):
      super(CloudkmsV1.ProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsService, self).__init__(client)
      self._upload_configs = {
          }

    def AsymmetricDecrypt(self, request, global_params=None):
      r"""Decrypts data that was encrypted with a public key retrieved from GetPublicKey corresponding to a CryptoKeyVersion with CryptoKey.purpose ASYMMETRIC_DECRYPT.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsAsymmetricDecryptRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AsymmetricDecryptResponse) The response message.
      """
      config = self.GetMethodConfig('AsymmetricDecrypt')
      return self._RunMethod(
          config, request, global_params=global_params)

    AsymmetricDecrypt.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/cryptoKeys/{cryptoKeysId}/cryptoKeyVersions/{cryptoKeyVersionsId}:asymmetricDecrypt',
        http_method='POST',
        method_id='cloudkms.projects.locations.keyRings.cryptoKeys.cryptoKeyVersions.asymmetricDecrypt',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:asymmetricDecrypt',
        request_field='asymmetricDecryptRequest',
        request_type_name='CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsAsymmetricDecryptRequest',
        response_type_name='AsymmetricDecryptResponse',
        supports_download=False,
    )

    def AsymmetricSign(self, request, global_params=None):
      r"""Signs data using a CryptoKeyVersion with CryptoKey.purpose ASYMMETRIC_SIGN, producing a signature that can be verified with the public key retrieved from GetPublicKey.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsAsymmetricSignRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AsymmetricSignResponse) The response message.
      """
      config = self.GetMethodConfig('AsymmetricSign')
      return self._RunMethod(
          config, request, global_params=global_params)

    AsymmetricSign.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/cryptoKeys/{cryptoKeysId}/cryptoKeyVersions/{cryptoKeyVersionsId}:asymmetricSign',
        http_method='POST',
        method_id='cloudkms.projects.locations.keyRings.cryptoKeys.cryptoKeyVersions.asymmetricSign',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:asymmetricSign',
        request_field='asymmetricSignRequest',
        request_type_name='CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsAsymmetricSignRequest',
        response_type_name='AsymmetricSignResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Create a new CryptoKeyVersion in a CryptoKey. The server will assign the next sequential id. If unset, state will be set to ENABLED.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CryptoKeyVersion) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/cryptoKeys/{cryptoKeysId}/cryptoKeyVersions',
        http_method='POST',
        method_id='cloudkms.projects.locations.keyRings.cryptoKeys.cryptoKeyVersions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/cryptoKeyVersions',
        request_field='cryptoKeyVersion',
        request_type_name='CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsCreateRequest',
        response_type_name='CryptoKeyVersion',
        supports_download=False,
    )

    def Decapsulate(self, request, global_params=None):
      r"""Decapsulates data that was encapsulated with a public key retrieved from GetPublicKey corresponding to a CryptoKeyVersion with CryptoKey.purpose KEY_ENCAPSULATION.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsDecapsulateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DecapsulateResponse) The response message.
      """
      config = self.GetMethodConfig('Decapsulate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Decapsulate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/cryptoKeys/{cryptoKeysId}/cryptoKeyVersions/{cryptoKeyVersionsId}:decapsulate',
        http_method='POST',
        method_id='cloudkms.projects.locations.keyRings.cryptoKeys.cryptoKeyVersions.decapsulate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:decapsulate',
        request_field='decapsulateRequest',
        request_type_name='CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsDecapsulateRequest',
        response_type_name='DecapsulateResponse',
        supports_download=False,
    )

    def Destroy(self, request, global_params=None):
      r"""Schedule a CryptoKeyVersion for destruction. Upon calling this method, CryptoKeyVersion.state will be set to DESTROY_SCHEDULED, and destroy_time will be set to the time destroy_scheduled_duration in the future. At that time, the state will automatically change to DESTROYED, and the key material will be irrevocably destroyed. Before the destroy_time is reached, RestoreCryptoKeyVersion may be called to reverse the process.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsDestroyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CryptoKeyVersion) The response message.
      """
      config = self.GetMethodConfig('Destroy')
      return self._RunMethod(
          config, request, global_params=global_params)

    Destroy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/cryptoKeys/{cryptoKeysId}/cryptoKeyVersions/{cryptoKeyVersionsId}:destroy',
        http_method='POST',
        method_id='cloudkms.projects.locations.keyRings.cryptoKeys.cryptoKeyVersions.destroy',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:destroy',
        request_field='destroyCryptoKeyVersionRequest',
        request_type_name='CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsDestroyRequest',
        response_type_name='CryptoKeyVersion',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns metadata for a given CryptoKeyVersion.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CryptoKeyVersion) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/cryptoKeys/{cryptoKeysId}/cryptoKeyVersions/{cryptoKeyVersionsId}',
        http_method='GET',
        method_id='cloudkms.projects.locations.keyRings.cryptoKeys.cryptoKeyVersions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsGetRequest',
        response_type_name='CryptoKeyVersion',
        supports_download=False,
    )

    def GetPublicKey(self, request, global_params=None):
      r"""Returns the public key for the given CryptoKeyVersion. The CryptoKey.purpose must be ASYMMETRIC_SIGN or ASYMMETRIC_DECRYPT.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsGetPublicKeyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PublicKey) The response message.
      """
      config = self.GetMethodConfig('GetPublicKey')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetPublicKey.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/cryptoKeys/{cryptoKeysId}/cryptoKeyVersions/{cryptoKeyVersionsId}/publicKey',
        http_method='GET',
        method_id='cloudkms.projects.locations.keyRings.cryptoKeys.cryptoKeyVersions.getPublicKey',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['publicKeyFormat'],
        relative_path='v1/{+name}/publicKey',
        request_field='',
        request_type_name='CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsGetPublicKeyRequest',
        response_type_name='PublicKey',
        supports_download=False,
    )

    def Import(self, request, global_params=None):
      r"""Import wrapped key material into a CryptoKeyVersion. All requests must specify a CryptoKey. If a CryptoKeyVersion is additionally specified in the request, key material will be reimported into that version. Otherwise, a new version will be created, and will be assigned the next sequential id within the CryptoKey.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CryptoKeyVersion) The response message.
      """
      config = self.GetMethodConfig('Import')
      return self._RunMethod(
          config, request, global_params=global_params)

    Import.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/cryptoKeys/{cryptoKeysId}/cryptoKeyVersions:import',
        http_method='POST',
        method_id='cloudkms.projects.locations.keyRings.cryptoKeys.cryptoKeyVersions.import',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/cryptoKeyVersions:import',
        request_field='importCryptoKeyVersionRequest',
        request_type_name='CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsImportRequest',
        response_type_name='CryptoKeyVersion',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists CryptoKeyVersions.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListCryptoKeyVersionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/cryptoKeys/{cryptoKeysId}/cryptoKeyVersions',
        http_method='GET',
        method_id='cloudkms.projects.locations.keyRings.cryptoKeys.cryptoKeyVersions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'view'],
        relative_path='v1/{+parent}/cryptoKeyVersions',
        request_field='',
        request_type_name='CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsListRequest',
        response_type_name='ListCryptoKeyVersionsResponse',
        supports_download=False,
    )

    def MacSign(self, request, global_params=None):
      r"""Signs data using a CryptoKeyVersion with CryptoKey.purpose MAC, producing a tag that can be verified by another source with the same key.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsMacSignRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MacSignResponse) The response message.
      """
      config = self.GetMethodConfig('MacSign')
      return self._RunMethod(
          config, request, global_params=global_params)

    MacSign.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/cryptoKeys/{cryptoKeysId}/cryptoKeyVersions/{cryptoKeyVersionsId}:macSign',
        http_method='POST',
        method_id='cloudkms.projects.locations.keyRings.cryptoKeys.cryptoKeyVersions.macSign',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:macSign',
        request_field='macSignRequest',
        request_type_name='CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsMacSignRequest',
        response_type_name='MacSignResponse',
        supports_download=False,
    )

    def MacVerify(self, request, global_params=None):
      r"""Verifies MAC tag using a CryptoKeyVersion with CryptoKey.purpose MAC, and returns a response that indicates whether or not the verification was successful.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsMacVerifyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MacVerifyResponse) The response message.
      """
      config = self.GetMethodConfig('MacVerify')
      return self._RunMethod(
          config, request, global_params=global_params)

    MacVerify.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/cryptoKeys/{cryptoKeysId}/cryptoKeyVersions/{cryptoKeyVersionsId}:macVerify',
        http_method='POST',
        method_id='cloudkms.projects.locations.keyRings.cryptoKeys.cryptoKeyVersions.macVerify',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:macVerify',
        request_field='macVerifyRequest',
        request_type_name='CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsMacVerifyRequest',
        response_type_name='MacVerifyResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a CryptoKeyVersion's metadata. state may be changed between ENABLED and DISABLED using this method. See DestroyCryptoKeyVersion and RestoreCryptoKeyVersion to move between other states.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CryptoKeyVersion) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/cryptoKeys/{cryptoKeysId}/cryptoKeyVersions/{cryptoKeyVersionsId}',
        http_method='PATCH',
        method_id='cloudkms.projects.locations.keyRings.cryptoKeys.cryptoKeyVersions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='cryptoKeyVersion',
        request_type_name='CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsPatchRequest',
        response_type_name='CryptoKeyVersion',
        supports_download=False,
    )

    def RawDecrypt(self, request, global_params=None):
      r"""Decrypts data that was originally encrypted using a raw cryptographic mechanism. The CryptoKey.purpose must be RAW_ENCRYPT_DECRYPT.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsRawDecryptRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RawDecryptResponse) The response message.
      """
      config = self.GetMethodConfig('RawDecrypt')
      return self._RunMethod(
          config, request, global_params=global_params)

    RawDecrypt.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/cryptoKeys/{cryptoKeysId}/cryptoKeyVersions/{cryptoKeyVersionsId}:rawDecrypt',
        http_method='POST',
        method_id='cloudkms.projects.locations.keyRings.cryptoKeys.cryptoKeyVersions.rawDecrypt',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:rawDecrypt',
        request_field='rawDecryptRequest',
        request_type_name='CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsRawDecryptRequest',
        response_type_name='RawDecryptResponse',
        supports_download=False,
    )

    def RawEncrypt(self, request, global_params=None):
      r"""Encrypts data using portable cryptographic primitives. Most users should choose Encrypt and Decrypt rather than their raw counterparts. The CryptoKey.purpose must be RAW_ENCRYPT_DECRYPT.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsRawEncryptRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RawEncryptResponse) The response message.
      """
      config = self.GetMethodConfig('RawEncrypt')
      return self._RunMethod(
          config, request, global_params=global_params)

    RawEncrypt.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/cryptoKeys/{cryptoKeysId}/cryptoKeyVersions/{cryptoKeyVersionsId}:rawEncrypt',
        http_method='POST',
        method_id='cloudkms.projects.locations.keyRings.cryptoKeys.cryptoKeyVersions.rawEncrypt',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:rawEncrypt',
        request_field='rawEncryptRequest',
        request_type_name='CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsRawEncryptRequest',
        response_type_name='RawEncryptResponse',
        supports_download=False,
    )

    def Restore(self, request, global_params=None):
      r"""Restore a CryptoKeyVersion in the DESTROY_SCHEDULED state. Upon restoration of the CryptoKeyVersion, state will be set to DISABLED, and destroy_time will be cleared.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsRestoreRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CryptoKeyVersion) The response message.
      """
      config = self.GetMethodConfig('Restore')
      return self._RunMethod(
          config, request, global_params=global_params)

    Restore.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/cryptoKeys/{cryptoKeysId}/cryptoKeyVersions/{cryptoKeyVersionsId}:restore',
        http_method='POST',
        method_id='cloudkms.projects.locations.keyRings.cryptoKeys.cryptoKeyVersions.restore',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:restore',
        request_field='restoreCryptoKeyVersionRequest',
        request_type_name='CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsRestoreRequest',
        response_type_name='CryptoKeyVersion',
        supports_download=False,
    )

  class ProjectsLocationsKeyRingsCryptoKeysService(base_api.BaseApiService):
    """Service class for the projects_locations_keyRings_cryptoKeys resource."""

    _NAME = 'projects_locations_keyRings_cryptoKeys'

    def __init__(self, client):
      super(CloudkmsV1.ProjectsLocationsKeyRingsCryptoKeysService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new CryptoKey within a KeyRing. CryptoKey.purpose and CryptoKey.version_template.algorithm are required.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsCryptoKeysCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CryptoKey) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/cryptoKeys',
        http_method='POST',
        method_id='cloudkms.projects.locations.keyRings.cryptoKeys.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['cryptoKeyId', 'skipInitialVersionCreation'],
        relative_path='v1/{+parent}/cryptoKeys',
        request_field='cryptoKey',
        request_type_name='CloudkmsProjectsLocationsKeyRingsCryptoKeysCreateRequest',
        response_type_name='CryptoKey',
        supports_download=False,
    )

    def Decrypt(self, request, global_params=None):
      r"""Decrypts data that was protected by Encrypt. The CryptoKey.purpose must be ENCRYPT_DECRYPT.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsCryptoKeysDecryptRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DecryptResponse) The response message.
      """
      config = self.GetMethodConfig('Decrypt')
      return self._RunMethod(
          config, request, global_params=global_params)

    Decrypt.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/cryptoKeys/{cryptoKeysId}:decrypt',
        http_method='POST',
        method_id='cloudkms.projects.locations.keyRings.cryptoKeys.decrypt',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:decrypt',
        request_field='decryptRequest',
        request_type_name='CloudkmsProjectsLocationsKeyRingsCryptoKeysDecryptRequest',
        response_type_name='DecryptResponse',
        supports_download=False,
    )

    def Encrypt(self, request, global_params=None):
      r"""Encrypts data, so that it can only be recovered by a call to Decrypt. The CryptoKey.purpose must be ENCRYPT_DECRYPT.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsCryptoKeysEncryptRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EncryptResponse) The response message.
      """
      config = self.GetMethodConfig('Encrypt')
      return self._RunMethod(
          config, request, global_params=global_params)

    Encrypt.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/cryptoKeys/{cryptoKeysId}:encrypt',
        http_method='POST',
        method_id='cloudkms.projects.locations.keyRings.cryptoKeys.encrypt',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:encrypt',
        request_field='encryptRequest',
        request_type_name='CloudkmsProjectsLocationsKeyRingsCryptoKeysEncryptRequest',
        response_type_name='EncryptResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns metadata for a given CryptoKey, as well as its primary CryptoKeyVersion.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsCryptoKeysGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CryptoKey) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/cryptoKeys/{cryptoKeysId}',
        http_method='GET',
        method_id='cloudkms.projects.locations.keyRings.cryptoKeys.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudkmsProjectsLocationsKeyRingsCryptoKeysGetRequest',
        response_type_name='CryptoKey',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsCryptoKeysGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/cryptoKeys/{cryptoKeysId}:getIamPolicy',
        http_method='GET',
        method_id='cloudkms.projects.locations.keyRings.cryptoKeys.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='CloudkmsProjectsLocationsKeyRingsCryptoKeysGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists CryptoKeys.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsCryptoKeysListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListCryptoKeysResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/cryptoKeys',
        http_method='GET',
        method_id='cloudkms.projects.locations.keyRings.cryptoKeys.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'versionView'],
        relative_path='v1/{+parent}/cryptoKeys',
        request_field='',
        request_type_name='CloudkmsProjectsLocationsKeyRingsCryptoKeysListRequest',
        response_type_name='ListCryptoKeysResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a CryptoKey.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsCryptoKeysPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CryptoKey) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/cryptoKeys/{cryptoKeysId}',
        http_method='PATCH',
        method_id='cloudkms.projects.locations.keyRings.cryptoKeys.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='cryptoKey',
        request_type_name='CloudkmsProjectsLocationsKeyRingsCryptoKeysPatchRequest',
        response_type_name='CryptoKey',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsCryptoKeysSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/cryptoKeys/{cryptoKeysId}:setIamPolicy',
        http_method='POST',
        method_id='cloudkms.projects.locations.keyRings.cryptoKeys.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='CloudkmsProjectsLocationsKeyRingsCryptoKeysSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsCryptoKeysTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/cryptoKeys/{cryptoKeysId}:testIamPermissions',
        http_method='POST',
        method_id='cloudkms.projects.locations.keyRings.cryptoKeys.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='CloudkmsProjectsLocationsKeyRingsCryptoKeysTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def UpdatePrimaryVersion(self, request, global_params=None):
      r"""Update the version of a CryptoKey that will be used in Encrypt. Returns an error if called on a key whose purpose is not ENCRYPT_DECRYPT.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsCryptoKeysUpdatePrimaryVersionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CryptoKey) The response message.
      """
      config = self.GetMethodConfig('UpdatePrimaryVersion')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdatePrimaryVersion.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/cryptoKeys/{cryptoKeysId}:updatePrimaryVersion',
        http_method='POST',
        method_id='cloudkms.projects.locations.keyRings.cryptoKeys.updatePrimaryVersion',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:updatePrimaryVersion',
        request_field='updateCryptoKeyPrimaryVersionRequest',
        request_type_name='CloudkmsProjectsLocationsKeyRingsCryptoKeysUpdatePrimaryVersionRequest',
        response_type_name='CryptoKey',
        supports_download=False,
    )

  class ProjectsLocationsKeyRingsImportJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_keyRings_importJobs resource."""

    _NAME = 'projects_locations_keyRings_importJobs'

    def __init__(self, client):
      super(CloudkmsV1.ProjectsLocationsKeyRingsImportJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new ImportJob within a KeyRing. ImportJob.import_method is required.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsImportJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ImportJob) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/importJobs',
        http_method='POST',
        method_id='cloudkms.projects.locations.keyRings.importJobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['importJobId'],
        relative_path='v1/{+parent}/importJobs',
        request_field='importJob',
        request_type_name='CloudkmsProjectsLocationsKeyRingsImportJobsCreateRequest',
        response_type_name='ImportJob',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns metadata for a given ImportJob.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsImportJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ImportJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/importJobs/{importJobsId}',
        http_method='GET',
        method_id='cloudkms.projects.locations.keyRings.importJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudkmsProjectsLocationsKeyRingsImportJobsGetRequest',
        response_type_name='ImportJob',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsImportJobsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/importJobs/{importJobsId}:getIamPolicy',
        http_method='GET',
        method_id='cloudkms.projects.locations.keyRings.importJobs.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='CloudkmsProjectsLocationsKeyRingsImportJobsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ImportJobs.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsImportJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListImportJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/importJobs',
        http_method='GET',
        method_id='cloudkms.projects.locations.keyRings.importJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/importJobs',
        request_field='',
        request_type_name='CloudkmsProjectsLocationsKeyRingsImportJobsListRequest',
        response_type_name='ListImportJobsResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsImportJobsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/importJobs/{importJobsId}:setIamPolicy',
        http_method='POST',
        method_id='cloudkms.projects.locations.keyRings.importJobs.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='CloudkmsProjectsLocationsKeyRingsImportJobsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsImportJobsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}/importJobs/{importJobsId}:testIamPermissions',
        http_method='POST',
        method_id='cloudkms.projects.locations.keyRings.importJobs.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='CloudkmsProjectsLocationsKeyRingsImportJobsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsKeyRingsService(base_api.BaseApiService):
    """Service class for the projects_locations_keyRings resource."""

    _NAME = 'projects_locations_keyRings'

    def __init__(self, client):
      super(CloudkmsV1.ProjectsLocationsKeyRingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new KeyRing in a given Project and Location.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (KeyRing) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings',
        http_method='POST',
        method_id='cloudkms.projects.locations.keyRings.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['keyRingId'],
        relative_path='v1/{+parent}/keyRings',
        request_field='keyRing',
        request_type_name='CloudkmsProjectsLocationsKeyRingsCreateRequest',
        response_type_name='KeyRing',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns metadata for a given KeyRing.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (KeyRing) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}',
        http_method='GET',
        method_id='cloudkms.projects.locations.keyRings.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudkmsProjectsLocationsKeyRingsGetRequest',
        response_type_name='KeyRing',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}:getIamPolicy',
        http_method='GET',
        method_id='cloudkms.projects.locations.keyRings.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='CloudkmsProjectsLocationsKeyRingsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists KeyRings.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListKeyRingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings',
        http_method='GET',
        method_id='cloudkms.projects.locations.keyRings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/keyRings',
        request_field='',
        request_type_name='CloudkmsProjectsLocationsKeyRingsListRequest',
        response_type_name='ListKeyRingsResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}:setIamPolicy',
        http_method='POST',
        method_id='cloudkms.projects.locations.keyRings.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='CloudkmsProjectsLocationsKeyRingsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (CloudkmsProjectsLocationsKeyRingsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/keyRings/{keyRingsId}:testIamPermissions',
        http_method='POST',
        method_id='cloudkms.projects.locations.keyRings.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='CloudkmsProjectsLocationsKeyRingsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(CloudkmsV1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (CloudkmsProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='cloudkms.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudkmsProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(CloudkmsV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def GenerateRandomBytes(self, request, global_params=None):
      r"""Generate random bytes using the Cloud KMS randomness source in the provided location.

      Args:
        request: (CloudkmsProjectsLocationsGenerateRandomBytesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GenerateRandomBytesResponse) The response message.
      """
      config = self.GetMethodConfig('GenerateRandomBytes')
      return self._RunMethod(
          config, request, global_params=global_params)

    GenerateRandomBytes.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}:generateRandomBytes',
        http_method='POST',
        method_id='cloudkms.projects.locations.generateRandomBytes',
        ordered_params=['location'],
        path_params=['location'],
        query_params=[],
        relative_path='v1/{+location}:generateRandomBytes',
        request_field='generateRandomBytesRequest',
        request_type_name='CloudkmsProjectsLocationsGenerateRandomBytesRequest',
        response_type_name='GenerateRandomBytesResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (CloudkmsProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='cloudkms.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudkmsProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def GetEkmConfig(self, request, global_params=None):
      r"""Returns the EkmConfig singleton resource for a given project and location.

      Args:
        request: (CloudkmsProjectsLocationsGetEkmConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EkmConfig) The response message.
      """
      config = self.GetMethodConfig('GetEkmConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetEkmConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/ekmConfig',
        http_method='GET',
        method_id='cloudkms.projects.locations.getEkmConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudkmsProjectsLocationsGetEkmConfigRequest',
        response_type_name='EkmConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (CloudkmsProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='cloudkms.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/locations',
        request_field='',
        request_type_name='CloudkmsProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

    def UpdateEkmConfig(self, request, global_params=None):
      r"""Updates the EkmConfig singleton resource for a given project and location.

      Args:
        request: (CloudkmsProjectsLocationsUpdateEkmConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EkmConfig) The response message.
      """
      config = self.GetMethodConfig('UpdateEkmConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateEkmConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/ekmConfig',
        http_method='PATCH',
        method_id='cloudkms.projects.locations.updateEkmConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='ekmConfig',
        request_type_name='CloudkmsProjectsLocationsUpdateEkmConfigRequest',
        response_type_name='EkmConfig',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(CloudkmsV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

    def GetKajPolicyConfig(self, request, global_params=None):
      r"""Gets the KeyAccessJustificationsPolicyConfig for a given organization/folder/projects.

      Args:
        request: (CloudkmsProjectsGetKajPolicyConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (KeyAccessJustificationsPolicyConfig) The response message.
      """
      config = self.GetMethodConfig('GetKajPolicyConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetKajPolicyConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/kajPolicyConfig',
        http_method='GET',
        method_id='cloudkms.projects.getKajPolicyConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='CloudkmsProjectsGetKajPolicyConfigRequest',
        response_type_name='KeyAccessJustificationsPolicyConfig',
        supports_download=False,
    )

    def ShowEffectiveAutokeyConfig(self, request, global_params=None):
      r"""Returns the effective Cloud KMS Autokey configuration for a given project.

      Args:
        request: (CloudkmsProjectsShowEffectiveAutokeyConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ShowEffectiveAutokeyConfigResponse) The response message.
      """
      config = self.GetMethodConfig('ShowEffectiveAutokeyConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    ShowEffectiveAutokeyConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}:showEffectiveAutokeyConfig',
        http_method='GET',
        method_id='cloudkms.projects.showEffectiveAutokeyConfig',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}:showEffectiveAutokeyConfig',
        request_field='',
        request_type_name='CloudkmsProjectsShowEffectiveAutokeyConfigRequest',
        response_type_name='ShowEffectiveAutokeyConfigResponse',
        supports_download=False,
    )

    def ShowEffectiveKeyAccessJustificationsEnrollmentConfig(self, request, global_params=None):
      r"""Returns the KeyAccessJustificationsEnrollmentConfig of the resource closest to the given project in hierarchy.

      Args:
        request: (CloudkmsProjectsShowEffectiveKeyAccessJustificationsEnrollmentConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ShowEffectiveKeyAccessJustificationsEnrollmentConfigResponse) The response message.
      """
      config = self.GetMethodConfig('ShowEffectiveKeyAccessJustificationsEnrollmentConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    ShowEffectiveKeyAccessJustificationsEnrollmentConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}:showEffectiveKeyAccessJustificationsEnrollmentConfig',
        http_method='GET',
        method_id='cloudkms.projects.showEffectiveKeyAccessJustificationsEnrollmentConfig',
        ordered_params=['project'],
        path_params=['project'],
        query_params=[],
        relative_path='v1/{+project}:showEffectiveKeyAccessJustificationsEnrollmentConfig',
        request_field='',
        request_type_name='CloudkmsProjectsShowEffectiveKeyAccessJustificationsEnrollmentConfigRequest',
        response_type_name='ShowEffectiveKeyAccessJustificationsEnrollmentConfigResponse',
        supports_download=False,
    )

    def ShowEffectiveKeyAccessJustificationsPolicyConfig(self, request, global_params=None):
      r"""Returns the KeyAccessJustificationsPolicyConfig of the resource closest to the given project in hierarchy.

      Args:
        request: (CloudkmsProjectsShowEffectiveKeyAccessJustificationsPolicyConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ShowEffectiveKeyAccessJustificationsPolicyConfigResponse) The response message.
      """
      config = self.GetMethodConfig('ShowEffectiveKeyAccessJustificationsPolicyConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    ShowEffectiveKeyAccessJustificationsPolicyConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}:showEffectiveKeyAccessJustificationsPolicyConfig',
        http_method='GET',
        method_id='cloudkms.projects.showEffectiveKeyAccessJustificationsPolicyConfig',
        ordered_params=['project'],
        path_params=['project'],
        query_params=[],
        relative_path='v1/{+project}:showEffectiveKeyAccessJustificationsPolicyConfig',
        request_field='',
        request_type_name='CloudkmsProjectsShowEffectiveKeyAccessJustificationsPolicyConfigRequest',
        response_type_name='ShowEffectiveKeyAccessJustificationsPolicyConfigResponse',
        supports_download=False,
    )

    def UpdateKajPolicyConfig(self, request, global_params=None):
      r"""Updates the KeyAccessJustificationsPolicyConfig for a given organization/folder/projects.

      Args:
        request: (CloudkmsProjectsUpdateKajPolicyConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (KeyAccessJustificationsPolicyConfig) The response message.
      """
      config = self.GetMethodConfig('UpdateKajPolicyConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateKajPolicyConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/kajPolicyConfig',
        http_method='PATCH',
        method_id='cloudkms.projects.updateKajPolicyConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='keyAccessJustificationsPolicyConfig',
        request_type_name='CloudkmsProjectsUpdateKajPolicyConfigRequest',
        response_type_name='KeyAccessJustificationsPolicyConfig',
        supports_download=False,
    )
