"""Generated client library for transferappliance version v1alpha1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.transferappliance.v1alpha1 import transferappliance_v1alpha1_messages as messages


class TransferapplianceV1alpha1(base_api.BaseApiClient):
  """Generated client library for service transferappliance version v1alpha1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://transferappliance.googleapis.com/'
  MTLS_BASE_URL = 'https://transferappliance.mtls.googleapis.com/'

  _PACKAGE = 'transferappliance'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1alpha1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'TransferapplianceV1alpha1'
  _URL_VERSION = 'v1alpha1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new transferappliance handle."""
    url = url or self.BASE_URL
    super(TransferapplianceV1alpha1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_appliances_credentials = self.ProjectsLocationsAppliancesCredentialsService(self)
    self.projects_locations_appliances = self.ProjectsLocationsAppliancesService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_orders = self.ProjectsLocationsOrdersService(self)
    self.projects_locations_savedAddresses = self.ProjectsLocationsSavedAddressesService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsAppliancesCredentialsService(base_api.BaseApiService):
    """Service class for the projects_locations_appliances_credentials resource."""

    _NAME = 'projects_locations_appliances_credentials'

    def __init__(self, client):
      super(TransferapplianceV1alpha1.ProjectsLocationsAppliancesCredentialsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets Credentials of the appliance.

      Args:
        request: (TransferapplianceProjectsLocationsAppliancesCredentialsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Credential) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/appliances/{appliancesId}/credentials/{credentialsId}',
        http_method='GET',
        method_id='transferappliance.projects.locations.appliances.credentials.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='TransferapplianceProjectsLocationsAppliancesCredentialsGetRequest',
        response_type_name='Credential',
        supports_download=False,
    )

  class ProjectsLocationsAppliancesService(base_api.BaseApiService):
    """Service class for the projects_locations_appliances resource."""

    _NAME = 'projects_locations_appliances'

    def __init__(self, client):
      super(TransferapplianceV1alpha1.ProjectsLocationsAppliancesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Appliance in a given project and location.

      Args:
        request: (TransferapplianceProjectsLocationsAppliancesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/appliances',
        http_method='POST',
        method_id='transferappliance.projects.locations.appliances.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['applianceId', 'requestId', 'validateOnly'],
        relative_path='v1alpha1/{+parent}/appliances',
        request_field='appliance',
        request_type_name='TransferapplianceProjectsLocationsAppliancesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an Appliance.

      Args:
        request: (TransferapplianceProjectsLocationsAppliancesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/appliances/{appliancesId}',
        http_method='DELETE',
        method_id='transferappliance.projects.locations.appliances.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId', 'validateOnly'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='TransferapplianceProjectsLocationsAppliancesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Export(self, request, global_params=None):
      r"""Gets user data by its resource name.

      Args:
        request: (TransferapplianceProjectsLocationsAppliancesExportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ExportApplianceUserDataResponse) The response message.
      """
      config = self.GetMethodConfig('Export')
      return self._RunMethod(
          config, request, global_params=global_params)

    Export.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/appliances/{appliancesId}:export',
        http_method='GET',
        method_id='transferappliance.projects.locations.appliances.export',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:export',
        request_field='',
        request_type_name='TransferapplianceProjectsLocationsAppliancesExportRequest',
        response_type_name='ExportApplianceUserDataResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an Appliance resource by its resource name.

      Args:
        request: (TransferapplianceProjectsLocationsAppliancesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Appliance) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/appliances/{appliancesId}',
        http_method='GET',
        method_id='transferappliance.projects.locations.appliances.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='TransferapplianceProjectsLocationsAppliancesGetRequest',
        response_type_name='Appliance',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Appliances in a given project and location.

      Args:
        request: (TransferapplianceProjectsLocationsAppliancesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAppliancesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/appliances',
        http_method='GET',
        method_id='transferappliance.projects.locations.appliances.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/appliances',
        request_field='',
        request_type_name='TransferapplianceProjectsLocationsAppliancesListRequest',
        response_type_name='ListAppliancesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates Appliance resource parameters.

      Args:
        request: (TransferapplianceProjectsLocationsAppliancesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/appliances/{appliancesId}',
        http_method='PATCH',
        method_id='transferappliance.projects.locations.appliances.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'requestId', 'updateMask', 'validateOnly'],
        relative_path='v1alpha1/{+name}',
        request_field='appliance',
        request_type_name='TransferapplianceProjectsLocationsAppliancesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(TransferapplianceV1alpha1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (TransferapplianceProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='transferappliance.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='TransferapplianceProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (TransferapplianceProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='transferappliance.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='TransferapplianceProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (TransferapplianceProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='transferappliance.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='TransferapplianceProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (TransferapplianceProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='transferappliance.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='TransferapplianceProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsOrdersService(base_api.BaseApiService):
    """Service class for the projects_locations_orders resource."""

    _NAME = 'projects_locations_orders'

    def __init__(self, client):
      super(TransferapplianceV1alpha1.ProjectsLocationsOrdersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Order in a given project and location.

      Args:
        request: (TransferapplianceProjectsLocationsOrdersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orders',
        http_method='POST',
        method_id='transferappliance.projects.locations.orders.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['orderId', 'requestId', 'validateOnly'],
        relative_path='v1alpha1/{+parent}/orders',
        request_field='order',
        request_type_name='TransferapplianceProjectsLocationsOrdersCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Order.

      Args:
        request: (TransferapplianceProjectsLocationsOrdersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orders/{ordersId}',
        http_method='DELETE',
        method_id='transferappliance.projects.locations.orders.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId', 'validateOnly'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='TransferapplianceProjectsLocationsOrdersDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Order.

      Args:
        request: (TransferapplianceProjectsLocationsOrdersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Order) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orders/{ordersId}',
        http_method='GET',
        method_id='transferappliance.projects.locations.orders.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='TransferapplianceProjectsLocationsOrdersGetRequest',
        response_type_name='Order',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Orders in a given project and location.

      Args:
        request: (TransferapplianceProjectsLocationsOrdersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOrdersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orders',
        http_method='GET',
        method_id='transferappliance.projects.locations.orders.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/orders',
        request_field='',
        request_type_name='TransferapplianceProjectsLocationsOrdersListRequest',
        response_type_name='ListOrdersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Order.

      Args:
        request: (TransferapplianceProjectsLocationsOrdersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orders/{ordersId}',
        http_method='PATCH',
        method_id='transferappliance.projects.locations.orders.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'requestId', 'updateMask', 'validateOnly'],
        relative_path='v1alpha1/{+name}',
        request_field='order',
        request_type_name='TransferapplianceProjectsLocationsOrdersPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Submit(self, request, global_params=None):
      r"""Submit an Order, moving it from the DRAFT state to PREPARING and updating any appliances associated with the order by moving them from the DRAFT state to ACTIVE. This method will attempt to set and validate any required permissions for a workload's service accounts on the workload's resources (e.g. KMS key, Cloud Storage bucket) for all appliances associated with the order. The caller must have the appropriate permissions to manage permissions for these resources.

      Args:
        request: (TransferapplianceProjectsLocationsOrdersSubmitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Submit')
      return self._RunMethod(
          config, request, global_params=global_params)

    Submit.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orders/{ordersId}:submit',
        http_method='POST',
        method_id='transferappliance.projects.locations.orders.submit',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:submit',
        request_field='submitOrderRequest',
        request_type_name='TransferapplianceProjectsLocationsOrdersSubmitRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsSavedAddressesService(base_api.BaseApiService):
    """Service class for the projects_locations_savedAddresses resource."""

    _NAME = 'projects_locations_savedAddresses'

    def __init__(self, client):
      super(TransferapplianceV1alpha1.ProjectsLocationsSavedAddressesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new SavedAddress in a given project and location.

      Args:
        request: (TransferapplianceProjectsLocationsSavedAddressesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/savedAddresses',
        http_method='POST',
        method_id='transferappliance.projects.locations.savedAddresses.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'savedAddressId', 'validateOnly'],
        relative_path='v1alpha1/{+parent}/savedAddresses',
        request_field='savedAddress',
        request_type_name='TransferapplianceProjectsLocationsSavedAddressesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single SavedAddress.

      Args:
        request: (TransferapplianceProjectsLocationsSavedAddressesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/savedAddresses/{savedAddressesId}',
        http_method='DELETE',
        method_id='transferappliance.projects.locations.savedAddresses.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId', 'validateOnly'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='TransferapplianceProjectsLocationsSavedAddressesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single SavedAddress.

      Args:
        request: (TransferapplianceProjectsLocationsSavedAddressesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SavedAddress) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/savedAddresses/{savedAddressesId}',
        http_method='GET',
        method_id='transferappliance.projects.locations.savedAddresses.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='TransferapplianceProjectsLocationsSavedAddressesGetRequest',
        response_type_name='SavedAddress',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists SavedAddresses in a given project and location.

      Args:
        request: (TransferapplianceProjectsLocationsSavedAddressesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSavedAddressesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/savedAddresses',
        http_method='GET',
        method_id='transferappliance.projects.locations.savedAddresses.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/savedAddresses',
        request_field='',
        request_type_name='TransferapplianceProjectsLocationsSavedAddressesListRequest',
        response_type_name='ListSavedAddressesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single SavedAddress.

      Args:
        request: (TransferapplianceProjectsLocationsSavedAddressesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/savedAddresses/{savedAddressesId}',
        http_method='PATCH',
        method_id='transferappliance.projects.locations.savedAddresses.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'requestId', 'updateMask', 'validateOnly'],
        relative_path='v1alpha1/{+name}',
        request_field='savedAddress',
        request_type_name='TransferapplianceProjectsLocationsSavedAddressesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(TransferapplianceV1alpha1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (TransferapplianceProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='transferappliance.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='TransferapplianceProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (TransferapplianceProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='transferappliance.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/locations',
        request_field='',
        request_type_name='TransferapplianceProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(TransferapplianceV1alpha1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
