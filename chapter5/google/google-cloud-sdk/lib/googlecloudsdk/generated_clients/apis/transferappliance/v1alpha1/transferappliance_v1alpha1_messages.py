"""Generated message classes for transferappliance version v1alpha1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'transferappliance'


class Appliance(_messages.Message):
  r"""Message describing a physical Appliance. NextID : 112

  Enums:
    ModelValueValuesEnum: The hardware form factor of the appliance.
    StateValueValuesEnum: Output only. The state of the physical appliance.
    VerificationStateValueValuesEnum: Output only. Indicates whether appliance
      has been verified by Google.

  Messages:
    AnnotationsValue: User annotations. See
      https://google.aip.dev/128#annotations The key must be between 0 and 63
      characters long. The value must be between 0 and 63 characters long.
    LabelsValue: Labels as key value pairs. Labels must meet the following
      constraints: * Keys and values can contain only lowercase letters,
      numeric characters, underscores, and dashes. * All characters must use
      UTF-8 encoding, and international characters are allowed. * Keys must
      start with a lowercase letter or international character. * Each
      resource is limited to a maximum of 64 labels. * Label keys must be
      between 1 and 63 characters long. * Label values must be between 0 and
      63 characters long. * Keys and values can contain only lowercase
      letters, numeric characters, underscores, and dashes.

  Fields:
    annotations: User annotations. See https://google.aip.dev/128#annotations
      The key must be between 0 and 63 characters long. The value must be
      between 0 and 63 characters long.
    applianceServiceAccount: Optional. Appliance service account used for
      accessing customer resources. Follow the steps mentioned here (Online
      transfer with transfer appliance :- https://cloud.google.com/transfer-
      appliance/docs/4.0/prepare-online#user_sa, Edge appliance :-
      https://cloud.google.com/distributed-cloud/edge-
      appliance/docs/configure-cloud#appliance_sa) before setting this field.
    atSecureCustomerLocation: Optional. Indicate whether the appliance is in
      the possession of the customer. Once the customer confirms the appliance
      is in hand, this field will be updated to true; and this field will be
      changed to false once the appliance is not being used by the customer.
    attestationPasscode: Optional. Attestation passcode of the appliance. If
      the customer runs offline attestation on the appliance, update this
      field with attestation_passcode retrieved on successful attestation. The
      field can only be updated to correct attestation_passcode. If the
      provided attestation passcode is wrong, the API will return an error.
    createTime: Output only. Create time.
    customerManagedKey: The resource name of the customer-managed KMS key used
      to encrypt data on the appliance. If none is set, a Google-managed key
      will be used. The name looks like
      "projects//locations//keyRings//cryptoKeys//cryptoKeyVersions/".
    customerRevokedAccess: Optional. If true, VerificationState is
      BLOCKED_BY_CUSTOMER or BLOCKED_BY_CUSTOMER_AND_GOOGLE. This field is
      used to block and unblock the appliance by customer. To block the
      appliance use the UpdateAppliance method with
      customer_revoked_access:true. To unblock the appliance use the
      UpdateAppliance method with customer_revoked_access:false. IMPORTANT: To
      unblock the appliance don't use empty update_mask in UpdateAppliance
      request since merge of fields happens in empty updatemask. Use instead
      wildcard * or nonempty updatemask with customer_revoked_access field.
    deleteTime: Output only. Delete time.
    displayName: A mutable, user-settable name for the resource. It does not
      need to be unique and should be less than 64 characters.
    etag: Strongly validated etag, computed by the server based on the value
      of other fields, and may be sent on update and delete requests to ensure
      the client has an up-to-date value before proceeding. See
      https://google.aip.dev/154.
    finalizationCode: The code displayed when finalizing the appliance. The
      return_label_uri in the shipment_info field will not be available until
      this has been set.
    internetEnabled: Optional. Whether the appliance can communicate with GCP
      for purposes like online transfer or monitoring.
    kubernetesFeature: Optional. The Kubernetes configuration for this
      appliance (only valid if it is Edge Appliance).
    labels: Labels as key value pairs. Labels must meet the following
      constraints: * Keys and values can contain only lowercase letters,
      numeric characters, underscores, and dashes. * All characters must use
      UTF-8 encoding, and international characters are allowed. * Keys must
      start with a lowercase letter or international character. * Each
      resource is limited to a maximum of 64 labels. * Label keys must be
      between 1 and 63 characters long. * Label values must be between 0 and
      63 characters long. * Keys and values can contain only lowercase
      letters, numeric characters, underscores, and dashes.
    lastVerified: Output only. Indicates when appliance was last verified.
    maintenanceInfo: Output only. Details of the software updates for the
      appliance.
    maintenancePolicy: Optional. Maintenance policy that needs to be attached
      to the transfer appliance. Customers can configure weekly windows when
      their appliance can receive disruptive updates, periods of time during
      which they want to block all updates and relative order in which their
      appliances can receive updates.
    model: The hardware form factor of the appliance.
    name: Name of resource.
    offlineExportFeature: Optional. Export configuration for offline
      appliance.
    offlineImportFeature: The offline import configuration for this appliance.
    onlineEnabled: Optional. Whether the appliance can communicate with GCP
      for purposes like online transfer or monitoring.
    onlineImportFeature: The online import configuration for this appliance.
    order: Output only. The order associated with the appliance. Format:
      projects/{project}/locations/{location}/orders/{order}
    pickupInfo: Optional. Details of pickup information for the appliance's
      return.
    projectSetupCompleted: Optional. Project setup completed is set to true
      after the customer successfully sets up the project using the Cloud
      Setup app or the Pantheon widget.
    reconciling: Output only. Reconciling
      (https://google.aip.dev/128#reconciliation). Set to true if the current
      state of Appliance does not match the user's intended state, and the
      service is actively updating the resource to reconcile them. This can
      happen due to user-triggered updates or system actions like failover or
      maintenance.
    serialNumber: Output only. Serial Number uniquely identifies a physical
      appliance.
    sessionId: Output only. session_id is a 16 character Unique identifier for
      the session, computed when the resource is created. Example:
      US-a123-b2cd.
    shipmentInfo: Details of the appliance's shipment.
    state: Output only. The state of the physical appliance.
    stateChangeTime: Output only. Record the time of latest appliance state
      change.
    subscriptionInfo: Optional. Details of the appliance subscription.
    uid: Output only. A system-assigned, unique identifier for the resource.
    updateTime: Output only. Update time.
    verificationState: Output only. Indicates whether appliance has been
      verified by Google.
    verificationStateReason: Output only. Reason for the verification state.
  """

  class ModelValueValuesEnum(_messages.Enum):
    r"""The hardware form factor of the appliance.

    Values:
      TYPE_UNSPECIFIED: Default value. This value is unused.
      TA40_RACKABLE: A rackable TA40.
      TA40_STANDALONE: A standalone TA40.
      TA300_RACKABLE: A rackable TA300.
      TA300_STANDALONE: A standalone TA300.
      TA7: A TA7.
      EA_STORAGE_7: The storage-heavy Edge Appliance.
      EA_GPU_T4: The T4 GPU-capable Edge Appliance.
    """
    TYPE_UNSPECIFIED = 0
    TA40_RACKABLE = 1
    TA40_STANDALONE = 2
    TA300_RACKABLE = 3
    TA300_STANDALONE = 4
    TA7 = 5
    EA_STORAGE_7 = 6
    EA_GPU_T4 = 7

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the physical appliance.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      DRAFT: The appliance is being drafted and has not yet been ordered.
      REQUESTED: The appliance has been ordered but is not yet being prepared
        by Google.
      AWAITING_INVENTORY: To hold appliances due to lack of inventory.
      PREPARING: Being prepared for shipment by Google.
      SHIPPING_TO_CUSTOMER: Shipping to the customer site.
      ON_SITE: At the customer site.
      SHIPPING_TO_GOOGLE: Returning from the customer site.
      PROCESSING: Being processed by Google after return.
      WIPED: The physical appliance has been wiped.
      CANCELLED: The appliance's order has been cancelled.
      OTHER: The physical appliance is in some other state, e.g. it has been
        lost or destroyed.
    """
    STATE_UNSPECIFIED = 0
    DRAFT = 1
    REQUESTED = 2
    AWAITING_INVENTORY = 3
    PREPARING = 4
    SHIPPING_TO_CUSTOMER = 5
    ON_SITE = 6
    SHIPPING_TO_GOOGLE = 7
    PROCESSING = 8
    WIPED = 9
    CANCELLED = 10
    OTHER = 11

  class VerificationStateValueValuesEnum(_messages.Enum):
    r"""Output only. Indicates whether appliance has been verified by Google.

    Values:
      VERIFICATION_STATE_UNSPECIFIED: Explicitly making un-initialized value
        undefined.
      UNVERIFIED: Appliance hasn't been verified yet.
      VERIFIED: Appliance has been verified successfully.
      BLOCKED_BY_GOOGLE: Appliance has been blocked by Google either because
        it's just been sent by Google or the verification failed at some step.
      BLOCKED_BY_CUSTOMER: Appliance has been blocked by customer because they
        have a reason not to trust the software running on the appliance, who
        has access to it, where it is, etc.
      BLOCKED_BY_CUSTOMER_AND_GOOGLE: Appliance has been blocked by both
        google and customer.
    """
    VERIFICATION_STATE_UNSPECIFIED = 0
    UNVERIFIED = 1
    VERIFIED = 2
    BLOCKED_BY_GOOGLE = 3
    BLOCKED_BY_CUSTOMER = 4
    BLOCKED_BY_CUSTOMER_AND_GOOGLE = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""User annotations. See https://google.aip.dev/128#annotations The key
    must be between 0 and 63 characters long. The value must be between 0 and
    63 characters long.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs. Labels must meet the following constraints:
    * Keys and values can contain only lowercase letters, numeric characters,
    underscores, and dashes. * All characters must use UTF-8 encoding, and
    international characters are allowed. * Keys must start with a lowercase
    letter or international character. * Each resource is limited to a maximum
    of 64 labels. * Label keys must be between 1 and 63 characters long. *
    Label values must be between 0 and 63 characters long. * Keys and values
    can contain only lowercase letters, numeric characters, underscores, and
    dashes.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  applianceServiceAccount = _messages.StringField(2)
  atSecureCustomerLocation = _messages.BooleanField(3)
  attestationPasscode = _messages.StringField(4)
  createTime = _messages.StringField(5)
  customerManagedKey = _messages.StringField(6)
  customerRevokedAccess = _messages.BooleanField(7)
  deleteTime = _messages.StringField(8)
  displayName = _messages.StringField(9)
  etag = _messages.StringField(10)
  finalizationCode = _messages.StringField(11)
  internetEnabled = _messages.BooleanField(12)
  kubernetesFeature = _messages.MessageField('KubernetesFeature', 13)
  labels = _messages.MessageField('LabelsValue', 14)
  lastVerified = _messages.MessageField('DateTime', 15)
  maintenanceInfo = _messages.MessageField('MaintenanceInfo', 16)
  maintenancePolicy = _messages.MessageField('MaintenancePolicy', 17)
  model = _messages.EnumField('ModelValueValuesEnum', 18)
  name = _messages.StringField(19)
  offlineExportFeature = _messages.MessageField('OfflineExportFeature', 20)
  offlineImportFeature = _messages.MessageField('OfflineImportFeature', 21)
  onlineEnabled = _messages.BooleanField(22)
  onlineImportFeature = _messages.MessageField('OnlineImportFeature', 23)
  order = _messages.StringField(24)
  pickupInfo = _messages.MessageField('PickupInfo', 25)
  projectSetupCompleted = _messages.BooleanField(26)
  reconciling = _messages.BooleanField(27)
  serialNumber = _messages.StringField(28)
  sessionId = _messages.StringField(29)
  shipmentInfo = _messages.MessageField('ShipmentInfo', 30)
  state = _messages.EnumField('StateValueValuesEnum', 31)
  stateChangeTime = _messages.StringField(32)
  subscriptionInfo = _messages.MessageField('SubscriptionInfo', 33)
  uid = _messages.StringField(34)
  updateTime = _messages.StringField(35)
  verificationState = _messages.EnumField('VerificationStateValueValuesEnum', 36)
  verificationStateReason = _messages.StringField(37)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class ContactInfo(_messages.Message):
  r"""Contact information to be used for communication regarding the order.

  Fields:
    additionalEmails: Additional emails to include in correspondence.
    business: The name of the business, if applicable.
    contactName: The name of the primary contact.
    email: The email of the primary contact.
    phone: The phone number of the primary contact. Should be given in E.164
      format consisting of the country calling code (1 to 3 digits) and the
      subscriber number, with no additional spaces or formatting, e.g.
      "***********".
  """

  additionalEmails = _messages.StringField(1, repeated=True)
  business = _messages.StringField(2)
  contactName = _messages.StringField(3)
  email = _messages.StringField(4)
  phone = _messages.StringField(5)


class Credential(_messages.Message):
  r"""Credential is login username & password for an appliance.

  Messages:
    LabelsValue: Labels as key value pairs. Labels must meet the following
      constraints: * Keys and values can contain only lowercase letters,
      numeric characters, underscores, and dashes. * All characters must use
      UTF-8 encoding, and international characters are allowed. * Keys must
      start with a lowercase letter or international character. * Each
      resource is limited to a maximum of 64 labels. * Label keys must be
      between 1 and 63 characters long and must conform to the following
      regular expression: a-z{0,62}. * Label values must be between 0 and 63
      characters long and must conform to the regular expression
      [a-z0-9_-]{0,63}.

  Fields:
    createTime: Output only. Create time.
    credentialSharedFirstTime: Output only. Timestamp when the credentials are
      shared with the customer for the first time.
    etag: This checksum is computed by the server based on the value of other
      fields, and may be sent on update and delete requests to ensure the
      client has an up-to-date value before proceeding. See
      https://google.aip.dev/154.
    labels: Labels as key value pairs. Labels must meet the following
      constraints: * Keys and values can contain only lowercase letters,
      numeric characters, underscores, and dashes. * All characters must use
      UTF-8 encoding, and international characters are allowed. * Keys must
      start with a lowercase letter or international character. * Each
      resource is limited to a maximum of 64 labels. * Label keys must be
      between 1 and 63 characters long and must conform to the following
      regular expression: a-z{0,62}. * Label values must be between 0 and 63
      characters long and must conform to the regular expression
      [a-z0-9_-]{0,63}.
    name: Unique name of the credential resource. username is the unique
      idenifier for the credential resource. Fromat:
      projects/*/locations/*/Appliances/*/Credentials/ Ex:
      projects/zimbruplayground/locations/us-
      central1/Appliances/myappliance/Credentials/ta_customer,
      projects/p1/locations/l1/Appliances/a1/Credentials/ta_operator
    password: Output only. Password of the Credential.
    updateTime: Output only. Update time.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs. Labels must meet the following constraints:
    * Keys and values can contain only lowercase letters, numeric characters,
    underscores, and dashes. * All characters must use UTF-8 encoding, and
    international characters are allowed. * Keys must start with a lowercase
    letter or international character. * Each resource is limited to a maximum
    of 64 labels. * Label keys must be between 1 and 63 characters long and
    must conform to the following regular expression: a-z{0,62}. * Label
    values must be between 0 and 63 characters long and must conform to the
    regular expression [a-z0-9_-]{0,63}.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  credentialSharedFirstTime = _messages.StringField(2)
  etag = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  password = _messages.StringField(6)
  updateTime = _messages.StringField(7)


class Date(_messages.Message):
  r"""Represents a whole or partial calendar date, such as a birthday. The
  time of day and time zone are either specified elsewhere or are
  insignificant. The date is relative to the Gregorian Calendar. This can
  represent one of the following: * A full date, with non-zero year, month,
  and day values. * A month and day, with a zero year (for example, an
  anniversary). * A year on its own, with a zero month and a zero day. * A
  year and month, with a zero day (for example, a credit card expiration
  date). Related types: * google.type.TimeOfDay * google.type.DateTime *
  google.protobuf.Timestamp

  Fields:
    day: Day of a month. Must be from 1 to 31 and valid for the year and
      month, or 0 to specify a year by itself or a year and month where the
      day isn't significant.
    month: Month of a year. Must be from 1 to 12, or 0 to specify a year
      without a month and day.
    year: Year of the date. Must be from 1 to 9999, or 0 to specify a date
      without a year.
  """

  day = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  month = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  year = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class DateTime(_messages.Message):
  r"""Represents civil time (or occasionally physical time). This type can
  represent a civil time in one of a few possible ways: * When utc_offset is
  set and time_zone is unset: a civil time on a calendar day with a particular
  offset from UTC. * When time_zone is set and utc_offset is unset: a civil
  time on a calendar day in a particular time zone. * When neither time_zone
  nor utc_offset is set: a civil time on a calendar day in local time. The
  date is relative to the Proleptic Gregorian Calendar. If year, month, or day
  are 0, the DateTime is considered not to have a specific year, month, or day
  respectively. This type may also be used to represent a physical time if all
  the date and time fields are set and either case of the `time_offset` oneof
  is set. Consider using `Timestamp` message for physical time instead. If
  your use case also would like to store the user's timezone, that can be done
  in another field. This type is more flexible than some applications may
  want. Make sure to document and validate your application's limitations.

  Fields:
    day: Optional. Day of month. Must be from 1 to 31 and valid for the year
      and month, or 0 if specifying a datetime without a day.
    hours: Optional. Hours of day in 24 hour format. Should be from 0 to 23,
      defaults to 0 (midnight). An API may choose to allow the value
      "24:00:00" for scenarios like business closing time.
    minutes: Optional. Minutes of hour of day. Must be from 0 to 59, defaults
      to 0.
    month: Optional. Month of year. Must be from 1 to 12, or 0 if specifying a
      datetime without a month.
    nanos: Optional. Fractions of seconds in nanoseconds. Must be from 0 to
      999,999,999, defaults to 0.
    seconds: Optional. Seconds of minutes of the time. Must normally be from 0
      to 59, defaults to 0. An API may allow the value 60 if it allows leap-
      seconds.
    timeZone: Time zone.
    utcOffset: UTC offset. Must be whole seconds, between -18 hours and +18
      hours. For example, a UTC offset of -4:00 would be represented as {
      seconds: -14400 }.
    year: Optional. Year of date. Must be from 1 to 9999, or 0 if specifying a
      datetime without a year.
  """

  day = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  hours = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  minutes = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  month = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  nanos = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  seconds = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  timeZone = _messages.MessageField('TimeZone', 7)
  utcOffset = _messages.StringField(8)
  year = _messages.IntegerField(9, variant=_messages.Variant.INT32)


class DenyMaintenancePeriod(_messages.Message):
  r"""Deny maintenance period is a period of time during which updates should
  not be applied to the transfer appliance.

  Fields:
    endDate: Deny period end date. This can be: * A full date, with non-zero
      year, month and day values. * A month and day value, with a zero year.
      Allows recurring deny periods each year. Date matching this period will
      have to be before the end.
    startDate: Deny period start date. This can be: * A full date, with non-
      zero year, month and day values. * A month and day value, with a zero
      year. Allows recurring deny periods each year. Date matching this period
      will have to be the same or after the start.
    time: Time in UTC when the Blackout period starts on start_date and ends
      on end_date. This can be: * Full time. * All zeros for 00:00:00 UTC
  """

  endDate = _messages.MessageField('Date', 1)
  startDate = _messages.MessageField('Date', 2)
  time = _messages.MessageField('TimeOfDay', 3)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class ExportApplianceUserDataResponse(_messages.Message):
  r"""Message containing user data for an appliance.

  Fields:
    appliance: Describes the physical appliance.
    shippingLabels: shipping_labels contains a list of shipping labels
      generated for inbound and outbound logistics.
    wipeCertificates: wipe_certificates contains a list of file created when
      an appliance is wiped.
  """

  appliance = _messages.MessageField('Appliance', 1)
  shippingLabels = _messages.MessageField('File', 2, repeated=True)
  wipeCertificates = _messages.MessageField('File', 3, repeated=True)


class File(_messages.Message):
  r"""Message containing a file.

  Fields:
    data: data is the file content.
    filename: filename is the file name.
  """

  data = _messages.BytesField(1)
  filename = _messages.StringField(2)


class GcsDestination(_messages.Message):
  r"""A message containing information about the Cloud Storage destination of
  a transfer.

  Fields:
    outputBucket: Optional. The name of the Cloud Storage bucket where files
      should be placed.
    outputPath: Optional. The path in the output bucket where files should be
      placed. This must either be empty, in which case files will be placed in
      the root of the bucket, or it must end with a "/".
  """

  outputBucket = _messages.StringField(1)
  outputPath = _messages.StringField(2)


class GcsSource(_messages.Message):
  r"""A message used in OfflineExportFeature to contain information about the
  Cloud Storage source of a transfer.

  Fields:
    bucket: Required. The name of the Cloud Storage bucket or folder inside
      the bucket where files should be downloaded from.
  """

  bucket = _messages.StringField(1)


class KubernetesFeature(_messages.Message):
  r"""For Edge Appliance, a feature that allows the appliance to run a
  Kubernetes node.

  Fields:
    serviceAccount: Optional. Name of the cluster registration service
      account. Follow the steps mentioned here
      (https://cloud.google.com/distributed-cloud/edge-
      appliance/docs/configure-cloud#cluster_sa) before setting this field.
  """

  serviceAccount = _messages.StringField(1)


class ListAppliancesResponse(_messages.Message):
  r"""Response message for the ListAppliances method.

  Fields:
    appliances: The list of Appliance. If the `{location}` value in the
      request is "-", the response contains a list of instances from all
      locations. In case any location is unreachable, the response will only
      return management servers in reachable locations and the 'unreachable'
      field will be populated with a list of unreachable locations.
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  appliances = _messages.MessageField('Appliance', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListOrdersResponse(_messages.Message):
  r"""Response message for the ListOrders method.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    orders: The list of Order. If the `{location}` value in the request is
      "-", the response contains a list of instances from all locations. In
      case any location is unreachable, the response will only return
      management servers in reachable locations and the 'unreachable' field
      will be populated with a list of unreachable locations.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  orders = _messages.MessageField('Order', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListSavedAddressesResponse(_messages.Message):
  r"""Response message for the ListSavedAddresses method.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    savedAddresses: The list of SavedAddress. If the `{location}` value in the
      request is "-", the response contains a list of instances from all
      locations. In case any location is unreachable, the response will only
      return management servers in reachable locations and the 'unreachable'
      field will be populated with a list of unreachable locations.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  savedAddresses = _messages.MessageField('SavedAddress', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class MaintenanceInfo(_messages.Message):
  r"""Message containing information about software updates for the appliance.

  Messages:
    RolloutInfoValue: Map for storing the information for all the rollout
      types for the appliance. The key for the map is the rollout_type_id, for
      example "appliance_rollout".

  Fields:
    rolloutInfo: Map for storing the information for all the rollout types for
      the appliance. The key for the map is the rollout_type_id, for example
      "appliance_rollout".
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class RolloutInfoValue(_messages.Message):
    r"""Map for storing the information for all the rollout types for the
    appliance. The key for the map is the rollout_type_id, for example
    "appliance_rollout".

    Messages:
      AdditionalProperty: An additional property for a RolloutInfoValue
        object.

    Fields:
      additionalProperties: Additional properties of type RolloutInfoValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a RolloutInfoValue object.

      Fields:
        key: Name of the additional property.
        value: A RolloutInfo attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('RolloutInfo', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  rolloutInfo = _messages.MessageField('RolloutInfoValue', 1)


class MaintenancePolicy(_messages.Message):
  r"""Maintenance policies to service maintenance events.

  Enums:
    ChannelValueValuesEnum: Optional. Relative scheduling channel applied to
      the transfer appliance.

  Fields:
    channel: Optional. Relative scheduling channel applied to the transfer
      appliance.
    denyMaintenancePeriods: Optional. User can specify zero or more non-
      overlapping deny periods. Minimum number of deny_maintenance_periods
      expected is one.
    weeklyWindow: Optional. Customers can specify multiple time windows in a
      week during which transfer appliances can be updated. Minimum of 1
      window should be configured.
  """

  class ChannelValueValuesEnum(_messages.Enum):
    r"""Optional. Relative scheduling channel applied to the transfer
    appliance.

    Values:
      UPDATE_CHANNEL_UNSPECIFIED: Unspecified channel.
      EARLIER: Early channel within a customer project. Appliances associated
        with this channel will be updated first.
      LATER: Later channel within a customer project. Appliances associated
        with this channel will be updated last.
    """
    UPDATE_CHANNEL_UNSPECIFIED = 0
    EARLIER = 1
    LATER = 2

  channel = _messages.EnumField('ChannelValueValuesEnum', 1)
  denyMaintenancePeriods = _messages.MessageField('DenyMaintenancePeriod', 2, repeated=True)
  weeklyWindow = _messages.MessageField('WeeklyWindow', 3, repeated=True)


class OfflineExportFeature(_messages.Message):
  r"""An offline export transfer, where data is downloaded onto the appliance
  at Google and copied from the appliance at the customer site.

  Fields:
    source: Required. The source of the transfer.
    stsAccount: Output only. The Storage Transfer Service service account used
      for this transfer. This account must be given roles/storage.admin access
      to the output bucket.
    transferManifest: Optional. Manifest file for the transfer. When this is
      provided, only files specificied in the manifest file are transferred.
      Only one GCSSource can be set with this option.
    workloadAccount: Output only. The service account associated with this
      transfer. This account must be granted the roles/storage.admin role on
      the output bucket, and the roles/cloudkms.cryptoKeyDecrypter and
      roles/cloudkms.publicKeyViewer roles on the customer managed key.
  """

  source = _messages.MessageField('GcsSource', 1, repeated=True)
  stsAccount = _messages.StringField(2)
  transferManifest = _messages.MessageField('TransferManifest', 3)
  workloadAccount = _messages.StringField(4)


class OfflineImportFeature(_messages.Message):
  r"""An offline import transfer, where data is loaded onto the appliance at
  the customer site and ingested at Google.

  Enums:
    LastNonCancelledStateValueValuesEnum: Output only. Holds the last active
      state of the transfer before it was set to CANCELLED. When the transfer
      state is CANCELLED, this field reflects its most recent active state. If
      the transfer is not CANCELLED, this field is set to STATE_UNSPECIFIED.
    StateValueValuesEnum: Output only. The state of the transfer.

  Fields:
    allocatedBytesCount: Output only. The total number of bytes allocated on
      the appliance for data transfer.
    destination: Optional. The destination of the transfer.
    lastNonCancelledState: Output only. Holds the last active state of the
      transfer before it was set to CANCELLED. When the transfer state is
      CANCELLED, this field reflects its most recent active state. If the
      transfer is not CANCELLED, this field is set to STATE_UNSPECIFIED.
    preparedBytesCount: Output only. The number of bytes securely encrypted
      and uploaded to a temporary Cloud storage location.
    state: Output only. The state of the transfer.
    stsAccount: Output only. The Storage Transfer Service service account used
      for this transfer. This account must be given roles/storage.admin access
      to the output bucket.
    transferResults: Output only. The results of the transfer.
    workloadAccount: Output only. The service account associated with this
      transfer. This account must be granted the roles/storage.admin role on
      the output bucket, and the roles/cloudkms.cryptoKeyDecrypter and
      roles/cloudkms.publicKeyViewer roles on the customer managed key, if
      using one.
  """

  class LastNonCancelledStateValueValuesEnum(_messages.Enum):
    r"""Output only. Holds the last active state of the transfer before it was
    set to CANCELLED. When the transfer state is CANCELLED, this field
    reflects its most recent active state. If the transfer is not CANCELLED,
    this field is set to STATE_UNSPECIFIED.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      DRAFT: The transfer is associated with a draft order.
      ACTIVE: The appliance used for this transfer has been ordered but is not
        yet back at Google for ingest.
      PREPARING: The encrypted data is being temporarily uploaded to a
        temporary Cloud storage location.
      TRANSFERRING: The data is being transferred to the destination bucket.
      VERIFYING: Verifying the data transferred to the destination bucket.
      COMPLETED: The transfer has completed and data is available in the
        output bucket.
      CANCELLED: The transfer has been cancelled and data will not be
        ingested.
    """
    STATE_UNSPECIFIED = 0
    DRAFT = 1
    ACTIVE = 2
    PREPARING = 3
    TRANSFERRING = 4
    VERIFYING = 5
    COMPLETED = 6
    CANCELLED = 7

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the transfer.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      DRAFT: The transfer is associated with a draft order.
      ACTIVE: The appliance used for this transfer has been ordered but is not
        yet back at Google for ingest.
      PREPARING: The encrypted data is being temporarily uploaded to a
        temporary Cloud storage location.
      TRANSFERRING: The data is being transferred to the destination bucket.
      VERIFYING: Verifying the data transferred to the destination bucket.
      COMPLETED: The transfer has completed and data is available in the
        output bucket.
      CANCELLED: The transfer has been cancelled and data will not be
        ingested.
    """
    STATE_UNSPECIFIED = 0
    DRAFT = 1
    ACTIVE = 2
    PREPARING = 3
    TRANSFERRING = 4
    VERIFYING = 5
    COMPLETED = 6
    CANCELLED = 7

  allocatedBytesCount = _messages.IntegerField(1)
  destination = _messages.MessageField('GcsDestination', 2)
  lastNonCancelledState = _messages.EnumField('LastNonCancelledStateValueValuesEnum', 3)
  preparedBytesCount = _messages.IntegerField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)
  stsAccount = _messages.StringField(6)
  transferResults = _messages.MessageField('TransferResults', 7)
  workloadAccount = _messages.StringField(8)


class OnlineImportFeature(_messages.Message):
  r"""An online import transfer, where data is loaded onto the appliance and
  automatically transferred to Cloud Storage whenever it has an internet
  connection.

  Fields:
    destination: Optional. The destination of the transfer.
    jobName: Output only. The Transfer Job Name for Online Imports.
    transferResults: Output only. The results of the transfer.
  """

  destination = _messages.MessageField('GcsDestination', 1)
  jobName = _messages.StringField(2)
  transferResults = _messages.MessageField('TransferResults', 3, repeated=True)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Order(_messages.Message):
  r"""An order for one or more physical appliances to a single location.

  Enums:
    DatacenterLocationValueValuesEnum: Output only. Datacenter location used
      to register the session.
    StateValueValuesEnum: Output only. The state of the order.

  Messages:
    AnnotationsValue: User annotations. See
      https://google.aip.dev/128#annotations.
    LabelsValue: Labels as key value pairs. Labels must meet the following
      constraints: * Keys and values can contain only lowercase letters,
      numeric characters, underscores, and dashes. * All characters must use
      UTF-8 encoding, and international characters are allowed. * Keys must
      start with a lowercase letter or international character. * Each
      resource is limited to a maximum of 64 labels. * Label keys must be
      between 1 and 63 characters long and must conform to the following
      regular expression: a-z{0,62}. * Label values must be between 0 and 63
      characters long and must conform to the regular expression
      [a-z0-9_-]{0,63}.

  Fields:
    address: Optional. The address to ship the order to. See https://github.co
      m/googleapis/googleapis/blob/master/google/type/postal_address.proto.
    annotations: User annotations. See https://google.aip.dev/128#annotations.
    appliances: The appliances included in this order.
    cancelled: Whether the order has been cancelled. For an order that is in
      the FULFILLING state, setting this to true will cause the order to move
      to the CANCELLED state.
    createTime: Output only. Create time.
    datacenterLocation: Output only. Datacenter location used to register the
      session.
    deleteTime: Output only. Delete time.
    deliveryNotes: Free text notes included in the order, for any additional
      details that should be passed on to the shipping company.
    displayName: A mutable, user-settable name for the resource. It does not
      need to be unique and should be less than 64 characters.
    etag: Strongly validated etag, computed by the server based on the value
      of other fields, and may be sent on update and delete requests to ensure
      the client has an up-to-date value before proceeding. See
      https://google.aip.dev/154.
    labels: Labels as key value pairs. Labels must meet the following
      constraints: * Keys and values can contain only lowercase letters,
      numeric characters, underscores, and dashes. * All characters must use
      UTF-8 encoding, and international characters are allowed. * Keys must
      start with a lowercase letter or international character. * Each
      resource is limited to a maximum of 64 labels. * Label keys must be
      between 1 and 63 characters long and must conform to the following
      regular expression: a-z{0,62}. * Label values must be between 0 and 63
      characters long and must conform to the regular expression
      [a-z0-9_-]{0,63}.
    name: name of resource.
    orderContact: Contact information used for general communication from
      Google about the order and its appliances.
    reconciling: Output only. Reconciling
      (https://google.aip.dev/128#reconciliation). Set to true if the current
      state of Order does not match the user's intended state, and the service
      is actively updating the resource to reconcile them. This can happen due
      to user-triggered updates or system actions like failover or
      maintenance.
    shippingContact: Contact information used for the order's shipments.
    skipDraft: If true, submit the order immediately upon creation. The order
      will skip the DRAFT state and go straight to the PREPARING state as if
      the Submit method were called on the order. For an order that is in the
      DRAFT state, setting this to true will also cause it to be submitted.
    state: Output only. The state of the order.
    submitTime: Output only. Submit time.
    uid: Output only. A system-assigned, unique identifier for the resource.
    updateTime: Output only. Update time.
  """

  class DatacenterLocationValueValuesEnum(_messages.Enum):
    r"""Output only. Datacenter location used to register the session.

    Values:
      DATACENTER_LOCATION_UNSPECIFIED: Default value. Should not be used.
      US: US datacenter location.
      EU: EU datacenter location.
      SGP: Singapore datacenter location.
      SIN: Singapore datacenter location.
      UK: UK datacenter location.
      JPN: Japan datacenter location.
      CAN: Canada(Montr\xe9al) data center.
      AUTP: Australia(Third Party) datacenter location.
      INTP: India(Third Party) datacenter location.
    """
    DATACENTER_LOCATION_UNSPECIFIED = 0
    US = 1
    EU = 2
    SGP = 3
    SIN = 4
    UK = 5
    JPN = 6
    CAN = 7
    AUTP = 8
    INTP = 9

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the order.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      DRAFT: The order has not yet been submitted to Google.
      SUBMITTED: The order has been submitted to Google for review.
      FULFILLING: Appliances in the order are in the process of being prepared
        and shipped to the customer.
      FULFILLED: All appliances in the order have been delivered to the
        customer.
      ACTIVE: Order has been accepted by Google and one or more appliances
        from the order are in active use.
      COMPLETED: All appliances in the order are processed and wiped and
        therefore the order can be declared complete.
      CANCELLED: The order has been cancelled.
    """
    STATE_UNSPECIFIED = 0
    DRAFT = 1
    SUBMITTED = 2
    FULFILLING = 3
    FULFILLED = 4
    ACTIVE = 5
    COMPLETED = 6
    CANCELLED = 7

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""User annotations. See https://google.aip.dev/128#annotations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs. Labels must meet the following constraints:
    * Keys and values can contain only lowercase letters, numeric characters,
    underscores, and dashes. * All characters must use UTF-8 encoding, and
    international characters are allowed. * Keys must start with a lowercase
    letter or international character. * Each resource is limited to a maximum
    of 64 labels. * Label keys must be between 1 and 63 characters long and
    must conform to the following regular expression: a-z{0,62}. * Label
    values must be between 0 and 63 characters long and must conform to the
    regular expression [a-z0-9_-]{0,63}.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  address = _messages.MessageField('PostalAddress', 1)
  annotations = _messages.MessageField('AnnotationsValue', 2)
  appliances = _messages.StringField(3, repeated=True)
  cancelled = _messages.BooleanField(4)
  createTime = _messages.StringField(5)
  datacenterLocation = _messages.EnumField('DatacenterLocationValueValuesEnum', 6)
  deleteTime = _messages.StringField(7)
  deliveryNotes = _messages.StringField(8)
  displayName = _messages.StringField(9)
  etag = _messages.StringField(10)
  labels = _messages.MessageField('LabelsValue', 11)
  name = _messages.StringField(12)
  orderContact = _messages.MessageField('ContactInfo', 13)
  reconciling = _messages.BooleanField(14)
  shippingContact = _messages.MessageField('ContactInfo', 15)
  skipDraft = _messages.BooleanField(16)
  state = _messages.EnumField('StateValueValuesEnum', 17)
  submitTime = _messages.StringField(18)
  uid = _messages.StringField(19)
  updateTime = _messages.StringField(20)


class Parameter(_messages.Message):
  r"""A message containing information about subscription parameter.

  Fields:
    name: Identifier of the parameter.
    value: Value of parameter.
  """

  name = _messages.StringField(1)
  value = _messages.MessageField('Value', 2)


class PickupInfo(_messages.Message):
  r"""Message containing pickup information for a return of an appliance.
  NextID: 8

  Enums:
    PickupShipmentTypeValueValuesEnum: Optional. PickupShipmentType is the
      shipment type selected by the customer.

  Fields:
    address: Optional. The address to pick up the appliance from
    contactName: Optional. The name of the customer site contact.
    phone: Optional. The phone number of the customer site contact. Should be
      given in E.164 format consisting of the country calling code (1 to 3
      digits) and the subscriber number, with no additional spaces or
      formatting, e.g. "***********".
    pickupDate: Optional. Preferred pick up date requested by the customer.
    pickupInstructions: Optional. Pickup instructions provided by the
      customer.
    pickupShipmentType: Optional. PickupShipmentType is the shipment type
      selected by the customer.
    pickupTimeslot: Optional. Preferred pick up time slot requested by the
      customer.
    shippingLabelEmail: Optional. Emails to include when sending shipping
      labels.
  """

  class PickupShipmentTypeValueValuesEnum(_messages.Enum):
    r"""Optional. PickupShipmentType is the shipment type selected by the
    customer.

    Values:
      PICKUP_SHIPMENT_TYPE_UNSPECIFIED: Default value. This value is unused.
      GOOGLE_COORDINATED: Pickup is coordinated by Google.
      CUSTOMER_COORDINATED: Pickup is coordinated by the customer. In this
        case fields like pickup_date, pickup_timeslot, and pickup_instructions
        are not set.
    """
    PICKUP_SHIPMENT_TYPE_UNSPECIFIED = 0
    GOOGLE_COORDINATED = 1
    CUSTOMER_COORDINATED = 2

  address = _messages.MessageField('PostalAddress', 1)
  contactName = _messages.StringField(2)
  phone = _messages.StringField(3)
  pickupDate = _messages.MessageField('Date', 4)
  pickupInstructions = _messages.StringField(5)
  pickupShipmentType = _messages.EnumField('PickupShipmentTypeValueValuesEnum', 6)
  pickupTimeslot = _messages.StringField(7)
  shippingLabelEmail = _messages.StringField(8, repeated=True)


class PostalAddress(_messages.Message):
  r"""Represents a postal address (for example, for postal delivery or
  payments addresses). Given a postal address, a postal service can deliver
  items to a premise, P.O. box or similar. It is not intended to model
  geographical locations (roads, towns, mountains). In typical usage, an
  address would be created by user input or from importing existing data,
  depending on the type of process. Advice on address input or editing: - Use
  an internationalization-ready address widget such as
  https://github.com/google/libaddressinput. - Users should not be presented
  with UI elements for input or editing of fields outside countries where that
  field is used. For more guidance on how to use this schema, see:
  https://support.google.com/business/answer/6397478.

  Fields:
    addressLines: Unstructured address lines describing the lower levels of an
      address. Because values in `address_lines` do not have type information
      and may sometimes contain multiple values in a single field (for
      example, "Austin, TX"), it is important that the line order is clear.
      The order of address lines should be "envelope order" for the country or
      region of the address. In places where this can vary (for example,
      Japan), `address_language` is used to make it explicit (for example,
      "ja" for large-to-small ordering and "ja-Latn" or "en" for small-to-
      large). In this way, the most specific line of an address can be
      selected based on the language. The minimum permitted structural
      representation of an address consists of a `region_code` with all
      remaining information placed in the `address_lines`. It would be
      possible to format such an address very approximately without geocoding,
      but no semantic reasoning could be made about any of the address
      components until it was at least partially resolved. Creating an address
      only containing a `region_code` and `address_lines` and then geocoding
      is the recommended way to handle completely unstructured addresses (as
      opposed to guessing which parts of the address should be localities or
      administrative areas).
    administrativeArea: Optional. Highest administrative subdivision which is
      used for postal addresses of a country or region. For example, this can
      be a state, a province, an oblast, or a prefecture. For Spain, this is
      the province and not the autonomous community (for example, "Barcelona"
      and not "Catalonia"). Many countries don't use an administrative area in
      postal addresses. For example, in Switzerland, this should be left
      unpopulated.
    languageCode: Optional. BCP-47 language code of the contents of this
      address (if known). This is often the UI language of the input form or
      is expected to match one of the languages used in the address'
      country/region, or their transliterated equivalents. This can affect
      formatting in certain countries, but is not critical to the correctness
      of the data and will never affect any validation or other non-formatting
      related operations. If this value is not known, it should be omitted
      (rather than specifying a possibly incorrect default). Examples: "zh-
      Hant", "ja", "ja-Latn", "en".
    locality: Optional. Generally refers to the city or town portion of the
      address. Examples: US city, IT comune, UK post town. In regions of the
      world where localities are not well defined or do not fit into this
      structure well, leave `locality` empty and use `address_lines`.
    organization: Optional. The name of the organization at the address.
    postalCode: Optional. Postal code of the address. Not all countries use or
      require postal codes to be present, but where they are used, they may
      trigger additional validation with other parts of the address (for
      example, state or zip code validation in the United States).
    recipients: Optional. The recipient at the address. This field may, under
      certain circumstances, contain multiline information. For example, it
      might contain "care of" information.
    regionCode: Required. CLDR region code of the country/region of the
      address. This is never inferred and it is up to the user to ensure the
      value is correct. See https://cldr.unicode.org/ and https://www.unicode.
      org/cldr/charts/30/supplemental/territory_information.html for details.
      Example: "CH" for Switzerland.
    revision: The schema revision of the `PostalAddress`. This must be set to
      0, which is the latest revision. All new revisions **must** be backward
      compatible with old revisions.
    sortingCode: Optional. Additional, country-specific, sorting code. This is
      not used in most regions. Where it is used, the value is either a string
      like "CEDEX", optionally followed by a number (for example, "CEDEX 7"),
      or just a number alone, representing the "sector code" (Jamaica),
      "delivery area indicator" (Malawi) or "post office indicator" (C\xf4te
      d'Ivoire).
    sublocality: Optional. Sublocality of the address. For example, this can
      be a neighborhood, borough, or district.
  """

  addressLines = _messages.StringField(1, repeated=True)
  administrativeArea = _messages.StringField(2)
  languageCode = _messages.StringField(3)
  locality = _messages.StringField(4)
  organization = _messages.StringField(5)
  postalCode = _messages.StringField(6)
  recipients = _messages.StringField(7, repeated=True)
  regionCode = _messages.StringField(8)
  revision = _messages.IntegerField(9, variant=_messages.Variant.INT32)
  sortingCode = _messages.StringField(10)
  sublocality = _messages.StringField(11)


class RolloutInfo(_messages.Message):
  r"""Message containing the information for the Software Rollout.

  Enums:
    PostRolloutStateValueValuesEnum: The state of the appliance after the last
      attempted rollout.
    RolloutStateValueValuesEnum: State of last update attempted on the
      appliance of the particular rollout type.

  Fields:
    attemptedSoftwareVersion: Software version of the last rollout attempted
      for the appliance.
    currentSoftwareVersion: Current software version on the appliance.
    errorMessage: Any error message that may be useful when update has failed.
    postRolloutState: The state of the appliance after the last attempted
      rollout.
    rolloutState: State of last update attempted on the appliance of the
      particular rollout type.
  """

  class PostRolloutStateValueValuesEnum(_messages.Enum):
    r"""The state of the appliance after the last attempted rollout.

    Values:
      POST_ROLLOUT_STATE_UNSPECIFIED: Default state. Used when update is not
        yet complete.
      ACTIVE: Appliance is in a healthy state.
      ERROR: Appliance is in an unhealthy state.
    """
    POST_ROLLOUT_STATE_UNSPECIFIED = 0
    ACTIVE = 1
    ERROR = 2

  class RolloutStateValueValuesEnum(_messages.Enum):
    r"""State of last update attempted on the appliance of the particular
    rollout type.

    Values:
      ROLLOUT_STATE_UNSPECIFIED: Default value. Should not be used.
      AVAILABLE: Update is available but not yet started.
      UPDATING: Update is in progress.
      SUCCEEDED: Latest update was successful.
      FAILED: Update was unsuccessful.
      UNKNOWN: State of the update is unknown.
    """
    ROLLOUT_STATE_UNSPECIFIED = 0
    AVAILABLE = 1
    UPDATING = 2
    SUCCEEDED = 3
    FAILED = 4
    UNKNOWN = 5

  attemptedSoftwareVersion = _messages.StringField(1)
  currentSoftwareVersion = _messages.StringField(2)
  errorMessage = _messages.StringField(3)
  postRolloutState = _messages.EnumField('PostRolloutStateValueValuesEnum', 4)
  rolloutState = _messages.EnumField('RolloutStateValueValuesEnum', 5)


class SavedAddress(_messages.Message):
  r"""An address where appliances can be shipped.

  Messages:
    AnnotationsValue: User annotations. See
      https://google.aip.dev/128#annotations.
    LabelsValue: Labels as key value pairs. Labels must meet the following
      constraints: * Keys and values can contain only lowercase letters,
      numeric characters, underscores, and dashes. * All characters must use
      UTF-8 encoding, and international characters are allowed. * Keys must
      start with a lowercase letter or international character. * Each
      resource is limited to a maximum of 64 labels. * Label keys must be
      between 1 and 63 characters long and must conform to the following
      regular expression: a-z{0,62}. * Label values must be between 0 and 63
      characters long and must conform to the regular expression
      [a-z0-9_-]{0,63}.

  Fields:
    address: Optional. The saved shipping address.
    annotations: User annotations. See https://google.aip.dev/128#annotations.
    createTime: Output only. Create time.
    deleteTime: Output only. Delete time stamp.
    displayName: Optional. A mutable, user-settable name for the resource. It
      does not need to be unique and should be less than 64 characters.
    etag: Strongly validated etag, computed by the server based on the value
      of other fields, and may be sent on update and delete requests to ensure
      the client has an up-to-date value before proceeding. See
      https://google.aip.dev/154.
    labels: Labels as key value pairs. Labels must meet the following
      constraints: * Keys and values can contain only lowercase letters,
      numeric characters, underscores, and dashes. * All characters must use
      UTF-8 encoding, and international characters are allowed. * Keys must
      start with a lowercase letter or international character. * Each
      resource is limited to a maximum of 64 labels. * Label keys must be
      between 1 and 63 characters long and must conform to the following
      regular expression: a-z{0,62}. * Label values must be between 0 and 63
      characters long and must conform to the regular expression
      [a-z0-9_-]{0,63}.
    name: name of resource.
    reconciling: Output only. Reconciling
      (https://google.aip.dev/128#reconciliation). Set to true if the current
      state of SavedAddress does not match the user's intended state, and the
      service is actively updating the resource to reconcile them. This can
      happen due to user-triggered updates or system actions like failover or
      maintenance.
    shippingContact: Contact information used for the shipments to the given
      address.
    uid: Output only. A system-assigned, unique identifier (UUID4) for the
      resource.
    updateTime: Output only. Update time.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""User annotations. See https://google.aip.dev/128#annotations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs. Labels must meet the following constraints:
    * Keys and values can contain only lowercase letters, numeric characters,
    underscores, and dashes. * All characters must use UTF-8 encoding, and
    international characters are allowed. * Keys must start with a lowercase
    letter or international character. * Each resource is limited to a maximum
    of 64 labels. * Label keys must be between 1 and 63 characters long and
    must conform to the following regular expression: a-z{0,62}. * Label
    values must be between 0 and 63 characters long and must conform to the
    regular expression [a-z0-9_-]{0,63}.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  address = _messages.MessageField('PostalAddress', 1)
  annotations = _messages.MessageField('AnnotationsValue', 2)
  createTime = _messages.StringField(3)
  deleteTime = _messages.StringField(4)
  displayName = _messages.StringField(5)
  etag = _messages.StringField(6)
  labels = _messages.MessageField('LabelsValue', 7)
  name = _messages.StringField(8)
  reconciling = _messages.BooleanField(9)
  shippingContact = _messages.MessageField('ContactInfo', 10)
  uid = _messages.StringField(11)
  updateTime = _messages.StringField(12)


class ShipmentInfo(_messages.Message):
  r"""Message containing information about the appliance's shipment.

  Fields:
    deliverAfterTime: An optional timestamp to indicate the earliest that the
      appliance should be delivered. If not set it will be delivered as soon
      as possible.
    deliveryTrackingCarrier: Output only. Shipment carrier/partner associated
      with the outbound shipment (Google to customer).
    deliveryTrackingId: Output only. Tracking id associated with the outbound
      shipment (Google to customer).
    deliveryTrackingUri: Output only. A web URI allowing you to track the
      shipment from Google.
    returnLabelUri: Output only. The web URI to access the return label. This
      is only available once the finalization_code has been set on the
      Appliance resource.
    returnTrackingCarrier: Output only. Shipment carrier/partner associated
      with the inbound shipment (customer to Google).
    returnTrackingId: Output only. Tracking id associated with the inbound
      shipment (customer to Google).
    returnTrackingUri: Output only. A web URI allowing you to track the return
      shipment to Google.
  """

  deliverAfterTime = _messages.MessageField('DateTime', 1)
  deliveryTrackingCarrier = _messages.StringField(2)
  deliveryTrackingId = _messages.StringField(3)
  deliveryTrackingUri = _messages.StringField(4)
  returnLabelUri = _messages.StringField(5)
  returnTrackingCarrier = _messages.StringField(6)
  returnTrackingId = _messages.StringField(7)
  returnTrackingUri = _messages.StringField(8)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class SubmitOrderRequest(_messages.Message):
  r"""Request message for the SubmitOrder method.

  Fields:
    etag: Optional. Strongly validated etag, computed by the server to ensure
      the client has an up-to-date value before proceeding. See
      https://google.aip.dev/154.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and t he request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  etag = _messages.StringField(1)
  requestId = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)


class Subscription(_messages.Message):
  r"""A message containing information about appliance subscription. NextId:
  24.

  Enums:
    ErrorStateInfoValueValuesEnum: Output only. Additional information on why
      the subscription is in error state.
    StateValueValuesEnum: A description of state of the subscription.
    TermEndBehaviorValueValuesEnum: Determine the subscription behavior when
      current terms end.

  Fields:
    creationTime: Output only. Creation time of the subscription.
    definition: Output only. The name of subscription definition.
    definitionDisplay: Output only. A human readable name that describes this
      subscription.
    display: An alias display name.
    endTime: Output only. The target end time of the subscription.
    errorStateInfo: Output only. Additional information on why the
      subscription is in error state.
    etag: Output only. Strongly validated etag, a consistency token set by
      subscription server.
    externalId: Output only. A unique field across all subscription. We use to
      identify the appliance.
    name: Output only. This is an ID which uniquely identifies the
      subscription.
    nextTermDuration: Output only. Indicates the duration for the next custom
      term plan.
    nextUpdateParameters: Output only. This field is used together with
      next_update_time field.
    nextUpdateTime: Output only. This field is populated iff there is
      scheduled update on the current subscription. The field should always be
      future.
    owningResource: Output only. The resource that owns the subscription and
      receives bill.
    parameters: Output only. A list of parameter that set by creation request.
    startTime: Output only. The start time of the subscription.
    state: A description of state of the subscription.
    termDefinitionDisplay: Output only. The term definition display name.
    termDefinitionId: The term definition id.
    termEndBehavior: Determine the subscription behavior when current terms
      end.
    termEndTime: Output only. End time of the term.
    termStartTime: Output only. Start time of the term.
    trialDuration: Output only. Specifies how long the trial lasts if trial
      definition is present in the subscription definition.
    trialPeriodEndTime: Output only. A trial period subscription will not
      generate a bill.
  """

  class ErrorStateInfoValueValuesEnum(_messages.Enum):
    r"""Output only. Additional information on why the subscription is in
    error state.

    Values:
      ERROR_STATE_INFO_UNSPECIFIED: Default value. Should not be used.
      CURRENT_SEGMENT_IS_PENDING: Current segment is pending.
      TERMINATION_SEGMENT_IS_PENDING: Termination segment is pending.
      SWITCH_CASE_FALL_THROUGH: Switch case fall through.
    """
    ERROR_STATE_INFO_UNSPECIFIED = 0
    CURRENT_SEGMENT_IS_PENDING = 1
    TERMINATION_SEGMENT_IS_PENDING = 2
    SWITCH_CASE_FALL_THROUGH = 3

  class StateValueValuesEnum(_messages.Enum):
    r"""A description of state of the subscription.

    Values:
      STATE_UNSPECIFIED: Default subscription state. Should not be used.
      ACTIVE: Active state means that the subscription is current and both
        entitlement and monetization are in working status.
      INACTIVE: Inactive means that none of the entitlement and monetization
        are running and the status may become ACTIVE in future.
      COMPLETED: The subscription ran past end date and entitlements and
        monetization are terminated or terminating.
      ERROR: The subscription is in an erroneous state. Please contact
        subscriptions team if this state is present.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    INACTIVE = 2
    COMPLETED = 3
    ERROR = 4

  class TermEndBehaviorValueValuesEnum(_messages.Enum):
    r"""Determine the subscription behavior when current terms end.

    Values:
      TERM_END_BEHAVIOR_UNSPECIFIED: Default term end behavior. Should not be
        used.
      RENEW_SAME_PLAN: Renews the subscription for another term.
      PERIODIC_SAME_PLAN: Renew the Term but subscription wil continue on a
        periodic basis.
      TERMINATE_AT_TERM_END: Terminates the subscription when the current term
        ends.
      RENEW_FOR_CUSTOM_TERM: Renews the subscription for a custom term.
    """
    TERM_END_BEHAVIOR_UNSPECIFIED = 0
    RENEW_SAME_PLAN = 1
    PERIODIC_SAME_PLAN = 2
    TERMINATE_AT_TERM_END = 3
    RENEW_FOR_CUSTOM_TERM = 4

  creationTime = _messages.StringField(1)
  definition = _messages.StringField(2)
  definitionDisplay = _messages.StringField(3)
  display = _messages.StringField(4)
  endTime = _messages.StringField(5)
  errorStateInfo = _messages.EnumField('ErrorStateInfoValueValuesEnum', 6)
  etag = _messages.StringField(7)
  externalId = _messages.StringField(8)
  name = _messages.StringField(9)
  nextTermDuration = _messages.StringField(10)
  nextUpdateParameters = _messages.MessageField('Parameter', 11, repeated=True)
  nextUpdateTime = _messages.StringField(12)
  owningResource = _messages.StringField(13)
  parameters = _messages.MessageField('Parameter', 14, repeated=True)
  startTime = _messages.StringField(15)
  state = _messages.EnumField('StateValueValuesEnum', 16)
  termDefinitionDisplay = _messages.StringField(17)
  termDefinitionId = _messages.StringField(18)
  termEndBehavior = _messages.EnumField('TermEndBehaviorValueValuesEnum', 19)
  termEndTime = _messages.StringField(20)
  termStartTime = _messages.StringField(21)
  trialDuration = _messages.StringField(22)
  trialPeriodEndTime = _messages.StringField(23)


class SubscriptionInfo(_messages.Message):
  r"""Message containing information for the appliance subscription.

  Enums:
    UpdateTypeValueValuesEnum: Input only. The time when the update will be
      applied. Only needed for an update.

  Fields:
    effectiveTime: Input only. The timestamp when update will be appliance if
      update_type is SPECIFIC_DATE.
    subscriptionTerm: Required. Input only. Customer selected subscription
      term.
    subscriptions: Optional. List of subscription details.
    updateType: Input only. The time when the update will be applied. Only
      needed for an update.
  """

  class UpdateTypeValueValuesEnum(_messages.Enum):
    r"""Input only. The time when the update will be applied. Only needed for
    an update.

    Values:
      CHANGE_TIME_TYPE_UNSPECIFIED: Default value. Should not be used.
      END_OF_PERIOD: End of the period.
      END_OF_TERM: End of the term.
      SPECIFIC_DATE: At a specific date.
      EARLIEST_POSSIBLE: At the earliest time possible.
    """
    CHANGE_TIME_TYPE_UNSPECIFIED = 0
    END_OF_PERIOD = 1
    END_OF_TERM = 2
    SPECIFIC_DATE = 3
    EARLIEST_POSSIBLE = 4

  effectiveTime = _messages.StringField(1)
  subscriptionTerm = _messages.StringField(2)
  subscriptions = _messages.MessageField('Subscription', 3, repeated=True)
  updateType = _messages.EnumField('UpdateTypeValueValuesEnum', 4)


class TimeOfDay(_messages.Message):
  r"""Represents a time of day. The date and time zone are either not
  significant or are specified elsewhere. An API may choose to allow leap
  seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.

  Fields:
    hours: Hours of a day in 24 hour format. Must be greater than or equal to
      0 and typically must be less than or equal to 23. An API may choose to
      allow the value "24:00:00" for scenarios like business closing time.
    minutes: Minutes of an hour. Must be greater than or equal to 0 and less
      than or equal to 59.
    nanos: Fractions of seconds, in nanoseconds. Must be greater than or equal
      to 0 and less than or equal to 999,999,999.
    seconds: Seconds of a minute. Must be greater than or equal to 0 and
      typically must be less than or equal to 59. An API may allow the value
      60 if it allows leap-seconds.
  """

  hours = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minutes = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  nanos = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  seconds = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class TimeZone(_messages.Message):
  r"""Represents a time zone from the [IANA Time Zone
  Database](https://www.iana.org/time-zones).

  Fields:
    id: IANA Time Zone Database time zone. For example "America/New_York".
    version: Optional. IANA Time Zone Database version number. For example
      "2019a".
  """

  id = _messages.StringField(1)
  version = _messages.StringField(2)


class TransferManifest(_messages.Message):
  r"""A message used in OfflineExportFeature to contain information about the
  Manifest file.

  Fields:
    location: Required. Specifies the path to the manifest in Cloud Storage.
      An example path is `gs://bucket_name/path/manifest.csv`. The paths in
      the manifest file are relative to bucketname. For example, to export
      SOURCE_PATH/object1.pdf, manifest will have object1.pdf in the first
      column, followed by object version which is optional. Refer to
      https://cloud.google.com/storage-
      transfer/docs/manifest#object_storage_transfers for more details.
  """

  location = _messages.StringField(1)


class TransferResults(_messages.Message):
  r"""A message used in OfflineImportFeature and OnlineImportFeature to
  display the results of a transfer. NextID : 15

  Fields:
    applianceFilesInfoUri: Output only. A URI to a file containing information
      about all files that were found on the appliance.
    bucketProjectId: Output only. The project id represents the project id
      where the destination bucket resides.
    bytesCopiedCount: Output only. The total number of bytes successfully
      copied to the destination.
    bytesCopyFailedCount: Output only. The total number of bytes failed to be
      copied to the destination bucket.
    bytesFoundCount: Output only. The total number of bytes found.
    copyFinishDate: Output only. The date when the transfer of data is
      finished.
    directoriesFoundCount: Output only. The number of directories found while
      listing. For example, if the root directory of the transfer is `base/`
      and there are two other directories, `a/` and `b/` under this directory,
      the count after listing `base/`, `base/a/` and `base/b/` is 3.
    endTime: Output only. The time that this transfer finished.
    errorLog: Output only. A URI to a file containing information about any
      files/directories that could not be transferred, or blank if there were
      no errors.
    objectsCopiedCount: Output only. The number of objects successfully copied
      to the destination.
    objectsFoundCount: Output only. The number of objects found.
    startTime: Output only. The time that this transfer started.
  """

  applianceFilesInfoUri = _messages.StringField(1)
  bucketProjectId = _messages.StringField(2)
  bytesCopiedCount = _messages.IntegerField(3)
  bytesCopyFailedCount = _messages.IntegerField(4)
  bytesFoundCount = _messages.IntegerField(5)
  copyFinishDate = _messages.MessageField('Date', 6)
  directoriesFoundCount = _messages.IntegerField(7)
  endTime = _messages.StringField(8)
  errorLog = _messages.StringField(9)
  objectsCopiedCount = _messages.IntegerField(10)
  objectsFoundCount = _messages.IntegerField(11)
  startTime = _messages.StringField(12)


class TransferapplianceProjectsLocationsAppliancesCreateRequest(_messages.Message):
  r"""A TransferapplianceProjectsLocationsAppliancesCreateRequest object.

  Fields:
    appliance: A Appliance resource to be passed as the request body.
    applianceId: Required. Id of the Appliance. See
      https://google.aip.dev/133#user-specified-ids for naming specifications.
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and t he request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  appliance = _messages.MessageField('Appliance', 1)
  applianceId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class TransferapplianceProjectsLocationsAppliancesCredentialsGetRequest(_messages.Message):
  r"""A TransferapplianceProjectsLocationsAppliancesCredentialsGetRequest
  object.

  Fields:
    name: Required. Name of the resource.
  """

  name = _messages.StringField(1, required=True)


class TransferapplianceProjectsLocationsAppliancesDeleteRequest(_messages.Message):
  r"""A TransferapplianceProjectsLocationsAppliancesDeleteRequest object.

  Fields:
    etag: Strongly validated etag, computed by the server to ensure the client
      has an up-to-date value before proceeding. See
      https://google.aip.dev/154.
    name: Required. Name of the resource.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and t he request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class TransferapplianceProjectsLocationsAppliancesExportRequest(_messages.Message):
  r"""A TransferapplianceProjectsLocationsAppliancesExportRequest object.

  Fields:
    name: Required. Name of the appliance resource.
  """

  name = _messages.StringField(1, required=True)


class TransferapplianceProjectsLocationsAppliancesGetRequest(_messages.Message):
  r"""A TransferapplianceProjectsLocationsAppliancesGetRequest object.

  Fields:
    name: Required. Name of the resource.
  """

  name = _messages.StringField(1, required=True)


class TransferapplianceProjectsLocationsAppliancesListRequest(_messages.Message):
  r"""A TransferapplianceProjectsLocationsAppliancesListRequest object.

  Fields:
    filter: Filtering results. See https://google.aip.dev/160 for more
      details.
    orderBy: Field to sort by. See https://google.aip.dev/132#ordering for
      more details.
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will use the default size of 500.
      Maximum allowed page_size is 1000.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListAppliancesRequest.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class TransferapplianceProjectsLocationsAppliancesPatchRequest(_messages.Message):
  r"""A TransferapplianceProjectsLocationsAppliancesPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, updating an `Appliance` that does
      not exist will result in the creation of a new `Appliance`.
    appliance: A Appliance resource to be passed as the request body.
    name: Name of resource.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Appliance resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all populated fields will be overwritten.
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  allowMissing = _messages.BooleanField(1)
  appliance = _messages.MessageField('Appliance', 2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class TransferapplianceProjectsLocationsGetRequest(_messages.Message):
  r"""A TransferapplianceProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class TransferapplianceProjectsLocationsListRequest(_messages.Message):
  r"""A TransferapplianceProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class TransferapplianceProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A TransferapplianceProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class TransferapplianceProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A TransferapplianceProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class TransferapplianceProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A TransferapplianceProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class TransferapplianceProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A TransferapplianceProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class TransferapplianceProjectsLocationsOrdersCreateRequest(_messages.Message):
  r"""A TransferapplianceProjectsLocationsOrdersCreateRequest object.

  Fields:
    order: A Order resource to be passed as the request body.
    orderId: Required. Id of the requesting order. See
      https://google.aip.dev/133#user-specified-ids for naming specifications.
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and t he request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  order = _messages.MessageField('Order', 1)
  orderId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class TransferapplianceProjectsLocationsOrdersDeleteRequest(_messages.Message):
  r"""A TransferapplianceProjectsLocationsOrdersDeleteRequest object.

  Fields:
    etag: Strongly validated etag, computed by the server to ensure the client
      has an up-to-date value before proceeding. See
      https://google.aip.dev/154.
    name: Required. Name of the resource.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and t he request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class TransferapplianceProjectsLocationsOrdersGetRequest(_messages.Message):
  r"""A TransferapplianceProjectsLocationsOrdersGetRequest object.

  Fields:
    name: Required. Name of the resource.
  """

  name = _messages.StringField(1, required=True)


class TransferapplianceProjectsLocationsOrdersListRequest(_messages.Message):
  r"""A TransferapplianceProjectsLocationsOrdersListRequest object.

  Fields:
    filter: Filtering results. See https://google.aip.dev/160 for more
      details.
    orderBy: Field to sort by. See https://google.aip.dev/132#ordering for
      more details.
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick the default size of 500.
      Maximum allowed page_size is 1000.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListOrdersRequest.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class TransferapplianceProjectsLocationsOrdersPatchRequest(_messages.Message):
  r"""A TransferapplianceProjectsLocationsOrdersPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, updating a `Order` that does not
      exist will result in the creation of a new `Order`.
    name: name of resource.
    order: A Order resource to be passed as the request body.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and t he request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Order resource by the update. The fields specified in
      the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all populated fields will be overwritten.
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  order = _messages.MessageField('Order', 3)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class TransferapplianceProjectsLocationsOrdersSubmitRequest(_messages.Message):
  r"""A TransferapplianceProjectsLocationsOrdersSubmitRequest object.

  Fields:
    name: Required. Name of the Order resource to submit.
    submitOrderRequest: A SubmitOrderRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  submitOrderRequest = _messages.MessageField('SubmitOrderRequest', 2)


class TransferapplianceProjectsLocationsSavedAddressesCreateRequest(_messages.Message):
  r"""A TransferapplianceProjectsLocationsSavedAddressesCreateRequest object.

  Fields:
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and t he request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    savedAddress: A SavedAddress resource to be passed as the request body.
    savedAddressId: Required. Id of the requesting SavedAddress. See
      https://google.aip.dev/133#user-specified-ids for naming specifications.
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  savedAddress = _messages.MessageField('SavedAddress', 3)
  savedAddressId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class TransferapplianceProjectsLocationsSavedAddressesDeleteRequest(_messages.Message):
  r"""A TransferapplianceProjectsLocationsSavedAddressesDeleteRequest object.

  Fields:
    etag: Strongly validated etag, computed by the server to ensure the client
      has an up-to-date value before proceeding. See
      https://google.aip.dev/154.
    name: Required. Name of the resource.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and t he request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class TransferapplianceProjectsLocationsSavedAddressesGetRequest(_messages.Message):
  r"""A TransferapplianceProjectsLocationsSavedAddressesGetRequest object.

  Fields:
    name: Required. Name of the resource.
  """

  name = _messages.StringField(1, required=True)


class TransferapplianceProjectsLocationsSavedAddressesListRequest(_messages.Message):
  r"""A TransferapplianceProjectsLocationsSavedAddressesListRequest object.

  Fields:
    filter: Filtering results. See https://google.aip.dev/160 for more
      details.
    orderBy: Field to sort by. See https://google.aip.dev/132#ordering for
      more details.
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick the default size of 500.
      Maximum allowed page_size is 1000.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListSavedAddressesRequest.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class TransferapplianceProjectsLocationsSavedAddressesPatchRequest(_messages.Message):
  r"""A TransferapplianceProjectsLocationsSavedAddressesPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, updating a `SavedAddress` that
      does not exist will result in the creation of a new `SavedAddress`.
    name: name of resource.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and t he request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    savedAddress: A SavedAddress resource to be passed as the request body.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the SavedAddress resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all populated fields will be overwritten.
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  savedAddress = _messages.MessageField('SavedAddress', 4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class Value(_messages.Message):
  r"""A message containing parameter value.

  Fields:
    boolValue: Represents a boolean value.
    doubleValue: Represents a double value.
    int64Value: Represents an int64 value.
    stringValue: Represents a string value.
  """

  boolValue = _messages.BooleanField(1)
  doubleValue = _messages.FloatField(2)
  int64Value = _messages.IntegerField(3)
  stringValue = _messages.StringField(4)


class WeeklyWindow(_messages.Message):
  r"""Defines the weekly maintenance windows. The duration of the window is 8
  hours.

  Enums:
    DayValueValuesEnum: Allows to define schedule that runs on a specified day
      of the week.

  Fields:
    day: Allows to define schedule that runs on a specified day of the week.
    startTime: Start time of the window in UTC.
  """

  class DayValueValuesEnum(_messages.Enum):
    r"""Allows to define schedule that runs on a specified day of the week.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  day = _messages.EnumField('DayValueValuesEnum', 1)
  startTime = _messages.MessageField('TimeOfDay', 2)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
