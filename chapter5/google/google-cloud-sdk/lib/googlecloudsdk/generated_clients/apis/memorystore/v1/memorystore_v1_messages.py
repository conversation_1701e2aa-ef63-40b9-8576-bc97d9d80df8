"""Generated message classes for memorystore version v1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'memorystore'


class AOFConfig(_messages.Message):
  r"""Configuration for AOF based persistence.

  Enums:
    AppendFsyncValueValuesEnum: Optional. The fsync mode.

  Fields:
    appendFsync: Optional. The fsync mode.
  """

  class AppendFsyncValueValuesEnum(_messages.Enum):
    r"""Optional. The fsync mode.

    Values:
      APPEND_FSYNC_UNSPECIFIED: Not set. Default: EVERY_SEC
      NEVER: Never fsync. Normally Linux will flush data every 30 seconds with
        this configuration, but it's up to the kernel's exact tuning.
      EVERY_SEC: Fsync every second. You may lose 1 second of data if there is
        a disaster.
      ALWAYS: Fsync every time new write commands are appended to the AOF. The
        best data loss protection at the cost of performance.
    """
    APPEND_FSYNC_UNSPECIFIED = 0
    NEVER = 1
    EVERY_SEC = 2
    ALWAYS = 3

  appendFsync = _messages.EnumField('AppendFsyncValueValuesEnum', 1)


class AutomatedBackupConfig(_messages.Message):
  r"""The automated backup config for an instance.

  Enums:
    AutomatedBackupModeValueValuesEnum: Optional. The automated backup mode.
      If the mode is disabled, the other fields will be ignored.

  Fields:
    automatedBackupMode: Optional. The automated backup mode. If the mode is
      disabled, the other fields will be ignored.
    fixedFrequencySchedule: Optional. Trigger automated backups at a fixed
      frequency.
    retention: Optional. How long to keep automated backups before the backups
      are deleted. The value should be between 1 day and 365 days. If not
      specified, the default value is 35 days.
  """

  class AutomatedBackupModeValueValuesEnum(_messages.Enum):
    r"""Optional. The automated backup mode. If the mode is disabled, the
    other fields will be ignored.

    Values:
      AUTOMATED_BACKUP_MODE_UNSPECIFIED: Default value. Automated backup
        config is not specified.
      DISABLED: Automated backup config disabled.
      ENABLED: Automated backup config enabled.
    """
    AUTOMATED_BACKUP_MODE_UNSPECIFIED = 0
    DISABLED = 1
    ENABLED = 2

  automatedBackupMode = _messages.EnumField('AutomatedBackupModeValueValuesEnum', 1)
  fixedFrequencySchedule = _messages.MessageField('FixedFrequencySchedule', 2)
  retention = _messages.StringField(3)


class Backup(_messages.Message):
  r"""Backup of an instance.

  Enums:
    BackupTypeValueValuesEnum: Output only. Type of the backup.
    NodeTypeValueValuesEnum: Output only. Node type of the instance.
    StateValueValuesEnum: Output only. State of the backup.

  Fields:
    backupFiles: Output only. List of backup files of the backup.
    backupType: Output only. Type of the backup.
    createTime: Output only. The time when the backup was created.
    encryptionInfo: Output only. Encryption information of the backup.
    engineVersion: Output only. valkey-7.5/valkey-8.0, etc.
    expireTime: Output only. The time when the backup will expire.
    instance: Output only. Instance resource path of this backup.
    instanceUid: Output only. Instance uid of this backup.
    name: Identifier. Full resource path of the backup. the last part of the
      name is the backup id with the following format:
      [YYYYMMDDHHMMSS]_[Shorted Instance UID] OR customer specified while
      backup instance. Example: 20240515123000_1234
    nodeType: Output only. Node type of the instance.
    replicaCount: Output only. Number of replicas for the instance.
    shardCount: Output only. Number of shards for the instance.
    state: Output only. State of the backup.
    totalSizeBytes: Output only. Total size of the backup in bytes.
    uid: Output only. System assigned unique identifier of the backup.
  """

  class BackupTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of the backup.

    Values:
      BACKUP_TYPE_UNSPECIFIED: The default value, not set.
      ON_DEMAND: On-demand backup.
      AUTOMATED: Automated backup.
    """
    BACKUP_TYPE_UNSPECIFIED = 0
    ON_DEMAND = 1
    AUTOMATED = 2

  class NodeTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Node type of the instance.

    Values:
      NODE_TYPE_UNSPECIFIED: Not set.
      SHARED_CORE_NANO: Shared core nano.
      HIGHMEM_MEDIUM: High memory medium.
      HIGHMEM_XLARGE: High memory extra large.
      STANDARD_SMALL: Standard small.
    """
    NODE_TYPE_UNSPECIFIED = 0
    SHARED_CORE_NANO = 1
    HIGHMEM_MEDIUM = 2
    HIGHMEM_XLARGE = 3
    STANDARD_SMALL = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the backup.

    Values:
      STATE_UNSPECIFIED: The default value, not set.
      CREATING: The backup is being created.
      ACTIVE: The backup is active to be used.
      DELETING: The backup is being deleted.
      SUSPENDED: The backup is currently suspended due to reasons like project
        deletion, billing account closure, etc.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    SUSPENDED = 4

  backupFiles = _messages.MessageField('BackupFile', 1, repeated=True)
  backupType = _messages.EnumField('BackupTypeValueValuesEnum', 2)
  createTime = _messages.StringField(3)
  encryptionInfo = _messages.MessageField('EncryptionInfo', 4)
  engineVersion = _messages.StringField(5)
  expireTime = _messages.StringField(6)
  instance = _messages.StringField(7)
  instanceUid = _messages.StringField(8)
  name = _messages.StringField(9)
  nodeType = _messages.EnumField('NodeTypeValueValuesEnum', 10)
  replicaCount = _messages.IntegerField(11, variant=_messages.Variant.INT32)
  shardCount = _messages.IntegerField(12, variant=_messages.Variant.INT32)
  state = _messages.EnumField('StateValueValuesEnum', 13)
  totalSizeBytes = _messages.IntegerField(14)
  uid = _messages.StringField(15)


class BackupCollection(_messages.Message):
  r"""BackupCollection of an instance.

  Fields:
    createTime: Output only. The time when the backup collection was created.
    instance: Output only. The full resource path of the instance the backup
      collection belongs to. Example:
      projects/{project}/locations/{location}/instances/{instance}
    instanceUid: Output only. The instance uid of the backup collection.
    kmsKey: Output only. The KMS key used to encrypt the backups under this
      backup collection.
    name: Identifier. Full resource path of the backup collection.
    uid: Output only. System assigned unique identifier of the backup
      collection.
  """

  createTime = _messages.StringField(1)
  instance = _messages.StringField(2)
  instanceUid = _messages.StringField(3)
  kmsKey = _messages.StringField(4)
  name = _messages.StringField(5)
  uid = _messages.StringField(6)


class BackupFile(_messages.Message):
  r"""Backup is consisted of multiple backup files.

  Fields:
    createTime: Output only. The time when the backup file was created.
    fileName: Output only. e.g: .rdb
    sizeBytes: Output only. Size of the backup file in bytes.
  """

  createTime = _messages.StringField(1)
  fileName = _messages.StringField(2)
  sizeBytes = _messages.IntegerField(3)


class BackupInstanceRequest(_messages.Message):
  r"""Request for [BackupInstance].

  Fields:
    backupId: Optional. The id of the backup to be created. If not specified,
      the default value ([YYYYMMDDHHMMSS]_[Shortened Instance UID] is used.
    ttl: Optional. TTL for the backup to expire. Value range is 1 day to 100
      years. If not specified, the default value is 100 years.
  """

  backupId = _messages.StringField(1)
  ttl = _messages.StringField(2)


class CertChain(_messages.Message):
  r"""A certificate chain.

  Fields:
    certificates: The certificates that form the CA chain in order of leaf to
      root.
  """

  certificates = _messages.StringField(1, repeated=True)


class CertificateAuthority(_messages.Message):
  r"""A certificate authority for an instance.

  Fields:
    managedServerCa: A managed server certificate authority.
    name: Identifier. Unique name of the certificate authority. Format:
      projects/{project}/locations/{location}/instances/{instance}
  """

  managedServerCa = _messages.MessageField('ManagedCertificateAuthority', 1)
  name = _messages.StringField(2)


class ConnectionDetail(_messages.Message):
  r"""Information of each PSC connection.

  Fields:
    pscAutoConnection: Immutable. Detailed information of a PSC connection
      that is created through service connectivity automation.
    pscConnection: Detailed information of a PSC connection that is created by
      the user.
  """

  pscAutoConnection = _messages.MessageField('PscAutoConnection', 1)
  pscConnection = _messages.MessageField('PscConnection', 2)


class CrossInstanceReplicationConfig(_messages.Message):
  r"""Cross instance replication config.

  Enums:
    InstanceRoleValueValuesEnum: Required. The role of the instance in cross
      instance replication.

  Fields:
    instanceRole: Required. The role of the instance in cross instance
      replication.
    membership: Output only. An output only view of all the member instances
      participating in the cross instance replication. This view will be
      provided by every member instance irrespective of its instance
      role(primary or secondary). A primary instance can provide information
      about all the secondary instances replicating from it. However, a
      secondary instance only knows about the primary instance from which it
      is replicating. However, for scenarios, where the primary instance is
      unavailable(e.g. regional outage), a Getinstance request can be sent to
      any other member instance and this field will list all the member
      instances participating in cross instance replication.
    primaryInstance: Optional. Details of the primary instance that is used as
      the replication source for this secondary instance. This field is only
      set for a secondary instance.
    secondaryInstances: Optional. List of secondary instances that are
      replicating from this primary instance. This field is only set for a
      primary instance.
    updateTime: Output only. The last time cross instance replication config
      was updated.
  """

  class InstanceRoleValueValuesEnum(_messages.Enum):
    r"""Required. The role of the instance in cross instance replication.

    Values:
      INSTANCE_ROLE_UNSPECIFIED: instance role is not set. The behavior is
        equivalent to NONE.
      NONE: This instance does not participate in cross instance replication.
        It is an independent instance and does not replicate to or from any
        other instances.
      PRIMARY: A instance that allows both reads and writes. Any data written
        to this instance is also replicated to the attached secondary
        instances.
      SECONDARY: A instance that allows only reads and replicates data from a
        primary instance.
    """
    INSTANCE_ROLE_UNSPECIFIED = 0
    NONE = 1
    PRIMARY = 2
    SECONDARY = 3

  instanceRole = _messages.EnumField('InstanceRoleValueValuesEnum', 1)
  membership = _messages.MessageField('Membership', 2)
  primaryInstance = _messages.MessageField('RemoteInstance', 3)
  secondaryInstances = _messages.MessageField('RemoteInstance', 4, repeated=True)
  updateTime = _messages.StringField(5)


class DiscoveryEndpoint(_messages.Message):
  r"""Represents an endpoint for clients to connect to the instance.

  Fields:
    address: Output only. IP address of the exposed endpoint clients connect
      to.
    network: Output only. The network where the IP address of the discovery
      endpoint will be reserved, in the form of
      projects/{network_project}/global/networks/{network_id}.
    port: Output only. The port number of the exposed endpoint.
  """

  address = _messages.StringField(1)
  network = _messages.StringField(2)
  port = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EncryptionInfo(_messages.Message):
  r"""EncryptionInfo describes the encryption information of a cluster.

  Enums:
    EncryptionTypeValueValuesEnum: Output only. Type of encryption.
    KmsKeyPrimaryStateValueValuesEnum: Output only. The state of the primary
      version of the KMS key perceived by the system. This field is not
      populated in backups.

  Fields:
    encryptionType: Output only. Type of encryption.
    kmsKeyPrimaryState: Output only. The state of the primary version of the
      KMS key perceived by the system. This field is not populated in backups.
    kmsKeyVersions: Output only. KMS key versions that are being used to
      protect the data at-rest.
    lastUpdateTime: Output only. The most recent time when the encryption info
      was updated.
  """

  class EncryptionTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of encryption.

    Values:
      TYPE_UNSPECIFIED: Encryption type not specified. Defaults to
        GOOGLE_DEFAULT_ENCRYPTION.
      GOOGLE_DEFAULT_ENCRYPTION: The data is encrypted at rest with a key that
        is fully managed by Google. No key version will be populated. This is
        the default state.
      CUSTOMER_MANAGED_ENCRYPTION: The data is encrypted at rest with a key
        that is managed by the customer. KMS key versions will be populated.
    """
    TYPE_UNSPECIFIED = 0
    GOOGLE_DEFAULT_ENCRYPTION = 1
    CUSTOMER_MANAGED_ENCRYPTION = 2

  class KmsKeyPrimaryStateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the primary version of the KMS key perceived
    by the system. This field is not populated in backups.

    Values:
      KMS_KEY_STATE_UNSPECIFIED: The default value. This value is unused.
      ENABLED: The KMS key is enabled and correctly configured.
      PERMISSION_DENIED: Permission denied on the KMS key.
      DISABLED: The KMS key is disabled.
      DESTROYED: The KMS key is destroyed.
      DESTROY_SCHEDULED: The KMS key is scheduled to be destroyed.
      EKM_KEY_UNREACHABLE_DETECTED: The EKM key is unreachable.
      BILLING_DISABLED: Billing is disabled for the project.
      UNKNOWN_FAILURE: All other unknown failures.
    """
    KMS_KEY_STATE_UNSPECIFIED = 0
    ENABLED = 1
    PERMISSION_DENIED = 2
    DISABLED = 3
    DESTROYED = 4
    DESTROY_SCHEDULED = 5
    EKM_KEY_UNREACHABLE_DETECTED = 6
    BILLING_DISABLED = 7
    UNKNOWN_FAILURE = 8

  encryptionType = _messages.EnumField('EncryptionTypeValueValuesEnum', 1)
  kmsKeyPrimaryState = _messages.EnumField('KmsKeyPrimaryStateValueValuesEnum', 2)
  kmsKeyVersions = _messages.StringField(3, repeated=True)
  lastUpdateTime = _messages.StringField(4)


class ExportBackupRequest(_messages.Message):
  r"""Request for [ExportBackup].

  Fields:
    gcsBucket: Google Cloud Storage bucket, like "my-bucket".
  """

  gcsBucket = _messages.StringField(1)


class FixedFrequencySchedule(_messages.Message):
  r"""This schedule allows the backup to be triggered at a fixed frequency
  (currently only daily is supported).

  Fields:
    startTime: Required. The start time of every automated backup in UTC. It
      must be set to the start of an hour. This field is required.
  """

  startTime = _messages.MessageField('TimeOfDay', 1)


class GcsBackupSource(_messages.Message):
  r"""Backups that stored in Cloud Storage buckets. The Cloud Storage buckets
  need to be the same region as the instances.

  Fields:
    uris: Optional. Example: gs://bucket1/object1,
      gs://bucket2/folder2/object2
  """

  uris = _messages.StringField(1, repeated=True)


class Instance(_messages.Message):
  r"""A Memorystore instance.

  Enums:
    AuthorizationModeValueValuesEnum: Optional. Immutable. Authorization mode
      of the instance.
    ModeValueValuesEnum: Optional. The mode config for the instance.
    NodeTypeValueValuesEnum: Optional. Machine type for individual nodes of
      the instance.
    StateValueValuesEnum: Output only. Current state of the instance.
    TransitEncryptionModeValueValuesEnum: Optional. Immutable. In-transit
      encryption mode of the instance.

  Messages:
    EngineConfigsValue: Optional. User-provided engine configurations for the
      instance.
    LabelsValue: Optional. Labels to represent user-provided metadata.

  Fields:
    allowFewerZonesDeployment: Optional. Immutable. Allows customers to
      specify if they are okay with deploying a multi-zone instance in less
      than 3 zones. Once set, if there is a zonal outage during the instance
      creation, the instance will only be deployed in 2 zones, and stay within
      the 2 zones for its lifecycle.
    asyncInstanceEndpointsDeletionEnabled: Optional. If true, instance
      endpoints that are created and registered by customers can be deleted
      asynchronously. That is, such an instance endpoint can be de-registered
      before the forwarding rules in the instance endpoint are deleted.
    authorizationMode: Optional. Immutable. Authorization mode of the
      instance.
    automatedBackupConfig: Optional. The automated backup config for the
      instance.
    backupCollection: Output only. The backup collection full resource name.
      Example:
      projects/{project}/locations/{location}/backupCollections/{collection}
    createTime: Output only. Creation timestamp of the instance.
    crossInstanceReplicationConfig: Optional. The config for cross instance
      replication.
    deletionProtectionEnabled: Optional. If set to true deletion of the
      instance will fail.
    discoveryEndpoints: Output only. Deprecated: The discovery_endpoints
      parameter is deprecated. As a result, it will not be populated if the
      connections are created using endpoints parameter. Instead of this
      parameter, for discovery, use endpoints.connections.pscConnection and
      endpoints.connections.pscAutoConnection with connectionType
      CONNECTION_TYPE_DISCOVERY.
    encryptionInfo: Output only. Encryption information of the data at rest of
      the cluster.
    endpoints: Optional. Endpoints for the instance.
    engineConfigs: Optional. User-provided engine configurations for the
      instance.
    engineVersion: Optional. Engine version of the instance.
    gcsSource: Optional. Immutable. Backups that stored in Cloud Storage
      buckets. The Cloud Storage buckets need to be the same region as the
      instances. Read permission is required to import from the provided Cloud
      Storage Objects.
    kmsKey: Optional. The KMS key used to encrypt the at-rest data of the
      cluster.
    labels: Optional. Labels to represent user-provided metadata.
    maintenancePolicy: Optional. The maintenance policy for the instance. If
      not provided, the maintenance event will be performed based on
      Memorystore internal rollout schedule.
    maintenanceSchedule: Output only. Published maintenance schedule.
    managedBackupSource: Optional. Immutable. Backups that generated and
      managed by memorystore service.
    mode: Optional. The mode config for the instance.
    name: Identifier. Unique name of the instance. Format:
      projects/{project}/locations/{location}/instances/{instance}
    nodeConfig: Output only. Configuration of individual nodes of the
      instance.
    nodeType: Optional. Machine type for individual nodes of the instance.
    ondemandMaintenance: Optional. Input only. Ondemand maintenance for the
      instance.
    persistenceConfig: Optional. Persistence configuration of the instance.
    pscAttachmentDetails: Output only. Service attachment details to configure
      PSC connections.
    pscAutoConnections: Optional. Immutable. Deprecated: Use the
      endpoints.connections.psc_auto_connection value instead.
    replicaCount: Optional. Number of replica nodes per shard. If omitted the
      default is 0 replicas.
    satisfiesPzi: Optional. Output only. Reserved for future use.
    satisfiesPzs: Optional. Output only. Reserved for future use.
    shardCount: Optional. Number of shards for the instance.
    simulateMaintenanceEvent: Optional. Input only. Simulate a maintenance
      event.
    state: Output only. Current state of the instance.
    stateInfo: Output only. Additional information about the state of the
      instance.
    transitEncryptionMode: Optional. Immutable. In-transit encryption mode of
      the instance.
    uid: Output only. System assigned, unique identifier for the instance.
    updateTime: Output only. Latest update timestamp of the instance.
    zoneDistributionConfig: Optional. Immutable. Zone distribution
      configuration of the instance for node allocation.
  """

  class AuthorizationModeValueValuesEnum(_messages.Enum):
    r"""Optional. Immutable. Authorization mode of the instance.

    Values:
      AUTHORIZATION_MODE_UNSPECIFIED: Not set.
      AUTH_DISABLED: Authorization disabled.
      IAM_AUTH: IAM basic authorization.
    """
    AUTHORIZATION_MODE_UNSPECIFIED = 0
    AUTH_DISABLED = 1
    IAM_AUTH = 2

  class ModeValueValuesEnum(_messages.Enum):
    r"""Optional. The mode config for the instance.

    Values:
      MODE_UNSPECIFIED: Mode is not specified.
      STANDALONE: Deprecated: Use CLUSTER_DISABLED instead.
      CLUSTER: Instance is in cluster mode.
      CLUSTER_DISABLED: Cluster mode is disabled for the instance.
    """
    MODE_UNSPECIFIED = 0
    STANDALONE = 1
    CLUSTER = 2
    CLUSTER_DISABLED = 3

  class NodeTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Machine type for individual nodes of the instance.

    Values:
      NODE_TYPE_UNSPECIFIED: Not set.
      SHARED_CORE_NANO: Shared core nano.
      HIGHMEM_MEDIUM: High memory medium.
      HIGHMEM_XLARGE: High memory extra large.
      STANDARD_SMALL: Standard small.
    """
    NODE_TYPE_UNSPECIFIED = 0
    SHARED_CORE_NANO = 1
    HIGHMEM_MEDIUM = 2
    HIGHMEM_XLARGE = 3
    STANDARD_SMALL = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the instance.

    Values:
      STATE_UNSPECIFIED: Not set.
      CREATING: Instance is being created.
      ACTIVE: Instance has been created and is usable.
      UPDATING: Instance is being updated.
      DELETING: Instance is being deleted.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    UPDATING = 3
    DELETING = 4

  class TransitEncryptionModeValueValuesEnum(_messages.Enum):
    r"""Optional. Immutable. In-transit encryption mode of the instance.

    Values:
      TRANSIT_ENCRYPTION_MODE_UNSPECIFIED: Not set.
      TRANSIT_ENCRYPTION_DISABLED: In-transit encryption is disabled.
      SERVER_AUTHENTICATION: Server-managed encryption is used for in-transit
        encryption.
    """
    TRANSIT_ENCRYPTION_MODE_UNSPECIFIED = 0
    TRANSIT_ENCRYPTION_DISABLED = 1
    SERVER_AUTHENTICATION = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EngineConfigsValue(_messages.Message):
    r"""Optional. User-provided engine configurations for the instance.

    Messages:
      AdditionalProperty: An additional property for a EngineConfigsValue
        object.

    Fields:
      additionalProperties: Additional properties of type EngineConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EngineConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels to represent user-provided metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  allowFewerZonesDeployment = _messages.BooleanField(1)
  asyncInstanceEndpointsDeletionEnabled = _messages.BooleanField(2)
  authorizationMode = _messages.EnumField('AuthorizationModeValueValuesEnum', 3)
  automatedBackupConfig = _messages.MessageField('AutomatedBackupConfig', 4)
  backupCollection = _messages.StringField(5)
  createTime = _messages.StringField(6)
  crossInstanceReplicationConfig = _messages.MessageField('CrossInstanceReplicationConfig', 7)
  deletionProtectionEnabled = _messages.BooleanField(8)
  discoveryEndpoints = _messages.MessageField('DiscoveryEndpoint', 9, repeated=True)
  encryptionInfo = _messages.MessageField('EncryptionInfo', 10)
  endpoints = _messages.MessageField('InstanceEndpoint', 11, repeated=True)
  engineConfigs = _messages.MessageField('EngineConfigsValue', 12)
  engineVersion = _messages.StringField(13)
  gcsSource = _messages.MessageField('GcsBackupSource', 14)
  kmsKey = _messages.StringField(15)
  labels = _messages.MessageField('LabelsValue', 16)
  maintenancePolicy = _messages.MessageField('MaintenancePolicy', 17)
  maintenanceSchedule = _messages.MessageField('MaintenanceSchedule', 18)
  managedBackupSource = _messages.MessageField('ManagedBackupSource', 19)
  mode = _messages.EnumField('ModeValueValuesEnum', 20)
  name = _messages.StringField(21)
  nodeConfig = _messages.MessageField('NodeConfig', 22)
  nodeType = _messages.EnumField('NodeTypeValueValuesEnum', 23)
  ondemandMaintenance = _messages.BooleanField(24)
  persistenceConfig = _messages.MessageField('PersistenceConfig', 25)
  pscAttachmentDetails = _messages.MessageField('PscAttachmentDetail', 26, repeated=True)
  pscAutoConnections = _messages.MessageField('PscAutoConnection', 27, repeated=True)
  replicaCount = _messages.IntegerField(28, variant=_messages.Variant.INT32)
  satisfiesPzi = _messages.BooleanField(29)
  satisfiesPzs = _messages.BooleanField(30)
  shardCount = _messages.IntegerField(31, variant=_messages.Variant.INT32)
  simulateMaintenanceEvent = _messages.BooleanField(32)
  state = _messages.EnumField('StateValueValuesEnum', 33)
  stateInfo = _messages.MessageField('StateInfo', 34)
  transitEncryptionMode = _messages.EnumField('TransitEncryptionModeValueValuesEnum', 35)
  uid = _messages.StringField(36)
  updateTime = _messages.StringField(37)
  zoneDistributionConfig = _messages.MessageField('ZoneDistributionConfig', 38)


class InstanceEndpoint(_messages.Message):
  r"""InstanceEndpoint consists of PSC connections that are created as a group
  in each VPC network for accessing the instance. In each group, there shall
  be one connection for each service attachment in the cluster.

  Fields:
    connections: Optional. A group of PSC connections. They are created in the
      same VPC network, one for each service attachment in the cluster.
  """

  connections = _messages.MessageField('ConnectionDetail', 1, repeated=True)


class ListBackupCollectionsResponse(_messages.Message):
  r"""Response for [ListBackupCollections].

  Fields:
    backupCollections: A list of backupCollections in the project. If the
      `location_id` in the parent field of the request is "-", all regions
      available to the project are queried, and the results aggregated. If in
      such an aggregated query a location is unavailable, a placeholder
      backupCollection entry is included in the response with the `name` field
      set to a value of the form
      `projects/{project_id}/locations/{location_id}/backupCollections/`- and
      the `status` field set to ERROR and `status_message` field set to
      "location not available for ListBackupCollections".
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: Locations that could not be reached.
  """

  backupCollections = _messages.MessageField('BackupCollection', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListBackupsResponse(_messages.Message):
  r"""Response for [ListBackups].

  Fields:
    backups: A list of backups in the project.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: Backups that could not be reached.
  """

  backups = _messages.MessageField('Backup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListInstancesResponse(_messages.Message):
  r"""Response message for ListInstances.

  Fields:
    instances: If the {location} requested was "-" the response contains a
      list of instances from all locations. Instances in unreachable locations
      will be omitted.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached.
  """

  instances = _messages.MessageField('Instance', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class MaintenancePolicy(_messages.Message):
  r"""Maintenance policy per instance.

  Fields:
    createTime: Output only. The time when the policy was created.
    updateTime: Output only. The time when the policy was updated.
    weeklyMaintenanceWindow: Optional. Maintenance window that is applied to
      resources covered by this policy. Minimum 1. For the current version,
      the maximum number of weekly_window is expected to be one.
  """

  createTime = _messages.StringField(1)
  updateTime = _messages.StringField(2)
  weeklyMaintenanceWindow = _messages.MessageField('WeeklyMaintenanceWindow', 3, repeated=True)


class MaintenanceSchedule(_messages.Message):
  r"""Upcoming maintenance schedule.

  Fields:
    endTime: Output only. The end time of any upcoming scheduled maintenance
      for this instance.
    startTime: Output only. The start time of any upcoming scheduled
      maintenance for this instance.
  """

  endTime = _messages.StringField(1)
  startTime = _messages.StringField(2)


class ManagedBackupSource(_messages.Message):
  r"""Backups that generated and managed by memorystore.

  Fields:
    backup: Optional. Example: //memorystore.googleapis.com/projects/{project}
      /locations/{location}/backupCollections/{collection}/backups/{backup} A
      shorter version (without the prefix) of the backup name is also
      supported, like projects/{project}/locations/{location}/backupCollection
      s/{collection}/backups/{backup_id} In this case, it assumes the backup
      is under memorystore.googleapis.com.
  """

  backup = _messages.StringField(1)


class ManagedCertificateAuthority(_messages.Message):
  r"""A managed certificate authority.

  Fields:
    caCerts: PEM encoded CA certificate chains for managed server
      authentication.
  """

  caCerts = _messages.MessageField('CertChain', 1, repeated=True)


class Membership(_messages.Message):
  r"""An output only view of all the member instances participating in the
  cross instance replication.

  Fields:
    primaryInstance: Output only. The primary instance that acts as the source
      of replication for the secondary instances.
    secondaryInstances: Output only. The list of secondary instances
      replicating from the primary instance.
  """

  primaryInstance = _messages.MessageField('RemoteInstance', 1)
  secondaryInstances = _messages.MessageField('RemoteInstance', 2, repeated=True)


class MemorystoreProjectsLocationsBackupCollectionsBackupsDeleteRequest(_messages.Message):
  r"""A MemorystoreProjectsLocationsBackupCollectionsBackupsDeleteRequest
  object.

  Fields:
    name: Required. Instance backup resource name using the form: `projects/{p
      roject_id}/locations/{location_id}/backupCollections/{backup_collection_
      id}/backups/{backup_id}`
    requestId: Optional. Idempotent request UUID.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class MemorystoreProjectsLocationsBackupCollectionsBackupsExportRequest(_messages.Message):
  r"""A MemorystoreProjectsLocationsBackupCollectionsBackupsExportRequest
  object.

  Fields:
    exportBackupRequest: A ExportBackupRequest resource to be passed as the
      request body.
    name: Required. Instance backup resource name using the form: `projects/{p
      roject_id}/locations/{location_id}/backupCollections/{backup_collection_
      id}/backups/{backup_id}`
  """

  exportBackupRequest = _messages.MessageField('ExportBackupRequest', 1)
  name = _messages.StringField(2, required=True)


class MemorystoreProjectsLocationsBackupCollectionsBackupsGetRequest(_messages.Message):
  r"""A MemorystoreProjectsLocationsBackupCollectionsBackupsGetRequest object.

  Fields:
    name: Required. Instance backup resource name using the form: `projects/{p
      roject_id}/locations/{location_id}/backupCollections/{backup_collection_
      id}/backups/{backup_id}`
  """

  name = _messages.StringField(1, required=True)


class MemorystoreProjectsLocationsBackupCollectionsBackupsListRequest(_messages.Message):
  r"""A MemorystoreProjectsLocationsBackupCollectionsBackupsListRequest
  object.

  Fields:
    pageSize: Optional. The maximum number of items to return. If not
      specified, a default value of 1000 will be used by the service.
      Regardless of the page_size value, the response may include a partial
      list and a caller should only rely on response's `next_page_token` to
      determine if there are more clusters left to be queried.
    pageToken: Optional. The `next_page_token` value returned from a previous
      [ListBackupCollections] request, if any.
    parent: Required. The resource name of the backupCollection using the
      form: `projects/{project_id}/locations/{location_id}/backupCollections/{
      backup_collection_id}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class MemorystoreProjectsLocationsBackupCollectionsGetRequest(_messages.Message):
  r"""A MemorystoreProjectsLocationsBackupCollectionsGetRequest object.

  Fields:
    name: Required. Instance backupCollection resource name using the form: `p
      rojects/{project_id}/locations/{location_id}/backupCollections/{backup_c
      ollection_id}` where `location_id` refers to a Google Cloud region.
  """

  name = _messages.StringField(1, required=True)


class MemorystoreProjectsLocationsBackupCollectionsListRequest(_messages.Message):
  r"""A MemorystoreProjectsLocationsBackupCollectionsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of items to return. If not
      specified, a default value of 1000 will be used by the service.
      Regardless of the page_size value, the response may include a partial
      list and a caller should only rely on response's `next_page_token` to
      determine if there are more clusters left to be queried.
    pageToken: Optional. The `next_page_token` value returned from a previous
      [ListBackupCollections] request, if any.
    parent: Required. The resource name of the backupCollection location using
      the form: `projects/{project_id}/locations/{location_id}` where
      `location_id` refers to a Google Cloud region.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class MemorystoreProjectsLocationsGetRequest(_messages.Message):
  r"""A MemorystoreProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class MemorystoreProjectsLocationsInstancesBackupRequest(_messages.Message):
  r"""A MemorystoreProjectsLocationsInstancesBackupRequest object.

  Fields:
    backupInstanceRequest: A BackupInstanceRequest resource to be passed as
      the request body.
    name: Required. Instance resource name using the form:
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
      where `location_id` refers to a Google Cloud region.
  """

  backupInstanceRequest = _messages.MessageField('BackupInstanceRequest', 1)
  name = _messages.StringField(2, required=True)


class MemorystoreProjectsLocationsInstancesCreateRequest(_messages.Message):
  r"""A MemorystoreProjectsLocationsInstancesCreateRequest object.

  Fields:
    instance: A Instance resource to be passed as the request body.
    instanceId: Required. The ID to use for the instance, which will become
      the final component of the instance's resource name. This value is
      subject to the following restrictions: * Must be 4-63 characters in
      length * Must begin with a letter or digit * Must contain only lowercase
      letters, digits, and hyphens * Must not end with a hyphen * Must be
      unique within a location
    parent: Required. The parent resource where this instance will be created.
      Format: projects/{project}/locations/{location}
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  instance = _messages.MessageField('Instance', 1)
  instanceId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class MemorystoreProjectsLocationsInstancesDeleteRequest(_messages.Message):
  r"""A MemorystoreProjectsLocationsInstancesDeleteRequest object.

  Fields:
    name: Required. The name of the instance to delete. Format:
      projects/{project}/locations/{location}/instances/{instance}
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class MemorystoreProjectsLocationsInstancesGetCertificateAuthorityRequest(_messages.Message):
  r"""A MemorystoreProjectsLocationsInstancesGetCertificateAuthorityRequest
  object.

  Fields:
    name: Required. The name of the certificate authority. Format: projects/{p
      roject}/locations/{location}/instances/{instance}/certificateAuthority
  """

  name = _messages.StringField(1, required=True)


class MemorystoreProjectsLocationsInstancesGetRequest(_messages.Message):
  r"""A MemorystoreProjectsLocationsInstancesGetRequest object.

  Fields:
    name: Required. The name of the instance to retrieve. Format:
      projects/{project}/locations/{location}/instances/{instance}
  """

  name = _messages.StringField(1, required=True)


class MemorystoreProjectsLocationsInstancesListRequest(_messages.Message):
  r"""A MemorystoreProjectsLocationsInstancesListRequest object.

  Fields:
    filter: Optional. Expression for filtering results.
    orderBy: Optional. Sort results by a defined order. Supported values:
      "name", "create_time".
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent to list instances from. Format:
      projects/{project}/locations/{location}
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class MemorystoreProjectsLocationsInstancesPatchRequest(_messages.Message):
  r"""A MemorystoreProjectsLocationsInstancesPatchRequest object.

  Fields:
    instance: A Instance resource to be passed as the request body.
    name: Identifier. Unique name of the instance. Format:
      projects/{project}/locations/{location}/instances/{instance}
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. The list of fields to be updated on the instance. At
      least one field must be specified.
  """

  instance = _messages.MessageField('Instance', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class MemorystoreProjectsLocationsInstancesRescheduleMaintenanceRequest(_messages.Message):
  r"""A MemorystoreProjectsLocationsInstancesRescheduleMaintenanceRequest
  object.

  Fields:
    name: Required. Name of the instance to reschedule maintenance for:
      `projects/{project}/locations/{location_id}/instances/{instance}`
    rescheduleMaintenanceRequest: A RescheduleMaintenanceRequest resource to
      be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  rescheduleMaintenanceRequest = _messages.MessageField('RescheduleMaintenanceRequest', 2)


class MemorystoreProjectsLocationsListRequest(_messages.Message):
  r"""A MemorystoreProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class MemorystoreProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A MemorystoreProjectsLocationsOperationsCancelRequest object.

  Fields:
    name: The name of the operation resource to be cancelled.
  """

  name = _messages.StringField(1, required=True)


class MemorystoreProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A MemorystoreProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class MemorystoreProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A MemorystoreProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class MemorystoreProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A MemorystoreProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class NodeConfig(_messages.Message):
  r"""Represents configuration for nodes of the instance.

  Fields:
    sizeGb: Output only. Memory size in GB of the node.
  """

  sizeGb = _messages.FloatField(1)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of a long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class PersistenceConfig(_messages.Message):
  r"""Represents persistence configuration for a instance.

  Enums:
    ModeValueValuesEnum: Optional. Current persistence mode.

  Fields:
    aofConfig: Optional. AOF configuration. This field will be ignored if mode
      is not AOF.
    mode: Optional. Current persistence mode.
    rdbConfig: Optional. RDB configuration. This field will be ignored if mode
      is not RDB.
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""Optional. Current persistence mode.

    Values:
      PERSISTENCE_MODE_UNSPECIFIED: Not set.
      DISABLED: Persistence is disabled, and any snapshot data is deleted.
      RDB: RDB based persistence is enabled.
      AOF: AOF based persistence is enabled.
    """
    PERSISTENCE_MODE_UNSPECIFIED = 0
    DISABLED = 1
    RDB = 2
    AOF = 3

  aofConfig = _messages.MessageField('AOFConfig', 1)
  mode = _messages.EnumField('ModeValueValuesEnum', 2)
  rdbConfig = _messages.MessageField('RDBConfig', 3)


class PscAttachmentDetail(_messages.Message):
  r"""Configuration of a service attachment of the cluster, for creating PSC
  connections.

  Enums:
    ConnectionTypeValueValuesEnum: Output only. Type of Psc endpoint.

  Fields:
    connectionType: Output only. Type of Psc endpoint.
    serviceAttachment: Output only. Service attachment URI which your self-
      created PscConnection should use as target.
  """

  class ConnectionTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of Psc endpoint.

    Values:
      CONNECTION_TYPE_UNSPECIFIED: Connection Type is not set
      CONNECTION_TYPE_DISCOVERY: Connection that will be used for topology
        discovery.
      CONNECTION_TYPE_PRIMARY: Connection that will be used as primary
        endpoint to access primary.
      CONNECTION_TYPE_READER: Connection that will be used as reader endpoint
        to access replicas.
    """
    CONNECTION_TYPE_UNSPECIFIED = 0
    CONNECTION_TYPE_DISCOVERY = 1
    CONNECTION_TYPE_PRIMARY = 2
    CONNECTION_TYPE_READER = 3

  connectionType = _messages.EnumField('ConnectionTypeValueValuesEnum', 1)
  serviceAttachment = _messages.StringField(2)


class PscAutoConnection(_messages.Message):
  r"""Details of consumer resources in a PSC connection.

  Enums:
    ConnectionTypeValueValuesEnum: Output only. Type of the PSC connection.
    PscConnectionStatusValueValuesEnum: Output only. The status of the PSC
      connection: whether a connection exists and ACTIVE or it no longer
      exists. Please note that this value is updated periodically. Please use
      Private Service Connect APIs for the latest status.

  Fields:
    connectionType: Output only. Type of the PSC connection.
    forwardingRule: Output only. The URI of the consumer side forwarding rule.
      Format:
      projects/{project}/regions/{region}/forwardingRules/{forwarding_rule}
    ipAddress: Output only. The IP allocated on the consumer network for the
      PSC forwarding rule.
    network: Required. The network where the PSC endpoints are created, in the
      form of projects/{project_id}/global/networks/{network_id}.
    port: Optional. port will only be set for Primary/Reader or Discovery
      endpoint.
    projectId: Required. The consumer project_id where PSC connections are
      established. This should be the same project_id that the instance is
      being created in.
    pscConnectionId: Output only. The PSC connection id of the forwarding rule
      connected to the service attachment.
    pscConnectionStatus: Output only. The status of the PSC connection:
      whether a connection exists and ACTIVE or it no longer exists. Please
      note that this value is updated periodically. Please use Private Service
      Connect APIs for the latest status.
    serviceAttachment: Output only. The service attachment which is the target
      of the PSC connection, in the form of projects/{project-
      id}/regions/{region}/serviceAttachments/{service-attachment-id}.
  """

  class ConnectionTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of the PSC connection.

    Values:
      CONNECTION_TYPE_UNSPECIFIED: Connection Type is not set
      CONNECTION_TYPE_DISCOVERY: Connection that will be used for topology
        discovery.
      CONNECTION_TYPE_PRIMARY: Connection that will be used as primary
        endpoint to access primary.
      CONNECTION_TYPE_READER: Connection that will be used as reader endpoint
        to access replicas.
    """
    CONNECTION_TYPE_UNSPECIFIED = 0
    CONNECTION_TYPE_DISCOVERY = 1
    CONNECTION_TYPE_PRIMARY = 2
    CONNECTION_TYPE_READER = 3

  class PscConnectionStatusValueValuesEnum(_messages.Enum):
    r"""Output only. The status of the PSC connection: whether a connection
    exists and ACTIVE or it no longer exists. Please note that this value is
    updated periodically. Please use Private Service Connect APIs for the
    latest status.

    Values:
      PSC_CONNECTION_STATUS_UNSPECIFIED: PSC connection status is not
        specified.
      ACTIVE: The connection is active
      NOT_FOUND: Connection not found
    """
    PSC_CONNECTION_STATUS_UNSPECIFIED = 0
    ACTIVE = 1
    NOT_FOUND = 2

  connectionType = _messages.EnumField('ConnectionTypeValueValuesEnum', 1)
  forwardingRule = _messages.StringField(2)
  ipAddress = _messages.StringField(3)
  network = _messages.StringField(4)
  port = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  projectId = _messages.StringField(6)
  pscConnectionId = _messages.StringField(7)
  pscConnectionStatus = _messages.EnumField('PscConnectionStatusValueValuesEnum', 8)
  serviceAttachment = _messages.StringField(9)


class PscConnection(_messages.Message):
  r"""User created Psc connection configuration.

  Enums:
    ConnectionTypeValueValuesEnum: Output only. Type of the PSC connection.
    PscConnectionStatusValueValuesEnum: Output only. The status of the PSC
      connection: whether a connection exists and ACTIVE or it no longer
      exists. Please note that this value is updated periodically. Please use
      Private Service Connect APIs for the latest status.

  Fields:
    connectionType: Output only. Type of the PSC connection.
    forwardingRule: Required. The URI of the consumer side forwarding rule.
      Format:
      projects/{project}/regions/{region}/forwardingRules/{forwarding_rule}
    ipAddress: Required. The IP allocated on the consumer network for the PSC
      forwarding rule.
    network: Required. The consumer network where the IP address resides, in
      the form of projects/{project_id}/global/networks/{network_id}.
    port: Optional. port will only be set for Primary/Reader or Discovery
      endpoint.
    projectId: Output only. The consumer project_id where the forwarding rule
      is created from.
    pscConnectionId: Required. The PSC connection id of the forwarding rule
      connected to the service attachment.
    pscConnectionStatus: Output only. The status of the PSC connection:
      whether a connection exists and ACTIVE or it no longer exists. Please
      note that this value is updated periodically. Please use Private Service
      Connect APIs for the latest status.
    serviceAttachment: Required. The service attachment which is the target of
      the PSC connection, in the form of projects/{project-
      id}/regions/{region}/serviceAttachments/{service-attachment-id}.
  """

  class ConnectionTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of the PSC connection.

    Values:
      CONNECTION_TYPE_UNSPECIFIED: Connection Type is not set
      CONNECTION_TYPE_DISCOVERY: Connection that will be used for topology
        discovery.
      CONNECTION_TYPE_PRIMARY: Connection that will be used as primary
        endpoint to access primary.
      CONNECTION_TYPE_READER: Connection that will be used as reader endpoint
        to access replicas.
    """
    CONNECTION_TYPE_UNSPECIFIED = 0
    CONNECTION_TYPE_DISCOVERY = 1
    CONNECTION_TYPE_PRIMARY = 2
    CONNECTION_TYPE_READER = 3

  class PscConnectionStatusValueValuesEnum(_messages.Enum):
    r"""Output only. The status of the PSC connection: whether a connection
    exists and ACTIVE or it no longer exists. Please note that this value is
    updated periodically. Please use Private Service Connect APIs for the
    latest status.

    Values:
      PSC_CONNECTION_STATUS_UNSPECIFIED: PSC connection status is not
        specified.
      ACTIVE: The connection is active
      NOT_FOUND: Connection not found
    """
    PSC_CONNECTION_STATUS_UNSPECIFIED = 0
    ACTIVE = 1
    NOT_FOUND = 2

  connectionType = _messages.EnumField('ConnectionTypeValueValuesEnum', 1)
  forwardingRule = _messages.StringField(2)
  ipAddress = _messages.StringField(3)
  network = _messages.StringField(4)
  port = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  projectId = _messages.StringField(6)
  pscConnectionId = _messages.StringField(7)
  pscConnectionStatus = _messages.EnumField('PscConnectionStatusValueValuesEnum', 8)
  serviceAttachment = _messages.StringField(9)


class RDBConfig(_messages.Message):
  r"""Configuration for RDB based persistence.

  Enums:
    RdbSnapshotPeriodValueValuesEnum: Optional. Period between RDB snapshots.

  Fields:
    rdbSnapshotPeriod: Optional. Period between RDB snapshots.
    rdbSnapshotStartTime: Optional. Time that the first snapshot was/will be
      attempted, and to which future snapshots will be aligned. If not
      provided, the current time will be used.
  """

  class RdbSnapshotPeriodValueValuesEnum(_messages.Enum):
    r"""Optional. Period between RDB snapshots.

    Values:
      SNAPSHOT_PERIOD_UNSPECIFIED: Not set.
      ONE_HOUR: One hour.
      SIX_HOURS: Six hours.
      TWELVE_HOURS: Twelve hours.
      TWENTY_FOUR_HOURS: Twenty four hours.
    """
    SNAPSHOT_PERIOD_UNSPECIFIED = 0
    ONE_HOUR = 1
    SIX_HOURS = 2
    TWELVE_HOURS = 3
    TWENTY_FOUR_HOURS = 4

  rdbSnapshotPeriod = _messages.EnumField('RdbSnapshotPeriodValueValuesEnum', 1)
  rdbSnapshotStartTime = _messages.StringField(2)


class RemoteInstance(_messages.Message):
  r"""Details of the remote instance associated with this instance in a cross
  instance replication setup.

  Fields:
    instance: Optional. The full resource path of the remote instance in the
      format: projects//locations//instances/
    uid: Output only. The unique identifier of the remote instance.
  """

  instance = _messages.StringField(1)
  uid = _messages.StringField(2)


class RescheduleMaintenanceRequest(_messages.Message):
  r"""Request for rescheduling instance maintenance.

  Enums:
    RescheduleTypeValueValuesEnum: Required. If reschedule type is
      SPECIFIC_TIME, schedule_time must be set.

  Fields:
    rescheduleType: Required. If reschedule type is SPECIFIC_TIME,
      schedule_time must be set.
    scheduleTime: Optional. Timestamp when the maintenance shall be
      rescheduled to if reschedule_type=SPECIFIC_TIME, in RFC 3339 format.
      Example: `2012-11-15T16:19:00.094Z`.
  """

  class RescheduleTypeValueValuesEnum(_messages.Enum):
    r"""Required. If reschedule type is SPECIFIC_TIME, schedule_time must be
    set.

    Values:
      RESCHEDULE_TYPE_UNSPECIFIED: Not set.
      IMMEDIATE: If the user wants to schedule the maintenance to happen now.
      SPECIFIC_TIME: If the user wants to reschedule the maintenance to a
        specific time.
    """
    RESCHEDULE_TYPE_UNSPECIFIED = 0
    IMMEDIATE = 1
    SPECIFIC_TIME = 2

  rescheduleType = _messages.EnumField('RescheduleTypeValueValuesEnum', 1)
  scheduleTime = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StateInfo(_messages.Message):
  r"""Additional information about the state of the instance.

  Fields:
    updateInfo: Output only. Describes ongoing update when instance state is
      UPDATING.
  """

  updateInfo = _messages.MessageField('UpdateInfo', 1)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TimeOfDay(_messages.Message):
  r"""Represents a time of day. The date and time zone are either not
  significant or are specified elsewhere. An API may choose to allow leap
  seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.

  Fields:
    hours: Hours of a day in 24 hour format. Must be greater than or equal to
      0 and typically must be less than or equal to 23. An API may choose to
      allow the value "24:00:00" for scenarios like business closing time.
    minutes: Minutes of an hour. Must be greater than or equal to 0 and less
      than or equal to 59.
    nanos: Fractions of seconds, in nanoseconds. Must be greater than or equal
      to 0 and less than or equal to 999,999,999.
    seconds: Seconds of a minute. Must be greater than or equal to 0 and
      typically must be less than or equal to 59. An API may allow the value
      60 if it allows leap-seconds.
  """

  hours = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minutes = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  nanos = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  seconds = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class UpdateInfo(_messages.Message):
  r"""Represents information about instance with state UPDATING.

  Enums:
    TargetNodeTypeValueValuesEnum: Output only. Target node type for the
      instance.

  Fields:
    targetEngineVersion: Output only. Target engine version for the instance.
    targetNodeType: Output only. Target node type for the instance.
    targetReplicaCount: Output only. Target number of replica nodes per shard
      for the instance.
    targetShardCount: Output only. Target number of shards for the instance.
  """

  class TargetNodeTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Target node type for the instance.

    Values:
      NODE_TYPE_UNSPECIFIED: Not set.
      SHARED_CORE_NANO: Shared core nano.
      HIGHMEM_MEDIUM: High memory medium.
      HIGHMEM_XLARGE: High memory extra large.
      STANDARD_SMALL: Standard small.
    """
    NODE_TYPE_UNSPECIFIED = 0
    SHARED_CORE_NANO = 1
    HIGHMEM_MEDIUM = 2
    HIGHMEM_XLARGE = 3
    STANDARD_SMALL = 4

  targetEngineVersion = _messages.StringField(1)
  targetNodeType = _messages.EnumField('TargetNodeTypeValueValuesEnum', 2)
  targetReplicaCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  targetShardCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class WeeklyMaintenanceWindow(_messages.Message):
  r"""Time window specified for weekly operations.

  Enums:
    DayValueValuesEnum: Optional. Allows to define schedule that runs
      specified day of the week.

  Fields:
    day: Optional. Allows to define schedule that runs specified day of the
      week.
    startTime: Optional. Start time of the window in UTC.
  """

  class DayValueValuesEnum(_messages.Enum):
    r"""Optional. Allows to define schedule that runs specified day of the
    week.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  day = _messages.EnumField('DayValueValuesEnum', 1)
  startTime = _messages.MessageField('TimeOfDay', 2)


class ZoneDistributionConfig(_messages.Message):
  r"""Zone distribution configuration for allocation of instance resources.

  Enums:
    ModeValueValuesEnum: Optional. Current zone distribution mode. Defaults to
      MULTI_ZONE.

  Fields:
    mode: Optional. Current zone distribution mode. Defaults to MULTI_ZONE.
    zone: Optional. Defines zone where all resources will be allocated with
      SINGLE_ZONE mode. Ignored for MULTI_ZONE mode.
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""Optional. Current zone distribution mode. Defaults to MULTI_ZONE.

    Values:
      ZONE_DISTRIBUTION_MODE_UNSPECIFIED: Not Set. Default: MULTI_ZONE
      MULTI_ZONE: Distribute resources across 3 zones picked at random within
        the region.
      SINGLE_ZONE: Provision resources in a single zone. Zone field must be
        specified.
    """
    ZONE_DISTRIBUTION_MODE_UNSPECIFIED = 0
    MULTI_ZONE = 1
    SINGLE_ZONE = 2

  mode = _messages.EnumField('ModeValueValuesEnum', 1)
  zone = _messages.StringField(2)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
