"""Generated message classes for edgecontainer version v1alpha.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'edgecontainer'


class Authorization(_messages.Message):
  r"""RBAC policy that will be applied and managed by GEC.

  Fields:
    adminUsers: Required. User that will be granted the cluster-admin role on
      the cluster, providing full access to the cluster. Currently, this is a
      singular field, but will be expanded to allow multiple admins in the
      future.
  """

  adminUsers = _messages.MessageField('ClusterUser', 1)


class Binding(_messages.Message):
  r"""Binding represents a role binding in the IAM policy.

  Fields:
    members: Optional. The members to bind the role to.
    role: Required. The role in the IAM policy to bind the members to.
  """

  members = _messages.MessageField('Principal', 1, repeated=True)
  role = _messages.StringField(2)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class ChannelConfig(_messages.Message):
  r"""Configuration for a release channel.

  Fields:
    defaultVersion: Output only. Default version for this release channel,
      e.g.: "1.4.0".
  """

  defaultVersion = _messages.StringField(1)


class CloudRouter(_messages.Message):
  r"""The Cloud Router info.

  Fields:
    name: The associated Cloud Router name.
  """

  name = _messages.StringField(1)


class CloudVpn(_messages.Message):
  r"""The Cloud VPN info.

  Fields:
    gateway: The created Cloud VPN gateway name.
  """

  gateway = _messages.StringField(1)


class Cluster(_messages.Message):
  r"""A Google Distributed Cloud Edge Kubernetes cluster.

  Enums:
    ClusterTypeValueValuesEnum: Optional. Cluster Type to specify if the
      cluster is BAREMETAL or VIRTUAL
    ReleaseChannelValueValuesEnum: Optional. The release channel a cluster is
      subscribed to.
    StatusValueValuesEnum: Output only. The current status of the cluster.

  Messages:
    LabelsValue: Labels associated with this resource.

  Fields:
    authorization: Required. Immutable. RBAC policy that will be applied and
      managed by GEC.
    clusterCaCertificate: Output only. The PEM-encoded public certificate of
      the cluster's CA.
    clusterType: Optional. Cluster Type to specify if the cluster is BAREMETAL
      or VIRTUAL
    connectionState: Output only. The current connection state of the cluster.
    containerRuntimeConfig: Optional. The container runtime config of the
      cluster.
    controlPlane: Optional. The configuration of the cluster control plane.
    controlPlaneEncryption: Optional. Remote control plane disk encryption
      options. This field is only used when enabling CMEK support.
    controlPlaneVersion: Output only. The control plane release version
    createTime: Output only. The time when the cluster was created.
    defaultMaxPodsPerNode: Optional. The default maximum number of pods per
      node used if a maximum value is not specified explicitly for a node pool
      in this cluster. If unspecified, the Kubernetes default value will be
      used.
    enableClusterIsolation: Optional. This denotes if the cluster is required
      to be isolated. go/cluster-isolation-in-gdcc-cluster
    enableRemoteBackup: Optional. If true, the remote backup/restore feature
      will be enabled for this cluster.
    endpoint: Output only. The IP address of the Kubernetes API server.
    externalLoadBalancerAddressPools: Optional. External load balancer pools
      for cluster.
    externalLoadBalancerIpv4AddressPools: Optional. IPv4 address pools for
      cluster data plane external load balancing.
    externalLoadBalancerIpv6AddressPools: Optional. IPv6 address pools for
      cluster data plane external load balancing.
    fleet: Required. Fleet configuration.
    googleGroupAuthentication: Optional. The Google Group authentication
      config of the cluster.
    labels: Labels associated with this resource.
    maintenanceEvents: Output only. All the maintenance events scheduled for
      the cluster, including the ones ongoing, planned for the future and done
      in the past (up to 90 days).
    maintenancePolicy: Optional. Cluster-wide maintenance policy
      configuration.
    name: Required. The resource name of the cluster.
    networking: Required. Cluster-wide networking configuration.
    nodeVersion: Output only. The lowest release version among all worker
      nodes. This field can be empty if the cluster does not have any worker
      nodes.
    port: Output only. The port number of the Kubernetes API server.
    releaseChannel: Optional. The release channel a cluster is subscribed to.
    status: Output only. The current status of the cluster.
    survivabilityConfig: Optional. Configuration of the cluster survivability,
      e.g., for the case when network connectivity is lost. Note: This only
      applies to local control plane clusters.
    systemAddonsConfig: Optional. The configuration of the system add-ons.
    targetVersion: Optional. The target cluster version. For example: "1.5.0".
    updateTime: Output only. The time when the cluster was last updated.
    upgradeSettings: Optional. Upgrade settings for the cluster.
    zoneStorageEncryption: Optional. The zone storage encryption configuration
  """

  class ClusterTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Cluster Type to specify if the cluster is BAREMETAL or
    VIRTUAL

    Values:
      CLUSTER_TYPE_UNSPECIFIED: Unspecified cluster type
      BAREMETAL: Cluster is a baremetal cluster
      VIRTUAL: Cluster is a virtual cluster
    """
    CLUSTER_TYPE_UNSPECIFIED = 0
    BAREMETAL = 1
    VIRTUAL = 2

  class ReleaseChannelValueValuesEnum(_messages.Enum):
    r"""Optional. The release channel a cluster is subscribed to.

    Values:
      RELEASE_CHANNEL_UNSPECIFIED: Unspecified release channel. This will
        default to the REGULAR channel.
      NONE: No release channel.
      REGULAR: Regular release channel.
    """
    RELEASE_CHANNEL_UNSPECIFIED = 0
    NONE = 1
    REGULAR = 2

  class StatusValueValuesEnum(_messages.Enum):
    r"""Output only. The current status of the cluster.

    Values:
      STATUS_UNSPECIFIED: Status unknown.
      PROVISIONING: The cluster is being created.
      RUNNING: The cluster is created and fully usable.
      DELETING: The cluster is being deleted.
      ERROR: The status indicates that some errors occurred while
        reconciling/deleting the cluster.
      RECONCILING: The cluster is undergoing some work such as version
        upgrades, etc.
    """
    STATUS_UNSPECIFIED = 0
    PROVISIONING = 1
    RUNNING = 2
    DELETING = 3
    ERROR = 4
    RECONCILING = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels associated with this resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  authorization = _messages.MessageField('Authorization', 1)
  clusterCaCertificate = _messages.StringField(2)
  clusterType = _messages.EnumField('ClusterTypeValueValuesEnum', 3)
  connectionState = _messages.MessageField('ConnectionState', 4)
  containerRuntimeConfig = _messages.MessageField('ContainerRuntimeConfig', 5)
  controlPlane = _messages.MessageField('ControlPlane', 6)
  controlPlaneEncryption = _messages.MessageField('ControlPlaneEncryption', 7)
  controlPlaneVersion = _messages.StringField(8)
  createTime = _messages.StringField(9)
  defaultMaxPodsPerNode = _messages.IntegerField(10, variant=_messages.Variant.INT32)
  enableClusterIsolation = _messages.BooleanField(11)
  enableRemoteBackup = _messages.BooleanField(12)
  endpoint = _messages.StringField(13)
  externalLoadBalancerAddressPools = _messages.MessageField('ExternalLoadBalancerPool', 14, repeated=True)
  externalLoadBalancerIpv4AddressPools = _messages.StringField(15, repeated=True)
  externalLoadBalancerIpv6AddressPools = _messages.StringField(16, repeated=True)
  fleet = _messages.MessageField('Fleet', 17)
  googleGroupAuthentication = _messages.MessageField('GoogleGroupAuthenticationConfig', 18)
  labels = _messages.MessageField('LabelsValue', 19)
  maintenanceEvents = _messages.MessageField('MaintenanceEvent', 20, repeated=True)
  maintenancePolicy = _messages.MessageField('MaintenancePolicy', 21)
  name = _messages.StringField(22)
  networking = _messages.MessageField('ClusterNetworking', 23)
  nodeVersion = _messages.StringField(24)
  port = _messages.IntegerField(25, variant=_messages.Variant.INT32)
  releaseChannel = _messages.EnumField('ReleaseChannelValueValuesEnum', 26)
  status = _messages.EnumField('StatusValueValuesEnum', 27)
  survivabilityConfig = _messages.MessageField('SurvivabilityConfig', 28)
  systemAddonsConfig = _messages.MessageField('SystemAddonsConfig', 29)
  targetVersion = _messages.StringField(30)
  updateTime = _messages.StringField(31)
  upgradeSettings = _messages.MessageField('UpgradeSettings', 32)
  zoneStorageEncryption = _messages.MessageField('ZoneStorageEncryption', 33)


class ClusterNetworking(_messages.Message):
  r"""Cluster-wide networking configuration.

  Enums:
    NetworkTypeValueValuesEnum: Output only. IP addressing type of this
      cluster i.e. SINGLESTACK_V4 vs DUALSTACK_V4_V6

  Fields:
    clusterIpv4CidrBlocks: Required. All pods in the cluster are assigned an
      RFC1918 IPv4 address from these blocks. Only a single block is
      supported. This field cannot be changed after creation.
    clusterIpv6CidrBlocks: Immutable. If specified, dual stack mode is enabled
      and all pods in the cluster are assigned an IPv6 address from these
      blocks alongside from an IPv4 address. Only a single block is supported.
      This field cannot be changed after creation.
    networkType: Output only. IP addressing type of this cluster i.e.
      SINGLESTACK_V4 vs DUALSTACK_V4_V6
    servicesIpv4CidrBlocks: Required. All services in the cluster are assigned
      an RFC1918 IPv4 address from these blocks. Only a single block is
      supported. This field cannot be changed after creation.
    servicesIpv6CidrBlocks: Immutable. If specified, dual stack mode is
      enabled and all services in the cluster are assigned an IPv6 address
      from these blocks alongside from an IPv4 address. Only a single block is
      supported. This field cannot be changed after creation.
  """

  class NetworkTypeValueValuesEnum(_messages.Enum):
    r"""Output only. IP addressing type of this cluster i.e. SINGLESTACK_V4 vs
    DUALSTACK_V4_V6

    Values:
      NETWORK_TYPE_UNSPECIFIED: Unknown cluster type
      SINGLESTACK_V4: SingleStack v4 address only cluster
      DUALSTACK_V4_V6: DualStack cluster - support v4 and v6 address
    """
    NETWORK_TYPE_UNSPECIFIED = 0
    SINGLESTACK_V4 = 1
    DUALSTACK_V4_V6 = 2

  clusterIpv4CidrBlocks = _messages.StringField(1, repeated=True)
  clusterIpv6CidrBlocks = _messages.StringField(2, repeated=True)
  networkType = _messages.EnumField('NetworkTypeValueValuesEnum', 3)
  servicesIpv4CidrBlocks = _messages.StringField(4, repeated=True)
  servicesIpv6CidrBlocks = _messages.StringField(5, repeated=True)


class ClusterUser(_messages.Message):
  r"""A user principal for an RBAC policy.

  Fields:
    username: Required. An active Google username.
  """

  username = _messages.StringField(1)


class ConfigData(_messages.Message):
  r"""Config data holds all the config related data for the zone.

  Fields:
    availableExternalLbPoolsIpv4: list of available v4 ip pools for external
      loadbalancer
    availableExternalLbPoolsIpv6: list of available v6 ip pools for external
      loadbalancer
  """

  availableExternalLbPoolsIpv4 = _messages.StringField(1, repeated=True)
  availableExternalLbPoolsIpv6 = _messages.StringField(2, repeated=True)


class ConnectionState(_messages.Message):
  r"""ConnectionState holds the current connection state from the cluster to
  Google.

  Enums:
    StateValueValuesEnum: Output only. The current connection state.

  Fields:
    state: Output only. The current connection state.
    updateTime: Output only. The time when the connection state was last
      changed.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current connection state.

    Values:
      STATE_UNSPECIFIED: Unknown connection state.
      DISCONNECTED: This cluster is currently disconnected from Google.
      CONNECTED: This cluster is currently connected to Google.
      CONNECTED_AND_SYNCING: This cluster is currently connected to Google,
        but may have recently reconnected after a disconnection. It is still
        syncing back.
    """
    STATE_UNSPECIFIED = 0
    DISCONNECTED = 1
    CONNECTED = 2
    CONNECTED_AND_SYNCING = 3

  state = _messages.EnumField('StateValueValuesEnum', 1)
  updateTime = _messages.StringField(2)


class ContainerRuntimeConfig(_messages.Message):
  r"""Container runtime config of the cluster.

  Enums:
    DefaultContainerRuntimeValueValuesEnum: Optional. The default container
      runtime to be configured in the cluster.

  Fields:
    defaultContainerRuntime: Optional. The default container runtime to be
      configured in the cluster.
  """

  class DefaultContainerRuntimeValueValuesEnum(_messages.Enum):
    r"""Optional. The default container runtime to be configured in the
    cluster.

    Values:
      DEFAULT_CONTAINER_RUNTIME_UNSPECIFIED: Container runtime not specified.
      RUNC: Use runc as the default container runtime in the cluster.
      GVISOR: Use gVisor as the default container runtime in the cluster.
    """
    DEFAULT_CONTAINER_RUNTIME_UNSPECIFIED = 0
    RUNC = 1
    GVISOR = 2

  defaultContainerRuntime = _messages.EnumField('DefaultContainerRuntimeValueValuesEnum', 1)


class ControlPlane(_messages.Message):
  r"""Configuration of the cluster control plane.

  Fields:
    local: Local control plane configuration. Warning: Local control plane
      clusters must be created in their own project. Local control plane
      clusters cannot coexist in the same project with any other type of
      clusters, including non-GDCE clusters. Mixing local control plane GDCE
      clusters with any other type of clusters in the same project can result
      in data loss.
    remote: Remote control plane configuration.
  """

  local = _messages.MessageField('Local', 1)
  remote = _messages.MessageField('Remote', 2)


class ControlPlaneEncryption(_messages.Message):
  r"""Configuration for Customer-managed KMS key support for control plane
  nodes.

  Enums:
    KmsKeyStateValueValuesEnum: Output only. Availability of the Cloud KMS
      CryptoKey. If not `KEY_AVAILABLE`, then nodes may go offline as they
      cannot access their local data. This can be caused by a lack of
      permissions to use the key, or if the key is disabled or deleted.
    ResourceStateValueValuesEnum: Output only. The current resource state
      associated with the cmek.

  Fields:
    kmsKey: Optional. The Cloud KMS CryptoKey e.g. projects/{project}/location
      s/{location}/keyRings/{keyRing}/cryptoKeys/{cryptoKey} to use for
      protecting control plane disks. If not specified, a Google-managed key
      will be used instead.
    kmsKeyActiveVersion: Output only. The Cloud KMS CryptoKeyVersion currently
      in use for protecting control plane disks. Only applicable if kms_key is
      set.
    kmsKeyState: Output only. Availability of the Cloud KMS CryptoKey. If not
      `KEY_AVAILABLE`, then nodes may go offline as they cannot access their
      local data. This can be caused by a lack of permissions to use the key,
      or if the key is disabled or deleted.
    kmsStatus: Output only. Error status returned by Cloud KMS when using this
      key. This field may be populated only if `kms_key_state` is not
      `KMS_KEY_STATE_KEY_AVAILABLE`. If populated, this field contains the
      error status reported by Cloud KMS.
    resourceState: Output only. The current resource state associated with the
      cmek.
  """

  class KmsKeyStateValueValuesEnum(_messages.Enum):
    r"""Output only. Availability of the Cloud KMS CryptoKey. If not
    `KEY_AVAILABLE`, then nodes may go offline as they cannot access their
    local data. This can be caused by a lack of permissions to use the key, or
    if the key is disabled or deleted.

    Values:
      KMS_KEY_STATE_UNSPECIFIED: Unspecified.
      KMS_KEY_STATE_KEY_AVAILABLE: The key is available for use, and dependent
        resources should be accessible.
      KMS_KEY_STATE_KEY_UNAVAILABLE: The key is unavailable for an unspecified
        reason. Dependent resources may be inaccessible.
    """
    KMS_KEY_STATE_UNSPECIFIED = 0
    KMS_KEY_STATE_KEY_AVAILABLE = 1
    KMS_KEY_STATE_KEY_UNAVAILABLE = 2

  class ResourceStateValueValuesEnum(_messages.Enum):
    r"""Output only. The current resource state associated with the cmek.

    Values:
      RESOURCE_STATE_UNSPECIFIED: Default value.
      RESOURCE_STATE_LOCK_DOWN: The resource is in LOCK DOWN state.
      RESOURCE_STATE_LOCK_DOWN_PENDING: The resource is pending lock down.
    """
    RESOURCE_STATE_UNSPECIFIED = 0
    RESOURCE_STATE_LOCK_DOWN = 1
    RESOURCE_STATE_LOCK_DOWN_PENDING = 2

  kmsKey = _messages.StringField(1)
  kmsKeyActiveVersion = _messages.StringField(2)
  kmsKeyState = _messages.EnumField('KmsKeyStateValueValuesEnum', 3)
  kmsStatus = _messages.MessageField('Status', 4)
  resourceState = _messages.EnumField('ResourceStateValueValuesEnum', 5)


class CreateIdentityProviderRequest(_messages.Message):
  r"""Request proto to configure the identity provider for an organization.

  Fields:
    identityProvider: Required. The identity provider to configure.
    identityProviderId: Required. The identity provider id.
    requestId: Optional. A unique identifier for this request. Restricted to
      36 ASCII characters. A random UUID is recommended. This request is only
      idempotent if `request_id` is provided.
  """

  identityProvider = _messages.MessageField('IdentityProvider', 1)
  identityProviderId = _messages.StringField(2)
  requestId = _messages.StringField(3)


class DNSServer(_messages.Message):
  r"""Represents a DNS server for the zone.

  Fields:
    ipAddress: Output only. The IP address of the DNS server.
    tld: Output only. The DNS server's top level domain.
  """

  ipAddress = _messages.StringField(1)
  tld = _messages.StringField(2)


class Details(_messages.Message):
  r"""The created connection details.

  Enums:
    StateValueValuesEnum: The state of this connection.

  Fields:
    cloudRouter: The Cloud Router info.
    cloudVpns: Each connection has multiple Cloud VPN gateways.
    error: The error message. This is only populated when state=ERROR.
    state: The state of this connection.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of this connection.

    Values:
      STATE_UNSPECIFIED: Unknown.
      STATE_CONNECTED: Connected.
      STATE_CONNECTING: Still connecting.
      STATE_ERROR: Error occurred.
    """
    STATE_UNSPECIFIED = 0
    STATE_CONNECTED = 1
    STATE_CONNECTING = 2
    STATE_ERROR = 3

  cloudRouter = _messages.MessageField('CloudRouter', 1)
  cloudVpns = _messages.MessageField('CloudVpn', 2, repeated=True)
  error = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)


class DisableZonalServiceRequest(_messages.Message):
  r"""Disable ZonalService Request. The API will remove access for the service
  producers.

  Fields:
    requestId: Optional. Idempotent request UUID.
  """

  requestId = _messages.StringField(1)


class EdgecontainerOrganizationsLocationsGetRequest(_messages.Message):
  r"""A EdgecontainerOrganizationsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class EdgecontainerOrganizationsLocationsIdentityProvidersCreateRequest(_messages.Message):
  r"""A EdgecontainerOrganizationsLocationsIdentityProvidersCreateRequest
  object.

  Fields:
    createIdentityProviderRequest: A CreateIdentityProviderRequest resource to
      be passed as the request body.
    parent: Required. The resource name of the identity provider to configure.
      e.g. organizations/{organization}/locations/{location}
  """

  createIdentityProviderRequest = _messages.MessageField('CreateIdentityProviderRequest', 1)
  parent = _messages.StringField(2, required=True)


class EdgecontainerOrganizationsLocationsIdentityProvidersDeleteRequest(_messages.Message):
  r"""A EdgecontainerOrganizationsLocationsIdentityProvidersDeleteRequest
  object.

  Fields:
    cluster: The fully qualified name of the target BMUC for which the
      identity provider is to be deleted.
    name: Required. The resource name of the identity provider to delete. The
      name to be formatted as: organizations/{organization}/locations/{locatio
      n}/identityProviders/{identity_provider}
    requestId: Optional. A unique identifier for this request. Restricted to
      36 ASCII characters. A random UUID is recommended. This request is only
      idempotent if `request_id` is provided.
    zoneId: The zone id of the target zone of the infra cluster for which the
      identity provider is to be deleted.
  """

  cluster = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  zoneId = _messages.StringField(4)


class EdgecontainerOrganizationsLocationsIdentityProvidersGetRequest(_messages.Message):
  r"""A EdgecontainerOrganizationsLocationsIdentityProvidersGetRequest object.

  Fields:
    cluster: The cluster name of the target BMUC for which the identity
      provider is configured.
    name: Required. The canonical resource name of the identity provider. E.g.
      organizations/*/locations/*/identityProviders/*
    zoneId: The zone id of the target zone for which the identity provider is
      configured.
  """

  cluster = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  zoneId = _messages.StringField(3)


class EdgecontainerOrganizationsLocationsIdentityProvidersListRequest(_messages.Message):
  r"""A EdgecontainerOrganizationsLocationsIdentityProvidersListRequest
  object.

  Fields:
    cluster: The fully qualified name of the target BMUC for which the
      identity providers are to be listed.
    pageSize: Optional. The maximum number of resources to list.
    pageToken: Optional. A page token received from previous list request.
    parent: Required. The parent organization and region for the identity
      providers.
    zoneId: The zone id of the target zone of the infra cluster for which the
      identity providers are to be listed.
  """

  cluster = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  zoneId = _messages.StringField(5)


class EdgecontainerOrganizationsLocationsListRequest(_messages.Message):
  r"""A EdgecontainerOrganizationsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    includeUnrevealedLocations: If true, the returned list will include
      locations which are not yet revealed.
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  includeUnrevealedLocations = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  pageSize = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(6)


class EdgecontainerOrganizationsLocationsOperationsCancelRequest(_messages.Message):
  r"""A EdgecontainerOrganizationsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class EdgecontainerOrganizationsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A EdgecontainerOrganizationsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class EdgecontainerOrganizationsLocationsOperationsGetRequest(_messages.Message):
  r"""A EdgecontainerOrganizationsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class EdgecontainerOrganizationsLocationsOperationsListRequest(_messages.Message):
  r"""A EdgecontainerOrganizationsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
    returnPartialSuccess: When set to `true`, operations that are reachable
      are returned as normal, and those that are unreachable are returned in
      the [ListOperationsResponse.unreachable] field. This can only be `true`
      when reading across collections e.g. when `parent` is set to
      `"projects/example/locations/-"`. This field is not by default supported
      and will result in an `UNIMPLEMENTED` error if set unless explicitly
      documented otherwise in service or product specific documentation.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  returnPartialSuccess = _messages.BooleanField(5)


class EdgecontainerOrganizationsLocationsZonesGetRequest(_messages.Message):
  r"""A EdgecontainerOrganizationsLocationsZonesGetRequest object.

  Fields:
    name: Required. The canonical resource name of the zone. E.g.
      organizations/*/locations/*/zone/*
  """

  name = _messages.StringField(1, required=True)


class EdgecontainerOrganizationsLocationsZonesListRequest(_messages.Message):
  r"""A EdgecontainerOrganizationsLocationsZonesListRequest object.

  Fields:
    filter: Optional. Only resources matching this filter will be listed.
    orderBy: Optional. Specifies the order in which resources will be listed.
    pageSize: Optional. The maximum number of items to return.
    pageToken: Optional. The next_page_token value returned from a previous
      List request, if any.
    parent: Required. The parent organization and location.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class EdgecontainerOrganizationsLocationsZonesZonalProjectsEnableRequest(_messages.Message):
  r"""A EdgecontainerOrganizationsLocationsZonesZonalProjectsEnableRequest
  object.

  Fields:
    parent: Required. The parent zone where the project will be created
    requestId: Optional. A unique identifier for this request. Restricted to
      36 ASCII characters. A random UUID is recommended. This request is only
      idempotent if `request_id` is provided.
    zonalProject: A ZonalProject resource to be passed as the request body.
    zonalProjectId: Required. Specified project_id of the consumer project to
      be enabled.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  zonalProject = _messages.MessageField('ZonalProject', 3)
  zonalProjectId = _messages.StringField(4)


class EdgecontainerOrganizationsLocationsZonesZonalProjectsGetRequest(_messages.Message):
  r"""A EdgecontainerOrganizationsLocationsZonesZonalProjectsGetRequest
  object.

  Fields:
    name: Required. The canonical resource name of the zonal project. E.g.
      organizations/*/locations/*/zonalProjects/*
  """

  name = _messages.StringField(1, required=True)


class EdgecontainerOrganizationsLocationsZonesZonalProjectsListRequest(_messages.Message):
  r"""A EdgecontainerOrganizationsLocationsZonesZonalProjectsListRequest
  object.

  Fields:
    filter: Optional. Only resources matching this filter will be listed.
    orderBy: Optional. Specifies the order in which resources will be listed.
      Order by fields for the result.
    pageSize: Optional. The maximum number of resources to list.
    pageToken: Optional. A page token received from previous list request.
    parent: Required. The parent zone where the project will be created.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class EdgecontainerProjectsLocationsClustersCreateRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsClustersCreateRequest object.

  Fields:
    cluster: A Cluster resource to be passed as the request body.
    clusterId: Required. A client-specified unique identifier for the cluster.
    parent: Required. The parent location where this cluster will be created.
    requestId: A unique identifier for this request. Restricted to 36 ASCII
      characters. A random UUID is recommended. This request is only
      idempotent if `request_id` is provided.
  """

  cluster = _messages.MessageField('Cluster', 1)
  clusterId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class EdgecontainerProjectsLocationsClustersDeleteRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsClustersDeleteRequest object.

  Fields:
    name: Required. The resource name of the cluster.
    requestId: A unique identifier for this request. Restricted to 36 ASCII
      characters. A random UUID is recommended. This request is only
      idempotent if `request_id` is provided.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class EdgecontainerProjectsLocationsClustersGenerateAccessTokenRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsClustersGenerateAccessTokenRequest
  object.

  Fields:
    cluster: Required. The resource name of the cluster.
  """

  cluster = _messages.StringField(1, required=True)


class EdgecontainerProjectsLocationsClustersGenerateOfflineCredentialRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsClustersGenerateOfflineCredentialRequest
  object.

  Fields:
    cluster: Required. The resource name of the cluster.
  """

  cluster = _messages.StringField(1, required=True)


class EdgecontainerProjectsLocationsClustersGetRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsClustersGetRequest object.

  Fields:
    name: Required. The resource name of the cluster.
  """

  name = _messages.StringField(1, required=True)


class EdgecontainerProjectsLocationsClustersListRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsClustersListRequest object.

  Fields:
    filter: Only resources matching this filter will be listed.
    orderBy: Specifies the order in which resources will be listed.
    pageSize: The maximum number of resources to list.
    pageToken: A page token received from previous list request. A page token
      received from previous list request.
    parent: Required. The parent location, which owns this collection of
      clusters.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class EdgecontainerProjectsLocationsClustersNodePoolsCreateRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsClustersNodePoolsCreateRequest object.

  Fields:
    nodePool: A NodePool resource to be passed as the request body.
    nodePoolId: Required. A client-specified unique identifier for the node
      pool.
    parent: Required. The parent cluster where this node pool will be created.
    requestId: A unique identifier for this request. Restricted to 36 ASCII
      characters. A random UUID is recommended. This request is only
      idempotent if `request_id` is provided.
  """

  nodePool = _messages.MessageField('NodePool', 1)
  nodePoolId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class EdgecontainerProjectsLocationsClustersNodePoolsDeleteRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsClustersNodePoolsDeleteRequest object.

  Fields:
    name: Required. The resource name of the node pool.
    requestId: A unique identifier for this request. Restricted to 36 ASCII
      characters. A random UUID is recommended. This request is only
      idempotent if `request_id` is provided.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class EdgecontainerProjectsLocationsClustersNodePoolsGetRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsClustersNodePoolsGetRequest object.

  Fields:
    name: Required. The resource name of the node pool.
  """

  name = _messages.StringField(1, required=True)


class EdgecontainerProjectsLocationsClustersNodePoolsListRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsClustersNodePoolsListRequest object.

  Fields:
    filter: Only resources matching this filter will be listed.
    orderBy: Specifies the order in which resources will be listed.
    pageSize: The maximum number of resources to list.
    pageToken: A page token received from previous list request.
    parent: Required. The parent cluster, which owns this collection of node
      pools.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class EdgecontainerProjectsLocationsClustersNodePoolsPatchRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsClustersNodePoolsPatchRequest object.

  Fields:
    name: Required. The resource name of the node pool.
    nodePool: A NodePool resource to be passed as the request body.
    requestId: A unique identifier for this request. Restricted to 36 ASCII
      characters. A random UUID is recommended. This request is only
      idempotent if `request_id` is provided.
    updateMask: Field mask is used to specify the fields to be overwritten in
      the NodePool resource by the update. The fields specified in the
      update_mask are relative to the resource, not the full request. A field
      will be overwritten if it is in the mask. If the user does not provide a
      mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  nodePool = _messages.MessageField('NodePool', 2)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class EdgecontainerProjectsLocationsClustersPatchRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsClustersPatchRequest object.

  Fields:
    cluster: A Cluster resource to be passed as the request body.
    name: Required. The resource name of the cluster.
    requestId: A unique identifier for this request. Restricted to 36 ASCII
      characters. A random UUID is recommended. This request is only
      idempotent if `request_id` is provided.
    updateMask: Field mask is used to specify the fields to be overwritten in
      the Cluster resource by the update. The fields specified in the
      update_mask are relative to the resource, not the full request. A field
      will be overwritten if it is in the mask. If the user does not provide a
      mask then all fields will be overwritten.
  """

  cluster = _messages.MessageField('Cluster', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class EdgecontainerProjectsLocationsClustersUpgradeRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsClustersUpgradeRequest object.

  Fields:
    name: Required. The resource name of the cluster.
    upgradeClusterRequest: A UpgradeClusterRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  upgradeClusterRequest = _messages.MessageField('UpgradeClusterRequest', 2)


class EdgecontainerProjectsLocationsGetRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class EdgecontainerProjectsLocationsGetServerConfigRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsGetServerConfigRequest object.

  Fields:
    name: Required. The name (project and location) of the server config to
      get, specified in the format `projects/*/locations/*`.
  """

  name = _messages.StringField(1, required=True)


class EdgecontainerProjectsLocationsListRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    includeUnrevealedLocations: If true, the returned list will include
      locations which are not yet revealed.
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  includeUnrevealedLocations = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  pageSize = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(6)


class EdgecontainerProjectsLocationsMachinesGetRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsMachinesGetRequest object.

  Fields:
    name: Required. The resource name of the machine.
  """

  name = _messages.StringField(1, required=True)


class EdgecontainerProjectsLocationsMachinesListRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsMachinesListRequest object.

  Fields:
    filter: Only resources matching this filter will be listed.
    orderBy: Specifies the order in which resources will be listed.
    pageSize: The maximum number of resources to list.
    pageToken: A page token received from previous list request.
    parent: Required. The parent site, which owns this collection of machines.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class EdgecontainerProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class EdgecontainerProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class EdgecontainerProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class EdgecontainerProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
    returnPartialSuccess: When set to `true`, operations that are reachable
      are returned as normal, and those that are unreachable are returned in
      the [ListOperationsResponse.unreachable] field. This can only be `true`
      when reading across collections e.g. when `parent` is set to
      `"projects/example/locations/-"`. This field is not by default supported
      and will result in an `UNIMPLEMENTED` error if set unless explicitly
      documented otherwise in service or product specific documentation.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  returnPartialSuccess = _messages.BooleanField(5)


class EdgecontainerProjectsLocationsServiceAccountsCreateRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsServiceAccountsCreateRequest object.

  Fields:
    parent: Required. The resource name of the identity provider to configure.
      e.g. project/{project}/locations/{location}
    requestId: Optional. A unique identifier for this request. Restricted to
      36 ASCII characters. A random UUID is recommended. This request is only
      idempotent if `request_id` is provided.
    serviceAccount: A ServiceAccount resource to be passed as the request
      body.
    serviceAccountId: Required. The service account id.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  serviceAccount = _messages.MessageField('ServiceAccount', 3)
  serviceAccountId = _messages.StringField(4)


class EdgecontainerProjectsLocationsServiceAccountsDeleteRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsServiceAccountsDeleteRequest object.

  Fields:
    name: Required. The canonical resource name of the project service
      account. E.g. projects/{project}/locations/{location}/serviceAccounts/{s
      ervice_account}
  """

  name = _messages.StringField(1, required=True)


class EdgecontainerProjectsLocationsServiceAccountsGenerateKeyRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsServiceAccountsGenerateKeyRequest
  object.

  Fields:
    generateServiceAccountKeyRequest: A GenerateServiceAccountKeyRequest
      resource to be passed as the request body.
    name: Required. The canonical resource name of the project service
      account. E.g. projects/{project}/locations/{location}/serviceAccounts/{s
      ervice_account}
  """

  generateServiceAccountKeyRequest = _messages.MessageField('GenerateServiceAccountKeyRequest', 1)
  name = _messages.StringField(2, required=True)


class EdgecontainerProjectsLocationsServiceAccountsGetRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsServiceAccountsGetRequest object.

  Fields:
    name: Required. The canonical resource name of the project service
      account. E.g. projects/{project}/locations/{location}/serviceAccounts/{s
      ervice_account}
  """

  name = _messages.StringField(1, required=True)


class EdgecontainerProjectsLocationsServiceAccountsListRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsServiceAccountsListRequest object.

  Fields:
    filter: Optional. Only resources matching this filter will be listed.
    orderBy: Optional. Specifies the order in which resources will be listed.
      Order by fields for the result.
    pageSize: Optional. The maximum number of resources to list.
    pageToken: Optional. A page token received from previous list request.
    parent: Required. The parent location, which owns this collection of
      project service accounts.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class EdgecontainerProjectsLocationsServiceAccountsPatchRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsServiceAccountsPatchRequest object.

  Fields:
    name: Identifier. The canonical resource name of the project service
      account. E.g. projects/{project}/locations/{location}/serviceAccounts/{s
      ervice_account}
    serviceAccount: A ServiceAccount resource to be passed as the request
      body.
    updateMask: Optional. The list of fields to update.
  """

  name = _messages.StringField(1, required=True)
  serviceAccount = _messages.MessageField('ServiceAccount', 2)
  updateMask = _messages.StringField(3)


class EdgecontainerProjectsLocationsVpnConnectionsCreateRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsVpnConnectionsCreateRequest object.

  Fields:
    parent: Required. The parent location where this vpn connection will be
      created.
    requestId: A unique identifier for this request. Restricted to 36 ASCII
      characters. A random UUID is recommended. This request is only
      idempotent if `request_id` is provided.
    vpnConnection: A VpnConnection resource to be passed as the request body.
    vpnConnectionId: Required. The VPN connection identifier.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  vpnConnection = _messages.MessageField('VpnConnection', 3)
  vpnConnectionId = _messages.StringField(4)


class EdgecontainerProjectsLocationsVpnConnectionsDeleteRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsVpnConnectionsDeleteRequest object.

  Fields:
    name: Required. The resource name of the vpn connection.
    requestId: A unique identifier for this request. Restricted to 36 ASCII
      characters. A random UUID is recommended. This request is only
      idempotent if `request_id` is provided.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class EdgecontainerProjectsLocationsVpnConnectionsGetRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsVpnConnectionsGetRequest object.

  Fields:
    name: Required. The resource name of the vpn connection.
  """

  name = _messages.StringField(1, required=True)


class EdgecontainerProjectsLocationsVpnConnectionsListRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsVpnConnectionsListRequest object.

  Fields:
    filter: Only resources matching this filter will be listed.
    orderBy: Specifies the order in which resources will be listed.
    pageSize: The maximum number of resources to list.
    pageToken: A page token received from previous list request.
    parent: Required. The parent location, which owns this collection of VPN
      connections.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class EdgecontainerProjectsLocationsZonalServicesDisableRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsZonalServicesDisableRequest object.

  Fields:
    disableZonalServiceRequest: A DisableZonalServiceRequest resource to be
      passed as the request body.
    name: Required. The name of the service to disable.
  """

  disableZonalServiceRequest = _messages.MessageField('DisableZonalServiceRequest', 1)
  name = _messages.StringField(2, required=True)


class EdgecontainerProjectsLocationsZonalServicesEnableRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsZonalServicesEnableRequest object.

  Fields:
    enableZonalServiceRequest: A EnableZonalServiceRequest resource to be
      passed as the request body.
    parent: Required. The parent location, which owns this collection of
      services.
  """

  enableZonalServiceRequest = _messages.MessageField('EnableZonalServiceRequest', 1)
  parent = _messages.StringField(2, required=True)


class EdgecontainerProjectsLocationsZonalServicesGetRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsZonalServicesGetRequest object.

  Fields:
    name: Required. The resource name of the service.
  """

  name = _messages.StringField(1, required=True)


class EdgecontainerProjectsLocationsZonalServicesListRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsZonalServicesListRequest object.

  Fields:
    filter: Optional. Only resources matching this filter will be listed.
    orderBy: Optional. Specifies the order in which resources will be listed.
      Order by fields for the result.
    pageSize: Optional. The maximum number of resources to list.
    pageToken: Optional. A page token received from previous list request.
    parent: Required. The parent location, which owns this collection of
      services.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class EdgecontainerProjectsLocationsZonesGetIamPolicyRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsZonesGetIamPolicyRequest object.

  Fields:
    name: Required. The canonical name of the zone from which the IamPolicy is
      to be fetched. E.g. projects/*/locations/*/zones/*
  """

  name = _messages.StringField(1, required=True)


class EdgecontainerProjectsLocationsZonesSetIamPolicyRequest(_messages.Message):
  r"""A EdgecontainerProjectsLocationsZonesSetIamPolicyRequest object.

  Fields:
    name: Required. The canonical resource name of the zone.
      projects/{project}/locations/{location}/zones/{zone}
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EnableZonalServiceRequest(_messages.Message):
  r"""Enable ZonalService Request. The API will configure access for the
  service producers on the cluster to create service resources.

  Fields:
    requestId: Optional. Idempotent request UUID.
    zonalService: Required. The service to create.
    zonalServiceId: Optional. Specified zonal_service_id.
  """

  requestId = _messages.StringField(1)
  zonalService = _messages.MessageField('ZonalService', 2)
  zonalServiceId = _messages.StringField(3)


class ExternalLoadBalancerPool(_messages.Message):
  r"""External load balancer pool with custom config such as name, manual/auto
  assign, non-overlapping ipv4 and optional ipv6 address range.

  Fields:
    addressPool: Optional. Name of the external load balancer pool.
    avoidBuggyIps: Optional. If true, the pool omits IP addresses ending in .0
      and .255. Some network hardware drops traffic to these special
      addresses. Its default value is false.
    ipv4Range: Required. Non-overlapping IPv4 address range of the external
      load balancer pool.
    ipv6Range: Optional. Non-overlapping IPv6 address range of the external
      load balancer pool.
    manualAssign: Optional. If true, addresses in this pool are not
      automatically assigned to Kubernetes Services. If true, an IP address in
      this pool is used only when it is specified explicitly by a service. Its
      default value is false.
  """

  addressPool = _messages.StringField(1)
  avoidBuggyIps = _messages.BooleanField(2)
  ipv4Range = _messages.StringField(3, repeated=True)
  ipv6Range = _messages.StringField(4, repeated=True)
  manualAssign = _messages.BooleanField(5)


class Fleet(_messages.Message):
  r"""Fleet related configuration. Fleets are a Google Cloud concept for
  logically organizing clusters, letting you use and manage multi-cluster
  capabilities and apply consistent policies across your systems.

  Fields:
    membership: Output only. The name of the managed Hub Membership resource
      associated to this cluster. Membership names are formatted as
      `projects//locations/global/membership/`.
    project: Required. The name of the Fleet host project where this cluster
      will be registered. Project names are formatted as `projects/`.
  """

  membership = _messages.StringField(1)
  project = _messages.StringField(2)


class GenerateAccessTokenResponse(_messages.Message):
  r"""An access token for a cluster.

  Fields:
    accessToken: Output only. Access token to authenticate to k8s api-server.
    expireTime: Output only. Timestamp at which the token will expire.
  """

  accessToken = _messages.StringField(1)
  expireTime = _messages.StringField(2)


class GenerateOfflineCredentialResponse(_messages.Message):
  r"""An offline credential for a cluster.

  Fields:
    clientCertificate: Output only. Client certificate to authenticate to k8s
      api-server.
    clientKey: Output only. Client private key to authenticate to k8s api-
      server.
    expireTime: Output only. Timestamp at which this credential will expire.
    userId: Output only. Client's identity.
  """

  clientCertificate = _messages.StringField(1)
  clientKey = _messages.StringField(2)
  expireTime = _messages.StringField(3)
  userId = _messages.StringField(4)


class GenerateServiceAccountKeyRequest(_messages.Message):
  r"""Request proto for GenerateServiceAccountKey API.

  Fields:
    caCertPath: Optional. The CA cert path.
  """

  caCertPath = _messages.StringField(1)


class GenerateServiceAccountKeyResponse(_messages.Message):
  r"""Response proto for GenerateServiceAccountKey API.

  Fields:
    ca_cert_path: Output only. The CA cert path.
    format_version: Output only. The format version.
    name: Output only. The name of service identity.
    private_key: Output only. The private key.
    private_key_id: Output only. The private key id.
    project: Output only. The project that the service account belongs to.
    token_uri: Output only. The token URI.
    type: The credential type.
  """

  ca_cert_path = _messages.StringField(1)
  format_version = _messages.StringField(2)
  name = _messages.StringField(3)
  private_key = _messages.StringField(4)
  private_key_id = _messages.StringField(5)
  project = _messages.StringField(6)
  token_uri = _messages.StringField(7)
  type = _messages.StringField(8)


class GoogleGroupAuthenticationConfig(_messages.Message):
  r"""Google Group authentication config of the cluster. go/gdc-google-group-
  authentication

  Fields:
    enable: Optional. If true, the cluster will be configured to use Google
      Group authentication.
  """

  enable = _messages.BooleanField(1)


class IamPolicy(_messages.Message):
  r"""IamPolicy represents a IAM policy.

  Fields:
    bindings: Optional. The policy is a list of bindings.
    etag: Optional. The etag of the IAM policy.
  """

  bindings = _messages.MessageField('Binding', 1, repeated=True)
  etag = _messages.StringField(2)


class IdentityProvider(_messages.Message):
  r"""Represents an identity provider resource which represents the identity
  provider configuration for the organization.

  Messages:
    LabelsValue: Optional. Labels associated with this resource.

  Fields:
    cluster: The fully qualified name of the target BMUC for which the
      identity provider is to be configured.
    createTime: Output only. The time when the identity provider was created.
    deleteTime: Output only. The time when the identity provider was deleted.
    labels: Optional. Labels associated with this resource.
    name: Identifier. The canonical resource name of the identity provider.
      E.g. organizations/{organization}/locations/{location}/identityProviders
      /{identity_provider}
    oidcConfig: The OIDC provider configuration.
    updateTime: Output only. The time when the identity provider was last
      updated.
    zoneId: The zone id of the target zone of the infra cluster for which the
      identity provider is to be configured.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels associated with this resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  cluster = _messages.StringField(1)
  createTime = _messages.StringField(2)
  deleteTime = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  oidcConfig = _messages.MessageField('OIDCProviderConfig', 6)
  updateTime = _messages.StringField(7)
  zoneId = _messages.StringField(8)


class Ingress(_messages.Message):
  r"""Config for the Ingress add-on which allows customers to create an
  Ingress object to manage external access to the servers in a cluster. The
  add-on consists of istiod and istio-ingress.

  Fields:
    disabled: Optional. Whether Ingress is disabled.
    ipv4Vip: Optional. Ingress VIP.
  """

  disabled = _messages.BooleanField(1)
  ipv4Vip = _messages.StringField(2)


class ListClustersResponse(_messages.Message):
  r"""List of clusters in a location.

  Fields:
    clusters: Clusters in the location.
    nextPageToken: A token to retrieve next page of results.
    unreachable: Locations that could not be reached.
  """

  clusters = _messages.MessageField('Cluster', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListIdentityProvidersResponse(_messages.Message):
  r"""Response proto to list the identity providers for an organization.

  Fields:
    identityProviders: A list of identity providers matching the request.
    nextPageToken: A token to retrieve next page of results.
  """

  identityProviders = _messages.MessageField('IdentityProvider', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListMachinesResponse(_messages.Message):
  r"""List of machines in a site.

  Fields:
    machines: Machines in the site.
    nextPageToken: A token to retrieve next page of results.
    unreachable: Locations that could not be reached.
  """

  machines = _messages.MessageField('Machine', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListNodePoolsResponse(_messages.Message):
  r"""List of node pools in a cluster.

  Fields:
    nextPageToken: A token to retrieve next page of results.
    nodePools: Node pools in the cluster.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  nodePools = _messages.MessageField('NodePool', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
    unreachable: Unordered list. Unreachable resources. Populated when the
      request sets `ListOperationsRequest.return_partial_success` and reads
      across collections e.g. when attempting to list all resources across all
      supported locations.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListServiceAccountsResponse(_messages.Message):
  r"""List ServiceAccounts Response.

  Fields:
    nextPageToken: A token to retrieve next page of results.
    serviceAccounts: List of ServiceAccounts matching the request.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  serviceAccounts = _messages.MessageField('ServiceAccount', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListVpnConnectionsResponse(_messages.Message):
  r"""List of VPN connections in a location.

  Fields:
    nextPageToken: A token to retrieve next page of results.
    unreachable: Locations that could not be reached.
    vpnConnections: VpnConnections in the location.
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  vpnConnections = _messages.MessageField('VpnConnection', 3, repeated=True)


class ListZonalProjectsResponse(_messages.Message):
  r"""List of consumer projects in a organization response.

  Fields:
    nextPageToken: A token to retrieve next page of results.
    unreachable: zones that could not be reached.
    zonalProjects: Clusters in the location.
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  zonalProjects = _messages.MessageField('ZonalProject', 3, repeated=True)


class ListZonalServicesResponse(_messages.Message):
  r"""List ZonalServices Response.

  Fields:
    nextPageToken: A token to retrieve next page of results.
    unreachable: Locations that could not be reached.
    zonalServices: ZonalServices in the location.
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  zonalServices = _messages.MessageField('ZonalService', 3, repeated=True)


class ListZonesResponse(_messages.Message):
  r"""Response message for listing zones.

  Fields:
    nextPageToken: A token to retrieve next page of results.
    unreachable: Locations that could not be reached.
    zones: A list of zones matching the request.
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  zones = _messages.MessageField('Zone', 3, repeated=True)


class Local(_messages.Message):
  r"""Configuration specific to clusters with a control plane hosted locally.
  Warning: Local control plane clusters must be created in their own project.
  Local control plane clusters cannot coexist in the same project with any
  other type of clusters, including non-GDCE clusters. Mixing local control
  plane GDCE clusters with any other type of clusters in the same project can
  result in data loss.

  Enums:
    SharedDeploymentPolicyValueValuesEnum: Policy configuration about how user
      applications are deployed.

  Fields:
    controlPlaneNodeStorageSchema: Optional. Name for the storage schema of
      control plane nodes.
    machineFilter: Only machines matching this filter will be allowed to host
      control plane nodes. The filtering language accepts strings like
      "name=", and is documented here: [AIP-160](https://google.aip.dev/160).
    nodeCount: The number of nodes to serve as replicas of the Control Plane.
    nodeLocation: Name of the Google Distributed Cloud Edge zones where this
      node pool will be created. For example: `us-central1-edge-customer-a`.
    sharedDeploymentPolicy: Policy configuration about how user applications
      are deployed.
  """

  class SharedDeploymentPolicyValueValuesEnum(_messages.Enum):
    r"""Policy configuration about how user applications are deployed.

    Values:
      SHARED_DEPLOYMENT_POLICY_UNSPECIFIED: Unspecified.
      ALLOWED: User applications can be deployed both on control plane and
        worker nodes.
      DISALLOWED: User applications can not be deployed on control plane nodes
        and can only be deployed on worker nodes.
    """
    SHARED_DEPLOYMENT_POLICY_UNSPECIFIED = 0
    ALLOWED = 1
    DISALLOWED = 2

  controlPlaneNodeStorageSchema = _messages.StringField(1)
  machineFilter = _messages.StringField(2)
  nodeCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  nodeLocation = _messages.StringField(4)
  sharedDeploymentPolicy = _messages.EnumField('SharedDeploymentPolicyValueValuesEnum', 5)


class LocalDiskEncryption(_messages.Message):
  r"""Configuration for CMEK support for edge machine local disk encryption.

  Enums:
    KmsKeyStateValueValuesEnum: Output only. Availability of the Cloud KMS
      CryptoKey. If not `KEY_AVAILABLE`, then nodes may go offline as they
      cannot access their local data. This can be caused by a lack of
      permissions to use the key, or if the key is disabled or deleted.
    ResourceStateValueValuesEnum: Output only. The current resource state
      associated with the cmek.

  Fields:
    kmsKey: Optional. The Cloud KMS CryptoKey e.g. projects/{project}/location
      s/{location}/keyRings/{keyRing}/cryptoKeys/{cryptoKey} to use for
      protecting node local disks. If not specified, a Google-managed key will
      be used instead.
    kmsKeyActiveVersion: Output only. The Cloud KMS CryptoKeyVersion currently
      in use for protecting node local disks. Only applicable if kms_key is
      set.
    kmsKeyState: Output only. Availability of the Cloud KMS CryptoKey. If not
      `KEY_AVAILABLE`, then nodes may go offline as they cannot access their
      local data. This can be caused by a lack of permissions to use the key,
      or if the key is disabled or deleted.
    kmsStatus: Output only. Error status returned by Cloud KMS when using this
      key. This field may be populated only if `kms_key_state` is not
      `KMS_KEY_STATE_KEY_AVAILABLE`. If populated, this field contains the
      error status reported by Cloud KMS.
    resourceState: Output only. The current resource state associated with the
      cmek.
  """

  class KmsKeyStateValueValuesEnum(_messages.Enum):
    r"""Output only. Availability of the Cloud KMS CryptoKey. If not
    `KEY_AVAILABLE`, then nodes may go offline as they cannot access their
    local data. This can be caused by a lack of permissions to use the key, or
    if the key is disabled or deleted.

    Values:
      KMS_KEY_STATE_UNSPECIFIED: Unspecified.
      KMS_KEY_STATE_KEY_AVAILABLE: The key is available for use, and dependent
        resources should be accessible.
      KMS_KEY_STATE_KEY_UNAVAILABLE: The key is unavailable for an unspecified
        reason. Dependent resources may be inaccessible.
    """
    KMS_KEY_STATE_UNSPECIFIED = 0
    KMS_KEY_STATE_KEY_AVAILABLE = 1
    KMS_KEY_STATE_KEY_UNAVAILABLE = 2

  class ResourceStateValueValuesEnum(_messages.Enum):
    r"""Output only. The current resource state associated with the cmek.

    Values:
      RESOURCE_STATE_UNSPECIFIED: Default value.
      RESOURCE_STATE_LOCK_DOWN: The resource is in LOCK DOWN state.
      RESOURCE_STATE_LOCK_DOWN_PENDING: The resource is pending lock down.
    """
    RESOURCE_STATE_UNSPECIFIED = 0
    RESOURCE_STATE_LOCK_DOWN = 1
    RESOURCE_STATE_LOCK_DOWN_PENDING = 2

  kmsKey = _messages.StringField(1)
  kmsKeyActiveVersion = _messages.StringField(2)
  kmsKeyState = _messages.EnumField('KmsKeyStateValueValuesEnum', 3)
  kmsStatus = _messages.MessageField('Status', 4)
  resourceState = _messages.EnumField('ResourceStateValueValuesEnum', 5)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class LocationMetadata(_messages.Message):
  r"""Metadata for a given google.cloud.location.Location.

  Messages:
    AvailableZonesValue: The set of available Google Distributed Cloud Edge
      zones in the location. The map is keyed by the lowercase ID of each
      zone.

  Fields:
    availableZones: The set of available Google Distributed Cloud Edge zones
      in the location. The map is keyed by the lowercase ID of each zone.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AvailableZonesValue(_messages.Message):
    r"""The set of available Google Distributed Cloud Edge zones in the
    location. The map is keyed by the lowercase ID of each zone.

    Messages:
      AdditionalProperty: An additional property for a AvailableZonesValue
        object.

    Fields:
      additionalProperties: Additional properties of type AvailableZonesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AvailableZonesValue object.

      Fields:
        key: Name of the additional property.
        value: A ZoneMetadata attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ZoneMetadata', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  availableZones = _messages.MessageField('AvailableZonesValue', 1)


class Machine(_messages.Message):
  r"""A Google Distributed Cloud Edge machine capable of acting as a
  Kubernetes node.

  Enums:
    PurposeValueValuesEnum: The type of cluster the machine is used for.
    StatusValueValuesEnum: Output only. The current status of the machine.

  Messages:
    LabelsValue: Labels associated with this resource.

  Fields:
    createTime: Output only. The time when the node pool was created.
    disabled: Output only. Whether the machine is disabled. If disabled, the
      machine is unable to enter service.
    hostedNode: Canonical resource name of the node that this machine is
      responsible for hosting e.g. projects/{project}/locations/{location}/clu
      sters/{cluster_id}/nodePools/{pool_id}/{node}, Or empty if the machine
      is not assigned to assume the role of a node. For control plane nodes
      hosted on edge machines, this will return the following format: "project
      s/{project}/locations/{location}/clusters/{cluster_id}/controlPlaneNodes
      /{node}".
    labels: Labels associated with this resource.
    name: Required. The resource name of the machine.
    purpose: The type of cluster the machine is used for.
    status: Output only. The current status of the machine.
    updateTime: Output only. The time when the node pool was last updated.
    version: Output only. The software version of the machine.
    zone: The Google Distributed Cloud Edge zone of this machine.
  """

  class PurposeValueValuesEnum(_messages.Enum):
    r"""The type of cluster the machine is used for.

    Values:
      PURPOSE_UNSPECIFIED: Unspecified purpose.
      VIRTUALIZED_WORKLOAD: Machine is used for virtual workload.
      BAREMETAL_CLUSTER: Machine is used for a baremetal user cluster.
    """
    PURPOSE_UNSPECIFIED = 0
    VIRTUALIZED_WORKLOAD = 1
    BAREMETAL_CLUSTER = 2

  class StatusValueValuesEnum(_messages.Enum):
    r"""Output only. The current status of the machine.

    Values:
      STATUS_UNSPECIFIED: Status unknown.
      READY: The machine is ready to host a node. This is the default.
      DISABLED_FOR_REPAIR: The machine has been disabled for repair by adding
        1 or more disable claims.
    """
    STATUS_UNSPECIFIED = 0
    READY = 1
    DISABLED_FOR_REPAIR = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels associated with this resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  disabled = _messages.BooleanField(2)
  hostedNode = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  purpose = _messages.EnumField('PurposeValueValuesEnum', 6)
  status = _messages.EnumField('StatusValueValuesEnum', 7)
  updateTime = _messages.StringField(8)
  version = _messages.StringField(9)
  zone = _messages.StringField(10)


class MaintenanceEvent(_messages.Message):
  r"""A Maintenance Event is an operation that could cause temporary
  disruptions to the cluster workloads, including Google-driven or user-
  initiated cluster upgrades, user-initiated cluster configuration changes
  that require restarting nodes, etc.

  Enums:
    ScheduleValueValuesEnum: Output only. The schedule of the maintenance
      event.
    StateValueValuesEnum: Output only. The state of the maintenance event.
    TypeValueValuesEnum: Output only. The type of the maintenance event.

  Fields:
    createTime: Output only. The time when the maintenance event request was
      created.
    endTime: Output only. The time when the maintenance event ended, either
      successfully or not. If the maintenance event is split into multiple
      maintenance windows, end_time is only updated when the whole flow ends.
    operation: Output only. The operation for running the maintenance event.
      Specified in the format projects/*/locations/*/operations/*. If the
      maintenance event is split into multiple operations (e.g. due to
      maintenance windows), the latest one is recorded.
    schedule: Output only. The schedule of the maintenance event.
    startTime: Output only. The time when the maintenance event started.
    state: Output only. The state of the maintenance event.
    targetVersion: Output only. The target version of the cluster.
    type: Output only. The type of the maintenance event.
    updateTime: Output only. The time when the maintenance event message was
      updated.
    uuid: Output only. UUID of the maintenance event.
  """

  class ScheduleValueValuesEnum(_messages.Enum):
    r"""Output only. The schedule of the maintenance event.

    Values:
      SCHEDULE_UNSPECIFIED: Unspecified.
      IMMEDIATELY: Immediately after receiving the request.
    """
    SCHEDULE_UNSPECIFIED = 0
    IMMEDIATELY = 1

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the maintenance event.

    Values:
      STATE_UNSPECIFIED: Unspecified.
      RECONCILING: The maintenance event is ongoing. The cluster might be
        unusable.
      SUCCEEDED: The maintenance event succeeded.
      FAILED: The maintenance event failed.
      STOPPED_BEFORE_MAINTENANCE_WINDOW_ENDED: The maintenance event is
        paused. The cluster should be usable.
    """
    STATE_UNSPECIFIED = 0
    RECONCILING = 1
    SUCCEEDED = 2
    FAILED = 3
    STOPPED_BEFORE_MAINTENANCE_WINDOW_ENDED = 4

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the maintenance event.

    Values:
      TYPE_UNSPECIFIED: Unspecified.
      USER_INITIATED_UPGRADE: Upgrade initiated by users.
      GOOGLE_DRIVEN_UPGRADE: Upgrade driven by Google.
    """
    TYPE_UNSPECIFIED = 0
    USER_INITIATED_UPGRADE = 1
    GOOGLE_DRIVEN_UPGRADE = 2

  createTime = _messages.StringField(1)
  endTime = _messages.StringField(2)
  operation = _messages.StringField(3)
  schedule = _messages.EnumField('ScheduleValueValuesEnum', 4)
  startTime = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  targetVersion = _messages.StringField(7)
  type = _messages.EnumField('TypeValueValuesEnum', 8)
  updateTime = _messages.StringField(9)
  uuid = _messages.StringField(10)


class MaintenanceExclusionWindow(_messages.Message):
  r"""Represents a maintenance exclusion window.

  Fields:
    id: Optional. A unique (per cluster) id for the window.
    window: Optional. The time window.
  """

  id = _messages.StringField(1)
  window = _messages.MessageField('TimeWindow', 2)


class MaintenancePolicy(_messages.Message):
  r"""Maintenance policy configuration.

  Fields:
    maintenanceExclusions: Optional. Exclusions to automatic maintenance. Non-
      emergency maintenance should not occur in these windows. Each exclusion
      has a unique name and may be active or expired. The max number of
      maintenance exclusions allowed at a given time is 3.
    window: Specifies the maintenance window in which maintenance may be
      performed.
  """

  maintenanceExclusions = _messages.MessageField('MaintenanceExclusionWindow', 1, repeated=True)
  window = _messages.MessageField('MaintenanceWindow', 2)


class MaintenanceWindow(_messages.Message):
  r"""Maintenance window configuration

  Fields:
    recurringWindow: Configuration of a recurring maintenance window.
  """

  recurringWindow = _messages.MessageField('RecurringTimeWindow', 1)


class NodeConfig(_messages.Message):
  r"""Configuration for each node in the NodePool

  Messages:
    LabelsValue: Optional. The Kubernetes node labels

  Fields:
    labels: Optional. The Kubernetes node labels
    nodeStorageSchema: Optional. Name for the storage schema of worker nodes.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The Kubernetes node labels

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)
  nodeStorageSchema = _messages.StringField(2)


class NodePool(_messages.Message):
  r"""A set of Kubernetes nodes in a cluster with common configuration and
  specification.

  Messages:
    LabelsValue: Labels associated with this resource.

  Fields:
    createTime: Output only. The time when the node pool was created.
    labels: Labels associated with this resource.
    localDiskEncryption: Optional. Local disk encryption options. This field
      is only used when enabling CMEK support.
    machineFilter: Only machines matching this filter will be allowed to join
      the node pool. The filtering language accepts strings like "name=", and
      is documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: Required. The resource name of the node pool.
    nodeConfig: Optional. Configuration for each node in the NodePool
    nodeCount: Required. The number of nodes in the pool.
    nodeLocation: Name of the Google Distributed Cloud Edge zone where this
      node pool will be created. For example: `us-central1-edge-customer-a`.
    nodeVersion: Output only. The lowest release version among all worker
      nodes.
    site: Name of the Google Distributed Cloud Edge zone where this node pool
      will be created.
    updateTime: Output only. The time when the node pool was last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels associated with this resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  localDiskEncryption = _messages.MessageField('LocalDiskEncryption', 3)
  machineFilter = _messages.StringField(4)
  name = _messages.StringField(5)
  nodeConfig = _messages.MessageField('NodeConfig', 6)
  nodeCount = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  nodeLocation = _messages.StringField(8)
  nodeVersion = _messages.StringField(9)
  site = _messages.StringField(10)
  updateTime = _messages.StringField(11)


class OIDCProviderConfig(_messages.Message):
  r"""Represents the OIDC provider configuration.

  Fields:
    clientId: Required. The client id of the identity provider.
    clientSecret: Optional. The client secret of the identity provider.
    cloudConsoleRedirectUri: Optional. CloudConsoleRedirectURI is the URI to
      redirect users going through the OAuth flow using cloud console.
    enableAccessToken: Optional. Flag that denotes if the access-token should
      be included in the request as part of the bearer token by `gcloud anthos
      auth login` and `kubectl oidc login`. Defaults to false.
    extraParams: Optional. Comma-separated list of key-value pairs that will
      be query-encoded and sent with the authentication endpoint request.
    issuerUri: Required. The issuer uri of the identity provider.
    kubectlRedirectUri: Optional. KubectlRedirectURI is the URI to redirect
      users authenticating to an OIDC provider with the kubectl plugin.
    scopes: Required. The scopes of the identity provider.
    userClaim: Optional. The user claim of the identity provider.
  """

  clientId = _messages.StringField(1)
  clientSecret = _messages.StringField(2)
  cloudConsoleRedirectUri = _messages.StringField(3)
  enableAccessToken = _messages.BooleanField(4)
  extraParams = _messages.StringField(5)
  issuerUri = _messages.StringField(6)
  kubectlRedirectUri = _messages.StringField(7)
  scopes = _messages.StringField(8)
  userClaim = _messages.StringField(9)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Long-running operation metadata for Edge Container API methods.

  Enums:
    StatusReasonValueValuesEnum: Machine-readable status of the operation, if
      any.

  Fields:
    apiVersion: API version used to start the operation.
    createTime: The time the operation was created.
    endTime: The time the operation finished running.
    requestedCancellation: Identifies whether the user has requested
      cancellation of the operation. Operations that have successfully been
      cancelled have Operation.error value with a google.rpc.Status.code of 1,
      corresponding to `Code.CANCELLED`.
    statusMessage: Human-readable status of the operation, if any.
    statusReason: Machine-readable status of the operation, if any.
    target: Server-defined resource path for the target of the operation.
    verb: The verb executed by the operation.
    warnings: Warnings that do not block the operation, but still hold
      relevant information for the end user to receive.
  """

  class StatusReasonValueValuesEnum(_messages.Enum):
    r"""Machine-readable status of the operation, if any.

    Values:
      STATUS_REASON_UNSPECIFIED: Reason unknown.
      UPGRADE_PAUSED: The cluster upgrade is currently paused.
    """
    STATUS_REASON_UNSPECIFIED = 0
    UPGRADE_PAUSED = 1

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  statusReason = _messages.EnumField('StatusReasonValueValuesEnum', 6)
  target = _messages.StringField(7)
  verb = _messages.StringField(8)
  warnings = _messages.StringField(9, repeated=True)


class Principal(_messages.Message):
  r"""Principal represents a principal in the IAM policy.

  Fields:
    serviceAccount: Service account represents a service account in the IAM
      policy.
    user: User represents a user in the IAM policy.
  """

  serviceAccount = _messages.StringField(1)
  user = _messages.StringField(2)


class Quota(_messages.Message):
  r"""Represents quota for Edge Container resources.

  Fields:
    limit: Quota limit for this metric.
    metric: Name of the quota metric.
    usage: Current usage of this metric.
  """

  limit = _messages.FloatField(1)
  metric = _messages.StringField(2)
  usage = _messages.FloatField(3)


class RecurringTimeWindow(_messages.Message):
  r"""Represents an arbitrary window of time that recurs.

  Fields:
    recurrence: An RRULE (https://tools.ietf.org/html/rfc5545#section-*******)
      for how this window recurs. They go on for the span of time between the
      start and end time.
    window: The window of the first recurrence.
  """

  recurrence = _messages.StringField(1)
  window = _messages.MessageField('TimeWindow', 2)


class Remote(_messages.Message):
  r"""Configuration specific to clusters with a control plane hosted remotely.
  """



class SdsOperator(_messages.Message):
  r"""Config for the SDS Operator add-on which installs Robin CNS.

  Fields:
    version: Optional. SDS Operator version.
  """

  version = _messages.StringField(1)


class ServerConfig(_messages.Message):
  r"""Server configuration for supported versions and release channels.

  Messages:
    ChannelsValue: Output only. Mapping from release channel to channel
      config.

  Fields:
    channels: Output only. Mapping from release channel to channel config.
    defaultVersion: Output only. Default version, e.g.: "1.4.0".
    versions: Output only. Supported versions, e.g.: ["1.4.0", "1.5.0"].
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ChannelsValue(_messages.Message):
    r"""Output only. Mapping from release channel to channel config.

    Messages:
      AdditionalProperty: An additional property for a ChannelsValue object.

    Fields:
      additionalProperties: Additional properties of type ChannelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ChannelsValue object.

      Fields:
        key: Name of the additional property.
        value: A ChannelConfig attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ChannelConfig', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  channels = _messages.MessageField('ChannelsValue', 1)
  defaultVersion = _messages.StringField(2)
  versions = _messages.MessageField('Version', 3, repeated=True)


class ServiceAccount(_messages.Message):
  r"""Represents the service account resource.

  Messages:
    LabelsValue: Optional. Labels associated with this resource.

  Fields:
    createTime: Output only. The time when the project service account was
      created.
    labels: Optional. Labels associated with this resource.
    name: Identifier. The canonical resource name of the project service
      account. E.g. projects/{project}/locations/{location}/serviceAccounts/{s
      ervice_account}
    updateTime: Output only. The time when the project service account was
      last updated.
    zone: Required. The zone id of the zone on which the project service
      account has to be created.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels associated with this resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  name = _messages.StringField(3)
  updateTime = _messages.StringField(4)
  zone = _messages.StringField(5)


class SetIamPolicyRequest(_messages.Message):
  r"""Request proto to set the IAM policy for a project in a zone.

  Fields:
    policy: Required. The IAM policy to be set.
    requestId: Optional. A unique identifier for this request. Restricted to
      36 ASCII characters. A random UUID is recommended. This request is only
      idempotent if `request_id` is provided.
  """

  policy = _messages.MessageField('IamPolicy', 1)
  requestId = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class SurvivabilityConfig(_messages.Message):
  r"""Configuration of the cluster survivability, e.g., for the case when
  network connectivity is lost.

  Fields:
    offlineRebootTtl: Optional. Time period that allows the cluster nodes to
      be rebooted and become functional without network connectivity to
      Google. The default 0 means not allowed. The maximum is 7 days.
  """

  offlineRebootTtl = _messages.StringField(1)


class SystemAddonsConfig(_messages.Message):
  r"""Config that customers are allowed to define for GDCE system add-ons.

  Fields:
    ingress: Optional. Config for Ingress.
    sdsOperator: Optional. Config for SDS Operator.
    unmanagedKafkaConfig: Optional. Config for unmanaged Kafka.
    vmServiceConfig: Optional. Config for VM Service.
  """

  ingress = _messages.MessageField('Ingress', 1)
  sdsOperator = _messages.MessageField('SdsOperator', 2)
  unmanagedKafkaConfig = _messages.MessageField('UnmanagedKafkaConfig', 3)
  vmServiceConfig = _messages.MessageField('VMServiceConfig', 4)


class TimeWindow(_messages.Message):
  r"""Represents an arbitrary window of time.

  Fields:
    endTime: The time that the window ends. The end time must take place after
      the start time.
    startTime: The time that the window first starts.
  """

  endTime = _messages.StringField(1)
  startTime = _messages.StringField(2)


class UnmanagedKafkaConfig(_messages.Message):
  r"""Config for customer provided Kafka to receive application logs from log
  forwarder. This field is only populated for LCP clusters.

  Fields:
    brokers: Required. Comma separated string of broker addresses, with IP and
      port.
    topicKey: Optional. Kafka topic key to select a topic if multiple topics
      exist.
    topics: Required. Comma separated string of Kafka topics.
  """

  brokers = _messages.StringField(1)
  topicKey = _messages.StringField(2)
  topics = _messages.StringField(3)


class UpgradeClusterRequest(_messages.Message):
  r"""Upgrades a cluster.

  Enums:
    ScheduleValueValuesEnum: The schedule for the upgrade.

  Fields:
    requestId: A unique identifier for this request. Restricted to 36 ASCII
      characters. A random UUID is recommended. This request is only
      idempotent if `request_id` is provided.
    schedule: The schedule for the upgrade.
    targetVersion: Required. The version the cluster is going to be upgraded
      to.
  """

  class ScheduleValueValuesEnum(_messages.Enum):
    r"""The schedule for the upgrade.

    Values:
      SCHEDULE_UNSPECIFIED: Unspecified. The default is to upgrade the cluster
        immediately which is the only option today.
      IMMEDIATELY: The cluster is going to be upgraded immediately after
        receiving the request.
    """
    SCHEDULE_UNSPECIFIED = 0
    IMMEDIATELY = 1

  requestId = _messages.StringField(1)
  schedule = _messages.EnumField('ScheduleValueValuesEnum', 2)
  targetVersion = _messages.StringField(3)


class UpgradeSettings(_messages.Message):
  r"""If upgrade_settings or max_unavailable_worker_nodes is unset, the Google
  defaults will be used.

  Fields:
    maxUnavailableWorkerNodes: Optional. Maximum number of unavailable worker
      nodes during upgrade.
  """

  maxUnavailableWorkerNodes = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class VMServiceConfig(_messages.Message):
  r"""VMServiceConfig defines the configuration for GDCE VM Service.

  Fields:
    vmmEnabled: Optional. Whether VMM is enabled.
  """

  vmmEnabled = _messages.BooleanField(1)


class Version(_messages.Message):
  r"""Version of a cluster.

  Fields:
    name: Output only. Name of the version, e.g.: "1.4.0".
  """

  name = _messages.StringField(1)


class VpcProject(_messages.Message):
  r"""Project detail of the VPC network.

  Fields:
    projectId: The project of the VPC to connect to. If not specified, it is
      the same as the cluster project.
    serviceAccount: Optional. Deprecated: do not use.
  """

  projectId = _messages.StringField(1)
  serviceAccount = _messages.StringField(2)


class VpnConnection(_messages.Message):
  r"""A VPN connection .

  Enums:
    BgpRoutingModeValueValuesEnum: Dynamic routing mode of the VPC network,
      `regional` or `global`.

  Messages:
    LabelsValue: Labels associated with this resource.

  Fields:
    bgpRoutingMode: Dynamic routing mode of the VPC network, `regional` or
      `global`.
    cluster: The canonical Cluster name to connect to. It is in the form of
      projects/{project}/locations/{location}/clusters/{cluster}.
    createTime: Output only. The time when the VPN connection was created.
    details: Output only. The created connection details.
    enableHighAvailability: Whether this VPN connection has HA enabled on
      cluster side. If enabled, when creating VPN connection we will attempt
      to use 2 ANG floating IPs.
    labels: Labels associated with this resource.
    name: Required. The resource name of VPN connection
    natGatewayIp: NAT gateway IP, or WAN IP address. If a customer has
      multiple NAT IPs, the customer needs to configure NAT such that only one
      external IP maps to the GMEC Anthos cluster. This is empty if NAT is not
      used.
    router: Optional. The VPN connection Cloud Router name.
    updateTime: Output only. The time when the VPN connection was last
      updated.
    vpc: The network ID of VPC to connect to.
    vpcProject: Optional. Project detail of the VPC network. Required if VPC
      is in a different project than the cluster project.
  """

  class BgpRoutingModeValueValuesEnum(_messages.Enum):
    r"""Dynamic routing mode of the VPC network, `regional` or `global`.

    Values:
      BGP_ROUTING_MODE_UNSPECIFIED: Unknown.
      REGIONAL: Regional mode.
      GLOBAL: Global mode.
    """
    BGP_ROUTING_MODE_UNSPECIFIED = 0
    REGIONAL = 1
    GLOBAL = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels associated with this resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  bgpRoutingMode = _messages.EnumField('BgpRoutingModeValueValuesEnum', 1)
  cluster = _messages.StringField(2)
  createTime = _messages.StringField(3)
  details = _messages.MessageField('Details', 4)
  enableHighAvailability = _messages.BooleanField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  natGatewayIp = _messages.StringField(8)
  router = _messages.StringField(9)
  updateTime = _messages.StringField(10)
  vpc = _messages.StringField(11)
  vpcProject = _messages.MessageField('VpcProject', 12)


class ZonalProject(_messages.Message):
  r"""Zonal project (corresponding to the cloud consumer project) which get
  enabled on Zone(s).

  Enums:
    StateValueValuesEnum: Output only. The state of the project on the zone.

  Messages:
    LabelsValue: Optional. Labels associated with this resource.

  Fields:
    createTime: Output only. The time when the project was created.
    labels: Optional. Labels associated with this resource.
    name: Identifier. The resource name of the project.
    state: Output only. The state of the project on the zone.
    updateTime: Output only. The time when the project was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the project on the zone.

    Values:
      STATE_UNSPECIFIED: Unspecified.
      STATE_ON: The project is enabled on the zone.
      STATE_OFF: The project is disabled on the zone.
    """
    STATE_UNSPECIFIED = 0
    STATE_ON = 1
    STATE_OFF = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels associated with this resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  name = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)
  updateTime = _messages.StringField(5)


class ZonalService(_messages.Message):
  r"""Service enabled on the project.

  Enums:
    ServiceSelectorValueValuesEnum: Required. The service to enable/disable.
    StateValueValuesEnum: Output only. The state of the service.

  Messages:
    LabelsValue: Optional. Labels associated with this resource.

  Fields:
    cluster: The fully qualified name of the cluster on which the service has
      to be enabled/disabled.
    createTime: Output only. The time when the service was enabled.
    labels: Optional. Labels associated with this resource.
    name: Identifier. The resource name of the service.
    serviceSelector: Required. The service to enable/disable.
    state: Output only. The state of the service.
    updateTime: Output only. The time when the service was last updated.
    zone: The zone id of the zone on which the service has to be
      enabled/disabled.
  """

  class ServiceSelectorValueValuesEnum(_messages.Enum):
    r"""Required. The service to enable/disable.

    Values:
      SERVICE_SELECTOR_UNSPECIFIED: Unspecified.
      ALLOYDB: AlloyDB service, alloydb.googleapis.com.
      VMM: VMM service, gdcvmmanager.googleapis.com.
      BOOKSTORE: Bookstore service, bookstore.googleapis.com.
      VERTEX: Vertex service, aiplatform.googleapis.com.
      BOOKSTORE_AI: Bookstore.AI service, bookstore-ai.googleapis.com.
    """
    SERVICE_SELECTOR_UNSPECIFIED = 0
    ALLOYDB = 1
    VMM = 2
    BOOKSTORE = 3
    VERTEX = 4
    BOOKSTORE_AI = 5

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the service.

    Values:
      STATE_UNSPECIFIED: Unspecified.
      STATE_ENABLED: The service is enabled on the project.
      STATE_DISABLED: The service is disabled on the project.
      STATE_ENABLING: The service is being enabled on the project.
      STATE_DISABLING: The service is being disabled on the project.
    """
    STATE_UNSPECIFIED = 0
    STATE_ENABLED = 1
    STATE_DISABLED = 2
    STATE_ENABLING = 3
    STATE_DISABLING = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels associated with this resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  cluster = _messages.StringField(1)
  createTime = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  serviceSelector = _messages.EnumField('ServiceSelectorValueValuesEnum', 5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  updateTime = _messages.StringField(7)
  zone = _messages.StringField(8)


class Zone(_messages.Message):
  r"""Represents a zone.

  Enums:
    StateValueValuesEnum: Output only. The state of the zone.

  Messages:
    LabelsValue: Optional. Labels associated with this resource.

  Fields:
    certificateAuthorities: Output only. The web CA certificate for the zone.
    createTime: Output only. The time when the zone was created.
    dnsServers: Output only. The DNS servers for the zone.
    labels: Optional. Labels associated with this resource.
    name: Identifier. The canonical resource name of the zone. E.g.
      organizations/{organization}/locations/{location}/zones/{zone}
    state: Output only. The state of the zone.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the zone.

    Values:
      STATE_UNSPECIFIED: Unspecified.
      STATE_RUNNING: The zone is in RUNNING state.
      STATE_ERROR: The zone is in ERROR state.
    """
    STATE_UNSPECIFIED = 0
    STATE_RUNNING = 1
    STATE_ERROR = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels associated with this resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  certificateAuthorities = _messages.StringField(1, repeated=True)
  createTime = _messages.StringField(2)
  dnsServers = _messages.MessageField('DNSServer', 3, repeated=True)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)


class ZoneMetadata(_messages.Message):
  r"""A Google Distributed Cloud Edge zone where edge machines are located.

  Messages:
    RackTypesValue: The map keyed by rack name and has value of RackType.

  Fields:
    configData: Config data for the zone.
    quota: Quota for resources in this zone.
    rackTypes: The map keyed by rack name and has value of RackType.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class RackTypesValue(_messages.Message):
    r"""The map keyed by rack name and has value of RackType.

    Messages:
      AdditionalProperty: An additional property for a RackTypesValue object.

    Fields:
      additionalProperties: Additional properties of type RackTypesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a RackTypesValue object.

      Enums:
        ValueValueValuesEnum:

      Fields:
        key: Name of the additional property.
        value: A ValueValueValuesEnum attribute.
      """

      class ValueValueValuesEnum(_messages.Enum):
        r"""ValueValueValuesEnum enum type.

        Values:
          RACK_TYPE_UNSPECIFIED: Unspecified rack type, single rack also
            belongs to this type.
          BASE: Base rack type, a pair of two modified Config-1 racks
            containing Aggregation switches.
          EXPANSION: Expansion rack type, also known as standalone racks,
            added by customers on demand.
        """
        RACK_TYPE_UNSPECIFIED = 0
        BASE = 1
        EXPANSION = 2

      key = _messages.StringField(1)
      value = _messages.EnumField('ValueValueValuesEnum', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  configData = _messages.MessageField('ConfigData', 1)
  quota = _messages.MessageField('Quota', 2, repeated=True)
  rackTypes = _messages.MessageField('RackTypesValue', 3)


class ZoneStorageEncryption(_messages.Message):
  r"""Configuration for Zone Storage CMEK Support

  Enums:
    ResourceStateValueValuesEnum: Output only. The current resource state of
      the CMEK

  Fields:
    kmsKey: Optional. The Cloud KMS Key
    kmsKeyActiveVersion: Output only. The Cloud KMS CryptoKeyVersion currently
      used for encryption/decryption
    resourceState: Output only. The current resource state of the CMEK
  """

  class ResourceStateValueValuesEnum(_messages.Enum):
    r"""Output only. The current resource state of the CMEK

    Values:
      RESOURCE_STATE_UNSPECIFIED: Default value.
      RESOURCE_STATE_LOCK_DOWN: The resource is in LOCK DOWN state.
      RESOURCE_STATE_LOCK_DOWN_PENDING: The resource is pending lock down.
    """
    RESOURCE_STATE_UNSPECIFIED = 0
    RESOURCE_STATE_LOCK_DOWN = 1
    RESOURCE_STATE_LOCK_DOWN_PENDING = 2

  kmsKey = _messages.StringField(1)
  kmsKeyActiveVersion = _messages.StringField(2)
  resourceState = _messages.EnumField('ResourceStateValueValuesEnum', 3)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
