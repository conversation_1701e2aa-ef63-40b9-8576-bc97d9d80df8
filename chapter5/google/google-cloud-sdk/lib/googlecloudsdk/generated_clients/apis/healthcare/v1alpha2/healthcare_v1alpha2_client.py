"""Generated client library for healthcare version v1alpha2."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.healthcare.v1alpha2 import healthcare_v1alpha2_messages as messages


class HealthcareV1alpha2(base_api.BaseApiClient):
  """Generated client library for service healthcare version v1alpha2."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://healthcare.googleapis.com/'
  MTLS_BASE_URL = 'https://healthcare.mtls.googleapis.com/'

  _PACKAGE = 'healthcare'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-healthcare', 'https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1alpha2'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'HealthcareV1alpha2'
  _URL_VERSION = 'v1alpha2'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new healthcare handle."""
    url = url or self.BASE_URL
    super(HealthcareV1alpha2, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_datasets_consentStores_attributeDefinitions = self.ProjectsLocationsDatasetsConsentStoresAttributeDefinitionsService(self)
    self.projects_locations_datasets_consentStores_consentArtifacts = self.ProjectsLocationsDatasetsConsentStoresConsentArtifactsService(self)
    self.projects_locations_datasets_consentStores_consents = self.ProjectsLocationsDatasetsConsentStoresConsentsService(self)
    self.projects_locations_datasets_consentStores_userDataMappings = self.ProjectsLocationsDatasetsConsentStoresUserDataMappingsService(self)
    self.projects_locations_datasets_consentStores = self.ProjectsLocationsDatasetsConsentStoresService(self)
    self.projects_locations_datasets_dataMapperWorkspaces = self.ProjectsLocationsDatasetsDataMapperWorkspacesService(self)
    self.projects_locations_datasets_dicomStores = self.ProjectsLocationsDatasetsDicomStoresService(self)
    self.projects_locations_datasets_fhirStores = self.ProjectsLocationsDatasetsFhirStoresService(self)
    self.projects_locations_datasets_hl7V2Stores_messages = self.ProjectsLocationsDatasetsHl7V2StoresMessagesService(self)
    self.projects_locations_datasets_hl7V2Stores = self.ProjectsLocationsDatasetsHl7V2StoresService(self)
    self.projects_locations_datasets_operations = self.ProjectsLocationsDatasetsOperationsService(self)
    self.projects_locations_datasets = self.ProjectsLocationsDatasetsService(self)
    self.projects_locations_services_deidentify = self.ProjectsLocationsServicesDeidentifyService(self)
    self.projects_locations_services_nlp = self.ProjectsLocationsServicesNlpService(self)
    self.projects_locations_services = self.ProjectsLocationsServicesService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsDatasetsConsentStoresAttributeDefinitionsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_consentStores_attributeDefinitions resource."""

    _NAME = 'projects_locations_datasets_consentStores_attributeDefinitions'

    def __init__(self, client):
      super(HealthcareV1alpha2.ProjectsLocationsDatasetsConsentStoresAttributeDefinitionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Attribute definition in the parent consent store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AttributeDefinition) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/attributeDefinitions',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.consentStores.attributeDefinitions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['attributeDefinitionId'],
        relative_path='v1alpha2/{+parent}/attributeDefinitions',
        request_field='attributeDefinition',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsCreateRequest',
        response_type_name='AttributeDefinition',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified Attribute definition. Fails if the Attribute definition is referenced by any User data mapping, the latest revision of any Consent, or the latest approved revision of any Consent content.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/attributeDefinitions/{attributeDefinitionsId}',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.consentStores.attributeDefinitions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the specified Attribute definition.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AttributeDefinition) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/attributeDefinitions/{attributeDefinitionsId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.consentStores.attributeDefinitions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsGetRequest',
        response_type_name='AttributeDefinition',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the Attribute definitions in the specified consent store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAttributeDefinitionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/attributeDefinitions',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.consentStores.attributeDefinitions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha2/{+parent}/attributeDefinitions',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsListRequest',
        response_type_name='ListAttributeDefinitionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified Attribute definition.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AttributeDefinition) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/attributeDefinitions/{attributeDefinitionsId}',
        http_method='PATCH',
        method_id='healthcare.projects.locations.datasets.consentStores.attributeDefinitions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha2/{+name}',
        request_field='attributeDefinition',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsPatchRequest',
        response_type_name='AttributeDefinition',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsConsentStoresConsentArtifactsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_consentStores_consentArtifacts resource."""

    _NAME = 'projects_locations_datasets_consentStores_consentArtifacts'

    def __init__(self, client):
      super(HealthcareV1alpha2.ProjectsLocationsDatasetsConsentStoresConsentArtifactsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Consent artifact in the parent consent store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ConsentArtifact) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/consentArtifacts',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.consentStores.consentArtifacts.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha2/{+parent}/consentArtifacts',
        request_field='consentArtifact',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsCreateRequest',
        response_type_name='ConsentArtifact',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified Consent artifact. Fails if the artifact is referenced by the latest revision of any Consent.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/consentArtifacts/{consentArtifactsId}',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.consentStores.consentArtifacts.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the specified Consent artifact.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ConsentArtifact) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/consentArtifacts/{consentArtifactsId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.consentStores.consentArtifacts.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsGetRequest',
        response_type_name='ConsentArtifact',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the Consent artifacts in the specified consent store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListConsentArtifactsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/consentArtifacts',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.consentStores.consentArtifacts.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha2/{+parent}/consentArtifacts',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsListRequest',
        response_type_name='ListConsentArtifactsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsConsentStoresConsentsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_consentStores_consents resource."""

    _NAME = 'projects_locations_datasets_consentStores_consents'

    def __init__(self, client):
      super(HealthcareV1alpha2.ProjectsLocationsDatasetsConsentStoresConsentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Consent in the parent consent store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresConsentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Consent) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/consents',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.consentStores.consents.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha2/{+parent}/consents',
        request_field='consent',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresConsentsCreateRequest',
        response_type_name='Consent',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the Consent and its revisions. To keep a record of the Consent but mark it inactive, see [RevokeConsent]. To delete a revision of a Consent, see [DeleteConsentRevision]. This operation does not delete the related Consent artifact.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresConsentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/consents/{consentsId}',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.consentStores.consents.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresConsentsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the specified revision of a Consent, or the latest revision if `revision_id` is not specified in the resource name.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresConsentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Consent) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/consents/{consentsId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.consentStores.consents.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresConsentsGetRequest',
        response_type_name='Consent',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the Consent in the given consent store, returning each Consent's latest revision.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresConsentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListConsentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/consents',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.consentStores.consents.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha2/{+parent}/consents',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresConsentsListRequest',
        response_type_name='ListConsentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the latest revision of the specified Consent by committing a new revision with the changes. A FAILED_PRECONDITION error occurs if the latest revision of the specified Consent is in the `REJECTED` or `REVOKED` state.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresConsentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Consent) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/consents/{consentsId}',
        http_method='PATCH',
        method_id='healthcare.projects.locations.datasets.consentStores.consents.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha2/{+name}',
        request_field='consent',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresConsentsPatchRequest',
        response_type_name='Consent',
        supports_download=False,
    )

    def Revoke(self, request, global_params=None):
      r"""Revokes the latest revision of the specified Consent by committing a new revision with `state` updated to `REVOKED`. If the latest revision of the specified Consent is in the `REVOKED` state, no new revision is committed. A FAILED_PRECONDITION error occurs if the latest revision of the given consent is in `DRAFT` or `REJECTED` state.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresConsentsRevokeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Consent) The response message.
      """
      config = self.GetMethodConfig('Revoke')
      return self._RunMethod(
          config, request, global_params=global_params)

    Revoke.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/consents/{consentsId}:revoke',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.consentStores.consents.revoke',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}:revoke',
        request_field='revokeConsentRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresConsentsRevokeRequest',
        response_type_name='Consent',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsConsentStoresUserDataMappingsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_consentStores_userDataMappings resource."""

    _NAME = 'projects_locations_datasets_consentStores_userDataMappings'

    def __init__(self, client):
      super(HealthcareV1alpha2.ProjectsLocationsDatasetsConsentStoresUserDataMappingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Archive(self, request, global_params=None):
      r"""Archives the specified User data mapping.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsArchiveRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ArchiveUserDataMappingResponse) The response message.
      """
      config = self.GetMethodConfig('Archive')
      return self._RunMethod(
          config, request, global_params=global_params)

    Archive.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/userDataMappings/{userDataMappingsId}:archive',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.consentStores.userDataMappings.archive',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}:archive',
        request_field='archiveUserDataMappingRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsArchiveRequest',
        response_type_name='ArchiveUserDataMappingResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new User data mapping in the parent consent store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (UserDataMapping) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/userDataMappings',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.consentStores.userDataMappings.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha2/{+parent}/userDataMappings',
        request_field='userDataMapping',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsCreateRequest',
        response_type_name='UserDataMapping',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified User data mapping.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/userDataMappings/{userDataMappingsId}',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.consentStores.userDataMappings.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the specified User data mapping.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (UserDataMapping) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/userDataMappings/{userDataMappingsId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.consentStores.userDataMappings.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsGetRequest',
        response_type_name='UserDataMapping',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the User data mappings in the specified consent store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListUserDataMappingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/userDataMappings',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.consentStores.userDataMappings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha2/{+parent}/userDataMappings',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsListRequest',
        response_type_name='ListUserDataMappingsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified User data mapping.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (UserDataMapping) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/userDataMappings/{userDataMappingsId}',
        http_method='PATCH',
        method_id='healthcare.projects.locations.datasets.consentStores.userDataMappings.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha2/{+name}',
        request_field='userDataMapping',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsPatchRequest',
        response_type_name='UserDataMapping',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsConsentStoresService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_consentStores resource."""

    _NAME = 'projects_locations_datasets_consentStores'

    def __init__(self, client):
      super(HealthcareV1alpha2.ProjectsLocationsDatasetsConsentStoresService, self).__init__(client)
      self._upload_configs = {
          }

    def CheckDataAccess(self, request, global_params=None):
      r"""Checks if a particular data_id of a User data mapping in the specified consent store is consented for the specified use.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresCheckDataAccessRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CheckDataAccessResponse) The response message.
      """
      config = self.GetMethodConfig('CheckDataAccess')
      return self._RunMethod(
          config, request, global_params=global_params)

    CheckDataAccess.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}:checkDataAccess',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.consentStores.checkDataAccess',
        ordered_params=['consentStore'],
        path_params=['consentStore'],
        query_params=[],
        relative_path='v1alpha2/{+consentStore}:checkDataAccess',
        request_field='checkDataAccessRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresCheckDataAccessRequest',
        response_type_name='CheckDataAccessResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new consent store in the parent dataset. Attempting to create a consent store with the same ID as an existing store fails with an ALREADY_EXISTS error.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ConsentStore) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.consentStores.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['consentStoreId'],
        relative_path='v1alpha2/{+parent}/consentStores',
        request_field='consentStore',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresCreateRequest',
        response_type_name='ConsentStore',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified consent store and removes all the consent store's data.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.consentStores.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def EvaluateUserConsents(self, request, global_params=None):
      r"""Evaluates the end user's Consents for all matching User data mappings. Note: User data mappings are indexed asynchronously, which can cause a slight delay between the time mappings are created or updated and when they are included in EvaluateUserConsents results.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresEvaluateUserConsentsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EvaluateUserConsentsResponse) The response message.
      """
      config = self.GetMethodConfig('EvaluateUserConsents')
      return self._RunMethod(
          config, request, global_params=global_params)

    EvaluateUserConsents.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}:evaluateUserConsents',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.consentStores.evaluateUserConsents',
        ordered_params=['consentStore'],
        path_params=['consentStore'],
        query_params=[],
        relative_path='v1alpha2/{+consentStore}:evaluateUserConsents',
        request_field='evaluateUserConsentsRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresEvaluateUserConsentsRequest',
        response_type_name='EvaluateUserConsentsResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the specified consent store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ConsentStore) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.consentStores.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresGetRequest',
        response_type_name='ConsentStore',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}:getIamPolicy',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.consentStores.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha2/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the consent stores in the given dataset.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListConsentStoresResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.consentStores.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha2/{+parent}/consentStores',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresListRequest',
        response_type_name='ListConsentStoresResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified consent store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ConsentStore) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}',
        http_method='PATCH',
        method_id='healthcare.projects.locations.datasets.consentStores.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha2/{+name}',
        request_field='consentStore',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresPatchRequest',
        response_type_name='ConsentStore',
        supports_download=False,
    )

    def QueryAccessibleData(self, request, global_params=None):
      r"""Queries all data_ids that are consented for a specified use in the given consent store and writes them to a specified destination. The returned Operation includes a progress counter for the number of User data mappings processed. If the request is successful, a detailed response is returned of type QueryAccessibleDataResponse, contained in the response field when the operation finishes. The metadata field type is OperationMetadata. Errors are logged to Cloud Logging (see [Viewing error logs in Cloud Logging](https://cloud.google.com/healthcare/docs/how-tos/logging)). For example, the following sample log entry shows a `failed to evaluate consent policy` error that occurred during a QueryAccessibleData call to consent store `projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/consentStores/{consent_store_id}`. ```json jsonPayload: { @type: "type.googleapis.com/google.cloud.healthcare.logging.QueryAccessibleDataLogEntry" error: { code: 9 message: "failed to evaluate consent policy" } resourceName: "projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/consentStores/{consent_store_id}/consents/{consent_id}" } logName: "projects/{project_id}/logs/healthcare.googleapis.com%2Fquery_accessible_data" operation: { id: "projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/operations/{operation_id}" producer: "healthcare.googleapis.com/QueryAccessibleData" } receiveTimestamp: "TIMESTAMP" resource: { labels: { consent_store_id: "{consent_store_id}" dataset_id: "{dataset_id}" location: "{location_id}" project_id: "{project_id}" } type: "healthcare_consent_store" } severity: "ERROR" timestamp: "TIMESTAMP" ```.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresQueryAccessibleDataRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('QueryAccessibleData')
      return self._RunMethod(
          config, request, global_params=global_params)

    QueryAccessibleData.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}:queryAccessibleData',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.consentStores.queryAccessibleData',
        ordered_params=['consentStore'],
        path_params=['consentStore'],
        query_params=[],
        relative_path='v1alpha2/{+consentStore}:queryAccessibleData',
        request_field='queryAccessibleDataRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresQueryAccessibleDataRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}:setIamPolicy',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.consentStores.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha2/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}:testIamPermissions',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.consentStores.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha2/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsDataMapperWorkspacesService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_dataMapperWorkspaces resource."""

    _NAME = 'projects_locations_datasets_dataMapperWorkspaces'

    def __init__(self, client):
      super(HealthcareV1alpha2.ProjectsLocationsDatasetsDataMapperWorkspacesService, self).__init__(client)
      self._upload_configs = {
          }

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (HealthcareProjectsLocationsDatasetsDataMapperWorkspacesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dataMapperWorkspaces/{dataMapperWorkspacesId}:getIamPolicy',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.dataMapperWorkspaces.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha2/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDataMapperWorkspacesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (HealthcareProjectsLocationsDatasetsDataMapperWorkspacesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dataMapperWorkspaces/{dataMapperWorkspacesId}:setIamPolicy',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.dataMapperWorkspaces.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha2/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsDataMapperWorkspacesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (HealthcareProjectsLocationsDatasetsDataMapperWorkspacesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dataMapperWorkspaces/{dataMapperWorkspacesId}:testIamPermissions',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.dataMapperWorkspaces.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha2/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsDataMapperWorkspacesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsDicomStoresService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_dicomStores resource."""

    _NAME = 'projects_locations_datasets_dicomStores'

    def __init__(self, client):
      super(HealthcareV1alpha2.ProjectsLocationsDatasetsDicomStoresService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new DICOM store within the parent dataset.

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DicomStore) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.dicomStores.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['dicomStoreId'],
        relative_path='v1alpha2/{+parent}/dicomStores',
        request_field='dicomStore',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresCreateRequest',
        response_type_name='DicomStore',
        supports_download=False,
    )

    def Deidentify(self, request, global_params=None):
      r"""De-identifies data from the source store and writes it to the destination store. The metadata field type is OperationMetadata. If the request is successful, the response field type is DeidentifyDicomStoreSummary. The LRO result may still be successful if de-identification fails for some DICOM instances. The output DICOM store will not contain these failed resources. The number of resources processed are tracked in Operation.metadata. Error details are logged to Cloud Logging. For more information, see [Viewing error logs in Cloud Logging](https://cloud.google.com/healthcare/docs/how-tos/logging).

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresDeidentifyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Deidentify')
      return self._RunMethod(
          config, request, global_params=global_params)

    Deidentify.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}:deidentify',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.dicomStores.deidentify',
        ordered_params=['sourceStore'],
        path_params=['sourceStore'],
        query_params=[],
        relative_path='v1alpha2/{+sourceStore}:deidentify',
        request_field='deidentifyDicomStoreRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresDeidentifyRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified DICOM store and removes all images that are contained within it.

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.dicomStores.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Export(self, request, global_params=None):
      r"""Exports data to the specified destination by copying it from the DICOM store. Errors are also logged to Cloud Logging. For more information, see [Viewing error logs in Cloud Logging](https://cloud.google.com/healthcare/docs/how-tos/logging). The metadata field type is OperationMetadata.

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresExportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Export')
      return self._RunMethod(
          config, request, global_params=global_params)

    Export.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}:export',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.dicomStores.export',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}:export',
        request_field='exportDicomDataRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresExportRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the specified DICOM store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DicomStore) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.dicomStores.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresGetRequest',
        response_type_name='DicomStore',
        supports_download=False,
    )

    def GetDICOMStoreMetrics(self, request, global_params=None):
      r"""Gets metrics associated with the DICOM store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresGetDICOMStoreMetricsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DicomStoreMetrics) The response message.
      """
      config = self.GetMethodConfig('GetDICOMStoreMetrics')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetDICOMStoreMetrics.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}:getDICOMStoreMetrics',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.dicomStores.getDICOMStoreMetrics',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}:getDICOMStoreMetrics',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresGetDICOMStoreMetricsRequest',
        response_type_name='DicomStoreMetrics',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}:getIamPolicy',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.dicomStores.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha2/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def Import(self, request, global_params=None):
      r"""Imports data into the DICOM store by copying it from the specified source. Errors are logged to Cloud Logging. For more information, see [Viewing error logs in Cloud Logging](https://cloud.google.com/healthcare/docs/how-tos/logging). The metadata field type is OperationMetadata.

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Import')
      return self._RunMethod(
          config, request, global_params=global_params)

    Import.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}:import',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.dicomStores.import',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}:import',
        request_field='importDicomDataRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresImportRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the DICOM stores in the given dataset.

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDicomStoresResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.dicomStores.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha2/{+parent}/dicomStores',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresListRequest',
        response_type_name='ListDicomStoresResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified DICOM store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DicomStore) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}',
        http_method='PATCH',
        method_id='healthcare.projects.locations.datasets.dicomStores.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha2/{+name}',
        request_field='dicomStore',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresPatchRequest',
        response_type_name='DicomStore',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}:setIamPolicy',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.dicomStores.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha2/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}:testIamPermissions',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.dicomStores.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha2/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsFhirStoresService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_fhirStores resource."""

    _NAME = 'projects_locations_datasets_fhirStores'

    def __init__(self, client):
      super(HealthcareV1alpha2.ProjectsLocationsDatasetsFhirStoresService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new FHIR store within the parent dataset.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FhirStore) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.fhirStores.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['fhirStoreId'],
        relative_path='v1alpha2/{+parent}/fhirStores',
        request_field='fhirStore',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresCreateRequest',
        response_type_name='FhirStore',
        supports_download=False,
    )

    def Deidentify(self, request, global_params=None):
      r"""De-identifies data from the source store and writes it to the destination store. The metadata field type is OperationMetadata. If the request is successful, the response field type is DeidentifyFhirStoreSummary. The number of resources processed are tracked in Operation.metadata. Error details are logged to Cloud Logging. For more information, see [Viewing error logs in Cloud Logging](https://cloud.google.com/healthcare/docs/how-tos/logging).

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresDeidentifyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Deidentify')
      return self._RunMethod(
          config, request, global_params=global_params)

    Deidentify.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}:deidentify',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.fhirStores.deidentify',
        ordered_params=['sourceStore'],
        path_params=['sourceStore'],
        query_params=[],
        relative_path='v1alpha2/{+sourceStore}:deidentify',
        request_field='deidentifyFhirStoreRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresDeidentifyRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified FHIR store and removes all resources within it.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.fhirStores.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Export(self, request, global_params=None):
      r"""Export resources from the FHIR store to the specified destination. This method returns an Operation that can be used to track the status of the export by calling GetOperation. Immediate fatal errors appear in the error field, errors are also logged to Cloud Logging (see [Viewing error logs in Cloud Logging](https://cloud.google.com/healthcare/docs/how-tos/logging)). Otherwise, when the operation finishes, a detailed response of type ExportResourcesResponse is returned in the response field. The metadata field type for this operation is OperationMetadata.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresExportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Export')
      return self._RunMethod(
          config, request, global_params=global_params)

    Export.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}:export',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.fhirStores.export',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}:export',
        request_field='exportResourcesRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresExportRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the configuration of the specified FHIR store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FhirStore) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.fhirStores.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresGetRequest',
        response_type_name='FhirStore',
        supports_download=False,
    )

    def GetFHIRStoreMetrics(self, request, global_params=None):
      r"""Gets metrics associated with the FHIR store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresGetFHIRStoreMetricsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FhirStoreMetrics) The response message.
      """
      config = self.GetMethodConfig('GetFHIRStoreMetrics')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetFHIRStoreMetrics.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}:getFHIRStoreMetrics',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.fhirStores.getFHIRStoreMetrics',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}:getFHIRStoreMetrics',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresGetFHIRStoreMetricsRequest',
        response_type_name='FhirStoreMetrics',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}:getIamPolicy',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.fhirStores.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha2/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def Import(self, request, global_params=None):
      r"""Import resources to the FHIR store by loading data from the specified sources. This method is optimized to load large quantities of data using import semantics that ignore some FHIR store configuration options and are not suitable for all use cases. It is primarily intended to load data into an empty FHIR store that is not being used by other clients. In cases where this method is not appropriate, consider using ExecuteBundle to load data. Every resource in the input must contain a client-supplied ID. Each resource is stored using the supplied ID regardless of the enable_update_create setting on the FHIR store. It is strongly advised not to include or encode any sensitive data such as patient identifiers in client-specified resource IDs. Those IDs are part of the FHIR resource path recorded in Cloud Audit Logs and Cloud Pub/Sub notifications. Those IDs can also be contained in reference fields within other resources. The import process does not enforce referential integrity, regardless of the disable_referential_integrity setting on the FHIR store. This allows the import of resources with arbitrary interdependencies without considering grouping or ordering, but if the input data contains invalid references or if some resources fail to be imported, the FHIR store might be left in a state that violates referential integrity. The import process does not trigger Pub/Sub notification or BigQuery streaming update, regardless of how those are configured on the FHIR store. If a resource with the specified ID already exists, the most recent version of the resource is overwritten without creating a new historical version, regardless of the disable_resource_versioning setting on the FHIR store. If transient failures occur during the import, successfully imported resources could be overwritten more than once. The import operation is idempotent unless the input data contains multiple valid resources with the same ID but different contents. In that case, after the import completes, the store contains exactly one resource with that ID but there is no ordering guarantee on which version of the contents it has. The operation result counters do not count duplicate IDs as an error and count one success for each resource in the input, which might result in a success count larger than the number of resources in the FHIR store. This often occurs when importing data organized in bundles produced by Patient-everything where each bundle contains its own copy of a resource such as Practitioner that might be referred to by many patients. If some resources fail to import, for example due to parsing errors, successfully imported resources are not rolled back. The location and format of the input data are specified by the parameters in ImportResourcesRequest. Note that if no format is specified, this method assumes the `BUNDLE` format. When using the `BUNDLE` format this method ignores the `Bundle.type` field, except that `history` bundles are rejected, and does not apply any of the bundle processing semantics for batch or transaction bundles. Unlike in ExecuteBundle, transaction bundles are not executed as a single transaction and bundle-internal references are not rewritten. The bundle is treated as a collection of resources to be written as provided in `Bundle.entry.resource`, ignoring `Bundle.entry.request`. As an example, this allows the import of `searchset` bundles produced by a FHIR search or Patient-everything operation. This method returns an Operation that can be used to track the status of the import by calling GetOperation. Immediate fatal errors appear in the error field, errors are also logged to Cloud Logging (see [Viewing error logs in Cloud Logging](https://cloud.google.com/healthcare/docs/how-tos/logging)). Otherwise, when the operation finishes, a detailed response of type ImportResourcesResponse is returned in the response field. The metadata field type for this operation is OperationMetadata.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Import')
      return self._RunMethod(
          config, request, global_params=global_params)

    Import.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}:import',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.fhirStores.import',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}:import',
        request_field='importResourcesRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresImportRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the FHIR stores in the given dataset.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFhirStoresResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.fhirStores.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha2/{+parent}/fhirStores',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresListRequest',
        response_type_name='ListFhirStoresResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the configuration of the specified FHIR store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FhirStore) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}',
        http_method='PATCH',
        method_id='healthcare.projects.locations.datasets.fhirStores.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha2/{+name}',
        request_field='fhirStore',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresPatchRequest',
        response_type_name='FhirStore',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}:setIamPolicy',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.fhirStores.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha2/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}:testIamPermissions',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.fhirStores.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha2/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsHl7V2StoresMessagesService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_hl7V2Stores_messages resource."""

    _NAME = 'projects_locations_datasets_hl7V2Stores_messages'

    def __init__(self, client):
      super(HealthcareV1alpha2.ProjectsLocationsDatasetsHl7V2StoresMessagesService, self).__init__(client)
      self._upload_configs = {
          }

    def Export(self, request, global_params=None):
      r"""Exports the messages to a destination in the store with transformations. The start and the end time relative to message generation time (MSH.7) can be specified to filter messages in a range instead of exporting all at once. This API returns an Operation that can be used to track the status of the job by calling GetOperation. Immediate fatal errors appear in the error field. Otherwise, when the operation finishes, a detailed response of type ExportMessagesResponse is returned in the response field. The metadata field type for this operation is OperationMetadata.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesExportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Export')
      return self._RunMethod(
          config, request, global_params=global_params)

    Export.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores/{hl7V2StoresId}/messages:export',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.messages.export',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}/messages:export',
        request_field='exportMessagesRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesExportRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsHl7V2StoresService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_hl7V2Stores resource."""

    _NAME = 'projects_locations_datasets_hl7V2Stores'

    def __init__(self, client):
      super(HealthcareV1alpha2.ProjectsLocationsDatasetsHl7V2StoresService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new HL7v2 store within the parent dataset.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Hl7V2Store) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['hl7V2StoreId'],
        relative_path='v1alpha2/{+parent}/hl7V2Stores',
        request_field='hl7V2Store',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresCreateRequest',
        response_type_name='Hl7V2Store',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified HL7v2 store and removes all messages that it contains.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores/{hl7V2StoresId}',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Export(self, request, global_params=None):
      r"""Exports the messages to a destination in the store with transformations. The start and the end time relative to message generation time (MSH.7) can be specified to filter messages in a range instead of exporting all at once. This API returns an Operation that can be used to track the status of the job by calling GetOperation. Immediate fatal errors appear in the error field. Otherwise, when the operation finishes, a detailed response of type ExportMessagesResponse is returned in the response field. The metadata field type for this operation is OperationMetadata.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresExportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Export')
      return self._RunMethod(
          config, request, global_params=global_params)

    Export.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores/{hl7V2StoresId}:export',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.export',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}:export',
        request_field='exportMessagesRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresExportRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the specified HL7v2 store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Hl7V2Store) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores/{hl7V2StoresId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresGetRequest',
        response_type_name='Hl7V2Store',
        supports_download=False,
    )

    def GetHL7v2StoreMetrics(self, request, global_params=None):
      r"""Gets metrics associated with the HL7v2 store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresGetHL7v2StoreMetricsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Hl7V2StoreMetrics) The response message.
      """
      config = self.GetMethodConfig('GetHL7v2StoreMetrics')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetHL7v2StoreMetrics.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores/{hl7V2StoresId}:getHL7v2StoreMetrics',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.getHL7v2StoreMetrics',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}:getHL7v2StoreMetrics',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresGetHL7v2StoreMetricsRequest',
        response_type_name='Hl7V2StoreMetrics',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores/{hl7V2StoresId}:getIamPolicy',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha2/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def Import(self, request, global_params=None):
      r"""Import messages to the HL7v2 store by loading data from the specified sources. This method is optimized to load large quantities of data using import semantics that ignore some HL7v2 store configuration options and are not suitable for all use cases. It is primarily intended to load data into an empty HL7v2 store that is not being used by other clients. An existing message will be overwritten if a duplicate message is imported. A duplicate message is a message with the same raw bytes as a message that already exists in this HL7v2 store. When a message is overwritten, its labels will also be overwritten. The import operation is idempotent unless the input data contains multiple valid messages with the same raw bytes but different labels. In that case, after the import completes, the store contains exactly one message with those raw bytes but there is no ordering guarantee on which version of the labels it has. The operation result counters do not count duplicated raw bytes as an error and count one success for each message in the input, which might result in a success count larger than the number of messages in the HL7v2 store. If some messages fail to import, for example due to parsing errors, successfully imported messages are not rolled back. This method returns an Operation that can be used to track the status of the import by calling GetOperation. Immediate fatal errors appear in the error field, errors are also logged to Cloud Logging (see [Viewing error logs in Cloud Logging](https://cloud.google.com/healthcare/docs/how-tos/logging)). Otherwise, when the operation finishes, a response of type ImportMessagesResponse is returned in the response field. The metadata field type for this operation is OperationMetadata.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Import')
      return self._RunMethod(
          config, request, global_params=global_params)

    Import.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores/{hl7V2StoresId}:import',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.import',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}:import',
        request_field='importMessagesRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresImportRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the HL7v2 stores in the given dataset.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListHl7V2StoresResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha2/{+parent}/hl7V2Stores',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresListRequest',
        response_type_name='ListHl7V2StoresResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the HL7v2 store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Hl7V2Store) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores/{hl7V2StoresId}',
        http_method='PATCH',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha2/{+name}',
        request_field='hl7V2Store',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresPatchRequest',
        response_type_name='Hl7V2Store',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores/{hl7V2StoresId}:setIamPolicy',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha2/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores/{hl7V2StoresId}:testIamPermissions',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha2/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_operations resource."""

    _NAME = 'projects_locations_datasets_operations'

    def __init__(self, client):
      super(HealthcareV1alpha2.ProjectsLocationsDatasetsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (HealthcareProjectsLocationsDatasetsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (HealthcareProjectsLocationsDatasetsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/operations/{operationsId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (HealthcareProjectsLocationsDatasetsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/operations',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha2/{+name}/operations',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets resource."""

    _NAME = 'projects_locations_datasets'

    def __init__(self, client):
      super(HealthcareV1alpha2.ProjectsLocationsDatasetsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new health dataset. Results are returned through the Operation interface which returns either an `Operation.response` which contains a Dataset or `Operation.error`. The metadata field type is OperationMetadata.

      Args:
        request: (HealthcareProjectsLocationsDatasetsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['datasetId'],
        relative_path='v1alpha2/{+parent}/datasets',
        request_field='dataset',
        request_type_name='HealthcareProjectsLocationsDatasetsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Deidentify(self, request, global_params=None):
      r"""Creates a new dataset containing de-identified data from the source dataset. The metadata field type is OperationMetadata. If the request is successful, the response field type is DeidentifySummary. The LRO result may still be successful if de-identification fails for some DICOM instances. The new de-identified dataset will not contain these failed resources. The number of resources processed are tracked in Operation.metadata. Error details are logged to Cloud Logging. For more information, see [Viewing error logs in Cloud Logging](https://cloud.google.com/healthcare/docs/how-tos/logging).

      Args:
        request: (HealthcareProjectsLocationsDatasetsDeidentifyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Deidentify')
      return self._RunMethod(
          config, request, global_params=global_params)

    Deidentify.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}:deidentify',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.deidentify',
        ordered_params=['sourceDataset'],
        path_params=['sourceDataset'],
        query_params=[],
        relative_path='v1alpha2/{+sourceDataset}:deidentify',
        request_field='deidentifyDatasetRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsDeidentifyRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified health dataset and all data contained in the dataset. Deleting a dataset does not affect the sources from which the dataset was imported (if any).

      Args:
        request: (HealthcareProjectsLocationsDatasetsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets any metadata associated with a dataset.

      Args:
        request: (HealthcareProjectsLocationsDatasetsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Dataset) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsGetRequest',
        response_type_name='Dataset',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (HealthcareProjectsLocationsDatasetsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}:getIamPolicy',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha2/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the health datasets in the current project.

      Args:
        request: (HealthcareProjectsLocationsDatasetsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDatasetsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha2/{+parent}/datasets',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsListRequest',
        response_type_name='ListDatasetsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates dataset metadata.

      Args:
        request: (HealthcareProjectsLocationsDatasetsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Dataset) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}',
        http_method='PATCH',
        method_id='healthcare.projects.locations.datasets.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha2/{+name}',
        request_field='dataset',
        request_type_name='HealthcareProjectsLocationsDatasetsPatchRequest',
        response_type_name='Dataset',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (HealthcareProjectsLocationsDatasetsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}:setIamPolicy',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha2/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (HealthcareProjectsLocationsDatasetsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}:testIamPermissions',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha2/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsServicesDeidentifyService(base_api.BaseApiService):
    """Service class for the projects_locations_services_deidentify resource."""

    _NAME = 'projects_locations_services_deidentify'

    def __init__(self, client):
      super(HealthcareV1alpha2.ProjectsLocationsServicesDeidentifyService, self).__init__(client)
      self._upload_configs = {
          }

    def DeidentifyDicomInstance(self, request, global_params=None):
      r"""De-identify a single DICOM instance. Uses the ATTRIBUTE_CONFIDENTIALITY_BASIC_PROFILE TagFilterProfile and the REDACT_ALL_TEXT TextRedactionMode.

      Args:
        request: (HealthcareProjectsLocationsServicesDeidentifyDeidentifyDicomInstanceRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('DeidentifyDicomInstance')
      return self._RunMethod(
          config, request, global_params=global_params)

    DeidentifyDicomInstance.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/services/deidentify:deidentifyDicomInstance',
        http_method='POST',
        method_id='healthcare.projects.locations.services.deidentify.deidentifyDicomInstance',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['gcsConfigUri'],
        relative_path='v1alpha2/{+name}:deidentifyDicomInstance',
        request_field='httpBody',
        request_type_name='HealthcareProjectsLocationsServicesDeidentifyDeidentifyDicomInstanceRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def DeidentifyFhirResource(self, request, global_params=None):
      r"""De-identify a single FHIR resource.

      Args:
        request: (HealthcareProjectsLocationsServicesDeidentifyDeidentifyFhirResourceRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('DeidentifyFhirResource')
      return self._RunMethod(
          config, request, global_params=global_params)

    DeidentifyFhirResource.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/services/deidentify:deidentifyFhirResource',
        http_method='POST',
        method_id='healthcare.projects.locations.services.deidentify.deidentifyFhirResource',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['gcsConfigUri', 'version'],
        relative_path='v1alpha2/{+name}:deidentifyFhirResource',
        request_field='httpBody',
        request_type_name='HealthcareProjectsLocationsServicesDeidentifyDeidentifyFhirResourceRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

  class ProjectsLocationsServicesNlpService(base_api.BaseApiService):
    """Service class for the projects_locations_services_nlp resource."""

    _NAME = 'projects_locations_services_nlp'

    def __init__(self, client):
      super(HealthcareV1alpha2.ProjectsLocationsServicesNlpService, self).__init__(client)
      self._upload_configs = {
          }

    def AnalyzeEntities(self, request, global_params=None):
      r"""Analyze heathcare entity in a document. Its response includes the recognized entity mentions and the relationships between them. AnalyzeEntities uses context aware models to detect entities.

      Args:
        request: (HealthcareProjectsLocationsServicesNlpAnalyzeEntitiesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AnalyzeEntitiesResponse) The response message.
      """
      config = self.GetMethodConfig('AnalyzeEntities')
      return self._RunMethod(
          config, request, global_params=global_params)

    AnalyzeEntities.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}/services/nlp:analyzeEntities',
        http_method='POST',
        method_id='healthcare.projects.locations.services.nlp.analyzeEntities',
        ordered_params=['nlpService'],
        path_params=['nlpService'],
        query_params=[],
        relative_path='v1alpha2/{+nlpService}:analyzeEntities',
        request_field='analyzeEntitiesRequest',
        request_type_name='HealthcareProjectsLocationsServicesNlpAnalyzeEntitiesRequest',
        response_type_name='AnalyzeEntitiesResponse',
        supports_download=False,
    )

  class ProjectsLocationsServicesService(base_api.BaseApiService):
    """Service class for the projects_locations_services resource."""

    _NAME = 'projects_locations_services'

    def __init__(self, client):
      super(HealthcareV1alpha2.ProjectsLocationsServicesService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(HealthcareV1alpha2.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (HealthcareProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='healthcare.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha2/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (HealthcareProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha2/projects/{projectsId}/locations',
        http_method='GET',
        method_id='healthcare.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha2/{+name}/locations',
        request_field='',
        request_type_name='HealthcareProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(HealthcareV1alpha2.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
