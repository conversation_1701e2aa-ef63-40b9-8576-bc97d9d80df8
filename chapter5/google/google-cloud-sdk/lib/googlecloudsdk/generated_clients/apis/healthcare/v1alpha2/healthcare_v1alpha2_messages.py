"""Generated message classes for healthcare version v1alpha2.

Manage, store, and access healthcare data in Google Cloud Platform.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'healthcare'


class AnalyzeEntitiesRequest(_messages.Message):
  r"""The request to analyze healthcare entities in a document.

  Enums:
    AlternativeOutputFormatValueValuesEnum: Alternative output format to be
      generated based on the results of analysis.
    LicensedVocabulariesValueListEntryValuesEnum:

  Fields:
    alternativeOutputFormat: Alternative output format to be generated based
      on the results of analysis.
    documentContent: document_content is a document to be annotated.
    licensedVocabularies: A list of licensed vocabularies to use in the
      request, in addition to the default unlicensed vocabularies.
  """

  class AlternativeOutputFormatValueValuesEnum(_messages.Enum):
    r"""Alternative output format to be generated based on the results of
    analysis.

    Values:
      ALTERNATIVE_OUTPUT_FORMAT_UNSPECIFIED: No alternative output format is
        specified.
      FHIR_BUNDLE: FHIR bundle output.
    """
    ALTERNATIVE_OUTPUT_FORMAT_UNSPECIFIED = 0
    FHIR_BUNDLE = 1

  class LicensedVocabulariesValueListEntryValuesEnum(_messages.Enum):
    r"""LicensedVocabulariesValueListEntryValuesEnum enum type.

    Values:
      LICENSED_VOCABULARY_UNSPECIFIED: No licensed vocabulary specified.
      ICD10CM: ICD-10-CM vocabulary
      SNOMEDCT_US: SNOMED CT (US version) vocabulary
    """
    LICENSED_VOCABULARY_UNSPECIFIED = 0
    ICD10CM = 1
    SNOMEDCT_US = 2

  alternativeOutputFormat = _messages.EnumField('AlternativeOutputFormatValueValuesEnum', 1)
  documentContent = _messages.StringField(2)
  licensedVocabularies = _messages.EnumField('LicensedVocabulariesValueListEntryValuesEnum', 3, repeated=True)


class AnalyzeEntitiesResponse(_messages.Message):
  r"""Includes recognized entity mentions and relationships between them.

  Fields:
    entities: The union of all the candidate entities that the entity_mentions
      in this response could link to. These are UMLS concepts or normalized
      mention content.
    entityMentions: The `entity_mentions` field contains all the annotated
      medical entities that were mentioned in the provided document.
    fhirBundle: The FHIR bundle ([`R4`](https://www.hl7.org/fhir/R4)) that
      includes all the entities, the entity mentions, and the relationships in
      JSON format.
    relationships: relationships contains all the binary relationships that
      were identified between entity mentions within the provided document.
  """

  entities = _messages.MessageField('Entity', 1, repeated=True)
  entityMentions = _messages.MessageField('EntityMention', 2, repeated=True)
  fhirBundle = _messages.StringField(3)
  relationships = _messages.MessageField('EntityMentionRelationship', 4, repeated=True)


class ArchiveUserDataMappingRequest(_messages.Message):
  r"""Archives the specified User data mapping."""


class ArchiveUserDataMappingResponse(_messages.Message):
  r"""Archives the specified User data mapping."""


class Attribute(_messages.Message):
  r"""An attribute value for a Consent or User data mapping. Each Attribute
  must have a corresponding AttributeDefinition in the consent store that
  defines the default and allowed values.

  Fields:
    attributeDefinitionId: Indicates the name of an attribute defined in the
      consent store.
    values: Required. The value of the attribute. Must be an acceptable value
      as defined in the consent store. For example, if the consent store
      defines "data type" with acceptable values "questionnaire" and "step-
      count", when the attribute name is data type, this field must contain
      one of those values.
  """

  attributeDefinitionId = _messages.StringField(1)
  values = _messages.StringField(2, repeated=True)


class AttributeDefinition(_messages.Message):
  r"""A client-defined consent attribute.

  Enums:
    CategoryValueValuesEnum: Required. The category of the attribute. The
      value of this field cannot be changed after creation.

  Fields:
    allowedValues: Required. Possible values for the attribute. The number of
      allowed values must not exceed 500. An empty list is invalid. The list
      can only be expanded after creation.
    category: Required. The category of the attribute. The value of this field
      cannot be changed after creation.
    consentDefaultValues: Optional. Default values of the attribute in
      Consents. If no default values are specified, it defaults to an empty
      value.
    dataMappingDefaultValue: Optional. Default value of the attribute in User
      data mappings. If no default value is specified, it defaults to an empty
      value. This field is only applicable to attributes of the category
      `RESOURCE`.
    description: Optional. A description of the attribute.
    name: Identifier. Resource name of the Attribute definition, of the form `
      projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/cons
      entStores/{consent_store_id}/attributeDefinitions/{attribute_definition_
      id}`. Cannot be changed after creation.
  """

  class CategoryValueValuesEnum(_messages.Enum):
    r"""Required. The category of the attribute. The value of this field
    cannot be changed after creation.

    Values:
      CATEGORY_UNSPECIFIED: No category specified. This option is invalid.
      RESOURCE: Specify this category when this attribute describes the
        properties of resources. For example, data anonymity or data type.
      REQUEST: Specify this category when this attribute describes the
        properties of requests. For example, requester's role or requester's
        organization.
    """
    CATEGORY_UNSPECIFIED = 0
    RESOURCE = 1
    REQUEST = 2

  allowedValues = _messages.StringField(1, repeated=True)
  category = _messages.EnumField('CategoryValueValuesEnum', 2)
  consentDefaultValues = _messages.StringField(3, repeated=True)
  dataMappingDefaultValue = _messages.StringField(4)
  description = _messages.StringField(5)
  name = _messages.StringField(6)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CharacterMaskConfig(_messages.Message):
  r"""Masks a string by replacing its characters with a fixed character.

  Fields:
    maskingCharacter: Character to mask the sensitive values. If not supplied,
      defaults to "*".
  """

  maskingCharacter = _messages.StringField(1)


class CheckDataAccessRequest(_messages.Message):
  r"""Checks if a particular data_id of a User data mapping in the given
  consent store is consented for a given use.

  Enums:
    ResponseViewValueValuesEnum: Optional. The view for
      CheckDataAccessResponse. If unspecified, defaults to `BASIC` and returns
      `consented` as `TRUE` or `FALSE`.

  Messages:
    RequestAttributesValue: The values of request attributes associated with
      this access request.

  Fields:
    consentList: Optional. Specific Consents to evaluate the access request
      against. These Consents must have the same `user_id` as the evaluated
      User data mapping, must exist in the current `consent_store`, and have a
      `state` of either `ACTIVE` or `DRAFT`. A maximum of 100 Consents can be
      provided here. If no selection is specified, the access request is
      evaluated against all `ACTIVE` unexpired Consents with the same
      `user_id` as the evaluated User data mapping.
    dataId: Required. The unique identifier of the resource to check access
      for. This identifier must correspond to a User data mapping in the given
      consent store.
    requestAttributes: The values of request attributes associated with this
      access request.
    responseView: Optional. The view for CheckDataAccessResponse. If
      unspecified, defaults to `BASIC` and returns `consented` as `TRUE` or
      `FALSE`.
  """

  class ResponseViewValueValuesEnum(_messages.Enum):
    r"""Optional. The view for CheckDataAccessResponse. If unspecified,
    defaults to `BASIC` and returns `consented` as `TRUE` or `FALSE`.

    Values:
      RESPONSE_VIEW_UNSPECIFIED: No response view specified. The API will
        default to the BASIC view.
      BASIC: Only the `consented` field is populated in
        CheckDataAccessResponse.
      FULL: All fields within CheckDataAccessResponse are populated. When set
        to `FULL`, all `ACTIVE` Consents are evaluated even if a matching
        policy is found during evaluation.
    """
    RESPONSE_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class RequestAttributesValue(_messages.Message):
    r"""The values of request attributes associated with this access request.

    Messages:
      AdditionalProperty: An additional property for a RequestAttributesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        RequestAttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a RequestAttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  consentList = _messages.MessageField('ConsentList', 1)
  dataId = _messages.StringField(2)
  requestAttributes = _messages.MessageField('RequestAttributesValue', 3)
  responseView = _messages.EnumField('ResponseViewValueValuesEnum', 4)


class CheckDataAccessResponse(_messages.Message):
  r"""Checks if a particular data_id of a User data mapping in the given
  consent store is consented for a given use.

  Messages:
    ConsentDetailsValue: The resource names of all evaluated Consents mapped
      to their evaluation.

  Fields:
    consentDetails: The resource names of all evaluated Consents mapped to
      their evaluation.
    consented: Whether the requested resource is consented for the given use.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ConsentDetailsValue(_messages.Message):
    r"""The resource names of all evaluated Consents mapped to their
    evaluation.

    Messages:
      AdditionalProperty: An additional property for a ConsentDetailsValue
        object.

    Fields:
      additionalProperties: Additional properties of type ConsentDetailsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ConsentDetailsValue object.

      Fields:
        key: Name of the additional property.
        value: A ConsentEvaluation attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ConsentEvaluation', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  consentDetails = _messages.MessageField('ConsentDetailsValue', 1)
  consented = _messages.BooleanField(2)


class Consent(_messages.Message):
  r"""Represents a user's consent.

  Enums:
    StateValueValuesEnum: Required. Indicates the current state of this
      Consent.

  Fields:
    consentArtifact: Required. The resource name of the Consent artifact that
      contains proof of the end user's consent, of the form `projects/{project
      _id}/locations/{location_id}/datasets/{dataset_id}/consentStores/{consen
      t_store_id}/consentArtifacts/{consent_artifact_id}`.
    name: Resource name of the Consent, of the form `projects/{project_id}/loc
      ations/{location_id}/datasets/{dataset_id}/consentStores/{consent_store_
      id}/consents/{consent_id}`. Cannot be changed after creation.
    policies: Optional. Represents a user's consent in terms of the resources
      that can be accessed and under what conditions.
    state: Required. Indicates the current state of this Consent.
    userId: Required. User's UUID provided by the client.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Required. Indicates the current state of this Consent.

    Values:
      STATE_UNSPECIFIED: No state specified. Treated as ACTIVE only at the
        time of resource creation.
      ACTIVE: The Consent is active and is considered when evaluating a user's
        consent on resources.
      ARCHIVED: The archived state is currently not being used.
      REVOKED: A revoked Consent is not considered when evaluating a user's
        consent on resources.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    ARCHIVED = 2
    REVOKED = 3

  consentArtifact = _messages.StringField(1)
  name = _messages.StringField(2)
  policies = _messages.MessageField('GoogleCloudHealthcareV1alpha2ConsentPolicy', 3, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 4)
  userId = _messages.StringField(5)


class ConsentArtifact(_messages.Message):
  r"""Documentation of a user's consent.

  Messages:
    MetadataValue: Optional. Metadata associated with the Consent artifact.
      For example, the consent locale or user agent version.

  Fields:
    consentContentScreenshots: Optional. Screenshots, PDFs, or other binary
      information documenting the user's consent.
    consentContentVersion: Optional. An string indicating the version of the
      consent information shown to the user.
    guardianSignature: Optional. A signature from a guardian.
    metadata: Optional. Metadata associated with the Consent artifact. For
      example, the consent locale or user agent version.
    name: Identifier. Resource name of the Consent artifact, of the form `proj
      ects/{project_id}/locations/{location_id}/datasets/{dataset_id}/consentS
      tores/{consent_store_id}/consentArtifacts/{consent_artifact_id}`. Cannot
      be changed after creation.
    userId: Required. User's UUID provided by the client.
    userSignature: Optional. User's signature.
    witnessSignature: Optional. A signature from a witness.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Optional. Metadata associated with the Consent artifact. For example,
    the consent locale or user agent version.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Additional properties of type MetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  consentContentScreenshots = _messages.MessageField('Image', 1, repeated=True)
  consentContentVersion = _messages.StringField(2)
  guardianSignature = _messages.MessageField('Signature', 3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)
  userId = _messages.StringField(6)
  userSignature = _messages.MessageField('Signature', 7)
  witnessSignature = _messages.MessageField('Signature', 8)


class ConsentEvaluation(_messages.Message):
  r"""The detailed evaluation of a particular Consent.

  Enums:
    EvaluationResultValueValuesEnum: The evaluation result.

  Fields:
    evaluationResult: The evaluation result.
  """

  class EvaluationResultValueValuesEnum(_messages.Enum):
    r"""The evaluation result.

    Values:
      EVALUATION_RESULT_UNSPECIFIED: No evaluation result specified. This
        option is invalid.
      NOT_APPLICABLE: The Consent is not applicable to the requested access
        determination. For example, the Consent does not apply to the user for
        which the access determination is requested, or it has a `state` of
        `REVOKED`, or it has expired.
      NO_MATCHING_POLICY: The Consent does not have a policy that matches the
        `resource_attributes` of the evaluated resource.
      NO_SATISFIED_POLICY: The Consent has at least one policy that matches
        the `resource_attributes` of the evaluated resource, but no
        `authorization_rule` was satisfied.
      HAS_SATISFIED_POLICY: The Consent has at least one policy that matches
        the `resource_attributes` of the evaluated resource, and at least one
        `authorization_rule` was satisfied.
    """
    EVALUATION_RESULT_UNSPECIFIED = 0
    NOT_APPLICABLE = 1
    NO_MATCHING_POLICY = 2
    NO_SATISFIED_POLICY = 3
    HAS_SATISFIED_POLICY = 4

  evaluationResult = _messages.EnumField('EvaluationResultValueValuesEnum', 1)


class ConsentList(_messages.Message):
  r"""List of resource names of Consent resources.

  Fields:
    consents: The resource names of the Consents to evaluate against, of the
      form `projects/{project_id}/locations/{location_id}/datasets/{dataset_id
      }/consentStores/{consent_store_id}/consents/{consent_id}`.
  """

  consents = _messages.StringField(1, repeated=True)


class ConsentStore(_messages.Message):
  r"""Represents a consent store.

  Messages:
    LabelsValue: Optional. User-supplied key-value pairs used to organize
      consent stores. Label keys must be between 1 and 63 characters long,
      have a UTF-8 encoding of maximum 128 bytes, and must conform to the
      following PCRE regular expression: \p{Ll}\p{Lo}{0,62}. Label values must
      be between 1 and 63 characters long, have a UTF-8 encoding of maximum
      128 bytes, and must conform to the following PCRE regular expression:
      [\p{Ll}\p{Lo}\p{N}_-]{0,63}. No more than 64 labels can be associated
      with a given store. For more information:
      https://cloud.google.com/healthcare/docs/how-tos/labeling-resources

  Fields:
    labels: Optional. User-supplied key-value pairs used to organize consent
      stores. Label keys must be between 1 and 63 characters long, have a
      UTF-8 encoding of maximum 128 bytes, and must conform to the following
      PCRE regular expression: \p{Ll}\p{Lo}{0,62}. Label values must be
      between 1 and 63 characters long, have a UTF-8 encoding of maximum 128
      bytes, and must conform to the following PCRE regular expression:
      [\p{Ll}\p{Lo}\p{N}_-]{0,63}. No more than 64 labels can be associated
      with a given store. For more information:
      https://cloud.google.com/healthcare/docs/how-tos/labeling-resources
    name: Identifier. Resource name of the consent store, of the form `project
      s/{project_id}/locations/{location_id}/datasets/{dataset_id}/consentStor
      es/{consent_store_id}`. Cannot be changed after creation.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. User-supplied key-value pairs used to organize consent
    stores. Label keys must be between 1 and 63 characters long, have a UTF-8
    encoding of maximum 128 bytes, and must conform to the following PCRE
    regular expression: \p{Ll}\p{Lo}{0,62}. Label values must be between 1 and
    63 characters long, have a UTF-8 encoding of maximum 128 bytes, and must
    conform to the following PCRE regular expression:
    [\p{Ll}\p{Lo}\p{N}_-]{0,63}. No more than 64 labels can be associated with
    a given store. For more information:
    https://cloud.google.com/healthcare/docs/how-tos/labeling-resources

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)
  name = _messages.StringField(2)


class CryptoHashConfig(_messages.Message):
  r"""Pseudonymization method that generates surrogates using cryptographic
  hashing. Uses SHA-256. Outputs a base64-encoded representation of the hashed
  output. For example, `L7k0BHmF1ha5U3NfGykjro4xWi1MPVQPjhMAZbSV9mM=`.

  Fields:
    cryptoKey: An AES 128/192/256 bit key. Causes the hash to be computed
      based on this key. A default key is generated for each Deidentify
      operation and is used when neither crypto_key nor kms_wrapped is
      specified. Must not be set if kms_wrapped is set.
  """

  cryptoKey = _messages.BytesField(1)


class Dataset(_messages.Message):
  r"""A message representing a health dataset. A health dataset represents a
  collection of healthcare data pertaining to one or more patients. This may
  include multiple modalities of healthcare data, such as electronic medical
  records or medical imaging data.

  Fields:
    encryptionSpec: Customer-managed encryption key spec for a Dataset. If
      set, this Dataset and all of its sub-resources will be secured by this
      key. If empty, the Dataset is secured by the default Google encryption
      key.
    name: Identifier. Resource name of the dataset, of the form
      `projects/{project_id}/locations/{location_id}/datasets/{dataset_id}`.
    satisfiesPzi: Output only. Whether the dataset satisfies zone isolation.
    satisfiesPzs: Output only. Whether the dataset satisfies zone separation.
    timeZone: The default timezone used by this dataset. Must be a either a
      valid IANA time zone name such as "America/New_York" or empty, which
      defaults to UTC. This is used for parsing times in resources, such as
      HL7v2 messages, where no explicit timezone is specified.
  """

  encryptionSpec = _messages.MessageField('EncryptionSpec', 1)
  name = _messages.StringField(2)
  satisfiesPzi = _messages.BooleanField(3)
  satisfiesPzs = _messages.BooleanField(4)
  timeZone = _messages.StringField(5)


class DateShiftConfig(_messages.Message):
  r"""Shifts a date forward or backward in time by a random amount that is
  consistent for a given patient and crypto key combination.

  Fields:
    cryptoKey: An AES 128/192/256 bit key. The date shift is computed based on
      this key and the patient ID. If the patient ID is empty for a DICOM
      resource, the date shift is computed based on this key and the study
      instance UID. If crypto_key is not set, then kms_wrapped is used to
      calculate the date shift. If neither is set, a default key is generated
      for each de-identify operation. Must not be set if kms_wrapped is set.
  """

  cryptoKey = _messages.BytesField(1)


class DeidentifiedStoreDestination(_messages.Message):
  r"""Contains configuration for streaming de-identified FHIR export.

  Fields:
    config: The configuration to use when de-identifying resources that are
      added to this store.
    store: The full resource name of a Cloud Healthcare FHIR store, for
      example, `projects/{project_id}/locations/{location_id}/datasets/{datase
      t_id}/fhirStores/{fhir_store_id}`.
  """

  config = _messages.MessageField('DeidentifyConfig', 1)
  store = _messages.StringField(2)


class DeidentifyConfig(_messages.Message):
  r"""Configures de-id options specific to different types of content. Each
  submessage customizes the handling of an https://tools.ietf.org/html/rfc6838
  media type or subtype. Configs are applied in a nested manner at runtime.

  Fields:
    dicom: Configures de-id of application/DICOM content. Deprecated. Use
      `dicom_tag_config` instead.
    fhir: Configures de-id of application/FHIR content. Deprecated. Use
      `fhir_field_config` instead.
    image: Configures the de-identification of image pixels in the
      source_dataset. Deprecated. Use `dicom_tag_config.options.clean_image`
      instead.
    text: Configures the de-identification of text in `source_dataset`.
  """

  dicom = _messages.MessageField('DicomConfig', 1)
  fhir = _messages.MessageField('FhirConfig', 2)
  image = _messages.MessageField('ImageConfig', 3)
  text = _messages.MessageField('TextConfig', 4)


class DeidentifyDatasetRequest(_messages.Message):
  r"""Redacts identifying information from the specified dataset.

  Fields:
    config: Deidentify configuration. Only one of `config` and
      `gcs_config_uri` can be specified.
    destinationDataset: Required. The name of the dataset resource to create
      and write the redacted data to. For example,
      `projects/{project_id}/locations/{location_id}/datasets/{dataset_id}`. *
      The destination dataset must not exist. * The destination dataset must
      be in the same location as the source dataset. De-identifying data
      across multiple locations is not supported.
  """

  config = _messages.MessageField('DeidentifyConfig', 1)
  destinationDataset = _messages.StringField(2)


class DeidentifyDicomStoreRequest(_messages.Message):
  r"""Creates a new DICOM store with sensitive information de-identified.

  Fields:
    config: Deidentify configuration. Only one of `config` and
      `gcs_config_uri` can be specified.
    destinationStore: Required. The name of the DICOM store to create and
      write the redacted data to. For example, `projects/{project_id}/location
      s/{location_id}/datasets/{dataset_id}/dicomStores/{dicom_store_id}`. *
      The destination dataset must exist. * The source dataset and destination
      dataset must both reside in the same location. De-identifying data
      across multiple locations is not supported. * The destination DICOM
      store must not exist. * The caller must have the necessary permissions
      to create the destination DICOM store.
  """

  config = _messages.MessageField('DeidentifyConfig', 1)
  destinationStore = _messages.StringField(2)


class DeidentifyFhirStoreRequest(_messages.Message):
  r"""Creates a new FHIR store with sensitive information de-identified.

  Fields:
    config: Deidentify configuration. Only one of `config` and
      `gcs_config_uri` can be specified.
    destinationStore: Required. The name of the FHIR store to create and write
      the redacted data to. For example, `projects/{project_id}/locations/{loc
      ation_id}/datasets/{dataset_id}/fhirStores/{fhir_store_id}`. * The
      destination dataset must exist. * The source dataset and destination
      dataset must both reside in the same location. De-identifying data
      across multiple locations is not supported. * The destination FHIR store
      must exist. * The caller must have the healthcare.fhirResources.update
      permission to write to the destination FHIR store.
  """

  config = _messages.MessageField('DeidentifyConfig', 1)
  destinationStore = _messages.StringField(2)


class DeidentifySummary(_messages.Message):
  r"""Contains a detailed summary of the Deidentify operation."""


class DicomConfig(_messages.Message):
  r"""Specifies the parameters needed for the de-identification of DICOM
  stores.

  Enums:
    FilterProfileValueValuesEnum: Tag filtering profile that determines the
      tags to keep or remove.

  Fields:
    filterProfile: Tag filtering profile that determines the tags to keep or
      remove.
    keepList: List of tags to keep. Remove all other tags.
    removeList: List of tags to remove. Keep all other tags.
  """

  class FilterProfileValueValuesEnum(_messages.Enum):
    r"""Tag filtering profile that determines the tags to keep or remove.

    Values:
      TAG_FILTER_PROFILE_UNSPECIFIED: No tag filtration profile provided. Same
        as KEEP_ALL_PROFILE.
      MINIMAL_KEEP_LIST_PROFILE: Keep only tags required to produce valid
        DICOM.
      ATTRIBUTE_CONFIDENTIALITY_BASIC_PROFILE: Remove tags based on DICOM
        Standard's [Attribute Confidentiality Basic Profile (DICOM Standard
        Edition 2018e)] (http://dicom.nema.org/medical/dicom/2018e/output/chtm
        l/part15/chapter_E.html).
      KEEP_ALL_PROFILE: Keep all tags.
      DEIDENTIFY_TAG_CONTENTS: Inspects within tag contents (including tags
        nested in a sequence) and replaces sensitive text. The process can be
        configured using the TextConfig. Applies to all tags with the
        following Value Representation names: AE, LO, LT, PN, SH, ST, UC, UT,
        DA, DT, AS.
    """
    TAG_FILTER_PROFILE_UNSPECIFIED = 0
    MINIMAL_KEEP_LIST_PROFILE = 1
    ATTRIBUTE_CONFIDENTIALITY_BASIC_PROFILE = 2
    KEEP_ALL_PROFILE = 3
    DEIDENTIFY_TAG_CONTENTS = 4

  filterProfile = _messages.EnumField('FilterProfileValueValuesEnum', 1)
  keepList = _messages.MessageField('TagFilterList', 2)
  removeList = _messages.MessageField('TagFilterList', 3)


class DicomFilterConfig(_messages.Message):
  r"""Specifies the filter configuration for DICOM resources.

  Fields:
    resourcePathsGcsUri: The Google Cloud Storage location of the filter
      configuration file. The `gcs_uri` must be in the format
      "gs://bucket/path/to/object" The filter configuration file must contain
      a list resource paths separated by new line characters (\n or \r\n).
      Each resource path must be in the format
      "/studies/{studyUID}[/series/{seriesUID}[/instances/{instanceUID}]]" The
      Cloud Healthcare API service account must have the
      `roles/storage.objectViewer` Cloud IAM role for this Cloud Storage
      location.
  """

  resourcePathsGcsUri = _messages.StringField(1)


class DicomNotificationConfig(_messages.Message):
  r"""Contains the configuration for DICOM notifications.

  Fields:
    pubsubTopic: Required. The
      [Pub/Sub](https://cloud.google.com/pubsub/docs/) topic that
      notifications of changes are published on. Supplied by the client. The
      notification is a `PubsubMessage` with the following fields: *
      `PubsubMessage.Data` contains the resource name. *
      `PubsubMessage.MessageId` is the ID of this notification. It is
      guaranteed to be unique within the topic. * `PubsubMessage.PublishTime`
      is the time when the message was published. * `PubsubMessage.Attributes`
      contains the following attributes: * `action`: The name of the endpoint
      that generated the notification. Possible values are `StoreInstances`,
      `SetBlobSettings`, `ImportDicomData`, etc. * `lastUpdatedTime`: The
      latest timestamp when the DICOM instance was updated. * `storeName`: The
      resource name of the DICOM store, of the form `projects/{project_id}/loc
      ations/{location_id}/datasets/{dataset_id}/dicomStores/{dicom_store_id}`
      . * `studyInstanceUID`: The study UID of the DICOM instance that was
      changed. * `seriesInstanceUID`: The series UID of the DICOM instance
      that was changed. * `sopInstanceUID`: The instance UID of the DICOM
      instance that was changed. * `versionId`: The version ID of the DICOM
      instance that was changed. * `modality`: The modality tag of the DICOM
      instance that was changed. * `previousStorageClass`: The storage class
      where the DICOM instance was previously stored if the storage class was
      changed. * `storageClass`: The storage class where the DICOM instance is
      currently stored. Note that notifications are only sent if the topic is
      non-empty. [Topic
      names](https://cloud.google.com/pubsub/docs/overview#names) must be
      scoped to a project. The Cloud Healthcare API service account,
      <EMAIL>, must have the
      `pubsub.topics.publish` permission (which is typically included in
      `roles/pubsub.publisher` role) on the given Pub/Sub topic. Not having
      adequate permissions causes the calls that send notifications to fail
      (https://cloud.google.com/healthcare-api/docs/permissions-healthcare-
      api-gcp-products#dicom_fhir_and_hl7v2_store_cloud_pubsub_permissions).
      If a notification can't be published to Pub/Sub, errors are logged to
      Cloud Logging. For more information, see [Viewing error logs in Cloud
      Logging](https://cloud.google.com/healthcare-api/docs/how-tos/logging).
  """

  pubsubTopic = _messages.StringField(1)


class DicomStore(_messages.Message):
  r"""Represents a DICOM store.

  Messages:
    LabelsValue: User-supplied key-value pairs used to organize DICOM stores.
      Label keys must be between 1 and 63 characters long, have a UTF-8
      encoding of maximum 128 bytes, and must conform to the following PCRE
      regular expression: \p{Ll}\p{Lo}{0,62} Label values are optional, must
      be between 1 and 63 characters long, have a UTF-8 encoding of maximum
      128 bytes, and must conform to the following PCRE regular expression:
      [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated
      with a given store.

  Fields:
    labels: User-supplied key-value pairs used to organize DICOM stores. Label
      keys must be between 1 and 63 characters long, have a UTF-8 encoding of
      maximum 128 bytes, and must conform to the following PCRE regular
      expression: \p{Ll}\p{Lo}{0,62} Label values are optional, must be
      between 1 and 63 characters long, have a UTF-8 encoding of maximum 128
      bytes, and must conform to the following PCRE regular expression:
      [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated
      with a given store.
    name: Identifier. Resource name of the DICOM store, of the form `projects/
      {project_id}/locations/{location_id}/datasets/{dataset_id}/dicomStores/{
      dicom_store_id}`.
    notificationConfig: Notification destination for new DICOM instances.
      Supplied by the client.
    notificationConfigs: Optional. Specifies where and whether to send
      notifications upon changes to a DICOM store.
    streamConfigs: Optional. A list of streaming configs used to configure the
      destination of streaming exports for every DICOM instance insertion in
      this DICOM store. After a new config is added to `stream_configs`, DICOM
      instance insertions are streamed to the new destination. When a config
      is removed from `stream_configs`, the server stops streaming to that
      destination. Each config must contain a unique destination.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""User-supplied key-value pairs used to organize DICOM stores. Label
    keys must be between 1 and 63 characters long, have a UTF-8 encoding of
    maximum 128 bytes, and must conform to the following PCRE regular
    expression: \p{Ll}\p{Lo}{0,62} Label values are optional, must be between
    1 and 63 characters long, have a UTF-8 encoding of maximum 128 bytes, and
    must conform to the following PCRE regular expression:
    [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated with
    a given store.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)
  name = _messages.StringField(2)
  notificationConfig = _messages.MessageField('NotificationConfig', 3)
  notificationConfigs = _messages.MessageField('DicomNotificationConfig', 4, repeated=True)
  streamConfigs = _messages.MessageField('GoogleCloudHealthcareV1alpha2DicomStreamConfig', 5, repeated=True)


class DicomStoreMetrics(_messages.Message):
  r"""DicomStoreMetrics contains metrics describing a DICOM store.

  Fields:
    blobStorageSizeBytes: Total blob storage bytes for all instances in the
      store.
    instanceCount: Number of instances in the store.
    name: Resource name of the DICOM store, of the form `projects/{project_id}
      /locations/{location_id}/datasets/{dataset_id}/dicomStores/{dicom_store_
      id}`.
    seriesCount: Number of series in the store.
    structuredStorageSizeBytes: Total structured storage bytes for all
      instances in the store.
    studyCount: Number of studies in the store.
  """

  blobStorageSizeBytes = _messages.IntegerField(1)
  instanceCount = _messages.IntegerField(2)
  name = _messages.StringField(3)
  seriesCount = _messages.IntegerField(4)
  structuredStorageSizeBytes = _messages.IntegerField(5)
  studyCount = _messages.IntegerField(6)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EncryptionSpec(_messages.Message):
  r"""Represents a customer-managed encryption key spec that can be applied to
  a resource.

  Fields:
    kmsKeyName: Required. The resource name of customer-managed encryption key
      that is used to secure a resource and its sub-resources. Only the key in
      the same location as this Dataset is allowed to be used for encryption.
      Format is: `projects/{project}/locations/{location}/keyRings/{keyRing}/c
      ryptoKeys/{key}`
  """

  kmsKeyName = _messages.StringField(1)


class Entity(_messages.Message):
  r"""The candidate entities that an entity mention could link to.

  Fields:
    entityId: entity_id is a top level field entity_id uniquely identifies
      this concept and its meta-vocabulary. For example, "UMLS/C0000970".
    preferredTerm: preferred_term is the preferred term for this concept. For
      example, "Acetaminophen". For ad hoc entities formed by normalization,
      this is the most popular unnormalized string.
    vocabularyCodes: Vocabulary codes are top-level fields and differentiated
      from the concept unique identifier (entity_id). vocabulary_codes
      contains the representation of this concept in particular vocabularies,
      such as ICD-10, SNOMED-CT and RxNORM. These are prefixed by the name of
      the vocabulary, followed by the unique code within that vocabulary. For
      example, "RXNORM/A10334543".
  """

  entityId = _messages.StringField(1)
  preferredTerm = _messages.StringField(2)
  vocabularyCodes = _messages.StringField(3, repeated=True)


class EntityMention(_messages.Message):
  r"""An entity mention in the document.

  Fields:
    certaintyAssessment: The certainty assessment of the entity mention. Its
      value is one of: LIKELY, SOMEWHAT_LIKELY, UNCERTAIN, SOMEWHAT_UNLIKELY,
      UNLIKELY, CONDITIONAL
    confidence: The model's confidence in this entity mention annotation. A
      number between 0 and 1.
    linkedEntities: linked_entities are candidate ontological concepts that
      this entity mention may refer to. They are sorted by decreasing
      confidence.
    mentionId: mention_id uniquely identifies each entity mention in a single
      response.
    subject: The subject this entity mention relates to. Its value is one of:
      PATIENT, FAMILY_MEMBER, OTHER
    temporalAssessment: How this entity mention relates to the subject
      temporally. Its value is one of: CURRENT, CLINICAL_HISTORY,
      FAMILY_HISTORY, UPCOMING, ALLERGY
    text: text is the location of the entity mention in the document.
    type: The semantic type of the entity: UNKNOWN_ENTITY_TYPE, ALONE,
      ANATOMICAL_STRUCTURE, ASSISTED_LIVING, BF_RESULT, BM_RESULT, BM_UNIT,
      BM_VALUE, BODY_FUNCTION, BODY_MEASUREMENT, COMPLIANT, DOESNOT_FOLLOWUP,
      FAMILY, FOLLOWSUP, LABORATORY_DATA, LAB_RESULT, LAB_UNIT, LAB_VALUE,
      MEDICAL_DEVICE, MEDICINE, MED_DOSE, MED_DURATION, MED_FORM,
      MED_FREQUENCY, MED_ROUTE, MED_STATUS, MED_STRENGTH, MED_TOTALDOSE,
      MED_UNIT, NON_COMPLIANT, OTHER_LIVINGSTATUS, PROBLEM, PROCEDURE,
      PROCEDURE_RESULT, PROC_METHOD, REASON_FOR_NONCOMPLIANCE, SEVERITY,
      SUBSTANCE_ABUSE, UNCLEAR_FOLLOWUP.
  """

  certaintyAssessment = _messages.MessageField('Feature', 1)
  confidence = _messages.FloatField(2)
  linkedEntities = _messages.MessageField('LinkedEntity', 3, repeated=True)
  mentionId = _messages.StringField(4)
  subject = _messages.MessageField('Feature', 5)
  temporalAssessment = _messages.MessageField('Feature', 6)
  text = _messages.MessageField('TextSpan', 7)
  type = _messages.StringField(8)


class EntityMentionRelationship(_messages.Message):
  r"""Defines directed relationship from one entity mention to another.

  Fields:
    confidence: The model's confidence in this annotation. A number between 0
      and 1.
    objectId: object_id is the id of the object entity mention.
    subjectId: subject_id is the id of the subject entity mention.
  """

  confidence = _messages.FloatField(1)
  objectId = _messages.StringField(2)
  subjectId = _messages.StringField(3)


class EvaluateUserConsentsRequest(_messages.Message):
  r"""Evaluate a user's Consents for all matching User data mappings. Note:
  User data mappings are indexed asynchronously, causing slight delays between
  the time mappings are created or updated and when they are included in
  EvaluateUserConsents results.

  Enums:
    ResponseViewValueValuesEnum: Optional. The view for
      EvaluateUserConsentsResponse. If unspecified, defaults to `BASIC` and
      returns `consented` as `TRUE` or `FALSE`.

  Messages:
    RequestAttributesValue: Required. The values of request attributes
      associated with this access request.
    ResourceAttributesValue: Optional. The values of resource attributes
      associated with the resources being requested. If no values are
      specified, then all resources are queried.

  Fields:
    consentList: Optional. Specific Consents to evaluate the access request
      against. These Consents must have the same `user_id` as the User data
      mappings being evalauted, must exist in the current `consent_store`, and
      must have a `state` of either `ACTIVE` or `DRAFT`. A maximum of 100
      Consents can be provided here. If unspecified, all `ACTIVE` unexpired
      Consents in the current `consent_store` will be evaluated.
    pageSize: Optional. Limit on the number of User data mappings to return in
      a single response. If not specified, 100 is used. May not be larger than
      1000.
    pageToken: Optional. Token to retrieve the next page of results, or empty
      to get the first page.
    requestAttributes: Required. The values of request attributes associated
      with this access request.
    resourceAttributes: Optional. The values of resource attributes associated
      with the resources being requested. If no values are specified, then all
      resources are queried.
    responseView: Optional. The view for EvaluateUserConsentsResponse. If
      unspecified, defaults to `BASIC` and returns `consented` as `TRUE` or
      `FALSE`.
    userId: Required. User ID to evaluate consents for.
  """

  class ResponseViewValueValuesEnum(_messages.Enum):
    r"""Optional. The view for EvaluateUserConsentsResponse. If unspecified,
    defaults to `BASIC` and returns `consented` as `TRUE` or `FALSE`.

    Values:
      RESPONSE_VIEW_UNSPECIFIED: No response view specified. The API will
        default to the BASIC view.
      BASIC: Only the `data_id` and `consented` fields are populated in the
        response.
      FULL: All fields within the response are populated. When set to `FULL`,
        all `ACTIVE` Consents are evaluated even if a matching policy is found
        during evaluation.
    """
    RESPONSE_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class RequestAttributesValue(_messages.Message):
    r"""Required. The values of request attributes associated with this access
    request.

    Messages:
      AdditionalProperty: An additional property for a RequestAttributesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        RequestAttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a RequestAttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourceAttributesValue(_messages.Message):
    r"""Optional. The values of resource attributes associated with the
    resources being requested. If no values are specified, then all resources
    are queried.

    Messages:
      AdditionalProperty: An additional property for a ResourceAttributesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        ResourceAttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourceAttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  consentList = _messages.MessageField('ConsentList', 1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  requestAttributes = _messages.MessageField('RequestAttributesValue', 4)
  resourceAttributes = _messages.MessageField('ResourceAttributesValue', 5)
  responseView = _messages.EnumField('ResponseViewValueValuesEnum', 6)
  userId = _messages.StringField(7)


class EvaluateUserConsentsResponse(_messages.Message):
  r"""A EvaluateUserConsentsResponse object.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list. This token is valid for 72 hours
      after it is created.
    results: The consent evaluation result for each `data_id`.
  """

  nextPageToken = _messages.StringField(1)
  results = _messages.MessageField('Result', 2, repeated=True)


class ExportDicomDataRequest(_messages.Message):
  r"""Exports data from the specified DICOM store. If a given resource, such
  as a DICOM object with the same SOPInstance UID, already exists in the
  output, it is overwritten with the version in the source dataset. Exported
  DICOM data persists when the DICOM store from which it was exported is
  deleted.

  Fields:
    bigqueryDestination: The BigQuery output destination. You can only export
      to a BigQuery dataset that's in the same project as the DICOM store
      you're exporting from. The Cloud Healthcare Service Agent requires two
      IAM roles on the BigQuery location: `roles/bigquery.dataEditor` and
      `roles/bigquery.jobUser`.
    filterConfig: Specifies the filter configuration.
    gcsDestination: The Cloud Storage output destination. The Cloud Healthcare
      Service Agent requires the `roles/storage.objectAdmin` Cloud IAM roles
      on the Cloud Storage location.
  """

  bigqueryDestination = _messages.MessageField('GoogleCloudHealthcareV1alpha2DicomBigQueryDestination', 1)
  filterConfig = _messages.MessageField('DicomFilterConfig', 2)
  gcsDestination = _messages.MessageField('GoogleCloudHealthcareV1alpha2DicomGcsDestination', 3)


class ExportDicomDataResponse(_messages.Message):
  r"""Returns additional information in regards to a completed DICOM store
  export.
  """



class ExportMessagesRequest(_messages.Message):
  r"""Request to schedule an export.

  Fields:
    endTime: The end of the range in `send_time` (MSH.7, https://www.hl7.org/d
      ocumentcenter/public_temp_2E58C1F9-1C23-BA17-
      0C6126475344DA9D/wg/conf/HL7MSH.htm) to process. If not specified, the
      time when the export is scheduled is used. This value has to be after
      the `start_time` defined above. Only messages whose `send_times` lie in
      the range defined by this value and the `start_time` above are exported.
    gcsDestination: A GoogleCloudHealthcareV1alpha2Hl7v2GcsDestination
      attribute.
    startTime: The start of the range in `send_time` (MSH.7, https://www.hl7.o
      rg/documentcenter/public_temp_2E58C1F9-1C23-BA17-
      0C6126475344DA9D/wg/conf/HL7MSH.htm) to process. If not specified, the
      UNIX epoch (1970-01-01T00:00:00Z) is used. This value has to come before
      the `end_time` defined below. Only messages whose `send_times` lie in
      the range defined by this value and `end_time` are exported.
  """

  endTime = _messages.StringField(1)
  gcsDestination = _messages.MessageField('GoogleCloudHealthcareV1alpha2Hl7v2GcsDestination', 2)
  startTime = _messages.StringField(3)


class ExportMessagesResponse(_messages.Message):
  r"""Final response for the export operation. This structure is included in
  the response to describe the detailed outcome.
  """



class ExportResourcesRequest(_messages.Message):
  r"""Request to export resources.

  Fields:
    _since: If provided, only resources updated after this time are exported.
      The time uses the format YYYY-MM-DDThh:mm:ss.sss+zz:zz. For example,
      `2015-02-07T13:28:17.239+02:00` or `2017-01-01T00:00:00Z`. The time must
      be specified to the second and include a time zone.
    _type: String of comma-delimited FHIR resource types. If provided, only
      resources of the specified resource type(s) are exported.
    bigqueryDestination: The BigQuery output destination. The Cloud Healthcare
      Service Agent requires two IAM roles on the BigQuery location:
      `roles/bigquery.dataEditor` and `roles/bigquery.jobUser`. The output is
      one BigQuery table per resource type. Unlike when setting
      `BigQueryDestination` for `StreamConfig`, `ExportResources` does not
      create BigQuery views.
    gcsDestination: The Cloud Storage output destination. The Healthcare
      Service Agent account requires the `roles/storage.objectAdmin` role on
      the Cloud Storage location. The exported outputs are organized by FHIR
      resource types. The server creates one or more objects per resource type
      depending on the volume of the resources exported. When there is only
      one object per resource type, the object name is in the form of
      `{operation_id}_{resource_type}`. When there are multiple objects for a
      given resource type, the object names are in the form of
      `{operation_id}_{resource_type}-{index}-of-{total}`. Each object
      contains newline delimited JSON, and each line is a FHIR resource.
  """

  _since = _messages.StringField(1)
  _type = _messages.StringField(2)
  bigqueryDestination = _messages.MessageField('GoogleCloudHealthcareV1alpha2FhirBigQueryDestination', 3)
  gcsDestination = _messages.MessageField('GoogleCloudHealthcareV1alpha2FhirGcsDestination', 4)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class Feature(_messages.Message):
  r"""A feature of an entity mention.

  Fields:
    confidence: The model's confidence in this feature annotation. A number
      between 0 and 1.
    value: The value of this feature annotation. Its range depends on the type
      of the feature.
  """

  confidence = _messages.FloatField(1)
  value = _messages.StringField(2)


class FhirConfig(_messages.Message):
  r"""Specifies how to handle the de-identification of a FHIR store.

  Fields:
    fieldMetadataList: Specifies FHIR paths to match and how to transform
      them. Any field that is not matched by a FieldMetadata is passed through
      to the output dataset unmodified. All extensions will be processed
      according to `default_keep_extensions`. If a field can be matched by
      more than one FieldMetadata, the first FieldMetadata.Action is applied.
  """

  fieldMetadataList = _messages.MessageField('FieldMetadata', 1, repeated=True)


class FhirStore(_messages.Message):
  r"""Represents a FHIR store.

  Enums:
    VersionValueValuesEnum: Required. Immutable. The FHIR specification
      version that this FHIR store supports natively. This field is immutable
      after store creation. Requests are rejected if they contain FHIR
      resources of a different version. Version is required for every FHIR
      store.

  Messages:
    LabelsValue: User-supplied key-value pairs used to organize FHIR stores.
      Label keys must be between 1 and 63 characters long, have a UTF-8
      encoding of maximum 128 bytes, and must conform to the following PCRE
      regular expression: \p{Ll}\p{Lo}{0,62} Label values are optional, must
      be between 1 and 63 characters long, have a UTF-8 encoding of maximum
      128 bytes, and must conform to the following PCRE regular expression:
      [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated
      with a given store.

  Fields:
    disableReferentialIntegrity: Immutable. Whether to disable referential
      integrity in this FHIR store. This field is immutable after FHIR store
      creation. The default value is false, meaning that the API enforces
      referential integrity and fail the requests that result in inconsistent
      state in the FHIR store. When this field is set to true, the API skips
      referential integrity checks. Consequently, operations that rely on
      references, such as Patient-everything, do not return all the results if
      broken references exist.
    disableResourceVersioning: Immutable. Whether to disable resource
      versioning for this FHIR store. This field can not be changed after the
      creation of FHIR store. If set to false, all write operations cause
      historical versions to be recorded automatically. The historical
      versions can be fetched through the history APIs, but cannot be updated.
      If set to true, no historical versions are kept. The server sends errors
      for attempts to read the historical versions. Defaults to false.
    enableHistoryModifications: Optional. Whether to allow ExecuteBundle to
      accept history bundles, and directly insert and overwrite historical
      resource versions into the FHIR store. If set to false, using history
      bundles fails with an error. Defaults to false.
    enableUpdateCreate: Whether this FHIR store has the [updateCreate
      capability](https://www.hl7.org/fhir/capabilitystatement-
      definitions.html#CapabilityStatement.rest.resource.updateCreate). This
      determines if the client can use an Update operation to create a new
      resource with a client-specified ID. If false, all IDs are server-
      assigned through the Create operation and attempts to update a non-
      existent resource return errors. It is strongly advised not to include
      or encode any sensitive data such as patient identifiers in client-
      specified resource IDs. Those IDs are part of the FHIR resource path
      recorded in Cloud audit logs and Pub/Sub notifications. Those IDs can
      also be contained in reference fields within other resources. Defaults
      to false.
    labels: User-supplied key-value pairs used to organize FHIR stores. Label
      keys must be between 1 and 63 characters long, have a UTF-8 encoding of
      maximum 128 bytes, and must conform to the following PCRE regular
      expression: \p{Ll}\p{Lo}{0,62} Label values are optional, must be
      between 1 and 63 characters long, have a UTF-8 encoding of maximum 128
      bytes, and must conform to the following PCRE regular expression:
      [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated
      with a given store.
    name: Output only. Identifier. Resource name of the FHIR store, of the
      form `projects/{project_id}/locations/{location}/datasets/{dataset_id}/f
      hirStores/{fhir_store_id}`.
    notificationConfig: Deprecated. Use `notification_configs` instead. If
      non-empty, publish all resource modifications of this FHIR store to this
      destination. The Pub/Sub message attributes contain a map with a string
      describing the action that has triggered the notification. For example,
      "action":"CreateResource". Not supported in R5. Use
      `notification_configs` instead.
    streamConfigs: A list of streaming configs that configure the destinations
      of streaming export for every resource mutation in this FHIR store. Each
      store is allowed to have up to 10 streaming configs. After a new config
      is added, the next resource mutation is streamed to the new location in
      addition to the existing ones. When a location is removed from the list,
      the server stops streaming to that location. Some lag (typically on the
      order of dozens of seconds) is expected before the results show up in
      the streaming destination.
    version: Required. Immutable. The FHIR specification version that this
      FHIR store supports natively. This field is immutable after store
      creation. Requests are rejected if they contain FHIR resources of a
      different version. Version is required for every FHIR store.
  """

  class VersionValueValuesEnum(_messages.Enum):
    r"""Required. Immutable. The FHIR specification version that this FHIR
    store supports natively. This field is immutable after store creation.
    Requests are rejected if they contain FHIR resources of a different
    version. Version is required for every FHIR store.

    Values:
      VERSION_UNSPECIFIED: VERSION_UNSPECIFIED is treated as STU3 to
        accommodate the existing FHIR stores.
      DSTU2: Draft Standard for Trial Use, [Release
        2](https://www.hl7.org/fhir/DSTU2)
      STU3: Standard for Trial Use, [Release 3](https://www.hl7.org/fhir/STU3)
      R4: [Release 4](https://www.hl7.org/fhir/R4)
    """
    VERSION_UNSPECIFIED = 0
    DSTU2 = 1
    STU3 = 2
    R4 = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""User-supplied key-value pairs used to organize FHIR stores. Label keys
    must be between 1 and 63 characters long, have a UTF-8 encoding of maximum
    128 bytes, and must conform to the following PCRE regular expression:
    \p{Ll}\p{Lo}{0,62} Label values are optional, must be between 1 and 63
    characters long, have a UTF-8 encoding of maximum 128 bytes, and must
    conform to the following PCRE regular expression:
    [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated with
    a given store.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  disableReferentialIntegrity = _messages.BooleanField(1)
  disableResourceVersioning = _messages.BooleanField(2)
  enableHistoryModifications = _messages.BooleanField(3)
  enableUpdateCreate = _messages.BooleanField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  notificationConfig = _messages.MessageField('NotificationConfig', 7)
  streamConfigs = _messages.MessageField('StreamConfig', 8, repeated=True)
  version = _messages.EnumField('VersionValueValuesEnum', 9)


class FhirStoreMetric(_messages.Message):
  r"""Count of resources and total storage size by type for a given FHIR
  store.

  Fields:
    count: The total count of FHIR resources in the store of this resource
      type.
    resourceType: The FHIR resource type this metric applies to.
    structuredStorageSizeBytes: The total amount of structured storage used by
      FHIR resources of this resource type in the store.
  """

  count = _messages.IntegerField(1)
  resourceType = _messages.StringField(2)
  structuredStorageSizeBytes = _messages.IntegerField(3)


class FhirStoreMetrics(_messages.Message):
  r"""List of metrics for a given FHIR store.

  Fields:
    metrics: List of FhirStoreMetric by resource type.
    name: The resource name of the FHIR store to get metrics for, in the
      format `projects/{project_id}/datasets/{dataset_id}/fhirStores/{fhir_sto
      re_id}`.
  """

  metrics = _messages.MessageField('FhirStoreMetric', 1, repeated=True)
  name = _messages.StringField(2)


class FieldMetadata(_messages.Message):
  r"""Specifies FHIR paths to match and how to handle the de-identification of
  matching fields.

  Enums:
    ActionValueValuesEnum: Deidentify action for one field.

  Fields:
    action: Deidentify action for one field.
    paths: List of paths to FHIR fields to redact. Each path is a period-
      separated list where each component is either a field name or FHIR type
      name. All types begin with an upper case letter. For example, the
      resource field "Patient.Address.city", which uses a string type, can be
      matched by "Patient.Address.String". Path also supports partial
      matching. For example, "Patient.Address.city" can be matched by
      "Address.city" (Patient omitted). Partial matching and type matching can
      be combined, for example "Patient.Address.city" can be matched by
      "Address.String". For "choice" types (those defined in the FHIR spec
      with the form: field[x]), use two separate components. For example,
      "deceasedAge.unit" is matched by "Deceased.Age.unit". Supported types
      are: AdministrativeGenderCode, Base64Binary, Boolean, Code, Date,
      DateTime, Decimal, HumanName, Id, Instant, Integer, LanguageCode,
      Markdown, Oid, PositiveInt, String, UnsignedInt, Uri, Uuid, Xhtml. The
      sub-type for HumanName(for example HumanName.given, HumanName.family)
      can be omitted.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Deidentify action for one field.

    Values:
      ACTION_UNSPECIFIED: No action specified.
      TRANSFORM: Transform the entire field based on transformations specified
        in TextConfig. When the specified transformation cannot be applied to
        a field (for example, a Crypto Hash transformation cannot be applied
        to a FHIR Date field), RedactConfig is used.
      INSPECT_AND_TRANSFORM: Inspect and transform any found PHI.
      DO_NOT_TRANSFORM: Do not transform.
    """
    ACTION_UNSPECIFIED = 0
    TRANSFORM = 1
    INSPECT_AND_TRANSFORM = 2
    DO_NOT_TRANSFORM = 3

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  paths = _messages.StringField(2, repeated=True)


class GoogleCloudHealthcareV1alpha2ConsentGcsDestination(_messages.Message):
  r"""The Cloud Storage location for export.

  Fields:
    uriPrefix: URI for a Cloud Storage directory where the server writes
      result files, in the format `gs://{bucket-
      id}/{path/to/destination/dir}`. If there is no trailing slash, the
      service appends one when composing the object path. The user is
      responsible for creating the Cloud Storage bucket and directory
      referenced in `uri_prefix`.
  """

  uriPrefix = _messages.StringField(1)


class GoogleCloudHealthcareV1alpha2ConsentPolicy(_messages.Message):
  r"""Represents a user's consent in terms of the resources that can be
  accessed and under what conditions.

  Fields:
    authorizationRule: Required. The request conditions to meet to grant
      access. In addition to any supported comparison operators, authorization
      rules may have `IN` operator as well as at most 10 logical operators
      that are limited to `AND` (`&&`), `OR` (`||`).
    resourceAttributes: The resources that this policy applies to. A resource
      is a match if it matches all the attributes listed here. If empty, this
      policy applies to all User data mappings for the given user.
  """

  authorizationRule = _messages.MessageField('Expr', 1)
  resourceAttributes = _messages.MessageField('Attribute', 2, repeated=True)


class GoogleCloudHealthcareV1alpha2DeidentifyDeidentifyDicomStoreSummary(_messages.Message):
  r"""Contains a summary of the DeidentifyDicomStore operation."""


class GoogleCloudHealthcareV1alpha2DeidentifyDeidentifyFhirStoreSummary(_messages.Message):
  r"""Contains a summary of the DeidentifyFhirStore operation."""


class GoogleCloudHealthcareV1alpha2DicomBigQueryDestination(_messages.Message):
  r"""The BigQuery table where the server writes output.

  Enums:
    WriteDispositionValueValuesEnum: Determines whether the existing table in
      the destination is to be overwritten or appended to. If a
      write_disposition is specified, the `force` parameter is ignored.

  Fields:
    force: Use `write_disposition` instead. If `write_disposition` is
      specified, this parameter is ignored. force=false is equivalent to
      write_disposition=WRITE_EMPTY and force=true is equivalent to
      write_disposition=WRITE_TRUNCATE.
    tableUri: BigQuery URI to a table, up to 2000 characters long, in the
      format `bq://projectId.bqDatasetId.tableId`
    writeDisposition: Determines whether the existing table in the destination
      is to be overwritten or appended to. If a write_disposition is
      specified, the `force` parameter is ignored.
  """

  class WriteDispositionValueValuesEnum(_messages.Enum):
    r"""Determines whether the existing table in the destination is to be
    overwritten or appended to. If a write_disposition is specified, the
    `force` parameter is ignored.

    Values:
      WRITE_DISPOSITION_UNSPECIFIED: Default behavior is the same as
        WRITE_EMPTY.
      WRITE_EMPTY: Only export data if the destination table is empty.
      WRITE_TRUNCATE: Erase all existing data in the destination table before
        writing the instances.
      WRITE_APPEND: Append data to the destination table.
    """
    WRITE_DISPOSITION_UNSPECIFIED = 0
    WRITE_EMPTY = 1
    WRITE_TRUNCATE = 2
    WRITE_APPEND = 3

  force = _messages.BooleanField(1)
  tableUri = _messages.StringField(2)
  writeDisposition = _messages.EnumField('WriteDispositionValueValuesEnum', 3)


class GoogleCloudHealthcareV1alpha2DicomGcsDestination(_messages.Message):
  r"""The Cloud Storage location where the server writes the output and the
  export configuration.

  Fields:
    mimeType: MIME types supported by DICOM spec. Each file is written in the
      following format:
      `.../{study_id}/{series_id}/{instance_id}[/{frame_number}].{extension}`
      The frame_number component exists only for multi-frame instances.
      Supported MIME types are consistent with supported formats in DICOMweb:
      https://cloud.google.com/healthcare/docs/dicom#retrieve_transaction.
      Specifically, the following are supported: - application/dicom;
      transfer-syntax=1.2.840.10008.1.2.1 (uncompressed DICOM) -
      application/dicom; transfer-syntax=1.2.840.10008.******** (DICOM with
      embedded JPEG Baseline) - application/dicom; transfer-
      syntax=1.2.840.10008.1.2.4.90 (DICOM with embedded JPEG 2000 Lossless
      Only) - application/dicom; transfer-syntax=1.2.840.10008.1.2.4.91 (DICOM
      with embedded JPEG 2000) - application/dicom; transfer-syntax=* (DICOM
      with no transcoding) - application/octet-stream; transfer-
      syntax=1.2.840.10008.1.2.1 (raw uncompressed PixelData) -
      application/octet-stream; transfer-syntax=* (raw PixelData in whatever
      format it was uploaded in) - image/jpeg; transfer-
      syntax=1.2.840.10008.******** (Consumer JPEG) - image/png The following
      extensions are used for output files: - application/dicom -> .dcm -
      image/jpeg -> .jpg - image/png -> .png - application/octet-stream -> no
      extension If unspecified, the instances are exported in the original
      DICOM format they were uploaded in.
    uriPrefix: The Cloud Storage destination to export to. URI for a Cloud
      Storage directory where the server writes the result files, in the
      format `gs://{bucket-id}/{path/to/destination/dir}`). If there is no
      trailing slash, the service appends one when composing the object path.
      The user is responsible for creating the Cloud Storage bucket referenced
      in `uri_prefix`.
  """

  mimeType = _messages.StringField(1)
  uriPrefix = _messages.StringField(2)


class GoogleCloudHealthcareV1alpha2DicomGcsSource(_messages.Message):
  r"""Specifies the configuration for importing data from Cloud Storage.

  Fields:
    uri: Points to a Cloud Storage URI containing file(s) with content only.
      The URI must be in the following format: `gs://{bucket_id}/{object_id}`.
      The URI can include wildcards in `object_id` and thus identify multiple
      files. Supported wildcards: * '*' to match 0 or more non-separator
      characters * '**' to match 0 or more characters (including separators).
      Must be used at the end of a path and with no other wildcards in the
      path. Can also be used with a file extension (such as .dcm), which
      imports all files with the extension in the specified directory and its
      sub-directories. For example, `gs://my-bucket/my-directory/**.dcm`
      imports all files with .dcm extensions in `my-directory/` and its sub-
      directories. * '?' to match 1 character. All other URI formats are
      invalid. Files matching the wildcard are expected to contain content
      only, no metadata.
  """

  uri = _messages.StringField(1)


class GoogleCloudHealthcareV1alpha2DicomStreamConfig(_messages.Message):
  r"""StreamConfig specifies configuration for a streaming DICOM export.

  Fields:
    bigqueryDestination: Results are appended to this table. The server
      creates a new table in the given BigQuery dataset if the specified table
      does not exist. To enable the Cloud Healthcare API to write to your
      BigQuery table, you must give the Cloud Healthcare API service account
      the bigquery.dataEditor role. The service account is:
      `service-{PROJECT_NUMBER}@gcp-sa-healthcare.iam.gserviceaccount.com`.
      The PROJECT_NUMBER identifies the project that the DICOM store resides
      in. To get the project number, go to the Cloud Console Dashboard. It is
      recommended to not have a custom schema in the destination table which
      could conflict with the schema created by the Cloud Healthcare API.
      Instance deletions are not applied to the destination table.
  """

  bigqueryDestination = _messages.MessageField('GoogleCloudHealthcareV1alpha2DicomBigQueryDestination', 1)


class GoogleCloudHealthcareV1alpha2FhirBigQueryDestination(_messages.Message):
  r"""The configuration for exporting to BigQuery.

  Enums:
    WriteDispositionValueValuesEnum: Determines if existing data in the
      destination dataset is overwritten, appended to, or not written if the
      tables contain data. If a write_disposition is specified, the `force`
      parameter is ignored.

  Fields:
    changeDataCaptureConfig: Optional. Setting this field will enable
      BigQuery's Change Data Capture (CDC) on the destination tables. Use this
      field if you: - Want to only keep the latest version of each resource.
      Updates and deletes to an existing resource will overwrite the
      corresponding row. - Have a store with enabled history modifications and
      want to keep the entire history of resource versions but want the
      history to be mutable. Updates and deletes to a specific resource
      version will overwrite the corresponding row. See
      https://cloud.google.com/bigquery/docs/change-data-capture for details.
    datasetUri: BigQuery URI to an existing dataset, up to 2000 characters
      long, in the format `bq://projectId.bqDatasetId`.
    force: Use `write_disposition` instead. If `write_disposition` is
      specified, this parameter is ignored. force=false is equivalent to
      write_disposition=WRITE_EMPTY and force=true is equivalent to
      write_disposition=WRITE_TRUNCATE.
    schemaConfig: The configuration for the exported BigQuery schema.
    writeDisposition: Determines if existing data in the destination dataset
      is overwritten, appended to, or not written if the tables contain data.
      If a write_disposition is specified, the `force` parameter is ignored.
  """

  class WriteDispositionValueValuesEnum(_messages.Enum):
    r"""Determines if existing data in the destination dataset is overwritten,
    appended to, or not written if the tables contain data. If a
    write_disposition is specified, the `force` parameter is ignored.

    Values:
      WRITE_DISPOSITION_UNSPECIFIED: Default behavior is the same as
        WRITE_EMPTY.
      WRITE_EMPTY: Only export data if the destination tables are empty.
      WRITE_TRUNCATE: Erase all existing data in the destination tables before
        writing the FHIR resources.
      WRITE_APPEND: Append data to the destination tables.
    """
    WRITE_DISPOSITION_UNSPECIFIED = 0
    WRITE_EMPTY = 1
    WRITE_TRUNCATE = 2
    WRITE_APPEND = 3

  changeDataCaptureConfig = _messages.MessageField('GoogleCloudHealthcareV1alpha2FhirChangeDataCaptureConfig', 1)
  datasetUri = _messages.StringField(2)
  force = _messages.BooleanField(3)
  schemaConfig = _messages.MessageField('SchemaConfig', 4)
  writeDisposition = _messages.EnumField('WriteDispositionValueValuesEnum', 5)


class GoogleCloudHealthcareV1alpha2FhirChangeDataCaptureConfig(_messages.Message):
  r"""BigQuery Change Data Capture configuration.

  Enums:
    HistoryModeValueValuesEnum: Optional. Configures how historical versions
      of FHIR resources will be reflected in the destination table through
      updates and deletes. Defaults to `HistoryMode.KEEP_LATEST_VERSION` if
      unspecified.

  Fields:
    historyMode: Optional. Configures how historical versions of FHIR
      resources will be reflected in the destination table through updates and
      deletes. Defaults to `HistoryMode.KEEP_LATEST_VERSION` if unspecified.
  """

  class HistoryModeValueValuesEnum(_messages.Enum):
    r"""Optional. Configures how historical versions of FHIR resources will be
    reflected in the destination table through updates and deletes. Defaults
    to `HistoryMode.KEEP_LATEST_VERSION` if unspecified.

    Values:
      HISTORY_MODE_UNSPECIFIED: Default behavior is the same as
        KEEP_LATEST_VERSION.
      KEEP_LATEST_VERSION: The table will have a unique entry for each
        resource ID. Updates and deletes will overwrite the row matching the
        resource ID if it exists in the table.
      KEEP_ALL_VERSIONS: Historical versions of resources will be maintained.
        However, history mutation is allowed. Updates will overwrite the row
        matching the resource ID and version if it exists in the table. This
        option is only supported for stores with history enabled.
    """
    HISTORY_MODE_UNSPECIFIED = 0
    KEEP_LATEST_VERSION = 1
    KEEP_ALL_VERSIONS = 2

  historyMode = _messages.EnumField('HistoryModeValueValuesEnum', 1)


class GoogleCloudHealthcareV1alpha2FhirExportResourcesResponse(_messages.Message):
  r"""Response when all resources export successfully. This structure is
  included in the response to describe the detailed outcome. It is only
  included when the operation finishes successfully.
  """



class GoogleCloudHealthcareV1alpha2FhirGcsDestination(_messages.Message):
  r"""The configuration for exporting to Cloud Storage.

  Fields:
    uriPrefix: URI for a Cloud Storage directory where the server writes
      result files, in the format `gs://{bucket-
      id}/{path/to/destination/dir}`. If there is no trailing slash, the
      service appends one when composing the object path. The Cloud Storage
      bucket referenced in `uri_prefix` must exist or an error occurs.
  """

  uriPrefix = _messages.StringField(1)


class GoogleCloudHealthcareV1alpha2FhirGcsErrorDestination(_messages.Message):
  r"""Specifies the Cloud Storage destination where errors are recorded.

  Fields:
    uriPrefix: URI for a Cloud Storage directory where the server writes error
      report files, in the format `gs://{bucket-
      id}/{path/to/destination/dir}`. If there is no trailing slash, the
      service appends one when composing the object path. The Cloud Storage
      bucket referenced in `uri_prefix` must exist or an error occurs.
  """

  uriPrefix = _messages.StringField(1)


class GoogleCloudHealthcareV1alpha2FhirGcsSource(_messages.Message):
  r"""Specifies the configuration for importing data from Cloud Storage.

  Fields:
    uri: Points to a Cloud Storage URI containing file(s) to import. The URI
      must be in the following format: `gs://{bucket_id}/{object_id}`. The URI
      can include wildcards in `object_id` and thus identify multiple files.
      Supported wildcards: * `*` to match 0 or more non-separator characters *
      `**` to match 0 or more characters (including separators). Must be used
      at the end of a path and with no other wildcards in the path. Can also
      be used with a file extension (such as .ndjson), which imports all files
      with the extension in the specified directory and its sub-directories.
      For example, `gs://my-bucket/my-directory/**.ndjson` imports all files
      with `.ndjson` extensions in `my-directory/` and its sub-directories. *
      `?` to match 1 character Files matching the wildcard are expected to
      contain content only, no metadata.
  """

  uri = _messages.StringField(1)


class GoogleCloudHealthcareV1alpha2FhirImportResourcesResponse(_messages.Message):
  r"""Final response of importing resources. This structure is included in the
  response to describe the detailed outcome. It is only included when the
  operation finishes successfully.
  """



class GoogleCloudHealthcareV1alpha2Hl7v2GcsDestination(_messages.Message):
  r"""The Cloud Storage output destination. The Cloud Healthcare Service Agent
  requires the `roles/storage.objectAdmin` Cloud IAM roles on the Cloud
  Storage location.

  Enums:
    ContentStructureValueValuesEnum: The format of the exported HL7v2 message
      files.
    MessageViewValueValuesEnum: Specifies the parts of the Message resource to
      include in the export. If not specified, FULL is used.

  Fields:
    contentStructure: The format of the exported HL7v2 message files.
    messageView: Specifies the parts of the Message resource to include in the
      export. If not specified, FULL is used.
    uriPrefix: URI for a Cloud Storage directory where the server writes
      result files, in the format `gs://{bucket-
      id}/{path/to/destination/dir}`. If there is no trailing slash, the
      service appends one when composing the object path. The user is
      responsible for creating the Cloud Storage bucket referenced in
      `uri_prefix`.
  """

  class ContentStructureValueValuesEnum(_messages.Enum):
    r"""The format of the exported HL7v2 message files.

    Values:
      CONTENT_STRUCTURE_UNSPECIFIED: If the content structure is not
        specified, the default value `MESSAGE_JSON` will be used.
      MESSAGE_JSON: Messages are printed using the JSON format returned from
        the `GetMessage` API. Messages are delimited with newlines.
    """
    CONTENT_STRUCTURE_UNSPECIFIED = 0
    MESSAGE_JSON = 1

  class MessageViewValueValuesEnum(_messages.Enum):
    r"""Specifies the parts of the Message resource to include in the export.
    If not specified, FULL is used.

    Values:
      MESSAGE_VIEW_UNSPECIFIED: Not specified, equivalent to FULL for
        getMessage, equivalent to BASIC for listMessages.
      RAW_ONLY: Server responses include all the message fields except
        parsed_data and schematized_data fields.
      PARSED_ONLY: Server responses include all the message fields except data
        and schematized_data fields.
      FULL: Server responses include all the message fields.
      SCHEMATIZED_ONLY: Server responses include all the message fields except
        data and parsed_data fields.
      BASIC: Server responses include only the name field.
    """
    MESSAGE_VIEW_UNSPECIFIED = 0
    RAW_ONLY = 1
    PARSED_ONLY = 2
    FULL = 3
    SCHEMATIZED_ONLY = 4
    BASIC = 5

  contentStructure = _messages.EnumField('ContentStructureValueValuesEnum', 1)
  messageView = _messages.EnumField('MessageViewValueValuesEnum', 2)
  uriPrefix = _messages.StringField(3)


class GoogleCloudHealthcareV1alpha2Hl7v2GcsSource(_messages.Message):
  r"""Specifies the configuration for importing data from Cloud Storage.

  Fields:
    uri: Points to a Cloud Storage URI containing file(s) to import. The URI
      must be in the following format: `gs://{bucket_id}/{object_id}`. The URI
      can include wildcards in `object_id` and thus identify multiple files.
      Supported wildcards: * `*` to match 0 or more non-separator characters *
      `**` to match 0 or more characters (including separators). Must be used
      at the end of a path and with no other wildcards in the path. Can also
      be used with a file extension (such as .ndjson), which imports all files
      with the extension in the specified directory and its sub-directories.
      For example, `gs://my-bucket/my-directory/**.ndjson` imports all files
      with `.ndjson` extensions in `my-directory/` and its sub-directories. *
      `?` to match 1 character Files matching the wildcard are expected to
      contain content only, no metadata.
  """

  uri = _messages.StringField(1)


class HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsCreateRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsCr
  eateRequest object.

  Fields:
    attributeDefinition: A AttributeDefinition resource to be passed as the
      request body.
    attributeDefinitionId: Required. The ID of the Attribute definition to
      create. The string must match the following regex: `_a-zA-Z{0,255}` and
      must not be a reserved keyword within the Common Expression Language as
      listed on https://github.com/google/cel-spec/blob/master/doc/langdef.md.
    parent: Required. The name of the consent store that this Attribute
      definition belongs to.
  """

  attributeDefinition = _messages.MessageField('AttributeDefinition', 1)
  attributeDefinitionId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsDeleteRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsDe
  leteRequest object.

  Fields:
    name: Required. The resource name of the Attribute definition to delete.
      To preserve referential integrity, Attribute definitions referenced by a
      User data mapping or the latest revision of a Consent cannot be deleted.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsGetRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsGe
  tRequest object.

  Fields:
    name: Required. The resource name of the Attribute definition to get.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsListRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsLi
  stRequest object.

  Fields:
    filter: Optional. Restricts the attributes returned to those matching a
      filter. The following syntax is available: * A string field value can be
      written as text inside quotation marks, for example `"query text"`. The
      only valid relational operation for text fields is equality (`=`), where
      text is searched within the field, rather than having the field be equal
      to the text. For example, `"Comment = great"` returns messages with
      `great` in the comment field. * A number field value can be written as
      an integer, a decimal, or an exponential. The valid relational operators
      for number fields are the equality operator (`=`), along with the less
      than/greater than operators (`<`, `<=`, `>`, `>=`). Note that there is
      no inequality (`!=`) operator. You can prepend the `NOT` operator to an
      expression to negate it. * A date field value must be written in `yyyy-
      mm-dd` form. Fields with date and time use the RFC3339 time format.
      Leading zeros are required for one-digit months and days. The valid
      relational operators for date fields are the equality operator (`=`) ,
      along with the less than/greater than operators (`<`, `<=`, `>`, `>=`).
      Note that there is no inequality (`!=`) operator. You can prepend the
      `NOT` operator to an expression to negate it. * Multiple field query
      expressions can be combined in one query by adding `AND` or `OR`
      operators between the expressions. If a boolean operator appears within
      a quoted string, it is not treated as special, it's just another part of
      the character string to be matched. You can prepend the `NOT` operator
      to an expression to negate it. The only field available for filtering is
      `category`. For example, `filter=category=\"REQUEST\"`.
    pageSize: Optional. Limit on the number of Attribute definitions to return
      in a single response. If not specified, 100 is used. May not be larger
      than 1000.
    pageToken: Optional. Token to retrieve the next page of results or empty
      to get the first page.
    parent: Required. Name of the consent store to retrieve Attribute
      definitions from.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsPatchRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsPa
  tchRequest object.

  Fields:
    attributeDefinition: A AttributeDefinition resource to be passed as the
      request body.
    name: Identifier. Resource name of the Attribute definition, of the form `
      projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/cons
      entStores/{consent_store_id}/attributeDefinitions/{attribute_definition_
      id}`. Cannot be changed after creation.
    updateMask: Required. The update mask that applies to the resource. For
      the `FieldMask` definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask. Only the
      `description`, `allowed_values`, `consent_default_values` and
      `data_mapping_default_value` fields can be updated. The updated
      `allowed_values` must contain all values from the previous
      `allowed_values`.
  """

  attributeDefinition = _messages.MessageField('AttributeDefinition', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class HealthcareProjectsLocationsDatasetsConsentStoresCheckDataAccessRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresCheckDataAccessRequest
  object.

  Fields:
    checkDataAccessRequest: A CheckDataAccessRequest resource to be passed as
      the request body.
    consentStore: Required. Name of the consent store where the requested
      data_id is stored, of the form `projects/{project_id}/locations/{locatio
      n_id}/datasets/{dataset_id}/consentStores/{consent_store_id}`.
  """

  checkDataAccessRequest = _messages.MessageField('CheckDataAccessRequest', 1)
  consentStore = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsCreateRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsCreate
  Request object.

  Fields:
    consentArtifact: A ConsentArtifact resource to be passed as the request
      body.
    parent: Required. The name of the consent store this Consent artifact
      belongs to.
  """

  consentArtifact = _messages.MessageField('ConsentArtifact', 1)
  parent = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsDeleteRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsDelete
  Request object.

  Fields:
    name: Required. The resource name of the Consent artifact to delete. To
      preserve referential integrity, Consent artifacts referenced by the
      latest revision of a Consent cannot be deleted.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsGetRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsGetRequest
  object.

  Fields:
    name: Required. The resource name of the Consent artifact to retrieve.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsListRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsListRequest
  object.

  Fields:
    filter: Optional. Restricts the artifacts returned to those matching a
      filter. The following syntax is available: * A string field value can be
      written as text inside quotation marks, for example `"query text"`. The
      only valid relational operation for text fields is equality (`=`), where
      text is searched within the field, rather than having the field be equal
      to the text. For example, `"Comment = great"` returns messages with
      `great` in the comment field. * A number field value can be written as
      an integer, a decimal, or an exponential. The valid relational operators
      for number fields are the equality operator (`=`), along with the less
      than/greater than operators (`<`, `<=`, `>`, `>=`). Note that there is
      no inequality (`!=`) operator. You can prepend the `NOT` operator to an
      expression to negate it. * A date field value must be written in `yyyy-
      mm-dd` form. Fields with date and time use the RFC3339 time format.
      Leading zeros are required for one-digit months and days. The valid
      relational operators for date fields are the equality operator (`=`) ,
      along with the less than/greater than operators (`<`, `<=`, `>`, `>=`).
      Note that there is no inequality (`!=`) operator. You can prepend the
      `NOT` operator to an expression to negate it. * Multiple field query
      expressions can be combined in one query by adding `AND` or `OR`
      operators between the expressions. If a boolean operator appears within
      a quoted string, it is not treated as special, it's just another part of
      the character string to be matched. You can prepend the `NOT` operator
      to an expression to negate it. The fields available for filtering are: -
      user_id. For example, `filter=user_id=\"user123\"`. -
      consent_content_version - metadata. For example,
      `filter=Metadata(\"testkey\")=\"value\"` or
      `filter=HasMetadata(\"testkey\")`.
    pageSize: Optional. Limit on the number of consent artifacts to return in
      a single response. If not specified, 100 is used. May not be larger than
      1000.
    pageToken: Optional. The next_page_token value returned from the previous
      List request, if any.
    parent: Required. Name of the consent store to retrieve consent artifacts
      from.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresConsentsCreateRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresConsentsCreateRequest
  object.

  Fields:
    consent: A Consent resource to be passed as the request body.
    parent: Required. Name of the consent store.
  """

  consent = _messages.MessageField('Consent', 1)
  parent = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresConsentsDeleteRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresConsentsDeleteRequest
  object.

  Fields:
    name: Required. The resource name of the Consent to delete, of the form `p
      rojects/{project_id}/locations/{location_id}/datasets/{dataset_id}/conse
      ntStores/{consent_store_id}/consents/{consent_id}`. An INVALID_ARGUMENT
      error occurs if `revision_id` is specified in the name.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresConsentsGetRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresConsentsGetRequest
  object.

  Fields:
    name: Required. The resource name of the Consent to retrieve, of the form
      `projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/con
      sentStores/{consent_store_id}/consents/{consent_id}`. In order to
      retrieve a previous revision of the Consent, also provide the revision
      ID: `projects/{project_id}/locations/{location_id}/datasets/{dataset_id}
      /consentStores/{consent_store_id}/consents/{consent_id}@{revision_id}`
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresConsentsListRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresConsentsListRequest
  object.

  Fields:
    filter: Optional. Restricts the consents returned to those matching a
      filter. The following syntax is available: * A string field value can be
      written as text inside quotation marks, for example `"query text"`. The
      only valid relational operation for text fields is equality (`=`), where
      text is searched within the field, rather than having the field be equal
      to the text. For example, `"Comment = great"` returns messages with
      `great` in the comment field. * A number field value can be written as
      an integer, a decimal, or an exponential. The valid relational operators
      for number fields are the equality operator (`=`), along with the less
      than/greater than operators (`<`, `<=`, `>`, `>=`). Note that there is
      no inequality (`!=`) operator. You can prepend the `NOT` operator to an
      expression to negate it. * A date field value must be written in `yyyy-
      mm-dd` form. Fields with date and time use the RFC3339 time format.
      Leading zeros are required for one-digit months and days. The valid
      relational operators for date fields are the equality operator (`=`) ,
      along with the less than/greater than operators (`<`, `<=`, `>`, `>=`).
      Note that there is no inequality (`!=`) operator. You can prepend the
      `NOT` operator to an expression to negate it. * Multiple field query
      expressions can be combined in one query by adding `AND` or `OR`
      operators between the expressions. If a boolean operator appears within
      a quoted string, it is not treated as special, it's just another part of
      the character string to be matched. You can prepend the `NOT` operator
      to an expression to negate it. The fields available for filtering are: -
      user_id. For example, `filter='user_id="user123"'`. - consent_artifact -
      state - revision_create_time - expire_time - metadata. For example,
      `filter=Metadata(\"testkey\")=\"value\"` or
      `filter=HasMetadata(\"testkey\")`.
    pageSize: Optional. Limit on the number of Consents to return in a single
      response. If not specified, 100 is used. May not be larger than 1000.
    pageToken: Optional. The next_page_token value returned from the previous
      List request, if any.
    parent: Required. Name of the consent store to retrieve Consents from.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresConsentsPatchRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresConsentsPatchRequest
  object.

  Fields:
    consent: A Consent resource to be passed as the request body.
    name: Resource name of the Consent, of the form `projects/{project_id}/loc
      ations/{location_id}/datasets/{dataset_id}/consentStores/{consent_store_
      id}/consents/{consent_id}`. Cannot be changed after creation.
    updateMask: Required. The update mask to apply to the resource. For the
      `FieldMask` definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask. Only the `user_id`,
      `policies`, `consent_artifact`, and `metadata` fields can be updated.
  """

  consent = _messages.MessageField('Consent', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class HealthcareProjectsLocationsDatasetsConsentStoresConsentsRevokeRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresConsentsRevokeRequest
  object.

  Fields:
    name: Required. The resource name of the Consent to revoke, of the form `p
      rojects/{project_id}/locations/{location_id}/datasets/{dataset_id}/conse
      ntStores/{consent_store_id}/consents/{consent_id}`. An INVALID_ARGUMENT
      error occurs if `revision_id` is specified in the name.
    revokeConsentRequest: A RevokeConsentRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  revokeConsentRequest = _messages.MessageField('RevokeConsentRequest', 2)


class HealthcareProjectsLocationsDatasetsConsentStoresCreateRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresCreateRequest object.

  Fields:
    consentStore: A ConsentStore resource to be passed as the request body.
    consentStoreId: Required. The ID of the consent store to create. The
      string must match the following regex: `[\p{L}\p{N}_\-\.]{1,256}`.
      Cannot be changed after creation.
    parent: Required. The name of the dataset this consent store belongs to.
  """

  consentStore = _messages.MessageField('ConsentStore', 1)
  consentStoreId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresDeleteRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresDeleteRequest object.

  Fields:
    name: Required. The resource name of the consent store to delete.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresEvaluateUserConsentsRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsConsentStoresEvaluateUserConsentsRequest
  object.

  Fields:
    consentStore: Required. Name of the consent store to retrieve User data
      mappings from.
    evaluateUserConsentsRequest: A EvaluateUserConsentsRequest resource to be
      passed as the request body.
  """

  consentStore = _messages.StringField(1, required=True)
  evaluateUserConsentsRequest = _messages.MessageField('EvaluateUserConsentsRequest', 2)


class HealthcareProjectsLocationsDatasetsConsentStoresGetIamPolicyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresGetRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresGetRequest object.

  Fields:
    name: Required. The resource name of the consent store to get.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresListRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresListRequest object.

  Fields:
    filter: Optional. Restricts the stores returned to those matching a
      filter. The following syntax is available: * A string field value can be
      written as text inside quotation marks, for example `"query text"`. The
      only valid relational operation for text fields is equality (`=`), where
      text is searched within the field, rather than having the field be equal
      to the text. For example, `"Comment = great"` returns messages with
      `great` in the comment field. * A number field value can be written as
      an integer, a decimal, or an exponential. The valid relational operators
      for number fields are the equality operator (`=`), along with the less
      than/greater than operators (`<`, `<=`, `>`, `>=`). Note that there is
      no inequality (`!=`) operator. You can prepend the `NOT` operator to an
      expression to negate it. * A date field value must be written in `yyyy-
      mm-dd` form. Fields with date and time use the RFC3339 time format.
      Leading zeros are required for one-digit months and days. The valid
      relational operators for date fields are the equality operator (`=`) ,
      along with the less than/greater than operators (`<`, `<=`, `>`, `>=`).
      Note that there is no inequality (`!=`) operator. You can prepend the
      `NOT` operator to an expression to negate it. * Multiple field query
      expressions can be combined in one query by adding `AND` or `OR`
      operators between the expressions. If a boolean operator appears within
      a quoted string, it is not treated as special, it's just another part of
      the character string to be matched. You can prepend the `NOT` operator
      to an expression to negate it. Only filtering on labels is supported.
      For example, `filter=labels.key=value`.
    pageSize: Optional. Limit on the number of consent stores to return in a
      single response. If not specified, 100 is used. May not be larger than
      1000.
    pageToken: Optional. Token to retrieve the next page of results, or empty
      to get the first page.
    parent: Required. Name of the dataset.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresPatchRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresPatchRequest object.

  Fields:
    consentStore: A ConsentStore resource to be passed as the request body.
    name: Identifier. Resource name of the consent store, of the form `project
      s/{project_id}/locations/{location_id}/datasets/{dataset_id}/consentStor
      es/{consent_store_id}`. Cannot be changed after creation.
    updateMask: Required. The update mask that applies to the resource. For
      the `FieldMask` definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask. Only the `labels`,
      `default_consent_ttl`, and `enable_consent_create_on_update` fields are
      allowed to be updated.
  """

  consentStore = _messages.MessageField('ConsentStore', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class HealthcareProjectsLocationsDatasetsConsentStoresQueryAccessibleDataRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsConsentStoresQueryAccessibleDataRequest
  object.

  Fields:
    consentStore: Required. Name of the consent store to retrieve User data
      mappings from.
    queryAccessibleDataRequest: A QueryAccessibleDataRequest resource to be
      passed as the request body.
  """

  consentStore = _messages.StringField(1, required=True)
  queryAccessibleDataRequest = _messages.MessageField('QueryAccessibleDataRequest', 2)


class HealthcareProjectsLocationsDatasetsConsentStoresSetIamPolicyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class HealthcareProjectsLocationsDatasetsConsentStoresTestIamPermissionsRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsConsentStoresTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsArchiveRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsArchiv
  eRequest object.

  Fields:
    archiveUserDataMappingRequest: A ArchiveUserDataMappingRequest resource to
      be passed as the request body.
    name: Required. The resource name of the User data mapping to archive.
  """

  archiveUserDataMappingRequest = _messages.MessageField('ArchiveUserDataMappingRequest', 1)
  name = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsCreateRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsCreate
  Request object.

  Fields:
    parent: Required. Name of the consent store.
    userDataMapping: A UserDataMapping resource to be passed as the request
      body.
  """

  parent = _messages.StringField(1, required=True)
  userDataMapping = _messages.MessageField('UserDataMapping', 2)


class HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsDeleteRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsDelete
  Request object.

  Fields:
    name: Required. The resource name of the User data mapping to delete.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsGetRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsGetRequest
  object.

  Fields:
    name: Required. The resource name of the User data mapping to retrieve.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsListRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsListRequest
  object.

  Fields:
    filter: Optional. Restricts the user data mappings returned to those
      matching a filter. The following syntax is available: * A string field
      value can be written as text inside quotation marks, for example `"query
      text"`. The only valid relational operation for text fields is equality
      (`=`), where text is searched within the field, rather than having the
      field be equal to the text. For example, `"Comment = great"` returns
      messages with `great` in the comment field. * A number field value can
      be written as an integer, a decimal, or an exponential. The valid
      relational operators for number fields are the equality operator (`=`),
      along with the less than/greater than operators (`<`, `<=`, `>`, `>=`).
      Note that there is no inequality (`!=`) operator. You can prepend the
      `NOT` operator to an expression to negate it. * A date field value must
      be written in `yyyy-mm-dd` form. Fields with date and time use the
      RFC3339 time format. Leading zeros are required for one-digit months and
      days. The valid relational operators for date fields are the equality
      operator (`=`) , along with the less than/greater than operators (`<`,
      `<=`, `>`, `>=`). Note that there is no inequality (`!=`) operator. You
      can prepend the `NOT` operator to an expression to negate it. * Multiple
      field query expressions can be combined in one query by adding `AND` or
      `OR` operators between the expressions. If a boolean operator appears
      within a quoted string, it is not treated as special, it's just another
      part of the character string to be matched. You can prepend the `NOT`
      operator to an expression to negate it. The fields available for
      filtering are: - data_id - user_id. For example,
      `filter=user_id=\"user123\"`. - archived - archive_time
    pageSize: Optional. Limit on the number of User data mappings to return in
      a single response. If not specified, 100 is used. May not be larger than
      1000.
    pageToken: Optional. Token to retrieve the next page of results, or empty
      to get the first page.
    parent: Required. Name of the consent store to retrieve User data mappings
      from.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsPatchRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsPatchRequest
  object.

  Fields:
    name: Resource name of the User data mapping, of the form `projects/{proje
      ct_id}/locations/{location_id}/datasets/{dataset_id}/consentStores/{cons
      ent_store_id}/userDataMappings/{user_data_mapping_id}`.
    updateMask: Required. The update mask that applies to the resource. For
      the `FieldMask` definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask. Only the `data_id`,
      `user_id` and `resource_attributes` fields can be updated.
    userDataMapping: A UserDataMapping resource to be passed as the request
      body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  userDataMapping = _messages.MessageField('UserDataMapping', 3)


class HealthcareProjectsLocationsDatasetsCreateRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsCreateRequest object.

  Fields:
    dataset: A Dataset resource to be passed as the request body.
    datasetId: Required. The ID of the dataset that is being created. The
      string must match the following regex: `[\p{L}\p{N}_\-\.]{1,256}`.
    parent: Required. The name of the project in which the server creates the
      dataset. For example,`projects/{project_id}/locations/{location_id}`.
  """

  dataset = _messages.MessageField('Dataset', 1)
  datasetId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class HealthcareProjectsLocationsDatasetsDataMapperWorkspacesGetIamPolicyRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsDataMapperWorkspacesGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDataMapperWorkspacesSetIamPolicyRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsDataMapperWorkspacesSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class HealthcareProjectsLocationsDatasetsDataMapperWorkspacesTestIamPermissionsRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDataMapperWorkspacesTestIamPermissi
  onsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class HealthcareProjectsLocationsDatasetsDeidentifyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDeidentifyRequest object.

  Fields:
    deidentifyDatasetRequest: A DeidentifyDatasetRequest resource to be passed
      as the request body.
    sourceDataset: Required. Source dataset resource name. For example,
      `projects/{project_id}/locations/{location_id}/datasets/{dataset_id}`.
      R5 FHIR stores are not supported and will be skipped.
  """

  deidentifyDatasetRequest = _messages.MessageField('DeidentifyDatasetRequest', 1)
  sourceDataset = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDeleteRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDeleteRequest object.

  Fields:
    name: Required. The name of the dataset to delete. For example,
      `projects/{project_id}/locations/{location_id}/datasets/{dataset_id}`.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresCreateRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresCreateRequest object.

  Fields:
    dicomStore: A DicomStore resource to be passed as the request body.
    dicomStoreId: Required. The ID of the DICOM store that is being created.
      Any string value up to 256 characters in length.
    parent: Required. The name of the dataset this DICOM store belongs to.
  """

  dicomStore = _messages.MessageField('DicomStore', 1)
  dicomStoreId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresDeidentifyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresDeidentifyRequest
  object.

  Fields:
    deidentifyDicomStoreRequest: A DeidentifyDicomStoreRequest resource to be
      passed as the request body.
    sourceStore: Required. Source DICOM store resource name. For example, `pro
      jects/{project_id}/locations/{location_id}/datasets/{dataset_id}/dicomSt
      ores/{dicom_store_id}`.
  """

  deidentifyDicomStoreRequest = _messages.MessageField('DeidentifyDicomStoreRequest', 1)
  sourceStore = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresDeleteRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresDeleteRequest object.

  Fields:
    name: Required. The resource name of the DICOM store to delete.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresExportRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresExportRequest object.

  Fields:
    exportDicomDataRequest: A ExportDicomDataRequest resource to be passed as
      the request body.
    name: Required. The DICOM store resource name from which to export the
      data. For example, `projects/{project_id}/locations/{location_id}/datase
      ts/{dataset_id}/dicomStores/{dicom_store_id}`.
  """

  exportDicomDataRequest = _messages.MessageField('ExportDicomDataRequest', 1)
  name = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresGetDICOMStoreMetricsRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsDicomStoresGetDICOMStoreMetricsRequest
  object.

  Fields:
    name: Required. The resource name of the DICOM store to get metrics for.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresGetIamPolicyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresGetRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresGetRequest object.

  Fields:
    name: Required. The resource name of the DICOM store to get.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresImportRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresImportRequest object.

  Fields:
    importDicomDataRequest: A ImportDicomDataRequest resource to be passed as
      the request body.
    name: Required. The name of the DICOM store resource into which the data
      is imported. For example, `projects/{project_id}/locations/{location_id}
      /datasets/{dataset_id}/dicomStores/{dicom_store_id}`.
  """

  importDicomDataRequest = _messages.MessageField('ImportDicomDataRequest', 1)
  name = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresListRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresListRequest object.

  Fields:
    filter: Restricts stores returned to those matching a filter. The
      following syntax is available: * A string field value can be written as
      text inside quotation marks, for example `"query text"`. The only valid
      relational operation for text fields is equality (`=`), where text is
      searched within the field, rather than having the field be equal to the
      text. For example, `"Comment = great"` returns messages with `great` in
      the comment field. * A number field value can be written as an integer,
      a decimal, or an exponential. The valid relational operators for number
      fields are the equality operator (`=`), along with the less than/greater
      than operators (`<`, `<=`, `>`, `>=`). Note that there is no inequality
      (`!=`) operator. You can prepend the `NOT` operator to an expression to
      negate it. * A date field value must be written in `yyyy-mm-dd` form.
      Fields with date and time use the RFC3339 time format. Leading zeros are
      required for one-digit months and days. The valid relational operators
      for date fields are the equality operator (`=`) , along with the less
      than/greater than operators (`<`, `<=`, `>`, `>=`). Note that there is
      no inequality (`!=`) operator. You can prepend the `NOT` operator to an
      expression to negate it. * Multiple field query expressions can be
      combined in one query by adding `AND` or `OR` operators between the
      expressions. If a boolean operator appears within a quoted string, it is
      not treated as special, it's just another part of the character string
      to be matched. You can prepend the `NOT` operator to an expression to
      negate it. Only filtering on labels is supported, for example
      `labels.key=value`.
    pageSize: Limit on the number of DICOM stores to return in a single
      response. If not specified, 100 is used. May not be larger than 1000.
    pageToken: The next_page_token value returned from the previous List
      request, if any.
    parent: Required. Name of the dataset.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresPatchRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresPatchRequest object.

  Fields:
    dicomStore: A DicomStore resource to be passed as the request body.
    name: Identifier. Resource name of the DICOM store, of the form `projects/
      {project_id}/locations/{location_id}/datasets/{dataset_id}/dicomStores/{
      dicom_store_id}`.
    updateMask: Required. The update mask applies to the resource. For the
      `FieldMask` definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  dicomStore = _messages.MessageField('DicomStore', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class HealthcareProjectsLocationsDatasetsDicomStoresSetIamPolicyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class HealthcareProjectsLocationsDatasetsDicomStoresTestIamPermissionsRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsDicomStoresTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class HealthcareProjectsLocationsDatasetsFhirStoresCreateRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresCreateRequest object.

  Fields:
    fhirStore: A FhirStore resource to be passed as the request body.
    fhirStoreId: Required. The ID of the FHIR store that is being created. The
      string must match the following regex: `[\p{L}\p{N}_\-\.]{1,256}`.
    parent: Required. The name of the dataset this FHIR store belongs to.
  """

  fhirStore = _messages.MessageField('FhirStore', 1)
  fhirStoreId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresDeidentifyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresDeidentifyRequest object.

  Fields:
    deidentifyFhirStoreRequest: A DeidentifyFhirStoreRequest resource to be
      passed as the request body.
    sourceStore: Required. Source FHIR store resource name. For example, `proj
      ects/{project_id}/locations/{location_id}/datasets/{dataset_id}/fhirStor
      es/{fhir_store_id}`. R5 stores are not supported.
  """

  deidentifyFhirStoreRequest = _messages.MessageField('DeidentifyFhirStoreRequest', 1)
  sourceStore = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresDeleteRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresDeleteRequest object.

  Fields:
    name: Required. The resource name of the FHIR store to delete.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresExportRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresExportRequest object.

  Fields:
    exportResourcesRequest: A ExportResourcesRequest resource to be passed as
      the request body.
    name: Required. The name of the FHIR store to export resource from, in the
      format `projects/{project_id}/locations/{location_id}/datasets/{dataset_
      id}/fhirStores/{fhir_store_id}`.
  """

  exportResourcesRequest = _messages.MessageField('ExportResourcesRequest', 1)
  name = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresGetFHIRStoreMetricsRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsFhirStoresGetFHIRStoreMetricsRequest
  object.

  Fields:
    name: Required. The resource name of the FHIR store to get metrics for.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresGetIamPolicyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresGetRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresGetRequest object.

  Fields:
    name: Required. The resource name of the FHIR store to get.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresImportRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresImportRequest object.

  Fields:
    importResourcesRequest: A ImportResourcesRequest resource to be passed as
      the request body.
    name: Required. The name of the FHIR store to which the server imports
      FHIR resources, in the format `projects/{project_id}/locations/{location
      _id}/datasets/{dataset_id}/fhirStores/{fhir_store_id}`.
  """

  importResourcesRequest = _messages.MessageField('ImportResourcesRequest', 1)
  name = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresListRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresListRequest object.

  Fields:
    filter: Restricts stores returned to those matching a filter. The
      following syntax is available: * A string field value can be written as
      text inside quotation marks, for example `"query text"`. The only valid
      relational operation for text fields is equality (`=`), where text is
      searched within the field, rather than having the field be equal to the
      text. For example, `"Comment = great"` returns messages with `great` in
      the comment field. * A number field value can be written as an integer,
      a decimal, or an exponential. The valid relational operators for number
      fields are the equality operator (`=`), along with the less than/greater
      than operators (`<`, `<=`, `>`, `>=`). Note that there is no inequality
      (`!=`) operator. You can prepend the `NOT` operator to an expression to
      negate it. * A date field value must be written in `yyyy-mm-dd` form.
      Fields with date and time use the RFC3339 time format. Leading zeros are
      required for one-digit months and days. The valid relational operators
      for date fields are the equality operator (`=`) , along with the less
      than/greater than operators (`<`, `<=`, `>`, `>=`). Note that there is
      no inequality (`!=`) operator. You can prepend the `NOT` operator to an
      expression to negate it. * Multiple field query expressions can be
      combined in one query by adding `AND` or `OR` operators between the
      expressions. If a boolean operator appears within a quoted string, it is
      not treated as special, it's just another part of the character string
      to be matched. You can prepend the `NOT` operator to an expression to
      negate it. Only filtering on labels is supported, for example
      `labels.key=value`.
    pageSize: Limit on the number of FHIR stores to return in a single
      response. If not specified, 100 is used. May not be larger than 1000.
    pageToken: The next_page_token value returned from the previous List
      request, if any.
    parent: Required. Name of the dataset.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresPatchRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresPatchRequest object.

  Fields:
    fhirStore: A FhirStore resource to be passed as the request body.
    name: Output only. Identifier. Resource name of the FHIR store, of the
      form `projects/{project_id}/locations/{location}/datasets/{dataset_id}/f
      hirStores/{fhir_store_id}`.
    updateMask: Required. The update mask applies to the resource. For the
      `FieldMask` definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  fhirStore = _messages.MessageField('FhirStore', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class HealthcareProjectsLocationsDatasetsFhirStoresSetIamPolicyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class HealthcareProjectsLocationsDatasetsFhirStoresTestIamPermissionsRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class HealthcareProjectsLocationsDatasetsGetIamPolicyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsGetRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsGetRequest object.

  Fields:
    name: Required. The name of the dataset to read. For example,
      `projects/{project_id}/locations/{location_id}/datasets/{dataset_id}`.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsHl7V2StoresCreateRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsHl7V2StoresCreateRequest object.

  Fields:
    hl7V2Store: A Hl7V2Store resource to be passed as the request body.
    hl7V2StoreId: Required. The ID of the HL7v2 store that is being created.
      The string must match the following regex: `[\p{L}\p{N}_\-\.]{1,256}`.
    parent: Required. The name of the dataset this HL7v2 store belongs to.
  """

  hl7V2Store = _messages.MessageField('Hl7V2Store', 1)
  hl7V2StoreId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class HealthcareProjectsLocationsDatasetsHl7V2StoresDeleteRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsHl7V2StoresDeleteRequest object.

  Fields:
    name: Required. The resource name of the HL7v2 store to delete.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsHl7V2StoresExportRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsHl7V2StoresExportRequest object.

  Fields:
    exportMessagesRequest: A ExportMessagesRequest resource to be passed as
      the request body.
    name: Required. The name of the source HL7v2 store, in the format `project
      s/{project_id}/locations/{location_id}/datasets/{dataset_id}/hl7v2Stores
      /{hl7v2_store_id}`
  """

  exportMessagesRequest = _messages.MessageField('ExportMessagesRequest', 1)
  name = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsHl7V2StoresGetHL7v2StoreMetricsRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsHl7V2StoresGetHL7v2StoreMetricsRequest
  object.

  Fields:
    name: Required. The resource name of the HL7v2 store to get metrics for,
      in the format `projects/{project_id}/locations/{location_id}/datasets/{d
      ataset_id}/hl7V2Stores/{hl7v2_store_id}`.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsHl7V2StoresGetIamPolicyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsHl7V2StoresGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsHl7V2StoresGetRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsHl7V2StoresGetRequest object.

  Fields:
    name: Required. The resource name of the HL7v2 store to get.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsHl7V2StoresImportRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsHl7V2StoresImportRequest object.

  Fields:
    importMessagesRequest: A ImportMessagesRequest resource to be passed as
      the request body.
    name: Required. The name of the target HL7v2 store, in the format `project
      s/{project_id}/locations/{location_id}/datasets/{dataset_id}/hl7v2Stores
      /{hl7v2_store_id}`
  """

  importMessagesRequest = _messages.MessageField('ImportMessagesRequest', 1)
  name = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsHl7V2StoresListRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsHl7V2StoresListRequest object.

  Fields:
    filter: Restricts stores returned to those matching a filter. The
      following syntax is available: * A string field value can be written as
      text inside quotation marks, for example `"query text"`. The only valid
      relational operation for text fields is equality (`=`), where text is
      searched within the field, rather than having the field be equal to the
      text. For example, `"Comment = great"` returns messages with `great` in
      the comment field. * A number field value can be written as an integer,
      a decimal, or an exponential. The valid relational operators for number
      fields are the equality operator (`=`), along with the less than/greater
      than operators (`<`, `<=`, `>`, `>=`). Note that there is no inequality
      (`!=`) operator. You can prepend the `NOT` operator to an expression to
      negate it. * A date field value must be written in `yyyy-mm-dd` form.
      Fields with date and time use the RFC3339 time format. Leading zeros are
      required for one-digit months and days. The valid relational operators
      for date fields are the equality operator (`=`) , along with the less
      than/greater than operators (`<`, `<=`, `>`, `>=`). Note that there is
      no inequality (`!=`) operator. You can prepend the `NOT` operator to an
      expression to negate it. * Multiple field query expressions can be
      combined in one query by adding `AND` or `OR` operators between the
      expressions. If a boolean operator appears within a quoted string, it is
      not treated as special, it's just another part of the character string
      to be matched. You can prepend the `NOT` operator to an expression to
      negate it. Only filtering on labels is supported. For example,
      `labels.key=value`.
    pageSize: Limit on the number of HL7v2 stores to return in a single
      response. If not specified, 100 is used. May not be larger than 1000.
    pageToken: The next_page_token value returned from the previous List
      request, if any.
    parent: Required. Name of the dataset.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesExportRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesExportRequest
  object.

  Fields:
    exportMessagesRequest: A ExportMessagesRequest resource to be passed as
      the request body.
    name: Required. The name of the source HL7v2 store, in the format `project
      s/{project_id}/locations/{location_id}/datasets/{dataset_id}/hl7v2Stores
      /{hl7v2_store_id}`
  """

  exportMessagesRequest = _messages.MessageField('ExportMessagesRequest', 1)
  name = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsHl7V2StoresPatchRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsHl7V2StoresPatchRequest object.

  Fields:
    hl7V2Store: A Hl7V2Store resource to be passed as the request body.
    name: Identifier. Resource name of the HL7v2 store, of the form `projects/
      {project_id}/locations/{location_id}/datasets/{dataset_id}/hl7V2Stores/{
      hl7v2_store_id}`.
    updateMask: Required. The update mask applies to the resource. For the
      `FieldMask` definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  hl7V2Store = _messages.MessageField('Hl7V2Store', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class HealthcareProjectsLocationsDatasetsHl7V2StoresSetIamPolicyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsHl7V2StoresSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class HealthcareProjectsLocationsDatasetsHl7V2StoresTestIamPermissionsRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsHl7V2StoresTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class HealthcareProjectsLocationsDatasetsListRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsListRequest object.

  Fields:
    pageSize: The maximum number of items to return. If not specified, 100 is
      used. May not be larger than 1000.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: Required. The name of the project whose datasets should be listed.
      For example, `projects/{project_id}/locations/{location_id}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class HealthcareProjectsLocationsDatasetsOperationsCancelRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsOperationsGetRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsOperationsListRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class HealthcareProjectsLocationsDatasetsPatchRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsPatchRequest object.

  Fields:
    dataset: A Dataset resource to be passed as the request body.
    name: Identifier. Resource name of the dataset, of the form
      `projects/{project_id}/locations/{location_id}/datasets/{dataset_id}`.
    updateMask: Required. The update mask applies to the resource. For the
      `FieldMask` definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  dataset = _messages.MessageField('Dataset', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class HealthcareProjectsLocationsDatasetsSetIamPolicyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class HealthcareProjectsLocationsDatasetsTestIamPermissionsRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class HealthcareProjectsLocationsGetRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsListRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class HealthcareProjectsLocationsServicesDeidentifyDeidentifyDicomInstanceRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsServicesDeidentifyDeidentifyDicomInstanceRequest
  object.

  Fields:
    gcsConfigUri: Cloud Storage location to read the JSON DeidentifyConfig
      from, overriding the default config. Must be of the form
      `gs://{bucket_id}/{object_id}`. The Cloud Storage location must grant
      the Cloud IAM role `roles/storage.objectViewer` to the project's Cloud
      Healthcare Service Agent service account.
    httpBody: A HttpBody resource to be passed as the request body.
    name: Required. The name of the service that should handle the request, of
      the form:
      `projects/{project_id}/locations/{location_id}/services/deidentify`.
  """

  gcsConfigUri = _messages.StringField(1)
  httpBody = _messages.MessageField('HttpBody', 2)
  name = _messages.StringField(3, required=True)


class HealthcareProjectsLocationsServicesDeidentifyDeidentifyFhirResourceRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsServicesDeidentifyDeidentifyFhirResourceRequest
  object.

  Enums:
    VersionValueValuesEnum:

  Fields:
    gcsConfigUri: Cloud Storage location to read the JSON DeidentifyConfig
      from.
    httpBody: A HttpBody resource to be passed as the request body.
    name: Required. The name of the service that should handle the request, of
      the form:
      `projects/{project_id}/locations/{location_id}/services/deidentify`.
    version: A VersionValueValuesEnum attribute.
  """

  class VersionValueValuesEnum(_messages.Enum):
    r"""VersionValueValuesEnum enum type.

    Values:
      VERSION_UNSPECIFIED: VERSION_UNSPECIFIED is treated as STU3.
      DSTU2: FHIR version DSTU2.
      STU3: FHIR version STU3.
      R4: FHIR version R4.
    """
    VERSION_UNSPECIFIED = 0
    DSTU2 = 1
    STU3 = 2
    R4 = 3

  gcsConfigUri = _messages.StringField(1)
  httpBody = _messages.MessageField('HttpBody', 2)
  name = _messages.StringField(3, required=True)
  version = _messages.EnumField('VersionValueValuesEnum', 4)


class HealthcareProjectsLocationsServicesNlpAnalyzeEntitiesRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsServicesNlpAnalyzeEntitiesRequest object.

  Fields:
    analyzeEntitiesRequest: A AnalyzeEntitiesRequest resource to be passed as
      the request body.
    nlpService: The resource name of the service of the form:
      "projects/{project_id}/locations/{location_id}/services/nlp".
  """

  analyzeEntitiesRequest = _messages.MessageField('AnalyzeEntitiesRequest', 1)
  nlpService = _messages.StringField(2, required=True)


class Hl7V2Store(_messages.Message):
  r"""Represents an HL7v2 store.

  Messages:
    LabelsValue: User-supplied key-value pairs used to organize HL7v2 stores.
      Label keys must be between 1 and 63 characters long, have a UTF-8
      encoding of maximum 128 bytes, and must conform to the following PCRE
      regular expression: \p{Ll}\p{Lo}{0,62} Label values are optional, must
      be between 1 and 63 characters long, have a UTF-8 encoding of maximum
      128 bytes, and must conform to the following PCRE regular expression:
      [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated
      with a given store.

  Fields:
    labels: User-supplied key-value pairs used to organize HL7v2 stores. Label
      keys must be between 1 and 63 characters long, have a UTF-8 encoding of
      maximum 128 bytes, and must conform to the following PCRE regular
      expression: \p{Ll}\p{Lo}{0,62} Label values are optional, must be
      between 1 and 63 characters long, have a UTF-8 encoding of maximum 128
      bytes, and must conform to the following PCRE regular expression:
      [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated
      with a given store.
    name: Identifier. Resource name of the HL7v2 store, of the form `projects/
      {project_id}/locations/{location_id}/datasets/{dataset_id}/hl7V2Stores/{
      hl7v2_store_id}`.
    notificationConfig: The notification destination all messages (both Ingest
      & Create) are published on. Only the message name is sent as part of the
      notification. If this is unset, no notifications are sent. Supplied by
      the client.
    parserConfig: The configuration for the parser. It determines how the
      server parses the messages.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""User-supplied key-value pairs used to organize HL7v2 stores. Label
    keys must be between 1 and 63 characters long, have a UTF-8 encoding of
    maximum 128 bytes, and must conform to the following PCRE regular
    expression: \p{Ll}\p{Lo}{0,62} Label values are optional, must be between
    1 and 63 characters long, have a UTF-8 encoding of maximum 128 bytes, and
    must conform to the following PCRE regular expression:
    [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated with
    a given store.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)
  name = _messages.StringField(2)
  notificationConfig = _messages.MessageField('NotificationConfig', 3)
  parserConfig = _messages.MessageField('ParserConfig', 4)


class Hl7V2StoreMetric(_messages.Message):
  r"""Count of messages and total storage size by type for a given HL7 store.

  Fields:
    count: The total count of HL7v2 messages in the store for the given
      message type.
    messageType: The Hl7v2 message type this metric applies to, such as `ADT`
      or `ORU`.
    structuredStorageSizeBytes: The total amount of structured storage used by
      HL7v2 messages of this message type in the store.
  """

  count = _messages.IntegerField(1)
  messageType = _messages.StringField(2)
  structuredStorageSizeBytes = _messages.IntegerField(3)


class Hl7V2StoreMetrics(_messages.Message):
  r"""List of metrics for a given HL7v2 store.

  Fields:
    metrics: List of HL7v2 store metrics by message type.
    name: The resource name of the HL7v2 store to get metrics for, in the
      format `projects/{project_id}/datasets/{dataset_id}/hl7V2Stores/{hl7v2_s
      tore_id}`.
  """

  metrics = _messages.MessageField('Hl7V2StoreMetric', 1, repeated=True)
  name = _messages.StringField(2)


class HttpBody(_messages.Message):
  r"""Message that represents an arbitrary HTTP body. It should only be used
  for payload formats that can't be represented as JSON, such as raw binary or
  an HTML page. This message can be used both in streaming and non-streaming
  API methods in the request as well as the response. It can be used as a top-
  level request field, which is convenient if one wants to extract parameters
  from either the URL or HTTP template into the request fields and also want
  access to the raw HTTP body. Example: message GetResourceRequest { // A
  unique request id. string request_id = 1; // The raw HTTP body is bound to
  this field. google.api.HttpBody http_body = 2; } service ResourceService {
  rpc GetResource(GetResourceRequest) returns (google.api.HttpBody); rpc
  UpdateResource(google.api.HttpBody) returns (google.protobuf.Empty); }
  Example with streaming methods: service CaldavService { rpc
  GetCalendar(stream google.api.HttpBody) returns (stream
  google.api.HttpBody); rpc UpdateCalendar(stream google.api.HttpBody) returns
  (stream google.api.HttpBody); } Use of this type only changes how the
  request and response bodies are handled, all other features will continue to
  work unchanged.

  Messages:
    ExtensionsValueListEntry: A ExtensionsValueListEntry object.

  Fields:
    contentType: The HTTP Content-Type header value specifying the content
      type of the body.
    data: The HTTP request/response body as raw binary.
    extensions: Application specific response metadata. Must be set in the
      first response for streaming APIs.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExtensionsValueListEntry(_messages.Message):
    r"""A ExtensionsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a
        ExtensionsValueListEntry object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExtensionsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  contentType = _messages.StringField(1)
  data = _messages.BytesField(2)
  extensions = _messages.MessageField('ExtensionsValueListEntry', 3, repeated=True)


class Image(_messages.Message):
  r"""Raw bytes representing consent artifact content.

  Fields:
    gcsUri: Input only. Points to a Cloud Storage URI containing the consent
      artifact content. The URI must be in the following format:
      `gs://{bucket_id}/{object_id}`. The Cloud Healthcare API service account
      must have the `roles/storage.objectViewer` Cloud IAM role for this Cloud
      Storage location. The consent artifact content at this URI is copied to
      a Cloud Storage location managed by the Cloud Healthcare API. Responses
      to fetching requests return the consent artifact content in raw_bytes.
    rawBytes: Consent artifact content represented as a stream of bytes. This
      field is populated when returned in GetConsentArtifact response, but not
      included in CreateConsentArtifact and ListConsentArtifact response.
  """

  gcsUri = _messages.StringField(1)
  rawBytes = _messages.BytesField(2)


class ImageConfig(_messages.Message):
  r"""Specifies how to handle the de-identification of image pixels.

  Enums:
    TextRedactionModeValueValuesEnum: Determines how to redact text from
      images.

  Fields:
    additionalInfoTypes: Additional InfoTypes to redact in the images in
      addition to those used by `text_redaction_mode`. Can only be used when
      `text_redaction_mode` is set to `REDACT_SENSITIVE_TEXT`,
      `REDACT_SENSITIVE_TEXT_CLEAN_DESCRIPTORS` or
      `TEXT_REDACTION_MODE_UNSPECIFIED`.
    excludeInfoTypes: InfoTypes to skip redacting, overriding those used by
      `text_redaction_mode`. Can only be used when `text_redaction_mode` is
      set to `REDACT_SENSITIVE_TEXT` or
      `REDACT_SENSITIVE_TEXT_CLEAN_DESCRIPTORS`.
    textRedactionMode: Determines how to redact text from images.
  """

  class TextRedactionModeValueValuesEnum(_messages.Enum):
    r"""Determines how to redact text from images.

    Values:
      TEXT_REDACTION_MODE_UNSPECIFIED: No text redaction specified. Same as
        REDACT_NO_TEXT.
      REDACT_ALL_TEXT: Redact all text.
      REDACT_SENSITIVE_TEXT: Redact sensitive text. Uses the set of [Default
        DICOM InfoTypes](https://cloud.google.com/healthcare-api/docs/how-
        tos/dicom-deidentify#default_dicom_infotypes).
      REDACT_NO_TEXT: Do not redact text.
      REDACT_SENSITIVE_TEXT_CLEAN_DESCRIPTORS: This mode is like
        `REDACT_SENSITIVE_TEXT` with the addition of the [Clean Descriptors
        Option] (https://dicom.nema.org/medical/dicom/2018e/output/chtml/part1
        5/sect_E.3.5.html) enabled: When cleaning text, the process attempts
        to transform phrases matching any of the tags marked for removal
        (action codes D, Z, X, and U) in the [Basic Profile] (https://dicom.ne
        ma.org/medical/dicom/2018e/output/chtml/part15/chapter_E.html). These
        contextual phrases are replaced with the token "[CTX]". This mode uses
        an additional infoType during inspection.
    """
    TEXT_REDACTION_MODE_UNSPECIFIED = 0
    REDACT_ALL_TEXT = 1
    REDACT_SENSITIVE_TEXT = 2
    REDACT_NO_TEXT = 3
    REDACT_SENSITIVE_TEXT_CLEAN_DESCRIPTORS = 4

  additionalInfoTypes = _messages.StringField(1, repeated=True)
  excludeInfoTypes = _messages.StringField(2, repeated=True)
  textRedactionMode = _messages.EnumField('TextRedactionModeValueValuesEnum', 3)


class ImportDicomDataRequest(_messages.Message):
  r"""Imports data into the specified DICOM store. Returns an error if any of
  the files to import are not DICOM files. This API accepts duplicate DICOM
  instances by ignoring the newly-pushed instance. It does not overwrite.

  Fields:
    gcsSource: Cloud Storage source data location and import configuration.
      The Cloud Healthcare Service Agent requires the
      `roles/storage.objectViewer` Cloud IAM roles on the Cloud Storage
      location.
  """

  gcsSource = _messages.MessageField('GoogleCloudHealthcareV1alpha2DicomGcsSource', 1)


class ImportDicomDataResponse(_messages.Message):
  r"""Returns additional information in regards to a completed DICOM store
  import.
  """



class ImportMessagesErrorDetails(_messages.Message):
  r"""Error response of importing messages. This structure is included in the
  error details to describe the detailed error. It is only included when the
  operation finishes with some failure.

  Fields:
    errorCount: The number of messages that had errors.
    hl7v2Store: The name of the target HL7v2 store, in the format `projects/{p
      roject_id}/locations/{location_id}/datasets/{dataset_id}/hl7v2Stores/{hl
      7v2_store_id}`
    inputSize: The total number of messages included in the source data. This
      is the sum of the success and error counts.
    successCount: The number of messages that have been imported.
  """

  errorCount = _messages.IntegerField(1)
  hl7v2Store = _messages.StringField(2)
  inputSize = _messages.IntegerField(3)
  successCount = _messages.IntegerField(4)


class ImportMessagesRequest(_messages.Message):
  r"""Request to import messages.

  Fields:
    gcsSource: Cloud Storage source data location and import configuration.
      The Cloud Healthcare Service Agent requires the
      `roles/storage.objectViewer` Cloud IAM roles on the Cloud Storage
      location.
  """

  gcsSource = _messages.MessageField('GoogleCloudHealthcareV1alpha2Hl7v2GcsSource', 1)


class ImportMessagesResponse(_messages.Message):
  r"""Final response of importing messages. This structure is included in the
  response to describe the detailed outcome. It is only included when the
  operation finishes successfully.

  Fields:
    hl7v2Store: The name of the target HL7v2 store, in the format `projects/{p
      roject_id}/locations/{location_id}/datasets/{dataset_id}/hl7v2Stores/{hl
      7v2_store_id}`
    inputSize: The total number of resources included in the source data.
  """

  hl7v2Store = _messages.StringField(1)
  inputSize = _messages.IntegerField(2)


class ImportResourcesRequest(_messages.Message):
  r"""Request to import resources.

  Enums:
    ContentStructureValueValuesEnum: The content structure in the source
      location. If not specified, the server treats the input source files as
      BUNDLE.

  Fields:
    contentStructure: The content structure in the source location. If not
      specified, the server treats the input source files as BUNDLE.
    gcsErrorDestination: The Cloud Storage destination to write the error
      report to. The Healthcare Service Agent account requires the
      `roles/storage.objectAdmin` role on the Cloud Storage location. Writing
      a file to the same destination multiple times results in the previous
      version of the file being overwritten.
    gcsSource: Cloud Storage source data location and import configuration.
      The Healthcare Service Agent account requires the
      `roles/storage.objectAdmin` role on the Cloud Storage location. For each
      Cloud Storage object, use a text file that contains the format specified
      in ContentStructure.
  """

  class ContentStructureValueValuesEnum(_messages.Enum):
    r"""The content structure in the source location. If not specified, the
    server treats the input source files as BUNDLE.

    Values:
      CONTENT_STRUCTURE_UNSPECIFIED: If the content structure is not
        specified, the default value `BUNDLE` is used.
      BUNDLE: The source file contains one or more lines of newline-delimited
        JSON (ndjson). Each line is a bundle that contains one or more
        resources.
      RESOURCE: The source file contains one or more lines of newline-
        delimited JSON (ndjson). Each line is a single resource.
      BUNDLE_PRETTY: The entire file is one JSON bundle. The JSON can span
        multiple lines.
      RESOURCE_PRETTY: The entire file is one JSON resource. The JSON can span
        multiple lines.
    """
    CONTENT_STRUCTURE_UNSPECIFIED = 0
    BUNDLE = 1
    RESOURCE = 2
    BUNDLE_PRETTY = 3
    RESOURCE_PRETTY = 4

  contentStructure = _messages.EnumField('ContentStructureValueValuesEnum', 1)
  gcsErrorDestination = _messages.MessageField('GoogleCloudHealthcareV1alpha2FhirGcsErrorDestination', 2)
  gcsSource = _messages.MessageField('GoogleCloudHealthcareV1alpha2FhirGcsSource', 3)


class InfoTypeTransformation(_messages.Message):
  r"""A transformation to apply to text that is identified as a specific
  info_type.

  Fields:
    characterMaskConfig: Config for character mask.
    cryptoHashConfig: Config for crypto hash.
    dateShiftConfig: Config for date shift.
    infoTypes: `InfoTypes` to apply this transformation to. If this is not
      specified, this transformation becomes the default transformation, and
      is used for any `info_type` that is not specified in another
      transformation.
    redactConfig: Config for text redaction.
    replaceWithInfoTypeConfig: Config for replace with InfoType.
  """

  characterMaskConfig = _messages.MessageField('CharacterMaskConfig', 1)
  cryptoHashConfig = _messages.MessageField('CryptoHashConfig', 2)
  dateShiftConfig = _messages.MessageField('DateShiftConfig', 3)
  infoTypes = _messages.StringField(4, repeated=True)
  redactConfig = _messages.MessageField('RedactConfig', 5)
  replaceWithInfoTypeConfig = _messages.MessageField('ReplaceWithInfoTypeConfig', 6)


class LinkedEntity(_messages.Message):
  r"""EntityMentions can be linked to multiple entities using a LinkedEntity
  message lets us add other fields, e.g. confidence.

  Fields:
    entityId: entity_id is a concept unique identifier. These are prefixed by
      a string that identifies the entity coding system, followed by the
      unique identifier within that system. For example, "UMLS/C0000970". This
      also supports ad hoc entities, which are formed by normalizing entity
      mention content.
  """

  entityId = _messages.StringField(1)


class ListAttributeDefinitionsResponse(_messages.Message):
  r"""A ListAttributeDefinitionsResponse object.

  Fields:
    attributeDefinitions: The returned Attribute definitions. The maximum
      number of attributes returned is determined by the value of page_size in
      the ListAttributeDefinitionsRequest.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  attributeDefinitions = _messages.MessageField('AttributeDefinition', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListConsentArtifactsResponse(_messages.Message):
  r"""A ListConsentArtifactsResponse object.

  Fields:
    consentArtifacts: The returned Consent artifacts. The maximum number of
      artifacts returned is determined by the value of page_size in the
      ListConsentArtifactsRequest.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  consentArtifacts = _messages.MessageField('ConsentArtifact', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListConsentStoresResponse(_messages.Message):
  r"""A ListConsentStoresResponse object.

  Fields:
    consentStores: The returned consent stores. The maximum number of stores
      returned is determined by the value of page_size in the
      ListConsentStoresRequest.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  consentStores = _messages.MessageField('ConsentStore', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListConsentsResponse(_messages.Message):
  r"""A ListConsentsResponse object.

  Fields:
    consents: The returned Consents. The maximum number of Consents returned
      is determined by the value of page_size in the ListConsentsRequest.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  consents = _messages.MessageField('Consent', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListDatasetsResponse(_messages.Message):
  r"""Lists the available datasets.

  Fields:
    datasets: The first page of datasets.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  datasets = _messages.MessageField('Dataset', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListDicomStoresResponse(_messages.Message):
  r"""Lists the DICOM stores in the given dataset.

  Fields:
    dicomStores: The returned DICOM stores. Won't be more DICOM stores than
      the value of page_size in the request.
    nextPageToken: Token to retrieve the next page of results or empty if
      there are no more results in the list.
  """

  dicomStores = _messages.MessageField('DicomStore', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListFhirStoresResponse(_messages.Message):
  r"""Lists the FHIR stores in the given dataset.

  Fields:
    fhirStores: The returned FHIR stores. Won't be more FHIR stores than the
      value of page_size in the request.
    nextPageToken: Token to retrieve the next page of results or empty if
      there are no more results in the list.
  """

  fhirStores = _messages.MessageField('FhirStore', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListHl7V2StoresResponse(_messages.Message):
  r"""Lists the HL7v2 stores in the given dataset.

  Fields:
    hl7V2Stores: The returned HL7v2 stores. Won't be more HL7v2 stores than
      the value of page_size in the request.
    nextPageToken: Token to retrieve the next page of results or empty if
      there are no more results in the list.
  """

  hl7V2Stores = _messages.MessageField('Hl7V2Store', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListUserDataMappingsResponse(_messages.Message):
  r"""A ListUserDataMappingsResponse object.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    userDataMappings: The returned User data mappings. The maximum number of
      User data mappings returned is determined by the value of page_size in
      the ListUserDataMappingsRequest.
  """

  nextPageToken = _messages.StringField(1)
  userDataMappings = _messages.MessageField('UserDataMapping', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class NotificationConfig(_messages.Message):
  r"""Specifies where the server sends notifications upon changes to a data
  store.

  Fields:
    pubsubTopic: The [Pub/Sub](https://cloud.google.com/pubsub/docs/) topic
      that notifications of changes are published on. Supplied by the client.
      PubsubMessage.Data contains the resource name. PubsubMessage.MessageId
      is the ID of this message. It is guaranteed to be unique within the
      topic. PubsubMessage.PublishTime is the time at which the message was
      published. Notifications are only sent if the topic is non-empty. [Topic
      names](https://cloud.google.com/pubsub/docs/overview#names) must be
      scoped to a project. Cloud Healthcare API service account must have
      publisher permissions on the given Pub/Sub topic. Not having adequate
      permissions causes the calls that send notifications to fail. If a
      notification can't be published to Pub/Sub, errors are logged to Cloud
      Logging (see [Viewing error logs in Cloud
      Logging](https://cloud.google.com/healthcare/docs/how-tos/logging)). If
      the number of errors exceeds a certain rate, some aren't submitted. Note
      that not all operations trigger notifications, see [Configuring Pub/Sub
      notifications](https://cloud.google.com/healthcare/docs/how-tos/pubsub)
      for specific details.
    sendForBulkImport: Indicates whether or not to send Pub/Sub notifications
      on bulk import. Only supported for DICOM imports.
  """

  pubsubTopic = _messages.StringField(1)
  sendForBulkImport = _messages.BooleanField(2)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""OperationMetadata provides information about the operation execution.
  Returned in the long-running operation's metadata field.

  Fields:
    apiMethodName: The name of the API method that initiated the operation.
    counter: A ProgressCounter attribute.
    createTime: The time at which the operation was created by the API.
    endTime: The time at which execution workloads were completed. Some tasks
      will complete after this time such as logging audit logs.
  """

  apiMethodName = _messages.StringField(1)
  counter = _messages.MessageField('ProgressCounter', 2)
  createTime = _messages.StringField(3)
  endTime = _messages.StringField(4)


class ParserConfig(_messages.Message):
  r"""The configuration for the parser. It determines how the server parses
  the messages.

  Enums:
    VersionValueValuesEnum: Immutable. Determines the version of both the
      default parser to be used when `schema` is not given, as well as the
      schematized parser used when `schema` is specified. This field is
      immutable after HL7v2 store creation.

  Fields:
    allowNullHeader: Determines whether messages with no header are allowed.
    segmentTerminator: Byte(s) to use as the segment terminator. If this is
      unset, '\r' is used as the segment terminator, matching the HL7 version
      2 specification.
    version: Immutable. Determines the version of both the default parser to
      be used when `schema` is not given, as well as the schematized parser
      used when `schema` is specified. This field is immutable after HL7v2
      store creation.
  """

  class VersionValueValuesEnum(_messages.Enum):
    r"""Immutable. Determines the version of both the default parser to be
    used when `schema` is not given, as well as the schematized parser used
    when `schema` is specified. This field is immutable after HL7v2 store
    creation.

    Values:
      PARSER_VERSION_UNSPECIFIED: Unspecified parser version, equivalent to
        V1.
      V1: The `parsed_data` includes every given non-empty message field
        except the Field Separator (MSH-1) field. As a result, the parsed MSH
        segment starts with the MSH-2 field and the field numbers are off-by-
        one with respect to the HL7 standard.
      V2: The `parsed_data` includes every given non-empty message field.
      V3: This version is the same as V2, with the following change. The
        `parsed_data` contains unescaped escaped field separators, component
        separators, sub-component separators, repetition separators, escape
        characters, and truncation characters. If `schema` is specified, the
        schematized parser uses improved parsing heuristics compared to
        previous versions.
    """
    PARSER_VERSION_UNSPECIFIED = 0
    V1 = 1
    V2 = 2
    V3 = 3

  allowNullHeader = _messages.BooleanField(1)
  segmentTerminator = _messages.BytesField(2)
  version = _messages.EnumField('VersionValueValuesEnum', 3)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class ProgressCounter(_messages.Message):
  r"""ProgressCounter provides counters to describe an operation's progress.

  Fields:
    failure: The number of units that failed in the operation.
    pending: The number of units that are pending in the operation.
    success: The number of units that succeeded in the operation.
  """

  failure = _messages.IntegerField(1)
  pending = _messages.IntegerField(2)
  success = _messages.IntegerField(3)


class QueryAccessibleDataRequest(_messages.Message):
  r"""Queries all data_ids that are consented for a given use in the given
  consent store and writes them to a specified destination. The returned
  Operation includes a progress counter for the number of User data mappings
  processed. Errors are logged to Cloud Logging (see [Viewing error logs in
  Cloud Logging] (https://cloud.google.com/healthcare/docs/how-tos/logging)
  and [QueryAccessibleData] for a sample log entry).

  Messages:
    RequestAttributesValue: The values of request attributes associated with
      this access request.
    ResourceAttributesValue: Optional. The values of resource attributes
      associated with the type of resources being requested. If no values are
      specified, then all resource types are included in the output.

  Fields:
    gcsDestination: The Cloud Storage destination. The Cloud Healthcare API
      service account must have the `roles/storage.objectAdmin` Cloud IAM role
      for this Cloud Storage location. The object name is in the following
      format: query-accessible-data-result-{operation_id}.txt where each line
      contains a single data_id.
    requestAttributes: The values of request attributes associated with this
      access request.
    resourceAttributes: Optional. The values of resource attributes associated
      with the type of resources being requested. If no values are specified,
      then all resource types are included in the output.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class RequestAttributesValue(_messages.Message):
    r"""The values of request attributes associated with this access request.

    Messages:
      AdditionalProperty: An additional property for a RequestAttributesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        RequestAttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a RequestAttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourceAttributesValue(_messages.Message):
    r"""Optional. The values of resource attributes associated with the type
    of resources being requested. If no values are specified, then all
    resource types are included in the output.

    Messages:
      AdditionalProperty: An additional property for a ResourceAttributesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        ResourceAttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourceAttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  gcsDestination = _messages.MessageField('GoogleCloudHealthcareV1alpha2ConsentGcsDestination', 1)
  requestAttributes = _messages.MessageField('RequestAttributesValue', 2)
  resourceAttributes = _messages.MessageField('ResourceAttributesValue', 3)


class QueryAccessibleDataResponse(_messages.Message):
  r"""Response for successful QueryAccessibleData operations. This structure
  is included in the response upon operation completion.

  Fields:
    gcsUris: List of files, each of which contains a list of data_id(s) that
      are consented for a specified use in the request.
  """

  gcsUris = _messages.StringField(1, repeated=True)


class RedactConfig(_messages.Message):
  r"""Defines how to redact sensitive values. Default behavior is erase. For
  example, "My name is Jane." becomes "My name is ."
  """



class ReplaceWithInfoTypeConfig(_messages.Message):
  r"""When using the INSPECT_AND_TRANSFORM action, each match is replaced with
  the name of the `info_type`. For example, "My name is Jane" becomes "My name
  is [PERSON_NAME]." The TRANSFORM action is equivalent to redacting.
  """



class Result(_messages.Message):
  r"""The consent evaluation result for a single `data_id`.

  Messages:
    ConsentDetailsValue: The resource names of all evaluated Consents mapped
      to their evaluation.

  Fields:
    consentDetails: The resource names of all evaluated Consents mapped to
      their evaluation.
    consented: Whether the resource is consented for the given use.
    dataId: The unique identifier of the evaluated resource.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ConsentDetailsValue(_messages.Message):
    r"""The resource names of all evaluated Consents mapped to their
    evaluation.

    Messages:
      AdditionalProperty: An additional property for a ConsentDetailsValue
        object.

    Fields:
      additionalProperties: Additional properties of type ConsentDetailsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ConsentDetailsValue object.

      Fields:
        key: Name of the additional property.
        value: A ConsentEvaluation attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ConsentEvaluation', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  consentDetails = _messages.MessageField('ConsentDetailsValue', 1)
  consented = _messages.BooleanField(2)
  dataId = _messages.StringField(3)


class RevokeConsentRequest(_messages.Message):
  r"""Revokes the latest revision of the specified Consent by committing a new
  revision with `state` updated to `REVOKED`. If the latest revision of the
  given Consent is in the `REVOKED` state, no new revision is committed.
  """



class SchemaConfig(_messages.Message):
  r"""Configuration for the FHIR BigQuery and Cloud Storage schema. Determines
  how the server generates the schema.

  Enums:
    SchemaTypeValueValuesEnum: Specifies the output schema type. Schema type
      is required.

  Fields:
    recursiveStructureDepth: The depth for all recursive structures in the
      output analytics schema. For example, `concept` in the CodeSystem
      resource is a recursive structure; when the depth is 2, the CodeSystem
      table will have a column called `concept.concept` but not
      `concept.concept.concept`. If not specified or set to 0, the server will
      use the default value 2. The maximum depth allowed is 5.
    schemaType: Specifies the output schema type. Schema type is required.
  """

  class SchemaTypeValueValuesEnum(_messages.Enum):
    r"""Specifies the output schema type. Schema type is required.

    Values:
      SCHEMA_TYPE_UNSPECIFIED: No schema type specified. This type is
        unsupported.
      LOSSLESS: A data-driven schema generated from the fields present in the
        FHIR data being exported, with no additional simplification. This type
        cannot be used for streaming to BigQuery.
      ANALYTICS: Analytics schema defined by the FHIR community. See
        https://github.com/FHIR/sql-on-fhir/blob/master/sql-on-fhir.md.
        BigQuery only allows a maximum of 10,000 columns per table. Due to
        this limitation, the server will not generate schemas for fields of
        type `Resource`, which can hold any resource type. The affected fields
        are `Parameters.parameter.resource`, `Bundle.entry.resource`, and
        `Bundle.entry.response.outcome`. Analytics schema does not gracefully
        handle extensions with one or more occurrences, anaytics schema also
        does not handle contained resource. Additionally, extensions with a
        URL ending in "/{existing_resource_field_name}" may cause undefined
        behavior.
      ANALYTICS_V2: Analytics V2, similar to schema defined by the FHIR
        community, with added support for extensions with one or more
        occurrences and contained resources in stringified JSON. Extensions
        with a URL ending in "/{existing_resource_field_name}" will cause
        conflict and prevent the resource from being sent to BigQuery.
        Analytics V2 uses more space in the destination table than Analytics
        V1. It is generally recommended to use Analytics V2 over Analytics.
    """
    SCHEMA_TYPE_UNSPECIFIED = 0
    LOSSLESS = 1
    ANALYTICS = 2
    ANALYTICS_V2 = 3

  recursiveStructureDepth = _messages.IntegerField(1)
  schemaType = _messages.EnumField('SchemaTypeValueValuesEnum', 2)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class Signature(_messages.Message):
  r"""User signature.

  Messages:
    MetadataValue: Optional. Metadata associated with the user's signature.
      For example, the user's name or the user's title.

  Fields:
    image: Optional. An image of the user's signature.
    metadata: Optional. Metadata associated with the user's signature. For
      example, the user's name or the user's title.
    signatureTime: Optional. Timestamp of the signature.
    userId: Required. User's UUID provided by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Optional. Metadata associated with the user's signature. For example,
    the user's name or the user's title.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Additional properties of type MetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  image = _messages.MessageField('Image', 1)
  metadata = _messages.MessageField('MetadataValue', 2)
  signatureTime = _messages.StringField(3)
  userId = _messages.StringField(4)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StreamConfig(_messages.Message):
  r"""Contains configuration for streaming FHIR export.

  Fields:
    bigqueryDestination: The destination BigQuery structure that contains both
      the dataset location and corresponding schema config. The output is
      organized in one table per resource type. The server reuses the existing
      tables (if any) that are named after the resource types, e.g. "Patient",
      "Observation". When there is no existing table for a given resource
      type, the server attempts to create one. When a table schema doesn't
      align with the schema config, either because of existing incompatible
      schema or out of band incompatible modification, the server does not
      stream in new data. One resolution in this case is to delete the
      incompatible table and let the server recreate one, though the newly
      created table only contains data after the table recreation. BigQuery
      imposes a 1 MB limit on streaming insert row size, therefore any
      resource mutation that generates more than 1 MB of BigQuery data will
      not be streamed. Results are written to BigQuery tables according to the
      parameters in BigQueryDestination.WriteDisposition. Different versions
      of the same resource are distinguishable by the meta.versionId and
      meta.lastUpdated columns. The operation (CREATE/UPDATE/DELETE) that
      results in the new version is recorded in the meta.tag. The tables
      contain all historical resource versions since streaming was enabled.
      For query convenience, the server also creates one view per table of the
      same name containing only the current resource version. The streamed
      data in the BigQuery dataset is not guaranteed to be completely unique.
      The combination of the id and meta.versionId columns should ideally
      identify a single unique row. But in rare cases, duplicates may exist.
      At query time, users may use the SQL select statement to keep only one
      of the duplicate rows given an id and meta.versionId pair.
      Alternatively, the server created view mentioned above also filters out
      duplicates. Before adding this configuration, you must add the
      [`bigquery.dataEditor`](https://cloud.google.com/bigquery/docs/access-
      control#bigquery.dataEditor) role to your project's **Cloud Healthcare
      Service Agent** [service
      account](https://cloud.google.com/iam/docs/service-accounts). If a
      resource mutation cannot be streamed to BigQuery, errors will be logged
      to Cloud Logging (see [Viewing error logs in Cloud
      Logging](https://cloud.google.com/healthcare/docs/how-tos/logging)).
    deidentifiedStoreDestination: The destination FHIR store for de-identified
      resources. After this field is added, all subsequent
      creates/updates/patches to the source store will be de-identified using
      the provided configuration and applied to the destination store.
      Resources deleted from the source store will be deleted from the
      destination store. Importing resources to the source store will not
      trigger the streaming. If the source store already contains resources
      when this option is enabled, those resources will not be copied to the
      destination store unless they are subsequently updated. This may result
      in invalid references in the destination store. Before adding this
      config, you must grant the healthcare.fhirResources.update permission on
      the destination store to your project's **Cloud Healthcare Service
      Agent** [service account](https://cloud.google.com/healthcare/docs/how-
      tos/permissions-healthcare-api-gcp-
      products#the_cloud_healthcare_service_agent). The destination store must
      set `enable_update_create` to true. The destination store must have
      `disable_referential_integrity` set to true. If a resource cannot be de-
      identified, errors will be logged to Cloud Logging (see [Viewing error
      logs in Cloud Logging](https://cloud.google.com/healthcare/docs/how-
      tos/cloud-logging)). Not supported for R5 stores.
    resourceTypes: Supply a FHIR resource type (such as "Patient" or
      "Observation"). See https://www.hl7.org/fhir/valueset-resource-
      types.html for a list of all FHIR resource types. The server treats an
      empty list as an intent to stream all the supported resource types in
      this FHIR store.
  """

  bigqueryDestination = _messages.MessageField('GoogleCloudHealthcareV1alpha2FhirBigQueryDestination', 1)
  deidentifiedStoreDestination = _messages.MessageField('DeidentifiedStoreDestination', 2)
  resourceTypes = _messages.StringField(3, repeated=True)


class TagFilterList(_messages.Message):
  r"""List of tags to filter.

  Fields:
    tags: Tags to filter. Tags must be DICOM Data Elements, File Meta
      Elements, or Directory Structuring Elements, as defined in the [Registry
      of DICOM Data Elements] (http://dicom.nema.org/medical/dicom/current/out
      put/html/part06.html#table_6-1). They can be provided by "Keyword" or
      "Tag". For example, "PatientID", "00100010".
  """

  tags = _messages.StringField(1, repeated=True)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TextConfig(_messages.Message):
  r"""Configures how to transform sensitive text `InfoTypes`.

  Fields:
    transformations: The transformations to apply to the detected data.
      Deprecated. Use `additional_transformations` instead.
  """

  transformations = _messages.MessageField('InfoTypeTransformation', 1, repeated=True)


class TextSpan(_messages.Message):
  r"""A span of text in the provided document.

  Fields:
    beginOffset: The unicode codepoint index of the beginning of this span.
    content: The original text contained in this span.
  """

  beginOffset = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  content = _messages.StringField(2)


class UserDataMapping(_messages.Message):
  r"""Maps a resource to the associated user and Attributes.

  Fields:
    archiveTime: Output only. Indicates the time when this mapping was
      archived.
    archived: Output only. Indicates whether this mapping is archived.
    dataId: Required. A unique identifier for the mapped resource.
    name: Resource name of the User data mapping, of the form `projects/{proje
      ct_id}/locations/{location_id}/datasets/{dataset_id}/consentStores/{cons
      ent_store_id}/userDataMappings/{user_data_mapping_id}`.
    resourceAttributes: Attributes of the resource. Only explicitly set
      attributes are displayed here. Attribute definitions with defaults set
      implicitly apply to these User data mappings. Attributes listed here
      must be single valued, that is, exactly one value is specified for the
      field "values" in each Attribute.
    userId: Required. User's UUID provided by the client.
  """

  archiveTime = _messages.StringField(1)
  archived = _messages.BooleanField(2)
  dataId = _messages.StringField(3)
  name = _messages.StringField(4)
  resourceAttributes = _messages.MessageField('Attribute', 5, repeated=True)
  userId = _messages.StringField(6)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    HealthcareProjectsLocationsDatasetsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    HealthcareProjectsLocationsDatasetsConsentStoresGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    HealthcareProjectsLocationsDatasetsDataMapperWorkspacesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    HealthcareProjectsLocationsDatasetsDicomStoresGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    HealthcareProjectsLocationsDatasetsFhirStoresGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    HealthcareProjectsLocationsDatasetsHl7V2StoresGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
