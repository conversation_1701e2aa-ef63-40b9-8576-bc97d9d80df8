"""Generated message classes for firebasedataconnect version v1alpha.

Firebase Data Connect is a relational database service for mobile and web apps
that lets you build and scale using a fully-managed PostgreSQL database
powered by Cloud SQL. The REST API lets developers manage the connections to
their database, change the schema of their database, and query the database.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'firebasedataconnect'


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CloudSqlInstance(_messages.Message):
  r"""Settings for CloudSQL instance configuration.

  Fields:
    instance: Required. Name of the CloudSQL instance, in the format: ```
      projects/{project}/locations/{location}/instances/{instance} ```
  """

  instance = _messages.StringField(1)


class Connector(_messages.Message):
  r"""Connector consists of a set of operations, i.e. queries and mutations.

  Messages:
    AnnotationsValue: Optional. Stores small amounts of arbitrary data.
    LabelsValue: Optional. Labels as key value pairs.

  Fields:
    annotations: Optional. Stores small amounts of arbitrary data.
    createTime: Output only. [Output only] Create time stamp.
    displayName: Optional. Mutable human-readable name. 63 character limit.
    etag: Output only. This checksum is computed by the server based on the
      value of other fields, and may be sent on update and delete requests to
      ensure the client has an up-to-date value before proceeding.
      [AIP-154](https://google.aip.dev/154)
    labels: Optional. Labels as key value pairs.
    name: Identifier. The relative resource name of the connector, in the
      format: ``` projects/{project}/locations/{location}/services/{service}/c
      onnectors/{connector} ```
    reconciling: Output only. A field that if true, indicates that the system
      is working to compile and deploy the connector.
    source: Required. The source files that comprise the connector.
    uid: Output only. System-assigned, unique identifier.
    updateTime: Output only. [Output only] Update time stamp.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Stores small amounts of arbitrary data.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  displayName = _messages.StringField(3)
  etag = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  reconciling = _messages.BooleanField(7)
  source = _messages.MessageField('Source', 8)
  uid = _messages.StringField(9)
  updateTime = _messages.StringField(10)


class Datasource(_messages.Message):
  r"""A data source that backs Firebase Data Connect services.

  Fields:
    postgresql: PostgreSQL configurations.
  """

  postgresql = _messages.MessageField('PostgreSql', 1)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class ExecuteMutationRequest(_messages.Message):
  r"""The ExecuteMutation request to Firebase Data Connect.

  Messages:
    VariablesValue: Optional. Values for GraphQL variables provided in this
      request.

  Fields:
    operationName: Required. The name of the GraphQL operation name. Required
      because all Connector operations must be named. See
      https://graphql.org/learn/queries/#operation-name.
    variables: Optional. Values for GraphQL variables provided in this
      request.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class VariablesValue(_messages.Message):
    r"""Optional. Values for GraphQL variables provided in this request.

    Messages:
      AdditionalProperty: An additional property for a VariablesValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a VariablesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  operationName = _messages.StringField(1)
  variables = _messages.MessageField('VariablesValue', 2)


class ExecuteMutationResponse(_messages.Message):
  r"""The ExecuteMutation response from Firebase Data Connect.

  Messages:
    DataValue: The result of executing the requested operation.

  Fields:
    data: The result of executing the requested operation.
    errors: Errors of this response.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DataValue(_messages.Message):
    r"""The result of executing the requested operation.

    Messages:
      AdditionalProperty: An additional property for a DataValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  data = _messages.MessageField('DataValue', 1)
  errors = _messages.MessageField('GraphqlError', 2, repeated=True)


class ExecuteQueryRequest(_messages.Message):
  r"""The ExecuteQuery request to Firebase Data Connect.

  Messages:
    VariablesValue: Optional. Values for GraphQL variables provided in this
      request.

  Fields:
    operationName: Required. The name of the GraphQL operation name. Required
      because all Connector operations must be named. See
      https://graphql.org/learn/queries/#operation-name.
    variables: Optional. Values for GraphQL variables provided in this
      request.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class VariablesValue(_messages.Message):
    r"""Optional. Values for GraphQL variables provided in this request.

    Messages:
      AdditionalProperty: An additional property for a VariablesValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a VariablesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  operationName = _messages.StringField(1)
  variables = _messages.MessageField('VariablesValue', 2)


class ExecuteQueryResponse(_messages.Message):
  r"""The ExecuteQuery response from Firebase Data Connect.

  Messages:
    DataValue: The result of executing the requested operation.

  Fields:
    data: The result of executing the requested operation.
    errors: Errors of this response.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DataValue(_messages.Message):
    r"""The result of executing the requested operation.

    Messages:
      AdditionalProperty: An additional property for a DataValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  data = _messages.MessageField('DataValue', 1)
  errors = _messages.MessageField('GraphqlError', 2, repeated=True)


class File(_messages.Message):
  r"""Individual files.

  Fields:
    content: Required. The file's textual content.
    path: Required. The file name including folder path, if applicable. The
      path should be relative to a local workspace (e.g.
      dataconnect/(schema|connector)/*.gql) and not an absolute path (e.g.
      /absolute/path/(schema|connector)/*.gql).
  """

  content = _messages.StringField(1)
  path = _messages.StringField(2)


class FirebasedataconnectProjectsLocationsGetRequest(_messages.Message):
  r"""A FirebasedataconnectProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class FirebasedataconnectProjectsLocationsListRequest(_messages.Message):
  r"""A FirebasedataconnectProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class FirebasedataconnectProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A FirebasedataconnectProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class FirebasedataconnectProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A FirebasedataconnectProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class FirebasedataconnectProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A FirebasedataconnectProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class FirebasedataconnectProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A FirebasedataconnectProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class FirebasedataconnectProjectsLocationsServicesConnectorsCreateRequest(_messages.Message):
  r"""A FirebasedataconnectProjectsLocationsServicesConnectorsCreateRequest
  object.

  Fields:
    connector: A Connector resource to be passed as the request body.
    connectorId: Required. The ID to use for the connector, which will become
      the final component of the connector's resource name.
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, validate the request and preview the
      Connector, but do not actually create it.
  """

  connector = _messages.MessageField('Connector', 1)
  connectorId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class FirebasedataconnectProjectsLocationsServicesConnectorsDeleteRequest(_messages.Message):
  r"""A FirebasedataconnectProjectsLocationsServicesConnectorsDeleteRequest
  object.

  Fields:
    allowMissing: Optional. If true and the Connector is not found, the
      request will succeed but no action will be taken on the server.
    etag: Optional. The etag of the Connector. If this is provided, it must
      match the server's etag.
    force: Optional. If set to true, any child resources (i.e.
      ConnectorRevisions) will also be deleted. Otherwise, the request will
      only work if the Connector has no child resources.
    name: Required. The name of the connector to delete, in the format: ``` pr
      ojects/{project}/locations/{location}/services/{service}/connectors/{con
      nector} ```
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, validate the request and preview the
      Connector, but do not actually delete it.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  force = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  requestId = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class FirebasedataconnectProjectsLocationsServicesConnectorsExecuteMutationRequest(_messages.Message):
  r"""A
  FirebasedataconnectProjectsLocationsServicesConnectorsExecuteMutationRequest
  object.

  Fields:
    executeMutationRequest: A ExecuteMutationRequest resource to be passed as
      the request body.
    name: Required. The resource name of the connector to find the predefined
      mutation, in the format: ``` projects/{project}/locations/{location}/ser
      vices/{service}/connectors/{connector} ```
  """

  executeMutationRequest = _messages.MessageField('ExecuteMutationRequest', 1)
  name = _messages.StringField(2, required=True)


class FirebasedataconnectProjectsLocationsServicesConnectorsExecuteQueryRequest(_messages.Message):
  r"""A
  FirebasedataconnectProjectsLocationsServicesConnectorsExecuteQueryRequest
  object.

  Fields:
    executeQueryRequest: A ExecuteQueryRequest resource to be passed as the
      request body.
    name: Required. The resource name of the connector to find the predefined
      query, in the format: ``` projects/{project}/locations/{location}/servic
      es/{service}/connectors/{connector} ```
  """

  executeQueryRequest = _messages.MessageField('ExecuteQueryRequest', 1)
  name = _messages.StringField(2, required=True)


class FirebasedataconnectProjectsLocationsServicesConnectorsGetRequest(_messages.Message):
  r"""A FirebasedataconnectProjectsLocationsServicesConnectorsGetRequest
  object.

  Fields:
    name: Required. The name of the connector to retrieve, in the format: ```
      projects/{project}/locations/{location}/services/{service}/connectors/{c
      onnector} ```
  """

  name = _messages.StringField(1, required=True)


class FirebasedataconnectProjectsLocationsServicesConnectorsListRequest(_messages.Message):
  r"""A FirebasedataconnectProjectsLocationsServicesConnectorsListRequest
  object.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint for how to order the results.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A page token, received from a previous
      `ListConnectors` call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters provided to `ListConnectors` must
      match the call that provided the page token.
    parent: Required. Value of parent.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class FirebasedataconnectProjectsLocationsServicesConnectorsPatchRequest(_messages.Message):
  r"""A FirebasedataconnectProjectsLocationsServicesConnectorsPatchRequest
  object.

  Fields:
    allowMissing: Optional. If true and the Connector is not found, a new
      Connector will be created. In this case, `update_mask` is ignored.
    connector: A Connector resource to be passed as the request body.
    name: Identifier. The relative resource name of the connector, in the
      format: ``` projects/{project}/locations/{location}/services/{service}/c
      onnectors/{connector} ```
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the Connector resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
    validateOnly: Optional. If set, validate the request and preview the
      Connector, but do not actually update it.
  """

  allowMissing = _messages.BooleanField(1)
  connector = _messages.MessageField('Connector', 2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class FirebasedataconnectProjectsLocationsServicesCreateRequest(_messages.Message):
  r"""A FirebasedataconnectProjectsLocationsServicesCreateRequest object.

  Fields:
    parent: Required. Value of parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    service: A Service resource to be passed as the request body.
    serviceId: Required. The ID to use for the service, which will become the
      final component of the service's resource name.
    validateOnly: Optional. If set, validate the request and preview the
      Service, but do not actually create it.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  service = _messages.MessageField('Service', 3)
  serviceId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class FirebasedataconnectProjectsLocationsServicesDeleteRequest(_messages.Message):
  r"""A FirebasedataconnectProjectsLocationsServicesDeleteRequest object.

  Fields:
    allowMissing: Optional. If true and the Service is not found, the request
      will succeed but no action will be taken on the server.
    etag: Optional. The etag of the Service. If this is provided, it must
      match the server's etag.
    force: Optional. If set to true, any child resources (i.e. Schema,
      SchemaRevisions, Connectors, and ConnectorRevisions) will also be
      deleted. Otherwise, the request will only work if the Service has no
      child resources.
    name: Required. The name of the service to delete, in the format: ```
      projects/{project}/locations/{location}/services/{service} ```
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, validate the request and preview the
      Service, but do not actually delete it.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  force = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  requestId = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class FirebasedataconnectProjectsLocationsServicesExecuteGraphqlReadRequest(_messages.Message):
  r"""A FirebasedataconnectProjectsLocationsServicesExecuteGraphqlReadRequest
  object.

  Fields:
    graphqlRequest: A GraphqlRequest resource to be passed as the request
      body.
    name: Required. The relative resource name of Firebase Data Connect
      service, in the format: ```
      projects/{project}/locations/{location}/services/{service} ```
  """

  graphqlRequest = _messages.MessageField('GraphqlRequest', 1)
  name = _messages.StringField(2, required=True)


class FirebasedataconnectProjectsLocationsServicesExecuteGraphqlRequest(_messages.Message):
  r"""A FirebasedataconnectProjectsLocationsServicesExecuteGraphqlRequest
  object.

  Fields:
    graphqlRequest: A GraphqlRequest resource to be passed as the request
      body.
    name: Required. The relative resource name of Firebase Data Connect
      service, in the format: ```
      projects/{project}/locations/{location}/services/{service} ```
  """

  graphqlRequest = _messages.MessageField('GraphqlRequest', 1)
  name = _messages.StringField(2, required=True)


class FirebasedataconnectProjectsLocationsServicesGetRequest(_messages.Message):
  r"""A FirebasedataconnectProjectsLocationsServicesGetRequest object.

  Fields:
    name: Required. The name of the service to retrieve, in the format: ```
      projects/{project}/locations/{location}/services/{service} ```
  """

  name = _messages.StringField(1, required=True)


class FirebasedataconnectProjectsLocationsServicesListRequest(_messages.Message):
  r"""A FirebasedataconnectProjectsLocationsServicesListRequest object.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint for how to order the results.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A page token, received from a previous `ListServices`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListServices` must match the call that
      provided the page token.
    parent: Required. Value of parent.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class FirebasedataconnectProjectsLocationsServicesPatchRequest(_messages.Message):
  r"""A FirebasedataconnectProjectsLocationsServicesPatchRequest object.

  Fields:
    allowMissing: Optional. If true and the Service is not found, a new
      Service will be created. In this case, `update_mask` is ignored.
    name: Identifier. The relative resource name of the Firebase Data Connect
      service, in the format: ```
      projects/{project}/locations/{location}/services/{service} ``` Note that
      the service ID is specific to Firebase Data Connect and does not
      correspond to any of the instance IDs of the underlying data source
      connections.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    service: A Service resource to be passed as the request body.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the Service resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
    validateOnly: Optional. If set, validate the request and preview the
      Service, but do not actually update it.
  """

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  service = _messages.MessageField('Service', 4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class FirebasedataconnectProjectsLocationsServicesSchemasCreateRequest(_messages.Message):
  r"""A FirebasedataconnectProjectsLocationsServicesSchemasCreateRequest
  object.

  Fields:
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    schema: A Schema resource to be passed as the request body.
    schemaId: Required. The ID to use for the schema, which will become the
      final component of the schema's resource name. Currently, only `main` is
      supported and any other schema ID will result in an error.
    validateOnly: Optional. If set, validate the request and preview the
      Schema, but do not actually update it.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  schema = _messages.MessageField('Schema', 3)
  schemaId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class FirebasedataconnectProjectsLocationsServicesSchemasDeleteRequest(_messages.Message):
  r"""A FirebasedataconnectProjectsLocationsServicesSchemasDeleteRequest
  object.

  Fields:
    allowMissing: Optional. If true and the Schema is not found, the request
      will succeed but no action will be taken on the server.
    etag: Optional. The etag of the Schema. If this is provided, it must match
      the server's etag.
    force: Optional. If set to true, any child resources (i.e.
      SchemaRevisions) will also be deleted.
    name: Required. The name of the schema to delete, in the format: ``` proje
      cts/{project}/locations/{location}/services/{service}/schemas/{schema}
      ```
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, validate the request and preview the
      Schema, but do not actually delete it.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  force = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  requestId = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class FirebasedataconnectProjectsLocationsServicesSchemasGetRequest(_messages.Message):
  r"""A FirebasedataconnectProjectsLocationsServicesSchemasGetRequest object.

  Fields:
    name: Required. The name of the schema to retrieve, in the format: ``` pro
      jects/{project}/locations/{location}/services/{service}/schemas/{schema}
      ```
  """

  name = _messages.StringField(1, required=True)


class FirebasedataconnectProjectsLocationsServicesSchemasListRequest(_messages.Message):
  r"""A FirebasedataconnectProjectsLocationsServicesSchemasListRequest object.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint for how to order the results.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A page token, received from a previous `ListSchemas`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListSchemas` must match the call that
      provided the page token.
    parent: Required. Value of parent.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class FirebasedataconnectProjectsLocationsServicesSchemasPatchRequest(_messages.Message):
  r"""A FirebasedataconnectProjectsLocationsServicesSchemasPatchRequest
  object.

  Fields:
    allowMissing: Optional. If true and the Schema is not found, a new Schema
      will be created. In this case, `update_mask` is ignored.
    name: Identifier. The relative resource name of the schema, in the format:
      ``` projects/{project}/locations/{location}/services/{service}/schemas/{
      schema} ``` Right now, the only supported schema is "main".
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    schema: A Schema resource to be passed as the request body.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the Schema resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
    validateOnly: Optional. If set, validate the request and preview the
      Schema, but do not actually update it.
  """

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  schema = _messages.MessageField('Schema', 4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class GraphqlError(_messages.Message):
  r"""GraphqlError conforms to the GraphQL error spec.
  https://spec.graphql.org/draft/#sec-Errors Firebase Data Connect API
  surfaces `GraphqlError` in various APIs: - Upon compile error,
  `UpdateSchema` and `UpdateConnector` return Code.Invalid_Argument with a
  list of `GraphqlError` in error details. - Upon query compile error,
  `ExecuteGraphql` and `ExecuteGraphqlRead` return Code.OK with a list of
  `GraphqlError` in response body. - Upon query execution error,
  `ExecuteGraphql`, `ExecuteGraphqlRead`, `ExecuteMutation` and `ExecuteQuery`
  all return Code.OK with a list of `GraphqlError` in response body.

  Fields:
    extensions: Additional error information.
    locations: The source locations where the error occurred. Locations should
      help developers and toolings identify the source of error quickly.
      Included in admin endpoints (`ExecuteGraphql`, `ExecuteGraphqlRead`,
      `UpdateSchema` and `UpdateConnector`) to reference the provided GraphQL
      GQL document. Omitted in `ExecuteMutation` and `ExecuteQuery` since the
      caller shouldn't have access access the underlying GQL source.
    message: The detailed error message. The message should help developer
      understand the underlying problem without leaking internal data.
    path: The result field which could not be populated due to error. Clients
      can use path to identify whether a null result is intentional or caused
      by a runtime error. It should be a list of string or index from the root
      of GraphQL query document.
  """

  extensions = _messages.MessageField('GraphqlErrorExtensions', 1)
  locations = _messages.MessageField('SourceLocation', 2, repeated=True)
  message = _messages.StringField(3)
  path = _messages.MessageField('extra_types.JsonValue', 4, repeated=True)


class GraphqlErrorExtensions(_messages.Message):
  r"""GraphqlErrorExtensions contains additional information of
  `GraphqlError`.

  Enums:
    CodeValueValuesEnum: Maps to canonical gRPC codes. If not specified, it
      represents `Code.INTERNAL`.

  Fields:
    code: Maps to canonical gRPC codes. If not specified, it represents
      `Code.INTERNAL`.
    debugDetails: More detailed error message to assist debugging. It contains
      application business logic that are inappropriate to leak publicly. In
      the emulator, Data Connect API always includes it to assist local
      development and debugging. In the backend, ConnectorService always hides
      it. GraphqlService without impersonation always include it.
      GraphqlService with impersonation includes it only if explicitly opted-
      in with `include_debug_details` in `GraphqlRequestExtensions`.
    file: The source file name where the error occurred. Included only for
      `UpdateSchema` and `UpdateConnector`, it corresponds to `File.path` of
      the provided `Source`.
    resource: Distinguish which schema or connector the error originates from.
      It should be set on errors from control plane APIs (e.g. `UpdateSchema`,
      `UpdateConnector`).
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Maps to canonical gRPC codes. If not specified, it represents
    `Code.INTERNAL`.

    Values:
      OK: Not an error; returned on success. HTTP Mapping: 200 OK
      CANCELLED: The operation was cancelled, typically by the caller. HTTP
        Mapping: 499 Client Closed Request
      UNKNOWN: Unknown error. For example, this error may be returned when a
        `Status` value received from another address space belongs to an error
        space that is not known in this address space. Also errors raised by
        APIs that do not return enough error information may be converted to
        this error. HTTP Mapping: 500 Internal Server Error
      INVALID_ARGUMENT: The client specified an invalid argument. Note that
        this differs from `FAILED_PRECONDITION`. `INVALID_ARGUMENT` indicates
        arguments that are problematic regardless of the state of the system
        (e.g., a malformed file name). HTTP Mapping: 400 Bad Request
      DEADLINE_EXCEEDED: The deadline expired before the operation could
        complete. For operations that change the state of the system, this
        error may be returned even if the operation has completed
        successfully. For example, a successful response from a server could
        have been delayed long enough for the deadline to expire. HTTP
        Mapping: 504 Gateway Timeout
      NOT_FOUND: Some requested entity (e.g., file or directory) was not
        found. Note to server developers: if a request is denied for an entire
        class of users, such as gradual feature rollout or undocumented
        allowlist, `NOT_FOUND` may be used. If a request is denied for some
        users within a class of users, such as user-based access control,
        `PERMISSION_DENIED` must be used. HTTP Mapping: 404 Not Found
      ALREADY_EXISTS: The entity that a client attempted to create (e.g., file
        or directory) already exists. HTTP Mapping: 409 Conflict
      PERMISSION_DENIED: The caller does not have permission to execute the
        specified operation. `PERMISSION_DENIED` must not be used for
        rejections caused by exhausting some resource (use
        `RESOURCE_EXHAUSTED` instead for those errors). `PERMISSION_DENIED`
        must not be used if the caller can not be identified (use
        `UNAUTHENTICATED` instead for those errors). This error code does not
        imply the request is valid or the requested entity exists or satisfies
        other pre-conditions. HTTP Mapping: 403 Forbidden
      UNAUTHENTICATED: The request does not have valid authentication
        credentials for the operation. HTTP Mapping: 401 Unauthorized
      RESOURCE_EXHAUSTED: Some resource has been exhausted, perhaps a per-user
        quota, or perhaps the entire file system is out of space. HTTP
        Mapping: 429 Too Many Requests
      FAILED_PRECONDITION: The operation was rejected because the system is
        not in a state required for the operation's execution. For example,
        the directory to be deleted is non-empty, an rmdir operation is
        applied to a non-directory, etc. Service implementors can use the
        following guidelines to decide between `FAILED_PRECONDITION`,
        `ABORTED`, and `UNAVAILABLE`: (a) Use `UNAVAILABLE` if the client can
        retry just the failing call. (b) Use `ABORTED` if the client should
        retry at a higher level. For example, when a client-specified test-
        and-set fails, indicating the client should restart a read-modify-
        write sequence. (c) Use `FAILED_PRECONDITION` if the client should not
        retry until the system state has been explicitly fixed. For example,
        if an "rmdir" fails because the directory is non-empty,
        `FAILED_PRECONDITION` should be returned since the client should not
        retry unless the files are deleted from the directory. HTTP Mapping:
        400 Bad Request
      ABORTED: The operation was aborted, typically due to a concurrency issue
        such as a sequencer check failure or transaction abort. See the
        guidelines above for deciding between `FAILED_PRECONDITION`,
        `ABORTED`, and `UNAVAILABLE`. HTTP Mapping: 409 Conflict
      OUT_OF_RANGE: The operation was attempted past the valid range. E.g.,
        seeking or reading past end-of-file. Unlike `INVALID_ARGUMENT`, this
        error indicates a problem that may be fixed if the system state
        changes. For example, a 32-bit file system will generate
        `INVALID_ARGUMENT` if asked to read at an offset that is not in the
        range [0,2^32-1], but it will generate `OUT_OF_RANGE` if asked to read
        from an offset past the current file size. There is a fair bit of
        overlap between `FAILED_PRECONDITION` and `OUT_OF_RANGE`. We recommend
        using `OUT_OF_RANGE` (the more specific error) when it applies so that
        callers who are iterating through a space can easily look for an
        `OUT_OF_RANGE` error to detect when they are done. HTTP Mapping: 400
        Bad Request
      UNIMPLEMENTED: The operation is not implemented or is not
        supported/enabled in this service. HTTP Mapping: 501 Not Implemented
      INTERNAL: Internal errors. This means that some invariants expected by
        the underlying system have been broken. This error code is reserved
        for serious errors. HTTP Mapping: 500 Internal Server Error
      UNAVAILABLE: The service is currently unavailable. This is most likely a
        transient condition, which can be corrected by retrying with a
        backoff. Note that it is not always safe to retry non-idempotent
        operations. See the guidelines above for deciding between
        `FAILED_PRECONDITION`, `ABORTED`, and `UNAVAILABLE`. HTTP Mapping: 503
        Service Unavailable
      DATA_LOSS: Unrecoverable data loss or corruption. HTTP Mapping: 500
        Internal Server Error
    """
    OK = 0
    CANCELLED = 1
    UNKNOWN = 2
    INVALID_ARGUMENT = 3
    DEADLINE_EXCEEDED = 4
    NOT_FOUND = 5
    ALREADY_EXISTS = 6
    PERMISSION_DENIED = 7
    UNAUTHENTICATED = 8
    RESOURCE_EXHAUSTED = 9
    FAILED_PRECONDITION = 10
    ABORTED = 11
    OUT_OF_RANGE = 12
    UNIMPLEMENTED = 13
    INTERNAL = 14
    UNAVAILABLE = 15
    DATA_LOSS = 16

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  debugDetails = _messages.StringField(2)
  file = _messages.StringField(3)
  resource = _messages.StringField(4)


class GraphqlRequest(_messages.Message):
  r"""The GraphQL request to Firebase Data Connect. It strives to match the
  GraphQL over HTTP spec. https://github.com/graphql/graphql-over-
  http/blob/main/spec/GraphQLOverHTTP.md#post

  Messages:
    VariablesValue: Optional. Values for GraphQL variables provided in this
      request.

  Fields:
    extensions: Optional. Additional GraphQL request information.
    operationName: Optional. The name of the GraphQL operation name. Required
      only if `query` contains multiple operations. See
      https://graphql.org/learn/queries/#operation-name.
    query: Required. The GraphQL query document source.
    variables: Optional. Values for GraphQL variables provided in this
      request.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class VariablesValue(_messages.Message):
    r"""Optional. Values for GraphQL variables provided in this request.

    Messages:
      AdditionalProperty: An additional property for a VariablesValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a VariablesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  extensions = _messages.MessageField('GraphqlRequestExtensions', 1)
  operationName = _messages.StringField(2)
  query = _messages.StringField(3)
  variables = _messages.MessageField('VariablesValue', 4)


class GraphqlRequestExtensions(_messages.Message):
  r"""GraphqlRequestExtensions contains additional information of
  `GraphqlRequest`.

  Fields:
    impersonate: Optional. If set, impersonate a request with given Firebase
      Auth context and evaluate the auth policies on the operation. If
      omitted, bypass any defined auth policies.
  """

  impersonate = _messages.MessageField('Impersonation', 1)


class GraphqlResponse(_messages.Message):
  r"""The GraphQL response from Firebase Data Connect. It strives to match the
  GraphQL over HTTP spec. Note: Firebase Data Connect always responds with
  `Content-Type: application/json`. https://github.com/graphql/graphql-over-
  http/blob/main/spec/GraphQLOverHTTP.md#body

  Messages:
    DataValue: The result of the execution of the requested operation. If an
      error was raised before execution begins, the data entry should not be
      present in the result. (a request error:
      https://spec.graphql.org/draft/#sec-Errors.Request-Errors) If an error
      was raised during the execution that prevented a valid response, the
      data entry in the response should be null. (a field error:
      https://spec.graphql.org/draft/#sec-Errors.Error-Result-Format)

  Fields:
    data: The result of the execution of the requested operation. If an error
      was raised before execution begins, the data entry should not be present
      in the result. (a request error: https://spec.graphql.org/draft/#sec-
      Errors.Request-Errors) If an error was raised during the execution that
      prevented a valid response, the data entry in the response should be
      null. (a field error: https://spec.graphql.org/draft/#sec-Errors.Error-
      Result-Format)
    errors: Errors of this response. If the data entry in the response is not
      present, the errors entry must be present. It conforms to
      https://spec.graphql.org/draft/#sec-Errors.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DataValue(_messages.Message):
    r"""The result of the execution of the requested operation. If an error
    was raised before execution begins, the data entry should not be present
    in the result. (a request error: https://spec.graphql.org/draft/#sec-
    Errors.Request-Errors) If an error was raised during the execution that
    prevented a valid response, the data entry in the response should be null.
    (a field error: https://spec.graphql.org/draft/#sec-Errors.Error-Result-
    Format)

    Messages:
      AdditionalProperty: An additional property for a DataValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  data = _messages.MessageField('DataValue', 1)
  errors = _messages.MessageField('GraphqlError', 2, repeated=True)


class Impersonation(_messages.Message):
  r"""Impersonation configures the Firebase Auth context to impersonate.

  Messages:
    AuthClaimsValue: Evaluate the auth policy with a customized JWT auth
      token. Should follow the Firebase Auth token format.
      https://firebase.google.com/docs/rules/rules-and-auth For example: a
      verified user may have auth_claims of {"sub": , "email_verified": true}

  Fields:
    authClaims: Evaluate the auth policy with a customized JWT auth token.
      Should follow the Firebase Auth token format.
      https://firebase.google.com/docs/rules/rules-and-auth For example: a
      verified user may have auth_claims of {"sub": , "email_verified": true}
    includeDebugDetails: Optional. If set, include debug details in GraphQL
      error extensions.
    unauthenticated: Evaluate the auth policy as an unauthenticated request.
      Can only be set to true.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AuthClaimsValue(_messages.Message):
    r"""Evaluate the auth policy with a customized JWT auth token. Should
    follow the Firebase Auth token format.
    https://firebase.google.com/docs/rules/rules-and-auth For example: a
    verified user may have auth_claims of {"sub": , "email_verified": true}

    Messages:
      AdditionalProperty: An additional property for a AuthClaimsValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AuthClaimsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  authClaims = _messages.MessageField('AuthClaimsValue', 1)
  includeDebugDetails = _messages.BooleanField(2)
  unauthenticated = _messages.BooleanField(3)


class ListConnectorsResponse(_messages.Message):
  r"""Message for response to listing Connectors. By default,
  `connectors.source` will not be included in the response. To specify the
  fields included in the response, the response field mask can be provided by
  using the query parameter `$fields` or the header `X-Goog-FieldMask`.

  Fields:
    connectors: The list of Connectors.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached.
  """

  connectors = _messages.MessageField('Connector', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListSchemasResponse(_messages.Message):
  r"""Message for response to listing Schemas. By default, `schemas.source`
  will not be included in the response. To specify the fields included in the
  response, the response field mask can be provided by using the query
  parameter `$fields` or the header `X-Goog-FieldMask`.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    schemas: The list of Schemas.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  schemas = _messages.MessageField('Schema', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListServicesResponse(_messages.Message):
  r"""Message for response to listing Services.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    services: The list of Services.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  services = _messages.MessageField('Service', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation. Note: This
  message is auto-generated by CCFE. CCFE's storage, called Resource Metadata
  Store (RMS), holds metadata about long-running operations (i.e.
  OperationMetadata) and resources (i.e. ResourceMetadata). OperationMetadata
  documents the status of the operation. See [CCFE documentation for
  sidechannel data](https://g3doc.corp.google.com/cloud/control2/g3doc/dev/cod
  elab_extras/sidechannel.md?cl=head#sidechannel-data) and
  yaqs/4289526912465764352.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class PostgreSql(_messages.Message):
  r"""Settings for PostgreSQL data source.

  Enums:
    SchemaMigrationValueValuesEnum: Optional. Configure how to perform
      Postgresql schema migration.
    SchemaValidationValueValuesEnum: Optional. Configure how much Postgresql
      schema validation to perform.

  Fields:
    cloudSql: Cloud SQL configurations.
    database: Required. Name of the PostgreSQL database.
    schemaMigration: Optional. Configure how to perform Postgresql schema
      migration.
    schemaValidation: Optional. Configure how much Postgresql schema
      validation to perform.
    unlinked: No Postgres data source is linked. If set, don't allow
      `database` and `schema_validation` to be configured.
  """

  class SchemaMigrationValueValuesEnum(_messages.Enum):
    r"""Optional. Configure how to perform Postgresql schema migration.

    Values:
      SQL_SCHEMA_MIGRATION_UNSPECIFIED: Unspecified SQL schema migration.
      MIGRATE_COMPATIBLE: Connect to the SQL database and identify any missing
        SQL resources used in the given Firebase Data Connect Schema.
        Automatically create necessary SQL resources (SQL table, column, etc)
        before deploying the schema. During migration steps, the SQL Schema
        must comply with the previous before_deploy setting in case the
        migration is interrupted. Therefore, the previous before_deploy
        setting must not be `schema_validation=STRICT`.
    """
    SQL_SCHEMA_MIGRATION_UNSPECIFIED = 0
    MIGRATE_COMPATIBLE = 1

  class SchemaValidationValueValuesEnum(_messages.Enum):
    r"""Optional. Configure how much Postgresql schema validation to perform.

    Values:
      SQL_SCHEMA_VALIDATION_UNSPECIFIED: Unspecified SQL schema validation.
        Default to STRICT.
      NONE: Skip no SQL schema validation. Use it with extreme caution.
        CreateSchema or UpdateSchema will succeed even if SQL database is
        unavailable or SQL schema is incompatible. Generated SQL may fail at
        execution time.
      STRICT: Connect to the SQL database and validate that the SQL DDL
        matches the schema exactly. Surface any discrepancies as
        `FAILED_PRECONDITION` with an `IncompatibleSqlSchemaError` error
        detail.
      COMPATIBLE: Connect to the SQL database and validate that the SQL DDL
        has all the SQL resources used in the given Firebase Data Connect
        Schema. Surface any missing resources as `FAILED_PRECONDITION` with an
        `IncompatibleSqlSchemaError` error detail. Succeed even if there are
        unknown tables and columns.
    """
    SQL_SCHEMA_VALIDATION_UNSPECIFIED = 0
    NONE = 1
    STRICT = 2
    COMPATIBLE = 3

  cloudSql = _messages.MessageField('CloudSqlInstance', 1)
  database = _messages.StringField(2)
  schemaMigration = _messages.EnumField('SchemaMigrationValueValuesEnum', 3)
  schemaValidation = _messages.EnumField('SchemaValidationValueValuesEnum', 4)
  unlinked = _messages.BooleanField(5)


class Schema(_messages.Message):
  r"""The application schema of a Firebase Data Connect service.

  Messages:
    AnnotationsValue: Optional. Stores small amounts of arbitrary data.
    LabelsValue: Optional. Labels as key value pairs.

  Fields:
    annotations: Optional. Stores small amounts of arbitrary data.
    createTime: Output only. [Output only] Create time stamp.
    datasources: Required. The data sources linked in the schema.
    displayName: Optional. Mutable human-readable name. 63 character limit.
    etag: Output only. This checksum is computed by the server based on the
      value of other fields, and may be sent on update and delete requests to
      ensure the client has an up-to-date value before proceeding.
      [AIP-154](https://google.aip.dev/154)
    labels: Optional. Labels as key value pairs.
    name: Identifier. The relative resource name of the schema, in the format:
      ``` projects/{project}/locations/{location}/services/{service}/schemas/{
      schema} ``` Right now, the only supported schema is "main".
    primaryDatasource: Required. The primary / default data source.
    reconciling: Output only. A field that if true, indicates that the system
      is working to compile and deploy the schema.
    source: Required. The source files that comprise the application schema.
    uid: Output only. System-assigned, unique identifier.
    updateTime: Output only. [Output only] Update time stamp.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Stores small amounts of arbitrary data.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  datasources = _messages.MessageField('Datasource', 3, repeated=True)
  displayName = _messages.StringField(4)
  etag = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  primaryDatasource = _messages.MessageField('Datasource', 8)
  reconciling = _messages.BooleanField(9)
  source = _messages.MessageField('Source', 10)
  uid = _messages.StringField(11)
  updateTime = _messages.StringField(12)


class Service(_messages.Message):
  r"""A Firebase Data Connect service.

  Messages:
    AnnotationsValue: Optional. Stores small amounts of arbitrary data.
    LabelsValue: Optional. Labels as key value pairs.

  Fields:
    annotations: Optional. Stores small amounts of arbitrary data.
    createTime: Output only. [Output only] Create time stamp.
    displayName: Optional. Mutable human-readable name. 63 character limit.
    etag: Output only. This checksum is computed by the server based on the
      value of other fields, and may be sent on update and delete requests to
      ensure the client has an up-to-date value before proceeding.
      [AIP-154](https://google.aip.dev/154)
    labels: Optional. Labels as key value pairs.
    name: Identifier. The relative resource name of the Firebase Data Connect
      service, in the format: ```
      projects/{project}/locations/{location}/services/{service} ``` Note that
      the service ID is specific to Firebase Data Connect and does not
      correspond to any of the instance IDs of the underlying data source
      connections.
    reconciling: Output only. A field that if true, indicates that the system
      is working update the service.
    uid: Output only. System-assigned, unique identifier.
    updateTime: Output only. [Output only] Update time stamp.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Stores small amounts of arbitrary data.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  displayName = _messages.StringField(3)
  etag = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  reconciling = _messages.BooleanField(7)
  uid = _messages.StringField(8)
  updateTime = _messages.StringField(9)


class Source(_messages.Message):
  r"""Used to represent a set of source files.

  Fields:
    files: Required. The files that comprise the source set.
  """

  files = _messages.MessageField('File', 1, repeated=True)


class SourceLocation(_messages.Message):
  r"""SourceLocation references a location in a GraphQL source.

  Fields:
    column: Column number starting at 1.
    line: Line number starting at 1.
  """

  column = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  line = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
