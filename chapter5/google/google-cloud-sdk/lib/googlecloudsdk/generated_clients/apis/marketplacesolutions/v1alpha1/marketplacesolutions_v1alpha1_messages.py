"""Generated message classes for marketplacesolutions version v1alpha1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'marketplacesolutions'


class ApplyPowerInstancePowerActionRequest(_messages.Message):
  r"""Message requesting to perform one of several power actions on an
  instance.

  Enums:
    ActionValueValuesEnum: Required. The action to perform on the instance.

  Fields:
    action: Required. The action to perform on the instance.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required. The action to perform on the instance.

    Values:
      ACTION_UNSPECIFIED: No action was specified.
      START: Start the instance.
      STOP: Cleanly shut down the instance.
      SOFT_REBOOT: Cleanly reboot the instance.
      HARD_REBOOT: Hard reboot the instance.
      IMMEDIATE_SHUTDOWN: Immediately shut down the instance.
    """
    ACTION_UNSPECIFIED = 0
    START = 1
    STOP = 2
    SOFT_REBOOT = 3
    HARD_REBOOT = 4
    IMMEDIATE_SHUTDOWN = 5

  action = _messages.EnumField('ActionValueValuesEnum', 1)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListPowerImagesResponse(_messages.Message):
  r"""Response message containing the list of Power images.

  Fields:
    nextPageToken: A token identifying a page of results from the server.
    powerImages: The list of images.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  powerImages = _messages.MessageField('PowerImage', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListPowerInstancesResponse(_messages.Message):
  r"""Response message for the list of Power instances.

  Fields:
    nextPageToken: A token identifying a page of results from the server.
    powerInstances: The list of Power instances.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  powerInstances = _messages.MessageField('PowerInstance', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListPowerNetworksResponse(_messages.Message):
  r"""Response message containing the list of Power networks.

  Fields:
    nextPageToken: A token identifying a page of results from the server.
    powerNetworks: The list of networks.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  powerNetworks = _messages.MessageField('PowerNetwork', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListPowerSSHKeysResponse(_messages.Message):
  r"""Response message containing the list of Power SSH Keys.

  Fields:
    nextPageToken: A token identifying a page of results from the server.
    powerSshKeys: The list of SSH Keys.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  powerSshKeys = _messages.MessageField('PowerSSHKey', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListPowerVolumesResponse(_messages.Message):
  r"""Response message containing the list of Powervolumes.

  Fields:
    nextPageToken: A token identifying a page of results from the server.
    powerVolumes: The list of volumes.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  powerVolumes = _messages.MessageField('PowerVolume', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class MarketplacesolutionsOperationsCancelRequest(_messages.Message):
  r"""A MarketplacesolutionsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class MarketplacesolutionsOperationsDeleteRequest(_messages.Message):
  r"""A MarketplacesolutionsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class MarketplacesolutionsOperationsGetRequest(_messages.Message):
  r"""A MarketplacesolutionsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class MarketplacesolutionsOperationsListRequest(_messages.Message):
  r"""A MarketplacesolutionsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class MarketplacesolutionsProjectsLocationsGetRequest(_messages.Message):
  r"""A MarketplacesolutionsProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class MarketplacesolutionsProjectsLocationsListRequest(_messages.Message):
  r"""A MarketplacesolutionsProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class MarketplacesolutionsProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A MarketplacesolutionsProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class MarketplacesolutionsProjectsLocationsPowerImagesGetRequest(_messages.Message):
  r"""A MarketplacesolutionsProjectsLocationsPowerImagesGetRequest object.

  Fields:
    name: Required. Name of the resource.
  """

  name = _messages.StringField(1, required=True)


class MarketplacesolutionsProjectsLocationsPowerImagesListRequest(_messages.Message):
  r"""A MarketplacesolutionsProjectsLocationsPowerImagesListRequest object.

  Fields:
    filter: Optional. List filter.
    pageSize: Optional. Requested page size. The server might return fewer
      items than requested. If unspecified, server will pick an appropriate
      default.
    pageToken: Optional. A token identifying a page of results from the
      server.
    parent: Required. Parent value for ListPowerImagesRequest.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class MarketplacesolutionsProjectsLocationsPowerInstancesApplyPowerActionRequest(_messages.Message):
  r"""A
  MarketplacesolutionsProjectsLocationsPowerInstancesApplyPowerActionRequest
  object.

  Fields:
    applyPowerInstancePowerActionRequest: A
      ApplyPowerInstancePowerActionRequest resource to be passed as the
      request body.
    name: Required. Name of the resource.
  """

  applyPowerInstancePowerActionRequest = _messages.MessageField('ApplyPowerInstancePowerActionRequest', 1)
  name = _messages.StringField(2, required=True)


class MarketplacesolutionsProjectsLocationsPowerInstancesCreateRequest(_messages.Message):
  r"""A MarketplacesolutionsProjectsLocationsPowerInstancesCreateRequest
  object.

  Fields:
    parent: Required. Parent of the resource.
    powerInstance: A PowerInstance resource to be passed as the request body.
    powerInstanceId: Required. The ID to use for the instance, which will
      become the final component of the instance name. This value should be
      4-63 characters, and valid characters are /a-z[0-9]-_/.
  """

  parent = _messages.StringField(1, required=True)
  powerInstance = _messages.MessageField('PowerInstance', 2)
  powerInstanceId = _messages.StringField(3)


class MarketplacesolutionsProjectsLocationsPowerInstancesDeleteRequest(_messages.Message):
  r"""A MarketplacesolutionsProjectsLocationsPowerInstancesDeleteRequest
  object.

  Fields:
    name: Required. The name of the instance to delete.
  """

  name = _messages.StringField(1, required=True)


class MarketplacesolutionsProjectsLocationsPowerInstancesGetRequest(_messages.Message):
  r"""A MarketplacesolutionsProjectsLocationsPowerInstancesGetRequest object.

  Fields:
    name: Required. Name of the resource.
  """

  name = _messages.StringField(1, required=True)


class MarketplacesolutionsProjectsLocationsPowerInstancesListRequest(_messages.Message):
  r"""A MarketplacesolutionsProjectsLocationsPowerInstancesListRequest object.

  Fields:
    filter: Optional. List filter.
    pageSize: Optional. Requested page size. server may return fewer items
      than requested. If unspecified, the server will pick an appropriate
      default.
    pageToken: Optional. A token identifying a page of results from the
      server.
    parent: Required. Parent of the resource.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class MarketplacesolutionsProjectsLocationsPowerInstancesPatchRequest(_messages.Message):
  r"""A MarketplacesolutionsProjectsLocationsPowerInstancesPatchRequest
  object.

  Fields:
    name: Identifier. The resource name of this PowerInstance. Resource names
      are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. Format: `projects/{
      project}/locations/{location}/powerInstances/{power_instance}`
    powerInstance: A PowerInstance resource to be passed as the request body.
    updateMask: Required. The list of fields to update. The currently
      supported fields are: - 'memory_gib' - 'virtual_cpu_cores'
  """

  name = _messages.StringField(1, required=True)
  powerInstance = _messages.MessageField('PowerInstance', 2)
  updateMask = _messages.StringField(3)


class MarketplacesolutionsProjectsLocationsPowerInstancesResetRequest(_messages.Message):
  r"""A MarketplacesolutionsProjectsLocationsPowerInstancesResetRequest
  object.

  Fields:
    name: Required. Name of the resource.
    resetPowerInstanceRequest: A ResetPowerInstanceRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  resetPowerInstanceRequest = _messages.MessageField('ResetPowerInstanceRequest', 2)


class MarketplacesolutionsProjectsLocationsPowerNetworksGetRequest(_messages.Message):
  r"""A MarketplacesolutionsProjectsLocationsPowerNetworksGetRequest object.

  Fields:
    name: Required. Name of the resource.
  """

  name = _messages.StringField(1, required=True)


class MarketplacesolutionsProjectsLocationsPowerNetworksListRequest(_messages.Message):
  r"""A MarketplacesolutionsProjectsLocationsPowerNetworksListRequest object.

  Fields:
    filter: Optional. List filter.
    pageSize: Optional. Requested page size. The server might return fewer
      items than requested. If unspecified, server will pick an appropriate
      default.
    pageToken: Optional. A token identifying a page of results from the
      server.
    parent: Required. Parent value for ListPowerNetworksRequest.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class MarketplacesolutionsProjectsLocationsPowerSshKeysGetRequest(_messages.Message):
  r"""A MarketplacesolutionsProjectsLocationsPowerSshKeysGetRequest object.

  Fields:
    name: Required. Name of the resource.
  """

  name = _messages.StringField(1, required=True)


class MarketplacesolutionsProjectsLocationsPowerSshKeysListRequest(_messages.Message):
  r"""A MarketplacesolutionsProjectsLocationsPowerSshKeysListRequest object.

  Fields:
    filter: Optional. List filter.
    pageSize: Optional. Requested page size. The server might return fewer
      items than requested. If unspecified, server will pick an appropriate
      default.
    pageToken: Optional. A token identifying a page of results from the
      server.
    parent: Required. Parent value for ListPowerSSHKeysRequest.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class MarketplacesolutionsProjectsLocationsPowerVolumesGetRequest(_messages.Message):
  r"""A MarketplacesolutionsProjectsLocationsPowerVolumesGetRequest object.

  Fields:
    name: Required. Name of the resource.
  """

  name = _messages.StringField(1, required=True)


class MarketplacesolutionsProjectsLocationsPowerVolumesListRequest(_messages.Message):
  r"""A MarketplacesolutionsProjectsLocationsPowerVolumesListRequest object.

  Fields:
    filter: Optional. List filter.
    pageSize: Optional. Requested page size. The server might return fewer
      items than requested. If unspecified, server will pick an appropriate
      default.
    pageToken: Optional. A token identifying a page of results from the
      server.
    parent: Required. Parent value for ListPowerVolumesRequest.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class NetworkAttachment(_messages.Message):
  r"""A VM attachment to a network.

  Enums:
    IpTypeValueValuesEnum: Output only. The type of this network attachment.
    IpVersionValueValuesEnum: Output only. The version of this IP address.

  Fields:
    ipAddress: Output only. The IP address on this network.
    ipType: Output only. The type of this network attachment.
    ipVersion: Output only. The version of this IP address.
    macAddress: Output only. The MAC address on this network.
    powerNetwork: Required. The name of the network attached to.
  """

  class IpTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of this network attachment.

    Values:
      IP_TYPE_UNSPECIFIED: The type of this ip is unknown.
      FIXED: The ip address is fixed.
      DYNAMIC: The ip address is dynamic.
    """
    IP_TYPE_UNSPECIFIED = 0
    FIXED = 1
    DYNAMIC = 2

  class IpVersionValueValuesEnum(_messages.Enum):
    r"""Output only. The version of this IP address.

    Values:
      IP_VERSION_UNSPECIFIED: The version of this ip is unknown.
      IPV4: The ip is an IPv4 address.
      IPV6: The ip is an IPv6 address.
    """
    IP_VERSION_UNSPECIFIED = 0
    IPV4 = 1
    IPV6 = 2

  ipAddress = _messages.StringField(1)
  ipType = _messages.EnumField('IpTypeValueValuesEnum', 2)
  ipVersion = _messages.EnumField('IpVersionValueValuesEnum', 3)
  macAddress = _messages.StringField(4)
  powerNetwork = _messages.StringField(5)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class OsImage(_messages.Message):
  r"""The OS image information.

  Enums:
    TypeValueValuesEnum: Required. The OS image type.

  Fields:
    type: Required. The OS image type.
    version: Optional. The OS image detailed information.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. The OS image type.

    Values:
      OS_TYPE_UNSPECIFIED: The OS image type is not specified.
      AIX: The OS image type is AIX.
      RHEL: The OS image type is RHEL.
      IBMI: The OS image type is IBMi.
    """
    OS_TYPE_UNSPECIFIED = 0
    AIX = 1
    RHEL = 2
    IBMI = 3

  type = _messages.EnumField('TypeValueValuesEnum', 1)
  version = _messages.StringField(2)


class PowerIPAddressMetrics(_messages.Message):
  r"""Power IP Address Metrics

  Fields:
    available: Number of available IP address
    total: Size of IP address space
    used: Number of used IP addresses
    utilization: Utilization for IP address
  """

  available = _messages.IntegerField(1)
  total = _messages.IntegerField(2)
  used = _messages.IntegerField(3)
  utilization = _messages.IntegerField(4)


class PowerIPAddressRanges(_messages.Message):
  r"""A PowerIPAddress Range

  Fields:
    endingIpAddress: The ending IP address of the network in IPv4 format.
    startingIpAddress: The starting IP address of the network in IPv4 format.
  """

  endingIpAddress = _messages.StringField(1)
  startingIpAddress = _messages.StringField(2)


class PowerImage(_messages.Message):
  r"""This is the description of a PowerImage.

  Enums:
    ImageTypeValueValuesEnum: The image type of this PowerImage.
    StateValueValuesEnum: Output only. The state of the image.
    StorageTypeValueValuesEnum: Storage type of this image.

  Fields:
    createTime: Output only. Time the Power Image was created.
    imageType: The image type of this PowerImage.
    name: Identifier. The resource name of this PowerImage. Resource names are
      schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. Format:
      `projects/{project}/locations/{location}/powerImages/{image}`
    operatingSystem: The operating system type of this PowerImage.
    sizeGib: Size of image
    state: Output only. The state of the image.
    storagePool: Storage Pool that the image is in.
    storageType: Storage type of this image.
    uid: Output only. An unique identifier for this PowerImage, generated by
      the backend.
    updateTime: Output only. Time the Power Image was last updated.
    virtualMachines: List of virtual machines associated with image
    volumes: List of volumes for the image
  """

  class ImageTypeValueValuesEnum(_messages.Enum):
    r"""The image type of this PowerImage.

    Values:
      IMAGE_TYPE_UNSPECIFIED: The type of image is not specified
      STOCK: The image is a stock image.
      IMPORT: The image is an imported image.
      SNAPSHOT: The image is a snapshot image.
      CAPTURE: The image is a captured image.
    """
    IMAGE_TYPE_UNSPECIFIED = 0
    STOCK = 1
    IMPORT = 2
    SNAPSHOT = 3
    CAPTURE = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the image.

    Values:
      STATE_UNSPECIFIED: The state is not specified.
      QUEUED: The image identifier has been reserved for an image, no image
        data has been uploaded yet.
      SAVING: Denotes that an image's raw data is currently being uploaded.
      UPLOADING: Denotes that an import data-put call has been made.
      IMPORTING: Denotes that an import call has been made but that the image
        is not yet ready for use.
      ACTIVE: Denotes an image that is fully available.
      DEACTIVATED: Denotes that access to image data is not allowed to any
        non-admin user.
      KILLED: Denotes that an error occurred during the uploading of an
        image's data, and that the image is not readable.
      DELETED: Image is no longer available to use. An image in this state
        will be removed automatically at a later date.
      PENDING_DELETE: This is similar to deleted, however, image data has not
        been removed. An image in this state is not recoverable.
    """
    STATE_UNSPECIFIED = 0
    QUEUED = 1
    SAVING = 2
    UPLOADING = 3
    IMPORTING = 4
    ACTIVE = 5
    DEACTIVATED = 6
    KILLED = 7
    DELETED = 8
    PENDING_DELETE = 9

  class StorageTypeValueValuesEnum(_messages.Enum):
    r"""Storage type of this image.

    Values:
      STORAGE_TYPE_UNSPECIFIED: The storage type for this image is unknown.
      SSD: The storage type for this image is SSD.
      HDD: This storage type for this image is HDD.
    """
    STORAGE_TYPE_UNSPECIFIED = 0
    SSD = 1
    HDD = 2

  createTime = _messages.StringField(1)
  imageType = _messages.EnumField('ImageTypeValueValuesEnum', 2)
  name = _messages.StringField(3)
  operatingSystem = _messages.StringField(4)
  sizeGib = _messages.IntegerField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  storagePool = _messages.StringField(7)
  storageType = _messages.EnumField('StorageTypeValueValuesEnum', 8)
  uid = _messages.StringField(9)
  updateTime = _messages.StringField(10)
  virtualMachines = _messages.StringField(11, repeated=True)
  volumes = _messages.MessageField('PowerVolume', 12, repeated=True)


class PowerInstance(_messages.Message):
  r"""A Power instance.

  Enums:
    StateValueValuesEnum: Output only. The state of the instance.
    VirtualCpuTypeValueValuesEnum: Required. The processor type of this
      instance.

  Fields:
    addresses: Output only. List of addresses associated with this instance,
      corresponds to `addresses` field from Power's API.
    bootImage: Required. The name of the boot image used to create this
      instance.
    createTime: Output only. Instance creation time.
    healthStatus: Output only. Last health status for instance.
    memoryGib: Required. Memory size for the instance.
    name: Identifier. The resource name of this PowerInstance. Resource names
      are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. Format: `projects/{
      project}/locations/{location}/powerInstances/{power_instance}`
    networkAttachments: Optional. List of network attachments associated with
      this instance, corresponds to `networks` field from Power's API.
    osImage: Required. The OS image currently installed on this instance.
    state: Output only. The state of the instance.
    systemType: Required. IBM Power System type, most commonly s922.
    uid: Output only. An unique identifier generated for the PowerInstance.
    updateTime: Output only. Instance update time.
    virtualCpuCores: Required. Processor for the instance.
    virtualCpuType: Required. The processor type of this instance.
    volumeIds: Optional. List of volumes IDs associated with this instance.
    volumes: Output only. List of volumes associated with this instance,
      retrieved by calling ListVolumes API.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the instance.

    Values:
      STATE_UNSPECIFIED: The state of the instance is unknown.
      ACTIVE: The instance is ACTIVE.
      SHUTOFF: The instance has been SHUTOFF.
      INACTIVE: The instance is INACTIVE.
      TIMEOUT: The instance is TIMEOUT.
      BUILD: The instance is BUILDing.
      REBOOT: The instance is REBOOTing.
      WARNING: The instance is in WARNING status.
      ERROR: The instance has ERROR.
      RESIZE: The instance is resizing.
      VERIFY_RESIZE: The instance is verifying resize.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    SHUTOFF = 2
    INACTIVE = 3
    TIMEOUT = 4
    BUILD = 5
    REBOOT = 6
    WARNING = 7
    ERROR = 8
    RESIZE = 9
    VERIFY_RESIZE = 10

  class VirtualCpuTypeValueValuesEnum(_messages.Enum):
    r"""Required. The processor type of this instance.

    Values:
      VIRTUAL_CPU_TYPE_UNSPECIFIED: Unspecified.
      DEDICATED: Dedicated processors. Processor counts for this type must be
        whole numbers.
      UNCAPPED_SHARED: Uncapped shared processors.
      CAPPED_SHARED: Capped shared processors.
    """
    VIRTUAL_CPU_TYPE_UNSPECIFIED = 0
    DEDICATED = 1
    UNCAPPED_SHARED = 2
    CAPPED_SHARED = 3

  addresses = _messages.MessageField('NetworkAttachment', 1, repeated=True)
  bootImage = _messages.StringField(2)
  createTime = _messages.StringField(3)
  healthStatus = _messages.StringField(4)
  memoryGib = _messages.IntegerField(5)
  name = _messages.StringField(6)
  networkAttachments = _messages.MessageField('NetworkAttachment', 7, repeated=True)
  osImage = _messages.MessageField('OsImage', 8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  systemType = _messages.StringField(10)
  uid = _messages.StringField(11)
  updateTime = _messages.StringField(12)
  virtualCpuCores = _messages.FloatField(13)
  virtualCpuType = _messages.EnumField('VirtualCpuTypeValueValuesEnum', 14)
  volumeIds = _messages.StringField(15, repeated=True)
  volumes = _messages.MessageField('PowerVolume', 16, repeated=True)


class PowerNetwork(_messages.Message):
  r"""A PowerNetwork.

  Enums:
    TypeValueValuesEnum: Type of the vlan.

  Fields:
    cidr: The cidr of the network.
    dnsServers: List of DNS servers for network
    gateway: The gateway of the network.
    ipAddressMetrics: IPAddressMetrics information for Power Network
    ipAddressRanges: List of IP address ranges for the network
    jumboFramesEnabled: Whether jumbo frames are enabled.
    name: Identifier. The resource name of this PowerNetwork. Resource names
      are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. Format:
      `projects/{project}/locations/{location}/powerNetworks/{power_network}`
    type: Type of the vlan.
    uid: Output only. An unique identifier for this PowerNetwork, generated by
      the backend.
    vlanId: The vlan id of the network. VLAN IDs are 12-bit, and range from
      0x000 to 0xFFF.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of the vlan.

    Values:
      TYPE_UNSPECIFIED: Value is not specified.
      VLAN: The type of the network is VLAN.
    """
    TYPE_UNSPECIFIED = 0
    VLAN = 1

  cidr = _messages.StringField(1)
  dnsServers = _messages.StringField(2, repeated=True)
  gateway = _messages.StringField(3)
  ipAddressMetrics = _messages.MessageField('PowerIPAddressMetrics', 4)
  ipAddressRanges = _messages.MessageField('PowerIPAddressRanges', 5, repeated=True)
  jumboFramesEnabled = _messages.BooleanField(6)
  name = _messages.StringField(7)
  type = _messages.EnumField('TypeValueValuesEnum', 8)
  uid = _messages.StringField(9)
  vlanId = _messages.IntegerField(10)


class PowerSSHKey(_messages.Message):
  r"""A PowerSSHKey.

  Fields:
    createTime: Output only. SSH Key creation time
    name: Identifier. The resource name of this PowerSSHKey. Resource names
      are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. Format:
      'projects/{project}/locations/{location}/powerSshKey/{ssh_key}'
    sshKey: SSH Key value.
    uid: Output only. An unique identifier for this SSHKey, generated by the
      backend.
  """

  createTime = _messages.StringField(1)
  name = _messages.StringField(2)
  sshKey = _messages.StringField(3)
  uid = _messages.StringField(4)


class PowerVolume(_messages.Message):
  r"""A PowerVolume.

  Enums:
    DiskTypeValueValuesEnum: Output only. The disk type for this volume.
    StateValueValuesEnum: Output only. The state of the volume.

  Fields:
    bootVolume: Output only. An option indicates whether the volume is the
      server's boot volume.
    bootable: Output only. An option indicates whether the volume is bootable.
    createTime: Output only. Volume creation time.
    diskType: Output only. The disk type for this volume.
    groupId: Output only. The group id this volume attached to.
    name: Identifier. The resource name of this PowerVolume. Resource names
      are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. Format:
      `projects/{project}/locations/{location}/powerVolumes/{volume}`
    shareable: Output only. An option indicates whether the volume is
      shareable.
    sizeGib: Optional. The size of the volume.
    state: Output only. The state of the volume.
    uid: Output only. An unique identifier for this PowerVolume, generated by
      the backend.
    updateTime: Output only. Volume update time.
    virtualMachines: Output only. A list of pvm instances associated with the
      volume
    volumePool: Output only. Volume pool information.
    volumeType: Output only. Volume type information.
    wwn: Output only. The world wide name for the volume.
  """

  class DiskTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The disk type for this volume.

    Values:
      DISK_TYPE_UNSPECIFIED: This disk type for this volume is Unspecified.
      STANDARD: This disk type for this volume is Standard.
      SSD: The disk type for this volume is SSD.
    """
    DISK_TYPE_UNSPECIFIED = 0
    STANDARD = 1
    SSD = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the volume.

    Values:
      STATE_UNSPECIFIED: The state of this volume is unknown.
      CREATING: The volume is being created.
      AVAILABLE: The volume is ready to attach to an instance.
      RESERVED: The volume is reserved for attaching or shelved.
      ATTACHING: The volume is attaching to an instance.
      DETACHING: The volume is detaching from an instance.
      IN_USE: The volume is attached to an instance.
      MAINTENANCE: The volume is locked and being migrated.
      DELETING: The volume is being deleted.
      AWAITING_TRANSFER: The volume is awaiting for transfer.
      ERROR: A volume creation error occurred.
      ERROR_DELETING: A volume deletion error occurred.
      BACKING_UP: The volume is being backed up.
      RESTORING_BACKUP: A backup is being restored to the volume.
      ERROR_BACKING_UP: A backup error occurred.
      ERROR_RESTORING: A backup restoration error occurred.
      ERROR_EXTENDING: An error occurred while attempting to extend a volume.
      DOWNLOADING: The volume is downloading an image.
      UPLOADING: The volume is being uploaded to an image.
      RETYPING: The volume is changing type to another volume type.
      EXTENDING: The volume is being extended.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    AVAILABLE = 2
    RESERVED = 3
    ATTACHING = 4
    DETACHING = 5
    IN_USE = 6
    MAINTENANCE = 7
    DELETING = 8
    AWAITING_TRANSFER = 9
    ERROR = 10
    ERROR_DELETING = 11
    BACKING_UP = 12
    RESTORING_BACKUP = 13
    ERROR_BACKING_UP = 14
    ERROR_RESTORING = 15
    ERROR_EXTENDING = 16
    DOWNLOADING = 17
    UPLOADING = 18
    RETYPING = 19
    EXTENDING = 20

  bootVolume = _messages.BooleanField(1)
  bootable = _messages.BooleanField(2)
  createTime = _messages.StringField(3)
  diskType = _messages.EnumField('DiskTypeValueValuesEnum', 4)
  groupId = _messages.StringField(5)
  name = _messages.StringField(6)
  shareable = _messages.BooleanField(7)
  sizeGib = _messages.IntegerField(8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  uid = _messages.StringField(10)
  updateTime = _messages.StringField(11)
  virtualMachines = _messages.StringField(12, repeated=True)
  volumePool = _messages.StringField(13)
  volumeType = _messages.StringField(14)
  wwn = _messages.StringField(15)


class ResetPowerInstanceRequest(_messages.Message):
  r"""Message requesting to reset a server."""


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
