"""Generated client library for marketplacesolutions version v1alpha1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.marketplacesolutions.v1alpha1 import marketplacesolutions_v1alpha1_messages as messages


class MarketplacesolutionsV1alpha1(base_api.BaseApiClient):
  """Generated client library for service marketplacesolutions version v1alpha1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://marketplacesolutions.googleapis.com/'
  MTLS_BASE_URL = 'https://marketplacesolutions.mtls.googleapis.com/'

  _PACKAGE = 'marketplacesolutions'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1alpha1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'MarketplacesolutionsV1alpha1'
  _URL_VERSION = 'v1alpha1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new marketplacesolutions handle."""
    url = url or self.BASE_URL
    super(MarketplacesolutionsV1alpha1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.operations = self.OperationsService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_powerImages = self.ProjectsLocationsPowerImagesService(self)
    self.projects_locations_powerInstances = self.ProjectsLocationsPowerInstancesService(self)
    self.projects_locations_powerNetworks = self.ProjectsLocationsPowerNetworksService(self)
    self.projects_locations_powerSshKeys = self.ProjectsLocationsPowerSshKeysService(self)
    self.projects_locations_powerVolumes = self.ProjectsLocationsPowerVolumesService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class OperationsService(base_api.BaseApiService):
    """Service class for the operations resource."""

    _NAME = 'operations'

    def __init__(self, client):
      super(MarketplacesolutionsV1alpha1.OperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (MarketplacesolutionsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='marketplacesolutions.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='MarketplacesolutionsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (MarketplacesolutionsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/operations/{operationsId}',
        http_method='DELETE',
        method_id='marketplacesolutions.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='MarketplacesolutionsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (MarketplacesolutionsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/operations/{operationsId}',
        http_method='GET',
        method_id='marketplacesolutions.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='MarketplacesolutionsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (MarketplacesolutionsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/operations',
        http_method='GET',
        method_id='marketplacesolutions.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='MarketplacesolutionsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(MarketplacesolutionsV1alpha1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get details about an operation.

      Args:
        request: (MarketplacesolutionsProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='marketplacesolutions.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='MarketplacesolutionsProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsPowerImagesService(base_api.BaseApiService):
    """Service class for the projects_locations_powerImages resource."""

    _NAME = 'projects_locations_powerImages'

    def __init__(self, client):
      super(MarketplacesolutionsV1alpha1.ProjectsLocationsPowerImagesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get details about a single image from Power.

      Args:
        request: (MarketplacesolutionsProjectsLocationsPowerImagesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PowerImage) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/powerImages/{powerImagesId}',
        http_method='GET',
        method_id='marketplacesolutions.projects.locations.powerImages.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='MarketplacesolutionsProjectsLocationsPowerImagesGetRequest',
        response_type_name='PowerImage',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List Images in a given project and location from Power.

      Args:
        request: (MarketplacesolutionsProjectsLocationsPowerImagesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPowerImagesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/powerImages',
        http_method='GET',
        method_id='marketplacesolutions.projects.locations.powerImages.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/powerImages',
        request_field='',
        request_type_name='MarketplacesolutionsProjectsLocationsPowerImagesListRequest',
        response_type_name='ListPowerImagesResponse',
        supports_download=False,
    )

  class ProjectsLocationsPowerInstancesService(base_api.BaseApiService):
    """Service class for the projects_locations_powerInstances resource."""

    _NAME = 'projects_locations_powerInstances'

    def __init__(self, client):
      super(MarketplacesolutionsV1alpha1.ProjectsLocationsPowerInstancesService, self).__init__(client)
      self._upload_configs = {
          }

    def ApplyPowerAction(self, request, global_params=None):
      r"""Performs one of several power-related actions on an instance.

      Args:
        request: (MarketplacesolutionsProjectsLocationsPowerInstancesApplyPowerActionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('ApplyPowerAction')
      return self._RunMethod(
          config, request, global_params=global_params)

    ApplyPowerAction.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/powerInstances/{powerInstancesId}:applyPowerAction',
        http_method='POST',
        method_id='marketplacesolutions.projects.locations.powerInstances.applyPowerAction',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:applyPowerAction',
        request_field='applyPowerInstancePowerActionRequest',
        request_type_name='MarketplacesolutionsProjectsLocationsPowerInstancesApplyPowerActionRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Create a Power instance.

      Args:
        request: (MarketplacesolutionsProjectsLocationsPowerInstancesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/powerInstances',
        http_method='POST',
        method_id='marketplacesolutions.projects.locations.powerInstances.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['powerInstanceId'],
        relative_path='v1alpha1/{+parent}/powerInstances',
        request_field='powerInstance',
        request_type_name='MarketplacesolutionsProjectsLocationsPowerInstancesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a Power instance.

      Args:
        request: (MarketplacesolutionsProjectsLocationsPowerInstancesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/powerInstances/{powerInstancesId}',
        http_method='DELETE',
        method_id='marketplacesolutions.projects.locations.powerInstances.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='MarketplacesolutionsProjectsLocationsPowerInstancesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get details about a single server from Power.

      Args:
        request: (MarketplacesolutionsProjectsLocationsPowerInstancesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PowerInstance) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/powerInstances/{powerInstancesId}',
        http_method='GET',
        method_id='marketplacesolutions.projects.locations.powerInstances.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='MarketplacesolutionsProjectsLocationsPowerInstancesGetRequest',
        response_type_name='PowerInstance',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List servers in a given project and location from Power.

      Args:
        request: (MarketplacesolutionsProjectsLocationsPowerInstancesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPowerInstancesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/powerInstances',
        http_method='GET',
        method_id='marketplacesolutions.projects.locations.powerInstances.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/powerInstances',
        request_field='',
        request_type_name='MarketplacesolutionsProjectsLocationsPowerInstancesListRequest',
        response_type_name='ListPowerInstancesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a Power instance.

      Args:
        request: (MarketplacesolutionsProjectsLocationsPowerInstancesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/powerInstances/{powerInstancesId}',
        http_method='PATCH',
        method_id='marketplacesolutions.projects.locations.powerInstances.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='powerInstance',
        request_type_name='MarketplacesolutionsProjectsLocationsPowerInstancesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Reset(self, request, global_params=None):
      r"""Reset a running instance's state.

      Args:
        request: (MarketplacesolutionsProjectsLocationsPowerInstancesResetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Reset')
      return self._RunMethod(
          config, request, global_params=global_params)

    Reset.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/powerInstances/{powerInstancesId}:reset',
        http_method='POST',
        method_id='marketplacesolutions.projects.locations.powerInstances.reset',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:reset',
        request_field='resetPowerInstanceRequest',
        request_type_name='MarketplacesolutionsProjectsLocationsPowerInstancesResetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsPowerNetworksService(base_api.BaseApiService):
    """Service class for the projects_locations_powerNetworks resource."""

    _NAME = 'projects_locations_powerNetworks'

    def __init__(self, client):
      super(MarketplacesolutionsV1alpha1.ProjectsLocationsPowerNetworksService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get details about a single network from Power.

      Args:
        request: (MarketplacesolutionsProjectsLocationsPowerNetworksGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PowerNetwork) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/powerNetworks/{powerNetworksId}',
        http_method='GET',
        method_id='marketplacesolutions.projects.locations.powerNetworks.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='MarketplacesolutionsProjectsLocationsPowerNetworksGetRequest',
        response_type_name='PowerNetwork',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List networks in a given project and location from Power.

      Args:
        request: (MarketplacesolutionsProjectsLocationsPowerNetworksListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPowerNetworksResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/powerNetworks',
        http_method='GET',
        method_id='marketplacesolutions.projects.locations.powerNetworks.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/powerNetworks',
        request_field='',
        request_type_name='MarketplacesolutionsProjectsLocationsPowerNetworksListRequest',
        response_type_name='ListPowerNetworksResponse',
        supports_download=False,
    )

  class ProjectsLocationsPowerSshKeysService(base_api.BaseApiService):
    """Service class for the projects_locations_powerSshKeys resource."""

    _NAME = 'projects_locations_powerSshKeys'

    def __init__(self, client):
      super(MarketplacesolutionsV1alpha1.ProjectsLocationsPowerSshKeysService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get details about a single Power SSH Key.

      Args:
        request: (MarketplacesolutionsProjectsLocationsPowerSshKeysGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PowerSSHKey) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/powerSshKeys/{powerSshKeysId}',
        http_method='GET',
        method_id='marketplacesolutions.projects.locations.powerSshKeys.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='MarketplacesolutionsProjectsLocationsPowerSshKeysGetRequest',
        response_type_name='PowerSSHKey',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List SSH Keys in a given project and location from Power.

      Args:
        request: (MarketplacesolutionsProjectsLocationsPowerSshKeysListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPowerSSHKeysResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/powerSshKeys',
        http_method='GET',
        method_id='marketplacesolutions.projects.locations.powerSshKeys.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/powerSshKeys',
        request_field='',
        request_type_name='MarketplacesolutionsProjectsLocationsPowerSshKeysListRequest',
        response_type_name='ListPowerSSHKeysResponse',
        supports_download=False,
    )

  class ProjectsLocationsPowerVolumesService(base_api.BaseApiService):
    """Service class for the projects_locations_powerVolumes resource."""

    _NAME = 'projects_locations_powerVolumes'

    def __init__(self, client):
      super(MarketplacesolutionsV1alpha1.ProjectsLocationsPowerVolumesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get details about a single volume from Power.

      Args:
        request: (MarketplacesolutionsProjectsLocationsPowerVolumesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PowerVolume) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/powerVolumes/{powerVolumesId}',
        http_method='GET',
        method_id='marketplacesolutions.projects.locations.powerVolumes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='MarketplacesolutionsProjectsLocationsPowerVolumesGetRequest',
        response_type_name='PowerVolume',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List servers in a given project and location from Power.

      Args:
        request: (MarketplacesolutionsProjectsLocationsPowerVolumesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPowerVolumesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/powerVolumes',
        http_method='GET',
        method_id='marketplacesolutions.projects.locations.powerVolumes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/powerVolumes',
        request_field='',
        request_type_name='MarketplacesolutionsProjectsLocationsPowerVolumesListRequest',
        response_type_name='ListPowerVolumesResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(MarketplacesolutionsV1alpha1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (MarketplacesolutionsProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='marketplacesolutions.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='MarketplacesolutionsProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (MarketplacesolutionsProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='marketplacesolutions.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/locations',
        request_field='',
        request_type_name='MarketplacesolutionsProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(MarketplacesolutionsV1alpha1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
