# -*- coding: utf-8 -*- #
# Copyright 2023 Google LLC. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""Resource definitions for Cloud Platform Apis generated from apitools."""

import enum


BASE_URL = 'https://sqladmin.googleapis.com/sql/v1beta4/'
DOCS_URL = 'https://developers.google.com/cloud-sql/'


class Collections(enum.Enum):
  """Collections for all supported apis."""

  BACKUPRUNS = (
      'backupRuns',
      'projects/{project}/instances/{instance}/backupRuns/{id}',
      {},
      ['project', 'instance', 'id'],
      True
  )
  CONNECT = (
      'connect',
      'projects/{project}/instances/{instance}/connectSettings',
      {},
      ['project', 'instance'],
      True
  )
  DATABASES = (
      'databases',
      'projects/{project}/instances/{instance}/databases/{database}',
      {},
      ['project', 'instance', 'database'],
      True
  )
  INSTANCES = (
      'instances',
      'projects/{project}/instances/{instance}',
      {},
      ['project', 'instance'],
      True
  )
  OPERATIONS = (
      'operations',
      'projects/{project}/operations/{operation}',
      {},
      ['project', 'operation'],
      True
  )
  SSLCERTS = (
      'sslCerts',
      'projects/{project}/instances/{instance}/sslCerts/{sha1Fingerprint}',
      {},
      ['project', 'instance', 'sha1Fingerprint'],
      True
  )
  USERS = (
      'users',
      'projects/{project}/instances/{instance}/users/{name}',
      {},
      ['project', 'instance', 'name'],
      True
  )
  PROJECTS = (
      'projects',
      'projects/{project}',
      {},
      ['project'],
      True
  )

  def __init__(self, collection_name, path, flat_paths, params,
               enable_uri_parsing):
    self.collection_name = collection_name
    self.path = path
    self.flat_paths = flat_paths
    self.params = params
    self.enable_uri_parsing = enable_uri_parsing
