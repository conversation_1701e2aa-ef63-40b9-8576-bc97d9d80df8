"""Generated client library for sqladmin version v1beta4."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.sqladmin.v1beta4 import sqladmin_v1beta4_messages as messages


class SqladminV1beta4(base_api.BaseApiClient):
  """Generated client library for service sqladmin version v1beta4."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://sqladmin.googleapis.com/'
  MTLS_BASE_URL = 'https://sqladmin.mtls.googleapis.com/'

  _PACKAGE = 'sqladmin'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform', 'https://www.googleapis.com/auth/sqlservice.admin']
  _VERSION = 'v1beta4'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'SqladminV1beta4'
  _URL_VERSION = 'v1beta4'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new sqladmin handle."""
    url = url or self.BASE_URL
    super(SqladminV1beta4, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.backupRuns = self.BackupRunsService(self)
    self.backups = self.BackupsService(self)
    self.connect = self.ConnectService(self)
    self.databases = self.DatabasesService(self)
    self.flags = self.FlagsService(self)
    self.instances = self.InstancesService(self)
    self.operations = self.OperationsService(self)
    self.projects_instances = self.ProjectsInstancesService(self)
    self.projects = self.ProjectsService(self)
    self.sslCerts = self.SslCertsService(self)
    self.tiers = self.TiersService(self)
    self.users = self.UsersService(self)

  class BackupRunsService(base_api.BaseApiService):
    """Service class for the backupRuns resource."""

    _NAME = 'backupRuns'

    def __init__(self, client):
      super(SqladminV1beta4.BackupRunsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes the backup taken by a backup run.

      Args:
        request: (SqlBackupRunsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        http_method='DELETE',
        method_id='sql.backupRuns.delete',
        ordered_params=['project', 'instance', 'id'],
        path_params=['id', 'instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/backupRuns/{id}',
        request_field='',
        request_type_name='SqlBackupRunsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a resource containing information about a backup run.

      Args:
        request: (SqlBackupRunsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BackupRun) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='sql.backupRuns.get',
        ordered_params=['project', 'instance', 'id'],
        path_params=['id', 'instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/backupRuns/{id}',
        request_field='',
        request_type_name='SqlBackupRunsGetRequest',
        response_type_name='BackupRun',
        supports_download=False,
    )

    def Insert(self, request, global_params=None):
      r"""Creates a new backup run on demand.

      Args:
        request: (SqlBackupRunsInsertRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Insert')
      return self._RunMethod(
          config, request, global_params=global_params)

    Insert.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.backupRuns.insert',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/backupRuns',
        request_field='backupRun',
        request_type_name='SqlBackupRunsInsertRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all backup runs associated with the project or a given instance and configuration in the reverse chronological order of the backup initiation time.

      Args:
        request: (SqlBackupRunsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BackupRunsListResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='sql.backupRuns.list',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=['maxResults', 'pageToken'],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/backupRuns',
        request_field='',
        request_type_name='SqlBackupRunsListRequest',
        response_type_name='BackupRunsListResponse',
        supports_download=False,
    )

  class BackupsService(base_api.BaseApiService):
    """Service class for the backups resource."""

    _NAME = 'backups'

    def __init__(self, client):
      super(SqladminV1beta4.BackupsService, self).__init__(client)
      self._upload_configs = {
          }

    def CreateBackup(self, request, global_params=None):
      r"""Creates a backup for a Cloud SQL instance. This API can be used only to create on-demand backups.

      Args:
        request: (SqlBackupsCreateBackupRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('CreateBackup')
      return self._RunMethod(
          config, request, global_params=global_params)

    CreateBackup.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='sql/v1beta4/projects/{projectsId}/backups',
        http_method='POST',
        method_id='sql.backups.createBackup',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='sql/v1beta4/{+parent}/backups',
        request_field='backup',
        request_type_name='SqlBackupsCreateBackupRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def DeleteBackup(self, request, global_params=None):
      r"""Deletes the backup.

      Args:
        request: (SqlBackupsDeleteBackupRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('DeleteBackup')
      return self._RunMethod(
          config, request, global_params=global_params)

    DeleteBackup.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='sql/v1beta4/projects/{projectsId}/backups/{backupsId}',
        http_method='DELETE',
        method_id='sql.backups.deleteBackup',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='sql/v1beta4/{+name}',
        request_field='',
        request_type_name='SqlBackupsDeleteBackupRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def GetBackup(self, request, global_params=None):
      r"""Retrieves a resource containing information about a backup.

      Args:
        request: (SqlBackupsGetBackupRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Backup) The response message.
      """
      config = self.GetMethodConfig('GetBackup')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetBackup.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='sql/v1beta4/projects/{projectsId}/backups/{backupsId}',
        http_method='GET',
        method_id='sql.backups.getBackup',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='sql/v1beta4/{+name}',
        request_field='',
        request_type_name='SqlBackupsGetBackupRequest',
        response_type_name='Backup',
        supports_download=False,
    )

    def ListBackups(self, request, global_params=None):
      r"""Lists all backups associated with the project.

      Args:
        request: (SqlBackupsListBackupsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBackupsResponse) The response message.
      """
      config = self.GetMethodConfig('ListBackups')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListBackups.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='sql/v1beta4/projects/{projectsId}/backups',
        http_method='GET',
        method_id='sql.backups.listBackups',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='sql/v1beta4/{+parent}/backups',
        request_field='',
        request_type_name='SqlBackupsListBackupsRequest',
        response_type_name='ListBackupsResponse',
        supports_download=False,
    )

    def UpdateBackup(self, request, global_params=None):
      r"""Updates the retention period and the description of the backup. You can use this API to update final backups only.

      Args:
        request: (SqlBackupsUpdateBackupRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('UpdateBackup')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateBackup.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='sql/v1beta4/projects/{projectsId}/backups/{backupsId}',
        http_method='PATCH',
        method_id='sql.backups.updateBackup',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='sql/v1beta4/{+name}',
        request_field='backup',
        request_type_name='SqlBackupsUpdateBackupRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ConnectService(base_api.BaseApiService):
    """Service class for the connect resource."""

    _NAME = 'connect'

    def __init__(self, client):
      super(SqladminV1beta4.ConnectService, self).__init__(client)
      self._upload_configs = {
          }

    def GenerateEphemeralCert(self, request, global_params=None):
      r"""Generates a short-lived X509 certificate containing the provided public key and signed by a private key specific to the target instance. Users may use the certificate to authenticate as themselves when connecting to the database.

      Args:
        request: (SqlConnectGenerateEphemeralRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GenerateEphemeralCertResponse) The response message.
      """
      config = self.GetMethodConfig('GenerateEphemeralCert')
      return self._RunMethod(
          config, request, global_params=global_params)

    GenerateEphemeralCert.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.connect.generateEphemeral',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}:generateEphemeralCert',
        request_field='generateEphemeralCertRequest',
        request_type_name='SqlConnectGenerateEphemeralRequest',
        response_type_name='GenerateEphemeralCertResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves connect settings about a Cloud SQL instance.

      Args:
        request: (SqlConnectGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ConnectSettings) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='sql.connect.get',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=['readTime'],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/connectSettings',
        request_field='',
        request_type_name='SqlConnectGetRequest',
        response_type_name='ConnectSettings',
        supports_download=False,
    )

  class DatabasesService(base_api.BaseApiService):
    """Service class for the databases resource."""

    _NAME = 'databases'

    def __init__(self, client):
      super(SqladminV1beta4.DatabasesService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a database from a Cloud SQL instance.

      Args:
        request: (SqlDatabasesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        http_method='DELETE',
        method_id='sql.databases.delete',
        ordered_params=['project', 'instance', 'database'],
        path_params=['database', 'instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/databases/{database}',
        request_field='',
        request_type_name='SqlDatabasesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a resource containing information about a database inside a Cloud SQL instance.

      Args:
        request: (SqlDatabasesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Database) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='sql.databases.get',
        ordered_params=['project', 'instance', 'database'],
        path_params=['database', 'instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/databases/{database}',
        request_field='',
        request_type_name='SqlDatabasesGetRequest',
        response_type_name='Database',
        supports_download=False,
    )

    def Insert(self, request, global_params=None):
      r"""Inserts a resource containing information about a database inside a Cloud SQL instance. **Note:** You can't modify the default character set and collation.

      Args:
        request: (Database) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Insert')
      return self._RunMethod(
          config, request, global_params=global_params)

    Insert.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.databases.insert',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/databases',
        request_field='<request>',
        request_type_name='Database',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists databases in the specified Cloud SQL instance.

      Args:
        request: (SqlDatabasesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DatabasesListResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='sql.databases.list',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/databases',
        request_field='',
        request_type_name='SqlDatabasesListRequest',
        response_type_name='DatabasesListResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Partially updates a resource containing information about a database inside a Cloud SQL instance. This method supports patch semantics.

      Args:
        request: (SqlDatabasesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PATCH',
        method_id='sql.databases.patch',
        ordered_params=['project', 'instance', 'database'],
        path_params=['database', 'instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/databases/{database}',
        request_field='databaseResource',
        request_type_name='SqlDatabasesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates a resource containing information about a database inside a Cloud SQL instance.

      Args:
        request: (SqlDatabasesUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PUT',
        method_id='sql.databases.update',
        ordered_params=['project', 'instance', 'database'],
        path_params=['database', 'instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/databases/{database}',
        request_field='databaseResource',
        request_type_name='SqlDatabasesUpdateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class FlagsService(base_api.BaseApiService):
    """Service class for the flags resource."""

    _NAME = 'flags'

    def __init__(self, client):
      super(SqladminV1beta4.FlagsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists all available database flags for Cloud SQL instances.

      Args:
        request: (SqlFlagsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FlagsListResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='sql.flags.list',
        ordered_params=[],
        path_params=[],
        query_params=['databaseVersion', 'flagScope'],
        relative_path='sql/v1beta4/flags',
        request_field='',
        request_type_name='SqlFlagsListRequest',
        response_type_name='FlagsListResponse',
        supports_download=False,
    )

  class InstancesService(base_api.BaseApiService):
    """Service class for the instances resource."""

    _NAME = 'instances'

    def __init__(self, client):
      super(SqladminV1beta4.InstancesService, self).__init__(client)
      self._upload_configs = {
          }

    def ListServerCertificates(self, request, global_params=None):
      r"""Lists all versions of server certificates and certificate authorities (CAs) for the specified instance. There can be up to three sets of certs listed: the certificate that is currently in use, a future that has been added but not yet used to sign a certificate, and a certificate that has been rotated out. For instances not using Certificate Authority Service (CAS) server CA, use ListServerCas instead.

      Args:
        request: (SqlInstancesListServerCertificatesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (InstancesListServerCertificatesResponse) The response message.
      """
      config = self.GetMethodConfig('ListServerCertificates')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListServerCertificates.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='sql.instances.ListServerCertificates',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/listServerCertificates',
        request_field='',
        request_type_name='SqlInstancesListServerCertificatesRequest',
        response_type_name='InstancesListServerCertificatesResponse',
        supports_download=False,
    )

    def RotateServerCertificate(self, request, global_params=None):
      r"""Rotates the server certificate version to one previously added with the addServerCertificate method. For instances not using Certificate Authority Service (CAS) server CA, use RotateServerCa instead.

      Args:
        request: (SqlInstancesRotateServerCertificateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('RotateServerCertificate')
      return self._RunMethod(
          config, request, global_params=global_params)

    RotateServerCertificate.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.instances.RotateServerCertificate',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/rotateServerCertificate',
        request_field='instancesRotateServerCertificateRequest',
        request_type_name='SqlInstancesRotateServerCertificateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def AcquireSsrsLease(self, request, global_params=None):
      r"""Acquire a lease for the setup of SQL Server Reporting Services (SSRS).

      Args:
        request: (SqlInstancesAcquireSsrsLeaseRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SqlInstancesAcquireSsrsLeaseResponse) The response message.
      """
      config = self.GetMethodConfig('AcquireSsrsLease')
      return self._RunMethod(
          config, request, global_params=global_params)

    AcquireSsrsLease.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.instances.acquireSsrsLease',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/acquireSsrsLease',
        request_field='instancesAcquireSsrsLeaseRequest',
        request_type_name='SqlInstancesAcquireSsrsLeaseRequest',
        response_type_name='SqlInstancesAcquireSsrsLeaseResponse',
        supports_download=False,
    )

    def AddServerCa(self, request, global_params=None):
      r"""Add a new trusted Certificate Authority (CA) version for the specified instance. Required to prepare for a certificate rotation. If a CA version was previously added but never used in a certificate rotation, this operation replaces that version. There cannot be more than one CA version waiting to be rotated in. For instances that have enabled Certificate Authority Service (CAS) based server CA, use AddServerCertificate to add a new server certificate.

      Args:
        request: (SqlInstancesAddServerCaRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('AddServerCa')
      return self._RunMethod(
          config, request, global_params=global_params)

    AddServerCa.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.instances.addServerCa',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/addServerCa',
        request_field='',
        request_type_name='SqlInstancesAddServerCaRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def AddServerCertificate(self, request, global_params=None):
      r"""Add a new trusted server certificate version for the specified instance using Certificate Authority Service (CAS) server CA. Required to prepare for a certificate rotation. If a server certificate version was previously added but never used in a certificate rotation, this operation replaces that version. There cannot be more than one certificate version waiting to be rotated in. For instances not using CAS server CA, use AddServerCa instead.

      Args:
        request: (SqlInstancesAddServerCertificateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('AddServerCertificate')
      return self._RunMethod(
          config, request, global_params=global_params)

    AddServerCertificate.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.instances.addServerCertificate',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/addServerCertificate',
        request_field='',
        request_type_name='SqlInstancesAddServerCertificateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Clone(self, request, global_params=None):
      r"""Creates a Cloud SQL instance as a clone of the source instance. Using this operation might cause your instance to restart.

      Args:
        request: (SqlInstancesCloneRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Clone')
      return self._RunMethod(
          config, request, global_params=global_params)

    Clone.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.instances.clone',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/clone',
        request_field='instancesCloneRequest',
        request_type_name='SqlInstancesCloneRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Cloud SQL instance.

      Args:
        request: (SqlInstancesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        http_method='DELETE',
        method_id='sql.instances.delete',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=['enableFinalBackup', 'finalBackupDescription', 'finalBackupExpiryTime', 'finalBackupTtlDays', 'retainBackups', 'retainBackupsExpiryTime', 'retainBackupsTtlDays', 'skipFinalBackup'],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}',
        request_field='',
        request_type_name='SqlInstancesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Demote(self, request, global_params=None):
      r"""Demotes an existing standalone instance to be a Cloud SQL read replica for an external database server.

      Args:
        request: (SqlInstancesDemoteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Demote')
      return self._RunMethod(
          config, request, global_params=global_params)

    Demote.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.instances.demote',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/demote',
        request_field='instancesDemoteRequest',
        request_type_name='SqlInstancesDemoteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def DemoteMaster(self, request, global_params=None):
      r"""Demotes the stand-alone instance to be a Cloud SQL read replica for an external database server.

      Args:
        request: (SqlInstancesDemoteMasterRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('DemoteMaster')
      return self._RunMethod(
          config, request, global_params=global_params)

    DemoteMaster.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.instances.demoteMaster',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/demoteMaster',
        request_field='instancesDemoteMasterRequest',
        request_type_name='SqlInstancesDemoteMasterRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def ExecuteSql(self, request, global_params=None):
      r"""Execute SQL statements.

      Args:
        request: (SqlInstancesExecuteSqlRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SqlInstancesExecuteSqlResponse) The response message.
      """
      config = self.GetMethodConfig('ExecuteSql')
      return self._RunMethod(
          config, request, global_params=global_params)

    ExecuteSql.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.instances.executeSql',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/executeSql',
        request_field='executeSqlPayload',
        request_type_name='SqlInstancesExecuteSqlRequest',
        response_type_name='SqlInstancesExecuteSqlResponse',
        supports_download=False,
    )

    def Export(self, request, global_params=None):
      r"""Exports data from a Cloud SQL instance to a Cloud Storage bucket as a SQL dump or CSV file.

      Args:
        request: (SqlInstancesExportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Export')
      return self._RunMethod(
          config, request, global_params=global_params)

    Export.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.instances.export',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/export',
        request_field='instancesExportRequest',
        request_type_name='SqlInstancesExportRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Failover(self, request, global_params=None):
      r"""Initiates a manual failover of a high availability (HA) primary instance to a standby instance, which becomes the primary instance. Users are then rerouted to the new primary. For more information, see the [Overview of high availability](https://cloud.google.com/sql/docs/mysql/high-availability) page in the Cloud SQL documentation. If using Legacy HA (MySQL only), this causes the instance to failover to its failover replica instance.

      Args:
        request: (SqlInstancesFailoverRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Failover')
      return self._RunMethod(
          config, request, global_params=global_params)

    Failover.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.instances.failover',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/failover',
        request_field='instancesFailoverRequest',
        request_type_name='SqlInstancesFailoverRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a resource containing information about a Cloud SQL instance.

      Args:
        request: (SqlInstancesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DatabaseInstance) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='sql.instances.get',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}',
        request_field='',
        request_type_name='SqlInstancesGetRequest',
        response_type_name='DatabaseInstance',
        supports_download=False,
    )

    def Import(self, request, global_params=None):
      r"""Imports data into a Cloud SQL instance from a SQL dump or CSV file in Cloud Storage.

      Args:
        request: (SqlInstancesImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Import')
      return self._RunMethod(
          config, request, global_params=global_params)

    Import.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.instances.import',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/import',
        request_field='instancesImportRequest',
        request_type_name='SqlInstancesImportRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Insert(self, request, global_params=None):
      r"""Creates a new Cloud SQL instance.

      Args:
        request: (SqlInstancesInsertRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Insert')
      return self._RunMethod(
          config, request, global_params=global_params)

    Insert.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.instances.insert',
        ordered_params=['project'],
        path_params=['project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances',
        request_field='databaseInstance',
        request_type_name='SqlInstancesInsertRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists instances under a given project.

      Args:
        request: (SqlInstancesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (InstancesListResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='sql.instances.list',
        ordered_params=['project'],
        path_params=['project'],
        query_params=['filter', 'maxResults', 'pageToken'],
        relative_path='sql/v1beta4/projects/{project}/instances',
        request_field='',
        request_type_name='SqlInstancesListRequest',
        response_type_name='InstancesListResponse',
        supports_download=False,
    )

    def ListServerCas(self, request, global_params=None):
      r"""Lists all of the trusted Certificate Authorities (CAs) for the specified instance. There can be up to three CAs listed: the CA that was used to sign the certificate that is currently in use, a CA that has been added but not yet used to sign a certificate, and a CA used to sign a certificate that has previously rotated out.

      Args:
        request: (SqlInstancesListServerCasRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (InstancesListServerCasResponse) The response message.
      """
      config = self.GetMethodConfig('ListServerCas')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListServerCas.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='sql.instances.listServerCas',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/listServerCas',
        request_field='',
        request_type_name='SqlInstancesListServerCasRequest',
        response_type_name='InstancesListServerCasResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Partially updates settings of a Cloud SQL instance by merging the request with the current configuration. This method supports patch semantics.

      Args:
        request: (SqlInstancesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PATCH',
        method_id='sql.instances.patch',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=['enforcePsaWriteEndpoint'],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}',
        request_field='databaseInstance',
        request_type_name='SqlInstancesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def PointInTimeRestore(self, request, global_params=None):
      r"""Point in time restore for an instance managed by Google Cloud Backup and Disaster Recovery.

      Args:
        request: (SqlInstancesPointInTimeRestoreRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('PointInTimeRestore')
      return self._RunMethod(
          config, request, global_params=global_params)

    PointInTimeRestore.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='sql/v1beta4/projects/{projectsId}:pointInTimeRestore',
        http_method='POST',
        method_id='sql.instances.pointInTimeRestore',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='sql/v1beta4/{+parent}:pointInTimeRestore',
        request_field='pointInTimeRestoreContext',
        request_type_name='SqlInstancesPointInTimeRestoreRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def PreCheckMajorVersionUpgrade(self, request, global_params=None):
      r"""Execute MVU Pre-checks.

      Args:
        request: (SqlInstancesPreCheckMajorVersionUpgradeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('PreCheckMajorVersionUpgrade')
      return self._RunMethod(
          config, request, global_params=global_params)

    PreCheckMajorVersionUpgrade.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.instances.preCheckMajorVersionUpgrade',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/preCheckMajorVersionUpgrade',
        request_field='instancesPreCheckMajorVersionUpgradeRequest',
        request_type_name='SqlInstancesPreCheckMajorVersionUpgradeRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def PromoteReplica(self, request, global_params=None):
      r"""Promotes the read replica instance to be an independent Cloud SQL primary instance. Using this operation might cause your instance to restart.

      Args:
        request: (SqlInstancesPromoteReplicaRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('PromoteReplica')
      return self._RunMethod(
          config, request, global_params=global_params)

    PromoteReplica.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.instances.promoteReplica',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=['failover'],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/promoteReplica',
        request_field='',
        request_type_name='SqlInstancesPromoteReplicaRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Reencrypt(self, request, global_params=None):
      r"""Reencrypt CMEK instance with latest key version.

      Args:
        request: (SqlInstancesReencryptRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Reencrypt')
      return self._RunMethod(
          config, request, global_params=global_params)

    Reencrypt.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.instances.reencrypt',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/reencrypt',
        request_field='instancesReencryptRequest',
        request_type_name='SqlInstancesReencryptRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def ReleaseSsrsLease(self, request, global_params=None):
      r"""Release a lease for the setup of SQL Server Reporting Services (SSRS).

      Args:
        request: (SqlInstancesReleaseSsrsLeaseRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SqlInstancesReleaseSsrsLeaseResponse) The response message.
      """
      config = self.GetMethodConfig('ReleaseSsrsLease')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReleaseSsrsLease.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.instances.releaseSsrsLease',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/releaseSsrsLease',
        request_field='',
        request_type_name='SqlInstancesReleaseSsrsLeaseRequest',
        response_type_name='SqlInstancesReleaseSsrsLeaseResponse',
        supports_download=False,
    )

    def ResetSslConfig(self, request, global_params=None):
      r"""Deletes all client certificates and generates a new server SSL certificate for the instance.

      Args:
        request: (SqlInstancesResetSslConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('ResetSslConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    ResetSslConfig.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.instances.resetSslConfig',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/resetSslConfig',
        request_field='',
        request_type_name='SqlInstancesResetSslConfigRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Restart(self, request, global_params=None):
      r"""Restarts a Cloud SQL instance.

      Args:
        request: (SqlInstancesRestartRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Restart')
      return self._RunMethod(
          config, request, global_params=global_params)

    Restart.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.instances.restart',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/restart',
        request_field='',
        request_type_name='SqlInstancesRestartRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def RestoreBackup(self, request, global_params=None):
      r"""Restores a backup of a Cloud SQL instance. Using this operation might cause your instance to restart.

      Args:
        request: (SqlInstancesRestoreBackupRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('RestoreBackup')
      return self._RunMethod(
          config, request, global_params=global_params)

    RestoreBackup.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.instances.restoreBackup',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/restoreBackup',
        request_field='instancesRestoreBackupRequest',
        request_type_name='SqlInstancesRestoreBackupRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def RotateServerCa(self, request, global_params=None):
      r"""Rotates the server certificate to one signed by the Certificate Authority (CA) version previously added with the addServerCA method. For instances that have enabled Certificate Authority Service (CAS) based server CA, use RotateServerCertificate to rotate the server certificate.

      Args:
        request: (SqlInstancesRotateServerCaRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('RotateServerCa')
      return self._RunMethod(
          config, request, global_params=global_params)

    RotateServerCa.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.instances.rotateServerCa',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/rotateServerCa',
        request_field='instancesRotateServerCaRequest',
        request_type_name='SqlInstancesRotateServerCaRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def StartReplica(self, request, global_params=None):
      r"""Starts the replication in the read replica instance.

      Args:
        request: (SqlInstancesStartReplicaRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('StartReplica')
      return self._RunMethod(
          config, request, global_params=global_params)

    StartReplica.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.instances.startReplica',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/startReplica',
        request_field='',
        request_type_name='SqlInstancesStartReplicaRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def StopReplica(self, request, global_params=None):
      r"""Stops the replication in the read replica instance.

      Args:
        request: (SqlInstancesStopReplicaRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('StopReplica')
      return self._RunMethod(
          config, request, global_params=global_params)

    StopReplica.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.instances.stopReplica',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/stopReplica',
        request_field='',
        request_type_name='SqlInstancesStopReplicaRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Switchover(self, request, global_params=None):
      r"""Switches over from the primary instance to the DR replica instance.

      Args:
        request: (SqlInstancesSwitchoverRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Switchover')
      return self._RunMethod(
          config, request, global_params=global_params)

    Switchover.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.instances.switchover',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=['dbTimeout'],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/switchover',
        request_field='',
        request_type_name='SqlInstancesSwitchoverRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def TruncateLog(self, request, global_params=None):
      r"""Truncate MySQL general and slow query log tables MySQL only.

      Args:
        request: (SqlInstancesTruncateLogRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('TruncateLog')
      return self._RunMethod(
          config, request, global_params=global_params)

    TruncateLog.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.instances.truncateLog',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/truncateLog',
        request_field='instancesTruncateLogRequest',
        request_type_name='SqlInstancesTruncateLogRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates settings of a Cloud SQL instance. Using this operation might cause your instance to restart.

      Args:
        request: (SqlInstancesUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PUT',
        method_id='sql.instances.update',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=['enforcePsaWriteEndpoint'],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}',
        request_field='databaseInstance',
        request_type_name='SqlInstancesUpdateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class OperationsService(base_api.BaseApiService):
    """Service class for the operations resource."""

    _NAME = 'operations'

    def __init__(self, client):
      super(SqladminV1beta4.OperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancels an instance operation that has been performed on an instance.

      Args:
        request: (SqlOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.operations.cancel',
        ordered_params=['project', 'operation'],
        path_params=['operation', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/operations/{operation}/cancel',
        request_field='',
        request_type_name='SqlOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves an instance operation that has been performed on an instance.

      Args:
        request: (SqlOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='sql.operations.get',
        ordered_params=['project', 'operation'],
        path_params=['operation', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/operations/{operation}',
        request_field='',
        request_type_name='SqlOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all instance operations that have been performed on the given Cloud SQL instance in the reverse chronological order of the start time.

      Args:
        request: (SqlOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (OperationsListResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='sql.operations.list',
        ordered_params=['project'],
        path_params=['project'],
        query_params=['filter', 'instance', 'maxResults', 'pageToken'],
        relative_path='sql/v1beta4/projects/{project}/operations',
        request_field='',
        request_type_name='SqlOperationsListRequest',
        response_type_name='OperationsListResponse',
        supports_download=False,
    )

  class ProjectsInstancesService(base_api.BaseApiService):
    """Service class for the projects_instances resource."""

    _NAME = 'projects_instances'

    def __init__(self, client):
      super(SqladminV1beta4.ProjectsInstancesService, self).__init__(client)
      self._upload_configs = {
          }

    def GetDiskShrinkConfig(self, request, global_params=None):
      r"""Get Disk Shrink Config for a given instance.

      Args:
        request: (SqlProjectsInstancesGetDiskShrinkConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SqlInstancesGetDiskShrinkConfigResponse) The response message.
      """
      config = self.GetMethodConfig('GetDiskShrinkConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetDiskShrinkConfig.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='sql.projects.instances.getDiskShrinkConfig',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/getDiskShrinkConfig',
        request_field='',
        request_type_name='SqlProjectsInstancesGetDiskShrinkConfigRequest',
        response_type_name='SqlInstancesGetDiskShrinkConfigResponse',
        supports_download=False,
    )

    def GetLatestRecoveryTime(self, request, global_params=None):
      r"""Get Latest Recovery Time for a given instance.

      Args:
        request: (SqlProjectsInstancesGetLatestRecoveryTimeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SqlInstancesGetLatestRecoveryTimeResponse) The response message.
      """
      config = self.GetMethodConfig('GetLatestRecoveryTime')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetLatestRecoveryTime.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='sql.projects.instances.getLatestRecoveryTime',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=['sourceInstanceDeletionTime'],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/getLatestRecoveryTime',
        request_field='',
        request_type_name='SqlProjectsInstancesGetLatestRecoveryTimeRequest',
        response_type_name='SqlInstancesGetLatestRecoveryTimeResponse',
        supports_download=False,
    )

    def PerformDiskShrink(self, request, global_params=None):
      r"""Perform Disk Shrink on primary instance.

      Args:
        request: (SqlProjectsInstancesPerformDiskShrinkRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('PerformDiskShrink')
      return self._RunMethod(
          config, request, global_params=global_params)

    PerformDiskShrink.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.projects.instances.performDiskShrink',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/performDiskShrink',
        request_field='performDiskShrinkContext',
        request_type_name='SqlProjectsInstancesPerformDiskShrinkRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def RescheduleMaintenance(self, request, global_params=None):
      r"""Reschedules the maintenance on the given instance.

      Args:
        request: (SqlProjectsInstancesRescheduleMaintenanceRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('RescheduleMaintenance')
      return self._RunMethod(
          config, request, global_params=global_params)

    RescheduleMaintenance.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.projects.instances.rescheduleMaintenance',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/rescheduleMaintenance',
        request_field='sqlInstancesRescheduleMaintenanceRequestBody',
        request_type_name='SqlProjectsInstancesRescheduleMaintenanceRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def ResetReplicaSize(self, request, global_params=None):
      r"""Reset Replica Size to primary instance disk size.

      Args:
        request: (SqlProjectsInstancesResetReplicaSizeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('ResetReplicaSize')
      return self._RunMethod(
          config, request, global_params=global_params)

    ResetReplicaSize.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.projects.instances.resetReplicaSize',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/resetReplicaSize',
        request_field='sqlInstancesResetReplicaSizeRequest',
        request_type_name='SqlProjectsInstancesResetReplicaSizeRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def StartExternalSync(self, request, global_params=None):
      r"""Start External primary instance migration.

      Args:
        request: (SqlProjectsInstancesStartExternalSyncRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('StartExternalSync')
      return self._RunMethod(
          config, request, global_params=global_params)

    StartExternalSync.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.projects.instances.startExternalSync',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/startExternalSync',
        request_field='sqlInstancesStartExternalSyncRequest',
        request_type_name='SqlProjectsInstancesStartExternalSyncRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def VerifyExternalSyncSettings(self, request, global_params=None):
      r"""Verify External primary instance external sync settings.

      Args:
        request: (SqlProjectsInstancesVerifyExternalSyncSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SqlInstancesVerifyExternalSyncSettingsResponse) The response message.
      """
      config = self.GetMethodConfig('VerifyExternalSyncSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    VerifyExternalSyncSettings.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.projects.instances.verifyExternalSyncSettings',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/verifyExternalSyncSettings',
        request_field='sqlInstancesVerifyExternalSyncSettingsRequest',
        request_type_name='SqlProjectsInstancesVerifyExternalSyncSettingsRequest',
        response_type_name='SqlInstancesVerifyExternalSyncSettingsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(SqladminV1beta4.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

  class SslCertsService(base_api.BaseApiService):
    """Service class for the sslCerts resource."""

    _NAME = 'sslCerts'

    def __init__(self, client):
      super(SqladminV1beta4.SslCertsService, self).__init__(client)
      self._upload_configs = {
          }

    def CreateEphemeral(self, request, global_params=None):
      r"""Generates a short-lived X509 certificate containing the provided public key and signed by a private key specific to the target instance. Users may use the certificate to authenticate as themselves when connecting to the database.

      Args:
        request: (SqlSslCertsCreateEphemeralRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SslCert) The response message.
      """
      config = self.GetMethodConfig('CreateEphemeral')
      return self._RunMethod(
          config, request, global_params=global_params)

    CreateEphemeral.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.sslCerts.createEphemeral',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/createEphemeral',
        request_field='sslCertsCreateEphemeralRequest',
        request_type_name='SqlSslCertsCreateEphemeralRequest',
        response_type_name='SslCert',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the SSL certificate. For First Generation instances, the certificate remains valid until the instance is restarted.

      Args:
        request: (SqlSslCertsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        http_method='DELETE',
        method_id='sql.sslCerts.delete',
        ordered_params=['project', 'instance', 'sha1Fingerprint'],
        path_params=['instance', 'project', 'sha1Fingerprint'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/sslCerts/{sha1Fingerprint}',
        request_field='',
        request_type_name='SqlSslCertsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a particular SSL certificate. Does not include the private key (required for usage). The private key must be saved from the response to initial creation.

      Args:
        request: (SqlSslCertsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SslCert) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='sql.sslCerts.get',
        ordered_params=['project', 'instance', 'sha1Fingerprint'],
        path_params=['instance', 'project', 'sha1Fingerprint'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/sslCerts/{sha1Fingerprint}',
        request_field='',
        request_type_name='SqlSslCertsGetRequest',
        response_type_name='SslCert',
        supports_download=False,
    )

    def Insert(self, request, global_params=None):
      r"""Creates an SSL certificate and returns it along with the private key and server certificate authority. The new certificate will not be usable until the instance is restarted.

      Args:
        request: (SqlSslCertsInsertRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SslCertsInsertResponse) The response message.
      """
      config = self.GetMethodConfig('Insert')
      return self._RunMethod(
          config, request, global_params=global_params)

    Insert.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.sslCerts.insert',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/sslCerts',
        request_field='sslCertsInsertRequest',
        request_type_name='SqlSslCertsInsertRequest',
        response_type_name='SslCertsInsertResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all of the current SSL certificates for the instance.

      Args:
        request: (SqlSslCertsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SslCertsListResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='sql.sslCerts.list',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/sslCerts',
        request_field='',
        request_type_name='SqlSslCertsListRequest',
        response_type_name='SslCertsListResponse',
        supports_download=False,
    )

  class TiersService(base_api.BaseApiService):
    """Service class for the tiers resource."""

    _NAME = 'tiers'

    def __init__(self, client):
      super(SqladminV1beta4.TiersService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists all available machine types (tiers) for Cloud SQL, for example, `db-custom-1-3840`. For related information, see [Pricing](/sql/pricing).

      Args:
        request: (SqlTiersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TiersListResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='sql.tiers.list',
        ordered_params=['project'],
        path_params=['project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/tiers',
        request_field='',
        request_type_name='SqlTiersListRequest',
        response_type_name='TiersListResponse',
        supports_download=False,
    )

  class UsersService(base_api.BaseApiService):
    """Service class for the users resource."""

    _NAME = 'users'

    def __init__(self, client):
      super(SqladminV1beta4.UsersService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a user from a Cloud SQL instance.

      Args:
        request: (SqlUsersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        http_method='DELETE',
        method_id='sql.users.delete',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=['host', 'name'],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/users',
        request_field='',
        request_type_name='SqlUsersDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a resource containing information about a user.

      Args:
        request: (SqlUsersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (User) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='sql.users.get',
        ordered_params=['project', 'instance', 'name'],
        path_params=['instance', 'name', 'project'],
        query_params=['host'],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/users/{name}',
        request_field='',
        request_type_name='SqlUsersGetRequest',
        response_type_name='User',
        supports_download=False,
    )

    def Insert(self, request, global_params=None):
      r"""Creates a new user in a Cloud SQL instance.

      Args:
        request: (User) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Insert')
      return self._RunMethod(
          config, request, global_params=global_params)

    Insert.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sql.users.insert',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/users',
        request_field='<request>',
        request_type_name='User',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists users in the specified Cloud SQL instance.

      Args:
        request: (SqlUsersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (UsersListResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='sql.users.list',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=[],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/users',
        request_field='',
        request_type_name='SqlUsersListRequest',
        response_type_name='UsersListResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates an existing user in a Cloud SQL instance.

      Args:
        request: (SqlUsersUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PUT',
        method_id='sql.users.update',
        ordered_params=['project', 'instance'],
        path_params=['instance', 'project'],
        query_params=['host', 'name'],
        relative_path='sql/v1beta4/projects/{project}/instances/{instance}/users',
        request_field='user',
        request_type_name='SqlUsersUpdateRequest',
        response_type_name='Operation',
        supports_download=False,
    )
