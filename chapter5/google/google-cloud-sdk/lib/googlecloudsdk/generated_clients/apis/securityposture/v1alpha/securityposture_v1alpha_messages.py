"""Generated message classes for securityposture version v1alpha.

Defines, assesses, and monitors the overall status of your security in Google
Cloud. You can use security postures to evaluate your current cloud security
against defined benchmarks and help maintain the level of security that your
organization requires.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'securityposture'


class Allowed(_messages.Message):
  r"""Allowed IP rule.

  Fields:
    ipRules: Optional. Optional list of allowed IP rules.
  """

  ipRules = _messages.MessageField('IpRule', 1, repeated=True)


class AssetDetails(_messages.Message):
  r"""Details of a Cloud Asset Inventory asset that caused a violation.

  Fields:
    asset: Information about the Cloud Asset Inventory asset that violated a
      policy. The format of this information can change at any time without
      prior notice. Your application must not depend on this information in
      any way.
    assetType: The type of Cloud Asset Inventory asset. For a list of asset
      types, see [Supported asset types](https://cloud.google.com/asset-
      inventory/docs/supported-asset-types).
  """

  asset = _messages.StringField(1)
  assetType = _messages.StringField(2)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class ComplianceStandard(_messages.Message):
  r"""Information about a compliance standard that the policy helps enforce.

  Fields:
    control: Optional. The control in the compliance standard that the policy
      helps enforce. For example, `AC-3`.
    standard: Optional. The compliance standard that the policy helps enforce.
      For example, `NIST SP 800-53`.
  """

  control = _messages.StringField(1)
  standard = _messages.StringField(2)


class Constraint(_messages.Message):
  r"""Metadata for a constraint in a Policy.

  Fields:
    dataAccessGovernancePolicy: Optional. Data Access Governance policy
      constraint.
    dataFlowGovernancePolicy: Optional. Data Flow Governance policy
      constraint.
    dataRetentionAndDeletionPolicy: Optional. Data Retention and Deletion
      policy constraint.
    orgPolicyConstraint: Optional. A predefined organization policy
      constraint.
    orgPolicyConstraintCustom: Optional. A custom organization policy
      constraint.
    regoPolicy: Optional. Rego policy constraint.
    securityHealthAnalyticsCustomModule: Optional. A custom module for
      Security Health Analytics.
    securityHealthAnalyticsModule: Optional. A built-in detector for Security
      Health Analytics.
  """

  dataAccessGovernancePolicy = _messages.MessageField('DataAccessGovernancePolicy', 1)
  dataFlowGovernancePolicy = _messages.MessageField('DataFlowGovernancePolicy', 2)
  dataRetentionAndDeletionPolicy = _messages.MessageField('DataRetentionAndDeletionPolicy', 3)
  orgPolicyConstraint = _messages.MessageField('OrgPolicyConstraint', 4)
  orgPolicyConstraintCustom = _messages.MessageField('OrgPolicyConstraintCustom', 5)
  regoPolicy = _messages.MessageField('RegoPolicy', 6)
  securityHealthAnalyticsCustomModule = _messages.MessageField('SecurityHealthAnalyticsCustomModule', 7)
  securityHealthAnalyticsModule = _messages.MessageField('SecurityHealthAnalyticsModule', 8)


class CreateFindingRemediationExecutionRequest(_messages.Message):
  r"""Request message.

  Fields:
    findingRemediationExecution: Required. The resource being created which
      contains the finding name.
  """

  findingRemediationExecution = _messages.MessageField('FindingRemediationExecution', 1)


class CreateIaCValidationReportRequest(_messages.Message):
  r"""Request message for CreateIaCValidationReport.

  Fields:
    iac: Required. The infrastructure-as-code (IaC) configuration to validate.
  """

  iac = _messages.MessageField('IaC', 1)


class CreatePredictionRequest(_messages.Message):
  r"""Request message for creating a Prediction.

  Enums:
    PredictionTypeValueValuesEnum: Required. The type of prediction.

  Fields:
    environmentOptions: Optional. Environment options taken into account while
      predicting a Security Posture.
    intent: Optional. Intent, user provided intent for the prediction. Custom
      posture predictions will require this input.
    predictionType: Required. The type of prediction.
  """

  class PredictionTypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of prediction.

    Values:
      PREDICTION_TYPE_UNSPECIFIED: Prediction type unspecified.
      BASIC_POSTURE: Basic predefined posture prediction type.
      NATURAL_LANGUAGE_QUERY: Posture prediction type to query predictions
        based on an intent provided in the request.
      ADVANCED_POSTURE: Advanced posture prediction type.
    """
    PREDICTION_TYPE_UNSPECIFIED = 0
    BASIC_POSTURE = 1
    NATURAL_LANGUAGE_QUERY = 2
    ADVANCED_POSTURE = 3

  environmentOptions = _messages.MessageField('EnvironmentOptions', 1)
  intent = _messages.StringField(2)
  predictionType = _messages.EnumField('PredictionTypeValueValuesEnum', 3)


class CreateRemediationIntentRequest(_messages.Message):
  r"""Request message for creating a RemediationIntent.

  Enums:
    WorkflowTypeValueValuesEnum: Optional. Type of workflow for the
      remediation intent. If not specified, the default workflow type is semi-
      autonomous.

  Fields:
    findingName: Optional. Canonical name of the finding for which the
      remediation intent is created. Eg format for finding at project level: p
      rojects/{project_id}/sources/{source}/locations/{location}/findings/{fin
      ding_id}
    workflowType: Optional. Type of workflow for the remediation intent. If
      not specified, the default workflow type is semi-autonomous.
  """

  class WorkflowTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Type of workflow for the remediation intent. If not
    specified, the default workflow type is semi-autonomous.

    Values:
      WORKFLOW_TYPE_UNSPECIFIED: Workflow type unspecified.
      WORKFLOW_TYPE_MANUAL: Workflow type is manual.
      WORKFLOW_TYPE_SEMI_AUTONOMOUS: Semi autonomous workflow type, triggered
        periodically.
    """
    WORKFLOW_TYPE_UNSPECIFIED = 0
    WORKFLOW_TYPE_MANUAL = 1
    WORKFLOW_TYPE_SEMI_AUTONOMOUS = 2

  findingName = _messages.StringField(1)
  workflowType = _messages.EnumField('WorkflowTypeValueValuesEnum', 2)


class CustomConfig(_messages.Message):
  r"""A custom module configuration for Security Health Analytics. Use
  `CustomConfig` to create custom detectors that generate custom findings for
  resources that you specify.

  Enums:
    SeverityValueValuesEnum: Required. The severity of findings generated by
      the custom module.

  Fields:
    customOutput: Optional. Definitions of custom source properties to include
      in findings.
    description: Optional. A description of the vulnerability or
      misconfiguration that the custom module detects. The description appears
      in each finding. Provide enough information to help an investigator
      understand the finding. The value must be enclosed in quotation marks.
    predicate: Required. The Common Expression Language (CEL) expression to
      evaluate. When the expression evaluates to `true` for a resource, a
      finding is generated.
    recommendation: Required. An explanation of the steps that security teams
      can take to resolve the detected issue. The explanation appears in each
      finding.
    resourceSelector: Required. The resource types that the custom module
      operates on.
    severity: Required. The severity of findings generated by the custom
      module.
  """

  class SeverityValueValuesEnum(_messages.Enum):
    r"""Required. The severity of findings generated by the custom module.

    Values:
      SEVERITY_UNSPECIFIED: Default value. This value is unused.
      CRITICAL: Critical severity.
      HIGH: High severity.
      MEDIUM: Medium severity.
      LOW: Low severity.
    """
    SEVERITY_UNSPECIFIED = 0
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4

  customOutput = _messages.MessageField('CustomOutputSpec', 1)
  description = _messages.StringField(2)
  predicate = _messages.MessageField('Expr', 3)
  recommendation = _messages.StringField(4)
  resourceSelector = _messages.MessageField('ResourceSelector', 5)
  severity = _messages.EnumField('SeverityValueValuesEnum', 6)


class CustomOutputSpec(_messages.Message):
  r"""Definitions of custom source properties that can appear in findings.

  Fields:
    properties: Optional. The custom source properties that can appear in
      findings.
  """

  properties = _messages.MessageField('Property', 1, repeated=True)


class DataAccessGovernancePolicy(_messages.Message):
  r"""A governance policy that specifies the principals that can access data
  without generating logs.

  Fields:
    displayName: A user-specified description of the policy. The maximum
      length is 63 characters.
    name: Output only. Uniquely identifies the policy. The name is ignored
      when creating the policy. The name is generated by the server with the
      format eg.
      `dataassetgroups/{data_asset_group_ref}/policies/{policy_id}`. `organiza
      tions/org_id/locations/location/resourceGroups/rg_id/policies/policy_id`
    rules: Rules that specify the principals that can access data without
      generating logs.
  """

  displayName = _messages.StringField(1)
  name = _messages.StringField(2)
  rules = _messages.MessageField('DataAccessGovernancePolicyRule', 3, repeated=True)


class DataAccessGovernancePolicyRule(_messages.Message):
  r"""A rule that specifies the principals that a
  [DataAccessGovernancePolicy[] applies to.

  Fields:
    allowedPrincipals: Principals that can access data without generating
      logs.
    description: A user-specified description of the rule. The maximum length
      is 255 characters.
    ruleId: A user-specified identifier for the rule.
  """

  allowedPrincipals = _messages.StringField(1, repeated=True)
  description = _messages.StringField(2)
  ruleId = _messages.StringField(3)


class DataFlowGovernancePolicy(_messages.Message):
  r"""A governance policy that defines the locations that data can flow
  through.

  Fields:
    displayName: A user-specified description of the policy. The maximum
      length is 63 characters.
    name: Output only. Uniquely identifies the policy. The name is ignored
      when creating the policy. The name is generated by the server with the
      format eg.
      `dataassetgroups/{data_asset_group_ref}/policies/{policy_id}`. `organiza
      tions/org_id/locations/location/resourceGroups/rg_id/policies/policy_id`
    rules: Rules that specify the locations that data can flow through.
  """

  displayName = _messages.StringField(1)
  name = _messages.StringField(2)
  rules = _messages.MessageField('DataFlowGovernancePolicyRule', 3, repeated=True)


class DataFlowGovernancePolicyRule(_messages.Message):
  r"""A rule in a [DataFlowGovernancePolicy[] that specifies the locations
  that data can flow through.

  Fields:
    allowedDataRegions: The [Unicode Common Locale Data Repository
      (CLDR)](https://cldr.unicode.org/) territory codes of the locations that
      data can flow through. No logs are generated for data that flows through
      these locations.
    description: A user-specified description of the rule. The maximum length
      is 255 characters.
    ruleId: A user-specified identifier for the rule.
  """

  allowedDataRegions = _messages.StringField(1, repeated=True)
  description = _messages.StringField(2)
  ruleId = _messages.StringField(3)


class DataRetentionAndDeletionPolicy(_messages.Message):
  r"""A policy that prescribes minimum and maximum allowed 'age' of customer's
  data asset.

  Fields:
    displayName: A user-specified description of the policy. The maximum
      length is 63 characters.
    maxTtlFromCreationSeconds: Defines a maximum allowed TTL from the asset's
      creation time.
    maxTtlFromLastModificationSeconds: Defines a maximum allowed TTL from the
      asset's last modification time.
    name: Output only. Uniquely identifies the policy. The name is ignored
      when creating the policy. The name is generated by the server with the
      format eg.
      `dataassetgroups/{data_asset_group_ref}/policies/{policy_id}`. `organiza
      tions/org_id/locations/location/resourceGroups/rg_id/policies/policy_id`
  """

  displayName = _messages.StringField(1)
  maxTtlFromCreationSeconds = _messages.IntegerField(2)
  maxTtlFromLastModificationSeconds = _messages.IntegerField(3)
  name = _messages.StringField(4)


class Denied(_messages.Message):
  r"""Denied IP rule.

  Fields:
    ipRules: Optional. Optional list of denied IP rules.
  """

  ipRules = _messages.MessageField('IpRule', 1, repeated=True)


class Duration(_messages.Message):
  r"""Duration representation

  Fields:
    days: Optional. Duration in days.
  """

  days = _messages.IntegerField(1)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EnvironmentOptions(_messages.Message):
  r"""Environment options taken into account while predicting a Security
  Posture.

  Enums:
    CloudPlatformValueValuesEnum: Cloud platform being used by the customer.

  Fields:
    cloudLocation: Cloud location the customer is in.
    cloudPlatform: Cloud platform being used by the customer.
    customerIndustries: A list of customer industries supported.
    industryStandards: A list of industry standards supported.
    services: A list of GCP services supported. Output will also include the
      count of resources protected by each GCP service.
  """

  class CloudPlatformValueValuesEnum(_messages.Enum):
    r"""Cloud platform being used by the customer.

    Values:
      CLOUD_PLATFORM_UNSPECIFIED: Cloud platform unspecified.
      GCP: Gcp Cloud Platform.
    """
    CLOUD_PLATFORM_UNSPECIFIED = 0
    GCP = 1

  cloudLocation = _messages.StringField(1)
  cloudPlatform = _messages.EnumField('CloudPlatformValueValuesEnum', 2)
  customerIndustries = _messages.StringField(3, repeated=True)
  industryStandards = _messages.MessageField('IndustryStandard', 4, repeated=True)
  services = _messages.MessageField('GcpServices', 5, repeated=True)


class ErrorDetails(_messages.Message):
  r"""Error details in case of failure while generating remediation.

  Fields:
    errorCode: Optional. Error code.
    reason: Optional. Reason for the error.
  """

  errorCode = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  reason = _messages.StringField(2)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class ExtractPostureRequest(_messages.Message):
  r"""Request message for ExtractPosture.

  Fields:
    postureId: Required. An identifier for the posture.
    workload: Required. The organization, folder, or project from which
      policies are extracted. Must be within the organization defined in
      parent. Use one of the following formats: *
      `organization/{organization_number}` * `folder/{folder_number}` *
      `project/{project_number}`
  """

  postureId = _messages.StringField(1)
  workload = _messages.StringField(2)


class FileData(_messages.Message):
  r"""Data for a file: path and contents

  Fields:
    fileContent: Required. File content
    filePath: Required. File path
  """

  fileContent = _messages.StringField(1)
  filePath = _messages.StringField(2)


class Finding(_messages.Message):
  r"""Finding information relayed during remediation fixing process

  Fields:
    category: Optional. Category of the finding, like UNUSED_IAM_ROLE,
      IAM_ROLE_HAS_EXCESSIVE_PERMISSIONS etc.
    findingMetadata: Optional. Finding metadata of the finding associated with
      this remediation intent.
    findingName: Required. Finding canonical name, used to identify the
      finding within an organization.
    resourceName: Optional. Resource name on which the finding is present.
  """

  category = _messages.StringField(1)
  findingMetadata = _messages.MessageField('FindingMetadata', 2)
  findingName = _messages.StringField(3)
  resourceName = _messages.StringField(4)


class FindingMetadata(_messages.Message):
  r"""Finding metadata of the finding associated with this remediation intent.

  Fields:
    firewallRule: Firewall rules of the finding associated with this
      remediation intent.
    iamBindingsList: IAM bindings of the finding associated with this
      remediation intent.
  """

  firewallRule = _messages.MessageField('FirewallRule', 1)
  iamBindingsList = _messages.MessageField('IAMBindingsList', 2)


class FindingRemediationExecution(_messages.Message):
  r"""Message describing FindingRemediationExecution object.

  Fields:
    finding: Required. Name of the finding. The format of this value is as
      follows:
      organizations/{organization}/sources/{source}/findings/{finding_id}
    name: Output only. The id of the finding remediation execution. The format
      of this value is as follows: organizations/{organization}/locations/glob
      al/findingRemediationExecutions/{finding_remediation_execution_id}
  """

  finding = _messages.StringField(1)
  name = _messages.StringField(2)


class FirewallRule(_messages.Message):
  r"""FirewallRule captures the details of a firewall rule for OPEN_FIREWALL
  Type finding. Error details in case of failure while generating remediation.

  Fields:
    ipRules: Required. IP rules associated with the finding.
    name: Required. The name of the firewall rule. For example, "//compute.goo
      gleapis.com/projects/{project_id}/global/firewalls/{firewall_name}".
  """

  ipRules = _messages.MessageField('IpRules', 1)
  name = _messages.StringField(2)


class GcpServices(_messages.Message):
  r"""GCP services with the count of resources.

  Fields:
    gcpServiceName: Name of the service.
    resourcesCount: Output only. Count of resources protected under this
      posture.
  """

  gcpServiceName = _messages.StringField(1)
  resourcesCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleCloudSecuritypostureV1alphaCustomConstraint(_messages.Message):
  r"""A custom, user-defined constraint. You can apply the constraint only to
  the resource types specified in the constraint, and only within the
  organization where the constraint is defined. _When you create a custom
  constraint, it is not enforced automatically._ You must use an organization
  policy to [enforce the constraint](https://cloud.google.com/resource-
  manager/help/organization-policy/constraints/enforce).

  Enums:
    ActionTypeValueValuesEnum: Whether to allow or deny the action.
    MethodTypesValueListEntryValuesEnum:

  Fields:
    actionType: Whether to allow or deny the action.
    condition: A Common Expression Language (CEL) condition expression that
      must evaluate to `true` for the constraint to be enforced. The maximum
      length is 1000 characters. For example: +
      `resource.instanceName.matches('(production|test)_(.+_)?[\d]+')`:
      Evaluates to `true` if the resource's `instanceName` attribute contains
      the following: + The prefix `production` or `test` + An underscore (`_`)
      + Optional: One or more characters, followed by an underscore (`_`) +
      One or more digits + `resource.management.auto_upgrade == true`:
      Evaluates to `true` if the resource's `management.auto_upgrade`
      attribute is `true`.
    description: A description of the constraint. The maximum length is 2000
      characters.
    displayName: A display name for the constraint. The maximum length is 200
      characters.
    methodTypes: The types of operations that the constraint applies to.
    name: Immutable. The name of the constraint, in the format `organizations/
      {organization_id}/customConstraints/custom.{custom_constraint_id}`. For
      example, `organizations/123456789012/customConstraints/custom.createOnly
      E2TypeVms`. Must contain 1 to 62 characters, excluding the prefix
      `organizations/{organization_id}/customConstraints/custom.`.
    resourceTypes: Immutable. The resource type that the constraint applies
      to, in the format `{canonical_service_name}/{resource_type_name}`. For
      example, `compute.googleapis.com/Instance`.
    updateTime: Output only. The last time at which the constraint was updated
      or created.
  """

  class ActionTypeValueValuesEnum(_messages.Enum):
    r"""Whether to allow or deny the action.

    Values:
      ACTION_TYPE_UNSPECIFIED: Default value. This value is unused.
      ALLOW: Allow the action.
      DENY: Deny the action.
    """
    ACTION_TYPE_UNSPECIFIED = 0
    ALLOW = 1
    DENY = 2

  class MethodTypesValueListEntryValuesEnum(_messages.Enum):
    r"""MethodTypesValueListEntryValuesEnum enum type.

    Values:
      METHOD_TYPE_UNSPECIFIED: Default value. This value is unused.
      CREATE: Constraint applied when creating the resource.
      UPDATE: Constraint applied when updating the resource.
      DELETE: Not supported. Constraint applied when deleting the resource.
    """
    METHOD_TYPE_UNSPECIFIED = 0
    CREATE = 1
    UPDATE = 2
    DELETE = 3

  actionType = _messages.EnumField('ActionTypeValueValuesEnum', 1)
  condition = _messages.StringField(2)
  description = _messages.StringField(3)
  displayName = _messages.StringField(4)
  methodTypes = _messages.EnumField('MethodTypesValueListEntryValuesEnum', 5, repeated=True)
  name = _messages.StringField(6)
  resourceTypes = _messages.StringField(7, repeated=True)
  updateTime = _messages.StringField(8)


class GoogleCloudSecuritypostureV1alphaIaCValidationReportPolicyDetails(_messages.Message):
  r"""Details of policies unsupported by evaluation services during IAC
  validation.

  Enums:
    ConstraintTypeValueValuesEnum: Type of policy constraint.

  Fields:
    constraintType: Type of policy constraint.
    policyId: Policy id of unsupported policy.
  """

  class ConstraintTypeValueValuesEnum(_messages.Enum):
    r"""Type of policy constraint.

    Values:
      CONSTRAINT_TYPE_UNSPECIFIED: Unspecified constraint type.
      ORG_POLICY: Org policy canned constraint type.
      SECURITY_HEALTH_ANALYTICS_MODULE: SHA module canned constraint type.
      ORG_POLICY_CUSTOM: Custom org policy constraint type.
      SECURITY_HEALTH_ANALYTICS_CUSTOM_MODULE: Custom SHA constraint type.
      REGO_POLICY: A custom rego policy constraint.
    """
    CONSTRAINT_TYPE_UNSPECIFIED = 0
    ORG_POLICY = 1
    SECURITY_HEALTH_ANALYTICS_MODULE = 2
    ORG_POLICY_CUSTOM = 3
    SECURITY_HEALTH_ANALYTICS_CUSTOM_MODULE = 4
    REGO_POLICY = 5

  constraintType = _messages.EnumField('ConstraintTypeValueValuesEnum', 1)
  policyId = _messages.StringField(2)


class GoogleCloudSecuritypostureV1alphaPolicyRule(_messages.Message):
  r"""A rule that defines the allowed and denied values for an organization
  policy constraint.

  Messages:
    ParametersValue: Optional. Required for managed constraints if parameters
      are defined. Passes parameter values when policy enforcement is enabled.
      Ensure that parameter value types match those defined in the constraint
      definition. For example: { "allowedLocations" : ["us-east1", "us-
      west1"], "allowAll" : true }

  Fields:
    allowAll: Whether to allow any value for a list constraint. Valid only for
      list constraints.
    condition: A condition that determines whether this rule is used to
      evaluate the policy. When set, the google.type.Expr.expression field
      must contain 1 to 10 subexpressions, joined by the `||` or `&&`
      operators. Each subexpression must use the `resource.matchTag()` or
      `resource.matchTagId()` Common Expression Language (CEL) function. The
      `resource.matchTag()` function takes the following arguments: *
      `key_name`: the namespaced name of the tag key, with the organization ID
      and a slash (`/`) as a prefix; for example, `123456789012/environment` *
      `value_name`: the short name of the tag value For example:
      `resource.matchTag('123456789012/environment, 'prod')` The
      `resource.matchTagId()` function takes the following arguments: *
      `key_id`: the permanent ID of the tag key; for example,
      `tagKeys/123456789012` * `value_id`: the permanent ID of the tag value;
      for example, `tagValues/567890123456` For example:
      `resource.matchTagId('tagKeys/123456789012', 'tagValues/567890123456')`
    denyAll: Whether to deny all values for a list constraint. Valid only for
      list constraints.
    enforce: Whether to enforce the constraint. Valid only for boolean
      constraints.
    parameters: Optional. Required for managed constraints if parameters are
      defined. Passes parameter values when policy enforcement is enabled.
      Ensure that parameter value types match those defined in the constraint
      definition. For example: { "allowedLocations" : ["us-east1", "us-
      west1"], "allowAll" : true }
    resourceTypes: Optional. The resource types policies can support, only
      used for managed constraints. Method type is `GOVERN_TAGS`.
    values: The allowed and denied values for a list constraint. Valid only
      for list constraints.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ParametersValue(_messages.Message):
    r"""Optional. Required for managed constraints if parameters are defined.
    Passes parameter values when policy enforcement is enabled. Ensure that
    parameter value types match those defined in the constraint definition.
    For example: { "allowedLocations" : ["us-east1", "us-west1"], "allowAll" :
    true }

    Messages:
      AdditionalProperty: An additional property for a ParametersValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ParametersValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  allowAll = _messages.BooleanField(1)
  condition = _messages.MessageField('Expr', 2)
  denyAll = _messages.BooleanField(3)
  enforce = _messages.BooleanField(4)
  parameters = _messages.MessageField('ParametersValue', 5)
  resourceTypes = _messages.MessageField('ResourceTypes', 6)
  values = _messages.MessageField('GoogleCloudSecuritypostureV1alphaPolicyRuleStringValues', 7)


class GoogleCloudSecuritypostureV1alphaPolicyRuleStringValues(_messages.Message):
  r"""The allowed and denied values for a list constraint. For all
  constraints, these fields can contain literal values. Optionally, you can
  add the `is:` prefix to these values. If the value contains a colon (`:`),
  then the `is:` prefix is required. Some constraints allow you to specify a
  portion of the resource hierarchy, known as a [_hierarchy
  subtree_](https://cloud.google.com/resource-manager/help/organization-
  policy/hierarchy-subtree), that the constraint applies to. To specify a
  hierarchy subtree, use the `under:` prefix, followed by a value with one of
  these formats: - `projects/{project_id}` (for example, `projects/tokyo-
  rain-123`) - `folders/{folder_id}` (for example, `folders/1234567890123`) -
  `organizations/{organization_id}` (for example,
  `organizations/123456789012`) A constraint's `supports_under` field
  indicates whether you can specify a hierarchy subtree. To learn which
  predefined constraints let you specify a hierarchy subtree, see the
  [constraints reference](https://cloud.google.com/resource-
  manager/help/organization-policy/constraints/reference).

  Fields:
    allowedValues: The allowed values for the constraint.
    deniedValues: The denied values for the constraint.
  """

  allowedValues = _messages.StringField(1, repeated=True)
  deniedValues = _messages.StringField(2, repeated=True)


class GoogleCloudSecuritypostureV1alphaReport(_messages.Message):
  r"""Details of a report.

  Fields:
    createTime: Output only. The time at which the report was created.
    iacValidationReport: Output only. An infrastructure-as-code (IaC)
      validation report.
    name: Required. The name of the report, in the format
      `organizations/{organization}/locations/global/reports/{report_id}`.
    updateTime: Output only. The time at which the report was last updated.
  """

  createTime = _messages.StringField(1)
  iacValidationReport = _messages.MessageField('IaCValidationReport', 2)
  name = _messages.StringField(3)
  updateTime = _messages.StringField(4)


class IAMBinding(_messages.Message):
  r"""IAMBinding captures a member's role addition, removal, or state.

  Enums:
    ActionValueValuesEnum: Required. The action that was performed on the IAM
      binding.

  Fields:
    action: Required. The action that was performed on the IAM binding.
    member: Required. The member to whom the role is assigned. For example,
      `user:<EMAIL>`, `group:<EMAIL>`, or
      `domain:google.com`.
    role: Required. The role that is assigned to the member. For example,
      `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required. The action that was performed on the IAM binding.

    Values:
      ACTION_UNSPECIFIED: Unspecified.
      ADD: Addition of an IAM binding.
      REMOVE: Removal of an IAM binding.
    """
    ACTION_UNSPECIFIED = 0
    ADD = 1
    REMOVE = 2

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  member = _messages.StringField(2)
  role = _messages.StringField(3)


class IAMBindingsList(_messages.Message):
  r"""List of IAM bindings of the finding associated with this remediation
  intent.

  Fields:
    iamBindings: Required. List of IAM bindings of the finding associated with
      this remediation intent. Example: [{"role": "roles/owner", "member":
      ["user:<EMAIL>"], Action: "ADD"}] It will be used to fetch the TF
      state of the finding.
  """

  iamBindings = _messages.MessageField('IAMBinding', 1, repeated=True)


class IaC(_messages.Message):
  r"""Details of an infrastructure-as-code (IaC) configuration.

  Fields:
    tfPlan: Optional. A Terraform plan file, formatted as a stringified JSON
      object. To learn how to generate a Terraform plan file in JSON format,
      see [JSON output
      format](https://developer.hashicorp.com/terraform/internals/json-format)
      in the Terraform documentation.
  """

  tfPlan = _messages.BytesField(1)


class IaCValidationReport(_messages.Message):
  r"""Details of an infrastructure-as-code (IaC) validation report.

  Fields:
    note: Additional information about the report.
    skippedAssetTypes: List of asset types skipped during IaC validation.
    skippedPolicies: List of policies unsupported by evaluation services
      during IAC validation.
    violations: A list of every Violation found in the IaC configuration.
  """

  note = _messages.StringField(1)
  skippedAssetTypes = _messages.StringField(2, repeated=True)
  skippedPolicies = _messages.MessageField('GoogleCloudSecuritypostureV1alphaIaCValidationReportPolicyDetails', 3, repeated=True)
  violations = _messages.MessageField('Violation', 4, repeated=True)


class IaCValidationReportMetrics(_messages.Message):
  r"""Definition of the resource 'IaCValidationReportMetrics'.

  Fields:
    createTime: Output only. The timestamp when the Report Metric was created.
    duration: Output only. The duration for which the Report Metric was
      created. The duration is in days. (smallest unit of duration is chosen
      as 1 Day)
    name: Required. Identifier. The name of this IaC Validation Report Metric
      resource, in the format of organizations/{organization}/locations/global
      /iacValidationReportMetrics.
    topViolatedPolicies: Top violated policies.
    topViolatedProjects: Top violated projects.
    topViolatingAssets: Top violating assets.
    totalPoliciesViolated: Total number of policies violated.
    totalReportsGenerated: Total number of reports generated.
    totalViolatingAssets: Total number of violating assets.
    totalViolations: Total number of violations.
  """

  createTime = _messages.StringField(1)
  duration = _messages.MessageField('Duration', 2)
  name = _messages.StringField(3)
  topViolatedPolicies = _messages.MessageField('ViolatedPolicy', 4, repeated=True)
  topViolatedProjects = _messages.StringField(5, repeated=True)
  topViolatingAssets = _messages.StringField(6, repeated=True)
  totalPoliciesViolated = _messages.IntegerField(7)
  totalReportsGenerated = _messages.IntegerField(8)
  totalViolatingAssets = _messages.IntegerField(9)
  totalViolations = _messages.IntegerField(10)


class IndustryStandard(_messages.Message):
  r"""Industry Standards with standard and version.

  Fields:
    standard: Industry Standard.
    version: Industry Standard Version.
  """

  standard = _messages.StringField(1)
  version = _messages.StringField(2)


class IpRule(_messages.Message):
  r"""IP rule information.

  Fields:
    portRanges: Optional. An optional list of ports to which this rule
      applies. This field is only applicable for the UDP or (S)TCP protocols.
      Each entry must be either an integer or a range including a min and max
      port number.
    protocol: Required. The IP protocol this rule applies to. This value can
      either be one of the following well known protocol strings (TCP, UDP,
      ICMP, ESP, AH, IPIP, SCTP) or a string representation of the integer
      value.
  """

  portRanges = _messages.MessageField('PortRange', 1, repeated=True)
  protocol = _messages.StringField(2)


class IpRules(_messages.Message):
  r"""IP rules associated with the finding.

  Enums:
    DirectionValueValuesEnum: Required. The direction that the rule is
      applicable to, one of ingress or egress.

  Fields:
    allowed: Tuple with allowed rules.
    denied: Tuple with denied rules.
    destinationIpRanges: Optional. If destination IP ranges are specified, the
      firewall rule applies only to traffic that has a destination IP address
      in these ranges. These ranges must be expressed in CIDR format. Only
      supports IPv4.
    direction: Required. The direction that the rule is applicable to, one of
      ingress or egress.
    exposedServices: Optional. Name of the network protocol service, such as
      FTP, that is exposed by the open port. Follows the naming convention
      available at: https://www.iana.org/assignments/service-names-port-
      numbers/service-names-port-numbers.xhtml.
    sourceIpRanges: Optional. If source IP ranges are specified, the firewall
      rule applies only to traffic that has a source IP address in these
      ranges. These ranges must be expressed in CIDR format. Only supports
      IPv4.
  """

  class DirectionValueValuesEnum(_messages.Enum):
    r"""Required. The direction that the rule is applicable to, one of ingress
    or egress.

    Values:
      DIRECTION_UNSPECIFIED: Unspecified direction value.
      INGRESS: Ingress direction value.
      EGRESS: Egress direction value.
    """
    DIRECTION_UNSPECIFIED = 0
    INGRESS = 1
    EGRESS = 2

  allowed = _messages.MessageField('Allowed', 1)
  denied = _messages.MessageField('Denied', 2)
  destinationIpRanges = _messages.StringField(3, repeated=True)
  direction = _messages.EnumField('DirectionValueValuesEnum', 4)
  exposedServices = _messages.StringField(5, repeated=True)
  sourceIpRanges = _messages.StringField(6, repeated=True)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListPostureDeploymentsResponse(_messages.Message):
  r"""Response message for ListPostureDeployments.

  Fields:
    nextPageToken: A pagination token. To retrieve the next page of results,
      call the method again with this token.
    postureDeployments: The list of PostureDeployment resources.
    unreachable: Locations that were temporarily unavailable and could not be
      reached.
  """

  nextPageToken = _messages.StringField(1)
  postureDeployments = _messages.MessageField('PostureDeployment', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListPostureRevisionsResponse(_messages.Message):
  r"""Response message for ListPostureRevisions.

  Fields:
    nextPageToken: A pagination token. To retrieve the next page of results,
      call the method again with this token.
    revisions: The list of revisions for the Posture.
  """

  nextPageToken = _messages.StringField(1)
  revisions = _messages.MessageField('Posture', 2, repeated=True)


class ListPostureTemplatesResponse(_messages.Message):
  r"""Response message for ListPostureTemplates.

  Fields:
    nextPageToken: A pagination token. To retrieve the next page of results,
      call the method again with this token.
    postureTemplates: The list of PostureTemplate resources.
  """

  nextPageToken = _messages.StringField(1)
  postureTemplates = _messages.MessageField('PostureTemplate', 2, repeated=True)


class ListPosturesResponse(_messages.Message):
  r"""Response message for ListPostures.

  Fields:
    nextPageToken: A pagination token. To retrieve the next page of results,
      call the method again with this token.
    postures: The list of Posture resources.
    unreachable: Locations that were temporarily unavailable and could not be
      reached.
  """

  nextPageToken = _messages.StringField(1)
  postures = _messages.MessageField('Posture', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListPredictionsResponse(_messages.Message):
  r"""Message for response to listing Predictions.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    predictions: The list of Reports.
    unreachable: Unreachable resources.
  """

  nextPageToken = _messages.StringField(1)
  predictions = _messages.MessageField('Prediction', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListRemediationIntentsResponse(_messages.Message):
  r"""Message for response to listing Remediation Intents.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    remediationIntents: The list of Remediation Intents.
    unreachable: Unreachable resources.
  """

  nextPageToken = _messages.StringField(1)
  remediationIntents = _messages.MessageField('RemediationIntent', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListReportsResponse(_messages.Message):
  r"""Response message for ListReports.

  Fields:
    nextPageToken: A pagination token. To retrieve the next page of results,
      call the method again with this token.
    reports: The list of Report resources.
    unreachable: Locations that were temporarily unavailable and could not be
      reached.
  """

  nextPageToken = _messages.StringField(1)
  reports = _messages.MessageField('GoogleCloudSecuritypostureV1alphaReport', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Metadata for an Operation.

  Fields:
    apiVersion: Output only. The API version used to start the operation.
    createTime: Output only. The time at which the operation was created.
    endTime: Output only. The time at which the operation finished running.
    errorMessage: Output only. An error message. Returned when a
      PostureDeployment enters a failure state like UPDATE_FAILED.
    predictionMetadata: Output only. Metadata for Prediction LRO.
    requestedCancellation: Output only. Whether a request to cancel the
      operation has been received. For operations that have been cancelled
      successfully, the Operation.error field contains the error code
      CANCELLED.
    statusMessage: Output only. The status of the operation, if any.
    target: Output only. The server-defined resource path for the target of
      the operation.
    verb: Output only. The name of the action executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  errorMessage = _messages.StringField(4)
  predictionMetadata = _messages.MessageField('PredictionMetadata', 5)
  requestedCancellation = _messages.BooleanField(6)
  statusMessage = _messages.StringField(7)
  target = _messages.StringField(8)
  verb = _messages.StringField(9)


class OrgPolicyConstraint(_messages.Message):
  r"""A predefined organization policy constraint.

  Fields:
    cannedConstraintId: Required. A unique identifier for the constraint.
    policyRules: Required. The rules enforced by the constraint.
  """

  cannedConstraintId = _messages.StringField(1)
  policyRules = _messages.MessageField('GoogleCloudSecuritypostureV1alphaPolicyRule', 2, repeated=True)


class OrgPolicyConstraintCustom(_messages.Message):
  r"""A custom organization policy constraint.

  Fields:
    customConstraint: Required. Metadata for the constraint.
    policyRules: Required. The rules enforced by the constraint.
  """

  customConstraint = _messages.MessageField('GoogleCloudSecuritypostureV1alphaCustomConstraint', 1)
  policyRules = _messages.MessageField('GoogleCloudSecuritypostureV1alphaPolicyRule', 2, repeated=True)


class OutputData(_messages.Message):
  r"""Output data for a remediation intent. This contains the output data for
  the generated remediation which returns the updated terraform files.

  Fields:
    tfData: Optional. Output Terraform file information.
  """

  tfData = _messages.MessageField('TfData', 1)


class Policy(_messages.Message):
  r"""The details of a policy, including the constraints that it includes.

  Fields:
    complianceStandards: Optional. The compliance standards that the policy
      helps enforce.
    constraint: Required. The constraints that the policy includes.
    description: Optional. A description of the policy.
    findingCategory: Output only. Finding category of the asset violation
      findings that will be generated on the deployment of the policy.
    policyId: Required. A user-specified identifier for the policy. In a
      PolicySet, each policy must have a unique identifier.
  """

  complianceStandards = _messages.MessageField('ComplianceStandard', 1, repeated=True)
  constraint = _messages.MessageField('Constraint', 2)
  description = _messages.StringField(3)
  findingCategory = _messages.StringField(4)
  policyId = _messages.StringField(5)


class PolicyDetails(_messages.Message):
  r"""Details of a policy that was violated.

  Enums:
    ConstraintTypeValueValuesEnum: The type of constraint that was violated.

  Fields:
    complianceStandards: The compliance standards that the policy maps to. For
      example, `CIS-2.0 1.15`.
    constraint: Information about the constraint that was violated. The format
      of this information can change at any time without prior notice. Your
      application must not depend on this information in any way.
    constraintType: The type of constraint that was violated.
    description: A description of the policy.
  """

  class ConstraintTypeValueValuesEnum(_messages.Enum):
    r"""The type of constraint that was violated.

    Values:
      CONSTRAINT_TYPE_UNSPECIFIED: Default value. This value is unused.
      SECURITY_HEALTH_ANALYTICS_CUSTOM_MODULE: A custom module for Security
        Health Analytics.
      ORG_POLICY_CUSTOM: A custom organization policy constraint.
      SECURITY_HEALTH_ANALYTICS_MODULE: A built-in detector for Security
        Health Analytics.
      ORG_POLICY: A predefined organization policy constraint.
      REGO_POLICY: A custom rego policy constraint.
    """
    CONSTRAINT_TYPE_UNSPECIFIED = 0
    SECURITY_HEALTH_ANALYTICS_CUSTOM_MODULE = 1
    ORG_POLICY_CUSTOM = 2
    SECURITY_HEALTH_ANALYTICS_MODULE = 3
    ORG_POLICY = 4
    REGO_POLICY = 5

  complianceStandards = _messages.StringField(1, repeated=True)
  constraint = _messages.StringField(2)
  constraintType = _messages.EnumField('ConstraintTypeValueValuesEnum', 3)
  description = _messages.StringField(4)


class PolicySet(_messages.Message):
  r"""A group of one or more Policy resources.

  Fields:
    description: Optional. A description of the policy set.
    policies: Required. The Policy resources in the policy set. Each policy
      must have a policy_id that's unique within the policy set.
    policySetId: Required. An identifier for the policy set.
  """

  description = _messages.StringField(1)
  policies = _messages.MessageField('Policy', 2, repeated=True)
  policySetId = _messages.StringField(3)


class PortRange(_messages.Message):
  r"""A port range which is inclusive of the min and max values. Values are
  between 0 and 2^16-1. The max can be equal / must be not smaller than the
  min value. If min and max are equal this indicates that it is a single port.

  Fields:
    max: Required. Maximum port value.
    min: Required. Minimum port value.
  """

  max = _messages.IntegerField(1)
  min = _messages.IntegerField(2)


class Posture(_messages.Message):
  r"""The details of a posture.

  Enums:
    CategoriesValueListEntryValuesEnum:
    PostureTypeValueValuesEnum: Optional. Immutable. The type of the posture.
      The default value is SECURITY.
    StateValueValuesEnum: Required. The state of the posture at the specified
      `revision_id`.

  Messages:
    AnnotationsValue: Optional. The user-specified annotations for the
      posture. For details about the values you can use in an annotation, see
      [AIP-148: Standard fields](https://google.aip.dev/148#annotations).

  Fields:
    annotations: Optional. The user-specified annotations for the posture. For
      details about the values you can use in an annotation, see [AIP-148:
      Standard fields](https://google.aip.dev/148#annotations).
    categories: Output only. The categories that the posture belongs to, as
      determined by the Security Posture API.
    createTime: Output only. The time at which the posture was created.
    description: Optional. A description of the posture.
    etag: Optional. An opaque identifier for the current version of the
      posture at the specified `revision_id`. To prevent concurrent updates
      from overwriting each other, always provide the `etag` when you update a
      posture. You can also provide the `etag` when you delete a posture, to
      help ensure that you're deleting the intended version of the posture.
    name: Required. Identifier. The name of the posture, in the format
      `organizations/{organization}/locations/global/postures/{posture_id}`.
    policySets: Required. The PolicySet resources that the posture includes.
    postureType: Optional. Immutable. The type of the posture. The default
      value is SECURITY.
    reconciling: Output only. Whether the posture is in the process of being
      updated.
    revisionId: Output only. Immutable. An opaque eight-character string that
      identifies the revision of the posture. A posture can have multiple
      revisions; when you deploy a posture, you deploy a specific revision of
      the posture.
    state: Required. The state of the posture at the specified `revision_id`.
    updateTime: Output only. The time at which the posture was last updated.
  """

  class CategoriesValueListEntryValuesEnum(_messages.Enum):
    r"""CategoriesValueListEntryValuesEnum enum type.

    Values:
      CATEGORY_UNSPECIFIED: Default value. This value is unused.
      AI: Artificial intelligence (AI).
      AWS: Amazon Web Services (AWS) policies.
      GCP: Google Cloud policies.
      AZURE: Microsoft Azure policies.
      GEMINI_ASSISTED: Postures created using assistence from Gemini.
    """
    CATEGORY_UNSPECIFIED = 0
    AI = 1
    AWS = 2
    GCP = 3
    AZURE = 4
    GEMINI_ASSISTED = 5

  class PostureTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Immutable. The type of the posture. The default value is
    SECURITY.

    Values:
      POSTURE_TYPE_UNSPECIFIED: Default value. This value is unused.
      SECURITY_POSTURE: A security posture.
      SECURITY: <no description>
      DATA_POSTURE: A data posture.
      DATA_SECURITY: <no description>
    """
    POSTURE_TYPE_UNSPECIFIED = 0
    SECURITY_POSTURE = 1
    SECURITY = 2
    DATA_POSTURE = 3
    DATA_SECURITY = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""Required. The state of the posture at the specified `revision_id`.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      DEPRECATED: The posture is deprecated and can no longer be deployed.
      DRAFT: The posture is a draft and is not ready to deploy.
      ACTIVE: The posture is complete and ready to deploy.
    """
    STATE_UNSPECIFIED = 0
    DEPRECATED = 1
    DRAFT = 2
    ACTIVE = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. The user-specified annotations for the posture. For details
    about the values you can use in an annotation, see [AIP-148: Standard
    fields](https://google.aip.dev/148#annotations).

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  categories = _messages.EnumField('CategoriesValueListEntryValuesEnum', 2, repeated=True)
  createTime = _messages.StringField(3)
  description = _messages.StringField(4)
  etag = _messages.StringField(5)
  name = _messages.StringField(6)
  policySets = _messages.MessageField('PolicySet', 7, repeated=True)
  postureType = _messages.EnumField('PostureTypeValueValuesEnum', 8)
  reconciling = _messages.BooleanField(9)
  revisionId = _messages.StringField(10)
  state = _messages.EnumField('StateValueValuesEnum', 11)
  updateTime = _messages.StringField(12)


class PostureDeployment(_messages.Message):
  r"""Details for a Posture deployment on an organization, folder, or project.
  You can deploy at most one posture to each organization, folder, or project.
  The parent resource for a posture deployment is always the organization,
  even if the deployment applies to a folder or project.

  Enums:
    CategoriesValueListEntryValuesEnum:
    StateValueValuesEnum: Output only. The state of the posture deployment.

  Messages:
    AnnotationsValue: Optional. The user-specified annotations for the posture
      deployment. For details about the values you can use in an annotation,
      see [AIP-148: Standard fields](https://google.aip.dev/148#annotations).

  Fields:
    annotations: Optional. The user-specified annotations for the posture
      deployment. For details about the values you can use in an annotation,
      see [AIP-148: Standard fields](https://google.aip.dev/148#annotations).
    categories: Output only. The categories that the posture deployment
      belongs to, as determined by the Security Posture API.
    createTime: Output only. The time at which the posture deployment was
      created.
    description: Optional. A description of the posture deployment.
    desiredPostureId: Output only. The posture ID that was specified for the
      deployment. Present only if the posture deployment is in a failed state.
    desiredPostureRevisionId: Output only. The revision ID of the posture that
      was specified for the deployment. Present only if the deployment is in a
      failed state.
    etag: Optional. An opaque identifier for the current version of the
      posture deployment. To prevent concurrent updates from overwriting each
      other, always provide the `etag` when you update a posture deployment.
      You can also provide the `etag` when you delete a posture deployment, to
      help ensure that you're deleting the intended posture deployment.
    failureMessage: Output only. A description of why the posture deployment
      failed. Present only if the deployment is in a failed state.
    name: Required. Identifier. The name of the posture deployment, in the
      format `organizations/{organization}/locations/global/postureDeployments
      /{deployment_id}`.
    postureId: Required. The posture used in the deployment, in the format
      `organizations/{organization}/locations/global/postures/{posture_id}`.
    postureRevisionId: Required. The revision ID of the posture used in the
      deployment.
    reconciling: Output only. Whether the posture deployment is in the process
      of being updated.
    state: Output only. The state of the posture deployment.
    targetResource: Required. The organization, folder, or project where the
      posture is deployed. Uses one of the following formats: *
      `organizations/{organization_number}` * `folders/{folder_number}` *
      `projects/{project_number}`
    updateTime: Output only. The time at which the posture deployment was last
      updated.
  """

  class CategoriesValueListEntryValuesEnum(_messages.Enum):
    r"""CategoriesValueListEntryValuesEnum enum type.

    Values:
      CATEGORY_UNSPECIFIED: Default value. This value is unused.
      AI: Artificial intelligence (AI).
      AWS: Amazon Web Services (AWS) policies.
      GCP: Google Cloud policies.
      AZURE: Microsoft Azure policies.
      GEMINI_ASSISTED: Postures created using assistence from Gemini.
    """
    CATEGORY_UNSPECIFIED = 0
    AI = 1
    AWS = 2
    GCP = 3
    AZURE = 4
    GEMINI_ASSISTED = 5

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the posture deployment.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      CREATING: The posture deployment is being created.
      DELETING: The posture deployment is being deleted.
      UPDATING: The posture deployment is being updated.
      ACTIVE: The posture deployment is active and in use.
      CREATE_FAILED: The posture deployment could not be created.
      UPDATE_FAILED: The posture deployment could not be updated.
      DELETE_FAILED: The posture deployment could not be deleted.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    DELETING = 2
    UPDATING = 3
    ACTIVE = 4
    CREATE_FAILED = 5
    UPDATE_FAILED = 6
    DELETE_FAILED = 7

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. The user-specified annotations for the posture deployment.
    For details about the values you can use in an annotation, see [AIP-148:
    Standard fields](https://google.aip.dev/148#annotations).

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  categories = _messages.EnumField('CategoriesValueListEntryValuesEnum', 2, repeated=True)
  createTime = _messages.StringField(3)
  description = _messages.StringField(4)
  desiredPostureId = _messages.StringField(5)
  desiredPostureRevisionId = _messages.StringField(6)
  etag = _messages.StringField(7)
  failureMessage = _messages.StringField(8)
  name = _messages.StringField(9)
  postureId = _messages.StringField(10)
  postureRevisionId = _messages.StringField(11)
  reconciling = _messages.BooleanField(12)
  state = _messages.EnumField('StateValueValuesEnum', 13)
  targetResource = _messages.StringField(14)
  updateTime = _messages.StringField(15)


class PostureDetails(_messages.Message):
  r"""Details of a posture deployment.

  Fields:
    policySet: The identifier for the PolicySet that the relevant policy
      belongs to.
    posture: The posture used in the deployment, in the format
      `organizations/{organization}/locations/global/postures/{posture_id}`.
    postureDeployment: The name of the posture deployment, in the format `orga
      nizations/{organization}/locations/global/postureDeployments/{deployment
      _id}`.
    postureDeploymentTargetResource: The organization, folder, or project
      where the posture is deployed. Uses one of the following formats: *
      `organizations/{organization_number}` * `folders/{folder_number}` *
      `projects/{project_number}`
    postureRevisionId: The revision ID of the posture used in the deployment.
  """

  policySet = _messages.StringField(1)
  posture = _messages.StringField(2)
  postureDeployment = _messages.StringField(3)
  postureDeploymentTargetResource = _messages.StringField(4)
  postureRevisionId = _messages.StringField(5)


class PostureTemplate(_messages.Message):
  r"""The details of a posture template.

  Enums:
    CategoriesValueListEntryValuesEnum:
    StateValueValuesEnum: Output only. The state of the posture template at
      the specified `revision_id`.

  Fields:
    categories: Output only. The categories that the posture template belongs
      to, as determined by the Security Posture API.
    description: Output only. A description of the posture template.
    name: Output only. Identifier. The name of the posture template, in the
      format `organizations/{organization}/locations/global/postureTemplates/{
      posture_template}`.
    policySets: Output only. The PolicySet resources that the posture template
      includes.
    revisionId: Output only. A string that identifies the revision of the
      posture template.
    state: Output only. The state of the posture template at the specified
      `revision_id`.
  """

  class CategoriesValueListEntryValuesEnum(_messages.Enum):
    r"""CategoriesValueListEntryValuesEnum enum type.

    Values:
      CATEGORY_UNSPECIFIED: Default value. This value is unused.
      AI: Artificial intelligence (AI).
      AWS: Amazon Web Services (AWS) policies.
      GCP: Google Cloud policies.
      AZURE: Microsoft Azure policies.
      GEMINI_ASSISTED: Postures created using assistence from Gemini.
    """
    CATEGORY_UNSPECIFIED = 0
    AI = 1
    AWS = 2
    GCP = 3
    AZURE = 4
    GEMINI_ASSISTED = 5

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the posture template at the specified
    `revision_id`.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      ACTIVE: The posture template follows the latest controls and standards.
      DEPRECATED: The posture template uses outdated controls and standards.
        We recommend that you use a newer revision of the posture template.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    DEPRECATED = 2

  categories = _messages.EnumField('CategoriesValueListEntryValuesEnum', 1, repeated=True)
  description = _messages.StringField(2)
  name = _messages.StringField(3)
  policySets = _messages.MessageField('PolicySet', 4, repeated=True)
  revisionId = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)


class Prediction(_messages.Message):
  r"""Definition of the resource 'Prediction'. It is an AI generated
  prediction.

  Enums:
    PredictionTypeValueValuesEnum: The type of prediction.

  Fields:
    createTime: Output only. The timestamp when the prediction was created.
    environmentOptions: EnvironmentOptions used to generate this prediction.
    name: Required. Identifier. The name of this Prediction resource, in the
      format of organizations/{organization}/locations/global/predictions/{pre
      dictionID}.
    posture: The Posture predicted.
    predictionType: The type of prediction.
  """

  class PredictionTypeValueValuesEnum(_messages.Enum):
    r"""The type of prediction.

    Values:
      PREDICTION_TYPE_UNSPECIFIED: Prediction type unspecified.
      BASIC_POSTURE: Basic predefined posture prediction type.
      NATURAL_LANGUAGE_QUERY: Posture prediction type to query predictions
        based on an intent provided in the request.
      ADVANCED_POSTURE: Advanced posture prediction type.
    """
    PREDICTION_TYPE_UNSPECIFIED = 0
    BASIC_POSTURE = 1
    NATURAL_LANGUAGE_QUERY = 2
    ADVANCED_POSTURE = 3

  createTime = _messages.StringField(1)
  environmentOptions = _messages.MessageField('EnvironmentOptions', 2)
  name = _messages.StringField(3)
  posture = _messages.MessageField('Posture', 4)
  predictionType = _messages.EnumField('PredictionTypeValueValuesEnum', 5)


class PredictionMetadata(_messages.Message):
  r"""Metadata for Prediction LRO.

  Enums:
    OperationStateValueValuesEnum: Output only. Progress state of the given
      operation.

  Fields:
    environmentOptions: Output only. The environment options used to generate
      the prediction.
    operationState: Output only. Progress state of the given operation.
  """

  class OperationStateValueValuesEnum(_messages.Enum):
    r"""Output only. Progress state of the given operation.

    Values:
      OPERATION_STATE_UNSPECIFIED: Operation state unspecified.
      ENQUEUED: Operation is enqueued.
      ROUTING_USER_INTENT: Operation is routing user intent.
      GENERATING_SHA_BUILT_IN_POLICIES: Operation is generating SHA built-in
        policies.
      GENERATING_SHA_CUSTOM_POLICIES: Operation is generating SHA custom
        policies.
      GENERATING_ORG_BUILT_IN_POLICIES: Operation is generating org built-in
        policies.
      GENERATING_ORG_CUSTOM_POLICIES: Operation is generating org custom
        policies.
      GENERATING_REGO_POLICIES: Operation is generating Rego policies.
      FILTERING_POLICIES: Operation is filtering policies.
      GENERATING_PREDICTION: Operation is generating prediction.
      PREDICTION_GENERATED: Operation has generated prediction.
      ADDING_POLICIES: Operation is adding policies for natural language
        query.
    """
    OPERATION_STATE_UNSPECIFIED = 0
    ENQUEUED = 1
    ROUTING_USER_INTENT = 2
    GENERATING_SHA_BUILT_IN_POLICIES = 3
    GENERATING_SHA_CUSTOM_POLICIES = 4
    GENERATING_ORG_BUILT_IN_POLICIES = 5
    GENERATING_ORG_CUSTOM_POLICIES = 6
    GENERATING_REGO_POLICIES = 7
    FILTERING_POLICIES = 8
    GENERATING_PREDICTION = 9
    PREDICTION_GENERATED = 10
    ADDING_POLICIES = 11

  environmentOptions = _messages.MessageField('EnvironmentOptions', 1)
  operationState = _messages.EnumField('OperationStateValueValuesEnum', 2)


class Property(_messages.Message):
  r"""A name-value pair used as a custom source property.

  Fields:
    name: Required. The name of the custom source property.
    valueExpression: Optional. The CEL expression for the value of the custom
      source property. For resource properties, you can return the value of
      the property or a string enclosed in quotation marks.
  """

  name = _messages.StringField(1)
  valueExpression = _messages.MessageField('Expr', 2)


class PullRequest(_messages.Message):
  r"""Pull request information.

  Fields:
    comments: Optional. Comments on the pull request.
    modifiedFileOwners: Optional. Modified file owners.
    modifiedFilePaths: Optional. Modified file paths.
    remediationExplanation: Optional. Explanation of the remediation.
    url: Optional. URL of the pull request.
  """

  comments = _messages.StringField(1)
  modifiedFileOwners = _messages.StringField(2, repeated=True)
  modifiedFilePaths = _messages.StringField(3, repeated=True)
  remediationExplanation = _messages.StringField(4)
  url = _messages.StringField(5)


class RegoPolicy(_messages.Message):
  r"""Message for Rego policy constraint.

  Enums:
    SeverityValueValuesEnum: Optional. Severity of the asset violation against
      this rego policy.

  Fields:
    definition: Required. Rego language based policy captured in string
      format. Total size of rego policy should not exceed 24KB.
    id: Required. The unique identifier (ID) for the rego policy. It should be
      unique across the posture. The regex pattern for id should be ^A-Za-z*$.
      This field is also used as the finding category for all the asset
      violation findings that the detector returns.
    nextSteps: Optional. Next steps required to fix an asset violation against
      this rego policy.
    severity: Optional. Severity of the asset violation against this rego
      policy.
  """

  class SeverityValueValuesEnum(_messages.Enum):
    r"""Optional. Severity of the asset violation against this rego policy.

    Values:
      SEVERITY_UNSPECIFIED: Unspecified severity.
      CRITICAL: Critical severity.
      HIGH: High severity.
      MEDIUM: Medium severity. This is the default severity if the severity is
        unknown.
      LOW: Low severity.
    """
    SEVERITY_UNSPECIFIED = 0
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4

  definition = _messages.StringField(1)
  id = _messages.StringField(2)
  nextSteps = _messages.StringField(3)
  severity = _messages.EnumField('SeverityValueValuesEnum', 4)


class RemediationArtifacts(_messages.Message):
  r"""Details related to artifacts produced for the intent eg PR info, and
  owner identification.

  Fields:
    prData: Raised pull request information.
  """

  prData = _messages.MessageField('PullRequest', 1)


class RemediationInput(_messages.Message):
  r"""Input data to be used for a remediation intent. Also contains error
  message in case of failure.

  Fields:
    errorDetails: Output only. Error details in case of failure.
    tfData: Optional. Input Terraform file information.
  """

  errorDetails = _messages.MessageField('ErrorDetails', 1)
  tfData = _messages.MessageField('TfData', 2)


class RemediationIntent(_messages.Message):
  r"""Definition of the resource 'RemediationIntent'.

  Enums:
    IacTypeValueValuesEnum: Optional. Type of IAC for the remediation intent.
    StateValueValuesEnum: Output only. State of the remediation intent.
    WorkflowTypeValueValuesEnum: Required. Type of workflow for the
      remediation intent.

  Fields:
    createTime: Output only. The timestamp when the remediation intent was
      created.
    errorDetails: Output only. Error details in case of failure.
    etag: Optional. To prevent concurrent updates from overwriting each other,
      always provide the `etag` when you update a remediation intent. You can
      also provide the `etag` when you delete a remediation intent, to help
      ensure that you're deleting the intended version of the remediation
      intent.
    findingData: Output only. SCC findings data, fields like finding name,
      severity, category, resource etc.
    iacType: Optional. Type of IAC for the remediation intent.
    name: Required. Identifier. The name of this Remediation Intent resource,
      in the format of
      organizations/{organization}/locations/{location}/remediationIntents/.
    remediatedOutput: Output only. Output remediated files data generated
      using LLM having code fix.
    remediationArtifacts: Output only. Details related to the artifacts
      generated for the remediation, eg Pull Request.
    remediationInput: Required. Input files data required for the remediation
      of the intent.
    repositoryData: Relevant repository data for which intent is created.
    state: Output only. State of the remediation intent.
    workflowType: Required. Type of workflow for the remediation intent.
  """

  class IacTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Type of IAC for the remediation intent.

    Values:
      IAC_TYPE_UNSPECIFIED: Unspecified IAC type.
      IAC_TYPE_TERRAFORM: Terraform IAC type.
    """
    IAC_TYPE_UNSPECIFIED = 0
    IAC_TYPE_TERRAFORM = 1

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the remediation intent.

    Values:
      STATE_UNSPECIFIED: Unspecified remediation intent state.
      REMEDIATION_INTENT_ENQUEUED: Remediation intent is enqueued.
      REMEDIATION_IN_PROGRESS: Remediation for the intent is in progress.
      REMEDIATION_FAILED: Remediation for the intent process has failed.
      REMEDIATION_SUCCESS: Remediation generation by LLM is successful.
      PR_GENERATION_SUCCESS: PR generated successfully.
      PR_GENERATION_FAILED: PR generation failed, post remediation successful
        creation.
    """
    STATE_UNSPECIFIED = 0
    REMEDIATION_INTENT_ENQUEUED = 1
    REMEDIATION_IN_PROGRESS = 2
    REMEDIATION_FAILED = 3
    REMEDIATION_SUCCESS = 4
    PR_GENERATION_SUCCESS = 5
    PR_GENERATION_FAILED = 6

  class WorkflowTypeValueValuesEnum(_messages.Enum):
    r"""Required. Type of workflow for the remediation intent.

    Values:
      WORKFLOW_TYPE_UNSPECIFIED: Workflow type unspecified.
      WORKFLOW_TYPE_MANUAL: Workflow type is manual.
      WORKFLOW_TYPE_SEMI_AUTONOMOUS: Semi autonomous workflow type, triggered
        periodically.
    """
    WORKFLOW_TYPE_UNSPECIFIED = 0
    WORKFLOW_TYPE_MANUAL = 1
    WORKFLOW_TYPE_SEMI_AUTONOMOUS = 2

  createTime = _messages.StringField(1)
  errorDetails = _messages.MessageField('ErrorDetails', 2)
  etag = _messages.StringField(3)
  findingData = _messages.MessageField('Finding', 4)
  iacType = _messages.EnumField('IacTypeValueValuesEnum', 5)
  name = _messages.StringField(6)
  remediatedOutput = _messages.MessageField('RemediationOutput', 7)
  remediationArtifacts = _messages.MessageField('RemediationArtifacts', 8)
  remediationInput = _messages.MessageField('RemediationInput', 9)
  repositoryData = _messages.MessageField('RepositoryData', 10)
  state = _messages.EnumField('StateValueValuesEnum', 11)
  workflowType = _messages.EnumField('WorkflowTypeValueValuesEnum', 12)


class RemediationOutput(_messages.Message):
  r"""Remediated output data for a remediation intent.

  Fields:
    outputData: Optional. Output data for the remediation intent. The field is
      repeated to support multiple output data for a single remediation intent
      for multiple iam bindings.
    remediationExplanation: Optional. Explanation of the remediation. The
      field is used to support multiple explanations for a single remediation
      intent for multiple iam bindings.
  """

  outputData = _messages.MessageField('OutputData', 1, repeated=True)
  remediationExplanation = _messages.StringField(2)


class RepositoryData(_messages.Message):
  r"""Relevant repository related data for the intent.

  Fields:
    repositoryUrl: Required. Repository url.
  """

  repositoryUrl = _messages.StringField(1)


class ResourceSelector(_messages.Message):
  r"""A selector for the resource types to run the detector on.

  Fields:
    resourceTypes: Required. The resource types to run the detector on. Each
      custom module can specify up to 5 resource types.
  """

  resourceTypes = _messages.StringField(1, repeated=True)


class ResourceTypes(_messages.Message):
  r"""Set multiple resource types for one policy, for example: resourceTypes:
  included: - compute.googleapis.com/Instance - compute.googleapis.com/Disk
  Constraint definition contains an empty resource type in order to support
  multiple resource types in the policy. Only supports managed constraints.
  Method type is `GOVERN_TAGS`. Refer go/multi-resource-support-force-tags-gmc
  to get more details.

  Fields:
    included: Optional. The resource types we currently support.
      cloud/orgpolicy/customconstraintconfig/prod/resource_types.prototext
  """

  included = _messages.StringField(1, repeated=True)


class SecurityHealthAnalyticsCustomModule(_messages.Message):
  r"""A custom module for Security Health Analytics.

  Enums:
    ModuleEnablementStateValueValuesEnum: Whether the custom module is enabled
      at a specified level of the resource hierarchy.

  Fields:
    config: Required. Configuration settings for the custom module.
    displayName: Optional. The display name of the custom module. This value
      is used as the finding category for all the asset violation findings
      that the custom module returns. The display name must contain between 1
      and 128 alphanumeric characters or underscores, and it must start with a
      lowercase letter.
    id: Output only. Immutable. The unique identifier for the custom module.
      Contains 1 to 20 digits.
    moduleEnablementState: Whether the custom module is enabled at a specified
      level of the resource hierarchy.
  """

  class ModuleEnablementStateValueValuesEnum(_messages.Enum):
    r"""Whether the custom module is enabled at a specified level of the
    resource hierarchy.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      ENABLED: The detector or custom module is enabled.
      DISABLED: The detector or custom module is disabled.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  config = _messages.MessageField('CustomConfig', 1)
  displayName = _messages.StringField(2)
  id = _messages.StringField(3)
  moduleEnablementState = _messages.EnumField('ModuleEnablementStateValueValuesEnum', 4)


class SecurityHealthAnalyticsModule(_messages.Message):
  r"""A built-in detector for Security Health Analytics.

  Enums:
    ModuleEnablementStateValueValuesEnum: Whether the detector is enabled at a
      specified level of the resource hierarchy.

  Fields:
    moduleEnablementState: Whether the detector is enabled at a specified
      level of the resource hierarchy.
    moduleName: Required. The name of the detector. For example,
      `BIGQUERY_TABLE_CMEK_DISABLED`. This field is also used as the finding
      category for all the asset violation findings that the detector returns.
  """

  class ModuleEnablementStateValueValuesEnum(_messages.Enum):
    r"""Whether the detector is enabled at a specified level of the resource
    hierarchy.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      ENABLED: The detector or custom module is enabled.
      DISABLED: The detector or custom module is disabled.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  moduleEnablementState = _messages.EnumField('ModuleEnablementStateValueValuesEnum', 1)
  moduleName = _messages.StringField(2)


class SecuritypostureOrganizationsLocationsFindingRemediationExecutionsCreateRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsFindingRemediationExecutionsCreat
  eRequest object.

  Fields:
    createFindingRemediationExecutionRequest: A
      CreateFindingRemediationExecutionRequest resource to be passed as the
      request body.
    parent: Required. Parent is the organisation id of the finding. The format
      of this value is as follows:
      organizations/{organization}/locations/global
  """

  createFindingRemediationExecutionRequest = _messages.MessageField('CreateFindingRemediationExecutionRequest', 1)
  parent = _messages.StringField(2, required=True)


class SecuritypostureOrganizationsLocationsGetIacValidationReportMetricsRequest(_messages.Message):
  r"""A
  SecuritypostureOrganizationsLocationsGetIacValidationReportMetricsRequest
  object.

  Fields:
    duration_days: Optional. Duration in days.
    name: Required. Name of the resource. The format of this value is as
      follows: `organizations/{organization}/locations/global/iacValidationRep
      ortMetrics`
  """

  duration_days = _messages.IntegerField(1)
  name = _messages.StringField(2, required=True)


class SecuritypostureOrganizationsLocationsOperationsCancelRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class SecuritypostureOrganizationsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class SecuritypostureOrganizationsLocationsOperationsGetRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class SecuritypostureOrganizationsLocationsOperationsListRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class SecuritypostureOrganizationsLocationsPostureDeploymentsCreateRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsPostureDeploymentsCreateRequest
  object.

  Fields:
    parent: Required. The parent resource name, in the format
      `organizations/{organization}/locations/global`.
    postureDeployment: A PostureDeployment resource to be passed as the
      request body.
    postureDeploymentId: Required. An identifier for the posture deployment.
  """

  parent = _messages.StringField(1, required=True)
  postureDeployment = _messages.MessageField('PostureDeployment', 2)
  postureDeploymentId = _messages.StringField(3)


class SecuritypostureOrganizationsLocationsPostureDeploymentsDeleteRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsPostureDeploymentsDeleteRequest
  object.

  Fields:
    etag: Optional. An opaque identifier for the current version of the
      posture deployment. If you provide this value, then it must match the
      existing value. If the values don't match, then the request fails with
      an ABORTED error. If you omit this value, then the posture deployment is
      deleted regardless of its current `etag` value.
    name: Required. The name of the posture deployment, in the format `organiz
      ations/{organization}/locations/global/postureDeployments/{posture_id}`.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class SecuritypostureOrganizationsLocationsPostureDeploymentsGetRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsPostureDeploymentsGetRequest
  object.

  Fields:
    name: Required. The name of the PostureDeployment, in the format `organiza
      tions/{organization}/locations/global/postureDeployments/{posture_deploy
      ment_id}`.
  """

  name = _messages.StringField(1, required=True)


class SecuritypostureOrganizationsLocationsPostureDeploymentsListRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsPostureDeploymentsListRequest
  object.

  Fields:
    filter: Optional. A filter to apply to the list of postures, in the format
      defined in [AIP-160: Filtering](https://google.aip.dev/160).
    pageSize: Optional. The maximum number of posture deployments to return.
      The default value is `500`. If you exceed the maximum value of `1000`,
      then the service uses the maximum value.
    pageToken: Optional. A pagination token returned from a previous request
      to list posture deployments. Provide this token to retrieve the next
      page of results.
    parent: Required. The parent resource name, in the format
      `organizations/{organization}/locations/global`.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SecuritypostureOrganizationsLocationsPostureDeploymentsPatchRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsPostureDeploymentsPatchRequest
  object.

  Fields:
    name: Required. Identifier. The name of the posture deployment, in the
      format `organizations/{organization}/locations/global/postureDeployments
      /{deployment_id}`.
    postureDeployment: A PostureDeployment resource to be passed as the
      request body.
    updateMask: Required. The fields in the PostureDeployment to update. You
      can update only the following fields: * PostureDeployment.posture_id *
      PostureDeployment.posture_revision_id
  """

  name = _messages.StringField(1, required=True)
  postureDeployment = _messages.MessageField('PostureDeployment', 2)
  updateMask = _messages.StringField(3)


class SecuritypostureOrganizationsLocationsPostureTemplatesGetRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsPostureTemplatesGetRequest
  object.

  Fields:
    name: Required. The name of the PostureTemplate, in the format `organizati
      ons/{organization}/locations/global/postureTemplates/{posture_template}`
      .
    revisionId: Optional. The posture template revision to retrieve. If not
      specified, the most recently updated revision is retrieved.
  """

  name = _messages.StringField(1, required=True)
  revisionId = _messages.StringField(2)


class SecuritypostureOrganizationsLocationsPostureTemplatesListRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsPostureTemplatesListRequest
  object.

  Fields:
    filter: Optional. A filter to apply to the list of postures, in the format
      defined in [AIP-160: Filtering](https://google.aip.dev/160).
    pageSize: Optional. The maximum number of posture templates to return. The
      default value is `500`. If you exceed the maximum value of `1000`, then
      the service uses the maximum value.
    pageToken: Optional. A pagination token returned from a previous request
      to list posture templates. Provide this token to retrieve the next page
      of results.
    parent: Required. The parent resource name, in the format
      `organizations/{organization}/locations/global`.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SecuritypostureOrganizationsLocationsPosturesCreateRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsPosturesCreateRequest object.

  Fields:
    parent: Required. The parent resource name, in the format
      `organizations/{organization}/locations/global`.
    posture: A Posture resource to be passed as the request body.
    postureId: Required. An identifier for the posture.
  """

  parent = _messages.StringField(1, required=True)
  posture = _messages.MessageField('Posture', 2)
  postureId = _messages.StringField(3)


class SecuritypostureOrganizationsLocationsPosturesDeleteRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsPosturesDeleteRequest object.

  Fields:
    etag: Optional. An opaque identifier for the current version of the
      posture. If you provide this value, then it must match the existing
      value. If the values don't match, then the request fails with an ABORTED
      error. If you omit this value, then the posture is deleted regardless of
      its current `etag` value.
    name: Required. The name of the Posture, in the format
      `organizations/{organization}/locations/global/postures/{posture_id}`.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class SecuritypostureOrganizationsLocationsPosturesExtractRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsPosturesExtractRequest object.

  Fields:
    extractPostureRequest: A ExtractPostureRequest resource to be passed as
      the request body.
    parent: Required. The parent resource name, in the format
      `organizations/{organization}/locations/global`.
  """

  extractPostureRequest = _messages.MessageField('ExtractPostureRequest', 1)
  parent = _messages.StringField(2, required=True)


class SecuritypostureOrganizationsLocationsPosturesGetRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsPosturesGetRequest object.

  Fields:
    name: Required. The name of the Posture, in the format
      `organizations/{organization}/locations/global/postures/{posture_id}`.
    revisionId: Optional. The posture revision to retrieve. If not specified,
      the most recently updated revision is retrieved.
  """

  name = _messages.StringField(1, required=True)
  revisionId = _messages.StringField(2)


class SecuritypostureOrganizationsLocationsPosturesListRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsPosturesListRequest object.

  Fields:
    filter: Optional. A filter to apply to the list of postures, in the format
      defined in [AIP-160: Filtering](https://google.aip.dev/160).
    pageSize: The maximum number of postures to return. The default value is
      `500`. If you exceed the maximum value of `1000`, then the service uses
      the maximum value.
    pageToken: A pagination token returned from a previous request to list
      postures. Provide this token to retrieve the next page of results.
    parent: Required. The parent resource name, in the format
      `organizations/{organization}/locations/global`.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SecuritypostureOrganizationsLocationsPosturesListRevisionsRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsPosturesListRevisionsRequest
  object.

  Fields:
    name: Required. The name of the Posture, in the format
      `organizations/{organization}/locations/global/postures/{posture_id}`.
    pageSize: Optional. The maximum number of posture revisions to return. The
      default value is `500`. If you exceed the maximum value of `1000`, then
      the service uses the maximum value.
    pageToken: Optional. A pagination token from a previous request to list
      posture revisions. Provide this token to retrieve the next page of
      results.
  """

  name = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class SecuritypostureOrganizationsLocationsPosturesPatchRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsPosturesPatchRequest object.

  Fields:
    name: Required. Identifier. The name of the posture, in the format
      `organizations/{organization}/locations/global/postures/{posture_id}`.
    posture: A Posture resource to be passed as the request body.
    revisionId: Required. The revision ID of the posture to update. If the
      posture revision that you update is currently deployed, then a new
      revision of the posture is created.
    updateMask: Required. The fields in the Posture to update. You can update
      only the following fields: * Posture.description * Posture.policy_sets *
      Posture.state
  """

  name = _messages.StringField(1, required=True)
  posture = _messages.MessageField('Posture', 2)
  revisionId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class SecuritypostureOrganizationsLocationsPredictionsCreatePredictionRequest(_messages.Message):
  r"""A
  SecuritypostureOrganizationsLocationsPredictionsCreatePredictionRequest
  object.

  Fields:
    createPredictionRequest: A CreatePredictionRequest resource to be passed
      as the request body.
    parent: Required. The parent resource name, in the format
      `organizations/{organization}/locations/global`.
  """

  createPredictionRequest = _messages.MessageField('CreatePredictionRequest', 1)
  parent = _messages.StringField(2, required=True)


class SecuritypostureOrganizationsLocationsPredictionsGetRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsPredictionsGetRequest object.

  Fields:
    name: Required. Name of the resource. The format of this value is as
      follows: `organizations/{organization}/locations/global/predictions/{pre
      dictionID}`
  """

  name = _messages.StringField(1, required=True)


class SecuritypostureOrganizationsLocationsPredictionsListRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsPredictionsListRequest object.

  Fields:
    filter: Optional. Filter to be applied on the resource, defined by EBNF
      grammar https://google.aip.dev/assets/misc/ebnf-filtering.txt.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListPredictionsRequest. The format of
      this value is as follows:
      `organizations/{organization}/locations/global`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SecuritypostureOrganizationsLocationsRemediationIntentsCreateRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsRemediationIntentsCreateRequest
  object.

  Fields:
    createRemediationIntentRequest: A CreateRemediationIntentRequest resource
      to be passed as the request body.
    parent: Required. The parent resource name. The format of this value is as
      follows: `organizations/{organization}/locations/{location}`
  """

  createRemediationIntentRequest = _messages.MessageField('CreateRemediationIntentRequest', 1)
  parent = _messages.StringField(2, required=True)


class SecuritypostureOrganizationsLocationsRemediationIntentsDeleteRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsRemediationIntentsDeleteRequest
  object.

  Fields:
    etag: Optional. An opaque identifier for the current version of the
      remediation intent. If you provide this value, then it must match the
      existing value. If the values don't match, then the request fails with
      an ABORTED error. If you omit this value, then the remediation intent is
      deleted regardless of its current `etag` value.
    name: Required. The name of the RemediationIntent, in the format `organiza
      tions/{organization}/locations/global/remediationIntents/{remediationInt
      ent_id}`.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class SecuritypostureOrganizationsLocationsRemediationIntentsGetRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsRemediationIntentsGetRequest
  object.

  Fields:
    name: Required. Name of the resource. The format of this value is as
      follows: `organizations/{organization}/locations/{location}/remediationI
      ntents/{intentID}`
  """

  name = _messages.StringField(1, required=True)


class SecuritypostureOrganizationsLocationsRemediationIntentsListRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsRemediationIntentsListRequest
  object.

  Fields:
    filter: Optional. Filter to be applied on the resource, defined by EBNF
      grammar https://google.aip.dev/assets/misc/ebnf-filtering.txt.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListRemediationIntentsRequest. The
      format of this value is as follows:
      `organizations/{organization}/locations/{location}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SecuritypostureOrganizationsLocationsRemediationIntentsPatchRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsRemediationIntentsPatchRequest
  object.

  Fields:
    name: Required. Identifier. The name of this Remediation Intent resource,
      in the format of
      organizations/{organization}/locations/{location}/remediationIntents/.
    remediationIntent: A RemediationIntent resource to be passed as the
      request body.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the RemediationIntent resource by the update method. You
      can update only the following fields depending on the state transition:
      * RemediationIntent.state (MUST for any transition) *
      RemediationIntent.remediation_input (update to IN_PROGRESS state) *
      RemediationIntent.remediation_artifacts (update to PR_GENERATION_SUCCESS
      state) * RemediationIntent.error_details (update to PR_GENERATION_FAILED
      state) Also the allowable state transitions are: *
      RemediationIntent.State.ENQUEUED to RemediationIntent.State.IN_PROGRESS
      * RemediationIntent.State.REMEDIATION_SUCCESS to
      RemediationIntent.State.PR_GENERATION_SUCCESS *
      RemediationIntent.State.REMEDIATION_SUCCESS to
      RemediationIntent.State.PR_GENERATION_FAILED The fields specified in the
      update_mask are relative to the resource, not the full request. A field
      will be overwritten if it is in the mask. If the user does not provide a
      mask then all relevant fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  remediationIntent = _messages.MessageField('RemediationIntent', 2)
  updateMask = _messages.StringField(3)


class SecuritypostureOrganizationsLocationsReportsCreateIaCValidationReportRequest(_messages.Message):
  r"""A
  SecuritypostureOrganizationsLocationsReportsCreateIaCValidationReportRequest
  object.

  Fields:
    createIaCValidationReportRequest: A CreateIaCValidationReportRequest
      resource to be passed as the request body.
    parent: Required. The parent resource name, in the format
      `organizations/{organization}/locations/global`.
  """

  createIaCValidationReportRequest = _messages.MessageField('CreateIaCValidationReportRequest', 1)
  parent = _messages.StringField(2, required=True)


class SecuritypostureOrganizationsLocationsReportsGetRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsReportsGetRequest object.

  Fields:
    name: Required. The name of the report, in the format
      `organizations/{organization}/locations/global/reports/{report_id}`.
  """

  name = _messages.StringField(1, required=True)


class SecuritypostureOrganizationsLocationsReportsListRequest(_messages.Message):
  r"""A SecuritypostureOrganizationsLocationsReportsListRequest object.

  Fields:
    filter: Optional. A filter to apply to the list of reports, in the format
      defined in [AIP-160: Filtering](https://google.aip.dev/160).
    pageSize: Optional. The maximum number of reports to return. The default
      value is `500`. If you exceed the maximum value of `1000`, then the
      service uses the maximum value.
    pageToken: Optional. A pagination token returned from a previous request
      to list reports. Provide this token to retrieve the next page of
      results.
    parent: Required. The parent resource name, in the format
      `organizations/{organization}/locations/global`.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TfData(_messages.Message):
  r"""Stores terraform data like tf state information and tf files

  Fields:
    fileData: Required. Terraform files data
    tfStateInfo: Optional. Terraform state information
  """

  fileData = _messages.MessageField('FileData', 1, repeated=True)
  tfStateInfo = _messages.StringField(2)


class ViolatedPolicy(_messages.Message):
  r"""message of violated policy.

  Fields:
    constraint: Name of the violated policy.
    constraintType: Type of the violated policy.
  """

  constraint = _messages.StringField(1)
  constraintType = _messages.StringField(2)


class Violation(_messages.Message):
  r"""Details of a violation.

  Enums:
    SeverityValueValuesEnum: The severity of the violation.

  Fields:
    assetId: The full resource name of the asset that caused the violation.
      For details about the format of the full resource name for each asset
      type, see [Resource name format](https://cloud.google.com/asset-
      inventory/docs/resource-name-format).
    nextSteps: A description of the steps that you can take to fix the
      violation.
    policyId: The policy that was violated.
    severity: The severity of the violation.
    violatedAsset: Details of the Cloud Asset Inventory asset that caused the
      violation.
    violatedPolicy: Details of the policy that was violated.
    violatedPosture: Details for the posture that was violated. This field is
      present only if the violated policy belongs to a deployed posture.
  """

  class SeverityValueValuesEnum(_messages.Enum):
    r"""The severity of the violation.

    Values:
      SEVERITY_UNSPECIFIED: Default value. This value is unused.
      CRITICAL: Critical severity.
      HIGH: High severity.
      MEDIUM: Medium severity.
      LOW: Low severity.
    """
    SEVERITY_UNSPECIFIED = 0
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4

  assetId = _messages.StringField(1)
  nextSteps = _messages.StringField(2)
  policyId = _messages.StringField(3)
  severity = _messages.EnumField('SeverityValueValuesEnum', 4)
  violatedAsset = _messages.MessageField('AssetDetails', 5)
  violatedPolicy = _messages.MessageField('PolicyDetails', 6)
  violatedPosture = _messages.MessageField('PostureDetails', 7)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    SecuritypostureOrganizationsLocationsGetIacValidationReportMetricsRequest, 'duration_days', 'duration.days')
