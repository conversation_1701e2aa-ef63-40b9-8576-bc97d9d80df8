"""Generated message classes for cloudidentity version v1beta1.

API for provisioning and managing identity resources.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'cloudidentity'


class AddIdpCredentialOperationMetadata(_messages.Message):
  r"""LRO response metadata for
  InboundSamlSsoProfilesService.AddIdpCredential.

  Fields:
    state: State of this Operation Will be "awaiting-multi-party-approval"
      when the operation is deferred due to the target customer having enabled
      [Multi-party approval for sensitive
      actions](https://support.google.com/a/answer/13790448).
  """

  state = _messages.StringField(1)


class AddIdpCredentialRequest(_messages.Message):
  r"""The request for creating an IdpCredential with its associated payload.
  An InboundSamlSsoProfile can own up to 2 credentials.

  Fields:
    pemData: PEM encoded x509 certificate containing the public key for
      verifying IdP signatures.
  """

  pemData = _messages.StringField(1)


class AndroidAttributes(_messages.Message):
  r"""Resource representing the Android specific attributes of a Device.

  Enums:
    OwnershipPrivilegeValueValuesEnum: Ownership privileges on device.

  Fields:
    ctsProfileMatch: Whether the device passes Android CTS compliance.
    enabledUnknownSources: Whether applications from unknown sources can be
      installed on device.
    hasPotentiallyHarmfulApps: Whether any potentially harmful apps were
      detected on the device.
    ownerProfileAccount: Whether this account is on an owner/primary profile.
      For phones, only true for owner profiles. Android 4+ devices can have
      secondary or restricted user profiles.
    ownershipPrivilege: Ownership privileges on device.
    supportsWorkProfile: Whether device supports Android work profiles. If
      false, this service will not block access to corp data even if an
      administrator turns on the "Enforce Work Profile" policy.
    verifiedBoot: Whether Android verified boot status is GREEN.
    verifyAppsEnabled: Whether Google Play Protect Verify Apps is enabled.
  """

  class OwnershipPrivilegeValueValuesEnum(_messages.Enum):
    r"""Ownership privileges on device.

    Values:
      OWNERSHIP_PRIVILEGE_UNSPECIFIED: Ownership privilege is not set.
      DEVICE_ADMINISTRATOR: Active device administrator privileges on the
        device.
      PROFILE_OWNER: Profile Owner privileges. The account is in a managed
        corporate profile.
      DEVICE_OWNER: Device Owner privileges on the device.
    """
    OWNERSHIP_PRIVILEGE_UNSPECIFIED = 0
    DEVICE_ADMINISTRATOR = 1
    PROFILE_OWNER = 2
    DEVICE_OWNER = 3

  ctsProfileMatch = _messages.BooleanField(1)
  enabledUnknownSources = _messages.BooleanField(2)
  hasPotentiallyHarmfulApps = _messages.BooleanField(3)
  ownerProfileAccount = _messages.BooleanField(4)
  ownershipPrivilege = _messages.EnumField('OwnershipPrivilegeValueValuesEnum', 5)
  supportsWorkProfile = _messages.BooleanField(6)
  verifiedBoot = _messages.BooleanField(7)
  verifyAppsEnabled = _messages.BooleanField(8)


class ApproveDeviceUserRequest(_messages.Message):
  r"""Request message for approving the device to access user data.

  Fields:
    customer: Optional. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      customer. If you're using this API for your own organization, use
      `customers/my_customer` If you're using this API to manage another
      organization, use `customers/{customer_id}`, where customer_id is the
      customer to whom the device belongs.
  """

  customer = _messages.StringField(1)


class ApproveDeviceUserResponse(_messages.Message):
  r"""Response message for approving the device to access user data.

  Fields:
    deviceUser: Resultant DeviceUser object for the action.
  """

  deviceUser = _messages.MessageField('DeviceUser', 1)


class BlockDeviceUserRequest(_messages.Message):
  r"""Request message for blocking account on device.

  Fields:
    customer: Optional. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      customer. If you're using this API for your own organization, use
      `customers/my_customer` If you're using this API to manage another
      organization, use `customers/{customer_id}`, where customer_id is the
      customer to whom the device belongs.
  """

  customer = _messages.StringField(1)


class BlockDeviceUserResponse(_messages.Message):
  r"""Response message for blocking the device from accessing user data.

  Fields:
    deviceUser: Resultant DeviceUser object for the action.
  """

  deviceUser = _messages.MessageField('DeviceUser', 1)


class BrowserAttributes(_messages.Message):
  r"""Contains information about browser profiles reported by the [Endpoint
  Verification extension](https://chromewebstore.google.com/detail/endpoint-
  verification/callobklhcbilhphinckomhgkigmfocg?pli=1).

  Fields:
    chromeBrowserInfo: Represents the current state of the [Chrome browser
      attributes](https://cloud.google.com/access-context-
      manager/docs/browser-attributes) sent by the [Endpoint Verification
      extension](https://chromewebstore.google.com/detail/endpoint-
      verification/callobklhcbilhphinckomhgkigmfocg?pli=1).
    chromeProfileId: Chrome profile ID that is exposed by the Chrome API. It
      is unique for each device.
    lastProfileSyncTime: Timestamp in milliseconds since the Unix epoch when
      the profile/gcm id was last synced.
  """

  chromeBrowserInfo = _messages.MessageField('BrowserInfo', 1)
  chromeProfileId = _messages.StringField(2)
  lastProfileSyncTime = _messages.StringField(3)


class BrowserInfo(_messages.Message):
  r"""Browser-specific fields reported by the [Endpoint Verification
  extension](https://chromewebstore.google.com/detail/endpoint-
  verification/callobklhcbilhphinckomhgkigmfocg?pli=1).

  Enums:
    BrowserManagementStateValueValuesEnum: Output only. Browser's management
      state.
    PasswordProtectionWarningTriggerValueValuesEnum: Current state of
      [password protection trigger](https://chromeenterprise.google/policies/#
      PasswordProtectionWarningTrigger).
    SafeBrowsingProtectionLevelValueValuesEnum: Current state of [Safe
      Browsing protection level](https://chromeenterprise.google/policies/#Saf
      eBrowsingProtectionLevel).

  Fields:
    browserManagementState: Output only. Browser's management state.
    browserVersion: Version of the request initiating browser. E.g.
      `91.0.4442.4`.
    isBuiltInDnsClientEnabled: Current state of [built-in DNS client](https://
      chromeenterprise.google/policies/#BuiltInDnsClientEnabled).
    isBulkDataEntryAnalysisEnabled: Current state of [bulk data analysis](http
      s://chromeenterprise.google/policies/#OnBulkDataEntryEnterpriseConnector
      ). Set to true if provider list from Chrome is non-empty.
    isChromeCleanupEnabled: Current state of [Chrome
      Cleanup](https://chromeenterprise.google/policies/#ChromeCleanupEnabled)
      .
    isChromeRemoteDesktopAppBlocked: Current state of [Chrome Remote Desktop
      app](https://chromeenterprise.google/policies/#URLBlocklist).
    isFileDownloadAnalysisEnabled: Current state of [file download analysis](h
      ttps://chromeenterprise.google/policies/#OnFileDownloadedEnterpriseConne
      ctor). Set to true if provider list from Chrome is non-empty.
    isFileUploadAnalysisEnabled: Current state of [file upload analysis](https
      ://chromeenterprise.google/policies/#OnFileAttachedEnterpriseConnector).
      Set to true if provider list from Chrome is non-empty.
    isRealtimeUrlCheckEnabled: Current state of [real-time URL check](https://
      chromeenterprise.google/policies/#EnterpriseRealTimeUrlCheckMode). Set
      to true if provider list from Chrome is non-empty.
    isSecurityEventAnalysisEnabled: Current state of [security event analysis]
      (https://chromeenterprise.google/policies/#OnSecurityEventEnterpriseConn
      ector). Set to true if provider list from Chrome is non-empty.
    isSiteIsolationEnabled: Current state of [site isolation](https://chromeen
      terprise.google/policies/?policy=IsolateOrigins).
    isThirdPartyBlockingEnabled: Current state of [third-party blocking](https
      ://chromeenterprise.google/policies/#ThirdPartyBlockingEnabled).
    passwordProtectionWarningTrigger: Current state of [password protection tr
      igger](https://chromeenterprise.google/policies/#PasswordProtectionWarni
      ngTrigger).
    safeBrowsingProtectionLevel: Current state of [Safe Browsing protection le
      vel](https://chromeenterprise.google/policies/#SafeBrowsingProtectionLev
      el).
  """

  class BrowserManagementStateValueValuesEnum(_messages.Enum):
    r"""Output only. Browser's management state.

    Values:
      UNSPECIFIED: Management state is not specified.
      UNMANAGED: Browser/Profile is not managed by any customer.
      MANAGED_BY_OTHER_DOMAIN: Browser/Profile is managed, but by some other
        customer.
      PROFILE_MANAGED: Profile is managed by customer.
      BROWSER_MANAGED: Browser is managed by customer.
    """
    UNSPECIFIED = 0
    UNMANAGED = 1
    MANAGED_BY_OTHER_DOMAIN = 2
    PROFILE_MANAGED = 3
    BROWSER_MANAGED = 4

  class PasswordProtectionWarningTriggerValueValuesEnum(_messages.Enum):
    r"""Current state of [password protection trigger](https://chromeenterpris
    e.google/policies/#PasswordProtectionWarningTrigger).

    Values:
      PASSWORD_PROTECTION_TRIGGER_UNSPECIFIED: Password protection is not
        specified.
      PROTECTION_OFF: Password reuse is never detected.
      PASSWORD_REUSE: Warning is shown when the user reuses their protected
        password on a non-allowed site.
      PHISHING_REUSE: Warning is shown when the user reuses their protected
        password on a phishing site.
    """
    PASSWORD_PROTECTION_TRIGGER_UNSPECIFIED = 0
    PROTECTION_OFF = 1
    PASSWORD_REUSE = 2
    PHISHING_REUSE = 3

  class SafeBrowsingProtectionLevelValueValuesEnum(_messages.Enum):
    r"""Current state of [Safe Browsing protection level](https://chromeenterp
    rise.google/policies/#SafeBrowsingProtectionLevel).

    Values:
      SAFE_BROWSING_LEVEL_UNSPECIFIED: Browser protection level is not
        specified.
      DISABLED: No protection against dangerous websites, downloads, and
        extensions.
      STANDARD: Standard protection against websites, downloads, and
        extensions that are known to be dangerous.
      ENHANCED: Faster, proactive protection against dangerous websites,
        downloads, and extensions.
    """
    SAFE_BROWSING_LEVEL_UNSPECIFIED = 0
    DISABLED = 1
    STANDARD = 2
    ENHANCED = 3

  browserManagementState = _messages.EnumField('BrowserManagementStateValueValuesEnum', 1)
  browserVersion = _messages.StringField(2)
  isBuiltInDnsClientEnabled = _messages.BooleanField(3)
  isBulkDataEntryAnalysisEnabled = _messages.BooleanField(4)
  isChromeCleanupEnabled = _messages.BooleanField(5)
  isChromeRemoteDesktopAppBlocked = _messages.BooleanField(6)
  isFileDownloadAnalysisEnabled = _messages.BooleanField(7)
  isFileUploadAnalysisEnabled = _messages.BooleanField(8)
  isRealtimeUrlCheckEnabled = _messages.BooleanField(9)
  isSecurityEventAnalysisEnabled = _messages.BooleanField(10)
  isSiteIsolationEnabled = _messages.BooleanField(11)
  isThirdPartyBlockingEnabled = _messages.BooleanField(12)
  passwordProtectionWarningTrigger = _messages.EnumField('PasswordProtectionWarningTriggerValueValuesEnum', 13)
  safeBrowsingProtectionLevel = _messages.EnumField('SafeBrowsingProtectionLevelValueValuesEnum', 14)


class CancelUserInvitationRequest(_messages.Message):
  r"""Request to cancel sent invitation for target email in UserInvitation."""


class CancelWipeDeviceRequest(_messages.Message):
  r"""Request message for cancelling an unfinished device wipe.

  Fields:
    customer: Optional. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      customer. If you're using this API for your own organization, use
      `customers/my_customer` If you're using this API to manage another
      organization, use `customers/{customer_id}`, where customer_id is the
      customer to whom the device belongs.
  """

  customer = _messages.StringField(1)


class CancelWipeDeviceResponse(_messages.Message):
  r"""Response message for cancelling an unfinished device wipe.

  Fields:
    device: Resultant Device object for the action. Note that asset tags will
      not be returned in the device object.
  """

  device = _messages.MessageField('Device', 1)


class CancelWipeDeviceUserRequest(_messages.Message):
  r"""Request message for cancelling an unfinished user account wipe.

  Fields:
    customer: Optional. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      customer. If you're using this API for your own organization, use
      `customers/my_customer` If you're using this API to manage another
      organization, use `customers/{customer_id}`, where customer_id is the
      customer to whom the device belongs.
  """

  customer = _messages.StringField(1)


class CancelWipeDeviceUserResponse(_messages.Message):
  r"""Response message for cancelling an unfinished user account wipe.

  Fields:
    deviceUser: Resultant DeviceUser object for the action.
  """

  deviceUser = _messages.MessageField('DeviceUser', 1)


class CertificateAttributes(_messages.Message):
  r"""Stores information about a certificate.

  Enums:
    ValidationStateValueValuesEnum: Validation state of this certificate.

  Fields:
    certificateTemplate: The X.509 extension for CertificateTemplate.
    fingerprint: The encoded certificate fingerprint.
    issuer: The name of the issuer of this certificate.
    serialNumber: Serial number of the certificate, Example: "*********".
    subject: The subject name of this certificate.
    thumbprint: The certificate thumbprint.
    validationState: Validation state of this certificate.
    validityExpirationTime: Certificate not valid at or after this timestamp.
    validityStartTime: Certificate not valid before this timestamp.
  """

  class ValidationStateValueValuesEnum(_messages.Enum):
    r"""Validation state of this certificate.

    Values:
      CERTIFICATE_VALIDATION_STATE_UNSPECIFIED: Default value.
      VALIDATION_SUCCESSFUL: Certificate validation was successful.
      VALIDATION_FAILED: Certificate validation failed.
    """
    CERTIFICATE_VALIDATION_STATE_UNSPECIFIED = 0
    VALIDATION_SUCCESSFUL = 1
    VALIDATION_FAILED = 2

  certificateTemplate = _messages.MessageField('CertificateTemplate', 1)
  fingerprint = _messages.StringField(2)
  issuer = _messages.StringField(3)
  serialNumber = _messages.StringField(4)
  subject = _messages.StringField(5)
  thumbprint = _messages.StringField(6)
  validationState = _messages.EnumField('ValidationStateValueValuesEnum', 7)
  validityExpirationTime = _messages.StringField(8)
  validityStartTime = _messages.StringField(9)


class CertificateTemplate(_messages.Message):
  r"""CertificateTemplate (v3 Extension in X.509).

  Fields:
    id: The template id of the template. Example: "*******.4.1.311.21.8.156086
      21.11768144.5720724.16068415.6889630.81.2472537.7784047".
    majorVersion: The Major version of the template. Example: 100.
    minorVersion: The minor version of the template. Example: 12.
  """

  id = _messages.StringField(1)
  majorVersion = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  minorVersion = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class CheckTransitiveMembershipResponse(_messages.Message):
  r"""The response message for MembershipsService.CheckTransitiveMembership.

  Fields:
    hasMembership: Response does not include the possible roles of a member
      since the behavior of this rpc is not all-or-nothing unlike the other
      rpcs. So, it may not be possible to list all the roles definitively, due
      to possible lack of authorization in some of the paths.
  """

  hasMembership = _messages.BooleanField(1)


class ClientState(_messages.Message):
  r"""Represents the state associated with an API client calling the Devices
  API. Resource representing ClientState and supports updates from API users

  Enums:
    ComplianceStateValueValuesEnum: The compliance state of the resource as
      specified by the API client.
    HealthScoreValueValuesEnum: The Health score of the resource
    ManagedValueValuesEnum: The management state of the resource as specified
      by the API client.
    OwnerTypeValueValuesEnum: Output only. The owner of the ClientState

  Messages:
    KeyValuePairsValue: The map of key-value attributes stored by callers
      specific to a device. The total serialized length of this map may not
      exceed 10KB. No limit is placed on the number of attributes in a map.

  Fields:
    assetTags: The caller can specify asset tags for this resource
    complianceState: The compliance state of the resource as specified by the
      API client.
    createTime: Output only. The time the client state data was created.
    customId: This field may be used to store a unique identifier for the API
      resource within which these CustomAttributes are a field.
    etag: The token that needs to be passed back for concurrency control in
      updates. Token needs to be passed back in UpdateRequest
    healthScore: The Health score of the resource
    keyValuePairs: The map of key-value attributes stored by callers specific
      to a device. The total serialized length of this map may not exceed
      10KB. No limit is placed on the number of attributes in a map.
    lastUpdateTime: Output only. The time the client state data was last
      updated.
    managed: The management state of the resource as specified by the API
      client.
    name: Output only. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      ClientState in format: `devices/{device_id}/deviceUsers/{device_user_id}
      /clientState/{partner_id}`, where partner_id corresponds to the partner
      storing the data.
    ownerType: Output only. The owner of the ClientState
    scoreReason: A descriptive cause of the health score.
  """

  class ComplianceStateValueValuesEnum(_messages.Enum):
    r"""The compliance state of the resource as specified by the API client.

    Values:
      COMPLIANCE_STATE_UNSPECIFIED: The compliance state of the resource is
        unknown or unspecified.
      COMPLIANT: Device is compliant with third party policies
      NON_COMPLIANT: Device is not compliant with third party policies
    """
    COMPLIANCE_STATE_UNSPECIFIED = 0
    COMPLIANT = 1
    NON_COMPLIANT = 2

  class HealthScoreValueValuesEnum(_messages.Enum):
    r"""The Health score of the resource

    Values:
      HEALTH_SCORE_UNSPECIFIED: Default value
      VERY_POOR: The object is in very poor health as defined by the caller.
      POOR: The object is in poor health as defined by the caller.
      NEUTRAL: The object health is neither good nor poor, as defined by the
        caller.
      GOOD: The object is in good health as defined by the caller.
      VERY_GOOD: The object is in very good health as defined by the caller.
    """
    HEALTH_SCORE_UNSPECIFIED = 0
    VERY_POOR = 1
    POOR = 2
    NEUTRAL = 3
    GOOD = 4
    VERY_GOOD = 5

  class ManagedValueValuesEnum(_messages.Enum):
    r"""The management state of the resource as specified by the API client.

    Values:
      MANAGED_STATE_UNSPECIFIED: The management state of the resource is
        unknown or unspecified.
      MANAGED: The resource is managed.
      UNMANAGED: The resource is not managed.
    """
    MANAGED_STATE_UNSPECIFIED = 0
    MANAGED = 1
    UNMANAGED = 2

  class OwnerTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The owner of the ClientState

    Values:
      OWNER_TYPE_UNSPECIFIED: Unknown owner type
      OWNER_TYPE_CUSTOMER: Customer is the owner
      OWNER_TYPE_PARTNER: Partner is the owner
    """
    OWNER_TYPE_UNSPECIFIED = 0
    OWNER_TYPE_CUSTOMER = 1
    OWNER_TYPE_PARTNER = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class KeyValuePairsValue(_messages.Message):
    r"""The map of key-value attributes stored by callers specific to a
    device. The total serialized length of this map may not exceed 10KB. No
    limit is placed on the number of attributes in a map.

    Messages:
      AdditionalProperty: An additional property for a KeyValuePairsValue
        object.

    Fields:
      additionalProperties: Additional properties of type KeyValuePairsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a KeyValuePairsValue object.

      Fields:
        key: Name of the additional property.
        value: A CustomAttributeValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('CustomAttributeValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  assetTags = _messages.StringField(1, repeated=True)
  complianceState = _messages.EnumField('ComplianceStateValueValuesEnum', 2)
  createTime = _messages.StringField(3)
  customId = _messages.StringField(4)
  etag = _messages.StringField(5)
  healthScore = _messages.EnumField('HealthScoreValueValuesEnum', 6)
  keyValuePairs = _messages.MessageField('KeyValuePairsValue', 7)
  lastUpdateTime = _messages.StringField(8)
  managed = _messages.EnumField('ManagedValueValuesEnum', 9)
  name = _messages.StringField(10)
  ownerType = _messages.EnumField('OwnerTypeValueValuesEnum', 11)
  scoreReason = _messages.StringField(12)


class CloudidentityCustomersUserinvitationsCancelRequest(_messages.Message):
  r"""A CloudidentityCustomersUserinvitationsCancelRequest object.

  Fields:
    cancelUserInvitationRequest: A CancelUserInvitationRequest resource to be
      passed as the request body.
    name: Required. `UserInvitation` name in the format
      `customers/{customer}/userinvitations/{user_email_address}`
  """

  cancelUserInvitationRequest = _messages.MessageField('CancelUserInvitationRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudidentityCustomersUserinvitationsGetRequest(_messages.Message):
  r"""A CloudidentityCustomersUserinvitationsGetRequest object.

  Fields:
    name: Required. `UserInvitation` name in the format
      `customers/{customer}/userinvitations/{user_email_address}`
  """

  name = _messages.StringField(1, required=True)


class CloudidentityCustomersUserinvitationsIsInvitableUserRequest(_messages.Message):
  r"""A CloudidentityCustomersUserinvitationsIsInvitableUserRequest object.

  Fields:
    name: Required. `UserInvitation` name in the format
      `customers/{customer}/userinvitations/{user_email_address}`
  """

  name = _messages.StringField(1, required=True)


class CloudidentityCustomersUserinvitationsListRequest(_messages.Message):
  r"""A CloudidentityCustomersUserinvitationsListRequest object.

  Fields:
    filter: Optional. A query string for filtering `UserInvitation` results by
      their current state, in the format: `"state=='invited'"`.
    orderBy: Optional. The sort order of the list results. You can sort the
      results in descending order based on either email or last update
      timestamp but not both, using `order_by="email desc"`. Currently,
      sorting is supported for `update_time asc`, `update_time desc`, `email
      asc`, and `email desc`. If not specified, results will be returned based
      on `email asc` order.
    pageSize: Optional. The maximum number of UserInvitation resources to
      return. If unspecified, at most 100 resources will be returned. The
      maximum value is 200; values above 200 will be set to 200.
    pageToken: Optional. A page token, received from a previous
      `ListUserInvitations` call. Provide this to retrieve the subsequent
      page. When paginating, all other parameters provided to `ListBooks` must
      match the call that provided the page token.
    parent: Required. The customer ID of the Google Workspace or Cloud
      Identity account the UserInvitation resources are associated with.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class CloudidentityCustomersUserinvitationsSendRequest(_messages.Message):
  r"""A CloudidentityCustomersUserinvitationsSendRequest object.

  Fields:
    name: Required. `UserInvitation` name in the format
      `customers/{customer}/userinvitations/{user_email_address}`
    sendUserInvitationRequest: A SendUserInvitationRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  sendUserInvitationRequest = _messages.MessageField('SendUserInvitationRequest', 2)


class CloudidentityDevicesCancelWipeRequest(_messages.Message):
  r"""A CloudidentityDevicesCancelWipeRequest object.

  Fields:
    cancelWipeDeviceRequest: A CancelWipeDeviceRequest resource to be passed
      as the request body.
    name: Required. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the Device
      in format: `devices/{device_id}`, where device_id is the unique ID
      assigned to the Device.
  """

  cancelWipeDeviceRequest = _messages.MessageField('CancelWipeDeviceRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudidentityDevicesDeleteRequest(_messages.Message):
  r"""A CloudidentityDevicesDeleteRequest object.

  Fields:
    customer: Optional. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      customer. If you're using this API for your own organization, use
      `customers/my_customer` If you're using this API to manage another
      organization, use `customers/{customer_id}`, where customer_id is the
      customer to whom the device belongs.
    name: Required. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the Device
      in format: `devices/{device_id}`, where device_id is the unique ID
      assigned to the Device.
  """

  customer = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class CloudidentityDevicesDeviceUsersApproveRequest(_messages.Message):
  r"""A CloudidentityDevicesDeviceUsersApproveRequest object.

  Fields:
    approveDeviceUserRequest: A ApproveDeviceUserRequest resource to be passed
      as the request body.
    name: Required. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the Device
      in format: `devices/{device_id}/deviceUsers/{device_user_id}`, where
      device_id is the unique ID assigned to the Device, and device_user_id is
      the unique ID assigned to the User.
  """

  approveDeviceUserRequest = _messages.MessageField('ApproveDeviceUserRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudidentityDevicesDeviceUsersBlockRequest(_messages.Message):
  r"""A CloudidentityDevicesDeviceUsersBlockRequest object.

  Fields:
    blockDeviceUserRequest: A BlockDeviceUserRequest resource to be passed as
      the request body.
    name: Required. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the Device
      in format: `devices/{device_id}/deviceUsers/{device_user_id}`, where
      device_id is the unique ID assigned to the Device, and device_user_id is
      the unique ID assigned to the User.
  """

  blockDeviceUserRequest = _messages.MessageField('BlockDeviceUserRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudidentityDevicesDeviceUsersCancelWipeRequest(_messages.Message):
  r"""A CloudidentityDevicesDeviceUsersCancelWipeRequest object.

  Fields:
    cancelWipeDeviceUserRequest: A CancelWipeDeviceUserRequest resource to be
      passed as the request body.
    name: Required. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the Device
      in format: `devices/{device_id}/deviceUsers/{device_user_id}`, where
      device_id is the unique ID assigned to the Device, and device_user_id is
      the unique ID assigned to the User.
  """

  cancelWipeDeviceUserRequest = _messages.MessageField('CancelWipeDeviceUserRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudidentityDevicesDeviceUsersClientStatesGetRequest(_messages.Message):
  r"""A CloudidentityDevicesDeviceUsersClientStatesGetRequest object.

  Fields:
    customer: Optional. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      customer. If you're using this API for your own organization, use
      `customers/my_customer` If you're using this API to manage another
      organization, use `customers/{customer_id}`, where customer_id is the
      customer to whom the device belongs.
    name: Required. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      ClientState in format: `devices/{device_id}/deviceUsers/{device_user_id}
      /clientStates/{partner_id}`, where `device_id` is the unique ID assigned
      to the Device, `device_user_id` is the unique ID assigned to the User
      and `partner_id` identifies the partner storing the data. To get the
      client state for devices belonging to your own organization, the
      `partnerId` is in the format: `customerId-*anystring*`. Where the
      `customerId` is your organization's customer ID and `anystring` is any
      suffix. This suffix is used in setting up Custom Access Levels in
      Context-Aware Access. You may use `my_customer` instead of the customer
      ID for devices managed by your own organization. You may specify `-` in
      place of the `{device_id}`, so the ClientState resource name can be: `de
      vices/-
      /deviceUsers/{device_user_resource_id}/clientStates/{partner_id}`.
  """

  customer = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class CloudidentityDevicesDeviceUsersClientStatesPatchRequest(_messages.Message):
  r"""A CloudidentityDevicesDeviceUsersClientStatesPatchRequest object.

  Fields:
    clientState: A ClientState resource to be passed as the request body.
    customer: Optional. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      customer. If you're using this API for your own organization, use
      `customers/my_customer` If you're using this API to manage another
      organization, use `customers/{customer_id}`, where customer_id is the
      customer to whom the device belongs.
    name: Output only. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      ClientState in format: `devices/{device_id}/deviceUsers/{device_user_id}
      /clientState/{partner_id}`, where partner_id corresponds to the partner
      storing the data.
    updateMask: Optional. Comma-separated list of fully qualified names of
      fields to be updated. If not specified, all updatable fields in
      ClientState are updated.
  """

  clientState = _messages.MessageField('ClientState', 1)
  customer = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  updateMask = _messages.StringField(4)


class CloudidentityDevicesDeviceUsersDeleteRequest(_messages.Message):
  r"""A CloudidentityDevicesDeviceUsersDeleteRequest object.

  Fields:
    customer: Optional. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      customer. If you're using this API for your own organization, use
      `customers/my_customer` If you're using this API to manage another
      organization, use `customers/{customer_id}`, where customer_id is the
      customer to whom the device belongs.
    name: Required. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the Device
      in format: `devices/{device_id}/deviceUsers/{device_user_id}`, where
      device_id is the unique ID assigned to the Device, and device_user_id is
      the unique ID assigned to the User.
  """

  customer = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class CloudidentityDevicesDeviceUsersGetRequest(_messages.Message):
  r"""A CloudidentityDevicesDeviceUsersGetRequest object.

  Fields:
    customer: Optional. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      customer. If you're using this API for your own organization, use
      `customers/my_customer` If you're using this API to manage another
      organization, use `customers/{customer_id}`, where customer_id is the
      customer to whom the device belongs.
    name: Required. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the Device
      in format: `devices/{device_id}/deviceUsers/{device_user_id}`, where
      device_id is the unique ID assigned to the Device, and device_user_id is
      the unique ID assigned to the User.
  """

  customer = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class CloudidentityDevicesDeviceUsersListRequest(_messages.Message):
  r"""A CloudidentityDevicesDeviceUsersListRequest object.

  Fields:
    customer: Optional. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      customer. If you're using this API for your own organization, use
      `customers/my_customer` If you're using this API to manage another
      organization, use `customers/{customer_id}`, where customer_id is the
      customer to whom the device belongs.
    filter: Optional. Additional restrictions when fetching list of devices.
      For a list of search fields, refer to [Mobile device search
      fields](https://developers.google.com/admin-sdk/directory/v1/search-
      operators). Multiple search fields are separated by the space character.
    orderBy: Optional. Order specification for devices in the response.
    pageSize: Optional. The maximum number of DeviceUsers to return. If
      unspecified, at most 5 DeviceUsers will be returned. The maximum value
      is 20; values above 20 will be coerced to 20.
    pageToken: Optional. A page token, received from a previous
      `ListDeviceUsers` call. Provide this to retrieve the subsequent page.
      When paginating, all other parameters provided to `ListBooks` must match
      the call that provided the page token.
    parent: Required. To list all DeviceUsers, set this to "devices/-". To
      list all DeviceUsers owned by a device, set this to the resource name of
      the device. Format: devices/{device}
  """

  customer = _messages.StringField(1)
  filter = _messages.StringField(2)
  orderBy = _messages.StringField(3)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)
  parent = _messages.StringField(6, required=True)


class CloudidentityDevicesDeviceUsersLookupRequest(_messages.Message):
  r"""A CloudidentityDevicesDeviceUsersLookupRequest object.

  Fields:
    androidId: Android Id returned by [Settings.Secure#ANDROID_ID](https://dev
      eloper.android.com/reference/android/provider/Settings.Secure.html#ANDRO
      ID_ID).
    pageSize: The maximum number of DeviceUsers to return. If unspecified, at
      most 20 DeviceUsers will be returned. The maximum value is 20; values
      above 20 will be coerced to 20.
    pageToken: A page token, received from a previous `LookupDeviceUsers`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `LookupDeviceUsers` must match the call
      that provided the page token.
    parent: Must be set to "devices/-/deviceUsers" to search across all
      DeviceUser belonging to the user.
    rawResourceId: Raw Resource Id used by Google Endpoint Verification. If
      the user is enrolled into Google Endpoint Verification, this id will be
      saved as the 'device_resource_id' field in the following platform
      dependent files. Mac: ~/.secureConnect/context_aware_config.json
      Windows:
      C:\\Users\<USER>\.secureConnect\context_aware_config.json Linux:
      ~/.secureConnect/context_aware_config.json
    userId: The user whose DeviceUser's resource name will be fetched. Must be
      set to 'me' to fetch the DeviceUser's resource name for the calling
      user.
  """

  androidId = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  rawResourceId = _messages.StringField(5)
  userId = _messages.StringField(6)


class CloudidentityDevicesDeviceUsersWipeRequest(_messages.Message):
  r"""A CloudidentityDevicesDeviceUsersWipeRequest object.

  Fields:
    name: Required. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the Device
      in format: `devices/{device_id}/deviceUsers/{device_user_id}`, where
      device_id is the unique ID assigned to the Device, and device_user_id is
      the unique ID assigned to the User.
    wipeDeviceUserRequest: A WipeDeviceUserRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  wipeDeviceUserRequest = _messages.MessageField('WipeDeviceUserRequest', 2)


class CloudidentityDevicesGetRequest(_messages.Message):
  r"""A CloudidentityDevicesGetRequest object.

  Fields:
    customer: Optional. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      Customer in format: `customers/{customer_id}`, where customer_id is the
      customer to whom the device belongs.
    name: Required. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the Device
      in format: `devices/{device_id}`, where device_id is the unique ID
      assigned to the Device.
  """

  customer = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class CloudidentityDevicesListRequest(_messages.Message):
  r"""A CloudidentityDevicesListRequest object.

  Enums:
    ViewValueValuesEnum: Optional. The view to use for the List request.

  Fields:
    customer: Optional. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      customer.
    filter: Optional. Additional restrictions when fetching list of devices.
      For a list of search fields, refer to [Mobile device search
      fields](https://developers.google.com/admin-sdk/directory/v1/search-
      operators). Multiple search fields are separated by the space character.
    orderBy: Optional. Order specification for devices in the response. Only
      one of the following field names may be used to specify the order:
      `create_time`, `last_sync_time`, `model`, `os_version`, `device_type`
      and `serial_number`. `desc` may be specified optionally to specify
      results to be sorted in descending order. Default order is ascending.
    pageSize: Optional. The maximum number of Devices to return. If
      unspecified, at most 20 Devices will be returned. The maximum value is
      100; values above 100 will be coerced to 100.
    pageToken: Optional. A page token, received from a previous `ListDevices`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListDevices` must match the call that
      provided the page token.
    view: Optional. The view to use for the List request.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional. The view to use for the List request.

    Values:
      VIEW_UNSPECIFIED: Default value. The value is unused.
      COMPANY_INVENTORY: This view contains all devices imported by the
        company admin. Each device in the response contains all information
        specified by the company admin when importing the device (i.e. asset
        tags).
      USER_ASSIGNED_DEVICES: This view contains all devices with at least one
        user registered on the device. Each device in the response contains
        all device information, except for asset tags.
    """
    VIEW_UNSPECIFIED = 0
    COMPANY_INVENTORY = 1
    USER_ASSIGNED_DEVICES = 2

  customer = _messages.StringField(1)
  filter = _messages.StringField(2)
  orderBy = _messages.StringField(3)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)
  view = _messages.EnumField('ViewValueValuesEnum', 6)


class CloudidentityDevicesWipeRequest(_messages.Message):
  r"""A CloudidentityDevicesWipeRequest object.

  Fields:
    name: Required. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the Device
      in format: `devices/{device_id}/deviceUsers/{device_user_id}`, where
      device_id is the unique ID assigned to the Device, and device_user_id is
      the unique ID assigned to the User.
    wipeDeviceRequest: A WipeDeviceRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  wipeDeviceRequest = _messages.MessageField('WipeDeviceRequest', 2)


class CloudidentityGroupsCreateRequest(_messages.Message):
  r"""A CloudidentityGroupsCreateRequest object.

  Enums:
    InitialGroupConfigValueValuesEnum: Required. The initial configuration
      option for the `Group`.

  Fields:
    group: A Group resource to be passed as the request body.
    initialGroupConfig: Required. The initial configuration option for the
      `Group`.
  """

  class InitialGroupConfigValueValuesEnum(_messages.Enum):
    r"""Required. The initial configuration option for the `Group`.

    Values:
      INITIAL_GROUP_CONFIG_UNSPECIFIED: Default. Should not be used.
      WITH_INITIAL_OWNER: The end user making the request will be added as the
        initial owner of the `Group`.
      EMPTY: An empty group is created without any initial owners. This can
        only be used by admins of the domain.
    """
    INITIAL_GROUP_CONFIG_UNSPECIFIED = 0
    WITH_INITIAL_OWNER = 1
    EMPTY = 2

  group = _messages.MessageField('Group', 1)
  initialGroupConfig = _messages.EnumField('InitialGroupConfigValueValuesEnum', 2)


class CloudidentityGroupsDeleteRequest(_messages.Message):
  r"""A CloudidentityGroupsDeleteRequest object.

  Fields:
    name: Required. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      `Group` to retrieve. Must be of the form `groups/{group_id}`.
  """

  name = _messages.StringField(1, required=True)


class CloudidentityGroupsGetRequest(_messages.Message):
  r"""A CloudidentityGroupsGetRequest object.

  Fields:
    name: Required. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      `Group` to retrieve. Must be of the form `groups/{group_id}`.
  """

  name = _messages.StringField(1, required=True)


class CloudidentityGroupsGetSecuritySettingsRequest(_messages.Message):
  r"""A CloudidentityGroupsGetSecuritySettingsRequest object.

  Fields:
    name: Required. The security settings to retrieve. Format:
      `groups/{group_id}/securitySettings`
    readMask: Field-level read mask of which fields to return. "*" returns all
      fields. If not specified, all fields will be returned. May only contain
      the following field: `member_restriction`.
  """

  name = _messages.StringField(1, required=True)
  readMask = _messages.StringField(2)


class CloudidentityGroupsListRequest(_messages.Message):
  r"""A CloudidentityGroupsListRequest object.

  Enums:
    ViewValueValuesEnum: The level of detail to be returned. If unspecified,
      defaults to `View.BASIC`.

  Fields:
    pageSize: The maximum number of results to return. Note that the number of
      results returned may be less than this value even if there are more
      available results. To fetch all results, clients must continue calling
      this method repeatedly until the response no longer contains a
      `next_page_token`. If unspecified, defaults to 200 for `View.BASIC` and
      to 50 for `View.FULL`. Must not be greater than 1000 for `View.BASIC` or
      500 for `View.FULL`.
    pageToken: The `next_page_token` value returned from a previous list
      request, if any.
    parent: Required. The parent resource under which to list all `Group`
      resources. Must be of the form `identitysources/{identity_source_id}`
      for external- identity-mapped groups or `customers/{customer_id}` for
      Google Groups. The `customer_id` must begin with "C" (for example,
      'C046psxkn'). [Find your customer ID.]
      (https://support.google.com/cloudidentity/answer/10070793)
    view: The level of detail to be returned. If unspecified, defaults to
      `View.BASIC`.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""The level of detail to be returned. If unspecified, defaults to
    `View.BASIC`.

    Values:
      VIEW_UNSPECIFIED: Default. Should not be used.
      BASIC: Only basic resource information is returned.
      FULL: All resource information is returned.
    """
    VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3)
  view = _messages.EnumField('ViewValueValuesEnum', 4)


class CloudidentityGroupsLookupRequest(_messages.Message):
  r"""A CloudidentityGroupsLookupRequest object.

  Fields:
    groupKey_id: The ID of the entity. For Google-managed entities, the `id`
      must be the email address of an existing group or user. For external-
      identity-mapped entities, the `id` must be a string conforming to the
      Identity Source's requirements. Must be unique within a `namespace`.
    groupKey_namespace: The namespace in which the entity exists. If not
      specified, the `EntityKey` represents a Google-managed entity such as a
      Google user or a Google Group. If specified, the `EntityKey` represents
      an external-identity-mapped group. The namespace must correspond to an
      identity source created in Admin Console and must be in the form of
      `identitysources/{identity_source_id}`.
  """

  groupKey_id = _messages.StringField(1)
  groupKey_namespace = _messages.StringField(2)


class CloudidentityGroupsMembershipsCheckTransitiveMembershipRequest(_messages.Message):
  r"""A CloudidentityGroupsMembershipsCheckTransitiveMembershipRequest object.

  Fields:
    parent: [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the group
      to check the transitive membership in. Format: `groups/{group_id}`,
      where `group_id` is the unique id assigned to the Group to which the
      Membership belongs to.
    query: Required. A CEL expression that MUST include member specification.
      This is a `required` field. Certain groups are uniquely identified by
      both a 'member_key_id' and a 'member_key_namespace', which requires an
      additional query input: 'member_key_namespace'. Example query:
      `member_key_id == 'member_key_id_value'`
  """

  parent = _messages.StringField(1, required=True)
  query = _messages.StringField(2)


class CloudidentityGroupsMembershipsCreateRequest(_messages.Message):
  r"""A CloudidentityGroupsMembershipsCreateRequest object.

  Fields:
    membership: A Membership resource to be passed as the request body.
    parent: Required. The parent `Group` resource under which to create the
      `Membership`. Must be of the form `groups/{group_id}`.
  """

  membership = _messages.MessageField('Membership', 1)
  parent = _messages.StringField(2, required=True)


class CloudidentityGroupsMembershipsDeleteRequest(_messages.Message):
  r"""A CloudidentityGroupsMembershipsDeleteRequest object.

  Fields:
    name: Required. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      `Membership` to delete. Must be of the form
      `groups/{group_id}/memberships/{membership_id}`.
  """

  name = _messages.StringField(1, required=True)


class CloudidentityGroupsMembershipsGetMembershipGraphRequest(_messages.Message):
  r"""A CloudidentityGroupsMembershipsGetMembershipGraphRequest object.

  Fields:
    parent: Required. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the group
      to search transitive memberships in. Format: `groups/{group_id}`, where
      `group_id` is the unique ID assigned to the Group to which the
      Membership belongs to. group_id can be a wildcard collection id "-".
      When `group_id` is specified, the membership graph will be constrained
      to paths between the member (defined in the query) and the parent. If a
      wildcard collection is provided, all membership paths connected to the
      member will be returned.
    query: Required. A CEL expression that MUST include member specification
      AND label(s). Certain groups are uniquely identified by both a
      'member_key_id' and a 'member_key_namespace', which requires an
      additional query input: 'member_key_namespace'. Example query:
      `member_key_id == 'member_key_id_value' && in labels`
  """

  parent = _messages.StringField(1, required=True)
  query = _messages.StringField(2)


class CloudidentityGroupsMembershipsGetRequest(_messages.Message):
  r"""A CloudidentityGroupsMembershipsGetRequest object.

  Fields:
    name: Required. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      `Membership` to retrieve. Must be of the form
      `groups/{group_id}/memberships/{membership_id}`.
  """

  name = _messages.StringField(1, required=True)


class CloudidentityGroupsMembershipsListRequest(_messages.Message):
  r"""A CloudidentityGroupsMembershipsListRequest object.

  Enums:
    ViewValueValuesEnum: The level of detail to be returned. If unspecified,
      defaults to `MembershipView.BASIC`.

  Fields:
    pageSize: The maximum number of results to return. Note that the number of
      results returned may be less than this value even if there are more
      available results. To fetch all results, clients must continue calling
      this method repeatedly until the response no longer contains a
      `next_page_token`. If unspecified, defaults to 200 for `GroupView.BASIC`
      and to 50 for `GroupView.FULL`. Must not be greater than 1000 for
      `GroupView.BASIC` or 500 for `GroupView.FULL`.
    pageToken: The `next_page_token` value returned from a previous search
      request, if any.
    parent: Required. The parent `Group` resource under which to lookup the
      `Membership` name. Must be of the form `groups/{group_id}`.
    view: The level of detail to be returned. If unspecified, defaults to
      `MembershipView.BASIC`.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""The level of detail to be returned. If unspecified, defaults to
    `MembershipView.BASIC`.

    Values:
      BASIC: Default. Only basic resource information is returned.
      FULL: All resource information is returned.
    """
    BASIC = 0
    FULL = 1

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 4)


class CloudidentityGroupsMembershipsLookupRequest(_messages.Message):
  r"""A CloudidentityGroupsMembershipsLookupRequest object.

  Fields:
    memberKey_id: The ID of the entity. For Google-managed entities, the `id`
      must be the email address of an existing group or user. For external-
      identity-mapped entities, the `id` must be a string conforming to the
      Identity Source's requirements. Must be unique within a `namespace`.
    memberKey_namespace: The namespace in which the entity exists. If not
      specified, the `EntityKey` represents a Google-managed entity such as a
      Google user or a Google Group. If specified, the `EntityKey` represents
      an external-identity-mapped group. The namespace must correspond to an
      identity source created in Admin Console and must be in the form of
      `identitysources/{identity_source_id}`.
    parent: Required. The parent `Group` resource under which to lookup the
      `Membership` name. Must be of the form `groups/{group_id}`.
  """

  memberKey_id = _messages.StringField(1)
  memberKey_namespace = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CloudidentityGroupsMembershipsModifyMembershipRolesRequest(_messages.Message):
  r"""A CloudidentityGroupsMembershipsModifyMembershipRolesRequest object.

  Fields:
    modifyMembershipRolesRequest: A ModifyMembershipRolesRequest resource to
      be passed as the request body.
    name: Required. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      `Membership` whose roles are to be modified. Must be of the form
      `groups/{group_id}/memberships/{membership_id}`.
  """

  modifyMembershipRolesRequest = _messages.MessageField('ModifyMembershipRolesRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudidentityGroupsMembershipsSearchDirectGroupsRequest(_messages.Message):
  r"""A CloudidentityGroupsMembershipsSearchDirectGroupsRequest object.

  Fields:
    orderBy: The ordering of membership relation for the display name or email
      in the response. The syntax for this field can be found at
      https://cloud.google.com/apis/design/design_patterns#sorting_order.
      Example: Sort by the ascending display name: order_by="group_name" or
      order_by="group_name asc". Sort by the descending display name:
      order_by="group_name desc". Sort by the ascending group key:
      order_by="group_key" or order_by="group_key asc". Sort by the descending
      group key: order_by="group_key desc".
    pageSize: The default page size is 200 (max 1000).
    pageToken: The next_page_token value returned from a previous list
      request, if any.
    parent: [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the group
      to search transitive memberships in. Format: groups/{group_id}, where
      group_id is always '-' as this API will search across all groups for a
      given member.
    query: Required. A CEL expression that MUST include member specification
      AND label(s). Users can search on label attributes of groups. CONTAINS
      match ('in') is supported on labels. Identity-mapped groups are uniquely
      identified by both a `member_key_id` and a `member_key_namespace`, which
      requires an additional query input: `member_key_namespace`. Example
      query: `member_key_id == 'member_key_id_value' && 'label_value' in
      labels`
  """

  orderBy = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  query = _messages.StringField(5)


class CloudidentityGroupsMembershipsSearchTransitiveGroupsRequest(_messages.Message):
  r"""A CloudidentityGroupsMembershipsSearchTransitiveGroupsRequest object.

  Fields:
    pageSize: The default page size is 200 (max 1000).
    pageToken: The `next_page_token` value returned from a previous list
      request, if any.
    parent: [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the group
      to search transitive memberships in. Format: `groups/{group_id}`, where
      `group_id` is always '-' as this API will search across all groups for a
      given member.
    query: Required. A CEL expression that MUST include member specification
      AND label(s). This is a `required` field. Users can search on label
      attributes of groups. CONTAINS match ('in') is supported on labels.
      Identity-mapped groups are uniquely identified by both a `member_key_id`
      and a `member_key_namespace`, which requires an additional query input:
      `member_key_namespace`. Example query: `member_key_id ==
      'member_key_id_value' && in labels` Query may optionally contain
      equality operators on the parent of the group restricting the search
      within a particular customer, e.g. `parent ==
      'customers/{customer_id}'`. The `customer_id` must begin with "C" (for
      example, 'C046psxkn'). This filtering is only supported for Admins with
      groups read permissions on the input customer. Example query:
      `member_key_id == 'member_key_id_value' && in labels && parent ==
      'customers/C046psxkn'`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  query = _messages.StringField(4)


class CloudidentityGroupsMembershipsSearchTransitiveMembershipsRequest(_messages.Message):
  r"""A CloudidentityGroupsMembershipsSearchTransitiveMembershipsRequest
  object.

  Fields:
    pageSize: The default page size is 200 (max 1000).
    pageToken: The next_page_token value returned from a previous list
      request, if any.
    parent: [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the group
      to search transitive memberships in. Format: `groups/{group_id}`, where
      `group_id` is the unique ID assigned to the Group.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CloudidentityGroupsPatchRequest(_messages.Message):
  r"""A CloudidentityGroupsPatchRequest object.

  Fields:
    group: A Group resource to be passed as the request body.
    name: Output only. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      `Group`. Shall be of the form `groups/{group_id}`.
    updateMask: Required. The names of fields to update. May only contain the
      following field names: `display_name`, `description`, `labels`,
      `dynamic_group_metadata`, `posix_groups`.
  """

  group = _messages.MessageField('Group', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class CloudidentityGroupsSearchRequest(_messages.Message):
  r"""A CloudidentityGroupsSearchRequest object.

  Enums:
    ViewValueValuesEnum: The level of detail to be returned. If unspecified,
      defaults to `View.BASIC`.

  Fields:
    orderBy: The ordering of groups for the display name or email in the
      search groups response. The syntax for this field can be found at
      https://cloud.google.com/apis/design/design_patterns#sorting_order.
      Example: Sort by the ascending name: order_by="display_name" Sort by the
      descending group key email: order_by="group_key desc"
    pageSize: The maximum number of results to return. Note that the number of
      results returned may be less than this value even if there are more
      available results. To fetch all results, clients must continue calling
      this method repeatedly until the response no longer contains a
      `next_page_token`. If unspecified, defaults to 200 for `GroupView.BASIC`
      and to 50 for `GroupView.FULL`. Must not be greater than 1000 for
      `GroupView.BASIC` or 500 for `GroupView.FULL`.
    pageToken: The `next_page_token` value returned from a previous search
      request, if any.
    query: Required. The search query. * Must be specified in [Common
      Expression Language](https://opensource.google/projects/cel). * Must
      contain equality operators on the parent, e.g. `parent ==
      'customers/{customer_id}'`. The `customer_id` must begin with "C" (for
      example, 'C046psxkn'). [Find your customer ID.]
      (https://support.google.com/cloudidentity/answer/10070793) * Can contain
      optional inclusion operators on `labels` such as
      `'cloudidentity.googleapis.com/groups.discussion_forum' in labels`). *
      Can contain an optional equality operator on `domain_name`. e.g.
      `domain_name == 'examplepetstore.com'` * Can contain optional
      `startsWith/contains/equality` operators on `group_key`, e.g.
      `group_key.startsWith('dev')`, `group_key.contains('dev'), group_key ==
      '<EMAIL>'` * Can contain optional
      `startsWith/contains/equality` operators on `display_name`, such as
      `display_name.startsWith('dev')` , `display_name.contains('dev')`,
      `display_name == 'dev'`
    view: The level of detail to be returned. If unspecified, defaults to
      `View.BASIC`.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""The level of detail to be returned. If unspecified, defaults to
    `View.BASIC`.

    Values:
      BASIC: Default. Only basic resource information is returned.
      FULL: All resource information is returned.
    """
    BASIC = 0
    FULL = 1

  orderBy = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  query = _messages.StringField(4)
  view = _messages.EnumField('ViewValueValuesEnum', 5)


class CloudidentityGroupsUpdateSecuritySettingsRequest(_messages.Message):
  r"""A CloudidentityGroupsUpdateSecuritySettingsRequest object.

  Fields:
    name: Output only. The resource name of the security settings. Shall be of
      the form `groups/{group_id}/securitySettings`.
    securitySettings: A SecuritySettings resource to be passed as the request
      body.
    updateMask: Required. The fully-qualified names of fields to update. May
      only contain the following field: `member_restriction.query`.
  """

  name = _messages.StringField(1, required=True)
  securitySettings = _messages.MessageField('SecuritySettings', 2)
  updateMask = _messages.StringField(3)


class CloudidentityInboundSamlSsoProfilesDeleteRequest(_messages.Message):
  r"""A CloudidentityInboundSamlSsoProfilesDeleteRequest object.

  Fields:
    name: Required. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      InboundSamlSsoProfile to delete. Format:
      `inboundSamlSsoProfiles/{sso_profile_id}`
  """

  name = _messages.StringField(1, required=True)


class CloudidentityInboundSamlSsoProfilesGetRequest(_messages.Message):
  r"""A CloudidentityInboundSamlSsoProfilesGetRequest object.

  Fields:
    name: Required. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      InboundSamlSsoProfile to get. Format:
      `inboundSamlSsoProfiles/{sso_profile_id}`
  """

  name = _messages.StringField(1, required=True)


class CloudidentityInboundSamlSsoProfilesIdpCredentialsAddRequest(_messages.Message):
  r"""A CloudidentityInboundSamlSsoProfilesIdpCredentialsAddRequest object.

  Fields:
    addIdpCredentialRequest: A AddIdpCredentialRequest resource to be passed
      as the request body.
    parent: Required. The InboundSamlSsoProfile that owns the IdpCredential.
      Format: `inboundSamlSsoProfiles/{sso_profile_id}`
  """

  addIdpCredentialRequest = _messages.MessageField('AddIdpCredentialRequest', 1)
  parent = _messages.StringField(2, required=True)


class CloudidentityInboundSamlSsoProfilesIdpCredentialsDeleteRequest(_messages.Message):
  r"""A CloudidentityInboundSamlSsoProfilesIdpCredentialsDeleteRequest object.

  Fields:
    name: Required. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      IdpCredential to delete. Format: `inboundSamlSsoProfiles/{sso_profile_id
      }/idpCredentials/{idp_credential_id}`
  """

  name = _messages.StringField(1, required=True)


class CloudidentityInboundSamlSsoProfilesIdpCredentialsGetRequest(_messages.Message):
  r"""A CloudidentityInboundSamlSsoProfilesIdpCredentialsGetRequest object.

  Fields:
    name: Required. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      IdpCredential to retrieve. Format: `inboundSamlSsoProfiles/{sso_profile_
      id}/idpCredentials/{idp_credential_id}`
  """

  name = _messages.StringField(1, required=True)


class CloudidentityInboundSamlSsoProfilesIdpCredentialsListRequest(_messages.Message):
  r"""A CloudidentityInboundSamlSsoProfilesIdpCredentialsListRequest object.

  Fields:
    pageSize: The maximum number of `IdpCredential`s to return. The service
      may return fewer than this value.
    pageToken: A page token, received from a previous `ListIdpCredentials`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListIdpCredentials` must match the call
      that provided the page token.
    parent: Required. The parent, which owns this collection of
      `IdpCredential`s. Format: `inboundSamlSsoProfiles/{sso_profile_id}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CloudidentityInboundSamlSsoProfilesListRequest(_messages.Message):
  r"""A CloudidentityInboundSamlSsoProfilesListRequest object.

  Fields:
    filter: A [Common Expression Language](https://github.com/google/cel-spec)
      expression to filter the results. The only supported filter is filtering
      by customer. For example: `customer=="customers/C0123abc"`. Omitting the
      filter or specifying a filter of `customer=="customers/my_customer"`
      will return the profiles for the customer that the caller (authenticated
      user) belongs to.
    pageSize: The maximum number of InboundSamlSsoProfiles to return. The
      service may return fewer than this value. If omitted (or defaulted to
      zero) the server will use a sensible default. This default may change
      over time. The maximum allowed value is 100. Requests with page_size
      greater than that will be silently interpreted as having this maximum
      value.
    pageToken: A page token, received from a previous
      `ListInboundSamlSsoProfiles` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `ListInboundSamlSsoProfiles` must match the call that provided the page
      token.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class CloudidentityInboundSamlSsoProfilesPatchRequest(_messages.Message):
  r"""A CloudidentityInboundSamlSsoProfilesPatchRequest object.

  Fields:
    inboundSamlSsoProfile: A InboundSamlSsoProfile resource to be passed as
      the request body.
    name: Output only. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the SAML
      SSO profile.
    updateMask: Required. The list of fields to be updated.
  """

  inboundSamlSsoProfile = _messages.MessageField('InboundSamlSsoProfile', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class CloudidentityInboundSsoAssignmentsDeleteRequest(_messages.Message):
  r"""A CloudidentityInboundSsoAssignmentsDeleteRequest object.

  Fields:
    name: Required. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      InboundSsoAssignment to delete. Format:
      `inboundSsoAssignments/{assignment}`
  """

  name = _messages.StringField(1, required=True)


class CloudidentityInboundSsoAssignmentsGetRequest(_messages.Message):
  r"""A CloudidentityInboundSsoAssignmentsGetRequest object.

  Fields:
    name: Required. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      InboundSsoAssignment to fetch. Format:
      `inboundSsoAssignments/{assignment}`
  """

  name = _messages.StringField(1, required=True)


class CloudidentityInboundSsoAssignmentsListRequest(_messages.Message):
  r"""A CloudidentityInboundSsoAssignmentsListRequest object.

  Fields:
    filter: A CEL expression to filter the results. The only supported filter
      is filtering by customer. For example: `customer==customers/C0123abc`.
      Omitting the filter or specifying a filter of
      `customer==customers/my_customer` will return the assignments for the
      customer that the caller (authenticated user) belongs to.
    pageSize: The maximum number of assignments to return. The service may
      return fewer than this value. If omitted (or defaulted to zero) the
      server will use a sensible default. This default may change over time.
      The maximum allowed value is 100, though requests with page_size greater
      than that will be silently interpreted as having this maximum value.
      This may increase in the futue.
    pageToken: A page token, received from a previous
      `ListInboundSsoAssignments` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `ListInboundSsoAssignments` must match the call that provided the page
      token.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class CloudidentityInboundSsoAssignmentsPatchRequest(_messages.Message):
  r"""A CloudidentityInboundSsoAssignmentsPatchRequest object.

  Fields:
    inboundSsoAssignment: A InboundSsoAssignment resource to be passed as the
      request body.
    name: Output only. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      Inbound SSO Assignment.
    updateMask: Required. The list of fields to be updated.
  """

  inboundSsoAssignment = _messages.MessageField('InboundSsoAssignment', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class CloudidentityOrgUnitsMembershipsListRequest(_messages.Message):
  r"""A CloudidentityOrgUnitsMembershipsListRequest object.

  Fields:
    customer: Required. Immutable. Customer that this OrgMembership belongs
      to. All authorization will happen on the role assignments of this
      customer. Format: customers/{$customerId} where `$customerId` is the
      `id` from the [Admin SDK `Customer`
      resource](https://developers.google.com/admin-
      sdk/directory/reference/rest/v1/customers). You may also use
      `customers/my_customer` to specify your own organization.
    filter: The search query. Must be specified in [Common Expression
      Language](https://opensource.google/projects/cel). May only contain
      equality operators on the `type` (e.g., `type == 'shared_drive'`).
    pageSize: The maximum number of results to return. The service may return
      fewer than this value. If omitted (or defaulted to zero) the server will
      default to 50. The maximum allowed value is 100, though requests with
      page_size greater than that will be silently interpreted as 100.
    pageToken: A page token, received from a previous
      `OrgMembershipsService.ListOrgMemberships` call. Provide this to
      retrieve the subsequent page. When paginating, all other parameters
      provided to `ListOrgMembershipsRequest` must match the call that
      provided the page token.
    parent: Required. Immutable. OrgUnit which is queried for a list of
      memberships. Format: orgUnits/{$orgUnitId} where `$orgUnitId` is the
      `orgUnitId` from the [Admin SDK `OrgUnit`
      resource](https://developers.google.com/admin-
      sdk/directory/reference/rest/v1/orgunits).
  """

  customer = _messages.StringField(1)
  filter = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class CloudidentityOrgUnitsMembershipsMoveRequest(_messages.Message):
  r"""A CloudidentityOrgUnitsMembershipsMoveRequest object.

  Fields:
    moveOrgMembershipRequest: A MoveOrgMembershipRequest resource to be passed
      as the request body.
    name: Required. Immutable. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      OrgMembership. Format: orgUnits/{$orgUnitId}/memberships/{$membership}
      The `$orgUnitId` is the `orgUnitId` from the [Admin SDK `OrgUnit`
      resource](https://developers.google.com/admin-
      sdk/directory/reference/rest/v1/orgunits). To manage a Membership
      without specifying source `orgUnitId`, this API also supports the
      wildcard character '-' for `$orgUnitId` per https://google.aip.dev/159.
      The `$membership` shall be of the form `{$entityType};{$memberId}`,
      where `$entityType` is the enum value of OrgMembership.EntityType, and
      `memberId` is the `id` from [Drive API (V3) `Drive` resource](https://de
      velopers.google.com/drive/api/v3/reference/drives#resource) for
      OrgMembership.EntityType.SHARED_DRIVE.
  """

  moveOrgMembershipRequest = _messages.MessageField('MoveOrgMembershipRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudidentityPoliciesGetRequest(_messages.Message):
  r"""A CloudidentityPoliciesGetRequest object.

  Fields:
    name: Required. The name of the policy to retrieve. Format:
      "policies/{policy}".
  """

  name = _messages.StringField(1, required=True)


class CloudidentityPoliciesListRequest(_messages.Message):
  r"""A CloudidentityPoliciesListRequest object.

  Fields:
    filter: Optional. A CEL expression for filtering the results. Policies can
      be filtered by application with this expression:
      setting.type.matches('^settings/gmail\\..*$') Policies can be filtered
      by setting type with this expression:
      setting.type.matches('^.*\\.service_status$') A maximum of one of the
      above setting.type clauses can be used. Policies can be filtered by
      customer with this expression: customer == "customers/{customer}" Where
      `customer` is the `id` from the [Admin SDK `Customer`
      resource](https://developers.google.com/admin-
      sdk/directory/reference/rest/v1/customers). You may use
      `customers/my_customer` to specify your own organization. When no
      customer is mentioned it will be default to customers/my_customer. A
      maximum of one customer clause can be used. The above clauses can only
      be combined together in a single filter expression with the `&&`
      operator.
    pageSize: Optional. The maximum number of results to return. The service
      can return fewer than this number. If omitted or set to 0, the default
      is 50 results per page. The maximum allowed value is 100. `page_size`
      values greater than 100 default to 100.
    pageToken: Optional. The pagination token received from a prior call to
      PoliciesService.ListPolicies to retrieve the next page of results. When
      paginating, all other parameters provided to `ListPoliciesRequest` must
      match the call that provided the page token.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class CreateDeviceRequest(_messages.Message):
  r"""Request message for creating a Company Owned device.

  Fields:
    customer: Optional. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      customer. If you're using this API for your own organization, use
      `customers/my_customer` If you're using this API to manage another
      organization, use `customers/{customer_id}`, where customer_id is the
      customer to whom the device belongs.
    device: Required. The device to be created. The name field within this
      device is ignored in the create method. A new name is created by the
      method, and returned within the response. Only the fields `device_type`,
      `serial_number` and `asset_tag` (if present) are used to create the
      device. All other fields are ignored. The `device_type` and
      `serial_number` fields are required.
  """

  customer = _messages.StringField(1)
  device = _messages.MessageField('Device', 2)


class CreateInboundSamlSsoProfileOperationMetadata(_messages.Message):
  r"""LRO response metadata for
  InboundSamlSsoProfilesService.CreateInboundSamlSsoProfile.

  Fields:
    state: State of this Operation Will be "awaiting-multi-party-approval"
      when the operation is deferred due to the target customer having enabled
      [Multi-party approval for sensitive
      actions](https://support.google.com/a/answer/13790448).
  """

  state = _messages.StringField(1)


class CreateInboundSsoAssignmentOperationMetadata(_messages.Message):
  r"""LRO response metadata for
  InboundSsoAssignmentsService.CreateInboundSsoAssignment.
  """



class CustomAttributeValue(_messages.Message):
  r"""Additional custom attribute values may be one of these types

  Fields:
    boolValue: Represents a boolean value.
    numberValue: Represents a double value.
    stringValue: Represents a string value.
  """

  boolValue = _messages.BooleanField(1)
  numberValue = _messages.FloatField(2)
  stringValue = _messages.StringField(3)


class DeleteIdpCredentialOperationMetadata(_messages.Message):
  r"""LRO response metadata for
  InboundSamlSsoProfilesService.DeleteIdpCredential.
  """



class DeleteInboundSamlSsoProfileOperationMetadata(_messages.Message):
  r"""LRO response metadata for
  InboundSamlSsoProfilesService.DeleteInboundSamlSsoProfile.
  """



class DeleteInboundSsoAssignmentOperationMetadata(_messages.Message):
  r"""LRO response metadata for
  InboundSsoAssignmentsService.DeleteInboundSsoAssignment.
  """



class Device(_messages.Message):
  r"""A Device within the Cloud Identity Devices API. Represents a Device
  known to Google Cloud, independent of the device ownership, type, and
  whether it is assigned or in use by a user. Important: Device API scopes
  require that you use domain-wide delegation to access the API. For more
  information, see [Set up the Devices
  API](https://cloud.google.com/identity/docs/how-to/setup-devices).

  Enums:
    ClientTypesValueListEntryValuesEnum:
    CompromisedStateValueValuesEnum: Output only. Represents whether the
      Device is compromised.
    DeviceTypeValueValuesEnum: Output only. Type of device.
    EncryptionStateValueValuesEnum: Output only. Device encryption state.
    ManagementStateValueValuesEnum: Output only. Management state of the
      device
    OwnerTypeValueValuesEnum: Output only. Whether the device is owned by the
      company or an individual

  Fields:
    androidSpecificAttributes: Output only. Attributes specific to Android
      devices.
    assetTag: Asset tag of the device.
    basebandVersion: Output only. Baseband version of the device.
    bootloaderVersion: Output only. Device bootloader version. Example: 0.6.7.
    brand: Output only. Device brand. Example: Samsung.
    buildNumber: Output only. Build number of the device.
    clientTypes: List of the clients the device is reporting to.
    compromisedState: Output only. Represents whether the Device is
      compromised.
    createTime: Output only. When the Company-Owned device was imported. This
      field is empty for BYOD devices.
    deviceId: Unique identifier for the device.
    deviceType: Output only. Type of device.
    enabledDeveloperOptions: Output only. Whether developer options is enabled
      on device.
    enabledUsbDebugging: Output only. Whether USB debugging is enabled on
      device.
    encryptionState: Output only. Device encryption state.
    endpointVerificationSpecificAttributes: Output only. Attributes specific
      to [Endpoint Verification](https://cloud.google.com/endpoint-
      verification/docs/overview) devices.
    hostname: Host name of the device.
    imei: Output only. IMEI number of device if GSM device; empty otherwise.
    kernelVersion: Output only. Kernel version of the device.
    lastSyncTime: Most recent time when device synced with this service.
    managementState: Output only. Management state of the device
    manufacturer: Output only. Device manufacturer. Example: Motorola.
    meid: Output only. MEID number of device if CDMA device; empty otherwise.
    model: Output only. Model name of device. Example: Pixel 3.
    name: Output only. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the Device
      in format: `devices/{device_id}`, where device_id is the unique id
      assigned to the Device.
    networkOperator: Output only. Mobile or network operator of device, if
      available.
    osVersion: Output only. OS version of the device. Example: Android 8.1.0.
    otherAccounts: Output only. Domain name for Google accounts on device.
      Type for other accounts on device. On Android, will only be populated if
      |ownership_privilege| is |PROFILE_OWNER| or |DEVICE_OWNER|. Does not
      include the account signed in to the device policy app if that account's
      domain has only one account. Examples: "com.example", "xyz.com".
    ownerType: Output only. Whether the device is owned by the company or an
      individual
    releaseVersion: Output only. OS release version. Example: 6.0.
    securityPatchTime: Output only. OS security patch update time on device.
    serialNumber: Serial Number of device. Example: HT82V1A01076.
    unifiedDeviceId: Output only. Unified device id of the device.
    wifiMacAddresses: WiFi MAC addresses of device.
  """

  class ClientTypesValueListEntryValuesEnum(_messages.Enum):
    r"""ClientTypesValueListEntryValuesEnum enum type.

    Values:
      CLIENT_TYPE_UNSPECIFIED: Default value
      DRIVE_FS: Managed by DriveFS
      FUNDAMENTAL: Management type for every secure device
      ENDPOINT_VERIFICATION: Managed by Endpoint Verification
      WINDOWS_ADVANCED: Managed by Windows
      GOOGLE_CREDENTIALS_PROVIDER_FOR_WINDOWS: Managed by Google credential
        provider for windows
    """
    CLIENT_TYPE_UNSPECIFIED = 0
    DRIVE_FS = 1
    FUNDAMENTAL = 2
    ENDPOINT_VERIFICATION = 3
    WINDOWS_ADVANCED = 4
    GOOGLE_CREDENTIALS_PROVIDER_FOR_WINDOWS = 5

  class CompromisedStateValueValuesEnum(_messages.Enum):
    r"""Output only. Represents whether the Device is compromised.

    Values:
      COMPROMISED_STATE_UNSPECIFIED: Default value.
      COMPROMISED: The device is compromised (currently, this means Android
        device is rooted).
      UNCOMPROMISED: The device is safe (currently, this means Android device
        is unrooted).
    """
    COMPROMISED_STATE_UNSPECIFIED = 0
    COMPROMISED = 1
    UNCOMPROMISED = 2

  class DeviceTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of device.

    Values:
      DEVICE_TYPE_UNSPECIFIED: Unknown device type
      ANDROID: Device is an Android device
      IOS: Device is an iOS device
      GOOGLE_SYNC: Device is a Google Sync device.
      WINDOWS: Device is a Windows device.
      MAC_OS: Device is a MacOS device.
      LINUX: Device is a Linux device.
      CHROME_OS: Device is a ChromeOS device.
    """
    DEVICE_TYPE_UNSPECIFIED = 0
    ANDROID = 1
    IOS = 2
    GOOGLE_SYNC = 3
    WINDOWS = 4
    MAC_OS = 5
    LINUX = 6
    CHROME_OS = 7

  class EncryptionStateValueValuesEnum(_messages.Enum):
    r"""Output only. Device encryption state.

    Values:
      ENCRYPTION_STATE_UNSPECIFIED: Encryption Status is not set.
      UNSUPPORTED_BY_DEVICE: Device doesn't support encryption.
      ENCRYPTED: Device is encrypted.
      NOT_ENCRYPTED: Device is not encrypted.
    """
    ENCRYPTION_STATE_UNSPECIFIED = 0
    UNSUPPORTED_BY_DEVICE = 1
    ENCRYPTED = 2
    NOT_ENCRYPTED = 3

  class ManagementStateValueValuesEnum(_messages.Enum):
    r"""Output only. Management state of the device

    Values:
      MANAGEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      APPROVED: Device is approved.
      BLOCKED: Device is blocked.
      PENDING: Device is pending approval.
      UNPROVISIONED: The device is not provisioned. Device will start from
        this state until some action is taken (i.e. a user starts using the
        device).
      WIPING: Data and settings on the device are being removed.
      WIPED: All data and settings on the device are removed.
    """
    MANAGEMENT_STATE_UNSPECIFIED = 0
    APPROVED = 1
    BLOCKED = 2
    PENDING = 3
    UNPROVISIONED = 4
    WIPING = 5
    WIPED = 6

  class OwnerTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Whether the device is owned by the company or an
    individual

    Values:
      DEVICE_OWNERSHIP_UNSPECIFIED: Default value. The value is unused.
      COMPANY: Company owns the device.
      BYOD: Bring Your Own Device (i.e. individual owns the device)
    """
    DEVICE_OWNERSHIP_UNSPECIFIED = 0
    COMPANY = 1
    BYOD = 2

  androidSpecificAttributes = _messages.MessageField('AndroidAttributes', 1)
  assetTag = _messages.StringField(2)
  basebandVersion = _messages.StringField(3)
  bootloaderVersion = _messages.StringField(4)
  brand = _messages.StringField(5)
  buildNumber = _messages.StringField(6)
  clientTypes = _messages.EnumField('ClientTypesValueListEntryValuesEnum', 7, repeated=True)
  compromisedState = _messages.EnumField('CompromisedStateValueValuesEnum', 8)
  createTime = _messages.StringField(9)
  deviceId = _messages.StringField(10)
  deviceType = _messages.EnumField('DeviceTypeValueValuesEnum', 11)
  enabledDeveloperOptions = _messages.BooleanField(12)
  enabledUsbDebugging = _messages.BooleanField(13)
  encryptionState = _messages.EnumField('EncryptionStateValueValuesEnum', 14)
  endpointVerificationSpecificAttributes = _messages.MessageField('EndpointVerificationSpecificAttributes', 15)
  hostname = _messages.StringField(16)
  imei = _messages.StringField(17)
  kernelVersion = _messages.StringField(18)
  lastSyncTime = _messages.StringField(19)
  managementState = _messages.EnumField('ManagementStateValueValuesEnum', 20)
  manufacturer = _messages.StringField(21)
  meid = _messages.StringField(22)
  model = _messages.StringField(23)
  name = _messages.StringField(24)
  networkOperator = _messages.StringField(25)
  osVersion = _messages.StringField(26)
  otherAccounts = _messages.StringField(27, repeated=True)
  ownerType = _messages.EnumField('OwnerTypeValueValuesEnum', 28)
  releaseVersion = _messages.StringField(29)
  securityPatchTime = _messages.StringField(30)
  serialNumber = _messages.StringField(31)
  unifiedDeviceId = _messages.StringField(32)
  wifiMacAddresses = _messages.StringField(33, repeated=True)


class DeviceUser(_messages.Message):
  r"""Represents a user's use of a Device in the Cloud Identity Devices API. A
  DeviceUser is a resource representing a user's use of a Device

  Enums:
    CompromisedStateValueValuesEnum: Compromised State of the DeviceUser
      object
    ManagementStateValueValuesEnum: Output only. Management state of the user
      on the device.
    PasswordStateValueValuesEnum: Password state of the DeviceUser object

  Fields:
    compromisedState: Compromised State of the DeviceUser object
    createTime: When the user first signed in to the device
    firstSyncTime: Output only. Most recent time when user registered with
      this service.
    languageCode: Output only. Default locale used on device, in IETF BCP-47
      format.
    lastSyncTime: Output only. Last time when user synced with policies.
    managementState: Output only. Management state of the user on the device.
    name: Output only. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      DeviceUser in format:
      `devices/{device_id}/deviceUsers/{device_user_id}`, where
      `device_user_id` uniquely identifies a user's use of a device.
    passwordState: Password state of the DeviceUser object
    userAgent: Output only. User agent on the device for this specific user
    userEmail: Email address of the user registered on the device.
  """

  class CompromisedStateValueValuesEnum(_messages.Enum):
    r"""Compromised State of the DeviceUser object

    Values:
      COMPROMISED_STATE_UNSPECIFIED: Compromised state of Device User account
        is unknown or unspecified.
      COMPROMISED: Device User Account is compromised.
      NOT_COMPROMISED: Device User Account is not compromised.
    """
    COMPROMISED_STATE_UNSPECIFIED = 0
    COMPROMISED = 1
    NOT_COMPROMISED = 2

  class ManagementStateValueValuesEnum(_messages.Enum):
    r"""Output only. Management state of the user on the device.

    Values:
      MANAGEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      WIPING: This user's data and profile is being removed from the device.
      WIPED: This user's data and profile is removed from the device.
      APPROVED: User is approved to access data on the device.
      BLOCKED: User is blocked from accessing data on the device.
      PENDING_APPROVAL: User is awaiting approval.
      UNENROLLED: User is unenrolled from Advanced Windows Management, but the
        Windows account is still intact.
    """
    MANAGEMENT_STATE_UNSPECIFIED = 0
    WIPING = 1
    WIPED = 2
    APPROVED = 3
    BLOCKED = 4
    PENDING_APPROVAL = 5
    UNENROLLED = 6

  class PasswordStateValueValuesEnum(_messages.Enum):
    r"""Password state of the DeviceUser object

    Values:
      PASSWORD_STATE_UNSPECIFIED: Password state not set.
      PASSWORD_SET: Password set in object.
      PASSWORD_NOT_SET: Password not set in object.
    """
    PASSWORD_STATE_UNSPECIFIED = 0
    PASSWORD_SET = 1
    PASSWORD_NOT_SET = 2

  compromisedState = _messages.EnumField('CompromisedStateValueValuesEnum', 1)
  createTime = _messages.StringField(2)
  firstSyncTime = _messages.StringField(3)
  languageCode = _messages.StringField(4)
  lastSyncTime = _messages.StringField(5)
  managementState = _messages.EnumField('ManagementStateValueValuesEnum', 6)
  name = _messages.StringField(7)
  passwordState = _messages.EnumField('PasswordStateValueValuesEnum', 8)
  userAgent = _messages.StringField(9)
  userEmail = _messages.StringField(10)


class DsaPublicKeyInfo(_messages.Message):
  r"""Information of a DSA public key.

  Fields:
    keySize: Key size in bits (size of parameter P).
  """

  keySize = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class DynamicGroupMetadata(_messages.Message):
  r"""Dynamic group metadata like queries and status.

  Fields:
    queries: Memberships will be the union of all queries. Only one entry with
      USER resource is currently supported. Customers can create up to 500
      dynamic groups.
    status: Output only. Status of the dynamic group.
  """

  queries = _messages.MessageField('DynamicGroupQuery', 1, repeated=True)
  status = _messages.MessageField('DynamicGroupStatus', 2)


class DynamicGroupQuery(_messages.Message):
  r"""Defines a query on a resource.

  Enums:
    ResourceTypeValueValuesEnum:

  Fields:
    query: Query that determines the memberships of the dynamic group.
      Examples: All users with at least one `organizations.department` of
      engineering. `user.organizations.exists(org,
      org.department=='engineering')` All users with at least one location
      that has `area` of `foo` and `building_id` of `bar`.
      `user.locations.exists(loc, loc.area=='foo' && loc.building_id=='bar')`
      All users with any variation of the name John Doe (case-insensitive
      queries add `equalsIgnoreCase()` to the value being queried).
      `user.name.value.equalsIgnoreCase('jOhn DoE')`
    resourceType: A ResourceTypeValueValuesEnum attribute.
  """

  class ResourceTypeValueValuesEnum(_messages.Enum):
    r"""ResourceTypeValueValuesEnum enum type.

    Values:
      RESOURCE_TYPE_UNSPECIFIED: Default value (not valid)
      USER: For queries on User
    """
    RESOURCE_TYPE_UNSPECIFIED = 0
    USER = 1

  query = _messages.StringField(1)
  resourceType = _messages.EnumField('ResourceTypeValueValuesEnum', 2)


class DynamicGroupStatus(_messages.Message):
  r"""The current status of a dynamic group along with timestamp.

  Enums:
    StatusValueValuesEnum: Status of the dynamic group.

  Fields:
    status: Status of the dynamic group.
    statusTime: The latest time at which the dynamic group is guaranteed to be
      in the given status. If status is `UP_TO_DATE`, the latest time at which
      the dynamic group was confirmed to be up-to-date. If status is
      `UPDATING_MEMBERSHIPS`, the time at which dynamic group was created.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""Status of the dynamic group.

    Values:
      STATUS_UNSPECIFIED: Default.
      UP_TO_DATE: The dynamic group is up-to-date.
      UPDATING_MEMBERSHIPS: The dynamic group has just been created and
        memberships are being updated.
      INVALID_QUERY: Group is in an unrecoverable state and its memberships
        can't be updated.
    """
    STATUS_UNSPECIFIED = 0
    UP_TO_DATE = 1
    UPDATING_MEMBERSHIPS = 2
    INVALID_QUERY = 3

  status = _messages.EnumField('StatusValueValuesEnum', 1)
  statusTime = _messages.StringField(2)


class EndpointVerificationSpecificAttributes(_messages.Message):
  r"""Resource representing the [Endpoint Verification-specific
  attributes](https://cloud.google.com/endpoint-verification/docs/device-
  information) of a device.

  Messages:
    AdditionalSignalsValue: [Additional
      signals](https://cloud.google.com/endpoint-verification/docs/device-
      information) reported by Endpoint Verification. It includes the
      following attributes: * Non-configurable attributes: hotfixes,
      av_installed, av_enabled, windows_domain_name,
      is_os_native_firewall_enabled, and is_secure_boot_enabled. *
      [Configurable attributes](https://cloud.google.com/endpoint-
      verification/docs/collect-config-attributes): file, folder, and binary
      attributes; registry entries; and properties in a plist.

  Fields:
    additionalSignals: [Additional signals](https://cloud.google.com/endpoint-
      verification/docs/device-information) reported by Endpoint Verification.
      It includes the following attributes: * Non-configurable attributes:
      hotfixes, av_installed, av_enabled, windows_domain_name,
      is_os_native_firewall_enabled, and is_secure_boot_enabled. *
      [Configurable attributes](https://cloud.google.com/endpoint-
      verification/docs/collect-config-attributes): file, folder, and binary
      attributes; registry entries; and properties in a plist.
    browserAttributes: Details of browser profiles reported by Endpoint
      Verification.
    certificateAttributes: Details of certificates.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AdditionalSignalsValue(_messages.Message):
    r"""[Additional signals](https://cloud.google.com/endpoint-
    verification/docs/device-information) reported by Endpoint Verification.
    It includes the following attributes: * Non-configurable attributes:
    hotfixes, av_installed, av_enabled, windows_domain_name,
    is_os_native_firewall_enabled, and is_secure_boot_enabled. * [Configurable
    attributes](https://cloud.google.com/endpoint-verification/docs/collect-
    config-attributes): file, folder, and binary attributes; registry entries;
    and properties in a plist.

    Messages:
      AdditionalProperty: An additional property for a AdditionalSignalsValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AdditionalSignalsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  additionalSignals = _messages.MessageField('AdditionalSignalsValue', 1)
  browserAttributes = _messages.MessageField('BrowserAttributes', 2, repeated=True)
  certificateAttributes = _messages.MessageField('CertificateAttributes', 3, repeated=True)


class EntityKey(_messages.Message):
  r"""A unique identifier for an entity in the Cloud Identity Groups API. An
  entity can represent either a group with an optional `namespace` or a user
  without a `namespace`. The combination of `id` and `namespace` must be
  unique; however, the same `id` can be used with different `namespace`s.

  Fields:
    id: The ID of the entity. For Google-managed entities, the `id` must be
      the email address of an existing group or user. For external-identity-
      mapped entities, the `id` must be a string conforming to the Identity
      Source's requirements. Must be unique within a `namespace`.
    namespace: The namespace in which the entity exists. If not specified, the
      `EntityKey` represents a Google-managed entity such as a Google user or
      a Google Group. If specified, the `EntityKey` represents an external-
      identity-mapped group. The namespace must correspond to an identity
      source created in Admin Console and must be in the form of
      `identitysources/{identity_source_id}`.
  """

  id = _messages.StringField(1)
  namespace = _messages.StringField(2)


class ExpiryDetail(_messages.Message):
  r"""The `MembershipRole` expiry details.

  Fields:
    expireTime: The time at which the `MembershipRole` will expire.
  """

  expireTime = _messages.StringField(1)


class GetMembershipGraphResponse(_messages.Message):
  r"""The response message for MembershipsService.GetMembershipGraph.

  Fields:
    adjacencyList: The membership graph's path information represented as an
      adjacency list.
    groups: The resources representing each group in the adjacency list. Each
      group in this list can be correlated to a 'group' of the
      MembershipAdjacencyList using the 'name' of the Group resource.
  """

  adjacencyList = _messages.MessageField('MembershipAdjacencyList', 1, repeated=True)
  groups = _messages.MessageField('Group', 2, repeated=True)


class GoogleAppsCloudidentityDevicesV1AndroidAttributes(_messages.Message):
  r"""Resource representing the Android specific attributes of a Device.

  Enums:
    OwnershipPrivilegeValueValuesEnum: Ownership privileges on device.

  Fields:
    ctsProfileMatch: Whether the device passes Android CTS compliance.
    enabledUnknownSources: Whether applications from unknown sources can be
      installed on device.
    hasPotentiallyHarmfulApps: Whether any potentially harmful apps were
      detected on the device.
    ownerProfileAccount: Whether this account is on an owner/primary profile.
      For phones, only true for owner profiles. Android 4+ devices can have
      secondary or restricted user profiles.
    ownershipPrivilege: Ownership privileges on device.
    supportsWorkProfile: Whether device supports Android work profiles. If
      false, this service will not block access to corp data even if an
      administrator turns on the "Enforce Work Profile" policy.
    verifiedBoot: Whether Android verified boot status is GREEN.
    verifyAppsEnabled: Whether Google Play Protect Verify Apps is enabled.
  """

  class OwnershipPrivilegeValueValuesEnum(_messages.Enum):
    r"""Ownership privileges on device.

    Values:
      OWNERSHIP_PRIVILEGE_UNSPECIFIED: Ownership privilege is not set.
      DEVICE_ADMINISTRATOR: Active device administrator privileges on the
        device.
      PROFILE_OWNER: Profile Owner privileges. The account is in a managed
        corporate profile.
      DEVICE_OWNER: Device Owner privileges on the device.
    """
    OWNERSHIP_PRIVILEGE_UNSPECIFIED = 0
    DEVICE_ADMINISTRATOR = 1
    PROFILE_OWNER = 2
    DEVICE_OWNER = 3

  ctsProfileMatch = _messages.BooleanField(1)
  enabledUnknownSources = _messages.BooleanField(2)
  hasPotentiallyHarmfulApps = _messages.BooleanField(3)
  ownerProfileAccount = _messages.BooleanField(4)
  ownershipPrivilege = _messages.EnumField('OwnershipPrivilegeValueValuesEnum', 5)
  supportsWorkProfile = _messages.BooleanField(6)
  verifiedBoot = _messages.BooleanField(7)
  verifyAppsEnabled = _messages.BooleanField(8)


class GoogleAppsCloudidentityDevicesV1ApproveDeviceUserMetadata(_messages.Message):
  r"""Metadata for ApproveDeviceUser LRO."""


class GoogleAppsCloudidentityDevicesV1ApproveDeviceUserResponse(_messages.Message):
  r"""Response message for approving the device to access user data.

  Fields:
    deviceUser: Resultant DeviceUser object for the action.
  """

  deviceUser = _messages.MessageField('GoogleAppsCloudidentityDevicesV1DeviceUser', 1)


class GoogleAppsCloudidentityDevicesV1BlockDeviceUserMetadata(_messages.Message):
  r"""Metadata for BlockDeviceUser LRO."""


class GoogleAppsCloudidentityDevicesV1BlockDeviceUserResponse(_messages.Message):
  r"""Response message for blocking the device from accessing user data.

  Fields:
    deviceUser: Resultant DeviceUser object for the action.
  """

  deviceUser = _messages.MessageField('GoogleAppsCloudidentityDevicesV1DeviceUser', 1)


class GoogleAppsCloudidentityDevicesV1BrowserAttributes(_messages.Message):
  r"""Contains information about browser profiles reported by the [Endpoint
  Verification extension](https://chromewebstore.google.com/detail/endpoint-
  verification/callobklhcbilhphinckomhgkigmfocg?pli=1).

  Fields:
    chromeBrowserInfo: Represents the current state of the [Chrome browser
      attributes](https://cloud.google.com/access-context-
      manager/docs/browser-attributes) sent by the [Endpoint Verification
      extension](https://chromewebstore.google.com/detail/endpoint-
      verification/callobklhcbilhphinckomhgkigmfocg?pli=1).
    chromeProfileId: Chrome profile ID that is exposed by the Chrome API. It
      is unique for each device.
    lastProfileSyncTime: Timestamp in milliseconds since the Unix epoch when
      the profile/gcm id was last synced.
  """

  chromeBrowserInfo = _messages.MessageField('GoogleAppsCloudidentityDevicesV1BrowserInfo', 1)
  chromeProfileId = _messages.StringField(2)
  lastProfileSyncTime = _messages.StringField(3)


class GoogleAppsCloudidentityDevicesV1BrowserInfo(_messages.Message):
  r"""Browser-specific fields reported by the [Endpoint Verification
  extension](https://chromewebstore.google.com/detail/endpoint-
  verification/callobklhcbilhphinckomhgkigmfocg?pli=1).

  Enums:
    BrowserManagementStateValueValuesEnum: Output only. Browser's management
      state.
    PasswordProtectionWarningTriggerValueValuesEnum: Current state of
      [password protection trigger](https://chromeenterprise.google/policies/#
      PasswordProtectionWarningTrigger).
    SafeBrowsingProtectionLevelValueValuesEnum: Current state of [Safe
      Browsing protection level](https://chromeenterprise.google/policies/#Saf
      eBrowsingProtectionLevel).

  Fields:
    browserManagementState: Output only. Browser's management state.
    browserVersion: Version of the request initiating browser. E.g.
      `91.0.4442.4`.
    isBuiltInDnsClientEnabled: Current state of [built-in DNS client](https://
      chromeenterprise.google/policies/#BuiltInDnsClientEnabled).
    isBulkDataEntryAnalysisEnabled: Current state of [bulk data analysis](http
      s://chromeenterprise.google/policies/#OnBulkDataEntryEnterpriseConnector
      ). Set to true if provider list from Chrome is non-empty.
    isChromeCleanupEnabled: Current state of [Chrome
      Cleanup](https://chromeenterprise.google/policies/#ChromeCleanupEnabled)
      .
    isChromeRemoteDesktopAppBlocked: Current state of [Chrome Remote Desktop
      app](https://chromeenterprise.google/policies/#URLBlocklist).
    isFileDownloadAnalysisEnabled: Current state of [file download analysis](h
      ttps://chromeenterprise.google/policies/#OnFileDownloadedEnterpriseConne
      ctor). Set to true if provider list from Chrome is non-empty.
    isFileUploadAnalysisEnabled: Current state of [file upload analysis](https
      ://chromeenterprise.google/policies/#OnFileAttachedEnterpriseConnector).
      Set to true if provider list from Chrome is non-empty.
    isRealtimeUrlCheckEnabled: Current state of [real-time URL check](https://
      chromeenterprise.google/policies/#EnterpriseRealTimeUrlCheckMode). Set
      to true if provider list from Chrome is non-empty.
    isSecurityEventAnalysisEnabled: Current state of [security event analysis]
      (https://chromeenterprise.google/policies/#OnSecurityEventEnterpriseConn
      ector). Set to true if provider list from Chrome is non-empty.
    isSiteIsolationEnabled: Current state of [site isolation](https://chromeen
      terprise.google/policies/?policy=IsolateOrigins).
    isThirdPartyBlockingEnabled: Current state of [third-party blocking](https
      ://chromeenterprise.google/policies/#ThirdPartyBlockingEnabled).
    passwordProtectionWarningTrigger: Current state of [password protection tr
      igger](https://chromeenterprise.google/policies/#PasswordProtectionWarni
      ngTrigger).
    safeBrowsingProtectionLevel: Current state of [Safe Browsing protection le
      vel](https://chromeenterprise.google/policies/#SafeBrowsingProtectionLev
      el).
  """

  class BrowserManagementStateValueValuesEnum(_messages.Enum):
    r"""Output only. Browser's management state.

    Values:
      UNSPECIFIED: Management state is not specified.
      UNMANAGED: Browser/Profile is not managed by any customer.
      MANAGED_BY_OTHER_DOMAIN: Browser/Profile is managed, but by some other
        customer.
      PROFILE_MANAGED: Profile is managed by customer.
      BROWSER_MANAGED: Browser is managed by customer.
    """
    UNSPECIFIED = 0
    UNMANAGED = 1
    MANAGED_BY_OTHER_DOMAIN = 2
    PROFILE_MANAGED = 3
    BROWSER_MANAGED = 4

  class PasswordProtectionWarningTriggerValueValuesEnum(_messages.Enum):
    r"""Current state of [password protection trigger](https://chromeenterpris
    e.google/policies/#PasswordProtectionWarningTrigger).

    Values:
      PASSWORD_PROTECTION_TRIGGER_UNSPECIFIED: Password protection is not
        specified.
      PROTECTION_OFF: Password reuse is never detected.
      PASSWORD_REUSE: Warning is shown when the user reuses their protected
        password on a non-allowed site.
      PHISHING_REUSE: Warning is shown when the user reuses their protected
        password on a phishing site.
    """
    PASSWORD_PROTECTION_TRIGGER_UNSPECIFIED = 0
    PROTECTION_OFF = 1
    PASSWORD_REUSE = 2
    PHISHING_REUSE = 3

  class SafeBrowsingProtectionLevelValueValuesEnum(_messages.Enum):
    r"""Current state of [Safe Browsing protection level](https://chromeenterp
    rise.google/policies/#SafeBrowsingProtectionLevel).

    Values:
      SAFE_BROWSING_LEVEL_UNSPECIFIED: Browser protection level is not
        specified.
      DISABLED: No protection against dangerous websites, downloads, and
        extensions.
      STANDARD: Standard protection against websites, downloads, and
        extensions that are known to be dangerous.
      ENHANCED: Faster, proactive protection against dangerous websites,
        downloads, and extensions.
    """
    SAFE_BROWSING_LEVEL_UNSPECIFIED = 0
    DISABLED = 1
    STANDARD = 2
    ENHANCED = 3

  browserManagementState = _messages.EnumField('BrowserManagementStateValueValuesEnum', 1)
  browserVersion = _messages.StringField(2)
  isBuiltInDnsClientEnabled = _messages.BooleanField(3)
  isBulkDataEntryAnalysisEnabled = _messages.BooleanField(4)
  isChromeCleanupEnabled = _messages.BooleanField(5)
  isChromeRemoteDesktopAppBlocked = _messages.BooleanField(6)
  isFileDownloadAnalysisEnabled = _messages.BooleanField(7)
  isFileUploadAnalysisEnabled = _messages.BooleanField(8)
  isRealtimeUrlCheckEnabled = _messages.BooleanField(9)
  isSecurityEventAnalysisEnabled = _messages.BooleanField(10)
  isSiteIsolationEnabled = _messages.BooleanField(11)
  isThirdPartyBlockingEnabled = _messages.BooleanField(12)
  passwordProtectionWarningTrigger = _messages.EnumField('PasswordProtectionWarningTriggerValueValuesEnum', 13)
  safeBrowsingProtectionLevel = _messages.EnumField('SafeBrowsingProtectionLevelValueValuesEnum', 14)


class GoogleAppsCloudidentityDevicesV1CancelWipeDeviceMetadata(_messages.Message):
  r"""Metadata for CancelWipeDevice LRO."""


class GoogleAppsCloudidentityDevicesV1CancelWipeDeviceResponse(_messages.Message):
  r"""Response message for cancelling an unfinished device wipe.

  Fields:
    device: Resultant Device object for the action. Note that asset tags will
      not be returned in the device object.
  """

  device = _messages.MessageField('GoogleAppsCloudidentityDevicesV1Device', 1)


class GoogleAppsCloudidentityDevicesV1CancelWipeDeviceUserMetadata(_messages.Message):
  r"""Metadata for CancelWipeDeviceUser LRO."""


class GoogleAppsCloudidentityDevicesV1CancelWipeDeviceUserResponse(_messages.Message):
  r"""Response message for cancelling an unfinished user account wipe.

  Fields:
    deviceUser: Resultant DeviceUser object for the action.
  """

  deviceUser = _messages.MessageField('GoogleAppsCloudidentityDevicesV1DeviceUser', 1)


class GoogleAppsCloudidentityDevicesV1CertificateAttributes(_messages.Message):
  r"""Stores information about a certificate.

  Enums:
    ValidationStateValueValuesEnum: Output only. Validation state of this
      certificate.

  Fields:
    certificateTemplate: The X.509 extension for CertificateTemplate.
    fingerprint: The encoded certificate fingerprint.
    issuer: The name of the issuer of this certificate.
    serialNumber: Serial number of the certificate, Example: "*********".
    subject: The subject name of this certificate.
    thumbprint: The certificate thumbprint.
    validationState: Output only. Validation state of this certificate.
    validityExpirationTime: Certificate not valid at or after this timestamp.
    validityStartTime: Certificate not valid before this timestamp.
  """

  class ValidationStateValueValuesEnum(_messages.Enum):
    r"""Output only. Validation state of this certificate.

    Values:
      CERTIFICATE_VALIDATION_STATE_UNSPECIFIED: Default value.
      VALIDATION_SUCCESSFUL: Certificate validation was successful.
      VALIDATION_FAILED: Certificate validation failed.
    """
    CERTIFICATE_VALIDATION_STATE_UNSPECIFIED = 0
    VALIDATION_SUCCESSFUL = 1
    VALIDATION_FAILED = 2

  certificateTemplate = _messages.MessageField('GoogleAppsCloudidentityDevicesV1CertificateTemplate', 1)
  fingerprint = _messages.StringField(2)
  issuer = _messages.StringField(3)
  serialNumber = _messages.StringField(4)
  subject = _messages.StringField(5)
  thumbprint = _messages.StringField(6)
  validationState = _messages.EnumField('ValidationStateValueValuesEnum', 7)
  validityExpirationTime = _messages.StringField(8)
  validityStartTime = _messages.StringField(9)


class GoogleAppsCloudidentityDevicesV1CertificateTemplate(_messages.Message):
  r"""CertificateTemplate (v3 Extension in X.509).

  Fields:
    id: The template id of the template. Example: "*******.4.1.311.21.8.156086
      21.11768144.5720724.16068415.6889630.81.2472537.7784047".
    majorVersion: The Major version of the template. Example: 100.
    minorVersion: The minor version of the template. Example: 12.
  """

  id = _messages.StringField(1)
  majorVersion = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  minorVersion = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class GoogleAppsCloudidentityDevicesV1ClientState(_messages.Message):
  r"""Represents the state associated with an API client calling the Devices
  API. Resource representing ClientState and supports updates from API users

  Enums:
    ComplianceStateValueValuesEnum: The compliance state of the resource as
      specified by the API client.
    HealthScoreValueValuesEnum: The Health score of the resource. The Health
      score is the callers specification of the condition of the device from a
      usability point of view. For example, a third-party device management
      provider may specify a health score based on its compliance with
      organizational policies.
    ManagedValueValuesEnum: The management state of the resource as specified
      by the API client.
    OwnerTypeValueValuesEnum: Output only. The owner of the ClientState

  Messages:
    KeyValuePairsValue: The map of key-value attributes stored by callers
      specific to a device. The total serialized length of this map may not
      exceed 10KB. No limit is placed on the number of attributes in a map.

  Fields:
    assetTags: The caller can specify asset tags for this resource
    complianceState: The compliance state of the resource as specified by the
      API client.
    createTime: Output only. The time the client state data was created.
    customId: This field may be used to store a unique identifier for the API
      resource within which these CustomAttributes are a field.
    etag: The token that needs to be passed back for concurrency control in
      updates. Token needs to be passed back in UpdateRequest
    healthScore: The Health score of the resource. The Health score is the
      callers specification of the condition of the device from a usability
      point of view. For example, a third-party device management provider may
      specify a health score based on its compliance with organizational
      policies.
    keyValuePairs: The map of key-value attributes stored by callers specific
      to a device. The total serialized length of this map may not exceed
      10KB. No limit is placed on the number of attributes in a map.
    lastUpdateTime: Output only. The time the client state data was last
      updated.
    managed: The management state of the resource as specified by the API
      client.
    name: Output only. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      ClientState in format:
      `devices/{device}/deviceUsers/{device_user}/clientState/{partner}`,
      where partner corresponds to the partner storing the data. For partners
      belonging to the "BeyondCorp Alliance", this is the partner ID specified
      to you by Google. For all other callers, this is a string of the form:
      `{customer}-suffix`, where `customer` is your customer ID. The *suffix*
      is any string the caller specifies. This string will be displayed
      verbatim in the administration console. This suffix is used in setting
      up Custom Access Levels in Context-Aware Access. Your organization's
      customer ID can be obtained from the URL: `GET
      https://www.googleapis.com/admin/directory/v1/customers/my_customer` The
      `id` field in the response contains the customer ID starting with the
      letter 'C'. The customer ID to be used in this API is the string after
      the letter 'C' (not including 'C')
    ownerType: Output only. The owner of the ClientState
    scoreReason: A descriptive cause of the health score.
  """

  class ComplianceStateValueValuesEnum(_messages.Enum):
    r"""The compliance state of the resource as specified by the API client.

    Values:
      COMPLIANCE_STATE_UNSPECIFIED: The compliance state of the resource is
        unknown or unspecified.
      COMPLIANT: Device is compliant with third party policies
      NON_COMPLIANT: Device is not compliant with third party policies
    """
    COMPLIANCE_STATE_UNSPECIFIED = 0
    COMPLIANT = 1
    NON_COMPLIANT = 2

  class HealthScoreValueValuesEnum(_messages.Enum):
    r"""The Health score of the resource. The Health score is the callers
    specification of the condition of the device from a usability point of
    view. For example, a third-party device management provider may specify a
    health score based on its compliance with organizational policies.

    Values:
      HEALTH_SCORE_UNSPECIFIED: Default value
      VERY_POOR: The object is in very poor health as defined by the caller.
      POOR: The object is in poor health as defined by the caller.
      NEUTRAL: The object health is neither good nor poor, as defined by the
        caller.
      GOOD: The object is in good health as defined by the caller.
      VERY_GOOD: The object is in very good health as defined by the caller.
    """
    HEALTH_SCORE_UNSPECIFIED = 0
    VERY_POOR = 1
    POOR = 2
    NEUTRAL = 3
    GOOD = 4
    VERY_GOOD = 5

  class ManagedValueValuesEnum(_messages.Enum):
    r"""The management state of the resource as specified by the API client.

    Values:
      MANAGED_STATE_UNSPECIFIED: The management state of the resource is
        unknown or unspecified.
      MANAGED: The resource is managed.
      UNMANAGED: The resource is not managed.
    """
    MANAGED_STATE_UNSPECIFIED = 0
    MANAGED = 1
    UNMANAGED = 2

  class OwnerTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The owner of the ClientState

    Values:
      OWNER_TYPE_UNSPECIFIED: Unknown owner type
      OWNER_TYPE_CUSTOMER: Customer is the owner
      OWNER_TYPE_PARTNER: Partner is the owner
    """
    OWNER_TYPE_UNSPECIFIED = 0
    OWNER_TYPE_CUSTOMER = 1
    OWNER_TYPE_PARTNER = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class KeyValuePairsValue(_messages.Message):
    r"""The map of key-value attributes stored by callers specific to a
    device. The total serialized length of this map may not exceed 10KB. No
    limit is placed on the number of attributes in a map.

    Messages:
      AdditionalProperty: An additional property for a KeyValuePairsValue
        object.

    Fields:
      additionalProperties: Additional properties of type KeyValuePairsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a KeyValuePairsValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleAppsCloudidentityDevicesV1CustomAttributeValue
          attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleAppsCloudidentityDevicesV1CustomAttributeValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  assetTags = _messages.StringField(1, repeated=True)
  complianceState = _messages.EnumField('ComplianceStateValueValuesEnum', 2)
  createTime = _messages.StringField(3)
  customId = _messages.StringField(4)
  etag = _messages.StringField(5)
  healthScore = _messages.EnumField('HealthScoreValueValuesEnum', 6)
  keyValuePairs = _messages.MessageField('KeyValuePairsValue', 7)
  lastUpdateTime = _messages.StringField(8)
  managed = _messages.EnumField('ManagedValueValuesEnum', 9)
  name = _messages.StringField(10)
  ownerType = _messages.EnumField('OwnerTypeValueValuesEnum', 11)
  scoreReason = _messages.StringField(12)


class GoogleAppsCloudidentityDevicesV1CreateDeviceMetadata(_messages.Message):
  r"""Metadata for CreateDevice LRO."""


class GoogleAppsCloudidentityDevicesV1CustomAttributeValue(_messages.Message):
  r"""Additional custom attribute values may be one of these types

  Fields:
    boolValue: Represents a boolean value.
    numberValue: Represents a double value.
    stringValue: Represents a string value.
  """

  boolValue = _messages.BooleanField(1)
  numberValue = _messages.FloatField(2)
  stringValue = _messages.StringField(3)


class GoogleAppsCloudidentityDevicesV1DeleteDeviceMetadata(_messages.Message):
  r"""Metadata for DeleteDevice LRO."""


class GoogleAppsCloudidentityDevicesV1DeleteDeviceUserMetadata(_messages.Message):
  r"""Metadata for DeleteDeviceUser LRO."""


class GoogleAppsCloudidentityDevicesV1Device(_messages.Message):
  r""" A Device within the Cloud Identity Devices API. Represents a Device
  known to Google Cloud, independent of the device ownership, type, and
  whether it is assigned or in use by a user.

  Enums:
    CompromisedStateValueValuesEnum: Output only. Represents whether the
      Device is compromised.
    DeviceTypeValueValuesEnum: Output only. Type of device.
    EncryptionStateValueValuesEnum: Output only. Device encryption state.
    ManagementStateValueValuesEnum: Output only. Management state of the
      device
    OwnerTypeValueValuesEnum: Output only. Whether the device is owned by the
      company or an individual

  Fields:
    androidSpecificAttributes: Output only. Attributes specific to Android
      devices.
    assetTag: Asset tag of the device.
    basebandVersion: Output only. Baseband version of the device.
    bootloaderVersion: Output only. Device bootloader version. Example: 0.6.7.
    brand: Output only. Device brand. Example: Samsung.
    buildNumber: Output only. Build number of the device.
    compromisedState: Output only. Represents whether the Device is
      compromised.
    createTime: Output only. When the Company-Owned device was imported. This
      field is empty for BYOD devices.
    deviceId: Unique identifier for the device.
    deviceType: Output only. Type of device.
    enabledDeveloperOptions: Output only. Whether developer options is enabled
      on device.
    enabledUsbDebugging: Output only. Whether USB debugging is enabled on
      device.
    encryptionState: Output only. Device encryption state.
    endpointVerificationSpecificAttributes: Output only. Attributes specific
      to [Endpoint Verification](https://cloud.google.com/endpoint-
      verification/docs/overview) devices.
    hostname: Host name of the device.
    imei: Output only. IMEI number of device if GSM device; empty otherwise.
    kernelVersion: Output only. Kernel version of the device.
    lastSyncTime: Most recent time when device synced with this service.
    managementState: Output only. Management state of the device
    manufacturer: Output only. Device manufacturer. Example: Motorola.
    meid: Output only. MEID number of device if CDMA device; empty otherwise.
    model: Output only. Model name of device. Example: Pixel 3.
    name: Output only. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the Device
      in format: `devices/{device}`, where device is the unique id assigned to
      the Device. Important: Device API scopes require that you use domain-
      wide delegation to access the API. For more information, see [Set up the
      Devices API](https://cloud.google.com/identity/docs/how-to/setup-
      devices).
    networkOperator: Output only. Mobile or network operator of device, if
      available.
    osVersion: Output only. OS version of the device. Example: Android 8.1.0.
    otherAccounts: Output only. Domain name for Google accounts on device.
      Type for other accounts on device. On Android, will only be populated if
      |ownership_privilege| is |PROFILE_OWNER| or |DEVICE_OWNER|. Does not
      include the account signed in to the device policy app if that account's
      domain has only one account. Examples: "com.example", "xyz.com".
    ownerType: Output only. Whether the device is owned by the company or an
      individual
    releaseVersion: Output only. OS release version. Example: 6.0.
    securityPatchTime: Output only. OS security patch update time on device.
    serialNumber: Serial Number of device. Example: HT82V1A01076.
    unifiedDeviceId: Output only. Unified device id of the device.
    wifiMacAddresses: WiFi MAC addresses of device.
  """

  class CompromisedStateValueValuesEnum(_messages.Enum):
    r"""Output only. Represents whether the Device is compromised.

    Values:
      COMPROMISED_STATE_UNSPECIFIED: Default value.
      COMPROMISED: The device is compromised (currently, this means Android
        device is rooted).
      UNCOMPROMISED: The device is safe (currently, this means Android device
        is unrooted).
    """
    COMPROMISED_STATE_UNSPECIFIED = 0
    COMPROMISED = 1
    UNCOMPROMISED = 2

  class DeviceTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of device.

    Values:
      DEVICE_TYPE_UNSPECIFIED: Unknown device type
      ANDROID: Device is an Android device
      IOS: Device is an iOS device
      GOOGLE_SYNC: Device is a Google Sync device.
      WINDOWS: Device is a Windows device.
      MAC_OS: Device is a MacOS device.
      LINUX: Device is a Linux device.
      CHROME_OS: Device is a ChromeOS device.
    """
    DEVICE_TYPE_UNSPECIFIED = 0
    ANDROID = 1
    IOS = 2
    GOOGLE_SYNC = 3
    WINDOWS = 4
    MAC_OS = 5
    LINUX = 6
    CHROME_OS = 7

  class EncryptionStateValueValuesEnum(_messages.Enum):
    r"""Output only. Device encryption state.

    Values:
      ENCRYPTION_STATE_UNSPECIFIED: Encryption Status is not set.
      UNSUPPORTED_BY_DEVICE: Device doesn't support encryption.
      ENCRYPTED: Device is encrypted.
      NOT_ENCRYPTED: Device is not encrypted.
    """
    ENCRYPTION_STATE_UNSPECIFIED = 0
    UNSUPPORTED_BY_DEVICE = 1
    ENCRYPTED = 2
    NOT_ENCRYPTED = 3

  class ManagementStateValueValuesEnum(_messages.Enum):
    r"""Output only. Management state of the device

    Values:
      MANAGEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      APPROVED: Device is approved.
      BLOCKED: Device is blocked.
      PENDING: Device is pending approval.
      UNPROVISIONED: The device is not provisioned. Device will start from
        this state until some action is taken (i.e. a user starts using the
        device).
      WIPING: Data and settings on the device are being removed.
      WIPED: All data and settings on the device are removed.
    """
    MANAGEMENT_STATE_UNSPECIFIED = 0
    APPROVED = 1
    BLOCKED = 2
    PENDING = 3
    UNPROVISIONED = 4
    WIPING = 5
    WIPED = 6

  class OwnerTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Whether the device is owned by the company or an
    individual

    Values:
      DEVICE_OWNERSHIP_UNSPECIFIED: Default value. The value is unused.
      COMPANY: Company owns the device.
      BYOD: Bring Your Own Device (i.e. individual owns the device)
    """
    DEVICE_OWNERSHIP_UNSPECIFIED = 0
    COMPANY = 1
    BYOD = 2

  androidSpecificAttributes = _messages.MessageField('GoogleAppsCloudidentityDevicesV1AndroidAttributes', 1)
  assetTag = _messages.StringField(2)
  basebandVersion = _messages.StringField(3)
  bootloaderVersion = _messages.StringField(4)
  brand = _messages.StringField(5)
  buildNumber = _messages.StringField(6)
  compromisedState = _messages.EnumField('CompromisedStateValueValuesEnum', 7)
  createTime = _messages.StringField(8)
  deviceId = _messages.StringField(9)
  deviceType = _messages.EnumField('DeviceTypeValueValuesEnum', 10)
  enabledDeveloperOptions = _messages.BooleanField(11)
  enabledUsbDebugging = _messages.BooleanField(12)
  encryptionState = _messages.EnumField('EncryptionStateValueValuesEnum', 13)
  endpointVerificationSpecificAttributes = _messages.MessageField('GoogleAppsCloudidentityDevicesV1EndpointVerificationSpecificAttributes', 14)
  hostname = _messages.StringField(15)
  imei = _messages.StringField(16)
  kernelVersion = _messages.StringField(17)
  lastSyncTime = _messages.StringField(18)
  managementState = _messages.EnumField('ManagementStateValueValuesEnum', 19)
  manufacturer = _messages.StringField(20)
  meid = _messages.StringField(21)
  model = _messages.StringField(22)
  name = _messages.StringField(23)
  networkOperator = _messages.StringField(24)
  osVersion = _messages.StringField(25)
  otherAccounts = _messages.StringField(26, repeated=True)
  ownerType = _messages.EnumField('OwnerTypeValueValuesEnum', 27)
  releaseVersion = _messages.StringField(28)
  securityPatchTime = _messages.StringField(29)
  serialNumber = _messages.StringField(30)
  unifiedDeviceId = _messages.StringField(31)
  wifiMacAddresses = _messages.StringField(32, repeated=True)


class GoogleAppsCloudidentityDevicesV1DeviceUser(_messages.Message):
  r"""Represents a user's use of a Device in the Cloud Identity Devices API. A
  DeviceUser is a resource representing a user's use of a Device

  Enums:
    CompromisedStateValueValuesEnum: Compromised State of the DeviceUser
      object
    ManagementStateValueValuesEnum: Output only. Management state of the user
      on the device.
    PasswordStateValueValuesEnum: Password state of the DeviceUser object

  Fields:
    compromisedState: Compromised State of the DeviceUser object
    createTime: When the user first signed in to the device
    firstSyncTime: Output only. Most recent time when user registered with
      this service.
    languageCode: Output only. Default locale used on device, in IETF BCP-47
      format.
    lastSyncTime: Output only. Last time when user synced with policies.
    managementState: Output only. Management state of the user on the device.
    name: Output only. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      DeviceUser in format: `devices/{device}/deviceUsers/{device_user}`,
      where `device_user` uniquely identifies a user's use of a device.
    passwordState: Password state of the DeviceUser object
    userAgent: Output only. User agent on the device for this specific user
    userEmail: Email address of the user registered on the device.
  """

  class CompromisedStateValueValuesEnum(_messages.Enum):
    r"""Compromised State of the DeviceUser object

    Values:
      COMPROMISED_STATE_UNSPECIFIED: Compromised state of Device User account
        is unknown or unspecified.
      COMPROMISED: Device User Account is compromised.
      NOT_COMPROMISED: Device User Account is not compromised.
    """
    COMPROMISED_STATE_UNSPECIFIED = 0
    COMPROMISED = 1
    NOT_COMPROMISED = 2

  class ManagementStateValueValuesEnum(_messages.Enum):
    r"""Output only. Management state of the user on the device.

    Values:
      MANAGEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      WIPING: This user's data and profile is being removed from the device.
      WIPED: This user's data and profile is removed from the device.
      APPROVED: User is approved to access data on the device.
      BLOCKED: User is blocked from accessing data on the device.
      PENDING_APPROVAL: User is awaiting approval.
      UNENROLLED: User is unenrolled from Advanced Windows Management, but the
        Windows account is still intact.
    """
    MANAGEMENT_STATE_UNSPECIFIED = 0
    WIPING = 1
    WIPED = 2
    APPROVED = 3
    BLOCKED = 4
    PENDING_APPROVAL = 5
    UNENROLLED = 6

  class PasswordStateValueValuesEnum(_messages.Enum):
    r"""Password state of the DeviceUser object

    Values:
      PASSWORD_STATE_UNSPECIFIED: Password state not set.
      PASSWORD_SET: Password set in object.
      PASSWORD_NOT_SET: Password not set in object.
    """
    PASSWORD_STATE_UNSPECIFIED = 0
    PASSWORD_SET = 1
    PASSWORD_NOT_SET = 2

  compromisedState = _messages.EnumField('CompromisedStateValueValuesEnum', 1)
  createTime = _messages.StringField(2)
  firstSyncTime = _messages.StringField(3)
  languageCode = _messages.StringField(4)
  lastSyncTime = _messages.StringField(5)
  managementState = _messages.EnumField('ManagementStateValueValuesEnum', 6)
  name = _messages.StringField(7)
  passwordState = _messages.EnumField('PasswordStateValueValuesEnum', 8)
  userAgent = _messages.StringField(9)
  userEmail = _messages.StringField(10)


class GoogleAppsCloudidentityDevicesV1EndpointVerificationSpecificAttributes(_messages.Message):
  r"""Resource representing the [Endpoint Verification-specific
  attributes](https://cloud.google.com/endpoint-verification/docs/device-
  information) of a device.

  Messages:
    AdditionalSignalsValue: [Additional
      signals](https://cloud.google.com/endpoint-verification/docs/device-
      information) reported by Endpoint Verification. It includes the
      following attributes: * Non-configurable attributes: hotfixes,
      av_installed, av_enabled, windows_domain_name,
      is_os_native_firewall_enabled, and is_secure_boot_enabled. *
      [Configurable attributes](https://cloud.google.com/endpoint-
      verification/docs/collect-config-attributes): file, folder, and binary
      attributes; registry entries; and properties in a plist.

  Fields:
    additionalSignals: [Additional signals](https://cloud.google.com/endpoint-
      verification/docs/device-information) reported by Endpoint Verification.
      It includes the following attributes: * Non-configurable attributes:
      hotfixes, av_installed, av_enabled, windows_domain_name,
      is_os_native_firewall_enabled, and is_secure_boot_enabled. *
      [Configurable attributes](https://cloud.google.com/endpoint-
      verification/docs/collect-config-attributes): file, folder, and binary
      attributes; registry entries; and properties in a plist.
    browserAttributes: Details of browser profiles reported by Endpoint
      Verification.
    certificateAttributes: Details of certificates.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AdditionalSignalsValue(_messages.Message):
    r"""[Additional signals](https://cloud.google.com/endpoint-
    verification/docs/device-information) reported by Endpoint Verification.
    It includes the following attributes: * Non-configurable attributes:
    hotfixes, av_installed, av_enabled, windows_domain_name,
    is_os_native_firewall_enabled, and is_secure_boot_enabled. * [Configurable
    attributes](https://cloud.google.com/endpoint-verification/docs/collect-
    config-attributes): file, folder, and binary attributes; registry entries;
    and properties in a plist.

    Messages:
      AdditionalProperty: An additional property for a AdditionalSignalsValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AdditionalSignalsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  additionalSignals = _messages.MessageField('AdditionalSignalsValue', 1)
  browserAttributes = _messages.MessageField('GoogleAppsCloudidentityDevicesV1BrowserAttributes', 2, repeated=True)
  certificateAttributes = _messages.MessageField('GoogleAppsCloudidentityDevicesV1CertificateAttributes', 3, repeated=True)


class GoogleAppsCloudidentityDevicesV1ListEndpointAppsMetadata(_messages.Message):
  r"""Metadata for ListEndpointApps LRO."""


class GoogleAppsCloudidentityDevicesV1SignoutDeviceUserMetadata(_messages.Message):
  r"""Metadata for SignoutDeviceUser LRO."""


class GoogleAppsCloudidentityDevicesV1UpdateClientStateMetadata(_messages.Message):
  r"""Metadata for UpdateClientState LRO."""


class GoogleAppsCloudidentityDevicesV1UpdateDeviceMetadata(_messages.Message):
  r"""Metadata for UpdateDevice LRO."""


class GoogleAppsCloudidentityDevicesV1WipeDeviceMetadata(_messages.Message):
  r"""Metadata for WipeDevice LRO."""


class GoogleAppsCloudidentityDevicesV1WipeDeviceResponse(_messages.Message):
  r"""Response message for wiping all data on the device.

  Fields:
    device: Resultant Device object for the action. Note that asset tags will
      not be returned in the device object.
  """

  device = _messages.MessageField('GoogleAppsCloudidentityDevicesV1Device', 1)


class GoogleAppsCloudidentityDevicesV1WipeDeviceUserMetadata(_messages.Message):
  r"""Metadata for WipeDeviceUser LRO."""


class GoogleAppsCloudidentityDevicesV1WipeDeviceUserResponse(_messages.Message):
  r"""Response message for wiping the user's account from the device.

  Fields:
    deviceUser: Resultant DeviceUser object for the action.
  """

  deviceUser = _messages.MessageField('GoogleAppsCloudidentityDevicesV1DeviceUser', 1)


class Group(_messages.Message):
  r"""A group within the Cloud Identity Groups API. A `Group` is a collection
  of entities, where each entity is either a user, another group, or a service
  account.

  Messages:
    LabelsValue: Required. One or more label entries that apply to the Group.
      Labels contain a key with an empty value. Google Groups are the default
      type of group and have a label with a key of
      `cloudidentity.googleapis.com/groups.discussion_forum` and an empty
      value. Existing Google Groups can have an additional label with a key of
      `cloudidentity.googleapis.com/groups.security` and an empty value added
      to them. **This is an immutable change and the security label cannot be
      removed once added.** Dynamic groups have a label with a key of
      `cloudidentity.googleapis.com/groups.dynamic`. Identity-mapped groups
      for Cloud Search have a label with a key of `system/groups/external` and
      an empty value. Google Groups can be
      [locked](https://support.google.com/a?p=locked-groups). To lock a group,
      add a label with a key of `cloudidentity.googleapis.com/groups.locked`
      and an empty value. Doing so locks the group. To unlock the group,
      remove this label.

  Fields:
    additionalGroupKeys: Output only. Additional group keys associated with
      the Group.
    createTime: Output only. The time when the `Group` was created.
    description: An extended description to help users determine the purpose
      of a `Group`. Must not be longer than 4,096 characters.
    displayName: The display name of the `Group`.
    dynamicGroupMetadata: Optional. Dynamic group metadata like queries and
      status.
    groupKey: Required. The `EntityKey` of the `Group`.
    labels: Required. One or more label entries that apply to the Group.
      Labels contain a key with an empty value. Google Groups are the default
      type of group and have a label with a key of
      `cloudidentity.googleapis.com/groups.discussion_forum` and an empty
      value. Existing Google Groups can have an additional label with a key of
      `cloudidentity.googleapis.com/groups.security` and an empty value added
      to them. **This is an immutable change and the security label cannot be
      removed once added.** Dynamic groups have a label with a key of
      `cloudidentity.googleapis.com/groups.dynamic`. Identity-mapped groups
      for Cloud Search have a label with a key of `system/groups/external` and
      an empty value. Google Groups can be
      [locked](https://support.google.com/a?p=locked-groups). To lock a group,
      add a label with a key of `cloudidentity.googleapis.com/groups.locked`
      and an empty value. Doing so locks the group. To unlock the group,
      remove this label.
    name: Output only. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      `Group`. Shall be of the form `groups/{group_id}`.
    parent: Required. Immutable. The resource name of the entity under which
      this `Group` resides in the Cloud Identity resource hierarchy. Must be
      of the form `identitysources/{identity_source}` for external [identity-
      mapped groups](https://support.google.com/a/answer/9039510) or
      `customers/{customer_id}` for Google Groups. The `customer_id` must
      begin with "C" (for example, 'C046psxkn'). [Find your customer ID.]
      (https://support.google.com/cloudidentity/answer/10070793)
    posixGroups: Optional. The POSIX groups associated with the `Group`.
    updateTime: Output only. The time when the `Group` was last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Required. One or more label entries that apply to the Group. Labels
    contain a key with an empty value. Google Groups are the default type of
    group and have a label with a key of
    `cloudidentity.googleapis.com/groups.discussion_forum` and an empty value.
    Existing Google Groups can have an additional label with a key of
    `cloudidentity.googleapis.com/groups.security` and an empty value added to
    them. **This is an immutable change and the security label cannot be
    removed once added.** Dynamic groups have a label with a key of
    `cloudidentity.googleapis.com/groups.dynamic`. Identity-mapped groups for
    Cloud Search have a label with a key of `system/groups/external` and an
    empty value. Google Groups can be
    [locked](https://support.google.com/a?p=locked-groups). To lock a group,
    add a label with a key of `cloudidentity.googleapis.com/groups.locked` and
    an empty value. Doing so locks the group. To unlock the group, remove this
    label.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  additionalGroupKeys = _messages.MessageField('EntityKey', 1, repeated=True)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  displayName = _messages.StringField(4)
  dynamicGroupMetadata = _messages.MessageField('DynamicGroupMetadata', 5)
  groupKey = _messages.MessageField('EntityKey', 6)
  labels = _messages.MessageField('LabelsValue', 7)
  name = _messages.StringField(8)
  parent = _messages.StringField(9)
  posixGroups = _messages.MessageField('PosixGroup', 10, repeated=True)
  updateTime = _messages.StringField(11)


class GroupRelation(_messages.Message):
  r"""Message representing a transitive group of a user or a group.

  Enums:
    RelationTypeValueValuesEnum: The relation between the member and the
      transitive group.

  Messages:
    LabelsValue: Labels for Group resource.

  Fields:
    displayName: Display name for this group.
    group: Resource name for this group.
    groupKey: Entity key has an id and a namespace. In case of discussion
      forums, the id will be an email address without a namespace.
    labels: Labels for Group resource.
    relationType: The relation between the member and the transitive group.
    roles: Membership roles of the member for the group.
  """

  class RelationTypeValueValuesEnum(_messages.Enum):
    r"""The relation between the member and the transitive group.

    Values:
      RELATION_TYPE_UNSPECIFIED: The relation type is undefined or
        undetermined.
      DIRECT: The two entities have only a direct membership with each other.
      INDIRECT: The two entities have only an indirect membership with each
        other.
      DIRECT_AND_INDIRECT: The two entities have both a direct and an indirect
        membership with each other.
    """
    RELATION_TYPE_UNSPECIFIED = 0
    DIRECT = 1
    INDIRECT = 2
    DIRECT_AND_INDIRECT = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels for Group resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  group = _messages.StringField(2)
  groupKey = _messages.MessageField('EntityKey', 3)
  labels = _messages.MessageField('LabelsValue', 4)
  relationType = _messages.EnumField('RelationTypeValueValuesEnum', 5)
  roles = _messages.MessageField('TransitiveMembershipRole', 6, repeated=True)


class IdpCredential(_messages.Message):
  r"""Credential for verifying signatures produced by the Identity Provider.

  Fields:
    dsaKeyInfo: Output only. Information of a DSA public key.
    name: Output only. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      credential.
    rsaKeyInfo: Output only. Information of a RSA public key.
    updateTime: Output only. Time when the `IdpCredential` was last updated.
  """

  dsaKeyInfo = _messages.MessageField('DsaPublicKeyInfo', 1)
  name = _messages.StringField(2)
  rsaKeyInfo = _messages.MessageField('RsaPublicKeyInfo', 3)
  updateTime = _messages.StringField(4)


class InboundSamlSsoProfile(_messages.Message):
  r"""A [SAML 2.0](https://www.oasis-open.org/standards#samlv2.0) federation
  between a Google enterprise customer and a SAML identity provider.

  Fields:
    customer: Immutable. The customer. For example: `customers/C0123abc`.
    displayName: Human-readable name of the SAML SSO profile.
    idpConfig: SAML identity provider configuration.
    name: Output only. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the SAML
      SSO profile.
    spConfig: SAML service provider configuration for this SAML SSO profile.
      These are the service provider details provided by Google that should be
      configured on the corresponding identity provider.
  """

  customer = _messages.StringField(1)
  displayName = _messages.StringField(2)
  idpConfig = _messages.MessageField('SamlIdpConfig', 3)
  name = _messages.StringField(4)
  spConfig = _messages.MessageField('SamlSpConfig', 5)


class InboundSsoAssignment(_messages.Message):
  r"""Targets with "set" SSO assignments and their respective assignments.

  Enums:
    SsoModeValueValuesEnum: Inbound SSO behavior.

  Fields:
    customer: Immutable. The customer. For example: `customers/C0123abc`.
    name: Output only. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      Inbound SSO Assignment.
    rank: Must be zero (which is the default value so it can be omitted) for
      assignments with `target_org_unit` set and must be greater-than-or-
      equal-to one for assignments with `target_group` set.
    samlSsoInfo: SAML SSO details. Must be set if and only if `sso_mode` is
      set to `SAML_SSO`.
    signInBehavior: Assertions about users assigned to an IdP will always be
      accepted from that IdP. This controls whether/when Google should
      redirect a user to the IdP. Unset (defaults) is the recommended
      configuration.
    ssoMode: Inbound SSO behavior.
    targetGroup: Immutable. Must be of the form `groups/{group}`.
    targetOrgUnit: Immutable. Must be of the form `orgUnits/{org_unit}`.
  """

  class SsoModeValueValuesEnum(_messages.Enum):
    r"""Inbound SSO behavior.

    Values:
      SSO_MODE_UNSPECIFIED: Not allowed.
      SSO_OFF: Disable SSO for the targeted users.
      SAML_SSO: Use an external SAML Identity Provider for SSO for the
        targeted users.
      DOMAIN_WIDE_SAML_IF_ENABLED: Use the domain-wide SAML Identity Provider
        for the targeted users if one is configured; otherwise, this is
        equivalent to `SSO_OFF`. Note that this will also be equivalent to
        `SSO_OFF` if/when support for domain-wide SAML is removed. Google may
        disallow this mode at that point and existing assignments with this
        mode may be automatically changed to `SSO_OFF`.
    """
    SSO_MODE_UNSPECIFIED = 0
    SSO_OFF = 1
    SAML_SSO = 2
    DOMAIN_WIDE_SAML_IF_ENABLED = 3

  customer = _messages.StringField(1)
  name = _messages.StringField(2)
  rank = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  samlSsoInfo = _messages.MessageField('SamlSsoInfo', 4)
  signInBehavior = _messages.MessageField('SignInBehavior', 5)
  ssoMode = _messages.EnumField('SsoModeValueValuesEnum', 6)
  targetGroup = _messages.StringField(7)
  targetOrgUnit = _messages.StringField(8)


class IsInvitableUserResponse(_messages.Message):
  r"""Response for IsInvitableUser RPC.

  Fields:
    isInvitableUser: Returns true if the email address is invitable.
  """

  isInvitableUser = _messages.BooleanField(1)


class ListClientStatesResponse(_messages.Message):
  r"""Response message that is returned in LRO result of ListClientStates
  Operation.

  Fields:
    clientStates: Client states meeting the list restrictions.
    nextPageToken: Token to retrieve the next page of results. Empty if there
      are no more results.
  """

  clientStates = _messages.MessageField('ClientState', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListDeviceUsersResponse(_messages.Message):
  r"""Response message that is returned from the ListDeviceUsers method.

  Fields:
    deviceUsers: Devices meeting the list restrictions.
    nextPageToken: Token to retrieve the next page of results. Empty if there
      are no more results.
  """

  deviceUsers = _messages.MessageField('DeviceUser', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListDevicesResponse(_messages.Message):
  r"""Response message that is returned from the ListDevices method.

  Fields:
    devices: Devices meeting the list restrictions.
    nextPageToken: Token to retrieve the next page of results. Empty if there
      are no more results.
  """

  devices = _messages.MessageField('Device', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListGroupsResponse(_messages.Message):
  r"""The response message for GroupsService.ListGroups.

  Fields:
    groups: The `Group` resources under the specified `parent`.
    nextPageToken: A continuation token to retrieve the next page of results,
      or empty if there are no more results available.
  """

  groups = _messages.MessageField('Group', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListIdpCredentialsResponse(_messages.Message):
  r"""Response of the InboundSamlSsoProfilesService.ListIdpCredentials method.

  Fields:
    idpCredentials: The IdpCredentials from the specified
      InboundSamlSsoProfile.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  idpCredentials = _messages.MessageField('IdpCredential', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListInboundSamlSsoProfilesResponse(_messages.Message):
  r"""Response of the InboundSamlSsoProfilesService.ListInboundSamlSsoProfiles
  method.

  Fields:
    inboundSamlSsoProfiles: List of InboundSamlSsoProfiles.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  inboundSamlSsoProfiles = _messages.MessageField('InboundSamlSsoProfile', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListInboundSsoAssignmentsResponse(_messages.Message):
  r"""Response of the InboundSsoAssignmentsService.ListInboundSsoAssignments
  method.

  Fields:
    inboundSsoAssignments: The assignments.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  inboundSsoAssignments = _messages.MessageField('InboundSsoAssignment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListMembershipsResponse(_messages.Message):
  r"""The response message for MembershipsService.ListMemberships.

  Fields:
    memberships: The `Membership`s under the specified `parent`.
    nextPageToken: A continuation token to retrieve the next page of results,
      or empty if there are no more results available.
  """

  memberships = _messages.MessageField('Membership', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOrgMembershipsResponse(_messages.Message):
  r"""The response message for OrgMembershipsService.ListOrgMemberships.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is empty, there are no subsequent pages.
    orgMemberships: The non-vacuous membership in an orgUnit.
  """

  nextPageToken = _messages.StringField(1)
  orgMemberships = _messages.MessageField('OrgMembership', 2, repeated=True)


class ListPoliciesResponse(_messages.Message):
  r"""The response message for PoliciesService.ListPolicies.

  Fields:
    nextPageToken: The pagination token to retrieve the next page of results.
      If this field is empty, there are no subsequent pages.
    policies: The results
  """

  nextPageToken = _messages.StringField(1)
  policies = _messages.MessageField('Policy', 2, repeated=True)


class ListUserInvitationsResponse(_messages.Message):
  r"""Response message for UserInvitation listing request.

  Fields:
    nextPageToken: The token for the next page. If not empty, indicates that
      there may be more `UserInvitation` resources that match the listing
      request; this value can be used in a subsequent
      ListUserInvitationsRequest to get continued results with the current
      list call.
    userInvitations: The list of UserInvitation resources.
  """

  nextPageToken = _messages.StringField(1)
  userInvitations = _messages.MessageField('UserInvitation', 2, repeated=True)


class LookupGroupNameResponse(_messages.Message):
  r"""The response message for GroupsService.LookupGroupName.

  Fields:
    name: Output only. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      looked-up `Group`.
  """

  name = _messages.StringField(1)


class LookupMembershipNameResponse(_messages.Message):
  r"""The response message for MembershipsService.LookupMembershipName.

  Fields:
    name: The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      looked-up `Membership`. Must be of the form
      `groups/{group_id}/memberships/{membership_id}`.
  """

  name = _messages.StringField(1)


class LookupSelfDeviceUsersResponse(_messages.Message):
  r"""Response containing resource names of the DeviceUsers associated with
  the caller's credentials.

  Fields:
    customer: The customer Id that may be passed back to other Devices API
      methods such as List, Get, etc.
    names: [Resource
      names](https://cloud.google.com/apis/design/resource_names) of the
      DeviceUsers in the format:
      `devices/{device_id}/deviceUsers/{user_resource_id}`, where device_id is
      the unique ID assigned to a Device and user_resource_id is the unique
      user ID
    nextPageToken: Token to retrieve the next page of results. Empty if there
      are no more results.
  """

  customer = _messages.StringField(1)
  names = _messages.StringField(2, repeated=True)
  nextPageToken = _messages.StringField(3)


class MemberRelation(_messages.Message):
  r"""Message representing a transitive membership of a group.

  Enums:
    RelationTypeValueValuesEnum: The relation between the group and the
      transitive membership.

  Fields:
    member: Resource name for this member.
    preferredMemberKey: Entity key has an id and a namespace. In case of
      discussion forums, the id will be an email address without a namespace.
    relationType: The relation between the group and the transitive
      membership.
    roles: The membership role details (i.e name of role and expiry time).
  """

  class RelationTypeValueValuesEnum(_messages.Enum):
    r"""The relation between the group and the transitive membership.

    Values:
      RELATION_TYPE_UNSPECIFIED: The relation type is undefined or
        undetermined.
      DIRECT: The two entities have only a direct membership with each other.
      INDIRECT: The two entities have only an indirect membership with each
        other.
      DIRECT_AND_INDIRECT: The two entities have both a direct and an indirect
        membership with each other.
    """
    RELATION_TYPE_UNSPECIFIED = 0
    DIRECT = 1
    INDIRECT = 2
    DIRECT_AND_INDIRECT = 3

  member = _messages.StringField(1)
  preferredMemberKey = _messages.MessageField('EntityKey', 2, repeated=True)
  relationType = _messages.EnumField('RelationTypeValueValuesEnum', 3)
  roles = _messages.MessageField('TransitiveMembershipRole', 4, repeated=True)


class MemberRestriction(_messages.Message):
  r"""The definition of MemberRestriction

  Fields:
    evaluation: The evaluated state of this restriction on a group.
    query: Member Restriction as defined by CEL expression. Supported
      restrictions are: `member.customer_id` and `member.type`. Valid values
      for `member.type` are `1`, `2` and `3`. They correspond to USER,
      SERVICE_ACCOUNT, and GROUP respectively. The value for
      `member.customer_id` only supports `groupCustomerId()` currently which
      means the customer id of the group will be used for restriction.
      Supported operators are `&&`, `||` and `==`, corresponding to AND, OR,
      and EQUAL. Examples: Allow only service accounts of given customer to be
      members. `member.type == 2 && member.customer_id == groupCustomerId()`
      Allow only users or groups to be members. `member.type == 1 ||
      member.type == 3`
  """

  evaluation = _messages.MessageField('RestrictionEvaluation', 1)
  query = _messages.StringField(2)


class Membership(_messages.Message):
  r"""A membership within the Cloud Identity Groups API. A `Membership`
  defines a relationship between a `Group` and an entity belonging to that
  `Group`, referred to as a "member".

  Enums:
    DeliverySettingValueValuesEnum: Output only. Delivery setting associated
      with the membership.
    TypeValueValuesEnum: Output only. The type of the membership.

  Fields:
    createTime: Output only. The time when the `Membership` was created.
    deliverySetting: Output only. Delivery setting associated with the
      membership.
    memberKey: Immutable. The `EntityKey` of the member. Either `member_key`
      or `preferred_member_key` must be set when calling
      MembershipsService.CreateMembership but not both; both shall be set when
      returned.
    name: Output only. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      `Membership`. Shall be of the form
      `groups/{group_id}/memberships/{membership_id}`.
    preferredMemberKey: Required. Immutable. The `EntityKey` of the member.
      Either `member_key` or `preferred_member_key` must be set when calling
      MembershipsService.CreateMembership but not both; both shall be set when
      returned.
    roles: The `MembershipRole`s that apply to the `Membership`. If
      unspecified, defaults to a single `MembershipRole` with `name` `MEMBER`.
      Must not contain duplicate `MembershipRole`s with the same `name`.
    type: Output only. The type of the membership.
    updateTime: Output only. The time when the `Membership` was last updated.
  """

  class DeliverySettingValueValuesEnum(_messages.Enum):
    r"""Output only. Delivery setting associated with the membership.

    Values:
      DELIVERY_SETTING_UNSPECIFIED: Default. Should not be used.
      ALL_MAIL: Represents each mail should be delivered
      DIGEST: Represents 1 email for every 25 messages.
      DAILY: Represents daily summary of messages.
      NONE: Represents no delivery.
      DISABLED: Represents disabled state.
    """
    DELIVERY_SETTING_UNSPECIFIED = 0
    ALL_MAIL = 1
    DIGEST = 2
    DAILY = 3
    NONE = 4
    DISABLED = 5

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the membership.

    Values:
      TYPE_UNSPECIFIED: Default. Should not be used.
      USER: Represents user type.
      SERVICE_ACCOUNT: Represents service account type.
      GROUP: Represents group type.
      SHARED_DRIVE: Represents Shared drive.
      CBCM_BROWSER: Represents a CBCM-managed Chrome Browser type.
      OTHER: Represents other type.
    """
    TYPE_UNSPECIFIED = 0
    USER = 1
    SERVICE_ACCOUNT = 2
    GROUP = 3
    SHARED_DRIVE = 4
    CBCM_BROWSER = 5
    OTHER = 6

  createTime = _messages.StringField(1)
  deliverySetting = _messages.EnumField('DeliverySettingValueValuesEnum', 2)
  memberKey = _messages.MessageField('EntityKey', 3)
  name = _messages.StringField(4)
  preferredMemberKey = _messages.MessageField('EntityKey', 5)
  roles = _messages.MessageField('MembershipRole', 6, repeated=True)
  type = _messages.EnumField('TypeValueValuesEnum', 7)
  updateTime = _messages.StringField(8)


class MembershipAdjacencyList(_messages.Message):
  r"""Membership graph's path information as an adjacency list.

  Fields:
    edges: Each edge contains information about the member that belongs to
      this group. Note: Fields returned here will help identify the specific
      Membership resource (e.g `name`, `preferred_member_key` and `role`), but
      may not be a comprehensive list of all fields.
    group: Resource name of the group that the members belong to.
  """

  edges = _messages.MessageField('Membership', 1, repeated=True)
  group = _messages.StringField(2)


class MembershipRelation(_messages.Message):
  r"""Message containing membership relation.

  Messages:
    LabelsValue: One or more label entries that apply to the Group. Currently
      supported labels contain a key with an empty value.

  Fields:
    description: An extended description to help users determine the purpose
      of a `Group`.
    displayName: The display name of the `Group`.
    group: The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      `Group`. Shall be of the form `groups/{group_id}`.
    groupKey: The `EntityKey` of the `Group`.
    labels: One or more label entries that apply to the Group. Currently
      supported labels contain a key with an empty value.
    membership: The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      `Membership`. Shall be of the form
      `groups/{group_id}/memberships/{membership_id}`.
    roles: The `MembershipRole`s that apply to the `Membership`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""One or more label entries that apply to the Group. Currently supported
    labels contain a key with an empty value.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  group = _messages.StringField(3)
  groupKey = _messages.MessageField('EntityKey', 4)
  labels = _messages.MessageField('LabelsValue', 5)
  membership = _messages.StringField(6)
  roles = _messages.MessageField('MembershipRole', 7, repeated=True)


class MembershipRole(_messages.Message):
  r"""A membership role within the Cloud Identity Groups API. A
  `MembershipRole` defines the privileges granted to a `Membership`.

  Fields:
    expiryDetail: The expiry details of the `MembershipRole`. Expiry details
      are only supported for `MEMBER` `MembershipRoles`. May be set if `name`
      is `MEMBER`. Must not be set if `name` is any other value.
    name: The name of the `MembershipRole`. Must be one of `OWNER`, `MANAGER`,
      `MEMBER`.
    restrictionEvaluations: Evaluations of restrictions applied to parent
      group on this membership.
  """

  expiryDetail = _messages.MessageField('ExpiryDetail', 1)
  name = _messages.StringField(2)
  restrictionEvaluations = _messages.MessageField('RestrictionEvaluations', 3)


class MembershipRoleRestrictionEvaluation(_messages.Message):
  r"""The evaluated state of this restriction.

  Enums:
    StateValueValuesEnum: Output only. The current state of the restriction

  Fields:
    state: Output only. The current state of the restriction
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the restriction

    Values:
      STATE_UNSPECIFIED: Default. Should not be used.
      COMPLIANT: The member adheres to the parent group's restriction.
      FORWARD_COMPLIANT: The group-group membership might be currently
        violating some parent group's restriction but in future, it will never
        allow any new member in the child group which can violate parent
        group's restriction.
      NON_COMPLIANT: The member violates the parent group's restriction.
      EVALUATING: The state of the membership is under evaluation.
    """
    STATE_UNSPECIFIED = 0
    COMPLIANT = 1
    FORWARD_COMPLIANT = 2
    NON_COMPLIANT = 3
    EVALUATING = 4

  state = _messages.EnumField('StateValueValuesEnum', 1)


class ModifyMembershipRolesRequest(_messages.Message):
  r"""The request message for MembershipsService.ModifyMembershipRoles.

  Fields:
    addRoles: The `MembershipRole`s to be added. Adding or removing roles in
      the same request as updating roles is not supported. Must not be set if
      `update_roles_params` is set.
    removeRoles: The `name`s of the `MembershipRole`s to be removed. Adding or
      removing roles in the same request as updating roles is not supported.
      It is not possible to remove the `MEMBER` `MembershipRole`. If you wish
      to delete a `Membership`, call MembershipsService.DeleteMembership
      instead. Must not contain `MEMBER`. Must not be set if
      `update_roles_params` is set.
    updateRolesParams: The `MembershipRole`s to be updated. Updating roles in
      the same request as adding or removing roles is not supported. Must not
      be set if either `add_roles` or `remove_roles` is set.
  """

  addRoles = _messages.MessageField('MembershipRole', 1, repeated=True)
  removeRoles = _messages.StringField(2, repeated=True)
  updateRolesParams = _messages.MessageField('UpdateMembershipRolesParams', 3, repeated=True)


class ModifyMembershipRolesResponse(_messages.Message):
  r"""The response message for MembershipsService.ModifyMembershipRoles.

  Fields:
    membership: The `Membership` resource after modifying its
      `MembershipRole`s.
  """

  membership = _messages.MessageField('Membership', 1)


class MoveOrgMembershipRequest(_messages.Message):
  r"""The request message for OrgMembershipsService.MoveOrgMembership.

  Fields:
    customer: Required. Immutable. Customer on whose membership change is
      made. All authorization will happen on the role assignments of this
      customer. Format: customers/{$customerId} where `$customerId` is the
      `id` from the [Admin SDK `Customer`
      resource](https://developers.google.com/admin-
      sdk/directory/reference/rest/v1/customers). You may also use
      `customers/my_customer` to specify your own organization.
    destinationOrgUnit: Required. Immutable. OrgUnit where the membership will
      be moved to. Format: orgUnits/{$orgUnitId} where `$orgUnitId` is the
      `orgUnitId` from the [Admin SDK `OrgUnit`
      resource](https://developers.google.com/admin-
      sdk/directory/reference/rest/v1/orgunits).
  """

  customer = _messages.StringField(1)
  destinationOrgUnit = _messages.StringField(2)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OrgMembership(_messages.Message):
  r"""A membership in an OrgUnit. An `OrgMembership` defines a relationship
  between an `OrgUnit` and an entity belonging to that `OrgUnit`, referred to
  as a "member".

  Enums:
    TypeValueValuesEnum: Immutable. Entity type for the org member.

  Fields:
    member: Immutable. Org member id as full resource name. Format for shared
      drive resource: //drive.googleapis.com/drives/{$memberId} where
      `$memberId` is the `id` from [Drive API (V3) `Drive` resource](https://d
      evelopers.google.com/drive/api/v3/reference/drives#resource).
    memberUri: Uri with which you can read the member. This follows
      https://aip.dev/122 Format for shared drive resource:
      https://drive.googleapis.com/drive/v3/drives/{$memberId} where
      `$memberId` is the `id` from [Drive API (V3) `Drive` resource](https://d
      evelopers.google.com/drive/api/v3/reference/drives#resource).
    name: Required. Immutable. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      OrgMembership. Format: orgUnits/{$orgUnitId}/memberships/{$membership}
      The `$orgUnitId` is the `orgUnitId` from the [Admin SDK `OrgUnit`
      resource](https://developers.google.com/admin-
      sdk/directory/reference/rest/v1/orgunits). The `$membership` shall be of
      the form `{$entityType};{$memberId}`, where `$entityType` is the enum
      value of [OrgMembership.EntityType], and `memberId` is the `id` from
      [Drive API (V3) `Drive` resource](https://developers.google.com/drive/ap
      i/v3/reference/drives#resource) for
      OrgMembership.EntityType.SHARED_DRIVE.
    type: Immutable. Entity type for the org member.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Immutable. Entity type for the org member.

    Values:
      ENTITY_TYPE_UNSPECIFIED: Equivalent to no resource type mentioned
      SHARED_DRIVE: Shared drive as resource type
    """
    ENTITY_TYPE_UNSPECIFIED = 0
    SHARED_DRIVE = 1

  member = _messages.StringField(1)
  memberUri = _messages.StringField(2)
  name = _messages.StringField(3)
  type = _messages.EnumField('TypeValueValuesEnum', 4)


class Policy(_messages.Message):
  r"""A Policy resource binds an instance of a single Setting with the scope
  of a PolicyQuery. The Setting instance will be applied to all entities that
  satisfy the query.

  Enums:
    TypeValueValuesEnum: Output only. The type of the policy.

  Fields:
    customer: Immutable. Customer that the Policy belongs to. The value is in
      the format 'customers/{customerId}'. The `customerId` must begin with
      "C" To find your customer ID in Admin Console see
      https://support.google.com/a/answer/10070793.
    name: Output only. Identifier. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      Policy. Format: policies/{policy}.
    policyQuery: Required. The PolicyQuery the Setting applies to.
    setting: Required. The Setting configured by this Policy.
    type: Output only. The type of the policy.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the policy.

    Values:
      POLICY_TYPE_UNSPECIFIED: Unspecified policy type.
      SYSTEM: Policy type denoting the system-configured policies.
      ADMIN: Policy type denoting the admin-configurable policies.
    """
    POLICY_TYPE_UNSPECIFIED = 0
    SYSTEM = 1
    ADMIN = 2

  customer = _messages.StringField(1)
  name = _messages.StringField(2)
  policyQuery = _messages.MessageField('PolicyQuery', 3)
  setting = _messages.MessageField('Setting', 4)
  type = _messages.EnumField('TypeValueValuesEnum', 5)


class PolicyQuery(_messages.Message):
  r"""PolicyQuery

  Fields:
    group: Immutable. The group that the query applies to. This field is only
      set if there is a single value for group that satisfies all clauses of
      the query. If no group applies, this will be the empty string.
    orgUnit: Required. Immutable. Non-empty default. The OrgUnit the query
      applies to. This field is only set if there is a single value for
      org_unit that satisfies all clauses of the query.
    query: Immutable. The CEL query that defines which entities the Policy
      applies to (ex. a User entity). For details about CEL see
      https://opensource.google.com/projects/cel. The OrgUnits the Policy
      applies to are represented by a clause like so:
      entity.org_units.exists(org_unit, org_unit.org_unit_id ==
      orgUnitId('{orgUnitId}')) The Group the Policy applies to are
      represented by a clause like so: entity.groups.exists(group,
      group.group_id == groupId('{groupId}')) The Licenses the Policy applies
      to are represented by a clause like so: entity.licenses.exists(license,
      license in ['/product/{productId}/sku/{skuId}']) The above clauses can
      be present in any combination, and used in conjunction with the &&, ||
      and ! operators. The org_unit and group fields below are helper fields
      that contain the corresponding value(s) as the query to make the query
      easier to use.
    sortOrder: Output only. The decimal sort order of this PolicyQuery. The
      value is relative to all other policies with the same setting type for
      the customer. (There are no duplicates within this set).
  """

  group = _messages.StringField(1)
  orgUnit = _messages.StringField(2)
  query = _messages.StringField(3)
  sortOrder = _messages.FloatField(4)


class PosixGroup(_messages.Message):
  r"""POSIX Group definition to represent a group in a POSIX compliant system.
  Caution: POSIX groups are deprecated. As of September 26, 2024, you can no
  longer create new POSIX groups. For more information, see
  https://cloud.google.com/identity/docs/deprecations/posix-groups

  Fields:
    gid: GID of the POSIX group.
    name: Name of the POSIX group.
    systemId: System identifier for which group name and gid apply to. If not
      specified it will default to empty value.
  """

  gid = _messages.IntegerField(1, variant=_messages.Variant.UINT64)
  name = _messages.StringField(2)
  systemId = _messages.StringField(3)


class RestrictionEvaluation(_messages.Message):
  r"""The evaluated state of this restriction.

  Enums:
    StateValueValuesEnum: Output only. The current state of the restriction

  Fields:
    state: Output only. The current state of the restriction
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the restriction

    Values:
      STATE_UNSPECIFIED: Default. Should not be used.
      EVALUATING: The restriction state is currently being evaluated.
      COMPLIANT: All transitive memberships are adhering to restriction.
      FORWARD_COMPLIANT: Some transitive memberships violate the restriction.
        No new violating memberships can be added.
      NON_COMPLIANT: Some transitive memberships violate the restriction. New
        violating direct memberships will be denied while indirect memberships
        may be added.
    """
    STATE_UNSPECIFIED = 0
    EVALUATING = 1
    COMPLIANT = 2
    FORWARD_COMPLIANT = 3
    NON_COMPLIANT = 4

  state = _messages.EnumField('StateValueValuesEnum', 1)


class RestrictionEvaluations(_messages.Message):
  r"""Evaluations of restrictions applied to parent group on this membership.

  Fields:
    memberRestrictionEvaluation: Evaluation of the member restriction applied
      to this membership. Empty if the user lacks permission to view the
      restriction evaluation.
  """

  memberRestrictionEvaluation = _messages.MessageField('MembershipRoleRestrictionEvaluation', 1)


class RsaPublicKeyInfo(_messages.Message):
  r"""Information of a RSA public key.

  Fields:
    keySize: Key size in bits (size of the modulus).
  """

  keySize = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class SamlIdpConfig(_messages.Message):
  r"""SAML IDP (identity provider) configuration.

  Fields:
    changePasswordUri: The **Change Password URL** of the identity provider.
      Users will be sent to this URL when changing their passwords at
      `myaccount.google.com`. This takes precedence over the change password
      URL configured at customer-level. Must use `HTTPS`.
    entityId: Required. The SAML **Entity ID** of the identity provider.
    logoutRedirectUri: The **Logout Redirect URL** (sign-out page URL) of the
      identity provider. When a user clicks the sign-out link on a Google
      page, they will be redirected to this URL. This is a pure redirect with
      no attached SAML `LogoutRequest` i.e. SAML single logout is not
      supported. Must use `HTTPS`.
    singleSignOnServiceUri: Required. The `SingleSignOnService` endpoint
      location (sign-in page URL) of the identity provider. This is the URL
      where the `AuthnRequest` will be sent. Must use `HTTPS`. Assumed to
      accept the `HTTP-Redirect` binding.
  """

  changePasswordUri = _messages.StringField(1)
  entityId = _messages.StringField(2)
  logoutRedirectUri = _messages.StringField(3)
  singleSignOnServiceUri = _messages.StringField(4)


class SamlSpConfig(_messages.Message):
  r"""SAML SP (service provider) configuration.

  Fields:
    assertionConsumerServiceUri: Output only. The SAML **Assertion Consumer
      Service (ACS) URL** to be used for the IDP-initiated login. Assumed to
      accept response messages via the `HTTP-POST` binding.
    entityId: Output only. The SAML **Entity ID** for this service provider.
  """

  assertionConsumerServiceUri = _messages.StringField(1)
  entityId = _messages.StringField(2)


class SamlSsoInfo(_messages.Message):
  r"""Details that are applicable when `sso_mode` == `SAML_SSO`.

  Fields:
    inboundSamlSsoProfile: Required. Name of the `InboundSamlSsoProfile` to
      use. Must be of the form
      `inboundSamlSsoProfiles/{inbound_saml_sso_profile}`.
  """

  inboundSamlSsoProfile = _messages.StringField(1)


class SearchDirectGroupsResponse(_messages.Message):
  r"""The response message for MembershipsService.SearchDirectGroups.

  Fields:
    memberships: List of direct groups satisfying the query.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results available for listing.
  """

  memberships = _messages.MessageField('MembershipRelation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class SearchGroupsResponse(_messages.Message):
  r"""The response message for GroupsService.SearchGroups.

  Fields:
    groups: The `Group` resources that match the search query.
    nextPageToken: A continuation token to retrieve the next page of results,
      or empty if there are no more results available.
  """

  groups = _messages.MessageField('Group', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class SearchTransitiveGroupsResponse(_messages.Message):
  r"""The response message for MembershipsService.SearchTransitiveGroups.

  Fields:
    memberships: List of transitive groups satisfying the query.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results available for listing.
  """

  memberships = _messages.MessageField('GroupRelation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class SearchTransitiveMembershipsResponse(_messages.Message):
  r"""The response message for MembershipsService.SearchTransitiveMemberships.

  Fields:
    memberships: List of transitive memberships satisfying the query.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results.
  """

  memberships = _messages.MessageField('MemberRelation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class SecuritySettings(_messages.Message):
  r"""The definiion of security settings.

  Fields:
    memberRestriction: The Member Restriction value
    name: Output only. The resource name of the security settings. Shall be of
      the form `groups/{group_id}/securitySettings`.
  """

  memberRestriction = _messages.MessageField('MemberRestriction', 1)
  name = _messages.StringField(2)


class SendUserInvitationRequest(_messages.Message):
  r"""A request to send email for inviting target user corresponding to the
  UserInvitation.
  """



class Setting(_messages.Message):
  r"""Setting

  Messages:
    ValueValue: Required. The value of the Setting.

  Fields:
    type: Required. Immutable. The type of the Setting. .
    value: Required. The value of the Setting.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ValueValue(_messages.Message):
    r"""Required. The value of the Setting.

    Messages:
      AdditionalProperty: An additional property for a ValueValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ValueValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  type = _messages.StringField(1)
  value = _messages.MessageField('ValueValue', 2)


class SignInBehavior(_messages.Message):
  r"""Controls sign-in behavior.

  Enums:
    RedirectConditionValueValuesEnum: When to redirect sign-ins to the IdP.

  Fields:
    redirectCondition: When to redirect sign-ins to the IdP.
  """

  class RedirectConditionValueValuesEnum(_messages.Enum):
    r"""When to redirect sign-ins to the IdP.

    Values:
      REDIRECT_CONDITION_UNSPECIFIED: Default and means "always"
      NEVER: Sign-in flows where the user is prompted for their identity will
        not redirect to the IdP (so the user will most likely be prompted by
        Google for a password), but special flows like IdP-initiated SAML and
        sign-in following automatic redirection to the IdP by domain-specific
        service URLs will accept the IdP's assertion of the user's identity.
    """
    REDIRECT_CONDITION_UNSPECIFIED = 0
    NEVER = 1

  redirectCondition = _messages.EnumField('RedirectConditionValueValuesEnum', 1)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TransitiveMembershipRole(_messages.Message):
  r"""Message representing the role of a TransitiveMembership.

  Fields:
    role: TransitiveMembershipRole in string format. Currently supported
      TransitiveMembershipRoles: `"MEMBER"`, `"OWNER"`, and `"MANAGER"`.
  """

  role = _messages.StringField(1)


class UpdateInboundSamlSsoProfileOperationMetadata(_messages.Message):
  r"""LRO response metadata for
  InboundSamlSsoProfilesService.UpdateInboundSamlSsoProfile.

  Fields:
    state: State of this Operation Will be "awaiting-multi-party-approval"
      when the operation is deferred due to the target customer having enabled
      [Multi-party approval for sensitive
      actions](https://support.google.com/a/answer/13790448).
  """

  state = _messages.StringField(1)


class UpdateInboundSsoAssignmentOperationMetadata(_messages.Message):
  r"""LRO response metadata for
  InboundSsoAssignmentsService.UpdateInboundSsoAssignment.
  """



class UpdateMembershipRolesParams(_messages.Message):
  r"""The details of an update to a `MembershipRole`.

  Fields:
    fieldMask: The fully-qualified names of fields to update. May only contain
      the field `expiry_detail.expire_time`.
    membershipRole: The `MembershipRole`s to be updated. Only `MEMBER`
      `MembershipRoles` can currently be updated. May only contain a
      `MembershipRole` with `name` `MEMBER`.
  """

  fieldMask = _messages.StringField(1)
  membershipRole = _messages.MessageField('MembershipRole', 2)


class UserInvitation(_messages.Message):
  r"""The `UserInvitation` resource represents an email that can be sent to an
  unmanaged user account inviting them to join the customer's Google Workspace
  or Cloud Identity account. An unmanaged account shares an email address
  domain with the Google Workspace or Cloud Identity account but is not
  managed by it yet. If the user accepts the `UserInvitation`, the user
  account will become managed.

  Enums:
    StateValueValuesEnum: State of the `UserInvitation`.

  Fields:
    mailsSentCount: Number of invitation emails sent to the user.
    name: Shall be of the form
      `customers/{customer}/userinvitations/{user_email_address}`.
    state: State of the `UserInvitation`.
    updateTime: Time when the `UserInvitation` was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""State of the `UserInvitation`.

    Values:
      STATE_UNSPECIFIED: The default value. This value is used if the state is
        omitted.
      NOT_YET_SENT: The `UserInvitation` has been created and is ready for
        sending as an email.
      INVITED: The user has been invited by email.
      ACCEPTED: The user has accepted the invitation and is part of the
        organization.
      DECLINED: The user declined the invitation.
    """
    STATE_UNSPECIFIED = 0
    NOT_YET_SENT = 1
    INVITED = 2
    ACCEPTED = 3
    DECLINED = 4

  mailsSentCount = _messages.IntegerField(1)
  name = _messages.StringField(2)
  state = _messages.EnumField('StateValueValuesEnum', 3)
  updateTime = _messages.StringField(4)


class WipeDeviceRequest(_messages.Message):
  r"""Request message for wiping all data on the device.

  Fields:
    customer: Optional. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      customer. If you're using this API for your own organization, use
      `customers/my_customer` If you're using this API to manage another
      organization, use `customers/{customer_id}`, where customer_id is the
      customer to whom the device belongs.
    removeResetLock: Optional. Specifies if a user is able to factory reset a
      device after a Device Wipe. On iOS, this is called "Activation Lock",
      while on Android, this is known as "Factory Reset Protection". If true,
      this protection will be removed from the device, so that a user can
      successfully factory reset. If false, the setting is untouched on the
      device.
  """

  customer = _messages.StringField(1)
  removeResetLock = _messages.BooleanField(2)


class WipeDeviceResponse(_messages.Message):
  r"""Response message for wiping all data on the device.

  Fields:
    device: Resultant Device object for the action. Note that asset tags will
      not be returned in the device object.
  """

  device = _messages.MessageField('Device', 1)


class WipeDeviceUserRequest(_messages.Message):
  r"""Request message for starting an account wipe on device.

  Fields:
    customer: Optional. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      customer. If you're using this API for your own organization, use
      `customers/my_customer` If you're using this API to manage another
      organization, use `customers/{customer_id}`, where customer_id is the
      customer to whom the device belongs.
  """

  customer = _messages.StringField(1)


class WipeDeviceUserResponse(_messages.Message):
  r"""Response message for wiping the user's account from the device.

  Fields:
    deviceUser: Resultant DeviceUser object for the action.
  """

  deviceUser = _messages.MessageField('DeviceUser', 1)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    CloudidentityGroupsLookupRequest, 'groupKey_id', 'groupKey.id')
encoding.AddCustomJsonFieldMapping(
    CloudidentityGroupsLookupRequest, 'groupKey_namespace', 'groupKey.namespace')
encoding.AddCustomJsonFieldMapping(
    CloudidentityGroupsMembershipsLookupRequest, 'memberKey_id', 'memberKey.id')
encoding.AddCustomJsonFieldMapping(
    CloudidentityGroupsMembershipsLookupRequest, 'memberKey_namespace', 'memberKey.namespace')
