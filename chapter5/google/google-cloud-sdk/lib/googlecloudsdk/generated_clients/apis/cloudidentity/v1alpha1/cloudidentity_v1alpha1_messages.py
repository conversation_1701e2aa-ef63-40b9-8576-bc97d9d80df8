"""Generated message classes for cloudidentity version v1alpha1.

API for provisioning and managing identity resources.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'cloudidentity'


class AndroidAttributes(_messages.Message):
  r"""Resource representing the Android specific attributes of a Device.

  Enums:
    OwnershipPrivilegeValueValuesEnum: Ownership privileges on device.

  Fields:
    ctsProfileMatch: Whether the device passes Android CTS compliance.
    enabledUnknownSources: Whether applications from unknown sources can be
      installed on device.
    hasPotentiallyHarmfulApps: Whether any potentially harmful apps were
      detected on the device.
    ownerProfileAccount: Whether this account is on an owner/primary profile.
      For phones, only true for owner profiles. Android 4+ devices can have
      secondary or restricted user profiles.
    ownershipPrivilege: Ownership privileges on device.
    supportsWorkProfile: Whether device supports Android work profiles. If
      false, this service will not block access to corp data even if an
      administrator turns on the "Enforce Work Profile" policy.
    verifiedBoot: Whether Android verified boot status is GREEN.
    verifyAppsEnabled: Whether Google Play Protect Verify Apps is enabled.
  """

  class OwnershipPrivilegeValueValuesEnum(_messages.Enum):
    r"""Ownership privileges on device.

    Values:
      OWNERSHIP_PRIVILEGE_UNSPECIFIED: Ownership privilege is not set.
      DEVICE_ADMINISTRATOR: Active device administrator privileges on the
        device.
      PROFILE_OWNER: Profile Owner privileges. The account is in a managed
        corporate profile.
      DEVICE_OWNER: Device Owner privileges on the device.
    """
    OWNERSHIP_PRIVILEGE_UNSPECIFIED = 0
    DEVICE_ADMINISTRATOR = 1
    PROFILE_OWNER = 2
    DEVICE_OWNER = 3

  ctsProfileMatch = _messages.BooleanField(1)
  enabledUnknownSources = _messages.BooleanField(2)
  hasPotentiallyHarmfulApps = _messages.BooleanField(3)
  ownerProfileAccount = _messages.BooleanField(4)
  ownershipPrivilege = _messages.EnumField('OwnershipPrivilegeValueValuesEnum', 5)
  supportsWorkProfile = _messages.BooleanField(6)
  verifiedBoot = _messages.BooleanField(7)
  verifyAppsEnabled = _messages.BooleanField(8)


class ApproveDeviceUserResponse(_messages.Message):
  r"""Response message for approving the device to access user data.

  Fields:
    deviceUser: Resultant DeviceUser object for the action.
  """

  deviceUser = _messages.MessageField('DeviceUser', 1)


class BlockDeviceUserResponse(_messages.Message):
  r"""Response message for blocking the device from accessing user data.

  Fields:
    deviceUser: Resultant DeviceUser object for the action.
  """

  deviceUser = _messages.MessageField('DeviceUser', 1)


class BrowserAttributes(_messages.Message):
  r"""Contains information about Browser Profiles reported by Endpoint
  Verification Extension

  Fields:
    chromeBrowserInfo: Represents the current state of the chrome webprotect
      attributes sent by the Endpoint verification extension.
    chromeProfileId: Chrome profile id exposed by chrome api and it will be
      unique within a device.
    lastProfileSyncTime: Timestamp in milliseconds since Epoch when the
      profile/gcm id was last synced.
  """

  chromeBrowserInfo = _messages.MessageField('BrowserInfo', 1)
  chromeProfileId = _messages.StringField(2)
  lastProfileSyncTime = _messages.StringField(3)


class BrowserInfo(_messages.Message):
  r"""Browser specific fields reported by Endpoint verification Extension. See
  go/additionalattributesdesign and go/caa-webprotect-attributes-extension

  Enums:
    BrowserManagementStateValueValuesEnum: Output only. Browser's management
      state.
    PasswordProtectionWarningTriggerValueValuesEnum: Current state of password
      protection trigger.
    SafeBrowsingProtectionLevelValueValuesEnum: Current state of safe browsing
      protection level.

  Fields:
    browserManagementState: Output only. Browser's management state.
    browserVersion: Version of the request initiating browser. E.g.
      `91.0.4442.4`.
    isBuiltInDnsClientEnabled: Current state of built in dns client.
    isBulkDataEntryAnalysisEnabled: Current state of bulk data analysis. Set
      to true if provider list from chrome is non-empty.
    isChromeCleanupEnabled: Current state of chrome cleanup.
    isChromeRemoteDesktopAppBlocked: Current state of chrome remote desktop
      app.
    isFileDownloadAnalysisEnabled: Current state of file download analysis.
      Set to true if provider list from chrome is non-empty.
    isFileUploadAnalysisEnabled: Current state of file upload analysis. Set to
      true if provider list from chrome is non-empty.
    isRealtimeUrlCheckEnabled: Current state of realtime url check. Set to
      true if provider list from chrome is non-empty.
    isSecurityEventAnalysisEnabled: Current state of security event analysis.
      Set to true if provider list from chrome is non-empty.
    isSiteIsolationEnabled: Current state of site isolation.
    isThirdPartyBlockingEnabled: Current state of third party blocking.
    passwordProtectionWarningTrigger: Current state of password protection
      trigger.
    safeBrowsingProtectionLevel: Current state of safe browsing protection
      level.
  """

  class BrowserManagementStateValueValuesEnum(_messages.Enum):
    r"""Output only. Browser's management state.

    Values:
      UNSPECIFIED: Management state is not specified.
      UNMANAGED: Browser/Profile not managed by any customer.
      MANAGED_BY_OTHER_DOMAIN: Browser/Profile managed, but by some other
        customer.
      PROFILE_MANAGED: Profile managed by customer.
      BROWSER_MANAGED: Browser managed by customer.
    """
    UNSPECIFIED = 0
    UNMANAGED = 1
    MANAGED_BY_OTHER_DOMAIN = 2
    PROFILE_MANAGED = 3
    BROWSER_MANAGED = 4

  class PasswordProtectionWarningTriggerValueValuesEnum(_messages.Enum):
    r"""Current state of password protection trigger.

    Values:
      PASSWORD_PROTECTION_TRIGGER_UNSPECIFIED: Password protection is not
        specified.
      PROTECTION_OFF: Password reuse is never detected.
      PASSWORD_REUSE: Warning will be shown when the user reuses their
        protected password on a non-allowed site.
      PHISHING_REUSE: Warning will be shown when the user reuses their
        protected password on a phishing site.
    """
    PASSWORD_PROTECTION_TRIGGER_UNSPECIFIED = 0
    PROTECTION_OFF = 1
    PASSWORD_REUSE = 2
    PHISHING_REUSE = 3

  class SafeBrowsingProtectionLevelValueValuesEnum(_messages.Enum):
    r"""Current state of safe browsing protection level.

    Values:
      SAFE_BROWSING_LEVEL_UNSPECIFIED: Browser protection level is not
        specified.
      DISABLED: No protection against dangerous websites, downloads, and
        extensions.
      STANDARD: Standard protection against websites, downloads, and
        extensions that are known to be dangerous.
      ENHANCED: Faster, proactive protection against dangerous websites,
        downloads, and extensions.
    """
    SAFE_BROWSING_LEVEL_UNSPECIFIED = 0
    DISABLED = 1
    STANDARD = 2
    ENHANCED = 3

  browserManagementState = _messages.EnumField('BrowserManagementStateValueValuesEnum', 1)
  browserVersion = _messages.StringField(2)
  isBuiltInDnsClientEnabled = _messages.BooleanField(3)
  isBulkDataEntryAnalysisEnabled = _messages.BooleanField(4)
  isChromeCleanupEnabled = _messages.BooleanField(5)
  isChromeRemoteDesktopAppBlocked = _messages.BooleanField(6)
  isFileDownloadAnalysisEnabled = _messages.BooleanField(7)
  isFileUploadAnalysisEnabled = _messages.BooleanField(8)
  isRealtimeUrlCheckEnabled = _messages.BooleanField(9)
  isSecurityEventAnalysisEnabled = _messages.BooleanField(10)
  isSiteIsolationEnabled = _messages.BooleanField(11)
  isThirdPartyBlockingEnabled = _messages.BooleanField(12)
  passwordProtectionWarningTrigger = _messages.EnumField('PasswordProtectionWarningTriggerValueValuesEnum', 13)
  safeBrowsingProtectionLevel = _messages.EnumField('SafeBrowsingProtectionLevelValueValuesEnum', 14)


class CancelWipeDeviceResponse(_messages.Message):
  r"""Response message for cancelling an unfinished device wipe.

  Fields:
    device: Resultant Device object for the action. Note that asset tags will
      not be returned in the device object.
  """

  device = _messages.MessageField('Device', 1)


class CancelWipeDeviceUserResponse(_messages.Message):
  r"""Response message for cancelling an unfinished user account wipe.

  Fields:
    deviceUser: Resultant DeviceUser object for the action.
  """

  deviceUser = _messages.MessageField('DeviceUser', 1)


class CertificateAttributes(_messages.Message):
  r"""Stores information about a certificate.

  Enums:
    ValidationStateValueValuesEnum: Validation state of this certificate.

  Fields:
    certificateTemplate: The X.509 extension for CertificateTemplate.
    fingerprint: The encoded certificate fingerprint.
    issuer: The name of the issuer of this certificate.
    serialNumber: Serial number of the certificate, Example: "*********".
    subject: The subject name of this certificate.
    thumbprint: The certificate thumbprint.
    validationState: Validation state of this certificate.
    validityExpirationTime: Certificate not valid after at and after this
      timestamp.
    validityStartTime: Certificate not valid before this timestamp.
  """

  class ValidationStateValueValuesEnum(_messages.Enum):
    r"""Validation state of this certificate.

    Values:
      CERTIFICATE_VALIDATION_STATE_UNSPECIFIED: Default value.
      VALIDATION_SUCCESSFUL: Certificate validation was successful.
      VALIDATION_FAILED: Certificate validation failed.
    """
    CERTIFICATE_VALIDATION_STATE_UNSPECIFIED = 0
    VALIDATION_SUCCESSFUL = 1
    VALIDATION_FAILED = 2

  certificateTemplate = _messages.MessageField('CertificateTemplate', 1)
  fingerprint = _messages.StringField(2)
  issuer = _messages.StringField(3)
  serialNumber = _messages.StringField(4)
  subject = _messages.StringField(5)
  thumbprint = _messages.StringField(6)
  validationState = _messages.EnumField('ValidationStateValueValuesEnum', 7)
  validityExpirationTime = _messages.StringField(8)
  validityStartTime = _messages.StringField(9)


class CertificateTemplate(_messages.Message):
  r"""CertificateTemplate (v3 Extension in X.509). See
  https://docs.microsoft.com/en-us/windows/win32/api/certenroll/nn-certenroll-
  ix509extensiontemplate.

  Fields:
    id: The template id of the template. Example: "*******.4.1.311.21.8.156086
      21.11768144.5720724.16068415.6889630.81.2472537.7784047".
    majorVersion: The Major version of the template. Example: 100.
    minorVersion: The minor version of the template. Example: 12.
  """

  id = _messages.StringField(1)
  majorVersion = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  minorVersion = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class CheckTransitiveMembershipResponse(_messages.Message):
  r"""The response message for MembershipsService.CheckTransitiveMembership.

  Fields:
    hasMembership: Response does not include the possible roles of a member
      since the behavior of this rpc is not all-or-nothing unlike the other
      rpcs. So, it may not be possible to list all the roles definitively, due
      to possible lack of authorization in some of the paths.
  """

  hasMembership = _messages.BooleanField(1)


class ClientState(_messages.Message):
  r"""Resource representing ClientState and supports updates from API users

  Enums:
    ComplianceStateValueValuesEnum: The compliance state of the resource as
      specified by the API client.
    HealthScoreValueValuesEnum: The Health score of the resource
    ManagedValueValuesEnum: The management state of the resource as specified
      by the API client.
    OwnerTypeValueValuesEnum: Output only. The owner of the ClientState

  Messages:
    KeyValuePairsValue: The map of key-value attributes stored by callers
      specific to a device. The total serialized length of this map may not
      exceed 10KB. No limit is placed on the number of attributes in a map.

  Fields:
    assetTags: The caller can specify asset tags for this resource
    complianceState: The compliance state of the resource as specified by the
      API client.
    createTime: Output only. The time the client state data was created.
    customId: This field may be used to store a unique identifier for the API
      resource within which these CustomAttributes are a field.
    etag: The token that needs to be passed back for concurrency control in
      updates. Token needs to be passed back in UpdateRequest
    healthScore: The Health score of the resource
    keyValuePairs: The map of key-value attributes stored by callers specific
      to a device. The total serialized length of this map may not exceed
      10KB. No limit is placed on the number of attributes in a map.
    lastUpdateTime: Output only. The time the client state data was last
      updated.
    managed: The management state of the resource as specified by the API
      client.
    name: Output only. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      ClientState in format: `devices/{device_id}/deviceUsers/{device_user_id}
      /clientState/{partner_id}`, where partner_id corresponds to the partner
      storing the data.
    ownerType: Output only. The owner of the ClientState
    scoreReason: A descriptive cause of the health score.
  """

  class ComplianceStateValueValuesEnum(_messages.Enum):
    r"""The compliance state of the resource as specified by the API client.

    Values:
      COMPLIANCE_STATE_UNSPECIFIED: The compliance state of the resource is
        unknown or unspecified.
      COMPLIANT: Device is compliant with third party policies
      NON_COMPLIANT: Device is not compliant with third party policies
    """
    COMPLIANCE_STATE_UNSPECIFIED = 0
    COMPLIANT = 1
    NON_COMPLIANT = 2

  class HealthScoreValueValuesEnum(_messages.Enum):
    r"""The Health score of the resource

    Values:
      HEALTH_SCORE_UNSPECIFIED: Default value
      VERY_POOR: The object is in very poor health as defined by the caller.
      POOR: The object is in poor health as defined by the caller.
      NEUTRAL: The object health is neither good nor poor, as defined by the
        caller.
      GOOD: The object is in good health as defined by the caller.
      VERY_GOOD: The object is in very good health as defined by the caller.
    """
    HEALTH_SCORE_UNSPECIFIED = 0
    VERY_POOR = 1
    POOR = 2
    NEUTRAL = 3
    GOOD = 4
    VERY_GOOD = 5

  class ManagedValueValuesEnum(_messages.Enum):
    r"""The management state of the resource as specified by the API client.

    Values:
      MANAGED_STATE_UNSPECIFIED: The management state of the resource is
        unknown or unspecified.
      MANAGED: The resource is managed.
      UNMANAGED: The resource is not managed.
    """
    MANAGED_STATE_UNSPECIFIED = 0
    MANAGED = 1
    UNMANAGED = 2

  class OwnerTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The owner of the ClientState

    Values:
      OWNER_TYPE_UNSPECIFIED: Unknown owner type
      OWNER_TYPE_CUSTOMER: Customer is the owner
      OWNER_TYPE_PARTNER: Partner is the owner
    """
    OWNER_TYPE_UNSPECIFIED = 0
    OWNER_TYPE_CUSTOMER = 1
    OWNER_TYPE_PARTNER = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class KeyValuePairsValue(_messages.Message):
    r"""The map of key-value attributes stored by callers specific to a
    device. The total serialized length of this map may not exceed 10KB. No
    limit is placed on the number of attributes in a map.

    Messages:
      AdditionalProperty: An additional property for a KeyValuePairsValue
        object.

    Fields:
      additionalProperties: Additional properties of type KeyValuePairsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a KeyValuePairsValue object.

      Fields:
        key: Name of the additional property.
        value: A CustomAttributeValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('CustomAttributeValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  assetTags = _messages.StringField(1, repeated=True)
  complianceState = _messages.EnumField('ComplianceStateValueValuesEnum', 2)
  createTime = _messages.StringField(3)
  customId = _messages.StringField(4)
  etag = _messages.StringField(5)
  healthScore = _messages.EnumField('HealthScoreValueValuesEnum', 6)
  keyValuePairs = _messages.MessageField('KeyValuePairsValue', 7)
  lastUpdateTime = _messages.StringField(8)
  managed = _messages.EnumField('ManagedValueValuesEnum', 9)
  name = _messages.StringField(10)
  ownerType = _messages.EnumField('OwnerTypeValueValuesEnum', 11)
  scoreReason = _messages.StringField(12)


class CloudidentityGroupsCreateRequest(_messages.Message):
  r"""A CloudidentityGroupsCreateRequest object.

  Enums:
    InitialGroupConfigValueValuesEnum: Required. The initial configuration
      option for the `Group`.

  Fields:
    group: A Group resource to be passed as the request body.
    initialGroupConfig: Required. The initial configuration option for the
      `Group`.
  """

  class InitialGroupConfigValueValuesEnum(_messages.Enum):
    r"""Required. The initial configuration option for the `Group`.

    Values:
      INITIAL_GROUP_CONFIG_UNSPECIFIED: Default. Should not be used.
      WITH_INITIAL_OWNER: The end user making the request will be added as the
        initial owner of the `Group`.
      EMPTY: An empty group is created without any initial owners. This can
        only be used by admins of the domain.
    """
    INITIAL_GROUP_CONFIG_UNSPECIFIED = 0
    WITH_INITIAL_OWNER = 1
    EMPTY = 2

  group = _messages.MessageField('Group', 1)
  initialGroupConfig = _messages.EnumField('InitialGroupConfigValueValuesEnum', 2)


class CloudidentityGroupsDeleteRequest(_messages.Message):
  r"""A CloudidentityGroupsDeleteRequest object.

  Fields:
    name: Required. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      `Group` to retrieve. Must be of the form `groups/{group_id}`.
  """

  name = _messages.StringField(1, required=True)


class CloudidentityGroupsGetRequest(_messages.Message):
  r"""A CloudidentityGroupsGetRequest object.

  Fields:
    name: Required. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      `Group` to retrieve. Must be of the form `groups/{group_id}`.
  """

  name = _messages.StringField(1, required=True)


class CloudidentityGroupsListRequest(_messages.Message):
  r"""A CloudidentityGroupsListRequest object.

  Enums:
    ViewValueValuesEnum: The level of detail to be returned. If unspecified,
      defaults to `View.BASIC`.

  Fields:
    pageSize: The maximum number of results to return. Note that the number of
      results returned may be less than this value even if there are more
      available results. To fetch all results, clients must continue calling
      this method repeatedly until the response no longer contains a
      `next_page_token`. If unspecified, defaults to 200 for `View.BASIC` and
      to 50 for `View.FULL`. Must not be greater than 1000 for `View.BASIC` or
      500 for `View.FULL`.
    pageToken: The `next_page_token` value returned from a previous list
      request, if any.
    parent: Required. The parent resource under which to list all `Group`
      resources. Must be of the form `identitysources/{identity_source_id}`
      for external- identity-mapped groups or `customers/{customer_id}` for
      Google Groups. The `customer_id` must begin with "C" (for example,
      'C046psxkn'). [Find your customer ID.]
      (https://support.google.com/cloudidentity/answer/10070793)
    view: The level of detail to be returned. If unspecified, defaults to
      `View.BASIC`.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""The level of detail to be returned. If unspecified, defaults to
    `View.BASIC`.

    Values:
      VIEW_UNSPECIFIED: Default. Should not be used.
      BASIC: Only basic resource information is returned.
      FULL: All resource information is returned.
    """
    VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3)
  view = _messages.EnumField('ViewValueValuesEnum', 4)


class CloudidentityGroupsLookupRequest(_messages.Message):
  r"""A CloudidentityGroupsLookupRequest object.

  Fields:
    groupKey_id: Required. The ID of the entity. For Google-managed entities,
      the `id` must be the email address of an existing group or user. For
      external-identity-mapped entities, the `id` must be a string conforming
      to the Identity Source's requirements. Must be unique within a
      `namespace`.
    groupKey_namespace: The namespace in which the entity exists. If not
      specified, the `EntityKey` represents a Google-managed entity such as a
      Google user or a Google Group. If specified, the `EntityKey` represents
      an external-identity-mapped group. The namespace must correspond to an
      identity source created in Admin Console and must be in the form of
      `identitysources/{identity_source_id}`.
  """

  groupKey_id = _messages.StringField(1)
  groupKey_namespace = _messages.StringField(2)


class CloudidentityGroupsMembershipsCheckTransitiveMembershipRequest(_messages.Message):
  r"""A CloudidentityGroupsMembershipsCheckTransitiveMembershipRequest object.

  Fields:
    parent: [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the group
      to check the transitive membership in. Format: `groups/{group_id}`,
      where `group_id` is the unique id assigned to the Group to which the
      Membership belongs to.
    query: Required. A CEL expression that MUST include member specification.
      This is a `required` field. Example query: member_key_id ==
      'member_key_id_value' [ && member_key_namespace ==
      'member_key_namespace_value' ]
  """

  parent = _messages.StringField(1, required=True)
  query = _messages.StringField(2)


class CloudidentityGroupsMembershipsCreateRequest(_messages.Message):
  r"""A CloudidentityGroupsMembershipsCreateRequest object.

  Fields:
    membership: A Membership resource to be passed as the request body.
    parent: Required. The parent `Group` resource under which to create the
      `Membership`. Must be of the form `groups/{group_id}`.
  """

  membership = _messages.MessageField('Membership', 1)
  parent = _messages.StringField(2, required=True)


class CloudidentityGroupsMembershipsDeleteRequest(_messages.Message):
  r"""A CloudidentityGroupsMembershipsDeleteRequest object.

  Fields:
    name: Required. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      `Membership` to delete. Must be of the form
      `groups/{group_id}/memberships/{membership_id}`.
  """

  name = _messages.StringField(1, required=True)


class CloudidentityGroupsMembershipsGetMembershipGraphRequest(_messages.Message):
  r"""A CloudidentityGroupsMembershipsGetMembershipGraphRequest object.

  Fields:
    parent: Required. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the group
      to search transitive memberships in. Format: `groups/{group_id}`, where
      `group_id` is the unique ID assigned to the Group to which the
      Membership belongs to. `group_id` can be a wildcard collection id "-".
      When a `group_id` is specified, the membership graph will be constrained
      to paths between the member (defined in the query) and the parent. If a
      wildcard collection is provided, all membership paths connected to the
      member will be returned.
    query: Required. A CEL expression that MUST include member specification
      AND label(s). Example query: member_key_id == 'member_key_id_value' [ &&
      member_key_namespace == 'member_key_namespace_value' ] && in labels
  """

  parent = _messages.StringField(1, required=True)
  query = _messages.StringField(2)


class CloudidentityGroupsMembershipsGetRequest(_messages.Message):
  r"""A CloudidentityGroupsMembershipsGetRequest object.

  Fields:
    name: Required. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      `Membership` to retrieve. Must be of the form
      `groups/{group_id}/memberships/{membership_id}`.
  """

  name = _messages.StringField(1, required=True)


class CloudidentityGroupsMembershipsListRequest(_messages.Message):
  r"""A CloudidentityGroupsMembershipsListRequest object.

  Enums:
    ViewValueValuesEnum: The level of detail to be returned. If unspecified,
      defaults to `View.BASIC`.

  Fields:
    pageSize: The maximum number of results to return. Note that the number of
      results returned may be less than this value even if there are more
      available results. To fetch all results, clients must continue calling
      this method repeatedly until the response no longer contains a
      `next_page_token`. If unspecified, defaults to 200 for `View.BASIC` and
      to 50 for `View.FULL`. Must not be greater than 1000 for `View.BASIC` or
      500 for `View.FULL`.
    pageToken: The `next_page_token` value returned from a previous list
      request, if any.
    parent: Required. The parent `Group` resource under which to lookup the
      `Membership` name. Must be of the form `groups/{group_id}`.
    view: The level of detail to be returned. If unspecified, defaults to
      `View.BASIC`.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""The level of detail to be returned. If unspecified, defaults to
    `View.BASIC`.

    Values:
      VIEW_UNSPECIFIED: Default. Should not be used.
      BASIC: Only basic resource information is returned.
      FULL: All resource information is returned.
    """
    VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 4)


class CloudidentityGroupsMembershipsLookupRequest(_messages.Message):
  r"""A CloudidentityGroupsMembershipsLookupRequest object.

  Fields:
    memberKey_id: Required. The ID of the entity. For Google-managed entities,
      the `id` must be the email address of an existing group or user. For
      external-identity-mapped entities, the `id` must be a string conforming
      to the Identity Source's requirements. Must be unique within a
      `namespace`.
    memberKey_namespace: The namespace in which the entity exists. If not
      specified, the `EntityKey` represents a Google-managed entity such as a
      Google user or a Google Group. If specified, the `EntityKey` represents
      an external-identity-mapped group. The namespace must correspond to an
      identity source created in Admin Console and must be in the form of
      `identitysources/{identity_source_id}`.
    parent: Required. The parent `Group` resource under which to lookup the
      `Membership` name. Must be of the form `groups/{group_id}`.
  """

  memberKey_id = _messages.StringField(1)
  memberKey_namespace = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CloudidentityGroupsMembershipsModifyMembershipRolesRequest(_messages.Message):
  r"""A CloudidentityGroupsMembershipsModifyMembershipRolesRequest object.

  Fields:
    modifyMembershipRolesRequest: A ModifyMembershipRolesRequest resource to
      be passed as the request body.
    name: Required. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      `Membership` whose roles are to be modified. Must be of the form
      `groups/{group_id}/memberships/{membership_id}`.
  """

  modifyMembershipRolesRequest = _messages.MessageField('ModifyMembershipRolesRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudidentityGroupsMembershipsPatchRequest(_messages.Message):
  r"""A CloudidentityGroupsMembershipsPatchRequest object.

  Fields:
    membership: A Membership resource to be passed as the request body.
    name: Output only. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      `Membership`. Shall be of the form
      `groups/{group_id}/memberships/{membership_id}`.
    updateMask: Required. The fully-qualified names of fields to update.
  """

  membership = _messages.MessageField('Membership', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class CloudidentityGroupsMembershipsSearchDirectGroupsRequest(_messages.Message):
  r"""A CloudidentityGroupsMembershipsSearchDirectGroupsRequest object.

  Fields:
    orderBy: The ordering of membership relation for the display name or email
      in the response. The syntax for this field can be found at
      https://cloud.google.com/apis/design/design_patterns#sorting_order.
      Example: Sort by the ascending display name: order_by="group_name" or
      order_by="group_name asc". Sort by the descending display name:
      order_by="group_name desc". Sort by the ascending group key:
      order_by="group_key" or order_by="group_key asc". Sort by the descending
      group key: order_by="group_key desc".
    pageSize: The default page size is 200 (max 1000).
    pageToken: The `next_page_token` value returned from a previous list
      request, if any
    parent: [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the group
      to search transitive memberships in. Format: groups/{group_id}, where
      group_id is always '-' as this API will search across all groups for a
      given member.
    query: Required. A CEL expression that MUST include member specification
      AND label(s). Users can search on label attributes of groups. CONTAINS
      match ('in') is supported on labels. Identity-mapped groups are uniquely
      identified by both a `member_key_id` and a `member_key_namespace`, which
      requires an additional query input: `member_key_namespace`. Example
      query: `member_key_id == 'member_key_id_value' && 'label_value' in
      labels`
  """

  orderBy = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  query = _messages.StringField(5)


class CloudidentityGroupsMembershipsSearchTransitiveGroupsRequest(_messages.Message):
  r"""A CloudidentityGroupsMembershipsSearchTransitiveGroupsRequest object.

  Fields:
    pageSize: The default page size is 200 (max 1000).
    pageToken: The `next_page_token` value returned from a previous list
      request, if any.
    parent: [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the group
      to search transitive memberships in. Format: `groups/{group_id}`, where
      `group_id` is always '-' as this API will search across all groups for a
      given member.
    query: Required. A CEL expression that MUST include member specification
      AND label(s). This is a `required` field. Users can search on label
      attributes of groups. CONTAINS match ('in') is supported on labels.
      Identity-mapped groups are uniquely identified by both a `member_key_id`
      and a `member_key_namespace`, which requires an additional query input:
      `member_key_namespace`. Example query: member_key_id ==
      'member_key_id_value' [ && member_key_namespace ==
      'member_key_namespace_value' ] && in labels Query may optionally contain
      equality operators on the parent of the group restricting the search
      within a particular customer, e.g. `parent ==
      'customers/{customer_id}'`. The `customer_id` must begin with "C" (for
      example, 'C046psxkn'). This filtering is only supported for Admins with
      groups read permissions on the input customer. Example query:
      `member_key_id == 'member_key_id_value' && in labels && parent ==
      'customers/C046psxkn'`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  query = _messages.StringField(4)


class CloudidentityGroupsMembershipsSearchTransitiveMembershipsRequest(_messages.Message):
  r"""A CloudidentityGroupsMembershipsSearchTransitiveMembershipsRequest
  object.

  Fields:
    pageSize: The default page size is 200 (max 1000).
    pageToken: The `next_page_token` value returned from a previous list
      request, if any.
    parent: [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the group
      to search transitive memberships in. Format: `groups/{group_id}`, where
      `group_id` is the unique ID assigned to the Group.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CloudidentityGroupsPatchRequest(_messages.Message):
  r"""A CloudidentityGroupsPatchRequest object.

  Fields:
    group: A Group resource to be passed as the request body.
    name: Output only. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      `Group`. Shall be of the form `groups/{group_id}`.
    updateMask: Required. The names of fields to update. May only contain the
      following field names: `display_name`, `description`, `labels`,
      `dynamic_group_metadata`, `posix_groups`.
  """

  group = _messages.MessageField('Group', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class CloudidentityGroupsSearchRequest(_messages.Message):
  r"""A CloudidentityGroupsSearchRequest object.

  Enums:
    ViewValueValuesEnum: The level of detail to be returned. If unspecified,
      defaults to `View.BASIC`.

  Fields:
    orderBy: The ordering of groups for the display name or email in the
      search groups response. The syntax for this field can be found at
      https://cloud.google.com/apis/design/design_patterns#sorting_order.
      Example: Sort by the ascending name: order_by="display_name" Sort by the
      descending group key email: order_by="group_key desc"
    pageSize: The maximum number of results to return. Note that the number of
      results returned may be less than this value even if there are more
      available results. To fetch all results, clients must continue calling
      this method repeatedly until the response no longer contains a
      `next_page_token`. If unspecified, defaults to 200 for `View.BASIC` and
      to 50 for `View.FULL`. Must not be greater than 1000 for `View.BASIC` or
      500 for `View.FULL`.
    pageToken: The `next_page_token` value returned from a previous search
      request, if any.
    query: Required. The search query. * Must be specified in [Common
      Expression Language](https://opensource.google/projects/cel). * Must
      contain equality operators on the parent, e.g. `parent ==
      'customers/{customer_id}'`. The `customer_id` must begin with "C" (for
      example, 'C046psxkn'). [Find your customer ID.]
      (https://support.google.com/cloudidentity/answer/10070793) * Can contain
      optional inclusion operators on `labels` such as
      `'cloudidentity.googleapis.com/groups.discussion_forum' in labels`). *
      Can contain an optional equality operator on `domain_name`. e.g.
      `domain_name == 'examplepetstore.com'` * Can contain optional
      `startsWith/contains/equality` operators on `group_key`, e.g.
      `group_key.startsWith('dev')`, `group_key.contains('dev'), group_key ==
      '<EMAIL>'` * Can contain optional
      `startsWith/contains/equality` operators on `display_name`, such as
      `display_name.startsWith('dev')` , `display_name.contains('dev')`,
      `display_name == 'dev'`
    view: The level of detail to be returned. If unspecified, defaults to
      `View.BASIC`.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""The level of detail to be returned. If unspecified, defaults to
    `View.BASIC`.

    Values:
      VIEW_UNSPECIFIED: Default. Should not be used.
      BASIC: Only basic resource information is returned.
      FULL: All resource information is returned.
    """
    VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  orderBy = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  query = _messages.StringField(4)
  view = _messages.EnumField('ViewValueValuesEnum', 5)


class CustomAttributeValue(_messages.Message):
  r"""Additional custom attribute values may be one of these types

  Fields:
    boolValue: Represents a boolean value.
    numberValue: Represents a double value.
    stringValue: Represents a string value.
  """

  boolValue = _messages.BooleanField(1)
  numberValue = _messages.FloatField(2)
  stringValue = _messages.StringField(3)


class Device(_messages.Message):
  r"""Represents a Device known to Google Cloud, independent of the device
  ownership, type, and whether it is assigned or in use by a user.

  Enums:
    ClientTypesValueListEntryValuesEnum:
    CompromisedStateValueValuesEnum: Output only. Represents whether the
      Device is compromised.
    DeviceTypeValueValuesEnum: Output only. Type of device.
    EncryptionStateValueValuesEnum: Output only. Device encryption state.
    ManagementStateValueValuesEnum: Output only. Management state of the
      device
    OwnerTypeValueValuesEnum: Output only. Whether the device is owned by the
      company or an individual

  Fields:
    androidSpecificAttributes: Output only. Attributes specific to Android
      devices.
    assetTag: Asset tag of the device.
    basebandVersion: Output only. Baseband version of the device.
    bootloaderVersion: Output only. Device bootloader version. Example: 0.6.7.
    brand: Output only. Device brand. Example: Samsung.
    buildNumber: Output only. Build number of the device.
    clientTypes: List of the clients the device is reporting to.
    compromisedState: Output only. Represents whether the Device is
      compromised.
    createTime: Output only. When the Company-Owned device was imported. This
      field is empty for BYOD devices.
    deviceId: Unique identifier for the device.
    deviceType: Output only. Type of device.
    enabledDeveloperOptions: Output only. Whether developer options is enabled
      on device.
    enabledUsbDebugging: Output only. Whether USB debugging is enabled on
      device.
    encryptionState: Output only. Device encryption state.
    endpointVerificationSpecificAttributes: Output only. Attributes specific
      to Endpoint Verification (SecureConnect) devices.
    hostname: Host name of the device.
    imei: Output only. IMEI number of device if GSM device; empty otherwise.
    kernelVersion: Output only. Kernel version of the device.
    lastSyncTime: Most recent time when device synced with this service.
    managementState: Output only. Management state of the device
    manufacturer: Output only. Device manufacturer. Example: Motorola.
    meid: Output only. MEID number of device if CDMA device; empty otherwise.
    model: Output only. Model name of device. Example: Pixel 3.
    name: Output only. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the Device
      in format: `devices/{device_id}`, where device_id is the unique id
      assigned to the Device.
    networkOperator: Output only. Mobile or network operator of device, if
      available.
    osVersion: Output only. OS version of the device. Example: Android 8.1.0.
    otherAccounts: Output only. Domain name for Google accounts on device.
      Type for other accounts on device. On Android, will only be populated if
      |ownership_privilege| is |PROFILE_OWNER| or |DEVICE_OWNER|. Does not
      include the account signed in to the device policy app if that account's
      domain has only one account. Examples: "com.example", "xyz.com".
    ownerType: Output only. Whether the device is owned by the company or an
      individual
    releaseVersion: Output only. OS release version. Example: 6.0.
    securityPatchTime: Output only. OS security patch update time on device.
    serialNumber: Serial Number of device. Example: HT82V1A01076.
    unifiedDeviceId: Output only. Unified device id of the device.
    wifiMacAddresses: WiFi MAC addresses of device.
  """

  class ClientTypesValueListEntryValuesEnum(_messages.Enum):
    r"""ClientTypesValueListEntryValuesEnum enum type.

    Values:
      CLIENT_TYPE_UNSPECIFIED: Default value
      DRIVE_FS: Managed by DriveFS
      FUNDAMENTAL: Management type for every secure device
      ENDPOINT_VERIFICATION: Managed by Endpoint Verification
      WINDOWS_ADVANCED: Managed by Windows
      GOOGLE_CREDENTIALS_PROVIDER_FOR_WINDOWS: Managed by Google credential
        provider for windows
    """
    CLIENT_TYPE_UNSPECIFIED = 0
    DRIVE_FS = 1
    FUNDAMENTAL = 2
    ENDPOINT_VERIFICATION = 3
    WINDOWS_ADVANCED = 4
    GOOGLE_CREDENTIALS_PROVIDER_FOR_WINDOWS = 5

  class CompromisedStateValueValuesEnum(_messages.Enum):
    r"""Output only. Represents whether the Device is compromised.

    Values:
      COMPROMISED_STATE_UNSPECIFIED: Default value.
      COMPROMISED: The device is compromised (currently, this means Android
        device is rooted).
      UNCOMPROMISED: The device is safe (currently, this means Android device
        is unrooted).
    """
    COMPROMISED_STATE_UNSPECIFIED = 0
    COMPROMISED = 1
    UNCOMPROMISED = 2

  class DeviceTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of device.

    Values:
      DEVICE_TYPE_UNSPECIFIED: Unknown device type
      ANDROID: Device is an Android device
      IOS: Device is an iOS device
      GOOGLE_SYNC: Device is a Google Sync device.
      WINDOWS: Device is a Windows device.
      MAC_OS: Device is a MacOS device.
      LINUX: Device is a Linux device.
      CHROME_OS: Device is a ChromeOS device.
    """
    DEVICE_TYPE_UNSPECIFIED = 0
    ANDROID = 1
    IOS = 2
    GOOGLE_SYNC = 3
    WINDOWS = 4
    MAC_OS = 5
    LINUX = 6
    CHROME_OS = 7

  class EncryptionStateValueValuesEnum(_messages.Enum):
    r"""Output only. Device encryption state.

    Values:
      ENCRYPTION_STATE_UNSPECIFIED: Encryption Status is not set.
      UNSUPPORTED_BY_DEVICE: Device doesn't support encryption.
      ENCRYPTED: Device is encrypted.
      NOT_ENCRYPTED: Device is not encrypted.
    """
    ENCRYPTION_STATE_UNSPECIFIED = 0
    UNSUPPORTED_BY_DEVICE = 1
    ENCRYPTED = 2
    NOT_ENCRYPTED = 3

  class ManagementStateValueValuesEnum(_messages.Enum):
    r"""Output only. Management state of the device

    Values:
      MANAGEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      APPROVED: Device is approved.
      BLOCKED: Device is blocked.
      PENDING: Device is pending approval.
      UNPROVISIONED: The device is not provisioned. Device will start from
        this state until some action is taken (i.e. a user starts using the
        device).
      WIPING: Data and settings on the device are being removed.
      WIPED: All data and settings on the device are removed.
    """
    MANAGEMENT_STATE_UNSPECIFIED = 0
    APPROVED = 1
    BLOCKED = 2
    PENDING = 3
    UNPROVISIONED = 4
    WIPING = 5
    WIPED = 6

  class OwnerTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Whether the device is owned by the company or an
    individual

    Values:
      DEVICE_OWNERSHIP_UNSPECIFIED: Default value. The value is unused.
      COMPANY: Company owns the device.
      BYOD: Bring Your Own Device (i.e. individual owns the device)
    """
    DEVICE_OWNERSHIP_UNSPECIFIED = 0
    COMPANY = 1
    BYOD = 2

  androidSpecificAttributes = _messages.MessageField('AndroidAttributes', 1)
  assetTag = _messages.StringField(2)
  basebandVersion = _messages.StringField(3)
  bootloaderVersion = _messages.StringField(4)
  brand = _messages.StringField(5)
  buildNumber = _messages.StringField(6)
  clientTypes = _messages.EnumField('ClientTypesValueListEntryValuesEnum', 7, repeated=True)
  compromisedState = _messages.EnumField('CompromisedStateValueValuesEnum', 8)
  createTime = _messages.StringField(9)
  deviceId = _messages.StringField(10)
  deviceType = _messages.EnumField('DeviceTypeValueValuesEnum', 11)
  enabledDeveloperOptions = _messages.BooleanField(12)
  enabledUsbDebugging = _messages.BooleanField(13)
  encryptionState = _messages.EnumField('EncryptionStateValueValuesEnum', 14)
  endpointVerificationSpecificAttributes = _messages.MessageField('EndpointVerificationSpecificAttributes', 15)
  hostname = _messages.StringField(16)
  imei = _messages.StringField(17)
  kernelVersion = _messages.StringField(18)
  lastSyncTime = _messages.StringField(19)
  managementState = _messages.EnumField('ManagementStateValueValuesEnum', 20)
  manufacturer = _messages.StringField(21)
  meid = _messages.StringField(22)
  model = _messages.StringField(23)
  name = _messages.StringField(24)
  networkOperator = _messages.StringField(25)
  osVersion = _messages.StringField(26)
  otherAccounts = _messages.StringField(27, repeated=True)
  ownerType = _messages.EnumField('OwnerTypeValueValuesEnum', 28)
  releaseVersion = _messages.StringField(29)
  securityPatchTime = _messages.StringField(30)
  serialNumber = _messages.StringField(31)
  unifiedDeviceId = _messages.StringField(32)
  wifiMacAddresses = _messages.StringField(33, repeated=True)


class DeviceUser(_messages.Message):
  r"""A DeviceUser is a resource representing a user's use of a Device

  Enums:
    CompromisedStateValueValuesEnum: Compromised State of the DeviceUser
      object
    ManagementStateValueValuesEnum: Output only. Management state of the user
      on the device.
    PasswordStateValueValuesEnum: Password state of the DeviceUser object

  Fields:
    compromisedState: Compromised State of the DeviceUser object
    createTime: When the user first signed in to the device
    firstSyncTime: Output only. Most recent time when user registered with
      this service.
    languageCode: Output only. Default locale used on device, in IETF BCP-47
      format.
    lastSyncTime: Output only. Last time when user synced with policies.
    lostModeDeviceInfo: Lost mode device info, contains location and battery
      information.
    managementState: Output only. Management state of the user on the device.
    name: Output only. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      DeviceUser in format: `devices/{device_id}/deviceUsers/{user_id}`, where
      user_id is the ID of the user associated with the user session.
    passwordState: Password state of the DeviceUser object
    userAgent: Output only. User agent on the device for this specific user
    userEmail: Email address of the user registered on the device.
  """

  class CompromisedStateValueValuesEnum(_messages.Enum):
    r"""Compromised State of the DeviceUser object

    Values:
      COMPROMISED_STATE_UNSPECIFIED: Compromised state of Device User account
        is unknown or unspecified.
      COMPROMISED: Device User Account is compromised.
      NOT_COMPROMISED: Device User Account is not compromised.
    """
    COMPROMISED_STATE_UNSPECIFIED = 0
    COMPROMISED = 1
    NOT_COMPROMISED = 2

  class ManagementStateValueValuesEnum(_messages.Enum):
    r"""Output only. Management state of the user on the device.

    Values:
      MANAGEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      WIPING: This user's data and profile is being removed from the device.
      WIPED: This user's data and profile is removed from the device.
      APPROVED: User is approved to access data on the device.
      BLOCKED: User is blocked from accessing data on the device.
      PENDING_APPROVAL: User is awaiting approval.
      UNENROLLED: User is unenrolled from Advanced Windows Management, but the
        Windows account is still intact.
      LOST_MODE_PENDING: Lost mode is being activated on the device.
      LOST_MODE_ACTIVATED: Lost mode is active for user on the device.
    """
    MANAGEMENT_STATE_UNSPECIFIED = 0
    WIPING = 1
    WIPED = 2
    APPROVED = 3
    BLOCKED = 4
    PENDING_APPROVAL = 5
    UNENROLLED = 6
    LOST_MODE_PENDING = 7
    LOST_MODE_ACTIVATED = 8

  class PasswordStateValueValuesEnum(_messages.Enum):
    r"""Password state of the DeviceUser object

    Values:
      PASSWORD_STATE_UNSPECIFIED: Password state not set.
      PASSWORD_SET: Password set in object.
      PASSWORD_NOT_SET: Password not set in object.
    """
    PASSWORD_STATE_UNSPECIFIED = 0
    PASSWORD_SET = 1
    PASSWORD_NOT_SET = 2

  compromisedState = _messages.EnumField('CompromisedStateValueValuesEnum', 1)
  createTime = _messages.StringField(2)
  firstSyncTime = _messages.StringField(3)
  languageCode = _messages.StringField(4)
  lastSyncTime = _messages.StringField(5)
  lostModeDeviceInfo = _messages.MessageField('LostModeDeviceInfo', 6)
  managementState = _messages.EnumField('ManagementStateValueValuesEnum', 7)
  name = _messages.StringField(8)
  passwordState = _messages.EnumField('PasswordStateValueValuesEnum', 9)
  userAgent = _messages.StringField(10)
  userEmail = _messages.StringField(11)


class DynamicGroupMetadata(_messages.Message):
  r"""Dynamic group metadata like queries and status.

  Fields:
    queries: Only one entry is supported for now. Memberships will be the
      union of all queries. Customers can create up to 500 dynamic groups.
    status: Status of the dynamic group. Output only.
  """

  queries = _messages.MessageField('DynamicGroupQuery', 1, repeated=True)
  status = _messages.MessageField('DynamicGroupStatus', 2)


class DynamicGroupQuery(_messages.Message):
  r"""Defines a query on a resource.

  Enums:
    ResourceTypeValueValuesEnum:

  Fields:
    query: Query that determines the memberships of the dynamic group.
    resourceType: A ResourceTypeValueValuesEnum attribute.
  """

  class ResourceTypeValueValuesEnum(_messages.Enum):
    r"""ResourceTypeValueValuesEnum enum type.

    Values:
      RESOURCE_TYPE_UNSPECIFIED: Default value (not valid)
      USER: For queries on User
    """
    RESOURCE_TYPE_UNSPECIFIED = 0
    USER = 1

  query = _messages.StringField(1)
  resourceType = _messages.EnumField('ResourceTypeValueValuesEnum', 2)


class DynamicGroupStatus(_messages.Message):
  r"""The current status of a dynamic group along with timestamp.

  Enums:
    StatusValueValuesEnum: Status of the dynamic group.

  Fields:
    status: Status of the dynamic group.
    statusTime: The latest time at which the dynamic group is guaranteed to be
      in the given status. For example, if status is: UP_TO_DATE - The latest
      time at which this dynamic group was confirmed to be up to date.
      UPDATING_MEMBERSHIPS - The time at which dynamic group was created.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""Status of the dynamic group.

    Values:
      STATUS_UNSPECIFIED: Default.
      UP_TO_DATE: The dynamic group is up-to-date.
      UPDATING_MEMBERSHIPS: The dynamic group has just been created and
        memberships are being updated.
      INVALID_QUERY: Group is in an unrecoverable state and its memberships
        can't be updated.
    """
    STATUS_UNSPECIFIED = 0
    UP_TO_DATE = 1
    UPDATING_MEMBERSHIPS = 2
    INVALID_QUERY = 3

  status = _messages.EnumField('StatusValueValuesEnum', 1)
  statusTime = _messages.StringField(2)


class EndpointVerificationSpecificAttributes(_messages.Message):
  r"""Resource representing the Endpoint Verification-specific attributes of a
  Device. https://cloud.google.com/endpoint-verification/docs/overview

  Messages:
    AdditionalSignalsValue: Additional Signals reported by Endpoint
      verification.

  Fields:
    additionalSignals: Additional Signals reported by Endpoint verification.
    browserAttributes: Details of Browser profiles reported by Endpoint
      verification.
    certificateAttributes: Details of certificates reported by Endpoint
      verification.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AdditionalSignalsValue(_messages.Message):
    r"""Additional Signals reported by Endpoint verification.

    Messages:
      AdditionalProperty: An additional property for a AdditionalSignalsValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AdditionalSignalsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  additionalSignals = _messages.MessageField('AdditionalSignalsValue', 1)
  browserAttributes = _messages.MessageField('BrowserAttributes', 2, repeated=True)
  certificateAttributes = _messages.MessageField('CertificateAttributes', 3, repeated=True)


class EntityKey(_messages.Message):
  r"""A unique identifier for an entity in the Cloud Identity Groups API. An
  entity can represent either a group with an optional `namespace` or a user
  without a `namespace`. The combination of `id` and `namespace` must be
  unique; however, the same `id` can be used with different `namespace`s.

  Fields:
    id: Required. The ID of the entity. For Google-managed entities, the `id`
      must be the email address of an existing group or user. For external-
      identity-mapped entities, the `id` must be a string conforming to the
      Identity Source's requirements. Must be unique within a `namespace`.
    namespace: The namespace in which the entity exists. If not specified, the
      `EntityKey` represents a Google-managed entity such as a Google user or
      a Google Group. If specified, the `EntityKey` represents an external-
      identity-mapped group. The namespace must correspond to an identity
      source created in Admin Console and must be in the form of
      `identitysources/{identity_source_id}`.
  """

  id = _messages.StringField(1)
  namespace = _messages.StringField(2)


class ExpiryDetail(_messages.Message):
  r"""Specifies Membership expiry attributes.

  Fields:
    expireTime: Expiration time for the Membership.
  """

  expireTime = _messages.StringField(1)


class GetMembershipGraphResponse(_messages.Message):
  r"""The response message for MembershipsService.GetMembershipGraph.

  Fields:
    adjacencyList: The membership graph's path information represented as an
      adjacency list.
    groups: The resources representing each group in the adjacency list. Each
      group in this list can be correlated to a 'group' of the
      MembershipAdjacencyList using the 'name' of the Group resource.
  """

  adjacencyList = _messages.MessageField('MembershipAdjacencyList', 1, repeated=True)
  groups = _messages.MessageField('Group', 2, repeated=True)


class GoogleAppsCloudidentityDevicesV1AndroidAttributes(_messages.Message):
  r"""Resource representing the Android specific attributes of a Device.

  Enums:
    OwnershipPrivilegeValueValuesEnum: Ownership privileges on device.

  Fields:
    ctsProfileMatch: Whether the device passes Android CTS compliance.
    enabledUnknownSources: Whether applications from unknown sources can be
      installed on device.
    hasPotentiallyHarmfulApps: Whether any potentially harmful apps were
      detected on the device.
    ownerProfileAccount: Whether this account is on an owner/primary profile.
      For phones, only true for owner profiles. Android 4+ devices can have
      secondary or restricted user profiles.
    ownershipPrivilege: Ownership privileges on device.
    supportsWorkProfile: Whether device supports Android work profiles. If
      false, this service will not block access to corp data even if an
      administrator turns on the "Enforce Work Profile" policy.
    verifiedBoot: Whether Android verified boot status is GREEN.
    verifyAppsEnabled: Whether Google Play Protect Verify Apps is enabled.
  """

  class OwnershipPrivilegeValueValuesEnum(_messages.Enum):
    r"""Ownership privileges on device.

    Values:
      OWNERSHIP_PRIVILEGE_UNSPECIFIED: Ownership privilege is not set.
      DEVICE_ADMINISTRATOR: Active device administrator privileges on the
        device.
      PROFILE_OWNER: Profile Owner privileges. The account is in a managed
        corporate profile.
      DEVICE_OWNER: Device Owner privileges on the device.
    """
    OWNERSHIP_PRIVILEGE_UNSPECIFIED = 0
    DEVICE_ADMINISTRATOR = 1
    PROFILE_OWNER = 2
    DEVICE_OWNER = 3

  ctsProfileMatch = _messages.BooleanField(1)
  enabledUnknownSources = _messages.BooleanField(2)
  hasPotentiallyHarmfulApps = _messages.BooleanField(3)
  ownerProfileAccount = _messages.BooleanField(4)
  ownershipPrivilege = _messages.EnumField('OwnershipPrivilegeValueValuesEnum', 5)
  supportsWorkProfile = _messages.BooleanField(6)
  verifiedBoot = _messages.BooleanField(7)
  verifyAppsEnabled = _messages.BooleanField(8)


class GoogleAppsCloudidentityDevicesV1ApproveDeviceUserMetadata(_messages.Message):
  r"""Metadata for ApproveDeviceUser LRO."""


class GoogleAppsCloudidentityDevicesV1ApproveDeviceUserResponse(_messages.Message):
  r"""Response message for approving the device to access user data.

  Fields:
    deviceUser: Resultant DeviceUser object for the action.
  """

  deviceUser = _messages.MessageField('GoogleAppsCloudidentityDevicesV1DeviceUser', 1)


class GoogleAppsCloudidentityDevicesV1BlockDeviceUserMetadata(_messages.Message):
  r"""Metadata for BlockDeviceUser LRO."""


class GoogleAppsCloudidentityDevicesV1BlockDeviceUserResponse(_messages.Message):
  r"""Response message for blocking the device from accessing user data.

  Fields:
    deviceUser: Resultant DeviceUser object for the action.
  """

  deviceUser = _messages.MessageField('GoogleAppsCloudidentityDevicesV1DeviceUser', 1)


class GoogleAppsCloudidentityDevicesV1BrowserAttributes(_messages.Message):
  r"""Contains information about browser profiles reported by the [Endpoint
  Verification extension](https://chromewebstore.google.com/detail/endpoint-
  verification/callobklhcbilhphinckomhgkigmfocg?pli=1).

  Fields:
    chromeBrowserInfo: Represents the current state of the [Chrome browser
      attributes](https://cloud.google.com/access-context-
      manager/docs/browser-attributes) sent by the [Endpoint Verification
      extension](https://chromewebstore.google.com/detail/endpoint-
      verification/callobklhcbilhphinckomhgkigmfocg?pli=1).
    chromeProfileId: Chrome profile ID that is exposed by the Chrome API. It
      is unique for each device.
    lastProfileSyncTime: Timestamp in milliseconds since the Unix epoch when
      the profile/gcm id was last synced.
  """

  chromeBrowserInfo = _messages.MessageField('GoogleAppsCloudidentityDevicesV1BrowserInfo', 1)
  chromeProfileId = _messages.StringField(2)
  lastProfileSyncTime = _messages.StringField(3)


class GoogleAppsCloudidentityDevicesV1BrowserInfo(_messages.Message):
  r"""Browser-specific fields reported by the [Endpoint Verification
  extension](https://chromewebstore.google.com/detail/endpoint-
  verification/callobklhcbilhphinckomhgkigmfocg?pli=1).

  Enums:
    BrowserManagementStateValueValuesEnum: Output only. Browser's management
      state.
    PasswordProtectionWarningTriggerValueValuesEnum: Current state of
      [password protection trigger](https://chromeenterprise.google/policies/#
      PasswordProtectionWarningTrigger).
    SafeBrowsingProtectionLevelValueValuesEnum: Current state of [Safe
      Browsing protection level](https://chromeenterprise.google/policies/#Saf
      eBrowsingProtectionLevel).

  Fields:
    browserManagementState: Output only. Browser's management state.
    browserVersion: Version of the request initiating browser. E.g.
      `91.0.4442.4`.
    isBuiltInDnsClientEnabled: Current state of [built-in DNS client](https://
      chromeenterprise.google/policies/#BuiltInDnsClientEnabled).
    isBulkDataEntryAnalysisEnabled: Current state of [bulk data analysis](http
      s://chromeenterprise.google/policies/#OnBulkDataEntryEnterpriseConnector
      ). Set to true if provider list from Chrome is non-empty.
    isChromeCleanupEnabled: Current state of [Chrome
      Cleanup](https://chromeenterprise.google/policies/#ChromeCleanupEnabled)
      .
    isChromeRemoteDesktopAppBlocked: Current state of [Chrome Remote Desktop
      app](https://chromeenterprise.google/policies/#URLBlocklist).
    isFileDownloadAnalysisEnabled: Current state of [file download analysis](h
      ttps://chromeenterprise.google/policies/#OnFileDownloadedEnterpriseConne
      ctor). Set to true if provider list from Chrome is non-empty.
    isFileUploadAnalysisEnabled: Current state of [file upload analysis](https
      ://chromeenterprise.google/policies/#OnFileAttachedEnterpriseConnector).
      Set to true if provider list from Chrome is non-empty.
    isRealtimeUrlCheckEnabled: Current state of [real-time URL check](https://
      chromeenterprise.google/policies/#EnterpriseRealTimeUrlCheckMode). Set
      to true if provider list from Chrome is non-empty.
    isSecurityEventAnalysisEnabled: Current state of [security event analysis]
      (https://chromeenterprise.google/policies/#OnSecurityEventEnterpriseConn
      ector). Set to true if provider list from Chrome is non-empty.
    isSiteIsolationEnabled: Current state of [site isolation](https://chromeen
      terprise.google/policies/?policy=IsolateOrigins).
    isThirdPartyBlockingEnabled: Current state of [third-party blocking](https
      ://chromeenterprise.google/policies/#ThirdPartyBlockingEnabled).
    passwordProtectionWarningTrigger: Current state of [password protection tr
      igger](https://chromeenterprise.google/policies/#PasswordProtectionWarni
      ngTrigger).
    safeBrowsingProtectionLevel: Current state of [Safe Browsing protection le
      vel](https://chromeenterprise.google/policies/#SafeBrowsingProtectionLev
      el).
  """

  class BrowserManagementStateValueValuesEnum(_messages.Enum):
    r"""Output only. Browser's management state.

    Values:
      UNSPECIFIED: Management state is not specified.
      UNMANAGED: Browser/Profile is not managed by any customer.
      MANAGED_BY_OTHER_DOMAIN: Browser/Profile is managed, but by some other
        customer.
      PROFILE_MANAGED: Profile is managed by customer.
      BROWSER_MANAGED: Browser is managed by customer.
    """
    UNSPECIFIED = 0
    UNMANAGED = 1
    MANAGED_BY_OTHER_DOMAIN = 2
    PROFILE_MANAGED = 3
    BROWSER_MANAGED = 4

  class PasswordProtectionWarningTriggerValueValuesEnum(_messages.Enum):
    r"""Current state of [password protection trigger](https://chromeenterpris
    e.google/policies/#PasswordProtectionWarningTrigger).

    Values:
      PASSWORD_PROTECTION_TRIGGER_UNSPECIFIED: Password protection is not
        specified.
      PROTECTION_OFF: Password reuse is never detected.
      PASSWORD_REUSE: Warning is shown when the user reuses their protected
        password on a non-allowed site.
      PHISHING_REUSE: Warning is shown when the user reuses their protected
        password on a phishing site.
    """
    PASSWORD_PROTECTION_TRIGGER_UNSPECIFIED = 0
    PROTECTION_OFF = 1
    PASSWORD_REUSE = 2
    PHISHING_REUSE = 3

  class SafeBrowsingProtectionLevelValueValuesEnum(_messages.Enum):
    r"""Current state of [Safe Browsing protection level](https://chromeenterp
    rise.google/policies/#SafeBrowsingProtectionLevel).

    Values:
      SAFE_BROWSING_LEVEL_UNSPECIFIED: Browser protection level is not
        specified.
      DISABLED: No protection against dangerous websites, downloads, and
        extensions.
      STANDARD: Standard protection against websites, downloads, and
        extensions that are known to be dangerous.
      ENHANCED: Faster, proactive protection against dangerous websites,
        downloads, and extensions.
    """
    SAFE_BROWSING_LEVEL_UNSPECIFIED = 0
    DISABLED = 1
    STANDARD = 2
    ENHANCED = 3

  browserManagementState = _messages.EnumField('BrowserManagementStateValueValuesEnum', 1)
  browserVersion = _messages.StringField(2)
  isBuiltInDnsClientEnabled = _messages.BooleanField(3)
  isBulkDataEntryAnalysisEnabled = _messages.BooleanField(4)
  isChromeCleanupEnabled = _messages.BooleanField(5)
  isChromeRemoteDesktopAppBlocked = _messages.BooleanField(6)
  isFileDownloadAnalysisEnabled = _messages.BooleanField(7)
  isFileUploadAnalysisEnabled = _messages.BooleanField(8)
  isRealtimeUrlCheckEnabled = _messages.BooleanField(9)
  isSecurityEventAnalysisEnabled = _messages.BooleanField(10)
  isSiteIsolationEnabled = _messages.BooleanField(11)
  isThirdPartyBlockingEnabled = _messages.BooleanField(12)
  passwordProtectionWarningTrigger = _messages.EnumField('PasswordProtectionWarningTriggerValueValuesEnum', 13)
  safeBrowsingProtectionLevel = _messages.EnumField('SafeBrowsingProtectionLevelValueValuesEnum', 14)


class GoogleAppsCloudidentityDevicesV1CancelWipeDeviceMetadata(_messages.Message):
  r"""Metadata for CancelWipeDevice LRO."""


class GoogleAppsCloudidentityDevicesV1CancelWipeDeviceResponse(_messages.Message):
  r"""Response message for cancelling an unfinished device wipe.

  Fields:
    device: Resultant Device object for the action. Note that asset tags will
      not be returned in the device object.
  """

  device = _messages.MessageField('GoogleAppsCloudidentityDevicesV1Device', 1)


class GoogleAppsCloudidentityDevicesV1CancelWipeDeviceUserMetadata(_messages.Message):
  r"""Metadata for CancelWipeDeviceUser LRO."""


class GoogleAppsCloudidentityDevicesV1CancelWipeDeviceUserResponse(_messages.Message):
  r"""Response message for cancelling an unfinished user account wipe.

  Fields:
    deviceUser: Resultant DeviceUser object for the action.
  """

  deviceUser = _messages.MessageField('GoogleAppsCloudidentityDevicesV1DeviceUser', 1)


class GoogleAppsCloudidentityDevicesV1CertificateAttributes(_messages.Message):
  r"""Stores information about a certificate.

  Enums:
    ValidationStateValueValuesEnum: Output only. Validation state of this
      certificate.

  Fields:
    certificateTemplate: The X.509 extension for CertificateTemplate.
    fingerprint: The encoded certificate fingerprint.
    issuer: The name of the issuer of this certificate.
    serialNumber: Serial number of the certificate, Example: "*********".
    subject: The subject name of this certificate.
    thumbprint: The certificate thumbprint.
    validationState: Output only. Validation state of this certificate.
    validityExpirationTime: Certificate not valid at or after this timestamp.
    validityStartTime: Certificate not valid before this timestamp.
  """

  class ValidationStateValueValuesEnum(_messages.Enum):
    r"""Output only. Validation state of this certificate.

    Values:
      CERTIFICATE_VALIDATION_STATE_UNSPECIFIED: Default value.
      VALIDATION_SUCCESSFUL: Certificate validation was successful.
      VALIDATION_FAILED: Certificate validation failed.
    """
    CERTIFICATE_VALIDATION_STATE_UNSPECIFIED = 0
    VALIDATION_SUCCESSFUL = 1
    VALIDATION_FAILED = 2

  certificateTemplate = _messages.MessageField('GoogleAppsCloudidentityDevicesV1CertificateTemplate', 1)
  fingerprint = _messages.StringField(2)
  issuer = _messages.StringField(3)
  serialNumber = _messages.StringField(4)
  subject = _messages.StringField(5)
  thumbprint = _messages.StringField(6)
  validationState = _messages.EnumField('ValidationStateValueValuesEnum', 7)
  validityExpirationTime = _messages.StringField(8)
  validityStartTime = _messages.StringField(9)


class GoogleAppsCloudidentityDevicesV1CertificateTemplate(_messages.Message):
  r"""CertificateTemplate (v3 Extension in X.509).

  Fields:
    id: The template id of the template. Example: "*******.4.1.311.21.8.156086
      21.11768144.5720724.16068415.6889630.81.2472537.7784047".
    majorVersion: The Major version of the template. Example: 100.
    minorVersion: The minor version of the template. Example: 12.
  """

  id = _messages.StringField(1)
  majorVersion = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  minorVersion = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class GoogleAppsCloudidentityDevicesV1ClientState(_messages.Message):
  r"""Represents the state associated with an API client calling the Devices
  API. Resource representing ClientState and supports updates from API users

  Enums:
    ComplianceStateValueValuesEnum: The compliance state of the resource as
      specified by the API client.
    HealthScoreValueValuesEnum: The Health score of the resource. The Health
      score is the callers specification of the condition of the device from a
      usability point of view. For example, a third-party device management
      provider may specify a health score based on its compliance with
      organizational policies.
    ManagedValueValuesEnum: The management state of the resource as specified
      by the API client.
    OwnerTypeValueValuesEnum: Output only. The owner of the ClientState

  Messages:
    KeyValuePairsValue: The map of key-value attributes stored by callers
      specific to a device. The total serialized length of this map may not
      exceed 10KB. No limit is placed on the number of attributes in a map.

  Fields:
    assetTags: The caller can specify asset tags for this resource
    complianceState: The compliance state of the resource as specified by the
      API client.
    createTime: Output only. The time the client state data was created.
    customId: This field may be used to store a unique identifier for the API
      resource within which these CustomAttributes are a field.
    etag: The token that needs to be passed back for concurrency control in
      updates. Token needs to be passed back in UpdateRequest
    healthScore: The Health score of the resource. The Health score is the
      callers specification of the condition of the device from a usability
      point of view. For example, a third-party device management provider may
      specify a health score based on its compliance with organizational
      policies.
    keyValuePairs: The map of key-value attributes stored by callers specific
      to a device. The total serialized length of this map may not exceed
      10KB. No limit is placed on the number of attributes in a map.
    lastUpdateTime: Output only. The time the client state data was last
      updated.
    managed: The management state of the resource as specified by the API
      client.
    name: Output only. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      ClientState in format:
      `devices/{device}/deviceUsers/{device_user}/clientState/{partner}`,
      where partner corresponds to the partner storing the data. For partners
      belonging to the "BeyondCorp Alliance", this is the partner ID specified
      to you by Google. For all other callers, this is a string of the form:
      `{customer}-suffix`, where `customer` is your customer ID. The *suffix*
      is any string the caller specifies. This string will be displayed
      verbatim in the administration console. This suffix is used in setting
      up Custom Access Levels in Context-Aware Access. Your organization's
      customer ID can be obtained from the URL: `GET
      https://www.googleapis.com/admin/directory/v1/customers/my_customer` The
      `id` field in the response contains the customer ID starting with the
      letter 'C'. The customer ID to be used in this API is the string after
      the letter 'C' (not including 'C')
    ownerType: Output only. The owner of the ClientState
    scoreReason: A descriptive cause of the health score.
  """

  class ComplianceStateValueValuesEnum(_messages.Enum):
    r"""The compliance state of the resource as specified by the API client.

    Values:
      COMPLIANCE_STATE_UNSPECIFIED: The compliance state of the resource is
        unknown or unspecified.
      COMPLIANT: Device is compliant with third party policies
      NON_COMPLIANT: Device is not compliant with third party policies
    """
    COMPLIANCE_STATE_UNSPECIFIED = 0
    COMPLIANT = 1
    NON_COMPLIANT = 2

  class HealthScoreValueValuesEnum(_messages.Enum):
    r"""The Health score of the resource. The Health score is the callers
    specification of the condition of the device from a usability point of
    view. For example, a third-party device management provider may specify a
    health score based on its compliance with organizational policies.

    Values:
      HEALTH_SCORE_UNSPECIFIED: Default value
      VERY_POOR: The object is in very poor health as defined by the caller.
      POOR: The object is in poor health as defined by the caller.
      NEUTRAL: The object health is neither good nor poor, as defined by the
        caller.
      GOOD: The object is in good health as defined by the caller.
      VERY_GOOD: The object is in very good health as defined by the caller.
    """
    HEALTH_SCORE_UNSPECIFIED = 0
    VERY_POOR = 1
    POOR = 2
    NEUTRAL = 3
    GOOD = 4
    VERY_GOOD = 5

  class ManagedValueValuesEnum(_messages.Enum):
    r"""The management state of the resource as specified by the API client.

    Values:
      MANAGED_STATE_UNSPECIFIED: The management state of the resource is
        unknown or unspecified.
      MANAGED: The resource is managed.
      UNMANAGED: The resource is not managed.
    """
    MANAGED_STATE_UNSPECIFIED = 0
    MANAGED = 1
    UNMANAGED = 2

  class OwnerTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The owner of the ClientState

    Values:
      OWNER_TYPE_UNSPECIFIED: Unknown owner type
      OWNER_TYPE_CUSTOMER: Customer is the owner
      OWNER_TYPE_PARTNER: Partner is the owner
    """
    OWNER_TYPE_UNSPECIFIED = 0
    OWNER_TYPE_CUSTOMER = 1
    OWNER_TYPE_PARTNER = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class KeyValuePairsValue(_messages.Message):
    r"""The map of key-value attributes stored by callers specific to a
    device. The total serialized length of this map may not exceed 10KB. No
    limit is placed on the number of attributes in a map.

    Messages:
      AdditionalProperty: An additional property for a KeyValuePairsValue
        object.

    Fields:
      additionalProperties: Additional properties of type KeyValuePairsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a KeyValuePairsValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleAppsCloudidentityDevicesV1CustomAttributeValue
          attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleAppsCloudidentityDevicesV1CustomAttributeValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  assetTags = _messages.StringField(1, repeated=True)
  complianceState = _messages.EnumField('ComplianceStateValueValuesEnum', 2)
  createTime = _messages.StringField(3)
  customId = _messages.StringField(4)
  etag = _messages.StringField(5)
  healthScore = _messages.EnumField('HealthScoreValueValuesEnum', 6)
  keyValuePairs = _messages.MessageField('KeyValuePairsValue', 7)
  lastUpdateTime = _messages.StringField(8)
  managed = _messages.EnumField('ManagedValueValuesEnum', 9)
  name = _messages.StringField(10)
  ownerType = _messages.EnumField('OwnerTypeValueValuesEnum', 11)
  scoreReason = _messages.StringField(12)


class GoogleAppsCloudidentityDevicesV1CreateDeviceMetadata(_messages.Message):
  r"""Metadata for CreateDevice LRO."""


class GoogleAppsCloudidentityDevicesV1CustomAttributeValue(_messages.Message):
  r"""Additional custom attribute values may be one of these types

  Fields:
    boolValue: Represents a boolean value.
    numberValue: Represents a double value.
    stringValue: Represents a string value.
  """

  boolValue = _messages.BooleanField(1)
  numberValue = _messages.FloatField(2)
  stringValue = _messages.StringField(3)


class GoogleAppsCloudidentityDevicesV1DeleteDeviceMetadata(_messages.Message):
  r"""Metadata for DeleteDevice LRO."""


class GoogleAppsCloudidentityDevicesV1DeleteDeviceUserMetadata(_messages.Message):
  r"""Metadata for DeleteDeviceUser LRO."""


class GoogleAppsCloudidentityDevicesV1Device(_messages.Message):
  r""" A Device within the Cloud Identity Devices API. Represents a Device
  known to Google Cloud, independent of the device ownership, type, and
  whether it is assigned or in use by a user.

  Enums:
    CompromisedStateValueValuesEnum: Output only. Represents whether the
      Device is compromised.
    DeviceTypeValueValuesEnum: Output only. Type of device.
    EncryptionStateValueValuesEnum: Output only. Device encryption state.
    ManagementStateValueValuesEnum: Output only. Management state of the
      device
    OwnerTypeValueValuesEnum: Output only. Whether the device is owned by the
      company or an individual

  Fields:
    androidSpecificAttributes: Output only. Attributes specific to Android
      devices.
    assetTag: Asset tag of the device.
    basebandVersion: Output only. Baseband version of the device.
    bootloaderVersion: Output only. Device bootloader version. Example: 0.6.7.
    brand: Output only. Device brand. Example: Samsung.
    buildNumber: Output only. Build number of the device.
    compromisedState: Output only. Represents whether the Device is
      compromised.
    createTime: Output only. When the Company-Owned device was imported. This
      field is empty for BYOD devices.
    deviceId: Unique identifier for the device.
    deviceType: Output only. Type of device.
    enabledDeveloperOptions: Output only. Whether developer options is enabled
      on device.
    enabledUsbDebugging: Output only. Whether USB debugging is enabled on
      device.
    encryptionState: Output only. Device encryption state.
    endpointVerificationSpecificAttributes: Output only. Attributes specific
      to [Endpoint Verification](https://cloud.google.com/endpoint-
      verification/docs/overview) devices.
    hostname: Host name of the device.
    imei: Output only. IMEI number of device if GSM device; empty otherwise.
    kernelVersion: Output only. Kernel version of the device.
    lastSyncTime: Most recent time when device synced with this service.
    managementState: Output only. Management state of the device
    manufacturer: Output only. Device manufacturer. Example: Motorola.
    meid: Output only. MEID number of device if CDMA device; empty otherwise.
    model: Output only. Model name of device. Example: Pixel 3.
    name: Output only. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the Device
      in format: `devices/{device}`, where device is the unique id assigned to
      the Device. Important: Device API scopes require that you use domain-
      wide delegation to access the API. For more information, see [Set up the
      Devices API](https://cloud.google.com/identity/docs/how-to/setup-
      devices).
    networkOperator: Output only. Mobile or network operator of device, if
      available.
    osVersion: Output only. OS version of the device. Example: Android 8.1.0.
    otherAccounts: Output only. Domain name for Google accounts on device.
      Type for other accounts on device. On Android, will only be populated if
      |ownership_privilege| is |PROFILE_OWNER| or |DEVICE_OWNER|. Does not
      include the account signed in to the device policy app if that account's
      domain has only one account. Examples: "com.example", "xyz.com".
    ownerType: Output only. Whether the device is owned by the company or an
      individual
    releaseVersion: Output only. OS release version. Example: 6.0.
    securityPatchTime: Output only. OS security patch update time on device.
    serialNumber: Serial Number of device. Example: HT82V1A01076.
    unifiedDeviceId: Output only. Unified device id of the device.
    wifiMacAddresses: WiFi MAC addresses of device.
  """

  class CompromisedStateValueValuesEnum(_messages.Enum):
    r"""Output only. Represents whether the Device is compromised.

    Values:
      COMPROMISED_STATE_UNSPECIFIED: Default value.
      COMPROMISED: The device is compromised (currently, this means Android
        device is rooted).
      UNCOMPROMISED: The device is safe (currently, this means Android device
        is unrooted).
    """
    COMPROMISED_STATE_UNSPECIFIED = 0
    COMPROMISED = 1
    UNCOMPROMISED = 2

  class DeviceTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of device.

    Values:
      DEVICE_TYPE_UNSPECIFIED: Unknown device type
      ANDROID: Device is an Android device
      IOS: Device is an iOS device
      GOOGLE_SYNC: Device is a Google Sync device.
      WINDOWS: Device is a Windows device.
      MAC_OS: Device is a MacOS device.
      LINUX: Device is a Linux device.
      CHROME_OS: Device is a ChromeOS device.
    """
    DEVICE_TYPE_UNSPECIFIED = 0
    ANDROID = 1
    IOS = 2
    GOOGLE_SYNC = 3
    WINDOWS = 4
    MAC_OS = 5
    LINUX = 6
    CHROME_OS = 7

  class EncryptionStateValueValuesEnum(_messages.Enum):
    r"""Output only. Device encryption state.

    Values:
      ENCRYPTION_STATE_UNSPECIFIED: Encryption Status is not set.
      UNSUPPORTED_BY_DEVICE: Device doesn't support encryption.
      ENCRYPTED: Device is encrypted.
      NOT_ENCRYPTED: Device is not encrypted.
    """
    ENCRYPTION_STATE_UNSPECIFIED = 0
    UNSUPPORTED_BY_DEVICE = 1
    ENCRYPTED = 2
    NOT_ENCRYPTED = 3

  class ManagementStateValueValuesEnum(_messages.Enum):
    r"""Output only. Management state of the device

    Values:
      MANAGEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      APPROVED: Device is approved.
      BLOCKED: Device is blocked.
      PENDING: Device is pending approval.
      UNPROVISIONED: The device is not provisioned. Device will start from
        this state until some action is taken (i.e. a user starts using the
        device).
      WIPING: Data and settings on the device are being removed.
      WIPED: All data and settings on the device are removed.
    """
    MANAGEMENT_STATE_UNSPECIFIED = 0
    APPROVED = 1
    BLOCKED = 2
    PENDING = 3
    UNPROVISIONED = 4
    WIPING = 5
    WIPED = 6

  class OwnerTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Whether the device is owned by the company or an
    individual

    Values:
      DEVICE_OWNERSHIP_UNSPECIFIED: Default value. The value is unused.
      COMPANY: Company owns the device.
      BYOD: Bring Your Own Device (i.e. individual owns the device)
    """
    DEVICE_OWNERSHIP_UNSPECIFIED = 0
    COMPANY = 1
    BYOD = 2

  androidSpecificAttributes = _messages.MessageField('GoogleAppsCloudidentityDevicesV1AndroidAttributes', 1)
  assetTag = _messages.StringField(2)
  basebandVersion = _messages.StringField(3)
  bootloaderVersion = _messages.StringField(4)
  brand = _messages.StringField(5)
  buildNumber = _messages.StringField(6)
  compromisedState = _messages.EnumField('CompromisedStateValueValuesEnum', 7)
  createTime = _messages.StringField(8)
  deviceId = _messages.StringField(9)
  deviceType = _messages.EnumField('DeviceTypeValueValuesEnum', 10)
  enabledDeveloperOptions = _messages.BooleanField(11)
  enabledUsbDebugging = _messages.BooleanField(12)
  encryptionState = _messages.EnumField('EncryptionStateValueValuesEnum', 13)
  endpointVerificationSpecificAttributes = _messages.MessageField('GoogleAppsCloudidentityDevicesV1EndpointVerificationSpecificAttributes', 14)
  hostname = _messages.StringField(15)
  imei = _messages.StringField(16)
  kernelVersion = _messages.StringField(17)
  lastSyncTime = _messages.StringField(18)
  managementState = _messages.EnumField('ManagementStateValueValuesEnum', 19)
  manufacturer = _messages.StringField(20)
  meid = _messages.StringField(21)
  model = _messages.StringField(22)
  name = _messages.StringField(23)
  networkOperator = _messages.StringField(24)
  osVersion = _messages.StringField(25)
  otherAccounts = _messages.StringField(26, repeated=True)
  ownerType = _messages.EnumField('OwnerTypeValueValuesEnum', 27)
  releaseVersion = _messages.StringField(28)
  securityPatchTime = _messages.StringField(29)
  serialNumber = _messages.StringField(30)
  unifiedDeviceId = _messages.StringField(31)
  wifiMacAddresses = _messages.StringField(32, repeated=True)


class GoogleAppsCloudidentityDevicesV1DeviceUser(_messages.Message):
  r"""Represents a user's use of a Device in the Cloud Identity Devices API. A
  DeviceUser is a resource representing a user's use of a Device

  Enums:
    CompromisedStateValueValuesEnum: Compromised State of the DeviceUser
      object
    ManagementStateValueValuesEnum: Output only. Management state of the user
      on the device.
    PasswordStateValueValuesEnum: Password state of the DeviceUser object

  Fields:
    compromisedState: Compromised State of the DeviceUser object
    createTime: When the user first signed in to the device
    firstSyncTime: Output only. Most recent time when user registered with
      this service.
    languageCode: Output only. Default locale used on device, in IETF BCP-47
      format.
    lastSyncTime: Output only. Last time when user synced with policies.
    managementState: Output only. Management state of the user on the device.
    name: Output only. [Resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      DeviceUser in format: `devices/{device}/deviceUsers/{device_user}`,
      where `device_user` uniquely identifies a user's use of a device.
    passwordState: Password state of the DeviceUser object
    userAgent: Output only. User agent on the device for this specific user
    userEmail: Email address of the user registered on the device.
  """

  class CompromisedStateValueValuesEnum(_messages.Enum):
    r"""Compromised State of the DeviceUser object

    Values:
      COMPROMISED_STATE_UNSPECIFIED: Compromised state of Device User account
        is unknown or unspecified.
      COMPROMISED: Device User Account is compromised.
      NOT_COMPROMISED: Device User Account is not compromised.
    """
    COMPROMISED_STATE_UNSPECIFIED = 0
    COMPROMISED = 1
    NOT_COMPROMISED = 2

  class ManagementStateValueValuesEnum(_messages.Enum):
    r"""Output only. Management state of the user on the device.

    Values:
      MANAGEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      WIPING: This user's data and profile is being removed from the device.
      WIPED: This user's data and profile is removed from the device.
      APPROVED: User is approved to access data on the device.
      BLOCKED: User is blocked from accessing data on the device.
      PENDING_APPROVAL: User is awaiting approval.
      UNENROLLED: User is unenrolled from Advanced Windows Management, but the
        Windows account is still intact.
    """
    MANAGEMENT_STATE_UNSPECIFIED = 0
    WIPING = 1
    WIPED = 2
    APPROVED = 3
    BLOCKED = 4
    PENDING_APPROVAL = 5
    UNENROLLED = 6

  class PasswordStateValueValuesEnum(_messages.Enum):
    r"""Password state of the DeviceUser object

    Values:
      PASSWORD_STATE_UNSPECIFIED: Password state not set.
      PASSWORD_SET: Password set in object.
      PASSWORD_NOT_SET: Password not set in object.
    """
    PASSWORD_STATE_UNSPECIFIED = 0
    PASSWORD_SET = 1
    PASSWORD_NOT_SET = 2

  compromisedState = _messages.EnumField('CompromisedStateValueValuesEnum', 1)
  createTime = _messages.StringField(2)
  firstSyncTime = _messages.StringField(3)
  languageCode = _messages.StringField(4)
  lastSyncTime = _messages.StringField(5)
  managementState = _messages.EnumField('ManagementStateValueValuesEnum', 6)
  name = _messages.StringField(7)
  passwordState = _messages.EnumField('PasswordStateValueValuesEnum', 8)
  userAgent = _messages.StringField(9)
  userEmail = _messages.StringField(10)


class GoogleAppsCloudidentityDevicesV1EndpointVerificationSpecificAttributes(_messages.Message):
  r"""Resource representing the [Endpoint Verification-specific
  attributes](https://cloud.google.com/endpoint-verification/docs/device-
  information) of a device.

  Messages:
    AdditionalSignalsValue: [Additional
      signals](https://cloud.google.com/endpoint-verification/docs/device-
      information) reported by Endpoint Verification. It includes the
      following attributes: * Non-configurable attributes: hotfixes,
      av_installed, av_enabled, windows_domain_name,
      is_os_native_firewall_enabled, and is_secure_boot_enabled. *
      [Configurable attributes](https://cloud.google.com/endpoint-
      verification/docs/collect-config-attributes): file, folder, and binary
      attributes; registry entries; and properties in a plist.

  Fields:
    additionalSignals: [Additional signals](https://cloud.google.com/endpoint-
      verification/docs/device-information) reported by Endpoint Verification.
      It includes the following attributes: * Non-configurable attributes:
      hotfixes, av_installed, av_enabled, windows_domain_name,
      is_os_native_firewall_enabled, and is_secure_boot_enabled. *
      [Configurable attributes](https://cloud.google.com/endpoint-
      verification/docs/collect-config-attributes): file, folder, and binary
      attributes; registry entries; and properties in a plist.
    browserAttributes: Details of browser profiles reported by Endpoint
      Verification.
    certificateAttributes: Details of certificates.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AdditionalSignalsValue(_messages.Message):
    r"""[Additional signals](https://cloud.google.com/endpoint-
    verification/docs/device-information) reported by Endpoint Verification.
    It includes the following attributes: * Non-configurable attributes:
    hotfixes, av_installed, av_enabled, windows_domain_name,
    is_os_native_firewall_enabled, and is_secure_boot_enabled. * [Configurable
    attributes](https://cloud.google.com/endpoint-verification/docs/collect-
    config-attributes): file, folder, and binary attributes; registry entries;
    and properties in a plist.

    Messages:
      AdditionalProperty: An additional property for a AdditionalSignalsValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AdditionalSignalsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  additionalSignals = _messages.MessageField('AdditionalSignalsValue', 1)
  browserAttributes = _messages.MessageField('GoogleAppsCloudidentityDevicesV1BrowserAttributes', 2, repeated=True)
  certificateAttributes = _messages.MessageField('GoogleAppsCloudidentityDevicesV1CertificateAttributes', 3, repeated=True)


class GoogleAppsCloudidentityDevicesV1ListEndpointAppsMetadata(_messages.Message):
  r"""Metadata for ListEndpointApps LRO."""


class GoogleAppsCloudidentityDevicesV1SignoutDeviceUserMetadata(_messages.Message):
  r"""Metadata for SignoutDeviceUser LRO."""


class GoogleAppsCloudidentityDevicesV1UpdateClientStateMetadata(_messages.Message):
  r"""Metadata for UpdateClientState LRO."""


class GoogleAppsCloudidentityDevicesV1UpdateDeviceMetadata(_messages.Message):
  r"""Metadata for UpdateDevice LRO."""


class GoogleAppsCloudidentityDevicesV1WipeDeviceMetadata(_messages.Message):
  r"""Metadata for WipeDevice LRO."""


class GoogleAppsCloudidentityDevicesV1WipeDeviceResponse(_messages.Message):
  r"""Response message for wiping all data on the device.

  Fields:
    device: Resultant Device object for the action. Note that asset tags will
      not be returned in the device object.
  """

  device = _messages.MessageField('GoogleAppsCloudidentityDevicesV1Device', 1)


class GoogleAppsCloudidentityDevicesV1WipeDeviceUserMetadata(_messages.Message):
  r"""Metadata for WipeDeviceUser LRO."""


class GoogleAppsCloudidentityDevicesV1WipeDeviceUserResponse(_messages.Message):
  r"""Response message for wiping the user's account from the device.

  Fields:
    deviceUser: Resultant DeviceUser object for the action.
  """

  deviceUser = _messages.MessageField('GoogleAppsCloudidentityDevicesV1DeviceUser', 1)


class Group(_messages.Message):
  r"""A group within the Cloud Identity Groups API. A `Group` is a collection
  of entities, where each entity is either a user or another group.

  Messages:
    LabelsValue: Required. One or more label entries that apply to the Group.
      Labels contain a key with an empty value. Google Groups are the default
      type of group and have a label with a key of
      `cloudidentity.googleapis.com/groups.discussion_forum` and an empty
      value. Existing Google Groups can have an additional label with a key of
      `cloudidentity.googleapis.com/groups.security` and an empty value added
      to them. **This is an immutable change and the security label cannot be
      removed once added.** Dynamic groups have a label with a key of
      `cloudidentity.googleapis.com/groups.dynamic`. Identity-mapped groups
      for Cloud Search have a label with a key of `system/groups/external` and
      an empty value. Google Groups can be
      [locked](https://support.google.com/a?p=locked-groups). To lock a group,
      add a label with a key of `cloudidentity.googleapis.com/groups.locked`
      and an empty value. Doing so locks the group. To unlock the group,
      remove this label.

  Fields:
    additionalGroupKeys: Output only. Additional group keys associated with
      the Group.
    createTime: Output only. The time when the `Group` was created.
    description: An extended description to help users determine the purpose
      of a `Group`. Must not be longer than 4,096 characters.
    displayName: The display name of the `Group`.
    dynamicGroupMetadata: Dynamic group metadata like queries and status.
    groupKey: Required. The `EntityKey` of the `Group`.
    labels: Required. One or more label entries that apply to the Group.
      Labels contain a key with an empty value. Google Groups are the default
      type of group and have a label with a key of
      `cloudidentity.googleapis.com/groups.discussion_forum` and an empty
      value. Existing Google Groups can have an additional label with a key of
      `cloudidentity.googleapis.com/groups.security` and an empty value added
      to them. **This is an immutable change and the security label cannot be
      removed once added.** Dynamic groups have a label with a key of
      `cloudidentity.googleapis.com/groups.dynamic`. Identity-mapped groups
      for Cloud Search have a label with a key of `system/groups/external` and
      an empty value. Google Groups can be
      [locked](https://support.google.com/a?p=locked-groups). To lock a group,
      add a label with a key of `cloudidentity.googleapis.com/groups.locked`
      and an empty value. Doing so locks the group. To unlock the group,
      remove this label.
    name: Output only. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      `Group`. Shall be of the form `groups/{group_id}`.
    parent: Required. Immutable. The resource name of the entity under which
      this `Group` resides in the Cloud Identity resource hierarchy. Must be
      of the form `identitysources/{identity_source}` for external [identity-
      mapped groups](https://support.google.com/a/answer/9039510) or
      `customers/{customer_id}` for Google Groups. The `customer_id` must
      begin with "C" (for example, 'C046psxkn'). [Find your customer ID.]
      (https://support.google.com/cloudidentity/answer/10070793)
    posixGroups: The POSIX groups associated with the `Group`.
    updateTime: Output only. The time when the `Group` was last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Required. One or more label entries that apply to the Group. Labels
    contain a key with an empty value. Google Groups are the default type of
    group and have a label with a key of
    `cloudidentity.googleapis.com/groups.discussion_forum` and an empty value.
    Existing Google Groups can have an additional label with a key of
    `cloudidentity.googleapis.com/groups.security` and an empty value added to
    them. **This is an immutable change and the security label cannot be
    removed once added.** Dynamic groups have a label with a key of
    `cloudidentity.googleapis.com/groups.dynamic`. Identity-mapped groups for
    Cloud Search have a label with a key of `system/groups/external` and an
    empty value. Google Groups can be
    [locked](https://support.google.com/a?p=locked-groups). To lock a group,
    add a label with a key of `cloudidentity.googleapis.com/groups.locked` and
    an empty value. Doing so locks the group. To unlock the group, remove this
    label.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  additionalGroupKeys = _messages.MessageField('EntityKey', 1, repeated=True)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  displayName = _messages.StringField(4)
  dynamicGroupMetadata = _messages.MessageField('DynamicGroupMetadata', 5)
  groupKey = _messages.MessageField('EntityKey', 6)
  labels = _messages.MessageField('LabelsValue', 7)
  name = _messages.StringField(8)
  parent = _messages.StringField(9)
  posixGroups = _messages.MessageField('PosixGroup', 10, repeated=True)
  updateTime = _messages.StringField(11)


class GroupRelation(_messages.Message):
  r"""Message representing a transitive group of a user or a group.

  Enums:
    RelationTypeValueValuesEnum: The relation between the member and the
      transitive group.

  Messages:
    LabelsValue: Labels for Group resource.

  Fields:
    displayName: Display name for this group.
    group: Resource name for this group.
    groupKey: Entity key has an id and a namespace. In case of discussion
      forums, the id will be an email address without a namespace.
    labels: Labels for Group resource.
    relationType: The relation between the member and the transitive group.
    roles: Membership roles of the member for the group.
  """

  class RelationTypeValueValuesEnum(_messages.Enum):
    r"""The relation between the member and the transitive group.

    Values:
      RELATION_TYPE_UNSPECIFIED: The relation type is undefined or
        undetermined.
      DIRECT: The two entities have only a direct membership with each other.
      INDIRECT: The two entities have only an indirect membership with each
        other.
      DIRECT_AND_INDIRECT: The two entities have both a direct and an indirect
        membership with each other.
    """
    RELATION_TYPE_UNSPECIFIED = 0
    DIRECT = 1
    INDIRECT = 2
    DIRECT_AND_INDIRECT = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels for Group resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  group = _messages.StringField(2)
  groupKey = _messages.MessageField('EntityKey', 3)
  labels = _messages.MessageField('LabelsValue', 4)
  relationType = _messages.EnumField('RelationTypeValueValuesEnum', 5)
  roles = _messages.MessageField('TransitiveMembershipRole', 6, repeated=True)


class LatLng(_messages.Message):
  r"""An object that represents a latitude/longitude pair. This is expressed
  as a pair of doubles to represent degrees latitude and degrees longitude.
  Unless specified otherwise, this object must conform to the WGS84 standard.
  Values must be within normalized ranges.

  Fields:
    latitude: The latitude in degrees. It must be in the range [-90.0, +90.0].
    longitude: The longitude in degrees. It must be in the range [-180.0,
      +180.0].
  """

  latitude = _messages.FloatField(1)
  longitude = _messages.FloatField(2)


class ListClientStatesResponse(_messages.Message):
  r"""Response message that is returned in LRO result of ListClientStates
  Operation.

  Fields:
    clientStates: Client states meeting the list restrictions.
    nextPageToken: Token to retrieve the next page of results. Empty if there
      are no more results.
  """

  clientStates = _messages.MessageField('ClientState', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListDeviceUsersResponse(_messages.Message):
  r"""Response message that is returned from the ListDeviceUsers method.

  Fields:
    deviceUsers: Devices meeting the list restrictions.
    nextPageToken: Token to retrieve the next page of results. Empty if there
      are no more results.
  """

  deviceUsers = _messages.MessageField('DeviceUser', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListDevicesResponse(_messages.Message):
  r"""Response message that is returned from the ListDevices method.

  Fields:
    devices: Devices meeting the list restrictions.
    nextPageToken: Token to retrieve the next page of results. Empty if there
      are no more results.
  """

  devices = _messages.MessageField('Device', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListGroupsResponse(_messages.Message):
  r"""The response message for GroupsService.ListGroups.

  Fields:
    groups: The `Group` resources under the specified `parent`.
    nextPageToken: A continuation token to retrieve the next page of results,
      or empty if there are no more results available.
  """

  groups = _messages.MessageField('Group', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListMembershipsResponse(_messages.Message):
  r"""The response message for MembershipsService.ListMemberships.

  Fields:
    memberships: The `Membership`s under the specified `parent`.
    nextPageToken: A continuation token to retrieve the next page of results,
      or empty if there are no more results available.
  """

  memberships = _messages.MessageField('Membership', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class LookupGroupNameResponse(_messages.Message):
  r"""The response message for GroupsService.LookupGroupName.

  Fields:
    name: Output only. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      looked-up `Group`.
  """

  name = _messages.StringField(1)


class LookupMembershipNameResponse(_messages.Message):
  r"""The response message for MembershipsService.LookupMembershipName.

  Fields:
    name: The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      looked-up `Membership`. Must be of the form
      `groups/{group_id}/memberships/{membership_id}`.
  """

  name = _messages.StringField(1)


class LookupSelfDeviceUsersResponse(_messages.Message):
  r"""Response containing resource names of the DeviceUsers associated with
  the caller's credentials.

  Fields:
    customer: The obfuscated customer Id that may be passed back to other
      Devices API methods such as List, Get, etc.
    names: [Resource
      names](https://cloud.google.com/apis/design/resource_names) of the
      DeviceUsers in the format:
      `devices/{device_id}/deviceUsers/{user_resource_id}`, where device_id is
      the unique ID assigned to a Device and user_resource_id is the unique
      user ID
    nextPageToken: Token to retrieve the next page of results. Empty if there
      are no more results.
  """

  customer = _messages.StringField(1)
  names = _messages.StringField(2, repeated=True)
  nextPageToken = _messages.StringField(3)


class LostModeDeviceInfo(_messages.Message):
  r"""Contains information about the location and battery level of a device in
  lost mode.

  Fields:
    lostModeRecords: List of one or more last known information (location and
      battery) of a device in lost mode.
  """

  lostModeRecords = _messages.MessageField('LostModeRecord', 1, repeated=True)


class LostModeRecord(_messages.Message):
  r"""Contains information about the location and battery level of a device in
  lost mode.

  Fields:
    batteryLevel: The decrypted device location containing a int32.
    collectionTime: Device timestamp when the event was logged
    location: The decrypted device location containing a LatLng
      (google3/google/type/latlng.proto).
  """

  batteryLevel = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  collectionTime = _messages.StringField(2)
  location = _messages.MessageField('LatLng', 3)


class MemberRelation(_messages.Message):
  r"""Message representing a transitive membership of a group.

  Enums:
    RelationTypeValueValuesEnum: The relation between the group and the
      transitive membership.

  Fields:
    member: Resource name for this member.
    preferredMemberKey: Entity key has an id and a namespace. In case of
      discussion forums, the id will be an email address without a namespace.
    relationType: The relation between the group and the transitive
      membership.
    roles: The membership role details (i.e name of role and expiry time).
  """

  class RelationTypeValueValuesEnum(_messages.Enum):
    r"""The relation between the group and the transitive membership.

    Values:
      RELATION_TYPE_UNSPECIFIED: The relation type is undefined or
        undetermined.
      DIRECT: The two entities have only a direct membership with each other.
      INDIRECT: The two entities have only an indirect membership with each
        other.
      DIRECT_AND_INDIRECT: The two entities have both a direct and an indirect
        membership with each other.
    """
    RELATION_TYPE_UNSPECIFIED = 0
    DIRECT = 1
    INDIRECT = 2
    DIRECT_AND_INDIRECT = 3

  member = _messages.StringField(1)
  preferredMemberKey = _messages.MessageField('EntityKey', 2, repeated=True)
  relationType = _messages.EnumField('RelationTypeValueValuesEnum', 3)
  roles = _messages.MessageField('TransitiveMembershipRole', 4, repeated=True)


class Membership(_messages.Message):
  r"""A membership within the Cloud Identity Groups API. A `Membership`
  defines a relationship between a `Group` and an entity belonging to that
  `Group`, referred to as a "member".

  Enums:
    DeliverySettingValueValuesEnum: Output only. Delivery setting associated
      with the membership.
    TypeValueValuesEnum: Output only. The type of the membership.

  Fields:
    createTime: Output only. The time when the `Membership` was created.
    deliverySetting: Output only. Delivery setting associated with the
      membership.
    expiryDetail: The expiry details of the `MembershipRole`. May be set if
      the only `MembershipRole` is one with `name` `MEMBER`. Must not be set
      if any other `MembershipRole`s exist.
    name: Output only. The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      `Membership`. Shall be of the form
      `groups/{group_id}/memberships/{membership_id}`.
    preferredMemberKey: Required. Immutable. The `EntityKey` of the member.
    roles: The `MembershipRole`s that apply to the `Membership`. If
      unspecified, defaults to a single `MembershipRole` with `name` `MEMBER`
      and no `expiry_detail`. Must not contain duplicate `MembershipRole`s
      with the same `name`.
    type: Output only. The type of the membership.
    updateTime: Output only. The time when the `Membership` was last updated.
  """

  class DeliverySettingValueValuesEnum(_messages.Enum):
    r"""Output only. Delivery setting associated with the membership.

    Values:
      DELIVERY_SETTING_UNSPECIFIED: Default. Should not be used.
      ALL_MAIL: Represents each mail should be delivered
      DIGEST: Represents 1 email for every 25 messages.
      DAILY: Represents daily summary of messages.
      NONE: Represents no delivery.
      DISABLED: Represents disabled state.
    """
    DELIVERY_SETTING_UNSPECIFIED = 0
    ALL_MAIL = 1
    DIGEST = 2
    DAILY = 3
    NONE = 4
    DISABLED = 5

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the membership.

    Values:
      TYPE_UNSPECIFIED: Default. Should not be used.
      USER: Represents user type.
      SERVICE_ACCOUNT: Represents service account type.
      GROUP: Represents group type.
      SHARED_DRIVE: Represents Shared drive.
      CBCM_BROWSER: Represents a CBCM-managed Chrome Browser type.
      OTHER: Represents other type.
    """
    TYPE_UNSPECIFIED = 0
    USER = 1
    SERVICE_ACCOUNT = 2
    GROUP = 3
    SHARED_DRIVE = 4
    CBCM_BROWSER = 5
    OTHER = 6

  createTime = _messages.StringField(1)
  deliverySetting = _messages.EnumField('DeliverySettingValueValuesEnum', 2)
  expiryDetail = _messages.MessageField('ExpiryDetail', 3)
  name = _messages.StringField(4)
  preferredMemberKey = _messages.MessageField('EntityKey', 5)
  roles = _messages.MessageField('MembershipRole', 6, repeated=True)
  type = _messages.EnumField('TypeValueValuesEnum', 7)
  updateTime = _messages.StringField(8)


class MembershipAdjacencyList(_messages.Message):
  r"""Membership graph's path information as an adjacency list.

  Fields:
    edges: Each edge contains information about the member that belongs to
      this group.
    group: Resource name of the group that the members belong to.
  """

  edges = _messages.MessageField('Membership', 1, repeated=True)
  group = _messages.StringField(2)


class MembershipRelation(_messages.Message):
  r"""Message containing membership relation.

  Messages:
    LabelsValue: One or more label entries that apply to the Group. Currently
      supported labels contain a key with an empty value.

  Fields:
    description: An extended description to help users determine the purpose
      of a `Group`.
    displayName: The display name of the `Group`.
    group: The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      `Group`. Shall be of the form `groups/{group_id}`.
    groupKey: The `EntityKey` of the `Group`.
    labels: One or more label entries that apply to the Group. Currently
      supported labels contain a key with an empty value.
    membership: The [resource
      name](https://cloud.google.com/apis/design/resource_names) of the
      `Membership`. Shall be of the form
      `groups/{group_id}/memberships/{membership_id}`.
    roles: The `MembershipRole`s that apply to the `Membership`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""One or more label entries that apply to the Group. Currently supported
    labels contain a key with an empty value.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  group = _messages.StringField(3)
  groupKey = _messages.MessageField('EntityKey', 4)
  labels = _messages.MessageField('LabelsValue', 5)
  membership = _messages.StringField(6)
  roles = _messages.MessageField('MembershipRole', 7, repeated=True)


class MembershipRole(_messages.Message):
  r"""A membership role within the Cloud Identity Groups API. A
  `MembershipRole` defines the privileges granted to a `Membership`.

  Fields:
    expiryDetail: The expiry details of the `MembershipRole`. Expiry details
      are only supported for `MEMBER` `MembershipRoles`. May be set if `name`
      is `MEMBER`. Must not be set if `name` is any other value.
    name: A string attribute.
    restrictionEvaluations: Evaluations of restrictions applied to parent
      group on this membership.
  """

  expiryDetail = _messages.MessageField('MembershipRoleExpiryDetail', 1)
  name = _messages.StringField(2)
  restrictionEvaluations = _messages.MessageField('RestrictionEvaluations', 3)


class MembershipRoleExpiryDetail(_messages.Message):
  r"""The `MembershipRole` expiry details.

  Fields:
    expireTime: The time at which the `MembershipRole` will expire.
  """

  expireTime = _messages.StringField(1)


class MembershipRoleRestrictionEvaluation(_messages.Message):
  r"""The evaluated state of this restriction.

  Enums:
    StateValueValuesEnum: Output only. The current state of the restriction

  Fields:
    state: Output only. The current state of the restriction
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the restriction

    Values:
      STATE_UNSPECIFIED: Default. Should not be used.
      COMPLIANT: The member adheres to the parent group's restriction.
      FORWARD_COMPLIANT: The group-group membership might be currently
        violating some parent group's restriction but in future, it will never
        allow any new member in the child group which can violate parent
        group's restriction.
      NON_COMPLIANT: The member violates the parent group's restriction.
      EVALUATING: The state of the membership is under evaluation.
    """
    STATE_UNSPECIFIED = 0
    COMPLIANT = 1
    FORWARD_COMPLIANT = 2
    NON_COMPLIANT = 3
    EVALUATING = 4

  state = _messages.EnumField('StateValueValuesEnum', 1)


class ModifyMembershipRolesRequest(_messages.Message):
  r"""The request message for MembershipsService.ModifyMembershipRoles.

  Fields:
    addRoles: The `MembershipRole`s to be added. Adding or removing roles in
      the same request as updating roles is not supported. Must not be set if
      `update_roles_params` is set.
    removeRoles: The `name`s of the `MembershipRole`s to be removed. Adding or
      removing roles in the same request as updating roles is not supported.
      It is not possible to remove the `MEMBER` `MembershipRole`. If you wish
      to delete a `Membership`, call MembershipsService.DeleteMembership
      instead. Must not contain `MEMBER`. Must not be set if
      `update_roles_params` is set.
    updateRolesParams: (== disable_replacement_words add_roles,remove_roles
      Updating roles in the same request as adding or removing roles is not
      supported. Must not be set if either `add_roles` or `remove_roles` is
      set.
  """

  addRoles = _messages.MessageField('MembershipRole', 1, repeated=True)
  removeRoles = _messages.StringField(2, repeated=True)
  updateRolesParams = _messages.MessageField('UpdateMembershipRolesParams', 3, repeated=True)


class ModifyMembershipRolesResponse(_messages.Message):
  r"""The response message for MembershipsService.ModifyMembershipRoles.

  Fields:
    membership: The `Membership` resource after modifying its
      `MembershipRole`s.
  """

  membership = _messages.MessageField('Membership', 1)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class PosixGroup(_messages.Message):
  r"""POSIX Group definition to represent a group in linux.

  Fields:
    gid: GID of the POSIX group.
    name: Name of the POSIX group.
    systemId: System identifier for which group name and gid apply to. If not
      specified it will default to empty value.
  """

  gid = _messages.IntegerField(1, variant=_messages.Variant.UINT64)
  name = _messages.StringField(2)
  systemId = _messages.StringField(3)


class RestrictionEvaluations(_messages.Message):
  r"""Evaluations of restrictions applied to parent group on this membership.

  Fields:
    memberRestrictionEvaluation: Evaluation of the member restriction applied
      to this membership. Empty if the user lacks permission to view the
      restriction evaluation.
  """

  memberRestrictionEvaluation = _messages.MessageField('MembershipRoleRestrictionEvaluation', 1)


class SearchDirectGroupsResponse(_messages.Message):
  r"""The response message for MembershipsService.SearchDirectGroups.

  Fields:
    memberships: List of direct groups satisfying the query.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results available for listing.
  """

  memberships = _messages.MessageField('MembershipRelation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class SearchGroupsResponse(_messages.Message):
  r"""The response message for GroupsService.SearchGroups.

  Fields:
    groups: The `Group` resources that match the search query.
    nextPageToken: A continuation token to retrieve the next page of results,
      or empty if there are no more results available.
  """

  groups = _messages.MessageField('Group', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class SearchTransitiveGroupsResponse(_messages.Message):
  r"""The response message for MembershipsService.SearchTransitiveGroups.

  Fields:
    memberships: List of transitive groups satisfying the query.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results available for listing.
  """

  memberships = _messages.MessageField('GroupRelation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class SearchTransitiveMembershipsResponse(_messages.Message):
  r"""The response message for MembershipsService.SearchTransitiveMemberships.

  Fields:
    memberships: List of transitive memberships satisfying the query.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results.
  """

  memberships = _messages.MessageField('MemberRelation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TransitiveMembershipRole(_messages.Message):
  r"""Message representing the role of a TransitiveMembership.

  Fields:
    role: TransitiveMembershipRole in string format. Currently supported
      TransitiveMembershipRoles: `"MEMBER"`, `"OWNER"`, and `"MANAGER"`.
  """

  role = _messages.StringField(1)


class UpdateMembershipRolesParams(_messages.Message):
  r"""The details of an update to a `MembershipRole`.

  Fields:
    fieldMask: The fully-qualified names of fields to update. May only contain
      the field `expiry_detail.expire_time`.
    membershipRole: The `MembershipRole`s to be updated. Only `MEMBER`
      `MembershipRoles` can currently be updated. May only contain a
      `MembershipRole` with `name` `MEMBER`.
  """

  fieldMask = _messages.StringField(1)
  membershipRole = _messages.MessageField('MembershipRole', 2)


class WipeDeviceResponse(_messages.Message):
  r"""Response message for wiping all data on the device.

  Fields:
    device: Resultant Device object for the action. Note that asset tags will
      not be returned in the device object.
  """

  device = _messages.MessageField('Device', 1)


class WipeDeviceUserResponse(_messages.Message):
  r"""Response message for wiping the user's account from the device.

  Fields:
    deviceUser: Resultant DeviceUser object for the action.
  """

  deviceUser = _messages.MessageField('DeviceUser', 1)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    CloudidentityGroupsLookupRequest, 'groupKey_id', 'groupKey.id')
encoding.AddCustomJsonFieldMapping(
    CloudidentityGroupsLookupRequest, 'groupKey_namespace', 'groupKey.namespace')
encoding.AddCustomJsonFieldMapping(
    CloudidentityGroupsMembershipsLookupRequest, 'memberKey_id', 'memberKey.id')
encoding.AddCustomJsonFieldMapping(
    CloudidentityGroupsMembershipsLookupRequest, 'memberKey_namespace', 'memberKey.namespace')
