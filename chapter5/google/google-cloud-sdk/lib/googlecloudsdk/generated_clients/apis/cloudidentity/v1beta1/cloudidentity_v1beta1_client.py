"""Generated client library for cloudidentity version v1beta1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.cloudidentity.v1beta1 import cloudidentity_v1beta1_messages as messages


class CloudidentityV1beta1(base_api.BaseApiClient):
  """Generated client library for service cloudidentity version v1beta1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://cloudidentity.googleapis.com/'
  MTLS_BASE_URL = 'https://cloudidentity.mtls.googleapis.com/'

  _PACKAGE = 'cloudidentity'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-identity.devices', 'https://www.googleapis.com/auth/cloud-identity.devices.lookup', 'https://www.googleapis.com/auth/cloud-identity.devices.readonly', 'https://www.googleapis.com/auth/cloud-identity.groups', 'https://www.googleapis.com/auth/cloud-identity.groups.readonly', 'https://www.googleapis.com/auth/cloud-identity.inboundsso', 'https://www.googleapis.com/auth/cloud-identity.inboundsso.readonly', 'https://www.googleapis.com/auth/cloud-identity.orgunits', 'https://www.googleapis.com/auth/cloud-identity.orgunits.readonly', 'https://www.googleapis.com/auth/cloud-identity.policies', 'https://www.googleapis.com/auth/cloud-identity.policies.readonly', 'https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1beta1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'CloudidentityV1beta1'
  _URL_VERSION = 'v1beta1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new cloudidentity handle."""
    url = url or self.BASE_URL
    super(CloudidentityV1beta1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.customers_userinvitations = self.CustomersUserinvitationsService(self)
    self.customers = self.CustomersService(self)
    self.devices_deviceUsers_clientStates = self.DevicesDeviceUsersClientStatesService(self)
    self.devices_deviceUsers = self.DevicesDeviceUsersService(self)
    self.devices = self.DevicesService(self)
    self.groups_memberships = self.GroupsMembershipsService(self)
    self.groups = self.GroupsService(self)
    self.inboundSamlSsoProfiles_idpCredentials = self.InboundSamlSsoProfilesIdpCredentialsService(self)
    self.inboundSamlSsoProfiles = self.InboundSamlSsoProfilesService(self)
    self.inboundSsoAssignments = self.InboundSsoAssignmentsService(self)
    self.orgUnits_memberships = self.OrgUnitsMembershipsService(self)
    self.orgUnits = self.OrgUnitsService(self)
    self.policies = self.PoliciesService(self)

  class CustomersUserinvitationsService(base_api.BaseApiService):
    """Service class for the customers_userinvitations resource."""

    _NAME = 'customers_userinvitations'

    def __init__(self, client):
      super(CloudidentityV1beta1.CustomersUserinvitationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancels a UserInvitation that was already sent.

      Args:
        request: (CloudidentityCustomersUserinvitationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/customers/{customersId}/userinvitations/{userinvitationsId}:cancel',
        http_method='POST',
        method_id='cloudidentity.customers.userinvitations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:cancel',
        request_field='cancelUserInvitationRequest',
        request_type_name='CloudidentityCustomersUserinvitationsCancelRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a UserInvitation resource. **Note:** New consumer accounts with the customer's verified domain created within the previous 48 hours will not appear in the result. This delay also applies to newly-verified domains.

      Args:
        request: (CloudidentityCustomersUserinvitationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (UserInvitation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/customers/{customersId}/userinvitations/{userinvitationsId}',
        http_method='GET',
        method_id='cloudidentity.customers.userinvitations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='CloudidentityCustomersUserinvitationsGetRequest',
        response_type_name='UserInvitation',
        supports_download=False,
    )

    def IsInvitableUser(self, request, global_params=None):
      r"""Verifies whether a user account is eligible to receive a UserInvitation (is an unmanaged account). Eligibility is based on the following criteria: * the email address is a consumer account and it's the primary email address of the account, and * the domain of the email address matches an existing verified Google Workspace or Cloud Identity domain If both conditions are met, the user is eligible. **Note:** This method is not supported for Workspace Essentials customers.

      Args:
        request: (CloudidentityCustomersUserinvitationsIsInvitableUserRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (IsInvitableUserResponse) The response message.
      """
      config = self.GetMethodConfig('IsInvitableUser')
      return self._RunMethod(
          config, request, global_params=global_params)

    IsInvitableUser.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/customers/{customersId}/userinvitations/{userinvitationsId}:isInvitableUser',
        http_method='GET',
        method_id='cloudidentity.customers.userinvitations.isInvitableUser',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:isInvitableUser',
        request_field='',
        request_type_name='CloudidentityCustomersUserinvitationsIsInvitableUserRequest',
        response_type_name='IsInvitableUserResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Retrieves a list of UserInvitation resources. **Note:** New consumer accounts with the customer's verified domain created within the previous 48 hours will not appear in the result. This delay also applies to newly-verified domains.

      Args:
        request: (CloudidentityCustomersUserinvitationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListUserInvitationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/customers/{customersId}/userinvitations',
        http_method='GET',
        method_id='cloudidentity.customers.userinvitations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/userinvitations',
        request_field='',
        request_type_name='CloudidentityCustomersUserinvitationsListRequest',
        response_type_name='ListUserInvitationsResponse',
        supports_download=False,
    )

    def Send(self, request, global_params=None):
      r"""Sends a UserInvitation to email. If the `UserInvitation` does not exist for this request and it is a valid request, the request creates a `UserInvitation`. **Note:** The `get` and `list` methods have a 48-hour delay where newly-created consumer accounts will not appear in the results. You can still send a `UserInvitation` to those accounts if you know the unmanaged email address and IsInvitableUser==True.

      Args:
        request: (CloudidentityCustomersUserinvitationsSendRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Send')
      return self._RunMethod(
          config, request, global_params=global_params)

    Send.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/customers/{customersId}/userinvitations/{userinvitationsId}:send',
        http_method='POST',
        method_id='cloudidentity.customers.userinvitations.send',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:send',
        request_field='sendUserInvitationRequest',
        request_type_name='CloudidentityCustomersUserinvitationsSendRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class CustomersService(base_api.BaseApiService):
    """Service class for the customers resource."""

    _NAME = 'customers'

    def __init__(self, client):
      super(CloudidentityV1beta1.CustomersService, self).__init__(client)
      self._upload_configs = {
          }

  class DevicesDeviceUsersClientStatesService(base_api.BaseApiService):
    """Service class for the devices_deviceUsers_clientStates resource."""

    _NAME = 'devices_deviceUsers_clientStates'

    def __init__(self, client):
      super(CloudidentityV1beta1.DevicesDeviceUsersClientStatesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the client state for the device user.

      Args:
        request: (CloudidentityDevicesDeviceUsersClientStatesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ClientState) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/devices/{devicesId}/deviceUsers/{deviceUsersId}/clientStates/{clientStatesId}',
        http_method='GET',
        method_id='cloudidentity.devices.deviceUsers.clientStates.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['customer'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='CloudidentityDevicesDeviceUsersClientStatesGetRequest',
        response_type_name='ClientState',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the client state for the device user **Note**: This method is available only to customers who have one of the following SKUs: Enterprise Standard, Enterprise Plus, Enterprise for Education, and Cloud Identity Premium.

      Args:
        request: (CloudidentityDevicesDeviceUsersClientStatesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/devices/{devicesId}/deviceUsers/{deviceUsersId}/clientStates/{clientStatesId}',
        http_method='PATCH',
        method_id='cloudidentity.devices.deviceUsers.clientStates.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['customer', 'updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='clientState',
        request_type_name='CloudidentityDevicesDeviceUsersClientStatesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class DevicesDeviceUsersService(base_api.BaseApiService):
    """Service class for the devices_deviceUsers resource."""

    _NAME = 'devices_deviceUsers'

    def __init__(self, client):
      super(CloudidentityV1beta1.DevicesDeviceUsersService, self).__init__(client)
      self._upload_configs = {
          }

    def Approve(self, request, global_params=None):
      r"""Approves device to access user data.

      Args:
        request: (CloudidentityDevicesDeviceUsersApproveRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Approve')
      return self._RunMethod(
          config, request, global_params=global_params)

    Approve.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/devices/{devicesId}/deviceUsers/{deviceUsersId}:approve',
        http_method='POST',
        method_id='cloudidentity.devices.deviceUsers.approve',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:approve',
        request_field='approveDeviceUserRequest',
        request_type_name='CloudidentityDevicesDeviceUsersApproveRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Block(self, request, global_params=None):
      r"""Blocks device from accessing user data.

      Args:
        request: (CloudidentityDevicesDeviceUsersBlockRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Block')
      return self._RunMethod(
          config, request, global_params=global_params)

    Block.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/devices/{devicesId}/deviceUsers/{deviceUsersId}:block',
        http_method='POST',
        method_id='cloudidentity.devices.deviceUsers.block',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:block',
        request_field='blockDeviceUserRequest',
        request_type_name='CloudidentityDevicesDeviceUsersBlockRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def CancelWipe(self, request, global_params=None):
      r"""Cancels an unfinished user account wipe. This operation can be used to cancel device wipe in the gap between the wipe operation returning success and the device being wiped.

      Args:
        request: (CloudidentityDevicesDeviceUsersCancelWipeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('CancelWipe')
      return self._RunMethod(
          config, request, global_params=global_params)

    CancelWipe.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/devices/{devicesId}/deviceUsers/{deviceUsersId}:cancelWipe',
        http_method='POST',
        method_id='cloudidentity.devices.deviceUsers.cancelWipe',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:cancelWipe',
        request_field='cancelWipeDeviceUserRequest',
        request_type_name='CloudidentityDevicesDeviceUsersCancelWipeRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified DeviceUser. This also revokes the user's access to device data.

      Args:
        request: (CloudidentityDevicesDeviceUsersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/devices/{devicesId}/deviceUsers/{deviceUsersId}',
        http_method='DELETE',
        method_id='cloudidentity.devices.deviceUsers.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['customer'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='CloudidentityDevicesDeviceUsersDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified DeviceUser.

      Args:
        request: (CloudidentityDevicesDeviceUsersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DeviceUser) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/devices/{devicesId}/deviceUsers/{deviceUsersId}',
        http_method='GET',
        method_id='cloudidentity.devices.deviceUsers.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['customer'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='CloudidentityDevicesDeviceUsersGetRequest',
        response_type_name='DeviceUser',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists/Searches DeviceUsers.

      Args:
        request: (CloudidentityDevicesDeviceUsersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDeviceUsersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/devices/{devicesId}/deviceUsers',
        http_method='GET',
        method_id='cloudidentity.devices.deviceUsers.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['customer', 'filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/deviceUsers',
        request_field='',
        request_type_name='CloudidentityDevicesDeviceUsersListRequest',
        response_type_name='ListDeviceUsersResponse',
        supports_download=False,
    )

    def Lookup(self, request, global_params=None):
      r"""Looks up resource names of the DeviceUsers associated with the caller's credentials, as well as the properties provided in the request. This method must be called with end-user credentials with the scope: https://www.googleapis.com/auth/cloud-identity.devices.lookup If multiple properties are provided, only DeviceUsers having all of these properties are considered as matches - i.e. the query behaves like an AND. Different platforms require different amounts of information from the caller to ensure that the DeviceUser is uniquely identified. - iOS: No properties need to be passed, the caller's credentials are sufficient to identify the corresponding DeviceUser. - Android: Specifying the 'android_id' field is required. - Desktop: Specifying the 'raw_resource_id' field is required.

      Args:
        request: (CloudidentityDevicesDeviceUsersLookupRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LookupSelfDeviceUsersResponse) The response message.
      """
      config = self.GetMethodConfig('Lookup')
      return self._RunMethod(
          config, request, global_params=global_params)

    Lookup.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/devices/{devicesId}/deviceUsers:lookup',
        http_method='GET',
        method_id='cloudidentity.devices.deviceUsers.lookup',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['androidId', 'pageSize', 'pageToken', 'rawResourceId', 'userId'],
        relative_path='v1beta1/{+parent}:lookup',
        request_field='',
        request_type_name='CloudidentityDevicesDeviceUsersLookupRequest',
        response_type_name='LookupSelfDeviceUsersResponse',
        supports_download=False,
    )

    def Wipe(self, request, global_params=None):
      r"""Wipes the user's account on a device.

      Args:
        request: (CloudidentityDevicesDeviceUsersWipeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Wipe')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wipe.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/devices/{devicesId}/deviceUsers/{deviceUsersId}:wipe',
        http_method='POST',
        method_id='cloudidentity.devices.deviceUsers.wipe',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:wipe',
        request_field='wipeDeviceUserRequest',
        request_type_name='CloudidentityDevicesDeviceUsersWipeRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class DevicesService(base_api.BaseApiService):
    """Service class for the devices resource."""

    _NAME = 'devices'

    def __init__(self, client):
      super(CloudidentityV1beta1.DevicesService, self).__init__(client)
      self._upload_configs = {
          }

    def CancelWipe(self, request, global_params=None):
      r"""Cancels an unfinished device wipe. This operation can be used to cancel device wipe in the gap between the wipe operation returning success and the device being wiped.

      Args:
        request: (CloudidentityDevicesCancelWipeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('CancelWipe')
      return self._RunMethod(
          config, request, global_params=global_params)

    CancelWipe.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/devices/{devicesId}:cancelWipe',
        http_method='POST',
        method_id='cloudidentity.devices.cancelWipe',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:cancelWipe',
        request_field='cancelWipeDeviceRequest',
        request_type_name='CloudidentityDevicesCancelWipeRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a device. Only company-owned device may be created. **Note**: This method is available only to customers who have one of the following SKUs: Enterprise Standard, Enterprise Plus, Enterprise for Education, and Cloud Identity Premium.

      Args:
        request: (CreateDeviceRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='cloudidentity.devices.create',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v1beta1/devices',
        request_field='<request>',
        request_type_name='CreateDeviceRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified device.

      Args:
        request: (CloudidentityDevicesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/devices/{devicesId}',
        http_method='DELETE',
        method_id='cloudidentity.devices.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['customer'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='CloudidentityDevicesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves the specified device.

      Args:
        request: (CloudidentityDevicesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Device) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/devices/{devicesId}',
        http_method='GET',
        method_id='cloudidentity.devices.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['customer'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='CloudidentityDevicesGetRequest',
        response_type_name='Device',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists/Searches devices.

      Args:
        request: (CloudidentityDevicesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDevicesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='cloudidentity.devices.list',
        ordered_params=[],
        path_params=[],
        query_params=['customer', 'filter', 'orderBy', 'pageSize', 'pageToken', 'view'],
        relative_path='v1beta1/devices',
        request_field='',
        request_type_name='CloudidentityDevicesListRequest',
        response_type_name='ListDevicesResponse',
        supports_download=False,
    )

    def Wipe(self, request, global_params=None):
      r"""Wipes all data on the specified device.

      Args:
        request: (CloudidentityDevicesWipeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Wipe')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wipe.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/devices/{devicesId}:wipe',
        http_method='POST',
        method_id='cloudidentity.devices.wipe',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:wipe',
        request_field='wipeDeviceRequest',
        request_type_name='CloudidentityDevicesWipeRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class GroupsMembershipsService(base_api.BaseApiService):
    """Service class for the groups_memberships resource."""

    _NAME = 'groups_memberships'

    def __init__(self, client):
      super(CloudidentityV1beta1.GroupsMembershipsService, self).__init__(client)
      self._upload_configs = {
          }

    def CheckTransitiveMembership(self, request, global_params=None):
      r"""Check a potential member for membership in a group. **Note:** This feature is only available to Google Workspace Enterprise Standard, Enterprise Plus, and Enterprise for Education; and Cloud Identity Premium accounts. A member has membership to a group as long as there is a single viewable transitive membership between the group and the member. The actor must have view permissions to at least one transitive membership between the member and group.

      Args:
        request: (CloudidentityGroupsMembershipsCheckTransitiveMembershipRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CheckTransitiveMembershipResponse) The response message.
      """
      config = self.GetMethodConfig('CheckTransitiveMembership')
      return self._RunMethod(
          config, request, global_params=global_params)

    CheckTransitiveMembership.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/groups/{groupsId}/memberships:checkTransitiveMembership',
        http_method='GET',
        method_id='cloudidentity.groups.memberships.checkTransitiveMembership',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['query'],
        relative_path='v1beta1/{+parent}/memberships:checkTransitiveMembership',
        request_field='',
        request_type_name='CloudidentityGroupsMembershipsCheckTransitiveMembershipRequest',
        response_type_name='CheckTransitiveMembershipResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a `Membership`.

      Args:
        request: (CloudidentityGroupsMembershipsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/groups/{groupsId}/memberships',
        http_method='POST',
        method_id='cloudidentity.groups.memberships.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/memberships',
        request_field='membership',
        request_type_name='CloudidentityGroupsMembershipsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a `Membership`.

      Args:
        request: (CloudidentityGroupsMembershipsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/groups/{groupsId}/memberships/{membershipsId}',
        http_method='DELETE',
        method_id='cloudidentity.groups.memberships.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='CloudidentityGroupsMembershipsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a `Membership`.

      Args:
        request: (CloudidentityGroupsMembershipsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Membership) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/groups/{groupsId}/memberships/{membershipsId}',
        http_method='GET',
        method_id='cloudidentity.groups.memberships.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='CloudidentityGroupsMembershipsGetRequest',
        response_type_name='Membership',
        supports_download=False,
    )

    def GetMembershipGraph(self, request, global_params=None):
      r"""Get a membership graph of just a member or both a member and a group. **Note:** This feature is only available to Google Workspace Enterprise Standard, Enterprise Plus, and Enterprise for Education; and Cloud Identity Premium accounts. Given a member, the response will contain all membership paths from the member. Given both a group and a member, the response will contain all membership paths between the group and the member.

      Args:
        request: (CloudidentityGroupsMembershipsGetMembershipGraphRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('GetMembershipGraph')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetMembershipGraph.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/groups/{groupsId}/memberships:getMembershipGraph',
        http_method='GET',
        method_id='cloudidentity.groups.memberships.getMembershipGraph',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['query'],
        relative_path='v1beta1/{+parent}/memberships:getMembershipGraph',
        request_field='',
        request_type_name='CloudidentityGroupsMembershipsGetMembershipGraphRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the `Membership`s within a `Group`.

      Args:
        request: (CloudidentityGroupsMembershipsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMembershipsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/groups/{groupsId}/memberships',
        http_method='GET',
        method_id='cloudidentity.groups.memberships.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'view'],
        relative_path='v1beta1/{+parent}/memberships',
        request_field='',
        request_type_name='CloudidentityGroupsMembershipsListRequest',
        response_type_name='ListMembershipsResponse',
        supports_download=False,
    )

    def Lookup(self, request, global_params=None):
      r"""Looks up the [resource name](https://cloud.google.com/apis/design/resource_names) of a `Membership` by its `EntityKey`.

      Args:
        request: (CloudidentityGroupsMembershipsLookupRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LookupMembershipNameResponse) The response message.
      """
      config = self.GetMethodConfig('Lookup')
      return self._RunMethod(
          config, request, global_params=global_params)

    Lookup.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/groups/{groupsId}/memberships:lookup',
        http_method='GET',
        method_id='cloudidentity.groups.memberships.lookup',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['memberKey_id', 'memberKey_namespace'],
        relative_path='v1beta1/{+parent}/memberships:lookup',
        request_field='',
        request_type_name='CloudidentityGroupsMembershipsLookupRequest',
        response_type_name='LookupMembershipNameResponse',
        supports_download=False,
    )

    def ModifyMembershipRoles(self, request, global_params=None):
      r"""Modifies the `MembershipRole`s of a `Membership`.

      Args:
        request: (CloudidentityGroupsMembershipsModifyMembershipRolesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ModifyMembershipRolesResponse) The response message.
      """
      config = self.GetMethodConfig('ModifyMembershipRoles')
      return self._RunMethod(
          config, request, global_params=global_params)

    ModifyMembershipRoles.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/groups/{groupsId}/memberships/{membershipsId}:modifyMembershipRoles',
        http_method='POST',
        method_id='cloudidentity.groups.memberships.modifyMembershipRoles',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:modifyMembershipRoles',
        request_field='modifyMembershipRolesRequest',
        request_type_name='CloudidentityGroupsMembershipsModifyMembershipRolesRequest',
        response_type_name='ModifyMembershipRolesResponse',
        supports_download=False,
    )

    def SearchDirectGroups(self, request, global_params=None):
      r"""Searches direct groups of a member.

      Args:
        request: (CloudidentityGroupsMembershipsSearchDirectGroupsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SearchDirectGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('SearchDirectGroups')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchDirectGroups.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/groups/{groupsId}/memberships:searchDirectGroups',
        http_method='GET',
        method_id='cloudidentity.groups.memberships.searchDirectGroups',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['orderBy', 'pageSize', 'pageToken', 'query'],
        relative_path='v1beta1/{+parent}/memberships:searchDirectGroups',
        request_field='',
        request_type_name='CloudidentityGroupsMembershipsSearchDirectGroupsRequest',
        response_type_name='SearchDirectGroupsResponse',
        supports_download=False,
    )

    def SearchTransitiveGroups(self, request, global_params=None):
      r"""Search transitive groups of a member. **Note:** This feature is only available to Google Workspace Enterprise Standard, Enterprise Plus, and Enterprise for Education; and Cloud Identity Premium accounts. A transitive group is any group that has a direct or indirect membership to the member. Actor must have view permissions all transitive groups.

      Args:
        request: (CloudidentityGroupsMembershipsSearchTransitiveGroupsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SearchTransitiveGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('SearchTransitiveGroups')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchTransitiveGroups.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/groups/{groupsId}/memberships:searchTransitiveGroups',
        http_method='GET',
        method_id='cloudidentity.groups.memberships.searchTransitiveGroups',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'query'],
        relative_path='v1beta1/{+parent}/memberships:searchTransitiveGroups',
        request_field='',
        request_type_name='CloudidentityGroupsMembershipsSearchTransitiveGroupsRequest',
        response_type_name='SearchTransitiveGroupsResponse',
        supports_download=False,
    )

    def SearchTransitiveMemberships(self, request, global_params=None):
      r"""Search transitive memberships of a group. **Note:** This feature is only available to Google Workspace Enterprise Standard, Enterprise Plus, and Enterprise for Education; and Cloud Identity Premium accounts. A transitive membership is any direct or indirect membership of a group. Actor must have view permissions to all transitive memberships.

      Args:
        request: (CloudidentityGroupsMembershipsSearchTransitiveMembershipsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SearchTransitiveMembershipsResponse) The response message.
      """
      config = self.GetMethodConfig('SearchTransitiveMemberships')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchTransitiveMemberships.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/groups/{groupsId}/memberships:searchTransitiveMemberships',
        http_method='GET',
        method_id='cloudidentity.groups.memberships.searchTransitiveMemberships',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/memberships:searchTransitiveMemberships',
        request_field='',
        request_type_name='CloudidentityGroupsMembershipsSearchTransitiveMembershipsRequest',
        response_type_name='SearchTransitiveMembershipsResponse',
        supports_download=False,
    )

  class GroupsService(base_api.BaseApiService):
    """Service class for the groups resource."""

    _NAME = 'groups'

    def __init__(self, client):
      super(CloudidentityV1beta1.GroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a `Group`.

      Args:
        request: (CloudidentityGroupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='cloudidentity.groups.create',
        ordered_params=[],
        path_params=[],
        query_params=['initialGroupConfig'],
        relative_path='v1beta1/groups',
        request_field='group',
        request_type_name='CloudidentityGroupsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a `Group`.

      Args:
        request: (CloudidentityGroupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/groups/{groupsId}',
        http_method='DELETE',
        method_id='cloudidentity.groups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='CloudidentityGroupsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a `Group`.

      Args:
        request: (CloudidentityGroupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Group) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/groups/{groupsId}',
        http_method='GET',
        method_id='cloudidentity.groups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='CloudidentityGroupsGetRequest',
        response_type_name='Group',
        supports_download=False,
    )

    def GetSecuritySettings(self, request, global_params=None):
      r"""Get Security Settings.

      Args:
        request: (CloudidentityGroupsGetSecuritySettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecuritySettings) The response message.
      """
      config = self.GetMethodConfig('GetSecuritySettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetSecuritySettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/groups/{groupsId}/securitySettings',
        http_method='GET',
        method_id='cloudidentity.groups.getSecuritySettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['readMask'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='CloudidentityGroupsGetSecuritySettingsRequest',
        response_type_name='SecuritySettings',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the `Group` resources under a customer or namespace.

      Args:
        request: (CloudidentityGroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='cloudidentity.groups.list',
        ordered_params=[],
        path_params=[],
        query_params=['pageSize', 'pageToken', 'parent', 'view'],
        relative_path='v1beta1/groups',
        request_field='',
        request_type_name='CloudidentityGroupsListRequest',
        response_type_name='ListGroupsResponse',
        supports_download=False,
    )

    def Lookup(self, request, global_params=None):
      r"""Looks up the [resource name](https://cloud.google.com/apis/design/resource_names) of a `Group` by its `EntityKey`.

      Args:
        request: (CloudidentityGroupsLookupRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LookupGroupNameResponse) The response message.
      """
      config = self.GetMethodConfig('Lookup')
      return self._RunMethod(
          config, request, global_params=global_params)

    Lookup.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='cloudidentity.groups.lookup',
        ordered_params=[],
        path_params=[],
        query_params=['groupKey_id', 'groupKey_namespace'],
        relative_path='v1beta1/groups:lookup',
        request_field='',
        request_type_name='CloudidentityGroupsLookupRequest',
        response_type_name='LookupGroupNameResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a `Group`.

      Args:
        request: (CloudidentityGroupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/groups/{groupsId}',
        http_method='PATCH',
        method_id='cloudidentity.groups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='group',
        request_type_name='CloudidentityGroupsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Search(self, request, global_params=None):
      r"""Searches for `Group` resources matching a specified query.

      Args:
        request: (CloudidentityGroupsSearchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SearchGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('Search')
      return self._RunMethod(
          config, request, global_params=global_params)

    Search.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='cloudidentity.groups.search',
        ordered_params=[],
        path_params=[],
        query_params=['orderBy', 'pageSize', 'pageToken', 'query', 'view'],
        relative_path='v1beta1/groups:search',
        request_field='',
        request_type_name='CloudidentityGroupsSearchRequest',
        response_type_name='SearchGroupsResponse',
        supports_download=False,
    )

    def UpdateSecuritySettings(self, request, global_params=None):
      r"""Update Security Settings.

      Args:
        request: (CloudidentityGroupsUpdateSecuritySettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('UpdateSecuritySettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSecuritySettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/groups/{groupsId}/securitySettings',
        http_method='PATCH',
        method_id='cloudidentity.groups.updateSecuritySettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='securitySettings',
        request_type_name='CloudidentityGroupsUpdateSecuritySettingsRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class InboundSamlSsoProfilesIdpCredentialsService(base_api.BaseApiService):
    """Service class for the inboundSamlSsoProfiles_idpCredentials resource."""

    _NAME = 'inboundSamlSsoProfiles_idpCredentials'

    def __init__(self, client):
      super(CloudidentityV1beta1.InboundSamlSsoProfilesIdpCredentialsService, self).__init__(client)
      self._upload_configs = {
          }

    def Add(self, request, global_params=None):
      r"""Adds an IdpCredential. Up to 2 credentials are allowed. When the target customer has enabled [Multi-party approval for sensitive actions](https://support.google.com/a/answer/13790448), the `Operation` in the response will have `"done": false`, it will not have a response, and the metadata will have `"state": "awaiting-multi-party-approval"`.

      Args:
        request: (CloudidentityInboundSamlSsoProfilesIdpCredentialsAddRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Add')
      return self._RunMethod(
          config, request, global_params=global_params)

    Add.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/inboundSamlSsoProfiles/{inboundSamlSsoProfilesId}/idpCredentials:add',
        http_method='POST',
        method_id='cloudidentity.inboundSamlSsoProfiles.idpCredentials.add',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/idpCredentials:add',
        request_field='addIdpCredentialRequest',
        request_type_name='CloudidentityInboundSamlSsoProfilesIdpCredentialsAddRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an IdpCredential.

      Args:
        request: (CloudidentityInboundSamlSsoProfilesIdpCredentialsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/inboundSamlSsoProfiles/{inboundSamlSsoProfilesId}/idpCredentials/{idpCredentialsId}',
        http_method='DELETE',
        method_id='cloudidentity.inboundSamlSsoProfiles.idpCredentials.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='CloudidentityInboundSamlSsoProfilesIdpCredentialsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an IdpCredential.

      Args:
        request: (CloudidentityInboundSamlSsoProfilesIdpCredentialsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (IdpCredential) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/inboundSamlSsoProfiles/{inboundSamlSsoProfilesId}/idpCredentials/{idpCredentialsId}',
        http_method='GET',
        method_id='cloudidentity.inboundSamlSsoProfiles.idpCredentials.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='CloudidentityInboundSamlSsoProfilesIdpCredentialsGetRequest',
        response_type_name='IdpCredential',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns a list of IdpCredentials in an InboundSamlSsoProfile.

      Args:
        request: (CloudidentityInboundSamlSsoProfilesIdpCredentialsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListIdpCredentialsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/inboundSamlSsoProfiles/{inboundSamlSsoProfilesId}/idpCredentials',
        http_method='GET',
        method_id='cloudidentity.inboundSamlSsoProfiles.idpCredentials.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/idpCredentials',
        request_field='',
        request_type_name='CloudidentityInboundSamlSsoProfilesIdpCredentialsListRequest',
        response_type_name='ListIdpCredentialsResponse',
        supports_download=False,
    )

  class InboundSamlSsoProfilesService(base_api.BaseApiService):
    """Service class for the inboundSamlSsoProfiles resource."""

    _NAME = 'inboundSamlSsoProfiles'

    def __init__(self, client):
      super(CloudidentityV1beta1.InboundSamlSsoProfilesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an InboundSamlSsoProfile for a customer. When the target customer has enabled [Multi-party approval for sensitive actions](https://support.google.com/a/answer/13790448), the `Operation` in the response will have `"done": false`, it will not have a response, and the metadata will have `"state": "awaiting-multi-party-approval"`.

      Args:
        request: (InboundSamlSsoProfile) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='cloudidentity.inboundSamlSsoProfiles.create',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v1beta1/inboundSamlSsoProfiles',
        request_field='<request>',
        request_type_name='InboundSamlSsoProfile',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an InboundSamlSsoProfile.

      Args:
        request: (CloudidentityInboundSamlSsoProfilesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/inboundSamlSsoProfiles/{inboundSamlSsoProfilesId}',
        http_method='DELETE',
        method_id='cloudidentity.inboundSamlSsoProfiles.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='CloudidentityInboundSamlSsoProfilesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an InboundSamlSsoProfile.

      Args:
        request: (CloudidentityInboundSamlSsoProfilesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (InboundSamlSsoProfile) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/inboundSamlSsoProfiles/{inboundSamlSsoProfilesId}',
        http_method='GET',
        method_id='cloudidentity.inboundSamlSsoProfiles.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='CloudidentityInboundSamlSsoProfilesGetRequest',
        response_type_name='InboundSamlSsoProfile',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists InboundSamlSsoProfiles for a customer.

      Args:
        request: (CloudidentityInboundSamlSsoProfilesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListInboundSamlSsoProfilesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='cloudidentity.inboundSamlSsoProfiles.list',
        ordered_params=[],
        path_params=[],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/inboundSamlSsoProfiles',
        request_field='',
        request_type_name='CloudidentityInboundSamlSsoProfilesListRequest',
        response_type_name='ListInboundSamlSsoProfilesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an InboundSamlSsoProfile. When the target customer has enabled [Multi-party approval for sensitive actions](https://support.google.com/a/answer/13790448), the `Operation` in the response will have `"done": false`, it will not have a response, and the metadata will have `"state": "awaiting-multi-party-approval"`.

      Args:
        request: (CloudidentityInboundSamlSsoProfilesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/inboundSamlSsoProfiles/{inboundSamlSsoProfilesId}',
        http_method='PATCH',
        method_id='cloudidentity.inboundSamlSsoProfiles.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='inboundSamlSsoProfile',
        request_type_name='CloudidentityInboundSamlSsoProfilesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class InboundSsoAssignmentsService(base_api.BaseApiService):
    """Service class for the inboundSsoAssignments resource."""

    _NAME = 'inboundSsoAssignments'

    def __init__(self, client):
      super(CloudidentityV1beta1.InboundSsoAssignmentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an InboundSsoAssignment for users and devices in a `Customer` under a given `Group` or `OrgUnit`.

      Args:
        request: (InboundSsoAssignment) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='cloudidentity.inboundSsoAssignments.create',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v1beta1/inboundSsoAssignments',
        request_field='<request>',
        request_type_name='InboundSsoAssignment',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an InboundSsoAssignment. To disable SSO, Create (or Update) an assignment that has `sso_mode` == `SSO_OFF`.

      Args:
        request: (CloudidentityInboundSsoAssignmentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/inboundSsoAssignments/{inboundSsoAssignmentsId}',
        http_method='DELETE',
        method_id='cloudidentity.inboundSsoAssignments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='CloudidentityInboundSsoAssignmentsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an InboundSsoAssignment.

      Args:
        request: (CloudidentityInboundSsoAssignmentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (InboundSsoAssignment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/inboundSsoAssignments/{inboundSsoAssignmentsId}',
        http_method='GET',
        method_id='cloudidentity.inboundSsoAssignments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='CloudidentityInboundSsoAssignmentsGetRequest',
        response_type_name='InboundSsoAssignment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the InboundSsoAssignments for a `Customer`.

      Args:
        request: (CloudidentityInboundSsoAssignmentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListInboundSsoAssignmentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='cloudidentity.inboundSsoAssignments.list',
        ordered_params=[],
        path_params=[],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/inboundSsoAssignments',
        request_field='',
        request_type_name='CloudidentityInboundSsoAssignmentsListRequest',
        response_type_name='ListInboundSsoAssignmentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an InboundSsoAssignment. The body of this request is the `inbound_sso_assignment` field and the `update_mask` is relative to that. For example: a PATCH to `/v1beta1/inboundSsoAssignments/0abcdefg1234567&update_mask=rank` with a body of `{ "rank": 1 }` moves that (presumably group-targeted) SSO assignment to the highest priority and shifts any other group-targeted assignments down in priority.

      Args:
        request: (CloudidentityInboundSsoAssignmentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/inboundSsoAssignments/{inboundSsoAssignmentsId}',
        http_method='PATCH',
        method_id='cloudidentity.inboundSsoAssignments.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='inboundSsoAssignment',
        request_type_name='CloudidentityInboundSsoAssignmentsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class OrgUnitsMembershipsService(base_api.BaseApiService):
    """Service class for the orgUnits_memberships resource."""

    _NAME = 'orgUnits_memberships'

    def __init__(self, client):
      super(CloudidentityV1beta1.OrgUnitsMembershipsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List OrgMembership resources in an OrgUnit treated as 'parent'. Parent format: orgUnits/{$orgUnitId} where `$orgUnitId` is the `orgUnitId` from the [Admin SDK `OrgUnit` resource](https://developers.google.com/admin-sdk/directory/reference/rest/v1/orgunits).

      Args:
        request: (CloudidentityOrgUnitsMembershipsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOrgMembershipsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/orgUnits/{orgUnitsId}/memberships',
        http_method='GET',
        method_id='cloudidentity.orgUnits.memberships.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['customer', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/memberships',
        request_field='',
        request_type_name='CloudidentityOrgUnitsMembershipsListRequest',
        response_type_name='ListOrgMembershipsResponse',
        supports_download=False,
    )

    def Move(self, request, global_params=None):
      r"""Move an OrgMembership to a new OrgUnit. NOTE: This is an atomic copy-and-delete. The resource will have a new copy under the destination OrgUnit and be deleted from the source OrgUnit. The resource can only be searched under the destination OrgUnit afterwards.

      Args:
        request: (CloudidentityOrgUnitsMembershipsMoveRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Move')
      return self._RunMethod(
          config, request, global_params=global_params)

    Move.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/orgUnits/{orgUnitsId}/memberships/{membershipsId}:move',
        http_method='POST',
        method_id='cloudidentity.orgUnits.memberships.move',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:move',
        request_field='moveOrgMembershipRequest',
        request_type_name='CloudidentityOrgUnitsMembershipsMoveRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class OrgUnitsService(base_api.BaseApiService):
    """Service class for the orgUnits resource."""

    _NAME = 'orgUnits'

    def __init__(self, client):
      super(CloudidentityV1beta1.OrgUnitsService, self).__init__(client)
      self._upload_configs = {
          }

  class PoliciesService(base_api.BaseApiService):
    """Service class for the policies resource."""

    _NAME = 'policies'

    def __init__(self, client):
      super(CloudidentityV1beta1.PoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get a Policy.

      Args:
        request: (CloudidentityPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/policies/{policiesId}',
        http_method='GET',
        method_id='cloudidentity.policies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='CloudidentityPoliciesGetRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List Policies.

      Args:
        request: (CloudidentityPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='cloudidentity.policies.list',
        ordered_params=[],
        path_params=[],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/policies',
        request_field='',
        request_type_name='CloudidentityPoliciesListRequest',
        response_type_name='ListPoliciesResponse',
        supports_download=False,
    )
