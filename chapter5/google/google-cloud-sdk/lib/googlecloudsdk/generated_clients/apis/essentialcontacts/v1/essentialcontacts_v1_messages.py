"""Generated message classes for essentialcontacts version v1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding


package = 'essentialcontacts'


class EssentialcontactsFoldersContactsComputeRequest(_messages.Message):
  r"""A EssentialcontactsFoldersContactsComputeRequest object.

  Enums:
    NotificationCategoriesValueValuesEnum: The categories of notifications to
      compute contacts for. If ALL is included in this list, contacts
      subscribed to any notification category will be returned.

  Fields:
    notificationCategories: The categories of notifications to compute
      contacts for. If <PERSON><PERSON> is included in this list, contacts subscribed to
      any notification category will be returned.
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of
      `next_page_token` in the response indicates that more results might be
      available. If not specified, the default page_size is 100.
    pageToken: Optional. If present, retrieves the next batch of results from
      the preceding call to this method. `page_token` must be the value of
      `next_page_token` from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The name of the resource to compute contacts for.
      Format: organizations/{organization_id}, folders/{folder_id} or
      projects/{project_id}
  """

  class NotificationCategoriesValueValuesEnum(_messages.Enum):
    r"""The categories of notifications to compute contacts for. If ALL is
    included in this list, contacts subscribed to any notification category
    will be returned.

    Values:
      NOTIFICATION_CATEGORY_UNSPECIFIED: Notification category is unrecognized
        or unspecified.
      ALL: All notifications related to the resource, including notifications
        pertaining to categories added in the future.
      SUSPENSION: Notifications related to imminent account suspension.
      SECURITY: Notifications related to security/privacy incidents,
        notifications, and vulnerabilities.
      TECHNICAL: Notifications related to technical events and issues such as
        outages, errors, or bugs.
      BILLING: Notifications related to billing and payments notifications,
        price updates, errors, or credits.
      LEGAL: Notifications related to enforcement actions, regulatory
        compliance, or government notices.
      PRODUCT_UPDATES: Notifications related to new versions, product terms
        updates, or deprecations.
      TECHNICAL_INCIDENTS: Child category of TECHNICAL. If assigned, technical
        incident notifications will go to these contacts instead of TECHNICAL.
    """
    NOTIFICATION_CATEGORY_UNSPECIFIED = 0
    ALL = 1
    SUSPENSION = 2
    SECURITY = 3
    TECHNICAL = 4
    BILLING = 5
    LEGAL = 6
    PRODUCT_UPDATES = 7
    TECHNICAL_INCIDENTS = 8

  notificationCategories = _messages.EnumField('NotificationCategoriesValueValuesEnum', 1, repeated=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class EssentialcontactsFoldersContactsCreateRequest(_messages.Message):
  r"""A EssentialcontactsFoldersContactsCreateRequest object.

  Fields:
    googleCloudEssentialcontactsV1Contact: A
      GoogleCloudEssentialcontactsV1Contact resource to be passed as the
      request body.
    parent: Required. The resource to save this contact for. Format:
      organizations/{organization_id}, folders/{folder_id} or
      projects/{project_id}
  """

  googleCloudEssentialcontactsV1Contact = _messages.MessageField('GoogleCloudEssentialcontactsV1Contact', 1)
  parent = _messages.StringField(2, required=True)


class EssentialcontactsFoldersContactsDeleteRequest(_messages.Message):
  r"""A EssentialcontactsFoldersContactsDeleteRequest object.

  Fields:
    name: Required. The name of the contact to delete. Format:
      organizations/{organization_id}/contacts/{contact_id},
      folders/{folder_id}/contacts/{contact_id} or
      projects/{project_id}/contacts/{contact_id}
  """

  name = _messages.StringField(1, required=True)


class EssentialcontactsFoldersContactsGetRequest(_messages.Message):
  r"""A EssentialcontactsFoldersContactsGetRequest object.

  Fields:
    name: Required. The name of the contact to retrieve. Format:
      organizations/{organization_id}/contacts/{contact_id},
      folders/{folder_id}/contacts/{contact_id} or
      projects/{project_id}/contacts/{contact_id}
  """

  name = _messages.StringField(1, required=True)


class EssentialcontactsFoldersContactsListRequest(_messages.Message):
  r"""A EssentialcontactsFoldersContactsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of
      `next_page_token` in the response indicates that more results might be
      available. If not specified, the default page_size is 100.
    pageToken: Optional. If present, retrieves the next batch of results from
      the preceding call to this method. `page_token` must be the value of
      `next_page_token` from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent resource name. Format:
      organizations/{organization_id}, folders/{folder_id} or
      projects/{project_id}
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class EssentialcontactsFoldersContactsPatchRequest(_messages.Message):
  r"""A EssentialcontactsFoldersContactsPatchRequest object.

  Fields:
    googleCloudEssentialcontactsV1Contact: A
      GoogleCloudEssentialcontactsV1Contact resource to be passed as the
      request body.
    name: The identifier for the contact. Format:
      {resource_type}/{resource_id}/contacts/{contact_id}
    updateMask: Optional. The update mask applied to the resource. For the
      `FieldMask` definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  googleCloudEssentialcontactsV1Contact = _messages.MessageField('GoogleCloudEssentialcontactsV1Contact', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class EssentialcontactsFoldersContactsSendTestMessageRequest(_messages.Message):
  r"""A EssentialcontactsFoldersContactsSendTestMessageRequest object.

  Fields:
    googleCloudEssentialcontactsV1SendTestMessageRequest: A
      GoogleCloudEssentialcontactsV1SendTestMessageRequest resource to be
      passed as the request body.
    resource: Required. The name of the resource to send the test message for.
      All contacts must either be set directly on this resource or inherited
      from another resource that is an ancestor of this one. Format:
      organizations/{organization_id}, folders/{folder_id} or
      projects/{project_id}
  """

  googleCloudEssentialcontactsV1SendTestMessageRequest = _messages.MessageField('GoogleCloudEssentialcontactsV1SendTestMessageRequest', 1)
  resource = _messages.StringField(2, required=True)


class EssentialcontactsOrganizationsContactsComputeRequest(_messages.Message):
  r"""A EssentialcontactsOrganizationsContactsComputeRequest object.

  Enums:
    NotificationCategoriesValueValuesEnum: The categories of notifications to
      compute contacts for. If ALL is included in this list, contacts
      subscribed to any notification category will be returned.

  Fields:
    notificationCategories: The categories of notifications to compute
      contacts for. If ALL is included in this list, contacts subscribed to
      any notification category will be returned.
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of
      `next_page_token` in the response indicates that more results might be
      available. If not specified, the default page_size is 100.
    pageToken: Optional. If present, retrieves the next batch of results from
      the preceding call to this method. `page_token` must be the value of
      `next_page_token` from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The name of the resource to compute contacts for.
      Format: organizations/{organization_id}, folders/{folder_id} or
      projects/{project_id}
  """

  class NotificationCategoriesValueValuesEnum(_messages.Enum):
    r"""The categories of notifications to compute contacts for. If ALL is
    included in this list, contacts subscribed to any notification category
    will be returned.

    Values:
      NOTIFICATION_CATEGORY_UNSPECIFIED: Notification category is unrecognized
        or unspecified.
      ALL: All notifications related to the resource, including notifications
        pertaining to categories added in the future.
      SUSPENSION: Notifications related to imminent account suspension.
      SECURITY: Notifications related to security/privacy incidents,
        notifications, and vulnerabilities.
      TECHNICAL: Notifications related to technical events and issues such as
        outages, errors, or bugs.
      BILLING: Notifications related to billing and payments notifications,
        price updates, errors, or credits.
      LEGAL: Notifications related to enforcement actions, regulatory
        compliance, or government notices.
      PRODUCT_UPDATES: Notifications related to new versions, product terms
        updates, or deprecations.
      TECHNICAL_INCIDENTS: Child category of TECHNICAL. If assigned, technical
        incident notifications will go to these contacts instead of TECHNICAL.
    """
    NOTIFICATION_CATEGORY_UNSPECIFIED = 0
    ALL = 1
    SUSPENSION = 2
    SECURITY = 3
    TECHNICAL = 4
    BILLING = 5
    LEGAL = 6
    PRODUCT_UPDATES = 7
    TECHNICAL_INCIDENTS = 8

  notificationCategories = _messages.EnumField('NotificationCategoriesValueValuesEnum', 1, repeated=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class EssentialcontactsOrganizationsContactsCreateRequest(_messages.Message):
  r"""A EssentialcontactsOrganizationsContactsCreateRequest object.

  Fields:
    googleCloudEssentialcontactsV1Contact: A
      GoogleCloudEssentialcontactsV1Contact resource to be passed as the
      request body.
    parent: Required. The resource to save this contact for. Format:
      organizations/{organization_id}, folders/{folder_id} or
      projects/{project_id}
  """

  googleCloudEssentialcontactsV1Contact = _messages.MessageField('GoogleCloudEssentialcontactsV1Contact', 1)
  parent = _messages.StringField(2, required=True)


class EssentialcontactsOrganizationsContactsDeleteRequest(_messages.Message):
  r"""A EssentialcontactsOrganizationsContactsDeleteRequest object.

  Fields:
    name: Required. The name of the contact to delete. Format:
      organizations/{organization_id}/contacts/{contact_id},
      folders/{folder_id}/contacts/{contact_id} or
      projects/{project_id}/contacts/{contact_id}
  """

  name = _messages.StringField(1, required=True)


class EssentialcontactsOrganizationsContactsGetRequest(_messages.Message):
  r"""A EssentialcontactsOrganizationsContactsGetRequest object.

  Fields:
    name: Required. The name of the contact to retrieve. Format:
      organizations/{organization_id}/contacts/{contact_id},
      folders/{folder_id}/contacts/{contact_id} or
      projects/{project_id}/contacts/{contact_id}
  """

  name = _messages.StringField(1, required=True)


class EssentialcontactsOrganizationsContactsListRequest(_messages.Message):
  r"""A EssentialcontactsOrganizationsContactsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of
      `next_page_token` in the response indicates that more results might be
      available. If not specified, the default page_size is 100.
    pageToken: Optional. If present, retrieves the next batch of results from
      the preceding call to this method. `page_token` must be the value of
      `next_page_token` from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent resource name. Format:
      organizations/{organization_id}, folders/{folder_id} or
      projects/{project_id}
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class EssentialcontactsOrganizationsContactsPatchRequest(_messages.Message):
  r"""A EssentialcontactsOrganizationsContactsPatchRequest object.

  Fields:
    googleCloudEssentialcontactsV1Contact: A
      GoogleCloudEssentialcontactsV1Contact resource to be passed as the
      request body.
    name: The identifier for the contact. Format:
      {resource_type}/{resource_id}/contacts/{contact_id}
    updateMask: Optional. The update mask applied to the resource. For the
      `FieldMask` definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  googleCloudEssentialcontactsV1Contact = _messages.MessageField('GoogleCloudEssentialcontactsV1Contact', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class EssentialcontactsOrganizationsContactsSendTestMessageRequest(_messages.Message):
  r"""A EssentialcontactsOrganizationsContactsSendTestMessageRequest object.

  Fields:
    googleCloudEssentialcontactsV1SendTestMessageRequest: A
      GoogleCloudEssentialcontactsV1SendTestMessageRequest resource to be
      passed as the request body.
    resource: Required. The name of the resource to send the test message for.
      All contacts must either be set directly on this resource or inherited
      from another resource that is an ancestor of this one. Format:
      organizations/{organization_id}, folders/{folder_id} or
      projects/{project_id}
  """

  googleCloudEssentialcontactsV1SendTestMessageRequest = _messages.MessageField('GoogleCloudEssentialcontactsV1SendTestMessageRequest', 1)
  resource = _messages.StringField(2, required=True)


class EssentialcontactsProjectsContactsComputeRequest(_messages.Message):
  r"""A EssentialcontactsProjectsContactsComputeRequest object.

  Enums:
    NotificationCategoriesValueValuesEnum: The categories of notifications to
      compute contacts for. If ALL is included in this list, contacts
      subscribed to any notification category will be returned.

  Fields:
    notificationCategories: The categories of notifications to compute
      contacts for. If ALL is included in this list, contacts subscribed to
      any notification category will be returned.
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of
      `next_page_token` in the response indicates that more results might be
      available. If not specified, the default page_size is 100.
    pageToken: Optional. If present, retrieves the next batch of results from
      the preceding call to this method. `page_token` must be the value of
      `next_page_token` from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The name of the resource to compute contacts for.
      Format: organizations/{organization_id}, folders/{folder_id} or
      projects/{project_id}
  """

  class NotificationCategoriesValueValuesEnum(_messages.Enum):
    r"""The categories of notifications to compute contacts for. If ALL is
    included in this list, contacts subscribed to any notification category
    will be returned.

    Values:
      NOTIFICATION_CATEGORY_UNSPECIFIED: Notification category is unrecognized
        or unspecified.
      ALL: All notifications related to the resource, including notifications
        pertaining to categories added in the future.
      SUSPENSION: Notifications related to imminent account suspension.
      SECURITY: Notifications related to security/privacy incidents,
        notifications, and vulnerabilities.
      TECHNICAL: Notifications related to technical events and issues such as
        outages, errors, or bugs.
      BILLING: Notifications related to billing and payments notifications,
        price updates, errors, or credits.
      LEGAL: Notifications related to enforcement actions, regulatory
        compliance, or government notices.
      PRODUCT_UPDATES: Notifications related to new versions, product terms
        updates, or deprecations.
      TECHNICAL_INCIDENTS: Child category of TECHNICAL. If assigned, technical
        incident notifications will go to these contacts instead of TECHNICAL.
    """
    NOTIFICATION_CATEGORY_UNSPECIFIED = 0
    ALL = 1
    SUSPENSION = 2
    SECURITY = 3
    TECHNICAL = 4
    BILLING = 5
    LEGAL = 6
    PRODUCT_UPDATES = 7
    TECHNICAL_INCIDENTS = 8

  notificationCategories = _messages.EnumField('NotificationCategoriesValueValuesEnum', 1, repeated=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class EssentialcontactsProjectsContactsCreateRequest(_messages.Message):
  r"""A EssentialcontactsProjectsContactsCreateRequest object.

  Fields:
    googleCloudEssentialcontactsV1Contact: A
      GoogleCloudEssentialcontactsV1Contact resource to be passed as the
      request body.
    parent: Required. The resource to save this contact for. Format:
      organizations/{organization_id}, folders/{folder_id} or
      projects/{project_id}
  """

  googleCloudEssentialcontactsV1Contact = _messages.MessageField('GoogleCloudEssentialcontactsV1Contact', 1)
  parent = _messages.StringField(2, required=True)


class EssentialcontactsProjectsContactsDeleteRequest(_messages.Message):
  r"""A EssentialcontactsProjectsContactsDeleteRequest object.

  Fields:
    name: Required. The name of the contact to delete. Format:
      organizations/{organization_id}/contacts/{contact_id},
      folders/{folder_id}/contacts/{contact_id} or
      projects/{project_id}/contacts/{contact_id}
  """

  name = _messages.StringField(1, required=True)


class EssentialcontactsProjectsContactsGetRequest(_messages.Message):
  r"""A EssentialcontactsProjectsContactsGetRequest object.

  Fields:
    name: Required. The name of the contact to retrieve. Format:
      organizations/{organization_id}/contacts/{contact_id},
      folders/{folder_id}/contacts/{contact_id} or
      projects/{project_id}/contacts/{contact_id}
  """

  name = _messages.StringField(1, required=True)


class EssentialcontactsProjectsContactsListRequest(_messages.Message):
  r"""A EssentialcontactsProjectsContactsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of
      `next_page_token` in the response indicates that more results might be
      available. If not specified, the default page_size is 100.
    pageToken: Optional. If present, retrieves the next batch of results from
      the preceding call to this method. `page_token` must be the value of
      `next_page_token` from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent resource name. Format:
      organizations/{organization_id}, folders/{folder_id} or
      projects/{project_id}
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class EssentialcontactsProjectsContactsPatchRequest(_messages.Message):
  r"""A EssentialcontactsProjectsContactsPatchRequest object.

  Fields:
    googleCloudEssentialcontactsV1Contact: A
      GoogleCloudEssentialcontactsV1Contact resource to be passed as the
      request body.
    name: The identifier for the contact. Format:
      {resource_type}/{resource_id}/contacts/{contact_id}
    updateMask: Optional. The update mask applied to the resource. For the
      `FieldMask` definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  googleCloudEssentialcontactsV1Contact = _messages.MessageField('GoogleCloudEssentialcontactsV1Contact', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class EssentialcontactsProjectsContactsSendTestMessageRequest(_messages.Message):
  r"""A EssentialcontactsProjectsContactsSendTestMessageRequest object.

  Fields:
    googleCloudEssentialcontactsV1SendTestMessageRequest: A
      GoogleCloudEssentialcontactsV1SendTestMessageRequest resource to be
      passed as the request body.
    resource: Required. The name of the resource to send the test message for.
      All contacts must either be set directly on this resource or inherited
      from another resource that is an ancestor of this one. Format:
      organizations/{organization_id}, folders/{folder_id} or
      projects/{project_id}
  """

  googleCloudEssentialcontactsV1SendTestMessageRequest = _messages.MessageField('GoogleCloudEssentialcontactsV1SendTestMessageRequest', 1)
  resource = _messages.StringField(2, required=True)


class GoogleCloudEssentialcontactsV1ComputeContactsResponse(_messages.Message):
  r"""Response message for the ComputeContacts method.

  Fields:
    contacts: All contacts for the resource that are subscribed to the
      specified notification categories, including contacts inherited from any
      parent resources.
    nextPageToken: If there are more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token` and the rest of the parameters the same as the original
      request.
  """

  contacts = _messages.MessageField('GoogleCloudEssentialcontactsV1Contact', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudEssentialcontactsV1Contact(_messages.Message):
  r"""A contact that will receive notifications from Google Cloud.

  Enums:
    NotificationCategorySubscriptionsValueListEntryValuesEnum:
    ValidationStateValueValuesEnum: The validity of the contact. A contact is
      considered valid if it is the correct recipient for notifications for a
      particular resource.

  Fields:
    email: Required. The email address to send notifications to. This does not
      need to be a Google account.
    languageTag: The preferred language for notifications, as a ISO 639-1
      language code. See [Supported
      languages](https://cloud.google.com/resource-manager/docs/managing-
      notification-contacts#supported-languages) for a list of supported
      languages.
    name: The identifier for the contact. Format:
      {resource_type}/{resource_id}/contacts/{contact_id}
    notificationCategorySubscriptions: The categories of notifications that
      the contact will receive communications for.
    validateTime: The last time the validation_state was updated, either
      manually or automatically. A contact is considered stale if its
      validation state was updated more than 1 year ago.
    validationState: The validity of the contact. A contact is considered
      valid if it is the correct recipient for notifications for a particular
      resource.
  """

  class NotificationCategorySubscriptionsValueListEntryValuesEnum(_messages.Enum):
    r"""NotificationCategorySubscriptionsValueListEntryValuesEnum enum type.

    Values:
      NOTIFICATION_CATEGORY_UNSPECIFIED: Notification category is unrecognized
        or unspecified.
      ALL: All notifications related to the resource, including notifications
        pertaining to categories added in the future.
      SUSPENSION: Notifications related to imminent account suspension.
      SECURITY: Notifications related to security/privacy incidents,
        notifications, and vulnerabilities.
      TECHNICAL: Notifications related to technical events and issues such as
        outages, errors, or bugs.
      BILLING: Notifications related to billing and payments notifications,
        price updates, errors, or credits.
      LEGAL: Notifications related to enforcement actions, regulatory
        compliance, or government notices.
      PRODUCT_UPDATES: Notifications related to new versions, product terms
        updates, or deprecations.
      TECHNICAL_INCIDENTS: Child category of TECHNICAL. If assigned, technical
        incident notifications will go to these contacts instead of TECHNICAL.
    """
    NOTIFICATION_CATEGORY_UNSPECIFIED = 0
    ALL = 1
    SUSPENSION = 2
    SECURITY = 3
    TECHNICAL = 4
    BILLING = 5
    LEGAL = 6
    PRODUCT_UPDATES = 7
    TECHNICAL_INCIDENTS = 8

  class ValidationStateValueValuesEnum(_messages.Enum):
    r"""The validity of the contact. A contact is considered valid if it is
    the correct recipient for notifications for a particular resource.

    Values:
      VALIDATION_STATE_UNSPECIFIED: The validation state is unknown or
        unspecified.
      VALID: The contact is marked as valid. This is usually done manually by
        the contact admin. All new contacts begin in the valid state.
      INVALID: The contact is considered invalid. This may become the state if
        the contact's email is found to be unreachable.
    """
    VALIDATION_STATE_UNSPECIFIED = 0
    VALID = 1
    INVALID = 2

  email = _messages.StringField(1)
  languageTag = _messages.StringField(2)
  name = _messages.StringField(3)
  notificationCategorySubscriptions = _messages.EnumField('NotificationCategorySubscriptionsValueListEntryValuesEnum', 4, repeated=True)
  validateTime = _messages.StringField(5)
  validationState = _messages.EnumField('ValidationStateValueValuesEnum', 6)


class GoogleCloudEssentialcontactsV1ListContactsResponse(_messages.Message):
  r"""Response message for the ListContacts method.

  Fields:
    contacts: The contacts for the specified resource.
    nextPageToken: If there are more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token` and the rest of the parameters the same as the original
      request.
  """

  contacts = _messages.MessageField('GoogleCloudEssentialcontactsV1Contact', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudEssentialcontactsV1SendTestMessageRequest(_messages.Message):
  r"""Request message for the SendTestMessage method.

  Enums:
    NotificationCategoryValueValuesEnum: Required. The notification category
      to send the test message for. All contacts must be subscribed to this
      category.

  Fields:
    contacts: Required. The list of names of the contacts to send a test
      message to. Format:
      organizations/{organization_id}/contacts/{contact_id},
      folders/{folder_id}/contacts/{contact_id} or
      projects/{project_id}/contacts/{contact_id}
    notificationCategory: Required. The notification category to send the test
      message for. All contacts must be subscribed to this category.
  """

  class NotificationCategoryValueValuesEnum(_messages.Enum):
    r"""Required. The notification category to send the test message for. All
    contacts must be subscribed to this category.

    Values:
      NOTIFICATION_CATEGORY_UNSPECIFIED: Notification category is unrecognized
        or unspecified.
      ALL: All notifications related to the resource, including notifications
        pertaining to categories added in the future.
      SUSPENSION: Notifications related to imminent account suspension.
      SECURITY: Notifications related to security/privacy incidents,
        notifications, and vulnerabilities.
      TECHNICAL: Notifications related to technical events and issues such as
        outages, errors, or bugs.
      BILLING: Notifications related to billing and payments notifications,
        price updates, errors, or credits.
      LEGAL: Notifications related to enforcement actions, regulatory
        compliance, or government notices.
      PRODUCT_UPDATES: Notifications related to new versions, product terms
        updates, or deprecations.
      TECHNICAL_INCIDENTS: Child category of TECHNICAL. If assigned, technical
        incident notifications will go to these contacts instead of TECHNICAL.
    """
    NOTIFICATION_CATEGORY_UNSPECIFIED = 0
    ALL = 1
    SUSPENSION = 2
    SECURITY = 3
    TECHNICAL = 4
    BILLING = 5
    LEGAL = 6
    PRODUCT_UPDATES = 7
    TECHNICAL_INCIDENTS = 8

  contacts = _messages.StringField(1, repeated=True)
  notificationCategory = _messages.EnumField('NotificationCategoryValueValuesEnum', 2)


class GoogleProtobufEmpty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); } The JSON
  representation for `Empty` is empty JSON object `{}`.
  """



class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
