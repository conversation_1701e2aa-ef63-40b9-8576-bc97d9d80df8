"""Generated client library for toolresults version v1beta3."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.toolresults.v1beta3 import toolresults_v1beta3_messages as messages


class ToolresultsV1beta3(base_api.BaseApiClient):
  """Generated client library for service toolresults version v1beta3."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://toolresults.googleapis.com/'
  MTLS_BASE_URL = 'https://toolresults.mtls.googleapis.com/'

  _PACKAGE = 'toolresults'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1beta3'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'ToolresultsV1beta3'
  _URL_VERSION = 'v1beta3'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new toolresults handle."""
    url = url or self.BASE_URL
    super(ToolresultsV1beta3, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_histories_executions_clusters = self.ProjectsHistoriesExecutionsClustersService(self)
    self.projects_histories_executions_environments = self.ProjectsHistoriesExecutionsEnvironmentsService(self)
    self.projects_histories_executions_steps_perfMetricsSummary = self.ProjectsHistoriesExecutionsStepsPerfMetricsSummaryService(self)
    self.projects_histories_executions_steps_perfSampleSeries_samples = self.ProjectsHistoriesExecutionsStepsPerfSampleSeriesSamplesService(self)
    self.projects_histories_executions_steps_perfSampleSeries = self.ProjectsHistoriesExecutionsStepsPerfSampleSeriesService(self)
    self.projects_histories_executions_steps_testCases = self.ProjectsHistoriesExecutionsStepsTestCasesService(self)
    self.projects_histories_executions_steps_thumbnails = self.ProjectsHistoriesExecutionsStepsThumbnailsService(self)
    self.projects_histories_executions_steps = self.ProjectsHistoriesExecutionsStepsService(self)
    self.projects_histories_executions = self.ProjectsHistoriesExecutionsService(self)
    self.projects_histories = self.ProjectsHistoriesService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsHistoriesExecutionsClustersService(base_api.BaseApiService):
    """Service class for the projects_histories_executions_clusters resource."""

    _NAME = 'projects_histories_executions_clusters'

    def __init__(self, client):
      super(ToolresultsV1beta3.ProjectsHistoriesExecutionsClustersService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Retrieves a single screenshot cluster by its ID.

      Args:
        request: (ToolresultsProjectsHistoriesExecutionsClustersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ScreenshotCluster) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='toolresults.projects.histories.executions.clusters.get',
        ordered_params=['projectId', 'historyId', 'executionId', 'clusterId'],
        path_params=['clusterId', 'executionId', 'historyId', 'projectId'],
        query_params=[],
        relative_path='toolresults/v1beta3/projects/{projectId}/histories/{historyId}/executions/{executionId}/clusters/{clusterId}',
        request_field='',
        request_type_name='ToolresultsProjectsHistoriesExecutionsClustersGetRequest',
        response_type_name='ScreenshotCluster',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Screenshot Clusters Returns the list of screenshot clusters corresponding to an execution. Screenshot clusters are created after the execution is finished. Clusters are created from a set of screenshots. Between any two screenshots, a matching score is calculated based off their metadata that determines how similar they are. Screenshots are placed in the cluster that has screens which have the highest matching scores.

      Args:
        request: (ToolresultsProjectsHistoriesExecutionsClustersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListScreenshotClustersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='toolresults.projects.histories.executions.clusters.list',
        ordered_params=['projectId', 'historyId', 'executionId'],
        path_params=['executionId', 'historyId', 'projectId'],
        query_params=[],
        relative_path='toolresults/v1beta3/projects/{projectId}/histories/{historyId}/executions/{executionId}/clusters',
        request_field='',
        request_type_name='ToolresultsProjectsHistoriesExecutionsClustersListRequest',
        response_type_name='ListScreenshotClustersResponse',
        supports_download=False,
    )

  class ProjectsHistoriesExecutionsEnvironmentsService(base_api.BaseApiService):
    """Service class for the projects_histories_executions_environments resource."""

    _NAME = 'projects_histories_executions_environments'

    def __init__(self, client):
      super(ToolresultsV1beta3.ProjectsHistoriesExecutionsEnvironmentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets an Environment. May return any of the following canonical error codes: - PERMISSION_DENIED - if the user is not authorized to read project - INVALID_ARGUMENT - if the request is malformed - NOT_FOUND - if the Environment does not exist.

      Args:
        request: (ToolresultsProjectsHistoriesExecutionsEnvironmentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Environment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='toolresults.projects.histories.executions.environments.get',
        ordered_params=['projectId', 'historyId', 'executionId', 'environmentId'],
        path_params=['environmentId', 'executionId', 'historyId', 'projectId'],
        query_params=[],
        relative_path='toolresults/v1beta3/projects/{projectId}/histories/{historyId}/executions/{executionId}/environments/{environmentId}',
        request_field='',
        request_type_name='ToolresultsProjectsHistoriesExecutionsEnvironmentsGetRequest',
        response_type_name='Environment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Environments for a given Execution. The Environments are sorted by display name. May return any of the following canonical error codes: - PERMISSION_DENIED - if the user is not authorized to read project - INVALID_ARGUMENT - if the request is malformed - NOT_FOUND - if the containing Execution does not exist.

      Args:
        request: (ToolresultsProjectsHistoriesExecutionsEnvironmentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEnvironmentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='toolresults.projects.histories.executions.environments.list',
        ordered_params=['projectId', 'historyId', 'executionId'],
        path_params=['executionId', 'historyId', 'projectId'],
        query_params=['pageSize', 'pageToken'],
        relative_path='toolresults/v1beta3/projects/{projectId}/histories/{historyId}/executions/{executionId}/environments',
        request_field='',
        request_type_name='ToolresultsProjectsHistoriesExecutionsEnvironmentsListRequest',
        response_type_name='ListEnvironmentsResponse',
        supports_download=False,
    )

  class ProjectsHistoriesExecutionsStepsPerfMetricsSummaryService(base_api.BaseApiService):
    """Service class for the projects_histories_executions_steps_perfMetricsSummary resource."""

    _NAME = 'projects_histories_executions_steps_perfMetricsSummary'

    def __init__(self, client):
      super(ToolresultsV1beta3.ProjectsHistoriesExecutionsStepsPerfMetricsSummaryService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a PerfMetricsSummary resource. Returns the existing one if it has already been created. May return any of the following error code(s): - NOT_FOUND - The containing Step does not exist.

      Args:
        request: (PerfMetricsSummary) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PerfMetricsSummary) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='toolresults.projects.histories.executions.steps.perfMetricsSummary.create',
        ordered_params=['projectId', 'historyId', 'executionId', 'stepId'],
        path_params=['executionId', 'historyId', 'projectId', 'stepId'],
        query_params=[],
        relative_path='toolresults/v1beta3/projects/{projectId}/histories/{historyId}/executions/{executionId}/steps/{stepId}/perfMetricsSummary',
        request_field='<request>',
        request_type_name='PerfMetricsSummary',
        response_type_name='PerfMetricsSummary',
        supports_download=False,
    )

  class ProjectsHistoriesExecutionsStepsPerfSampleSeriesSamplesService(base_api.BaseApiService):
    """Service class for the projects_histories_executions_steps_perfSampleSeries_samples resource."""

    _NAME = 'projects_histories_executions_steps_perfSampleSeries_samples'

    def __init__(self, client):
      super(ToolresultsV1beta3.ProjectsHistoriesExecutionsStepsPerfSampleSeriesSamplesService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchCreate(self, request, global_params=None):
      r"""Creates a batch of PerfSamples - a client can submit multiple batches of Perf Samples through repeated calls to this method in order to split up a large request payload - duplicates and existing timestamp entries will be ignored. - the batch operation may partially succeed - the set of elements successfully inserted is returned in the response (omits items which already existed in the database). May return any of the following canonical error codes: - NOT_FOUND - The containing PerfSampleSeries does not exist.

      Args:
        request: (ToolresultsProjectsHistoriesExecutionsStepsPerfSampleSeriesSamplesBatchCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BatchCreatePerfSamplesResponse) The response message.
      """
      config = self.GetMethodConfig('BatchCreate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchCreate.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='toolresults.projects.histories.executions.steps.perfSampleSeries.samples.batchCreate',
        ordered_params=['projectId', 'historyId', 'executionId', 'stepId', 'sampleSeriesId'],
        path_params=['executionId', 'historyId', 'projectId', 'sampleSeriesId', 'stepId'],
        query_params=[],
        relative_path='toolresults/v1beta3/projects/{projectId}/histories/{historyId}/executions/{executionId}/steps/{stepId}/perfSampleSeries/{sampleSeriesId}/samples:batchCreate',
        request_field='batchCreatePerfSamplesRequest',
        request_type_name='ToolresultsProjectsHistoriesExecutionsStepsPerfSampleSeriesSamplesBatchCreateRequest',
        response_type_name='BatchCreatePerfSamplesResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the Performance Samples of a given Sample Series - The list results are sorted by timestamps ascending - The default page size is 500 samples; and maximum size allowed 5000 - The response token indicates the last returned PerfSample timestamp - When the results size exceeds the page size, submit a subsequent request including the page token to return the rest of the samples up to the page limit May return any of the following canonical error codes: - OUT_OF_RANGE - The specified request page_token is out of valid range - NOT_FOUND - The containing PerfSampleSeries does not exist.

      Args:
        request: (ToolresultsProjectsHistoriesExecutionsStepsPerfSampleSeriesSamplesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPerfSamplesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='toolresults.projects.histories.executions.steps.perfSampleSeries.samples.list',
        ordered_params=['projectId', 'historyId', 'executionId', 'stepId', 'sampleSeriesId'],
        path_params=['executionId', 'historyId', 'projectId', 'sampleSeriesId', 'stepId'],
        query_params=['pageSize', 'pageToken'],
        relative_path='toolresults/v1beta3/projects/{projectId}/histories/{historyId}/executions/{executionId}/steps/{stepId}/perfSampleSeries/{sampleSeriesId}/samples',
        request_field='',
        request_type_name='ToolresultsProjectsHistoriesExecutionsStepsPerfSampleSeriesSamplesListRequest',
        response_type_name='ListPerfSamplesResponse',
        supports_download=False,
    )

  class ProjectsHistoriesExecutionsStepsPerfSampleSeriesService(base_api.BaseApiService):
    """Service class for the projects_histories_executions_steps_perfSampleSeries resource."""

    _NAME = 'projects_histories_executions_steps_perfSampleSeries'

    def __init__(self, client):
      super(ToolresultsV1beta3.ProjectsHistoriesExecutionsStepsPerfSampleSeriesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a PerfSampleSeries. May return any of the following error code(s): - ALREADY_EXISTS - PerfMetricSummary already exists for the given Step - NOT_FOUND - The containing Step does not exist.

      Args:
        request: (PerfSampleSeries) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PerfSampleSeries) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='toolresults.projects.histories.executions.steps.perfSampleSeries.create',
        ordered_params=['projectId', 'historyId', 'executionId', 'stepId'],
        path_params=['executionId', 'historyId', 'projectId', 'stepId'],
        query_params=[],
        relative_path='toolresults/v1beta3/projects/{projectId}/histories/{historyId}/executions/{executionId}/steps/{stepId}/perfSampleSeries',
        request_field='<request>',
        request_type_name='PerfSampleSeries',
        response_type_name='PerfSampleSeries',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a PerfSampleSeries. May return any of the following error code(s): - NOT_FOUND - The specified PerfSampleSeries does not exist.

      Args:
        request: (ToolresultsProjectsHistoriesExecutionsStepsPerfSampleSeriesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PerfSampleSeries) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='toolresults.projects.histories.executions.steps.perfSampleSeries.get',
        ordered_params=['projectId', 'historyId', 'executionId', 'stepId', 'sampleSeriesId'],
        path_params=['executionId', 'historyId', 'projectId', 'sampleSeriesId', 'stepId'],
        query_params=[],
        relative_path='toolresults/v1beta3/projects/{projectId}/histories/{historyId}/executions/{executionId}/steps/{stepId}/perfSampleSeries/{sampleSeriesId}',
        request_field='',
        request_type_name='ToolresultsProjectsHistoriesExecutionsStepsPerfSampleSeriesGetRequest',
        response_type_name='PerfSampleSeries',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists PerfSampleSeries for a given Step. The request provides an optional filter which specifies one or more PerfMetricsType to include in the result; if none returns all. The resulting PerfSampleSeries are sorted by ids. May return any of the following canonical error codes: - NOT_FOUND - The containing Step does not exist.

      Args:
        request: (ToolresultsProjectsHistoriesExecutionsStepsPerfSampleSeriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPerfSampleSeriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='toolresults.projects.histories.executions.steps.perfSampleSeries.list',
        ordered_params=['projectId', 'historyId', 'executionId', 'stepId'],
        path_params=['executionId', 'historyId', 'projectId', 'stepId'],
        query_params=['filter'],
        relative_path='toolresults/v1beta3/projects/{projectId}/histories/{historyId}/executions/{executionId}/steps/{stepId}/perfSampleSeries',
        request_field='',
        request_type_name='ToolresultsProjectsHistoriesExecutionsStepsPerfSampleSeriesListRequest',
        response_type_name='ListPerfSampleSeriesResponse',
        supports_download=False,
    )

  class ProjectsHistoriesExecutionsStepsTestCasesService(base_api.BaseApiService):
    """Service class for the projects_histories_executions_steps_testCases resource."""

    _NAME = 'projects_histories_executions_steps_testCases'

    def __init__(self, client):
      super(ToolresultsV1beta3.ProjectsHistoriesExecutionsStepsTestCasesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details of a Test Case for a Step. Experimental test cases API. Still in active development. May return any of the following canonical error codes: - PERMISSION_DENIED - if the user is not authorized to write to project - INVALID_ARGUMENT - if the request is malformed - NOT_FOUND - if the containing Test Case does not exist.

      Args:
        request: (ToolresultsProjectsHistoriesExecutionsStepsTestCasesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestCase) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='toolresults.projects.histories.executions.steps.testCases.get',
        ordered_params=['projectId', 'historyId', 'executionId', 'stepId', 'testCaseId'],
        path_params=['executionId', 'historyId', 'projectId', 'stepId', 'testCaseId'],
        query_params=[],
        relative_path='toolresults/v1beta3/projects/{projectId}/histories/{historyId}/executions/{executionId}/steps/{stepId}/testCases/{testCaseId}',
        request_field='',
        request_type_name='ToolresultsProjectsHistoriesExecutionsStepsTestCasesGetRequest',
        response_type_name='TestCase',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Test Cases attached to a Step. Experimental test cases API. Still in active development. May return any of the following canonical error codes: - PERMISSION_DENIED - if the user is not authorized to write to project - INVALID_ARGUMENT - if the request is malformed - NOT_FOUND - if the containing Step does not exist.

      Args:
        request: (ToolresultsProjectsHistoriesExecutionsStepsTestCasesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListTestCasesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='toolresults.projects.histories.executions.steps.testCases.list',
        ordered_params=['projectId', 'historyId', 'executionId', 'stepId'],
        path_params=['executionId', 'historyId', 'projectId', 'stepId'],
        query_params=['pageSize', 'pageToken'],
        relative_path='toolresults/v1beta3/projects/{projectId}/histories/{historyId}/executions/{executionId}/steps/{stepId}/testCases',
        request_field='',
        request_type_name='ToolresultsProjectsHistoriesExecutionsStepsTestCasesListRequest',
        response_type_name='ListTestCasesResponse',
        supports_download=False,
    )

  class ProjectsHistoriesExecutionsStepsThumbnailsService(base_api.BaseApiService):
    """Service class for the projects_histories_executions_steps_thumbnails resource."""

    _NAME = 'projects_histories_executions_steps_thumbnails'

    def __init__(self, client):
      super(ToolresultsV1beta3.ProjectsHistoriesExecutionsStepsThumbnailsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists thumbnails of images attached to a step. May return any of the following canonical error codes: - PERMISSION_DENIED - if the user is not authorized to read from the project, or from any of the images - INVALID_ARGUMENT - if the request is malformed - NOT_FOUND - if the step does not exist, or if any of the images do not exist.

      Args:
        request: (ToolresultsProjectsHistoriesExecutionsStepsThumbnailsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListStepThumbnailsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='toolresults.projects.histories.executions.steps.thumbnails.list',
        ordered_params=['projectId', 'historyId', 'executionId', 'stepId'],
        path_params=['executionId', 'historyId', 'projectId', 'stepId'],
        query_params=['pageSize', 'pageToken'],
        relative_path='toolresults/v1beta3/projects/{projectId}/histories/{historyId}/executions/{executionId}/steps/{stepId}/thumbnails',
        request_field='',
        request_type_name='ToolresultsProjectsHistoriesExecutionsStepsThumbnailsListRequest',
        response_type_name='ListStepThumbnailsResponse',
        supports_download=False,
    )

  class ProjectsHistoriesExecutionsStepsService(base_api.BaseApiService):
    """Service class for the projects_histories_executions_steps resource."""

    _NAME = 'projects_histories_executions_steps'

    def __init__(self, client):
      super(ToolresultsV1beta3.ProjectsHistoriesExecutionsStepsService, self).__init__(client)
      self._upload_configs = {
          }

    def AccessibilityClusters(self, request, global_params=None):
      r"""Lists accessibility clusters for a given Step May return any of the following canonical error codes: - PERMISSION_DENIED - if the user is not authorized to read project - INVALID_ARGUMENT - if the request is malformed - FAILED_PRECONDITION - if an argument in the request happens to be invalid; e.g. if the locale format is incorrect - NOT_FOUND - if the containing Step does not exist.

      Args:
        request: (ToolresultsProjectsHistoriesExecutionsStepsAccessibilityClustersRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListStepAccessibilityClustersResponse) The response message.
      """
      config = self.GetMethodConfig('AccessibilityClusters')
      return self._RunMethod(
          config, request, global_params=global_params)

    AccessibilityClusters.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='toolresults/v1beta3/projects/{projectsId}/histories/{historiesId}/executions/{executionsId}/steps/{stepsId}:accessibilityClusters',
        http_method='GET',
        method_id='toolresults.projects.histories.executions.steps.accessibilityClusters',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['locale'],
        relative_path='toolresults/v1beta3/{+name}:accessibilityClusters',
        request_field='',
        request_type_name='ToolresultsProjectsHistoriesExecutionsStepsAccessibilityClustersRequest',
        response_type_name='ListStepAccessibilityClustersResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a Step. The returned Step will have the id set. May return any of the following canonical error codes: - PERMISSION_DENIED - if the user is not authorized to write to project - INVALID_ARGUMENT - if the request is malformed - FAILED_PRECONDITION - if the step is too large (more than 10Mib) - NOT_FOUND - if the containing Execution does not exist.

      Args:
        request: (ToolresultsProjectsHistoriesExecutionsStepsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Step) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='toolresults.projects.histories.executions.steps.create',
        ordered_params=['projectId', 'historyId', 'executionId'],
        path_params=['executionId', 'historyId', 'projectId'],
        query_params=['requestId'],
        relative_path='toolresults/v1beta3/projects/{projectId}/histories/{historyId}/executions/{executionId}/steps',
        request_field='step',
        request_type_name='ToolresultsProjectsHistoriesExecutionsStepsCreateRequest',
        response_type_name='Step',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a Step. May return any of the following canonical error codes: - PERMISSION_DENIED - if the user is not authorized to read project - INVALID_ARGUMENT - if the request is malformed - NOT_FOUND - if the Step does not exist.

      Args:
        request: (ToolresultsProjectsHistoriesExecutionsStepsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Step) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='toolresults.projects.histories.executions.steps.get',
        ordered_params=['projectId', 'historyId', 'executionId', 'stepId'],
        path_params=['executionId', 'historyId', 'projectId', 'stepId'],
        query_params=[],
        relative_path='toolresults/v1beta3/projects/{projectId}/histories/{historyId}/executions/{executionId}/steps/{stepId}',
        request_field='',
        request_type_name='ToolresultsProjectsHistoriesExecutionsStepsGetRequest',
        response_type_name='Step',
        supports_download=False,
    )

    def GetPerfMetricsSummary(self, request, global_params=None):
      r"""Retrieves a PerfMetricsSummary. May return any of the following error code(s): - NOT_FOUND - The specified PerfMetricsSummary does not exist.

      Args:
        request: (ToolresultsProjectsHistoriesExecutionsStepsGetPerfMetricsSummaryRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PerfMetricsSummary) The response message.
      """
      config = self.GetMethodConfig('GetPerfMetricsSummary')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetPerfMetricsSummary.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='toolresults.projects.histories.executions.steps.getPerfMetricsSummary',
        ordered_params=['projectId', 'historyId', 'executionId', 'stepId'],
        path_params=['executionId', 'historyId', 'projectId', 'stepId'],
        query_params=[],
        relative_path='toolresults/v1beta3/projects/{projectId}/histories/{historyId}/executions/{executionId}/steps/{stepId}/perfMetricsSummary',
        request_field='',
        request_type_name='ToolresultsProjectsHistoriesExecutionsStepsGetPerfMetricsSummaryRequest',
        response_type_name='PerfMetricsSummary',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Steps for a given Execution. The steps are sorted by creation_time in descending order. The step_id key will be used to order the steps with the same creation_time. May return any of the following canonical error codes: - PERMISSION_DENIED - if the user is not authorized to read project - INVALID_ARGUMENT - if the request is malformed - FAILED_PRECONDITION - if an argument in the request happens to be invalid; e.g. if an attempt is made to list the children of a nonexistent Step - NOT_FOUND - if the containing Execution does not exist.

      Args:
        request: (ToolresultsProjectsHistoriesExecutionsStepsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListStepsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='toolresults.projects.histories.executions.steps.list',
        ordered_params=['projectId', 'historyId', 'executionId'],
        path_params=['executionId', 'historyId', 'projectId'],
        query_params=['pageSize', 'pageToken'],
        relative_path='toolresults/v1beta3/projects/{projectId}/histories/{historyId}/executions/{executionId}/steps',
        request_field='',
        request_type_name='ToolresultsProjectsHistoriesExecutionsStepsListRequest',
        response_type_name='ListStepsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing Step with the supplied partial entity. May return any of the following canonical error codes: - PERMISSION_DENIED - if the user is not authorized to write project - INVALID_ARGUMENT - if the request is malformed - FAILED_PRECONDITION - if the requested state transition is illegal (e.g try to upload a duplicate xml file), if the updated step is too large (more than 10Mib) - NOT_FOUND - if the containing Execution does not exist.

      Args:
        request: (ToolresultsProjectsHistoriesExecutionsStepsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Step) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PATCH',
        method_id='toolresults.projects.histories.executions.steps.patch',
        ordered_params=['projectId', 'historyId', 'executionId', 'stepId'],
        path_params=['executionId', 'historyId', 'projectId', 'stepId'],
        query_params=['requestId'],
        relative_path='toolresults/v1beta3/projects/{projectId}/histories/{historyId}/executions/{executionId}/steps/{stepId}',
        request_field='step',
        request_type_name='ToolresultsProjectsHistoriesExecutionsStepsPatchRequest',
        response_type_name='Step',
        supports_download=False,
    )

    def PublishXunitXmlFiles(self, request, global_params=None):
      r"""Publish xml files to an existing Step. May return any of the following canonical error codes: - PERMISSION_DENIED - if the user is not authorized to write project - INVALID_ARGUMENT - if the request is malformed - FAILED_PRECONDITION - if the requested state transition is illegal, e.g. try to upload a duplicate xml file or a file too large. - NOT_FOUND - if the containing Execution does not exist.

      Args:
        request: (ToolresultsProjectsHistoriesExecutionsStepsPublishXunitXmlFilesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Step) The response message.
      """
      config = self.GetMethodConfig('PublishXunitXmlFiles')
      return self._RunMethod(
          config, request, global_params=global_params)

    PublishXunitXmlFiles.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='toolresults.projects.histories.executions.steps.publishXunitXmlFiles',
        ordered_params=['projectId', 'historyId', 'executionId', 'stepId'],
        path_params=['executionId', 'historyId', 'projectId', 'stepId'],
        query_params=[],
        relative_path='toolresults/v1beta3/projects/{projectId}/histories/{historyId}/executions/{executionId}/steps/{stepId}:publishXunitXmlFiles',
        request_field='publishXunitXmlFilesRequest',
        request_type_name='ToolresultsProjectsHistoriesExecutionsStepsPublishXunitXmlFilesRequest',
        response_type_name='Step',
        supports_download=False,
    )

  class ProjectsHistoriesExecutionsService(base_api.BaseApiService):
    """Service class for the projects_histories_executions resource."""

    _NAME = 'projects_histories_executions'

    def __init__(self, client):
      super(ToolresultsV1beta3.ProjectsHistoriesExecutionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an Execution. The returned Execution will have the id set. May return any of the following canonical error codes: - PERMISSION_DENIED - if the user is not authorized to write to project - INVALID_ARGUMENT - if the request is malformed - NOT_FOUND - if the containing History does not exist.

      Args:
        request: (ToolresultsProjectsHistoriesExecutionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Execution) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='toolresults.projects.histories.executions.create',
        ordered_params=['projectId', 'historyId'],
        path_params=['historyId', 'projectId'],
        query_params=['requestId'],
        relative_path='toolresults/v1beta3/projects/{projectId}/histories/{historyId}/executions',
        request_field='execution',
        request_type_name='ToolresultsProjectsHistoriesExecutionsCreateRequest',
        response_type_name='Execution',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an Execution. May return any of the following canonical error codes: - PERMISSION_DENIED - if the user is not authorized to write to project - INVALID_ARGUMENT - if the request is malformed - NOT_FOUND - if the Execution does not exist.

      Args:
        request: (ToolresultsProjectsHistoriesExecutionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Execution) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='toolresults.projects.histories.executions.get',
        ordered_params=['projectId', 'historyId', 'executionId'],
        path_params=['executionId', 'historyId', 'projectId'],
        query_params=[],
        relative_path='toolresults/v1beta3/projects/{projectId}/histories/{historyId}/executions/{executionId}',
        request_field='',
        request_type_name='ToolresultsProjectsHistoriesExecutionsGetRequest',
        response_type_name='Execution',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Executions for a given History. The executions are sorted by creation_time in descending order. The execution_id key will be used to order the executions with the same creation_time. May return any of the following canonical error codes: - PERMISSION_DENIED - if the user is not authorized to read project - INVALID_ARGUMENT - if the request is malformed - NOT_FOUND - if the containing History does not exist.

      Args:
        request: (ToolresultsProjectsHistoriesExecutionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListExecutionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='toolresults.projects.histories.executions.list',
        ordered_params=['projectId', 'historyId'],
        path_params=['historyId', 'projectId'],
        query_params=['pageSize', 'pageToken'],
        relative_path='toolresults/v1beta3/projects/{projectId}/histories/{historyId}/executions',
        request_field='',
        request_type_name='ToolresultsProjectsHistoriesExecutionsListRequest',
        response_type_name='ListExecutionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing Execution with the supplied partial entity. May return any of the following canonical error codes: - PERMISSION_DENIED - if the user is not authorized to write to project - INVALID_ARGUMENT - if the request is malformed - FAILED_PRECONDITION - if the requested state transition is illegal - NOT_FOUND - if the containing History does not exist.

      Args:
        request: (ToolresultsProjectsHistoriesExecutionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Execution) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PATCH',
        method_id='toolresults.projects.histories.executions.patch',
        ordered_params=['projectId', 'historyId', 'executionId'],
        path_params=['executionId', 'historyId', 'projectId'],
        query_params=['requestId'],
        relative_path='toolresults/v1beta3/projects/{projectId}/histories/{historyId}/executions/{executionId}',
        request_field='execution',
        request_type_name='ToolresultsProjectsHistoriesExecutionsPatchRequest',
        response_type_name='Execution',
        supports_download=False,
    )

  class ProjectsHistoriesService(base_api.BaseApiService):
    """Service class for the projects_histories resource."""

    _NAME = 'projects_histories'

    def __init__(self, client):
      super(ToolresultsV1beta3.ProjectsHistoriesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a History. The returned History will have the id set. May return any of the following canonical error codes: - PERMISSION_DENIED - if the user is not authorized to write to project - INVALID_ARGUMENT - if the request is malformed - NOT_FOUND - if the containing project does not exist.

      Args:
        request: (ToolresultsProjectsHistoriesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (History) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='toolresults.projects.histories.create',
        ordered_params=['projectId'],
        path_params=['projectId'],
        query_params=['requestId'],
        relative_path='toolresults/v1beta3/projects/{projectId}/histories',
        request_field='history',
        request_type_name='ToolresultsProjectsHistoriesCreateRequest',
        response_type_name='History',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a History. May return any of the following canonical error codes: - PERMISSION_DENIED - if the user is not authorized to read project - INVALID_ARGUMENT - if the request is malformed - NOT_FOUND - if the History does not exist.

      Args:
        request: (ToolresultsProjectsHistoriesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (History) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='toolresults.projects.histories.get',
        ordered_params=['projectId', 'historyId'],
        path_params=['historyId', 'projectId'],
        query_params=[],
        relative_path='toolresults/v1beta3/projects/{projectId}/histories/{historyId}',
        request_field='',
        request_type_name='ToolresultsProjectsHistoriesGetRequest',
        response_type_name='History',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Histories for a given Project. The histories are sorted by modification time in descending order. The history_id key will be used to order the history with the same modification time. May return any of the following canonical error codes: - PERMISSION_DENIED - if the user is not authorized to read project - INVALID_ARGUMENT - if the request is malformed - NOT_FOUND - if the History does not exist.

      Args:
        request: (ToolresultsProjectsHistoriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListHistoriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='toolresults.projects.histories.list',
        ordered_params=['projectId'],
        path_params=['projectId'],
        query_params=['filterByName', 'pageSize', 'pageToken'],
        relative_path='toolresults/v1beta3/projects/{projectId}/histories',
        request_field='',
        request_type_name='ToolresultsProjectsHistoriesListRequest',
        response_type_name='ListHistoriesResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(ToolresultsV1beta3.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

    def GetSettings(self, request, global_params=None):
      r"""Gets the Tool Results settings for a project. May return any of the following canonical error codes: - PERMISSION_DENIED - if the user is not authorized to read from project.

      Args:
        request: (ToolresultsProjectsGetSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ProjectSettings) The response message.
      """
      config = self.GetMethodConfig('GetSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetSettings.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='toolresults.projects.getSettings',
        ordered_params=['projectId'],
        path_params=['projectId'],
        query_params=[],
        relative_path='toolresults/v1beta3/projects/{projectId}/settings',
        request_field='',
        request_type_name='ToolresultsProjectsGetSettingsRequest',
        response_type_name='ProjectSettings',
        supports_download=False,
    )

    def InitializeSettings(self, request, global_params=None):
      r"""Creates resources for settings which have not yet been set. Currently, this creates a single resource: a Google Cloud Storage bucket, to be used as the default bucket for this project. The bucket is created in an FTL-own storage project. Except for in rare cases, calling this method in parallel from multiple clients will only create a single bucket. In order to avoid unnecessary storage charges, the bucket is configured to automatically delete objects older than 90 days. The bucket is created with the following permissions: - Owner access for owners of central storage project (FTL-owned) - Writer access for owners/editors of customer project - Reader access for viewers of customer project The default ACL on objects created in the bucket is: - Owner access for owners of central storage project - Reader access for owners/editors/viewers of customer project See Google Cloud Storage documentation for more details. If there is already a default bucket set and the project can access the bucket, this call does nothing. However, if the project doesn't have the permission to access the bucket or the bucket is deleted, a new bucket will be created. May return any canonical error codes, including the following: - PERMISSION_DENIED - if the user is not authorized to write to project - Any error code raised by Google Cloud Storage.

      Args:
        request: (ToolresultsProjectsInitializeSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ProjectSettings) The response message.
      """
      config = self.GetMethodConfig('InitializeSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    InitializeSettings.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='toolresults.projects.initializeSettings',
        ordered_params=['projectId'],
        path_params=['projectId'],
        query_params=[],
        relative_path='toolresults/v1beta3/projects/{projectId}:initializeSettings',
        request_field='',
        request_type_name='ToolresultsProjectsInitializeSettingsRequest',
        response_type_name='ProjectSettings',
        supports_download=False,
    )
