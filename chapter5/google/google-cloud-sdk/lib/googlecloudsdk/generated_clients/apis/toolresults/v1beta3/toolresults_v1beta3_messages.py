"""Generated message classes for toolresults version v1beta3.

API to publish and access results from developer tools.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'toolresults'


class ANR(_messages.Message):
  r"""Additional details for an ANR crash.

  Fields:
    stackTrace: The stack trace of the ANR crash. Optional.
  """

  stackTrace = _messages.MessageField('StackTrace', 1)


class AndroidAppInfo(_messages.Message):
  r"""Android app information.

  Fields:
    name: The name of the app. Optional
    packageName: The package name of the app. Required.
    versionCode: The internal version code of the app. Optional.
    versionName: The version name of the app. Optional.
  """

  name = _messages.StringField(1)
  packageName = _messages.StringField(2)
  versionCode = _messages.StringField(3)
  versionName = _messages.StringField(4)


class AndroidInstrumentationTest(_messages.Message):
  r"""A test of an Android application that can control an Android component
  independently of its normal lifecycle. See for more information on types of
  Android tests.

  Fields:
    testPackageId: The java package for the test to be executed. Required
    testRunnerClass: The InstrumentationTestRunner class. Required
    testTargets: Each target must be fully qualified with the package name or
      class name, in one of these formats: - "package package_name" - "class
      package_name.class_name" - "class package_name.class_name#method_name"
      If empty, all targets in the module will be run.
    useOrchestrator: The flag indicates whether Android Test Orchestrator will
      be used to run test or not.
  """

  testPackageId = _messages.StringField(1)
  testRunnerClass = _messages.StringField(2)
  testTargets = _messages.StringField(3, repeated=True)
  useOrchestrator = _messages.BooleanField(4)


class AndroidRoboTest(_messages.Message):
  r"""A test of an android application that explores the application on a
  virtual or physical Android device, finding culprits and crashes as it goes.

  Fields:
    appInitialActivity: The initial activity that should be used to start the
      app. Optional
    bootstrapPackageId: The java package for the bootstrap. Optional
    bootstrapRunnerClass: The runner class for the bootstrap. Optional
    maxDepth: The max depth of the traversal stack Robo can explore. Optional
    maxSteps: The max number of steps/actions Robo can execute. Default is no
      limit (0). Optional
  """

  appInitialActivity = _messages.StringField(1)
  bootstrapPackageId = _messages.StringField(2)
  bootstrapRunnerClass = _messages.StringField(3)
  maxDepth = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  maxSteps = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class AndroidTest(_messages.Message):
  r"""An Android mobile test specification.

  Fields:
    androidAppInfo: Information about the application under test.
    androidInstrumentationTest: An Android instrumentation test.
    androidRoboTest: An Android robo test.
    androidTestLoop: An Android test loop.
    testTimeout: Max time a test is allowed to run before it is automatically
      cancelled.
  """

  androidAppInfo = _messages.MessageField('AndroidAppInfo', 1)
  androidInstrumentationTest = _messages.MessageField('AndroidInstrumentationTest', 2)
  androidRoboTest = _messages.MessageField('AndroidRoboTest', 3)
  androidTestLoop = _messages.MessageField('AndroidTestLoop', 4)
  testTimeout = _messages.MessageField('Duration', 5)


class AndroidTestLoop(_messages.Message):
  r"""Test Loops are tests that can be launched by the app itself, determining
  when to run by listening for an intent.
  """



class Any(_messages.Message):
  r""" `Any` contains an arbitrary serialized protocol buffer message along
  with a URL that describes the type of the serialized message. Protobuf
  library provides support to pack/unpack Any values in the form of utility
  functions or additional generated methods of the Any type. Example 1: Pack
  and unpack a message in C++. Foo foo = ...; Any any; any.PackFrom(foo); ...
  if (any.UnpackTo(&foo)) { ... } Example 2: Pack and unpack a message in
  Java. Foo foo = ...; Any any = Any.pack(foo); ... if (any.is(Foo.class)) {
  foo = any.unpack(Foo.class); } Example 3: Pack and unpack a message in
  Python. foo = Foo(...) any = Any() any.Pack(foo) ... if
  any.Is(Foo.DESCRIPTOR): any.Unpack(foo) ... Example 4: Pack and unpack a
  message in Go foo := &pb.Foo{...} any, err := ptypes.MarshalAny(foo) ... foo
  := &pb.Foo{} if err := ptypes.UnmarshalAny(any, foo); err != nil { ... } The
  pack methods provided by protobuf library will by default use
  'type.googleapis.com/full.type.name' as the type URL and the unpack methods
  only use the fully qualified type name after the last '/' in the type URL,
  for example "foo.bar.com/x/y.z" will yield type name "y.z". # JSON The JSON
  representation of an `Any` value uses the regular representation of the
  deserialized, embedded message, with an additional field `@type` which
  contains the type URL. Example: package google.profile; message Person {
  string first_name = 1; string last_name = 2; } { "@type":
  "type.googleapis.com/google.profile.Person", "firstName": , "lastName": } If
  the embedded message type is well-known and has a custom JSON
  representation, that representation will be embedded adding a field `value`
  which holds the custom JSON in addition to the `@type` field. Example (for
  message google.protobuf.Duration): { "@type":
  "type.googleapis.com/google.protobuf.Duration", "value": "1.212s" }

  Fields:
    typeUrl: A URL/resource name that uniquely identifies the type of the
      serialized protocol buffer message. This string must contain at least
      one "/" character. The last segment of the URL's path must represent the
      fully qualified name of the type (as in
      `path/google.protobuf.Duration`). The name should be in a canonical form
      (e.g., leading "." is not accepted). In practice, teams usually
      precompile into the binary all types that they expect it to use in the
      context of Any. However, for URLs which use the scheme `http`, `https`,
      or no scheme, one can optionally set up a type server that maps type
      URLs to message definitions as follows: * If no scheme is provided,
      `https` is assumed. * An HTTP GET on the URL must yield a
      google.protobuf.Type value in binary format, or produce an error. *
      Applications are allowed to cache lookup results based on the URL, or
      have them precompiled into a binary to avoid any lookup. Therefore,
      binary compatibility needs to be preserved on changes to types. (Use
      versioned type names to manage breaking changes.) Note: this
      functionality is not currently available in the official protobuf
      release, and it is not used for type URLs beginning with
      type.googleapis.com. Schemes other than `http`, `https` (or the empty
      scheme) might be used with implementation specific semantics.
    value: Must be a valid serialized protocol buffer of the above specified
      type.
  """

  typeUrl = _messages.StringField(1)
  value = _messages.BytesField(2)


class AppStartTime(_messages.Message):
  r"""A AppStartTime object.

  Fields:
    fullyDrawnTime: Optional. The time from app start to reaching the
      developer-reported "fully drawn" time. This is only stored if the app
      includes a call to Activity.reportFullyDrawn(). See
      https://developer.android.com/topic/performance/launch-time.html#time-
      full
    initialDisplayTime: The time from app start to the first displayed
      activity being drawn, as reported in Logcat. See
      https://developer.android.com/topic/performance/launch-time.html#time-
      initial
  """

  fullyDrawnTime = _messages.MessageField('Duration', 1)
  initialDisplayTime = _messages.MessageField('Duration', 2)


class AssetIssue(_messages.Message):
  r"""There was an issue with the assets in this test."""


class AvailableDeepLinks(_messages.Message):
  r"""A suggestion to use deep links for a Robo run."""


class BasicPerfSampleSeries(_messages.Message):
  r"""Encapsulates the metadata for basic sample series represented by a line
  chart

  Enums:
    PerfMetricTypeValueValuesEnum:
    PerfUnitValueValuesEnum:
    SampleSeriesLabelValueValuesEnum:

  Fields:
    perfMetricType: A PerfMetricTypeValueValuesEnum attribute.
    perfUnit: A PerfUnitValueValuesEnum attribute.
    sampleSeriesLabel: A SampleSeriesLabelValueValuesEnum attribute.
  """

  class PerfMetricTypeValueValuesEnum(_messages.Enum):
    r"""PerfMetricTypeValueValuesEnum enum type.

    Values:
      perfMetricTypeUnspecified: <no description>
      memory: <no description>
      cpu: <no description>
      network: <no description>
      graphics: <no description>
    """
    perfMetricTypeUnspecified = 0
    memory = 1
    cpu = 2
    network = 3
    graphics = 4

  class PerfUnitValueValuesEnum(_messages.Enum):
    r"""PerfUnitValueValuesEnum enum type.

    Values:
      perfUnitUnspecified: <no description>
      kibibyte: <no description>
      percent: <no description>
      bytesPerSecond: <no description>
      framesPerSecond: <no description>
      byte: <no description>
    """
    perfUnitUnspecified = 0
    kibibyte = 1
    percent = 2
    bytesPerSecond = 3
    framesPerSecond = 4
    byte = 5

  class SampleSeriesLabelValueValuesEnum(_messages.Enum):
    r"""SampleSeriesLabelValueValuesEnum enum type.

    Values:
      sampleSeriesTypeUnspecified: <no description>
      memoryRssPrivate: Memory sample series
      memoryRssShared: <no description>
      memoryRssTotal: <no description>
      memoryTotal: <no description>
      cpuUser: CPU sample series
      cpuKernel: <no description>
      cpuTotal: <no description>
      ntBytesTransferred: Network sample series
      ntBytesReceived: <no description>
      networkSent: <no description>
      networkReceived: <no description>
      graphicsFrameRate: Graphics sample series
    """
    sampleSeriesTypeUnspecified = 0
    memoryRssPrivate = 1
    memoryRssShared = 2
    memoryRssTotal = 3
    memoryTotal = 4
    cpuUser = 5
    cpuKernel = 6
    cpuTotal = 7
    ntBytesTransferred = 8
    ntBytesReceived = 9
    networkSent = 10
    networkReceived = 11
    graphicsFrameRate = 12

  perfMetricType = _messages.EnumField('PerfMetricTypeValueValuesEnum', 1)
  perfUnit = _messages.EnumField('PerfUnitValueValuesEnum', 2)
  sampleSeriesLabel = _messages.EnumField('SampleSeriesLabelValueValuesEnum', 3)


class BatchCreatePerfSamplesRequest(_messages.Message):
  r"""The request must provide up to a maximum of 5000 samples to be created;
  a larger sample size will cause an INVALID_ARGUMENT error

  Fields:
    perfSamples: The set of PerfSamples to create should not include existing
      timestamps
  """

  perfSamples = _messages.MessageField('PerfSample', 1, repeated=True)


class BatchCreatePerfSamplesResponse(_messages.Message):
  r"""A BatchCreatePerfSamplesResponse object.

  Fields:
    perfSamples: A PerfSample attribute.
  """

  perfSamples = _messages.MessageField('PerfSample', 1, repeated=True)


class BlankScreen(_messages.Message):
  r"""A warning that Robo encountered a screen that was mostly blank; this may
  indicate a problem with the app.

  Fields:
    screenId: The screen id of the element
  """

  screenId = _messages.StringField(1)


class CPUInfo(_messages.Message):
  r"""A CPUInfo object.

  Fields:
    cpuProcessor: description of the device processor ie '1.8 GHz hexa core
      64-bit ARMv8-A'
    cpuSpeedInGhz: the CPU clock speed in GHz
    numberOfCores: the number of CPU cores
  """

  cpuProcessor = _messages.StringField(1)
  cpuSpeedInGhz = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  numberOfCores = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class CrashDialogError(_messages.Message):
  r"""Crash dialog was detected during the test execution

  Fields:
    crashPackage: The name of the package that caused the dialog.
  """

  crashPackage = _messages.StringField(1)


class DetectedAppSplashScreen(_messages.Message):
  r"""A notification that Robo detected a splash screen provided by app (vs.
  Android OS splash screen).
  """



class DeviceOutOfMemory(_messages.Message):
  r"""A warning that device ran out of memory"""


class Duration(_messages.Message):
  r""" A Duration represents a signed, fixed-length span of time represented
  as a count of seconds and fractions of seconds at nanosecond resolution. It
  is independent of any calendar and concepts like "day" or "month". It is
  related to Timestamp in that the difference between two Timestamp values is
  a Duration and it can be added or subtracted from a Timestamp. Range is
  approximately +-10,000 years.

  Fields:
    nanos: Signed fractions of a second at nanosecond resolution of the span
      of time. Durations less than one second are represented with a 0
      `seconds` field and a positive or negative `nanos` field. For durations
      of one second or more, a non-zero value for the `nanos` field must be of
      the same sign as the `seconds` field. Must be from -999,999,999 to
      +999,999,999 inclusive.
    seconds: Signed seconds of the span of time. Must be from -315,576,000,000
      to +315,576,000,000 inclusive. Note: these bounds are computed from: 60
      sec/min * 60 min/hr * 24 hr/day * 365.25 days/year * 10000 years
  """

  nanos = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  seconds = _messages.IntegerField(2)


class EncounteredLoginScreen(_messages.Message):
  r"""Additional details about encountered login screens.

  Fields:
    distinctScreens: Number of encountered distinct login screens.
    screenIds: Subset of login screens.
  """

  distinctScreens = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  screenIds = _messages.StringField(2, repeated=True)


class EncounteredNonAndroidUiWidgetScreen(_messages.Message):
  r"""Additional details about encountered screens with elements that are not
  Android UI widgets.

  Fields:
    distinctScreens: Number of encountered distinct screens with non Android
      UI widgets.
    screenIds: Subset of screens which contain non Android UI widgets.
  """

  distinctScreens = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  screenIds = _messages.StringField(2, repeated=True)


class Environment(_messages.Message):
  r"""An Environment represents the set of test runs (Steps) from the parent
  Execution that are configured with the same set of dimensions (Model,
  Version, Locale, and Orientation). Multiple such runs occur particularly
  because of features like sharding (splitting up a test suite to run in
  parallel across devices) and reruns (running a test multiple times to check
  for different outcomes).

  Fields:
    completionTime: Output only. The time when the Environment status was set
      to complete. This value will be set automatically when state transitions
      to COMPLETE.
    creationTime: Output only. The time when the Environment was created.
    dimensionValue: Dimension values describing the environment. Dimension
      values always consist of "Model", "Version", "Locale", and
      "Orientation". - In response: always set - In create request: always set
      - In update request: never set
    displayName: A short human-readable name to display in the UI. Maximum of
      100 characters. For example: Nexus 5, API 27.
    environmentId: Output only. An Environment id.
    environmentResult: Merged result of the environment.
    executionId: Output only. An Execution id.
    historyId: Output only. A History id.
    projectId: Output only. A Project id.
    resultsStorage: The location where output files are stored in the user
      bucket.
    shardSummaries: Output only. Summaries of shards. Only one shard will
      present unless sharding feature is enabled in TestExecutionService.
  """

  completionTime = _messages.MessageField('Timestamp', 1)
  creationTime = _messages.MessageField('Timestamp', 2)
  dimensionValue = _messages.MessageField('EnvironmentDimensionValueEntry', 3, repeated=True)
  displayName = _messages.StringField(4)
  environmentId = _messages.StringField(5)
  environmentResult = _messages.MessageField('MergedResult', 6)
  executionId = _messages.StringField(7)
  historyId = _messages.StringField(8)
  projectId = _messages.StringField(9)
  resultsStorage = _messages.MessageField('ResultsStorage', 10)
  shardSummaries = _messages.MessageField('ShardSummary', 11, repeated=True)


class EnvironmentDimensionValueEntry(_messages.Message):
  r"""A EnvironmentDimensionValueEntry object.

  Fields:
    key: A string attribute.
    value: A string attribute.
  """

  key = _messages.StringField(1)
  value = _messages.StringField(2)


class Execution(_messages.Message):
  r"""An Execution represents a collection of Steps. For instance, it could
  represent: - a mobile test executed across a range of device configurations
  - a jenkins job with a build step followed by a test step The maximum size
  of an execution message is 1 MiB. An Execution can be updated until its
  state is set to COMPLETE at which point it becomes immutable.

  Enums:
    StateValueValuesEnum: The initial state is IN_PROGRESS. The only legal
      state transitions is from IN_PROGRESS to COMPLETE. A PRECONDITION_FAILED
      will be returned if an invalid transition is requested. The state can
      only be set to COMPLETE once. A FAILED_PRECONDITION will be returned if
      the state is set to COMPLETE multiple times. If the state is set to
      COMPLETE, all the in-progress steps within the execution will be set as
      COMPLETE. If the outcome of the step is not set, the outcome will be set
      to INCONCLUSIVE. - In response always set - In create/update request:
      optional

  Fields:
    completionTime: The time when the Execution status transitioned to
      COMPLETE. This value will be set automatically when state transitions to
      COMPLETE. - In response: set if the execution state is COMPLETE. - In
      create/update request: never set
    creationTime: The time when the Execution was created. This value will be
      set automatically when CreateExecution is called. - In response: always
      set - In create/update request: never set
    dimensionDefinitions: The dimensions along which different steps in this
      execution may vary. This must remain fixed over the life of the
      execution. Returns INVALID_ARGUMENT if this field is set in an update
      request. Returns INVALID_ARGUMENT if the same name occurs in more than
      one dimension_definition. Returns INVALID_ARGUMENT if the size of the
      list is over 100. - In response: present if set by create - In create
      request: optional - In update request: never set
    executionId: A unique identifier within a History for this Execution.
      Returns INVALID_ARGUMENT if this field is set or overwritten by the
      caller. - In response always set - In create/update request: never set
    outcome: Classify the result, for example into SUCCESS or FAILURE - In
      response: present if set by create/update request - In create/update
      request: optional
    specification: Lightweight information about execution request. - In
      response: present if set by create - In create: optional - In update:
      optional
    state: The initial state is IN_PROGRESS. The only legal state transitions
      is from IN_PROGRESS to COMPLETE. A PRECONDITION_FAILED will be returned
      if an invalid transition is requested. The state can only be set to
      COMPLETE once. A FAILED_PRECONDITION will be returned if the state is
      set to COMPLETE multiple times. If the state is set to COMPLETE, all the
      in-progress steps within the execution will be set as COMPLETE. If the
      outcome of the step is not set, the outcome will be set to INCONCLUSIVE.
      - In response always set - In create/update request: optional
    testExecutionMatrixId: TestExecution Matrix ID that the
      TestExecutionService uses. - In response: present if set by create - In
      create: optional - In update: never set
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The initial state is IN_PROGRESS. The only legal state transitions is
    from IN_PROGRESS to COMPLETE. A PRECONDITION_FAILED will be returned if an
    invalid transition is requested. The state can only be set to COMPLETE
    once. A FAILED_PRECONDITION will be returned if the state is set to
    COMPLETE multiple times. If the state is set to COMPLETE, all the in-
    progress steps within the execution will be set as COMPLETE. If the
    outcome of the step is not set, the outcome will be set to INCONCLUSIVE. -
    In response always set - In create/update request: optional

    Values:
      unknownState: Should never be in this state. Exists for proto
        deserialization backward compatibility.
      pending: The Execution/Step is created, ready to run, but not running
        yet. If an Execution/Step is created without initial state, it is
        assumed that the Execution/Step is in PENDING state.
      inProgress: The Execution/Step is in progress.
      complete: The finalized, immutable state. Steps/Executions in this state
        cannot be modified.
    """
    unknownState = 0
    pending = 1
    inProgress = 2
    complete = 3

  completionTime = _messages.MessageField('Timestamp', 1)
  creationTime = _messages.MessageField('Timestamp', 2)
  dimensionDefinitions = _messages.MessageField('MatrixDimensionDefinition', 3, repeated=True)
  executionId = _messages.StringField(4)
  outcome = _messages.MessageField('Outcome', 5)
  specification = _messages.MessageField('Specification', 6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  testExecutionMatrixId = _messages.StringField(8)


class FailedToInstall(_messages.Message):
  r"""Failed to install the App."""


class FailureDetail(_messages.Message):
  r"""Details for an outcome with a FAILURE outcome summary.

  Fields:
    crashed: If the failure was severe because the system (app) under test
      crashed.
    deviceOutOfMemory: If the device ran out of memory during a test, causing
      the test to crash.
    failedRoboscript: If the Roboscript failed to complete successfully, e.g.,
      because a Roboscript action or assertion failed or a Roboscript action
      could not be matched during the entire crawl.
    notInstalled: If an app is not installed and thus no test can be run with
      the app. This might be caused by trying to run a test on an unsupported
      platform.
    otherNativeCrash: If a native process (including any other than the app)
      crashed.
    timedOut: If the test overran some time limit, and that is why it failed.
    unableToCrawl: If the robo was unable to crawl the app; perhaps because
      the app did not start.
  """

  crashed = _messages.BooleanField(1)
  deviceOutOfMemory = _messages.BooleanField(2)
  failedRoboscript = _messages.BooleanField(3)
  notInstalled = _messages.BooleanField(4)
  otherNativeCrash = _messages.BooleanField(5)
  timedOut = _messages.BooleanField(6)
  unableToCrawl = _messages.BooleanField(7)


class FatalException(_messages.Message):
  r"""Additional details for a fatal exception.

  Fields:
    stackTrace: The stack trace of the fatal exception. Optional.
  """

  stackTrace = _messages.MessageField('StackTrace', 1)


class FileReference(_messages.Message):
  r"""A reference to a file.

  Fields:
    fileUri: The URI of a file stored in Google Cloud Storage. For example:
      http://storage.googleapis.com/mybucket/path/to/test.xml or in gsutil
      format: gs://mybucket/path/to/test.xml with version-specific info,
      gs://mybucket/path/to/test.xml#1360383693690000 An INVALID_ARGUMENT
      error will be returned if the URI format is not supported. - In
      response: always set - In create/update request: always set
  """

  fileUri = _messages.StringField(1)


class GraphicsStats(_messages.Message):
  r"""Graphics statistics for the App. The information is collected from 'adb
  shell dumpsys graphicsstats'. For more info see:
  https://developer.android.com/training/testing/performance.html Statistics
  will only be present for API 23+.

  Fields:
    buckets: Histogram of frame render times. There should be 154 buckets
      ranging from [5ms, 6ms) to [4950ms, infinity)
    highInputLatencyCount: Total "high input latency" events.
    jankyFrames: Total frames with slow render time. Should be <=
      total_frames.
    missedVsyncCount: Total "missed vsync" events.
    p50Millis: 50th percentile frame render time in milliseconds.
    p90Millis: 90th percentile frame render time in milliseconds.
    p95Millis: 95th percentile frame render time in milliseconds.
    p99Millis: 99th percentile frame render time in milliseconds.
    slowBitmapUploadCount: Total "slow bitmap upload" events.
    slowDrawCount: Total "slow draw" events.
    slowUiThreadCount: Total "slow UI thread" events.
    totalFrames: Total frames rendered by package.
  """

  buckets = _messages.MessageField('GraphicsStatsBucket', 1, repeated=True)
  highInputLatencyCount = _messages.IntegerField(2)
  jankyFrames = _messages.IntegerField(3)
  missedVsyncCount = _messages.IntegerField(4)
  p50Millis = _messages.IntegerField(5)
  p90Millis = _messages.IntegerField(6)
  p95Millis = _messages.IntegerField(7)
  p99Millis = _messages.IntegerField(8)
  slowBitmapUploadCount = _messages.IntegerField(9)
  slowDrawCount = _messages.IntegerField(10)
  slowUiThreadCount = _messages.IntegerField(11)
  totalFrames = _messages.IntegerField(12)


class GraphicsStatsBucket(_messages.Message):
  r"""A GraphicsStatsBucket object.

  Fields:
    frameCount: Number of frames in the bucket.
    renderMillis: Lower bound of render time in milliseconds.
  """

  frameCount = _messages.IntegerField(1)
  renderMillis = _messages.IntegerField(2)


class History(_messages.Message):
  r"""A History represents a sorted list of Executions ordered by the
  start_timestamp_millis field (descending). It can be used to group all the
  Executions of a continuous build. Note that the ordering only operates on
  one-dimension. If a repository has multiple branches, it means that multiple
  histories will need to be used in order to order Executions per branch.

  Enums:
    TestPlatformValueValuesEnum: The platform of the test history. - In
      response: always set. Returns the platform of the last execution if
      unknown.

  Fields:
    displayName: A short human-readable (plain text) name to display in the
      UI. Maximum of 100 characters. - In response: present if set during
      create. - In create request: optional
    historyId: A unique identifier within a project for this History. Returns
      INVALID_ARGUMENT if this field is set or overwritten by the caller. - In
      response always set - In create request: never set
    name: A name to uniquely identify a history within a project. Maximum of
      200 characters. - In response always set - In create request: always set
    testPlatform: The platform of the test history. - In response: always set.
      Returns the platform of the last execution if unknown.
  """

  class TestPlatformValueValuesEnum(_messages.Enum):
    r"""The platform of the test history. - In response: always set. Returns
    the platform of the last execution if unknown.

    Values:
      unknownPlatform: <no description>
      android: <no description>
      ios: <no description>
    """
    unknownPlatform = 0
    android = 1
    ios = 2

  displayName = _messages.StringField(1)
  historyId = _messages.StringField(2)
  name = _messages.StringField(3)
  testPlatform = _messages.EnumField('TestPlatformValueValuesEnum', 4)


class Image(_messages.Message):
  r"""An image, with a link to the main image and a thumbnail.

  Fields:
    error: An error explaining why the thumbnail could not be rendered.
    sourceImage: A reference to the full-size, original image. This is the
      same as the tool_outputs entry for the image under its Step. Always set.
    stepId: The step to which the image is attached. Always set.
    thumbnail: The thumbnail.
  """

  error = _messages.MessageField('Status', 1)
  sourceImage = _messages.MessageField('ToolOutputReference', 2)
  stepId = _messages.StringField(3)
  thumbnail = _messages.MessageField('Thumbnail', 4)


class InAppPurchasesFound(_messages.Message):
  r"""Additional details of in-app purchases encountered during the crawl.

  Fields:
    inAppPurchasesFlowsExplored: The total number of in-app purchases flows
      explored: how many times the robo tries to buy a SKU.
    inAppPurchasesFlowsStarted: The total number of in-app purchases flows
      started.
  """

  inAppPurchasesFlowsExplored = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  inAppPurchasesFlowsStarted = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class InconclusiveDetail(_messages.Message):
  r"""Details for an outcome with an INCONCLUSIVE outcome summary.

  Fields:
    abortedByUser: If the end user aborted the test execution before a pass or
      fail could be determined. For example, the user pressed ctrl-c which
      sent a kill signal to the test runner while the test was running.
    hasErrorLogs: If results are being provided to the user in certain cases
      of infrastructure failures
    infrastructureFailure: If the test runner could not determine success or
      failure because the test depends on a component other than the system
      under test which failed. For example, a mobile test requires
      provisioning a device where the test executes, and that provisioning can
      fail.
  """

  abortedByUser = _messages.BooleanField(1)
  hasErrorLogs = _messages.BooleanField(2)
  infrastructureFailure = _messages.BooleanField(3)


class IndividualOutcome(_messages.Message):
  r"""Step Id and outcome of each individual step that was run as a group with
  other steps with the same configuration.

  Enums:
    OutcomeSummaryValueValuesEnum:

  Fields:
    multistepNumber: Unique int given to each step. Ranges from 0(inclusive)
      to total number of steps(exclusive). The primary step is 0.
    outcomeSummary: A OutcomeSummaryValueValuesEnum attribute.
    runDuration: How long it took for this step to run.
    stepId: A string attribute.
  """

  class OutcomeSummaryValueValuesEnum(_messages.Enum):
    r"""OutcomeSummaryValueValuesEnum enum type.

    Values:
      unset: Do not use. For proto versioning only.
      success: The test matrix run was successful, for instance: - All the
        test cases passed. - Robo did not detect a crash of the application
        under test.
      failure: A run failed, for instance: - One or more test case failed. - A
        test timed out. - The application under test crashed.
      inconclusive: Something unexpected happened. The run should still be
        considered unsuccessful but this is likely a transient problem and re-
        running the test might be successful.
      skipped: All tests were skipped, for instance: - All device
        configurations were incompatible.
      flaky: A group of steps that were run with the same configuration had
        both failure and success outcomes.
    """
    unset = 0
    success = 1
    failure = 2
    inconclusive = 3
    skipped = 4
    flaky = 5

  multistepNumber = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  outcomeSummary = _messages.EnumField('OutcomeSummaryValueValuesEnum', 2)
  runDuration = _messages.MessageField('Duration', 3)
  stepId = _messages.StringField(4)


class InsufficientCoverage(_messages.Message):
  r"""A warning that Robo did not crawl potentially important parts of the
  app.
  """



class IosAppCrashed(_messages.Message):
  r"""Additional details for an iOS app crash.

  Fields:
    stackTrace: The stack trace, if one is available. Optional.
  """

  stackTrace = _messages.MessageField('StackTrace', 1)


class IosAppInfo(_messages.Message):
  r"""iOS app information

  Fields:
    name: The name of the app. Required
  """

  name = _messages.StringField(1)


class IosRoboTest(_messages.Message):
  r"""A Robo test for an iOS application."""


class IosTest(_messages.Message):
  r"""A iOS mobile test specification

  Fields:
    iosAppInfo: Information about the application under test.
    iosRoboTest: An iOS Robo test.
    iosTestLoop: An iOS test loop.
    iosXcTest: An iOS XCTest.
    testTimeout: Max time a test is allowed to run before it is automatically
      cancelled.
  """

  iosAppInfo = _messages.MessageField('IosAppInfo', 1)
  iosRoboTest = _messages.MessageField('IosRoboTest', 2)
  iosTestLoop = _messages.MessageField('IosTestLoop', 3)
  iosXcTest = _messages.MessageField('IosXcTest', 4)
  testTimeout = _messages.MessageField('Duration', 5)


class IosTestLoop(_messages.Message):
  r"""A game loop test of an iOS application.

  Fields:
    bundleId: Bundle ID of the app.
  """

  bundleId = _messages.StringField(1)


class IosXcTest(_messages.Message):
  r"""A test of an iOS application that uses the XCTest framework.

  Fields:
    bundleId: Bundle ID of the app.
    xcodeVersion: Xcode version that the test was run with.
  """

  bundleId = _messages.StringField(1)
  xcodeVersion = _messages.StringField(2)


class LauncherActivityNotFound(_messages.Message):
  r"""Failed to find the launcher activity of an app."""


class ListEnvironmentsResponse(_messages.Message):
  r"""Response message for EnvironmentService.ListEnvironments.

  Fields:
    environments: Environments. Always set.
    executionId: A Execution id Always set.
    historyId: A History id. Always set.
    nextPageToken: A continuation token to resume the query at the next item.
      Will only be set if there are more Environments to fetch.
    projectId: A Project id. Always set.
  """

  environments = _messages.MessageField('Environment', 1, repeated=True)
  executionId = _messages.StringField(2)
  historyId = _messages.StringField(3)
  nextPageToken = _messages.StringField(4)
  projectId = _messages.StringField(5)


class ListExecutionsResponse(_messages.Message):
  r"""A ListExecutionsResponse object.

  Fields:
    executions: Executions. Always set.
    nextPageToken: A continuation token to resume the query at the next item.
      Will only be set if there are more Executions to fetch.
  """

  executions = _messages.MessageField('Execution', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListHistoriesResponse(_messages.Message):
  r"""Response message for HistoryService.List

  Fields:
    histories: Histories.
    nextPageToken: A continuation token to resume the query at the next item.
      Will only be set if there are more histories to fetch. Tokens are valid
      for up to one hour from the time of the first list request. For
      instance, if you make a list request at 1PM and use the token from this
      first request 10 minutes later, the token from this second response will
      only be valid for 50 minutes.
  """

  histories = _messages.MessageField('History', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListPerfSampleSeriesResponse(_messages.Message):
  r"""A ListPerfSampleSeriesResponse object.

  Fields:
    perfSampleSeries: The resulting PerfSampleSeries sorted by id
  """

  perfSampleSeries = _messages.MessageField('PerfSampleSeries', 1, repeated=True)


class ListPerfSamplesResponse(_messages.Message):
  r"""A ListPerfSamplesResponse object.

  Fields:
    nextPageToken: Optional, returned if result size exceeds the page size
      specified in the request (or the default page size, 500, if
      unspecified). It indicates the last sample timestamp to be used as
      page_token in subsequent request
    perfSamples: A PerfSample attribute.
  """

  nextPageToken = _messages.StringField(1)
  perfSamples = _messages.MessageField('PerfSample', 2, repeated=True)


class ListScreenshotClustersResponse(_messages.Message):
  r"""A ListScreenshotClustersResponse object.

  Fields:
    clusters: The set of clusters associated with an execution Always set
  """

  clusters = _messages.MessageField('ScreenshotCluster', 1, repeated=True)


class ListStepAccessibilityClustersResponse(_messages.Message):
  r"""Response message for AccessibilityService.ListStepAccessibilityClusters.

  Fields:
    clusters: A sequence of accessibility suggestions, grouped into clusters.
      Within the sequence, clusters that belong to the same SuggestionCategory
      should be adjacent. Within each category, clusters should be ordered by
      their SuggestionPriority (ERRORs first). The categories should be
      ordered by their highest priority cluster.
    name: A full resource name of the step. For example, projects/my-
      project/histories/bh.1234567890abcdef/executions/
      1234567890123456789/steps/bs.1234567890abcdef Always presents.
  """

  clusters = _messages.MessageField('SuggestionClusterProto', 1, repeated=True)
  name = _messages.StringField(2)


class ListStepThumbnailsResponse(_messages.Message):
  r"""A response containing the thumbnails in a step.

  Fields:
    nextPageToken: A continuation token to resume the query at the next item.
      If set, indicates that there are more thumbnails to read, by calling
      list again with this value in the page_token field.
    thumbnails: A list of image data. Images are returned in a deterministic
      order; they are ordered by these factors, in order of importance: *
      First, by their associated test case. Images without a test case are
      considered greater than images with one. * Second, by their creation
      time. Images without a creation time are greater than images with one. *
      Third, by the order in which they were added to the step (by calls to
      CreateStep or UpdateStep).
  """

  nextPageToken = _messages.StringField(1)
  thumbnails = _messages.MessageField('Image', 2, repeated=True)


class ListStepsResponse(_messages.Message):
  r"""Response message for StepService.List.

  Fields:
    nextPageToken: A continuation token to resume the query at the next item.
      If set, indicates that there are more steps to read, by calling list
      again with this value in the page_token field.
    steps: Steps.
  """

  nextPageToken = _messages.StringField(1)
  steps = _messages.MessageField('Step', 2, repeated=True)


class ListTestCasesResponse(_messages.Message):
  r"""Response message for StepService.ListTestCases.

  Fields:
    nextPageToken: A string attribute.
    testCases: List of test cases.
  """

  nextPageToken = _messages.StringField(1)
  testCases = _messages.MessageField('TestCase', 2, repeated=True)


class LogcatCollectionError(_messages.Message):
  r"""A warning that there were issues in logcat collection."""


class MatrixDimensionDefinition(_messages.Message):
  r"""One dimension of the matrix of different runs of a step."""


class MemoryInfo(_messages.Message):
  r"""A MemoryInfo object.

  Fields:
    memoryCapInKibibyte: Maximum memory that can be allocated to the process
      in KiB
    memoryTotalInKibibyte: Total memory available on the device in KiB
  """

  memoryCapInKibibyte = _messages.IntegerField(1)
  memoryTotalInKibibyte = _messages.IntegerField(2)


class MergedResult(_messages.Message):
  r"""Merged test result for environment. If the environment has only one step
  (no reruns or shards), then the merged result is the same as the step
  result. If the environment has multiple shards and/or reruns, then the
  results of shards and reruns that belong to the same environment are merged
  into one environment result.

  Enums:
    StateValueValuesEnum: State of the resource

  Fields:
    outcome: Outcome of the resource
    state: State of the resource
    testSuiteOverviews: The combined and rolled-up result of each test suite
      that was run as part of this environment. Combining: When the test cases
      from a suite are run in different steps (sharding), the results are
      added back together in one overview. (e.g., if shard1 has 2 failures and
      shard2 has 1 failure than the overview failure_count = 3). Rollup: When
      test cases from the same suite are run multiple times (flaky), the
      results are combined (e.g., if testcase1.run1 fails, testcase1.run2
      passes, and both testcase2.run1 and testcase2.run2 fail then the
      overview flaky_count = 1 and failure_count = 1).
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""State of the resource

    Values:
      unknownState: Should never be in this state. Exists for proto
        deserialization backward compatibility.
      pending: The Execution/Step is created, ready to run, but not running
        yet. If an Execution/Step is created without initial state, it is
        assumed that the Execution/Step is in PENDING state.
      inProgress: The Execution/Step is in progress.
      complete: The finalized, immutable state. Steps/Executions in this state
        cannot be modified.
    """
    unknownState = 0
    pending = 1
    inProgress = 2
    complete = 3

  outcome = _messages.MessageField('Outcome', 1)
  state = _messages.EnumField('StateValueValuesEnum', 2)
  testSuiteOverviews = _messages.MessageField('TestSuiteOverview', 3, repeated=True)


class MultiStep(_messages.Message):
  r"""Details when multiple steps are run with the same configuration as a
  group.

  Fields:
    multistepNumber: Unique int given to each step. Ranges from 0(inclusive)
      to total number of steps(exclusive). The primary step is 0.
    primaryStep: Present if it is a primary (original) step.
    primaryStepId: Step Id of the primary (original) step, which might be this
      step.
  """

  multistepNumber = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  primaryStep = _messages.MessageField('PrimaryStep', 2)
  primaryStepId = _messages.StringField(3)


class NativeCrash(_messages.Message):
  r"""Additional details for a native crash.

  Fields:
    stackTrace: The stack trace of the native crash. Optional.
  """

  stackTrace = _messages.MessageField('StackTrace', 1)


class NonSdkApi(_messages.Message):
  r"""A non-sdk API and examples of it being called along with other metadata
  See https://developer.android.com/distribute/best-
  practices/develop/restrictions-non-sdk-interfaces

  Enums:
    ListValueValuesEnum: Which list this API appears on

  Fields:
    apiSignature: The signature of the Non-SDK API
    exampleStackTraces: Example stack traces of this API being called.
    insights: Optional debugging insights for non-SDK API violations.
    invocationCount: The total number of times this API was observed to have
      been called.
    list: Which list this API appears on
  """

  class ListValueValuesEnum(_messages.Enum):
    r"""Which list this API appears on

    Values:
      NONE: <no description>
      WHITE: <no description>
      BLACK: <no description>
      GREY: <no description>
      GREY_MAX_O: <no description>
      GREY_MAX_P: <no description>
      GREY_MAX_Q: <no description>
      GREY_MAX_R: <no description>
      GREY_MAX_S: <no description>
    """
    NONE = 0
    WHITE = 1
    BLACK = 2
    GREY = 3
    GREY_MAX_O = 4
    GREY_MAX_P = 5
    GREY_MAX_Q = 6
    GREY_MAX_R = 7
    GREY_MAX_S = 8

  apiSignature = _messages.StringField(1)
  exampleStackTraces = _messages.StringField(2, repeated=True)
  insights = _messages.MessageField('NonSdkApiInsight', 3, repeated=True)
  invocationCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  list = _messages.EnumField('ListValueValuesEnum', 5)


class NonSdkApiInsight(_messages.Message):
  r"""Non-SDK API insights (to address debugging solutions).

  Fields:
    exampleTraceMessages: Optional sample stack traces, for which this insight
      applies (there should be at least one).
    matcherId: A unique ID, to be used for determining the effectiveness of
      this particular insight in the context of a matcher. (required)
    pendingGoogleUpdateInsight: An insight indicating that the hidden API
      usage originates from a Google-provided library.
    upgradeInsight: An insight indicating that the hidden API usage originates
      from the use of a library that needs to be upgraded.
  """

  exampleTraceMessages = _messages.StringField(1, repeated=True)
  matcherId = _messages.StringField(2)
  pendingGoogleUpdateInsight = _messages.MessageField('PendingGoogleUpdateInsight', 3)
  upgradeInsight = _messages.MessageField('UpgradeInsight', 4)


class NonSdkApiUsageViolation(_messages.Message):
  r"""Additional details for a non-sdk API usage violation.

  Fields:
    apiSignatures: Signatures of a subset of those hidden API's.
    uniqueApis: Total number of unique hidden API's accessed.
  """

  apiSignatures = _messages.StringField(1, repeated=True)
  uniqueApis = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class NonSdkApiUsageViolationReport(_messages.Message):
  r"""Contains a summary and examples of non-sdk API usage violations.

  Fields:
    exampleApis: Examples of the detected API usages.
    minSdkVersion: Minimum API level required for the application to run.
    targetSdkVersion: Specifies the API Level on which the application is
      designed to run.
    uniqueApis: Total number of unique Non-SDK API's accessed.
  """

  exampleApis = _messages.MessageField('NonSdkApi', 1, repeated=True)
  minSdkVersion = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  targetSdkVersion = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  uniqueApis = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class Outcome(_messages.Message):
  r"""Interprets a result so that humans and machines can act on it.

  Enums:
    SummaryValueValuesEnum: The simplest way to interpret a result. Required

  Fields:
    failureDetail: More information about a FAILURE outcome. Returns
      INVALID_ARGUMENT if this field is set but the summary is not FAILURE.
      Optional
    inconclusiveDetail: More information about an INCONCLUSIVE outcome.
      Returns INVALID_ARGUMENT if this field is set but the summary is not
      INCONCLUSIVE. Optional
    skippedDetail: More information about a SKIPPED outcome. Returns
      INVALID_ARGUMENT if this field is set but the summary is not SKIPPED.
      Optional
    successDetail: More information about a SUCCESS outcome. Returns
      INVALID_ARGUMENT if this field is set but the summary is not SUCCESS.
      Optional
    summary: The simplest way to interpret a result. Required
  """

  class SummaryValueValuesEnum(_messages.Enum):
    r"""The simplest way to interpret a result. Required

    Values:
      unset: Do not use. For proto versioning only.
      success: The test matrix run was successful, for instance: - All the
        test cases passed. - Robo did not detect a crash of the application
        under test.
      failure: A run failed, for instance: - One or more test case failed. - A
        test timed out. - The application under test crashed.
      inconclusive: Something unexpected happened. The run should still be
        considered unsuccessful but this is likely a transient problem and re-
        running the test might be successful.
      skipped: All tests were skipped, for instance: - All device
        configurations were incompatible.
      flaky: A group of steps that were run with the same configuration had
        both failure and success outcomes.
    """
    unset = 0
    success = 1
    failure = 2
    inconclusive = 3
    skipped = 4
    flaky = 5

  failureDetail = _messages.MessageField('FailureDetail', 1)
  inconclusiveDetail = _messages.MessageField('InconclusiveDetail', 2)
  skippedDetail = _messages.MessageField('SkippedDetail', 3)
  successDetail = _messages.MessageField('SuccessDetail', 4)
  summary = _messages.EnumField('SummaryValueValuesEnum', 5)


class OverlappingUIElements(_messages.Message):
  r"""A warning that Robo encountered a screen that has overlapping clickable
  elements; this may indicate a potential UI issue.

  Fields:
    resourceName: Resource names of the overlapping screen elements
    screenId: The screen id of the elements
  """

  resourceName = _messages.StringField(1, repeated=True)
  screenId = _messages.StringField(2)


class PendingGoogleUpdateInsight(_messages.Message):
  r"""This insight indicates that the hidden API usage originates from a
  Google-provided library. Users need not take any action.

  Fields:
    nameOfGoogleLibrary: The name of the Google-provided library with the non-
      SDK API dependency.
  """

  nameOfGoogleLibrary = _messages.StringField(1)


class PerfEnvironment(_messages.Message):
  r"""Encapsulates performance environment info

  Fields:
    cpuInfo: CPU related environment info
    memoryInfo: Memory related environment info
  """

  cpuInfo = _messages.MessageField('CPUInfo', 1)
  memoryInfo = _messages.MessageField('MemoryInfo', 2)


class PerfMetricsSummary(_messages.Message):
  r"""A summary of perf metrics collected and performance environment info

  Enums:
    PerfMetricsValueListEntryValuesEnum:

  Fields:
    appStartTime: A AppStartTime attribute.
    executionId: A tool results execution ID. @OutputOnly
    graphicsStats: Graphics statistics for the entire run. Statistics are
      reset at the beginning of the run and collected at the end of the run.
    historyId: A tool results history ID. @OutputOnly
    perfEnvironment: Describes the environment in which the performance
      metrics were collected
    perfMetrics: Set of resource collected
    projectId: The cloud project @OutputOnly
    stepId: A tool results step ID. @OutputOnly
  """

  class PerfMetricsValueListEntryValuesEnum(_messages.Enum):
    r"""PerfMetricsValueListEntryValuesEnum enum type.

    Values:
      perfMetricTypeUnspecified: <no description>
      memory: <no description>
      cpu: <no description>
      network: <no description>
      graphics: <no description>
    """
    perfMetricTypeUnspecified = 0
    memory = 1
    cpu = 2
    network = 3
    graphics = 4

  appStartTime = _messages.MessageField('AppStartTime', 1)
  executionId = _messages.StringField(2)
  graphicsStats = _messages.MessageField('GraphicsStats', 3)
  historyId = _messages.StringField(4)
  perfEnvironment = _messages.MessageField('PerfEnvironment', 5)
  perfMetrics = _messages.EnumField('PerfMetricsValueListEntryValuesEnum', 6, repeated=True)
  projectId = _messages.StringField(7)
  stepId = _messages.StringField(8)


class PerfSample(_messages.Message):
  r"""Resource representing a single performance measure or data point

  Fields:
    sampleTime: Timestamp of collection.
    value: Value observed
  """

  sampleTime = _messages.MessageField('Timestamp', 1)
  value = _messages.FloatField(2)


class PerfSampleSeries(_messages.Message):
  r"""Resource representing a collection of performance samples (or data
  points)

  Fields:
    basicPerfSampleSeries: Basic series represented by a line chart
    executionId: A tool results execution ID. @OutputOnly
    historyId: A tool results history ID. @OutputOnly
    projectId: The cloud project @OutputOnly
    sampleSeriesId: A sample series id @OutputOnly
    stepId: A tool results step ID. @OutputOnly
  """

  basicPerfSampleSeries = _messages.MessageField('BasicPerfSampleSeries', 1)
  executionId = _messages.StringField(2)
  historyId = _messages.StringField(3)
  projectId = _messages.StringField(4)
  sampleSeriesId = _messages.StringField(5)
  stepId = _messages.StringField(6)


class PerformedGoogleLogin(_messages.Message):
  r"""A notification that Robo signed in with Google."""


class PerformedMonkeyActions(_messages.Message):
  r"""A notification that Robo performed some monkey actions.

  Fields:
    totalActions: The total number of monkey actions performed during the
      crawl.
  """

  totalActions = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class PrimaryStep(_messages.Message):
  r"""Stores rollup test status of multiple steps that were run as a group and
  outcome of each individual step.

  Enums:
    RollUpValueValuesEnum: Rollup test status of multiple steps that were run
      with the same configuration as a group.

  Fields:
    individualOutcome: Step Id and outcome of each individual step.
    rollUp: Rollup test status of multiple steps that were run with the same
      configuration as a group.
  """

  class RollUpValueValuesEnum(_messages.Enum):
    r"""Rollup test status of multiple steps that were run with the same
    configuration as a group.

    Values:
      unset: Do not use. For proto versioning only.
      success: The test matrix run was successful, for instance: - All the
        test cases passed. - Robo did not detect a crash of the application
        under test.
      failure: A run failed, for instance: - One or more test case failed. - A
        test timed out. - The application under test crashed.
      inconclusive: Something unexpected happened. The run should still be
        considered unsuccessful but this is likely a transient problem and re-
        running the test might be successful.
      skipped: All tests were skipped, for instance: - All device
        configurations were incompatible.
      flaky: A group of steps that were run with the same configuration had
        both failure and success outcomes.
    """
    unset = 0
    success = 1
    failure = 2
    inconclusive = 3
    skipped = 4
    flaky = 5

  individualOutcome = _messages.MessageField('IndividualOutcome', 1, repeated=True)
  rollUp = _messages.EnumField('RollUpValueValuesEnum', 2)


class ProjectSettings(_messages.Message):
  r"""Per-project settings for the Tool Results service.

  Fields:
    defaultBucket: The name of the Google Cloud Storage bucket to which
      results are written. By default, this is unset. In update request:
      optional In response: optional
    name: The name of the project's settings. Always of the form:
      projects/{project-id}/settings In update request: never set In response:
      always set
  """

  defaultBucket = _messages.StringField(1)
  name = _messages.StringField(2)


class PublishXunitXmlFilesRequest(_messages.Message):
  r"""Request message for StepService.PublishXunitXmlFiles.

  Fields:
    xunitXmlFiles: URI of the Xunit XML files to publish. The maximum size of
      the file this reference is pointing to is 50MB. Required.
  """

  xunitXmlFiles = _messages.MessageField('FileReference', 1, repeated=True)


class RegionProto(_messages.Message):
  r"""A rectangular region.

  Fields:
    heightPx: The height, in pixels. Always set.
    leftPx: The left side of the rectangle, in pixels. Always set.
    topPx: The top of the rectangle, in pixels. Always set.
    widthPx: The width, in pixels. Always set.
  """

  heightPx = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  leftPx = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  topPx = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  widthPx = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class ResultsStorage(_messages.Message):
  r"""The storage for test results.

  Fields:
    resultsStoragePath: The root directory for test results.
    xunitXmlFile: The path to the Xunit XML file.
  """

  resultsStoragePath = _messages.MessageField('FileReference', 1)
  xunitXmlFile = _messages.MessageField('FileReference', 2)


class RoboScriptExecution(_messages.Message):
  r"""Execution stats for a user-provided Robo script.

  Fields:
    successfulActions: The number of Robo script actions executed
      successfully.
    totalActions: The total number of actions in the Robo script.
  """

  successfulActions = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  totalActions = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class SafeHtmlProto(_messages.Message):
  r"""IMPORTANT: It is unsafe to accept this message from an untrusted source,
  since it's trivial for an attacker to forge serialized messages that don't
  fulfill the type's safety contract -- for example, it could contain attacker
  controlled script. A system which receives a SafeHtmlProto implicitly trusts
  the producer of the SafeHtmlProto. So, it's generally safe to return this
  message in RPC responses, but generally unsafe to accept it in RPC requests.

  Fields:
    privateDoNotAccessOrElseSafeHtmlWrappedValue: IMPORTANT: Never set or read
      this field, even from tests, it is private. See documentation at the top
      of .proto file for programming language packages with which to create or
      read this message.
  """

  privateDoNotAccessOrElseSafeHtmlWrappedValue = _messages.StringField(1)


class Screen(_messages.Message):
  r"""A Screen object.

  Fields:
    fileReference: File reference of the png file. Required.
    locale: Locale of the device that the screenshot was taken on. Required.
    model: Model of the device that the screenshot was taken on. Required.
    version: OS version of the device that the screenshot was taken on.
      Required.
  """

  fileReference = _messages.StringField(1)
  locale = _messages.StringField(2)
  model = _messages.StringField(3)
  version = _messages.StringField(4)


class ScreenshotCluster(_messages.Message):
  r"""A ScreenshotCluster object.

  Fields:
    activity: A string that describes the activity of every screen in the
      cluster.
    clusterId: A unique identifier for the cluster. @OutputOnly
    keyScreen: A singular screen that represents the cluster as a whole. This
      screen will act as the "cover" of the entire cluster. When users look at
      the clusters, only the key screen from each cluster will be shown. Which
      screen is the key screen is determined by the ClusteringAlgorithm
    screens: Full list of screens.
  """

  activity = _messages.StringField(1)
  clusterId = _messages.StringField(2)
  keyScreen = _messages.MessageField('Screen', 3)
  screens = _messages.MessageField('Screen', 4, repeated=True)


class ShardSummary(_messages.Message):
  r"""Result summary for a shard in an environment.

  Fields:
    runs: Summaries of the steps belonging to the shard. With
      flaky_test_attempts enabled from TestExecutionService, more than one run
      (Step) can present. And the runs will be sorted by multistep_number.
    shardResult: Merged result of the shard.
  """

  runs = _messages.MessageField('StepSummary', 1, repeated=True)
  shardResult = _messages.MessageField('MergedResult', 2)


class SkippedDetail(_messages.Message):
  r"""Details for an outcome with a SKIPPED outcome summary.

  Fields:
    incompatibleAppVersion: If the App doesn't support the specific API level.
    incompatibleArchitecture: If the App doesn't run on the specific
      architecture, for example, x86.
    incompatibleDevice: If the requested OS version doesn't run on the
      specific device model.
  """

  incompatibleAppVersion = _messages.BooleanField(1)
  incompatibleArchitecture = _messages.BooleanField(2)
  incompatibleDevice = _messages.BooleanField(3)


class Specification(_messages.Message):
  r"""The details about how to run the execution.

  Fields:
    androidTest: An Android mobile test execution specification.
    iosTest: An iOS mobile test execution specification.
  """

  androidTest = _messages.MessageField('AndroidTest', 1)
  iosTest = _messages.MessageField('IosTest', 2)


class StackTrace(_messages.Message):
  r"""A stacktrace.

  Fields:
    exception: The stack trace message. Required
  """

  exception = _messages.StringField(1)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StartActivityNotFound(_messages.Message):
  r"""User provided intent failed to resolve to an activity.

  Fields:
    action: A string attribute.
    uri: A string attribute.
  """

  action = _messages.StringField(1)
  uri = _messages.StringField(2)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class Step(_messages.Message):
  r"""A Step represents a single operation performed as part of Execution. A
  step can be used to represent the execution of a tool ( for example a test
  runner execution or an execution of a compiler). Steps can overlap (for
  instance two steps might have the same start time if some operations are
  done in parallel). Here is an example, let's consider that we have a
  continuous build is executing a test runner for each iteration. The workflow
  would look like: - user creates a Execution with id 1 - user creates a
  TestExecutionStep with id 100 for Execution 1 - user update
  TestExecutionStep with id 100 to add a raw xml log + the service parses the
  xml logs and returns a TestExecutionStep with updated TestResult(s). - user
  update the status of TestExecutionStep with id 100 to COMPLETE A Step can be
  updated until its state is set to COMPLETE at which points it becomes
  immutable.

  Enums:
    StateValueValuesEnum: The initial state is IN_PROGRESS. The only legal
      state transitions are * IN_PROGRESS -> COMPLETE A PRECONDITION_FAILED
      will be returned if an invalid transition is requested. It is valid to
      create Step with a state set to COMPLETE. The state can only be set to
      COMPLETE once. A PRECONDITION_FAILED will be returned if the state is
      set to COMPLETE multiple times. - In response: always set - In
      create/update request: optional

  Fields:
    completionTime: The time when the step status was set to complete. This
      value will be set automatically when state transitions to COMPLETE. - In
      response: set if the execution state is COMPLETE. - In create/update
      request: never set
    creationTime: The time when the step was created. - In response: always
      set - In create/update request: never set
    description: A description of this tool For example: mvn clean package -D
      skipTests=true - In response: present if set by create/update request -
      In create/update request: optional
    deviceUsageDuration: How much the device resource is used to perform the
      test. This is the device usage used for billing purpose, which is
      different from the run_duration, for example, infrastructure failure
      won't be charged for device usage. PRECONDITION_FAILED will be returned
      if one attempts to set a device_usage on a step which already has this
      field set. - In response: present if previously set. - In create
      request: optional - In update request: optional
    dimensionValue: If the execution containing this step has any
      dimension_definition set, then this field allows the child to specify
      the values of the dimensions. The keys must exactly match the
      dimension_definition of the execution. For example, if the execution has
      `dimension_definition = ['attempt', 'device']` then a step must define
      values for those dimensions, eg. `dimension_value = ['attempt': '1',
      'device': 'Nexus 6']` If a step does not participate in one dimension of
      the matrix, the value for that dimension should be empty string. For
      example, if one of the tests is executed by a runner which does not
      support retries, the step could have `dimension_value = ['attempt': '',
      'device': 'Nexus 6']` If the step does not participate in any dimensions
      of the matrix, it may leave dimension_value unset. A PRECONDITION_FAILED
      will be returned if any of the keys do not exist in the
      dimension_definition of the execution. A PRECONDITION_FAILED will be
      returned if another step in this execution already has the same name and
      dimension_value, but differs on other data fields, for example, step
      field is different. A PRECONDITION_FAILED will be returned if
      dimension_value is set, and there is a dimension_definition in the
      execution which is not specified as one of the keys. - In response:
      present if set by create - In create request: optional - In update
      request: never set
    hasImages: Whether any of the outputs of this step are images whose
      thumbnails can be fetched with ListThumbnails. - In response: always set
      - In create/update request: never set
    labels: Arbitrary user-supplied key/value pairs that are associated with
      the step. Users are responsible for managing the key namespace such that
      keys don't accidentally collide. An INVALID_ARGUMENT will be returned if
      the number of labels exceeds 100 or if the length of any of the keys or
      values exceeds 100 characters. - In response: always set - In create
      request: optional - In update request: optional; any new key/value pair
      will be added to the map, and any new value for an existing key will
      update that key's value
    multiStep: Details when multiple steps are run with the same configuration
      as a group. These details can be used identify which group this step is
      part of. It also identifies the groups 'primary step' which indexes all
      the group members. - In response: present if previously set. - In create
      request: optional, set iff this step was performed more than once. - In
      update request: optional
    name: A short human-readable name to display in the UI. Maximum of 100
      characters. For example: Clean build A PRECONDITION_FAILED will be
      returned upon creating a new step if it shares its name and
      dimension_value with an existing step. If two steps represent a similar
      action, but have different dimension values, they should share the same
      name. For instance, if the same set of tests is run on two different
      platforms, the two steps should have the same name. - In response:
      always set - In create request: always set - In update request: never
      set
    outcome: Classification of the result, for example into SUCCESS or FAILURE
      - In response: present if set by create/update request - In
      create/update request: optional
    runDuration: How long it took for this step to run. If unset, this is set
      to the difference between creation_time and completion_time when the
      step is set to the COMPLETE state. In some cases, it is appropriate to
      set this value separately: For instance, if a step is created, but the
      operation it represents is queued for a few minutes before it executes,
      it would be appropriate not to include the time spent queued in its
      run_duration. PRECONDITION_FAILED will be returned if one attempts to
      set a run_duration on a step which already has this field set. - In
      response: present if previously set; always present on COMPLETE step -
      In create request: optional - In update request: optional
    state: The initial state is IN_PROGRESS. The only legal state transitions
      are * IN_PROGRESS -> COMPLETE A PRECONDITION_FAILED will be returned if
      an invalid transition is requested. It is valid to create Step with a
      state set to COMPLETE. The state can only be set to COMPLETE once. A
      PRECONDITION_FAILED will be returned if the state is set to COMPLETE
      multiple times. - In response: always set - In create/update request:
      optional
    stepId: A unique identifier within a Execution for this Step. Returns
      INVALID_ARGUMENT if this field is set or overwritten by the caller. - In
      response: always set - In create/update request: never set
    testExecutionStep: An execution of a test runner.
    toolExecutionStep: An execution of a tool (used for steps we don't
      explicitly support).
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The initial state is IN_PROGRESS. The only legal state transitions are
    * IN_PROGRESS -> COMPLETE A PRECONDITION_FAILED will be returned if an
    invalid transition is requested. It is valid to create Step with a state
    set to COMPLETE. The state can only be set to COMPLETE once. A
    PRECONDITION_FAILED will be returned if the state is set to COMPLETE
    multiple times. - In response: always set - In create/update request:
    optional

    Values:
      unknownState: Should never be in this state. Exists for proto
        deserialization backward compatibility.
      pending: The Execution/Step is created, ready to run, but not running
        yet. If an Execution/Step is created without initial state, it is
        assumed that the Execution/Step is in PENDING state.
      inProgress: The Execution/Step is in progress.
      complete: The finalized, immutable state. Steps/Executions in this state
        cannot be modified.
    """
    unknownState = 0
    pending = 1
    inProgress = 2
    complete = 3

  completionTime = _messages.MessageField('Timestamp', 1)
  creationTime = _messages.MessageField('Timestamp', 2)
  description = _messages.StringField(3)
  deviceUsageDuration = _messages.MessageField('Duration', 4)
  dimensionValue = _messages.MessageField('StepDimensionValueEntry', 5, repeated=True)
  hasImages = _messages.BooleanField(6)
  labels = _messages.MessageField('StepLabelsEntry', 7, repeated=True)
  multiStep = _messages.MessageField('MultiStep', 8)
  name = _messages.StringField(9)
  outcome = _messages.MessageField('Outcome', 10)
  runDuration = _messages.MessageField('Duration', 11)
  state = _messages.EnumField('StateValueValuesEnum', 12)
  stepId = _messages.StringField(13)
  testExecutionStep = _messages.MessageField('TestExecutionStep', 14)
  toolExecutionStep = _messages.MessageField('ToolExecutionStep', 15)


class StepDimensionValueEntry(_messages.Message):
  r"""A StepDimensionValueEntry object.

  Fields:
    key: A string attribute.
    value: A string attribute.
  """

  key = _messages.StringField(1)
  value = _messages.StringField(2)


class StepLabelsEntry(_messages.Message):
  r"""A StepLabelsEntry object.

  Fields:
    key: A string attribute.
    value: A string attribute.
  """

  key = _messages.StringField(1)
  value = _messages.StringField(2)


class StepSummary(_messages.Message):
  r"""Lightweight summary of a step within this execution."""


class SuccessDetail(_messages.Message):
  r"""Details for an outcome with a SUCCESS outcome summary. LINT.IfChange

  Fields:
    otherNativeCrash: If a native process other than the app crashed.
  """

  otherNativeCrash = _messages.BooleanField(1)


class SuggestionClusterProto(_messages.Message):
  r"""A set of similar suggestions that we suspect are closely related. This
  proto and most of the nested protos are branched from
  foxandcrown.prelaunchreport.service.SuggestionClusterProto, replacing PLR's
  dependencies with FTL's.

  Enums:
    CategoryValueValuesEnum: Category in which these types of suggestions
      should appear. Always set.

  Fields:
    category: Category in which these types of suggestions should appear.
      Always set.
    suggestions: A sequence of suggestions. All of the suggestions within a
      cluster must have the same SuggestionPriority and belong to the same
      SuggestionCategory. Suggestions with the same screenshot URL should be
      adjacent.
  """

  class CategoryValueValuesEnum(_messages.Enum):
    r"""Category in which these types of suggestions should appear. Always
    set.

    Values:
      unknownCategory: <no description>
      contentLabeling: <no description>
      touchTargetSize: <no description>
      lowContrast: <no description>
      implementation: <no description>
    """
    unknownCategory = 0
    contentLabeling = 1
    touchTargetSize = 2
    lowContrast = 3
    implementation = 4

  category = _messages.EnumField('CategoryValueValuesEnum', 1)
  suggestions = _messages.MessageField('SuggestionProto', 2, repeated=True)


class SuggestionProto(_messages.Message):
  r"""A SuggestionProto object.

  Enums:
    PriorityValueValuesEnum: Relative importance of a suggestion. Always set.

  Fields:
    helpUrl: Reference to a help center article concerning this type of
      suggestion. Always set.
    longMessage: Message, in the user's language, explaining the suggestion,
      which may contain markup. Always set.
    priority: Relative importance of a suggestion. Always set.
    pseudoResourceId: A somewhat human readable identifier of the source view,
      if it does not have a resource_name. This is a path within the
      accessibility hierarchy, an element with resource name; similar to an
      XPath.
    region: Region within the screenshot that is relevant to this suggestion.
      Optional.
    resourceName: Reference to a view element, identified by its resource
      name, if it has one.
    screenId: ID of the screen for the suggestion. It is used for getting the
      corresponding screenshot path. For example, screen_id "1" corresponds to
      "1.png" file in GCS. Always set.
    secondaryPriority: Relative importance of a suggestion as compared with
      other suggestions that have the same priority and category. This is a
      meaningless value that can be used to order suggestions that are in the
      same category and have the same priority. The larger values have higher
      priority (i.e., are more important). Optional.
    shortMessage: Concise message, in the user's language, representing the
      suggestion, which may contain markup. Always set.
    title: General title for the suggestion, in the user's language, without
      markup. Always set.
  """

  class PriorityValueValuesEnum(_messages.Enum):
    r"""Relative importance of a suggestion. Always set.

    Values:
      unknownPriority: <no description>
      error: <no description>
      warning: <no description>
      info: <no description>
    """
    unknownPriority = 0
    error = 1
    warning = 2
    info = 3

  helpUrl = _messages.StringField(1)
  longMessage = _messages.MessageField('SafeHtmlProto', 2)
  priority = _messages.EnumField('PriorityValueValuesEnum', 3)
  pseudoResourceId = _messages.StringField(4)
  region = _messages.MessageField('RegionProto', 5)
  resourceName = _messages.StringField(6)
  screenId = _messages.StringField(7)
  secondaryPriority = _messages.FloatField(8)
  shortMessage = _messages.MessageField('SafeHtmlProto', 9)
  title = _messages.StringField(10)


class TestCase(_messages.Message):
  r"""A TestCase object.

  Enums:
    StatusValueValuesEnum: The status of the test case. Required.

  Fields:
    elapsedTime: The elapsed run time of the test case. Required.
    endTime: The end time of the test case.
    skippedMessage: Why the test case was skipped. Present only for skipped
      test case
    stackTraces: The stack trace details if the test case failed or
      encountered an error. The maximum size of the stack traces is 100KiB,
      beyond which the stack track will be truncated. Zero if the test case
      passed.
    startTime: The start time of the test case.
    status: The status of the test case. Required.
    testCaseId: A unique identifier within a Step for this Test Case.
    testCaseReference: Test case reference, e.g. name, class name and test
      suite name. Required.
    toolOutputs: References to opaque files of any format output by the tool
      execution. @OutputOnly
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""The status of the test case. Required.

    Values:
      passed: Test passed.
      failed: Test failed.
      error: Test encountered an error
      skipped: Test skipped
      flaky: Test flaked. Present only for rollup test cases; test cases from
        steps that were run with the same configuration had both failure and
        success outcomes.
    """
    passed = 0
    failed = 1
    error = 2
    skipped = 3
    flaky = 4

  elapsedTime = _messages.MessageField('Duration', 1)
  endTime = _messages.MessageField('Timestamp', 2)
  skippedMessage = _messages.StringField(3)
  stackTraces = _messages.MessageField('StackTrace', 4, repeated=True)
  startTime = _messages.MessageField('Timestamp', 5)
  status = _messages.EnumField('StatusValueValuesEnum', 6)
  testCaseId = _messages.StringField(7)
  testCaseReference = _messages.MessageField('TestCaseReference', 8)
  toolOutputs = _messages.MessageField('ToolOutputReference', 9, repeated=True)


class TestCaseReference(_messages.Message):
  r"""A reference to a test case. Test case references are canonically ordered
  lexicographically by these three factors: * First, by test_suite_name. *
  Second, by class_name. * Third, by name.

  Fields:
    className: The name of the class.
    name: The name of the test case. Required.
    testSuiteName: The name of the test suite to which this test case belongs.
  """

  className = _messages.StringField(1)
  name = _messages.StringField(2)
  testSuiteName = _messages.StringField(3)


class TestExecutionStep(_messages.Message):
  r"""A step that represents running tests. It accepts ant-junit xml files
  which will be parsed into structured test results by the service. Xml file
  paths are updated in order to append more files, however they can't be
  deleted. Users can also add test results manually by using the test_result
  field.

  Fields:
    testIssues: Issues observed during the test execution. For example, if the
      mobile app under test crashed during the test, the error message and the
      stack trace content can be recorded here to assist debugging. - In
      response: present if set by create or update - In create/update request:
      optional
    testSuiteOverviews: List of test suite overview contents. This could be
      parsed from xUnit XML log by server, or uploaded directly by user. This
      references should only be called when test suites are fully parsed or
      uploaded. The maximum allowed number of test suite overviews per step is
      1000. - In response: always set - In create request: optional - In
      update request: never (use publishXunitXmlFiles custom method instead)
    testTiming: The timing break down of the test execution. - In response:
      present if set by create or update - In create/update request: optional
    toolExecution: Represents the execution of the test runner. The exit code
      of this tool will be used to determine if the test passed. - In
      response: always set - In create/update request: optional
  """

  testIssues = _messages.MessageField('TestIssue', 1, repeated=True)
  testSuiteOverviews = _messages.MessageField('TestSuiteOverview', 2, repeated=True)
  testTiming = _messages.MessageField('TestTiming', 3)
  toolExecution = _messages.MessageField('ToolExecution', 4)


class TestIssue(_messages.Message):
  r"""An issue detected occurring during a test execution.

  Enums:
    CategoryValueValuesEnum: Category of issue. Required.
    SeverityValueValuesEnum: Severity of issue. Required.
    TypeValueValuesEnum: Type of issue. Required.

  Fields:
    category: Category of issue. Required.
    errorMessage: A brief human-readable message describing the issue.
      Required.
    severity: Severity of issue. Required.
    stackTrace: Deprecated in favor of stack trace fields inside specific
      warnings.
    type: Type of issue. Required.
    warning: Warning message with additional details of the issue. Should
      always be a message from com.google.devtools.toolresults.v1.warnings
  """

  class CategoryValueValuesEnum(_messages.Enum):
    r"""Category of issue. Required.

    Values:
      unspecifiedCategory: Default unspecified category. Do not use. For
        versioning only.
      common: Issue is not specific to a particular test kind (e.g., a native
        crash).
      robo: Issue is specific to Robo run.
    """
    unspecifiedCategory = 0
    common = 1
    robo = 2

  class SeverityValueValuesEnum(_messages.Enum):
    r"""Severity of issue. Required.

    Values:
      unspecifiedSeverity: Default unspecified severity. Do not use. For
        versioning only.
      info: Non critical issue, providing users with some info about the test
        run.
      suggestion: Non critical issue, providing users with some hints on
        improving their testing experience, e.g., suggesting to use Game
        Loops.
      warning: Potentially critical issue.
      severe: Critical issue.
    """
    unspecifiedSeverity = 0
    info = 1
    suggestion = 2
    warning = 3
    severe = 4

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of issue. Required.

    Values:
      unspecifiedType: Default unspecified type. Do not use. For versioning
        only.
      fatalException: Issue is a fatal exception.
      nativeCrash: Issue is a native crash.
      anr: Issue is an ANR crash.
      unusedRoboDirective: Issue is an unused robo directive.
      compatibleWithOrchestrator: Issue is a suggestion to use orchestrator.
      launcherActivityNotFound: Issue with finding a launcher activity
      startActivityNotFound: Issue with resolving a user-provided intent to
        start an activity
      incompleteRoboScriptExecution: A Robo script was not fully executed.
      completeRoboScriptExecution: A Robo script was fully and successfully
        executed.
      failedToInstall: The APK failed to install.
      availableDeepLinks: The app-under-test has deep links, but none were
        provided to Robo.
      nonSdkApiUsageViolation: App accessed a non-sdk Api.
      nonSdkApiUsageReport: App accessed a non-sdk Api (new detailed report)
      encounteredNonAndroidUiWidgetScreen: Robo crawl encountered at least one
        screen with elements that are not Android UI widgets.
      encounteredLoginScreen: Robo crawl encountered at least one probable
        login screen.
      performedGoogleLogin: Robo signed in with Google.
      iosException: iOS App crashed with an exception.
      iosCrash: iOS App crashed without an exception (e.g. killed).
      performedMonkeyActions: Robo crawl involved performing some monkey
        actions.
      usedRoboDirective: Robo crawl used a Robo directive.
      usedRoboIgnoreDirective: Robo crawl used a Robo directive to ignore an
        UI element.
      insufficientCoverage: Robo did not crawl some potentially important
        parts of the app.
      inAppPurchases: Robo crawl involved some in-app purchases.
      crashDialogError: Crash dialog was detected during the test execution
      uiElementsTooDeep: UI element depth is greater than the threshold
      blankScreen: Blank screen is found in the Robo crawl
      overlappingUiElements: Overlapping UI elements are found in the Robo
        crawl
      unityException: An uncaught Unity exception was detected (these don't
        crash apps).
      deviceOutOfMemory: Device running out of memory was detected
      logcatCollectionError: Problems detected while collecting logcat
      detectedAppSplashScreen: Robo detected a splash screen provided by app
        (vs. Android OS splash screen).
      assetIssue: There was an issue with the assets in this test.
    """
    unspecifiedType = 0
    fatalException = 1
    nativeCrash = 2
    anr = 3
    unusedRoboDirective = 4
    compatibleWithOrchestrator = 5
    launcherActivityNotFound = 6
    startActivityNotFound = 7
    incompleteRoboScriptExecution = 8
    completeRoboScriptExecution = 9
    failedToInstall = 10
    availableDeepLinks = 11
    nonSdkApiUsageViolation = 12
    nonSdkApiUsageReport = 13
    encounteredNonAndroidUiWidgetScreen = 14
    encounteredLoginScreen = 15
    performedGoogleLogin = 16
    iosException = 17
    iosCrash = 18
    performedMonkeyActions = 19
    usedRoboDirective = 20
    usedRoboIgnoreDirective = 21
    insufficientCoverage = 22
    inAppPurchases = 23
    crashDialogError = 24
    uiElementsTooDeep = 25
    blankScreen = 26
    overlappingUiElements = 27
    unityException = 28
    deviceOutOfMemory = 29
    logcatCollectionError = 30
    detectedAppSplashScreen = 31
    assetIssue = 32

  category = _messages.EnumField('CategoryValueValuesEnum', 1)
  errorMessage = _messages.StringField(2)
  severity = _messages.EnumField('SeverityValueValuesEnum', 3)
  stackTrace = _messages.MessageField('StackTrace', 4)
  type = _messages.EnumField('TypeValueValuesEnum', 5)
  warning = _messages.MessageField('Any', 6)


class TestSuiteOverview(_messages.Message):
  r"""A summary of a test suite result either parsed from XML or uploaded
  directly by a user. Note: the API related comments are for StepService only.
  This message is also being used in ExecutionService in a read only mode for
  the corresponding step.

  Fields:
    elapsedTime: Elapsed time of test suite.
    errorCount: Number of test cases in error, typically set by the service by
      parsing the xml_source. - In create/response: always set - In update
      request: never
    failureCount: Number of failed test cases, typically set by the service by
      parsing the xml_source. May also be set by the user. - In
      create/response: always set - In update request: never
    flakyCount: Number of flaky test cases, set by the service by rolling up
      flaky test attempts. Present only for rollup test suite overview at
      environment level. A step cannot have flaky test cases.
    name: The name of the test suite. - In create/response: always set - In
      update request: never
    skippedCount: Number of test cases not run, typically set by the service
      by parsing the xml_source. - In create/response: always set - In update
      request: never
    totalCount: Number of test cases, typically set by the service by parsing
      the xml_source. - In create/response: always set - In update request:
      never
    xmlSource: If this test suite was parsed from XML, this is the URI where
      the original XML file is stored. Note: Multiple test suites can share
      the same xml_source Returns INVALID_ARGUMENT if the uri format is not
      supported. - In create/response: optional - In update request: never
  """

  elapsedTime = _messages.MessageField('Duration', 1)
  errorCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  failureCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  flakyCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  name = _messages.StringField(5)
  skippedCount = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  totalCount = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  xmlSource = _messages.MessageField('FileReference', 8)


class TestTiming(_messages.Message):
  r"""Testing timing break down to know phases.

  Fields:
    testProcessDuration: How long it took to run the test process. - In
      response: present if previously set. - In create/update request:
      optional
  """

  testProcessDuration = _messages.MessageField('Duration', 1)


class Thumbnail(_messages.Message):
  r"""A single thumbnail, with its size and format.

  Fields:
    contentType: The thumbnail's content type, i.e. "image/png". Always set.
    data: The thumbnail file itself. That is, the bytes here are precisely the
      bytes that make up the thumbnail file; they can be served as an image
      as-is (with the appropriate content type.) Always set.
    heightPx: The height of the thumbnail, in pixels. Always set.
    widthPx: The width of the thumbnail, in pixels. Always set.
  """

  contentType = _messages.StringField(1)
  data = _messages.BytesField(2)
  heightPx = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  widthPx = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class Timestamp(_messages.Message):
  r"""A Timestamp represents a point in time independent of any time zone or
  local calendar, encoded as a count of seconds and fractions of seconds at
  nanosecond resolution. The count is relative to an epoch at UTC midnight on
  January 1, 1970, in the proleptic Gregorian calendar which extends the
  Gregorian calendar backwards to year one. All minutes are 60 seconds long.
  Leap seconds are "smeared" so that no leap second table is needed for
  interpretation, using a [24-hour linear
  smear](https://developers.google.com/time/smear). The range is from
  0001-01-01T00:00:00Z to 9999-12-31T23:59:59.999999999Z. By restricting to
  that range, we ensure that we can convert to and from [RFC
  3339](https://www.ietf.org/rfc/rfc3339.txt) date strings.

  Fields:
    nanos: Non-negative fractions of a second at nanosecond resolution.
      Negative second values with fractions must still have non-negative nanos
      values that count forward in time. Must be from 0 to 999,999,999
      inclusive.
    seconds: Represents seconds of UTC time since Unix epoch
      1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to
      9999-12-31T23:59:59Z inclusive.
  """

  nanos = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  seconds = _messages.IntegerField(2)


class ToolExecution(_messages.Message):
  r"""An execution of an arbitrary tool. It could be a test runner or a tool
  copying artifacts or deploying code.

  Fields:
    commandLineArguments: The full tokenized command line including the
      program name (equivalent to argv in a C program). - In response: present
      if set by create request - In create request: optional - In update
      request: never set
    exitCode: Tool execution exit code. This field will be set once the tool
      has exited. - In response: present if set by create/update request - In
      create request: optional - In update request: optional, a
      FAILED_PRECONDITION error will be returned if an exit_code is already
      set.
    toolLogs: References to any plain text logs output the tool execution.
      This field can be set before the tool has exited in order to be able to
      have access to a live view of the logs while the tool is running. The
      maximum allowed number of tool logs per step is 1000. - In response:
      present if set by create/update request - In create request: optional -
      In update request: optional, any value provided will be appended to the
      existing list
    toolOutputs: References to opaque files of any format output by the tool
      execution. The maximum allowed number of tool outputs per step is 1000.
      - In response: present if set by create/update request - In create
      request: optional - In update request: optional, any value provided will
      be appended to the existing list
  """

  commandLineArguments = _messages.StringField(1, repeated=True)
  exitCode = _messages.MessageField('ToolExitCode', 2)
  toolLogs = _messages.MessageField('FileReference', 3, repeated=True)
  toolOutputs = _messages.MessageField('ToolOutputReference', 4, repeated=True)


class ToolExecutionStep(_messages.Message):
  r"""Generic tool step to be used for binaries we do not explicitly support.
  For example: running cp to copy artifacts from one location to another.

  Fields:
    toolExecution: A Tool execution. - In response: present if set by
      create/update request - In create/update request: optional
  """

  toolExecution = _messages.MessageField('ToolExecution', 1)


class ToolExitCode(_messages.Message):
  r"""Exit code from a tool execution.

  Fields:
    number: Tool execution exit code. A value of 0 means that the execution
      was successful. - In response: always set - In create/update request:
      always set
  """

  number = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class ToolOutputReference(_messages.Message):
  r"""A reference to a ToolExecution output file.

  Fields:
    creationTime: The creation time of the file. - In response: present if set
      by create/update request - In create/update request: optional
    output: A FileReference to an output file. - In response: always set - In
      create/update request: always set
    testCase: The test case to which this output file belongs. - In response:
      present if set by create/update request - In create/update request:
      optional
  """

  creationTime = _messages.MessageField('Timestamp', 1)
  output = _messages.MessageField('FileReference', 2)
  testCase = _messages.MessageField('TestCaseReference', 3)


class ToolresultsProjectsGetSettingsRequest(_messages.Message):
  r"""A ToolresultsProjectsGetSettingsRequest object.

  Fields:
    projectId: A Project id. Required.
  """

  projectId = _messages.StringField(1, required=True)


class ToolresultsProjectsHistoriesCreateRequest(_messages.Message):
  r"""A ToolresultsProjectsHistoriesCreateRequest object.

  Fields:
    history: A History resource to be passed as the request body.
    projectId: A Project id. Required.
    requestId: A unique request ID for server to detect duplicated requests.
      For example, a UUID. Optional, but strongly recommended.
  """

  history = _messages.MessageField('History', 1)
  projectId = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class ToolresultsProjectsHistoriesExecutionsClustersGetRequest(_messages.Message):
  r"""A ToolresultsProjectsHistoriesExecutionsClustersGetRequest object.

  Fields:
    clusterId: A Cluster id Required.
    executionId: An Execution id. Required.
    historyId: A History id. Required.
    projectId: A Project id. Required.
  """

  clusterId = _messages.StringField(1, required=True)
  executionId = _messages.StringField(2, required=True)
  historyId = _messages.StringField(3, required=True)
  projectId = _messages.StringField(4, required=True)


class ToolresultsProjectsHistoriesExecutionsClustersListRequest(_messages.Message):
  r"""A ToolresultsProjectsHistoriesExecutionsClustersListRequest object.

  Fields:
    executionId: An Execution id. Required.
    historyId: A History id. Required.
    projectId: A Project id. Required.
  """

  executionId = _messages.StringField(1, required=True)
  historyId = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3, required=True)


class ToolresultsProjectsHistoriesExecutionsCreateRequest(_messages.Message):
  r"""A ToolresultsProjectsHistoriesExecutionsCreateRequest object.

  Fields:
    execution: A Execution resource to be passed as the request body.
    historyId: A History id. Required.
    projectId: A Project id. Required.
    requestId: A unique request ID for server to detect duplicated requests.
      For example, a UUID. Optional, but strongly recommended.
  """

  execution = _messages.MessageField('Execution', 1)
  historyId = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class ToolresultsProjectsHistoriesExecutionsEnvironmentsGetRequest(_messages.Message):
  r"""A ToolresultsProjectsHistoriesExecutionsEnvironmentsGetRequest object.

  Fields:
    environmentId: Required. An Environment id.
    executionId: Required. An Execution id.
    historyId: Required. A History id.
    projectId: Required. A Project id.
  """

  environmentId = _messages.StringField(1, required=True)
  executionId = _messages.StringField(2, required=True)
  historyId = _messages.StringField(3, required=True)
  projectId = _messages.StringField(4, required=True)


class ToolresultsProjectsHistoriesExecutionsEnvironmentsListRequest(_messages.Message):
  r"""A ToolresultsProjectsHistoriesExecutionsEnvironmentsListRequest object.

  Fields:
    executionId: Required. An Execution id.
    historyId: Required. A History id.
    pageSize: The maximum number of Environments to fetch. Default value: 25.
      The server will use this default if the field is not set or has a value
      of 0.
    pageToken: A continuation token to resume the query at the next item.
    projectId: Required. A Project id.
  """

  executionId = _messages.StringField(1, required=True)
  historyId = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  projectId = _messages.StringField(5, required=True)


class ToolresultsProjectsHistoriesExecutionsGetRequest(_messages.Message):
  r"""A ToolresultsProjectsHistoriesExecutionsGetRequest object.

  Fields:
    executionId: An Execution id. Required.
    historyId: A History id. Required.
    projectId: A Project id. Required.
  """

  executionId = _messages.StringField(1, required=True)
  historyId = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3, required=True)


class ToolresultsProjectsHistoriesExecutionsListRequest(_messages.Message):
  r"""A ToolresultsProjectsHistoriesExecutionsListRequest object.

  Fields:
    historyId: A History id. Required.
    pageSize: The maximum number of Executions to fetch. Default value: 25.
      The server will use this default if the field is not set or has a value
      of 0. Optional.
    pageToken: A continuation token to resume the query at the next item.
      Optional.
    projectId: A Project id. Required.
  """

  historyId = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  projectId = _messages.StringField(4, required=True)


class ToolresultsProjectsHistoriesExecutionsPatchRequest(_messages.Message):
  r"""A ToolresultsProjectsHistoriesExecutionsPatchRequest object.

  Fields:
    execution: A Execution resource to be passed as the request body.
    executionId: Required.
    historyId: Required.
    projectId: A Project id. Required.
    requestId: A unique request ID for server to detect duplicated requests.
      For example, a UUID. Optional, but strongly recommended.
  """

  execution = _messages.MessageField('Execution', 1)
  executionId = _messages.StringField(2, required=True)
  historyId = _messages.StringField(3, required=True)
  projectId = _messages.StringField(4, required=True)
  requestId = _messages.StringField(5)


class ToolresultsProjectsHistoriesExecutionsStepsAccessibilityClustersRequest(_messages.Message):
  r"""A
  ToolresultsProjectsHistoriesExecutionsStepsAccessibilityClustersRequest
  object.

  Fields:
    locale: The accepted format is the canonical Unicode format with hyphen as
      a delimiter. Language must be lowercase, Language Script - Capitalized,
      Region - UPPERCASE. See
      http://www.unicode.org/reports/tr35/#Unicode_locale_identifier for
      details. Required.
    name: A full resource name of the step. For example, projects/my-
      project/histories/bh.1234567890abcdef/executions/
      1234567890123456789/steps/bs.1234567890abcdef Required.
  """

  locale = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class ToolresultsProjectsHistoriesExecutionsStepsCreateRequest(_messages.Message):
  r"""A ToolresultsProjectsHistoriesExecutionsStepsCreateRequest object.

  Fields:
    executionId: Required. An Execution id.
    historyId: Required. A History id.
    projectId: Required. A Project id.
    requestId: A unique request ID for server to detect duplicated requests.
      For example, a UUID. Optional, but strongly recommended.
    step: A Step resource to be passed as the request body.
  """

  executionId = _messages.StringField(1, required=True)
  historyId = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  step = _messages.MessageField('Step', 5)


class ToolresultsProjectsHistoriesExecutionsStepsGetPerfMetricsSummaryRequest(_messages.Message):
  r"""A
  ToolresultsProjectsHistoriesExecutionsStepsGetPerfMetricsSummaryRequest
  object.

  Fields:
    executionId: A tool results execution ID.
    historyId: A tool results history ID.
    projectId: The cloud project
    stepId: A tool results step ID.
  """

  executionId = _messages.StringField(1, required=True)
  historyId = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3, required=True)
  stepId = _messages.StringField(4, required=True)


class ToolresultsProjectsHistoriesExecutionsStepsGetRequest(_messages.Message):
  r"""A ToolresultsProjectsHistoriesExecutionsStepsGetRequest object.

  Fields:
    executionId: A Execution id. Required.
    historyId: A History id. Required.
    projectId: A Project id. Required.
    stepId: A Step id. Required.
  """

  executionId = _messages.StringField(1, required=True)
  historyId = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3, required=True)
  stepId = _messages.StringField(4, required=True)


class ToolresultsProjectsHistoriesExecutionsStepsListRequest(_messages.Message):
  r"""A ToolresultsProjectsHistoriesExecutionsStepsListRequest object.

  Fields:
    executionId: A Execution id. Required.
    historyId: A History id. Required.
    pageSize: The maximum number of Steps to fetch. Default value: 25. The
      server will use this default if the field is not set or has a value of
      0. Optional.
    pageToken: A continuation token to resume the query at the next item.
      Optional.
    projectId: A Project id. Required.
  """

  executionId = _messages.StringField(1, required=True)
  historyId = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  projectId = _messages.StringField(5, required=True)


class ToolresultsProjectsHistoriesExecutionsStepsPatchRequest(_messages.Message):
  r"""A ToolresultsProjectsHistoriesExecutionsStepsPatchRequest object.

  Fields:
    executionId: A Execution id. Required.
    historyId: A History id. Required.
    projectId: A Project id. Required.
    requestId: A unique request ID for server to detect duplicated requests.
      For example, a UUID. Optional, but strongly recommended.
    step: A Step resource to be passed as the request body.
    stepId: A Step id. Required.
  """

  executionId = _messages.StringField(1, required=True)
  historyId = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  step = _messages.MessageField('Step', 5)
  stepId = _messages.StringField(6, required=True)


class ToolresultsProjectsHistoriesExecutionsStepsPerfSampleSeriesGetRequest(_messages.Message):
  r"""A ToolresultsProjectsHistoriesExecutionsStepsPerfSampleSeriesGetRequest
  object.

  Fields:
    executionId: A tool results execution ID.
    historyId: A tool results history ID.
    projectId: The cloud project
    sampleSeriesId: A sample series id
    stepId: A tool results step ID.
  """

  executionId = _messages.StringField(1, required=True)
  historyId = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3, required=True)
  sampleSeriesId = _messages.StringField(4, required=True)
  stepId = _messages.StringField(5, required=True)


class ToolresultsProjectsHistoriesExecutionsStepsPerfSampleSeriesListRequest(_messages.Message):
  r"""A ToolresultsProjectsHistoriesExecutionsStepsPerfSampleSeriesListRequest
  object.

  Enums:
    FilterValueValuesEnum: Specify one or more PerfMetricType values such as
      CPU to filter the result

  Fields:
    executionId: A tool results execution ID.
    filter: Specify one or more PerfMetricType values such as CPU to filter
      the result
    historyId: A tool results history ID.
    projectId: The cloud project
    stepId: A tool results step ID.
  """

  class FilterValueValuesEnum(_messages.Enum):
    r"""Specify one or more PerfMetricType values such as CPU to filter the
    result

    Values:
      perfMetricTypeUnspecified: <no description>
      memory: <no description>
      cpu: <no description>
      network: <no description>
      graphics: <no description>
    """
    perfMetricTypeUnspecified = 0
    memory = 1
    cpu = 2
    network = 3
    graphics = 4

  executionId = _messages.StringField(1, required=True)
  filter = _messages.EnumField('FilterValueValuesEnum', 2, repeated=True)
  historyId = _messages.StringField(3, required=True)
  projectId = _messages.StringField(4, required=True)
  stepId = _messages.StringField(5, required=True)


class ToolresultsProjectsHistoriesExecutionsStepsPerfSampleSeriesSamplesBatchCreateRequest(_messages.Message):
  r"""A ToolresultsProjectsHistoriesExecutionsStepsPerfSampleSeriesSamplesBatc
  hCreateRequest object.

  Fields:
    batchCreatePerfSamplesRequest: A BatchCreatePerfSamplesRequest resource to
      be passed as the request body.
    executionId: A tool results execution ID.
    historyId: A tool results history ID.
    projectId: The cloud project
    sampleSeriesId: A sample series id
    stepId: A tool results step ID.
  """

  batchCreatePerfSamplesRequest = _messages.MessageField('BatchCreatePerfSamplesRequest', 1)
  executionId = _messages.StringField(2, required=True)
  historyId = _messages.StringField(3, required=True)
  projectId = _messages.StringField(4, required=True)
  sampleSeriesId = _messages.StringField(5, required=True)
  stepId = _messages.StringField(6, required=True)


class ToolresultsProjectsHistoriesExecutionsStepsPerfSampleSeriesSamplesListRequest(_messages.Message):
  r"""A ToolresultsProjectsHistoriesExecutionsStepsPerfSampleSeriesSamplesList
  Request object.

  Fields:
    executionId: A tool results execution ID.
    historyId: A tool results history ID.
    pageSize: The default page size is 500 samples, and the maximum size is
      5000. If the page_size is greater than 5000, the effective page size
      will be 5000
    pageToken: Optional, the next_page_token returned in the previous response
    projectId: The cloud project
    sampleSeriesId: A sample series id
    stepId: A tool results step ID.
  """

  executionId = _messages.StringField(1, required=True)
  historyId = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  projectId = _messages.StringField(5, required=True)
  sampleSeriesId = _messages.StringField(6, required=True)
  stepId = _messages.StringField(7, required=True)


class ToolresultsProjectsHistoriesExecutionsStepsPublishXunitXmlFilesRequest(_messages.Message):
  r"""A ToolresultsProjectsHistoriesExecutionsStepsPublishXunitXmlFilesRequest
  object.

  Fields:
    executionId: A Execution id. Required.
    historyId: A History id. Required.
    projectId: A Project id. Required.
    publishXunitXmlFilesRequest: A PublishXunitXmlFilesRequest resource to be
      passed as the request body.
    stepId: A Step id. Note: This step must include a TestExecutionStep.
      Required.
  """

  executionId = _messages.StringField(1, required=True)
  historyId = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3, required=True)
  publishXunitXmlFilesRequest = _messages.MessageField('PublishXunitXmlFilesRequest', 4)
  stepId = _messages.StringField(5, required=True)


class ToolresultsProjectsHistoriesExecutionsStepsTestCasesGetRequest(_messages.Message):
  r"""A ToolresultsProjectsHistoriesExecutionsStepsTestCasesGetRequest object.

  Fields:
    executionId: A Execution id Required.
    historyId: A History id. Required.
    projectId: A Project id. Required.
    stepId: A Step id. Note: This step must include a TestExecutionStep.
      Required.
    testCaseId: A Test Case id. Required.
  """

  executionId = _messages.StringField(1, required=True)
  historyId = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3, required=True)
  stepId = _messages.StringField(4, required=True)
  testCaseId = _messages.StringField(5, required=True)


class ToolresultsProjectsHistoriesExecutionsStepsTestCasesListRequest(_messages.Message):
  r"""A ToolresultsProjectsHistoriesExecutionsStepsTestCasesListRequest
  object.

  Fields:
    executionId: A Execution id Required.
    historyId: A History id. Required.
    pageSize: The maximum number of TestCases to fetch. Default value: 100.
      The server will use this default if the field is not set or has a value
      of 0. Optional.
    pageToken: A continuation token to resume the query at the next item.
      Optional.
    projectId: A Project id. Required.
    stepId: A Step id. Note: This step must include a TestExecutionStep.
      Required.
  """

  executionId = _messages.StringField(1, required=True)
  historyId = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  projectId = _messages.StringField(5, required=True)
  stepId = _messages.StringField(6, required=True)


class ToolresultsProjectsHistoriesExecutionsStepsThumbnailsListRequest(_messages.Message):
  r"""A ToolresultsProjectsHistoriesExecutionsStepsThumbnailsListRequest
  object.

  Fields:
    executionId: An Execution id. Required.
    historyId: A History id. Required.
    pageSize: The maximum number of thumbnails to fetch. Default value: 50.
      The server will use this default if the field is not set or has a value
      of 0. Optional.
    pageToken: A continuation token to resume the query at the next item.
      Optional.
    projectId: A Project id. Required.
    stepId: A Step id. Required.
  """

  executionId = _messages.StringField(1, required=True)
  historyId = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  projectId = _messages.StringField(5, required=True)
  stepId = _messages.StringField(6, required=True)


class ToolresultsProjectsHistoriesGetRequest(_messages.Message):
  r"""A ToolresultsProjectsHistoriesGetRequest object.

  Fields:
    historyId: A History id. Required.
    projectId: A Project id. Required.
  """

  historyId = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2, required=True)


class ToolresultsProjectsHistoriesListRequest(_messages.Message):
  r"""A ToolresultsProjectsHistoriesListRequest object.

  Fields:
    filterByName: If set, only return histories with the given name. Optional.
    pageSize: The maximum number of Histories to fetch. Default value: 20. The
      server will use this default if the field is not set or has a value of
      0. Any value greater than 100 will be treated as 100. Optional.
    pageToken: A continuation token to resume the query at the next item.
      Optional.
    projectId: A Project id. Required.
  """

  filterByName = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  projectId = _messages.StringField(4, required=True)


class ToolresultsProjectsInitializeSettingsRequest(_messages.Message):
  r"""A ToolresultsProjectsInitializeSettingsRequest object.

  Fields:
    projectId: A Project id. Required.
  """

  projectId = _messages.StringField(1, required=True)


class UIElementTooDeep(_messages.Message):
  r"""A warning that the screen hierarchy is deeper than the recommended
  threshold.

  Fields:
    depth: The depth of the screen element
    screenId: The screen id of the element
    screenStateId: The screen state id of the element
  """

  depth = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  screenId = _messages.StringField(2)
  screenStateId = _messages.StringField(3)


class UnspecifiedWarning(_messages.Message):
  r"""Default unspecified warning."""


class UnusedRoboDirective(_messages.Message):
  r"""Additional details of an unused robodirective.

  Fields:
    resourceName: The name of the resource that was unused.
  """

  resourceName = _messages.StringField(1)


class UpgradeInsight(_messages.Message):
  r"""This insight is a recommendation to upgrade a given library to the
  specified version, in order to avoid dependencies on non-SDK APIs.

  Fields:
    packageName: The name of the package to be upgraded.
    upgradeToVersion: The suggested version to upgrade to. Optional: In case
      we are not sure which version solves this problem
  """

  packageName = _messages.StringField(1)
  upgradeToVersion = _messages.StringField(2)


class UsedRoboDirective(_messages.Message):
  r"""Additional details of a used Robo directive.

  Fields:
    resourceName: The name of the resource that was used.
  """

  resourceName = _messages.StringField(1)


class UsedRoboIgnoreDirective(_messages.Message):
  r"""Additional details of a used Robo directive with an ignore action. Note:
  This is a different scenario than unused directive.

  Fields:
    resourceName: The name of the resource that was ignored.
  """

  resourceName = _messages.StringField(1)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
