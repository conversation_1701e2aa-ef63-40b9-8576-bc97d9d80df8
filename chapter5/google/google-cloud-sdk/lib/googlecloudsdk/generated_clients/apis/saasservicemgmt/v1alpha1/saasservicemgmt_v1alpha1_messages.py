"""Generated message classes for saasservicemgmt version v1alpha1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'saasservicemgmt'


class Activity(_messages.Message):
  r"""Activity performed on a specific resource, like pause/resume/cancel.

  Enums:
    ActivityTypeValueValuesEnum:
    StateValueValuesEnum: Optional. Immutable. The state for the action. By
      default, it's in progress. Once reaching completion state, it can be
      either succeeded or failed.

  Fields:
    activityType: A ActivityTypeValueValuesEnum attribute.
    endTime: Required. Immutable. The time when the action is finished.
    errorCode: Optional. Once failed, indicate what kind of error code it is
      for the UI.
    errorMessage: Optional. Once failed, indicate what kind of error message
      it is for the UI.
    reason: Optional. Immutable. The reason for the activity.
    requestedBy: Optional. Immutable. Indicate the individual who triggers the
      action. Oftentimes such information can be PII.
    requestedTime: Required. Immutable. The time when the action is requested.
    state: Optional. Immutable. The state for the action. By default, it's in
      progress. Once reaching completion state, it can be either succeeded or
      failed.
  """

  class ActivityTypeValueValuesEnum(_messages.Enum):
    r"""ActivityTypeValueValuesEnum enum type.

    Values:
      ACTIVITY_TYPE_UNSPECIFIED: <no description>
      ACTIVITY_TYPE_PAUSE: <no description>
      ACTIVITY_TYPE_RESUME: <no description>
      ACTIVITY_TYPE_CANCEL: <no description>
    """
    ACTIVITY_TYPE_UNSPECIFIED = 0
    ACTIVITY_TYPE_PAUSE = 1
    ACTIVITY_TYPE_RESUME = 2
    ACTIVITY_TYPE_CANCEL = 3

  class StateValueValuesEnum(_messages.Enum):
    r"""Optional. Immutable. The state for the action. By default, it's in
    progress. Once reaching completion state, it can be either succeeded or
    failed.

    Values:
      ACTIVITY_STATE_UNSPECIFIED: <no description>
      ACTIVITY_STATE_IN_PROGRESS: <no description>
      ACTIVITY_STATE_SUCCEEDED: <no description>
      ACTIVITY_STATE_FAILED: <no description>
    """
    ACTIVITY_STATE_UNSPECIFIED = 0
    ACTIVITY_STATE_IN_PROGRESS = 1
    ACTIVITY_STATE_SUCCEEDED = 2
    ACTIVITY_STATE_FAILED = 3

  activityType = _messages.EnumField('ActivityTypeValueValuesEnum', 1)
  endTime = _messages.StringField(2)
  errorCode = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  errorMessage = _messages.StringField(4)
  reason = _messages.StringField(5)
  requestedBy = _messages.StringField(6)
  requestedTime = _messages.StringField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)


class Aggregate(_messages.Message):
  r"""Represents the aggregation of a set of population of like records by a
  certain group. For example, a collection of unit counts can be aggregated
  and grouped by their state.

  Fields:
    count: Required. Number of records in the group.
    group: Required. Group by which to aggregate.
  """

  count = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  group = _messages.StringField(2)


class Blueprint(_messages.Message):
  r"""Blueprints are OCI Images that contain all of the artifacts needed to
  provision a unit. Metadata such as, type of the engine used to actuate the
  blueprint (e.g. terraform, helm etc) and version will come from the image
  manifest. If the hostname is omitted, it will be assumed to be the regional
  path to Artifact Registry (eg. us-east1-docker.pkg.dev).

  Fields:
    engine: Output only. Type of the engine used to actuate the blueprint.
      e.g. terraform, helm etc.
    package: Optional. Immutable. URI to a blueprint used by the Unit
      (required unless unitKind or release is set).
    version: Output only. Version metadata if present on the blueprint.
  """

  engine = _messages.StringField(1)
  package = _messages.StringField(2)
  version = _messages.StringField(3)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class Dependency(_messages.Message):
  r"""Dependency represent a single dependency with another unit kind by
  alias.

  Fields:
    alias: Required. An alias for the dependency. Used for input variable
      mapping.
    unitKind: Required. Immutable. The unit kind of the dependency.
  """

  alias = _messages.StringField(1)
  unitKind = _messages.StringField(2)


class Deprovision(_messages.Message):
  r"""Deprovision is the unit operation that deprovision the underlying
  resources represented by a Unit. Can only execute if the Unit is currently
  provisioned.
  """



class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class ErrorBudget(_messages.Message):
  r"""The configuration for error budget. If the number of failed units
  exceeds max(allowed_count, allowed_ratio * total_units), the rollout will be
  paused.

  Fields:
    allowedCount: Optional. The maximum number of failed units allowed in a
      location without pausing the rollout.
    allowedPercentage: Optional. The maximum percentage of units allowed to
      fail (0, 100] within a location without pausing the rollout.
  """

  allowedCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  allowedPercentage = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class EvaluationSpec(_messages.Message):
  r"""EvaluationSpec holds rules for evaluating the value of a flag.

  Fields:
    defaultTarget: Required. Default variant for the flag.
    variants: Optional. A list of variants.
  """

  defaultTarget = _messages.StringField(1)
  variants = _messages.MessageField('Variant', 2, repeated=True)


class Flag(_messages.Message):
  r"""Represents a single Flag.

  Enums:
    ValueTypeValueValuesEnum: Required. Immutable. Flag value type.

  Messages:
    AnnotationsValue: Optional. Annotations is an unstructured key-value map
      stored with a resource that may be set by external tools to store and
      retrieve arbitrary metadata. They are not queryable and should be
      preserved when modifying objects. More info:
      https://kubernetes.io/docs/user-guide/annotations
    LabelsValue: Optional. The labels on the resource, which can be used for
      categorization. similar to Kubernetes resource labels.

  Fields:
    annotations: Optional. Annotations is an unstructured key-value map stored
      with a resource that may be set by external tools to store and retrieve
      arbitrary metadata. They are not queryable and should be preserved when
      modifying objects. More info: https://kubernetes.io/docs/user-
      guide/annotations
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. Description of the flag. Max length: 500 bytes.
    etag: Output only. An opaque value that uniquely identifies a version or
      generation of a resource. It can be used to confirm that the client and
      server agree on the ordering of a resource being written.
    evaluationSpec: Optional. Specification of how the flag value should be
      evaluated. If a bool flag is created without an evaluation_spec
      specified, two default variants, "Enabled" (with bool_value = true) and
      "Disabled" (with bool_value = false), are created by default, and
      "Disabled" is set as the default_target.
    key: Required. Immutable. Flag key used in runtime evaluation APIs
      (OpenFeature). Max length: 256 bytes.
    labels: Optional. The labels on the resource, which can be used for
      categorization. similar to Kubernetes resource labels.
    name: Identifier. The resource name (full URI of the resource) following
      the standard naming scheme:
      "projects/{project}/locations/{location}/flags/{flag_id}"
    uid: Output only. The unique identifier of the resource. UID is unique in
      the time and space for this resource within the scope of the service. It
      is typically generated by the server on successful creation of a
      resource and must not be changed. UID is used to uniquely identify
      resources with resource name reuses. This should be a UUID4.
    unitKind: Required. Immutable. UnitKind that can consume this flag.
    updateTime: Output only. The timestamp when the resource was last updated.
      Any change to the resource made by users must refresh this value.
      Changes to a resource made by the service should refresh this value.
    valueType: Required. Immutable. Flag value type.
  """

  class ValueTypeValueValuesEnum(_messages.Enum):
    r"""Required. Immutable. Flag value type.

    Values:
      FLAG_VALUE_TYPE_UNSPECIFIED: <no description>
      FLAG_VALUE_TYPE_BOOL: <no description>
      FLAG_VALUE_TYPE_INT: <no description>
      FLAG_VALUE_TYPE_STRING: <no description>
    """
    FLAG_VALUE_TYPE_UNSPECIFIED = 0
    FLAG_VALUE_TYPE_BOOL = 1
    FLAG_VALUE_TYPE_INT = 2
    FLAG_VALUE_TYPE_STRING = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Annotations is an unstructured key-value map stored with a
    resource that may be set by external tools to store and retrieve arbitrary
    metadata. They are not queryable and should be preserved when modifying
    objects. More info: https://kubernetes.io/docs/user-guide/annotations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels on the resource, which can be used for
    categorization. similar to Kubernetes resource labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  etag = _messages.StringField(4)
  evaluationSpec = _messages.MessageField('EvaluationSpec', 5)
  key = _messages.StringField(6)
  labels = _messages.MessageField('LabelsValue', 7)
  name = _messages.StringField(8)
  uid = _messages.StringField(9)
  unitKind = _messages.StringField(10)
  updateTime = _messages.StringField(11)
  valueType = _messages.EnumField('ValueTypeValueValuesEnum', 12)


class FlagRelease(_messages.Message):
  r"""A collection of FlagRevisions.

  Messages:
    AnnotationsValue: Optional. Annotations is an unstructured key-value map
      stored with a resource that may be set by external tools to store and
      retrieve arbitrary metadata. They are not queryable and should be
      preserved when modifying objects. More info:
      https://kubernetes.io/docs/user-guide/annotations
    LabelsValue: Optional. The labels on the resource, which can be used for
      categorization. similar to Kubernetes resource labels.

  Fields:
    annotations: Optional. Annotations is an unstructured key-value map stored
      with a resource that may be set by external tools to store and retrieve
      arbitrary metadata. They are not queryable and should be preserved when
      modifying objects. More info: https://kubernetes.io/docs/user-
      guide/annotations
    createTime: Output only. The timestamp when the resource was created.
    etag: Output only. An opaque value that uniquely identifies a version or
      generation of a resource. It can be used to confirm that the client and
      server agree on the ordering of a resource being written.
    flagRevisions: Optional. Immutable. FlagRevisions to be rolled out. This
      is the ultimate source of truth of what a Rollout or a UnitOperation
      carries.
    labels: Optional. The labels on the resource, which can be used for
      categorization. similar to Kubernetes resource labels.
    name: Identifier. The resource name (full URI of the resource) following
      the standard naming scheme:
      "projects/{project}/locations/{location}/flagReleases/{flag_release_id}"
    obsoleteFlags: Optional. Immutable. Flags to be removed from given
      UnitKind and all related Units. If Flag is provided here, its
      FlagRevisions will be removed from UnitKind and Units.
    uid: Output only. The unique identifier of the resource. UID is unique in
      the time and space for this resource within the scope of the service. It
      is typically generated by the server on successful creation of a
      resource and must not be changed. UID is used to uniquely identify
      resources with resource name reuses. This should be a UUID4.
    unitKind: Required. Immutable. The UnitKind this FlagRelease applies to.
    updateTime: Output only. The timestamp when the resource was last updated.
      Any change to the resource made by users must refresh this value.
      Changes to a resource made by the service should refresh this value.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Annotations is an unstructured key-value map stored with a
    resource that may be set by external tools to store and retrieve arbitrary
    metadata. They are not queryable and should be preserved when modifying
    objects. More info: https://kubernetes.io/docs/user-guide/annotations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels on the resource, which can be used for
    categorization. similar to Kubernetes resource labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  etag = _messages.StringField(3)
  flagRevisions = _messages.StringField(4, repeated=True)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  obsoleteFlags = _messages.StringField(7, repeated=True)
  uid = _messages.StringField(8)
  unitKind = _messages.StringField(9)
  updateTime = _messages.StringField(10)


class FlagRevision(_messages.Message):
  r"""A snapshot of the EvaluationSpec for the Flag.

  Messages:
    AnnotationsValue: Optional. Annotations is an unstructured key-value map
      stored with a resource that may be set by external tools to store and
      retrieve arbitrary metadata. They are not queryable and should be
      preserved when modifying objects. More info:
      https://kubernetes.io/docs/user-guide/annotations
    LabelsValue: Optional. The labels on the resource, which can be used for
      categorization. similar to Kubernetes resource labels.

  Fields:
    annotations: Optional. Annotations is an unstructured key-value map stored
      with a resource that may be set by external tools to store and retrieve
      arbitrary metadata. They are not queryable and should be preserved when
      modifying objects. More info: https://kubernetes.io/docs/user-
      guide/annotations
    createTime: Output only. The timestamp when the resource was created.
    etag: Output only. An opaque value that uniquely identifies a version or
      generation of a resource. It can be used to confirm that the client and
      server agree on the ordering of a resource being written.
    evaluationSpec: Output only. Immutable. Snapshot of the EvaluationSpec for
      the flag.
    flag: Required. Immutable. Name of the Flag this is a revision of.
    labels: Optional. The labels on the resource, which can be used for
      categorization. similar to Kubernetes resource labels.
    name: Identifier. The resource name (full URI of the resource) following
      the standard naming scheme: "projects/{project}/locations/{location}/fla
      gRevisions/{flag_revision_id}"
    uid: Output only. The unique identifier of the resource. UID is unique in
      the time and space for this resource within the scope of the service. It
      is typically generated by the server on successful creation of a
      resource and must not be changed. UID is used to uniquely identify
      resources with resource name reuses. This should be a UUID4.
    updateTime: Output only. The timestamp when the resource was last updated.
      Any change to the resource made by users must refresh this value.
      Changes to a resource made by the service should refresh this value.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Annotations is an unstructured key-value map stored with a
    resource that may be set by external tools to store and retrieve arbitrary
    metadata. They are not queryable and should be preserved when modifying
    objects. More info: https://kubernetes.io/docs/user-guide/annotations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels on the resource, which can be used for
    categorization. similar to Kubernetes resource labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  etag = _messages.StringField(3)
  evaluationSpec = _messages.MessageField('EvaluationSpec', 4)
  flag = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  uid = _messages.StringField(8)
  updateTime = _messages.StringField(9)


class FlagUpdate(_messages.Message):
  r"""FlagUpdate is a UnitOperation that pushes new flag values to Units.

  Fields:
    flagRelease: Required. Flag release being applied by UnitOperation.
  """

  flagRelease = _messages.StringField(1)


class FromMapping(_messages.Message):
  r"""Output variables whose values will be passed on to dependencies

  Fields:
    dependency: Required. Alias of the dependency that the outputVariable will
      pass its value to
    outputVariable: Required. Name of the outputVariable on the dependency
  """

  dependency = _messages.StringField(1)
  outputVariable = _messages.StringField(2)


class GoogleCloudLocationLocation(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class ListFlagReleasesResponse(_messages.Message):
  r"""The response structure for the ListFlagReleases method.

  Fields:
    flagReleases: The resulting flag releases.
    nextPageToken: If present, the next page token can be provided to a
      subsequent ListFlagReleases call to list the next page. If empty, there
      are no more pages.
    unreachable: Locations that could not be reached.
  """

  flagReleases = _messages.MessageField('FlagRelease', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListFlagRevisionsResponse(_messages.Message):
  r"""The response structure for the ListFlagRevisions method.

  Fields:
    flagRevisions: The resulting flag revisions.
    nextPageToken: If present, the next page token can be provided to a
      subsequent ListFlagRevisions call to list the next page. If empty, there
      are no more pages.
    unreachable: Locations that could not be reached.
  """

  flagRevisions = _messages.MessageField('FlagRevision', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListFlagsResponse(_messages.Message):
  r"""The response structure for the ListFlags method.

  Fields:
    flags: The resulting flags.
    nextPageToken: If present, the next page token can be provided to a
      subsequent ListFlags call to list the next page. If empty, there are no
      more pages.
    unreachable: Locations that could not be reached.
  """

  flags = _messages.MessageField('Flag', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('GoogleCloudLocationLocation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListReleasesResponse(_messages.Message):
  r"""The response structure for the ListReleases method.

  Fields:
    nextPageToken: If present, the next page token can be provided to a
      subsequent ListReleases call to list the next page. If empty, there are
      no more pages.
    releases: The resulting releases.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  releases = _messages.MessageField('Release', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListRolloutKindsResponse(_messages.Message):
  r"""The response structure for the ListRolloutKinds method.

  Fields:
    nextPageToken: If present, the next page token can be provided to a
      subsequent ListRolloutKinds call to list the next page. If empty, there
      are no more pages.
    rolloutKinds: The resulting rollout kinds.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  rolloutKinds = _messages.MessageField('RolloutKind', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListRolloutTypesResponse(_messages.Message):
  r"""The response structure for the ListRolloutTypes method.

  Fields:
    nextPageToken: If present, the next page token can be provided to a
      subsequent ListRolloutTypes call to list the next page. If empty, there
      are no more pages.
    rolloutTypes: The resulting rollout types.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  rolloutTypes = _messages.MessageField('RolloutType', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListRolloutsResponse(_messages.Message):
  r"""The response structure for the ListRollouts method.

  Fields:
    nextPageToken: If present, the next page token can be provided to a
      subsequent ListRollouts call to list the next page. If empty, there are
      no more pages.
    rollouts: The resulting rollouts.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  rollouts = _messages.MessageField('Rollout', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListSaasResponse(_messages.Message):
  r"""The response structure for the ListSaas method.

  Fields:
    nextPageToken: If present, the next page token can be provided to a
      subsequent ListSaas call to list the next page. If empty, there are no
      more pages.
    saas: The resulting saas.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  saas = _messages.MessageField('Saas', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListSaasTypesResponse(_messages.Message):
  r"""The response structure for the ListSaasTypes method.

  Fields:
    nextPageToken: If present, the next page token can be provided to a
      subsequent ListSaasTypes call to list the next page. If empty, there are
      no more pages.
    saasTypes: The resulting saas types.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  saasTypes = _messages.MessageField('SaasType', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListTenantsResponse(_messages.Message):
  r"""The response structure for the ListTenants method.

  Fields:
    nextPageToken: If present, the next page token can be provided to a
      subsequent ListTenants call to list the next page. If empty, there are
      no more pages.
    tenants: The resulting tenants.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  tenants = _messages.MessageField('Tenant', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListUnitKindsResponse(_messages.Message):
  r"""The response structure for the ListUnitKinds method.

  Fields:
    nextPageToken: If present, the next page token can be provided to a
      subsequent ListUnitKinds call to list the next page. If empty, there are
      no more pages.
    unitKinds: The resulting unit kinds.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  unitKinds = _messages.MessageField('UnitKind', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListUnitOperationsResponse(_messages.Message):
  r"""The response structure for the ListUnitOperations method.

  Fields:
    nextPageToken: If present, the next page token can be provided to a
      subsequent ListUnitOperations call to list the next page. If empty,
      there are no more pages.
    unitOperations: The resulting unit operations.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  unitOperations = _messages.MessageField('UnitOperation', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListUnitsResponse(_messages.Message):
  r"""The response structure for the ListUnits method.

  Fields:
    nextPageToken: If present, the next page token can be provided to a
      subsequent ListUnits call to list the next page. If empty, there are no
      more pages.
    units: The resulting units.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  units = _messages.MessageField('Unit', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Location(_messages.Message):
  r"""Location information that the service is available in.

  Fields:
    name: Optional. Name of location.
  """

  name = _messages.StringField(1)


class MaintenanceSettings(_messages.Message):
  r"""Captures requested directives for performing future maintenance on the
  unit. This includes a request for the unit to skip maintenance for a period
  of time and remain pinned to its current release as well as controls for
  postponing maintenance scheduled in future.

  Fields:
    pinnedUntilTime: Optional. If present, it fixes the release on the unit
      until the given time; i.e. changes to the release field will be
      rejected. Rollouts should and will also respect this by not requesting
      an upgrade in the first place.
    rescheduleAfterTime: Optional. If set, designates that the next
      maintenance (producer specified) should be scheduled after a given time.
      This can be used in cases where the initial scheduled maintenance needs
      to be postponed to a later date for example due to not being ready for
      an upgrade.
  """

  pinnedUntilTime = _messages.StringField(1)
  rescheduleAfterTime = _messages.StringField(2)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""A OperationMetadata object.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Provision(_messages.Message):
  r"""Provision is the unit operation that provision the underlying resources
  represented by a Unit. Can only execute if the Unit is not currently
  provisioned.

  Fields:
    inputVariables: Optional. Set of input variables. Maximum 100. (optional)
    package: Optional. URI to a blueprint used by the Unit (optional).
    release: Optional. Reference to the Release object to use for the Unit.
      (optional).
  """

  inputVariables = _messages.MessageField('UnitVariable', 1, repeated=True)
  package = _messages.StringField(2)
  release = _messages.StringField(3)


class Release(_messages.Message):
  r"""A new version to be propagated and deployed to units. This includes
  pointers to packaged blueprints for actuation (e.g Helm or Terraform
  configuration packages) via artifact registry.

  Messages:
    AnnotationsValue: Optional. Annotations is an unstructured key-value map
      stored with a resource that may be set by external tools to store and
      retrieve arbitrary metadata. They are not queryable and should be
      preserved when modifying objects. More info:
      https://kubernetes.io/docs/user-guide/annotations
    LabelsValue: Optional. The labels on the resource, which can be used for
      categorization. similar to Kubernetes resource labels.

  Fields:
    annotations: Optional. Annotations is an unstructured key-value map stored
      with a resource that may be set by external tools to store and retrieve
      arbitrary metadata. They are not queryable and should be preserved when
      modifying objects. More info: https://kubernetes.io/docs/user-
      guide/annotations
    blueprint: Optional. Blueprints are OCI Images that contain all of the
      artifacts needed to provision a unit.
    createTime: Output only. The timestamp when the resource was created.
    etag: Output only. An opaque value that uniquely identifies a version or
      generation of a resource. It can be used to confirm that the client and
      server agree on the ordering of a resource being written.
    inputVariableDefaults: Optional. Mapping of input variables to default
      values. Maximum 100
    inputVariables: Optional. Output only. List of input variables declared on
      the blueprint and can be present with their values on the unit spec
    labels: Optional. The labels on the resource, which can be used for
      categorization. similar to Kubernetes resource labels.
    name: Identifier. The resource name (full URI of the resource) following
      the standard naming scheme:
      "projects/{project}/locations/{location}/releases/{release}"
    outputVariables: Optional. Output only. List of output variables declared
      on the blueprint and can be present with their values on the unit status
    releaseRequirements: Optional. Set of requirements to be fulfilled on the
      Unit when using this Release.
    uid: Output only. The unique identifier of the resource. UID is unique in
      the time and space for this resource within the scope of the service. It
      is typically generated by the server on successful creation of a
      resource and must not be changed. UID is used to uniquely identify
      resources with resource name reuses. This should be a UUID4.
    unitKind: Required. Immutable. Reference to the UnitKind this Release
      corresponds to (required and immutable once created).
    updateTime: Output only. The timestamp when the resource was last updated.
      Any change to the resource made by users must refresh this value.
      Changes to a resource made by the service should refresh this value.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Annotations is an unstructured key-value map stored with a
    resource that may be set by external tools to store and retrieve arbitrary
    metadata. They are not queryable and should be preserved when modifying
    objects. More info: https://kubernetes.io/docs/user-guide/annotations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels on the resource, which can be used for
    categorization. similar to Kubernetes resource labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  blueprint = _messages.MessageField('Blueprint', 2)
  createTime = _messages.StringField(3)
  etag = _messages.StringField(4)
  inputVariableDefaults = _messages.MessageField('UnitVariable', 5, repeated=True)
  inputVariables = _messages.MessageField('UnitVariable', 6, repeated=True)
  labels = _messages.MessageField('LabelsValue', 7)
  name = _messages.StringField(8)
  outputVariables = _messages.MessageField('UnitVariable', 9, repeated=True)
  releaseRequirements = _messages.MessageField('ReleaseRequirements', 10)
  uid = _messages.StringField(11)
  unitKind = _messages.StringField(12)
  updateTime = _messages.StringField(13)


class ReleaseRequirements(_messages.Message):
  r"""Set of requirements to be fulfilled on the Unit when using this Release.

  Fields:
    upgradeableFromReleases: Optional. A list of releases from which a unit
      can be upgraded to this one (optional). If left empty no constraints
      will be applied. When provided, unit upgrade requests to this release
      will check and enforce this constraint.
  """

  upgradeableFromReleases = _messages.StringField(1, repeated=True)


class Rollout(_messages.Message):
  r"""Represents a single rollout execution and its results

  Enums:
    StateValueValuesEnum: Output only. Current state of the rollout.
    TargetStateValueValuesEnum: Optional. Specifies the state that the users
      want the rollout to be in. The state field will finally be reconciled by
      the system to be the same as the target_state.

  Messages:
    AnnotationsValue: Optional. Annotations is an unstructured key-value map
      stored with a resource that may be set by external tools to store and
      retrieve arbitrary metadata. They are not queryable and should be
      preserved when modifying objects. More info:
      https://kubernetes.io/docs/user-guide/annotations
    LabelsValue: Optional. The labels on the resource, which can be used for
      categorization. similar to Kubernetes resource labels.

  Fields:
    activityLog: Optional. Output only. Activities performed on this rollout.
    activityReason: Optional. When performing a specific activity like
      pause/resume/cancel, users can choose to provide the activity reason
      which can be populated into the given activity.
    annotations: Optional. Annotations is an unstructured key-value map stored
      with a resource that may be set by external tools to store and retrieve
      arbitrary metadata. They are not queryable and should be preserved when
      modifying objects. More info: https://kubernetes.io/docs/user-
      guide/annotations
    cancel: Optional. Specifies whether the rollout should be canceled or not.
      Once set to true, it can't be reverted back to false. Cancellation while
      best-effort is a terminal state.
    control: Optional. Requested change to the execution of this rollout.
      Default RolloutControl.action is ROLLOUT_ACTION_RUN meaning the rollout
      will be executed to completion while progressing through all natural
      Rollout States (such as RUNNING -> SUCCEEDED or RUNNING -> FAILED).
      Requests can only be made when the Rollout is in a non-terminal state.
    createTime: Output only. The timestamp when the resource was created.
    endTime: Optional. Output only. The time when the rollout finished
      execution (regardless of success, failure, or cancellation). Will be
      empty if the rollout hasn't finished yet. Once set, the rollout is in
      terminal state and all the results are final.
    etag: Output only. An opaque value that uniquely identifies a version or
      generation of a resource. It can be used to confirm that the client and
      server agree on the ordering of a resource being written.
    flagRelease: Optional. Immutable. Name of the FlagRelease to be rolled out
      to the target Units. Release and FlagRelease are mutually exclusive.
      Note: `release` comment needs to be adjusted to mention that "Release
      and FlagRelease are mutually exclusive" when visibility restriction will
      be lifted.
    labels: Optional. The labels on the resource, which can be used for
      categorization. similar to Kubernetes resource labels.
    name: Identifier. The resource name (full URI of the resource) following
      the standard naming scheme:
      "projects/{project}/locations/{location}/rollout/{rollout_id}"
    parentRollout: Optional. Output only. The direct parent rollout that this
      rollout is stemming from. The resource name (full URI of the resource)
      following the standard naming scheme:
      "projects/{project}/locations/{location}/rollouts/{rollout_id}"
    pause: Optional. Specifies whether the rollout should pause and pause the
      execution or not.
    release: Optional. Immutable. Name of the Release that gets rolled out to
      target Units. Required if no other type of release is specified.
    results: Output only. Information about progress of rollouts such as
      number of units identified, upgraded, pending etc. Note: this can be
      expanded to include finer-grain per-scope (e.g. per location) results as
      well as the top level aggregated ones listed here.
    rolloutKind: Optional. Immutable. Name of the RolloutKind this rollout is
      stemming from and adhering to.
    rolloutOrchestrationStrategy: Optional. The strategy used for executing
      this Rollout. This strategy will override whatever strategy is specified
      in the RolloutType. If not specified on creation, the strategy from
      RolloutType will be used. There are two supported values strategies
      which are used to control - "Google.Cloud.Simple.AllAtOnce" -
      "Google.Cloud.Simple.OneLocationAtATime" A rollout with one of these
      simple strategies will rollout across all locations defined in the
      targeted UnitKind's Saas Locations.
    rolloutStrategy: Optional. The strategy to use for executing this rollout.
      By default, the strategy from Rollout Type will be used, If not provided
      at creation time of the rollout. (immutable once created)
    rolloutType: Optional. Immutable. Name of the RolloutType this rollout is
      stemming from and adhering to.
    rootRollout: Optional. Output only. The root rollout that this rollout is
      stemming from. The resource name (full URI of the resource) following
      the standard naming scheme:
      "projects/{project}/locations/{location}/rollouts/{rollout_id}"
    startTime: Optional. Output only. The time when the rollout started
      executing. Will be empty if the rollout hasn't started yet.
    state: Output only. Current state of the rollout.
    stateMessage: Output only. Human readable message indicating details about
      the last state transition.
    stateTransitionTime: Optional. Output only. The time when the rollout
      transitioned into its current state.
    stats: Optional. Output only. Details about the progress of the rollout.
    targetState: Optional. Specifies the state that the users want the rollout
      to be in. The state field will finally be reconciled by the system to be
      the same as the target_state.
    uid: Output only. The unique identifier of the resource. UID is unique in
      the time and space for this resource within the scope of the service. It
      is typically generated by the server on successful creation of a
      resource and must not be changed. UID is used to uniquely identify
      resources with resource name reuses. This should be a UUID4.
    unitFilter: Optional. CEL(https://github.com/google/cel-spec) formatted
      filter string against Unit. The filter will be applied to determine the
      eligible unit population. This filter can only reduce, but not expand
      the scope of the rollout. If not provided, the unit_filter from the
      RolloutType will be used.
    updateTime: Output only. The timestamp when the resource was last updated.
      Any change to the resource made by users must refresh this value.
      Changes to a resource made by the service should refresh this value.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the rollout.

    Values:
      ROLLOUT_STATE_UNSPECIFIED: Unspecified state.
      ROLLOUT_STATE_RUNNING: Rollout is in progress.
      ROLLOUT_STATE_PAUSED: Rollout has been paused.
      ROLLOUT_STATE_SUCCEEDED: Rollout completed successfully.
      ROLLOUT_STATE_FAILED: Rollout has failed.
      ROLLOUT_STATE_CANCELLED: Rollout has been cancelled.
      ROLLOUT_STATE_WAITING: Rollout is waiting for some condition to be met
        before starting.
      ROLLOUT_STATE_CANCELLING: Rollout is being cancelled.
      ROLLOUT_STATE_RESUMING: Rollout is being resumed.
      ROLLOUT_STATE_PAUSING: Rollout is being paused.
    """
    ROLLOUT_STATE_UNSPECIFIED = 0
    ROLLOUT_STATE_RUNNING = 1
    ROLLOUT_STATE_PAUSED = 2
    ROLLOUT_STATE_SUCCEEDED = 3
    ROLLOUT_STATE_FAILED = 4
    ROLLOUT_STATE_CANCELLED = 5
    ROLLOUT_STATE_WAITING = 6
    ROLLOUT_STATE_CANCELLING = 7
    ROLLOUT_STATE_RESUMING = 8
    ROLLOUT_STATE_PAUSING = 9

  class TargetStateValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies the state that the users want the rollout to be
    in. The state field will finally be reconciled by the system to be the
    same as the target_state.

    Values:
      ROLLOUT_STATE_UNSPECIFIED: Unspecified state.
      ROLLOUT_STATE_RUNNING: Rollout is in progress.
      ROLLOUT_STATE_PAUSED: Rollout has been paused.
      ROLLOUT_STATE_SUCCEEDED: Rollout completed successfully.
      ROLLOUT_STATE_FAILED: Rollout has failed.
      ROLLOUT_STATE_CANCELLED: Rollout has been cancelled.
      ROLLOUT_STATE_WAITING: Rollout is waiting for some condition to be met
        before starting.
      ROLLOUT_STATE_CANCELLING: Rollout is being cancelled.
      ROLLOUT_STATE_RESUMING: Rollout is being resumed.
      ROLLOUT_STATE_PAUSING: Rollout is being paused.
    """
    ROLLOUT_STATE_UNSPECIFIED = 0
    ROLLOUT_STATE_RUNNING = 1
    ROLLOUT_STATE_PAUSED = 2
    ROLLOUT_STATE_SUCCEEDED = 3
    ROLLOUT_STATE_FAILED = 4
    ROLLOUT_STATE_CANCELLED = 5
    ROLLOUT_STATE_WAITING = 6
    ROLLOUT_STATE_CANCELLING = 7
    ROLLOUT_STATE_RESUMING = 8
    ROLLOUT_STATE_PAUSING = 9

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Annotations is an unstructured key-value map stored with a
    resource that may be set by external tools to store and retrieve arbitrary
    metadata. They are not queryable and should be preserved when modifying
    objects. More info: https://kubernetes.io/docs/user-guide/annotations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels on the resource, which can be used for
    categorization. similar to Kubernetes resource labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  activityLog = _messages.MessageField('Activity', 1, repeated=True)
  activityReason = _messages.StringField(2)
  annotations = _messages.MessageField('AnnotationsValue', 3)
  cancel = _messages.BooleanField(4)
  control = _messages.MessageField('RolloutControl', 5)
  createTime = _messages.StringField(6)
  endTime = _messages.StringField(7)
  etag = _messages.StringField(8)
  flagRelease = _messages.StringField(9)
  labels = _messages.MessageField('LabelsValue', 10)
  name = _messages.StringField(11)
  parentRollout = _messages.StringField(12)
  pause = _messages.BooleanField(13)
  release = _messages.StringField(14)
  results = _messages.MessageField('RolloutResults', 15)
  rolloutKind = _messages.StringField(16)
  rolloutOrchestrationStrategy = _messages.StringField(17)
  rolloutStrategy = _messages.MessageField('RolloutStrategy', 18)
  rolloutType = _messages.StringField(19)
  rootRollout = _messages.StringField(20)
  startTime = _messages.StringField(21)
  state = _messages.EnumField('StateValueValuesEnum', 22)
  stateMessage = _messages.StringField(23)
  stateTransitionTime = _messages.StringField(24)
  stats = _messages.MessageField('RolloutStats', 25)
  targetState = _messages.EnumField('TargetStateValueValuesEnum', 26)
  uid = _messages.StringField(27)
  unitFilter = _messages.StringField(28)
  updateTime = _messages.StringField(29)


class RolloutControl(_messages.Message):
  r"""RolloutControl provides a way to request a change to the execution of a
  Rollout by pausing or canceling it.

  Enums:
    ActionValueValuesEnum: Required. Action to be performed on the Rollout.
      The default behavior is to run the rollout until it naturally reaches a
      terminal state.

  Fields:
    action: Required. Action to be performed on the Rollout. The default
      behavior is to run the rollout until it naturally reaches a terminal
      state.
    runParams: Optional. Parameters for the RUN action. It is an error to
      specify this if the RolloutAction is not set to RUN. By default, the
      rollout will retry failed operations when resumed.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required. Action to be performed on the Rollout. The default behavior
    is to run the rollout until it naturally reaches a terminal state.

    Values:
      ROLLOUT_ACTION_UNSPECIFIED: Unspecified action, will be treated as RUN
        by default.
      ROLLOUT_ACTION_RUN: Run the Rollout until it naturally reaches a
        terminal state. A rollout requested to run will progress through all
        natural Rollout States (such as RUNNING -> SUCCEEDED or RUNNING ->
        FAILED). If retriable errors are encountered during the rollout, the
        rollout will paused by default and can be resumed by re-requesting
        this RUN action.
      ROLLOUT_ACTION_PAUSE: Pause the Rollout until it is resumed (i.e. RUN is
        requested).
      ROLLOUT_ACTION_CANCEL: Cancel the Rollout permanently.
    """
    ROLLOUT_ACTION_UNSPECIFIED = 0
    ROLLOUT_ACTION_RUN = 1
    ROLLOUT_ACTION_PAUSE = 2
    ROLLOUT_ACTION_CANCEL = 3

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  runParams = _messages.MessageField('RunRolloutActionParams', 2)


class RolloutKind(_messages.Message):
  r"""An object that describes various settings of Rollout execution. Includes
  built-in policies across GCP and GDC, and customizable policies.

  Enums:
    MaintenancePolicyEnforcementValueValuesEnum:
    UpdateUnitKindStrategyValueValuesEnum: Optional. The config for updating
      the unit kind. By default, the unit kind will be updated on the rollout
      start.

  Messages:
    AnnotationsValue: Optional. Annotations is an unstructured key-value map
      stored with a resource that may be set by external tools to store and
      retrieve arbitrary metadata. They are not queryable and should be
      preserved when modifying objects. More info:
      https://kubernetes.io/docs/user-guide/annotations
    LabelsValue: Optional. The labels on the resource, which can be used for
      categorization. similar to Kubernetes resource labels.

  Fields:
    annotations: Optional. Annotations is an unstructured key-value map stored
      with a resource that may be set by external tools to store and retrieve
      arbitrary metadata. They are not queryable and should be preserved when
      modifying objects. More info: https://kubernetes.io/docs/user-
      guide/annotations
    createTime: Output only. The timestamp when the resource was created.
    errorBudget: Optional. The configuration for error budget. If the number
      of failed units exceeds max(allowed_count, allowed_ratio * total_units),
      the rollout will be paused. If not set, all units will be attempted to
      be updated regardless of the number of failures encountered.
    etag: Output only. An opaque value that uniquely identifies a version or
      generation of a resource. It can be used to confirm that the client and
      server agree on the ordering of a resource being written.
    labels: Optional. The labels on the resource, which can be used for
      categorization. similar to Kubernetes resource labels.
    maintenancePolicyEnforcement: A
      MaintenancePolicyEnforcementValueValuesEnum attribute.
    name: Identifier. The resource name (full URI of the resource) following
      the standard naming scheme:
      "projects/{project}/locations/{location}/rolloutKinds/{rollout_kind_id}"
    rolloutOrchestrationStrategy: Optional. The strategy used for executing a
      Rollout. This is a required field. There are two supported values
      strategies which are used to control - "Google.Cloud.Simple.AllAtOnce" -
      "Google.Cloud.Simple.OneLocationAtATime" A rollout with one of these
      simple strategies will rollout across all locations defined in the
      associated UnitKind's Saas Locations.
    uid: Output only. The unique identifier of the resource. UID is unique in
      the time and space for this resource within the scope of the service. It
      is typically generated by the server on successful creation of a
      resource and must not be changed. UID is used to uniquely identify
      resources with resource name reuses. This should be a UUID4.
    unitFilter: Optional. CEL(https://github.com/google/cel-spec) formatted
      filter string against Unit. The filter will be applied to determine the
      eligible unit population. This filter can only reduce, but not expand
      the scope of the rollout.
    unitKind: Required. Immutable. UnitKind that this rollout kind corresponds
      to. Rollouts stemming from this rollout kind will target the units of
      this unit kind. In other words, this defines the population of target
      units to be upgraded by rollouts.
    updateTime: Output only. The timestamp when the resource was last updated.
      Any change to the resource made by users must refresh this value.
      Changes to a resource made by the service should refresh this value.
    updateUnitKindStrategy: Optional. The config for updating the unit kind.
      By default, the unit kind will be updated on the rollout start.
  """

  class MaintenancePolicyEnforcementValueValuesEnum(_messages.Enum):
    r"""MaintenancePolicyEnforcementValueValuesEnum enum type.

    Values:
      MAINTENANCE_POLICY_ENFORCEMENT_UNSPECIFIED: <no description>
      MAINTENANCE_POLICY_ENFORCEMENT_STRICT: <no description>
      MAINTENANCE_POLICY_ENFORCEMENT_IGNORED: <no description>
      MAINTENANCE_POLICY_ENFORCEMENT_SKIPPED: <no description>
    """
    MAINTENANCE_POLICY_ENFORCEMENT_UNSPECIFIED = 0
    MAINTENANCE_POLICY_ENFORCEMENT_STRICT = 1
    MAINTENANCE_POLICY_ENFORCEMENT_IGNORED = 2
    MAINTENANCE_POLICY_ENFORCEMENT_SKIPPED = 3

  class UpdateUnitKindStrategyValueValuesEnum(_messages.Enum):
    r"""Optional. The config for updating the unit kind. By default, the unit
    kind will be updated on the rollout start.

    Values:
      UPDATE_UNIT_KIND_STRATEGY_UNSPECIFIED: Strategy unspecified.
      UPDATE_UNIT_KIND_STRATEGY_ON_START: Update the unit kind strategy on the
        rollout start.
      UPDATE_UNIT_KIND_STRATEGY_NEVER: Never update the unit kind.
    """
    UPDATE_UNIT_KIND_STRATEGY_UNSPECIFIED = 0
    UPDATE_UNIT_KIND_STRATEGY_ON_START = 1
    UPDATE_UNIT_KIND_STRATEGY_NEVER = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Annotations is an unstructured key-value map stored with a
    resource that may be set by external tools to store and retrieve arbitrary
    metadata. They are not queryable and should be preserved when modifying
    objects. More info: https://kubernetes.io/docs/user-guide/annotations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels on the resource, which can be used for
    categorization. similar to Kubernetes resource labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  errorBudget = _messages.MessageField('ErrorBudget', 3)
  etag = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  maintenancePolicyEnforcement = _messages.EnumField('MaintenancePolicyEnforcementValueValuesEnum', 6)
  name = _messages.StringField(7)
  rolloutOrchestrationStrategy = _messages.StringField(8)
  uid = _messages.StringField(9)
  unitFilter = _messages.StringField(10)
  unitKind = _messages.StringField(11)
  updateTime = _messages.StringField(12)
  updateUnitKindStrategy = _messages.EnumField('UpdateUnitKindStrategyValueValuesEnum', 13)


class RolloutResults(_messages.Message):
  r"""Information about progress of rollouts such as number of units
  identified, upgraded, pending etc. Note: this can be expanded to include
  finer-grain per-scope (e.g. per location) results as well as the top level
  aggregated ones listed here.

  Fields:
    canceledUnits: Output only. Number of Units canceled.
    failedLocations: Optional. Output only.
    failedUnits: Output only. Number of Units failed to update.
    ignoredUnits: Output only. Number of Units ignored.
    pendingLocations: A string attribute.
    runningLocations: A string attribute.
    totalUnits: Output only. Total number of units observed or captured by the
      rollout. This field is only set once the rollout has started.
    updatedLocations: A string attribute.
    updatedUnits: Output only. Number of Units successfully updated.
  """

  canceledUnits = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  failedLocations = _messages.StringField(2, repeated=True)
  failedUnits = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  ignoredUnits = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pendingLocations = _messages.StringField(5, repeated=True)
  runningLocations = _messages.StringField(6, repeated=True)
  totalUnits = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  updatedLocations = _messages.StringField(8, repeated=True)
  updatedUnits = _messages.IntegerField(9, variant=_messages.Variant.INT32)


class RolloutStats(_messages.Message):
  r"""RolloutStats contains information about the progress of a rollout.

  Fields:
    operationsByState: Output only. A breakdown of the progress of operations
      triggered by the rollout. Provides a count of Operations by their state.
      This can be used to determine the number of units which have been
      updated, or are scheduled to be updated. There will be at most one entry
      per group. Possible values for operation groups are: - "SCHEDULED" -
      "PENDING" - "RUNNING" - "SUCCEEDED" - "FAILED" - "CANCELLED"
  """

  operationsByState = _messages.MessageField('Aggregate', 1, repeated=True)


class RolloutStrategy(_messages.Message):
  r"""The strategy to use for executing rollouts (initially a small set of
  predefined strategies are used but possible to expand on settings and
  introduction of custom defined strategies in future).

  Enums:
    MaintenancePolicyEnforcementValueValuesEnum: Optional. Value among strict
      (enforcing maintenance policy and only looking at Units with maintenance
      policy), ignore (ignoring maintenance policy) and skip (skipping Units
      with maintenance policy)

  Fields:
    maintenancePolicyEnforcement: Optional. Value among strict (enforcing
      maintenance policy and only looking at Units with maintenance policy),
      ignore (ignoring maintenance policy) and skip (skipping Units with
      maintenance policy)
    type: Required. Immutable. The name of a canned (predefined) strategy to
      be used by rollouts.
  """

  class MaintenancePolicyEnforcementValueValuesEnum(_messages.Enum):
    r"""Optional. Value among strict (enforcing maintenance policy and only
    looking at Units with maintenance policy), ignore (ignoring maintenance
    policy) and skip (skipping Units with maintenance policy)

    Values:
      MAINTENANCE_POLICY_ENFORCEMENT_UNSPECIFIED: <no description>
      MAINTENANCE_POLICY_ENFORCEMENT_STRICT: <no description>
      MAINTENANCE_POLICY_ENFORCEMENT_IGNORED: <no description>
      MAINTENANCE_POLICY_ENFORCEMENT_SKIPPED: <no description>
    """
    MAINTENANCE_POLICY_ENFORCEMENT_UNSPECIFIED = 0
    MAINTENANCE_POLICY_ENFORCEMENT_STRICT = 1
    MAINTENANCE_POLICY_ENFORCEMENT_IGNORED = 2
    MAINTENANCE_POLICY_ENFORCEMENT_SKIPPED = 3

  maintenancePolicyEnforcement = _messages.EnumField('MaintenancePolicyEnforcementValueValuesEnum', 1)
  type = _messages.StringField(2)


class RolloutType(_messages.Message):
  r"""An object that describes various settings of Rollout execution. Includes
  built-in policies across GCP and GDC, and customizable policies.

  Enums:
    MaintenancePolicyEnforcementValueValuesEnum: Optional. Value among strict
      (enforcing maintenance policy and only looking at Units with maintenance
      policy), ignore (ignoring maintenance policy) and skip (skipping Units
      with maintenance policy)
    UpdateUnitKindStrategyValueValuesEnum: Optional. The config for updating
      the unit kind. By default, the unit kind will be updated on the rollout
      start.

  Messages:
    AnnotationsValue: Optional. Annotations is an unstructured key-value map
      stored with a resource that may be set by external tools to store and
      retrieve arbitrary metadata. They are not queryable and should be
      preserved when modifying objects. More info:
      https://kubernetes.io/docs/user-guide/annotations
    LabelsValue: Optional. The labels on the resource, which can be used for
      categorization. similar to Kubernetes resource labels.

  Fields:
    annotations: Optional. Annotations is an unstructured key-value map stored
      with a resource that may be set by external tools to store and retrieve
      arbitrary metadata. They are not queryable and should be preserved when
      modifying objects. More info: https://kubernetes.io/docs/user-
      guide/annotations
    createTime: Output only. The timestamp when the resource was created.
    etag: Output only. An opaque value that uniquely identifies a version or
      generation of a resource. It can be used to confirm that the client and
      server agree on the ordering of a resource being written.
    labels: Optional. The labels on the resource, which can be used for
      categorization. similar to Kubernetes resource labels.
    maintenancePolicyEnforcement: Optional. Value among strict (enforcing
      maintenance policy and only looking at Units with maintenance policy),
      ignore (ignoring maintenance policy) and skip (skipping Units with
      maintenance policy)
    name: Identifier. The resource name (full URI of the resource) following
      the standard naming scheme:
      "projects/{project}/locations/{location}/rolloutTypes/{rollout_type_id}"
    ongoingRollouts: Optional. Output only. List of ongoing rollouts under
      this rollout type. For MVP, only one live rollout is allowed.
    rolloutOrchestrationStrategy: Optional. The strategy to use for executing
      rollouts.
    rolloutStrategy: Optional. The strategy to use for executing rollouts
      (initially a small set of predefined strategies are used but possible to
      expand on settings and introduction of custom defined strategies in
      future).
    uid: Output only. The unique identifier of the resource. UID is unique in
      the time and space for this resource within the scope of the service. It
      is typically generated by the server on successful creation of a
      resource and must not be changed. UID is used to uniquely identify
      resources with resource name reuses. This should be a UUID4.
    unitFilter: Optional. CEL(https://github.com/google/cel-spec) formatted
      filter string against Unit. The filter will be applied to determine the
      eligible unit population. This filter can only reduce, but not expand
      the scope of the rollout.
    unitKind: Required. Immutable. UnitKind that this rollout type corresponds
      to. Rollouts stemming from this rollout type will target the units of
      this unit kind. In other words, this defines the population of target
      units to be upgraded by rollouts.
    updateTime: Output only. The timestamp when the resource was last updated.
      Any change to the resource made by users must refresh this value.
      Changes to a resource made by the service should refresh this value.
    updateUnitKindStrategy: Optional. The config for updating the unit kind.
      By default, the unit kind will be updated on the rollout start.
  """

  class MaintenancePolicyEnforcementValueValuesEnum(_messages.Enum):
    r"""Optional. Value among strict (enforcing maintenance policy and only
    looking at Units with maintenance policy), ignore (ignoring maintenance
    policy) and skip (skipping Units with maintenance policy)

    Values:
      MAINTENANCE_POLICY_ENFORCEMENT_UNSPECIFIED: <no description>
      MAINTENANCE_POLICY_ENFORCEMENT_STRICT: <no description>
      MAINTENANCE_POLICY_ENFORCEMENT_IGNORED: <no description>
      MAINTENANCE_POLICY_ENFORCEMENT_SKIPPED: <no description>
    """
    MAINTENANCE_POLICY_ENFORCEMENT_UNSPECIFIED = 0
    MAINTENANCE_POLICY_ENFORCEMENT_STRICT = 1
    MAINTENANCE_POLICY_ENFORCEMENT_IGNORED = 2
    MAINTENANCE_POLICY_ENFORCEMENT_SKIPPED = 3

  class UpdateUnitKindStrategyValueValuesEnum(_messages.Enum):
    r"""Optional. The config for updating the unit kind. By default, the unit
    kind will be updated on the rollout start.

    Values:
      UPDATE_UNIT_KIND_STRATEGY_UNSPECIFIED: Strategy unspecified.
      UPDATE_UNIT_KIND_STRATEGY_ON_START: Update the unit kind strategy on the
        rollout start.
      UPDATE_UNIT_KIND_STRATEGY_NEVER: Never update the unit kind.
    """
    UPDATE_UNIT_KIND_STRATEGY_UNSPECIFIED = 0
    UPDATE_UNIT_KIND_STRATEGY_ON_START = 1
    UPDATE_UNIT_KIND_STRATEGY_NEVER = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Annotations is an unstructured key-value map stored with a
    resource that may be set by external tools to store and retrieve arbitrary
    metadata. They are not queryable and should be preserved when modifying
    objects. More info: https://kubernetes.io/docs/user-guide/annotations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels on the resource, which can be used for
    categorization. similar to Kubernetes resource labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  etag = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  maintenancePolicyEnforcement = _messages.EnumField('MaintenancePolicyEnforcementValueValuesEnum', 5)
  name = _messages.StringField(6)
  ongoingRollouts = _messages.StringField(7, repeated=True)
  rolloutOrchestrationStrategy = _messages.StringField(8)
  rolloutStrategy = _messages.MessageField('RolloutStrategy', 9)
  uid = _messages.StringField(10)
  unitFilter = _messages.StringField(11)
  unitKind = _messages.StringField(12)
  updateTime = _messages.StringField(13)
  updateUnitKindStrategy = _messages.EnumField('UpdateUnitKindStrategyValueValuesEnum', 14)


class RunRolloutActionParams(_messages.Message):
  r"""Parameters for the RUN action controlling the behavior of the rollout
  when it is resumed from a PAUSED state.

  Fields:
    retryFailedOperations: Required. If true, the rollout will retry failed
      operations when resumed. This is applicable only the current state of
      the Rollout is PAUSED and the requested action is RUN.
  """

  retryFailedOperations = _messages.BooleanField(1)


class Saas(_messages.Message):
  r"""Saas is a representation of a SaaS service managed by the Producer.

  Messages:
    AnnotationsValue: Optional. Annotations is an unstructured key-value map
      stored with a resource that may be set by external tools to store and
      retrieve arbitrary metadata. They are not queryable and should be
      preserved when modifying objects. More info:
      https://kubernetes.io/docs/user-guide/annotations
    LabelsValue: Optional. The labels on the resource, which can be used for
      categorization. similar to Kubernetes resource labels.

  Fields:
    annotations: Optional. Annotations is an unstructured key-value map stored
      with a resource that may be set by external tools to store and retrieve
      arbitrary metadata. They are not queryable and should be preserved when
      modifying objects. More info: https://kubernetes.io/docs/user-
      guide/annotations
    createTime: Output only. The timestamp when the resource was created.
    etag: Output only. An opaque value that uniquely identifies a version or
      generation of a resource. It can be used to confirm that the client and
      server agree on the ordering of a resource being written.
    labels: Optional. The labels on the resource, which can be used for
      categorization. similar to Kubernetes resource labels.
    locations: Optional. Immutable. List of locations that the service is
      available in. Rollout refers to the list to generate a rollout plan.
    name: Identifier. The resource name (full URI of the resource) following
      the standard naming scheme:
      "projects/{project}/locations/{location}/saas/{saas}"
    uid: Output only. The unique identifier of the resource. UID is unique in
      the time and space for this resource within the scope of the service. It
      is typically generated by the server on successful creation of a
      resource and must not be changed. UID is used to uniquely identify
      resources with resource name reuses. This should be a UUID4.
    updateTime: Output only. The timestamp when the resource was last updated.
      Any change to the resource made by users must refresh this value.
      Changes to a resource made by the service should refresh this value.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Annotations is an unstructured key-value map stored with a
    resource that may be set by external tools to store and retrieve arbitrary
    metadata. They are not queryable and should be preserved when modifying
    objects. More info: https://kubernetes.io/docs/user-guide/annotations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels on the resource, which can be used for
    categorization. similar to Kubernetes resource labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  etag = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  locations = _messages.MessageField('Location', 5, repeated=True)
  name = _messages.StringField(6)
  uid = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class SaasType(_messages.Message):
  r"""SaasType is a representation of a SaaS service managed by the Producer.

  Messages:
    AnnotationsValue: Optional. Annotations is an unstructured key-value map
      stored with a resource that may be set by external tools to store and
      retrieve arbitrary metadata. They are not queryable and should be
      preserved when modifying objects. More info:
      https://kubernetes.io/docs/user-guide/annotations
    LabelsValue: Optional. The labels on the resource, which can be used for
      categorization. similar to Kubernetes resource labels.

  Fields:
    annotations: Optional. Annotations is an unstructured key-value map stored
      with a resource that may be set by external tools to store and retrieve
      arbitrary metadata. They are not queryable and should be preserved when
      modifying objects. More info: https://kubernetes.io/docs/user-
      guide/annotations
    createTime: Output only. The timestamp when the resource was created.
    etag: Output only. An opaque value that uniquely identifies a version or
      generation of a resource. It can be used to confirm that the client and
      server agree on the ordering of a resource being written.
    labels: Optional. The labels on the resource, which can be used for
      categorization. similar to Kubernetes resource labels.
    locations: Optional. Immutable. List of locations that the service is
      available in. Rollout refers to the list to generate a rollout plan.
    name: Identifier. The resource name (full URI of the resource) following
      the standard naming scheme:
      "projects/{project}/locations/{location}/saasTypes/{saasType}"
    uid: Output only. The unique identifier of the resource. UID is unique in
      the time and space for this resource within the scope of the service. It
      is typically generated by the server on successful creation of a
      resource and must not be changed. UID is used to uniquely identify
      resources with resource name reuses. This should be a UUID4.
    updateTime: Output only. The timestamp when the resource was last updated.
      Any change to the resource made by users must refresh this value.
      Changes to a resource made by the service should refresh this value.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Annotations is an unstructured key-value map stored with a
    resource that may be set by external tools to store and retrieve arbitrary
    metadata. They are not queryable and should be preserved when modifying
    objects. More info: https://kubernetes.io/docs/user-guide/annotations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels on the resource, which can be used for
    categorization. similar to Kubernetes resource labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  etag = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  locations = _messages.MessageField('Location', 5, repeated=True)
  name = _messages.StringField(6)
  uid = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class SaasservicemgmtProjectsLocationsFlagReleasesCreateRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsFlagReleasesCreateRequest object.

  Fields:
    flagRelease: A FlagRelease resource to be passed as the request body.
    flagReleaseId: Required. The ID value for the new flag release.
    parent: Required. The parent of the flag release.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  flagRelease = _messages.MessageField('FlagRelease', 1)
  flagReleaseId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class SaasservicemgmtProjectsLocationsFlagReleasesDeleteRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsFlagReleasesDeleteRequest object.

  Fields:
    etag: The etag known to the client for the expected state of the flag
      release. This is used with state-changing methods to prevent accidental
      overwrites when multiple user agents might be acting in parallel on the
      same resource. An etag wildcard provide optimistic concurrency based on
      the expected existence of the flag release. The Any wildcard (`*`)
      requires that the resource must already exists, and the Not Any wildcard
      (`!*`) requires that it must not.
    name: Required. The resource name of the resource within a service.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SaasservicemgmtProjectsLocationsFlagReleasesGetRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsFlagReleasesGetRequest object.

  Fields:
    name: Required. The resource name of the resource within a service.
  """

  name = _messages.StringField(1, required=True)


class SaasservicemgmtProjectsLocationsFlagReleasesListRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsFlagReleasesListRequest object.

  Fields:
    filter: Filter the list as specified in https://google.aip.dev/160.
    orderBy: Order results as specified in https://google.aip.dev/132.
    pageSize: The maximum number of flag releases to send per page.
    pageToken: The page token: If the next_page_token from a previous response
      is provided, this request will send the subsequent page.
    parent: Required. The parent of the flag release.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class SaasservicemgmtProjectsLocationsFlagReleasesPatchRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsFlagReleasesPatchRequest object.

  Fields:
    etag: The etag known to the client for the expected state of the flag
      release. This is used with state-changing methods to prevent accidental
      overwrites when multiple user agents might be acting in parallel on the
      same resource. An etag wildcard provide optimistic concurrency based on
      the expected existence of the flag release. The Any wildcard (`*`)
      requires that the resource must already exists, and the Not Any wildcard
      (`!*`) requires that it must not.
    flagRelease: A FlagRelease resource to be passed as the request body.
    name: Identifier. The resource name (full URI of the resource) following
      the standard naming scheme:
      "projects/{project}/locations/{location}/flagReleases/{flag_release_id}"
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Field mask is used to specify the fields to be overwritten in
      the FlagRelease resource by the update. The fields specified in the
      update_mask are relative to the resource, not the full request. A field
      will be overwritten if it is in the mask. If the user does not provide a
      mask then all fields in the FlagRelease will be overwritten.
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  etag = _messages.StringField(1)
  flagRelease = _messages.MessageField('FlagRelease', 2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class SaasservicemgmtProjectsLocationsFlagRevisionsCreateRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsFlagRevisionsCreateRequest object.

  Fields:
    flagRevision: A FlagRevision resource to be passed as the request body.
    flagRevisionId: Required. The ID value for the new flag revision.
    parent: Required. The parent of the flag revision.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  flagRevision = _messages.MessageField('FlagRevision', 1)
  flagRevisionId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class SaasservicemgmtProjectsLocationsFlagRevisionsDeleteRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsFlagRevisionsDeleteRequest object.

  Fields:
    etag: The etag known to the client for the expected state of the flag
      revision. This is used with state-changing methods to prevent accidental
      overwrites when multiple user agents might be acting in parallel on the
      same resource. An etag wildcard provide optimistic concurrency based on
      the expected existence of the flag revision. The Any wildcard (`*`)
      requires that the resource must already exists, and the Not Any wildcard
      (`!*`) requires that it must not.
    name: Required. The resource name of the resource within a service.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SaasservicemgmtProjectsLocationsFlagRevisionsGetRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsFlagRevisionsGetRequest object.

  Fields:
    name: Required. The resource name of the resource within a service.
  """

  name = _messages.StringField(1, required=True)


class SaasservicemgmtProjectsLocationsFlagRevisionsListRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsFlagRevisionsListRequest object.

  Fields:
    filter: Filter the list as specified in https://google.aip.dev/160.
    orderBy: Order results as specified in https://google.aip.dev/132.
    pageSize: The maximum number of flag revisions to send per page.
    pageToken: The page token: If the next_page_token from a previous response
      is provided, this request will send the subsequent page.
    parent: Required. The parent of the flag revision.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class SaasservicemgmtProjectsLocationsFlagRevisionsPatchRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsFlagRevisionsPatchRequest object.

  Fields:
    etag: The etag known to the client for the expected state of the flag
      revision. This is used with state-changing methods to prevent accidental
      overwrites when multiple user agents might be acting in parallel on the
      same resource. An etag wildcard provide optimistic concurrency based on
      the expected existence of the flag revision. The Any wildcard (`*`)
      requires that the resource must already exists, and the Not Any wildcard
      (`!*`) requires that it must not.
    flagRevision: A FlagRevision resource to be passed as the request body.
    name: Identifier. The resource name (full URI of the resource) following
      the standard naming scheme: "projects/{project}/locations/{location}/fla
      gRevisions/{flag_revision_id}"
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Field mask is used to specify the fields to be overwritten in
      the FlagRevision resource by the update. The fields specified in the
      update_mask are relative to the resource, not the full request. A field
      will be overwritten if it is in the mask. If the user does not provide a
      mask then all fields in the FlagRevision will be overwritten.
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  etag = _messages.StringField(1)
  flagRevision = _messages.MessageField('FlagRevision', 2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class SaasservicemgmtProjectsLocationsFlagsCreateRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsFlagsCreateRequest object.

  Fields:
    flag: A Flag resource to be passed as the request body.
    flagId: Required. The ID value for the new flag.
    parent: Required. The parent of the flag.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  flag = _messages.MessageField('Flag', 1)
  flagId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class SaasservicemgmtProjectsLocationsFlagsDeleteRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsFlagsDeleteRequest object.

  Fields:
    etag: The etag known to the client for the expected state of the flag.
      This is used with state-changing methods to prevent accidental
      overwrites when multiple user agents might be acting in parallel on the
      same resource. An etag wildcard provide optimistic concurrency based on
      the expected existence of the flag. The Any wildcard (`*`) requires that
      the resource must already exists, and the Not Any wildcard (`!*`)
      requires that it must not.
    name: Required. The resource name of the resource within a service.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SaasservicemgmtProjectsLocationsFlagsGetRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsFlagsGetRequest object.

  Fields:
    name: Required. The resource name of the resource within a service.
  """

  name = _messages.StringField(1, required=True)


class SaasservicemgmtProjectsLocationsFlagsListRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsFlagsListRequest object.

  Fields:
    filter: Filter the list as specified in https://google.aip.dev/160.
    orderBy: Order results as specified in https://google.aip.dev/132.
    pageSize: The maximum number of flags to send per page.
    pageToken: The page token: If the next_page_token from a previous response
      is provided, this request will send the subsequent page.
    parent: Required. The parent of the flag.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class SaasservicemgmtProjectsLocationsFlagsPatchRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsFlagsPatchRequest object.

  Fields:
    etag: The etag known to the client for the expected state of the flag.
      This is used with state-changing methods to prevent accidental
      overwrites when multiple user agents might be acting in parallel on the
      same resource. An etag wildcard provide optimistic concurrency based on
      the expected existence of the flag. The Any wildcard (`*`) requires that
      the resource must already exists, and the Not Any wildcard (`!*`)
      requires that it must not.
    flag: A Flag resource to be passed as the request body.
    name: Identifier. The resource name (full URI of the resource) following
      the standard naming scheme:
      "projects/{project}/locations/{location}/flags/{flag_id}"
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Field mask is used to specify the fields to be overwritten in
      the Flag resource by the update. The fields specified in the update_mask
      are relative to the resource, not the full request. A field will be
      overwritten if it is in the mask. If the user does not provide a mask
      then all fields in the Flag will be overwritten.
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  etag = _messages.StringField(1)
  flag = _messages.MessageField('Flag', 2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class SaasservicemgmtProjectsLocationsGetRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class SaasservicemgmtProjectsLocationsListRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class SaasservicemgmtProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class SaasservicemgmtProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class SaasservicemgmtProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class SaasservicemgmtProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class SaasservicemgmtProjectsLocationsReleasesCreateRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsReleasesCreateRequest object.

  Fields:
    parent: Required. The parent of the release.
    release: A Release resource to be passed as the request body.
    releaseId: Required. The ID value for the new release.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  parent = _messages.StringField(1, required=True)
  release = _messages.MessageField('Release', 2)
  releaseId = _messages.StringField(3)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class SaasservicemgmtProjectsLocationsReleasesDeleteRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsReleasesDeleteRequest object.

  Fields:
    etag: The etag known to the client for the expected state of the release.
      This is used with state-changing methods to prevent accidental
      overwrites when multiple user agents might be acting in parallel on the
      same resource. An etag wildcard provide optimistic concurrency based on
      the expected existence of the release. The Any wildcard (`*`) requires
      that the resource must already exists, and the Not Any wildcard (`!*`)
      requires that it must not.
    name: Required. The resource name of the resource within a service.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SaasservicemgmtProjectsLocationsReleasesGetRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsReleasesGetRequest object.

  Fields:
    name: Required. The resource name of the resource within a service.
  """

  name = _messages.StringField(1, required=True)


class SaasservicemgmtProjectsLocationsReleasesListRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsReleasesListRequest object.

  Fields:
    filter: Filter the list as specified in https://google.aip.dev/160.
    orderBy: Order results as specified in https://google.aip.dev/132.
    pageSize: The maximum number of releases to send per page.
    pageToken: The page token: If the next_page_token from a previous response
      is provided, this request will send the subsequent page.
    parent: Required. The parent of the release.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class SaasservicemgmtProjectsLocationsReleasesPatchRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsReleasesPatchRequest object.

  Fields:
    etag: The etag known to the client for the expected state of the release.
      This is used with state-changing methods to prevent accidental
      overwrites when multiple user agents might be acting in parallel on the
      same resource. An etag wildcard provide optimistic concurrency based on
      the expected existence of the release. The Any wildcard (`*`) requires
      that the resource must already exists, and the Not Any wildcard (`!*`)
      requires that it must not.
    name: Identifier. The resource name (full URI of the resource) following
      the standard naming scheme:
      "projects/{project}/locations/{location}/releases/{release}"
    release: A Release resource to be passed as the request body.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Field mask is used to specify the fields to be overwritten in
      the Release resource by the update. The fields specified in the
      update_mask are relative to the resource, not the full request. A field
      will be overwritten if it is in the mask. If the user does not provide a
      mask then all fields in the Release will be overwritten.
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  release = _messages.MessageField('Release', 3)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class SaasservicemgmtProjectsLocationsRolloutKindsCreateRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsRolloutKindsCreateRequest object.

  Fields:
    parent: Required. The parent of the rollout kind.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    rolloutKind: A RolloutKind resource to be passed as the request body.
    rolloutKindId: Required. The ID value for the new rollout kind.
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  rolloutKind = _messages.MessageField('RolloutKind', 3)
  rolloutKindId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class SaasservicemgmtProjectsLocationsRolloutKindsDeleteRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsRolloutKindsDeleteRequest object.

  Fields:
    etag: The etag known to the client for the expected state of the rollout
      kind. This is used with state-changing methods to prevent accidental
      overwrites when multiple user agents might be acting in parallel on the
      same resource. An etag wildcard provide optimistic concurrency based on
      the expected existence of the rollout kind. The Any wildcard (`*`)
      requires that the resource must already exists, and the Not Any wildcard
      (`!*`) requires that it must not.
    name: Required. The resource name of the resource within a service.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SaasservicemgmtProjectsLocationsRolloutKindsGetRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsRolloutKindsGetRequest object.

  Fields:
    name: Required. The resource name of the resource within a service.
  """

  name = _messages.StringField(1, required=True)


class SaasservicemgmtProjectsLocationsRolloutKindsListRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsRolloutKindsListRequest object.

  Fields:
    filter: Filter the list as specified in https://google.aip.dev/160.
    orderBy: Order results as specified in https://google.aip.dev/132.
    pageSize: The maximum number of rollout kinds to send per page.
    pageToken: The page token: If the next_page_token from a previous response
      is provided, this request will send the subsequent page.
    parent: Required. The parent of the rollout kind.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class SaasservicemgmtProjectsLocationsRolloutKindsPatchRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsRolloutKindsPatchRequest object.

  Fields:
    etag: The etag known to the client for the expected state of the rollout
      kind. This is used with state-changing methods to prevent accidental
      overwrites when multiple user agents might be acting in parallel on the
      same resource. An etag wildcard provide optimistic concurrency based on
      the expected existence of the rollout kind. The Any wildcard (`*`)
      requires that the resource must already exists, and the Not Any wildcard
      (`!*`) requires that it must not.
    name: Identifier. The resource name (full URI of the resource) following
      the standard naming scheme:
      "projects/{project}/locations/{location}/rolloutKinds/{rollout_kind_id}"
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    rolloutKind: A RolloutKind resource to be passed as the request body.
    updateMask: Field mask is used to specify the fields to be overwritten in
      the RolloutKind resource by the update. The fields specified in the
      update_mask are relative to the resource, not the full request. A field
      will be overwritten if it is in the mask. If the user does not provide a
      mask then all fields in the RolloutKind will be overwritten.
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  rolloutKind = _messages.MessageField('RolloutKind', 4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class SaasservicemgmtProjectsLocationsRolloutTypesCreateRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsRolloutTypesCreateRequest object.

  Fields:
    parent: Required. The parent of the rollout type.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    rolloutType: A RolloutType resource to be passed as the request body.
    rolloutTypeId: Required. The ID value for the new rollout type.
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  rolloutType = _messages.MessageField('RolloutType', 3)
  rolloutTypeId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class SaasservicemgmtProjectsLocationsRolloutTypesDeleteRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsRolloutTypesDeleteRequest object.

  Fields:
    etag: The etag known to the client for the expected state of the rollout
      type. This is used with state-changing methods to prevent accidental
      overwrites when multiple user agents might be acting in parallel on the
      same resource. An etag wildcard provide optimistic concurrency based on
      the expected existence of the rollout type. The Any wildcard (`*`)
      requires that the resource must already exists, and the Not Any wildcard
      (`!*`) requires that it must not.
    name: Required. The resource name of the resource within a service.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SaasservicemgmtProjectsLocationsRolloutTypesGetRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsRolloutTypesGetRequest object.

  Fields:
    name: Required. The resource name of the resource within a service.
  """

  name = _messages.StringField(1, required=True)


class SaasservicemgmtProjectsLocationsRolloutTypesListRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsRolloutTypesListRequest object.

  Fields:
    filter: Filter the list as specified in https://google.aip.dev/160.
    orderBy: Order results as specified in https://google.aip.dev/132.
    pageSize: The maximum number of rollout types to send per page.
    pageToken: The page token: If the next_page_token from a previous response
      is provided, this request will send the subsequent page.
    parent: Required. The parent of the rollout type.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class SaasservicemgmtProjectsLocationsRolloutTypesPatchRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsRolloutTypesPatchRequest object.

  Fields:
    etag: The etag known to the client for the expected state of the rollout
      type. This is used with state-changing methods to prevent accidental
      overwrites when multiple user agents might be acting in parallel on the
      same resource. An etag wildcard provide optimistic concurrency based on
      the expected existence of the rollout type. The Any wildcard (`*`)
      requires that the resource must already exists, and the Not Any wildcard
      (`!*`) requires that it must not.
    name: Identifier. The resource name (full URI of the resource) following
      the standard naming scheme:
      "projects/{project}/locations/{location}/rolloutTypes/{rollout_type_id}"
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    rolloutType: A RolloutType resource to be passed as the request body.
    updateMask: Field mask is used to specify the fields to be overwritten in
      the RolloutType resource by the update. The fields specified in the
      update_mask are relative to the resource, not the full request. A field
      will be overwritten if it is in the mask. If the user does not provide a
      mask then all fields in the RolloutType will be overwritten.
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  rolloutType = _messages.MessageField('RolloutType', 4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class SaasservicemgmtProjectsLocationsRolloutsCreateRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsRolloutsCreateRequest object.

  Fields:
    parent: Required. The parent of the rollout.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    rollout: A Rollout resource to be passed as the request body.
    rolloutId: Required. The ID value for the new rollout.
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  rollout = _messages.MessageField('Rollout', 3)
  rolloutId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class SaasservicemgmtProjectsLocationsRolloutsDeleteRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsRolloutsDeleteRequest object.

  Fields:
    etag: The etag known to the client for the expected state of the rollout.
      This is used with state-changing methods to prevent accidental
      overwrites when multiple user agents might be acting in parallel on the
      same resource. An etag wildcard provide optimistic concurrency based on
      the expected existence of the rollout. The Any wildcard (`*`) requires
      that the resource must already exists, and the Not Any wildcard (`!*`)
      requires that it must not.
    name: Required. The resource name of the resource within a service.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SaasservicemgmtProjectsLocationsRolloutsGetRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsRolloutsGetRequest object.

  Fields:
    name: Required. The resource name of the resource within a service.
  """

  name = _messages.StringField(1, required=True)


class SaasservicemgmtProjectsLocationsRolloutsListRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsRolloutsListRequest object.

  Fields:
    filter: Filter the list as specified in https://google.aip.dev/160.
    orderBy: Order results as specified in https://google.aip.dev/132.
    pageSize: The maximum number of rollouts to send per page.
    pageToken: The page token: If the next_page_token from a previous response
      is provided, this request will send the subsequent page.
    parent: Required. The parent of the rollout.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class SaasservicemgmtProjectsLocationsRolloutsPatchRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsRolloutsPatchRequest object.

  Fields:
    etag: The etag known to the client for the expected state of the rollout.
      This is used with state-changing methods to prevent accidental
      overwrites when multiple user agents might be acting in parallel on the
      same resource. An etag wildcard provide optimistic concurrency based on
      the expected existence of the rollout. The Any wildcard (`*`) requires
      that the resource must already exists, and the Not Any wildcard (`!*`)
      requires that it must not.
    name: Identifier. The resource name (full URI of the resource) following
      the standard naming scheme:
      "projects/{project}/locations/{location}/rollout/{rollout_id}"
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    rollout: A Rollout resource to be passed as the request body.
    updateMask: Field mask is used to specify the fields to be overwritten in
      the Rollout resource by the update. The fields specified in the
      update_mask are relative to the resource, not the full request. A field
      will be overwritten if it is in the mask. If the user does not provide a
      mask then all fields in the Rollout will be overwritten.
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  rollout = _messages.MessageField('Rollout', 4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class SaasservicemgmtProjectsLocationsSaasCreateRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsSaasCreateRequest object.

  Fields:
    parent: Required. The parent of the saas.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    saas: A Saas resource to be passed as the request body.
    saasId: Required. The ID value for the new saas.
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  saas = _messages.MessageField('Saas', 3)
  saasId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class SaasservicemgmtProjectsLocationsSaasDeleteRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsSaasDeleteRequest object.

  Fields:
    etag: The etag known to the client for the expected state of the saas.
      This is used with state-changing methods to prevent accidental
      overwrites when multiple user agents might be acting in parallel on the
      same resource. An etag wildcard provide optimistic concurrency based on
      the expected existence of the saas. The Any wildcard (`*`) requires that
      the resource must already exists, and the Not Any wildcard (`!*`)
      requires that it must not.
    name: Required. The resource name of the resource within a service.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SaasservicemgmtProjectsLocationsSaasGetRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsSaasGetRequest object.

  Fields:
    name: Required. The resource name of the resource within a service.
  """

  name = _messages.StringField(1, required=True)


class SaasservicemgmtProjectsLocationsSaasListRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsSaasListRequest object.

  Fields:
    filter: Filter the list as specified in https://google.aip.dev/160.
    orderBy: Order results as specified in https://google.aip.dev/132.
    pageSize: The maximum number of saas to send per page.
    pageToken: The page token: If the next_page_token from a previous response
      is provided, this request will send the subsequent page.
    parent: Required. The parent of the saas.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class SaasservicemgmtProjectsLocationsSaasPatchRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsSaasPatchRequest object.

  Fields:
    etag: The etag known to the client for the expected state of the saas.
      This is used with state-changing methods to prevent accidental
      overwrites when multiple user agents might be acting in parallel on the
      same resource. An etag wildcard provide optimistic concurrency based on
      the expected existence of the saas. The Any wildcard (`*`) requires that
      the resource must already exists, and the Not Any wildcard (`!*`)
      requires that it must not.
    name: Identifier. The resource name (full URI of the resource) following
      the standard naming scheme:
      "projects/{project}/locations/{location}/saas/{saas}"
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    saas: A Saas resource to be passed as the request body.
    updateMask: Field mask is used to specify the fields to be overwritten in
      the Saas resource by the update. The fields specified in the update_mask
      are relative to the resource, not the full request. A field will be
      overwritten if it is in the mask. If the user does not provide a mask
      then all fields in the Saas will be overwritten.
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  saas = _messages.MessageField('Saas', 4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class SaasservicemgmtProjectsLocationsSaasTypesCreateRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsSaasTypesCreateRequest object.

  Fields:
    parent: Required. The parent of the saas type.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    saasType: A SaasType resource to be passed as the request body.
    saasTypeId: Required. The ID value for the new saas type.
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  saasType = _messages.MessageField('SaasType', 3)
  saasTypeId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class SaasservicemgmtProjectsLocationsSaasTypesDeleteRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsSaasTypesDeleteRequest object.

  Fields:
    etag: The etag known to the client for the expected state of the saas
      type. This is used with state-changing methods to prevent accidental
      overwrites when multiple user agents might be acting in parallel on the
      same resource. An etag wildcard provide optimistic concurrency based on
      the expected existence of the saas type. The Any wildcard (`*`) requires
      that the resource must already exists, and the Not Any wildcard (`!*`)
      requires that it must not.
    name: Required. The resource name of the resource within a service.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SaasservicemgmtProjectsLocationsSaasTypesGetRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsSaasTypesGetRequest object.

  Fields:
    name: Required. The resource name of the resource within a service.
  """

  name = _messages.StringField(1, required=True)


class SaasservicemgmtProjectsLocationsSaasTypesListRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsSaasTypesListRequest object.

  Fields:
    filter: Filter the list as specified in https://google.aip.dev/160.
    orderBy: Order results as specified in https://google.aip.dev/132.
    pageSize: The maximum number of saas types to send per page.
    pageToken: The page token: If the next_page_token from a previous response
      is provided, this request will send the subsequent page.
    parent: Required. The parent of the saas type.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class SaasservicemgmtProjectsLocationsSaasTypesPatchRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsSaasTypesPatchRequest object.

  Fields:
    etag: The etag known to the client for the expected state of the saas
      type. This is used with state-changing methods to prevent accidental
      overwrites when multiple user agents might be acting in parallel on the
      same resource. An etag wildcard provide optimistic concurrency based on
      the expected existence of the saas type. The Any wildcard (`*`) requires
      that the resource must already exists, and the Not Any wildcard (`!*`)
      requires that it must not.
    name: Identifier. The resource name (full URI of the resource) following
      the standard naming scheme:
      "projects/{project}/locations/{location}/saasTypes/{saasType}"
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    saasType: A SaasType resource to be passed as the request body.
    updateMask: Field mask is used to specify the fields to be overwritten in
      the SaasType resource by the update. The fields specified in the
      update_mask are relative to the resource, not the full request. A field
      will be overwritten if it is in the mask. If the user does not provide a
      mask then all fields in the SaasType will be overwritten.
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  saasType = _messages.MessageField('SaasType', 4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class SaasservicemgmtProjectsLocationsTenantsCreateRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsTenantsCreateRequest object.

  Fields:
    parent: Required. The parent of the tenant.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    tenant: A Tenant resource to be passed as the request body.
    tenantId: Required. The ID value for the new tenant.
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  tenant = _messages.MessageField('Tenant', 3)
  tenantId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class SaasservicemgmtProjectsLocationsTenantsDeleteRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsTenantsDeleteRequest object.

  Fields:
    etag: The etag known to the client for the expected state of the tenant.
      This is used with state-changing methods to prevent accidental
      overwrites when multiple user agents might be acting in parallel on the
      same resource. An etag wildcard provide optimistic concurrency based on
      the expected existence of the tenant. The Any wildcard (`*`) requires
      that the resource must already exists, and the Not Any wildcard (`!*`)
      requires that it must not.
    name: Required. The resource name of the resource within a service.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SaasservicemgmtProjectsLocationsTenantsGetRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsTenantsGetRequest object.

  Fields:
    name: Required. The resource name of the resource within a service.
  """

  name = _messages.StringField(1, required=True)


class SaasservicemgmtProjectsLocationsTenantsListRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsTenantsListRequest object.

  Fields:
    filter: Filter the list as specified in https://google.aip.dev/160.
    orderBy: Order results as specified in https://google.aip.dev/132.
    pageSize: The maximum number of tenants to send per page.
    pageToken: The page token: If the next_page_token from a previous response
      is provided, this request will send the subsequent page.
    parent: Required. The parent of the tenant.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class SaasservicemgmtProjectsLocationsTenantsPatchRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsTenantsPatchRequest object.

  Fields:
    etag: The etag known to the client for the expected state of the tenant.
      This is used with state-changing methods to prevent accidental
      overwrites when multiple user agents might be acting in parallel on the
      same resource. An etag wildcard provide optimistic concurrency based on
      the expected existence of the tenant. The Any wildcard (`*`) requires
      that the resource must already exists, and the Not Any wildcard (`!*`)
      requires that it must not.
    name: Identifier. The resource name (full URI of the resource) following
      the standard naming scheme:
      "projects/{project}/locations/{location}/tenants/{tenant}"
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    tenant: A Tenant resource to be passed as the request body.
    updateMask: Field mask is used to specify the fields to be overwritten in
      the Tenant resource by the update. The fields specified in the
      update_mask are relative to the resource, not the full request. A field
      will be overwritten if it is in the mask. If the user does not provide a
      mask then all fields in the Tenant will be overwritten.
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  tenant = _messages.MessageField('Tenant', 4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class SaasservicemgmtProjectsLocationsUnitKindsCreateRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsUnitKindsCreateRequest object.

  Fields:
    parent: Required. The parent of the unit kind.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    unitKind: A UnitKind resource to be passed as the request body.
    unitKindId: Required. The ID value for the new unit kind.
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  unitKind = _messages.MessageField('UnitKind', 3)
  unitKindId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class SaasservicemgmtProjectsLocationsUnitKindsDeleteRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsUnitKindsDeleteRequest object.

  Fields:
    etag: The etag known to the client for the expected state of the unit
      kind. This is used with state-changing methods to prevent accidental
      overwrites when multiple user agents might be acting in parallel on the
      same resource. An etag wildcard provide optimistic concurrency based on
      the expected existence of the unit kind. The Any wildcard (`*`) requires
      that the resource must already exists, and the Not Any wildcard (`!*`)
      requires that it must not.
    name: Required. The resource name of the resource within a service.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SaasservicemgmtProjectsLocationsUnitKindsGetRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsUnitKindsGetRequest object.

  Fields:
    name: Required. The resource name of the resource within a service.
  """

  name = _messages.StringField(1, required=True)


class SaasservicemgmtProjectsLocationsUnitKindsListRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsUnitKindsListRequest object.

  Fields:
    filter: Filter the list as specified in https://google.aip.dev/160.
    orderBy: Order results as specified in https://google.aip.dev/132.
    pageSize: The maximum number of unit kinds to send per page.
    pageToken: The page token: If the next_page_token from a previous response
      is provided, this request will send the subsequent page.
    parent: Required. The parent of the unit kind.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class SaasservicemgmtProjectsLocationsUnitKindsPatchRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsUnitKindsPatchRequest object.

  Fields:
    etag: The etag known to the client for the expected state of the unit
      kind. This is used with state-changing methods to prevent accidental
      overwrites when multiple user agents might be acting in parallel on the
      same resource. An etag wildcard provide optimistic concurrency based on
      the expected existence of the unit kind. The Any wildcard (`*`) requires
      that the resource must already exists, and the Not Any wildcard (`!*`)
      requires that it must not.
    name: Identifier. The resource name (full URI of the resource) following
      the standard naming scheme:
      "projects/{project}/locations/{location}/unitKinds/{unitKind}"
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    unitKind: A UnitKind resource to be passed as the request body.
    updateMask: Field mask is used to specify the fields to be overwritten in
      the UnitKind resource by the update. The fields specified in the
      update_mask are relative to the resource, not the full request. A field
      will be overwritten if it is in the mask. If the user does not provide a
      mask then all fields in the UnitKind will be overwritten.
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  unitKind = _messages.MessageField('UnitKind', 4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class SaasservicemgmtProjectsLocationsUnitOperationsCreateRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsUnitOperationsCreateRequest object.

  Fields:
    parent: Required. The parent of the unit operation.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    unitOperation: A UnitOperation resource to be passed as the request body.
    unitOperationId: Required. The ID value for the new unit operation.
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  unitOperation = _messages.MessageField('UnitOperation', 3)
  unitOperationId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class SaasservicemgmtProjectsLocationsUnitOperationsDeleteRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsUnitOperationsDeleteRequest object.

  Fields:
    etag: The etag known to the client for the expected state of the unit
      operation. This is used with state-changing methods to prevent
      accidental overwrites when multiple user agents might be acting in
      parallel on the same resource. An etag wildcard provide optimistic
      concurrency based on the expected existence of the unit operation. The
      Any wildcard (`*`) requires that the resource must already exists, and
      the Not Any wildcard (`!*`) requires that it must not.
    name: Required. The resource name of the resource within a service.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SaasservicemgmtProjectsLocationsUnitOperationsGetRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsUnitOperationsGetRequest object.

  Fields:
    name: Required. The resource name of the resource within a service.
  """

  name = _messages.StringField(1, required=True)


class SaasservicemgmtProjectsLocationsUnitOperationsListRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsUnitOperationsListRequest object.

  Fields:
    filter: Filter the list as specified in https://google.aip.dev/160.
    orderBy: Order results as specified in https://google.aip.dev/132.
    pageSize: The maximum number of unit operations to send per page.
    pageToken: The page token: If the next_page_token from a previous response
      is provided, this request will send the subsequent page.
    parent: Required. The parent of the unit operation.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class SaasservicemgmtProjectsLocationsUnitOperationsPatchRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsUnitOperationsPatchRequest object.

  Fields:
    etag: The etag known to the client for the expected state of the unit
      operation. This is used with state-changing methods to prevent
      accidental overwrites when multiple user agents might be acting in
      parallel on the same resource. An etag wildcard provide optimistic
      concurrency based on the expected existence of the unit operation. The
      Any wildcard (`*`) requires that the resource must already exists, and
      the Not Any wildcard (`!*`) requires that it must not.
    name: Identifier. The resource name (full URI of the resource) following
      the standard naming scheme:
      "projects/{project}/locations/{location}/unitOperations/{unitOperation}"
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    unitOperation: A UnitOperation resource to be passed as the request body.
    updateMask: Field mask is used to specify the fields to be overwritten in
      the UnitOperation resource by the update. The fields specified in the
      update_mask are relative to the resource, not the full request. A field
      will be overwritten if it is in the mask. If the user does not provide a
      mask then all fields in the UnitOperation will be overwritten.
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  unitOperation = _messages.MessageField('UnitOperation', 4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class SaasservicemgmtProjectsLocationsUnitsCreateRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsUnitsCreateRequest object.

  Fields:
    parent: Required. The parent of the unit.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    unit: A Unit resource to be passed as the request body.
    unitId: Required. The ID value for the new unit.
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  unit = _messages.MessageField('Unit', 3)
  unitId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class SaasservicemgmtProjectsLocationsUnitsDeleteRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsUnitsDeleteRequest object.

  Fields:
    etag: The etag known to the client for the expected state of the unit.
      This is used with state-changing methods to prevent accidental
      overwrites when multiple user agents might be acting in parallel on the
      same resource. An etag wildcard provide optimistic concurrency based on
      the expected existence of the unit. The Any wildcard (`*`) requires that
      the resource must already exists, and the Not Any wildcard (`!*`)
      requires that it must not.
    name: Required. The resource name of the resource within a service.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SaasservicemgmtProjectsLocationsUnitsGetRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsUnitsGetRequest object.

  Fields:
    name: Required. The resource name of the resource within a service.
  """

  name = _messages.StringField(1, required=True)


class SaasservicemgmtProjectsLocationsUnitsListRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsUnitsListRequest object.

  Fields:
    filter: Filter the list as specified in https://google.aip.dev/160.
    orderBy: Order results as specified in https://google.aip.dev/132.
    pageSize: The maximum number of units to send per page.
    pageToken: The page token: If the next_page_token from a previous response
      is provided, this request will send the subsequent page.
    parent: Required. The parent of the unit.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class SaasservicemgmtProjectsLocationsUnitsPatchRequest(_messages.Message):
  r"""A SaasservicemgmtProjectsLocationsUnitsPatchRequest object.

  Fields:
    etag: The etag known to the client for the expected state of the unit.
      This is used with state-changing methods to prevent accidental
      overwrites when multiple user agents might be acting in parallel on the
      same resource. An etag wildcard provide optimistic concurrency based on
      the expected existence of the unit. The Any wildcard (`*`) requires that
      the resource must already exists, and the Not Any wildcard (`!*`)
      requires that it must not.
    name: Identifier. The resource name (full URI of the resource) following
      the standard naming scheme:
      "projects/{project}/locations/{location}/units/{unit}"
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    unit: A Unit resource to be passed as the request body.
    updateMask: Field mask is used to specify the fields to be overwritten in
      the Unit resource by the update. The fields specified in the update_mask
      are relative to the resource, not the full request. A field will be
      overwritten if it is in the mask. If the user does not provide a mask
      then all fields in the Unit will be overwritten.
    validateOnly: If "validate_only" is set to true, the service will try to
      validate that this request would succeed, but will not actually make
      changes.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  unit = _messages.MessageField('Unit', 4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class Schedule(_messages.Message):
  r"""A time specification to schedule the maintenance.

  Fields:
    startTime: Optional. Start of operation. If not set, will be set to the
      start of the next window. (optional)
  """

  startTime = _messages.StringField(1)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class Tenant(_messages.Message):
  r"""Tenant represents the service producer side of an instance of the
  service created based on a request from a consumer. In a typical scenario a
  Tenant has a one-to-one mapping with a resource given out to a service
  consumer. Example: tenant: name:
  "projects/svc1/locations/loc/tenants/inst-068afff8" consumer_resource:
  "projects/gshoe/locations/loc/shoes/black-shoe"

  Messages:
    AnnotationsValue: Optional. Annotations is an unstructured key-value map
      stored with a resource that may be set by external tools to store and
      retrieve arbitrary metadata. They are not queryable and should be
      preserved when modifying objects. More info:
      https://kubernetes.io/docs/user-guide/annotations
    LabelsValue: Optional. The labels on the resource, which can be used for
      categorization. similar to Kubernetes resource labels.

  Fields:
    annotations: Optional. Annotations is an unstructured key-value map stored
      with a resource that may be set by external tools to store and retrieve
      arbitrary metadata. They are not queryable and should be preserved when
      modifying objects. More info: https://kubernetes.io/docs/user-
      guide/annotations
    consumerResource: Optional. Immutable. A reference to the consumer
      resource this SaaS Tenant is representing. The relationship with a
      consumer resource can be used by EasySaaS for retrieving consumer-
      defined settings and policies such as maintenance policies (using
      Unified Maintenance Policy API).
    createTime: Output only. The timestamp when the resource was created.
    etag: Output only. An opaque value that uniquely identifies a version or
      generation of a resource. It can be used to confirm that the client and
      server agree on the ordering of a resource being written.
    labels: Optional. The labels on the resource, which can be used for
      categorization. similar to Kubernetes resource labels.
    name: Identifier. The resource name (full URI of the resource) following
      the standard naming scheme:
      "projects/{project}/locations/{location}/tenants/{tenant}"
    saas: Required. Immutable. A reference to the Saas that defines the
      product (managed service) that the producer wants to manage with
      EasySaaS. Part of the EasySaaS common data model.
    uid: Output only. The unique identifier of the resource. UID is unique in
      the time and space for this resource within the scope of the service. It
      is typically generated by the server on successful creation of a
      resource and must not be changed. UID is used to uniquely identify
      resources with resource name reuses. This should be a UUID4.
    updateTime: Output only. The timestamp when the resource was last updated.
      Any change to the resource made by users must refresh this value.
      Changes to a resource made by the service should refresh this value.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Annotations is an unstructured key-value map stored with a
    resource that may be set by external tools to store and retrieve arbitrary
    metadata. They are not queryable and should be preserved when modifying
    objects. More info: https://kubernetes.io/docs/user-guide/annotations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels on the resource, which can be used for
    categorization. similar to Kubernetes resource labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  consumerResource = _messages.StringField(2)
  createTime = _messages.StringField(3)
  etag = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  saas = _messages.StringField(7)
  uid = _messages.StringField(8)
  updateTime = _messages.StringField(9)


class ToMapping(_messages.Message):
  r"""Input variables whose values will be passed on to dependencies

  Fields:
    dependency: Required. Alias of the dependency that the inputVariable will
      pass its value to
    ignoreForLookup: Optional. Tells EasySaaS if this mapping should be used
      during lookup or not
    inputVariable: Required. Name of the inputVariable on the dependency
  """

  dependency = _messages.StringField(1)
  ignoreForLookup = _messages.BooleanField(2)
  inputVariable = _messages.StringField(3)


class Unit(_messages.Message):
  r"""A unit of deployment that has its lifecycle via a CRUD API using an
  actuation engine under the hood (e.g. based on Terraform, Helm or a custom
  implementation provided by a service producer). A building block of a SaaS
  Tenant.

  Enums:
    ManagementModeValueValuesEnum: Optional. Immutable. Indicates whether the
      Unit life cycle is controlled by the user or by the system. Immutable
      once created.
    OperationModeValueValuesEnum: Optional. The operation mode for how the
      unit can be run.
    StateValueValuesEnum: Optional. Output only. Current lifecycle state of
      the resource (e.g. if it's being created or ready to use).
    SystemManagedStateValueValuesEnum: Optional. Output only. Indicates the
      system managed state of the unit.

  Messages:
    AnnotationsValue: Optional. Annotations is an unstructured key-value map
      stored with a resource that may be set by external tools to store and
      retrieve arbitrary metadata. They are not queryable and should be
      preserved when modifying objects. More info:
      https://kubernetes.io/docs/user-guide/annotations
    LabelsValue: Optional. The labels on the resource, which can be used for
      categorization. similar to Kubernetes resource labels.

  Fields:
    annotations: Optional. Annotations is an unstructured key-value map stored
      with a resource that may be set by external tools to store and retrieve
      arbitrary metadata. They are not queryable and should be preserved when
      modifying objects. More info: https://kubernetes.io/docs/user-
      guide/annotations
    blueprint: Optional. Blueprints are OCI Images that contain all of the
      artifacts needed to provision a unit.
    conditions: Optional. Output only. A set of conditions which indicate the
      various conditions this resource can have.
    createTime: Output only. The timestamp when the resource was created.
    dependencies: Optional. Output only. Set of dependencies for this unit.
      Maximum 10.
    dependents: Optional. Output only. List of Units that depend on this unit.
      Unit can only be deprovisioned if this list is empty. Maximum 1000.
    etag: Output only. An opaque value that uniquely identifies a version or
      generation of a resource. It can be used to confirm that the client and
      server agree on the ordering of a resource being written.
    flagRevisions: Optional. Output only. Flag revisions used by this Unit.
    inputVariables: Optional. Output only. Indicates the current input
      variables deployed by the unit
    labels: Optional. The labels on the resource, which can be used for
      categorization. similar to Kubernetes resource labels.
    maintenance: Optional. Captures requested directives for performing future
      maintenance on the unit. This includes a request for the unit to skip
      maintenance for a period of time and remain pinned to its current
      release as well as controls for postponing maintenance scheduled in
      future.
    managementMode: Optional. Immutable. Indicates whether the Unit life cycle
      is controlled by the user or by the system. Immutable once created.
    name: Identifier. The resource name (full URI of the resource) following
      the standard naming scheme:
      "projects/{project}/locations/{location}/units/{unit}"
    nextMaintenance: Optional. Output only. Next scheduled maintenance on the
      unit. The maintenance schedule is determined according to maintenance
      policy rules of the associated Tenant.
    ongoingOperations: Optional. Output only. List of concurrent
      UnitOperations that are operating on this Unit.
    operationMode: Optional. The operation mode for how the unit can be run.
    outputVariables: Optional. Output only. Set of key/value pairs
      corresponding to output variables from execution of actuation templates.
      The variables are declared in actuation configs (e.g in helm chart or
      terraform) and the values are fetched and returned by the actuation
      engine upon completion of execution.
    pendingOperations: Optional. Output only. List of pending (wait to be
      executed) UnitOperations for this unit.
    release: Optional. Output only. The current Release object for this Unit.
    scheduledOperations: Optional. Output only. List of scheduled
      UnitOperations for this unit.
    state: Optional. Output only. Current lifecycle state of the resource
      (e.g. if it's being created or ready to use).
    systemCleanupAt: Optional. Output only. If set, indicates the time when
      the system will start removing the unit.
    systemManagedState: Optional. Output only. Indicates the system managed
      state of the unit.
    targetInputVariables: Optional. Indicates the target input variables
      specified by the customer. Maximum 100.
    targetRelease: Optional. Reference to the Release object to use for the
      Unit. (optional). If not specified, UnitKind's defaultRelease will be
      used. Note that this can only be changed if it is the same as
      current_release (i.e. there is no ongoing upgrade), otherwise it will
      fail validation. Note: While the KRM version of Unit will also include
      the `target_release` field, in OnePlatform version only the `release` is
      exposed on the resource, based on the guideline from go/aip/128 which
      states: > Services responding to a GET request must return the
      resource's current > state (not the intended state). In OnePlatform, the
      desired state (`target_release`) will be reconciled onto the resource
      via a long running Operation.
    tenant: Optional. Reference to the Saas Tenant resource this unit belongs
      to. This for example informs the maintenance policies to use for
      scheduling future updates on a unit. (optional and immutable once
      created)
    uid: Output only. The unique identifier of the resource. UID is unique in
      the time and space for this resource within the scope of the service. It
      is typically generated by the server on successful creation of a
      resource and must not be changed. UID is used to uniquely identify
      resources with resource name reuses. This should be a UUID4.
    unitKind: Optional. Reference to the UnitKind this Unit belongs to.
      Immutable once set.
    updateTime: Output only. The timestamp when the resource was last updated.
      Any change to the resource made by users must refresh this value.
      Changes to a resource made by the service should refresh this value.
  """

  class ManagementModeValueValuesEnum(_messages.Enum):
    r"""Optional. Immutable. Indicates whether the Unit life cycle is
    controlled by the user or by the system. Immutable once created.

    Values:
      MANAGEMENT_MODE_UNSPECIFIED: <no description>
      MANAGEMENT_MODE_USER: Unit's lifecycle is managed by the user.
      MANAGEMENT_MODE_SYSTEM: The system will decide when to deprovision and
        delete the unit. User still can deprovision or delete the unit
        manually.
    """
    MANAGEMENT_MODE_UNSPECIFIED = 0
    MANAGEMENT_MODE_USER = 1
    MANAGEMENT_MODE_SYSTEM = 2

  class OperationModeValueValuesEnum(_messages.Enum):
    r"""Optional. The operation mode for how the unit can be run.

    Values:
      OPERATION_MODE_UNSPECIFIED: Unspecified operation.
      OPERATION_MODE_MANUAL: In manual mode, changes to Unit will have no
        effect until a corresponding operation is created.
      OPERATION_MODE_AUTOMATIC: In automatic mode, deployments will
        automatically create unit operations to reflect changes reconciled on
        unit.
    """
    OPERATION_MODE_UNSPECIFIED = 0
    OPERATION_MODE_MANUAL = 1
    OPERATION_MODE_AUTOMATIC = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Optional. Output only. Current lifecycle state of the resource (e.g.
    if it's being created or ready to use).

    Values:
      UNIT_STATE_UNSPECIFIED: Unspecified state.
      UNIT_STATE_NOT_PROVISIONED: Unit is not provisioned.
      UNIT_STATE_PROVISIONING: Unit is being provisioned.
      UNIT_STATE_UPDATING: Unit is being updated. This is typically when a
        unit is being upgraded to a new release or some of the input variables
        on the Unit is being changed. Certain kinds of updates may cause the
        Unit to become unusable while the update is in progress.
      UNIT_STATE_DEPROVISIONING: Unit is being deleted.
      UNIT_STATE_READY: Unit has been provisioned and is ready for use
      UNIT_STATE_ERROR: Unit has error, when it is not ready and some error
        operation
    """
    UNIT_STATE_UNSPECIFIED = 0
    UNIT_STATE_NOT_PROVISIONED = 1
    UNIT_STATE_PROVISIONING = 2
    UNIT_STATE_UPDATING = 3
    UNIT_STATE_DEPROVISIONING = 4
    UNIT_STATE_READY = 5
    UNIT_STATE_ERROR = 6

  class SystemManagedStateValueValuesEnum(_messages.Enum):
    r"""Optional. Output only. Indicates the system managed state of the unit.

    Values:
      SYSTEM_MANAGED_STATE_UNSPECIFIED: <no description>
      SYSTEM_MANAGED_STATE_ACTIVE: Unit has dependents attached.
      SYSTEM_MANAGED_STATE_INACTIVE: Unit has no dependencies attached, but
        attachment is allowed.
      SYSTEM_MANAGED_STATE_DECOMMISSIONED: Unit has no dependencies attached,
        and attachment is not allowed.
    """
    SYSTEM_MANAGED_STATE_UNSPECIFIED = 0
    SYSTEM_MANAGED_STATE_ACTIVE = 1
    SYSTEM_MANAGED_STATE_INACTIVE = 2
    SYSTEM_MANAGED_STATE_DECOMMISSIONED = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Annotations is an unstructured key-value map stored with a
    resource that may be set by external tools to store and retrieve arbitrary
    metadata. They are not queryable and should be preserved when modifying
    objects. More info: https://kubernetes.io/docs/user-guide/annotations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels on the resource, which can be used for
    categorization. similar to Kubernetes resource labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  blueprint = _messages.MessageField('Blueprint', 2)
  conditions = _messages.MessageField('UnitCondition', 3, repeated=True)
  createTime = _messages.StringField(4)
  dependencies = _messages.MessageField('UnitDependency', 5, repeated=True)
  dependents = _messages.MessageField('UnitDependency', 6, repeated=True)
  etag = _messages.StringField(7)
  flagRevisions = _messages.StringField(8, repeated=True)
  inputVariables = _messages.MessageField('UnitVariable', 9, repeated=True)
  labels = _messages.MessageField('LabelsValue', 10)
  maintenance = _messages.MessageField('MaintenanceSettings', 11)
  managementMode = _messages.EnumField('ManagementModeValueValuesEnum', 12)
  name = _messages.StringField(13)
  nextMaintenance = _messages.MessageField('UnitMaintenanceSchedule', 14)
  ongoingOperations = _messages.StringField(15, repeated=True)
  operationMode = _messages.EnumField('OperationModeValueValuesEnum', 16)
  outputVariables = _messages.MessageField('UnitVariable', 17, repeated=True)
  pendingOperations = _messages.StringField(18, repeated=True)
  release = _messages.StringField(19)
  scheduledOperations = _messages.StringField(20, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 21)
  systemCleanupAt = _messages.StringField(22)
  systemManagedState = _messages.EnumField('SystemManagedStateValueValuesEnum', 23)
  targetInputVariables = _messages.MessageField('UnitVariable', 24, repeated=True)
  targetRelease = _messages.StringField(25)
  tenant = _messages.StringField(26)
  uid = _messages.StringField(27)
  unitKind = _messages.StringField(28)
  updateTime = _messages.StringField(29)


class UnitCondition(_messages.Message):
  r"""UnitCondition describes the status of an Unit. UnitCondition is
  individual components that contribute to an overall state.

  Enums:
    StatusValueValuesEnum: Required. Status of the condition.
    TypeValueValuesEnum: Required. Type of the condition.

  Fields:
    lastTransitionTime: Required. Last time the condition transited from one
      status to another.
    message: Required. Human readable message indicating details about the
      last transition.
    reason: Required. Brief reason for the condition's last transition.
    status: Required. Status of the condition.
    type: Required. Type of the condition.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""Required. Status of the condition.

    Values:
      STATUS_UNSPECIFIED: Condition status is unspecified.
      STATUS_UNKNOWN: Condition is unknown.
      STATUS_TRUE: Condition is true.
      STATUS_FALSE: Condition is false.
    """
    STATUS_UNSPECIFIED = 0
    STATUS_UNKNOWN = 1
    STATUS_TRUE = 2
    STATUS_FALSE = 3

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. Type of the condition.

    Values:
      TYPE_UNSPECIFIED: Condition type is unspecified.
      TYPE_READY: Condition type is ready.
      TYPE_UPDATING: Condition type is updating.
      TYPE_PROVISIONED: Condition type is provisioned.
      TYPE_OPERATION_ERROR: Condition type is operationError. True when the
        last unit operation fails with a non-ignorable error.
    """
    TYPE_UNSPECIFIED = 0
    TYPE_READY = 1
    TYPE_UPDATING = 2
    TYPE_PROVISIONED = 3
    TYPE_OPERATION_ERROR = 4

  lastTransitionTime = _messages.StringField(1)
  message = _messages.StringField(2)
  reason = _messages.StringField(3)
  status = _messages.EnumField('StatusValueValuesEnum', 4)
  type = _messages.EnumField('TypeValueValuesEnum', 5)


class UnitDependency(_messages.Message):
  r"""Set of dependencies for this unit. Maximum 10.

  Fields:
    alias: Output only. Alias for the name of the dependency.
    unit: Output only. A reference to the Unit object.
  """

  alias = _messages.StringField(1)
  unit = _messages.StringField(2)


class UnitKind(_messages.Message):
  r"""Definition of a Unit. Units belonging to the same UnitKind are managed
  together; for example they follow the same release model (blueprints,
  versions etc.) and are typically rolled out together.

  Messages:
    AnnotationsValue: Optional. Annotations is an unstructured key-value map
      stored with a resource that may be set by external tools to store and
      retrieve arbitrary metadata. They are not queryable and should be
      preserved when modifying objects. More info:
      https://kubernetes.io/docs/user-guide/annotations
    LabelsValue: Optional. The labels on the resource, which can be used for
      categorization. similar to Kubernetes resource labels.

  Fields:
    annotations: Optional. Annotations is an unstructured key-value map stored
      with a resource that may be set by external tools to store and retrieve
      arbitrary metadata. They are not queryable and should be preserved when
      modifying objects. More info: https://kubernetes.io/docs/user-
      guide/annotations
    createTime: Output only. The timestamp when the resource was created.
    defaultFlagRevisions: Optional. Default revisions of flags for this
      UnitKind. Newly created units will use the flag default_flag_revisions
      present at the time of creation.
    defaultRelease: Optional. A reference to the Release object to use as
      default for creating new units of this UnitKind (optional). If not
      specified, a new unit must explicitly reference which release to use for
      its creation.
    dependencies: Optional. Immutable. List of other unit kinds that this
      release will depend on. Dependencies will be automatically provisioned
      if not found. Maximum 10.
    etag: Output only. An opaque value that uniquely identifies a version or
      generation of a resource. It can be used to confirm that the client and
      server agree on the ordering of a resource being written.
    inputVariableMappings: Optional. List of inputVariables for this release
      that will either be retrieved from a dependency's outputVariables, or
      will be passed on to a dependency's inputVariables. Maximum 100.
    labels: Optional. The labels on the resource, which can be used for
      categorization. similar to Kubernetes resource labels.
    name: Identifier. The resource name (full URI of the resource) following
      the standard naming scheme:
      "projects/{project}/locations/{location}/unitKinds/{unitKind}"
    outputVariableMappings: Optional. List of outputVariables for this unit
      kind will be passed to this unit's outputVariables. Maximum 100.
    saas: Required. Immutable. A reference to the Saas that defines the
      product (managed service) that the producer wants to manage with
      EasySaaS. Part of the EasySaaS common data model. Immutable once set.
    saasType: Optional. A reference to the SaasType that defines the product
      (managed service) that the producer wants to manage with EasySaaS. Part
      of the EasySaaS common data model. Immutable once set.
    uid: Output only. The unique identifier of the resource. UID is unique in
      the time and space for this resource within the scope of the service. It
      is typically generated by the server on successful creation of a
      resource and must not be changed. UID is used to uniquely identify
      resources with resource name reuses. This should be a UUID4.
    updateTime: Output only. The timestamp when the resource was last updated.
      Any change to the resource made by users must refresh this value.
      Changes to a resource made by the service should refresh this value.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Annotations is an unstructured key-value map stored with a
    resource that may be set by external tools to store and retrieve arbitrary
    metadata. They are not queryable and should be preserved when modifying
    objects. More info: https://kubernetes.io/docs/user-guide/annotations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels on the resource, which can be used for
    categorization. similar to Kubernetes resource labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  defaultFlagRevisions = _messages.StringField(3, repeated=True)
  defaultRelease = _messages.StringField(4)
  dependencies = _messages.MessageField('Dependency', 5, repeated=True)
  etag = _messages.StringField(6)
  inputVariableMappings = _messages.MessageField('VariableMapping', 7, repeated=True)
  labels = _messages.MessageField('LabelsValue', 8)
  name = _messages.StringField(9)
  outputVariableMappings = _messages.MessageField('VariableMapping', 10, repeated=True)
  saas = _messages.StringField(11)
  saasType = _messages.StringField(12)
  uid = _messages.StringField(13)
  updateTime = _messages.StringField(14)


class UnitMaintenanceSchedule(_messages.Message):
  r"""A UnitMaintenanceSchedule object.

  Fields:
    endTime: Output only. End of scheduled maintenance window.
    startTime: Output only. Start of scheduled maintenance window.
  """

  endTime = _messages.StringField(1)
  startTime = _messages.StringField(2)


class UnitOperation(_messages.Message):
  r"""UnitOperation encapsulates the intent of changing/interacting with the
  service component represented by the specific Unit. Multiple UnitOperations
  can be created (requested) and scheduled in the future, however only one
  will be allowed to execute at a time (that can change in the future for non-
  mutating operations). UnitOperations allow different actors interacting with
  the same unit to focus only on the change they have requested. This is a
  base object that contains the common fields in all unit operations.

  Enums:
    ErrorCategoryValueValuesEnum: Optional. Output only.
      UnitOperationErrorCategory describe the error category.
    StateValueValuesEnum: Optional. Output only. UnitOperationState describes
      the current state of the unit operation.

  Messages:
    AnnotationsValue: Optional. Annotations is an unstructured key-value map
      stored with a resource that may be set by external tools to store and
      retrieve arbitrary metadata. They are not queryable and should be
      preserved when modifying objects. More info:
      https://kubernetes.io/docs/user-guide/annotations
    LabelsValue: Optional. The labels on the resource, which can be used for
      categorization. similar to Kubernetes resource labels.

  Fields:
    annotations: Optional. Annotations is an unstructured key-value map stored
      with a resource that may be set by external tools to store and retrieve
      arbitrary metadata. They are not queryable and should be preserved when
      modifying objects. More info: https://kubernetes.io/docs/user-
      guide/annotations
    cancel: Optional. When true, attempt to cancel the operation. Cancellation
      may fail if the operation is already executing. (Optional)
    conditions: Optional. Output only. A set of conditions which indicate the
      various conditions this resource can have.
    createTime: Output only. The timestamp when the resource was created.
    deprovision: A Deprovision attribute.
    engineState: Optional. Output only. The engine state for on-going
      deployment engine operation(s). This field is opaque for external usage.
    errorCategory: Optional. Output only. UnitOperationErrorCategory describe
      the error category.
    etag: Output only. An opaque value that uniquely identifies a version or
      generation of a resource. It can be used to confirm that the client and
      server agree on the ordering of a resource being written.
    flagUpdate: A FlagUpdate attribute.
    labels: Optional. The labels on the resource, which can be used for
      categorization. similar to Kubernetes resource labels.
    name: Identifier. The resource name (full URI of the resource) following
      the standard naming scheme:
      "projects/{project}/locations/{location}/unitOperations/{unitOperation}"
    parentUnitOperation: Optional. Reference to parent resource:
      UnitOperation. If an operation needs to create other operations as part
      of its workflow, each of the child operations should have this field set
      to the parent. This can be used for tracing. (Optional)
    provision: A Provision attribute.
    rollout: Optional. Specifies which rollout created this Unit Operation.
      This cannot be modified and is used for filtering purposes only. If a
      dependent unit and unit operation are created as part of another unit
      operation, they will use the same rolloutId.
    schedule: Optional. When to schedule this operation.
    state: Optional. Output only. UnitOperationState describes the current
      state of the unit operation.
    uid: Output only. The unique identifier of the resource. UID is unique in
      the time and space for this resource within the scope of the service. It
      is typically generated by the server on successful creation of a
      resource and must not be changed. UID is used to uniquely identify
      resources with resource name reuses. This should be a UUID4.
    unit: Required. Immutable. The Unit a given UnitOperation will act upon.
    updateTime: Output only. The timestamp when the resource was last updated.
      Any change to the resource made by users must refresh this value.
      Changes to a resource made by the service should refresh this value.
    upgrade: A Upgrade attribute.
  """

  class ErrorCategoryValueValuesEnum(_messages.Enum):
    r"""Optional. Output only. UnitOperationErrorCategory describe the error
    category.

    Values:
      UNIT_OPERATION_ERROR_CATEGORY_UNSPECIFIED: Unit operation error category
        is unspecified
      NOT_APPLICABLE: Unit operation error category is not applicable, or it
        is not an error
      FATAL: Unit operation error category is fatal
      RETRIABLE: Unit operation error category is retriable
      IGNORABLE: Unit operation error category is ignorable
      STANDARD: Unit operation error category is standard, counts towards
        Rollout error budget
    """
    UNIT_OPERATION_ERROR_CATEGORY_UNSPECIFIED = 0
    NOT_APPLICABLE = 1
    FATAL = 2
    RETRIABLE = 3
    IGNORABLE = 4
    STANDARD = 5

  class StateValueValuesEnum(_messages.Enum):
    r"""Optional. Output only. UnitOperationState describes the current state
    of the unit operation.

    Values:
      UNIT_OPERATION_STATE_UNKNOWN: <no description>
      UNIT_OPERATION_STATE_PENDING: Unit operation is accepted but not ready
        to run.
      UNIT_OPERATION_STATE_SCHEDULED: Unit operation is accepted and
        scheduled.
      UNIT_OPERATION_STATE_RUNNING: Unit operation is running.
      UNIT_OPERATION_STATE_SUCCEEDED: Unit operation has completed
        successfully.
      UNIT_OPERATION_STATE_FAILED: Unit operation has failed.
      UNIT_OPERATION_STATE_CANCELLED: Unit operation was cancelled.
    """
    UNIT_OPERATION_STATE_UNKNOWN = 0
    UNIT_OPERATION_STATE_PENDING = 1
    UNIT_OPERATION_STATE_SCHEDULED = 2
    UNIT_OPERATION_STATE_RUNNING = 3
    UNIT_OPERATION_STATE_SUCCEEDED = 4
    UNIT_OPERATION_STATE_FAILED = 5
    UNIT_OPERATION_STATE_CANCELLED = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Annotations is an unstructured key-value map stored with a
    resource that may be set by external tools to store and retrieve arbitrary
    metadata. They are not queryable and should be preserved when modifying
    objects. More info: https://kubernetes.io/docs/user-guide/annotations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels on the resource, which can be used for
    categorization. similar to Kubernetes resource labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  cancel = _messages.BooleanField(2)
  conditions = _messages.MessageField('UnitOperationCondition', 3, repeated=True)
  createTime = _messages.StringField(4)
  deprovision = _messages.MessageField('Deprovision', 5)
  engineState = _messages.StringField(6)
  errorCategory = _messages.EnumField('ErrorCategoryValueValuesEnum', 7)
  etag = _messages.StringField(8)
  flagUpdate = _messages.MessageField('FlagUpdate', 9)
  labels = _messages.MessageField('LabelsValue', 10)
  name = _messages.StringField(11)
  parentUnitOperation = _messages.StringField(12)
  provision = _messages.MessageField('Provision', 13)
  rollout = _messages.StringField(14)
  schedule = _messages.MessageField('Schedule', 15)
  state = _messages.EnumField('StateValueValuesEnum', 16)
  uid = _messages.StringField(17)
  unit = _messages.StringField(18)
  updateTime = _messages.StringField(19)
  upgrade = _messages.MessageField('Upgrade', 20)


class UnitOperationCondition(_messages.Message):
  r"""UnitOperationCondition describes the status of an Unit Operation.
  UnitOperationCondition is individual components that contribute to an
  overall state.

  Enums:
    StatusValueValuesEnum: Required. Status of the condition.
    TypeValueValuesEnum: Required. Type of the condition.

  Fields:
    lastTransitionTime: Required. Last time the condition transited from one
      status to another.
    message: Required. Human readable message indicating details about the
      last transition.
    reason: Required. Brief reason for the condition's last transition.
    status: Required. Status of the condition.
    type: Required. Type of the condition.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""Required. Status of the condition.

    Values:
      STATUS_UNSPECIFIED: Condition status is unspecified.
      STATUS_UNKNOWN: Condition is unknown.
      STATUS_TRUE: Condition is true.
      STATUS_FALSE: Condition is false.
    """
    STATUS_UNSPECIFIED = 0
    STATUS_UNKNOWN = 1
    STATUS_TRUE = 2
    STATUS_FALSE = 3

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. Type of the condition.

    Values:
      TYPE_UNSPECIFIED: Condition type is unspecified.
      TYPE_SCHEDULED: Condition type is scheduled.
      TYPE_RUNNING: Condition type is running.
      TYPE_SUCCEEDED: Condition type is succeeded.
      TYPE_CANCELLED: Condition type is cancelled.
    """
    TYPE_UNSPECIFIED = 0
    TYPE_SCHEDULED = 1
    TYPE_RUNNING = 2
    TYPE_SUCCEEDED = 3
    TYPE_CANCELLED = 4

  lastTransitionTime = _messages.StringField(1)
  message = _messages.StringField(2)
  reason = _messages.StringField(3)
  status = _messages.EnumField('StatusValueValuesEnum', 4)
  type = _messages.EnumField('TypeValueValuesEnum', 5)


class UnitVariable(_messages.Message):
  r"""UnitVariable describes a parameter for a Unit.

  Enums:
    TypeValueValuesEnum: Optional. Immutable. Name of a supported variable
      type. Supported types are string, int, bool.

  Fields:
    type: Optional. Immutable. Name of a supported variable type. Supported
      types are string, int, bool.
    value: Optional. String encoded value for the variable.
    variable: Required. Immutable. Name of the variable from actuation
      configs.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Optional. Immutable. Name of a supported variable type. Supported
    types are string, int, bool.

    Values:
      TYPE_UNSPECIFIED: Variable type is unspecified.
      STRING: Variable type is string.
      INT: Variable type is int.
      BOOL: Variable type is bool.
    """
    TYPE_UNSPECIFIED = 0
    STRING = 1
    INT = 2
    BOOL = 3

  type = _messages.EnumField('TypeValueValuesEnum', 1)
  value = _messages.StringField(2)
  variable = _messages.StringField(3)


class Upgrade(_messages.Message):
  r"""Upgrade is the unit operation that upgrades a provisioned unit, which
  may also include the underlying resources represented by a Unit. Can only
  execute if the Unit is currently provisioned.

  Fields:
    inputVariables: Optional. Set of input variables. Maximum 100. (optional)
    package: Optional. URI to a blueprint used by the Unit (optional).
    release: Optional. Reference to the Release object to use for the Unit.
      (optional).
  """

  inputVariables = _messages.MessageField('UnitVariable', 1, repeated=True)
  package = _messages.StringField(2)
  release = _messages.StringField(3)


class VariableMapping(_messages.Message):
  r"""Mapping of input variables to their respective output variable for
  depedenencies

  Fields:
    from_: Optional. Output variables which will get their values from
      dependencies
    to: Optional. Input variables whose values will be passed on to
      dependencies.
    variable: Required. name of the variable
  """

  from_ = _messages.MessageField('FromMapping', 1)
  to = _messages.MessageField('ToMapping', 2)
  variable = _messages.StringField(3)


class Variant(_messages.Message):
  r"""Variant is an identifier for a value (name assigned to a value).

  Fields:
    boolValue: A boolean attribute.
    intValue: A string attribute.
    name: Required. Name of the variant. Max length: 128 bytes.
    stringValue: A string attribute.
  """

  boolValue = _messages.BooleanField(1)
  intValue = _messages.IntegerField(2)
  name = _messages.StringField(3)
  stringValue = _messages.StringField(4)


encoding.AddCustomJsonFieldMapping(
    VariableMapping, 'from_', 'from')
encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
