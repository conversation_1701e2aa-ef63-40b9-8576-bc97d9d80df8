"""Generated client library for saasservicemgmt version v1beta1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.saasservicemgmt.v1beta1 import saasservicemgmt_v1beta1_messages as messages


class SaasservicemgmtV1beta1(base_api.BaseApiClient):
  """Generated client library for service saasservicemgmt version v1beta1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://saasservicemgmt.googleapis.com/'
  MTLS_BASE_URL = 'https://saasservicemgmt.mtls.googleapis.com/'

  _PACKAGE = 'saasservicemgmt'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1beta1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'SaasservicemgmtV1beta1'
  _URL_VERSION = 'v1beta1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new saasservicemgmt handle."""
    url = url or self.BASE_URL
    super(SaasservicemgmtV1beta1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_flagReleases = self.ProjectsLocationsFlagReleasesService(self)
    self.projects_locations_flagRevisions = self.ProjectsLocationsFlagRevisionsService(self)
    self.projects_locations_flags = self.ProjectsLocationsFlagsService(self)
    self.projects_locations_releases = self.ProjectsLocationsReleasesService(self)
    self.projects_locations_rolloutKinds = self.ProjectsLocationsRolloutKindsService(self)
    self.projects_locations_rollouts = self.ProjectsLocationsRolloutsService(self)
    self.projects_locations_saas = self.ProjectsLocationsSaasService(self)
    self.projects_locations_tenants = self.ProjectsLocationsTenantsService(self)
    self.projects_locations_unitKinds = self.ProjectsLocationsUnitKindsService(self)
    self.projects_locations_unitOperations = self.ProjectsLocationsUnitOperationsService(self)
    self.projects_locations_units = self.ProjectsLocationsUnitsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsFlagReleasesService(base_api.BaseApiService):
    """Service class for the projects_locations_flagReleases resource."""

    _NAME = 'projects_locations_flagReleases'

    def __init__(self, client):
      super(SaasservicemgmtV1beta1.ProjectsLocationsFlagReleasesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new flag release.

      Args:
        request: (SaasservicemgmtProjectsLocationsFlagReleasesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FlagRelease) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/flagReleases',
        http_method='POST',
        method_id='saasservicemgmt.projects.locations.flagReleases.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['flagReleaseId', 'requestId', 'validateOnly'],
        relative_path='v1beta1/{+parent}/flagReleases',
        request_field='flagRelease',
        request_type_name='SaasservicemgmtProjectsLocationsFlagReleasesCreateRequest',
        response_type_name='FlagRelease',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a single flag release.

      Args:
        request: (SaasservicemgmtProjectsLocationsFlagReleasesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/flagReleases/{flagReleasesId}',
        http_method='DELETE',
        method_id='saasservicemgmt.projects.locations.flagReleases.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsFlagReleasesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieve a single flag release.

      Args:
        request: (SaasservicemgmtProjectsLocationsFlagReleasesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FlagRelease) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/flagReleases/{flagReleasesId}',
        http_method='GET',
        method_id='saasservicemgmt.projects.locations.flagReleases.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsFlagReleasesGetRequest',
        response_type_name='FlagRelease',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Retrieve a collection of flag releases.

      Args:
        request: (SaasservicemgmtProjectsLocationsFlagReleasesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFlagReleasesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/flagReleases',
        http_method='GET',
        method_id='saasservicemgmt.projects.locations.flagReleases.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/flagReleases',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsFlagReleasesListRequest',
        response_type_name='ListFlagReleasesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a single flag release.

      Args:
        request: (SaasservicemgmtProjectsLocationsFlagReleasesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FlagRelease) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/flagReleases/{flagReleasesId}',
        http_method='PATCH',
        method_id='saasservicemgmt.projects.locations.flagReleases.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='flagRelease',
        request_type_name='SaasservicemgmtProjectsLocationsFlagReleasesPatchRequest',
        response_type_name='FlagRelease',
        supports_download=False,
    )

  class ProjectsLocationsFlagRevisionsService(base_api.BaseApiService):
    """Service class for the projects_locations_flagRevisions resource."""

    _NAME = 'projects_locations_flagRevisions'

    def __init__(self, client):
      super(SaasservicemgmtV1beta1.ProjectsLocationsFlagRevisionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new flag revision.

      Args:
        request: (SaasservicemgmtProjectsLocationsFlagRevisionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FlagRevision) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/flagRevisions',
        http_method='POST',
        method_id='saasservicemgmt.projects.locations.flagRevisions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['flagRevisionId', 'requestId', 'validateOnly'],
        relative_path='v1beta1/{+parent}/flagRevisions',
        request_field='flagRevision',
        request_type_name='SaasservicemgmtProjectsLocationsFlagRevisionsCreateRequest',
        response_type_name='FlagRevision',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a single flag revision.

      Args:
        request: (SaasservicemgmtProjectsLocationsFlagRevisionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/flagRevisions/{flagRevisionsId}',
        http_method='DELETE',
        method_id='saasservicemgmt.projects.locations.flagRevisions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsFlagRevisionsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieve a single flag revision.

      Args:
        request: (SaasservicemgmtProjectsLocationsFlagRevisionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FlagRevision) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/flagRevisions/{flagRevisionsId}',
        http_method='GET',
        method_id='saasservicemgmt.projects.locations.flagRevisions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsFlagRevisionsGetRequest',
        response_type_name='FlagRevision',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Retrieve a collection of flag revisions.

      Args:
        request: (SaasservicemgmtProjectsLocationsFlagRevisionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFlagRevisionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/flagRevisions',
        http_method='GET',
        method_id='saasservicemgmt.projects.locations.flagRevisions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/flagRevisions',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsFlagRevisionsListRequest',
        response_type_name='ListFlagRevisionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a single flag revision.

      Args:
        request: (SaasservicemgmtProjectsLocationsFlagRevisionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FlagRevision) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/flagRevisions/{flagRevisionsId}',
        http_method='PATCH',
        method_id='saasservicemgmt.projects.locations.flagRevisions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='flagRevision',
        request_type_name='SaasservicemgmtProjectsLocationsFlagRevisionsPatchRequest',
        response_type_name='FlagRevision',
        supports_download=False,
    )

  class ProjectsLocationsFlagsService(base_api.BaseApiService):
    """Service class for the projects_locations_flags resource."""

    _NAME = 'projects_locations_flags'

    def __init__(self, client):
      super(SaasservicemgmtV1beta1.ProjectsLocationsFlagsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new flag.

      Args:
        request: (SaasservicemgmtProjectsLocationsFlagsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Flag) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/flags',
        http_method='POST',
        method_id='saasservicemgmt.projects.locations.flags.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['flagId', 'requestId', 'validateOnly'],
        relative_path='v1beta1/{+parent}/flags',
        request_field='flag',
        request_type_name='SaasservicemgmtProjectsLocationsFlagsCreateRequest',
        response_type_name='Flag',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a single flag.

      Args:
        request: (SaasservicemgmtProjectsLocationsFlagsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/flags/{flagsId}',
        http_method='DELETE',
        method_id='saasservicemgmt.projects.locations.flags.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsFlagsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieve a single flag.

      Args:
        request: (SaasservicemgmtProjectsLocationsFlagsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Flag) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/flags/{flagsId}',
        http_method='GET',
        method_id='saasservicemgmt.projects.locations.flags.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsFlagsGetRequest',
        response_type_name='Flag',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Retrieve a collection of flags.

      Args:
        request: (SaasservicemgmtProjectsLocationsFlagsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFlagsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/flags',
        http_method='GET',
        method_id='saasservicemgmt.projects.locations.flags.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/flags',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsFlagsListRequest',
        response_type_name='ListFlagsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a single flag.

      Args:
        request: (SaasservicemgmtProjectsLocationsFlagsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Flag) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/flags/{flagsId}',
        http_method='PATCH',
        method_id='saasservicemgmt.projects.locations.flags.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='flag',
        request_type_name='SaasservicemgmtProjectsLocationsFlagsPatchRequest',
        response_type_name='Flag',
        supports_download=False,
    )

  class ProjectsLocationsReleasesService(base_api.BaseApiService):
    """Service class for the projects_locations_releases resource."""

    _NAME = 'projects_locations_releases'

    def __init__(self, client):
      super(SaasservicemgmtV1beta1.ProjectsLocationsReleasesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new release.

      Args:
        request: (SaasservicemgmtProjectsLocationsReleasesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Release) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/releases',
        http_method='POST',
        method_id='saasservicemgmt.projects.locations.releases.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['releaseId', 'requestId', 'validateOnly'],
        relative_path='v1beta1/{+parent}/releases',
        request_field='release',
        request_type_name='SaasservicemgmtProjectsLocationsReleasesCreateRequest',
        response_type_name='Release',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a single release.

      Args:
        request: (SaasservicemgmtProjectsLocationsReleasesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/releases/{releasesId}',
        http_method='DELETE',
        method_id='saasservicemgmt.projects.locations.releases.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsReleasesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieve a single release.

      Args:
        request: (SaasservicemgmtProjectsLocationsReleasesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Release) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/releases/{releasesId}',
        http_method='GET',
        method_id='saasservicemgmt.projects.locations.releases.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsReleasesGetRequest',
        response_type_name='Release',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Retrieve a collection of releases.

      Args:
        request: (SaasservicemgmtProjectsLocationsReleasesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListReleasesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/releases',
        http_method='GET',
        method_id='saasservicemgmt.projects.locations.releases.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/releases',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsReleasesListRequest',
        response_type_name='ListReleasesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a single release.

      Args:
        request: (SaasservicemgmtProjectsLocationsReleasesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Release) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/releases/{releasesId}',
        http_method='PATCH',
        method_id='saasservicemgmt.projects.locations.releases.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='release',
        request_type_name='SaasservicemgmtProjectsLocationsReleasesPatchRequest',
        response_type_name='Release',
        supports_download=False,
    )

  class ProjectsLocationsRolloutKindsService(base_api.BaseApiService):
    """Service class for the projects_locations_rolloutKinds resource."""

    _NAME = 'projects_locations_rolloutKinds'

    def __init__(self, client):
      super(SaasservicemgmtV1beta1.ProjectsLocationsRolloutKindsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new rollout kind.

      Args:
        request: (SaasservicemgmtProjectsLocationsRolloutKindsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RolloutKind) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/rolloutKinds',
        http_method='POST',
        method_id='saasservicemgmt.projects.locations.rolloutKinds.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'rolloutKindId', 'validateOnly'],
        relative_path='v1beta1/{+parent}/rolloutKinds',
        request_field='rolloutKind',
        request_type_name='SaasservicemgmtProjectsLocationsRolloutKindsCreateRequest',
        response_type_name='RolloutKind',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a single rollout kind.

      Args:
        request: (SaasservicemgmtProjectsLocationsRolloutKindsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/rolloutKinds/{rolloutKindsId}',
        http_method='DELETE',
        method_id='saasservicemgmt.projects.locations.rolloutKinds.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsRolloutKindsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieve a single rollout kind.

      Args:
        request: (SaasservicemgmtProjectsLocationsRolloutKindsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RolloutKind) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/rolloutKinds/{rolloutKindsId}',
        http_method='GET',
        method_id='saasservicemgmt.projects.locations.rolloutKinds.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsRolloutKindsGetRequest',
        response_type_name='RolloutKind',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Retrieve a collection of rollout kinds.

      Args:
        request: (SaasservicemgmtProjectsLocationsRolloutKindsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRolloutKindsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/rolloutKinds',
        http_method='GET',
        method_id='saasservicemgmt.projects.locations.rolloutKinds.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/rolloutKinds',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsRolloutKindsListRequest',
        response_type_name='ListRolloutKindsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a single rollout kind.

      Args:
        request: (SaasservicemgmtProjectsLocationsRolloutKindsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RolloutKind) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/rolloutKinds/{rolloutKindsId}',
        http_method='PATCH',
        method_id='saasservicemgmt.projects.locations.rolloutKinds.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='rolloutKind',
        request_type_name='SaasservicemgmtProjectsLocationsRolloutKindsPatchRequest',
        response_type_name='RolloutKind',
        supports_download=False,
    )

  class ProjectsLocationsRolloutsService(base_api.BaseApiService):
    """Service class for the projects_locations_rollouts resource."""

    _NAME = 'projects_locations_rollouts'

    def __init__(self, client):
      super(SaasservicemgmtV1beta1.ProjectsLocationsRolloutsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new rollout.

      Args:
        request: (SaasservicemgmtProjectsLocationsRolloutsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Rollout) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/rollouts',
        http_method='POST',
        method_id='saasservicemgmt.projects.locations.rollouts.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'rolloutId', 'validateOnly'],
        relative_path='v1beta1/{+parent}/rollouts',
        request_field='rollout',
        request_type_name='SaasservicemgmtProjectsLocationsRolloutsCreateRequest',
        response_type_name='Rollout',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a single rollout.

      Args:
        request: (SaasservicemgmtProjectsLocationsRolloutsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/rollouts/{rolloutsId}',
        http_method='DELETE',
        method_id='saasservicemgmt.projects.locations.rollouts.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsRolloutsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieve a single rollout.

      Args:
        request: (SaasservicemgmtProjectsLocationsRolloutsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Rollout) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/rollouts/{rolloutsId}',
        http_method='GET',
        method_id='saasservicemgmt.projects.locations.rollouts.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsRolloutsGetRequest',
        response_type_name='Rollout',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Retrieve a collection of rollouts.

      Args:
        request: (SaasservicemgmtProjectsLocationsRolloutsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRolloutsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/rollouts',
        http_method='GET',
        method_id='saasservicemgmt.projects.locations.rollouts.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/rollouts',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsRolloutsListRequest',
        response_type_name='ListRolloutsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a single rollout.

      Args:
        request: (SaasservicemgmtProjectsLocationsRolloutsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Rollout) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/rollouts/{rolloutsId}',
        http_method='PATCH',
        method_id='saasservicemgmt.projects.locations.rollouts.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='rollout',
        request_type_name='SaasservicemgmtProjectsLocationsRolloutsPatchRequest',
        response_type_name='Rollout',
        supports_download=False,
    )

  class ProjectsLocationsSaasService(base_api.BaseApiService):
    """Service class for the projects_locations_saas resource."""

    _NAME = 'projects_locations_saas'

    def __init__(self, client):
      super(SaasservicemgmtV1beta1.ProjectsLocationsSaasService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new saas.

      Args:
        request: (SaasservicemgmtProjectsLocationsSaasCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Saas) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/saas',
        http_method='POST',
        method_id='saasservicemgmt.projects.locations.saas.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'saasId', 'validateOnly'],
        relative_path='v1beta1/{+parent}/saas',
        request_field='saas',
        request_type_name='SaasservicemgmtProjectsLocationsSaasCreateRequest',
        response_type_name='Saas',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a single saas.

      Args:
        request: (SaasservicemgmtProjectsLocationsSaasDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/saas/{saasId}',
        http_method='DELETE',
        method_id='saasservicemgmt.projects.locations.saas.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsSaasDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieve a single saas.

      Args:
        request: (SaasservicemgmtProjectsLocationsSaasGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Saas) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/saas/{saasId}',
        http_method='GET',
        method_id='saasservicemgmt.projects.locations.saas.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsSaasGetRequest',
        response_type_name='Saas',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Retrieve a collection of saas.

      Args:
        request: (SaasservicemgmtProjectsLocationsSaasListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSaasResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/saas',
        http_method='GET',
        method_id='saasservicemgmt.projects.locations.saas.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/saas',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsSaasListRequest',
        response_type_name='ListSaasResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a single saas.

      Args:
        request: (SaasservicemgmtProjectsLocationsSaasPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Saas) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/saas/{saasId}',
        http_method='PATCH',
        method_id='saasservicemgmt.projects.locations.saas.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='saas',
        request_type_name='SaasservicemgmtProjectsLocationsSaasPatchRequest',
        response_type_name='Saas',
        supports_download=False,
    )

  class ProjectsLocationsTenantsService(base_api.BaseApiService):
    """Service class for the projects_locations_tenants resource."""

    _NAME = 'projects_locations_tenants'

    def __init__(self, client):
      super(SaasservicemgmtV1beta1.ProjectsLocationsTenantsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new tenant.

      Args:
        request: (SaasservicemgmtProjectsLocationsTenantsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Tenant) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/tenants',
        http_method='POST',
        method_id='saasservicemgmt.projects.locations.tenants.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'tenantId', 'validateOnly'],
        relative_path='v1beta1/{+parent}/tenants',
        request_field='tenant',
        request_type_name='SaasservicemgmtProjectsLocationsTenantsCreateRequest',
        response_type_name='Tenant',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a single tenant.

      Args:
        request: (SaasservicemgmtProjectsLocationsTenantsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/tenants/{tenantsId}',
        http_method='DELETE',
        method_id='saasservicemgmt.projects.locations.tenants.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsTenantsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieve a single tenant.

      Args:
        request: (SaasservicemgmtProjectsLocationsTenantsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Tenant) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/tenants/{tenantsId}',
        http_method='GET',
        method_id='saasservicemgmt.projects.locations.tenants.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsTenantsGetRequest',
        response_type_name='Tenant',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Retrieve a collection of tenants.

      Args:
        request: (SaasservicemgmtProjectsLocationsTenantsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListTenantsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/tenants',
        http_method='GET',
        method_id='saasservicemgmt.projects.locations.tenants.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/tenants',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsTenantsListRequest',
        response_type_name='ListTenantsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a single tenant.

      Args:
        request: (SaasservicemgmtProjectsLocationsTenantsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Tenant) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/tenants/{tenantsId}',
        http_method='PATCH',
        method_id='saasservicemgmt.projects.locations.tenants.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='tenant',
        request_type_name='SaasservicemgmtProjectsLocationsTenantsPatchRequest',
        response_type_name='Tenant',
        supports_download=False,
    )

  class ProjectsLocationsUnitKindsService(base_api.BaseApiService):
    """Service class for the projects_locations_unitKinds resource."""

    _NAME = 'projects_locations_unitKinds'

    def __init__(self, client):
      super(SaasservicemgmtV1beta1.ProjectsLocationsUnitKindsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new unit kind.

      Args:
        request: (SaasservicemgmtProjectsLocationsUnitKindsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (UnitKind) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/unitKinds',
        http_method='POST',
        method_id='saasservicemgmt.projects.locations.unitKinds.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'unitKindId', 'validateOnly'],
        relative_path='v1beta1/{+parent}/unitKinds',
        request_field='unitKind',
        request_type_name='SaasservicemgmtProjectsLocationsUnitKindsCreateRequest',
        response_type_name='UnitKind',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a single unit kind.

      Args:
        request: (SaasservicemgmtProjectsLocationsUnitKindsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/unitKinds/{unitKindsId}',
        http_method='DELETE',
        method_id='saasservicemgmt.projects.locations.unitKinds.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsUnitKindsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieve a single unit kind.

      Args:
        request: (SaasservicemgmtProjectsLocationsUnitKindsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (UnitKind) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/unitKinds/{unitKindsId}',
        http_method='GET',
        method_id='saasservicemgmt.projects.locations.unitKinds.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsUnitKindsGetRequest',
        response_type_name='UnitKind',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Retrieve a collection of unit kinds.

      Args:
        request: (SaasservicemgmtProjectsLocationsUnitKindsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListUnitKindsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/unitKinds',
        http_method='GET',
        method_id='saasservicemgmt.projects.locations.unitKinds.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/unitKinds',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsUnitKindsListRequest',
        response_type_name='ListUnitKindsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a single unit kind.

      Args:
        request: (SaasservicemgmtProjectsLocationsUnitKindsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (UnitKind) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/unitKinds/{unitKindsId}',
        http_method='PATCH',
        method_id='saasservicemgmt.projects.locations.unitKinds.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='unitKind',
        request_type_name='SaasservicemgmtProjectsLocationsUnitKindsPatchRequest',
        response_type_name='UnitKind',
        supports_download=False,
    )

  class ProjectsLocationsUnitOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_unitOperations resource."""

    _NAME = 'projects_locations_unitOperations'

    def __init__(self, client):
      super(SaasservicemgmtV1beta1.ProjectsLocationsUnitOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new unit operation.

      Args:
        request: (SaasservicemgmtProjectsLocationsUnitOperationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (UnitOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/unitOperations',
        http_method='POST',
        method_id='saasservicemgmt.projects.locations.unitOperations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'unitOperationId', 'validateOnly'],
        relative_path='v1beta1/{+parent}/unitOperations',
        request_field='unitOperation',
        request_type_name='SaasservicemgmtProjectsLocationsUnitOperationsCreateRequest',
        response_type_name='UnitOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a single unit operation.

      Args:
        request: (SaasservicemgmtProjectsLocationsUnitOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/unitOperations/{unitOperationsId}',
        http_method='DELETE',
        method_id='saasservicemgmt.projects.locations.unitOperations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsUnitOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieve a single unit operation.

      Args:
        request: (SaasservicemgmtProjectsLocationsUnitOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (UnitOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/unitOperations/{unitOperationsId}',
        http_method='GET',
        method_id='saasservicemgmt.projects.locations.unitOperations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsUnitOperationsGetRequest',
        response_type_name='UnitOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Retrieve a collection of unit operations.

      Args:
        request: (SaasservicemgmtProjectsLocationsUnitOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListUnitOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/unitOperations',
        http_method='GET',
        method_id='saasservicemgmt.projects.locations.unitOperations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/unitOperations',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsUnitOperationsListRequest',
        response_type_name='ListUnitOperationsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a single unit operation.

      Args:
        request: (SaasservicemgmtProjectsLocationsUnitOperationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (UnitOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/unitOperations/{unitOperationsId}',
        http_method='PATCH',
        method_id='saasservicemgmt.projects.locations.unitOperations.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='unitOperation',
        request_type_name='SaasservicemgmtProjectsLocationsUnitOperationsPatchRequest',
        response_type_name='UnitOperation',
        supports_download=False,
    )

  class ProjectsLocationsUnitsService(base_api.BaseApiService):
    """Service class for the projects_locations_units resource."""

    _NAME = 'projects_locations_units'

    def __init__(self, client):
      super(SaasservicemgmtV1beta1.ProjectsLocationsUnitsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new unit.

      Args:
        request: (SaasservicemgmtProjectsLocationsUnitsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Unit) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/units',
        http_method='POST',
        method_id='saasservicemgmt.projects.locations.units.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'unitId', 'validateOnly'],
        relative_path='v1beta1/{+parent}/units',
        request_field='unit',
        request_type_name='SaasservicemgmtProjectsLocationsUnitsCreateRequest',
        response_type_name='Unit',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a single unit.

      Args:
        request: (SaasservicemgmtProjectsLocationsUnitsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/units/{unitsId}',
        http_method='DELETE',
        method_id='saasservicemgmt.projects.locations.units.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsUnitsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieve a single unit.

      Args:
        request: (SaasservicemgmtProjectsLocationsUnitsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Unit) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/units/{unitsId}',
        http_method='GET',
        method_id='saasservicemgmt.projects.locations.units.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsUnitsGetRequest',
        response_type_name='Unit',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Retrieve a collection of units.

      Args:
        request: (SaasservicemgmtProjectsLocationsUnitsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListUnitsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/units',
        http_method='GET',
        method_id='saasservicemgmt.projects.locations.units.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/units',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsUnitsListRequest',
        response_type_name='ListUnitsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a single unit.

      Args:
        request: (SaasservicemgmtProjectsLocationsUnitsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Unit) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/units/{unitsId}',
        http_method='PATCH',
        method_id='saasservicemgmt.projects.locations.units.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='unit',
        request_type_name='SaasservicemgmtProjectsLocationsUnitsPatchRequest',
        response_type_name='Unit',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(SaasservicemgmtV1beta1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (SaasservicemgmtProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudLocationLocation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='saasservicemgmt.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsGetRequest',
        response_type_name='GoogleCloudLocationLocation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (SaasservicemgmtProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='saasservicemgmt.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+name}/locations',
        request_field='',
        request_type_name='SaasservicemgmtProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(SaasservicemgmtV1beta1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
