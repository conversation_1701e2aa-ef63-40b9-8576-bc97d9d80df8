"""Generated client library for securitycentermanagement version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.securitycentermanagement.v1 import securitycentermanagement_v1_messages as messages


class SecuritycentermanagementV1(base_api.BaseApiClient):
  """Generated client library for service securitycentermanagement version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://securitycentermanagement.googleapis.com/'
  MTLS_BASE_URL = 'https://securitycentermanagement.mtls.googleapis.com/'

  _PACKAGE = 'securitycentermanagement'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'SecuritycentermanagementV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new securitycentermanagement handle."""
    url = url or self.BASE_URL
    super(SecuritycentermanagementV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.folders_locations_effectiveEventThreatDetectionCustomModules = self.FoldersLocationsEffectiveEventThreatDetectionCustomModulesService(self)
    self.folders_locations_effectiveSecurityHealthAnalyticsCustomModules = self.FoldersLocationsEffectiveSecurityHealthAnalyticsCustomModulesService(self)
    self.folders_locations_eventThreatDetectionCustomModules = self.FoldersLocationsEventThreatDetectionCustomModulesService(self)
    self.folders_locations_securityCenterServices = self.FoldersLocationsSecurityCenterServicesService(self)
    self.folders_locations_securityHealthAnalyticsCustomModules = self.FoldersLocationsSecurityHealthAnalyticsCustomModulesService(self)
    self.folders_locations = self.FoldersLocationsService(self)
    self.folders = self.FoldersService(self)
    self.organizations_locations_effectiveEventThreatDetectionCustomModules = self.OrganizationsLocationsEffectiveEventThreatDetectionCustomModulesService(self)
    self.organizations_locations_effectiveSecurityHealthAnalyticsCustomModules = self.OrganizationsLocationsEffectiveSecurityHealthAnalyticsCustomModulesService(self)
    self.organizations_locations_eventThreatDetectionCustomModules = self.OrganizationsLocationsEventThreatDetectionCustomModulesService(self)
    self.organizations_locations_securityCenterServices = self.OrganizationsLocationsSecurityCenterServicesService(self)
    self.organizations_locations_securityHealthAnalyticsCustomModules = self.OrganizationsLocationsSecurityHealthAnalyticsCustomModulesService(self)
    self.organizations_locations = self.OrganizationsLocationsService(self)
    self.organizations = self.OrganizationsService(self)
    self.projects_locations_effectiveEventThreatDetectionCustomModules = self.ProjectsLocationsEffectiveEventThreatDetectionCustomModulesService(self)
    self.projects_locations_effectiveSecurityHealthAnalyticsCustomModules = self.ProjectsLocationsEffectiveSecurityHealthAnalyticsCustomModulesService(self)
    self.projects_locations_eventThreatDetectionCustomModules = self.ProjectsLocationsEventThreatDetectionCustomModulesService(self)
    self.projects_locations_securityCenterServices = self.ProjectsLocationsSecurityCenterServicesService(self)
    self.projects_locations_securityHealthAnalyticsCustomModules = self.ProjectsLocationsSecurityHealthAnalyticsCustomModulesService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class FoldersLocationsEffectiveEventThreatDetectionCustomModulesService(base_api.BaseApiService):
    """Service class for the folders_locations_effectiveEventThreatDetectionCustomModules resource."""

    _NAME = 'folders_locations_effectiveEventThreatDetectionCustomModules'

    def __init__(self, client):
      super(SecuritycentermanagementV1.FoldersLocationsEffectiveEventThreatDetectionCustomModulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the effective Event Threat Detection custom module at the given level. The difference between an EffectiveEventThreatDetectionCustomModule and an EventThreatDetectionCustomModule is that the fields for an `EffectiveEventThreatDetectionCustomModule` are computed from ancestors if needed. For example, the enablement state for an `EventThreatDetectionCustomModule` can be `ENABLED`, `DISABLED`, or `INHERITED`. In contrast, the enablement state for an `EffectiveEventThreatDetectionCustomModule` is always computed as `ENABLED` or `DISABLED`.

      Args:
        request: (SecuritycentermanagementFoldersLocationsEffectiveEventThreatDetectionCustomModulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EffectiveEventThreatDetectionCustomModule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/effectiveEventThreatDetectionCustomModules/{effectiveEventThreatDetectionCustomModulesId}',
        http_method='GET',
        method_id='securitycentermanagement.folders.locations.effectiveEventThreatDetectionCustomModules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycentermanagementFoldersLocationsEffectiveEventThreatDetectionCustomModulesGetRequest',
        response_type_name='EffectiveEventThreatDetectionCustomModule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all effective Event Threat Detection custom modules for the given parent. This includes resident modules defined at the scope of the parent along with modules inherited from its ancestors.

      Args:
        request: (SecuritycentermanagementFoldersLocationsEffectiveEventThreatDetectionCustomModulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEffectiveEventThreatDetectionCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/effectiveEventThreatDetectionCustomModules',
        http_method='GET',
        method_id='securitycentermanagement.folders.locations.effectiveEventThreatDetectionCustomModules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/effectiveEventThreatDetectionCustomModules',
        request_field='',
        request_type_name='SecuritycentermanagementFoldersLocationsEffectiveEventThreatDetectionCustomModulesListRequest',
        response_type_name='ListEffectiveEventThreatDetectionCustomModulesResponse',
        supports_download=False,
    )

  class FoldersLocationsEffectiveSecurityHealthAnalyticsCustomModulesService(base_api.BaseApiService):
    """Service class for the folders_locations_effectiveSecurityHealthAnalyticsCustomModules resource."""

    _NAME = 'folders_locations_effectiveSecurityHealthAnalyticsCustomModules'

    def __init__(self, client):
      super(SecuritycentermanagementV1.FoldersLocationsEffectiveSecurityHealthAnalyticsCustomModulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details of a single EffectiveSecurityHealthAnalyticsCustomModule.

      Args:
        request: (SecuritycentermanagementFoldersLocationsEffectiveSecurityHealthAnalyticsCustomModulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EffectiveSecurityHealthAnalyticsCustomModule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/effectiveSecurityHealthAnalyticsCustomModules/{effectiveSecurityHealthAnalyticsCustomModulesId}',
        http_method='GET',
        method_id='securitycentermanagement.folders.locations.effectiveSecurityHealthAnalyticsCustomModules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycentermanagementFoldersLocationsEffectiveSecurityHealthAnalyticsCustomModulesGetRequest',
        response_type_name='EffectiveSecurityHealthAnalyticsCustomModule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns a list of all EffectiveSecurityHealthAnalyticsCustomModule resources for the given parent. This includes resident modules defined at the scope of the parent, and inherited modules, inherited from ancestor organizations, folders, and projects (no descendants).

      Args:
        request: (SecuritycentermanagementFoldersLocationsEffectiveSecurityHealthAnalyticsCustomModulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEffectiveSecurityHealthAnalyticsCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/effectiveSecurityHealthAnalyticsCustomModules',
        http_method='GET',
        method_id='securitycentermanagement.folders.locations.effectiveSecurityHealthAnalyticsCustomModules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/effectiveSecurityHealthAnalyticsCustomModules',
        request_field='',
        request_type_name='SecuritycentermanagementFoldersLocationsEffectiveSecurityHealthAnalyticsCustomModulesListRequest',
        response_type_name='ListEffectiveSecurityHealthAnalyticsCustomModulesResponse',
        supports_download=False,
    )

  class FoldersLocationsEventThreatDetectionCustomModulesService(base_api.BaseApiService):
    """Service class for the folders_locations_eventThreatDetectionCustomModules resource."""

    _NAME = 'folders_locations_eventThreatDetectionCustomModules'

    def __init__(self, client):
      super(SecuritycentermanagementV1.FoldersLocationsEventThreatDetectionCustomModulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a resident Event Threat Detection custom module at the scope of the given organization, folder, or project, and creates inherited custom modules for all descendants of the given parent. These modules are enabled by default.

      Args:
        request: (SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModulesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EventThreatDetectionCustomModule) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/eventThreatDetectionCustomModules',
        http_method='POST',
        method_id='securitycentermanagement.folders.locations.eventThreatDetectionCustomModules.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['validateOnly'],
        relative_path='v1/{+parent}/eventThreatDetectionCustomModules',
        request_field='eventThreatDetectionCustomModule',
        request_type_name='SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModulesCreateRequest',
        response_type_name='EventThreatDetectionCustomModule',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified Event Threat Detection custom module and all of its descendants in the resource hierarchy. This method is only supported for resident custom modules.

      Args:
        request: (SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModulesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/eventThreatDetectionCustomModules/{eventThreatDetectionCustomModulesId}',
        http_method='DELETE',
        method_id='securitycentermanagement.folders.locations.eventThreatDetectionCustomModules.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModulesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an Event Threat Detection custom module.

      Args:
        request: (SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EventThreatDetectionCustomModule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/eventThreatDetectionCustomModules/{eventThreatDetectionCustomModulesId}',
        http_method='GET',
        method_id='securitycentermanagement.folders.locations.eventThreatDetectionCustomModules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModulesGetRequest',
        response_type_name='EventThreatDetectionCustomModule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all Event Threat Detection custom modules for the given organization, folder, or project. This includes resident modules defined at the scope of the parent along with modules inherited from ancestors.

      Args:
        request: (SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEventThreatDetectionCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/eventThreatDetectionCustomModules',
        http_method='GET',
        method_id='securitycentermanagement.folders.locations.eventThreatDetectionCustomModules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/eventThreatDetectionCustomModules',
        request_field='',
        request_type_name='SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModulesListRequest',
        response_type_name='ListEventThreatDetectionCustomModulesResponse',
        supports_download=False,
    )

    def ListDescendant(self, request, global_params=None):
      r"""Lists all resident Event Threat Detection custom modules for the given organization, folder, or project and its descendants.

      Args:
        request: (SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModulesListDescendantRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDescendantEventThreatDetectionCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('ListDescendant')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListDescendant.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/eventThreatDetectionCustomModules:listDescendant',
        http_method='GET',
        method_id='securitycentermanagement.folders.locations.eventThreatDetectionCustomModules.listDescendant',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/eventThreatDetectionCustomModules:listDescendant',
        request_field='',
        request_type_name='SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModulesListDescendantRequest',
        response_type_name='ListDescendantEventThreatDetectionCustomModulesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the Event Threat Detection custom module with the given name based on the given update mask. Updating the enablement state is supported for both resident and inherited modules (though resident modules cannot have an enablement state of "inherited"). Updating the display name or configuration of a module is supported for resident modules only. The type of a module cannot be changed.

      Args:
        request: (SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModulesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EventThreatDetectionCustomModule) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/eventThreatDetectionCustomModules/{eventThreatDetectionCustomModulesId}',
        http_method='PATCH',
        method_id='securitycentermanagement.folders.locations.eventThreatDetectionCustomModules.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='eventThreatDetectionCustomModule',
        request_type_name='SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModulesPatchRequest',
        response_type_name='EventThreatDetectionCustomModule',
        supports_download=False,
    )

    def Validate(self, request, global_params=None):
      r"""Validates the given Event Threat Detection custom module.

      Args:
        request: (SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModulesValidateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ValidateEventThreatDetectionCustomModuleResponse) The response message.
      """
      config = self.GetMethodConfig('Validate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Validate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/eventThreatDetectionCustomModules:validate',
        http_method='POST',
        method_id='securitycentermanagement.folders.locations.eventThreatDetectionCustomModules.validate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/eventThreatDetectionCustomModules:validate',
        request_field='validateEventThreatDetectionCustomModuleRequest',
        request_type_name='SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModulesValidateRequest',
        response_type_name='ValidateEventThreatDetectionCustomModuleResponse',
        supports_download=False,
    )

  class FoldersLocationsSecurityCenterServicesService(base_api.BaseApiService):
    """Service class for the folders_locations_securityCenterServices resource."""

    _NAME = 'folders_locations_securityCenterServices'

    def __init__(self, client):
      super(SecuritycentermanagementV1.FoldersLocationsSecurityCenterServicesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets service settings for the specified Security Command Center service.

      Args:
        request: (SecuritycentermanagementFoldersLocationsSecurityCenterServicesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityCenterService) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/securityCenterServices/{securityCenterServicesId}',
        http_method='GET',
        method_id='securitycentermanagement.folders.locations.securityCenterServices.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['showEligibleModulesOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycentermanagementFoldersLocationsSecurityCenterServicesGetRequest',
        response_type_name='SecurityCenterService',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns a list of all Security Command Center services for the given parent.

      Args:
        request: (SecuritycentermanagementFoldersLocationsSecurityCenterServicesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSecurityCenterServicesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/securityCenterServices',
        http_method='GET',
        method_id='securitycentermanagement.folders.locations.securityCenterServices.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'showEligibleModulesOnly'],
        relative_path='v1/{+parent}/securityCenterServices',
        request_field='',
        request_type_name='SecuritycentermanagementFoldersLocationsSecurityCenterServicesListRequest',
        response_type_name='ListSecurityCenterServicesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a Security Command Center service using the given update mask.

      Args:
        request: (SecuritycentermanagementFoldersLocationsSecurityCenterServicesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityCenterService) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/securityCenterServices/{securityCenterServicesId}',
        http_method='PATCH',
        method_id='securitycentermanagement.folders.locations.securityCenterServices.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='securityCenterService',
        request_type_name='SecuritycentermanagementFoldersLocationsSecurityCenterServicesPatchRequest',
        response_type_name='SecurityCenterService',
        supports_download=False,
    )

  class FoldersLocationsSecurityHealthAnalyticsCustomModulesService(base_api.BaseApiService):
    """Service class for the folders_locations_securityHealthAnalyticsCustomModules resource."""

    _NAME = 'folders_locations_securityHealthAnalyticsCustomModules'

    def __init__(self, client):
      super(SecuritycentermanagementV1.FoldersLocationsSecurityHealthAnalyticsCustomModulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a resident SecurityHealthAnalyticsCustomModule at the scope of the given organization, folder, or project, and also creates inherited `SecurityHealthAnalyticsCustomModule` resources for all folders and projects that are descendants of the given parent. These modules are enabled by default.

      Args:
        request: (SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomModulesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityHealthAnalyticsCustomModule) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/securityHealthAnalyticsCustomModules',
        http_method='POST',
        method_id='securitycentermanagement.folders.locations.securityHealthAnalyticsCustomModules.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['validateOnly'],
        relative_path='v1/{+parent}/securityHealthAnalyticsCustomModules',
        request_field='securityHealthAnalyticsCustomModule',
        request_type_name='SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomModulesCreateRequest',
        response_type_name='SecurityHealthAnalyticsCustomModule',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified SecurityHealthAnalyticsCustomModule and all of its descendants in the resource hierarchy. This method is only supported for resident custom modules.

      Args:
        request: (SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomModulesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/securityHealthAnalyticsCustomModules/{securityHealthAnalyticsCustomModulesId}',
        http_method='DELETE',
        method_id='securitycentermanagement.folders.locations.securityHealthAnalyticsCustomModules.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomModulesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a SecurityHealthAnalyticsCustomModule.

      Args:
        request: (SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomModulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityHealthAnalyticsCustomModule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/securityHealthAnalyticsCustomModules/{securityHealthAnalyticsCustomModulesId}',
        http_method='GET',
        method_id='securitycentermanagement.folders.locations.securityHealthAnalyticsCustomModules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomModulesGetRequest',
        response_type_name='SecurityHealthAnalyticsCustomModule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns a list of all SecurityHealthAnalyticsCustomModule resources for the given parent. This includes resident modules defined at the scope of the parent, and inherited modules, inherited from ancestor organizations, folders, and projects (no descendants).

      Args:
        request: (SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomModulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSecurityHealthAnalyticsCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/securityHealthAnalyticsCustomModules',
        http_method='GET',
        method_id='securitycentermanagement.folders.locations.securityHealthAnalyticsCustomModules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/securityHealthAnalyticsCustomModules',
        request_field='',
        request_type_name='SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomModulesListRequest',
        response_type_name='ListSecurityHealthAnalyticsCustomModulesResponse',
        supports_download=False,
    )

    def ListDescendant(self, request, global_params=None):
      r"""Returns a list of all resident SecurityHealthAnalyticsCustomModule resources under the given organization, folder, or project and all of its descendants.

      Args:
        request: (SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomModulesListDescendantRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDescendantSecurityHealthAnalyticsCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('ListDescendant')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListDescendant.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/securityHealthAnalyticsCustomModules:listDescendant',
        http_method='GET',
        method_id='securitycentermanagement.folders.locations.securityHealthAnalyticsCustomModules.listDescendant',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/securityHealthAnalyticsCustomModules:listDescendant',
        request_field='',
        request_type_name='SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomModulesListDescendantRequest',
        response_type_name='ListDescendantSecurityHealthAnalyticsCustomModulesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the SecurityHealthAnalyticsCustomModule under the given name based on the given update mask. Updating the enablement state is supported on both resident and inherited modules (though resident modules cannot have an enablement state of "inherited"). Updating the display name and custom configuration of a module is supported on resident modules only.

      Args:
        request: (SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomModulesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityHealthAnalyticsCustomModule) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/securityHealthAnalyticsCustomModules/{securityHealthAnalyticsCustomModulesId}',
        http_method='PATCH',
        method_id='securitycentermanagement.folders.locations.securityHealthAnalyticsCustomModules.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='securityHealthAnalyticsCustomModule',
        request_type_name='SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomModulesPatchRequest',
        response_type_name='SecurityHealthAnalyticsCustomModule',
        supports_download=False,
    )

    def Simulate(self, request, global_params=None):
      r"""Simulates the result of using a SecurityHealthAnalyticsCustomModule to check a resource.

      Args:
        request: (SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomModulesSimulateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SimulateSecurityHealthAnalyticsCustomModuleResponse) The response message.
      """
      config = self.GetMethodConfig('Simulate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Simulate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/locations/{locationsId}/securityHealthAnalyticsCustomModules:simulate',
        http_method='POST',
        method_id='securitycentermanagement.folders.locations.securityHealthAnalyticsCustomModules.simulate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/securityHealthAnalyticsCustomModules:simulate',
        request_field='simulateSecurityHealthAnalyticsCustomModuleRequest',
        request_type_name='SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomModulesSimulateRequest',
        response_type_name='SimulateSecurityHealthAnalyticsCustomModuleResponse',
        supports_download=False,
    )

  class FoldersLocationsService(base_api.BaseApiService):
    """Service class for the folders_locations resource."""

    _NAME = 'folders_locations'

    def __init__(self, client):
      super(SecuritycentermanagementV1.FoldersLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class FoldersService(base_api.BaseApiService):
    """Service class for the folders resource."""

    _NAME = 'folders'

    def __init__(self, client):
      super(SecuritycentermanagementV1.FoldersService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsLocationsEffectiveEventThreatDetectionCustomModulesService(base_api.BaseApiService):
    """Service class for the organizations_locations_effectiveEventThreatDetectionCustomModules resource."""

    _NAME = 'organizations_locations_effectiveEventThreatDetectionCustomModules'

    def __init__(self, client):
      super(SecuritycentermanagementV1.OrganizationsLocationsEffectiveEventThreatDetectionCustomModulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the effective Event Threat Detection custom module at the given level. The difference between an EffectiveEventThreatDetectionCustomModule and an EventThreatDetectionCustomModule is that the fields for an `EffectiveEventThreatDetectionCustomModule` are computed from ancestors if needed. For example, the enablement state for an `EventThreatDetectionCustomModule` can be `ENABLED`, `DISABLED`, or `INHERITED`. In contrast, the enablement state for an `EffectiveEventThreatDetectionCustomModule` is always computed as `ENABLED` or `DISABLED`.

      Args:
        request: (SecuritycentermanagementOrganizationsLocationsEffectiveEventThreatDetectionCustomModulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EffectiveEventThreatDetectionCustomModule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/effectiveEventThreatDetectionCustomModules/{effectiveEventThreatDetectionCustomModulesId}',
        http_method='GET',
        method_id='securitycentermanagement.organizations.locations.effectiveEventThreatDetectionCustomModules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycentermanagementOrganizationsLocationsEffectiveEventThreatDetectionCustomModulesGetRequest',
        response_type_name='EffectiveEventThreatDetectionCustomModule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all effective Event Threat Detection custom modules for the given parent. This includes resident modules defined at the scope of the parent along with modules inherited from its ancestors.

      Args:
        request: (SecuritycentermanagementOrganizationsLocationsEffectiveEventThreatDetectionCustomModulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEffectiveEventThreatDetectionCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/effectiveEventThreatDetectionCustomModules',
        http_method='GET',
        method_id='securitycentermanagement.organizations.locations.effectiveEventThreatDetectionCustomModules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/effectiveEventThreatDetectionCustomModules',
        request_field='',
        request_type_name='SecuritycentermanagementOrganizationsLocationsEffectiveEventThreatDetectionCustomModulesListRequest',
        response_type_name='ListEffectiveEventThreatDetectionCustomModulesResponse',
        supports_download=False,
    )

  class OrganizationsLocationsEffectiveSecurityHealthAnalyticsCustomModulesService(base_api.BaseApiService):
    """Service class for the organizations_locations_effectiveSecurityHealthAnalyticsCustomModules resource."""

    _NAME = 'organizations_locations_effectiveSecurityHealthAnalyticsCustomModules'

    def __init__(self, client):
      super(SecuritycentermanagementV1.OrganizationsLocationsEffectiveSecurityHealthAnalyticsCustomModulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details of a single EffectiveSecurityHealthAnalyticsCustomModule.

      Args:
        request: (SecuritycentermanagementOrganizationsLocationsEffectiveSecurityHealthAnalyticsCustomModulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EffectiveSecurityHealthAnalyticsCustomModule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/effectiveSecurityHealthAnalyticsCustomModules/{effectiveSecurityHealthAnalyticsCustomModulesId}',
        http_method='GET',
        method_id='securitycentermanagement.organizations.locations.effectiveSecurityHealthAnalyticsCustomModules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycentermanagementOrganizationsLocationsEffectiveSecurityHealthAnalyticsCustomModulesGetRequest',
        response_type_name='EffectiveSecurityHealthAnalyticsCustomModule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns a list of all EffectiveSecurityHealthAnalyticsCustomModule resources for the given parent. This includes resident modules defined at the scope of the parent, and inherited modules, inherited from ancestor organizations, folders, and projects (no descendants).

      Args:
        request: (SecuritycentermanagementOrganizationsLocationsEffectiveSecurityHealthAnalyticsCustomModulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEffectiveSecurityHealthAnalyticsCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/effectiveSecurityHealthAnalyticsCustomModules',
        http_method='GET',
        method_id='securitycentermanagement.organizations.locations.effectiveSecurityHealthAnalyticsCustomModules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/effectiveSecurityHealthAnalyticsCustomModules',
        request_field='',
        request_type_name='SecuritycentermanagementOrganizationsLocationsEffectiveSecurityHealthAnalyticsCustomModulesListRequest',
        response_type_name='ListEffectiveSecurityHealthAnalyticsCustomModulesResponse',
        supports_download=False,
    )

  class OrganizationsLocationsEventThreatDetectionCustomModulesService(base_api.BaseApiService):
    """Service class for the organizations_locations_eventThreatDetectionCustomModules resource."""

    _NAME = 'organizations_locations_eventThreatDetectionCustomModules'

    def __init__(self, client):
      super(SecuritycentermanagementV1.OrganizationsLocationsEventThreatDetectionCustomModulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a resident Event Threat Detection custom module at the scope of the given organization, folder, or project, and creates inherited custom modules for all descendants of the given parent. These modules are enabled by default.

      Args:
        request: (SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCustomModulesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EventThreatDetectionCustomModule) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/eventThreatDetectionCustomModules',
        http_method='POST',
        method_id='securitycentermanagement.organizations.locations.eventThreatDetectionCustomModules.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['validateOnly'],
        relative_path='v1/{+parent}/eventThreatDetectionCustomModules',
        request_field='eventThreatDetectionCustomModule',
        request_type_name='SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCustomModulesCreateRequest',
        response_type_name='EventThreatDetectionCustomModule',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified Event Threat Detection custom module and all of its descendants in the resource hierarchy. This method is only supported for resident custom modules.

      Args:
        request: (SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCustomModulesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/eventThreatDetectionCustomModules/{eventThreatDetectionCustomModulesId}',
        http_method='DELETE',
        method_id='securitycentermanagement.organizations.locations.eventThreatDetectionCustomModules.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCustomModulesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an Event Threat Detection custom module.

      Args:
        request: (SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCustomModulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EventThreatDetectionCustomModule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/eventThreatDetectionCustomModules/{eventThreatDetectionCustomModulesId}',
        http_method='GET',
        method_id='securitycentermanagement.organizations.locations.eventThreatDetectionCustomModules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCustomModulesGetRequest',
        response_type_name='EventThreatDetectionCustomModule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all Event Threat Detection custom modules for the given organization, folder, or project. This includes resident modules defined at the scope of the parent along with modules inherited from ancestors.

      Args:
        request: (SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCustomModulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEventThreatDetectionCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/eventThreatDetectionCustomModules',
        http_method='GET',
        method_id='securitycentermanagement.organizations.locations.eventThreatDetectionCustomModules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/eventThreatDetectionCustomModules',
        request_field='',
        request_type_name='SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCustomModulesListRequest',
        response_type_name='ListEventThreatDetectionCustomModulesResponse',
        supports_download=False,
    )

    def ListDescendant(self, request, global_params=None):
      r"""Lists all resident Event Threat Detection custom modules for the given organization, folder, or project and its descendants.

      Args:
        request: (SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCustomModulesListDescendantRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDescendantEventThreatDetectionCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('ListDescendant')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListDescendant.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/eventThreatDetectionCustomModules:listDescendant',
        http_method='GET',
        method_id='securitycentermanagement.organizations.locations.eventThreatDetectionCustomModules.listDescendant',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/eventThreatDetectionCustomModules:listDescendant',
        request_field='',
        request_type_name='SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCustomModulesListDescendantRequest',
        response_type_name='ListDescendantEventThreatDetectionCustomModulesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the Event Threat Detection custom module with the given name based on the given update mask. Updating the enablement state is supported for both resident and inherited modules (though resident modules cannot have an enablement state of "inherited"). Updating the display name or configuration of a module is supported for resident modules only. The type of a module cannot be changed.

      Args:
        request: (SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCustomModulesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EventThreatDetectionCustomModule) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/eventThreatDetectionCustomModules/{eventThreatDetectionCustomModulesId}',
        http_method='PATCH',
        method_id='securitycentermanagement.organizations.locations.eventThreatDetectionCustomModules.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='eventThreatDetectionCustomModule',
        request_type_name='SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCustomModulesPatchRequest',
        response_type_name='EventThreatDetectionCustomModule',
        supports_download=False,
    )

    def Validate(self, request, global_params=None):
      r"""Validates the given Event Threat Detection custom module.

      Args:
        request: (SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCustomModulesValidateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ValidateEventThreatDetectionCustomModuleResponse) The response message.
      """
      config = self.GetMethodConfig('Validate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Validate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/eventThreatDetectionCustomModules:validate',
        http_method='POST',
        method_id='securitycentermanagement.organizations.locations.eventThreatDetectionCustomModules.validate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/eventThreatDetectionCustomModules:validate',
        request_field='validateEventThreatDetectionCustomModuleRequest',
        request_type_name='SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCustomModulesValidateRequest',
        response_type_name='ValidateEventThreatDetectionCustomModuleResponse',
        supports_download=False,
    )

  class OrganizationsLocationsSecurityCenterServicesService(base_api.BaseApiService):
    """Service class for the organizations_locations_securityCenterServices resource."""

    _NAME = 'organizations_locations_securityCenterServices'

    def __init__(self, client):
      super(SecuritycentermanagementV1.OrganizationsLocationsSecurityCenterServicesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets service settings for the specified Security Command Center service.

      Args:
        request: (SecuritycentermanagementOrganizationsLocationsSecurityCenterServicesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityCenterService) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/securityCenterServices/{securityCenterServicesId}',
        http_method='GET',
        method_id='securitycentermanagement.organizations.locations.securityCenterServices.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['showEligibleModulesOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycentermanagementOrganizationsLocationsSecurityCenterServicesGetRequest',
        response_type_name='SecurityCenterService',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns a list of all Security Command Center services for the given parent.

      Args:
        request: (SecuritycentermanagementOrganizationsLocationsSecurityCenterServicesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSecurityCenterServicesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/securityCenterServices',
        http_method='GET',
        method_id='securitycentermanagement.organizations.locations.securityCenterServices.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'showEligibleModulesOnly'],
        relative_path='v1/{+parent}/securityCenterServices',
        request_field='',
        request_type_name='SecuritycentermanagementOrganizationsLocationsSecurityCenterServicesListRequest',
        response_type_name='ListSecurityCenterServicesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a Security Command Center service using the given update mask.

      Args:
        request: (SecuritycentermanagementOrganizationsLocationsSecurityCenterServicesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityCenterService) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/securityCenterServices/{securityCenterServicesId}',
        http_method='PATCH',
        method_id='securitycentermanagement.organizations.locations.securityCenterServices.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='securityCenterService',
        request_type_name='SecuritycentermanagementOrganizationsLocationsSecurityCenterServicesPatchRequest',
        response_type_name='SecurityCenterService',
        supports_download=False,
    )

  class OrganizationsLocationsSecurityHealthAnalyticsCustomModulesService(base_api.BaseApiService):
    """Service class for the organizations_locations_securityHealthAnalyticsCustomModules resource."""

    _NAME = 'organizations_locations_securityHealthAnalyticsCustomModules'

    def __init__(self, client):
      super(SecuritycentermanagementV1.OrganizationsLocationsSecurityHealthAnalyticsCustomModulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a resident SecurityHealthAnalyticsCustomModule at the scope of the given organization, folder, or project, and also creates inherited `SecurityHealthAnalyticsCustomModule` resources for all folders and projects that are descendants of the given parent. These modules are enabled by default.

      Args:
        request: (SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsCustomModulesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityHealthAnalyticsCustomModule) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/securityHealthAnalyticsCustomModules',
        http_method='POST',
        method_id='securitycentermanagement.organizations.locations.securityHealthAnalyticsCustomModules.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['validateOnly'],
        relative_path='v1/{+parent}/securityHealthAnalyticsCustomModules',
        request_field='securityHealthAnalyticsCustomModule',
        request_type_name='SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsCustomModulesCreateRequest',
        response_type_name='SecurityHealthAnalyticsCustomModule',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified SecurityHealthAnalyticsCustomModule and all of its descendants in the resource hierarchy. This method is only supported for resident custom modules.

      Args:
        request: (SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsCustomModulesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/securityHealthAnalyticsCustomModules/{securityHealthAnalyticsCustomModulesId}',
        http_method='DELETE',
        method_id='securitycentermanagement.organizations.locations.securityHealthAnalyticsCustomModules.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsCustomModulesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a SecurityHealthAnalyticsCustomModule.

      Args:
        request: (SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsCustomModulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityHealthAnalyticsCustomModule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/securityHealthAnalyticsCustomModules/{securityHealthAnalyticsCustomModulesId}',
        http_method='GET',
        method_id='securitycentermanagement.organizations.locations.securityHealthAnalyticsCustomModules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsCustomModulesGetRequest',
        response_type_name='SecurityHealthAnalyticsCustomModule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns a list of all SecurityHealthAnalyticsCustomModule resources for the given parent. This includes resident modules defined at the scope of the parent, and inherited modules, inherited from ancestor organizations, folders, and projects (no descendants).

      Args:
        request: (SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsCustomModulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSecurityHealthAnalyticsCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/securityHealthAnalyticsCustomModules',
        http_method='GET',
        method_id='securitycentermanagement.organizations.locations.securityHealthAnalyticsCustomModules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/securityHealthAnalyticsCustomModules',
        request_field='',
        request_type_name='SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsCustomModulesListRequest',
        response_type_name='ListSecurityHealthAnalyticsCustomModulesResponse',
        supports_download=False,
    )

    def ListDescendant(self, request, global_params=None):
      r"""Returns a list of all resident SecurityHealthAnalyticsCustomModule resources under the given organization, folder, or project and all of its descendants.

      Args:
        request: (SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsCustomModulesListDescendantRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDescendantSecurityHealthAnalyticsCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('ListDescendant')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListDescendant.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/securityHealthAnalyticsCustomModules:listDescendant',
        http_method='GET',
        method_id='securitycentermanagement.organizations.locations.securityHealthAnalyticsCustomModules.listDescendant',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/securityHealthAnalyticsCustomModules:listDescendant',
        request_field='',
        request_type_name='SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsCustomModulesListDescendantRequest',
        response_type_name='ListDescendantSecurityHealthAnalyticsCustomModulesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the SecurityHealthAnalyticsCustomModule under the given name based on the given update mask. Updating the enablement state is supported on both resident and inherited modules (though resident modules cannot have an enablement state of "inherited"). Updating the display name and custom configuration of a module is supported on resident modules only.

      Args:
        request: (SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsCustomModulesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityHealthAnalyticsCustomModule) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/securityHealthAnalyticsCustomModules/{securityHealthAnalyticsCustomModulesId}',
        http_method='PATCH',
        method_id='securitycentermanagement.organizations.locations.securityHealthAnalyticsCustomModules.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='securityHealthAnalyticsCustomModule',
        request_type_name='SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsCustomModulesPatchRequest',
        response_type_name='SecurityHealthAnalyticsCustomModule',
        supports_download=False,
    )

    def Simulate(self, request, global_params=None):
      r"""Simulates the result of using a SecurityHealthAnalyticsCustomModule to check a resource.

      Args:
        request: (SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsCustomModulesSimulateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SimulateSecurityHealthAnalyticsCustomModuleResponse) The response message.
      """
      config = self.GetMethodConfig('Simulate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Simulate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/securityHealthAnalyticsCustomModules:simulate',
        http_method='POST',
        method_id='securitycentermanagement.organizations.locations.securityHealthAnalyticsCustomModules.simulate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/securityHealthAnalyticsCustomModules:simulate',
        request_field='simulateSecurityHealthAnalyticsCustomModuleRequest',
        request_type_name='SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsCustomModulesSimulateRequest',
        response_type_name='SimulateSecurityHealthAnalyticsCustomModuleResponse',
        supports_download=False,
    )

  class OrganizationsLocationsService(base_api.BaseApiService):
    """Service class for the organizations_locations resource."""

    _NAME = 'organizations_locations'

    def __init__(self, client):
      super(SecuritycentermanagementV1.OrganizationsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def GetBillingMetadata(self, request, global_params=None):
      r"""Retrieve the billing metadata explicitly set on a specific Resource.

      Args:
        request: (SecuritycentermanagementOrganizationsLocationsGetBillingMetadataRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BillingMetadata) The response message.
      """
      config = self.GetMethodConfig('GetBillingMetadata')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetBillingMetadata.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/locations/{locationsId}/billingMetadata',
        http_method='GET',
        method_id='securitycentermanagement.organizations.locations.getBillingMetadata',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycentermanagementOrganizationsLocationsGetBillingMetadataRequest',
        response_type_name='BillingMetadata',
        supports_download=False,
    )

  class OrganizationsService(base_api.BaseApiService):
    """Service class for the organizations resource."""

    _NAME = 'organizations'

    def __init__(self, client):
      super(SecuritycentermanagementV1.OrganizationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsEffectiveEventThreatDetectionCustomModulesService(base_api.BaseApiService):
    """Service class for the projects_locations_effectiveEventThreatDetectionCustomModules resource."""

    _NAME = 'projects_locations_effectiveEventThreatDetectionCustomModules'

    def __init__(self, client):
      super(SecuritycentermanagementV1.ProjectsLocationsEffectiveEventThreatDetectionCustomModulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the effective Event Threat Detection custom module at the given level. The difference between an EffectiveEventThreatDetectionCustomModule and an EventThreatDetectionCustomModule is that the fields for an `EffectiveEventThreatDetectionCustomModule` are computed from ancestors if needed. For example, the enablement state for an `EventThreatDetectionCustomModule` can be `ENABLED`, `DISABLED`, or `INHERITED`. In contrast, the enablement state for an `EffectiveEventThreatDetectionCustomModule` is always computed as `ENABLED` or `DISABLED`.

      Args:
        request: (SecuritycentermanagementProjectsLocationsEffectiveEventThreatDetectionCustomModulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EffectiveEventThreatDetectionCustomModule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/effectiveEventThreatDetectionCustomModules/{effectiveEventThreatDetectionCustomModulesId}',
        http_method='GET',
        method_id='securitycentermanagement.projects.locations.effectiveEventThreatDetectionCustomModules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycentermanagementProjectsLocationsEffectiveEventThreatDetectionCustomModulesGetRequest',
        response_type_name='EffectiveEventThreatDetectionCustomModule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all effective Event Threat Detection custom modules for the given parent. This includes resident modules defined at the scope of the parent along with modules inherited from its ancestors.

      Args:
        request: (SecuritycentermanagementProjectsLocationsEffectiveEventThreatDetectionCustomModulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEffectiveEventThreatDetectionCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/effectiveEventThreatDetectionCustomModules',
        http_method='GET',
        method_id='securitycentermanagement.projects.locations.effectiveEventThreatDetectionCustomModules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/effectiveEventThreatDetectionCustomModules',
        request_field='',
        request_type_name='SecuritycentermanagementProjectsLocationsEffectiveEventThreatDetectionCustomModulesListRequest',
        response_type_name='ListEffectiveEventThreatDetectionCustomModulesResponse',
        supports_download=False,
    )

  class ProjectsLocationsEffectiveSecurityHealthAnalyticsCustomModulesService(base_api.BaseApiService):
    """Service class for the projects_locations_effectiveSecurityHealthAnalyticsCustomModules resource."""

    _NAME = 'projects_locations_effectiveSecurityHealthAnalyticsCustomModules'

    def __init__(self, client):
      super(SecuritycentermanagementV1.ProjectsLocationsEffectiveSecurityHealthAnalyticsCustomModulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details of a single EffectiveSecurityHealthAnalyticsCustomModule.

      Args:
        request: (SecuritycentermanagementProjectsLocationsEffectiveSecurityHealthAnalyticsCustomModulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EffectiveSecurityHealthAnalyticsCustomModule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/effectiveSecurityHealthAnalyticsCustomModules/{effectiveSecurityHealthAnalyticsCustomModulesId}',
        http_method='GET',
        method_id='securitycentermanagement.projects.locations.effectiveSecurityHealthAnalyticsCustomModules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycentermanagementProjectsLocationsEffectiveSecurityHealthAnalyticsCustomModulesGetRequest',
        response_type_name='EffectiveSecurityHealthAnalyticsCustomModule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns a list of all EffectiveSecurityHealthAnalyticsCustomModule resources for the given parent. This includes resident modules defined at the scope of the parent, and inherited modules, inherited from ancestor organizations, folders, and projects (no descendants).

      Args:
        request: (SecuritycentermanagementProjectsLocationsEffectiveSecurityHealthAnalyticsCustomModulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEffectiveSecurityHealthAnalyticsCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/effectiveSecurityHealthAnalyticsCustomModules',
        http_method='GET',
        method_id='securitycentermanagement.projects.locations.effectiveSecurityHealthAnalyticsCustomModules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/effectiveSecurityHealthAnalyticsCustomModules',
        request_field='',
        request_type_name='SecuritycentermanagementProjectsLocationsEffectiveSecurityHealthAnalyticsCustomModulesListRequest',
        response_type_name='ListEffectiveSecurityHealthAnalyticsCustomModulesResponse',
        supports_download=False,
    )

  class ProjectsLocationsEventThreatDetectionCustomModulesService(base_api.BaseApiService):
    """Service class for the projects_locations_eventThreatDetectionCustomModules resource."""

    _NAME = 'projects_locations_eventThreatDetectionCustomModules'

    def __init__(self, client):
      super(SecuritycentermanagementV1.ProjectsLocationsEventThreatDetectionCustomModulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a resident Event Threat Detection custom module at the scope of the given organization, folder, or project, and creates inherited custom modules for all descendants of the given parent. These modules are enabled by default.

      Args:
        request: (SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomModulesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EventThreatDetectionCustomModule) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/eventThreatDetectionCustomModules',
        http_method='POST',
        method_id='securitycentermanagement.projects.locations.eventThreatDetectionCustomModules.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['validateOnly'],
        relative_path='v1/{+parent}/eventThreatDetectionCustomModules',
        request_field='eventThreatDetectionCustomModule',
        request_type_name='SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomModulesCreateRequest',
        response_type_name='EventThreatDetectionCustomModule',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified Event Threat Detection custom module and all of its descendants in the resource hierarchy. This method is only supported for resident custom modules.

      Args:
        request: (SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomModulesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/eventThreatDetectionCustomModules/{eventThreatDetectionCustomModulesId}',
        http_method='DELETE',
        method_id='securitycentermanagement.projects.locations.eventThreatDetectionCustomModules.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomModulesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an Event Threat Detection custom module.

      Args:
        request: (SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomModulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EventThreatDetectionCustomModule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/eventThreatDetectionCustomModules/{eventThreatDetectionCustomModulesId}',
        http_method='GET',
        method_id='securitycentermanagement.projects.locations.eventThreatDetectionCustomModules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomModulesGetRequest',
        response_type_name='EventThreatDetectionCustomModule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all Event Threat Detection custom modules for the given organization, folder, or project. This includes resident modules defined at the scope of the parent along with modules inherited from ancestors.

      Args:
        request: (SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomModulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEventThreatDetectionCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/eventThreatDetectionCustomModules',
        http_method='GET',
        method_id='securitycentermanagement.projects.locations.eventThreatDetectionCustomModules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/eventThreatDetectionCustomModules',
        request_field='',
        request_type_name='SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomModulesListRequest',
        response_type_name='ListEventThreatDetectionCustomModulesResponse',
        supports_download=False,
    )

    def ListDescendant(self, request, global_params=None):
      r"""Lists all resident Event Threat Detection custom modules for the given organization, folder, or project and its descendants.

      Args:
        request: (SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomModulesListDescendantRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDescendantEventThreatDetectionCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('ListDescendant')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListDescendant.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/eventThreatDetectionCustomModules:listDescendant',
        http_method='GET',
        method_id='securitycentermanagement.projects.locations.eventThreatDetectionCustomModules.listDescendant',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/eventThreatDetectionCustomModules:listDescendant',
        request_field='',
        request_type_name='SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomModulesListDescendantRequest',
        response_type_name='ListDescendantEventThreatDetectionCustomModulesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the Event Threat Detection custom module with the given name based on the given update mask. Updating the enablement state is supported for both resident and inherited modules (though resident modules cannot have an enablement state of "inherited"). Updating the display name or configuration of a module is supported for resident modules only. The type of a module cannot be changed.

      Args:
        request: (SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomModulesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EventThreatDetectionCustomModule) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/eventThreatDetectionCustomModules/{eventThreatDetectionCustomModulesId}',
        http_method='PATCH',
        method_id='securitycentermanagement.projects.locations.eventThreatDetectionCustomModules.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='eventThreatDetectionCustomModule',
        request_type_name='SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomModulesPatchRequest',
        response_type_name='EventThreatDetectionCustomModule',
        supports_download=False,
    )

    def Validate(self, request, global_params=None):
      r"""Validates the given Event Threat Detection custom module.

      Args:
        request: (SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomModulesValidateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ValidateEventThreatDetectionCustomModuleResponse) The response message.
      """
      config = self.GetMethodConfig('Validate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Validate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/eventThreatDetectionCustomModules:validate',
        http_method='POST',
        method_id='securitycentermanagement.projects.locations.eventThreatDetectionCustomModules.validate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/eventThreatDetectionCustomModules:validate',
        request_field='validateEventThreatDetectionCustomModuleRequest',
        request_type_name='SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomModulesValidateRequest',
        response_type_name='ValidateEventThreatDetectionCustomModuleResponse',
        supports_download=False,
    )

  class ProjectsLocationsSecurityCenterServicesService(base_api.BaseApiService):
    """Service class for the projects_locations_securityCenterServices resource."""

    _NAME = 'projects_locations_securityCenterServices'

    def __init__(self, client):
      super(SecuritycentermanagementV1.ProjectsLocationsSecurityCenterServicesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets service settings for the specified Security Command Center service.

      Args:
        request: (SecuritycentermanagementProjectsLocationsSecurityCenterServicesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityCenterService) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/securityCenterServices/{securityCenterServicesId}',
        http_method='GET',
        method_id='securitycentermanagement.projects.locations.securityCenterServices.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['showEligibleModulesOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycentermanagementProjectsLocationsSecurityCenterServicesGetRequest',
        response_type_name='SecurityCenterService',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns a list of all Security Command Center services for the given parent.

      Args:
        request: (SecuritycentermanagementProjectsLocationsSecurityCenterServicesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSecurityCenterServicesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/securityCenterServices',
        http_method='GET',
        method_id='securitycentermanagement.projects.locations.securityCenterServices.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'showEligibleModulesOnly'],
        relative_path='v1/{+parent}/securityCenterServices',
        request_field='',
        request_type_name='SecuritycentermanagementProjectsLocationsSecurityCenterServicesListRequest',
        response_type_name='ListSecurityCenterServicesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a Security Command Center service using the given update mask.

      Args:
        request: (SecuritycentermanagementProjectsLocationsSecurityCenterServicesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityCenterService) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/securityCenterServices/{securityCenterServicesId}',
        http_method='PATCH',
        method_id='securitycentermanagement.projects.locations.securityCenterServices.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='securityCenterService',
        request_type_name='SecuritycentermanagementProjectsLocationsSecurityCenterServicesPatchRequest',
        response_type_name='SecurityCenterService',
        supports_download=False,
    )

  class ProjectsLocationsSecurityHealthAnalyticsCustomModulesService(base_api.BaseApiService):
    """Service class for the projects_locations_securityHealthAnalyticsCustomModules resource."""

    _NAME = 'projects_locations_securityHealthAnalyticsCustomModules'

    def __init__(self, client):
      super(SecuritycentermanagementV1.ProjectsLocationsSecurityHealthAnalyticsCustomModulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a resident SecurityHealthAnalyticsCustomModule at the scope of the given organization, folder, or project, and also creates inherited `SecurityHealthAnalyticsCustomModule` resources for all folders and projects that are descendants of the given parent. These modules are enabled by default.

      Args:
        request: (SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustomModulesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityHealthAnalyticsCustomModule) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/securityHealthAnalyticsCustomModules',
        http_method='POST',
        method_id='securitycentermanagement.projects.locations.securityHealthAnalyticsCustomModules.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['validateOnly'],
        relative_path='v1/{+parent}/securityHealthAnalyticsCustomModules',
        request_field='securityHealthAnalyticsCustomModule',
        request_type_name='SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustomModulesCreateRequest',
        response_type_name='SecurityHealthAnalyticsCustomModule',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified SecurityHealthAnalyticsCustomModule and all of its descendants in the resource hierarchy. This method is only supported for resident custom modules.

      Args:
        request: (SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustomModulesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/securityHealthAnalyticsCustomModules/{securityHealthAnalyticsCustomModulesId}',
        http_method='DELETE',
        method_id='securitycentermanagement.projects.locations.securityHealthAnalyticsCustomModules.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustomModulesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a SecurityHealthAnalyticsCustomModule.

      Args:
        request: (SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustomModulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityHealthAnalyticsCustomModule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/securityHealthAnalyticsCustomModules/{securityHealthAnalyticsCustomModulesId}',
        http_method='GET',
        method_id='securitycentermanagement.projects.locations.securityHealthAnalyticsCustomModules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustomModulesGetRequest',
        response_type_name='SecurityHealthAnalyticsCustomModule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns a list of all SecurityHealthAnalyticsCustomModule resources for the given parent. This includes resident modules defined at the scope of the parent, and inherited modules, inherited from ancestor organizations, folders, and projects (no descendants).

      Args:
        request: (SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustomModulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSecurityHealthAnalyticsCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/securityHealthAnalyticsCustomModules',
        http_method='GET',
        method_id='securitycentermanagement.projects.locations.securityHealthAnalyticsCustomModules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/securityHealthAnalyticsCustomModules',
        request_field='',
        request_type_name='SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustomModulesListRequest',
        response_type_name='ListSecurityHealthAnalyticsCustomModulesResponse',
        supports_download=False,
    )

    def ListDescendant(self, request, global_params=None):
      r"""Returns a list of all resident SecurityHealthAnalyticsCustomModule resources under the given organization, folder, or project and all of its descendants.

      Args:
        request: (SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustomModulesListDescendantRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDescendantSecurityHealthAnalyticsCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('ListDescendant')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListDescendant.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/securityHealthAnalyticsCustomModules:listDescendant',
        http_method='GET',
        method_id='securitycentermanagement.projects.locations.securityHealthAnalyticsCustomModules.listDescendant',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/securityHealthAnalyticsCustomModules:listDescendant',
        request_field='',
        request_type_name='SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustomModulesListDescendantRequest',
        response_type_name='ListDescendantSecurityHealthAnalyticsCustomModulesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the SecurityHealthAnalyticsCustomModule under the given name based on the given update mask. Updating the enablement state is supported on both resident and inherited modules (though resident modules cannot have an enablement state of "inherited"). Updating the display name and custom configuration of a module is supported on resident modules only.

      Args:
        request: (SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustomModulesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityHealthAnalyticsCustomModule) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/securityHealthAnalyticsCustomModules/{securityHealthAnalyticsCustomModulesId}',
        http_method='PATCH',
        method_id='securitycentermanagement.projects.locations.securityHealthAnalyticsCustomModules.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='securityHealthAnalyticsCustomModule',
        request_type_name='SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustomModulesPatchRequest',
        response_type_name='SecurityHealthAnalyticsCustomModule',
        supports_download=False,
    )

    def Simulate(self, request, global_params=None):
      r"""Simulates the result of using a SecurityHealthAnalyticsCustomModule to check a resource.

      Args:
        request: (SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustomModulesSimulateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SimulateSecurityHealthAnalyticsCustomModuleResponse) The response message.
      """
      config = self.GetMethodConfig('Simulate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Simulate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/securityHealthAnalyticsCustomModules:simulate',
        http_method='POST',
        method_id='securitycentermanagement.projects.locations.securityHealthAnalyticsCustomModules.simulate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/securityHealthAnalyticsCustomModules:simulate',
        request_field='simulateSecurityHealthAnalyticsCustomModuleRequest',
        request_type_name='SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustomModulesSimulateRequest',
        response_type_name='SimulateSecurityHealthAnalyticsCustomModuleResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(SecuritycentermanagementV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (SecuritycentermanagementProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudLocationLocation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='securitycentermanagement.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycentermanagementProjectsLocationsGetRequest',
        response_type_name='GoogleCloudLocationLocation',
        supports_download=False,
    )

    def GetBillingMetadata(self, request, global_params=None):
      r"""Retrieve the billing metadata explicitly set on a specific Resource.

      Args:
        request: (SecuritycentermanagementProjectsLocationsGetBillingMetadataRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BillingMetadata) The response message.
      """
      config = self.GetMethodConfig('GetBillingMetadata')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetBillingMetadata.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/billingMetadata',
        http_method='GET',
        method_id='securitycentermanagement.projects.locations.getBillingMetadata',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycentermanagementProjectsLocationsGetBillingMetadataRequest',
        response_type_name='BillingMetadata',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (SecuritycentermanagementProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='securitycentermanagement.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/locations',
        request_field='',
        request_type_name='SecuritycentermanagementProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(SecuritycentermanagementV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
