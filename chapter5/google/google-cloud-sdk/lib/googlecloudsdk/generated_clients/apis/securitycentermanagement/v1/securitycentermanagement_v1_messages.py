"""Generated message classes for securitycentermanagement version v1.

Management API for Security Command Center, a built-in security and risk
management solution for Google Cloud. Use this API to programmatically update
the settings and configuration of Security Command Center.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'securitycentermanagement'


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class BillingMetadata(_messages.Message):
  r"""Response message for SecurityCenterManagement.GetBillingMetadata.

  Enums:
    BillingTierValueValuesEnum: The billing tier of the resource.

  Fields:
    billingTier: The billing tier of the resource.
    name: Identifier. The full resource name of the billingMetadata, in one of
      the following formats: *
      `organizations/{organization}/locations/{location}/billingMetadata` *
      `projects/{project}/locations/{location}/billingMetadata`
  """

  class BillingTierValueValuesEnum(_messages.Enum):
    r"""The billing tier of the resource.

    Values:
      BILLING_TIER_UNSPECIFIED: Default value. This value is unused.
      STANDARD: Standard free tier.
      PREMIUM: Security Command Center Premium.
      ENTERPRISE: Security Command Center Enterprise.
    """
    BILLING_TIER_UNSPECIFIED = 0
    STANDARD = 1
    PREMIUM = 2
    ENTERPRISE = 3

  billingTier = _messages.EnumField('BillingTierValueValuesEnum', 1)
  name = _messages.StringField(2)


class CelPolicySpec(_messages.Message):
  r"""YAML-based rule that uses CEL, which supports the declaration of
  variables and a filtering predicate. A vulnerable resource is emitted if the
  evaluation is false. Given: 1) the resource types as: - resource_types:
  "compute.googleapis.com/Instance" - resource_types:
  "compute.googleapis.com/Firewall" 2) the CEL policy spec as: name:
  bad_instance resource_filters: - name: instance resource_type:
  compute.googleapis.com/Instance filter: > instance.status == 'RUNNING' &&
  'public' in instance.tags.items - name: firewall resource_type:
  compute.googleapis.com/Firewall filter: > firewall.direction == 'INGRESS' &&
  !firewall.disabled && firewall.allowed.exists(rule,
  rule.IPProtocol.upperAscii() in ['TCP', 'ALL'] && rule.ports.exists(port,
  network.portsInRange(port, '11-256'))) rule: match: - predicate: >
  instance.networkInterfaces.exists(net, firewall.network == net.network)
  output: > {'message': 'Compute instance with publicly accessible ports',
  'instance': instance.name} Users are able to join resource types together
  using the exact format as Kubernetes Validating Admission policies.

  Fields:
    spec: Optional. The CEL policy to evaluate to produce findings. A finding
      is generated when the policy validation evaluates to false.
  """

  spec = _messages.StringField(1)


class CustomConfig(_messages.Message):
  r"""Defines the properties in a custom module configuration for Security
  Health Analytics. Use the custom module configuration to create custom
  detectors that generate custom findings for resources that you specify.

  Enums:
    SeverityValueValuesEnum: Optional. The severity to assign to findings
      generated by the module.

  Fields:
    celPolicy: Optional. The CEL policy spec attached to the custom module.
    customOutput: Optional. Custom output properties.
    description: Optional. Text that describes the vulnerability or
      misconfiguration that the custom module detects. This explanation is
      returned with each finding instance to help investigators understand the
      detected issue. The text must be enclosed in quotation marks.
    predicate: Optional. The Common Expression Language (CEL) expression to
      evaluate to produce findings. When the expression evaluates to `true`
      against a resource, a finding is generated.
    recommendation: Optional. An explanation of the recommended steps that
      security teams can take to resolve the detected issue. This explanation
      is returned with each finding generated by this module.
    resourceSelector: Optional. The Cloud Asset Inventory resource types that
      the custom module operates on. For information about resource types, see
      [Supported asset types](https://cloud.google.com/asset-
      inventory/docs/supported-asset-types). Each custom module can specify up
      to 5 resource types.
    severity: Optional. The severity to assign to findings generated by the
      module.
  """

  class SeverityValueValuesEnum(_messages.Enum):
    r"""Optional. The severity to assign to findings generated by the module.

    Values:
      SEVERITY_UNSPECIFIED: Default value. This value is unused.
      CRITICAL: Critical severity.
      HIGH: High severity.
      MEDIUM: Medium severity.
      LOW: Low severity.
    """
    SEVERITY_UNSPECIFIED = 0
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4

  celPolicy = _messages.MessageField('CelPolicySpec', 1)
  customOutput = _messages.MessageField('CustomOutputSpec', 2)
  description = _messages.StringField(3)
  predicate = _messages.MessageField('Expr', 4)
  recommendation = _messages.StringField(5)
  resourceSelector = _messages.MessageField('ResourceSelector', 6)
  severity = _messages.EnumField('SeverityValueValuesEnum', 7)


class CustomModuleValidationError(_messages.Message):
  r"""An error encountered while validating the uploaded configuration of an
  Event Threat Detection custom module.

  Fields:
    description: A human-readable description of the error.
    end: The end position of the error in the uploaded text version of the
      module. Omitted if no specific position applies, or if the position
      could not be computed.
    fieldPath: The path, in [RFC 6901: JSON
      Pointer](https://datatracker.ietf.org/doc/html/rfc6901) format, to the
      field that failed validation. Omitted if no specific field is affected.
    start: The initial position of the error in the uploaded text version of
      the module. Omitted if no specific position applies, or if the position
      could not be computed.
  """

  description = _messages.StringField(1)
  end = _messages.MessageField('Position', 2)
  fieldPath = _messages.StringField(3)
  start = _messages.MessageField('Position', 4)


class CustomOutputSpec(_messages.Message):
  r"""A set of optional name-value pairs that define custom source properties
  to return with each finding that is generated by the custom module. The
  custom source properties that are defined here are included in the finding.

  Fields:
    properties: Optional. A list of custom output properties to add to the
      finding.
  """

  properties = _messages.MessageField('Property', 1, repeated=True)


class EffectiveEventThreatDetectionCustomModule(_messages.Message):
  r"""The representation of an EventThreatDetectionCustomModule at a given
  level, taking hierarchy into account and resolving various fields
  accordingly. For example, if the module is enabled at the ancestor level,
  then effective modules at all descendant levels will have their enablement
  state set to `ENABLED`. Similarly, if `module.inherited` is set, then the
  effective module's configuration will reflect the ancestor's configuration.

  Enums:
    EnablementStateValueValuesEnum: Output only. The effective state of
      enablement for the module at the given level of the hierarchy.

  Messages:
    ConfigValue: Output only. Configuration for the effective module.

  Fields:
    config: Output only. Configuration for the effective module.
    description: Output only. A description of the module.
    displayName: Output only. The human-readable name of the module.
    enablementState: Output only. The effective state of enablement for the
      module at the given level of the hierarchy.
    name: Identifier. The resource name of the Event Threat Detection custom
      module, in one of the following formats: * `organizations/{organization}
      /locations/{location}/effectiveEventThreatDetectionCustomModules/{custom
      _module}` * `folders/{folder}/locations/{location}/effectiveEventThreatD
      etectionCustomModules/{custom_module}` * `projects/{project}/locations/{
      location}/effectiveEventThreatDetectionCustomModules/{custom_module}`
    type: Output only. Type for the module (for example,
      `CONFIGURABLE_BAD_IP`).
  """

  class EnablementStateValueValuesEnum(_messages.Enum):
    r"""Output only. The effective state of enablement for the module at the
    given level of the hierarchy.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      ENABLED: The module is enabled at the given level.
      DISABLED: The module is disabled at the given level.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ConfigValue(_messages.Message):
    r"""Output only. Configuration for the effective module.

    Messages:
      AdditionalProperty: An additional property for a ConfigValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ConfigValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  config = _messages.MessageField('ConfigValue', 1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  enablementState = _messages.EnumField('EnablementStateValueValuesEnum', 4)
  name = _messages.StringField(5)
  type = _messages.StringField(6)


class EffectiveSecurityHealthAnalyticsCustomModule(_messages.Message):
  r"""The representation of a Security Health Analytics custom module at a
  specified level of the resource hierarchy: organization, folder, or project.
  If a custom module is inherited from an ancestor organization or folder,
  then the enablement state is set to the value that is effective in the
  parent, not to `INHERITED`. For example, if the module is enabled in an
  organization or folder, then the effective enablement state for the module
  is `ENABLED` in all descendant folders or projects.

  Enums:
    EnablementStateValueValuesEnum: Output only. The effective enablement
      state for the module at the given level of the hierarchy.

  Fields:
    customConfig: Output only. The user-specified configuration for the
      module.
    displayName: Output only. The display name for the custom module. The name
      must be between 1 and 128 characters, start with a lowercase letter, and
      contain alphanumeric characters or underscores only.
    enablementState: Output only. The effective enablement state for the
      module at the given level of the hierarchy.
    name: Identifier. The full resource name of the custom module, in one of
      the following formats: * `organizations/organization/{location}/effectiv
      eSecurityHealthAnalyticsCustomModules/{custom_module}` * `folders/folder
      /{location}/effectiveSecurityHealthAnalyticsCustomModules/{custom_module
      }` * `projects/project/{location}/effectiveSecurityHealthAnalyticsCustom
      Modules/{custom_module}`
  """

  class EnablementStateValueValuesEnum(_messages.Enum):
    r"""Output only. The effective enablement state for the module at the
    given level of the hierarchy.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      ENABLED: The module is enabled at the given level.
      DISABLED: The module is disabled at the given level.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  customConfig = _messages.MessageField('CustomConfig', 1)
  displayName = _messages.StringField(2)
  enablementState = _messages.EnumField('EnablementStateValueValuesEnum', 3)
  name = _messages.StringField(4)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EventThreatDetectionCustomModule(_messages.Message):
  r"""A Security Command Center resource that contains the configuration and
  enablement state of a custom module, which enables Event Threat Detection to
  write certain findings to Security Command Center.

  Enums:
    EnablementStateValueValuesEnum: Optional. The state of enablement for the
      module at the given level of the hierarchy.

  Messages:
    ConfigValue: Optional. Configuration for the module. For the resident
      module, its configuration value is defined at this level. For the
      inherited module, its configuration value is inherited from the ancestor
      module.

  Fields:
    ancestorModule: Output only. The closest ancestor module that this module
      inherits the enablement state from. If empty, indicates that the custom
      module was created in the requesting parent organization, folder, or
      project. The format is the same as the custom module's resource name.
    config: Optional. Configuration for the module. For the resident module,
      its configuration value is defined at this level. For the inherited
      module, its configuration value is inherited from the ancestor module.
    description: Optional. A description of the module.
    displayName: Optional. The human-readable name of the module.
    enablementState: Optional. The state of enablement for the module at the
      given level of the hierarchy.
    lastEditor: Output only. The editor the module was last updated by.
    name: Identifier. The resource name of the Event Threat Detection custom
      module, in one of the following formats: * `organizations/{organization}
      /locations/{location}/eventThreatDetectionCustomModules/{custom_module}`
      * `folders/{folder}/locations/{location}/eventThreatDetectionCustomModul
      es/{custom_module}` * `projects/{project}/locations/{location}/eventThre
      atDetectionCustomModules/{custom_module}`
    type: Optional. Type for the module. For example, `CONFIGURABLE_BAD_IP`.
    updateTime: Output only. The time the module was last updated.
  """

  class EnablementStateValueValuesEnum(_messages.Enum):
    r"""Optional. The state of enablement for the module at the given level of
    the hierarchy.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Unspecified enablement state.
      ENABLED: The module is enabled at the given level.
      DISABLED: The module is disabled at the given level.
      INHERITED: State is inherited from an ancestor module. The module will
        either be effectively `ENABLED` or `DISABLED` based on its closest
        non-inherited ancestor module in the CRM hierarchy. If you try to set
        a top-level module (a module with no parent) to the `INHERITED` state,
        you receive an `INVALID_ARGUMENT` error.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2
    INHERITED = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ConfigValue(_messages.Message):
    r"""Optional. Configuration for the module. For the resident module, its
    configuration value is defined at this level. For the inherited module,
    its configuration value is inherited from the ancestor module.

    Messages:
      AdditionalProperty: An additional property for a ConfigValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ConfigValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  ancestorModule = _messages.StringField(1)
  config = _messages.MessageField('ConfigValue', 2)
  description = _messages.StringField(3)
  displayName = _messages.StringField(4)
  enablementState = _messages.EnumField('EnablementStateValueValuesEnum', 5)
  lastEditor = _messages.StringField(6)
  name = _messages.StringField(7)
  type = _messages.StringField(8)
  updateTime = _messages.StringField(9)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GoogleCloudLocationLocation(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class GoogleIamV1Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class ListDescendantEventThreatDetectionCustomModulesResponse(_messages.Message):
  r"""Response message for
  SecurityCenterManagement.ListDescendantEventThreatDetectionCustomModules.

  Fields:
    eventThreatDetectionCustomModules: The list of custom modules.
    nextPageToken: A pagination token. To retrieve the next page of results,
      call the method again with this token.
  """

  eventThreatDetectionCustomModules = _messages.MessageField('EventThreatDetectionCustomModule', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListDescendantSecurityHealthAnalyticsCustomModulesResponse(_messages.Message):
  r"""Response message for
  SecurityCenterManagement.ListDescendantSecurityHealthAnalyticsCustomModules.

  Fields:
    nextPageToken: A pagination token. To retrieve the next page of results,
      call the method again with this token.
    securityHealthAnalyticsCustomModules: The list of
      SecurityHealthAnalyticsCustomModules
  """

  nextPageToken = _messages.StringField(1)
  securityHealthAnalyticsCustomModules = _messages.MessageField('SecurityHealthAnalyticsCustomModule', 2, repeated=True)


class ListEffectiveEventThreatDetectionCustomModulesResponse(_messages.Message):
  r"""Response message for
  SecurityCenterManagement.ListEffectiveEventThreatDetectionCustomModules.

  Fields:
    effectiveEventThreatDetectionCustomModules: The list of effective Event
      Threat Detection custom modules.
    nextPageToken: A pagination token. To retrieve the next page of results,
      call the method again with this token.
  """

  effectiveEventThreatDetectionCustomModules = _messages.MessageField('EffectiveEventThreatDetectionCustomModule', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListEffectiveSecurityHealthAnalyticsCustomModulesResponse(_messages.Message):
  r"""Response message for
  SecurityCenterManagement.ListEffectiveSecurityHealthAnalyticsCustomModules.

  Fields:
    effectiveSecurityHealthAnalyticsCustomModules: The list of effective
      Security Health Analytics custom modules.
    nextPageToken: A pagination token. To retrieve the next page of results,
      call the method again with this token.
  """

  effectiveSecurityHealthAnalyticsCustomModules = _messages.MessageField('EffectiveSecurityHealthAnalyticsCustomModule', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListEventThreatDetectionCustomModulesResponse(_messages.Message):
  r"""Response message for
  SecurityCenterManagement.ListEventThreatDetectionCustomModules.

  Fields:
    eventThreatDetectionCustomModules: The list of custom modules.
    nextPageToken: A pagination token. To retrieve the next page of results,
      call the method again with this token.
  """

  eventThreatDetectionCustomModules = _messages.MessageField('EventThreatDetectionCustomModule', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('GoogleCloudLocationLocation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListSecurityCenterServicesResponse(_messages.Message):
  r"""Response message for
  SecurityCenterManagement.ListSecurityCenterServices.

  Fields:
    nextPageToken: A pagination token. To retrieve the next page of results,
      call the method again with this token.
    securityCenterServices: The list of services.
  """

  nextPageToken = _messages.StringField(1)
  securityCenterServices = _messages.MessageField('SecurityCenterService', 2, repeated=True)


class ListSecurityHealthAnalyticsCustomModulesResponse(_messages.Message):
  r"""Response message for
  SecurityCenterManagement.ListSecurityHealthAnalyticsCustomModules.

  Fields:
    nextPageToken: A pagination token. To retrieve the next page of results,
      call the method again with this token.
    securityHealthAnalyticsCustomModules: The list of Security Health
      Analytics custom modules.
  """

  nextPageToken = _messages.StringField(1)
  securityHealthAnalyticsCustomModules = _messages.MessageField('SecurityHealthAnalyticsCustomModule', 2, repeated=True)


class ModuleSettings(_messages.Message):
  r"""The settings for individual modules.

  Enums:
    EffectiveEnablementStateValueValuesEnum: Output only. The effective
      enablement state for the module at its level of the resource hierarchy.
      If the intended state is set to `INHERITED`, the effective state will be
      inherited from the enablement state of an ancestor. This state may
      differ from the intended enablement state due to billing eligibility or
      onboarding status.
    IntendedEnablementStateValueValuesEnum: Optional. The intended enablement
      state for the module at its level of the resource hierarchy.

  Fields:
    effectiveEnablementState: Output only. The effective enablement state for
      the module at its level of the resource hierarchy. If the intended state
      is set to `INHERITED`, the effective state will be inherited from the
      enablement state of an ancestor. This state may differ from the intended
      enablement state due to billing eligibility or onboarding status.
    intendedEnablementState: Optional. The intended enablement state for the
      module at its level of the resource hierarchy.
  """

  class EffectiveEnablementStateValueValuesEnum(_messages.Enum):
    r"""Output only. The effective enablement state for the module at its
    level of the resource hierarchy. If the intended state is set to
    `INHERITED`, the effective state will be inherited from the enablement
    state of an ancestor. This state may differ from the intended enablement
    state due to billing eligibility or onboarding status.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      INHERITED: State is inherited from the parent resource. Valid as an
        intended enablement state, but not as an effective enablement state.
      ENABLED: State is enabled.
      DISABLED: State is disabled.
      INGEST_ONLY: Security Command Center is configured to ingest findings
        from this service, but not to enable this service. This state
        indicates that Security Command Center is misconfigured. You can't set
        this state yourself.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    INHERITED = 1
    ENABLED = 2
    DISABLED = 3
    INGEST_ONLY = 4

  class IntendedEnablementStateValueValuesEnum(_messages.Enum):
    r"""Optional. The intended enablement state for the module at its level of
    the resource hierarchy.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      INHERITED: State is inherited from the parent resource. Valid as an
        intended enablement state, but not as an effective enablement state.
      ENABLED: State is enabled.
      DISABLED: State is disabled.
      INGEST_ONLY: Security Command Center is configured to ingest findings
        from this service, but not to enable this service. This state
        indicates that Security Command Center is misconfigured. You can't set
        this state yourself.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    INHERITED = 1
    ENABLED = 2
    DISABLED = 3
    INGEST_ONLY = 4

  effectiveEnablementState = _messages.EnumField('EffectiveEnablementStateValueValuesEnum', 1)
  intendedEnablementState = _messages.EnumField('IntendedEnablementStateValueValuesEnum', 2)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('GoogleIamV1Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class Position(_messages.Message):
  r"""A position in the uploaded text version of a module.

  Fields:
    columnNumber: The column position in the line.
    lineNumber: The line position in the text.
  """

  columnNumber = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  lineNumber = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class Property(_messages.Message):
  r"""An individual name-value pair that defines a custom source property.

  Fields:
    name: Optional. Name of the property for the custom output.
    valueExpression: Optional. The CEL expression for the custom output. A
      resource property can be specified to return the value of the property
      or a text string enclosed in quotation marks.
  """

  name = _messages.StringField(1)
  valueExpression = _messages.MessageField('Expr', 2)


class ResourceSelector(_messages.Message):
  r"""Resource for selecting resource type.

  Fields:
    resourceTypes: Optional. The resource types to run the detector on.
  """

  resourceTypes = _messages.StringField(1, repeated=True)


class SecurityCenterService(_messages.Message):
  r"""Represents a particular Security Command Center service. This includes
  settings information such as top-level enablement in addition to individual
  module settings. Service settings can be configured at the organization,
  folder, or project level. Service settings at the organization or folder
  level are inherited by those in descendant folders and projects.

  Enums:
    EffectiveEnablementStateValueValuesEnum: Output only. The effective
      enablement state for the service at its level of the resource hierarchy.
      If the intended state is set to `INHERITED`, the effective state will be
      inherited from the enablement state of an ancestor. This state may
      differ from the intended enablement state due to billing eligibility or
      onboarding status.
    IntendedEnablementStateValueValuesEnum: Optional. The intended enablement
      state for the service at its level of the resource hierarchy. A
      `DISABLED` state will override all module enablement states to
      `DISABLED`.

  Messages:
    ModulesValue: Optional. The module configurations, including the
      enablement state for the service's modules. The absence of a module in
      the map implies that its configuration is inherited from its parents.
    ServiceConfigValue: Optional. Additional service-specific configuration.
      Not all services will utilize this field.

  Fields:
    effectiveEnablementState: Output only. The effective enablement state for
      the service at its level of the resource hierarchy. If the intended
      state is set to `INHERITED`, the effective state will be inherited from
      the enablement state of an ancestor. This state may differ from the
      intended enablement state due to billing eligibility or onboarding
      status.
    intendedEnablementState: Optional. The intended enablement state for the
      service at its level of the resource hierarchy. A `DISABLED` state will
      override all module enablement states to `DISABLED`.
    modules: Optional. The module configurations, including the enablement
      state for the service's modules. The absence of a module in the map
      implies that its configuration is inherited from its parents.
    name: Identifier. The name of the service, in one of the following
      formats: * `organizations/{organization}/locations/{location}/securityCe
      nterServices/{service}` *
      `folders/{folder}/locations/{location}/securityCenterServices/{service}`
      * `projects/{project}/locations/{location}/securityCenterServices/{servi
      ce}` The following values are valid for `{service}`: * `container-
      threat-detection` * `event-threat-detection` * `security-health-
      analytics` * `vm-threat-detection` * `web-security-scanner` * `vm-
      threat-detection-aws` * `cloud-run-threat-detection`
    serviceConfig: Optional. Additional service-specific configuration. Not
      all services will utilize this field.
    updateTime: Output only. The time the service was last updated. This could
      be due to an explicit user update or due to a side effect of another
      system change, such as billing subscription expiry.
  """

  class EffectiveEnablementStateValueValuesEnum(_messages.Enum):
    r"""Output only. The effective enablement state for the service at its
    level of the resource hierarchy. If the intended state is set to
    `INHERITED`, the effective state will be inherited from the enablement
    state of an ancestor. This state may differ from the intended enablement
    state due to billing eligibility or onboarding status.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      INHERITED: State is inherited from the parent resource. Valid as an
        intended enablement state, but not as an effective enablement state.
      ENABLED: State is enabled.
      DISABLED: State is disabled.
      INGEST_ONLY: Security Command Center is configured to ingest findings
        from this service, but not to enable this service. This state
        indicates that Security Command Center is misconfigured. You can't set
        this state yourself.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    INHERITED = 1
    ENABLED = 2
    DISABLED = 3
    INGEST_ONLY = 4

  class IntendedEnablementStateValueValuesEnum(_messages.Enum):
    r"""Optional. The intended enablement state for the service at its level
    of the resource hierarchy. A `DISABLED` state will override all module
    enablement states to `DISABLED`.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      INHERITED: State is inherited from the parent resource. Valid as an
        intended enablement state, but not as an effective enablement state.
      ENABLED: State is enabled.
      DISABLED: State is disabled.
      INGEST_ONLY: Security Command Center is configured to ingest findings
        from this service, but not to enable this service. This state
        indicates that Security Command Center is misconfigured. You can't set
        this state yourself.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    INHERITED = 1
    ENABLED = 2
    DISABLED = 3
    INGEST_ONLY = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ModulesValue(_messages.Message):
    r"""Optional. The module configurations, including the enablement state
    for the service's modules. The absence of a module in the map implies that
    its configuration is inherited from its parents.

    Messages:
      AdditionalProperty: An additional property for a ModulesValue object.

    Fields:
      additionalProperties: Additional properties of type ModulesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ModulesValue object.

      Fields:
        key: Name of the additional property.
        value: A ModuleSettings attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ModuleSettings', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ServiceConfigValue(_messages.Message):
    r"""Optional. Additional service-specific configuration. Not all services
    will utilize this field.

    Messages:
      AdditionalProperty: An additional property for a ServiceConfigValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ServiceConfigValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  effectiveEnablementState = _messages.EnumField('EffectiveEnablementStateValueValuesEnum', 1)
  intendedEnablementState = _messages.EnumField('IntendedEnablementStateValueValuesEnum', 2)
  modules = _messages.MessageField('ModulesValue', 3)
  name = _messages.StringField(4)
  serviceConfig = _messages.MessageField('ServiceConfigValue', 5)
  updateTime = _messages.StringField(6)


class SecurityHealthAnalyticsCustomModule(_messages.Message):
  r"""Represents an instance of a Security Health Analytics custom module,
  including its full module name, display name, enablement state, and last
  updated time. You can create a custom module at the organization, folder, or
  project level. Custom modules that you create at the organization or folder
  level are inherited by the descendant folders and projects.

  Enums:
    EnablementStateValueValuesEnum: Optional. The enablement state of the
      custom module.

  Fields:
    ancestorModule: Output only. Specifies the organization or folder from
      which the custom module is inherited. If empty, indicates that the
      custom module was created in the organization, folder, or project in
      which you are viewing the custom module.
    customConfig: Optional. The user-specified custom configuration for the
      module.
    displayName: Optional. The display name of the Security Health Analytics
      custom module. This display name becomes the finding category for all
      findings that are returned by this custom module. The display name must
      be between 1 and 128 characters, start with a lowercase letter, and
      contain alphanumeric characters or underscores only.
    enablementState: Optional. The enablement state of the custom module.
    lastEditor: Output only. The editor that last updated the custom module.
    name: Identifier. The full resource name of the custom module, in one of
      the following formats: * `organizations/{organization}/locations/{locati
      on}/securityHealthAnalyticsCustomModules/{custom_module}` * `folders/{fo
      lder}/locations/{location}/securityHealthAnalyticsCustomModules/{custom_
      module}` * `projects/{project}/locations/{location}/securityHealthAnalyt
      icsCustomModules/{custom_module}`
    updateTime: Output only. The time at which the custom module was last
      updated.
  """

  class EnablementStateValueValuesEnum(_messages.Enum):
    r"""Optional. The enablement state of the custom module.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      ENABLED: The module is enabled at the given organization, folder, or
        project.
      DISABLED: The module is disabled at the given organization, folder, or
        project.
      INHERITED: State is inherited from an ancestor module. The module will
        either be effectively `ENABLED` or `DISABLED` based on its closest
        non-inherited ancestor module in the resource hierarchy. If you try to
        set a top-level module (a module with no parent) to the `INHERITED`
        state, you receive an `INVALID_ARGUMENT` error.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2
    INHERITED = 3

  ancestorModule = _messages.StringField(1)
  customConfig = _messages.MessageField('CustomConfig', 2)
  displayName = _messages.StringField(3)
  enablementState = _messages.EnumField('EnablementStateValueValuesEnum', 4)
  lastEditor = _messages.StringField(5)
  name = _messages.StringField(6)
  updateTime = _messages.StringField(7)


class SecuritycentermanagementFoldersLocationsEffectiveEventThreatDetectionCustomModulesGetRequest(_messages.Message):
  r"""A SecuritycentermanagementFoldersLocationsEffectiveEventThreatDetectionC
  ustomModulesGetRequest object.

  Fields:
    name: Required. The resource name of the Event Threat Detection custom
      module, in one of the following formats: * `organizations/{organization}
      /locations/{location}/effectiveEventThreatDetectionCustomModules/{custom
      _module}` * `folders/{folder}/locations/{location}/effectiveEventThreatD
      etectionCustomModules/{custom_module}` * `projects/{project}/locations/{
      location}/effectiveEventThreatDetectionCustomModules/{custom_module}`
  """

  name = _messages.StringField(1, required=True)


class SecuritycentermanagementFoldersLocationsEffectiveEventThreatDetectionCustomModulesListRequest(_messages.Message):
  r"""A SecuritycentermanagementFoldersLocationsEffectiveEventThreatDetectionC
  ustomModulesListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return in a single
      response. Default is 10, minimum is 1, maximum is 1000.
    pageToken: Optional. A pagination token returned from a previous request.
      Provide this token to retrieve the next page of results. When
      paginating, the rest of the request must match the request that
      generated the page token.
    parent: Required. Name of parent to list effective custom modules, in one
      of the following formats: *
      `organizations/{organization}/locations/{location}` *
      `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycentermanagementFoldersLocationsEffectiveSecurityHealthAnalyticsCustomModulesGetRequest(_messages.Message):
  r"""A SecuritycentermanagementFoldersLocationsEffectiveSecurityHealthAnalyti
  csCustomModulesGetRequest object.

  Fields:
    name: Required. The full resource name of the custom module, specified in
      one of the following formats: * `organizations/organization/{location}/e
      ffectiveSecurityHealthAnalyticsCustomModules/{custom_module}` * `folders
      /folder/{location}/effectiveSecurityHealthAnalyticsCustomModules/{custom
      _module}` * `projects/project/{location}/effectiveSecurityHealthAnalytic
      sCustomModules/{custom_module}`
  """

  name = _messages.StringField(1, required=True)


class SecuritycentermanagementFoldersLocationsEffectiveSecurityHealthAnalyticsCustomModulesListRequest(_messages.Message):
  r"""A SecuritycentermanagementFoldersLocationsEffectiveSecurityHealthAnalyti
  csCustomModulesListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return in a single
      response. Default is 10, minimum is 1, maximum is 1000.
    pageToken: Optional. A pagination token returned from a previous request.
      Provide this token to retrieve the next page of results. When
      paginating, the rest of the request must match the request that
      generated the page token.
    parent: Required. Name of parent to list effective custom modules, in one
      of the following formats: *
      `organizations/{organization}/locations/{location}` *
      `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModulesCreateRequest(_messages.Message):
  r"""A SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModu
  lesCreateRequest object.

  Fields:
    eventThreatDetectionCustomModule: A EventThreatDetectionCustomModule
      resource to be passed as the request body.
    parent: Required. Name of parent for the module, in one of the following
      formats: * `organizations/{organization}/locations/{location}` *
      `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
    validateOnly: Optional. When set to `true`, the request will be validated
      (including IAM checks), but no module will be created. An `OK` response
      indicates that the request is valid, while an error response indicates
      that the request is invalid. If the request is valid, a subsequent
      request to create the module could still fail for one of the following
      reasons: * The state of your cloud resources changed; for example, you
      lost a required IAM permission * An error occurred during creation of
      the module Defaults to `false`.
  """

  eventThreatDetectionCustomModule = _messages.MessageField('EventThreatDetectionCustomModule', 1)
  parent = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModulesDeleteRequest(_messages.Message):
  r"""A SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModu
  lesDeleteRequest object.

  Fields:
    name: Required. The resource name of the Event Threat Detection custom
      module, in one of the following formats: * `organizations/{organization}
      /locations/{location}/eventThreatDetectionCustomModules/{custom_module}`
      * `folders/{folder}/locations/{location}/eventThreatDetectionCustomModul
      es/{custom_module}` * `projects/{project}/locations/{location}/eventThre
      atDetectionCustomModules/{custom_module}`
    validateOnly: Optional. When set to `true`, the request will be validated
      (including IAM checks), but no module will be deleted. An `OK` response
      indicates that the request is valid, while an error response indicates
      that the request is invalid. If the request is valid, a subsequent
      request to delete the module could still fail for one of the following
      reasons: * The state of your cloud resources changed; for example, you
      lost a required IAM permission * An error occurred during creation of
      the module Defaults to `false`.
  """

  name = _messages.StringField(1, required=True)
  validateOnly = _messages.BooleanField(2)


class SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModulesGetRequest(_messages.Message):
  r"""A SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModu
  lesGetRequest object.

  Fields:
    name: Required. The resource name of the Event Threat Detection custom
      module, in one of the following formats: * `organizations/{organization}
      /locations/{location}/eventThreatDetectionCustomModules/{custom_module}`
      * `folders/{folder}/locations/{location}/eventThreatDetectionCustomModul
      es/{custom_module}` * `projects/{project}/locations/{location}/eventThre
      atDetectionCustomModules/{custom_module}`
  """

  name = _messages.StringField(1, required=True)


class SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModulesListDescendantRequest(_messages.Message):
  r"""A SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModu
  lesListDescendantRequest object.

  Fields:
    pageSize: Optional. The maximum number of modules to return. The service
      may return fewer than this value. If unspecified, at most 10 configs
      will be returned. The maximum value is 1000; values above 1000 will be
      coerced to 1000.
    pageToken: Optional. A pagination token returned from a previous request.
      Provide this token to retrieve the next page of results. When
      paginating, the rest of the request must match the request that
      generated the page token.
    parent: Required. Name of parent to list custom modules, in one of the
      following formats: * `organizations/{organization}/locations/{location}`
      * `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModulesListRequest(_messages.Message):
  r"""A SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModu
  lesListRequest object.

  Fields:
    pageSize: Optional. The maximum number of modules to return. The service
      may return fewer than this value. If unspecified, at most 10 modules
      will be returned. The maximum value is 1000; values above 1000 will be
      coerced to 1000.
    pageToken: Optional. A pagination token returned from a previous request.
      Provide this token to retrieve the next page of results. When
      paginating, the rest of the request must match the request that
      generated the page token.
    parent: Required. Name of parent to list custom modules, in one of the
      following formats: * `organizations/{organization}/locations/{location}`
      * `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModulesPatchRequest(_messages.Message):
  r"""A SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModu
  lesPatchRequest object.

  Fields:
    eventThreatDetectionCustomModule: A EventThreatDetectionCustomModule
      resource to be passed as the request body.
    name: Identifier. The resource name of the Event Threat Detection custom
      module, in one of the following formats: * `organizations/{organization}
      /locations/{location}/eventThreatDetectionCustomModules/{custom_module}`
      * `folders/{folder}/locations/{location}/eventThreatDetectionCustomModul
      es/{custom_module}` * `projects/{project}/locations/{location}/eventThre
      atDetectionCustomModules/{custom_module}`
    updateMask: Required. The fields to update. If omitted, then all fields
      are updated.
    validateOnly: Optional. When set to `true`, the request will be validated
      (including IAM checks), but no module will be updated. An `OK` response
      indicates that the request is valid, while an error response indicates
      that the request is invalid. If the request is valid, a subsequent
      request to update the module could still fail for one of the following
      reasons: * The state of your cloud resources changed; for example, you
      lost a required IAM permission * An error occurred during creation of
      the module Defaults to `false`.
  """

  eventThreatDetectionCustomModule = _messages.MessageField('EventThreatDetectionCustomModule', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModulesValidateRequest(_messages.Message):
  r"""A SecuritycentermanagementFoldersLocationsEventThreatDetectionCustomModu
  lesValidateRequest object.

  Fields:
    parent: Required. Resource name of the parent to validate the custom
      modules under, in one of the following formats: *
      `organizations/{organization}/locations/{location}`
    validateEventThreatDetectionCustomModuleRequest: A
      ValidateEventThreatDetectionCustomModuleRequest resource to be passed as
      the request body.
  """

  parent = _messages.StringField(1, required=True)
  validateEventThreatDetectionCustomModuleRequest = _messages.MessageField('ValidateEventThreatDetectionCustomModuleRequest', 2)


class SecuritycentermanagementFoldersLocationsSecurityCenterServicesGetRequest(_messages.Message):
  r"""A
  SecuritycentermanagementFoldersLocationsSecurityCenterServicesGetRequest
  object.

  Fields:
    name: Required. The Security Command Center service to retrieve, in one of
      the following formats: * organizations/{organization}/locations/{locatio
      n}/securityCenterServices/{service} *
      folders/{folder}/locations/{location}/securityCenterServices/{service} *
      projects/{project}/locations/{location}/securityCenterServices/{service}
      The following values are valid for `{service}`: * `container-threat-
      detection` * `event-threat-detection` * `security-health-analytics` *
      `vm-threat-detection` * `web-security-scanner` * `vm-threat-detection-
      aws` * `cloud-run-threat-detection`
    showEligibleModulesOnly: Optional. Set to `true` to show only modules that
      are in scope. By default, all modules are shown.
  """

  name = _messages.StringField(1, required=True)
  showEligibleModulesOnly = _messages.BooleanField(2)


class SecuritycentermanagementFoldersLocationsSecurityCenterServicesListRequest(_messages.Message):
  r"""A
  SecuritycentermanagementFoldersLocationsSecurityCenterServicesListRequest
  object.

  Fields:
    pageSize: Optional. The maximum number of results to return in a single
      response. Default is 10, minimum is 1, maximum is 1000.
    pageToken: Optional. A pagination token returned from a previous request.
      Provide this token to retrieve the next page of results. When
      paginating, the rest of the request must match the request that
      generated the page token.
    parent: Required. The name of the parent to list Security Command Center
      services, in one of the following formats: *
      `organizations/{organization}/locations/{location}` *
      `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
    showEligibleModulesOnly: Flag that, when set, is used to filter the module
      settings that are shown. The default setting is that all modules are
      shown.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  showEligibleModulesOnly = _messages.BooleanField(4)


class SecuritycentermanagementFoldersLocationsSecurityCenterServicesPatchRequest(_messages.Message):
  r"""A
  SecuritycentermanagementFoldersLocationsSecurityCenterServicesPatchRequest
  object.

  Fields:
    name: Identifier. The name of the service, in one of the following
      formats: * `organizations/{organization}/locations/{location}/securityCe
      nterServices/{service}` *
      `folders/{folder}/locations/{location}/securityCenterServices/{service}`
      * `projects/{project}/locations/{location}/securityCenterServices/{servi
      ce}` The following values are valid for `{service}`: * `container-
      threat-detection` * `event-threat-detection` * `security-health-
      analytics` * `vm-threat-detection` * `web-security-scanner` * `vm-
      threat-detection-aws` * `cloud-run-threat-detection`
    securityCenterService: A SecurityCenterService resource to be passed as
      the request body.
    updateMask: Optional. Required. The fields to update. Accepts the
      following values: * `intended_enablement_state` * `modules` If omitted,
      then all eligible fields are updated.
    validateOnly: Optional. When set to `true`, the request will be validated
      (including IAM checks), but no service will be updated. An `OK` response
      indicates that the request is valid, while an error response indicates
      that the request is invalid. If the request is valid, a subsequent
      request to update the service could still fail for one of the following
      reasons: * The state of your cloud resources changed; for example, you
      lost a required IAM permission * An error occurred during update of the
      service Defaults to `false`.
  """

  name = _messages.StringField(1, required=True)
  securityCenterService = _messages.MessageField('SecurityCenterService', 2)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomModulesCreateRequest(_messages.Message):
  r"""A SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomM
  odulesCreateRequest object.

  Fields:
    parent: Required. Name of the parent organization, folder, or project of
      the module, in one of the following formats: *
      `organizations/{organization}/locations/{location}` *
      `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
    securityHealthAnalyticsCustomModule: A SecurityHealthAnalyticsCustomModule
      resource to be passed as the request body.
    validateOnly: Optional. When set to `true`, the request will be validated
      (including IAM checks), but no module will be created. An `OK` response
      indicates that the request is valid, while an error response indicates
      that the request is invalid. If the request is valid, a subsequent
      request to create the module could still fail for one of the following
      reasons: * The state of your cloud resources changed; for example, you
      lost a required IAM permission * An error occurred during creation of
      the module Defaults to `false`.
  """

  parent = _messages.StringField(1, required=True)
  securityHealthAnalyticsCustomModule = _messages.MessageField('SecurityHealthAnalyticsCustomModule', 2)
  validateOnly = _messages.BooleanField(3)


class SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomModulesDeleteRequest(_messages.Message):
  r"""A SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomM
  odulesDeleteRequest object.

  Fields:
    name: Required. The resource name of the SHA custom module, in one of the
      following formats: * `organizations/{organization}/locations/{location}/
      securityHealthAnalyticsCustomModules/{custom_module}` * `folders/{folder
      }/locations/{location}/securityHealthAnalyticsCustomModules/{custom_modu
      le}` * `projects/{project}/locations/{location}/securityHealthAnalyticsC
      ustomModules/{custom_module}`
    validateOnly: Optional. When set to `true`, the request will be validated
      (including IAM checks), but no module will be deleted. An `OK` response
      indicates that the request is valid, while an error response indicates
      that the request is invalid. If the request is valid, a subsequent
      request to delete the module could still fail for one of the following
      reasons: * The state of your cloud resources changed; for example, you
      lost a required IAM permission * An error occurred during deletion of
      the module Defaults to `false`.
  """

  name = _messages.StringField(1, required=True)
  validateOnly = _messages.BooleanField(2)


class SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomModulesGetRequest(_messages.Message):
  r"""A SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomM
  odulesGetRequest object.

  Fields:
    name: Required. Name of the resource, in the format `projects/{project}/lo
      cations/{location}/securityHealthAnalyticsCustomModules/{custom_module}`
      .
  """

  name = _messages.StringField(1, required=True)


class SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomModulesListDescendantRequest(_messages.Message):
  r"""A SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomM
  odulesListDescendantRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return in a single
      response. Default is 10, minimum is 1, maximum is 1000.
    pageToken: Optional. A pagination token returned from a previous request.
      Provide this token to retrieve the next page of results. When
      paginating, the rest of the request must match the request that
      generated the page token.
    parent: Required. Name of the parent organization, folder, or project in
      which to list custom modules, in one of the following formats: *
      `organizations/{organization}/locations/{location}` *
      `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomModulesListRequest(_messages.Message):
  r"""A SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomM
  odulesListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return in a single
      response. Default is 10, minimum is 1, maximum is 1000.
    pageToken: Optional. A pagination token returned from a previous request.
      Provide this token to retrieve the next page of results. When
      paginating, the rest of the request must match the request that
      generated the page token.
    parent: Required. Name of the parent organization, folder, or project in
      which to list custom modules, in one of the following formats: *
      `organizations/{organization}/locations/{location}` *
      `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomModulesPatchRequest(_messages.Message):
  r"""A SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomM
  odulesPatchRequest object.

  Fields:
    name: Identifier. The full resource name of the custom module, in one of
      the following formats: * `organizations/{organization}/locations/{locati
      on}/securityHealthAnalyticsCustomModules/{custom_module}` * `folders/{fo
      lder}/locations/{location}/securityHealthAnalyticsCustomModules/{custom_
      module}` * `projects/{project}/locations/{location}/securityHealthAnalyt
      icsCustomModules/{custom_module}`
    securityHealthAnalyticsCustomModule: A SecurityHealthAnalyticsCustomModule
      resource to be passed as the request body.
    updateMask: Optional. Required. The fields to update. The following values
      are valid: * `custom_config` * `enablement_state` If you omit this field
      or set it to the wildcard value `*`, then all eligible fields are
      updated.
    validateOnly: Optional. When set to `true`, the request will be validated
      (including IAM checks), but no module will be updated. An `OK` response
      indicates that the request is valid, while an error response indicates
      that the request is invalid. If the request is valid, a subsequent
      request to update the module could still fail for one of the following
      reasons: * The state of your cloud resources changed; for example, you
      lost a required IAM permission * An error occurred during creation of
      the module Defaults to `false`.
  """

  name = _messages.StringField(1, required=True)
  securityHealthAnalyticsCustomModule = _messages.MessageField('SecurityHealthAnalyticsCustomModule', 2)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomModulesSimulateRequest(_messages.Message):
  r"""A SecuritycentermanagementFoldersLocationsSecurityHealthAnalyticsCustomM
  odulesSimulateRequest object.

  Fields:
    parent: Required. The relative resource name of the organization, project,
      or folder. For more information about relative resource names, see
      [AIP-122: Resource names](https://google.aip.dev/122). Example:
      `organizations/{organization_id}`.
    simulateSecurityHealthAnalyticsCustomModuleRequest: A
      SimulateSecurityHealthAnalyticsCustomModuleRequest resource to be passed
      as the request body.
  """

  parent = _messages.StringField(1, required=True)
  simulateSecurityHealthAnalyticsCustomModuleRequest = _messages.MessageField('SimulateSecurityHealthAnalyticsCustomModuleRequest', 2)


class SecuritycentermanagementOrganizationsLocationsEffectiveEventThreatDetectionCustomModulesGetRequest(_messages.Message):
  r"""A SecuritycentermanagementOrganizationsLocationsEffectiveEventThreatDete
  ctionCustomModulesGetRequest object.

  Fields:
    name: Required. The resource name of the Event Threat Detection custom
      module, in one of the following formats: * `organizations/{organization}
      /locations/{location}/effectiveEventThreatDetectionCustomModules/{custom
      _module}` * `folders/{folder}/locations/{location}/effectiveEventThreatD
      etectionCustomModules/{custom_module}` * `projects/{project}/locations/{
      location}/effectiveEventThreatDetectionCustomModules/{custom_module}`
  """

  name = _messages.StringField(1, required=True)


class SecuritycentermanagementOrganizationsLocationsEffectiveEventThreatDetectionCustomModulesListRequest(_messages.Message):
  r"""A SecuritycentermanagementOrganizationsLocationsEffectiveEventThreatDete
  ctionCustomModulesListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return in a single
      response. Default is 10, minimum is 1, maximum is 1000.
    pageToken: Optional. A pagination token returned from a previous request.
      Provide this token to retrieve the next page of results. When
      paginating, the rest of the request must match the request that
      generated the page token.
    parent: Required. Name of parent to list effective custom modules, in one
      of the following formats: *
      `organizations/{organization}/locations/{location}` *
      `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycentermanagementOrganizationsLocationsEffectiveSecurityHealthAnalyticsCustomModulesGetRequest(_messages.Message):
  r"""A SecuritycentermanagementOrganizationsLocationsEffectiveSecurityHealthA
  nalyticsCustomModulesGetRequest object.

  Fields:
    name: Required. The full resource name of the custom module, specified in
      one of the following formats: * `organizations/organization/{location}/e
      ffectiveSecurityHealthAnalyticsCustomModules/{custom_module}` * `folders
      /folder/{location}/effectiveSecurityHealthAnalyticsCustomModules/{custom
      _module}` * `projects/project/{location}/effectiveSecurityHealthAnalytic
      sCustomModules/{custom_module}`
  """

  name = _messages.StringField(1, required=True)


class SecuritycentermanagementOrganizationsLocationsEffectiveSecurityHealthAnalyticsCustomModulesListRequest(_messages.Message):
  r"""A SecuritycentermanagementOrganizationsLocationsEffectiveSecurityHealthA
  nalyticsCustomModulesListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return in a single
      response. Default is 10, minimum is 1, maximum is 1000.
    pageToken: Optional. A pagination token returned from a previous request.
      Provide this token to retrieve the next page of results. When
      paginating, the rest of the request must match the request that
      generated the page token.
    parent: Required. Name of parent to list effective custom modules, in one
      of the following formats: *
      `organizations/{organization}/locations/{location}` *
      `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCustomModulesCreateRequest(_messages.Message):
  r"""A SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCust
  omModulesCreateRequest object.

  Fields:
    eventThreatDetectionCustomModule: A EventThreatDetectionCustomModule
      resource to be passed as the request body.
    parent: Required. Name of parent for the module, in one of the following
      formats: * `organizations/{organization}/locations/{location}` *
      `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
    validateOnly: Optional. When set to `true`, the request will be validated
      (including IAM checks), but no module will be created. An `OK` response
      indicates that the request is valid, while an error response indicates
      that the request is invalid. If the request is valid, a subsequent
      request to create the module could still fail for one of the following
      reasons: * The state of your cloud resources changed; for example, you
      lost a required IAM permission * An error occurred during creation of
      the module Defaults to `false`.
  """

  eventThreatDetectionCustomModule = _messages.MessageField('EventThreatDetectionCustomModule', 1)
  parent = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCustomModulesDeleteRequest(_messages.Message):
  r"""A SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCust
  omModulesDeleteRequest object.

  Fields:
    name: Required. The resource name of the Event Threat Detection custom
      module, in one of the following formats: * `organizations/{organization}
      /locations/{location}/eventThreatDetectionCustomModules/{custom_module}`
      * `folders/{folder}/locations/{location}/eventThreatDetectionCustomModul
      es/{custom_module}` * `projects/{project}/locations/{location}/eventThre
      atDetectionCustomModules/{custom_module}`
    validateOnly: Optional. When set to `true`, the request will be validated
      (including IAM checks), but no module will be deleted. An `OK` response
      indicates that the request is valid, while an error response indicates
      that the request is invalid. If the request is valid, a subsequent
      request to delete the module could still fail for one of the following
      reasons: * The state of your cloud resources changed; for example, you
      lost a required IAM permission * An error occurred during creation of
      the module Defaults to `false`.
  """

  name = _messages.StringField(1, required=True)
  validateOnly = _messages.BooleanField(2)


class SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCustomModulesGetRequest(_messages.Message):
  r"""A SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCust
  omModulesGetRequest object.

  Fields:
    name: Required. The resource name of the Event Threat Detection custom
      module, in one of the following formats: * `organizations/{organization}
      /locations/{location}/eventThreatDetectionCustomModules/{custom_module}`
      * `folders/{folder}/locations/{location}/eventThreatDetectionCustomModul
      es/{custom_module}` * `projects/{project}/locations/{location}/eventThre
      atDetectionCustomModules/{custom_module}`
  """

  name = _messages.StringField(1, required=True)


class SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCustomModulesListDescendantRequest(_messages.Message):
  r"""A SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCust
  omModulesListDescendantRequest object.

  Fields:
    pageSize: Optional. The maximum number of modules to return. The service
      may return fewer than this value. If unspecified, at most 10 configs
      will be returned. The maximum value is 1000; values above 1000 will be
      coerced to 1000.
    pageToken: Optional. A pagination token returned from a previous request.
      Provide this token to retrieve the next page of results. When
      paginating, the rest of the request must match the request that
      generated the page token.
    parent: Required. Name of parent to list custom modules, in one of the
      following formats: * `organizations/{organization}/locations/{location}`
      * `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCustomModulesListRequest(_messages.Message):
  r"""A SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCust
  omModulesListRequest object.

  Fields:
    pageSize: Optional. The maximum number of modules to return. The service
      may return fewer than this value. If unspecified, at most 10 modules
      will be returned. The maximum value is 1000; values above 1000 will be
      coerced to 1000.
    pageToken: Optional. A pagination token returned from a previous request.
      Provide this token to retrieve the next page of results. When
      paginating, the rest of the request must match the request that
      generated the page token.
    parent: Required. Name of parent to list custom modules, in one of the
      following formats: * `organizations/{organization}/locations/{location}`
      * `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCustomModulesPatchRequest(_messages.Message):
  r"""A SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCust
  omModulesPatchRequest object.

  Fields:
    eventThreatDetectionCustomModule: A EventThreatDetectionCustomModule
      resource to be passed as the request body.
    name: Identifier. The resource name of the Event Threat Detection custom
      module, in one of the following formats: * `organizations/{organization}
      /locations/{location}/eventThreatDetectionCustomModules/{custom_module}`
      * `folders/{folder}/locations/{location}/eventThreatDetectionCustomModul
      es/{custom_module}` * `projects/{project}/locations/{location}/eventThre
      atDetectionCustomModules/{custom_module}`
    updateMask: Required. The fields to update. If omitted, then all fields
      are updated.
    validateOnly: Optional. When set to `true`, the request will be validated
      (including IAM checks), but no module will be updated. An `OK` response
      indicates that the request is valid, while an error response indicates
      that the request is invalid. If the request is valid, a subsequent
      request to update the module could still fail for one of the following
      reasons: * The state of your cloud resources changed; for example, you
      lost a required IAM permission * An error occurred during creation of
      the module Defaults to `false`.
  """

  eventThreatDetectionCustomModule = _messages.MessageField('EventThreatDetectionCustomModule', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCustomModulesValidateRequest(_messages.Message):
  r"""A SecuritycentermanagementOrganizationsLocationsEventThreatDetectionCust
  omModulesValidateRequest object.

  Fields:
    parent: Required. Resource name of the parent to validate the custom
      modules under, in one of the following formats: *
      `organizations/{organization}/locations/{location}`
    validateEventThreatDetectionCustomModuleRequest: A
      ValidateEventThreatDetectionCustomModuleRequest resource to be passed as
      the request body.
  """

  parent = _messages.StringField(1, required=True)
  validateEventThreatDetectionCustomModuleRequest = _messages.MessageField('ValidateEventThreatDetectionCustomModuleRequest', 2)


class SecuritycentermanagementOrganizationsLocationsGetBillingMetadataRequest(_messages.Message):
  r"""A
  SecuritycentermanagementOrganizationsLocationsGetBillingMetadataRequest
  object.

  Fields:
    name: Required. The resource to look up the billing tier for, in the
      format
      `organizations/{organization}/locations/{location}/billingMetadata` or
      `projects/{project}/locations/{location}/billingMetadata`.
  """

  name = _messages.StringField(1, required=True)


class SecuritycentermanagementOrganizationsLocationsSecurityCenterServicesGetRequest(_messages.Message):
  r"""A SecuritycentermanagementOrganizationsLocationsSecurityCenterServicesGe
  tRequest object.

  Fields:
    name: Required. The Security Command Center service to retrieve, in one of
      the following formats: * organizations/{organization}/locations/{locatio
      n}/securityCenterServices/{service} *
      folders/{folder}/locations/{location}/securityCenterServices/{service} *
      projects/{project}/locations/{location}/securityCenterServices/{service}
      The following values are valid for `{service}`: * `container-threat-
      detection` * `event-threat-detection` * `security-health-analytics` *
      `vm-threat-detection` * `web-security-scanner` * `vm-threat-detection-
      aws` * `cloud-run-threat-detection`
    showEligibleModulesOnly: Optional. Set to `true` to show only modules that
      are in scope. By default, all modules are shown.
  """

  name = _messages.StringField(1, required=True)
  showEligibleModulesOnly = _messages.BooleanField(2)


class SecuritycentermanagementOrganizationsLocationsSecurityCenterServicesListRequest(_messages.Message):
  r"""A SecuritycentermanagementOrganizationsLocationsSecurityCenterServicesLi
  stRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return in a single
      response. Default is 10, minimum is 1, maximum is 1000.
    pageToken: Optional. A pagination token returned from a previous request.
      Provide this token to retrieve the next page of results. When
      paginating, the rest of the request must match the request that
      generated the page token.
    parent: Required. The name of the parent to list Security Command Center
      services, in one of the following formats: *
      `organizations/{organization}/locations/{location}` *
      `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
    showEligibleModulesOnly: Flag that, when set, is used to filter the module
      settings that are shown. The default setting is that all modules are
      shown.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  showEligibleModulesOnly = _messages.BooleanField(4)


class SecuritycentermanagementOrganizationsLocationsSecurityCenterServicesPatchRequest(_messages.Message):
  r"""A SecuritycentermanagementOrganizationsLocationsSecurityCenterServicesPa
  tchRequest object.

  Fields:
    name: Identifier. The name of the service, in one of the following
      formats: * `organizations/{organization}/locations/{location}/securityCe
      nterServices/{service}` *
      `folders/{folder}/locations/{location}/securityCenterServices/{service}`
      * `projects/{project}/locations/{location}/securityCenterServices/{servi
      ce}` The following values are valid for `{service}`: * `container-
      threat-detection` * `event-threat-detection` * `security-health-
      analytics` * `vm-threat-detection` * `web-security-scanner` * `vm-
      threat-detection-aws` * `cloud-run-threat-detection`
    securityCenterService: A SecurityCenterService resource to be passed as
      the request body.
    updateMask: Optional. Required. The fields to update. Accepts the
      following values: * `intended_enablement_state` * `modules` If omitted,
      then all eligible fields are updated.
    validateOnly: Optional. When set to `true`, the request will be validated
      (including IAM checks), but no service will be updated. An `OK` response
      indicates that the request is valid, while an error response indicates
      that the request is invalid. If the request is valid, a subsequent
      request to update the service could still fail for one of the following
      reasons: * The state of your cloud resources changed; for example, you
      lost a required IAM permission * An error occurred during update of the
      service Defaults to `false`.
  """

  name = _messages.StringField(1, required=True)
  securityCenterService = _messages.MessageField('SecurityCenterService', 2)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsCustomModulesCreateRequest(_messages.Message):
  r"""A SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsC
  ustomModulesCreateRequest object.

  Fields:
    parent: Required. Name of the parent organization, folder, or project of
      the module, in one of the following formats: *
      `organizations/{organization}/locations/{location}` *
      `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
    securityHealthAnalyticsCustomModule: A SecurityHealthAnalyticsCustomModule
      resource to be passed as the request body.
    validateOnly: Optional. When set to `true`, the request will be validated
      (including IAM checks), but no module will be created. An `OK` response
      indicates that the request is valid, while an error response indicates
      that the request is invalid. If the request is valid, a subsequent
      request to create the module could still fail for one of the following
      reasons: * The state of your cloud resources changed; for example, you
      lost a required IAM permission * An error occurred during creation of
      the module Defaults to `false`.
  """

  parent = _messages.StringField(1, required=True)
  securityHealthAnalyticsCustomModule = _messages.MessageField('SecurityHealthAnalyticsCustomModule', 2)
  validateOnly = _messages.BooleanField(3)


class SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsCustomModulesDeleteRequest(_messages.Message):
  r"""A SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsC
  ustomModulesDeleteRequest object.

  Fields:
    name: Required. The resource name of the SHA custom module, in one of the
      following formats: * `organizations/{organization}/locations/{location}/
      securityHealthAnalyticsCustomModules/{custom_module}` * `folders/{folder
      }/locations/{location}/securityHealthAnalyticsCustomModules/{custom_modu
      le}` * `projects/{project}/locations/{location}/securityHealthAnalyticsC
      ustomModules/{custom_module}`
    validateOnly: Optional. When set to `true`, the request will be validated
      (including IAM checks), but no module will be deleted. An `OK` response
      indicates that the request is valid, while an error response indicates
      that the request is invalid. If the request is valid, a subsequent
      request to delete the module could still fail for one of the following
      reasons: * The state of your cloud resources changed; for example, you
      lost a required IAM permission * An error occurred during deletion of
      the module Defaults to `false`.
  """

  name = _messages.StringField(1, required=True)
  validateOnly = _messages.BooleanField(2)


class SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsCustomModulesGetRequest(_messages.Message):
  r"""A SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsC
  ustomModulesGetRequest object.

  Fields:
    name: Required. Name of the resource, in the format `projects/{project}/lo
      cations/{location}/securityHealthAnalyticsCustomModules/{custom_module}`
      .
  """

  name = _messages.StringField(1, required=True)


class SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsCustomModulesListDescendantRequest(_messages.Message):
  r"""A SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsC
  ustomModulesListDescendantRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return in a single
      response. Default is 10, minimum is 1, maximum is 1000.
    pageToken: Optional. A pagination token returned from a previous request.
      Provide this token to retrieve the next page of results. When
      paginating, the rest of the request must match the request that
      generated the page token.
    parent: Required. Name of the parent organization, folder, or project in
      which to list custom modules, in one of the following formats: *
      `organizations/{organization}/locations/{location}` *
      `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsCustomModulesListRequest(_messages.Message):
  r"""A SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsC
  ustomModulesListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return in a single
      response. Default is 10, minimum is 1, maximum is 1000.
    pageToken: Optional. A pagination token returned from a previous request.
      Provide this token to retrieve the next page of results. When
      paginating, the rest of the request must match the request that
      generated the page token.
    parent: Required. Name of the parent organization, folder, or project in
      which to list custom modules, in one of the following formats: *
      `organizations/{organization}/locations/{location}` *
      `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsCustomModulesPatchRequest(_messages.Message):
  r"""A SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsC
  ustomModulesPatchRequest object.

  Fields:
    name: Identifier. The full resource name of the custom module, in one of
      the following formats: * `organizations/{organization}/locations/{locati
      on}/securityHealthAnalyticsCustomModules/{custom_module}` * `folders/{fo
      lder}/locations/{location}/securityHealthAnalyticsCustomModules/{custom_
      module}` * `projects/{project}/locations/{location}/securityHealthAnalyt
      icsCustomModules/{custom_module}`
    securityHealthAnalyticsCustomModule: A SecurityHealthAnalyticsCustomModule
      resource to be passed as the request body.
    updateMask: Optional. Required. The fields to update. The following values
      are valid: * `custom_config` * `enablement_state` If you omit this field
      or set it to the wildcard value `*`, then all eligible fields are
      updated.
    validateOnly: Optional. When set to `true`, the request will be validated
      (including IAM checks), but no module will be updated. An `OK` response
      indicates that the request is valid, while an error response indicates
      that the request is invalid. If the request is valid, a subsequent
      request to update the module could still fail for one of the following
      reasons: * The state of your cloud resources changed; for example, you
      lost a required IAM permission * An error occurred during creation of
      the module Defaults to `false`.
  """

  name = _messages.StringField(1, required=True)
  securityHealthAnalyticsCustomModule = _messages.MessageField('SecurityHealthAnalyticsCustomModule', 2)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsCustomModulesSimulateRequest(_messages.Message):
  r"""A SecuritycentermanagementOrganizationsLocationsSecurityHealthAnalyticsC
  ustomModulesSimulateRequest object.

  Fields:
    parent: Required. The relative resource name of the organization, project,
      or folder. For more information about relative resource names, see
      [AIP-122: Resource names](https://google.aip.dev/122). Example:
      `organizations/{organization_id}`.
    simulateSecurityHealthAnalyticsCustomModuleRequest: A
      SimulateSecurityHealthAnalyticsCustomModuleRequest resource to be passed
      as the request body.
  """

  parent = _messages.StringField(1, required=True)
  simulateSecurityHealthAnalyticsCustomModuleRequest = _messages.MessageField('SimulateSecurityHealthAnalyticsCustomModuleRequest', 2)


class SecuritycentermanagementProjectsLocationsEffectiveEventThreatDetectionCustomModulesGetRequest(_messages.Message):
  r"""A SecuritycentermanagementProjectsLocationsEffectiveEventThreatDetection
  CustomModulesGetRequest object.

  Fields:
    name: Required. The resource name of the Event Threat Detection custom
      module, in one of the following formats: * `organizations/{organization}
      /locations/{location}/effectiveEventThreatDetectionCustomModules/{custom
      _module}` * `folders/{folder}/locations/{location}/effectiveEventThreatD
      etectionCustomModules/{custom_module}` * `projects/{project}/locations/{
      location}/effectiveEventThreatDetectionCustomModules/{custom_module}`
  """

  name = _messages.StringField(1, required=True)


class SecuritycentermanagementProjectsLocationsEffectiveEventThreatDetectionCustomModulesListRequest(_messages.Message):
  r"""A SecuritycentermanagementProjectsLocationsEffectiveEventThreatDetection
  CustomModulesListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return in a single
      response. Default is 10, minimum is 1, maximum is 1000.
    pageToken: Optional. A pagination token returned from a previous request.
      Provide this token to retrieve the next page of results. When
      paginating, the rest of the request must match the request that
      generated the page token.
    parent: Required. Name of parent to list effective custom modules, in one
      of the following formats: *
      `organizations/{organization}/locations/{location}` *
      `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycentermanagementProjectsLocationsEffectiveSecurityHealthAnalyticsCustomModulesGetRequest(_messages.Message):
  r"""A SecuritycentermanagementProjectsLocationsEffectiveSecurityHealthAnalyt
  icsCustomModulesGetRequest object.

  Fields:
    name: Required. The full resource name of the custom module, specified in
      one of the following formats: * `organizations/organization/{location}/e
      ffectiveSecurityHealthAnalyticsCustomModules/{custom_module}` * `folders
      /folder/{location}/effectiveSecurityHealthAnalyticsCustomModules/{custom
      _module}` * `projects/project/{location}/effectiveSecurityHealthAnalytic
      sCustomModules/{custom_module}`
  """

  name = _messages.StringField(1, required=True)


class SecuritycentermanagementProjectsLocationsEffectiveSecurityHealthAnalyticsCustomModulesListRequest(_messages.Message):
  r"""A SecuritycentermanagementProjectsLocationsEffectiveSecurityHealthAnalyt
  icsCustomModulesListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return in a single
      response. Default is 10, minimum is 1, maximum is 1000.
    pageToken: Optional. A pagination token returned from a previous request.
      Provide this token to retrieve the next page of results. When
      paginating, the rest of the request must match the request that
      generated the page token.
    parent: Required. Name of parent to list effective custom modules, in one
      of the following formats: *
      `organizations/{organization}/locations/{location}` *
      `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomModulesCreateRequest(_messages.Message):
  r"""A SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomMod
  ulesCreateRequest object.

  Fields:
    eventThreatDetectionCustomModule: A EventThreatDetectionCustomModule
      resource to be passed as the request body.
    parent: Required. Name of parent for the module, in one of the following
      formats: * `organizations/{organization}/locations/{location}` *
      `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
    validateOnly: Optional. When set to `true`, the request will be validated
      (including IAM checks), but no module will be created. An `OK` response
      indicates that the request is valid, while an error response indicates
      that the request is invalid. If the request is valid, a subsequent
      request to create the module could still fail for one of the following
      reasons: * The state of your cloud resources changed; for example, you
      lost a required IAM permission * An error occurred during creation of
      the module Defaults to `false`.
  """

  eventThreatDetectionCustomModule = _messages.MessageField('EventThreatDetectionCustomModule', 1)
  parent = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomModulesDeleteRequest(_messages.Message):
  r"""A SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomMod
  ulesDeleteRequest object.

  Fields:
    name: Required. The resource name of the Event Threat Detection custom
      module, in one of the following formats: * `organizations/{organization}
      /locations/{location}/eventThreatDetectionCustomModules/{custom_module}`
      * `folders/{folder}/locations/{location}/eventThreatDetectionCustomModul
      es/{custom_module}` * `projects/{project}/locations/{location}/eventThre
      atDetectionCustomModules/{custom_module}`
    validateOnly: Optional. When set to `true`, the request will be validated
      (including IAM checks), but no module will be deleted. An `OK` response
      indicates that the request is valid, while an error response indicates
      that the request is invalid. If the request is valid, a subsequent
      request to delete the module could still fail for one of the following
      reasons: * The state of your cloud resources changed; for example, you
      lost a required IAM permission * An error occurred during creation of
      the module Defaults to `false`.
  """

  name = _messages.StringField(1, required=True)
  validateOnly = _messages.BooleanField(2)


class SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomModulesGetRequest(_messages.Message):
  r"""A SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomMod
  ulesGetRequest object.

  Fields:
    name: Required. The resource name of the Event Threat Detection custom
      module, in one of the following formats: * `organizations/{organization}
      /locations/{location}/eventThreatDetectionCustomModules/{custom_module}`
      * `folders/{folder}/locations/{location}/eventThreatDetectionCustomModul
      es/{custom_module}` * `projects/{project}/locations/{location}/eventThre
      atDetectionCustomModules/{custom_module}`
  """

  name = _messages.StringField(1, required=True)


class SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomModulesListDescendantRequest(_messages.Message):
  r"""A SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomMod
  ulesListDescendantRequest object.

  Fields:
    pageSize: Optional. The maximum number of modules to return. The service
      may return fewer than this value. If unspecified, at most 10 configs
      will be returned. The maximum value is 1000; values above 1000 will be
      coerced to 1000.
    pageToken: Optional. A pagination token returned from a previous request.
      Provide this token to retrieve the next page of results. When
      paginating, the rest of the request must match the request that
      generated the page token.
    parent: Required. Name of parent to list custom modules, in one of the
      following formats: * `organizations/{organization}/locations/{location}`
      * `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomModulesListRequest(_messages.Message):
  r"""A SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomMod
  ulesListRequest object.

  Fields:
    pageSize: Optional. The maximum number of modules to return. The service
      may return fewer than this value. If unspecified, at most 10 modules
      will be returned. The maximum value is 1000; values above 1000 will be
      coerced to 1000.
    pageToken: Optional. A pagination token returned from a previous request.
      Provide this token to retrieve the next page of results. When
      paginating, the rest of the request must match the request that
      generated the page token.
    parent: Required. Name of parent to list custom modules, in one of the
      following formats: * `organizations/{organization}/locations/{location}`
      * `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomModulesPatchRequest(_messages.Message):
  r"""A SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomMod
  ulesPatchRequest object.

  Fields:
    eventThreatDetectionCustomModule: A EventThreatDetectionCustomModule
      resource to be passed as the request body.
    name: Identifier. The resource name of the Event Threat Detection custom
      module, in one of the following formats: * `organizations/{organization}
      /locations/{location}/eventThreatDetectionCustomModules/{custom_module}`
      * `folders/{folder}/locations/{location}/eventThreatDetectionCustomModul
      es/{custom_module}` * `projects/{project}/locations/{location}/eventThre
      atDetectionCustomModules/{custom_module}`
    updateMask: Required. The fields to update. If omitted, then all fields
      are updated.
    validateOnly: Optional. When set to `true`, the request will be validated
      (including IAM checks), but no module will be updated. An `OK` response
      indicates that the request is valid, while an error response indicates
      that the request is invalid. If the request is valid, a subsequent
      request to update the module could still fail for one of the following
      reasons: * The state of your cloud resources changed; for example, you
      lost a required IAM permission * An error occurred during creation of
      the module Defaults to `false`.
  """

  eventThreatDetectionCustomModule = _messages.MessageField('EventThreatDetectionCustomModule', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomModulesValidateRequest(_messages.Message):
  r"""A SecuritycentermanagementProjectsLocationsEventThreatDetectionCustomMod
  ulesValidateRequest object.

  Fields:
    parent: Required. Resource name of the parent to validate the custom
      modules under, in one of the following formats: *
      `organizations/{organization}/locations/{location}`
    validateEventThreatDetectionCustomModuleRequest: A
      ValidateEventThreatDetectionCustomModuleRequest resource to be passed as
      the request body.
  """

  parent = _messages.StringField(1, required=True)
  validateEventThreatDetectionCustomModuleRequest = _messages.MessageField('ValidateEventThreatDetectionCustomModuleRequest', 2)


class SecuritycentermanagementProjectsLocationsGetBillingMetadataRequest(_messages.Message):
  r"""A SecuritycentermanagementProjectsLocationsGetBillingMetadataRequest
  object.

  Fields:
    name: Required. The resource to look up the billing tier for, in the
      format
      `organizations/{organization}/locations/{location}/billingMetadata` or
      `projects/{project}/locations/{location}/billingMetadata`.
  """

  name = _messages.StringField(1, required=True)


class SecuritycentermanagementProjectsLocationsGetRequest(_messages.Message):
  r"""A SecuritycentermanagementProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class SecuritycentermanagementProjectsLocationsListRequest(_messages.Message):
  r"""A SecuritycentermanagementProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class SecuritycentermanagementProjectsLocationsSecurityCenterServicesGetRequest(_messages.Message):
  r"""A
  SecuritycentermanagementProjectsLocationsSecurityCenterServicesGetRequest
  object.

  Fields:
    name: Required. The Security Command Center service to retrieve, in one of
      the following formats: * organizations/{organization}/locations/{locatio
      n}/securityCenterServices/{service} *
      folders/{folder}/locations/{location}/securityCenterServices/{service} *
      projects/{project}/locations/{location}/securityCenterServices/{service}
      The following values are valid for `{service}`: * `container-threat-
      detection` * `event-threat-detection` * `security-health-analytics` *
      `vm-threat-detection` * `web-security-scanner` * `vm-threat-detection-
      aws` * `cloud-run-threat-detection`
    showEligibleModulesOnly: Optional. Set to `true` to show only modules that
      are in scope. By default, all modules are shown.
  """

  name = _messages.StringField(1, required=True)
  showEligibleModulesOnly = _messages.BooleanField(2)


class SecuritycentermanagementProjectsLocationsSecurityCenterServicesListRequest(_messages.Message):
  r"""A
  SecuritycentermanagementProjectsLocationsSecurityCenterServicesListRequest
  object.

  Fields:
    pageSize: Optional. The maximum number of results to return in a single
      response. Default is 10, minimum is 1, maximum is 1000.
    pageToken: Optional. A pagination token returned from a previous request.
      Provide this token to retrieve the next page of results. When
      paginating, the rest of the request must match the request that
      generated the page token.
    parent: Required. The name of the parent to list Security Command Center
      services, in one of the following formats: *
      `organizations/{organization}/locations/{location}` *
      `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
    showEligibleModulesOnly: Flag that, when set, is used to filter the module
      settings that are shown. The default setting is that all modules are
      shown.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  showEligibleModulesOnly = _messages.BooleanField(4)


class SecuritycentermanagementProjectsLocationsSecurityCenterServicesPatchRequest(_messages.Message):
  r"""A
  SecuritycentermanagementProjectsLocationsSecurityCenterServicesPatchRequest
  object.

  Fields:
    name: Identifier. The name of the service, in one of the following
      formats: * `organizations/{organization}/locations/{location}/securityCe
      nterServices/{service}` *
      `folders/{folder}/locations/{location}/securityCenterServices/{service}`
      * `projects/{project}/locations/{location}/securityCenterServices/{servi
      ce}` The following values are valid for `{service}`: * `container-
      threat-detection` * `event-threat-detection` * `security-health-
      analytics` * `vm-threat-detection` * `web-security-scanner` * `vm-
      threat-detection-aws` * `cloud-run-threat-detection`
    securityCenterService: A SecurityCenterService resource to be passed as
      the request body.
    updateMask: Optional. Required. The fields to update. Accepts the
      following values: * `intended_enablement_state` * `modules` If omitted,
      then all eligible fields are updated.
    validateOnly: Optional. When set to `true`, the request will be validated
      (including IAM checks), but no service will be updated. An `OK` response
      indicates that the request is valid, while an error response indicates
      that the request is invalid. If the request is valid, a subsequent
      request to update the service could still fail for one of the following
      reasons: * The state of your cloud resources changed; for example, you
      lost a required IAM permission * An error occurred during update of the
      service Defaults to `false`.
  """

  name = _messages.StringField(1, required=True)
  securityCenterService = _messages.MessageField('SecurityCenterService', 2)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustomModulesCreateRequest(_messages.Message):
  r"""A SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustom
  ModulesCreateRequest object.

  Fields:
    parent: Required. Name of the parent organization, folder, or project of
      the module, in one of the following formats: *
      `organizations/{organization}/locations/{location}` *
      `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
    securityHealthAnalyticsCustomModule: A SecurityHealthAnalyticsCustomModule
      resource to be passed as the request body.
    validateOnly: Optional. When set to `true`, the request will be validated
      (including IAM checks), but no module will be created. An `OK` response
      indicates that the request is valid, while an error response indicates
      that the request is invalid. If the request is valid, a subsequent
      request to create the module could still fail for one of the following
      reasons: * The state of your cloud resources changed; for example, you
      lost a required IAM permission * An error occurred during creation of
      the module Defaults to `false`.
  """

  parent = _messages.StringField(1, required=True)
  securityHealthAnalyticsCustomModule = _messages.MessageField('SecurityHealthAnalyticsCustomModule', 2)
  validateOnly = _messages.BooleanField(3)


class SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustomModulesDeleteRequest(_messages.Message):
  r"""A SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustom
  ModulesDeleteRequest object.

  Fields:
    name: Required. The resource name of the SHA custom module, in one of the
      following formats: * `organizations/{organization}/locations/{location}/
      securityHealthAnalyticsCustomModules/{custom_module}` * `folders/{folder
      }/locations/{location}/securityHealthAnalyticsCustomModules/{custom_modu
      le}` * `projects/{project}/locations/{location}/securityHealthAnalyticsC
      ustomModules/{custom_module}`
    validateOnly: Optional. When set to `true`, the request will be validated
      (including IAM checks), but no module will be deleted. An `OK` response
      indicates that the request is valid, while an error response indicates
      that the request is invalid. If the request is valid, a subsequent
      request to delete the module could still fail for one of the following
      reasons: * The state of your cloud resources changed; for example, you
      lost a required IAM permission * An error occurred during deletion of
      the module Defaults to `false`.
  """

  name = _messages.StringField(1, required=True)
  validateOnly = _messages.BooleanField(2)


class SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustomModulesGetRequest(_messages.Message):
  r"""A SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustom
  ModulesGetRequest object.

  Fields:
    name: Required. Name of the resource, in the format `projects/{project}/lo
      cations/{location}/securityHealthAnalyticsCustomModules/{custom_module}`
      .
  """

  name = _messages.StringField(1, required=True)


class SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustomModulesListDescendantRequest(_messages.Message):
  r"""A SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustom
  ModulesListDescendantRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return in a single
      response. Default is 10, minimum is 1, maximum is 1000.
    pageToken: Optional. A pagination token returned from a previous request.
      Provide this token to retrieve the next page of results. When
      paginating, the rest of the request must match the request that
      generated the page token.
    parent: Required. Name of the parent organization, folder, or project in
      which to list custom modules, in one of the following formats: *
      `organizations/{organization}/locations/{location}` *
      `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustomModulesListRequest(_messages.Message):
  r"""A SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustom
  ModulesListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return in a single
      response. Default is 10, minimum is 1, maximum is 1000.
    pageToken: Optional. A pagination token returned from a previous request.
      Provide this token to retrieve the next page of results. When
      paginating, the rest of the request must match the request that
      generated the page token.
    parent: Required. Name of the parent organization, folder, or project in
      which to list custom modules, in one of the following formats: *
      `organizations/{organization}/locations/{location}` *
      `folders/{folder}/locations/{location}` *
      `projects/{project}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustomModulesPatchRequest(_messages.Message):
  r"""A SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustom
  ModulesPatchRequest object.

  Fields:
    name: Identifier. The full resource name of the custom module, in one of
      the following formats: * `organizations/{organization}/locations/{locati
      on}/securityHealthAnalyticsCustomModules/{custom_module}` * `folders/{fo
      lder}/locations/{location}/securityHealthAnalyticsCustomModules/{custom_
      module}` * `projects/{project}/locations/{location}/securityHealthAnalyt
      icsCustomModules/{custom_module}`
    securityHealthAnalyticsCustomModule: A SecurityHealthAnalyticsCustomModule
      resource to be passed as the request body.
    updateMask: Optional. Required. The fields to update. The following values
      are valid: * `custom_config` * `enablement_state` If you omit this field
      or set it to the wildcard value `*`, then all eligible fields are
      updated.
    validateOnly: Optional. When set to `true`, the request will be validated
      (including IAM checks), but no module will be updated. An `OK` response
      indicates that the request is valid, while an error response indicates
      that the request is invalid. If the request is valid, a subsequent
      request to update the module could still fail for one of the following
      reasons: * The state of your cloud resources changed; for example, you
      lost a required IAM permission * An error occurred during creation of
      the module Defaults to `false`.
  """

  name = _messages.StringField(1, required=True)
  securityHealthAnalyticsCustomModule = _messages.MessageField('SecurityHealthAnalyticsCustomModule', 2)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustomModulesSimulateRequest(_messages.Message):
  r"""A SecuritycentermanagementProjectsLocationsSecurityHealthAnalyticsCustom
  ModulesSimulateRequest object.

  Fields:
    parent: Required. The relative resource name of the organization, project,
      or folder. For more information about relative resource names, see
      [AIP-122: Resource names](https://google.aip.dev/122). Example:
      `organizations/{organization_id}`.
    simulateSecurityHealthAnalyticsCustomModuleRequest: A
      SimulateSecurityHealthAnalyticsCustomModuleRequest resource to be passed
      as the request body.
  """

  parent = _messages.StringField(1, required=True)
  simulateSecurityHealthAnalyticsCustomModuleRequest = _messages.MessageField('SimulateSecurityHealthAnalyticsCustomModuleRequest', 2)


class SimulateSecurityHealthAnalyticsCustomModuleRequest(_messages.Message):
  r"""Request message for
  SecurityCenterManagement.SimulateSecurityHealthAnalyticsCustomModule. The
  maximum size of the request is 4 MiB.

  Fields:
    customConfig: Required. The custom configuration that you need to test.
    resource: Required. Resource data to simulate custom module against.
  """

  customConfig = _messages.MessageField('CustomConfig', 1)
  resource = _messages.MessageField('SimulatedResource', 2)


class SimulateSecurityHealthAnalyticsCustomModuleResponse(_messages.Message):
  r"""Response message for
  SecurityCenterManagement.SimulateSecurityHealthAnalyticsCustomModule.

  Fields:
    result: Result for test case in the corresponding request.
  """

  result = _messages.MessageField('SimulatedResult', 1)


class SimulatedFinding(_messages.Message):
  r"""The minimum set of fields needed to represent a simulated finding from a
  Security Health Analytics custom module.

  Enums:
    FindingClassValueValuesEnum: The class of the finding.
    SeverityValueValuesEnum: The severity of the finding. This field is
      managed by the source that writes the finding.
    StateValueValuesEnum: Output only. The state of the finding.

  Messages:
    SourcePropertiesValue: Source-specific properties. These properties are
      managed by the source that writes the finding. The key names must be
      between 1 and 255 characters; they must start with a letter and contain
      alphanumeric characters or underscores only.

  Fields:
    category: The additional taxonomy group within findings from a given
      source. For example, `XSS_FLASH_INJECTION`. This field is immutable
      after creation time.
    eventTime: The time the finding was first detected. If an existing finding
      is updated, then this is the time the update occurred. If the finding is
      later resolved, then this time reflects when the finding was resolved.
      For example, if the finding represents an open firewall, this property
      captures the time the detector believes the firewall became open. The
      accuracy is determined by the detector. The event time must not be set
      to a value greater than the current timestamp.
    findingClass: The class of the finding.
    name: Identifier. The [relative resource name](https://google.aip.dev/122)
      of the finding, in one of the following formats: * `organizations/{organ
      ization_id}/sources/{source_id}/findings/{finding_id}` *
      `folders/{folder_id}/sources/{source_id}/findings/{finding_id}` *
      `projects/{project_id}/sources/{source_id}/findings/{finding_id}`
    parent:  The [relative resource name](https://google.aip.dev/122) of the
      source the finding belongs to. For example,
      `organizations/{organization_id}/sources/{source_id}`. This field is
      immutable after creation time.
    resourceName: For findings on Google Cloud resources, the [full resource
      name](https://google.aip.dev/122#full-resource-names) of the Google
      Cloud resource this finding is for. When the finding is for a non-Google
      Cloud resource, the value can be a customer or partner defined string.
      This field is immutable after creation time.
    severity: The severity of the finding. This field is managed by the source
      that writes the finding.
    sourceProperties: Source-specific properties. These properties are managed
      by the source that writes the finding. The key names must be between 1
      and 255 characters; they must start with a letter and contain
      alphanumeric characters or underscores only.
    state: Output only. The state of the finding.
  """

  class FindingClassValueValuesEnum(_messages.Enum):
    r"""The class of the finding.

    Values:
      FINDING_CLASS_UNSPECIFIED: Default value. This value is unused.
      THREAT: Describes unwanted or malicious activity.
      VULNERABILITY: Describes a potential weakness in software that increases
        risk to confidentiality, integrity, and availability.
      MISCONFIGURATION: Describes a potential weakness in cloud resource or
        asset configuration that increases risk.
      OBSERVATION: Describes a security observation that is for informational
        purposes.
      SCC_ERROR: Describes an error that prevents Security Command Center from
        working correctly.
      POSTURE_VIOLATION: Describes a potential security risk due to a change
        in the security posture.
      TOXIC_COMBINATION: Describes a combination of security issues that
        represent a more severe security problem when taken together.
      CHOKEPOINT: Describes a resource or resource group where high risk
        attack paths converge, based on attack path simulations (APS).
    """
    FINDING_CLASS_UNSPECIFIED = 0
    THREAT = 1
    VULNERABILITY = 2
    MISCONFIGURATION = 3
    OBSERVATION = 4
    SCC_ERROR = 5
    POSTURE_VIOLATION = 6
    TOXIC_COMBINATION = 7
    CHOKEPOINT = 8

  class SeverityValueValuesEnum(_messages.Enum):
    r"""The severity of the finding. This field is managed by the source that
    writes the finding.

    Values:
      SEVERITY_UNSPECIFIED: Default value. This value is unused.
      CRITICAL: For vulnerabilities: A critical vulnerability is easily
        discoverable by an external actor, exploitable, and results in the
        direct ability to execute arbitrary code, exfiltrate data, and
        otherwise gain additional access and privileges to cloud resources and
        workloads. Examples include publicly accessible unprotected user data
        and public SSH access with weak or no passwords. For threats:
        Indicates a threat that is able to access, modify, or delete data or
        execute unauthorized code within existing resources.
      HIGH: For vulnerabilities: A high-risk vulnerability can be easily
        discovered and exploited in combination with other vulnerabilities in
        order to gain direct access and the ability to execute arbitrary code,
        exfiltrate data, and otherwise gain additional access and privileges
        to cloud resources and workloads. An example is a database with weak
        or no passwords that is only accessible internally. This database
        could easily be compromised by an actor that had access to the
        internal network. For threats: Indicates a threat that is able to
        create new computational resources in an environment but not able to
        access data or execute code in existing resources.
      MEDIUM: For vulnerabilities: A medium-risk vulnerability could be used
        by an actor to gain access to resources or privileges that enable them
        to eventually (through multiple steps or a complex exploit) gain
        access and the ability to execute arbitrary code or exfiltrate data.
        An example is a service account with access to more projects than it
        should have. If an actor gains access to the service account, they
        could potentially use that access to manipulate a project the service
        account was not intended to. For threats: Indicates a threat that is
        able to cause operational impact but may not access data or execute
        unauthorized code.
      LOW: For vulnerabilities: A low-risk vulnerability hampers a security
        organization's ability to detect vulnerabilities or active threats in
        their deployment, or prevents the root cause investigation of security
        issues. An example is monitoring and logs being disabled for resource
        configurations and access. For threats: Indicates a threat that has
        obtained minimal access to an environment but is not able to access
        data, execute code, or create resources.
    """
    SEVERITY_UNSPECIFIED = 0
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the finding.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      ACTIVE: The finding requires attention and has not been addressed yet.
      INACTIVE: The finding has been fixed, triaged as a non-issue, or
        otherwise addressed and is no longer active.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    INACTIVE = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SourcePropertiesValue(_messages.Message):
    r"""Source-specific properties. These properties are managed by the source
    that writes the finding. The key names must be between 1 and 255
    characters; they must start with a letter and contain alphanumeric
    characters or underscores only.

    Messages:
      AdditionalProperty: An additional property for a SourcePropertiesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        SourcePropertiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SourcePropertiesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  category = _messages.StringField(1)
  eventTime = _messages.StringField(2)
  findingClass = _messages.EnumField('FindingClassValueValuesEnum', 3)
  name = _messages.StringField(4)
  parent = _messages.StringField(5)
  resourceName = _messages.StringField(6)
  severity = _messages.EnumField('SeverityValueValuesEnum', 7)
  sourceProperties = _messages.MessageField('SourcePropertiesValue', 8)
  state = _messages.EnumField('StateValueValuesEnum', 9)


class SimulatedResource(_messages.Message):
  r"""Manually constructed information about a resource.

  Messages:
    ResourceDataValue: Optional. A representation of the Google Cloud
      resource. Should match the Google Cloud resource JSON format. If the
      custom module evaluates only the IAM allow policy, then you can omit
      this field.

  Fields:
    iamPolicyData: Optional. A representation of the IAM allow policy. If the
      custom module evaluates only the resource data, then you can omit this
      field.
    resourceData: Optional. A representation of the Google Cloud resource.
      Should match the Google Cloud resource JSON format. If the custom module
      evaluates only the IAM allow policy, then you can omit this field.
    resourceType: Required. The type of the resource. For example,
      `compute.googleapis.com/Disk`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourceDataValue(_messages.Message):
    r"""Optional. A representation of the Google Cloud resource. Should match
    the Google Cloud resource JSON format. If the custom module evaluates only
    the IAM allow policy, then you can omit this field.

    Messages:
      AdditionalProperty: An additional property for a ResourceDataValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourceDataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  iamPolicyData = _messages.MessageField('Policy', 1)
  resourceData = _messages.MessageField('ResourceDataValue', 2)
  resourceType = _messages.StringField(3)


class SimulatedResult(_messages.Message):
  r"""Possible test result.

  Fields:
    error: Error encountered during the test.
    finding: Finding that would be published for the test case if a violation
      is detected.
    noViolation: Indicates that the test case does not trigger any violation.
  """

  error = _messages.MessageField('Status', 1)
  finding = _messages.MessageField('SimulatedFinding', 2)
  noViolation = _messages.MessageField('Empty', 3)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class ValidateEventThreatDetectionCustomModuleRequest(_messages.Message):
  r"""Request message for
  SecurityCenterManagement.ValidateEventThreatDetectionCustomModule.

  Fields:
    rawText: Required. The raw text of the module's contents. Used to generate
      error messages.
    type: Required. The type of the module. For example,
      `CONFIGURABLE_BAD_IP`.
  """

  rawText = _messages.StringField(1)
  type = _messages.StringField(2)


class ValidateEventThreatDetectionCustomModuleResponse(_messages.Message):
  r"""Response message for
  SecurityCenterManagement.ValidateEventThreatDetectionCustomModule.

  Fields:
    errors: A list of errors returned by the validator. If the list is empty,
      there were no errors.
  """

  errors = _messages.MessageField('CustomModuleValidationError', 1, repeated=True)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
