"""Generated message classes for domains version v1alpha2.

Enables management and configuration of domain names.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'domains'


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class AuthorizationCode(_messages.Message):
  r"""Defines an authorization code.

  Fields:
    code: The Authorization Code in ASCII. It can be used to transfer the
      domain to or from another registrar.
  """

  code = _messages.StringField(1)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class ConfigureContactSettingsRequest(_messages.Message):
  r"""Request for the `ConfigureContactSettings` method.

  Enums:
    ContactNoticesValueListEntryValuesEnum:

  Fields:
    contactNotices: The list of contact notices that the caller acknowledges.
      The notices needed here depend on the values specified in
      `contact_settings`.
    contactSettings: Fields of the `ContactSettings` to update.
    updateMask: Required. The field mask describing which fields to update as
      a comma-separated list. For example, if only the registrant contact is
      being updated, the `update_mask` is `"registrant_contact"`.
    validateOnly: Validate the request without actually updating the contact
      settings.
  """

  class ContactNoticesValueListEntryValuesEnum(_messages.Enum):
    r"""ContactNoticesValueListEntryValuesEnum enum type.

    Values:
      CONTACT_NOTICE_UNSPECIFIED: The notice is undefined.
      PUBLIC_CONTACT_DATA_ACKNOWLEDGEMENT: Required when setting the `privacy`
        field of `ContactSettings` to `PUBLIC_CONTACT_DATA`, which exposes
        contact data publicly.
    """
    CONTACT_NOTICE_UNSPECIFIED = 0
    PUBLIC_CONTACT_DATA_ACKNOWLEDGEMENT = 1

  contactNotices = _messages.EnumField('ContactNoticesValueListEntryValuesEnum', 1, repeated=True)
  contactSettings = _messages.MessageField('ContactSettings', 2)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class ConfigureDnsSettingsRequest(_messages.Message):
  r"""Request for the `ConfigureDnsSettings` method.

  Fields:
    dnsSettings: Fields of the `DnsSettings` to update.
    updateMask: Required. The field mask describing which fields to update as
      a comma-separated list. For example, if only the name servers are being
      updated for an existing Custom DNS configuration, the `update_mask` is
      `"custom_dns.name_servers"`. When changing the DNS provider from one
      type to another, pass the new provider's field name as part of the field
      mask. For example, when changing from a Google Domains DNS configuration
      to a Custom DNS configuration, the `update_mask` is `"custom_dns"`. //
    validateOnly: Validate the request without actually updating the DNS
      settings.
  """

  dnsSettings = _messages.MessageField('DnsSettings', 1)
  updateMask = _messages.StringField(2)
  validateOnly = _messages.BooleanField(3)


class ConfigureManagementSettingsRequest(_messages.Message):
  r"""Request for the `ConfigureManagementSettings` method.

  Fields:
    managementSettings: Fields of the `ManagementSettings` to update.
    updateMask: Required. The field mask describing which fields to update as
      a comma-separated list. For example, if only the transfer lock is being
      updated, the `update_mask` is `"transfer_lock_state"`.
  """

  managementSettings = _messages.MessageField('ManagementSettings', 1)
  updateMask = _messages.StringField(2)


class Contact(_messages.Message):
  r"""Details required for a contact associated with a `Registration`.

  Fields:
    email: Required. Email address of the contact.
    faxNumber: Fax number of the contact in international format. For example,
      `"******-555-0123"`.
    phoneNumber: Required. Phone number of the contact in international
      format. For example, `"******-555-0123"`.
    postalAddress: Required. Postal address of the contact.
  """

  email = _messages.StringField(1)
  faxNumber = _messages.StringField(2)
  phoneNumber = _messages.StringField(3)
  postalAddress = _messages.MessageField('PostalAddress', 4)


class ContactSettings(_messages.Message):
  r"""Defines the contact information associated with a `Registration`.
  [ICANN](https://icann.org/) requires all domain names to have associated
  contact information. The `registrant_contact` is considered the domain's
  legal owner, and often the other contacts are identical.

  Enums:
    PrivacyValueValuesEnum: Required. Privacy setting for the contacts
      associated with the `Registration`.

  Fields:
    adminContact: Required. The administrative contact for the `Registration`.
    privacy: Required. Privacy setting for the contacts associated with the
      `Registration`.
    registrantContact: Required. The registrant contact for the
      `Registration`. *Caution: Anyone with access to this email address,
      phone number, and/or postal address can take control of the domain.*
      *Warning: For new `Registration`s, the registrant receives an email
      confirmation that they must complete within 15 days to avoid domain
      suspension.*
    technicalContact: Required. The technical contact for the `Registration`.
  """

  class PrivacyValueValuesEnum(_messages.Enum):
    r"""Required. Privacy setting for the contacts associated with the
    `Registration`.

    Values:
      CONTACT_PRIVACY_UNSPECIFIED: The contact privacy settings are undefined.
      PUBLIC_CONTACT_DATA: All the data from `ContactSettings` is publicly
        available. When setting this option, you must also provide a
        `PUBLIC_CONTACT_DATA_ACKNOWLEDGEMENT` in the `contact_notices` field
        of the request.
      PRIVATE_CONTACT_DATA: Deprecated: For more information, see [Cloud
        Domains feature deprecation](https://cloud.google.com/domains/docs/dep
        recations/feature-deprecations). None of the data from
        `ContactSettings` is publicly available. Instead, proxy contact data
        is published for your domain. Email sent to the proxy email address is
        forwarded to the registrant's email address. Cloud Domains provides
        this privacy proxy service at no additional cost.
      REDACTED_CONTACT_DATA: The organization name (if provided) and limited
        non-identifying data from `ContactSettings` is available to the public
        (e.g. country and state). The remaining data is marked as `REDACTED
        FOR PRIVACY` in the WHOIS database. The actual information redacted
        depends on the domain. For details, see [the registration privacy
        article](https://support.google.com/domains/answer/3251242).
    """
    CONTACT_PRIVACY_UNSPECIFIED = 0
    PUBLIC_CONTACT_DATA = 1
    PRIVATE_CONTACT_DATA = 2
    REDACTED_CONTACT_DATA = 3

  adminContact = _messages.MessageField('Contact', 1)
  privacy = _messages.EnumField('PrivacyValueValuesEnum', 2)
  registrantContact = _messages.MessageField('Contact', 3)
  technicalContact = _messages.MessageField('Contact', 4)


class CustomDns(_messages.Message):
  r"""Configuration for an arbitrary DNS provider.

  Fields:
    dsRecords: The list of DS records for this domain, which are used to
      enable DNSSEC. The domain's DNS provider can provide the values to set
      here. If this field is empty, DNSSEC is disabled.
    nameServers: Required. A list of name servers that store the DNS zone for
      this domain. Each name server is a domain name, with Unicode domain
      names expressed in Punycode format.
  """

  dsRecords = _messages.MessageField('DsRecord', 1, repeated=True)
  nameServers = _messages.StringField(2, repeated=True)


class DnsSettings(_messages.Message):
  r"""Defines the DNS configuration of a `Registration`, including name
  servers, DNSSEC, and glue records.

  Fields:
    customDns: An arbitrary DNS provider identified by its name servers.
    glueRecords: The list of glue records for this `Registration`. Commonly
      empty.
    googleDomainsDns: Deprecated: For more information, see [Cloud Domains
      feature
      deprecation](https://cloud.google.com/domains/docs/deprecations/feature-
      deprecations). The free DNS zone provided by [Google
      Domains](https://domains.google/).
    googleDomainsRedirectsDataAvailable: Output only. Indicates if this
      `Registration` has configured one of the following deprecated Google
      Domains DNS features: * Domain forwarding (HTTP `301` and `302` response
      status codes), * Email forwarding. See
      https://cloud.google.com/domains/docs/deprecations/feature-deprecations
      for more details. If any of these features is enabled call the
      `RetrieveGoogleDomainsForwardingConfig` method to get details about the
      feature's configuration. A forwarding configuration might not work
      correctly if required DNS records are not present in the domain's
      authoritative DNS Zone.
  """

  customDns = _messages.MessageField('CustomDns', 1)
  glueRecords = _messages.MessageField('GlueRecord', 2, repeated=True)
  googleDomainsDns = _messages.MessageField('GoogleDomainsDns', 3)
  googleDomainsRedirectsDataAvailable = _messages.BooleanField(4)


class Domain(_messages.Message):
  r"""A domain that the calling user manages in Google Domains.

  Enums:
    ResourceStateValueValuesEnum: The state of this domain as a `Registration`
      resource.

  Fields:
    domainName: The domain name. Unicode domain names are expressed in
      Punycode format.
    resourceState: The state of this domain as a `Registration` resource.
    yearlyPrice: Price to renew the domain for one year. Only set when
      `resource_state` is `IMPORTABLE`.
  """

  class ResourceStateValueValuesEnum(_messages.Enum):
    r"""The state of this domain as a `Registration` resource.

    Values:
      RESOURCE_STATE_UNSPECIFIED: The assessment is undefined.
      IMPORTABLE: A `Registration` resource can be created for this domain by
        calling `ImportDomain`.
      UNSUPPORTED: A `Registration` resource cannot be created for this domain
        because it is not supported by Cloud Domains; for example, the top-
        level domain is not supported or the registry charges non-standard
        pricing for yearly renewals.
      SUSPENDED: A `Registration` resource cannot be created for this domain
        because it is suspended and needs to be resolved with Google Domains.
      EXPIRED: A `Registration` resource cannot be created for this domain
        because it is expired and needs to be renewed with Google Domains.
      DELETED: A `Registration` resource cannot be created for this domain
        because it is deleted, but it may be possible to restore it with
        Google Domains.
    """
    RESOURCE_STATE_UNSPECIFIED = 0
    IMPORTABLE = 1
    UNSUPPORTED = 2
    SUSPENDED = 3
    EXPIRED = 4
    DELETED = 5

  domainName = _messages.StringField(1)
  resourceState = _messages.EnumField('ResourceStateValueValuesEnum', 2)
  yearlyPrice = _messages.MessageField('Money', 3)


class DomainForwarding(_messages.Message):
  r"""Domain forwarding configuration.

  Enums:
    RedirectTypeValueValuesEnum: The redirect type.

  Fields:
    pathForwarding: If true, forwards the path after the domain name to the
      same path at the new address.
    pemCertificate: The PEM-encoded certificate chain.
    redirectType: The redirect type.
    sslEnabled: If true, the forwarding works also over HTTPS.
    subdomain: The subdomain of the registered domain that is being forwarded.
      E.g. `www.example.com`, `example.com` (i.e. the registered domain
      itself) or `*.example.com` (i.e. all subdomains).
    targetUri: The target of the domain forwarding, i.e. the path to redirect
      the `subdomain` to.
  """

  class RedirectTypeValueValuesEnum(_messages.Enum):
    r"""The redirect type.

    Values:
      REDIRECT_TYPE_UNSPECIFIED: Redirect Type is unspecified.
      TEMPORARY: 301 redirect. Allows to propagate changes to the forwarding
        address quickly.
      PERMANENT: 302 redirect. Allows browsers to cache the forwarding
        address. This may help the address resolve more quickly. Changes may
        take longer to propagate
    """
    REDIRECT_TYPE_UNSPECIFIED = 0
    TEMPORARY = 1
    PERMANENT = 2

  pathForwarding = _messages.BooleanField(1)
  pemCertificate = _messages.StringField(2)
  redirectType = _messages.EnumField('RedirectTypeValueValuesEnum', 3)
  sslEnabled = _messages.BooleanField(4)
  subdomain = _messages.StringField(5)
  targetUri = _messages.StringField(6)


class DomainsProjectsLocationsGetRequest(_messages.Message):
  r"""A DomainsProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class DomainsProjectsLocationsListRequest(_messages.Message):
  r"""A DomainsProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class DomainsProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A DomainsProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class DomainsProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A DomainsProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class DomainsProjectsLocationsRegistrationsConfigureContactSettingsRequest(_messages.Message):
  r"""A DomainsProjectsLocationsRegistrationsConfigureContactSettingsRequest
  object.

  Fields:
    configureContactSettingsRequest: A ConfigureContactSettingsRequest
      resource to be passed as the request body.
    registration: Required. The name of the `Registration` whose contact
      settings are being updated, in the format
      `projects/*/locations/*/registrations/*`.
  """

  configureContactSettingsRequest = _messages.MessageField('ConfigureContactSettingsRequest', 1)
  registration = _messages.StringField(2, required=True)


class DomainsProjectsLocationsRegistrationsConfigureDnsSettingsRequest(_messages.Message):
  r"""A DomainsProjectsLocationsRegistrationsConfigureDnsSettingsRequest
  object.

  Fields:
    configureDnsSettingsRequest: A ConfigureDnsSettingsRequest resource to be
      passed as the request body.
    registration: Required. The name of the `Registration` whose DNS settings
      are being updated, in the format
      `projects/*/locations/*/registrations/*`.
  """

  configureDnsSettingsRequest = _messages.MessageField('ConfigureDnsSettingsRequest', 1)
  registration = _messages.StringField(2, required=True)


class DomainsProjectsLocationsRegistrationsConfigureManagementSettingsRequest(_messages.Message):
  r"""A
  DomainsProjectsLocationsRegistrationsConfigureManagementSettingsRequest
  object.

  Fields:
    configureManagementSettingsRequest: A ConfigureManagementSettingsRequest
      resource to be passed as the request body.
    registration: Required. The name of the `Registration` whose management
      settings are being updated, in the format
      `projects/*/locations/*/registrations/*`.
  """

  configureManagementSettingsRequest = _messages.MessageField('ConfigureManagementSettingsRequest', 1)
  registration = _messages.StringField(2, required=True)


class DomainsProjectsLocationsRegistrationsDeleteRequest(_messages.Message):
  r"""A DomainsProjectsLocationsRegistrationsDeleteRequest object.

  Fields:
    name: Required. The name of the `Registration` to delete, in the format
      `projects/*/locations/*/registrations/*`.
  """

  name = _messages.StringField(1, required=True)


class DomainsProjectsLocationsRegistrationsExportRequest(_messages.Message):
  r"""A DomainsProjectsLocationsRegistrationsExportRequest object.

  Fields:
    exportRegistrationRequest: A ExportRegistrationRequest resource to be
      passed as the request body.
    name: Required. The name of the `Registration` to export, in the format
      `projects/*/locations/*/registrations/*`.
  """

  exportRegistrationRequest = _messages.MessageField('ExportRegistrationRequest', 1)
  name = _messages.StringField(2, required=True)


class DomainsProjectsLocationsRegistrationsGetIamPolicyRequest(_messages.Message):
  r"""A DomainsProjectsLocationsRegistrationsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class DomainsProjectsLocationsRegistrationsGetRequest(_messages.Message):
  r"""A DomainsProjectsLocationsRegistrationsGetRequest object.

  Fields:
    name: Required. The name of the `Registration` to get, in the format
      `projects/*/locations/*/registrations/*`.
  """

  name = _messages.StringField(1, required=True)


class DomainsProjectsLocationsRegistrationsImportRequest(_messages.Message):
  r"""A DomainsProjectsLocationsRegistrationsImportRequest object.

  Fields:
    importDomainRequest: A ImportDomainRequest resource to be passed as the
      request body.
    parent: Required. The parent resource of the Registration. Must be in the
      format `projects/*/locations/*`.
  """

  importDomainRequest = _messages.MessageField('ImportDomainRequest', 1)
  parent = _messages.StringField(2, required=True)


class DomainsProjectsLocationsRegistrationsInitiatePushTransferRequest(_messages.Message):
  r"""A DomainsProjectsLocationsRegistrationsInitiatePushTransferRequest
  object.

  Fields:
    initiatePushTransferRequest: A InitiatePushTransferRequest resource to be
      passed as the request body.
    registration: Required. The name of the `Registration` for which the push
      transfer is initiated, in the format
      `projects/*/locations/*/registrations/*`.
  """

  initiatePushTransferRequest = _messages.MessageField('InitiatePushTransferRequest', 1)
  registration = _messages.StringField(2, required=True)


class DomainsProjectsLocationsRegistrationsListRequest(_messages.Message):
  r"""A DomainsProjectsLocationsRegistrationsListRequest object.

  Fields:
    filter: Filter expression to restrict the `Registration`s returned. The
      expression must specify the field name, a comparison operator, and the
      value that you want to use for filtering. The value must be a string, a
      number, a boolean, or an enum value. The comparison operator should be
      one of =, !=, >, <, >=, <=, or : for prefix or wildcard matches. For
      example, to filter to a specific domain name, use an expression like
      `domainName="example.com"`. You can also check for the existence of a
      field; for example, to find domains using custom DNS settings, use an
      expression like `dnsSettings.customDns:*`. You can also create compound
      filters by combining expressions with the `AND` and `OR` operators. For
      example, to find domains that are suspended or have specific issues
      flagged, use an expression like `(state=SUSPENDED) OR (issue:*)`.
    pageSize: Maximum number of results to return.
    pageToken: When set to the `next_page_token` from a prior response,
      provides the next page of results.
    parent: Required. The project and location from which to list
      `Registration`s, specified in the format `projects/*/locations/*`.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class DomainsProjectsLocationsRegistrationsPatchRequest(_messages.Message):
  r"""A DomainsProjectsLocationsRegistrationsPatchRequest object.

  Fields:
    name: Output only. Name of the `Registration` resource, in the format
      `projects/*/locations/*/registrations/`.
    registration: A Registration resource to be passed as the request body.
    updateMask: Required. The field mask describing which fields to update as
      a comma-separated list. For example, if only the labels are being
      updated, the `update_mask` is `"labels"`.
  """

  name = _messages.StringField(1, required=True)
  registration = _messages.MessageField('Registration', 2)
  updateMask = _messages.StringField(3)


class DomainsProjectsLocationsRegistrationsRegisterRequest(_messages.Message):
  r"""A DomainsProjectsLocationsRegistrationsRegisterRequest object.

  Fields:
    parent: Required. The parent resource of the `Registration`. Must be in
      the format `projects/*/locations/*`.
    registerDomainRequest: A RegisterDomainRequest resource to be passed as
      the request body.
  """

  parent = _messages.StringField(1, required=True)
  registerDomainRequest = _messages.MessageField('RegisterDomainRequest', 2)


class DomainsProjectsLocationsRegistrationsRenewDomainRequest(_messages.Message):
  r"""A DomainsProjectsLocationsRegistrationsRenewDomainRequest object.

  Fields:
    registration: Required. The name of the `Registration` whish is being
      renewed, in the format `projects/*/locations/*/registrations/*`.
    renewDomainRequest: A RenewDomainRequest resource to be passed as the
      request body.
  """

  registration = _messages.StringField(1, required=True)
  renewDomainRequest = _messages.MessageField('RenewDomainRequest', 2)


class DomainsProjectsLocationsRegistrationsResetAuthorizationCodeRequest(_messages.Message):
  r"""A DomainsProjectsLocationsRegistrationsResetAuthorizationCodeRequest
  object.

  Fields:
    registration: Required. The name of the `Registration` whose authorization
      code is being reset, in the format
      `projects/*/locations/*/registrations/*`.
    resetAuthorizationCodeRequest: A ResetAuthorizationCodeRequest resource to
      be passed as the request body.
  """

  registration = _messages.StringField(1, required=True)
  resetAuthorizationCodeRequest = _messages.MessageField('ResetAuthorizationCodeRequest', 2)


class DomainsProjectsLocationsRegistrationsRetrieveAuthorizationCodeRequest(_messages.Message):
  r"""A DomainsProjectsLocationsRegistrationsRetrieveAuthorizationCodeRequest
  object.

  Fields:
    registration: Required. The name of the `Registration` whose authorization
      code is being retrieved, in the format
      `projects/*/locations/*/registrations/*`.
  """

  registration = _messages.StringField(1, required=True)


class DomainsProjectsLocationsRegistrationsRetrieveGoogleDomainsDnsRecordsRequest(_messages.Message):
  r"""A
  DomainsProjectsLocationsRegistrationsRetrieveGoogleDomainsDnsRecordsRequest
  object.

  Fields:
    pageSize: Optional. Maximum number of results to return.
    pageToken: Optional. When set to the `next_page_token` from a prior
      response, provides the next page of results.
    registration: Required. The name of the `Registration` whose Google
      Domains DNS records details you are retrieving, in the format
      `projects/*/locations/*/registrations/*`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  registration = _messages.StringField(3, required=True)


class DomainsProjectsLocationsRegistrationsRetrieveGoogleDomainsForwardingConfigRequest(_messages.Message):
  r"""A DomainsProjectsLocationsRegistrationsRetrieveGoogleDomainsForwardingCo
  nfigRequest object.

  Fields:
    registration: Required. The name of the `Registration` whose Google
      Domains forwarding configuration details are being retrieved, in the
      format `projects/*/locations/*/registrations/*`.
  """

  registration = _messages.StringField(1, required=True)


class DomainsProjectsLocationsRegistrationsRetrieveImportableDomainsRequest(_messages.Message):
  r"""A DomainsProjectsLocationsRegistrationsRetrieveImportableDomainsRequest
  object.

  Fields:
    location: Required. The location. Must be in the format
      `projects/*/locations/*`.
    pageSize: Maximum number of results to return.
    pageToken: When set to the `next_page_token` from a prior response,
      provides the next page of results.
  """

  location = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class DomainsProjectsLocationsRegistrationsRetrieveRegisterParametersRequest(_messages.Message):
  r"""A DomainsProjectsLocationsRegistrationsRetrieveRegisterParametersRequest
  object.

  Fields:
    domainName: Required. The domain name. Unicode domain names must be
      expressed in Punycode format.
    location: Required. The location. Must be in the format
      `projects/*/locations/*`.
  """

  domainName = _messages.StringField(1)
  location = _messages.StringField(2, required=True)


class DomainsProjectsLocationsRegistrationsRetrieveTransferParametersRequest(_messages.Message):
  r"""A DomainsProjectsLocationsRegistrationsRetrieveTransferParametersRequest
  object.

  Fields:
    domainName: Required. The domain name. Unicode domain names must be
      expressed in Punycode format.
    location: Required. The location. Must be in the format
      `projects/*/locations/*`.
  """

  domainName = _messages.StringField(1)
  location = _messages.StringField(2, required=True)


class DomainsProjectsLocationsRegistrationsSearchDomainsRequest(_messages.Message):
  r"""A DomainsProjectsLocationsRegistrationsSearchDomainsRequest object.

  Fields:
    location: Required. The location. Must be in the format
      `projects/*/locations/*`.
    query: Required. String used to search for available domain names.
  """

  location = _messages.StringField(1, required=True)
  query = _messages.StringField(2)


class DomainsProjectsLocationsRegistrationsSetIamPolicyRequest(_messages.Message):
  r"""A DomainsProjectsLocationsRegistrationsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class DomainsProjectsLocationsRegistrationsTestIamPermissionsRequest(_messages.Message):
  r"""A DomainsProjectsLocationsRegistrationsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class DomainsProjectsLocationsRegistrationsTransferRequest(_messages.Message):
  r"""A DomainsProjectsLocationsRegistrationsTransferRequest object.

  Fields:
    parent: Required. The parent resource of the `Registration`. Must be in
      the format `projects/*/locations/*`.
    transferDomainRequest: A TransferDomainRequest resource to be passed as
      the request body.
  """

  parent = _messages.StringField(1, required=True)
  transferDomainRequest = _messages.MessageField('TransferDomainRequest', 2)


class DsRecord(_messages.Message):
  r"""Defines a Delegation Signer (DS) record, which is needed to enable
  DNSSEC for a domain. It contains a digest (hash) of a DNSKEY record that
  must be present in the domain's DNS zone.

  Enums:
    AlgorithmValueValuesEnum: The algorithm used to generate the referenced
      DNSKEY.
    DigestTypeValueValuesEnum: The hash function used to generate the digest
      of the referenced DNSKEY.

  Fields:
    algorithm: The algorithm used to generate the referenced DNSKEY.
    digest: The digest generated from the referenced DNSKEY.
    digestType: The hash function used to generate the digest of the
      referenced DNSKEY.
    keyTag: The key tag of the record. Must be set in range 0 -- 65535.
  """

  class AlgorithmValueValuesEnum(_messages.Enum):
    r"""The algorithm used to generate the referenced DNSKEY.

    Values:
      ALGORITHM_UNSPECIFIED: The algorithm is unspecified.
      RSAMD5: RSA/MD5. Cannot be used for new deployments.
      DH: Diffie-Hellman. Cannot be used for new deployments.
      DSA: DSA/SHA1. Not recommended for new deployments.
      ECC: ECC. Not recommended for new deployments.
      RSASHA1: RSA/SHA-1. Not recommended for new deployments.
      DSANSEC3SHA1: DSA-NSEC3-SHA1. Not recommended for new deployments.
      RSASHA1NSEC3SHA1: RSA/SHA1-NSEC3-SHA1. Not recommended for new
        deployments.
      RSASHA256: RSA/SHA-256.
      RSASHA512: RSA/SHA-512.
      ECCGOST: GOST R 34.10-2001.
      ECDSAP256SHA256: ECDSA Curve P-256 with SHA-256.
      ECDSAP384SHA384: ECDSA Curve P-384 with SHA-384.
      ED25519: Ed25519.
      ED448: Ed448.
      INDIRECT: Reserved for Indirect Keys. Cannot be used for new
        deployments.
      PRIVATEDNS: Private algorithm. Cannot be used for new deployments.
      PRIVATEOID: Private algorithm OID. Cannot be used for new deployments.
    """
    ALGORITHM_UNSPECIFIED = 0
    RSAMD5 = 1
    DH = 2
    DSA = 3
    ECC = 4
    RSASHA1 = 5
    DSANSEC3SHA1 = 6
    RSASHA1NSEC3SHA1 = 7
    RSASHA256 = 8
    RSASHA512 = 9
    ECCGOST = 10
    ECDSAP256SHA256 = 11
    ECDSAP384SHA384 = 12
    ED25519 = 13
    ED448 = 14
    INDIRECT = 15
    PRIVATEDNS = 16
    PRIVATEOID = 17

  class DigestTypeValueValuesEnum(_messages.Enum):
    r"""The hash function used to generate the digest of the referenced
    DNSKEY.

    Values:
      DIGEST_TYPE_UNSPECIFIED: The DigestType is unspecified.
      SHA1: SHA-1. Not recommended for new deployments.
      SHA256: SHA-256.
      GOST3411: GOST R 34.11-94.
      SHA384: SHA-384.
    """
    DIGEST_TYPE_UNSPECIFIED = 0
    SHA1 = 1
    SHA256 = 2
    GOST3411 = 3
    SHA384 = 4

  algorithm = _messages.EnumField('AlgorithmValueValuesEnum', 1)
  digest = _messages.StringField(2)
  digestType = _messages.EnumField('DigestTypeValueValuesEnum', 3)
  keyTag = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class EmailForwarding(_messages.Message):
  r"""Email forwarding configuration.

  Fields:
    alias: An alias recipient email that forwards emails to the
      `target_email_address`. For example, `<EMAIL>` or
      `*@example.com` (wildcard alias forwards all the emails under the
      registered domain).
    targetEmailAddress: Target email that receives emails sent to the `alias`.
  """

  alias = _messages.StringField(1)
  targetEmailAddress = _messages.StringField(2)


class ExportRegistrationRequest(_messages.Message):
  r"""Deprecated: For more information, see [Cloud Domains feature
  deprecation](https://cloud.google.com/domains/docs/deprecations/feature-
  deprecations). Request for the `ExportRegistration` method.
  """



class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GeoPolicy(_messages.Message):
  r"""Configures a `RRSetRoutingPolicy` that routes based on the geo location
  of the querying user.

  Fields:
    enableFencing: Without fencing, if health check fails for all configured
      items in the current geo bucket, we failover to the next nearest geo
      bucket. With fencing, if health checking is enabled, as long as some
      targets in the current geo bucket are healthy, we return only the
      healthy targets. However, if all targets are unhealthy, we don't
      failover to the next nearest bucket; instead, we return all the items in
      the current bucket even when all targets are unhealthy.
    item: The primary geo routing configuration. If there are multiple items
      with the same location, an error is returned instead.
  """

  enableFencing = _messages.BooleanField(1)
  item = _messages.MessageField('GeoPolicyItem', 2, repeated=True)


class GeoPolicyItem(_messages.Message):
  r"""ResourceRecordSet data for one geo location.

  Fields:
    healthCheckedTargets: For A and AAAA types only. Endpoints to return in
      the query result only if they are healthy. These can be specified along
      with `rrdata` within this item.
    location: The geo-location granularity is a GCP region. This location
      string should correspond to a GCP region. e.g. "us-east1",
      "southamerica-east1", "asia-east1", etc.
    rrdata: A string attribute.
    signatureRrdata: DNSSEC generated signatures for all the `rrdata` within
      this item. When using health-checked targets for DNSSEC-enabled zones,
      you can only use at most one health-checked IP address per item.
  """

  healthCheckedTargets = _messages.MessageField('HealthCheckTargets', 1)
  location = _messages.StringField(2)
  rrdata = _messages.StringField(3, repeated=True)
  signatureRrdata = _messages.StringField(4, repeated=True)


class GlueRecord(_messages.Message):
  r"""Defines a host on your domain that is a DNS name server for your domain
  and/or other domains. Glue records are a way of making the IP address of a
  name server known, even when it serves DNS queries for its parent domain.
  For example, when `ns.example.com` is a name server for `example.com`, the
  host `ns.example.com` must have a glue record to break the circular DNS
  reference.

  Fields:
    hostName: Required. Domain name of the host in Punycode format.
    ipv4Addresses: List of IPv4 addresses corresponding to this host in the
      standard decimal format (e.g. `************`). At least one of
      `ipv4_address` and `ipv6_address` must be set.
    ipv6Addresses: List of IPv6 addresses corresponding to this host in the
      standard hexadecimal format (e.g. `2001:db8::`). At least one of
      `ipv4_address` and `ipv6_address` must be set.
  """

  hostName = _messages.StringField(1)
  ipv4Addresses = _messages.StringField(2, repeated=True)
  ipv6Addresses = _messages.StringField(3, repeated=True)


class GoogleDomainsDns(_messages.Message):
  r"""Deprecated: For more information, see [Cloud Domains feature
  deprecation](https://cloud.google.com/domains/docs/deprecations/feature-
  deprecations). Configuration for using the free DNS zone provided by Google
  Domains as a `Registration`'s `dns_provider`. You cannot configure the DNS
  zone itself using the API. To configure the DNS zone, go to [Google
  Domains](https://domains.google/).

  Enums:
    DsStateValueValuesEnum: Required. The state of DS records for this domain.
      Used to enable or disable automatic DNSSEC.

  Fields:
    dsRecords: Output only. The list of DS records published for this domain.
      The list is automatically populated when `ds_state` is
      `DS_RECORDS_PUBLISHED`, otherwise it remains empty.
    dsState: Required. The state of DS records for this domain. Used to enable
      or disable automatic DNSSEC.
    nameServers: Output only. A list of name servers that store the DNS zone
      for this domain. Each name server is a domain name, with Unicode domain
      names expressed in Punycode format. This field is automatically
      populated with the name servers assigned to the Google Domains DNS zone.
  """

  class DsStateValueValuesEnum(_messages.Enum):
    r"""Required. The state of DS records for this domain. Used to enable or
    disable automatic DNSSEC.

    Values:
      DS_STATE_UNSPECIFIED: DS state is unspecified.
      DS_RECORDS_UNPUBLISHED: DNSSEC is disabled for this domain. No DS
        records for this domain are published in the parent DNS zone.
      DS_RECORDS_PUBLISHED: DNSSEC is enabled for this domain. Appropriate DS
        records for this domain are published in the parent DNS zone. This
        option is valid only if the DNS zone referenced in the
        `Registration`'s `dns_provider` field is already DNSSEC-signed.
    """
    DS_STATE_UNSPECIFIED = 0
    DS_RECORDS_UNPUBLISHED = 1
    DS_RECORDS_PUBLISHED = 2

  dsRecords = _messages.MessageField('DsRecord', 1, repeated=True)
  dsState = _messages.EnumField('DsStateValueValuesEnum', 2)
  nameServers = _messages.StringField(3, repeated=True)


class HealthCheckTargets(_messages.Message):
  r"""HealthCheckTargets describes endpoints to health-check when responding
  to Routing Policy queries. Only the healthy endpoints will be included in
  the response. Set either `internal_load_balancer` or `external_endpoints`.
  Do not set both.

  Fields:
    externalEndpoints: The Internet IP addresses to be health checked. The
      format matches the format of ResourceRecordSet.rrdata as defined in RFC
      1035 (section 5) and RFC 1034 (section 3.6.1)
    internalLoadBalancer: Configuration for internal load balancers to be
      health checked.
  """

  externalEndpoints = _messages.StringField(1, repeated=True)
  internalLoadBalancer = _messages.MessageField('LoadBalancerTarget', 2, repeated=True)


class ImportDomainRequest(_messages.Message):
  r"""Deprecated: For more information, see [Cloud Domains feature
  deprecation](https://cloud.google.com/domains/docs/deprecations/feature-
  deprecations). Request for the `ImportDomain` method.

  Messages:
    LabelsValue: Set of labels associated with the `Registration`.

  Fields:
    domainName: Required. The domain name. Unicode domain names must be
      expressed in Punycode format.
    labels: Set of labels associated with the `Registration`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Set of labels associated with the `Registration`.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  domainName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)


class InitiatePushTransferRequest(_messages.Message):
  r"""Request for the `InitiatePushTransfer` method.

  Fields:
    tag: Required. The Tag of the new registrar. Can be found at [List of
      registrars](https://nominet.uk/registrar-list/).
  """

  tag = _messages.StringField(1)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListRegistrationsResponse(_messages.Message):
  r"""Response for the `ListRegistrations` method.

  Fields:
    nextPageToken: When present, there are more results to retrieve. Set
      `page_token` to this value on a subsequent call to get the next page of
      results.
    registrations: A list of `Registration`s.
  """

  nextPageToken = _messages.StringField(1)
  registrations = _messages.MessageField('Registration', 2, repeated=True)


class LoadBalancerTarget(_messages.Message):
  r"""The configuration for an individual load balancer to health check.

  Enums:
    IpProtocolValueValuesEnum: The protocol of the load balancer to health
      check.
    LoadBalancerTypeValueValuesEnum: The type of load balancer specified by
      this target. This value must match the configuration of the load
      balancer located at the LoadBalancerTarget's IP address, port, and
      region. Use the following: - *regionalL4ilb*: for a regional internal
      passthrough Network Load Balancer. - *regionalL7ilb*: for a regional
      internal Application Load Balancer. - *globalL7ilb*: for a global
      internal Application Load Balancer.

  Fields:
    ipAddress: The frontend IP address of the load balancer to health check.
    ipProtocol: The protocol of the load balancer to health check.
    loadBalancerType: The type of load balancer specified by this target. This
      value must match the configuration of the load balancer located at the
      LoadBalancerTarget's IP address, port, and region. Use the following: -
      *regionalL4ilb*: for a regional internal passthrough Network Load
      Balancer. - *regionalL7ilb*: for a regional internal Application Load
      Balancer. - *globalL7ilb*: for a global internal Application Load
      Balancer.
    networkUrl: The fully qualified URL of the network that the load balancer
      is attached to. This should be formatted like `https://www.googleapis.co
      m/compute/v1/projects/{project}/global/networks/{network}`.
    port: The configured port of the load balancer.
    project: The project ID in which the load balancer is located.
    region: The region in which the load balancer is located.
  """

  class IpProtocolValueValuesEnum(_messages.Enum):
    r"""The protocol of the load balancer to health check.

    Values:
      UNDEFINED: <no description>
      TCP: Indicates the load balancer is accessible via TCP.
      UDP: Indicates the load balancer is accessible via UDP.
    """
    UNDEFINED = 0
    TCP = 1
    UDP = 2

  class LoadBalancerTypeValueValuesEnum(_messages.Enum):
    r"""The type of load balancer specified by this target. This value must
    match the configuration of the load balancer located at the
    LoadBalancerTarget's IP address, port, and region. Use the following: -
    *regionalL4ilb*: for a regional internal passthrough Network Load
    Balancer. - *regionalL7ilb*: for a regional internal Application Load
    Balancer. - *globalL7ilb*: for a global internal Application Load
    Balancer.

    Values:
      NONE: <no description>
      GLOBAL_L7ILB: Indicates the load balancer is a Cross-Region Application
        Load Balancer.
      REGIONAL_L4ILB: Indicates the load balancer is a Regional Network
        Passthrough Load Balancer.
      REGIONAL_L7ILB: Indicates the load balancer is a Regional Application
        Load Balancer.
    """
    NONE = 0
    GLOBAL_L7ILB = 1
    REGIONAL_L4ILB = 2
    REGIONAL_L7ILB = 3

  ipAddress = _messages.StringField(1)
  ipProtocol = _messages.EnumField('IpProtocolValueValuesEnum', 2)
  loadBalancerType = _messages.EnumField('LoadBalancerTypeValueValuesEnum', 3)
  networkUrl = _messages.StringField(4)
  port = _messages.StringField(5)
  project = _messages.StringField(6)
  region = _messages.StringField(7)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class ManagementSettings(_messages.Message):
  r"""Defines renewal, billing, and transfer settings for a `Registration`.

  Enums:
    EffectiveTransferLockStateValueValuesEnum: Output only. The actual
      transfer lock state for this `Registration`.
    PreferredRenewalMethodValueValuesEnum: Optional. The desired renewal
      method for this `Registration`. The actual `renewal_method` is
      automatically updated to reflect this choice. If unset or equal to
      `RENEWAL_METHOD_UNSPECIFIED`, the actual `renewalMethod` is treated as
      if it were set to `AUTOMATIC_RENEWAL`. You cannot use `RENEWAL_DISABLED`
      during resource creation, and you can update the renewal status only
      when the `Registration` resource has state `ACTIVE` or `SUSPENDED`. When
      `preferred_renewal_method` is set to `AUTOMATIC_RENEWAL`, the actual
      `renewal_method` can be set to `RENEWAL_DISABLED` in case of problems
      with the billing account or reported domain abuse. In such cases, check
      the `issues` field on the `Registration`. After the problem is resolved,
      the `renewal_method` is automatically updated to
      `preferred_renewal_method` in a few hours.
    RenewalMethodValueValuesEnum: Output only. The actual renewal method for
      this `Registration`. When `preferred_renewal_method` is set to
      `AUTOMATIC_RENEWAL`, the actual `renewal_method` can be equal to
      `RENEWAL_DISABLED`-for example, when there are problems with the billing
      account or reported domain abuse. In such cases, check the `issues`
      field on the `Registration`. After the problem is resolved, the
      `renewal_method` is automatically updated to `preferred_renewal_method`
      in a few hours.
    TransferLockStateValueValuesEnum: This is the desired transfer lock state
      for this `Registration`. A transfer lock controls whether the domain can
      be transferred to another registrar. The transfer lock state of the
      domain is returned in the `effective_transfer_lock_state` property. The
      transfer lock state values might be different for the following reasons:
      * `transfer_lock_state` was updated only a short time ago. * Domains
      with the `TRANSFER_LOCK_UNSUPPORTED_BY_REGISTRY` state are in the list
      of `domain_properties`. These domains are always in the `UNLOCKED`
      state.

  Fields:
    effectiveTransferLockState: Output only. The actual transfer lock state
      for this `Registration`.
    preferredRenewalMethod: Optional. The desired renewal method for this
      `Registration`. The actual `renewal_method` is automatically updated to
      reflect this choice. If unset or equal to `RENEWAL_METHOD_UNSPECIFIED`,
      the actual `renewalMethod` is treated as if it were set to
      `AUTOMATIC_RENEWAL`. You cannot use `RENEWAL_DISABLED` during resource
      creation, and you can update the renewal status only when the
      `Registration` resource has state `ACTIVE` or `SUSPENDED`. When
      `preferred_renewal_method` is set to `AUTOMATIC_RENEWAL`, the actual
      `renewal_method` can be set to `RENEWAL_DISABLED` in case of problems
      with the billing account or reported domain abuse. In such cases, check
      the `issues` field on the `Registration`. After the problem is resolved,
      the `renewal_method` is automatically updated to
      `preferred_renewal_method` in a few hours.
    renewalMethod: Output only. The actual renewal method for this
      `Registration`. When `preferred_renewal_method` is set to
      `AUTOMATIC_RENEWAL`, the actual `renewal_method` can be equal to
      `RENEWAL_DISABLED`-for example, when there are problems with the billing
      account or reported domain abuse. In such cases, check the `issues`
      field on the `Registration`. After the problem is resolved, the
      `renewal_method` is automatically updated to `preferred_renewal_method`
      in a few hours.
    transferLockState: This is the desired transfer lock state for this
      `Registration`. A transfer lock controls whether the domain can be
      transferred to another registrar. The transfer lock state of the domain
      is returned in the `effective_transfer_lock_state` property. The
      transfer lock state values might be different for the following reasons:
      * `transfer_lock_state` was updated only a short time ago. * Domains
      with the `TRANSFER_LOCK_UNSUPPORTED_BY_REGISTRY` state are in the list
      of `domain_properties`. These domains are always in the `UNLOCKED`
      state.
  """

  class EffectiveTransferLockStateValueValuesEnum(_messages.Enum):
    r"""Output only. The actual transfer lock state for this `Registration`.

    Values:
      TRANSFER_LOCK_STATE_UNSPECIFIED: The state is unspecified.
      UNLOCKED: The domain is unlocked and can be transferred to another
        registrar.
      LOCKED: The domain is locked and cannot be transferred to another
        registrar.
    """
    TRANSFER_LOCK_STATE_UNSPECIFIED = 0
    UNLOCKED = 1
    LOCKED = 2

  class PreferredRenewalMethodValueValuesEnum(_messages.Enum):
    r"""Optional. The desired renewal method for this `Registration`. The
    actual `renewal_method` is automatically updated to reflect this choice.
    If unset or equal to `RENEWAL_METHOD_UNSPECIFIED`, the actual
    `renewalMethod` is treated as if it were set to `AUTOMATIC_RENEWAL`. You
    cannot use `RENEWAL_DISABLED` during resource creation, and you can update
    the renewal status only when the `Registration` resource has state
    `ACTIVE` or `SUSPENDED`. When `preferred_renewal_method` is set to
    `AUTOMATIC_RENEWAL`, the actual `renewal_method` can be set to
    `RENEWAL_DISABLED` in case of problems with the billing account or
    reported domain abuse. In such cases, check the `issues` field on the
    `Registration`. After the problem is resolved, the `renewal_method` is
    automatically updated to `preferred_renewal_method` in a few hours.

    Values:
      RENEWAL_METHOD_UNSPECIFIED: The renewal method is undefined.
      AUTOMATIC_RENEWAL: The domain is automatically renewed each year.
      MANUAL_RENEWAL: Deprecated: For more information, see [Cloud Domains
        feature deprecation](https://cloud.google.com/domains/docs/deprecation
        s/feature-deprecations). This option was never used. Use
        `RENEWAL_DISABLED` instead.
      RENEWAL_DISABLED: The domain won't be renewed and will expire at its
        expiration time.
    """
    RENEWAL_METHOD_UNSPECIFIED = 0
    AUTOMATIC_RENEWAL = 1
    MANUAL_RENEWAL = 2
    RENEWAL_DISABLED = 3

  class RenewalMethodValueValuesEnum(_messages.Enum):
    r"""Output only. The actual renewal method for this `Registration`. When
    `preferred_renewal_method` is set to `AUTOMATIC_RENEWAL`, the actual
    `renewal_method` can be equal to `RENEWAL_DISABLED`-for example, when
    there are problems with the billing account or reported domain abuse. In
    such cases, check the `issues` field on the `Registration`. After the
    problem is resolved, the `renewal_method` is automatically updated to
    `preferred_renewal_method` in a few hours.

    Values:
      RENEWAL_METHOD_UNSPECIFIED: The renewal method is undefined.
      AUTOMATIC_RENEWAL: The domain is automatically renewed each year.
      MANUAL_RENEWAL: Deprecated: For more information, see [Cloud Domains
        feature deprecation](https://cloud.google.com/domains/docs/deprecation
        s/feature-deprecations). This option was never used. Use
        `RENEWAL_DISABLED` instead.
      RENEWAL_DISABLED: The domain won't be renewed and will expire at its
        expiration time.
    """
    RENEWAL_METHOD_UNSPECIFIED = 0
    AUTOMATIC_RENEWAL = 1
    MANUAL_RENEWAL = 2
    RENEWAL_DISABLED = 3

  class TransferLockStateValueValuesEnum(_messages.Enum):
    r"""This is the desired transfer lock state for this `Registration`. A
    transfer lock controls whether the domain can be transferred to another
    registrar. The transfer lock state of the domain is returned in the
    `effective_transfer_lock_state` property. The transfer lock state values
    might be different for the following reasons: * `transfer_lock_state` was
    updated only a short time ago. * Domains with the
    `TRANSFER_LOCK_UNSUPPORTED_BY_REGISTRY` state are in the list of
    `domain_properties`. These domains are always in the `UNLOCKED` state.

    Values:
      TRANSFER_LOCK_STATE_UNSPECIFIED: The state is unspecified.
      UNLOCKED: The domain is unlocked and can be transferred to another
        registrar.
      LOCKED: The domain is locked and cannot be transferred to another
        registrar.
    """
    TRANSFER_LOCK_STATE_UNSPECIFIED = 0
    UNLOCKED = 1
    LOCKED = 2

  effectiveTransferLockState = _messages.EnumField('EffectiveTransferLockStateValueValuesEnum', 1)
  preferredRenewalMethod = _messages.EnumField('PreferredRenewalMethodValueValuesEnum', 2)
  renewalMethod = _messages.EnumField('RenewalMethodValueValuesEnum', 3)
  transferLockState = _messages.EnumField('TransferLockStateValueValuesEnum', 4)


class Money(_messages.Message):
  r"""Represents an amount of money with its currency type.

  Fields:
    currencyCode: The three-letter currency code defined in ISO 4217.
    nanos: Number of nano (10^-9) units of the amount. The value must be
      between -999,999,999 and +999,999,999 inclusive. If `units` is positive,
      `nanos` must be positive or zero. If `units` is zero, `nanos` can be
      positive, zero, or negative. If `units` is negative, `nanos` must be
      negative or zero. For example $-1.75 is represented as `units`=-1 and
      `nanos`=-750,000,000.
    units: The whole units of the amount. For example if `currencyCode` is
      `"USD"`, then 1 unit is one US dollar.
  """

  currencyCode = _messages.StringField(1)
  nanos = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  units = _messages.IntegerField(3)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation. Output only.

  Fields:
    apiVersion: API version used to start the operation.
    createTime: The time the operation was created.
    endTime: The time the operation finished running.
    statusDetail: Human-readable status of the operation, if any.
    target: Server-defined resource path for the target of the operation.
    verb: Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  statusDetail = _messages.StringField(4)
  target = _messages.StringField(5)
  verb = _messages.StringField(6)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class PostalAddress(_messages.Message):
  r"""Represents a postal address, such as for postal delivery or payments
  addresses. With a postal address, a postal service can deliver items to a
  premise, P.O. box, or similar. A postal address is not intended to model
  geographical locations like roads, towns, or mountains. In typical usage, an
  address would be created by user input or from importing existing data,
  depending on the type of process. Advice on address input or editing: - Use
  an internationalization-ready address widget such as
  https://github.com/google/libaddressinput. - Users should not be presented
  with UI elements for input or editing of fields outside countries where that
  field is used. For more guidance on how to use this schema, see:
  https://support.google.com/business/answer/6397478.

  Fields:
    addressLines: Unstructured address lines describing the lower levels of an
      address. Because values in `address_lines` do not have type information
      and may sometimes contain multiple values in a single field (for
      example, "Austin, TX"), it is important that the line order is clear.
      The order of address lines should be "envelope order" for the country or
      region of the address. In places where this can vary (for example,
      Japan), `address_language` is used to make it explicit (for example,
      "ja" for large-to-small ordering and "ja-Latn" or "en" for small-to-
      large). In this way, the most specific line of an address can be
      selected based on the language. The minimum permitted structural
      representation of an address consists of a `region_code` with all
      remaining information placed in the `address_lines`. It would be
      possible to format such an address very approximately without geocoding,
      but no semantic reasoning could be made about any of the address
      components until it was at least partially resolved. Creating an address
      only containing a `region_code` and `address_lines` and then geocoding
      is the recommended way to handle completely unstructured addresses (as
      opposed to guessing which parts of the address should be localities or
      administrative areas).
    administrativeArea: Optional. Highest administrative subdivision which is
      used for postal addresses of a country or region. For example, this can
      be a state, a province, an oblast, or a prefecture. For Spain, this is
      the province and not the autonomous community (for example, "Barcelona"
      and not "Catalonia"). Many countries don't use an administrative area in
      postal addresses. For example, in Switzerland, this should be left
      unpopulated.
    languageCode: Optional. BCP-47 language code of the contents of this
      address (if known). This is often the UI language of the input form or
      is expected to match one of the languages used in the address'
      country/region, or their transliterated equivalents. This can affect
      formatting in certain countries, but is not critical to the correctness
      of the data and will never affect any validation or other non-formatting
      related operations. If this value is not known, it should be omitted
      (rather than specifying a possibly incorrect default). Examples: "zh-
      Hant", "ja", "ja-Latn", "en".
    locality: Optional. Generally refers to the city or town portion of the
      address. Examples: US city, IT comune, UK post town. In regions of the
      world where localities are not well defined or do not fit into this
      structure well, leave `locality` empty and use `address_lines`.
    organization: Optional. The name of the organization at the address.
    postalCode: Optional. Postal code of the address. Not all countries use or
      require postal codes to be present, but where they are used, they may
      trigger additional validation with other parts of the address (for
      example, state or zip code validation in the United States).
    recipients: Optional. The recipient at the address. This field may, under
      certain circumstances, contain multiline information. For example, it
      might contain "care of" information.
    regionCode: Required. CLDR region code of the country/region of the
      address. This is never inferred and it is up to the user to ensure the
      value is correct. See https://cldr.unicode.org/ and https://www.unicode.
      org/cldr/charts/30/supplemental/territory_information.html for details.
      Example: "CH" for Switzerland.
    revision: The schema revision of the `PostalAddress`. This must be set to
      0, which is the latest revision. All new revisions **must** be backward
      compatible with old revisions.
    sortingCode: Optional. Additional, country-specific, sorting code. This is
      not used in most regions. Where it is used, the value is either a string
      like "CEDEX", optionally followed by a number (for example, "CEDEX 7"),
      or just a number alone, representing the "sector code" (Jamaica),
      "delivery area indicator" (Malawi) or "post office indicator" (C\xf4te
      d'Ivoire).
    sublocality: Optional. Sublocality of the address. For example, this can
      be a neighborhood, borough, or district.
  """

  addressLines = _messages.StringField(1, repeated=True)
  administrativeArea = _messages.StringField(2)
  languageCode = _messages.StringField(3)
  locality = _messages.StringField(4)
  organization = _messages.StringField(5)
  postalCode = _messages.StringField(6)
  recipients = _messages.StringField(7, repeated=True)
  regionCode = _messages.StringField(8)
  revision = _messages.IntegerField(9, variant=_messages.Variant.INT32)
  sortingCode = _messages.StringField(10)
  sublocality = _messages.StringField(11)


class PrimaryBackupPolicy(_messages.Message):
  r"""Configures a RRSetRoutingPolicy such that all queries are responded with
  the primary_targets if they are healthy. And if all of them are unhealthy,
  then we fallback to a geo localized policy.

  Fields:
    backupGeoTargets: Backup targets provide a regional failover policy for
      the otherwise global primary targets. If serving state is set to
      `BACKUP`, this policy essentially becomes a geo routing policy.
    primaryTargets: Endpoints that are health checked before making the
      routing decision. Unhealthy endpoints are omitted from the results. If
      all endpoints are unhealthy, we serve a response based on the
      `backup_geo_targets`.
    trickleTraffic: When serving state is `PRIMARY`, this field provides the
      option of sending a small percentage of the traffic to the backup
      targets.
  """

  backupGeoTargets = _messages.MessageField('GeoPolicy', 1)
  primaryTargets = _messages.MessageField('HealthCheckTargets', 2)
  trickleTraffic = _messages.FloatField(3)


class RRSetRoutingPolicy(_messages.Message):
  r"""A RRSetRoutingPolicy represents ResourceRecordSet data that is returned
  dynamically with the response varying based on configured properties such as
  geolocation or by weighted random selection.

  Fields:
    geo: A GeoPolicy attribute.
    geoPolicy: A GeoPolicy attribute.
    healthCheck: The fully qualified URL of the HealthCheck to use for this
      RRSetRoutingPolicy. Format this URL like `https://www.googleapis.com/com
      pute/v1/projects/{project}/global/healthChecks/{healthCheck}`.
      https://cloud.google.com/compute/docs/reference/rest/v1/healthChecks
    primaryBackup: A PrimaryBackupPolicy attribute.
    wrr: A WrrPolicy attribute.
    wrrPolicy: A WrrPolicy attribute.
  """

  geo = _messages.MessageField('GeoPolicy', 1)
  geoPolicy = _messages.MessageField('GeoPolicy', 2)
  healthCheck = _messages.StringField(3)
  primaryBackup = _messages.MessageField('PrimaryBackupPolicy', 4)
  wrr = _messages.MessageField('WrrPolicy', 5)
  wrrPolicy = _messages.MessageField('WrrPolicy', 6)


class RegisterDomainRequest(_messages.Message):
  r"""Request for the `RegisterDomain` method.

  Enums:
    ContactNoticesValueListEntryValuesEnum:
    DomainNoticesValueListEntryValuesEnum:

  Fields:
    contactNotices: The list of contact notices that the caller acknowledges.
      The notices needed here depend on the values specified in
      `registration.contact_settings`.
    domainNotices: The list of domain notices that you acknowledge. Call
      `RetrieveRegisterParameters` to see the notices that need
      acknowledgement.
    registration: Required. The complete `Registration` resource to be
      created.
    validateOnly: When true, only validation is performed, without actually
      registering the domain. Follows:
      https://cloud.google.com/apis/design/design_patterns#request_validation
    yearlyPrice: Required. Yearly price to register or renew the domain. The
      value that should be put here can be obtained from
      RetrieveRegisterParameters or SearchDomains calls.
  """

  class ContactNoticesValueListEntryValuesEnum(_messages.Enum):
    r"""ContactNoticesValueListEntryValuesEnum enum type.

    Values:
      CONTACT_NOTICE_UNSPECIFIED: The notice is undefined.
      PUBLIC_CONTACT_DATA_ACKNOWLEDGEMENT: Required when setting the `privacy`
        field of `ContactSettings` to `PUBLIC_CONTACT_DATA`, which exposes
        contact data publicly.
    """
    CONTACT_NOTICE_UNSPECIFIED = 0
    PUBLIC_CONTACT_DATA_ACKNOWLEDGEMENT = 1

  class DomainNoticesValueListEntryValuesEnum(_messages.Enum):
    r"""DomainNoticesValueListEntryValuesEnum enum type.

    Values:
      DOMAIN_NOTICE_UNSPECIFIED: The notice is undefined.
      HSTS_PRELOADED: Indicates that the domain is preloaded on the HTTP
        Strict Transport Security list in browsers. Serving a website on such
        domain requires an SSL certificate. For details, see [how to get an
        SSL certificate](https://support.google.com/domains/answer/7638036).
    """
    DOMAIN_NOTICE_UNSPECIFIED = 0
    HSTS_PRELOADED = 1

  contactNotices = _messages.EnumField('ContactNoticesValueListEntryValuesEnum', 1, repeated=True)
  domainNotices = _messages.EnumField('DomainNoticesValueListEntryValuesEnum', 2, repeated=True)
  registration = _messages.MessageField('Registration', 3)
  validateOnly = _messages.BooleanField(4)
  yearlyPrice = _messages.MessageField('Money', 5)


class RegisterParameters(_messages.Message):
  r"""Parameters required to register a new domain.

  Enums:
    AvailabilityValueValuesEnum: Indicates whether the domain is available for
      registration. This value is accurate when obtained by calling
      `RetrieveRegisterParameters`, but is approximate when obtained by
      calling `SearchDomains`.
    DomainNoticesValueListEntryValuesEnum:
    SupportedPrivacyValueListEntryValuesEnum:

  Fields:
    availability: Indicates whether the domain is available for registration.
      This value is accurate when obtained by calling
      `RetrieveRegisterParameters`, but is approximate when obtained by
      calling `SearchDomains`.
    domainName: The domain name. Unicode domain names are expressed in
      Punycode format.
    domainNotices: Notices about special properties of the domain.
    supportedPrivacy: Contact privacy options that the domain supports.
    yearlyPrice: Price to register or renew the domain for one year.
  """

  class AvailabilityValueValuesEnum(_messages.Enum):
    r"""Indicates whether the domain is available for registration. This value
    is accurate when obtained by calling `RetrieveRegisterParameters`, but is
    approximate when obtained by calling `SearchDomains`.

    Values:
      AVAILABILITY_UNSPECIFIED: The availability is unspecified.
      AVAILABLE: The domain is available for registration.
      UNAVAILABLE: The domain is not available for registration. Generally
        this means it is already registered to another party.
      UNSUPPORTED: The domain is not currently supported by Cloud Domains, but
        may be available elsewhere.
      UNKNOWN: Cloud Domains is unable to determine domain availability,
        generally due to system maintenance at the domain name registry.
    """
    AVAILABILITY_UNSPECIFIED = 0
    AVAILABLE = 1
    UNAVAILABLE = 2
    UNSUPPORTED = 3
    UNKNOWN = 4

  class DomainNoticesValueListEntryValuesEnum(_messages.Enum):
    r"""DomainNoticesValueListEntryValuesEnum enum type.

    Values:
      DOMAIN_NOTICE_UNSPECIFIED: The notice is undefined.
      HSTS_PRELOADED: Indicates that the domain is preloaded on the HTTP
        Strict Transport Security list in browsers. Serving a website on such
        domain requires an SSL certificate. For details, see [how to get an
        SSL certificate](https://support.google.com/domains/answer/7638036).
    """
    DOMAIN_NOTICE_UNSPECIFIED = 0
    HSTS_PRELOADED = 1

  class SupportedPrivacyValueListEntryValuesEnum(_messages.Enum):
    r"""SupportedPrivacyValueListEntryValuesEnum enum type.

    Values:
      CONTACT_PRIVACY_UNSPECIFIED: The contact privacy settings are undefined.
      PUBLIC_CONTACT_DATA: All the data from `ContactSettings` is publicly
        available. When setting this option, you must also provide a
        `PUBLIC_CONTACT_DATA_ACKNOWLEDGEMENT` in the `contact_notices` field
        of the request.
      PRIVATE_CONTACT_DATA: Deprecated: For more information, see [Cloud
        Domains feature deprecation](https://cloud.google.com/domains/docs/dep
        recations/feature-deprecations). None of the data from
        `ContactSettings` is publicly available. Instead, proxy contact data
        is published for your domain. Email sent to the proxy email address is
        forwarded to the registrant's email address. Cloud Domains provides
        this privacy proxy service at no additional cost.
      REDACTED_CONTACT_DATA: The organization name (if provided) and limited
        non-identifying data from `ContactSettings` is available to the public
        (e.g. country and state). The remaining data is marked as `REDACTED
        FOR PRIVACY` in the WHOIS database. The actual information redacted
        depends on the domain. For details, see [the registration privacy
        article](https://support.google.com/domains/answer/3251242).
    """
    CONTACT_PRIVACY_UNSPECIFIED = 0
    PUBLIC_CONTACT_DATA = 1
    PRIVATE_CONTACT_DATA = 2
    REDACTED_CONTACT_DATA = 3

  availability = _messages.EnumField('AvailabilityValueValuesEnum', 1)
  domainName = _messages.StringField(2)
  domainNotices = _messages.EnumField('DomainNoticesValueListEntryValuesEnum', 3, repeated=True)
  supportedPrivacy = _messages.EnumField('SupportedPrivacyValueListEntryValuesEnum', 4, repeated=True)
  yearlyPrice = _messages.MessageField('Money', 5)


class Registration(_messages.Message):
  r"""The `Registration` resource facilitates managing and configuring domain
  name registrations. There are several ways to create a new `Registration`
  resource: To create a new `Registration` resource, find a suitable domain
  name by calling the `SearchDomains` method with a query to see available
  domain name options. After choosing a name, call
  `RetrieveRegisterParameters` to ensure availability and obtain information
  like pricing, which is needed to build a call to `RegisterDomain`. Another
  way to create a new `Registration` is to transfer an existing domain from
  another registrar (Deprecated: For more information, see [Cloud Domains
  feature
  deprecation](https://cloud.google.com/domains/docs/deprecations/feature-
  deprecations)). First, go to the current registrar to unlock the domain for
  transfer and retrieve the domain's transfer authorization code. Then call
  `RetrieveTransferParameters` to confirm that the domain is unlocked and to
  get values needed to build a call to `TransferDomain`. Finally, you can
  create a new `Registration` by importing an existing domain managed with
  [Google Domains](https://domains.google/) (Deprecated: For more information,
  see [Cloud Domains feature
  deprecation](https://cloud.google.com/domains/docs/deprecations/feature-
  deprecations)). First, call `RetrieveImportableDomains` to list domains to
  which the calling user has sufficient access. Then call `ImportDomain` on
  any domain names you want to use with Cloud Domains.

  Enums:
    DomainPropertiesValueListEntryValuesEnum:
    IssuesValueListEntryValuesEnum:
    ProviderValueValuesEnum: Output only. Current domain management provider.
    RegisterFailureReasonValueValuesEnum: Output only. The reason the domain
      registration failed. Only set for domains in REGISTRATION_FAILED state.
    StateValueValuesEnum: Output only. The state of the `Registration`
    SupportedPrivacyValueListEntryValuesEnum:
    TransferFailureReasonValueValuesEnum: Output only. Deprecated: For more
      information, see [Cloud Domains feature
      deprecation](https://cloud.google.com/domains/docs/deprecations/feature-
      deprecations). The reason the domain transfer failed. Only set for
      domains in TRANSFER_FAILED state.

  Messages:
    LabelsValue: Set of labels associated with the `Registration`.

  Fields:
    contactSettings: Required. Settings for contact information linked to the
      `Registration`. You cannot update these with the `UpdateRegistration`
      method. To update these settings, use the `ConfigureContactSettings`
      method.
    createTime: Output only. The creation timestamp of the `Registration`
      resource.
    dnsSettings: Settings controlling the DNS configuration of the
      `Registration`. You cannot update these with the `UpdateRegistration`
      method. To update these settings, use the `ConfigureDnsSettings` method.
    domainName: Required. Immutable. The domain name. Unicode domain names
      must be expressed in Punycode format.
    domainProperties: Output only. Special properties of the domain.
    expireTime: Output only. The expiration timestamp of the `Registration`.
    issues: Output only. The set of issues with the `Registration` that
      require attention.
    labels: Set of labels associated with the `Registration`.
    managementSettings: Settings for management of the `Registration`,
      including renewal, billing, and transfer. You cannot update these with
      the `UpdateRegistration` method. To update these settings, use the
      `ConfigureManagementSettings` method.
    name: Output only. Name of the `Registration` resource, in the format
      `projects/*/locations/*/registrations/`.
    pendingContactSettings: Output only. Pending contact settings for the
      `Registration`. Updates to the `contact_settings` field that change its
      `registrant_contact` or `privacy` fields require email confirmation by
      the `registrant_contact` before taking effect. This field is set only if
      there are pending updates to the `contact_settings` that have not been
      confirmed. To confirm the changes, the `registrant_contact` must follow
      the instructions in the email they receive.
    provider: Output only. Current domain management provider.
    registerFailureReason: Output only. The reason the domain registration
      failed. Only set for domains in REGISTRATION_FAILED state.
    state: Output only. The state of the `Registration`
    supportedPrivacy: Output only. Set of options for the
      `contact_settings.privacy` field that this `Registration` supports.
    transferFailureReason: Output only. Deprecated: For more information, see
      [Cloud Domains feature
      deprecation](https://cloud.google.com/domains/docs/deprecations/feature-
      deprecations). The reason the domain transfer failed. Only set for
      domains in TRANSFER_FAILED state.
  """

  class DomainPropertiesValueListEntryValuesEnum(_messages.Enum):
    r"""DomainPropertiesValueListEntryValuesEnum enum type.

    Values:
      DOMAIN_PROPERTY_UNSPECIFIED: The property is undefined.
      TRANSFER_LOCK_UNSUPPORTED_BY_REGISTRY: The domain does not support
        transfer locks due to restrictions of the registry. Such domains are
        effectively always unlocked and any change made to
        `management_settings.transfer_lock_state` is ignored.
      REQUIRE_PUSH_TRANSFER: The domain uses an alternative `Push Transfer`
        process to transfer the domain to another registrar. There are two
        important consequences: * Cloud Domains does not supply the
        authorization codes. * To initiate the process to transfer the domain
        to another registrar, you must provide a tag of the registrar you want
        to transfer to. You can do this by using the `InitiatePushTransfer`
        method. For more information, see [Transfer a registered domain to
        another registrar](https://cloud.google.com/domains/docs/transfer-
        domain-to-another-registrar).
    """
    DOMAIN_PROPERTY_UNSPECIFIED = 0
    TRANSFER_LOCK_UNSUPPORTED_BY_REGISTRY = 1
    REQUIRE_PUSH_TRANSFER = 2

  class IssuesValueListEntryValuesEnum(_messages.Enum):
    r"""IssuesValueListEntryValuesEnum enum type.

    Values:
      ISSUE_UNSPECIFIED: The issue is undefined.
      CONTACT_SUPPORT: Contact the Cloud Support team to resolve a problem
        with this domain.
      UNVERIFIED_EMAIL: [ICANN](https://icann.org/) requires verification of
        the email address in the `Registration`'s
        `contact_settings.registrant_contact` field. To verify the email
        address, follow the instructions in the email the `registrant_contact`
        receives following registration. If you do not complete email
        verification within 15 days of registration, the domain is suspended.
        To resend the verification email, call ConfigureContactSettings and
        provide the current `registrant_contact.email`.
      PROBLEM_WITH_BILLING: The billing account is not in good standing. The
        domain is not automatically renewed at its expiration time unless you
        resolve problems with your billing account.
      DNS_NOT_ACTIVATED: The registry failed to validate your DNS
        configuration and activate your domain. The failure might happen for
        the following reasons: * You recently registered the domain (wait up
        to 72 hours). * You provided invalid name servers or name servers that
        correspond to a newly created DNS zone. Verify your DNS configuration.
        If the configuration is incorrect, you must fix it. If the
        configuration is correct, either wait or call the ConfigureDnsSettings
        method to retry the registry validation.
      AUTO_RENEWAL_UPDATE_NOT_EFFECTIVE: Due to SquareSpace's constraints, the
        auto-renewal update you made may not be effective during a certain
        period of time. Generally, the time period is 15 days before
        expiration for generic TLD domains, and 15 days before expiration + 3
        days after expiration for country-code TLD domains.
    """
    ISSUE_UNSPECIFIED = 0
    CONTACT_SUPPORT = 1
    UNVERIFIED_EMAIL = 2
    PROBLEM_WITH_BILLING = 3
    DNS_NOT_ACTIVATED = 4
    AUTO_RENEWAL_UPDATE_NOT_EFFECTIVE = 5

  class ProviderValueValuesEnum(_messages.Enum):
    r"""Output only. Current domain management provider.

    Values:
      REGISTRAR_UNSPECIFIED: Registrar is not selected.
      GOOGLE_DOMAINS: Use Google Domains registrar.
      SQUARESPACE: Use Squarespace registrar
    """
    REGISTRAR_UNSPECIFIED = 0
    GOOGLE_DOMAINS = 1
    SQUARESPACE = 2

  class RegisterFailureReasonValueValuesEnum(_messages.Enum):
    r"""Output only. The reason the domain registration failed. Only set for
    domains in REGISTRATION_FAILED state.

    Values:
      REGISTER_FAILURE_REASON_UNSPECIFIED: Register failure unspecified.
      REGISTER_FAILURE_REASON_UNKNOWN: Registration failed for an unknown
        reason.
      DOMAIN_NOT_AVAILABLE: The domain is not available for registration.
      INVALID_CONTACTS: The provided contact information was rejected.
    """
    REGISTER_FAILURE_REASON_UNSPECIFIED = 0
    REGISTER_FAILURE_REASON_UNKNOWN = 1
    DOMAIN_NOT_AVAILABLE = 2
    INVALID_CONTACTS = 3

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the `Registration`

    Values:
      STATE_UNSPECIFIED: The state is undefined.
      REGISTRATION_PENDING: The domain is being registered.
      REGISTRATION_FAILED: The domain registration failed. You can delete
        resources in this state to allow registration to be retried.
      TRANSFER_PENDING: The domain is being transferred from another registrar
        to Cloud Domains.
      TRANSFER_FAILED: The attempt to transfer the domain from another
        registrar to Cloud Domains failed. You can delete resources in this
        state and retry the transfer.
      IMPORT_PENDING: The domain is being imported from Google Domains to
        Cloud Domains.
      ACTIVE: The domain is registered and operational. The domain renews
        automatically as long as it remains in this state and the
        `RenewalMethod` is set to `AUTOMATIC_RENEWAL`.
      SUSPENDED: The domain is suspended and inoperative. For more details,
        see the `issues` field.
      EXPORTED: The domain is no longer managed with Cloud Domains. It may
        have been transferred to another registrar or exported for management
        in [Google Domains](https://domains.google/). You can no longer update
        it with this API, and information shown about it may be stale. Domains
        in this state are not automatically renewed by Cloud Domains.
      EXPIRED: The domain is expired.
    """
    STATE_UNSPECIFIED = 0
    REGISTRATION_PENDING = 1
    REGISTRATION_FAILED = 2
    TRANSFER_PENDING = 3
    TRANSFER_FAILED = 4
    IMPORT_PENDING = 5
    ACTIVE = 6
    SUSPENDED = 7
    EXPORTED = 8
    EXPIRED = 9

  class SupportedPrivacyValueListEntryValuesEnum(_messages.Enum):
    r"""SupportedPrivacyValueListEntryValuesEnum enum type.

    Values:
      CONTACT_PRIVACY_UNSPECIFIED: The contact privacy settings are undefined.
      PUBLIC_CONTACT_DATA: All the data from `ContactSettings` is publicly
        available. When setting this option, you must also provide a
        `PUBLIC_CONTACT_DATA_ACKNOWLEDGEMENT` in the `contact_notices` field
        of the request.
      PRIVATE_CONTACT_DATA: Deprecated: For more information, see [Cloud
        Domains feature deprecation](https://cloud.google.com/domains/docs/dep
        recations/feature-deprecations). None of the data from
        `ContactSettings` is publicly available. Instead, proxy contact data
        is published for your domain. Email sent to the proxy email address is
        forwarded to the registrant's email address. Cloud Domains provides
        this privacy proxy service at no additional cost.
      REDACTED_CONTACT_DATA: The organization name (if provided) and limited
        non-identifying data from `ContactSettings` is available to the public
        (e.g. country and state). The remaining data is marked as `REDACTED
        FOR PRIVACY` in the WHOIS database. The actual information redacted
        depends on the domain. For details, see [the registration privacy
        article](https://support.google.com/domains/answer/3251242).
    """
    CONTACT_PRIVACY_UNSPECIFIED = 0
    PUBLIC_CONTACT_DATA = 1
    PRIVATE_CONTACT_DATA = 2
    REDACTED_CONTACT_DATA = 3

  class TransferFailureReasonValueValuesEnum(_messages.Enum):
    r"""Output only. Deprecated: For more information, see [Cloud Domains
    feature
    deprecation](https://cloud.google.com/domains/docs/deprecations/feature-
    deprecations). The reason the domain transfer failed. Only set for domains
    in TRANSFER_FAILED state.

    Values:
      TRANSFER_FAILURE_REASON_UNSPECIFIED: Transfer failure unspecified.
      TRANSFER_FAILURE_REASON_UNKNOWN: Transfer failed for an unknown reason.
      EMAIL_CONFIRMATION_FAILURE: An email confirmation sent to the user was
        rejected or expired.
      DOMAIN_NOT_REGISTERED: The domain is available for registration.
      DOMAIN_HAS_TRANSFER_LOCK: The domain has a transfer lock with its
        current registrar which must be removed prior to transfer.
      INVALID_AUTHORIZATION_CODE: The authorization code entered is not valid.
      TRANSFER_CANCELLED: The transfer was cancelled by the domain owner,
        current registrar, or TLD registry.
      TRANSFER_REJECTED: The transfer was rejected by the current registrar.
        Contact the current registrar for more information.
      INVALID_REGISTRANT_EMAIL_ADDRESS: The registrant email address cannot be
        parsed from the domain's current public contact data.
      DOMAIN_NOT_ELIGIBLE_FOR_TRANSFER: The domain is not eligible for
        transfer due requirements imposed by the current registrar or TLD
        registry.
      TRANSFER_ALREADY_PENDING: Another transfer is already pending for this
        domain. The existing transfer attempt must expire or be cancelled in
        order to proceed.
    """
    TRANSFER_FAILURE_REASON_UNSPECIFIED = 0
    TRANSFER_FAILURE_REASON_UNKNOWN = 1
    EMAIL_CONFIRMATION_FAILURE = 2
    DOMAIN_NOT_REGISTERED = 3
    DOMAIN_HAS_TRANSFER_LOCK = 4
    INVALID_AUTHORIZATION_CODE = 5
    TRANSFER_CANCELLED = 6
    TRANSFER_REJECTED = 7
    INVALID_REGISTRANT_EMAIL_ADDRESS = 8
    DOMAIN_NOT_ELIGIBLE_FOR_TRANSFER = 9
    TRANSFER_ALREADY_PENDING = 10

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Set of labels associated with the `Registration`.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  contactSettings = _messages.MessageField('ContactSettings', 1)
  createTime = _messages.StringField(2)
  dnsSettings = _messages.MessageField('DnsSettings', 3)
  domainName = _messages.StringField(4)
  domainProperties = _messages.EnumField('DomainPropertiesValueListEntryValuesEnum', 5, repeated=True)
  expireTime = _messages.StringField(6)
  issues = _messages.EnumField('IssuesValueListEntryValuesEnum', 7, repeated=True)
  labels = _messages.MessageField('LabelsValue', 8)
  managementSettings = _messages.MessageField('ManagementSettings', 9)
  name = _messages.StringField(10)
  pendingContactSettings = _messages.MessageField('ContactSettings', 11)
  provider = _messages.EnumField('ProviderValueValuesEnum', 12)
  registerFailureReason = _messages.EnumField('RegisterFailureReasonValueValuesEnum', 13)
  state = _messages.EnumField('StateValueValuesEnum', 14)
  supportedPrivacy = _messages.EnumField('SupportedPrivacyValueListEntryValuesEnum', 15, repeated=True)
  transferFailureReason = _messages.EnumField('TransferFailureReasonValueValuesEnum', 16)


class RenewDomainRequest(_messages.Message):
  r"""Request for the `RenewDomain` method.

  Fields:
    validateOnly: Optional. When true, only validation is performed, without
      actually renewing the domain. For more information, see [Request validat
      ion](https://cloud.google.com/apis/design/design_patterns#request_valida
      tion)
    yearlyPrice: Required. Acknowledgement of the price to renew the domain
      for one year. To get the price, see [Cloud Domains
      pricing](https://cloud.google.com/domains/pricing). If not provided, the
      expected price is returned in the error message.
  """

  validateOnly = _messages.BooleanField(1)
  yearlyPrice = _messages.MessageField('Money', 2)


class ResetAuthorizationCodeRequest(_messages.Message):
  r"""Request for the `ResetAuthorizationCode` method."""


class ResourceRecordSet(_messages.Message):
  r"""A unit of data that is returned by the DNS servers.

  Fields:
    name: For example, www.example.com.
    routingPolicy: Configures dynamic query responses based on either the geo
      location of the querying user or a weighted round robin based routing
      policy. A valid `ResourceRecordSet` contains only `rrdata` (for static
      resolution) or a `routing_policy` (for dynamic resolution).
    rrdata: As defined in RFC 1035 (section 5) and RFC 1034 (section 3.6.1) --
      see examples.
    signatureRrdata: As defined in RFC 4034 (section 3.2).
    ttl: Number of seconds that this `ResourceRecordSet` can be cached by
      resolvers.
    type: The identifier of a supported record type. See the list of Supported
      DNS record types.
  """

  name = _messages.StringField(1)
  routingPolicy = _messages.MessageField('RRSetRoutingPolicy', 2)
  rrdata = _messages.StringField(3, repeated=True)
  signatureRrdata = _messages.StringField(4, repeated=True)
  ttl = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  type = _messages.StringField(6)


class RetrieveGoogleDomainsDnsRecordsResponse(_messages.Message):
  r"""Response for the `RetrieveGoogleDomainsDnsRecords` method.

  Fields:
    nextPageToken: When present, there are more results to retrieve. Set
      `page_token` to this value on a subsequent call to get the next page of
      results.
    rrset: The resource record set resources (DNS Zone records).
  """

  nextPageToken = _messages.StringField(1)
  rrset = _messages.MessageField('ResourceRecordSet', 2, repeated=True)


class RetrieveGoogleDomainsForwardingConfigResponse(_messages.Message):
  r"""Response for the `RetrieveGoogleDomainsForwardingConfig` method.

  Fields:
    domainForwardings: The list of domain forwarding configurations. A
      forwarding configuration might not work correctly if the required DNS
      records are not present in the domain's authoritative DNS zone.
    emailForwardings: The list of email forwarding configurations. A
      forwarding configuration might not work correctly if the required DNS
      records are not present in the domain's authoritative DNS zone.
  """

  domainForwardings = _messages.MessageField('DomainForwarding', 1, repeated=True)
  emailForwardings = _messages.MessageField('EmailForwarding', 2, repeated=True)


class RetrieveImportableDomainsResponse(_messages.Message):
  r"""Deprecated: For more information, see [Cloud Domains feature
  deprecation](https://cloud.google.com/domains/docs/deprecations/feature-
  deprecations). Response for the `RetrieveImportableDomains` method.

  Fields:
    domains: A list of domains that the calling user manages in Google
      Domains.
    nextPageToken: When present, there are more results to retrieve. Set
      `page_token` to this value on a subsequent call to get the next page of
      results.
  """

  domains = _messages.MessageField('Domain', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class RetrieveRegisterParametersResponse(_messages.Message):
  r"""Response for the `RetrieveRegisterParameters` method.

  Fields:
    registerParameters: Parameters to use when calling the `RegisterDomain`
      method.
  """

  registerParameters = _messages.MessageField('RegisterParameters', 1)


class RetrieveTransferParametersResponse(_messages.Message):
  r"""Deprecated: For more information, see [Cloud Domains feature
  deprecation](https://cloud.google.com/domains/docs/deprecations/feature-
  deprecations). Response for the `RetrieveTransferParameters` method.

  Fields:
    transferParameters: Parameters to use when calling the `TransferDomain`
      method.
  """

  transferParameters = _messages.MessageField('TransferParameters', 1)


class SearchDomainsResponse(_messages.Message):
  r"""Response for the `SearchDomains` method.

  Fields:
    registerParameters: Results of the domain name search.
  """

  registerParameters = _messages.MessageField('RegisterParameters', 1, repeated=True)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TransferDomainRequest(_messages.Message):
  r"""Deprecated: For more information, see [Cloud Domains feature
  deprecation](https://cloud.google.com/domains/docs/deprecations/feature-
  deprecations). Request for the `TransferDomain` method.

  Enums:
    ContactNoticesValueListEntryValuesEnum:

  Fields:
    authorizationCode: The domain's transfer authorization code. You can
      obtain this from the domain's current registrar.
    contactNotices: The list of contact notices that you acknowledge. The
      notices needed here depend on the values specified in
      `registration.contact_settings`.
    registration: Required. The complete `Registration` resource to be
      created. You can leave `registration.dns_settings` unset to import the
      domain's current DNS configuration from its current registrar. Use this
      option only if you are sure that the domain's current DNS service does
      not cease upon transfer, as is often the case for DNS services provided
      for free by the registrar.
    validateOnly: Validate the request without actually transferring the
      domain.
    yearlyPrice: Required. Acknowledgement of the price to transfer or renew
      the domain for one year. Call `RetrieveTransferParameters` to obtain the
      price, which you must acknowledge.
  """

  class ContactNoticesValueListEntryValuesEnum(_messages.Enum):
    r"""ContactNoticesValueListEntryValuesEnum enum type.

    Values:
      CONTACT_NOTICE_UNSPECIFIED: The notice is undefined.
      PUBLIC_CONTACT_DATA_ACKNOWLEDGEMENT: Required when setting the `privacy`
        field of `ContactSettings` to `PUBLIC_CONTACT_DATA`, which exposes
        contact data publicly.
    """
    CONTACT_NOTICE_UNSPECIFIED = 0
    PUBLIC_CONTACT_DATA_ACKNOWLEDGEMENT = 1

  authorizationCode = _messages.MessageField('AuthorizationCode', 1)
  contactNotices = _messages.EnumField('ContactNoticesValueListEntryValuesEnum', 2, repeated=True)
  registration = _messages.MessageField('Registration', 3)
  validateOnly = _messages.BooleanField(4)
  yearlyPrice = _messages.MessageField('Money', 5)


class TransferParameters(_messages.Message):
  r"""Deprecated: For more information, see [Cloud Domains feature
  deprecation](https://cloud.google.com/domains/docs/deprecations/feature-
  deprecations). Parameters required to transfer a domain from another
  registrar.

  Enums:
    SupportedPrivacyValueListEntryValuesEnum:
    TransferLockStateValueValuesEnum: Indicates whether the domain is
      protected by a transfer lock. For a transfer to succeed, this must show
      `UNLOCKED`. To unlock a domain, go to its current registrar.

  Fields:
    currentRegistrar: The registrar that currently manages the domain.
    currentRegistrarUri: The URL of the registrar that currently manages the
      domain.
    domainName: The domain name. Unicode domain names are expressed in
      Punycode format.
    nameServers: The name servers that currently store the configuration of
      the domain.
    supportedPrivacy: Contact privacy options that the domain supports.
    transferLockState: Indicates whether the domain is protected by a transfer
      lock. For a transfer to succeed, this must show `UNLOCKED`. To unlock a
      domain, go to its current registrar.
    yearlyPrice: Price to transfer or renew the domain for one year.
  """

  class SupportedPrivacyValueListEntryValuesEnum(_messages.Enum):
    r"""SupportedPrivacyValueListEntryValuesEnum enum type.

    Values:
      CONTACT_PRIVACY_UNSPECIFIED: The contact privacy settings are undefined.
      PUBLIC_CONTACT_DATA: All the data from `ContactSettings` is publicly
        available. When setting this option, you must also provide a
        `PUBLIC_CONTACT_DATA_ACKNOWLEDGEMENT` in the `contact_notices` field
        of the request.
      PRIVATE_CONTACT_DATA: Deprecated: For more information, see [Cloud
        Domains feature deprecation](https://cloud.google.com/domains/docs/dep
        recations/feature-deprecations). None of the data from
        `ContactSettings` is publicly available. Instead, proxy contact data
        is published for your domain. Email sent to the proxy email address is
        forwarded to the registrant's email address. Cloud Domains provides
        this privacy proxy service at no additional cost.
      REDACTED_CONTACT_DATA: The organization name (if provided) and limited
        non-identifying data from `ContactSettings` is available to the public
        (e.g. country and state). The remaining data is marked as `REDACTED
        FOR PRIVACY` in the WHOIS database. The actual information redacted
        depends on the domain. For details, see [the registration privacy
        article](https://support.google.com/domains/answer/3251242).
    """
    CONTACT_PRIVACY_UNSPECIFIED = 0
    PUBLIC_CONTACT_DATA = 1
    PRIVATE_CONTACT_DATA = 2
    REDACTED_CONTACT_DATA = 3

  class TransferLockStateValueValuesEnum(_messages.Enum):
    r"""Indicates whether the domain is protected by a transfer lock. For a
    transfer to succeed, this must show `UNLOCKED`. To unlock a domain, go to
    its current registrar.

    Values:
      TRANSFER_LOCK_STATE_UNSPECIFIED: The state is unspecified.
      UNLOCKED: The domain is unlocked and can be transferred to another
        registrar.
      LOCKED: The domain is locked and cannot be transferred to another
        registrar.
    """
    TRANSFER_LOCK_STATE_UNSPECIFIED = 0
    UNLOCKED = 1
    LOCKED = 2

  currentRegistrar = _messages.StringField(1)
  currentRegistrarUri = _messages.StringField(2)
  domainName = _messages.StringField(3)
  nameServers = _messages.StringField(4, repeated=True)
  supportedPrivacy = _messages.EnumField('SupportedPrivacyValueListEntryValuesEnum', 5, repeated=True)
  transferLockState = _messages.EnumField('TransferLockStateValueValuesEnum', 6)
  yearlyPrice = _messages.MessageField('Money', 7)


class WrrPolicy(_messages.Message):
  r"""Configures a RRSetRoutingPolicy that routes in a weighted round robin
  fashion.

  Fields:
    item: A WrrPolicyItem attribute.
  """

  item = _messages.MessageField('WrrPolicyItem', 1, repeated=True)


class WrrPolicyItem(_messages.Message):
  r"""A routing block which contains the routing information for one WRR item.

  Fields:
    healthCheckedTargets: Endpoints that are health checked before making the
      routing decision. The unhealthy endpoints are omitted from the result.
      If all endpoints within a bucket are unhealthy, we choose a different
      bucket (sampled with respect to its weight) for responding. If DNSSEC is
      enabled for this zone, only one of `rrdata` or `health_checked_targets`
      can be set.
    rrdata: A string attribute.
    signatureRrdata: DNSSEC generated signatures for all the `rrdata` within
      this item. When using health-checked targets for DNSSEC-enabled zones,
      you can only use at most one health-checked IP address per item.
    weight: The weight corresponding to this `WrrPolicyItem` object. When
      multiple `WrrPolicyItem` objects are configured, the probability of
      returning an `WrrPolicyItem` object's data is proportional to its weight
      relative to the sum of weights configured for all items. This weight
      must be non-negative.
  """

  healthCheckedTargets = _messages.MessageField('HealthCheckTargets', 1)
  rrdata = _messages.StringField(2, repeated=True)
  signatureRrdata = _messages.StringField(3, repeated=True)
  weight = _messages.FloatField(4)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    DomainsProjectsLocationsRegistrationsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
