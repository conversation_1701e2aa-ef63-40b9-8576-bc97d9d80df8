"""Generated client library for cloudlocationfinder version v1alpha."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.cloudlocationfinder.v1alpha import cloudlocationfinder_v1alpha_messages as messages


class CloudlocationfinderV1alpha(base_api.BaseApiClient):
  """Generated client library for service cloudlocationfinder version v1alpha."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://cloudlocationfinder.googleapis.com/'
  MTLS_BASE_URL = 'https://cloudlocationfinder.mtls.googleapis.com/'

  _PACKAGE = 'cloudlocationfinder'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1alpha'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'CloudlocationfinderV1alpha'
  _URL_VERSION = 'v1alpha'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new cloudlocationfinder handle."""
    url = url or self.BASE_URL
    super(CloudlocationfinderV1alpha, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_cloudLocations = self.ProjectsLocationsCloudLocationsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsCloudLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations_cloudLocations resource."""

    _NAME = 'projects_locations_cloudLocations'

    def __init__(self, client):
      super(CloudlocationfinderV1alpha.ProjectsLocationsCloudLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Retrieves a resource containing information about a cloud location.

      Args:
        request: (CloudlocationfinderProjectsLocationsCloudLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CloudLocation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/cloudLocations/{cloudLocationsId}',
        http_method='GET',
        method_id='cloudlocationfinder.projects.locations.cloudLocations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='CloudlocationfinderProjectsLocationsCloudLocationsGetRequest',
        response_type_name='CloudLocation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists cloud locations under a given project and location.

      Args:
        request: (CloudlocationfinderProjectsLocationsCloudLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListCloudLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/cloudLocations',
        http_method='GET',
        method_id='cloudlocationfinder.projects.locations.cloudLocations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/cloudLocations',
        request_field='',
        request_type_name='CloudlocationfinderProjectsLocationsCloudLocationsListRequest',
        response_type_name='ListCloudLocationsResponse',
        supports_download=False,
    )

    def Search(self, request, global_params=None):
      r"""Searches for cloud locations from a given source location.

      Args:
        request: (CloudlocationfinderProjectsLocationsCloudLocationsSearchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SearchCloudLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('Search')
      return self._RunMethod(
          config, request, global_params=global_params)

    Search.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/cloudLocations:search',
        http_method='GET',
        method_id='cloudlocationfinder.projects.locations.cloudLocations.search',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'query', 'sourceCloudLocation'],
        relative_path='v1alpha/{+parent}/cloudLocations:search',
        request_field='',
        request_type_name='CloudlocationfinderProjectsLocationsCloudLocationsSearchRequest',
        response_type_name='SearchCloudLocationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(CloudlocationfinderV1alpha.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (CloudlocationfinderProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='cloudlocationfinder.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='CloudlocationfinderProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (CloudlocationfinderProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations',
        http_method='GET',
        method_id='cloudlocationfinder.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+name}/locations',
        request_field='',
        request_type_name='CloudlocationfinderProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(CloudlocationfinderV1alpha.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
