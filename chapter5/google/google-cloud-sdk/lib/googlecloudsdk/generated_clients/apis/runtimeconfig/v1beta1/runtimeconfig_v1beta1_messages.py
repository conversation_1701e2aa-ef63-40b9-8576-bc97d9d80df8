"""Generated message classes for runtimeconfig version v1beta1.

The Runtime Configurator allows you to dynamically configure and expose
variables through Google Cloud Platform. In addition, you can also set
Watchers and Waiters that will watch for changes to your data and return based
on certain conditions.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'runtimeconfig'


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    ignoreChildExemptions: A boolean attribute.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  ignoreChildExemptions = _messages.BooleanField(2)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 3)


class AuthorizationLoggingOptions(_messages.Message):
  r"""Authorization-related information used by Cloud Audit Logging.

  Enums:
    PermissionTypeValueValuesEnum: The type of the permission that was
      checked.

  Fields:
    permissionType: The type of the permission that was checked.
  """

  class PermissionTypeValueValuesEnum(_messages.Enum):
    r"""The type of the permission that was checked.

    Values:
      PERMISSION_TYPE_UNSPECIFIED: Default. Should not be used.
      ADMIN_READ: A read of admin (meta) data.
      ADMIN_WRITE: A write of admin (meta) data.
      DATA_READ: A read of standard data.
      DATA_WRITE: A write of standard data.
    """
    PERMISSION_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    ADMIN_WRITE = 2
    DATA_READ = 3
    DATA_WRITE = 4

  permissionType = _messages.EnumField('PermissionTypeValueValuesEnum', 1)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    bindingId: A string attribute.
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  bindingId = _messages.StringField(1)
  condition = _messages.MessageField('Expr', 2)
  members = _messages.StringField(3, repeated=True)
  role = _messages.StringField(4)


class Cardinality(_messages.Message):
  r"""A Cardinality condition for the Waiter resource. A cardinality condition
  is met when the number of variables under a specified path prefix reaches a
  predefined number. For example, if you set a Cardinality condition where the
  `path` is set to `/foo` and the number of paths is set to `2`, the following
  variables would meet the condition in a RuntimeConfig resource: +
  `/foo/variable1 = "value1"` + `/foo/variable2 = "value2"` + `/bar/variable3
  = "value3"` It would not satisfy the same condition with the `number` set to
  `3`, however, because there is only 2 paths that start with `/foo`.
  Cardinality conditions are recursive; all subtrees under the specific path
  prefix are counted.

  Fields:
    number: The number variables under the `path` that must exist to meet this
      condition. Defaults to 1 if not specified.
    path: The root of the variable subtree to monitor. For example, `/foo`.
  """

  number = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  path = _messages.StringField(2)


class CloudAuditOptions(_messages.Message):
  r"""Write a Cloud Audit log

  Enums:
    LogNameValueValuesEnum: The log_name to populate in the Cloud Audit
      Record.
    PermissionTypeValueValuesEnum: The type associated with the permission.

  Fields:
    authorizationLoggingOptions: Information used by the Cloud Audit Logging
      pipeline. Will be deprecated once the migration to PermissionType is
      complete (b/201806118).
    logName: The log_name to populate in the Cloud Audit Record.
    permissionType: The type associated with the permission.
  """

  class LogNameValueValuesEnum(_messages.Enum):
    r"""The log_name to populate in the Cloud Audit Record.

    Values:
      UNSPECIFIED_LOG_NAME: Default. Should not be used.
      ADMIN_ACTIVITY: Corresponds to "cloudaudit.googleapis.com/activity"
      DATA_ACCESS: Corresponds to "cloudaudit.googleapis.com/data_access"
    """
    UNSPECIFIED_LOG_NAME = 0
    ADMIN_ACTIVITY = 1
    DATA_ACCESS = 2

  class PermissionTypeValueValuesEnum(_messages.Enum):
    r"""The type associated with the permission.

    Values:
      PERMISSION_TYPE_UNSPECIFIED: Default. Should not be used.
      ADMIN_READ: Permissions that gate reading resource configuration or
        metadata.
      ADMIN_WRITE: Permissions that gate modification of resource
        configuration or metadata.
      DATA_READ: Permissions that gate reading user-provided data.
      DATA_WRITE: Permissions that gate writing user-provided data.
    """
    PERMISSION_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    ADMIN_WRITE = 2
    DATA_READ = 3
    DATA_WRITE = 4

  authorizationLoggingOptions = _messages.MessageField('AuthorizationLoggingOptions', 1)
  logName = _messages.EnumField('LogNameValueValuesEnum', 2)
  permissionType = _messages.EnumField('PermissionTypeValueValuesEnum', 3)


class Condition(_messages.Message):
  r"""A condition to be met.

  Enums:
    IamValueValuesEnum: Trusted attributes supplied by the IAM system.
    OpValueValuesEnum: An operator to apply the subject with.
    SysValueValuesEnum: Trusted attributes supplied by any service that owns
      resources and uses the IAM system for access control.

  Fields:
    iam: Trusted attributes supplied by the IAM system.
    op: An operator to apply the subject with.
    svc: Trusted attributes discharged by the service.
    sys: Trusted attributes supplied by any service that owns resources and
      uses the IAM system for access control.
    values: The objects of the condition.
  """

  class IamValueValuesEnum(_messages.Enum):
    r"""Trusted attributes supplied by the IAM system.

    Values:
      NO_ATTR: Default non-attribute.
      AUTHORITY: Either principal or (if present) authority selector.
      ATTRIBUTION: The principal (even if an authority selector is present),
        which must only be used for attribution, not authorization.
      SECURITY_REALM: Any of the security realms in the IAMContext
        (go/security-realms). When used with IN, the condition indicates "any
        of the request's realms match one of the given values; with NOT_IN,
        "none of the realms match any of the given values". Note that a value
        can be: - 'self:campus' (i.e., clients that are in the same campus) -
        'self:metro' (i.e., clients that are in the same metro) - 'self:cloud-
        region' (i.e., allow connections from clients that are in the same
        cloud region) - 'self:prod-region' (i.e., allow connections from
        clients that are in the same prod region) - 'guardians' (i.e., allow
        connections from its guardian realms. See go/security-realms-
        glossary#guardian for more information.) - 'cryto_core_guardians'
        (i.e., allow connections from its crypto core guardian realms. See
        go/security-realms-glossary#guardian for more information.) Crypto
        Core coverage is a super-set of Default coverage, containing
        information about coverage between higher tier data centers (e.g.,
        YAWNs). Most services should use Default coverage and only use Crypto
        Core coverage if the service is involved in greenfield turnup of new
        higher tier data centers (e.g., credential infrastructure, machine/job
        management systems, etc.). - 'self' [DEPRECATED] (i.e., allow
        connections from clients that are in the same security realm, which is
        currently but not guaranteed to be campus-sized) - a realm (e.g.,
        'campus-abc') - a realm group (e.g., 'realms-for-borg-cell-xx', see:
        go/realm-groups) A match is determined by a realm group membership
        check performed by a RealmAclRep object (go/realm-acl-howto). It is
        not permitted to grant access based on the *absence* of a realm, so
        realm conditions can only be used in a "positive" context (e.g.,
        ALLOW/IN or DENY/NOT_IN).
      APPROVER: An approver (distinct from the requester) that has authorized
        this request. When used with IN, the condition indicates that one of
        the approvers associated with the request matches the specified
        principal, or is a member of the specified group. Approvers can only
        grant additional access, and are thus only used in a strictly positive
        context (e.g. ALLOW/IN or DENY/NOT_IN).
      JUSTIFICATION_TYPE: What types of justifications have been supplied with
        this request. String values should match enum names from
        security.credentials.JustificationType, e.g. "MANUAL_STRING". It is
        not permitted to grant access based on the *absence* of a
        justification, so justification conditions can only be used in a
        "positive" context (e.g., ALLOW/IN or DENY/NOT_IN). Multiple
        justifications, e.g., a Buganizer ID and a manually-entered reason,
        are normal and supported.
      CREDENTIALS_TYPE: What type of credentials have been supplied with this
        request. String values should match enum names from
        security_loas_l2.CredentialsType - currently, only
        CREDS_TYPE_EMERGENCY is supported. It is not permitted to grant access
        based on the *absence* of a credentials type, so the conditions can
        only be used in a "positive" context (e.g., ALLOW/IN or DENY/NOT_IN).
      CREDS_ASSERTION: Properties of the credentials supplied with this
        request. See http://go/rpcsp-credential-assertions?polyglot=rpcsp-v1-0
        The conditions can only be used in a "positive" context (e.g.,
        ALLOW/IN or DENY/NOT_IN).
    """
    NO_ATTR = 0
    AUTHORITY = 1
    ATTRIBUTION = 2
    SECURITY_REALM = 3
    APPROVER = 4
    JUSTIFICATION_TYPE = 5
    CREDENTIALS_TYPE = 6
    CREDS_ASSERTION = 7

  class OpValueValuesEnum(_messages.Enum):
    r"""An operator to apply the subject with.

    Values:
      NO_OP: Default no-op.
      EQUALS: DEPRECATED. Use IN instead.
      NOT_EQUALS: DEPRECATED. Use NOT_IN instead.
      IN: The condition is true if the subject (or any element of it if it is
        a set) matches any of the supplied values.
      NOT_IN: The condition is true if the subject (or every element of it if
        it is a set) matches none of the supplied values.
      DISCHARGED: Subject is discharged
    """
    NO_OP = 0
    EQUALS = 1
    NOT_EQUALS = 2
    IN = 3
    NOT_IN = 4
    DISCHARGED = 5

  class SysValueValuesEnum(_messages.Enum):
    r"""Trusted attributes supplied by any service that owns resources and
    uses the IAM system for access control.

    Values:
      NO_ATTR: Default non-attribute type
      REGION: Region of the resource
      SERVICE: Service name
      NAME: Resource name
      IP: IP address of the caller
    """
    NO_ATTR = 0
    REGION = 1
    SERVICE = 2
    NAME = 3
    IP = 4

  iam = _messages.EnumField('IamValueValuesEnum', 1)
  op = _messages.EnumField('OpValueValuesEnum', 2)
  svc = _messages.StringField(3)
  sys = _messages.EnumField('SysValueValuesEnum', 4)
  values = _messages.StringField(5, repeated=True)


class CounterOptions(_messages.Message):
  r"""Increment a streamz counter with the specified metric and field names.
  Metric names should start with a '/', generally be lowercase-only, and end
  in "_count". Field names should not contain an initial slash. The actual
  exported metric names will have "/iam/policy" prepended. Field names
  correspond to IAM request parameters and field values are their respective
  values. Supported field names: - "authority", which is "[token]" if
  IAMContext.token is present, otherwise the value of
  IAMContext.authority_selector if present, and otherwise a representation of
  IAMContext.principal; or - "iam_principal", a representation of
  IAMContext.principal even if a token or authority selector is present; or -
  "" (empty string), resulting in a counter with no fields. Examples: counter
  { metric: "/debug_access_count" field: "iam_principal" } ==> increment
  counter /iam/policy/debug_access_count {iam_principal=[value of
  IAMContext.principal]}

  Fields:
    customFields: Custom fields.
    field: The field value to attribute.
    metric: The metric to update.
  """

  customFields = _messages.MessageField('CustomField', 1, repeated=True)
  field = _messages.StringField(2)
  metric = _messages.StringField(3)


class CustomField(_messages.Message):
  r"""Custom fields. These can be used to create a counter with arbitrary
  field/value pairs. See: go/rpcsp-custom-fields.

  Fields:
    name: Name is the field name.
    value: Value is the field value. It is important that in contrast to the
      CounterOptions.field, the value here is a constant that is not derived
      from the IAMContext.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class DataAccessOptions(_messages.Message):
  r"""Write a Data Access (Gin) log

  Enums:
    LogModeValueValuesEnum:

  Fields:
    isDirectAuth: Indicates that access was granted by a regular grant policy
    logMode: A LogModeValueValuesEnum attribute.
  """

  class LogModeValueValuesEnum(_messages.Enum):
    r"""LogModeValueValuesEnum enum type.

    Values:
      LOG_MODE_UNSPECIFIED: Client is not required to write a partial Gin log
        immediately after the authorization check. If client chooses to write
        one and it fails, client may either fail open (allow the operation to
        continue) or fail closed (handle as a DENY outcome).
      LOG_FAIL_CLOSED: The application's operation in the context of which
        this authorization check is being made may only be performed if it is
        successfully logged to Gin. For instance, the authorization library
        may satisfy this obligation by emitting a partial log entry at
        authorization check time and only returning ALLOW to the application
        if it succeeds. If a matching Rule has this directive, but the client
        has not indicated that it will honor such requirements, then the IAM
        check will result in authorization failure by setting
        CheckPolicyResponse.success=false.
    """
    LOG_MODE_UNSPECIFIED = 0
    LOG_FAIL_CLOSED = 1

  isDirectAuth = _messages.BooleanField(1)
  logMode = _messages.EnumField('LogModeValueValuesEnum', 2)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EndCondition(_messages.Message):
  r"""The condition that a Waiter resource is waiting for.

  Fields:
    cardinality: The cardinality of the `EndCondition`.
  """

  cardinality = _messages.MessageField('Cardinality', 1)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class ListConfigsResponse(_messages.Message):
  r"""`ListConfigs()` returns the following response. The order of returned
  objects is arbitrary; that is, it is not ordered in any particular way.

  Fields:
    configs: A list of the configurations in the project. The order of
      returned objects is arbitrary; that is, it is not ordered in any
      particular way.
    nextPageToken: This token allows you to get the next page of results for
      list requests. If the number of results is larger than `pageSize`, use
      the `nextPageToken` as a value for the query parameter `pageToken` in
      the next list request. Subsequent list requests will have their own
      `nextPageToken` to continue paging through the results
  """

  configs = _messages.MessageField('RuntimeConfig', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListVariablesResponse(_messages.Message):
  r"""Response for the `ListVariables()` method.

  Fields:
    nextPageToken: This token allows you to get the next page of results for
      list requests. If the number of results is larger than `pageSize`, use
      the `nextPageToken` as a value for the query parameter `pageToken` in
      the next list request. Subsequent list requests will have their own
      `nextPageToken` to continue paging through the results
    variables: A list of variables and their values. The order of returned
      variable objects is arbitrary.
  """

  nextPageToken = _messages.StringField(1)
  variables = _messages.MessageField('Variable', 2, repeated=True)


class ListWaitersResponse(_messages.Message):
  r"""Response for the `ListWaiters()` method. Order of returned waiter
  objects is arbitrary.

  Fields:
    nextPageToken: This token allows you to get the next page of results for
      list requests. If the number of results is larger than `pageSize`, use
      the `nextPageToken` as a value for the query parameter `pageToken` in
      the next list request. Subsequent list requests will have their own
      `nextPageToken` to continue paging through the results
    waiters: Found waiters in the project.
  """

  nextPageToken = _messages.StringField(1)
  waiters = _messages.MessageField('Waiter', 2, repeated=True)


class LogConfig(_messages.Message):
  r"""Specifies what kind of log the caller must write

  Fields:
    cloudAudit: Cloud audit options.
    counter: Counter options.
    dataAccess: Data access options.
  """

  cloudAudit = _messages.MessageField('CloudAuditOptions', 1)
  counter = _messages.MessageField('CounterOptions', 2)
  dataAccess = _messages.MessageField('DataAccessOptions', 3)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    rules: If more than one rule is specified, the rules are applied in the
      following manner: - All matching LOG rules are always applied. - If any
      DENY/DENY_WITH_LOG rule matches, permission is denied. Logging will be
      applied if one or more matching rule requires logging. - Otherwise, if
      any ALLOW/ALLOW_WITH_LOG rule matches, permission is granted. Logging
      will be applied if one or more matching rule requires logging. -
      Otherwise, if no rule applies, permission is denied.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  rules = _messages.MessageField('Rule', 4, repeated=True)
  version = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class Rule(_messages.Message):
  r"""A rule to be applied in a Policy.

  Enums:
    ActionValueValuesEnum: Required

  Fields:
    action: Required
    conditions: Additional restrictions that must be met. All conditions must
      pass for the rule to match.
    description: Human-readable description of the rule.
    in_: If one or more 'in' clauses are specified, the rule matches if the
      PRINCIPAL/AUTHORITY_SELECTOR is in at least one of these entries.
    logConfig: The config returned to callers of CheckPolicy for any entries
      that match the LOG action.
    notIn: If one or more 'not_in' clauses are specified, the rule matches if
      the PRINCIPAL/AUTHORITY_SELECTOR is in none of the entries. The format
      for in and not_in entries can be found at in the Local IAM documentation
      (see go/local-iam#features).
    permissions: A permission is a string of form `..` (e.g.,
      'storage.buckets.list'). A value of '*' matches all permissions, and a
      verb part of '*' (e.g., 'storage.buckets.*') matches all verbs.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required

    Values:
      NO_ACTION: Default no action.
      ALLOW: Matching 'Entries' grant access.
      ALLOW_WITH_LOG: Matching 'Entries' grant access and the caller promises
        to log the request per the returned log_configs.
      DENY: Matching 'Entries' deny access.
      DENY_WITH_LOG: Matching 'Entries' deny access and the caller promises to
        log the request per the returned log_configs.
      LOG: Matching 'Entries' tell IAM.Check callers to generate logs.
    """
    NO_ACTION = 0
    ALLOW = 1
    ALLOW_WITH_LOG = 2
    DENY = 3
    DENY_WITH_LOG = 4
    LOG = 5

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  conditions = _messages.MessageField('Condition', 2, repeated=True)
  description = _messages.StringField(3)
  in_ = _messages.StringField(4, repeated=True)
  logConfig = _messages.MessageField('LogConfig', 5, repeated=True)
  notIn = _messages.StringField(6, repeated=True)
  permissions = _messages.StringField(7, repeated=True)


class RuntimeConfig(_messages.Message):
  r"""A RuntimeConfig resource is the primary resource in the Cloud
  RuntimeConfig service. A RuntimeConfig resource consists of metadata and a
  hierarchy of variables.

  Fields:
    description: An optional description of the RuntimeConfig object.
    name: The resource name of a runtime config. The name must have the
      format: projects/[PROJECT_ID]/configs/[CONFIG_NAME] The `[PROJECT_ID]`
      must be a valid project ID, and `[CONFIG_NAME]` is an arbitrary name
      that matches the `[0-9A-Za-z](?:[_.A-Za-z0-9-]{0,62}[_.A-Za-z0-9])?`
      regular expression. The length of `[CONFIG_NAME]` must be less than 64
      characters. You pick the RuntimeConfig resource name, but the server
      will validate that the name adheres to this format. After you create the
      resource, you cannot change the resource's name.
  """

  description = _messages.StringField(1)
  name = _messages.StringField(2)


class RuntimeconfigProjectsConfigsCreateRequest(_messages.Message):
  r"""A RuntimeconfigProjectsConfigsCreateRequest object.

  Fields:
    parent: The [project ID](https://support.google.com/cloud/answer/6158840?h
      l=en&ref_topic=6158848) for this request, in the format
      `projects/[PROJECT_ID]`.
    requestId: An optional but recommended unique `request_id`. If the server
      receives two `create()` requests with the same `request_id`, then the
      second request will be ignored and the first resource created and stored
      in the backend is returned. Empty `request_id` fields are ignored. It is
      responsibility of the client to ensure uniqueness of the `request_id`
      strings. `request_id` strings are limited to 64 characters.
    runtimeConfig: A RuntimeConfig resource to be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  runtimeConfig = _messages.MessageField('RuntimeConfig', 3)


class RuntimeconfigProjectsConfigsDeleteRequest(_messages.Message):
  r"""A RuntimeconfigProjectsConfigsDeleteRequest object.

  Fields:
    name: The RuntimeConfig resource to delete, in the format:
      `projects/[PROJECT_ID]/configs/[CONFIG_NAME]`
  """

  name = _messages.StringField(1, required=True)


class RuntimeconfigProjectsConfigsGetIamPolicyRequest(_messages.Message):
  r"""A RuntimeconfigProjectsConfigsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class RuntimeconfigProjectsConfigsGetRequest(_messages.Message):
  r"""A RuntimeconfigProjectsConfigsGetRequest object.

  Fields:
    name: The name of the RuntimeConfig resource to retrieve, in the format:
      `projects/[PROJECT_ID]/configs/[CONFIG_NAME]`
  """

  name = _messages.StringField(1, required=True)


class RuntimeconfigProjectsConfigsListRequest(_messages.Message):
  r"""A RuntimeconfigProjectsConfigsListRequest object.

  Fields:
    pageSize: Specifies the number of results to return per page. If there are
      fewer elements than the specified number, returns all elements.
    pageToken: Specifies a page token to use. Set `pageToken` to a
      `nextPageToken` returned by a previous list request to get the next page
      of results.
    parent: The [project ID](https://support.google.com/cloud/answer/6158840?h
      l=en&ref_topic=6158848) for this request, in the format
      `projects/[PROJECT_ID]`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class RuntimeconfigProjectsConfigsOperationsGetRequest(_messages.Message):
  r"""A RuntimeconfigProjectsConfigsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class RuntimeconfigProjectsConfigsOperationsTestIamPermissionsRequest(_messages.Message):
  r"""A RuntimeconfigProjectsConfigsOperationsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class RuntimeconfigProjectsConfigsSetIamPolicyRequest(_messages.Message):
  r"""A RuntimeconfigProjectsConfigsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class RuntimeconfigProjectsConfigsTestIamPermissionsRequest(_messages.Message):
  r"""A RuntimeconfigProjectsConfigsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class RuntimeconfigProjectsConfigsVariablesCreateRequest(_messages.Message):
  r"""A RuntimeconfigProjectsConfigsVariablesCreateRequest object.

  Fields:
    parent: The path to the RutimeConfig resource that this variable should
      belong to. The configuration must exist beforehand; the path must be in
      the format: `projects/[PROJECT_ID]/configs/[CONFIG_NAME]`
    requestId: An optional but recommended unique `request_id`. If the server
      receives two `create()` requests with the same `request_id`, then the
      second request will be ignored and the first resource created and stored
      in the backend is returned. Empty `request_id` fields are ignored. It is
      responsibility of the client to ensure uniqueness of the `request_id`
      strings. `request_id` strings are limited to 64 characters.
    variable: A Variable resource to be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  variable = _messages.MessageField('Variable', 3)


class RuntimeconfigProjectsConfigsVariablesDeleteRequest(_messages.Message):
  r"""A RuntimeconfigProjectsConfigsVariablesDeleteRequest object.

  Fields:
    name: The name of the variable to delete, in the format:
      `projects/[PROJECT_ID]/configs/[CONFIG_NAME]/variables/[VARIABLE_NAME]`
    recursive: Set to `true` to recursively delete multiple variables with the
      same prefix.
  """

  name = _messages.StringField(1, required=True)
  recursive = _messages.BooleanField(2)


class RuntimeconfigProjectsConfigsVariablesGetRequest(_messages.Message):
  r"""A RuntimeconfigProjectsConfigsVariablesGetRequest object.

  Fields:
    name: The name of the variable to return, in the format:
      `projects/[PROJECT_ID]/configs/[CONFIG_NAME]/variables/[VARIBLE_NAME]`
  """

  name = _messages.StringField(1, required=True)


class RuntimeconfigProjectsConfigsVariablesListRequest(_messages.Message):
  r"""A RuntimeconfigProjectsConfigsVariablesListRequest object.

  Fields:
    filter: Filters variables by matching the specified filter. For example:
      `projects/example-project/config/[CONFIG_NAME]/variables/example-
      variable`.
    pageSize: Specifies the number of results to return per page. If there are
      fewer elements than the specified number, returns all elements.
    pageToken: Specifies a page token to use. Set `pageToken` to a
      `nextPageToken` returned by a previous list request to get the next page
      of results.
    parent: The path to the RuntimeConfig resource for which you want to list
      variables. The configuration must exist beforehand; the path must be in
      the format: `projects/[PROJECT_ID]/configs/[CONFIG_NAME]`
    returnValues: The flag indicates whether the user wants to return values
      of variables. If true, then only those variables that user has IAM
      GetVariable permission will be returned along with their values.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  returnValues = _messages.BooleanField(5)


class RuntimeconfigProjectsConfigsVariablesTestIamPermissionsRequest(_messages.Message):
  r"""A RuntimeconfigProjectsConfigsVariablesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class RuntimeconfigProjectsConfigsVariablesWatchRequest(_messages.Message):
  r"""A RuntimeconfigProjectsConfigsVariablesWatchRequest object.

  Fields:
    name: The name of the variable to watch, in the format:
      `projects/[PROJECT_ID]/configs/[CONFIG_NAME]`
    watchVariableRequest: A WatchVariableRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  watchVariableRequest = _messages.MessageField('WatchVariableRequest', 2)


class RuntimeconfigProjectsConfigsWaitersCreateRequest(_messages.Message):
  r"""A RuntimeconfigProjectsConfigsWaitersCreateRequest object.

  Fields:
    parent: The path to the configuration that will own the waiter. The
      configuration must exist beforehand; the path must be in the format:
      `projects/[PROJECT_ID]/configs/[CONFIG_NAME]`.
    requestId: An optional but recommended unique `request_id`. If the server
      receives two `create()` requests with the same `request_id`, then the
      second request will be ignored and the first resource created and stored
      in the backend is returned. Empty `request_id` fields are ignored. It is
      responsibility of the client to ensure uniqueness of the `request_id`
      strings. `request_id` strings are limited to 64 characters.
    waiter: A Waiter resource to be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  waiter = _messages.MessageField('Waiter', 3)


class RuntimeconfigProjectsConfigsWaitersDeleteRequest(_messages.Message):
  r"""A RuntimeconfigProjectsConfigsWaitersDeleteRequest object.

  Fields:
    name: The Waiter resource to delete, in the format:
      `projects/[PROJECT_ID]/configs/[CONFIG_NAME]/waiters/[WAITER_NAME]`
  """

  name = _messages.StringField(1, required=True)


class RuntimeconfigProjectsConfigsWaitersGetRequest(_messages.Message):
  r"""A RuntimeconfigProjectsConfigsWaitersGetRequest object.

  Fields:
    name: The fully-qualified name of the Waiter resource object to retrieve,
      in the format:
      `projects/[PROJECT_ID]/configs/[CONFIG_NAME]/waiters/[WAITER_NAME]`
  """

  name = _messages.StringField(1, required=True)


class RuntimeconfigProjectsConfigsWaitersListRequest(_messages.Message):
  r"""A RuntimeconfigProjectsConfigsWaitersListRequest object.

  Fields:
    pageSize: Specifies the number of results to return per page. If there are
      fewer elements than the specified number, returns all elements.
    pageToken: Specifies a page token to use. Set `pageToken` to a
      `nextPageToken` returned by a previous list request to get the next page
      of results.
    parent: The path to the configuration for which you want to get a list of
      waiters. The configuration must exist beforehand; the path must be in
      the format: `projects/[PROJECT_ID]/configs/[CONFIG_NAME]`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class RuntimeconfigProjectsConfigsWaitersTestIamPermissionsRequest(_messages.Message):
  r"""A RuntimeconfigProjectsConfigsWaitersTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class Variable(_messages.Message):
  r"""Describes a single variable within a RuntimeConfig resource. The name
  denotes the hierarchical variable name. For example, `ports/serving_port` is
  a valid variable name. The variable value is an opaque string and only leaf
  variables can have values (that is, variables that do not have any child
  variables).

  Enums:
    StateValueValuesEnum: Output only. The current state of the variable. The
      variable state indicates the outcome of the `variables().watch` call and
      is visible through the `get` and `list` calls.

  Fields:
    name: The name of the variable resource, in the format:
      projects/[PROJECT_ID]/configs/[CONFIG_NAME]/variables/[VARIABLE_NAME]
      The `[PROJECT_ID]` must be a valid project ID, `[CONFIG_NAME]` must be a
      valid RuntimeConfig resource and `[VARIABLE_NAME]` follows Unix file
      system file path naming. The `[VARIABLE_NAME]` can contain ASCII
      letters, numbers, slashes and dashes. Slashes are used as path element
      separators and are not part of the `[VARIABLE_NAME]` itself, so
      `[VARIABLE_NAME]` must contain at least one non-slash character.
      Multiple slashes are coalesced into single slash character. Each path
      segment should match [0-9A-Za-z](?:[_.A-Za-z0-9-]{0,62}[_.A-Za-z0-9])?
      regular expression. The length of a `[VARIABLE_NAME]` must be less than
      256 characters. Once you create a variable, you cannot change the
      variable name.
    state: Output only. The current state of the variable. The variable state
      indicates the outcome of the `variables().watch` call and is visible
      through the `get` and `list` calls.
    text: The string value of the variable. The length of the value must be
      less than 4096 bytes. Empty values are also accepted. For example,
      `text: "my text value"`. The string must be valid UTF-8.
    updateTime: Output only. The time of the last variable update. Timestamp
      will be UTC timestamp.
    value: The binary value of the variable. The length of the value must be
      less than 4096 bytes. Empty values are also accepted. The value must be
      base64 encoded, and must comply with IETF RFC4648
      (https://www.ietf.org/rfc/rfc4648.txt). Only one of `value` or `text`
      can be set.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the variable. The variable state
    indicates the outcome of the `variables().watch` call and is visible
    through the `get` and `list` calls.

    Values:
      VARIABLE_STATE_UNSPECIFIED: Default variable state.
      UPDATED: The variable was updated, while `variables().watch` was
        executing.
      DELETED: The variable was deleted, while `variables().watch` was
        executing.
    """
    VARIABLE_STATE_UNSPECIFIED = 0
    UPDATED = 1
    DELETED = 2

  name = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)
  text = _messages.StringField(3)
  updateTime = _messages.StringField(4)
  value = _messages.BytesField(5)


class Waiter(_messages.Message):
  r"""A Waiter resource waits for some end condition within a RuntimeConfig
  resource to be met before it returns. For example, assume you have a
  distributed system where each node writes to a Variable resource indicating
  the node's readiness as part of the startup process. You then configure a
  Waiter resource with the success condition set to wait until some number of
  nodes have checked in. Afterwards, your application runs some arbitrary code
  after the condition has been met and the waiter returns successfully. Once
  created, a Waiter resource is immutable. To learn more about using waiters,
  read the [Creating a Waiter](/deployment-manager/runtime-
  configurator/creating-a-waiter) documentation.

  Fields:
    createTime: Output only. The instant at which this Waiter resource was
      created. Adding the value of `timeout` to this instant yields the
      timeout deadline for the waiter.
    done: Output only. If the value is `false`, it means the waiter is still
      waiting for one of its conditions to be met. If true, the waiter has
      finished. If the waiter finished due to a timeout or failure, `error`
      will be set.
    error: Output only. If the waiter ended due to a failure or timeout, this
      value will be set.
    failure: [Optional] The failure condition of this waiter. If this
      condition is met, `done` will be set to `true` and the `error` code will
      be set to `ABORTED`. The failure condition takes precedence over the
      success condition. If both conditions are met, a failure will be
      indicated. This value is optional; if no failure condition is set, the
      only failure scenario will be a timeout.
    name: The name of the Waiter resource, in the format:
      projects/[PROJECT_ID]/configs/[CONFIG_NAME]/waiters/[WAITER_NAME] The
      `[PROJECT_ID]` must be a valid Google Cloud project ID, the
      `[CONFIG_NAME]` must be a valid RuntimeConfig resource, the
      `[WAITER_NAME]` must match RFC 1035 segment specification, and the
      length of `[WAITER_NAME]` must be less than 64 bytes. After you create a
      Waiter resource, you cannot change the resource name.
    success: [Required] The success condition. If this condition is met,
      `done` will be set to `true` and the `error` value will remain unset.
      The failure condition takes precedence over the success condition. If
      both conditions are met, a failure will be indicated.
    timeout: [Required] Specifies the timeout of the waiter in seconds,
      beginning from the instant that `waiters().create` method is called. If
      this time elapses before the success or failure conditions are met, the
      waiter fails and sets the `error` code to `DEADLINE_EXCEEDED`.
  """

  createTime = _messages.StringField(1)
  done = _messages.BooleanField(2)
  error = _messages.MessageField('Status', 3)
  failure = _messages.MessageField('EndCondition', 4)
  name = _messages.StringField(5)
  success = _messages.MessageField('EndCondition', 6)
  timeout = _messages.StringField(7)


class WatchVariableRequest(_messages.Message):
  r"""Request for the `WatchVariable()` method.

  Fields:
    newerThan: If specified, checks the current timestamp of the variable and
      if the current timestamp is newer than `newerThan` timestamp, the method
      returns immediately. If not specified or the variable has an older
      timestamp, the watcher waits for a the value to change before returning.
  """

  newerThan = _messages.StringField(1)


encoding.AddCustomJsonFieldMapping(
    Rule, 'in_', 'in')
encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    RuntimeconfigProjectsConfigsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
