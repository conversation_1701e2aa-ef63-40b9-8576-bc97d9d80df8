"""Generated client library for ml version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.ml.v1 import ml_v1_messages as messages


class MlV1(base_api.BaseApiClient):
  """Generated client library for service ml version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://ml.googleapis.com/'
  MTLS_BASE_URL = 'https://ml.mtls.googleapis.com/'

  _PACKAGE = 'ml'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform', 'https://www.googleapis.com/auth/cloud-platform.read-only']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'MlV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new ml handle."""
    url = url or self.BASE_URL
    super(MlV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_jobs = self.ProjectsJobsService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_studies_trials = self.ProjectsLocationsStudiesTrialsService(self)
    self.projects_locations_studies = self.ProjectsLocationsStudiesService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects_models_versions = self.ProjectsModelsVersionsService(self)
    self.projects_models = self.ProjectsModelsService(self)
    self.projects_operations = self.ProjectsOperationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsJobsService(base_api.BaseApiService):
    """Service class for the projects_jobs resource."""

    _NAME = 'projects_jobs'

    def __init__(self, client):
      super(MlV1.ProjectsJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancels a running job.

      Args:
        request: (MlProjectsJobsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/jobs/{jobsId}:cancel',
        http_method='POST',
        method_id='ml.projects.jobs.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='googleCloudMlV1CancelJobRequest',
        request_type_name='MlProjectsJobsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a training or a batch prediction job.

      Args:
        request: (MlProjectsJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudMlV1Job) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/jobs',
        http_method='POST',
        method_id='ml.projects.jobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/jobs',
        request_field='googleCloudMlV1Job',
        request_type_name='MlProjectsJobsCreateRequest',
        response_type_name='GoogleCloudMlV1Job',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Describes a job.

      Args:
        request: (MlProjectsJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudMlV1Job) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/jobs/{jobsId}',
        http_method='GET',
        method_id='ml.projects.jobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='MlProjectsJobsGetRequest',
        response_type_name='GoogleCloudMlV1Job',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (MlProjectsJobsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/jobs/{jobsId}:getIamPolicy',
        http_method='GET',
        method_id='ml.projects.jobs.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='MlProjectsJobsGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the jobs in the project. If there are no jobs that match the request parameters, the list request returns an empty response body: {}.

      Args:
        request: (MlProjectsJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudMlV1ListJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/jobs',
        http_method='GET',
        method_id='ml.projects.jobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/jobs',
        request_field='',
        request_type_name='MlProjectsJobsListRequest',
        response_type_name='GoogleCloudMlV1ListJobsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a specific job resource. Currently the only supported fields to update are `labels`.

      Args:
        request: (MlProjectsJobsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudMlV1Job) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/jobs/{jobsId}',
        http_method='PATCH',
        method_id='ml.projects.jobs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudMlV1Job',
        request_type_name='MlProjectsJobsPatchRequest',
        response_type_name='GoogleCloudMlV1Job',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (MlProjectsJobsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/jobs/{jobsId}:setIamPolicy',
        http_method='POST',
        method_id='ml.projects.jobs.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='MlProjectsJobsSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (MlProjectsJobsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/jobs/{jobsId}:testIamPermissions',
        http_method='POST',
        method_id='ml.projects.jobs.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='MlProjectsJobsTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(MlV1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (MlProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='ml.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='',
        request_type_name='MlProjectsLocationsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (MlProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='ml.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='MlProjectsLocationsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsStudiesTrialsService(base_api.BaseApiService):
    """Service class for the projects_locations_studies_trials resource."""

    _NAME = 'projects_locations_studies_trials'

    def __init__(self, client):
      super(MlV1.ProjectsLocationsStudiesTrialsService, self).__init__(client)
      self._upload_configs = {
          }

    def AddMeasurement(self, request, global_params=None):
      r"""Adds a measurement of the objective metrics to a trial. This measurement is assumed to have been taken before the trial is complete.

      Args:
        request: (MlProjectsLocationsStudiesTrialsAddMeasurementRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudMlV1Trial) The response message.
      """
      config = self.GetMethodConfig('AddMeasurement')
      return self._RunMethod(
          config, request, global_params=global_params)

    AddMeasurement.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}:addMeasurement',
        http_method='POST',
        method_id='ml.projects.locations.studies.trials.addMeasurement',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:addMeasurement',
        request_field='googleCloudMlV1AddTrialMeasurementRequest',
        request_type_name='MlProjectsLocationsStudiesTrialsAddMeasurementRequest',
        response_type_name='GoogleCloudMlV1Trial',
        supports_download=False,
    )

    def CheckEarlyStoppingState(self, request, global_params=None):
      r"""Checks whether a trial should stop or not. Returns a long-running operation. When the operation is successful, it will contain a CheckTrialEarlyStoppingStateResponse.

      Args:
        request: (MlProjectsLocationsStudiesTrialsCheckEarlyStoppingStateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('CheckEarlyStoppingState')
      return self._RunMethod(
          config, request, global_params=global_params)

    CheckEarlyStoppingState.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}:checkEarlyStoppingState',
        http_method='POST',
        method_id='ml.projects.locations.studies.trials.checkEarlyStoppingState',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:checkEarlyStoppingState',
        request_field='googleCloudMlV1CheckTrialEarlyStoppingStateRequest',
        request_type_name='MlProjectsLocationsStudiesTrialsCheckEarlyStoppingStateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Complete(self, request, global_params=None):
      r"""Marks a trial as complete.

      Args:
        request: (MlProjectsLocationsStudiesTrialsCompleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudMlV1Trial) The response message.
      """
      config = self.GetMethodConfig('Complete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Complete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}:complete',
        http_method='POST',
        method_id='ml.projects.locations.studies.trials.complete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:complete',
        request_field='googleCloudMlV1CompleteTrialRequest',
        request_type_name='MlProjectsLocationsStudiesTrialsCompleteRequest',
        response_type_name='GoogleCloudMlV1Trial',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Adds a user provided trial to a study.

      Args:
        request: (MlProjectsLocationsStudiesTrialsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudMlV1Trial) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials',
        http_method='POST',
        method_id='ml.projects.locations.studies.trials.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/trials',
        request_field='googleCloudMlV1Trial',
        request_type_name='MlProjectsLocationsStudiesTrialsCreateRequest',
        response_type_name='GoogleCloudMlV1Trial',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a trial.

      Args:
        request: (MlProjectsLocationsStudiesTrialsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}',
        http_method='DELETE',
        method_id='ml.projects.locations.studies.trials.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='MlProjectsLocationsStudiesTrialsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a trial.

      Args:
        request: (MlProjectsLocationsStudiesTrialsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudMlV1Trial) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}',
        http_method='GET',
        method_id='ml.projects.locations.studies.trials.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='MlProjectsLocationsStudiesTrialsGetRequest',
        response_type_name='GoogleCloudMlV1Trial',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the trials associated with a study.

      Args:
        request: (MlProjectsLocationsStudiesTrialsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudMlV1ListTrialsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials',
        http_method='GET',
        method_id='ml.projects.locations.studies.trials.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/trials',
        request_field='',
        request_type_name='MlProjectsLocationsStudiesTrialsListRequest',
        response_type_name='GoogleCloudMlV1ListTrialsResponse',
        supports_download=False,
    )

    def ListOptimalTrials(self, request, global_params=None):
      r"""Lists the pareto-optimal trials for multi-objective study or the optimal trials for single-objective study. The definition of pareto-optimal can be checked in wiki page. https://en.wikipedia.org/wiki/Pareto_efficiency.

      Args:
        request: (MlProjectsLocationsStudiesTrialsListOptimalTrialsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudMlV1ListOptimalTrialsResponse) The response message.
      """
      config = self.GetMethodConfig('ListOptimalTrials')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListOptimalTrials.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials:listOptimalTrials',
        http_method='POST',
        method_id='ml.projects.locations.studies.trials.listOptimalTrials',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/trials:listOptimalTrials',
        request_field='googleCloudMlV1ListOptimalTrialsRequest',
        request_type_name='MlProjectsLocationsStudiesTrialsListOptimalTrialsRequest',
        response_type_name='GoogleCloudMlV1ListOptimalTrialsResponse',
        supports_download=False,
    )

    def Stop(self, request, global_params=None):
      r"""Stops a trial.

      Args:
        request: (MlProjectsLocationsStudiesTrialsStopRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudMlV1Trial) The response message.
      """
      config = self.GetMethodConfig('Stop')
      return self._RunMethod(
          config, request, global_params=global_params)

    Stop.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}:stop',
        http_method='POST',
        method_id='ml.projects.locations.studies.trials.stop',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:stop',
        request_field='googleCloudMlV1StopTrialRequest',
        request_type_name='MlProjectsLocationsStudiesTrialsStopRequest',
        response_type_name='GoogleCloudMlV1Trial',
        supports_download=False,
    )

    def Suggest(self, request, global_params=None):
      r"""Adds one or more trials to a study, with parameter values suggested by AI Platform Vizier. Returns a long-running operation associated with the generation of trial suggestions. When this long-running operation succeeds, it will contain a SuggestTrialsResponse.

      Args:
        request: (MlProjectsLocationsStudiesTrialsSuggestRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Suggest')
      return self._RunMethod(
          config, request, global_params=global_params)

    Suggest.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials:suggest',
        http_method='POST',
        method_id='ml.projects.locations.studies.trials.suggest',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/trials:suggest',
        request_field='googleCloudMlV1SuggestTrialsRequest',
        request_type_name='MlProjectsLocationsStudiesTrialsSuggestRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsStudiesService(base_api.BaseApiService):
    """Service class for the projects_locations_studies resource."""

    _NAME = 'projects_locations_studies'

    def __init__(self, client):
      super(MlV1.ProjectsLocationsStudiesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a study.

      Args:
        request: (MlProjectsLocationsStudiesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudMlV1Study) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies',
        http_method='POST',
        method_id='ml.projects.locations.studies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['studyId'],
        relative_path='v1/{+parent}/studies',
        request_field='googleCloudMlV1Study',
        request_type_name='MlProjectsLocationsStudiesCreateRequest',
        response_type_name='GoogleCloudMlV1Study',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a study.

      Args:
        request: (MlProjectsLocationsStudiesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}',
        http_method='DELETE',
        method_id='ml.projects.locations.studies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='MlProjectsLocationsStudiesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a study.

      Args:
        request: (MlProjectsLocationsStudiesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudMlV1Study) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}',
        http_method='GET',
        method_id='ml.projects.locations.studies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='MlProjectsLocationsStudiesGetRequest',
        response_type_name='GoogleCloudMlV1Study',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all the studies in a region for an associated project.

      Args:
        request: (MlProjectsLocationsStudiesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudMlV1ListStudiesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies',
        http_method='GET',
        method_id='ml.projects.locations.studies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/studies',
        request_field='',
        request_type_name='MlProjectsLocationsStudiesListRequest',
        response_type_name='GoogleCloudMlV1ListStudiesResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(MlV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get the complete list of CMLE capabilities in a location, along with their location-specific properties.

      Args:
        request: (MlProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudMlV1Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='ml.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='MlProjectsLocationsGetRequest',
        response_type_name='GoogleCloudMlV1Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List all locations that provides at least one type of CMLE capability.

      Args:
        request: (MlProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudMlV1ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='ml.projects.locations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/locations',
        request_field='',
        request_type_name='MlProjectsLocationsListRequest',
        response_type_name='GoogleCloudMlV1ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsModelsVersionsService(base_api.BaseApiService):
    """Service class for the projects_models_versions resource."""

    _NAME = 'projects_models_versions'

    def __init__(self, client):
      super(MlV1.ProjectsModelsVersionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new version of a model from a trained TensorFlow model. If the version created in the cloud by this call is the first deployed version of the specified model, it will be made the default version of the model. When you add a version to a model that already has one or more versions, the default version does not automatically change. If you want a new version to be the default, you must call projects.models.versions.setDefault.

      Args:
        request: (MlProjectsModelsVersionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/models/{modelsId}/versions',
        http_method='POST',
        method_id='ml.projects.models.versions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/versions',
        request_field='googleCloudMlV1Version',
        request_type_name='MlProjectsModelsVersionsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a model version. Each model can have multiple versions deployed and in use at any given time. Use this method to remove a single version. Note: You cannot delete the version that is set as the default version of the model unless it is the only remaining version.

      Args:
        request: (MlProjectsModelsVersionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/models/{modelsId}/versions/{versionsId}',
        http_method='DELETE',
        method_id='ml.projects.models.versions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='MlProjectsModelsVersionsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about a model version. Models can have multiple versions. You can call projects.models.versions.list to get the same information that this method returns for all of the versions of a model.

      Args:
        request: (MlProjectsModelsVersionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudMlV1Version) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/models/{modelsId}/versions/{versionsId}',
        http_method='GET',
        method_id='ml.projects.models.versions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='MlProjectsModelsVersionsGetRequest',
        response_type_name='GoogleCloudMlV1Version',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Gets basic information about all the versions of a model. If you expect that a model has many versions, or if you need to handle only a limited number of results at a time, you can request that the list be retrieved in batches (called pages). If there are no versions that match the request parameters, the list request returns an empty response body: {}.

      Args:
        request: (MlProjectsModelsVersionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudMlV1ListVersionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/models/{modelsId}/versions',
        http_method='GET',
        method_id='ml.projects.models.versions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/versions',
        request_field='',
        request_type_name='MlProjectsModelsVersionsListRequest',
        response_type_name='GoogleCloudMlV1ListVersionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified Version resource. Currently the only update-able fields are `description`, `requestLoggingConfig`, `autoScaling.minNodes`, and `manualScaling.nodes`.

      Args:
        request: (MlProjectsModelsVersionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/models/{modelsId}/versions/{versionsId}',
        http_method='PATCH',
        method_id='ml.projects.models.versions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudMlV1Version',
        request_type_name='MlProjectsModelsVersionsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetDefault(self, request, global_params=None):
      r"""Designates a version to be the default for the model. The default version is used for prediction requests made against the model that don't specify a version. The first version to be created for a model is automatically set as the default. You must make any subsequent changes to the default version setting manually using this method.

      Args:
        request: (MlProjectsModelsVersionsSetDefaultRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudMlV1Version) The response message.
      """
      config = self.GetMethodConfig('SetDefault')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetDefault.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/models/{modelsId}/versions/{versionsId}:setDefault',
        http_method='POST',
        method_id='ml.projects.models.versions.setDefault',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:setDefault',
        request_field='googleCloudMlV1SetDefaultVersionRequest',
        request_type_name='MlProjectsModelsVersionsSetDefaultRequest',
        response_type_name='GoogleCloudMlV1Version',
        supports_download=False,
    )

  class ProjectsModelsService(base_api.BaseApiService):
    """Service class for the projects_models resource."""

    _NAME = 'projects_models'

    def __init__(self, client):
      super(MlV1.ProjectsModelsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a model which will later contain one or more versions. You must add at least one version before you can request predictions from the model. Add versions by calling projects.models.versions.create.

      Args:
        request: (MlProjectsModelsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudMlV1Model) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/models',
        http_method='POST',
        method_id='ml.projects.models.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/models',
        request_field='googleCloudMlV1Model',
        request_type_name='MlProjectsModelsCreateRequest',
        response_type_name='GoogleCloudMlV1Model',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a model. You can only delete a model if there are no versions in it. You can delete versions by calling projects.models.versions.delete.

      Args:
        request: (MlProjectsModelsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/models/{modelsId}',
        http_method='DELETE',
        method_id='ml.projects.models.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='MlProjectsModelsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about a model, including its name, the description (if set), and the default version (if at least one version of the model has been deployed).

      Args:
        request: (MlProjectsModelsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudMlV1Model) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/models/{modelsId}',
        http_method='GET',
        method_id='ml.projects.models.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='MlProjectsModelsGetRequest',
        response_type_name='GoogleCloudMlV1Model',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (MlProjectsModelsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/models/{modelsId}:getIamPolicy',
        http_method='GET',
        method_id='ml.projects.models.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='MlProjectsModelsGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the models in a project. Each project can contain multiple models, and each model can have multiple versions. If there are no models that match the request parameters, the list request returns an empty response body: {}.

      Args:
        request: (MlProjectsModelsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudMlV1ListModelsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/models',
        http_method='GET',
        method_id='ml.projects.models.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/models',
        request_field='',
        request_type_name='MlProjectsModelsListRequest',
        response_type_name='GoogleCloudMlV1ListModelsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a specific model resource. Currently the only supported fields to update are `description` and `default_version.name`.

      Args:
        request: (MlProjectsModelsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/models/{modelsId}',
        http_method='PATCH',
        method_id='ml.projects.models.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudMlV1Model',
        request_type_name='MlProjectsModelsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (MlProjectsModelsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/models/{modelsId}:setIamPolicy',
        http_method='POST',
        method_id='ml.projects.models.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='MlProjectsModelsSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (MlProjectsModelsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/models/{modelsId}:testIamPermissions',
        http_method='POST',
        method_id='ml.projects.models.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='MlProjectsModelsTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsOperationsService(base_api.BaseApiService):
    """Service class for the projects_operations resource."""

    _NAME = 'projects_operations'

    def __init__(self, client):
      super(MlV1.ProjectsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (MlProjectsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='ml.projects.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='',
        request_type_name='MlProjectsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (MlProjectsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/operations/{operationsId}',
        http_method='GET',
        method_id='ml.projects.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='MlProjectsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (MlProjectsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/operations',
        http_method='GET',
        method_id='ml.projects.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='MlProjectsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(MlV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

    def Explain(self, request, global_params=None):
      r"""Performs explanation on the data in the request. {% dynamic include "/ai-platform/includes/___explain-request" %} .

      Args:
        request: (MlProjectsExplainRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleApiHttpBody) The response message.
      """
      config = self.GetMethodConfig('Explain')
      return self._RunMethod(
          config, request, global_params=global_params)

    Explain.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}:explain',
        http_method='POST',
        method_id='ml.projects.explain',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:explain',
        request_field='googleCloudMlV1ExplainRequest',
        request_type_name='MlProjectsExplainRequest',
        response_type_name='GoogleApiHttpBody',
        supports_download=False,
    )

    def GetConfig(self, request, global_params=None):
      r"""Get the service account information associated with your project. You need this information in order to grant the service account permissions for the Google Cloud Storage location where you put your model training code for training the model with Google Cloud Machine Learning.

      Args:
        request: (MlProjectsGetConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudMlV1GetConfigResponse) The response message.
      """
      config = self.GetMethodConfig('GetConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}:getConfig',
        http_method='GET',
        method_id='ml.projects.getConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:getConfig',
        request_field='',
        request_type_name='MlProjectsGetConfigRequest',
        response_type_name='GoogleCloudMlV1GetConfigResponse',
        supports_download=False,
    )

    def Predict(self, request, global_params=None):
      r"""Performs online prediction on the data in the request. {% dynamic include "/ai-platform/includes/___predict-request" %} .

      Args:
        request: (MlProjectsPredictRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleApiHttpBody) The response message.
      """
      config = self.GetMethodConfig('Predict')
      return self._RunMethod(
          config, request, global_params=global_params)

    Predict.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}:predict',
        http_method='POST',
        method_id='ml.projects.predict',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:predict',
        request_field='googleCloudMlV1PredictRequest',
        request_type_name='MlProjectsPredictRequest',
        response_type_name='GoogleApiHttpBody',
        supports_download=False,
    )
