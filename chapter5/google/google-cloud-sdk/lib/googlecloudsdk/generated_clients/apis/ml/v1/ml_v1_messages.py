"""Generated message classes for ml version v1.

An API to enable creating and using machine learning models.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'ml'


class GoogleApiHttpBody(_messages.Message):
  r"""Message that represents an arbitrary HTTP body. It should only be used
  for payload formats that can't be represented as JSON, such as raw binary or
  an HTML page. This message can be used both in streaming and non-streaming
  API methods in the request as well as the response. It can be used as a top-
  level request field, which is convenient if one wants to extract parameters
  from either the URL or HTTP template into the request fields and also want
  access to the raw HTTP body. Example: message GetResourceRequest { // A
  unique request id. string request_id = 1; // The raw HTTP body is bound to
  this field. google.api.HttpBody http_body = 2; } service ResourceService {
  rpc GetResource(GetResourceRequest) returns (google.api.HttpBody); rpc
  UpdateResource(google.api.HttpBody) returns (google.protobuf.Empty); }
  Example with streaming methods: service CaldavService { rpc
  GetCalendar(stream google.api.HttpBody) returns (stream
  google.api.HttpBody); rpc UpdateCalendar(stream google.api.HttpBody) returns
  (stream google.api.HttpBody); } Use of this type only changes how the
  request and response bodies are handled, all other features will continue to
  work unchanged.

  Messages:
    ExtensionsValueListEntry: A ExtensionsValueListEntry object.

  Fields:
    contentType: The HTTP Content-Type header value specifying the content
      type of the body.
    data: The HTTP request/response body as raw binary.
    extensions: Application specific response metadata. Must be set in the
      first response for streaming APIs.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExtensionsValueListEntry(_messages.Message):
    r"""A ExtensionsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a
        ExtensionsValueListEntry object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExtensionsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  contentType = _messages.StringField(1)
  data = _messages.BytesField(2)
  extensions = _messages.MessageField('ExtensionsValueListEntry', 3, repeated=True)


class GoogleCloudMlV1AblationAttribution(_messages.Message):
  r"""Attributes credit to model inputs by ablating features (ie. setting them
  to their default/missing values) and computing corresponding model score
  delta per feature. The term "ablation" is in reference to running an
  "ablation study" to analyze input effects on the outcome of interest, which
  in this case is the model's output. This attribution method is supported for
  TensorFlow and XGBoost models.

  Fields:
    numFeatureInteractions: Number of feature interactions to account for in
      the ablation process, capped at the maximum number of provided input
      features. Currently, only the value 1 is supported.
  """

  numFeatureInteractions = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class GoogleCloudMlV1AcceleratorConfig(_messages.Message):
  r"""Represents a hardware accelerator request config. Note that the
  AcceleratorConfig can be used in both Jobs and Versions. Learn more about
  [accelerators for training](/ml-engine/docs/using-gpus) and [accelerators
  for online prediction](/ml-engine/docs/machine-types-online-
  prediction#gpus).

  Enums:
    TypeValueValuesEnum: The type of accelerator to use.

  Fields:
    count: The number of accelerators to attach to each machine running the
      job.
    type: The type of accelerator to use.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of accelerator to use.

    Values:
      ACCELERATOR_TYPE_UNSPECIFIED: Unspecified accelerator type. Default to
        no GPU.
      NVIDIA_TESLA_K80: Nvidia Tesla K80 GPU.
      NVIDIA_TESLA_P100: Nvidia Tesla P100 GPU.
      NVIDIA_TESLA_V100: Nvidia V100 GPU.
      NVIDIA_TESLA_P4: Nvidia Tesla P4 GPU.
      NVIDIA_TESLA_T4: Nvidia T4 GPU.
      NVIDIA_TESLA_A100: Nvidia A100 GPU.
      TPU_V2: TPU v2.
      TPU_V3: TPU v3.
      TPU_V2_POD: TPU v2 POD.
      TPU_V3_POD: TPU v3 POD.
      TPU_V4_POD: TPU v4 POD.
    """
    ACCELERATOR_TYPE_UNSPECIFIED = 0
    NVIDIA_TESLA_K80 = 1
    NVIDIA_TESLA_P100 = 2
    NVIDIA_TESLA_V100 = 3
    NVIDIA_TESLA_P4 = 4
    NVIDIA_TESLA_T4 = 5
    NVIDIA_TESLA_A100 = 6
    TPU_V2 = 7
    TPU_V3 = 8
    TPU_V2_POD = 9
    TPU_V3_POD = 10
    TPU_V4_POD = 11

  count = _messages.IntegerField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class GoogleCloudMlV1AddTrialMeasurementRequest(_messages.Message):
  r"""The request message for the AddTrialMeasurement service method.

  Fields:
    measurement: Required. The measurement to be added to a trial.
  """

  measurement = _messages.MessageField('GoogleCloudMlV1Measurement', 1)


class GoogleCloudMlV1AutoScaling(_messages.Message):
  r"""Options for automatically scaling a model.

  Fields:
    maxNodes: The maximum number of nodes to scale this model under load. The
      actual value will depend on resource quota and availability.
    metrics: MetricSpec contains the specifications to use to calculate the
      desired nodes count.
    minNodes: Optional. The minimum number of nodes to allocate for this
      model. These nodes are always up, starting from the time the model is
      deployed. Therefore, the cost of operating this model will be at least
      `rate` * `min_nodes` * number of hours since last billing cycle, where
      `rate` is the cost per node-hour as documented in the [pricing
      guide](/ml-engine/docs/pricing), even if no predictions are performed.
      There is additional cost for each prediction performed. Unlike manual
      scaling, if the load gets too heavy for the nodes that are up, the
      service will automatically add nodes to handle the increased load as
      well as scale back as traffic drops, always maintaining at least
      `min_nodes`. You will be charged for the time in which additional nodes
      are used. If `min_nodes` is not specified and AutoScaling is used with a
      [legacy (MLS1) machine type](/ml-engine/docs/machine-types-online-
      prediction), `min_nodes` defaults to 0, in which case, when traffic to a
      model stops (and after a cool-down period), nodes will be shut down and
      no charges will be incurred until traffic to the model resumes. If
      `min_nodes` is not specified and AutoScaling is used with a [Compute
      Engine (N1) machine type](/ml-engine/docs/machine-types-online-
      prediction), `min_nodes` defaults to 1. `min_nodes` must be at least 1
      for use with a Compute Engine machine type. You can set `min_nodes` when
      creating the model version, and you can also update `min_nodes` for an
      existing version: update_body.json: { 'autoScaling': { 'minNodes': 5 } }
      HTTP request: PATCH https://ml.googleapis.com/v1/{name=projects/*/models
      /*/versions/*}?update_mask=autoScaling.minNodes -d @./update_body.json
  """

  maxNodes = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  metrics = _messages.MessageField('GoogleCloudMlV1MetricSpec', 2, repeated=True)
  minNodes = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class GoogleCloudMlV1AutomatedStoppingConfig(_messages.Message):
  r"""Configuration for Automated Early Stopping of Trials. If no
  implementation_config is set, automated early stopping will not be run.

  Fields:
    decayCurveStoppingConfig: A
      GoogleCloudMlV1AutomatedStoppingConfigDecayCurveAutomatedStoppingConfig
      attribute.
    medianAutomatedStoppingConfig: A
      GoogleCloudMlV1AutomatedStoppingConfigMedianAutomatedStoppingConfig
      attribute.
  """

  decayCurveStoppingConfig = _messages.MessageField('GoogleCloudMlV1AutomatedStoppingConfigDecayCurveAutomatedStoppingConfig', 1)
  medianAutomatedStoppingConfig = _messages.MessageField('GoogleCloudMlV1AutomatedStoppingConfigMedianAutomatedStoppingConfig', 2)


class GoogleCloudMlV1AutomatedStoppingConfigDecayCurveAutomatedStoppingConfig(_messages.Message):
  r"""A
  GoogleCloudMlV1AutomatedStoppingConfigDecayCurveAutomatedStoppingConfig
  object.

  Fields:
    useElapsedTime: If true, measurement.elapsed_time is used as the x-axis of
      each Trials Decay Curve. Otherwise, Measurement.steps will be used as
      the x-axis.
  """

  useElapsedTime = _messages.BooleanField(1)


class GoogleCloudMlV1AutomatedStoppingConfigMedianAutomatedStoppingConfig(_messages.Message):
  r"""The median automated stopping rule stops a pending trial if the trial's
  best objective_value is strictly below the median 'performance' of all
  completed trials reported up to the trial's last measurement. Currently,
  'performance' refers to the running average of the objective values reported
  by the trial in each measurement.

  Fields:
    useElapsedTime: If true, the median automated stopping rule applies to
      measurement.use_elapsed_time, which means the elapsed_time field of the
      current trial's latest measurement is used to compute the median
      objective value for each completed trial.
  """

  useElapsedTime = _messages.BooleanField(1)


class GoogleCloudMlV1BlurBaselineConfig(_messages.Message):
  r"""Config for blur baseline. When enabled, a linear path from the maximally
  blurred image to the input image is created. Using a blurred baseline
  instead of zero (black image) is motivated by the BlurIG approach explained
  here: https://arxiv.org/abs/2004.03383

  Fields:
    maxBlurSigma: The standard deviation of the blur kernel for the blurred
      baseline. The same blurring parameter is used for both the height and
      the width dimension. If not set, the method defaults to the zero (i.e.
      black for images) baseline.
  """

  maxBlurSigma = _messages.FloatField(1, variant=_messages.Variant.FLOAT)


class GoogleCloudMlV1BuiltInAlgorithmOutput(_messages.Message):
  r"""Represents output related to a built-in algorithm Job.

  Fields:
    framework: Framework on which the built-in algorithm was trained.
    modelPath: The Cloud Storage path to the `model/` directory where the
      training job saves the trained model. Only set for successful jobs that
      don't use hyperparameter tuning.
    pythonVersion: Python version on which the built-in algorithm was trained.
    runtimeVersion: AI Platform runtime version on which the built-in
      algorithm was trained.
  """

  framework = _messages.StringField(1)
  modelPath = _messages.StringField(2)
  pythonVersion = _messages.StringField(3)
  runtimeVersion = _messages.StringField(4)


class GoogleCloudMlV1CancelJobRequest(_messages.Message):
  r"""Request message for the CancelJob method."""


class GoogleCloudMlV1Capability(_messages.Message):
  r"""A GoogleCloudMlV1Capability object.

  Enums:
    AvailableAcceleratorsValueListEntryValuesEnum:
    TypeValueValuesEnum:

  Fields:
    availableAccelerators: Available accelerators for the capability.
    type: A TypeValueValuesEnum attribute.
  """

  class AvailableAcceleratorsValueListEntryValuesEnum(_messages.Enum):
    r"""AvailableAcceleratorsValueListEntryValuesEnum enum type.

    Values:
      ACCELERATOR_TYPE_UNSPECIFIED: Unspecified accelerator type. Default to
        no GPU.
      NVIDIA_TESLA_K80: Nvidia Tesla K80 GPU.
      NVIDIA_TESLA_P100: Nvidia Tesla P100 GPU.
      NVIDIA_TESLA_V100: Nvidia V100 GPU.
      NVIDIA_TESLA_P4: Nvidia Tesla P4 GPU.
      NVIDIA_TESLA_T4: Nvidia T4 GPU.
      NVIDIA_TESLA_A100: Nvidia A100 GPU.
      TPU_V2: TPU v2.
      TPU_V3: TPU v3.
      TPU_V2_POD: TPU v2 POD.
      TPU_V3_POD: TPU v3 POD.
      TPU_V4_POD: TPU v4 POD.
    """
    ACCELERATOR_TYPE_UNSPECIFIED = 0
    NVIDIA_TESLA_K80 = 1
    NVIDIA_TESLA_P100 = 2
    NVIDIA_TESLA_V100 = 3
    NVIDIA_TESLA_P4 = 4
    NVIDIA_TESLA_T4 = 5
    NVIDIA_TESLA_A100 = 6
    TPU_V2 = 7
    TPU_V3 = 8
    TPU_V2_POD = 9
    TPU_V3_POD = 10
    TPU_V4_POD = 11

  class TypeValueValuesEnum(_messages.Enum):
    r"""TypeValueValuesEnum enum type.

    Values:
      TYPE_UNSPECIFIED: <no description>
      TRAINING: <no description>
      BATCH_PREDICTION: <no description>
      ONLINE_PREDICTION: <no description>
    """
    TYPE_UNSPECIFIED = 0
    TRAINING = 1
    BATCH_PREDICTION = 2
    ONLINE_PREDICTION = 3

  availableAccelerators = _messages.EnumField('AvailableAcceleratorsValueListEntryValuesEnum', 1, repeated=True)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class GoogleCloudMlV1CheckTrialEarlyStoppingStateMetatdata(_messages.Message):
  r"""This message will be placed in the metadata field of a
  google.longrunning.Operation associated with a CheckTrialEarlyStoppingState
  request.

  Fields:
    createTime: The time at which the operation was submitted.
    study: The name of the study that the trial belongs to.
    trial: The trial name.
  """

  createTime = _messages.StringField(1)
  study = _messages.StringField(2)
  trial = _messages.StringField(3)


class GoogleCloudMlV1CheckTrialEarlyStoppingStateRequest(_messages.Message):
  r"""The request message for the CheckTrialEarlyStoppingState service method.
  """



class GoogleCloudMlV1CheckTrialEarlyStoppingStateResponse(_messages.Message):
  r"""The message will be placed in the response field of a completed
  google.longrunning.Operation associated with a CheckTrialEarlyStoppingState
  request.

  Fields:
    endTime: The time at which operation processing completed.
    shouldStop: True if the Trial should stop.
    startTime: The time at which the operation was started.
  """

  endTime = _messages.StringField(1)
  shouldStop = _messages.BooleanField(2)
  startTime = _messages.StringField(3)


class GoogleCloudMlV1CompleteTrialRequest(_messages.Message):
  r"""The request message for the CompleteTrial service method.

  Fields:
    finalMeasurement: Optional. If provided, it will be used as the completed
      trial's final_measurement; Otherwise, the service will auto-select a
      previously reported measurement as the final-measurement
    infeasibleReason: Optional. A human readable reason why the trial was
      infeasible. This should only be provided if `trial_infeasible` is true.
    trialInfeasible: Optional. True if the trial cannot be run with the given
      Parameter, and final_measurement will be ignored.
  """

  finalMeasurement = _messages.MessageField('GoogleCloudMlV1Measurement', 1)
  infeasibleReason = _messages.StringField(2)
  trialInfeasible = _messages.BooleanField(3)


class GoogleCloudMlV1Config(_messages.Message):
  r"""A GoogleCloudMlV1Config object.

  Fields:
    tpuServiceAccount: The service account Cloud ML uses to run on TPU node.
  """

  tpuServiceAccount = _messages.StringField(1)


class GoogleCloudMlV1ContainerPort(_messages.Message):
  r"""Represents a network port in a single container. This message is a
  subset of the [Kubernetes ContainerPort v1 core
  specification](https://kubernetes.io/docs/reference/generated/kubernetes-
  api/v1.18/#containerport-v1-core).

  Fields:
    containerPort: Number of the port to expose on the container. This must be
      a valid port number: 0 < PORT_NUMBER < 65536.
  """

  containerPort = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class GoogleCloudMlV1ContainerSpec(_messages.Message):
  r"""Specification of a custom container for serving predictions. This
  message is a subset of the [Kubernetes Container v1 core
  specification](https://kubernetes.io/docs/reference/generated/kubernetes-
  api/v1.18/#container-v1-core).

  Fields:
    args: Immutable. Specifies arguments for the command that runs when the
      container starts. This overrides the container's
      [`CMD`](https://docs.docker.com/engine/reference/builder/#cmd). Specify
      this field as an array of executable and arguments, similar to a Docker
      `CMD`'s "default parameters" form. If you don't specify this field but
      do specify the command field, then the command from the `command` field
      runs without any additional arguments. See the [Kubernetes documentation
      about how the `command` and `args` fields interact with a container's
      `ENTRYPOINT` and `CMD`](https://kubernetes.io/docs/tasks/inject-data-
      application/define-command-argument-container/#notes). If you don't
      specify this field and don't specify the `commmand` field, then the
      container's
      [`ENTRYPOINT`](https://docs.docker.com/engine/reference/builder/#cmd)
      and `CMD` determine what runs based on their default behavior. See the
      [Docker documentation about how `CMD` and `ENTRYPOINT`
      interact](https://docs.docker.com/engine/reference/builder/#understand-
      how-cmd-and-entrypoint-interact). In this field, you can reference
      [environment variables set by AI Platform Prediction](/ai-
      platform/prediction/docs/custom-container-requirements#aip-variables)
      and environment variables set in the env field. You cannot reference
      environment variables set in the Docker image. In order for environment
      variables to be expanded, reference them by using the following syntax:
      $( VARIABLE_NAME) Note that this differs from Bash variable expansion,
      which does not use parentheses. If a variable cannot be resolved, the
      reference in the input string is used unchanged. To avoid variable
      expansion, you can escape this syntax with `$$`; for example:
      $$(VARIABLE_NAME) This field corresponds to the `args` field of the
      [Kubernetes Containers v1 core
      API](https://kubernetes.io/docs/reference/generated/kubernetes-
      api/v1.18/#container-v1-core).
    command: Immutable. Specifies the command that runs when the container
      starts. This overrides the container's [`ENTRYPOINT`](https://docs.docke
      r.com/engine/reference/builder/#entrypoint). Specify this field as an
      array of executable and arguments, similar to a Docker `ENTRYPOINT`'s
      "exec" form, not its "shell" form. If you do not specify this field,
      then the container's `ENTRYPOINT` runs, in conjunction with the args
      field or the container's
      [`CMD`](https://docs.docker.com/engine/reference/builder/#cmd), if
      either exists. If this field is not specified and the container does not
      have an `ENTRYPOINT`, then refer to the [Docker documentation about how
      `CMD` and `ENTRYPOINT`
      interact](https://docs.docker.com/engine/reference/builder/#understand-
      how-cmd-and-entrypoint-interact). If you specify this field, then you
      can also specify the `args` field to provide additional arguments for
      this command. However, if you specify this field, then the container's
      `CMD` is ignored. See the [Kubernetes documentation about how the
      `command` and `args` fields interact with a container's `ENTRYPOINT` and
      `CMD`](https://kubernetes.io/docs/tasks/inject-data-application/define-
      command-argument-container/#notes). In this field, you can reference
      [environment variables set by AI Platform Prediction](/ai-
      platform/prediction/docs/custom-container-requirements#aip-variables)
      and environment variables set in the env field. You cannot reference
      environment variables set in the Docker image. In order for environment
      variables to be expanded, reference them by using the following syntax:
      $( VARIABLE_NAME) Note that this differs from Bash variable expansion,
      which does not use parentheses. If a variable cannot be resolved, the
      reference in the input string is used unchanged. To avoid variable
      expansion, you can escape this syntax with `$$`; for example:
      $$(VARIABLE_NAME) This field corresponds to the `command` field of the
      [Kubernetes Containers v1 core
      API](https://kubernetes.io/docs/reference/generated/kubernetes-
      api/v1.18/#container-v1-core).
    env: Immutable. List of environment variables to set in the container.
      After the container starts running, code running in the container can
      read these environment variables. Additionally, the command and args
      fields can reference these variables. Later entries in this list can
      also reference earlier entries. For example, the following example sets
      the variable `VAR_2` to have the value `foo bar`: ```json [ { "name":
      "VAR_1", "value": "foo" }, { "name": "VAR_2", "value": "$(VAR_1) bar" }
      ] ``` If you switch the order of the variables in the example, then the
      expansion does not occur. This field corresponds to the `env` field of
      the [Kubernetes Containers v1 core
      API](https://kubernetes.io/docs/reference/generated/kubernetes-
      api/v1.18/#container-v1-core).
    image: URI of the Docker image to be used as the custom container for
      serving predictions. This URI must identify [an image in Artifact
      Registry](/artifact-registry/docs/overview) and begin with the hostname
      `{REGION}-docker.pkg.dev`, where `{REGION}` is replaced by the region
      that matches AI Platform Prediction [regional endpoint](/ai-
      platform/prediction/docs/regional-endpoints) that you are using. For
      example, if you are using the `us-central1-ml.googleapis.com` endpoint,
      then this URI must begin with `us-central1-docker.pkg.dev`. To use a
      custom container, the [AI Platform Google-managed service account](/ai-
      platform/prediction/docs/custom-service-account#default) must have
      permission to pull (read) the Docker image at this URI. The AI Platform
      Google-managed service account has the following format:
      `service-{PROJECT_NUMBER}@cloud-ml.google.com.iam.gserviceaccount.com`
      {PROJECT_NUMBER} is replaced by your Google Cloud project number. By
      default, this service account has necessary permissions to pull an
      Artifact Registry image in the same Google Cloud project where you are
      using AI Platform Prediction. In this case, no configuration is
      necessary. If you want to use an image from a different Google Cloud
      project, learn how to [grant the Artifact Registry Reader
      (roles/artifactregistry.reader) role for a repository](/artifact-
      registry/docs/access-control#grant-repo) to your projet's AI Platform
      Google-managed service account. To learn about the requirements for the
      Docker image itself, read [Custom container requirements](/ai-
      platform/prediction/docs/custom-container-requirements).
    ports: Immutable. List of ports to expose from the container. AI Platform
      Prediction sends any prediction requests that it receives to the first
      port on this list. AI Platform Prediction also sends [liveness and
      health checks](/ai-platform/prediction/docs/custom-container-
      requirements#health) to this port. If you do not specify this field, it
      defaults to following value: ```json [ { "containerPort": 8080 } ] ```
      AI Platform Prediction does not use ports other than the first one
      listed. This field corresponds to the `ports` field of the [Kubernetes
      Containers v1 core
      API](https://kubernetes.io/docs/reference/generated/kubernetes-
      api/v1.18/#container-v1-core).
  """

  args = _messages.StringField(1, repeated=True)
  command = _messages.StringField(2, repeated=True)
  env = _messages.MessageField('GoogleCloudMlV1EnvVar', 3, repeated=True)
  image = _messages.StringField(4)
  ports = _messages.MessageField('GoogleCloudMlV1ContainerPort', 5, repeated=True)


class GoogleCloudMlV1DiskConfig(_messages.Message):
  r"""Represents the config of disk options.

  Fields:
    bootDiskSizeGb: Size in GB of the boot disk (default is 100GB).
    bootDiskType: Type of the boot disk (default is "pd-ssd"). Valid values:
      "pd-ssd" (Persistent Disk Solid State Drive) or "pd-standard"
      (Persistent Disk Hard Disk Drive).
  """

  bootDiskSizeGb = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  bootDiskType = _messages.StringField(2)


class GoogleCloudMlV1EncryptionConfig(_messages.Message):
  r"""Represents a custom encryption key configuration that can be applied to
  a resource.

  Fields:
    kmsKeyName: The Cloud KMS resource identifier of the customer-managed
      encryption key used to protect a resource, such as a training job. It
      has the following format: `projects/{PROJECT_ID}/locations/{REGION}/keyR
      ings/{KEY_RING_NAME}/cryptoKeys/{KEY_NAME}`
  """

  kmsKeyName = _messages.StringField(1)


class GoogleCloudMlV1EnvVar(_messages.Message):
  r"""Represents an environment variable to be made available in a container.
  This message is a subset of the [Kubernetes EnvVar v1 core
  specification](https://kubernetes.io/docs/reference/generated/kubernetes-
  api/v1.18/#envvar-v1-core).

  Fields:
    name: Name of the environment variable. Must be a [valid C identifier](htt
      ps://github.com/kubernetes/kubernetes/blob/v1.18.8/staging/src/k8s.io/ap
      imachinery/pkg/util/validation/validation.go#L258) and must not begin
      with the prefix `AIP_`.
    value: Value of the environment variable. Defaults to an empty string. In
      this field, you can reference [environment variables set by AI Platform
      Prediction](/ai-platform/prediction/docs/custom-container-
      requirements#aip-variables) and environment variables set earlier in the
      same env field as where this message occurs. You cannot reference
      environment variables set in the Docker image. In order for environment
      variables to be expanded, reference them by using the following syntax:
      $(VARIABLE_NAME) Note that this differs from Bash variable expansion,
      which does not use parentheses. If a variable cannot be resolved, the
      reference in the input string is used unchanged. To avoid variable
      expansion, you can escape this syntax with `$$`; for example:
      $$(VARIABLE_NAME)
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class GoogleCloudMlV1ExplainRequest(_messages.Message):
  r"""Request for explanations to be issued against a trained model.

  Fields:
    httpBody: Required. The explanation request body.
  """

  httpBody = _messages.MessageField('GoogleApiHttpBody', 1)


class GoogleCloudMlV1ExplanationConfig(_messages.Message):
  r"""Message holding configuration options for explaining model predictions.
  There are three feature attribution methods supported for TensorFlow models:
  integrated gradients, sampled Shapley, and XRAI. [Learn more about feature
  attributions.](/ai-platform/prediction/docs/ai-explanations/overview)

  Fields:
    ablationAttribution: TensorFlow framework explanation methods. Deprecated.
      Attributes credit to model inputs by ablating features (ie. setting them
      to their default/missing values) and computing corresponding model score
      delta per feature. The term "ablation" is in reference to running an
      "ablation study" to analyze input effects on the outcome of interest,
      which in this case is the model's output. This attribution method is
      supported for TensorFlow and XGBoost models.
    integratedGradientsAttribution: Attributes credit by computing the Aumann-
      Shapley value taking advantage of the model's fully differentiable
      structure. Refer to this paper for more details:
      https://arxiv.org/abs/1703.01365
    saabasAttribution: Attributes credit by running a faster approximation to
      the TreeShap method. Please refer to this link for more details:
      https://blog.datadive.net/interpreting-random-forests/ This attribution
      method is only supported for XGBoost models.
    sampledShapleyAttribution: An attribution method that approximates Shapley
      values for features that contribute to the label being predicted. A
      sampling strategy is used to approximate the value rather than
      considering all subsets of features.
    treeShapAttribution: XGBoost framework explanation methods. Attributes
      credit by computing the Shapley value taking advantage of the model's
      tree ensemble structure. Refer to this paper for more details:
      http://papers.nips.cc/paper/7062-a-unified-approach-to-interpreting-
      model-predictions.pdf. This attribution method is supported for XGBoost
      models.
    xraiAttribution: Attributes credit by computing the XRAI taking advantage
      of the model's fully differentiable structure. Refer to this paper for
      more details: https://arxiv.org/abs/1906.02825 Currently only
      implemented for models with natural image inputs.
  """

  ablationAttribution = _messages.MessageField('GoogleCloudMlV1AblationAttribution', 1)
  integratedGradientsAttribution = _messages.MessageField('GoogleCloudMlV1IntegratedGradientsAttribution', 2)
  saabasAttribution = _messages.MessageField('GoogleCloudMlV1SaabasAttribution', 3)
  sampledShapleyAttribution = _messages.MessageField('GoogleCloudMlV1SampledShapleyAttribution', 4)
  treeShapAttribution = _messages.MessageField('GoogleCloudMlV1TreeShapAttribution', 5)
  xraiAttribution = _messages.MessageField('GoogleCloudMlV1XraiAttribution', 6)


class GoogleCloudMlV1ExplanationInput(_messages.Message):
  r"""Represents input parameters for a model explanation job.

  Enums:
    DataFormatValueValuesEnum: Required. The format of the input data.
    FrameworkValueValuesEnum: Optional. The framework used to train this
      model. Only needed if model_version is a GCS path. Otherwise the
      framework specified during version creation will be used.
    OutputDataFormatValueValuesEnum: Optional. The format of the output data,
      defaults to BIGQUERY.

  Fields:
    accelerator: Optional. The type and number of accelerators to be attached
      to each machine running the job.
    batchSize: Optional. Number of records per batch, defaults to 64. The
      service will buffer batch_size number of records in memory before
      invoking one Tensorflow prediction call internally. So take the record
      size and memory available into consideration when setting this
      parameter.
    dataFormat: Required. The format of the input data.
    explanationConfig: Required only if model_version is specified through a
      uri, otherwise the same explanation config specified at model version
      creation will be used. Configures explainability features on the model's
      version. Some explanation features require additional metadata to be
      loaded as part of the model payload.
    framework: Optional. The framework used to train this model. Only needed
      if model_version is a GCS path. Otherwise the framework specified during
      version creation will be used.
    initialWorkerCount: Optional. The initial number of workers to be used for
      parallel processing. Defaults to 0 if one wants the service to figure
      out the number. The actual number of workers being used may change after
      the job starts depending on the autoscaling policy.
    inputPaths: Required when data_format is JSON. The Cloud Storage location
      of the input data. May contain wildcards.
    maxWorkerCount: Optional. The maximum number of workers to be used for
      parallel processing. Defaults to 10 if not specified.
    modelName: Use this field if you want to use the default version for the
      specified model. The string must use the following format:
      `"projects/YOUR_PROJECT/models/YOUR_MODEL"`
    outputBigqueryTable: Required when output_data_format is BIGQUERY. The
      output fully qualified BigQuery table name in the format of
      "[project_id].[dataset_name].[table_name]".
    outputDataFormat: Optional. The format of the output data, defaults to
      BIGQUERY.
    region: Required. The Compute Engine region to run the explanation job in.
      See the available regions for AI Platform services.
    runtimeVersion: Required. The AI Platform runtime version to use for the
      explanation job. See <a href="https://cloud.google.com/ml-
      engine/docs/tensorflow/runtime-version-list</a> for available runtime
      versions. Must be >=1.12.
    signatureName: Optional. The name of the signature defined in the
      SavedModel to use for this job. Please refer to
      [SavedModel](https://tensorflow.github.io/serving/serving_basic.html)
      for information about how to use signatures. Defaults to [DEFAULT_SERVIN
      G_SIGNATURE_DEF_KEY](https://www.tensorflow.org/api_docs/python/tf/saved
      _model/signature_constants) , which is "serving_default".
    tagsOverride: Optional. The set of tags to select which meta graph defined
      in the SavedModel to use for this job. Please refer to
      [SavedModel](https://www.tensorflow.org/serving/serving_basic) for
      information about how to use tags. Overrides the default tags when
      predicting from a deployed model version. When predicting from a model
      directory, the tag defaults to [SERVING](https://www.tensorflow.org/api_
      docs/python/tf/saved_model/tag_constants) , which is "serve".
    uri: Use this field if you want to specify a Google Cloud Storage path for
      the model to use, e.g. gs://{BUCKET}/{MODEL_DIR}/{MODEL_NAME}.
    versionName: Use this field if you want to specify a version of the model
      to use. The string is formatted the same way as `model_version`, with
      the addition of the version information:
      `"projects/YOUR_PROJECT/models/YOUR_MODEL/versions/YOUR_VERSION"`
    workerType: Optional. The type of virtual machine to use for the
      explanation job's worker nodes. It supports all machine types available
      on GCP ( https://cloud.google.com/compute/docs/machine-types), subject
      to the availability in the specific region the job runs.
  """

  class DataFormatValueValuesEnum(_messages.Enum):
    r"""Required. The format of the input data.

    Values:
      DATA_FORMAT_UNSPECIFIED: Unspecified format.
      JSON: Each line of the file is a JSON dictionary representing one
        record. Currently available only for input data.
      BIGQUERY: Values are rows in a BigQuery table given its associated
        schema. Currently available only for output data.
    """
    DATA_FORMAT_UNSPECIFIED = 0
    JSON = 1
    BIGQUERY = 2

  class FrameworkValueValuesEnum(_messages.Enum):
    r"""Optional. The framework used to train this model. Only needed if
    model_version is a GCS path. Otherwise the framework specified during
    version creation will be used.

    Values:
      FRAMEWORK_UNSPECIFIED: Unspecified framework. Assigns a value based on
        the file suffix.
      TENSORFLOW: Tensorflow framework.
      SCIKIT_LEARN: Scikit-learn framework.
      XGBOOST: XGBoost framework.
    """
    FRAMEWORK_UNSPECIFIED = 0
    TENSORFLOW = 1
    SCIKIT_LEARN = 2
    XGBOOST = 3

  class OutputDataFormatValueValuesEnum(_messages.Enum):
    r"""Optional. The format of the output data, defaults to BIGQUERY.

    Values:
      DATA_FORMAT_UNSPECIFIED: Unspecified format.
      JSON: Each line of the file is a JSON dictionary representing one
        record. Currently available only for input data.
      BIGQUERY: Values are rows in a BigQuery table given its associated
        schema. Currently available only for output data.
    """
    DATA_FORMAT_UNSPECIFIED = 0
    JSON = 1
    BIGQUERY = 2

  accelerator = _messages.MessageField('GoogleCloudMlV1AcceleratorConfig', 1)
  batchSize = _messages.IntegerField(2)
  dataFormat = _messages.EnumField('DataFormatValueValuesEnum', 3)
  explanationConfig = _messages.MessageField('GoogleCloudMlV1ExplanationConfig', 4)
  framework = _messages.EnumField('FrameworkValueValuesEnum', 5)
  initialWorkerCount = _messages.IntegerField(6)
  inputPaths = _messages.StringField(7, repeated=True)
  maxWorkerCount = _messages.IntegerField(8)
  modelName = _messages.StringField(9)
  outputBigqueryTable = _messages.StringField(10)
  outputDataFormat = _messages.EnumField('OutputDataFormatValueValuesEnum', 11)
  region = _messages.StringField(12)
  runtimeVersion = _messages.StringField(13)
  signatureName = _messages.StringField(14)
  tagsOverride = _messages.StringField(15, repeated=True)
  uri = _messages.StringField(16)
  versionName = _messages.StringField(17)
  workerType = _messages.StringField(18)


class GoogleCloudMlV1ExplanationOutput(_messages.Message):
  r"""Represents results of an explanation job.

  Fields:
    errorCount: The number of data instances which resulted in errors.
    explanationCount: The number of generated explanations.
    nodeHours: Node hours used by the batch explanation job.
    outputBigqueryTable: The output BigQuery table name provided at the job
      creation time.
  """

  errorCount = _messages.IntegerField(1)
  explanationCount = _messages.IntegerField(2)
  nodeHours = _messages.FloatField(3)
  outputBigqueryTable = _messages.StringField(4)


class GoogleCloudMlV1FeatureNoiseSigma(_messages.Message):
  r"""Noise sigma by features. Noise sigma represents the standard deviation
  of the gaussian kernel that will be used to add noise to interpolated inputs
  prior to computing gradients.

  Fields:
    noiseSigma: Noise sigma per feature. No noise is added to features that
      are not set.
  """

  noiseSigma = _messages.MessageField('GoogleCloudMlV1FeatureNoiseSigmaNoiseSigmaForFeature', 1, repeated=True)


class GoogleCloudMlV1FeatureNoiseSigmaNoiseSigmaForFeature(_messages.Message):
  r"""Noise sigma for a single feature.

  Fields:
    name: The name of the input feature for which noise sigma is provided.
    sigma: Standard deviation of gaussian kernel for noise.
  """

  name = _messages.StringField(1)
  sigma = _messages.FloatField(2, variant=_messages.Variant.FLOAT)


class GoogleCloudMlV1GetConfigResponse(_messages.Message):
  r"""Returns service account information associated with a project.

  Fields:
    config: A GoogleCloudMlV1Config attribute.
    serviceAccount: The service account Cloud ML uses to access resources in
      the project.
    serviceAccountProject: The project number for `service_account`.
  """

  config = _messages.MessageField('GoogleCloudMlV1Config', 1)
  serviceAccount = _messages.StringField(2)
  serviceAccountProject = _messages.IntegerField(3)


class GoogleCloudMlV1HyperparameterOutput(_messages.Message):
  r"""Represents the result of a single hyperparameter tuning trial from a
  training job. The TrainingOutput object that is returned on successful
  completion of a training job with hyperparameter tuning includes a list of
  HyperparameterOutput objects, one for each successful trial.

  Enums:
    StateValueValuesEnum: Output only. The detailed state of the trial.

  Messages:
    HyperparametersValue: The hyperparameters given to this trial.
    WebAccessUrisValue: URIs for accessing [interactive
      shells](https://cloud.google.com/ai-platform/training/docs/monitor-
      debug-interactive-shell) (one URI for each training node). Only
      available if this trial is part of a hyperparameter tuning job and the
      job's training_input.enable_web_access is `true`. The keys are names of
      each node in the training job; for example, `master-replica-0` for the
      master node, `worker-replica-0` for the first worker, and `ps-replica-0`
      for the first parameter server. The values are the URIs for each node's
      interactive shell.

  Fields:
    allMetrics: All recorded object metrics for this trial. This field is not
      currently populated.
    builtInAlgorithmOutput: Details related to built-in algorithms jobs. Only
      set for trials of built-in algorithms jobs that have succeeded.
    endTime: Output only. End time for the trial.
    finalMetric: The final objective metric seen for this trial.
    hyperparameters: The hyperparameters given to this trial.
    isTrialStoppedEarly: True if the trial is stopped early.
    startTime: Output only. Start time for the trial.
    state: Output only. The detailed state of the trial.
    trialId: The trial id for these results.
    webAccessUris: URIs for accessing [interactive
      shells](https://cloud.google.com/ai-platform/training/docs/monitor-
      debug-interactive-shell) (one URI for each training node). Only
      available if this trial is part of a hyperparameter tuning job and the
      job's training_input.enable_web_access is `true`. The keys are names of
      each node in the training job; for example, `master-replica-0` for the
      master node, `worker-replica-0` for the first worker, and `ps-replica-0`
      for the first parameter server. The values are the URIs for each node's
      interactive shell.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The detailed state of the trial.

    Values:
      STATE_UNSPECIFIED: The job state is unspecified.
      QUEUED: The job has been just created and processing has not yet begun.
      PREPARING: The service is preparing to run the job.
      RUNNING: The job is in progress.
      SUCCEEDED: The job completed successfully.
      FAILED: The job failed. `error_message` should contain the details of
        the failure.
      CANCELLING: The job is being cancelled. `error_message` should describe
        the reason for the cancellation.
      CANCELLED: The job has been cancelled. `error_message` should describe
        the reason for the cancellation.
    """
    STATE_UNSPECIFIED = 0
    QUEUED = 1
    PREPARING = 2
    RUNNING = 3
    SUCCEEDED = 4
    FAILED = 5
    CANCELLING = 6
    CANCELLED = 7

  @encoding.MapUnrecognizedFields('additionalProperties')
  class HyperparametersValue(_messages.Message):
    r"""The hyperparameters given to this trial.

    Messages:
      AdditionalProperty: An additional property for a HyperparametersValue
        object.

    Fields:
      additionalProperties: Additional properties of type HyperparametersValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a HyperparametersValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class WebAccessUrisValue(_messages.Message):
    r"""URIs for accessing [interactive shells](https://cloud.google.com/ai-
    platform/training/docs/monitor-debug-interactive-shell) (one URI for each
    training node). Only available if this trial is part of a hyperparameter
    tuning job and the job's training_input.enable_web_access is `true`. The
    keys are names of each node in the training job; for example, `master-
    replica-0` for the master node, `worker-replica-0` for the first worker,
    and `ps-replica-0` for the first parameter server. The values are the URIs
    for each node's interactive shell.

    Messages:
      AdditionalProperty: An additional property for a WebAccessUrisValue
        object.

    Fields:
      additionalProperties: Additional properties of type WebAccessUrisValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a WebAccessUrisValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  allMetrics = _messages.MessageField('GoogleCloudMlV1HyperparameterOutputHyperparameterMetric', 1, repeated=True)
  builtInAlgorithmOutput = _messages.MessageField('GoogleCloudMlV1BuiltInAlgorithmOutput', 2)
  endTime = _messages.StringField(3)
  finalMetric = _messages.MessageField('GoogleCloudMlV1HyperparameterOutputHyperparameterMetric', 4)
  hyperparameters = _messages.MessageField('HyperparametersValue', 5)
  isTrialStoppedEarly = _messages.BooleanField(6)
  startTime = _messages.StringField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  trialId = _messages.StringField(9)
  webAccessUris = _messages.MessageField('WebAccessUrisValue', 10)


class GoogleCloudMlV1HyperparameterOutputHyperparameterMetric(_messages.Message):
  r"""An observed value of a metric.

  Fields:
    objectiveValue: The objective value at this training step.
    trainingStep: The global training step for this metric.
  """

  objectiveValue = _messages.FloatField(1)
  trainingStep = _messages.IntegerField(2)


class GoogleCloudMlV1HyperparameterSpec(_messages.Message):
  r"""Represents a set of hyperparameters to optimize.

  Enums:
    AlgorithmValueValuesEnum: Optional. The search algorithm specified for the
      hyperparameter tuning job. Uses the default AI Platform hyperparameter
      tuning algorithm if unspecified.
    GoalValueValuesEnum: Required. The type of goal to use for tuning.
      Available types are `MAXIMIZE` and `MINIMIZE`. Defaults to `MAXIMIZE`.

  Fields:
    algorithm: Optional. The search algorithm specified for the hyperparameter
      tuning job. Uses the default AI Platform hyperparameter tuning algorithm
      if unspecified.
    enableTrialEarlyStopping: Optional. Indicates if the hyperparameter tuning
      job enables auto trial early stopping.
    goal: Required. The type of goal to use for tuning. Available types are
      `MAXIMIZE` and `MINIMIZE`. Defaults to `MAXIMIZE`.
    hyperparameterMetricTag: Optional. The TensorFlow summary tag name to use
      for optimizing trials. For current versions of TensorFlow, this tag name
      should exactly match what is shown in TensorBoard, including all scopes.
      For versions of TensorFlow prior to 0.12, this should be only the tag
      passed to tf.Summary. By default, "training/hptuning/metric" will be
      used.
    maxFailedTrials: Optional. The number of failed trials that need to be
      seen before failing the hyperparameter tuning job. You can specify this
      field to override the default failing criteria for AI Platform
      hyperparameter tuning jobs. Defaults to zero, which means the service
      decides when a hyperparameter job should fail.
    maxParallelTrials: Optional. The number of training trials to run
      concurrently. You can reduce the time it takes to perform hyperparameter
      tuning by adding trials in parallel. However, each trail only benefits
      from the information gained in completed trials. That means that a trial
      does not get access to the results of trials running at the same time,
      which could reduce the quality of the overall optimization. Each trial
      will use the same scale tier and machine types. Defaults to one.
    maxTrials: Optional. How many training trials should be attempted to
      optimize the specified hyperparameters. Defaults to one.
    params: Required. The set of parameters to tune.
    resumePreviousJobId: Optional. The prior hyperparameter tuning job id that
      users hope to continue with. The job id will be used to find the
      corresponding vizier study guid and resume the study.
  """

  class AlgorithmValueValuesEnum(_messages.Enum):
    r"""Optional. The search algorithm specified for the hyperparameter tuning
    job. Uses the default AI Platform hyperparameter tuning algorithm if
    unspecified.

    Values:
      ALGORITHM_UNSPECIFIED: The default algorithm used by the hyperparameter
        tuning service. This is a Bayesian optimization algorithm.
      GRID_SEARCH: Simple grid search within the feasible space. To use grid
        search, all parameters must be `INTEGER`, `CATEGORICAL`, or
        `DISCRETE`.
      RANDOM_SEARCH: Simple random search within the feasible space.
      POPULATION_BASED_TRAINING: Population Based Training Algorithm.
    """
    ALGORITHM_UNSPECIFIED = 0
    GRID_SEARCH = 1
    RANDOM_SEARCH = 2
    POPULATION_BASED_TRAINING = 3

  class GoalValueValuesEnum(_messages.Enum):
    r"""Required. The type of goal to use for tuning. Available types are
    `MAXIMIZE` and `MINIMIZE`. Defaults to `MAXIMIZE`.

    Values:
      GOAL_TYPE_UNSPECIFIED: Goal Type will default to maximize.
      MAXIMIZE: Maximize the goal metric.
      MINIMIZE: Minimize the goal metric.
    """
    GOAL_TYPE_UNSPECIFIED = 0
    MAXIMIZE = 1
    MINIMIZE = 2

  algorithm = _messages.EnumField('AlgorithmValueValuesEnum', 1)
  enableTrialEarlyStopping = _messages.BooleanField(2)
  goal = _messages.EnumField('GoalValueValuesEnum', 3)
  hyperparameterMetricTag = _messages.StringField(4)
  maxFailedTrials = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  maxParallelTrials = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  maxTrials = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  params = _messages.MessageField('GoogleCloudMlV1ParameterSpec', 8, repeated=True)
  resumePreviousJobId = _messages.StringField(9)


class GoogleCloudMlV1IntegratedGradientsAttribution(_messages.Message):
  r"""Attributes credit by computing the Aumann-Shapley value taking advantage
  of the model's fully differentiable structure. Refer to this paper for more
  details: https://arxiv.org/abs/1703.01365

  Fields:
    blurBaselineConfig: Config for IG with blur baseline. When enabled, a
      linear path from the maximally blurred image to the input image is
      created. Using a blurred baseline instead of zero (black image) is
      motivated by the BlurIG approach explained here:
      https://arxiv.org/abs/2004.03383
    numIntegralSteps: Number of steps for approximating the path integral. A
      good value to start is 50 and gradually increase until the sum to diff
      property is met within the desired error range.
    smoothGradConfig: Config for SmoothGrad approximation of gradients. When
      enabled, the gradients are approximated by averaging the gradients from
      noisy samples in the vicinity of the inputs. Adding noise can help
      improve the computed gradients, see here for why:
      https://arxiv.org/pdf/1706.03825.pdf
  """

  blurBaselineConfig = _messages.MessageField('GoogleCloudMlV1BlurBaselineConfig', 1)
  numIntegralSteps = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  smoothGradConfig = _messages.MessageField('GoogleCloudMlV1SmoothGradConfig', 3)


class GoogleCloudMlV1Job(_messages.Message):
  r"""Represents a training or prediction job.

  Enums:
    StateValueValuesEnum: Output only. The detailed state of a job.

  Messages:
    LabelsValue: Optional. One or more labels that you can add, to organize
      your jobs. Each label is a key-value pair, where both the key and the
      value are arbitrary strings that you supply. For more information, see
      the documentation on using labels.

  Fields:
    createTime: Output only. When the job was created.
    endTime: Output only. When the job processing was completed.
    errorMessage: Output only. The details of a failure or a cancellation.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a job from overwriting each other. It is
      strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform job updates in order to avoid race
      conditions: An `etag` is returned in the response to `GetJob`, and
      systems are expected to put that etag in the request to `UpdateJob` to
      ensure that their change will be applied to the same version of the job.
    explanationInput: Input parameters to create an explanation job.
    explanationOutput: The current explanation job result.
    jobId: Required. The user-specified id of the job.
    jobPosition: Output only. It's only effect when the job is in QUEUED
      state. If it's positive, it indicates the job's position in the job
      scheduler. It's 0 when the job is already scheduled.
    labels: Optional. One or more labels that you can add, to organize your
      jobs. Each label is a key-value pair, where both the key and the value
      are arbitrary strings that you supply. For more information, see the
      documentation on using labels.
    predictionInput: Input parameters to create a prediction job.
    predictionOutput: The current prediction job result.
    startTime: Output only. When the job processing was started.
    state: Output only. The detailed state of a job.
    trainingInput: Input parameters to create a training job.
    trainingOutput: The current training job result.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The detailed state of a job.

    Values:
      STATE_UNSPECIFIED: The job state is unspecified.
      QUEUED: The job has been just created and processing has not yet begun.
      PREPARING: The service is preparing to run the job.
      RUNNING: The job is in progress.
      SUCCEEDED: The job completed successfully.
      FAILED: The job failed. `error_message` should contain the details of
        the failure.
      CANCELLING: The job is being cancelled. `error_message` should describe
        the reason for the cancellation.
      CANCELLED: The job has been cancelled. `error_message` should describe
        the reason for the cancellation.
    """
    STATE_UNSPECIFIED = 0
    QUEUED = 1
    PREPARING = 2
    RUNNING = 3
    SUCCEEDED = 4
    FAILED = 5
    CANCELLING = 6
    CANCELLED = 7

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. One or more labels that you can add, to organize your jobs.
    Each label is a key-value pair, where both the key and the value are
    arbitrary strings that you supply. For more information, see the
    documentation on using labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  endTime = _messages.StringField(2)
  errorMessage = _messages.StringField(3)
  etag = _messages.BytesField(4)
  explanationInput = _messages.MessageField('GoogleCloudMlV1ExplanationInput', 5)
  explanationOutput = _messages.MessageField('GoogleCloudMlV1ExplanationOutput', 6)
  jobId = _messages.StringField(7)
  jobPosition = _messages.IntegerField(8)
  labels = _messages.MessageField('LabelsValue', 9)
  predictionInput = _messages.MessageField('GoogleCloudMlV1PredictionInput', 10)
  predictionOutput = _messages.MessageField('GoogleCloudMlV1PredictionOutput', 11)
  startTime = _messages.StringField(12)
  state = _messages.EnumField('StateValueValuesEnum', 13)
  trainingInput = _messages.MessageField('GoogleCloudMlV1TrainingInput', 14)
  trainingOutput = _messages.MessageField('GoogleCloudMlV1TrainingOutput', 15)


class GoogleCloudMlV1ListJobsResponse(_messages.Message):
  r"""Response message for the ListJobs method.

  Fields:
    jobs: The list of jobs.
    nextPageToken: Optional. Pass this token as the `page_token` field of the
      request for a subsequent call.
  """

  jobs = _messages.MessageField('GoogleCloudMlV1Job', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudMlV1ListLocationsResponse(_messages.Message):
  r"""A GoogleCloudMlV1ListLocationsResponse object.

  Fields:
    locations: Locations where at least one type of CMLE capability is
      available.
    nextPageToken: Optional. Pass this token as the `page_token` field of the
      request for a subsequent call.
  """

  locations = _messages.MessageField('GoogleCloudMlV1Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudMlV1ListModelsResponse(_messages.Message):
  r"""Response message for the ListModels method.

  Fields:
    models: The list of models.
    nextPageToken: Optional. Pass this token as the `page_token` field of the
      request for a subsequent call.
  """

  models = _messages.MessageField('GoogleCloudMlV1Model', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudMlV1ListOptimalTrialsRequest(_messages.Message):
  r"""The request message for the ListTrials service method."""


class GoogleCloudMlV1ListOptimalTrialsResponse(_messages.Message):
  r"""The response message for the ListOptimalTrials method.

  Fields:
    trials: The pareto-optimal trials for multiple objective study or the
      optimal trial for single objective study. The definition of pareto-
      optimal can be checked in wiki page.
      https://en.wikipedia.org/wiki/Pareto_efficiency
  """

  trials = _messages.MessageField('GoogleCloudMlV1Trial', 1, repeated=True)


class GoogleCloudMlV1ListStudiesResponse(_messages.Message):
  r"""A GoogleCloudMlV1ListStudiesResponse object.

  Fields:
    studies: The studies associated with the project.
  """

  studies = _messages.MessageField('GoogleCloudMlV1Study', 1, repeated=True)


class GoogleCloudMlV1ListTrialsResponse(_messages.Message):
  r"""The response message for the ListTrials method.

  Fields:
    trials: The trials associated with the study.
  """

  trials = _messages.MessageField('GoogleCloudMlV1Trial', 1, repeated=True)


class GoogleCloudMlV1ListVersionsResponse(_messages.Message):
  r"""Response message for the ListVersions method.

  Fields:
    nextPageToken: Optional. Pass this token as the `page_token` field of the
      request for a subsequent call.
    versions: The list of versions.
  """

  nextPageToken = _messages.StringField(1)
  versions = _messages.MessageField('GoogleCloudMlV1Version', 2, repeated=True)


class GoogleCloudMlV1Location(_messages.Message):
  r"""A GoogleCloudMlV1Location object.

  Fields:
    capabilities: Capabilities available in the location.
    name: A string attribute.
  """

  capabilities = _messages.MessageField('GoogleCloudMlV1Capability', 1, repeated=True)
  name = _messages.StringField(2)


class GoogleCloudMlV1ManualScaling(_messages.Message):
  r"""Options for manually scaling a model.

  Fields:
    nodes: The number of nodes to allocate for this model. These nodes are
      always up, starting from the time the model is deployed, so the cost of
      operating this model will be proportional to `nodes` * number of hours
      since last billing cycle plus the cost for each prediction performed.
  """

  nodes = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class GoogleCloudMlV1Measurement(_messages.Message):
  r"""A message representing a measurement.

  Fields:
    elapsedTime: Output only. Time that the trial has been running at the
      point of this measurement.
    metrics: Provides a list of metrics that act as inputs into the objective
      function.
    stepCount: The number of steps a machine learning model has been trained
      for. Must be non-negative.
  """

  elapsedTime = _messages.StringField(1)
  metrics = _messages.MessageField('GoogleCloudMlV1MeasurementMetric', 2, repeated=True)
  stepCount = _messages.IntegerField(3)


class GoogleCloudMlV1MeasurementMetric(_messages.Message):
  r"""A message representing a metric in the measurement.

  Fields:
    metric: Required. Metric name.
    value: Required. The value for this metric.
  """

  metric = _messages.StringField(1)
  value = _messages.FloatField(2)


class GoogleCloudMlV1MetricSpec(_messages.Message):
  r"""MetricSpec contains the specifications to use to calculate the desired
  nodes count when autoscaling is enabled.

  Enums:
    NameValueValuesEnum: metric name.

  Fields:
    name: metric name.
    target: Target specifies the target value for the given metric; once real
      metric deviates from the threshold by a certain percentage, the node
      count changes.
  """

  class NameValueValuesEnum(_messages.Enum):
    r"""metric name.

    Values:
      METRIC_NAME_UNSPECIFIED: Unspecified MetricName.
      CPU_USAGE: CPU usage.
      GPU_DUTY_CYCLE: GPU duty cycle.
    """
    METRIC_NAME_UNSPECIFIED = 0
    CPU_USAGE = 1
    GPU_DUTY_CYCLE = 2

  name = _messages.EnumField('NameValueValuesEnum', 1)
  target = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleCloudMlV1Model(_messages.Message):
  r"""Represents a machine learning solution. A model can have multiple
  versions, each of which is a deployed, trained model ready to receive
  prediction requests. The model itself is just a container.

  Messages:
    LabelsValue: Optional. One or more labels that you can add, to organize
      your models. Each label is a key-value pair, where both the key and the
      value are arbitrary strings that you supply. For more information, see
      the documentation on using labels. Note that this field is not updatable
      for mls1* models.

  Fields:
    defaultVersion: Output only. The default version of the model. This
      version will be used to handle prediction requests that do not specify a
      version. You can change the default version by calling
      projects.models.versions.setDefault.
    description: Optional. The description specified for the model when it was
      created.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a model from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform model updates in order to avoid race
      conditions: An `etag` is returned in the response to `GetModel`, and
      systems are expected to put that etag in the request to `UpdateModel` to
      ensure that their change will be applied to the model as intended.
    labels: Optional. One or more labels that you can add, to organize your
      models. Each label is a key-value pair, where both the key and the value
      are arbitrary strings that you supply. For more information, see the
      documentation on using labels. Note that this field is not updatable for
      mls1* models.
    name: Required. The name specified for the model when it was created. The
      model name must be unique within the project it is created in.
    onlinePredictionConsoleLogging: Optional. If true, online prediction nodes
      send `stderr` and `stdout` streams to Cloud Logging. These can be more
      verbose than the standard access logs (see `onlinePredictionLogging`)
      and can incur higher cost. However, they are helpful for debugging. Note
      that [logs may incur a cost](/stackdriver/pricing), especially if your
      project receives prediction requests at a high QPS. Estimate your costs
      before enabling this option. Default is false.
    onlinePredictionLogging: Optional. If true, online prediction access logs
      are sent to Cloud Logging. These logs are like standard server access
      logs, containing information like timestamp and latency for each
      request. Note that [logs may incur a cost](/stackdriver/pricing),
      especially if your project receives prediction requests at a high
      queries per second rate (QPS). Estimate your costs before enabling this
      option. Default is false.
    regions: Optional. The list of regions where the model is going to be
      deployed. Only one region per model is supported. Defaults to 'us-
      central1' if nothing is set. See the available regions for AI Platform
      services. Note: * No matter where a model is deployed, it can always be
      accessed by users from anywhere, both for online and batch prediction. *
      The region for a batch prediction job is set by the region field when
      submitting the batch prediction job and does not take its value from
      this field.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. One or more labels that you can add, to organize your
    models. Each label is a key-value pair, where both the key and the value
    are arbitrary strings that you supply. For more information, see the
    documentation on using labels. Note that this field is not updatable for
    mls1* models.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  defaultVersion = _messages.MessageField('GoogleCloudMlV1Version', 1)
  description = _messages.StringField(2)
  etag = _messages.BytesField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  onlinePredictionConsoleLogging = _messages.BooleanField(6)
  onlinePredictionLogging = _messages.BooleanField(7)
  regions = _messages.StringField(8, repeated=True)


class GoogleCloudMlV1NasJobOutput(_messages.Message):
  r"""The output of Neural Archhitecture Search (NAS) jobs.

  Fields:
    multiTrialJobOutputs: The output of a multi-trial Neural Architecture
      Search (NAS) job.
  """

  multiTrialJobOutputs = _messages.MessageField('GoogleCloudMlV1NasJobOutputMultiTrialJobOutputs', 1)


class GoogleCloudMlV1NasJobOutputMultiTrialJobOutput(_messages.Message):
  r"""The output of Multi-trial Neural Architecture Search (NAS) jobs.

  Enums:
    StateValueValuesEnum: Output only. The detailed state of the trial.

  Fields:
    allMetrics: All objective metrics for this Neural Architecture Search
      (NAS) job.
    endTime: Output only. End time for the trial.
    finalMetric: The final objective metric seen for this Neural Architecture
      Search (NAS) job.
    nasParamsStr: The parameters that are associated with this Neural
      Architecture Search (NAS) job.
    startTime: Output only. Start time for the trial.
    state: Output only. The detailed state of the trial.
    trialId: The trial id for these results.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The detailed state of the trial.

    Values:
      STATE_UNSPECIFIED: The job state is unspecified.
      QUEUED: The job has been just created and processing has not yet begun.
      PREPARING: The service is preparing to run the job.
      RUNNING: The job is in progress.
      SUCCEEDED: The job completed successfully.
      FAILED: The job failed. `error_message` should contain the details of
        the failure.
      CANCELLING: The job is being cancelled. `error_message` should describe
        the reason for the cancellation.
      CANCELLED: The job has been cancelled. `error_message` should describe
        the reason for the cancellation.
    """
    STATE_UNSPECIFIED = 0
    QUEUED = 1
    PREPARING = 2
    RUNNING = 3
    SUCCEEDED = 4
    FAILED = 5
    CANCELLING = 6
    CANCELLED = 7

  allMetrics = _messages.MessageField('GoogleCloudMlV1NasJobOutputMultiTrialJobOutputNasParameterMetric', 1, repeated=True)
  endTime = _messages.StringField(2)
  finalMetric = _messages.MessageField('GoogleCloudMlV1NasJobOutputMultiTrialJobOutputNasParameterMetric', 3)
  nasParamsStr = _messages.StringField(4)
  startTime = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  trialId = _messages.StringField(7)


class GoogleCloudMlV1NasJobOutputMultiTrialJobOutputNasParameterMetric(_messages.Message):
  r"""An observed value of a metric of the trial.

  Messages:
    MetricsValue: Reported metrics other than objective and model_flops

  Fields:
    metrics: Reported metrics other than objective and model_flops
    modelFlops: The model flops associated with the `objective_value`.
    objectiveValue: The objective value at this training step.
    trainingStep: The global training step for this metric.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetricsValue(_messages.Message):
    r"""Reported metrics other than objective and model_flops

    Messages:
      AdditionalProperty: An additional property for a MetricsValue object.

    Fields:
      additionalProperties: Additional properties of type MetricsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetricsValue object.

      Fields:
        key: Name of the additional property.
        value: A number attribute.
      """

      key = _messages.StringField(1)
      value = _messages.FloatField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  metrics = _messages.MessageField('MetricsValue', 1)
  modelFlops = _messages.FloatField(2)
  objectiveValue = _messages.FloatField(3)
  trainingStep = _messages.IntegerField(4)


class GoogleCloudMlV1NasJobOutputMultiTrialJobOutputs(_messages.Message):
  r"""The list of all MultiTrialJobOutput.

  Fields:
    multiTrialJobOutput: A GoogleCloudMlV1NasJobOutputMultiTrialJobOutput
      attribute.
  """

  multiTrialJobOutput = _messages.MessageField('GoogleCloudMlV1NasJobOutputMultiTrialJobOutput', 1, repeated=True)


class GoogleCloudMlV1NasSpec(_messages.Message):
  r"""Spec for Neural Architecture Search (NAS) jobs.

  Fields:
    multiTrialAlgorithmSpec: The spec of multi-trial algorithms.
    oneShotAlgorithmSpec: The spec of one-shot algorithms.
    previousNasJobId: The previous NAS job ID to resume search. The
      `search_space_spec` needs to be the same between this and previous NAS
      job and its job state is `FINISHED` or `CANCELLED`.
    searchSpaceSpec: Required. It defines the search space for Neural
      Architecture Search (NAS).
  """

  multiTrialAlgorithmSpec = _messages.MessageField('GoogleCloudMlV1NasSpecMultiTrialAlgorithmSpec', 1)
  oneShotAlgorithmSpec = _messages.MessageField('GoogleCloudMlV1NasSpecOneShotAlgorithmSpec', 2)
  previousNasJobId = _messages.StringField(3)
  searchSpaceSpec = _messages.StringField(4)


class GoogleCloudMlV1NasSpecMultiTrialAlgorithmSpec(_messages.Message):
  r"""The spec of multi-trial Neural Architecture Search (NAS).

  Enums:
    MultiTrialAlgorithmValueValuesEnum: Optional. The multi-trial Neural
      Architecture Search (NAS) algorithm type. Defaults to
      `NAS_MULTI_TRIAL_ALGORITHM_REINFORCEMENT_LEARNING`.

  Fields:
    initialIgnoredModelCount: If non-zero, it specifies the number of first
      models whose rewards will be ignored.
    maxFailedNasTrials: Optional. It decides when a Neural Architecture Search
      (NAS) job should fail. Defaults to zero.
    maxNasTrials: Optional. How many Neural Architecture Search (NAS) trials
      should be attempted.
    maxParallelNasTrials: Required. The number of Neural Architecture Search
      (NAS) trials to run concurrently.
    multiTrialAlgorithm: Optional. The multi-trial Neural Architecture Search
      (NAS) algorithm type. Defaults to
      `NAS_MULTI_TRIAL_ALGORITHM_REINFORCEMENT_LEARNING`.
    nasTargetRewardMetric: Required. The TensorFlow summary tag that the
      controller tries to optimize. Its value needs to be consistent with the
      TensorFlow summary tag that is reported by trainer (customer provided
      dockers).
  """

  class MultiTrialAlgorithmValueValuesEnum(_messages.Enum):
    r"""Optional. The multi-trial Neural Architecture Search (NAS) algorithm
    type. Defaults to `NAS_MULTI_TRIAL_ALGORITHM_REINFORCEMENT_LEARNING`.

    Values:
      MULTI_TRIAL_ALGORITHM_UNSPECIFIED: <no description>
      REINFORCEMENT_LEARNING: The Reinforcement Learning Algorithm for Multi-
        trial Neural Architecture Search (NAS).
      GRID_SEARCH: The Grid Search Algorithm for Multi-trial Neural
        Architecture Search (NAS).
      REGULARIZED_EVOLUTION: The Regularized evolution Algorithm for Multi-
        trial Neural Architecture Search (NAS).
    """
    MULTI_TRIAL_ALGORITHM_UNSPECIFIED = 0
    REINFORCEMENT_LEARNING = 1
    GRID_SEARCH = 2
    REGULARIZED_EVOLUTION = 3

  initialIgnoredModelCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  maxFailedNasTrials = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  maxNasTrials = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  maxParallelNasTrials = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  multiTrialAlgorithm = _messages.EnumField('MultiTrialAlgorithmValueValuesEnum', 5)
  nasTargetRewardMetric = _messages.StringField(6)


class GoogleCloudMlV1NasSpecOneShotAlgorithmSpec(_messages.Message):
  r"""The spec of one shot Neural Architecture Search (NAS).

  Enums:
    OneShotAlgorithmValueValuesEnum: Optional. The one-shot Neural
      Architecture Search (NAS) algorithm type. Defaults to
      `ONE_SHOT_ALGORITHM_REINFORCEMENT_LEARNING`.

  Fields:
    oneShotAlgorithm: Optional. The one-shot Neural Architecture Search (NAS)
      algorithm type. Defaults to `ONE_SHOT_ALGORITHM_REINFORCEMENT_LEARNING`.
  """

  class OneShotAlgorithmValueValuesEnum(_messages.Enum):
    r"""Optional. The one-shot Neural Architecture Search (NAS) algorithm
    type. Defaults to `ONE_SHOT_ALGORITHM_REINFORCEMENT_LEARNING`.

    Values:
      ONE_SHOT_ALGORITHM_UNSPECIFIED: <no description>
      REINFORCEMENT_LEARNING: The Reinforcement Learning Algorithm for one-
        shot Neural Architecture Search (NAS).
    """
    ONE_SHOT_ALGORITHM_UNSPECIFIED = 0
    REINFORCEMENT_LEARNING = 1

  oneShotAlgorithm = _messages.EnumField('OneShotAlgorithmValueValuesEnum', 1)


class GoogleCloudMlV1OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Enums:
    OperationTypeValueValuesEnum: The operation type.

  Messages:
    LabelsValue: The user labels, inherited from the model or the model
      version being operated on.

  Fields:
    createTime: The time the operation was submitted.
    endTime: The time operation processing completed.
    isCancellationRequested: Indicates whether a request to cancel this
      operation has been made.
    labels: The user labels, inherited from the model or the model version
      being operated on.
    modelName: Contains the name of the model associated with the operation.
    operationType: The operation type.
    projectNumber: Contains the project number associated with the operation.
    startTime: The time operation processing started.
    version: Contains the version associated with the operation.
  """

  class OperationTypeValueValuesEnum(_messages.Enum):
    r"""The operation type.

    Values:
      OPERATION_TYPE_UNSPECIFIED: Unspecified operation type.
      CREATE_VERSION: An operation to create a new version.
      DELETE_VERSION: An operation to delete an existing version.
      DELETE_MODEL: An operation to delete an existing model.
      UPDATE_MODEL: An operation to update an existing model.
      UPDATE_VERSION: An operation to update an existing version.
      UPDATE_CONFIG: An operation to update project configuration.
    """
    OPERATION_TYPE_UNSPECIFIED = 0
    CREATE_VERSION = 1
    DELETE_VERSION = 2
    DELETE_MODEL = 3
    UPDATE_MODEL = 4
    UPDATE_VERSION = 5
    UPDATE_CONFIG = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The user labels, inherited from the model or the model version being
    operated on.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  endTime = _messages.StringField(2)
  isCancellationRequested = _messages.BooleanField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  modelName = _messages.StringField(5)
  operationType = _messages.EnumField('OperationTypeValueValuesEnum', 6)
  projectNumber = _messages.IntegerField(7)
  startTime = _messages.StringField(8)
  version = _messages.MessageField('GoogleCloudMlV1Version', 9)


class GoogleCloudMlV1ParameterSpec(_messages.Message):
  r"""Represents a single hyperparameter to optimize.

  Enums:
    ScaleTypeValueValuesEnum: Optional. How the parameter should be scaled to
      the hypercube. Leave unset for categorical parameters. Some kind of
      scaling is strongly recommended for real or integral parameters (e.g.,
      `UNIT_LINEAR_SCALE`).
    TypeValueValuesEnum: Required. The type of the parameter.

  Fields:
    categoricalValues: Required if type is `CATEGORICAL`. The list of possible
      categories.
    discreteValues: Required if type is `DISCRETE`. A list of feasible points.
      The list should be in strictly increasing order. For instance, this
      parameter might have possible settings of 1.5, 2.5, and 4.0. This list
      should not contain more than 1,000 values.
    maxValue: Required if type is `DOUBLE` or `INTEGER`. This field should be
      unset if type is `CATEGORICAL`. This value should be integers if type is
      `INTEGER`.
    minValue: Required if type is `DOUBLE` or `INTEGER`. This field should be
      unset if type is `CATEGORICAL`. This value should be integers if type is
      INTEGER.
    parameterName: Required. The parameter name must be unique amongst all
      ParameterConfigs in a HyperparameterSpec message. E.g., "learning_rate".
    scaleType: Optional. How the parameter should be scaled to the hypercube.
      Leave unset for categorical parameters. Some kind of scaling is strongly
      recommended for real or integral parameters (e.g., `UNIT_LINEAR_SCALE`).
    type: Required. The type of the parameter.
  """

  class ScaleTypeValueValuesEnum(_messages.Enum):
    r"""Optional. How the parameter should be scaled to the hypercube. Leave
    unset for categorical parameters. Some kind of scaling is strongly
    recommended for real or integral parameters (e.g., `UNIT_LINEAR_SCALE`).

    Values:
      NONE: By default, no scaling is applied.
      UNIT_LINEAR_SCALE: Scales the feasible space to (0, 1) linearly.
      UNIT_LOG_SCALE: Scales the feasible space logarithmically to (0, 1). The
        entire feasible space must be strictly positive.
      UNIT_REVERSE_LOG_SCALE: Scales the feasible space "reverse"
        logarithmically to (0, 1). The result is that values close to the top
        of the feasible space are spread out more than points near the bottom.
        The entire feasible space must be strictly positive.
    """
    NONE = 0
    UNIT_LINEAR_SCALE = 1
    UNIT_LOG_SCALE = 2
    UNIT_REVERSE_LOG_SCALE = 3

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of the parameter.

    Values:
      PARAMETER_TYPE_UNSPECIFIED: You must specify a valid type. Using this
        unspecified type will result in an error.
      DOUBLE: Type for real-valued parameters.
      INTEGER: Type for integral parameters.
      CATEGORICAL: The parameter is categorical, with a value chosen from the
        categories field.
      DISCRETE: The parameter is real valued, with a fixed set of feasible
        points. If `type==DISCRETE`, feasible_points must be provided, and
        {`min_value`, `max_value`} will be ignored.
    """
    PARAMETER_TYPE_UNSPECIFIED = 0
    DOUBLE = 1
    INTEGER = 2
    CATEGORICAL = 3
    DISCRETE = 4

  categoricalValues = _messages.StringField(1, repeated=True)
  discreteValues = _messages.FloatField(2, repeated=True)
  maxValue = _messages.FloatField(3)
  minValue = _messages.FloatField(4)
  parameterName = _messages.StringField(5)
  scaleType = _messages.EnumField('ScaleTypeValueValuesEnum', 6)
  type = _messages.EnumField('TypeValueValuesEnum', 7)


class GoogleCloudMlV1PredictRequest(_messages.Message):
  r"""Request for predictions to be issued against a trained model.

  Fields:
    httpBody:  Required. The prediction request body. Refer to the [request
      body details section](#request-body-details) for more information on how
      to structure your request.
  """

  httpBody = _messages.MessageField('GoogleApiHttpBody', 1)


class GoogleCloudMlV1PredictionInput(_messages.Message):
  r"""Represents input parameters for a prediction job.

  Enums:
    DataFormatValueValuesEnum: Required. The format of the input data files.
    FrameworkValueValuesEnum: Optional. The framework used to train this
      model. Only needed if model_version is a GCS path. Otherwise the
      framework specified during version creation will be used.
    OutputDataFormatValueValuesEnum: Optional. Format of the output data
      files, defaults to JSON.

  Fields:
    accelerator: Optional. The type and number of accelerators to be attached
      to each machine running the job.
    batchSize: Optional. Number of records per batch, defaults to 64. The
      service will buffer batch_size number of records in memory before
      invoking one Tensorflow prediction call internally. So take the record
      size and memory available into consideration when setting this
      parameter.
    dataFormat: Required. The format of the input data files.
    framework: Optional. The framework used to train this model. Only needed
      if model_version is a GCS path. Otherwise the framework specified during
      version creation will be used.
    initialWorkerCount: Optional. The initial number of workers to be used for
      parallel processing. Defaults to 0 if one wants the service to figure
      out the number. The actual number of workers being used may change after
      the job starts depending on the autoscaling policy.
    inputPaths: Required. The Cloud Storage location of the input data files.
      May contain wildcards.
    maxWorkerCount: Optional. The maximum number of workers to be used for
      parallel processing. Defaults to 10 if not specified.
    modelName: Use this field if you want to use the default version for the
      specified model. The string must use the following format:
      `"projects/YOUR_PROJECT/models/YOUR_MODEL"`
    outputDataFormat: Optional. Format of the output data files, defaults to
      JSON.
    outputPath: Required. The output Google Cloud Storage location.
    region: Required. The Google Compute Engine region to run the prediction
      job in. See the available regions for AI Platform services.
    runtimeVersion: Optional. The AI Platform runtime version to use for this
      batch prediction. If not set, AI Platform will pick the runtime version
      used during the CreateVersion request for this model version, or choose
      the latest stable version when model version information is not
      available such as when the model is specified by uri.
    signatureName: Optional. The name of the signature defined in the
      SavedModel to use for this job. Please refer to
      [SavedModel](https://tensorflow.github.io/serving/serving_basic.html)
      for information about how to use signatures. Defaults to [DEFAULT_SERVIN
      G_SIGNATURE_DEF_KEY](https://www.tensorflow.org/api_docs/python/tf/saved
      _model/signature_constants) , which is "serving_default".
    tagsOverride: Optional. The set of tags to select which meta graph defined
      in the SavedModel to use for this job. Please refer to
      [SavedModel](https://www.tensorflow.org/serving/serving_basic) for
      information about how to use tags. Overrides the default tags when
      predicting from a deployed model version. When predicting from a model
      directory, the tag defaults to [SERVING](https://www.tensorflow.org/api_
      docs/python/tf/saved_model/tag_constants) , which is "serve".
    uri: Use this field if you want to specify a Google Cloud Storage path for
      the model to use.
    versionName: Use this field if you want to specify a version of the model
      to use. The string is formatted the same way as `model_version`, with
      the addition of the version information:
      `"projects/YOUR_PROJECT/models/YOUR_MODEL/versions/YOUR_VERSION"`
    workerType: Optional. The type of virtual machine to use for batch
      prediction job's worker nodes. It supports all machine types available
      on GCP ( https://cloud.google.com/compute/docs/machine-types), subject
      to the availability in the specific region the job runs.
  """

  class DataFormatValueValuesEnum(_messages.Enum):
    r"""Required. The format of the input data files.

    Values:
      DATA_FORMAT_UNSPECIFIED: Unspecified format.
      JSON: Each line of the file is a JSON dictionary representing one
        record.
      TEXT: Deprecated. Use JSON instead.
      TF_RECORD: The source file is a TFRecord file. Currently available only
        for input data.
      TF_RECORD_GZIP: The source file is a GZIP-compressed TFRecord file.
        Currently available only for input data.
      FILE_LIST: Each line of the file is the location of an instance to
        process. Currently available only for input data.
      CSV: Values are comma-separated rows, with keys in a separate file.
        Currently available only for output data.
    """
    DATA_FORMAT_UNSPECIFIED = 0
    JSON = 1
    TEXT = 2
    TF_RECORD = 3
    TF_RECORD_GZIP = 4
    FILE_LIST = 5
    CSV = 6

  class FrameworkValueValuesEnum(_messages.Enum):
    r"""Optional. The framework used to train this model. Only needed if
    model_version is a GCS path. Otherwise the framework specified during
    version creation will be used.

    Values:
      FRAMEWORK_UNSPECIFIED: Unspecified framework. Assigns a value based on
        the file suffix.
      TENSORFLOW: Tensorflow framework.
      SCIKIT_LEARN: Scikit-learn framework.
      XGBOOST: XGBoost framework.
    """
    FRAMEWORK_UNSPECIFIED = 0
    TENSORFLOW = 1
    SCIKIT_LEARN = 2
    XGBOOST = 3

  class OutputDataFormatValueValuesEnum(_messages.Enum):
    r"""Optional. Format of the output data files, defaults to JSON.

    Values:
      DATA_FORMAT_UNSPECIFIED: Unspecified format.
      JSON: Each line of the file is a JSON dictionary representing one
        record.
      TEXT: Deprecated. Use JSON instead.
      TF_RECORD: The source file is a TFRecord file. Currently available only
        for input data.
      TF_RECORD_GZIP: The source file is a GZIP-compressed TFRecord file.
        Currently available only for input data.
      FILE_LIST: Each line of the file is the location of an instance to
        process. Currently available only for input data.
      CSV: Values are comma-separated rows, with keys in a separate file.
        Currently available only for output data.
    """
    DATA_FORMAT_UNSPECIFIED = 0
    JSON = 1
    TEXT = 2
    TF_RECORD = 3
    TF_RECORD_GZIP = 4
    FILE_LIST = 5
    CSV = 6

  accelerator = _messages.MessageField('GoogleCloudMlV1AcceleratorConfig', 1)
  batchSize = _messages.IntegerField(2)
  dataFormat = _messages.EnumField('DataFormatValueValuesEnum', 3)
  framework = _messages.EnumField('FrameworkValueValuesEnum', 4)
  initialWorkerCount = _messages.IntegerField(5)
  inputPaths = _messages.StringField(6, repeated=True)
  maxWorkerCount = _messages.IntegerField(7)
  modelName = _messages.StringField(8)
  outputDataFormat = _messages.EnumField('OutputDataFormatValueValuesEnum', 9)
  outputPath = _messages.StringField(10)
  region = _messages.StringField(11)
  runtimeVersion = _messages.StringField(12)
  signatureName = _messages.StringField(13)
  tagsOverride = _messages.StringField(14, repeated=True)
  uri = _messages.StringField(15)
  versionName = _messages.StringField(16)
  workerType = _messages.StringField(17)


class GoogleCloudMlV1PredictionOutput(_messages.Message):
  r"""Represents results of a prediction job.

  Fields:
    errorCount: The number of data instances which resulted in errors.
    nodeHours: Node hours used by the batch prediction job.
    outputPath: The output Google Cloud Storage location provided at the job
      creation time.
    predictionCount: The number of generated predictions.
  """

  errorCount = _messages.IntegerField(1)
  nodeHours = _messages.FloatField(2)
  outputPath = _messages.StringField(3)
  predictionCount = _messages.IntegerField(4)


class GoogleCloudMlV1ReplicaConfig(_messages.Message):
  r"""Represents the configuration for a replica in a cluster.

  Fields:
    acceleratorConfig: Represents the type and number of accelerators used by
      the replica. [Learn about restrictions on accelerator configurations for
      training.](/ai-platform/training/docs/using-gpus#compute-engine-machine-
      types-with-gpu)
    containerArgs: Arguments to the entrypoint command. The following rules
      apply for container_command and container_args: - If you do not supply
      command or args: The defaults defined in the Docker image are used. - If
      you supply a command but no args: The default EntryPoint and the default
      Cmd defined in the Docker image are ignored. Your command is run without
      any arguments. - If you supply only args: The default Entrypoint defined
      in the Docker image is run with the args that you supplied. - If you
      supply a command and args: The default Entrypoint and the default Cmd
      defined in the Docker image are ignored. Your command is run with your
      args. It cannot be set if custom container image is not provided. Note
      that this field and [TrainingInput.args] are mutually exclusive, i.e.,
      both cannot be set at the same time.
    containerCommand: The command with which the replica's custom container is
      run. If provided, it will override default ENTRYPOINT of the docker
      image. If not provided, the docker image's ENTRYPOINT is used. It cannot
      be set if custom container image is not provided. Note that this field
      and [TrainingInput.args] are mutually exclusive, i.e., both cannot be
      set at the same time.
    diskConfig: Represents the configuration of disk options.
    imageUri: The Docker image to run on the replica. This image must be in
      Container Registry. Learn more about [configuring custom
      containers](/ai-platform/training/docs/distributed-training-containers).
    tpuTfVersion: The AI Platform runtime version that includes a TensorFlow
      version matching the one used in the custom container. This field is
      required if the replica is a TPU worker that uses a custom container.
      Otherwise, do not specify this field. This must be a [runtime version
      that currently supports training with TPUs](/ml-
      engine/docs/tensorflow/runtime-version-list#tpu-support). Note that the
      version of TensorFlow included in a runtime version may differ from the
      numbering of the runtime version itself, because it may have a different
      [patch version](https://www.tensorflow.org/guide/version_compat#semantic
      _versioning_20). In this field, you must specify the runtime version
      (TensorFlow minor version). For example, if your custom container runs
      TensorFlow `1.x.y`, specify `1.x`.
  """

  acceleratorConfig = _messages.MessageField('GoogleCloudMlV1AcceleratorConfig', 1)
  containerArgs = _messages.StringField(2, repeated=True)
  containerCommand = _messages.StringField(3, repeated=True)
  diskConfig = _messages.MessageField('GoogleCloudMlV1DiskConfig', 4)
  imageUri = _messages.StringField(5)
  tpuTfVersion = _messages.StringField(6)


class GoogleCloudMlV1RequestLoggingConfig(_messages.Message):
  r"""Configuration for logging request-response pairs to a BigQuery table.
  Online prediction requests to a model version and the responses to these
  requests are converted to raw strings and saved to the specified BigQuery
  table. Logging is constrained by [BigQuery quotas and
  limits](/bigquery/quotas). If your project exceeds BigQuery quotas or
  limits, AI Platform Prediction does not log request-response pairs, but it
  continues to serve predictions. If you are using [continuous
  evaluation](/ml-engine/docs/continuous-evaluation/), you do not need to
  specify this configuration manually. Setting up continuous evaluation
  automatically enables logging of request-response pairs.

  Fields:
    bigqueryTableName: Required. Fully qualified BigQuery table name in the
      following format: " project_id.dataset_name.table_name" The specified
      table must already exist, and the "Cloud ML Service Agent" for your
      project must have permission to write to it. The table must have the
      following [schema](/bigquery/docs/schemas): Field name Type Mode model
      STRING REQUIRED model_version STRING REQUIRED time TIMESTAMP REQUIRED
      raw_data STRING REQUIRED raw_prediction STRING NULLABLE groundtruth
      STRING NULLABLE
    samplingPercentage: Percentage of requests to be logged, expressed as a
      fraction from 0 to 1. For example, if you want to log 10% of requests,
      enter `0.1`. The sampling window is the lifetime of the model version.
      Defaults to 0.
  """

  bigqueryTableName = _messages.StringField(1)
  samplingPercentage = _messages.FloatField(2)


class GoogleCloudMlV1RouteMap(_messages.Message):
  r"""Specifies HTTP paths served by a custom container. AI Platform
  Prediction sends requests to these paths on the container; the custom
  container must run an HTTP server that responds to these requests with
  appropriate responses. Read [Custom container requirements](/ai-
  platform/prediction/docs/custom-container-requirements) for details on how
  to create your container image to meet these requirements.

  Fields:
    health: HTTP path on the container to send health checkss to. AI Platform
      Prediction intermittently sends GET requests to this path on the
      container's IP address and port to check that the container is healthy.
      Read more about [health checks](/ai-platform/prediction/docs/custom-
      container-requirements#checks). For example, if you set this field to
      `/bar`, then AI Platform Prediction intermittently sends a GET request
      to the `/bar` path on the port of your container specified by the first
      value of Version.container.ports. If you don't specify this field, it
      defaults to the following value: /v1/models/ MODEL/versions/VERSION The
      placeholders in this value are replaced as follows: * MODEL: The name of
      the parent Model. This does not include the
      "projects/PROJECT_ID/models/" prefix that the API returns in output; it
      is the bare model name, as provided to projects.models.create. *
      VERSION: The name of the model version. This does not include the
      "projects/PROJECT_ID /models/MODEL/versions/" prefix that the API
      returns in output; it is the bare version name, as provided to
      projects.models.versions.create.
    predict: HTTP path on the container to send prediction requests to. AI
      Platform Prediction forwards requests sent using projects.predict to
      this path on the container's IP address and port. AI Platform Prediction
      then returns the container's response in the API response. For example,
      if you set this field to `/foo`, then when AI Platform Prediction
      receives a prediction request, it forwards the request body in a POST
      request to the `/foo` path on the port of your container specified by
      the first value of Version.container.ports. If you don't specify this
      field, it defaults to the following value:
      /v1/models/MODEL/versions/VERSION:predict The placeholders in this value
      are replaced as follows: * MODEL: The name of the parent Model. This
      does not include the "projects/PROJECT_ID/models/" prefix that the API
      returns in output; it is the bare model name, as provided to
      projects.models.create. * VERSION: The name of the model version. This
      does not include the "projects/PROJECT_ID/models/MODEL/versions/" prefix
      that the API returns in output; it is the bare version name, as provided
      to projects.models.versions.create.
  """

  health = _messages.StringField(1)
  predict = _messages.StringField(2)


class GoogleCloudMlV1SaabasAttribution(_messages.Message):
  r"""Attributes credit by running a faster approximation to the TreeShap
  method. Please refer to this link for more details:
  https://blog.datadive.net/interpreting-random-forests/ This attribution
  method is only supported for XGBoost models.
  """



class GoogleCloudMlV1SampledShapleyAttribution(_messages.Message):
  r"""An attribution method that approximates Shapley values for features that
  contribute to the label being predicted. A sampling strategy is used to
  approximate the value rather than considering all subsets of features.

  Fields:
    numPaths: The number of feature permutations to consider when
      approximating the Shapley values.
  """

  numPaths = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class GoogleCloudMlV1Scheduling(_messages.Message):
  r"""All parameters related to scheduling of training jobs.

  Enums:
    StrategyValueValuesEnum: Optional. TODO(b/148493578) : point to
      documentation when ready.

  Fields:
    maxRunningTime: Optional. The maximum job running time, expressed in
      seconds. The field can contain up to nine fractional digits, terminated
      by `s`. If not specified, this field defaults to `604800s` (seven days).
      If the training job is still running after this duration, AI Platform
      Training cancels it. The duration is measured from when the job enters
      the `RUNNING` state; therefore it does not overlap with the duration
      limited by Scheduling.max_wait_time. For example, if you want to ensure
      your job runs for no more than 2 hours, set this field to `7200s` (2
      hours * 60 minutes / hour * 60 seconds / minute). If you submit your
      training job using the `gcloud` tool, you can [specify this field in a
      `config.yaml` file](/ai-platform/training/docs/training-
      jobs#formatting_your_configuration_parameters). For example: ```yaml
      trainingInput: scheduling: maxRunningTime: 7200s ```
    maxWaitTime: Optional. The maximum job wait time, expressed in seconds.
      The field can contain up to nine fractional digits, terminated by `s`.
      If not specified, there is no limit to the wait time. The minimum for
      this field is `1800s` (30 minutes). If the training job has not entered
      the `RUNNING` state after this duration, AI Platform Training cancels
      it. After the job begins running, it can no longer be cancelled due to
      the maximum wait time. Therefore the duration limited by this field does
      not overlap with the duration limited by Scheduling.max_running_time.
      For example, if the job temporarily stops running and retries due to a
      [VM restart](/ai-platform/training/docs/overview#restarts), this cannot
      lead to a maximum wait time cancellation. However, independently of this
      constraint, AI Platform Training might stop a job if there are too many
      retries due to exhausted resources in a region. The following example
      describes how you might use this field: To cancel your job if it doesn't
      start running within 1 hour, set this field to `3600s` (1 hour * 60
      minutes / hour * 60 seconds / minute). If the job is still in the
      `QUEUED` or `PREPARING` state after an hour of waiting, AI Platform
      Training cancels the job. If you submit your training job using the
      `gcloud` tool, you can [specify this field in a `config.yaml` file](/ai-
      platform/training/docs/training-
      jobs#formatting_your_configuration_parameters). For example: ```yaml
      trainingInput: scheduling: maxWaitTime: 3600s ```
    priority: Optional. Job scheduling will be based on this priority, which
      in the range [0, 1000]. The bigger the number, the higher the priority.
      Default to 0 if not set. If there are multiple jobs requesting same type
      of accelerators, the high priority job will be scheduled prior to ones
      with low priority.
    resilientToWorkerRestart: Optional. If true, reschedules an entire job if
      a worker gets restarted. This feature can be used by distributed
      training jobs that are not resilient to workers leaving and joining a
      job.
    strategy: Optional. TODO(b/148493578) : point to documentation when ready.
  """

  class StrategyValueValuesEnum(_messages.Enum):
    r"""Optional. TODO(b/148493578) : point to documentation when ready.

    Values:
      STRATEGY_UNSPECIFIED: Strategy will default to ON_DEMAND.
      ON_DEMAND: Regular on-demand provisioning strategy.
      LOW_COST: Low cost by making potential use of Preemptible resources.
    """
    STRATEGY_UNSPECIFIED = 0
    ON_DEMAND = 1
    LOW_COST = 2

  maxRunningTime = _messages.StringField(1)
  maxWaitTime = _messages.StringField(2)
  priority = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  resilientToWorkerRestart = _messages.BooleanField(4)
  strategy = _messages.EnumField('StrategyValueValuesEnum', 5)


class GoogleCloudMlV1SetDefaultVersionRequest(_messages.Message):
  r"""Request message for the SetDefaultVersion request."""


class GoogleCloudMlV1SmoothGradConfig(_messages.Message):
  r"""Config for SmoothGrad approximation of gradients. When enabled, the
  gradients are approximated by averaging the gradients from noisy samples in
  the vicinity of the inputs. Adding noise can help improve the computed
  gradients. See here for why https://arxiv.org/pdf/1706.03825.pdf

  Fields:
    featureNoiseSigma: Alternatively, set this to use different noise_sigma
      per feature. One entry per feature. No noise is added to features that
      are not set.
    noiseSigma: If set, this std. deviation will be used to apply noise to all
      features.
    noisySampleCount: The number of gradient samples to use for approximation.
      The higher this number, the more accurate the gradient is, but the
      runtime complexity of IG increases by this factor as well.
  """

  featureNoiseSigma = _messages.MessageField('GoogleCloudMlV1FeatureNoiseSigma', 1)
  noiseSigma = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  noisySampleCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class GoogleCloudMlV1StopTrialRequest(_messages.Message):
  r"""A GoogleCloudMlV1StopTrialRequest object."""


class GoogleCloudMlV1Study(_messages.Message):
  r"""A message representing a Study.

  Enums:
    StateValueValuesEnum: Output only. The detailed state of a study.

  Fields:
    createTime: Output only. Time at which the study was created.
    inactiveReason: Output only. A human readable reason why the Study is
      inactive. This should be empty if a study is ACTIVE or COMPLETED.
    name: Output only. The name of a study.
    state: Output only. The detailed state of a study.
    studyConfig: Required. Configuration of the study.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The detailed state of a study.

    Values:
      STATE_UNSPECIFIED: The study state is unspecified.
      ACTIVE: The study is active.
      INACTIVE: The study is stopped due to an internal error.
      COMPLETED: The study is done when the service exhausts the parameter
        search space or max_trial_count is reached.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    INACTIVE = 2
    COMPLETED = 3

  createTime = _messages.StringField(1)
  inactiveReason = _messages.StringField(2)
  name = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)
  studyConfig = _messages.MessageField('GoogleCloudMlV1StudyConfig', 5)


class GoogleCloudMlV1StudyConfig(_messages.Message):
  r"""Represents configuration of a study.

  Enums:
    AlgorithmValueValuesEnum: The search algorithm specified for the study.

  Fields:
    algorithm: The search algorithm specified for the study.
    automatedStoppingConfig: Configuration for automated stopping of
      unpromising Trials.
    metrics: Metric specs for the study.
    parameters: Required. The set of parameters to tune.
  """

  class AlgorithmValueValuesEnum(_messages.Enum):
    r"""The search algorithm specified for the study.

    Values:
      ALGORITHM_UNSPECIFIED: The default algorithm used by the Cloud AI
        Platform Vizier service.
      GAUSSIAN_PROCESS_BANDIT: Gaussian Process Bandit.
      GRID_SEARCH: Simple grid search within the feasible space. To use grid
        search, all parameters must be `INTEGER`, `CATEGORICAL`, or
        `DISCRETE`.
      RANDOM_SEARCH: Simple random search within the feasible space.
    """
    ALGORITHM_UNSPECIFIED = 0
    GAUSSIAN_PROCESS_BANDIT = 1
    GRID_SEARCH = 2
    RANDOM_SEARCH = 3

  algorithm = _messages.EnumField('AlgorithmValueValuesEnum', 1)
  automatedStoppingConfig = _messages.MessageField('GoogleCloudMlV1AutomatedStoppingConfig', 2)
  metrics = _messages.MessageField('GoogleCloudMlV1StudyConfigMetricSpec', 3, repeated=True)
  parameters = _messages.MessageField('GoogleCloudMlV1StudyConfigParameterSpec', 4, repeated=True)


class GoogleCloudMlV1StudyConfigMetricSpec(_messages.Message):
  r"""Represents a metric to optimize.

  Enums:
    GoalValueValuesEnum: Required. The optimization goal of the metric.

  Fields:
    goal: Required. The optimization goal of the metric.
    metric: Required. The name of the metric.
  """

  class GoalValueValuesEnum(_messages.Enum):
    r"""Required. The optimization goal of the metric.

    Values:
      GOAL_TYPE_UNSPECIFIED: Goal Type will default to maximize.
      MAXIMIZE: Maximize the goal metric.
      MINIMIZE: Minimize the goal metric.
    """
    GOAL_TYPE_UNSPECIFIED = 0
    MAXIMIZE = 1
    MINIMIZE = 2

  goal = _messages.EnumField('GoalValueValuesEnum', 1)
  metric = _messages.StringField(2)


class GoogleCloudMlV1StudyConfigParameterSpec(_messages.Message):
  r"""Represents a single parameter to optimize.

  Enums:
    ScaleTypeValueValuesEnum: How the parameter should be scaled. Leave unset
      for categorical parameters.
    TypeValueValuesEnum: Required. The type of the parameter.

  Fields:
    categoricalValueSpec: The value spec for a 'CATEGORICAL' parameter.
    childParameterSpecs: A child node is active if the parameter's value
      matches the child node's matching_parent_values. If two items in
      child_parameter_specs have the same name, they must have disjoint
      matching_parent_values.
    discreteValueSpec: The value spec for a 'DISCRETE' parameter.
    doubleValueSpec: The value spec for a 'DOUBLE' parameter.
    integerValueSpec: The value spec for an 'INTEGER' parameter.
    parameter: Required. The parameter name must be unique amongst all
      ParameterSpecs.
    parentCategoricalValues: A
      GoogleCloudMlV1StudyConfigParameterSpecMatchingParentCategoricalValueSpe
      c attribute.
    parentDiscreteValues: A
      GoogleCloudMlV1StudyConfigParameterSpecMatchingParentDiscreteValueSpec
      attribute.
    parentIntValues: A
      GoogleCloudMlV1StudyConfigParameterSpecMatchingParentIntValueSpec
      attribute.
    scaleType: How the parameter should be scaled. Leave unset for categorical
      parameters.
    type: Required. The type of the parameter.
  """

  class ScaleTypeValueValuesEnum(_messages.Enum):
    r"""How the parameter should be scaled. Leave unset for categorical
    parameters.

    Values:
      SCALE_TYPE_UNSPECIFIED: By default, no scaling is applied.
      UNIT_LINEAR_SCALE: Scales the feasible space to (0, 1) linearly.
      UNIT_LOG_SCALE: Scales the feasible space logarithmically to (0, 1). The
        entire feasible space must be strictly positive.
      UNIT_REVERSE_LOG_SCALE: Scales the feasible space "reverse"
        logarithmically to (0, 1). The result is that values close to the top
        of the feasible space are spread out more than points near the bottom.
        The entire feasible space must be strictly positive.
    """
    SCALE_TYPE_UNSPECIFIED = 0
    UNIT_LINEAR_SCALE = 1
    UNIT_LOG_SCALE = 2
    UNIT_REVERSE_LOG_SCALE = 3

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of the parameter.

    Values:
      PARAMETER_TYPE_UNSPECIFIED: You must specify a valid type. Using this
        unspecified type will result in an error.
      DOUBLE: Type for real-valued parameters.
      INTEGER: Type for integral parameters.
      CATEGORICAL: The parameter is categorical, with a value chosen from the
        categories field.
      DISCRETE: The parameter is real valued, with a fixed set of feasible
        points. If `type==DISCRETE`, feasible_points must be provided, and
        {`min_value`, `max_value`} will be ignored.
    """
    PARAMETER_TYPE_UNSPECIFIED = 0
    DOUBLE = 1
    INTEGER = 2
    CATEGORICAL = 3
    DISCRETE = 4

  categoricalValueSpec = _messages.MessageField('GoogleCloudMlV1StudyConfigParameterSpecCategoricalValueSpec', 1)
  childParameterSpecs = _messages.MessageField('GoogleCloudMlV1StudyConfigParameterSpec', 2, repeated=True)
  discreteValueSpec = _messages.MessageField('GoogleCloudMlV1StudyConfigParameterSpecDiscreteValueSpec', 3)
  doubleValueSpec = _messages.MessageField('GoogleCloudMlV1StudyConfigParameterSpecDoubleValueSpec', 4)
  integerValueSpec = _messages.MessageField('GoogleCloudMlV1StudyConfigParameterSpecIntegerValueSpec', 5)
  parameter = _messages.StringField(6)
  parentCategoricalValues = _messages.MessageField('GoogleCloudMlV1StudyConfigParameterSpecMatchingParentCategoricalValueSpec', 7)
  parentDiscreteValues = _messages.MessageField('GoogleCloudMlV1StudyConfigParameterSpecMatchingParentDiscreteValueSpec', 8)
  parentIntValues = _messages.MessageField('GoogleCloudMlV1StudyConfigParameterSpecMatchingParentIntValueSpec', 9)
  scaleType = _messages.EnumField('ScaleTypeValueValuesEnum', 10)
  type = _messages.EnumField('TypeValueValuesEnum', 11)


class GoogleCloudMlV1StudyConfigParameterSpecCategoricalValueSpec(_messages.Message):
  r"""A GoogleCloudMlV1StudyConfigParameterSpecCategoricalValueSpec object.

  Fields:
    values: Must be specified if type is `CATEGORICAL`. The list of possible
      categories.
  """

  values = _messages.StringField(1, repeated=True)


class GoogleCloudMlV1StudyConfigParameterSpecDiscreteValueSpec(_messages.Message):
  r"""A GoogleCloudMlV1StudyConfigParameterSpecDiscreteValueSpec object.

  Fields:
    values: Must be specified if type is `DISCRETE`. A list of feasible
      points. The list should be in strictly increasing order. For instance,
      this parameter might have possible settings of 1.5, 2.5, and 4.0. This
      list should not contain more than 1,000 values.
  """

  values = _messages.FloatField(1, repeated=True)


class GoogleCloudMlV1StudyConfigParameterSpecDoubleValueSpec(_messages.Message):
  r"""A GoogleCloudMlV1StudyConfigParameterSpecDoubleValueSpec object.

  Fields:
    maxValue: Must be specified if type is `DOUBLE`. Maximum value of the
      parameter.
    minValue: Must be specified if type is `DOUBLE`. Minimum value of the
      parameter.
  """

  maxValue = _messages.FloatField(1)
  minValue = _messages.FloatField(2)


class GoogleCloudMlV1StudyConfigParameterSpecIntegerValueSpec(_messages.Message):
  r"""A GoogleCloudMlV1StudyConfigParameterSpecIntegerValueSpec object.

  Fields:
    maxValue: Must be specified if type is `INTEGER`. Maximum value of the
      parameter.
    minValue: Must be specified if type is `INTEGER`. Minimum value of the
      parameter.
  """

  maxValue = _messages.IntegerField(1)
  minValue = _messages.IntegerField(2)


class GoogleCloudMlV1StudyConfigParameterSpecMatchingParentCategoricalValueSpec(_messages.Message):
  r"""Represents the spec to match categorical values from parent parameter.

  Fields:
    values: Matches values of the parent parameter with type 'CATEGORICAL'.
      All values must exist in `categorical_value_spec` of parent parameter.
  """

  values = _messages.StringField(1, repeated=True)


class GoogleCloudMlV1StudyConfigParameterSpecMatchingParentDiscreteValueSpec(_messages.Message):
  r"""Represents the spec to match discrete values from parent parameter.

  Fields:
    values: Matches values of the parent parameter with type 'DISCRETE'. All
      values must exist in `discrete_value_spec` of parent parameter.
  """

  values = _messages.FloatField(1, repeated=True)


class GoogleCloudMlV1StudyConfigParameterSpecMatchingParentIntValueSpec(_messages.Message):
  r"""Represents the spec to match integer values from parent parameter.

  Fields:
    values: Matches values of the parent parameter with type 'INTEGER'. All
      values must lie in `integer_value_spec` of parent parameter.
  """

  values = _messages.IntegerField(1, repeated=True)


class GoogleCloudMlV1SuggestTrialsMetadata(_messages.Message):
  r"""Metadata field of a google.longrunning.Operation associated with a
  SuggestTrialsRequest.

  Fields:
    clientId: The identifier of the client that is requesting the suggestion.
    createTime: The time operation was submitted.
    study: The name of the study that the trial belongs to.
    suggestionCount: The number of suggestions requested.
  """

  clientId = _messages.StringField(1)
  createTime = _messages.StringField(2)
  study = _messages.StringField(3)
  suggestionCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleCloudMlV1SuggestTrialsRequest(_messages.Message):
  r"""The request message for the SuggestTrial service method.

  Fields:
    clientId: Required. The identifier of the client that is requesting the
      suggestion. If multiple SuggestTrialsRequests have the same `client_id`,
      the service will return the identical suggested trial if the trial is
      pending, and provide a new trial if the last suggested trial was
      completed.
    suggestionCount: Required. The number of suggestions requested.
  """

  clientId = _messages.StringField(1)
  suggestionCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleCloudMlV1SuggestTrialsResponse(_messages.Message):
  r"""This message will be placed in the response field of a completed
  google.longrunning.Operation associated with a SuggestTrials request.

  Enums:
    StudyStateValueValuesEnum: The state of the study.

  Fields:
    endTime: The time at which operation processing completed.
    startTime: The time at which the operation was started.
    studyState: The state of the study.
    trials: A list of trials.
  """

  class StudyStateValueValuesEnum(_messages.Enum):
    r"""The state of the study.

    Values:
      STATE_UNSPECIFIED: The study state is unspecified.
      ACTIVE: The study is active.
      INACTIVE: The study is stopped due to an internal error.
      COMPLETED: The study is done when the service exhausts the parameter
        search space or max_trial_count is reached.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    INACTIVE = 2
    COMPLETED = 3

  endTime = _messages.StringField(1)
  startTime = _messages.StringField(2)
  studyState = _messages.EnumField('StudyStateValueValuesEnum', 3)
  trials = _messages.MessageField('GoogleCloudMlV1Trial', 4, repeated=True)


class GoogleCloudMlV1TrainingInput(_messages.Message):
  r"""Represents input parameters for a training job. When using the gcloud
  command to submit your training job, you can specify the input parameters as
  command-line arguments and/or in a YAML configuration file referenced from
  the --config command-line argument. For details, see the guide to
  [submitting a training job](/ai-platform/training/docs/training-jobs).

  Enums:
    ScaleTierValueValuesEnum: Required. Specifies the machine types, the
      number of replicas for workers and parameter servers.

  Fields:
    args: Optional. Command-line arguments passed to the training application
      when it starts. If your job uses a custom container, then the arguments
      are passed to the container's `ENTRYPOINT` command.
    enableWebAccess: Optional. Whether you want AI Platform Training to enable
      [interactive shell access](https://cloud.google.com/ai-
      platform/training/docs/monitor-debug-interactive-shell) to training
      containers. If set to `true`, you can access interactive shells at the
      URIs given by TrainingOutput.web_access_uris or
      HyperparameterOutput.web_access_uris (within TrainingOutput.trials).
    encryptionConfig: Optional. Options for using customer-managed encryption
      keys (CMEK) to protect resources created by a training job, instead of
      using Google's default encryption. If this is set, then all resources
      created by the training job will be encrypted with the customer-managed
      encryption key that you specify. [Learn how and when to use CMEK with AI
      Platform Training](/ai-platform/training/docs/cmek).
    evaluatorConfig: Optional. The configuration for evaluators. You should
      only set `evaluatorConfig.acceleratorConfig` if `evaluatorType` is set
      to a Compute Engine machine type. [Learn about restrictions on
      accelerator configurations for training.](/ai-
      platform/training/docs/using-gpus#compute-engine-machine-types-with-gpu)
      Set `evaluatorConfig.imageUri` only if you build a custom image for your
      evaluator. If `evaluatorConfig.imageUri` has not been set, AI Platform
      uses the value of `masterConfig.imageUri`. Learn more about [configuring
      custom containers](/ai-platform/training/docs/distributed-training-
      containers).
    evaluatorCount: Optional. The number of evaluator replicas to use for the
      training job. Each replica in the cluster will be of the type specified
      in `evaluator_type`. This value can only be used when `scale_tier` is
      set to `CUSTOM`. If you set this value, you must also set
      `evaluator_type`. The default value is zero.
    evaluatorType: Optional. Specifies the type of virtual machine to use for
      your training job's evaluator nodes. The supported values are the same
      as those described in the entry for `masterType`. This value must be
      consistent with the category of machine type that `masterType` uses. In
      other words, both must be Compute Engine machine types or both must be
      legacy machine types. This value must be present when `scaleTier` is set
      to `CUSTOM` and `evaluatorCount` is greater than zero.
    hyperparameters: Optional. The set of Hyperparameters to tune.
    jobDir: Optional. A Google Cloud Storage path in which to store training
      outputs and other data needed for training. This path is passed to your
      TensorFlow program as the '--job-dir' command-line argument. The benefit
      of specifying this field is that Cloud ML validates the path for use in
      training.
    masterConfig: Optional. The configuration for your master worker. You
      should only set `masterConfig.acceleratorConfig` if `masterType` is set
      to a Compute Engine machine type. Learn about [restrictions on
      accelerator configurations for training.](/ai-
      platform/training/docs/using-gpus#compute-engine-machine-types-with-gpu)
      Set `masterConfig.imageUri` only if you build a custom image. Only one
      of `masterConfig.imageUri` and `runtimeVersion` should be set. Learn
      more about [configuring custom containers](/ai-
      platform/training/docs/distributed-training-containers).
    masterType: Optional. Specifies the type of virtual machine to use for
      your training job's master worker. You must specify this field when
      `scaleTier` is set to `CUSTOM`. You can use certain Compute Engine
      machine types directly in this field. See the [list of compatible
      Compute Engine machine types](/ai-platform/training/docs/machine-
      types#compute-engine-machine-types). Alternatively, you can use the
      certain legacy machine types in this field. See the [list of legacy
      machine types](/ai-platform/training/docs/machine-types#legacy-machine-
      types). Finally, if you want to use a TPU for training, specify
      `cloud_tpu` in this field. Learn more about the [special configuration
      options for training with TPUs](/ai-platform/training/docs/using-
      tpus#configuring_a_custom_tpu_machine).
    nasJobSpec: Optional. The spec of a Neural Architecture Search (NAS) job.
    network: Optional. The full name of the [Compute Engine
      network](/vpc/docs/vpc) to which the Job is peered. For example,
      `projects/12345/global/networks/myVPC`. The format of this field is
      `projects/{project}/global/networks/{network}`, where {project} is a
      project number (like `12345`) and {network} is network name. Private
      services access must already be configured for the network. If left
      unspecified, the Job is not peered with any network. [Learn about using
      VPC Network Peering.](/ai-platform/training/docs/vpc-peering).
    packageUris: Required. The Google Cloud Storage location of the packages
      with the training program and any additional dependencies. The maximum
      number of package URIs is 100.
    parameterServerConfig: Optional. The configuration for parameter servers.
      You should only set `parameterServerConfig.acceleratorConfig` if
      `parameterServerType` is set to a Compute Engine machine type. [Learn
      about restrictions on accelerator configurations for training.](/ai-
      platform/training/docs/using-gpus#compute-engine-machine-types-with-gpu)
      Set `parameterServerConfig.imageUri` only if you build a custom image
      for your parameter server. If `parameterServerConfig.imageUri` has not
      been set, AI Platform uses the value of `masterConfig.imageUri`. Learn
      more about [configuring custom containers](/ai-
      platform/training/docs/distributed-training-containers).
    parameterServerCount: Optional. The number of parameter server replicas to
      use for the training job. Each replica in the cluster will be of the
      type specified in `parameter_server_type`. This value can only be used
      when `scale_tier` is set to `CUSTOM`. If you set this value, you must
      also set `parameter_server_type`. The default value is zero.
    parameterServerType: Optional. Specifies the type of virtual machine to
      use for your training job's parameter server. The supported values are
      the same as those described in the entry for `master_type`. This value
      must be consistent with the category of machine type that `masterType`
      uses. In other words, both must be Compute Engine machine types or both
      must be legacy machine types. This value must be present when
      `scaleTier` is set to `CUSTOM` and `parameter_server_count` is greater
      than zero.
    pythonModule: Required. The Python module name to run after installing the
      packages.
    pythonVersion: Optional. The version of Python used in training. You must
      either specify this field or specify `masterConfig.imageUri`. The
      following Python versions are available: * Python '3.7' is available
      when `runtime_version` is set to '1.15' or later. * Python '3.5' is
      available when `runtime_version` is set to a version from '1.4' to
      '1.14'. * Python '2.7' is available when `runtime_version` is set to
      '1.15' or earlier. Read more about the Python versions available for
      [each runtime version](/ml-engine/docs/runtime-version-list).
    region: Required. The region to run the training job in. See the
      [available regions](/ai-platform/training/docs/regions) for AI Platform
      Training.
    runtimeVersion: Optional. The AI Platform runtime version to use for
      training. You must either specify this field or specify
      `masterConfig.imageUri`. For more information, see the [runtime version
      list](/ai-platform/training/docs/runtime-version-list) and learn [how to
      manage runtime versions](/ai-platform/training/docs/versioning).
    scaleTier: Required. Specifies the machine types, the number of replicas
      for workers and parameter servers.
    scheduling: Optional. Scheduling options for a training job.
    serviceAccount: Optional. The email address of a service account to use
      when running the training appplication. You must have the
      `iam.serviceAccounts.actAs` permission for the specified service
      account. In addition, the AI Platform Training Google-managed service
      account must have the `roles/iam.serviceAccountAdmin` role for the
      specified service account. [Learn more about configuring a service
      account.](/ai-platform/training/docs/custom-service-account) If not
      specified, the AI Platform Training Google-managed service account is
      used by default.
    useChiefInTfConfig: Optional. Use `chief` instead of `master` in the
      `TF_CONFIG` environment variable when training with a custom container.
      Defaults to `false`. [Learn more about this field.](/ai-
      platform/training/docs/distributed-training-details#chief-versus-master)
      This field has no effect for training jobs that don't use a custom
      container.
    workerConfig: Optional. The configuration for workers. You should only set
      `workerConfig.acceleratorConfig` if `workerType` is set to a Compute
      Engine machine type. [Learn about restrictions on accelerator
      configurations for training.](/ai-platform/training/docs/using-
      gpus#compute-engine-machine-types-with-gpu) Set `workerConfig.imageUri`
      only if you build a custom image for your worker. If
      `workerConfig.imageUri` has not been set, AI Platform uses the value of
      `masterConfig.imageUri`. Learn more about [configuring custom
      containers](/ai-platform/training/docs/distributed-training-containers).
    workerCount: Optional. The number of worker replicas to use for the
      training job. Each replica in the cluster will be of the type specified
      in `worker_type`. This value can only be used when `scale_tier` is set
      to `CUSTOM`. If you set this value, you must also set `worker_type`. The
      default value is zero.
    workerType: Optional. Specifies the type of virtual machine to use for
      your training job's worker nodes. The supported values are the same as
      those described in the entry for `masterType`. This value must be
      consistent with the category of machine type that `masterType` uses. In
      other words, both must be Compute Engine machine types or both must be
      legacy machine types. If you use `cloud_tpu` for this value, see special
      instructions for [configuring a custom TPU machine](/ml-
      engine/docs/tensorflow/using-tpus#configuring_a_custom_tpu_machine).
      This value must be present when `scaleTier` is set to `CUSTOM` and
      `workerCount` is greater than zero.
  """

  class ScaleTierValueValuesEnum(_messages.Enum):
    r"""Required. Specifies the machine types, the number of replicas for
    workers and parameter servers.

    Values:
      BASIC: A single worker instance. This tier is suitable for learning how
        to use Cloud ML, and for experimenting with new models using small
        datasets.
      STANDARD_1: Many workers and a few parameter servers.
      PREMIUM_1: A large number of workers with many parameter servers.
      BASIC_GPU: A single worker instance [with a GPU](/ai-
        platform/training/docs/using-gpus).
      BASIC_TPU: A single worker instance with a [Cloud TPU](/ml-
        engine/docs/tensorflow/using-tpus).
      CUSTOM: The CUSTOM tier is not a set tier, but rather enables you to use
        your own cluster specification. When you use this tier, set values to
        configure your processing cluster according to these guidelines: * You
        _must_ set `TrainingInput.masterType` to specify the type of machine
        to use for your master node. This is the only required setting. * You
        _may_ set `TrainingInput.workerCount` to specify the number of workers
        to use. If you specify one or more workers, you _must_ also set
        `TrainingInput.workerType` to specify the type of machine to use for
        your worker nodes. * You _may_ set
        `TrainingInput.parameterServerCount` to specify the number of
        parameter servers to use. If you specify one or more parameter
        servers, you _must_ also set `TrainingInput.parameterServerType` to
        specify the type of machine to use for your parameter servers. Note
        that all of your workers must use the same machine type, which can be
        different from your parameter server type and master type. Your
        parameter servers must likewise use the same machine type, which can
        be different from your worker type and master type.
    """
    BASIC = 0
    STANDARD_1 = 1
    PREMIUM_1 = 2
    BASIC_GPU = 3
    BASIC_TPU = 4
    CUSTOM = 5

  args = _messages.StringField(1, repeated=True)
  enableWebAccess = _messages.BooleanField(2)
  encryptionConfig = _messages.MessageField('GoogleCloudMlV1EncryptionConfig', 3)
  evaluatorConfig = _messages.MessageField('GoogleCloudMlV1ReplicaConfig', 4)
  evaluatorCount = _messages.IntegerField(5)
  evaluatorType = _messages.StringField(6)
  hyperparameters = _messages.MessageField('GoogleCloudMlV1HyperparameterSpec', 7)
  jobDir = _messages.StringField(8)
  masterConfig = _messages.MessageField('GoogleCloudMlV1ReplicaConfig', 9)
  masterType = _messages.StringField(10)
  nasJobSpec = _messages.MessageField('GoogleCloudMlV1NasSpec', 11)
  network = _messages.StringField(12)
  packageUris = _messages.StringField(13, repeated=True)
  parameterServerConfig = _messages.MessageField('GoogleCloudMlV1ReplicaConfig', 14)
  parameterServerCount = _messages.IntegerField(15)
  parameterServerType = _messages.StringField(16)
  pythonModule = _messages.StringField(17)
  pythonVersion = _messages.StringField(18)
  region = _messages.StringField(19)
  runtimeVersion = _messages.StringField(20)
  scaleTier = _messages.EnumField('ScaleTierValueValuesEnum', 21)
  scheduling = _messages.MessageField('GoogleCloudMlV1Scheduling', 22)
  serviceAccount = _messages.StringField(23)
  useChiefInTfConfig = _messages.BooleanField(24)
  workerConfig = _messages.MessageField('GoogleCloudMlV1ReplicaConfig', 25)
  workerCount = _messages.IntegerField(26)
  workerType = _messages.StringField(27)


class GoogleCloudMlV1TrainingOutput(_messages.Message):
  r"""Represents results of a training job. Output only.

  Messages:
    WebAccessUrisValue: Output only. URIs for accessing [interactive
      shells](https://cloud.google.com/ai-platform/training/docs/monitor-
      debug-interactive-shell) (one URI for each training node). Only
      available if training_input.enable_web_access is `true`. The keys are
      names of each node in the training job; for example, `master-replica-0`
      for the master node, `worker-replica-0` for the first worker, and `ps-
      replica-0` for the first parameter server. The values are the URIs for
      each node's interactive shell.

  Fields:
    builtInAlgorithmOutput: Details related to built-in algorithms jobs. Only
      set for built-in algorithms jobs.
    completedTrialCount: The number of hyperparameter tuning trials that
      completed successfully. Only set for hyperparameter tuning jobs.
    consumedMLUnits: The amount of ML units consumed by the job.
    hyperparameterMetricTag: The TensorFlow summary tag name used for
      optimizing hyperparameter tuning trials. See [`HyperparameterSpec.hyperp
      arameterMetricTag`](#HyperparameterSpec.FIELDS.hyperparameter_metric_tag
      ) for more information. Only set for hyperparameter tuning jobs.
    isBuiltInAlgorithmJob: Whether this job is a built-in Algorithm job.
    isHyperparameterTuningJob: Whether this job is a hyperparameter tuning
      job.
    nasJobOutput: The output of a Neural Architecture Search (NAS) job.
    trials: Results for individual Hyperparameter trials. Only set for
      hyperparameter tuning jobs.
    webAccessUris: Output only. URIs for accessing [interactive
      shells](https://cloud.google.com/ai-platform/training/docs/monitor-
      debug-interactive-shell) (one URI for each training node). Only
      available if training_input.enable_web_access is `true`. The keys are
      names of each node in the training job; for example, `master-replica-0`
      for the master node, `worker-replica-0` for the first worker, and `ps-
      replica-0` for the first parameter server. The values are the URIs for
      each node's interactive shell.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class WebAccessUrisValue(_messages.Message):
    r"""Output only. URIs for accessing [interactive
    shells](https://cloud.google.com/ai-platform/training/docs/monitor-debug-
    interactive-shell) (one URI for each training node). Only available if
    training_input.enable_web_access is `true`. The keys are names of each
    node in the training job; for example, `master-replica-0` for the master
    node, `worker-replica-0` for the first worker, and `ps-replica-0` for the
    first parameter server. The values are the URIs for each node's
    interactive shell.

    Messages:
      AdditionalProperty: An additional property for a WebAccessUrisValue
        object.

    Fields:
      additionalProperties: Additional properties of type WebAccessUrisValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a WebAccessUrisValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  builtInAlgorithmOutput = _messages.MessageField('GoogleCloudMlV1BuiltInAlgorithmOutput', 1)
  completedTrialCount = _messages.IntegerField(2)
  consumedMLUnits = _messages.FloatField(3)
  hyperparameterMetricTag = _messages.StringField(4)
  isBuiltInAlgorithmJob = _messages.BooleanField(5)
  isHyperparameterTuningJob = _messages.BooleanField(6)
  nasJobOutput = _messages.MessageField('GoogleCloudMlV1NasJobOutput', 7)
  trials = _messages.MessageField('GoogleCloudMlV1HyperparameterOutput', 8, repeated=True)
  webAccessUris = _messages.MessageField('WebAccessUrisValue', 9)


class GoogleCloudMlV1TreeShapAttribution(_messages.Message):
  r"""Attributes credit by computing the Shapley value taking advantage of the
  model's tree ensemble structure. Refer to this paper for more details:
  https://arxiv.org/abs/1705.07874 This attribution method is supported for
  XGBoost models.
  """



class GoogleCloudMlV1Trial(_messages.Message):
  r"""A message representing a trial.

  Enums:
    StateValueValuesEnum: The detailed state of a trial.

  Fields:
    clientId: Output only. The identifier of the client that originally
      requested this trial.
    endTime: Output only. Time at which the trial's status changed to
      COMPLETED.
    finalMeasurement: The final measurement containing the objective value.
    infeasibleReason: Output only. A human readable string describing why the
      trial is infeasible. This should only be set if trial_infeasible is
      true.
    measurements: A list of measurements that are strictly lexicographically
      ordered by their induced tuples (steps, elapsed_time). These are used
      for early stopping computations.
    name: Output only. Name of the trial assigned by the service.
    parameters: The parameters of the trial.
    startTime: Output only. Time at which the trial was started.
    state: The detailed state of a trial.
    trialInfeasible: Output only. If true, the parameters in this trial are
      not attempted again.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The detailed state of a trial.

    Values:
      STATE_UNSPECIFIED: The trial state is unspecified.
      REQUESTED: Indicates that a specific trial has been requested, but it
        has not yet been suggested by the service.
      ACTIVE: Indicates that the trial has been suggested.
      COMPLETED: Indicates that the trial is done, and either has a
        final_measurement set, or is marked as trial_infeasible.
      STOPPING: Indicates that the trial should stop according to the service.
    """
    STATE_UNSPECIFIED = 0
    REQUESTED = 1
    ACTIVE = 2
    COMPLETED = 3
    STOPPING = 4

  clientId = _messages.StringField(1)
  endTime = _messages.StringField(2)
  finalMeasurement = _messages.MessageField('GoogleCloudMlV1Measurement', 3)
  infeasibleReason = _messages.StringField(4)
  measurements = _messages.MessageField('GoogleCloudMlV1Measurement', 5, repeated=True)
  name = _messages.StringField(6)
  parameters = _messages.MessageField('GoogleCloudMlV1TrialParameter', 7, repeated=True)
  startTime = _messages.StringField(8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  trialInfeasible = _messages.BooleanField(10)


class GoogleCloudMlV1TrialParameter(_messages.Message):
  r"""A message representing a parameter to be tuned. Contains the name of the
  parameter and the suggested value to use for this trial.

  Fields:
    floatValue: Must be set if ParameterType is DOUBLE or DISCRETE.
    intValue: Must be set if ParameterType is INTEGER
    parameter: The name of the parameter.
    stringValue: Must be set if ParameterTypeis CATEGORICAL
  """

  floatValue = _messages.FloatField(1)
  intValue = _messages.IntegerField(2)
  parameter = _messages.StringField(3)
  stringValue = _messages.StringField(4)


class GoogleCloudMlV1Version(_messages.Message):
  r"""Represents a version of the model. Each version is a trained model
  deployed in the cloud, ready to handle prediction requests. A model can have
  multiple versions. You can get information about all of the versions of a
  given model by calling projects.models.versions.list.

  Enums:
    FrameworkValueValuesEnum: Optional. The machine learning framework AI
      Platform uses to train this version of the model. Valid values are
      `TENSORFLOW`, `SCIKIT_LEARN`, `XGBOOST`. If you do not specify a
      framework, AI Platform will analyze files in the deployment_uri to
      determine a framework. If you choose `SCIKIT_LEARN` or `XGBOOST`, you
      must also set the runtime version of the model to 1.4 or greater. Do
      **not** specify a framework if you're deploying a [custom prediction
      routine](/ai-platform/prediction/docs/custom-prediction-routines) or if
      you're using a [custom container](/ai-platform/prediction/docs/use-
      custom-container).
    StateValueValuesEnum: Output only. The state of a version.

  Messages:
    LabelsValue: Optional. One or more labels that you can add, to organize
      your model versions. Each label is a key-value pair, where both the key
      and the value are arbitrary strings that you supply. For more
      information, see the documentation on using labels. Note that this field
      is not updatable for mls1* models.

  Fields:
    acceleratorConfig: Optional. Accelerator config for using GPUs for online
      prediction (beta). Only specify this field if you have specified a
      Compute Engine (N1) machine type in the `machineType` field. Learn more
      about [using GPUs for online prediction](/ml-engine/docs/machine-types-
      online-prediction#gpus).
    autoScaling: Automatically scale the number of nodes used to serve the
      model in response to increases and decreases in traffic. Care should be
      taken to ramp up traffic according to the model's ability to scale or
      you will start seeing increases in latency and 429 response codes.
    container: Optional. Specifies a custom container to use for serving
      predictions. If you specify this field, then `machineType` is required.
      If you specify this field, then `deploymentUri` is optional. If you
      specify this field, then you must not specify `runtimeVersion`,
      `packageUris`, `framework`, `pythonVersion`, or `predictionClass`.
    createTime: Output only. The time the version was created.
    deploymentUri: The Cloud Storage URI of a directory containing trained
      model artifacts to be used to create the model version. See the [guide
      to deploying models](/ai-platform/prediction/docs/deploying-models) for
      more information. The total number of files under this directory must
      not exceed 1000. During projects.models.versions.create, AI Platform
      Prediction copies all files from the specified directory to a location
      managed by the service. From then on, AI Platform Prediction uses these
      copies of the model artifacts to serve predictions, not the original
      files in Cloud Storage, so this location is useful only as a historical
      record. If you specify container, then this field is optional.
      Otherwise, it is required. Learn [how to use this field with a custom
      container](/ai-platform/prediction/docs/custom-container-
      requirements#artifacts).
    description: Optional. The description specified for the version when it
      was created.
    errorMessage: Output only. The details of a failure or a cancellation.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a model from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform model updates in order to avoid race
      conditions: An `etag` is returned in the response to `GetVersion`, and
      systems are expected to put that etag in the request to `UpdateVersion`
      to ensure that their change will be applied to the model as intended.
    explanationConfig: Optional. Configures explainability features on the
      model's version. Some explanation features require additional metadata
      to be loaded as part of the model payload.
    framework: Optional. The machine learning framework AI Platform uses to
      train this version of the model. Valid values are `TENSORFLOW`,
      `SCIKIT_LEARN`, `XGBOOST`. If you do not specify a framework, AI
      Platform will analyze files in the deployment_uri to determine a
      framework. If you choose `SCIKIT_LEARN` or `XGBOOST`, you must also set
      the runtime version of the model to 1.4 or greater. Do **not** specify a
      framework if you're deploying a [custom prediction routine](/ai-
      platform/prediction/docs/custom-prediction-routines) or if you're using
      a [custom container](/ai-platform/prediction/docs/use-custom-container).
    imageUri: Optional. The docker image to run for custom serving container.
      This image must be in Google Container Registry.
    isDefault: Output only. If true, this version will be used to handle
      prediction requests that do not specify a version. You can change the
      default version by calling projects.methods.versions.setDefault.
    labels: Optional. One or more labels that you can add, to organize your
      model versions. Each label is a key-value pair, where both the key and
      the value are arbitrary strings that you supply. For more information,
      see the documentation on using labels. Note that this field is not
      updatable for mls1* models.
    lastMigrationModelId: Output only. The [AI Platform (Unified)
      `Model`](https://cloud.google.com/ai-platform-
      unified/docs/reference/rest/v1beta1/projects.locations.models) ID for
      the last [model migration](https://cloud.google.com/ai-platform-
      unified/docs/start/migrating-to-ai-platform-unified).
    lastMigrationTime: Output only. The last time this version was
      successfully [migrated to AI Platform
      (Unified)](https://cloud.google.com/ai-platform-
      unified/docs/start/migrating-to-ai-platform-unified).
    lastUseTime: Output only. The time the version was last used for
      prediction.
    machineType: Optional. The type of machine on which to serve the model.
      Currently only applies to online prediction service. To learn about
      valid values for this field, read [Choosing a machine type for online
      prediction](/ai-platform/prediction/docs/machine-types-online-
      prediction). If this field is not specified and you are using a
      [regional endpoint](/ai-platform/prediction/docs/regional-endpoints),
      then the machine type defaults to `n1-standard-2`. If this field is not
      specified and you are using the global endpoint (`ml.googleapis.com`),
      then the machine type defaults to `mls1-c1-m2`.
    manualScaling: Manually select the number of nodes to use for serving the
      model. You should generally use `auto_scaling` with an appropriate
      `min_nodes` instead, but this option is available if you want more
      predictable billing. Beware that latency and error rates will increase
      if the traffic exceeds that capability of the system to serve it based
      on the selected number of nodes.
    modelClass: A string attribute.
    name: Required. The name specified for the version when it was created.
      The version name must be unique within the model it is created in.
    packageUris: Optional. Cloud Storage paths (`gs://...`) of packages for
      [custom prediction routines](/ml-engine/docs/tensorflow/custom-
      prediction-routines) or [scikit-learn pipelines with custom code](/ml-
      engine/docs/scikit/exporting-for-prediction#custom-pipeline-code). For a
      custom prediction routine, one of these packages must contain your
      Predictor class (see
      [`predictionClass`](#Version.FIELDS.prediction_class)). Additionally,
      include any dependencies used by your Predictor or scikit-learn pipeline
      uses that are not already included in your selected [runtime
      version](/ml-engine/docs/tensorflow/runtime-version-list). If you
      specify this field, you must also set
      [`runtimeVersion`](#Version.FIELDS.runtime_version) to 1.4 or greater.
    predictionClass: Optional. The fully qualified name
      (module_name.class_name) of a class that implements the Predictor
      interface described in this reference field. The module containing this
      class should be included in a package provided to the [`packageUris`
      field](#Version.FIELDS.package_uris). Specify this field if and only if
      you are deploying a [custom prediction routine (beta)](/ml-
      engine/docs/tensorflow/custom-prediction-routines). If you specify this
      field, you must set [`runtimeVersion`](#Version.FIELDS.runtime_version)
      to 1.4 or greater and you must set `machineType` to a [legacy (MLS1)
      machine type](/ml-engine/docs/machine-types-online-prediction). The
      following code sample provides the Predictor interface: class
      Predictor(object): " " "Interface for constructing custom predictors." "
      " def predict(self, instances, **kwargs): " " "Performs custom
      prediction. Instances are the decoded values from the request. They have
      already been deserialized from JSON. Args: instances: A list of
      prediction input instances. **kwargs: A dictionary of keyword args
      provided as additional fields on the predict request body. Returns: A
      list of outputs containing the prediction results. This list must be
      JSON serializable. " " " raise NotImplementedError() @classmethod def
      from_path(cls, model_dir): " " "Creates an instance of Predictor using
      the given path. Loading of the predictor should be done in this method.
      Args: model_dir: The local directory that contains the exported model
      file along with any additional files uploaded when creating the version
      resource. Returns: An instance implementing this Predictor class. " " "
      raise NotImplementedError() Learn more about [the Predictor interface
      and custom prediction routines](/ml-engine/docs/tensorflow/custom-
      prediction-routines).
    pythonVersion: Required. The version of Python used in prediction. The
      following Python versions are available: * Python '3.7' is available
      when `runtime_version` is set to '1.15' or later. * Python '3.5' is
      available when `runtime_version` is set to a version from '1.4' to
      '1.14'. * Python '2.7' is available when `runtime_version` is set to
      '1.15' or earlier. Read more about the Python versions available for
      [each runtime version](/ml-engine/docs/runtime-version-list).
    requestLoggingConfig: Optional. *Only* specify this field in a
      projects.models.versions.patch request. Specifying it in a
      projects.models.versions.create request has no effect. Configures the
      request-response pair logging on predictions from this Version.
    routes: Optional. Specifies paths on a custom container's HTTP server
      where AI Platform Prediction sends certain requests. If you specify this
      field, then you must also specify the `container` field. If you specify
      the `container` field and do not specify this field, it defaults to the
      following: ```json { "predict":
      "/v1/models/MODEL/versions/VERSION:predict", "health":
      "/v1/models/MODEL/versions/VERSION" } ``` See RouteMap for more details
      about these default values.
    runtimeVersion: Required. The AI Platform runtime version to use for this
      deployment. For more information, see the [runtime version list](/ml-
      engine/docs/runtime-version-list) and [how to manage runtime
      versions](/ml-engine/docs/versioning).
    serviceAccount: Optional. Specifies the service account for resource
      access control. If you specify this field, then you must also specify
      either the `containerSpec` or the `predictionClass` field. Learn more
      about [using a custom service account](/ai-
      platform/prediction/docs/custom-service-account).
    state: Output only. The state of a version.
  """

  class FrameworkValueValuesEnum(_messages.Enum):
    r"""Optional. The machine learning framework AI Platform uses to train
    this version of the model. Valid values are `TENSORFLOW`, `SCIKIT_LEARN`,
    `XGBOOST`. If you do not specify a framework, AI Platform will analyze
    files in the deployment_uri to determine a framework. If you choose
    `SCIKIT_LEARN` or `XGBOOST`, you must also set the runtime version of the
    model to 1.4 or greater. Do **not** specify a framework if you're
    deploying a [custom prediction routine](/ai-
    platform/prediction/docs/custom-prediction-routines) or if you're using a
    [custom container](/ai-platform/prediction/docs/use-custom-container).

    Values:
      FRAMEWORK_UNSPECIFIED: Unspecified framework. Assigns a value based on
        the file suffix.
      TENSORFLOW: Tensorflow framework.
      SCIKIT_LEARN: Scikit-learn framework.
      XGBOOST: XGBoost framework.
    """
    FRAMEWORK_UNSPECIFIED = 0
    TENSORFLOW = 1
    SCIKIT_LEARN = 2
    XGBOOST = 3

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of a version.

    Values:
      UNKNOWN: The version state is unspecified.
      READY: The version is ready for prediction.
      CREATING: The version is being created. New UpdateVersion and
        DeleteVersion requests will fail if a version is in the CREATING
        state.
      FAILED: The version failed to be created, possibly cancelled.
        `error_message` should contain the details of the failure.
      DELETING: The version is being deleted. New UpdateVersion and
        DeleteVersion requests will fail if a version is in the DELETING
        state.
      UPDATING: The version is being updated. New UpdateVersion and
        DeleteVersion requests will fail if a version is in the UPDATING
        state.
    """
    UNKNOWN = 0
    READY = 1
    CREATING = 2
    FAILED = 3
    DELETING = 4
    UPDATING = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. One or more labels that you can add, to organize your model
    versions. Each label is a key-value pair, where both the key and the value
    are arbitrary strings that you supply. For more information, see the
    documentation on using labels. Note that this field is not updatable for
    mls1* models.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  acceleratorConfig = _messages.MessageField('GoogleCloudMlV1AcceleratorConfig', 1)
  autoScaling = _messages.MessageField('GoogleCloudMlV1AutoScaling', 2)
  container = _messages.MessageField('GoogleCloudMlV1ContainerSpec', 3)
  createTime = _messages.StringField(4)
  deploymentUri = _messages.StringField(5)
  description = _messages.StringField(6)
  errorMessage = _messages.StringField(7)
  etag = _messages.BytesField(8)
  explanationConfig = _messages.MessageField('GoogleCloudMlV1ExplanationConfig', 9)
  framework = _messages.EnumField('FrameworkValueValuesEnum', 10)
  imageUri = _messages.StringField(11)
  isDefault = _messages.BooleanField(12)
  labels = _messages.MessageField('LabelsValue', 13)
  lastMigrationModelId = _messages.StringField(14)
  lastMigrationTime = _messages.StringField(15)
  lastUseTime = _messages.StringField(16)
  machineType = _messages.StringField(17)
  manualScaling = _messages.MessageField('GoogleCloudMlV1ManualScaling', 18)
  modelClass = _messages.StringField(19)
  name = _messages.StringField(20)
  packageUris = _messages.StringField(21, repeated=True)
  predictionClass = _messages.StringField(22)
  pythonVersion = _messages.StringField(23)
  requestLoggingConfig = _messages.MessageField('GoogleCloudMlV1RequestLoggingConfig', 24)
  routes = _messages.MessageField('GoogleCloudMlV1RouteMap', 25)
  runtimeVersion = _messages.StringField(26)
  serviceAccount = _messages.StringField(27)
  state = _messages.EnumField('StateValueValuesEnum', 28)


class GoogleCloudMlV1XraiAttribution(_messages.Message):
  r"""Attributes credit by computing the XRAI taking advantage of the model's
  fully differentiable structure. Refer to this paper for more details:
  https://arxiv.org/abs/1906.02825 Currently only implemented for models with
  natural image inputs.

  Fields:
    blurBaselineConfig: Config for XRAI with blur baseline. When enabled, a
      linear path from the maximally blurred image to the input image is
      created. Using a blurred baseline instead of zero (black image) is
      motivated by the BlurIG approach explained here:
      https://arxiv.org/abs/2004.03383
    numIntegralSteps: Number of steps for approximating the path integral. A
      good value to start is 50 and gradually increase until the sum to diff
      property is met within the desired error range.
    smoothGradConfig: Config for SmoothGrad approximation of gradients. When
      enabled, the gradients are approximated by averaging the gradients from
      noisy samples in the vicinity of the inputs. Adding noise can help
      improve the computed gradients, see here for why:
      https://arxiv.org/pdf/1706.03825.pdf
  """

  blurBaselineConfig = _messages.MessageField('GoogleCloudMlV1BlurBaselineConfig', 1)
  numIntegralSteps = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  smoothGradConfig = _messages.MessageField('GoogleCloudMlV1SmoothGradConfig', 3)


class GoogleIamV1AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('GoogleIamV1AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class GoogleIamV1AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class GoogleIamV1Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('GoogleTypeExpr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class GoogleIamV1Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('GoogleIamV1AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('GoogleIamV1Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleIamV1SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('GoogleIamV1Policy', 1)
  updateMask = _messages.StringField(2)


class GoogleIamV1TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class GoogleIamV1TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class GoogleLongrunningListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('GoogleLongrunningOperation', 2, repeated=True)


class GoogleLongrunningOperation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('GoogleRpcStatus', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class GoogleProtobufEmpty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class GoogleRpcStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class GoogleTypeExpr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class MlProjectsExplainRequest(_messages.Message):
  r"""A MlProjectsExplainRequest object.

  Fields:
    googleCloudMlV1ExplainRequest: A GoogleCloudMlV1ExplainRequest resource to
      be passed as the request body.
    name: Required. The resource name of a model or a version. Authorization:
      requires the `predict` permission on the specified resource.
  """

  googleCloudMlV1ExplainRequest = _messages.MessageField('GoogleCloudMlV1ExplainRequest', 1)
  name = _messages.StringField(2, required=True)


class MlProjectsGetConfigRequest(_messages.Message):
  r"""A MlProjectsGetConfigRequest object.

  Fields:
    name: Required. The project name.
  """

  name = _messages.StringField(1, required=True)


class MlProjectsJobsCancelRequest(_messages.Message):
  r"""A MlProjectsJobsCancelRequest object.

  Fields:
    googleCloudMlV1CancelJobRequest: A GoogleCloudMlV1CancelJobRequest
      resource to be passed as the request body.
    name: Required. The name of the job to cancel.
  """

  googleCloudMlV1CancelJobRequest = _messages.MessageField('GoogleCloudMlV1CancelJobRequest', 1)
  name = _messages.StringField(2, required=True)


class MlProjectsJobsCreateRequest(_messages.Message):
  r"""A MlProjectsJobsCreateRequest object.

  Fields:
    googleCloudMlV1Job: A GoogleCloudMlV1Job resource to be passed as the
      request body.
    parent: Required. The project name.
  """

  googleCloudMlV1Job = _messages.MessageField('GoogleCloudMlV1Job', 1)
  parent = _messages.StringField(2, required=True)


class MlProjectsJobsGetIamPolicyRequest(_messages.Message):
  r"""A MlProjectsJobsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class MlProjectsJobsGetRequest(_messages.Message):
  r"""A MlProjectsJobsGetRequest object.

  Fields:
    name: Required. The name of the job to get the description of.
  """

  name = _messages.StringField(1, required=True)


class MlProjectsJobsListRequest(_messages.Message):
  r"""A MlProjectsJobsListRequest object.

  Fields:
    filter: Optional. Specifies the subset of jobs to retrieve. You can filter
      on the value of one or more attributes of the job object. For example,
      retrieve jobs with a job identifier that starts with 'census': gcloud
      ai-platform jobs list --filter='jobId:census*' List all failed jobs with
      names that start with 'rnn': gcloud ai-platform jobs list
      --filter='jobId:rnn* AND state:FAILED' For more examples, see the guide
      to monitoring jobs.
    pageSize: Optional. The number of jobs to retrieve per "page" of results.
      If there are more remaining results than this number, the response
      message will contain a valid value in the `next_page_token` field. The
      default value is 20, and the maximum page size is 100.
    pageToken: Optional. A page token to request the next page of results. You
      get the token from the `next_page_token` field of the response from the
      previous call.
    parent: Required. The name of the project for which to list jobs.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class MlProjectsJobsPatchRequest(_messages.Message):
  r"""A MlProjectsJobsPatchRequest object.

  Fields:
    googleCloudMlV1Job: A GoogleCloudMlV1Job resource to be passed as the
      request body.
    name: Required. The job name.
    updateMask: Required. Specifies the path, relative to `Job`, of the field
      to update. To adopt etag mechanism, include `etag` field in the mask,
      and include the `etag` value in your job resource. For example, to
      change the labels of a job, the `update_mask` parameter would be
      specified as `labels`, `etag`, and the `PATCH` request body would
      specify the new value, as follows: { "labels": { "owner": "Google",
      "color": "Blue" } "etag": "33a64df551425fcc55e4d42a148795d9f25f89d4" }
      If `etag` matches the one on the server, the labels of the job will be
      replaced with the given ones, and the server end `etag` will be
      recalculated. Currently the only supported update masks are `labels` and
      `etag`.
  """

  googleCloudMlV1Job = _messages.MessageField('GoogleCloudMlV1Job', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class MlProjectsJobsSetIamPolicyRequest(_messages.Message):
  r"""A MlProjectsJobsSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class MlProjectsJobsTestIamPermissionsRequest(_messages.Message):
  r"""A MlProjectsJobsTestIamPermissionsRequest object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class MlProjectsLocationsGetRequest(_messages.Message):
  r"""A MlProjectsLocationsGetRequest object.

  Fields:
    name: Required. The name of the location.
  """

  name = _messages.StringField(1, required=True)


class MlProjectsLocationsListRequest(_messages.Message):
  r"""A MlProjectsLocationsListRequest object.

  Fields:
    pageSize: Optional. The number of locations to retrieve per "page" of
      results. If there are more remaining results than this number, the
      response message will contain a valid value in the `next_page_token`
      field. The default value is 20, and the maximum page size is 100.
    pageToken: Optional. A page token to request the next page of results. You
      get the token from the `next_page_token` field of the response from the
      previous call.
    parent: Required. The name of the project for which available locations
      are to be listed (since some locations might be whitelisted for specific
      projects).
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class MlProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A MlProjectsLocationsOperationsCancelRequest object.

  Fields:
    name: The name of the operation resource to be cancelled.
  """

  name = _messages.StringField(1, required=True)


class MlProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A MlProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class MlProjectsLocationsStudiesCreateRequest(_messages.Message):
  r"""A MlProjectsLocationsStudiesCreateRequest object.

  Fields:
    googleCloudMlV1Study: A GoogleCloudMlV1Study resource to be passed as the
      request body.
    parent: Required. The project and location that the study belongs to.
      Format: projects/{project}/locations/{location}
    studyId: Required. The ID to use for the study, which will become the
      final component of the study's resource name.
  """

  googleCloudMlV1Study = _messages.MessageField('GoogleCloudMlV1Study', 1)
  parent = _messages.StringField(2, required=True)
  studyId = _messages.StringField(3)


class MlProjectsLocationsStudiesDeleteRequest(_messages.Message):
  r"""A MlProjectsLocationsStudiesDeleteRequest object.

  Fields:
    name: Required. The study name.
  """

  name = _messages.StringField(1, required=True)


class MlProjectsLocationsStudiesGetRequest(_messages.Message):
  r"""A MlProjectsLocationsStudiesGetRequest object.

  Fields:
    name: Required. The study name.
  """

  name = _messages.StringField(1, required=True)


class MlProjectsLocationsStudiesListRequest(_messages.Message):
  r"""A MlProjectsLocationsStudiesListRequest object.

  Fields:
    parent: Required. The project and location that the study belongs to.
      Format: projects/{project}/locations/{location}
  """

  parent = _messages.StringField(1, required=True)


class MlProjectsLocationsStudiesTrialsAddMeasurementRequest(_messages.Message):
  r"""A MlProjectsLocationsStudiesTrialsAddMeasurementRequest object.

  Fields:
    googleCloudMlV1AddTrialMeasurementRequest: A
      GoogleCloudMlV1AddTrialMeasurementRequest resource to be passed as the
      request body.
    name: Required. The trial name.
  """

  googleCloudMlV1AddTrialMeasurementRequest = _messages.MessageField('GoogleCloudMlV1AddTrialMeasurementRequest', 1)
  name = _messages.StringField(2, required=True)


class MlProjectsLocationsStudiesTrialsCheckEarlyStoppingStateRequest(_messages.Message):
  r"""A MlProjectsLocationsStudiesTrialsCheckEarlyStoppingStateRequest object.

  Fields:
    googleCloudMlV1CheckTrialEarlyStoppingStateRequest: A
      GoogleCloudMlV1CheckTrialEarlyStoppingStateRequest resource to be passed
      as the request body.
    name: Required. The trial name.
  """

  googleCloudMlV1CheckTrialEarlyStoppingStateRequest = _messages.MessageField('GoogleCloudMlV1CheckTrialEarlyStoppingStateRequest', 1)
  name = _messages.StringField(2, required=True)


class MlProjectsLocationsStudiesTrialsCompleteRequest(_messages.Message):
  r"""A MlProjectsLocationsStudiesTrialsCompleteRequest object.

  Fields:
    googleCloudMlV1CompleteTrialRequest: A GoogleCloudMlV1CompleteTrialRequest
      resource to be passed as the request body.
    name: Required. The trial name.metat
  """

  googleCloudMlV1CompleteTrialRequest = _messages.MessageField('GoogleCloudMlV1CompleteTrialRequest', 1)
  name = _messages.StringField(2, required=True)


class MlProjectsLocationsStudiesTrialsCreateRequest(_messages.Message):
  r"""A MlProjectsLocationsStudiesTrialsCreateRequest object.

  Fields:
    googleCloudMlV1Trial: A GoogleCloudMlV1Trial resource to be passed as the
      request body.
    parent: Required. The name of the study that the trial belongs to.
  """

  googleCloudMlV1Trial = _messages.MessageField('GoogleCloudMlV1Trial', 1)
  parent = _messages.StringField(2, required=True)


class MlProjectsLocationsStudiesTrialsDeleteRequest(_messages.Message):
  r"""A MlProjectsLocationsStudiesTrialsDeleteRequest object.

  Fields:
    name: Required. The trial name.
  """

  name = _messages.StringField(1, required=True)


class MlProjectsLocationsStudiesTrialsGetRequest(_messages.Message):
  r"""A MlProjectsLocationsStudiesTrialsGetRequest object.

  Fields:
    name: Required. The trial name.
  """

  name = _messages.StringField(1, required=True)


class MlProjectsLocationsStudiesTrialsListOptimalTrialsRequest(_messages.Message):
  r"""A MlProjectsLocationsStudiesTrialsListOptimalTrialsRequest object.

  Fields:
    googleCloudMlV1ListOptimalTrialsRequest: A
      GoogleCloudMlV1ListOptimalTrialsRequest resource to be passed as the
      request body.
    parent: Required. The name of the study that the pareto-optimal trial
      belongs to.
  """

  googleCloudMlV1ListOptimalTrialsRequest = _messages.MessageField('GoogleCloudMlV1ListOptimalTrialsRequest', 1)
  parent = _messages.StringField(2, required=True)


class MlProjectsLocationsStudiesTrialsListRequest(_messages.Message):
  r"""A MlProjectsLocationsStudiesTrialsListRequest object.

  Fields:
    parent: Required. The name of the study that the trial belongs to.
  """

  parent = _messages.StringField(1, required=True)


class MlProjectsLocationsStudiesTrialsStopRequest(_messages.Message):
  r"""A MlProjectsLocationsStudiesTrialsStopRequest object.

  Fields:
    googleCloudMlV1StopTrialRequest: A GoogleCloudMlV1StopTrialRequest
      resource to be passed as the request body.
    name: Required. The trial name.
  """

  googleCloudMlV1StopTrialRequest = _messages.MessageField('GoogleCloudMlV1StopTrialRequest', 1)
  name = _messages.StringField(2, required=True)


class MlProjectsLocationsStudiesTrialsSuggestRequest(_messages.Message):
  r"""A MlProjectsLocationsStudiesTrialsSuggestRequest object.

  Fields:
    googleCloudMlV1SuggestTrialsRequest: A GoogleCloudMlV1SuggestTrialsRequest
      resource to be passed as the request body.
    parent: Required. The name of the study that the trial belongs to.
  """

  googleCloudMlV1SuggestTrialsRequest = _messages.MessageField('GoogleCloudMlV1SuggestTrialsRequest', 1)
  parent = _messages.StringField(2, required=True)


class MlProjectsModelsCreateRequest(_messages.Message):
  r"""A MlProjectsModelsCreateRequest object.

  Fields:
    googleCloudMlV1Model: A GoogleCloudMlV1Model resource to be passed as the
      request body.
    parent: Required. The project name.
  """

  googleCloudMlV1Model = _messages.MessageField('GoogleCloudMlV1Model', 1)
  parent = _messages.StringField(2, required=True)


class MlProjectsModelsDeleteRequest(_messages.Message):
  r"""A MlProjectsModelsDeleteRequest object.

  Fields:
    name: Required. The name of the model.
  """

  name = _messages.StringField(1, required=True)


class MlProjectsModelsGetIamPolicyRequest(_messages.Message):
  r"""A MlProjectsModelsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class MlProjectsModelsGetRequest(_messages.Message):
  r"""A MlProjectsModelsGetRequest object.

  Fields:
    name: Required. The name of the model.
  """

  name = _messages.StringField(1, required=True)


class MlProjectsModelsListRequest(_messages.Message):
  r"""A MlProjectsModelsListRequest object.

  Fields:
    filter: Optional. Specifies the subset of models to retrieve.
    pageSize: Optional. The number of models to retrieve per "page" of
      results. If there are more remaining results than this number, the
      response message will contain a valid value in the `next_page_token`
      field. The default value is 20, and the maximum page size is 100.
    pageToken: Optional. A page token to request the next page of results. You
      get the token from the `next_page_token` field of the response from the
      previous call.
    parent: Required. The name of the project whose models are to be listed.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class MlProjectsModelsPatchRequest(_messages.Message):
  r"""A MlProjectsModelsPatchRequest object.

  Fields:
    googleCloudMlV1Model: A GoogleCloudMlV1Model resource to be passed as the
      request body.
    name: Required. The project name.
    updateMask: Required. Specifies the path, relative to `Model`, of the
      field to update. For example, to change the description of a model to
      "foo" and set its default version to "version_1", the `update_mask`
      parameter would be specified as `description`, `default_version.name`,
      and the `PATCH` request body would specify the new value, as follows: {
      "description": "foo", "defaultVersion": { "name":"version_1" } }
      Currently the supported update masks are `description` and
      `default_version.name`.
  """

  googleCloudMlV1Model = _messages.MessageField('GoogleCloudMlV1Model', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class MlProjectsModelsSetIamPolicyRequest(_messages.Message):
  r"""A MlProjectsModelsSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class MlProjectsModelsTestIamPermissionsRequest(_messages.Message):
  r"""A MlProjectsModelsTestIamPermissionsRequest object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class MlProjectsModelsVersionsCreateRequest(_messages.Message):
  r"""A MlProjectsModelsVersionsCreateRequest object.

  Fields:
    googleCloudMlV1Version: A GoogleCloudMlV1Version resource to be passed as
      the request body.
    parent: Required. The name of the model.
  """

  googleCloudMlV1Version = _messages.MessageField('GoogleCloudMlV1Version', 1)
  parent = _messages.StringField(2, required=True)


class MlProjectsModelsVersionsDeleteRequest(_messages.Message):
  r"""A MlProjectsModelsVersionsDeleteRequest object.

  Fields:
    name: Required. The name of the version. You can get the names of all the
      versions of a model by calling projects.models.versions.list.
  """

  name = _messages.StringField(1, required=True)


class MlProjectsModelsVersionsGetRequest(_messages.Message):
  r"""A MlProjectsModelsVersionsGetRequest object.

  Fields:
    name: Required. The name of the version.
  """

  name = _messages.StringField(1, required=True)


class MlProjectsModelsVersionsListRequest(_messages.Message):
  r"""A MlProjectsModelsVersionsListRequest object.

  Fields:
    filter: Optional. Specifies the subset of versions to retrieve.
    pageSize: Optional. The number of versions to retrieve per "page" of
      results. If there are more remaining results than this number, the
      response message will contain a valid value in the `next_page_token`
      field. The default value is 20, and the maximum page size is 100.
    pageToken: Optional. A page token to request the next page of results. You
      get the token from the `next_page_token` field of the response from the
      previous call.
    parent: Required. The name of the model for which to list the version.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class MlProjectsModelsVersionsPatchRequest(_messages.Message):
  r"""A MlProjectsModelsVersionsPatchRequest object.

  Fields:
    googleCloudMlV1Version: A GoogleCloudMlV1Version resource to be passed as
      the request body.
    name: Required. The name of the model.
    updateMask: Required. Specifies the path, relative to `Version`, of the
      field to update. Must be present and non-empty. For example, to change
      the description of a version to "foo", the `update_mask` parameter would
      be specified as `description`, and the `PATCH` request body would
      specify the new value, as follows: ``` { "description": "foo" } ```
      Currently the only supported update mask fields are `description`,
      `requestLoggingConfig`, `autoScaling.minNodes`, and
      `manualScaling.nodes`. However, you can only update
      `manualScaling.nodes` if the version uses a [Compute Engine (N1) machine
      type](/ml-engine/docs/machine-types-online-prediction).
  """

  googleCloudMlV1Version = _messages.MessageField('GoogleCloudMlV1Version', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class MlProjectsModelsVersionsSetDefaultRequest(_messages.Message):
  r"""A MlProjectsModelsVersionsSetDefaultRequest object.

  Fields:
    googleCloudMlV1SetDefaultVersionRequest: A
      GoogleCloudMlV1SetDefaultVersionRequest resource to be passed as the
      request body.
    name: Required. The name of the version to make the default for the model.
      You can get the names of all the versions of a model by calling
      projects.models.versions.list.
  """

  googleCloudMlV1SetDefaultVersionRequest = _messages.MessageField('GoogleCloudMlV1SetDefaultVersionRequest', 1)
  name = _messages.StringField(2, required=True)


class MlProjectsOperationsCancelRequest(_messages.Message):
  r"""A MlProjectsOperationsCancelRequest object.

  Fields:
    name: The name of the operation resource to be cancelled.
  """

  name = _messages.StringField(1, required=True)


class MlProjectsOperationsGetRequest(_messages.Message):
  r"""A MlProjectsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class MlProjectsOperationsListRequest(_messages.Message):
  r"""A MlProjectsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class MlProjectsPredictRequest(_messages.Message):
  r"""A MlProjectsPredictRequest object.

  Fields:
    googleCloudMlV1PredictRequest: A GoogleCloudMlV1PredictRequest resource to
      be passed as the request body.
    name: Required. The resource name of a model or a version. Authorization:
      requires the `predict` permission on the specified resource.
  """

  googleCloudMlV1PredictRequest = _messages.MessageField('GoogleCloudMlV1PredictRequest', 1)
  name = _messages.StringField(2, required=True)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    MlProjectsJobsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    MlProjectsModelsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
