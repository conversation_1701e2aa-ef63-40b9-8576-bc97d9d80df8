"""Generated client library for servicenetworking version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.servicenetworking.v1 import servicenetworking_v1_messages as messages


class ServicenetworkingV1(base_api.BaseApiClient):
  """Generated client library for service servicenetworking version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://servicenetworking.googleapis.com/'
  MTLS_BASE_URL = 'https://servicenetworking.mtls.googleapis.com/'

  _PACKAGE = 'servicenetworking'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform', 'https://www.googleapis.com/auth/service.management']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'ServicenetworkingV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new servicenetworking handle."""
    url = url or self.BASE_URL
    super(ServicenetworkingV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.operations = self.OperationsService(self)
    self.services_connections = self.ServicesConnectionsService(self)
    self.services_dnsRecordSets = self.ServicesDnsRecordSetsService(self)
    self.services_dnsZones = self.ServicesDnsZonesService(self)
    self.services_projects_global_networks_dnsZones = self.ServicesProjectsGlobalNetworksDnsZonesService(self)
    self.services_projects_global_networks_peeredDnsDomains = self.ServicesProjectsGlobalNetworksPeeredDnsDomainsService(self)
    self.services_projects_global_networks = self.ServicesProjectsGlobalNetworksService(self)
    self.services_projects_global = self.ServicesProjectsGlobalService(self)
    self.services_projects = self.ServicesProjectsService(self)
    self.services_roles = self.ServicesRolesService(self)
    self.services = self.ServicesService(self)

  class OperationsService(base_api.BaseApiService):
    """Service class for the operations resource."""

    _NAME = 'operations'

    def __init__(self, client):
      super(ServicenetworkingV1.OperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (ServicenetworkingOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='servicenetworking.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='ServicenetworkingOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (ServicenetworkingOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/operations/{operationsId}',
        http_method='DELETE',
        method_id='servicenetworking.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ServicenetworkingOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (ServicenetworkingOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/operations/{operationsId}',
        http_method='GET',
        method_id='servicenetworking.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ServicenetworkingOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (ServicenetworkingOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/operations',
        http_method='GET',
        method_id='servicenetworking.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ServicenetworkingOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ServicesConnectionsService(base_api.BaseApiService):
    """Service class for the services_connections resource."""

    _NAME = 'services_connections'

    def __init__(self, client):
      super(ServicenetworkingV1.ServicesConnectionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a private connection that establishes a VPC Network Peering connection to a VPC network in the service producer's organization. The administrator of the service consumer's VPC network invokes this method. The administrator must assign one or more allocated IP ranges for provisioning subnetworks in the service producer's VPC network. This connection is used for all supported services in the service producer's organization, so it only needs to be invoked once.

      Args:
        request: (ServicenetworkingServicesConnectionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/services/{servicesId}/connections',
        http_method='POST',
        method_id='servicenetworking.services.connections.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/connections',
        request_field='connection',
        request_type_name='ServicenetworkingServicesConnectionsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def DeleteConnection(self, request, global_params=None):
      r"""Deletes a private service access connection.

      Args:
        request: (ServicenetworkingServicesConnectionsDeleteConnectionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('DeleteConnection')
      return self._RunMethod(
          config, request, global_params=global_params)

    DeleteConnection.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/services/{servicesId}/connections/{connectionsId}',
        http_method='POST',
        method_id='servicenetworking.services.connections.deleteConnection',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='deleteConnectionRequest',
        request_type_name='ServicenetworkingServicesConnectionsDeleteConnectionRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List the private connections that are configured in a service consumer's VPC network.

      Args:
        request: (ServicenetworkingServicesConnectionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListConnectionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/services/{servicesId}/connections',
        http_method='GET',
        method_id='servicenetworking.services.connections.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['network'],
        relative_path='v1/{+parent}/connections',
        request_field='',
        request_type_name='ServicenetworkingServicesConnectionsListRequest',
        response_type_name='ListConnectionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the allocated ranges that are assigned to a connection.

      Args:
        request: (ServicenetworkingServicesConnectionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/services/{servicesId}/connections/{connectionsId}',
        http_method='PATCH',
        method_id='servicenetworking.services.connections.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='connection',
        request_type_name='ServicenetworkingServicesConnectionsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ServicesDnsRecordSetsService(base_api.BaseApiService):
    """Service class for the services_dnsRecordSets resource."""

    _NAME = 'services_dnsRecordSets'

    def __init__(self, client):
      super(ServicenetworkingV1.ServicesDnsRecordSetsService, self).__init__(client)
      self._upload_configs = {
          }

    def Add(self, request, global_params=None):
      r"""Service producers can use this method to add DNS record sets to private DNS zones in the shared producer host project.

      Args:
        request: (ServicenetworkingServicesDnsRecordSetsAddRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Add')
      return self._RunMethod(
          config, request, global_params=global_params)

    Add.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/services/{servicesId}/dnsRecordSets:add',
        http_method='POST',
        method_id='servicenetworking.services.dnsRecordSets.add',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/dnsRecordSets:add',
        request_field='addDnsRecordSetRequest',
        request_type_name='ServicenetworkingServicesDnsRecordSetsAddRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Producers can use this method to retrieve information about the DNS record set added to the private zone inside the shared tenant host project associated with a consumer network.

      Args:
        request: (ServicenetworkingServicesDnsRecordSetsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DnsRecordSet) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/services/{servicesId}/dnsRecordSets:get',
        http_method='GET',
        method_id='servicenetworking.services.dnsRecordSets.get',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['consumerNetwork', 'domain', 'type', 'zone'],
        relative_path='v1/{+parent}/dnsRecordSets:get',
        request_field='',
        request_type_name='ServicenetworkingServicesDnsRecordSetsGetRequest',
        response_type_name='DnsRecordSet',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Producers can use this method to retrieve a list of available DNS RecordSets available inside the private zone on the tenant host project accessible from their network.

      Args:
        request: (ServicenetworkingServicesDnsRecordSetsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDnsRecordSetsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/services/{servicesId}/dnsRecordSets:list',
        http_method='GET',
        method_id='servicenetworking.services.dnsRecordSets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['consumerNetwork', 'zone'],
        relative_path='v1/{+parent}/dnsRecordSets:list',
        request_field='',
        request_type_name='ServicenetworkingServicesDnsRecordSetsListRequest',
        response_type_name='ListDnsRecordSetsResponse',
        supports_download=False,
    )

    def Remove(self, request, global_params=None):
      r"""Service producers can use this method to remove DNS record sets from private DNS zones in the shared producer host project.

      Args:
        request: (ServicenetworkingServicesDnsRecordSetsRemoveRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Remove')
      return self._RunMethod(
          config, request, global_params=global_params)

    Remove.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/services/{servicesId}/dnsRecordSets:remove',
        http_method='POST',
        method_id='servicenetworking.services.dnsRecordSets.remove',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/dnsRecordSets:remove',
        request_field='removeDnsRecordSetRequest',
        request_type_name='ServicenetworkingServicesDnsRecordSetsRemoveRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Service producers can use this method to update DNS record sets from private DNS zones in the shared producer host project.

      Args:
        request: (ServicenetworkingServicesDnsRecordSetsUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/services/{servicesId}/dnsRecordSets:update',
        http_method='POST',
        method_id='servicenetworking.services.dnsRecordSets.update',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/dnsRecordSets:update',
        request_field='updateDnsRecordSetRequest',
        request_type_name='ServicenetworkingServicesDnsRecordSetsUpdateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ServicesDnsZonesService(base_api.BaseApiService):
    """Service class for the services_dnsZones resource."""

    _NAME = 'services_dnsZones'

    def __init__(self, client):
      super(ServicenetworkingV1.ServicesDnsZonesService, self).__init__(client)
      self._upload_configs = {
          }

    def Add(self, request, global_params=None):
      r"""Service producers can use this method to add private DNS zones in the shared producer host project and matching peering zones in the consumer project.

      Args:
        request: (ServicenetworkingServicesDnsZonesAddRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Add')
      return self._RunMethod(
          config, request, global_params=global_params)

    Add.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/services/{servicesId}/dnsZones:add',
        http_method='POST',
        method_id='servicenetworking.services.dnsZones.add',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/dnsZones:add',
        request_field='addDnsZoneRequest',
        request_type_name='ServicenetworkingServicesDnsZonesAddRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Remove(self, request, global_params=None):
      r"""Service producers can use this method to remove private DNS zones in the shared producer host project and matching peering zones in the consumer project.

      Args:
        request: (ServicenetworkingServicesDnsZonesRemoveRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Remove')
      return self._RunMethod(
          config, request, global_params=global_params)

    Remove.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/services/{servicesId}/dnsZones:remove',
        http_method='POST',
        method_id='servicenetworking.services.dnsZones.remove',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/dnsZones:remove',
        request_field='removeDnsZoneRequest',
        request_type_name='ServicenetworkingServicesDnsZonesRemoveRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ServicesProjectsGlobalNetworksDnsZonesService(base_api.BaseApiService):
    """Service class for the services_projects_global_networks_dnsZones resource."""

    _NAME = 'services_projects_global_networks_dnsZones'

    def __init__(self, client):
      super(ServicenetworkingV1.ServicesProjectsGlobalNetworksDnsZonesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Service producers can use this method to retrieve a DNS zone in the shared producer host project and the matching peering zones in consumer project.

      Args:
        request: (ServicenetworkingServicesProjectsGlobalNetworksDnsZonesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GetDnsZoneResponse) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/services/{servicesId}/projects/{projectsId}/global/networks/{networksId}/dnsZones/{dnsZonesId}',
        http_method='GET',
        method_id='servicenetworking.services.projects.global.networks.dnsZones.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ServicenetworkingServicesProjectsGlobalNetworksDnsZonesGetRequest',
        response_type_name='GetDnsZoneResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""* Service producers can use this method to retrieve a list of available DNS zones in the shared producer host project and the matching peering zones in the consumer project. *.

      Args:
        request: (ServicenetworkingServicesProjectsGlobalNetworksDnsZonesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDnsZonesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/services/{servicesId}/projects/{projectsId}/global/networks/{networksId}/dnsZones:list',
        http_method='GET',
        method_id='servicenetworking.services.projects.global.networks.dnsZones.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/dnsZones:list',
        request_field='',
        request_type_name='ServicenetworkingServicesProjectsGlobalNetworksDnsZonesListRequest',
        response_type_name='ListDnsZonesResponse',
        supports_download=False,
    )

  class ServicesProjectsGlobalNetworksPeeredDnsDomainsService(base_api.BaseApiService):
    """Service class for the services_projects_global_networks_peeredDnsDomains resource."""

    _NAME = 'services_projects_global_networks_peeredDnsDomains'

    def __init__(self, client):
      super(ServicenetworkingV1.ServicesProjectsGlobalNetworksPeeredDnsDomainsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a peered DNS domain which sends requests for records in given namespace originating in the service producer VPC network to the consumer VPC network to be resolved.

      Args:
        request: (ServicenetworkingServicesProjectsGlobalNetworksPeeredDnsDomainsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/services/{servicesId}/projects/{projectsId}/global/networks/{networksId}/peeredDnsDomains',
        http_method='POST',
        method_id='servicenetworking.services.projects.global.networks.peeredDnsDomains.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/peeredDnsDomains',
        request_field='peeredDnsDomain',
        request_type_name='ServicenetworkingServicesProjectsGlobalNetworksPeeredDnsDomainsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a peered DNS domain.

      Args:
        request: (ServicenetworkingServicesProjectsGlobalNetworksPeeredDnsDomainsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/services/{servicesId}/projects/{projectsId}/global/networks/{networksId}/peeredDnsDomains/{peeredDnsDomainsId}',
        http_method='DELETE',
        method_id='servicenetworking.services.projects.global.networks.peeredDnsDomains.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ServicenetworkingServicesProjectsGlobalNetworksPeeredDnsDomainsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists peered DNS domains for a connection.

      Args:
        request: (ServicenetworkingServicesProjectsGlobalNetworksPeeredDnsDomainsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPeeredDnsDomainsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/services/{servicesId}/projects/{projectsId}/global/networks/{networksId}/peeredDnsDomains',
        http_method='GET',
        method_id='servicenetworking.services.projects.global.networks.peeredDnsDomains.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/peeredDnsDomains',
        request_field='',
        request_type_name='ServicenetworkingServicesProjectsGlobalNetworksPeeredDnsDomainsListRequest',
        response_type_name='ListPeeredDnsDomainsResponse',
        supports_download=False,
    )

  class ServicesProjectsGlobalNetworksService(base_api.BaseApiService):
    """Service class for the services_projects_global_networks resource."""

    _NAME = 'services_projects_global_networks'

    def __init__(self, client):
      super(ServicenetworkingV1.ServicesProjectsGlobalNetworksService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Service producers use this method to get the configuration of their connection including the import/export of custom routes and subnetwork routes with public IP.

      Args:
        request: (ServicenetworkingServicesProjectsGlobalNetworksGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ConsumerConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/services/{servicesId}/projects/{projectsId}/global/networks/{networksId}',
        http_method='GET',
        method_id='servicenetworking.services.projects.global.networks.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['includeUsedIpRanges'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ServicenetworkingServicesProjectsGlobalNetworksGetRequest',
        response_type_name='ConsumerConfig',
        supports_download=False,
    )

    def GetVpcServiceControls(self, request, global_params=None):
      r"""Consumers use this method to find out the state of VPC Service Controls. The controls could be enabled or disabled for a connection.

      Args:
        request: (ServicenetworkingServicesProjectsGlobalNetworksGetVpcServiceControlsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (VpcServiceControls) The response message.
      """
      config = self.GetMethodConfig('GetVpcServiceControls')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetVpcServiceControls.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/services/{servicesId}/projects/{projectsId}/global/networks/{networksId}/vpcServiceControls',
        http_method='GET',
        method_id='servicenetworking.services.projects.global.networks.getVpcServiceControls',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}/vpcServiceControls',
        request_field='',
        request_type_name='ServicenetworkingServicesProjectsGlobalNetworksGetVpcServiceControlsRequest',
        response_type_name='VpcServiceControls',
        supports_download=False,
    )

    def UpdateConsumerConfig(self, request, global_params=None):
      r"""Service producers use this method to update the configuration of their connection including the import/export of custom routes and subnetwork routes with public IP.

      Args:
        request: (ServicenetworkingServicesProjectsGlobalNetworksUpdateConsumerConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('UpdateConsumerConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateConsumerConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/services/{servicesId}/projects/{projectsId}/global/networks/{networksId}:updateConsumerConfig',
        http_method='PATCH',
        method_id='servicenetworking.services.projects.global.networks.updateConsumerConfig',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}:updateConsumerConfig',
        request_field='updateConsumerConfigRequest',
        request_type_name='ServicenetworkingServicesProjectsGlobalNetworksUpdateConsumerConfigRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ServicesProjectsGlobalService(base_api.BaseApiService):
    """Service class for the services_projects_global resource."""

    _NAME = 'services_projects_global'

    def __init__(self, client):
      super(ServicenetworkingV1.ServicesProjectsGlobalService, self).__init__(client)
      self._upload_configs = {
          }

  class ServicesProjectsService(base_api.BaseApiService):
    """Service class for the services_projects resource."""

    _NAME = 'services_projects'

    def __init__(self, client):
      super(ServicenetworkingV1.ServicesProjectsService, self).__init__(client)
      self._upload_configs = {
          }

  class ServicesRolesService(base_api.BaseApiService):
    """Service class for the services_roles resource."""

    _NAME = 'services_roles'

    def __init__(self, client):
      super(ServicenetworkingV1.ServicesRolesService, self).__init__(client)
      self._upload_configs = {
          }

    def Add(self, request, global_params=None):
      r"""Service producers can use this method to add roles in the shared VPC host project. Each role is bound to the provided member. Each role must be selected from within an allowlisted set of roles. Each role is applied at only the granularity specified in the allowlist.

      Args:
        request: (ServicenetworkingServicesRolesAddRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Add')
      return self._RunMethod(
          config, request, global_params=global_params)

    Add.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/services/{servicesId}/roles:add',
        http_method='POST',
        method_id='servicenetworking.services.roles.add',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/roles:add',
        request_field='addRolesRequest',
        request_type_name='ServicenetworkingServicesRolesAddRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ServicesService(base_api.BaseApiService):
    """Service class for the services resource."""

    _NAME = 'services'

    def __init__(self, client):
      super(ServicenetworkingV1.ServicesService, self).__init__(client)
      self._upload_configs = {
          }

    def AddSubnetwork(self, request, global_params=None):
      r"""For service producers, provisions a new subnet in a peered service's shared VPC network in the requested region and with the requested size that's expressed as a CIDR range (number of leading bits of ipV4 network mask). The method checks against the assigned allocated ranges to find a non-conflicting IP address range. The method will reuse a subnet if subsequent calls contain the same subnet name, region, and prefix length. This method will make producer's tenant project to be a shared VPC service project as needed.

      Args:
        request: (ServicenetworkingServicesAddSubnetworkRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('AddSubnetwork')
      return self._RunMethod(
          config, request, global_params=global_params)

    AddSubnetwork.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/services/{servicesId}/{servicesId1}/{servicesId2}:addSubnetwork',
        http_method='POST',
        method_id='servicenetworking.services.addSubnetwork',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}:addSubnetwork',
        request_field='addSubnetworkRequest',
        request_type_name='ServicenetworkingServicesAddSubnetworkRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def DisableVpcServiceControls(self, request, global_params=None):
      r"""Disables VPC service controls for a connection.

      Args:
        request: (ServicenetworkingServicesDisableVpcServiceControlsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('DisableVpcServiceControls')
      return self._RunMethod(
          config, request, global_params=global_params)

    DisableVpcServiceControls.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/services/{servicesId}:disableVpcServiceControls',
        http_method='PATCH',
        method_id='servicenetworking.services.disableVpcServiceControls',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}:disableVpcServiceControls',
        request_field='disableVpcServiceControlsRequest',
        request_type_name='ServicenetworkingServicesDisableVpcServiceControlsRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def EnableVpcServiceControls(self, request, global_params=None):
      r"""Enables VPC service controls for a connection.

      Args:
        request: (ServicenetworkingServicesEnableVpcServiceControlsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('EnableVpcServiceControls')
      return self._RunMethod(
          config, request, global_params=global_params)

    EnableVpcServiceControls.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/services/{servicesId}:enableVpcServiceControls',
        http_method='PATCH',
        method_id='servicenetworking.services.enableVpcServiceControls',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}:enableVpcServiceControls',
        request_field='enableVpcServiceControlsRequest',
        request_type_name='ServicenetworkingServicesEnableVpcServiceControlsRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SearchRange(self, request, global_params=None):
      r"""Service producers can use this method to find a currently unused range within consumer allocated ranges. This returned range is not reserved, and not guaranteed to remain unused. It will validate previously provided allocated ranges, find non-conflicting sub-range of requested size (expressed in number of leading bits of ipv4 network mask, as in CIDR range notation).

      Args:
        request: (ServicenetworkingServicesSearchRangeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('SearchRange')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchRange.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/services/{servicesId}:searchRange',
        http_method='POST',
        method_id='servicenetworking.services.searchRange',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}:searchRange',
        request_field='searchRangeRequest',
        request_type_name='ServicenetworkingServicesSearchRangeRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Validate(self, request, global_params=None):
      r"""Service producers use this method to validate if the consumer provided network, project and requested range are valid. This allows them to use a fail-fast mechanism for consumer requests, and not have to wait for AddSubnetwork operation completion to determine if user request is invalid.

      Args:
        request: (ServicenetworkingServicesValidateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ValidateConsumerConfigResponse) The response message.
      """
      config = self.GetMethodConfig('Validate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Validate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/services/{servicesId}:validate',
        http_method='POST',
        method_id='servicenetworking.services.validate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}:validate',
        request_field='validateConsumerConfigRequest',
        request_type_name='ServicenetworkingServicesValidateRequest',
        response_type_name='ValidateConsumerConfigResponse',
        supports_download=False,
    )
