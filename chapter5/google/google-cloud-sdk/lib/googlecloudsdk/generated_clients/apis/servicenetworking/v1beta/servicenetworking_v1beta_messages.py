"""Generated message classes for servicenetworking version v1beta.

The Service Networking API provides automatic management of network
configurations necessary for certain services.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'servicenetworking'


class AddSubnetworkRequest(_messages.Message):
  r"""Request to create a subnetwork in a previously peered service network.

  Fields:
    consumer: Resource representing service consumer. It may be different from
      the project number in consumer network parameter in case of that network
      being a shared VPC network. In that case, Service Networking will
      validate that this resource belongs to that shared VPC.  Required. For
      example 'projects/123456'.
    consumerNetwork: Network name in the consumer project.   This network must
      have been already peered with a shared VPC network using
      CreateConnection method. Must be in a form
      'projects/{project}/global/networks/{network}'. {project} is a project
      number, as in '12345' {network} is network name.
    description: Description of the subnetwork.
    ipPrefixLength: The prefix length of the IP range. Use usual CIDR range
      notation. For example, '30' to provision subnet with x.x.x.x/30 CIDR
      range. Actual range will determined using reserved range for the
      consumer peered network and returned in the result.
    region: Cloud [region](/compute/docs/reference/rest/v1/regions) for the
      new subnetwork.
    subnetwork: Name for the new subnetwork. Must be a legal
      [subnetwork](compute/docs/reference/rest/v1/subnetworks) name.
    subnetworkUsers: List of members that will be granted
      'compute.networkUser' role on the newly added subnetwork.
  """

  consumer = _messages.StringField(1)
  consumerNetwork = _messages.StringField(2)
  description = _messages.StringField(3)
  ipPrefixLength = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  region = _messages.StringField(5)
  subnetwork = _messages.StringField(6)
  subnetworkUsers = _messages.StringField(7, repeated=True)


class AddSubnetworkResponse(_messages.Message):
  r"""Message returning the name of the created service subnetwork.

  Fields:
    ipCidrRange: Subnetwork CIDR range in "10.x.x.x/y" format.
    name: Subnetwork name. See https://cloud.google.com/compute/docs/vpc/
  """

  ipCidrRange = _messages.StringField(1)
  name = _messages.StringField(2)


class Api(_messages.Message):
  r"""Api is a light-weight descriptor for an API Interface.  Interfaces are
  also described as "protocol buffer services" in some contexts, such as by
  the "service" keyword in a .proto file, but they are different from API
  Services, which represent a concrete implementation of an interface as
  opposed to simply a description of methods and bindings. They are also
  sometimes simply referred to as "APIs" in other contexts, such as the name
  of this message itself. See https://cloud.google.com/apis/design/glossary
  for detailed terminology.

  Enums:
    SyntaxValueValuesEnum: The source syntax of the service.

  Fields:
    methods: The methods of this interface, in unspecified order.
    mixins: Included interfaces. See Mixin.
    name: The fully qualified name of this interface, including package name
      followed by the interface's simple name.
    options: Any metadata attached to the interface.
    sourceContext: Source context for the protocol buffer service represented
      by this message.
    syntax: The source syntax of the service.
    version: A version string for this interface. If specified, must have the
      form `major-version.minor-version`, as in `1.10`. If the minor version
      is omitted, it defaults to zero. If the entire version field is empty,
      the major version is derived from the package name, as outlined below.
      If the field is not empty, the version in the package name will be
      verified to be consistent with what is provided here.  The versioning
      schema uses [semantic versioning](http://semver.org) where the major
      version number indicates a breaking change and the minor version an
      additive, non-breaking change. Both version numbers are signals to users
      what to expect from different versions, and should be carefully chosen
      based on the product plan.  The major version is also reflected in the
      package name of the interface, which must end in `v<major-version>`, as
      in `google.feature.v1`. For major versions 0 and 1, the suffix can be
      omitted. Zero major versions must only be used for experimental, non-GA
      interfaces.
  """

  class SyntaxValueValuesEnum(_messages.Enum):
    r"""The source syntax of the service.

    Values:
      SYNTAX_PROTO2: Syntax `proto2`.
      SYNTAX_PROTO3: Syntax `proto3`.
    """
    SYNTAX_PROTO2 = 0
    SYNTAX_PROTO3 = 1

  methods = _messages.MessageField('Method', 1, repeated=True)
  mixins = _messages.MessageField('Mixin', 2, repeated=True)
  name = _messages.StringField(3)
  options = _messages.MessageField('Option', 4, repeated=True)
  sourceContext = _messages.MessageField('SourceContext', 5)
  syntax = _messages.EnumField('SyntaxValueValuesEnum', 6)
  version = _messages.StringField(7)


class AuthProvider(_messages.Message):
  r"""Configuration for an anthentication provider, including support for
  [JSON Web Token (JWT)](https://tools.ietf.org/html/draft-ietf-oauth-json-
  web-token-32).

  Fields:
    audiences: The list of JWT [audiences](https://tools.ietf.org/html/draft-
      ietf-oauth-json-web-token-32#section-4.1.3). that are allowed to access.
      A JWT containing any of these audiences will be accepted. When this
      setting is absent, only JWTs with audience
      "https://Service_name/API_name" will be accepted. For example, if no
      audiences are in the setting, LibraryService API will only accept JWTs
      with the following audience "https://library-
      example.googleapis.com/google.example.library.v1.LibraryService".
      Example:      audiences: bookstore_android.apps.googleusercontent.com,
      bookstore_web.apps.googleusercontent.com
    authorizationUrl: Redirect URL if JWT token is required but no present or
      is expired. Implement authorizationUrl of securityDefinitions in OpenAPI
      spec.
    id: The unique identifier of the auth provider. It will be referred to by
      `AuthRequirement.provider_id`.  Example: "bookstore_auth".
    issuer: Identifies the principal that issued the JWT. See
      https://tools.ietf.org/html/draft-ietf-oauth-json-web-
      token-32#section-4.1.1 Usually a URL or an email address.  Example:
      https://securetoken.google.com Example:
      <EMAIL>
    jwksUri: URL of the provider's public key set to validate signature of the
      JWT. See [OpenID Discovery](https://openid.net/specs/openid-connect-
      discovery-1_0.html#ProviderMetadata). Optional if the key set document:
      - can be retrieved from    [OpenID
      Discovery](https://openid.net/specs/openid-connect-discovery-1_0.html
      of the issuer.  - can be inferred from the email domain of the issuer
      (e.g. a Google service account).  Example:
      https://www.googleapis.com/oauth2/v1/certs
  """

  audiences = _messages.StringField(1)
  authorizationUrl = _messages.StringField(2)
  id = _messages.StringField(3)
  issuer = _messages.StringField(4)
  jwksUri = _messages.StringField(5)


class AuthRequirement(_messages.Message):
  r"""User-defined authentication requirements, including support for [JSON
  Web Token (JWT)](https://tools.ietf.org/html/draft-ietf-oauth-json-web-
  token-32).

  Fields:
    audiences: NOTE: This will be deprecated soon, once AuthProvider.audiences
      is implemented and accepted in all the runtime components.  The list of
      JWT [audiences](https://tools.ietf.org/html/draft-ietf-oauth-json-web-
      token-32#section-4.1.3). that are allowed to access. A JWT containing
      any of these audiences will be accepted. When this setting is absent,
      only JWTs with audience "https://Service_name/API_name" will be
      accepted. For example, if no audiences are in the setting,
      LibraryService API will only accept JWTs with the following audience
      "https://library-
      example.googleapis.com/google.example.library.v1.LibraryService".
      Example:      audiences: bookstore_android.apps.googleusercontent.com,
      bookstore_web.apps.googleusercontent.com
    providerId: id from authentication provider.  Example:      provider_id:
      bookstore_auth
  """

  audiences = _messages.StringField(1)
  providerId = _messages.StringField(2)


class Authentication(_messages.Message):
  r"""`Authentication` defines the authentication configuration for an API.
  Example for an API targeted for external use:      name:
  calendar.googleapis.com     authentication:       providers:       - id:
  google_calendar_auth         jwks_uri:
  https://www.googleapis.com/oauth2/v1/certs         issuer:
  https://securetoken.google.com       rules:       - selector: "*"
  requirements:           provider_id: google_calendar_auth

  Fields:
    providers: Defines a set of authentication providers that a service
      supports.
    rules: A list of authentication rules that apply to individual API
      methods.  **NOTE:** All service configuration rules follow "last one
      wins" order.
  """

  providers = _messages.MessageField('AuthProvider', 1, repeated=True)
  rules = _messages.MessageField('AuthenticationRule', 2, repeated=True)


class AuthenticationRule(_messages.Message):
  r"""Authentication rules for the service.  By default, if a method has any
  authentication requirements, every request must include a valid credential
  matching one of the requirements. It's an error to include more than one
  kind of credential in a single request.  If a method doesn't have any auth
  requirements, request credentials will be ignored.

  Fields:
    allowWithoutCredential: If true, the service accepts API keys without any
      other credential.
    oauth: The requirements for OAuth credentials.
    requirements: Requirements for additional authentication providers.
    selector: Selects the methods to which this rule applies.  Refer to
      selector for syntax details.
  """

  allowWithoutCredential = _messages.BooleanField(1)
  oauth = _messages.MessageField('OAuthRequirements', 2)
  requirements = _messages.MessageField('AuthRequirement', 3, repeated=True)
  selector = _messages.StringField(4)


class AuthorizationConfig(_messages.Message):
  r"""Configuration of authorization.  This section determines the
  authorization provider, if unspecified, then no authorization check will be
  done.  Example:      experimental:       authorization:         provider:
  firebaserules.googleapis.com

  Fields:
    provider: The name of the authorization provider, such as
      firebaserules.googleapis.com.
  """

  provider = _messages.StringField(1)


class AuthorizationRule(_messages.Message):
  r"""Authorization rule for API services.  It specifies the permission(s)
  required for an API element for the overall API request to succeed. It is
  typically used to mark request message fields that contain the name of the
  resource and indicates the permissions that will be checked on that
  resource.  For example:      package google.storage.v1;      message
  CopyObjectRequest {       string source = 1 [
  (google.api.authz).permissions = "storage.objects.get"];        string
  destination = 2 [         (google.api.authz).permissions =
  "storage.objects.create,storage.objects.update"];     }

  Fields:
    permissions: The required permissions. The acceptable values vary depend
      on the authorization system used. For Google APIs, it should be a comma-
      separated Google IAM permission values. When multiple permissions are
      listed, the semantics is not defined by the system. Additional
      documentation must be provided manually.
    selector: Selects the API elements to which this rule applies.  Refer to
      selector for syntax details.
  """

  permissions = _messages.StringField(1)
  selector = _messages.StringField(2)


class Backend(_messages.Message):
  r"""`Backend` defines the backend configuration for a service.

  Fields:
    rules: A list of API backend rules that apply to individual API methods.
      **NOTE:** All service configuration rules follow "last one wins" order.
  """

  rules = _messages.MessageField('BackendRule', 1, repeated=True)


class BackendRule(_messages.Message):
  r"""A backend rule provides configuration for an individual API element.

  Fields:
    address: The address of the API backend.
    deadline: The number of seconds to wait for a response from a request.
      The default deadline for gRPC is infinite (no deadline) and HTTP
      requests is 5 seconds.
    minDeadline: Minimum deadline in seconds needed for this method. Calls
      having deadline value lower than this will be rejected.
    selector: Selects the methods to which this rule applies.  Refer to
      selector for syntax details.
  """

  address = _messages.StringField(1)
  deadline = _messages.FloatField(2)
  minDeadline = _messages.FloatField(3)
  selector = _messages.StringField(4)


class Billing(_messages.Message):
  r"""Billing related configuration of the service.  The following example
  shows how to configure monitored resources and metrics for billing:
  monitored_resources:     - type: library.googleapis.com/branch       labels:
  - key: /city         description: The city where the library branch is
  located in.       - key: /name         description: The name of the branch.
  metrics:     - name: library.googleapis.com/book/borrowed_count
  metric_kind: DELTA       value_type: INT64     billing:
  consumer_destinations:       - monitored_resource:
  library.googleapis.com/branch         metrics:         -
  library.googleapis.com/book/borrowed_count

  Fields:
    consumerDestinations: Billing configurations for sending metrics to the
      consumer project. There can be multiple consumer destinations per
      service, each one must have a different monitored resource type. A
      metric can be used in at most one consumer destination.
  """

  consumerDestinations = _messages.MessageField('BillingDestination', 1, repeated=True)


class BillingDestination(_messages.Message):
  r"""Configuration of a specific billing destination (Currently only support
  bill against consumer project).

  Fields:
    metrics: Names of the metrics to report to this billing destination. Each
      name must be defined in Service.metrics section.
    monitoredResource: The monitored resource type. The type must be defined
      in Service.monitored_resources section.
  """

  metrics = _messages.StringField(1, repeated=True)
  monitoredResource = _messages.StringField(2)


class Connection(_messages.Message):
  r"""Message returning the created service connection.

  Fields:
    network: Name of an existing VPC network that will be connected with
      service producers using VPC peering. Must be in a form
      'projects/{project}/global/networks/{network}'. {project} is a project
      number, as in '12345' {network} is a network name.
    peering: Output only. Name of the peering connection that is created by
      the peering service.
    reservedPeeringRanges: Named IP address range(s) of PEERING type reserved
      for this service provider. Note that invoking this method with a
      different range when connection is already established will not
      reallocate already provisioned service producer subnetworks.
  """

  network = _messages.StringField(1)
  peering = _messages.StringField(2)
  reservedPeeringRanges = _messages.StringField(3, repeated=True)


class Context(_messages.Message):
  r"""`Context` defines which contexts an API requests.  Example:
  context:       rules:       - selector: "*"         requested:         -
  google.rpc.context.ProjectContext         - google.rpc.context.OriginContext
  The above specifies that all methods in the API request
  `google.rpc.context.ProjectContext` and `google.rpc.context.OriginContext`.
  Available context types are defined in package `google.rpc.context`.  This
  also provides mechanism to whitelist any protobuf message extension that can
  be sent in grpc metadata using "x-goog-ext-<extension_id>-bin" and "x-goog-
  ext-<extension_id>-jspb" format. For example, list any service specific
  protobuf types that can appear in grpc metadata as follows in your yaml
  file:  Example:      context:       rules:        - selector:
  "google.example.library.v1.LibraryService.CreateBook"
  allowed_request_extensions:          - google.foo.v1.NewExtension
  allowed_response_extensions:          - google.foo.v1.NewExtension  You can
  also specify extension ID instead of fully qualified extension name here.

  Fields:
    rules: A list of RPC context rules that apply to individual API methods.
      **NOTE:** All service configuration rules follow "last one wins" order.
  """

  rules = _messages.MessageField('ContextRule', 1, repeated=True)


class ContextRule(_messages.Message):
  r"""A context rule provides information about the context for an individual
  API element.

  Fields:
    allowedRequestExtensions: A list of full type names or extension IDs of
      extensions allowed in grpc side channel from client to backend.
    allowedResponseExtensions: A list of full type names or extension IDs of
      extensions allowed in grpc side channel from backend to client.
    provided: A list of full type names of provided contexts.
    requested: A list of full type names of requested contexts.
    selector: Selects the methods to which this rule applies.  Refer to
      selector for syntax details.
  """

  allowedRequestExtensions = _messages.StringField(1, repeated=True)
  allowedResponseExtensions = _messages.StringField(2, repeated=True)
  provided = _messages.StringField(3, repeated=True)
  requested = _messages.StringField(4, repeated=True)
  selector = _messages.StringField(5)


class Control(_messages.Message):
  r"""Selects and configures the service controller used by the service.  The
  service controller handles features like abuse, quota, billing, logging,
  monitoring, etc.

  Fields:
    environment: The service control environment to use. If empty, no control
      plane feature (like quota and billing) will be enabled.
  """

  environment = _messages.StringField(1)


class CustomError(_messages.Message):
  r"""Customize service error responses.  For example, list any service
  specific protobuf types that can appear in error detail lists of error
  responses.  Example:      custom_error:       types:       -
  google.foo.v1.CustomError       - google.foo.v1.AnotherError

  Fields:
    rules: The list of custom error rules that apply to individual API
      messages.  **NOTE:** All service configuration rules follow "last one
      wins" order.
    types: The list of custom error detail types, e.g.
      'google.foo.v1.CustomError'.
  """

  rules = _messages.MessageField('CustomErrorRule', 1, repeated=True)
  types = _messages.StringField(2, repeated=True)


class CustomErrorRule(_messages.Message):
  r"""A custom error rule.

  Fields:
    isErrorType: Mark this message as possible payload in error response.
      Otherwise, objects of this type will be filtered when they appear in
      error payload.
    selector: Selects messages to which this rule applies.  Refer to selector
      for syntax details.
  """

  isErrorType = _messages.BooleanField(1)
  selector = _messages.StringField(2)


class CustomHttpPattern(_messages.Message):
  r"""A custom pattern is used for defining custom HTTP verb.

  Fields:
    kind: The name of this custom HTTP verb.
    path: The path matched by this custom verb.
  """

  kind = _messages.StringField(1)
  path = _messages.StringField(2)


class Documentation(_messages.Message):
  r"""`Documentation` provides the information for describing a service.
  Example: <pre><code>documentation:   summary: >     The Google Calendar API
  gives access     to most calendar features.   pages:   - name: Overview
  content: &#40;== include google/foo/overview.md ==&#41;   - name: Tutorial
  content: &#40;== include google/foo/tutorial.md ==&#41;     subpages;     -
  name: Java       content: &#40;== include google/foo/tutorial_java.md
  ==&#41;   rules:   - selector: google.calendar.Calendar.Get     description:
  >       ...   - selector: google.calendar.Calendar.Put     description: >
  ... </code></pre> Documentation is provided in markdown syntax. In addition
  to standard markdown features, definition lists, tables and fenced code
  blocks are supported. Section headers can be provided and are interpreted
  relative to the section nesting of the context where a documentation
  fragment is embedded.  Documentation from the IDL is merged with
  documentation defined via the config at normalization time, where
  documentation provided by config rules overrides IDL provided.  A number of
  constructs specific to the API platform are supported in documentation text.
  In order to reference a proto element, the following notation can be used:
  <pre><code>&#91;fully.qualified.proto.name]&#91;]</code></pre> To override
  the display text used for the link, this can be used:
  <pre><code>&#91;display text]&#91;fully.qualified.proto.name]</code></pre>
  Text can be excluded from doc using the following notation:
  <pre><code>&#40;-- internal comment --&#41;</code></pre>  A few directives
  are available in documentation. Note that directives must appear on a single
  line to be properly identified. The `include` directive includes a markdown
  file from an external source: <pre><code>&#40;== include path/to/file
  ==&#41;</code></pre> The `resource_for` directive marks a message to be the
  resource of a collection in REST view. If it is not specified, tools attempt
  to infer the resource from the operations in a collection:
  <pre><code>&#40;== resource_for v1.shelves.books ==&#41;</code></pre> The
  directive `suppress_warning` does not directly affect documentation and is
  documented together with service config validation.

  Fields:
    documentationRootUrl: The URL to the root of documentation.
    overview: Declares a single overview page. For example:
      <pre><code>documentation:   summary: ...   overview: &#40;== include
      overview.md ==&#41; </code></pre> This is a shortcut for the following
      declaration (using pages style): <pre><code>documentation:   summary:
      ...   pages:   - name: Overview     content: &#40;== include overview.md
      ==&#41; </code></pre> Note: you cannot specify both `overview` field and
      `pages` field.
    pages: The top level pages for the documentation set.
    rules: A list of documentation rules that apply to individual API
      elements.  **NOTE:** All service configuration rules follow "last one
      wins" order.
    summary: A short summary of what the service does. Can only be provided by
      plain text.
  """

  documentationRootUrl = _messages.StringField(1)
  overview = _messages.StringField(2)
  pages = _messages.MessageField('Page', 3, repeated=True)
  rules = _messages.MessageField('DocumentationRule', 4, repeated=True)
  summary = _messages.StringField(5)


class DocumentationRule(_messages.Message):
  r"""A documentation rule provides information about individual API elements.

  Fields:
    deprecationDescription: Deprecation description of the selected
      element(s). It can be provided if an element is marked as `deprecated`.
    description: Description of the selected API(s).
    selector: The selector is a comma-separated list of patterns. Each pattern
      is a qualified name of the element which may end in "*", indicating a
      wildcard. Wildcards are only allowed at the end and for a whole
      component of the qualified name, i.e. "foo.*" is ok, but not "foo.b*" or
      "foo.*.bar". To specify a default for all applicable elements, the whole
      pattern "*" is used.
  """

  deprecationDescription = _messages.StringField(1)
  description = _messages.StringField(2)
  selector = _messages.StringField(3)


class Endpoint(_messages.Message):
  r"""`Endpoint` describes a network endpoint that serves a set of APIs. A
  service may expose any number of endpoints, and all endpoints share the same
  service configuration, such as quota configuration and monitoring
  configuration.  Example service configuration:      name: library-
  example.googleapis.com     endpoints:       # Below entry makes
  'google.example.library.v1.Library'       # API be served from endpoint
  address library-example.googleapis.com.       # It also allows HTTP OPTIONS
  calls to be passed to the backend, for       # it to decide whether the
  subsequent cross-origin request is       # allowed to proceed.     - name:
  library-example.googleapis.com       allow_cors: true

  Fields:
    aliases: DEPRECATED: This field is no longer supported. Instead of using
      aliases, please specify multiple google.api.Endpoint for each of the
      intended aliases.  Additional names that this endpoint will be hosted
      on.
    allowCors: Allowing [CORS](https://en.wikipedia.org/wiki/Cross-
      origin_resource_sharing), aka cross-domain traffic, would allow the
      backends served from this endpoint to receive and respond to HTTP
      OPTIONS requests. The response will be used by the browser to determine
      whether the subsequent cross-origin request is allowed to proceed.
    features: The list of features enabled on this endpoint.
    name: The canonical name of this endpoint.
    target: The specification of an Internet routable address of API frontend
      that will handle requests to this [API
      Endpoint](https://cloud.google.com/apis/design/glossary). It should be
      either a valid IPv4 address or a fully-qualified domain name. For
      example, "*******" or "myservice.appspot.com".
  """

  aliases = _messages.StringField(1, repeated=True)
  allowCors = _messages.BooleanField(2)
  features = _messages.StringField(3, repeated=True)
  name = _messages.StringField(4)
  target = _messages.StringField(5)


class Enum(_messages.Message):
  r"""Enum type definition.

  Enums:
    SyntaxValueValuesEnum: The source syntax.

  Fields:
    enumvalue: Enum value definitions.
    name: Enum type name.
    options: Protocol buffer options.
    sourceContext: The source context.
    syntax: The source syntax.
  """

  class SyntaxValueValuesEnum(_messages.Enum):
    r"""The source syntax.

    Values:
      SYNTAX_PROTO2: Syntax `proto2`.
      SYNTAX_PROTO3: Syntax `proto3`.
    """
    SYNTAX_PROTO2 = 0
    SYNTAX_PROTO3 = 1

  enumvalue = _messages.MessageField('EnumValue', 1, repeated=True)
  name = _messages.StringField(2)
  options = _messages.MessageField('Option', 3, repeated=True)
  sourceContext = _messages.MessageField('SourceContext', 4)
  syntax = _messages.EnumField('SyntaxValueValuesEnum', 5)


class EnumValue(_messages.Message):
  r"""Enum value definition.

  Fields:
    name: Enum value name.
    number: Enum value number.
    options: Protocol buffer options.
  """

  name = _messages.StringField(1)
  number = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  options = _messages.MessageField('Option', 3, repeated=True)


class Experimental(_messages.Message):
  r"""Experimental service configuration. These configuration options can only
  be used by whitelisted users.

  Fields:
    authorization: Authorization configuration.
  """

  authorization = _messages.MessageField('AuthorizationConfig', 1)


class Field(_messages.Message):
  r"""A single field of a message type.

  Enums:
    CardinalityValueValuesEnum: The field cardinality.
    KindValueValuesEnum: The field type.

  Fields:
    cardinality: The field cardinality.
    defaultValue: The string value of the default value of this field. Proto2
      syntax only.
    jsonName: The field JSON name.
    kind: The field type.
    name: The field name.
    number: The field number.
    oneofIndex: The index of the field type in `Type.oneofs`, for message or
      enumeration types. The first type has index 1; zero means the type is
      not in the list.
    options: The protocol buffer options.
    packed: Whether to use alternative packed wire representation.
    typeUrl: The field type URL, without the scheme, for message or
      enumeration types. Example:
      `"type.googleapis.com/google.protobuf.Timestamp"`.
  """

  class CardinalityValueValuesEnum(_messages.Enum):
    r"""The field cardinality.

    Values:
      CARDINALITY_UNKNOWN: For fields with unknown cardinality.
      CARDINALITY_OPTIONAL: For optional fields.
      CARDINALITY_REQUIRED: For required fields. Proto2 syntax only.
      CARDINALITY_REPEATED: For repeated fields.
    """
    CARDINALITY_UNKNOWN = 0
    CARDINALITY_OPTIONAL = 1
    CARDINALITY_REQUIRED = 2
    CARDINALITY_REPEATED = 3

  class KindValueValuesEnum(_messages.Enum):
    r"""The field type.

    Values:
      TYPE_UNKNOWN: Field type unknown.
      TYPE_DOUBLE: Field type double.
      TYPE_FLOAT: Field type float.
      TYPE_INT64: Field type int64.
      TYPE_UINT64: Field type uint64.
      TYPE_INT32: Field type int32.
      TYPE_FIXED64: Field type fixed64.
      TYPE_FIXED32: Field type fixed32.
      TYPE_BOOL: Field type bool.
      TYPE_STRING: Field type string.
      TYPE_GROUP: Field type group. Proto2 syntax only, and deprecated.
      TYPE_MESSAGE: Field type message.
      TYPE_BYTES: Field type bytes.
      TYPE_UINT32: Field type uint32.
      TYPE_ENUM: Field type enum.
      TYPE_SFIXED32: Field type sfixed32.
      TYPE_SFIXED64: Field type sfixed64.
      TYPE_SINT32: Field type sint32.
      TYPE_SINT64: Field type sint64.
    """
    TYPE_UNKNOWN = 0
    TYPE_DOUBLE = 1
    TYPE_FLOAT = 2
    TYPE_INT64 = 3
    TYPE_UINT64 = 4
    TYPE_INT32 = 5
    TYPE_FIXED64 = 6
    TYPE_FIXED32 = 7
    TYPE_BOOL = 8
    TYPE_STRING = 9
    TYPE_GROUP = 10
    TYPE_MESSAGE = 11
    TYPE_BYTES = 12
    TYPE_UINT32 = 13
    TYPE_ENUM = 14
    TYPE_SFIXED32 = 15
    TYPE_SFIXED64 = 16
    TYPE_SINT32 = 17
    TYPE_SINT64 = 18

  cardinality = _messages.EnumField('CardinalityValueValuesEnum', 1)
  defaultValue = _messages.StringField(2)
  jsonName = _messages.StringField(3)
  kind = _messages.EnumField('KindValueValuesEnum', 4)
  name = _messages.StringField(5)
  number = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  oneofIndex = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  options = _messages.MessageField('Option', 8, repeated=True)
  packed = _messages.BooleanField(9)
  typeUrl = _messages.StringField(10)


class Http(_messages.Message):
  r"""Defines the HTTP configuration for an API service. It contains a list of
  HttpRule, each specifying the mapping of an RPC method to one or more HTTP
  REST API methods.

  Fields:
    fullyDecodeReservedExpansion: When set to true, URL path parmeters will be
      fully URI-decoded except in cases of single segment matches in reserved
      expansion, where "%2F" will be left encoded.  The default behavior is to
      not decode RFC 6570 reserved characters in multi segment matches.
    rules: A list of HTTP configuration rules that apply to individual API
      methods.  **NOTE:** All service configuration rules follow "last one
      wins" order.
  """

  fullyDecodeReservedExpansion = _messages.BooleanField(1)
  rules = _messages.MessageField('HttpRule', 2, repeated=True)


class HttpRule(_messages.Message):
  r"""`HttpRule` defines the mapping of an RPC method to one or more HTTP REST
  API methods. The mapping specifies how different portions of the RPC request
  message are mapped to URL path, URL query parameters, and HTTP request body.
  The mapping is typically specified as an `google.api.http` annotation on the
  RPC method, see "google/api/annotations.proto" for details.  The mapping
  consists of a field specifying the path template and method kind.  The path
  template can refer to fields in the request message, as in the example below
  which describes a REST GET operation on a resource collection of messages:
  service Messaging {       rpc GetMessage(GetMessageRequest) returns
  (Message) {         option (google.api.http).get =
  "/v1/messages/{message_id}/{sub.subfield}";       }     }     message
  GetMessageRequest {       message SubMessage {         string subfield = 1;
  }       string message_id = 1; // mapped to the URL       SubMessage sub =
  2;    // `sub.subfield` is url-mapped     }     message Message {
  string text = 1; // content of the resource     }  The same http annotation
  can alternatively be expressed inside the `GRPC API Configuration` YAML
  file.      http:       rules:         - selector:
  <proto_package_name>.Messaging.GetMessage           get:
  /v1/messages/{message_id}/{sub.subfield}  This definition enables an
  automatic, bidrectional mapping of HTTP JSON to RPC. Example:  HTTP | RPC
  -----|----- `GET /v1/messages/123456/foo`  | `GetMessage(message_id:
  "123456" sub: SubMessage(subfield: "foo"))`  In general, not only fields but
  also field paths can be referenced from a path pattern. Fields mapped to the
  path pattern cannot be repeated and must have a primitive (non-message)
  type.  Any fields in the request message which are not bound by the path
  pattern automatically become (optional) HTTP query parameters. Assume the
  following definition of the request message:       service Messaging {
  rpc GetMessage(GetMessageRequest) returns (Message) {         option
  (google.api.http).get = "/v1/messages/{message_id}";       }     }
  message GetMessageRequest {       message SubMessage {         string
  subfield = 1;       }       string message_id = 1; // mapped to the URL
  int64 revision = 2;    // becomes a parameter       SubMessage sub = 3;
  // `sub.subfield` becomes a parameter     }   This enables a HTTP JSON to
  RPC mapping as below:  HTTP | RPC -----|----- `GET
  /v1/messages/123456?revision=2&sub.subfield=foo` | `GetMessage(message_id:
  "123456" revision: 2 sub: SubMessage(subfield: "foo"))`  Note that fields
  which are mapped to HTTP parameters must have a primitive type or a repeated
  primitive type. Message types are not allowed. In the case of a repeated
  type, the parameter can be repeated in the URL, as in `...?param=A&param=B`.
  For HTTP method kinds which allow a request body, the `body` field specifies
  the mapping. Consider a REST update method on the message resource
  collection:       service Messaging {       rpc
  UpdateMessage(UpdateMessageRequest) returns (Message) {         option
  (google.api.http) = {           put: "/v1/messages/{message_id}"
  body: "message"         };       }     }     message UpdateMessageRequest {
  string message_id = 1; // mapped to the URL       Message message = 2;   //
  mapped to the body     }   The following HTTP JSON to RPC mapping is
  enabled, where the representation of the JSON in the request body is
  determined by protos JSON encoding:  HTTP | RPC -----|----- `PUT
  /v1/messages/123456 { "text": "Hi!" }` | `UpdateMessage(message_id: "123456"
  message { text: "Hi!" })`  The special name `*` can be used in the body
  mapping to define that every field not bound by the path template should be
  mapped to the request body.  This enables the following alternative
  definition of the update method:      service Messaging {       rpc
  UpdateMessage(Message) returns (Message) {         option (google.api.http)
  = {           put: "/v1/messages/{message_id}"           body: "*"
  };       }     }     message Message {       string message_id = 1;
  string text = 2;     }   The following HTTP JSON to RPC mapping is enabled:
  HTTP | RPC -----|----- `PUT /v1/messages/123456 { "text": "Hi!" }` |
  `UpdateMessage(message_id: "123456" text: "Hi!")`  Note that when using `*`
  in the body mapping, it is not possible to have HTTP parameters, as all
  fields not bound by the path end in the body. This makes this option more
  rarely used in practice of defining REST APIs. The common usage of `*` is in
  custom methods which don't use the URL at all for transferring data.  It is
  possible to define multiple HTTP methods for one RPC by using the
  `additional_bindings` option. Example:      service Messaging {       rpc
  GetMessage(GetMessageRequest) returns (Message) {         option
  (google.api.http) = {           get: "/v1/messages/{message_id}"
  additional_bindings {             get:
  "/v1/users/{user_id}/messages/{message_id}"           }         };       }
  }     message GetMessageRequest {       string message_id = 1;       string
  user_id = 2;     }   This enables the following two alternative HTTP JSON to
  RPC mappings:  HTTP | RPC -----|----- `GET /v1/messages/123456` |
  `GetMessage(message_id: "123456")` `GET /v1/users/me/messages/123456` |
  `GetMessage(user_id: "me" message_id: "123456")`  # Rules for HTTP mapping
  The rules for mapping HTTP path, query parameters, and body fields to the
  request message are as follows:  1. The `body` field specifies either `*` or
  a field path, or is    omitted. If omitted, it indicates there is no HTTP
  request body. 2. Leaf fields (recursive expansion of nested messages in the
  request) can be classified into three types:     (a) Matched in the URL
  template.     (b) Covered by body (if body is `*`, everything except (a)
  fields;         else everything under the body field)     (c) All other
  fields. 3. URL query parameters found in the HTTP request are mapped to (c)
  fields. 4. Any body sent with an HTTP request can contain only (b) fields.
  The syntax of the path template is as follows:      Template = "/" Segments
  [ Verb ] ;     Segments = Segment { "/" Segment } ;     Segment  = "*" |
  "**" | LITERAL | Variable ;     Variable = "{" FieldPath [ "=" Segments ]
  "}" ;     FieldPath = IDENT { "." IDENT } ;     Verb     = ":" LITERAL ;
  The syntax `*` matches a single path segment. The syntax `**` matches zero
  or more path segments, which must be the last part of the path except the
  `Verb`. The syntax `LITERAL` matches literal text in the path.  The syntax
  `Variable` matches part of the URL path as specified by its template. A
  variable template must not contain other variables. If a variable matches a
  single path segment, its template may be omitted, e.g. `{var}` is equivalent
  to `{var=*}`.  If a variable contains exactly one path segment, such as
  `"{var}"` or `"{var=*}"`, when such a variable is expanded into a URL path,
  all characters except `[-_.~0-9a-zA-Z]` are percent-encoded. Such variables
  show up in the Discovery Document as `{var}`.  If a variable contains one or
  more path segments, such as `"{var=foo/*}"` or `"{var=**}"`, when such a
  variable is expanded into a URL path, all characters except
  `[-_.~/0-9a-zA-Z]` are percent-encoded. Such variables show up in the
  Discovery Document as `{+var}`.  NOTE: While the single segment variable
  matches the semantics of [RFC 6570](https://tools.ietf.org/html/rfc6570)
  Section 3.2.2 Simple String Expansion, the multi segment variable **does
  not** match RFC 6570 Reserved Expansion. The reason is that the Reserved
  Expansion does not expand special characters like `?` and `#`, which would
  lead to invalid URLs.  NOTE: the field paths in variables and in the `body`
  must not refer to repeated fields or map fields.

  Fields:
    additionalBindings: Additional HTTP bindings for the selector. Nested
      bindings must not contain an `additional_bindings` field themselves
      (that is, the nesting may only be one level deep).
    authorizations: Specifies the permission(s) required for an API element
      for the overall API request to succeed. It is typically used to mark
      request message fields that contain the name of the resource and
      indicates the permissions that will be checked on that resource.
    body: The name of the request field whose value is mapped to the HTTP
      body, or `*` for mapping all fields not captured by the path pattern to
      the HTTP body. NOTE: the referred field must not be a repeated field and
      must be present at the top-level of request message type.
    custom: The custom pattern is used for specifying an HTTP method that is
      not included in the `pattern` field, such as HEAD, or "*" to leave the
      HTTP method unspecified for this rule. The wild-card rule is useful for
      services that provide content to Web (HTML) clients.
    delete: Used for deleting a resource.
    get: Used for listing and getting information about resources.
    mediaDownload: Use this only for Scotty Requests. Do not use this for
      bytestream methods. For media support, add instead
      [][google.bytestream.RestByteStream] as an API to your configuration.
    mediaUpload: Use this only for Scotty Requests. Do not use this for media
      support using Bytestream, add instead
      [][google.bytestream.RestByteStream] as an API to your configuration for
      Bytestream methods.
    patch: Used for updating a resource.
    post: Used for creating a resource.
    put: Used for updating a resource.
    restCollection: DO NOT USE. This is an experimental field.  Optional. The
      REST collection name is by default derived from the URL pattern. If
      specified, this field overrides the default collection name. Example:
      rpc AddressesAggregatedList(AddressesAggregatedListRequest)
      returns (AddressesAggregatedListResponse) {       option
      (google.api.http) = {         get:
      "/v1/projects/{project_id}/aggregated/addresses"
      rest_collection: "projects.addresses"       };     }  This method has
      the automatically derived collection name "projects.aggregated".
      Because, semantically, this rpc is actually an operation on the
      "projects.addresses" collection, the `rest_collection` field is
      configured to override the derived collection name.
    restMethodName: DO NOT USE. This is an experimental field.  Optional. The
      rest method name is by default derived from the URL pattern. If
      specified, this field overrides the default method name. Example:
      rpc CreateResource(CreateResourceRequest)         returns
      (CreateResourceResponse) {       option (google.api.http) = {
      post: "/v1/resources",         body: "resource",
      rest_method_name: "insert"       };     }  This method has the
      automatically derived rest method name "create", but for backwards
      compatibility with apiary, it is specified as insert.
    selector: Selects methods to which this rule applies.  Refer to selector
      for syntax details.
  """

  additionalBindings = _messages.MessageField('HttpRule', 1, repeated=True)
  authorizations = _messages.MessageField('AuthorizationRule', 2, repeated=True)
  body = _messages.StringField(3)
  custom = _messages.MessageField('CustomHttpPattern', 4)
  delete = _messages.StringField(5)
  get = _messages.StringField(6)
  mediaDownload = _messages.MessageField('MediaDownload', 7)
  mediaUpload = _messages.MessageField('MediaUpload', 8)
  patch = _messages.StringField(9)
  post = _messages.StringField(10)
  put = _messages.StringField(11)
  restCollection = _messages.StringField(12)
  restMethodName = _messages.StringField(13)
  selector = _messages.StringField(14)


class LabelDescriptor(_messages.Message):
  r"""A description of a label.

  Enums:
    ValueTypeValueValuesEnum: The type of data that can be assigned to the
      label.

  Fields:
    description: A human-readable description for the label.
    key: The label key.
    valueType: The type of data that can be assigned to the label.
  """

  class ValueTypeValueValuesEnum(_messages.Enum):
    r"""The type of data that can be assigned to the label.

    Values:
      STRING: A variable-length string. This is the default.
      BOOL: Boolean; true or false.
      INT64: A 64-bit signed integer.
    """
    STRING = 0
    BOOL = 1
    INT64 = 2

  description = _messages.StringField(1)
  key = _messages.StringField(2)
  valueType = _messages.EnumField('ValueTypeValueValuesEnum', 3)


class ListConnectionsResponse(_messages.Message):
  r"""ListConnectionsResponse is the response to list peering states for the
  given service and consumer project.

  Fields:
    connections: The list of Connections.
  """

  connections = _messages.MessageField('Connection', 1, repeated=True)


class LogDescriptor(_messages.Message):
  r"""A description of a log type. Example in YAML format:      - name:
  library.googleapis.com/activity_history       description: The history of
  borrowing and returning library items.       display_name: Activity
  labels:       - key: /customer_id         description: Identifier of a
  library customer

  Fields:
    description: A human-readable description of this log. This information
      appears in the documentation and can contain details.
    displayName: The human-readable name for this log. This information
      appears on the user interface and should be concise.
    labels: The set of labels that are available to describe a specific log
      entry. Runtime requests that contain labels not specified here are
      considered invalid.
    name: The name of the log. It must be less than 512 characters long and
      can include the following characters: upper- and lower-case alphanumeric
      characters [A-Za-z0-9], and punctuation characters including slash,
      underscore, hyphen, period [/_-.].
  """

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  labels = _messages.MessageField('LabelDescriptor', 3, repeated=True)
  name = _messages.StringField(4)


class Logging(_messages.Message):
  r"""Logging configuration of the service.  The following example shows how
  to configure logs to be sent to the producer and consumer projects. In the
  example, the `activity_history` log is sent to both the producer and
  consumer projects, whereas the `purchase_history` log is only sent to the
  producer project.      monitored_resources:     - type:
  library.googleapis.com/branch       labels:       - key: /city
  description: The city where the library branch is located in.       - key:
  /name         description: The name of the branch.     logs:     - name:
  activity_history       labels:       - key: /customer_id     - name:
  purchase_history     logging:       producer_destinations:       -
  monitored_resource: library.googleapis.com/branch         logs:         -
  activity_history         - purchase_history       consumer_destinations:
  - monitored_resource: library.googleapis.com/branch         logs:         -
  activity_history

  Fields:
    consumerDestinations: Logging configurations for sending logs to the
      consumer project. There can be multiple consumer destinations, each one
      must have a different monitored resource type. A log can be used in at
      most one consumer destination.
    producerDestinations: Logging configurations for sending logs to the
      producer project. There can be multiple producer destinations, each one
      must have a different monitored resource type. A log can be used in at
      most one producer destination.
  """

  consumerDestinations = _messages.MessageField('LoggingDestination', 1, repeated=True)
  producerDestinations = _messages.MessageField('LoggingDestination', 2, repeated=True)


class LoggingDestination(_messages.Message):
  r"""Configuration of a specific logging destination (the producer project or
  the consumer project).

  Fields:
    logs: Names of the logs to be sent to this destination. Each name must be
      defined in the Service.logs section. If the log name is not a domain
      scoped name, it will be automatically prefixed with the service name
      followed by "/".
    monitoredResource: The monitored resource type. The type must be defined
      in the Service.monitored_resources section.
  """

  logs = _messages.StringField(1, repeated=True)
  monitoredResource = _messages.StringField(2)


class MediaDownload(_messages.Message):
  r"""Defines the Media configuration for a service in case of a download. Use
  this only for Scotty Requests. Do not use this for media support using
  Bytestream, add instead [][google.bytestream.RestByteStream] as an API to
  your configuration for Bytestream methods.

  Fields:
    completeNotification: A boolean that determines whether a notification for
      the completion of a download should be sent to the backend.
    downloadService: DO NOT USE FIELDS BELOW THIS LINE UNTIL THIS WARNING IS
      REMOVED.  Specify name of the download service if one is used for
      download.
    dropzone: Name of the Scotty dropzone to use for the current API.
    enabled: Whether download is enabled.
    maxDirectDownloadSize: Optional maximum acceptable size for direct
      download. The size is specified in bytes.
    useDirectDownload: A boolean that determines if direct download from ESF
      should be used for download of this media.
  """

  completeNotification = _messages.BooleanField(1)
  downloadService = _messages.StringField(2)
  dropzone = _messages.StringField(3)
  enabled = _messages.BooleanField(4)
  maxDirectDownloadSize = _messages.IntegerField(5)
  useDirectDownload = _messages.BooleanField(6)


class MediaUpload(_messages.Message):
  r"""Defines the Media configuration for a service in case of an upload. Use
  this only for Scotty Requests. Do not use this for media support using
  Bytestream, add instead [][google.bytestream.RestByteStream] as an API to
  your configuration for Bytestream methods.

  Fields:
    completeNotification: A boolean that determines whether a notification for
      the completion of an upload should be sent to the backend. These
      notifications will not be seen by the client and will not consume quota.
    dropzone: Name of the Scotty dropzone to use for the current API.
    enabled: Whether upload is enabled.
    maxSize: Optional maximum acceptable size for an upload. The size is
      specified in bytes.
    mimeTypes: An array of mimetype patterns. Esf will only accept uploads
      that match one of the given patterns.
    progressNotification: Whether to receive a notification for progress
      changes of media upload.
    startNotification: Whether to receive a notification on the start of media
      upload.
    uploadService: DO NOT USE FIELDS BELOW THIS LINE UNTIL THIS WARNING IS
      REMOVED.  Specify name of the upload service if one is used for upload.
  """

  completeNotification = _messages.BooleanField(1)
  dropzone = _messages.StringField(2)
  enabled = _messages.BooleanField(3)
  maxSize = _messages.IntegerField(4)
  mimeTypes = _messages.StringField(5, repeated=True)
  progressNotification = _messages.BooleanField(6)
  startNotification = _messages.BooleanField(7)
  uploadService = _messages.StringField(8)


class Method(_messages.Message):
  r"""Method represents a method of an API interface.

  Enums:
    SyntaxValueValuesEnum: The source syntax of this method.

  Fields:
    name: The simple name of this method.
    options: Any metadata attached to the method.
    requestStreaming: If true, the request is streamed.
    requestTypeUrl: A URL of the input message type.
    responseStreaming: If true, the response is streamed.
    responseTypeUrl: The URL of the output message type.
    syntax: The source syntax of this method.
  """

  class SyntaxValueValuesEnum(_messages.Enum):
    r"""The source syntax of this method.

    Values:
      SYNTAX_PROTO2: Syntax `proto2`.
      SYNTAX_PROTO3: Syntax `proto3`.
    """
    SYNTAX_PROTO2 = 0
    SYNTAX_PROTO3 = 1

  name = _messages.StringField(1)
  options = _messages.MessageField('Option', 2, repeated=True)
  requestStreaming = _messages.BooleanField(3)
  requestTypeUrl = _messages.StringField(4)
  responseStreaming = _messages.BooleanField(5)
  responseTypeUrl = _messages.StringField(6)
  syntax = _messages.EnumField('SyntaxValueValuesEnum', 7)


class MetricDescriptor(_messages.Message):
  r"""Defines a metric type and its schema. Once a metric descriptor is
  created, deleting or altering it stops data collection and makes the metric
  type's existing data unusable.

  Enums:
    MetricKindValueValuesEnum: Whether the metric records instantaneous
      values, changes to a value, etc. Some combinations of `metric_kind` and
      `value_type` might not be supported.
    ValueTypeValueValuesEnum: Whether the measurement is an integer, a
      floating-point number, etc. Some combinations of `metric_kind` and
      `value_type` might not be supported.

  Fields:
    description: A detailed description of the metric, which can be used in
      documentation.
    displayName: A concise name for the metric, which can be displayed in user
      interfaces. Use sentence case without an ending period, for example
      "Request count". This field is optional but it is recommended to be set
      for any metrics associated with user-visible concepts, such as Quota.
    labels: The set of labels that can be used to describe a specific instance
      of this metric type. For example, the
      `appengine.googleapis.com/http/server/response_latencies` metric type
      has a label for the HTTP response code, `response_code`, so you can look
      at latencies for successful responses or just for responses that failed.
    metadata: Optional. Metadata which can be used to guide usage of the
      metric.
    metricKind: Whether the metric records instantaneous values, changes to a
      value, etc. Some combinations of `metric_kind` and `value_type` might
      not be supported.
    name: The resource name of the metric descriptor.
    type: The metric type, including its DNS name prefix. The type is not URL-
      encoded.  All user-defined custom metric types have the DNS name
      `custom.googleapis.com`.  Metric types should use a natural hierarchical
      grouping. For example:      "custom.googleapis.com/invoice/paid/amount"
      "appengine.googleapis.com/http/server/response_latencies"
    unit: The unit in which the metric value is reported. It is only
      applicable if the `value_type` is `INT64`, `DOUBLE`, or `DISTRIBUTION`.
      The supported units are a subset of [The Unified Code for Units of
      Measure](http://unitsofmeasure.org/ucum.html) standard:  **Basic units
      (UNIT)**  * `bit`   bit * `By`    byte * `s`     second * `min`   minute
      * `h`     hour * `d`     day  **Prefixes (PREFIX)**  * `k`     kilo
      (10**3) * `M`     mega    (10**6) * `G`     giga    (10**9) * `T`
      tera    (10**12) * `P`     peta    (10**15) * `E`     exa     (10**18) *
      `Z`     zetta   (10**21) * `Y`     yotta   (10**24) * `m`     milli
      (10**-3) * `u`     micro   (10**-6) * `n`     nano    (10**-9) * `p`
      pico    (10**-12) * `f`     femto   (10**-15) * `a`     atto
      (10**-18) * `z`     zepto   (10**-21) * `y`     yocto   (10**-24) * `Ki`
      kibi    (2**10) * `Mi`    mebi    (2**20) * `Gi`    gibi    (2**30) *
      `Ti`    tebi    (2**40)  **Grammar**  The grammar also includes these
      connectors:  * `/`    division (as an infix operator, e.g. `1/s`). * `.`
      multiplication (as an infix operator, e.g. `GBy.d`)  The grammar for a
      unit is as follows:      Expression = Component { "." Component } { "/"
      Component } ;      Component = ( [ PREFIX ] UNIT | "%" ) [ Annotation ]
      | Annotation               | "1"               ;      Annotation = "{"
      NAME "}" ;  Notes:  * `Annotation` is just a comment if it follows a
      `UNIT` and is    equivalent to `1` if it is used alone. For examples,
      `{requests}/s == 1/s`, `By{transmitted}/s == By/s`. * `NAME` is a
      sequence of non-blank printable ASCII characters not    containing '{'
      or '}'. * `1` represents dimensionless value 1, such as in `1/s`. * `%`
      represents dimensionless value 1/100, and annotates values giving    a
      percentage.
    valueType: Whether the measurement is an integer, a floating-point number,
      etc. Some combinations of `metric_kind` and `value_type` might not be
      supported.
  """

  class MetricKindValueValuesEnum(_messages.Enum):
    r"""Whether the metric records instantaneous values, changes to a value,
    etc. Some combinations of `metric_kind` and `value_type` might not be
    supported.

    Values:
      METRIC_KIND_UNSPECIFIED: Do not use this default value.
      GAUGE: An instantaneous measurement of a value.
      DELTA: The change in a value during a time interval.
      CUMULATIVE: A value accumulated over a time interval.  Cumulative
        measurements in a time series should have the same start time and
        increasing end times, until an event resets the cumulative value to
        zero and sets a new start time for the following points.
    """
    METRIC_KIND_UNSPECIFIED = 0
    GAUGE = 1
    DELTA = 2
    CUMULATIVE = 3

  class ValueTypeValueValuesEnum(_messages.Enum):
    r"""Whether the measurement is an integer, a floating-point number, etc.
    Some combinations of `metric_kind` and `value_type` might not be
    supported.

    Values:
      VALUE_TYPE_UNSPECIFIED: Do not use this default value.
      BOOL: The value is a boolean. This value type can be used only if the
        metric kind is `GAUGE`.
      INT64: The value is a signed 64-bit integer.
      DOUBLE: The value is a double precision floating point number.
      STRING: The value is a text string. This value type can be used only if
        the metric kind is `GAUGE`.
      DISTRIBUTION: The value is a `Distribution`.
      MONEY: The value is money.
    """
    VALUE_TYPE_UNSPECIFIED = 0
    BOOL = 1
    INT64 = 2
    DOUBLE = 3
    STRING = 4
    DISTRIBUTION = 5
    MONEY = 6

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  labels = _messages.MessageField('LabelDescriptor', 3, repeated=True)
  metadata = _messages.MessageField('MetricDescriptorMetadata', 4)
  metricKind = _messages.EnumField('MetricKindValueValuesEnum', 5)
  name = _messages.StringField(6)
  type = _messages.StringField(7)
  unit = _messages.StringField(8)
  valueType = _messages.EnumField('ValueTypeValueValuesEnum', 9)


class MetricDescriptorMetadata(_messages.Message):
  r"""Additional annotations that can be used to guide the usage of a metric.

  Enums:
    LaunchStageValueValuesEnum: The launch stage of the metric definition.

  Fields:
    ingestDelay: The delay of data points caused by ingestion. Data points
      older than this age are guaranteed to be ingested and available to be
      read, excluding data loss due to errors.
    launchStage: The launch stage of the metric definition.
    samplePeriod: The sampling period of metric data points. For metrics which
      are written periodically, consecutive data points are stored at this
      time interval, excluding data loss due to errors. Metrics with a higher
      granularity have a smaller sampling period.
  """

  class LaunchStageValueValuesEnum(_messages.Enum):
    r"""The launch stage of the metric definition.

    Values:
      LAUNCH_STAGE_UNSPECIFIED: Do not use this default value.
      EARLY_ACCESS: Early Access features are limited to a closed group of
        testers. To use these features, you must sign up in advance and sign a
        Trusted Tester agreement (which includes confidentiality provisions).
        These features may be unstable, changed in backward-incompatible ways,
        and are not guaranteed to be released.
      ALPHA: Alpha is a limited availability test for releases before they are
        cleared for widespread use. By Alpha, all significant design issues
        are resolved and we are in the process of verifying functionality.
        Alpha customers need to apply for access, agree to applicable terms,
        and have their projects whitelisted. Alpha releases don't have to be
        feature complete, no SLAs are provided, and there are no technical
        support obligations, but they will be far enough along that customers
        can actually use them in test environments or for limited-use tests --
        just like they would in normal production cases.
      BETA: Beta is the point at which we are ready to open a release for any
        customer to use. There are no SLA or technical support obligations in
        a Beta release. Products will be complete from a feature perspective,
        but may have some open outstanding issues. Beta releases are suitable
        for limited production use cases.
      GA: GA features are open to all developers and are considered stable and
        fully qualified for production use.
      DEPRECATED: Deprecated features are scheduled to be shut down and
        removed. For more information, see the "Deprecation Policy" section of
        our [Terms of Service](https://cloud.google.com/terms/) and the
        [Google Cloud Platform Subject to the Deprecation
        Policy](https://cloud.google.com/terms/deprecation) documentation.
    """
    LAUNCH_STAGE_UNSPECIFIED = 0
    EARLY_ACCESS = 1
    ALPHA = 2
    BETA = 3
    GA = 4
    DEPRECATED = 5

  ingestDelay = _messages.StringField(1)
  launchStage = _messages.EnumField('LaunchStageValueValuesEnum', 2)
  samplePeriod = _messages.StringField(3)


class MetricRule(_messages.Message):
  r"""Bind API methods to metrics. Binding a method to a metric causes that
  metric's configured quota behaviors to apply to the method call.

  Messages:
    MetricCostsValue: Metrics to update when the selected methods are called,
      and the associated cost applied to each metric.  The key of the map is
      the metric name, and the values are the amount increased for the metric
      against which the quota limits are defined. The value must not be
      negative.

  Fields:
    metricCosts: Metrics to update when the selected methods are called, and
      the associated cost applied to each metric.  The key of the map is the
      metric name, and the values are the amount increased for the metric
      against which the quota limits are defined. The value must not be
      negative.
    selector: Selects the methods to which this rule applies.  Refer to
      selector for syntax details.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetricCostsValue(_messages.Message):
    r"""Metrics to update when the selected methods are called, and the
    associated cost applied to each metric.  The key of the map is the metric
    name, and the values are the amount increased for the metric against which
    the quota limits are defined. The value must not be negative.

    Messages:
      AdditionalProperty: An additional property for a MetricCostsValue
        object.

    Fields:
      additionalProperties: Additional properties of type MetricCostsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetricCostsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.IntegerField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  metricCosts = _messages.MessageField('MetricCostsValue', 1)
  selector = _messages.StringField(2)


class Mixin(_messages.Message):
  r"""Declares an API Interface to be included in this interface. The
  including interface must redeclare all the methods from the included
  interface, but documentation and options are inherited as follows:  - If
  after comment and whitespace stripping, the documentation   string of the
  redeclared method is empty, it will be inherited   from the original method.
  - Each annotation belonging to the service config (http,   visibility) which
  is not set in the redeclared method will be   inherited.  - If an http
  annotation is inherited, the path pattern will be   modified as follows. Any
  version prefix will be replaced by the   version of the including interface
  plus the root path if   specified.  Example of a simple mixin:      package
  google.acl.v1;     service AccessControl {       // Get the underlying ACL
  object.       rpc GetAcl(GetAclRequest) returns (Acl) {         option
  (google.api.http).get = "/v1/{resource=**}:getAcl";       }     }
  package google.storage.v2;     service Storage {       //       rpc
  GetAcl(GetAclRequest) returns (Acl);        // Get a data record.       rpc
  GetData(GetDataRequest) returns (Data) {         option
  (google.api.http).get = "/v2/{resource=**}";       }     }  Example of a
  mixin configuration:      apis:     - name: google.storage.v2.Storage
  mixins:       - name: google.acl.v1.AccessControl  The mixin construct
  implies that all methods in `AccessControl` are also declared with same name
  and request/response types in `Storage`. A documentation generator or
  annotation processor will see the effective `Storage.GetAcl` method after
  inherting documentation and annotations as follows:      service Storage {
  // Get the underlying ACL object.       rpc GetAcl(GetAclRequest) returns
  (Acl) {         option (google.api.http).get = "/v2/{resource=**}:getAcl";
  }       ...     }  Note how the version in the path pattern changed from
  `v1` to `v2`.  If the `root` field in the mixin is specified, it should be a
  relative path under which inherited HTTP paths are placed. Example:
  apis:     - name: google.storage.v2.Storage       mixins:       - name:
  google.acl.v1.AccessControl         root: acls  This implies the following
  inherited HTTP annotation:      service Storage {       // Get the
  underlying ACL object.       rpc GetAcl(GetAclRequest) returns (Acl) {
  option (google.api.http).get = "/v2/acls/{resource=**}:getAcl";       }
  ...     }

  Fields:
    name: The fully qualified name of the interface which is included.
    root: If non-empty specifies a path under which inherited HTTP paths are
      rooted.
  """

  name = _messages.StringField(1)
  root = _messages.StringField(2)


class MonitoredResourceDescriptor(_messages.Message):
  r"""An object that describes the schema of a MonitoredResource object using
  a type name and a set of labels.  For example, the monitored resource
  descriptor for Google Compute Engine VM instances has a type of
  `"gce_instance"` and specifies the use of the labels `"instance_id"` and
  `"zone"` to identify particular VM instances.  Different APIs can support
  different monitored resource types. APIs generally provide a `list` method
  that returns the monitored resource descriptors used by the API.

  Fields:
    description: Optional. A detailed description of the monitored resource
      type that might be used in documentation.
    displayName: Optional. A concise name for the monitored resource type that
      might be displayed in user interfaces. It should be a Title Cased Noun
      Phrase, without any article or other determiners. For example, `"Google
      Cloud SQL Database"`.
    labels: Required. A set of labels used to describe instances of this
      monitored resource type. For example, an individual Google Cloud SQL
      database is identified by values for the labels `"database_id"` and
      `"zone"`.
    name: Optional. The resource name of the monitored resource descriptor:
      `"projects/{project_id}/monitoredResourceDescriptors/{type}"` where
      {type} is the value of the `type` field in this object and {project_id}
      is a project ID that provides API-specific context for accessing the
      type.  APIs that do not use project information can use the resource
      name format `"monitoredResourceDescriptors/{type}"`.
    type: Required. The monitored resource type. For example, the type
      `"cloudsql_database"` represents databases in Google Cloud SQL. The
      maximum length of this value is 256 characters.
  """

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  labels = _messages.MessageField('LabelDescriptor', 3, repeated=True)
  name = _messages.StringField(4)
  type = _messages.StringField(5)


class Monitoring(_messages.Message):
  r"""Monitoring configuration of the service.  The example below shows how to
  configure monitored resources and metrics for monitoring. In the example, a
  monitored resource and two metrics are defined. The
  `library.googleapis.com/book/returned_count` metric is sent to both producer
  and consumer projects, whereas the
  `library.googleapis.com/book/overdue_count` metric is only sent to the
  consumer project.      monitored_resources:     - type:
  library.googleapis.com/branch       labels:       - key: /city
  description: The city where the library branch is located in.       - key:
  /name         description: The name of the branch.     metrics:     - name:
  library.googleapis.com/book/returned_count       metric_kind: DELTA
  value_type: INT64       labels:       - key: /customer_id     - name:
  library.googleapis.com/book/overdue_count       metric_kind: GAUGE
  value_type: INT64       labels:       - key: /customer_id     monitoring:
  producer_destinations:       - monitored_resource:
  library.googleapis.com/branch         metrics:         -
  library.googleapis.com/book/returned_count       consumer_destinations:
  - monitored_resource: library.googleapis.com/branch         metrics:
  - library.googleapis.com/book/returned_count         -
  library.googleapis.com/book/overdue_count

  Fields:
    consumerDestinations: Monitoring configurations for sending metrics to the
      consumer project. There can be multiple consumer destinations, each one
      must have a different monitored resource type. A metric can be used in
      at most one consumer destination.
    producerDestinations: Monitoring configurations for sending metrics to the
      producer project. There can be multiple producer destinations, each one
      must have a different monitored resource type. A metric can be used in
      at most one producer destination.
  """

  consumerDestinations = _messages.MessageField('MonitoringDestination', 1, repeated=True)
  producerDestinations = _messages.MessageField('MonitoringDestination', 2, repeated=True)


class MonitoringDestination(_messages.Message):
  r"""Configuration of a specific monitoring destination (the producer project
  or the consumer project).

  Fields:
    metrics: Names of the metrics to report to this monitoring destination.
      Each name must be defined in Service.metrics section.
    monitoredResource: The monitored resource type. The type must be defined
      in Service.monitored_resources section.
  """

  metrics = _messages.StringField(1, repeated=True)
  monitoredResource = _messages.StringField(2)


class OAuthRequirements(_messages.Message):
  r"""OAuth scopes are a way to define data and permissions on data. For
  example, there are scopes defined for "Read-only access to Google Calendar"
  and "Access to Cloud Platform". Users can consent to a scope for an
  application, giving it permission to access that data on their behalf.
  OAuth scope specifications should be fairly coarse grained; a user will need
  to see and understand the text description of what your scope means.  In
  most cases: use one or at most two OAuth scopes for an entire family of
  products. If your product has multiple APIs, you should probably be sharing
  the OAuth scope across all of those APIs.  When you need finer grained OAuth
  consent screens: talk with your product management about how developers will
  use them in practice.  Please note that even though each of the canonical
  scopes is enough for a request to be accepted and passed to the backend, a
  request can still fail due to the backend requiring additional scopes or
  permissions.

  Fields:
    canonicalScopes: The list of publicly documented OAuth scopes that are
      allowed access. An OAuth token containing any of these scopes will be
      accepted.  Example:       canonical_scopes:
      https://www.googleapis.com/auth/calendar,
      https://www.googleapis.com/auth/calendar.read
  """

  canonicalScopes = _messages.StringField(1)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation.
      It typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata.  Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal response of the operation in case of success.
      If the original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`.  If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource.  For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name.  For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation.  It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata.  Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should have the format of `operations/some/unique/name`.
    response: The normal response of the operation in case of success.  If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`.  If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource.  For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name.  For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation.  It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata.  Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal response of the operation in case of success.  If the
    original method returns no data on success, such as `Delete`, the response
    is `google.protobuf.Empty`.  If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource.  For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name.  For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class Option(_messages.Message):
  r"""A protocol buffer option, which can be attached to a message, field,
  enumeration, etc.

  Messages:
    ValueValue: The option's value packed in an Any message. If the value is a
      primitive, the corresponding wrapper type defined in
      google/protobuf/wrappers.proto should be used. If the value is an enum,
      it should be stored as an int32 value using the
      google.protobuf.Int32Value type.

  Fields:
    name: The option's name. For protobuf built-in options (options defined in
      descriptor.proto), this is the short name. For example, `"map_entry"`.
      For custom options, it should be the fully-qualified name. For example,
      `"google.api.http"`.
    value: The option's value packed in an Any message. If the value is a
      primitive, the corresponding wrapper type defined in
      google/protobuf/wrappers.proto should be used. If the value is an enum,
      it should be stored as an int32 value using the
      google.protobuf.Int32Value type.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ValueValue(_messages.Message):
    r"""The option's value packed in an Any message. If the value is a
    primitive, the corresponding wrapper type defined in
    google/protobuf/wrappers.proto should be used. If the value is an enum, it
    should be stored as an int32 value using the google.protobuf.Int32Value
    type.

    Messages:
      AdditionalProperty: An additional property for a ValueValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ValueValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  name = _messages.StringField(1)
  value = _messages.MessageField('ValueValue', 2)


class Page(_messages.Message):
  r"""Represents a documentation page. A page can contain subpages to
  represent nested documentation set structure.

  Fields:
    content: The Markdown content of the page. You can use <code>&#40;==
      include {path} ==&#41;</code> to include content from a Markdown file.
    name: The name of the page. It will be used as an identity of the page to
      generate URI of the page, text of the link to this page in navigation,
      etc. The full page name (start from the root page name to this page
      concatenated with `.`) can be used as reference to the page in your
      documentation. For example: <pre><code>pages: - name: Tutorial
      content: &#40;== include tutorial.md ==&#41;   subpages:   - name: Java
      content: &#40;== include tutorial_java.md ==&#41; </code></pre> You can
      reference `Java` page using Markdown reference link syntax: `Java`.
    subpages: Subpages of this page. The order of subpages specified here will
      be honored in the generated docset.
  """

  content = _messages.StringField(1)
  name = _messages.StringField(2)
  subpages = _messages.MessageField('Page', 3, repeated=True)


class Quota(_messages.Message):
  r"""Quota configuration helps to achieve fairness and budgeting in service
  usage.  The quota configuration works this way: - The service configuration
  defines a set of metrics. - For API calls, the quota.metric_rules maps
  methods to metrics with   corresponding costs. - The quota.limits defines
  limits on the metrics, which will be used for   quota checks at runtime.  An
  example quota configuration in yaml format:     quota:       - name:
  apiWriteQpsPerProject        metric: library.googleapis.com/write_calls
  unit: "1/min/{project}"  # rate limit for consumer projects        values:
  STANDARD: 10000        # The metric rules bind all methods to the read_calls
  metric,      # except for the UpdateBook and DeleteBook methods. These two
  methods      # are mapped to the write_calls metric, with the UpdateBook
  method      # consuming at twice rate as the DeleteBook method.
  metric_rules:      - selector: "*"        metric_costs:
  library.googleapis.com/read_calls: 1      - selector:
  google.example.library.v1.LibraryService.UpdateBook        metric_costs:
  library.googleapis.com/write_calls: 2      - selector:
  google.example.library.v1.LibraryService.DeleteBook        metric_costs:
  library.googleapis.com/write_calls: 1   Corresponding Metric definition:
  metrics:      - name: library.googleapis.com/read_calls        display_name:
  Read requests        metric_kind: DELTA        value_type: INT64       -
  name: library.googleapis.com/write_calls        display_name: Write requests
  metric_kind: DELTA        value_type: INT64

  Fields:
    limits: List of `QuotaLimit` definitions for the service.
    metricRules: List of `MetricRule` definitions, each one mapping a selected
      method to one or more metrics.
  """

  limits = _messages.MessageField('QuotaLimit', 1, repeated=True)
  metricRules = _messages.MessageField('MetricRule', 2, repeated=True)


class QuotaLimit(_messages.Message):
  r"""`QuotaLimit` defines a specific limit that applies over a specified
  duration for a limit type. There can be at most one limit for a duration and
  limit type combination defined within a `QuotaGroup`.

  Messages:
    ValuesValue: Tiered limit values. You must specify this as a key:value
      pair, with an integer value that is the maximum number of requests
      allowed for the specified unit. Currently only STANDARD is supported.

  Fields:
    defaultLimit: Default number of tokens that can be consumed during the
      specified duration. This is the number of tokens assigned when a client
      application developer activates the service for his/her project.
      Specifying a value of 0 will block all requests. This can be used if you
      are provisioning quota to selected consumers and blocking others.
      Similarly, a value of -1 will indicate an unlimited quota. No other
      negative values are allowed.  Used by group-based quotas only.
    description: Optional. User-visible, extended description for this quota
      limit. Should be used only when more context is needed to understand
      this limit than provided by the limit's display name (see:
      `display_name`).
    displayName: User-visible display name for this limit. Optional. If not
      set, the UI will provide a default display name based on the quota
      configuration. This field can be used to override the default display
      name generated from the configuration.
    duration: Duration of this limit in textual notation. Example: "100s",
      "24h", "1d". For duration longer than a day, only multiple of days is
      supported. We support only "100s" and "1d" for now. Additional support
      will be added in the future. "0" indicates indefinite duration.  Used by
      group-based quotas only.
    freeTier: Free tier value displayed in the Developers Console for this
      limit. The free tier is the number of tokens that will be subtracted
      from the billed amount when billing is enabled. This field can only be
      set on a limit with duration "1d", in a billable group; it is invalid on
      any other limit. If this field is not set, it defaults to 0, indicating
      that there is no free tier for this service.  Used by group-based quotas
      only.
    maxLimit: Maximum number of tokens that can be consumed during the
      specified duration. Client application developers can override the
      default limit up to this maximum. If specified, this value cannot be set
      to a value less than the default limit. If not specified, it is set to
      the default limit.  To allow clients to apply overrides with no upper
      bound, set this to -1, indicating unlimited maximum quota.  Used by
      group-based quotas only.
    metric: The name of the metric this quota limit applies to. The quota
      limits with the same metric will be checked together during runtime. The
      metric must be defined within the service config.
    name: Name of the quota limit.  The name must be provided, and it must be
      unique within the service. The name can only include alphanumeric
      characters as well as '-'.  The maximum length of the limit name is 64
      characters.
    unit: Specify the unit of the quota limit. It uses the same syntax as
      Metric.unit. The supported unit kinds are determined by the quota
      backend system.  Here are some examples: * "1/min/{project}" for quota
      per minute per project.  Note: the order of unit components is
      insignificant. The "1" at the beginning is required to follow the metric
      unit syntax.
    values: Tiered limit values. You must specify this as a key:value pair,
      with an integer value that is the maximum number of requests allowed for
      the specified unit. Currently only STANDARD is supported.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ValuesValue(_messages.Message):
    r"""Tiered limit values. You must specify this as a key:value pair, with
    an integer value that is the maximum number of requests allowed for the
    specified unit. Currently only STANDARD is supported.

    Messages:
      AdditionalProperty: An additional property for a ValuesValue object.

    Fields:
      additionalProperties: Additional properties of type ValuesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ValuesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.IntegerField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  defaultLimit = _messages.IntegerField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  duration = _messages.StringField(4)
  freeTier = _messages.IntegerField(5)
  maxLimit = _messages.IntegerField(6)
  metric = _messages.StringField(7)
  name = _messages.StringField(8)
  unit = _messages.StringField(9)
  values = _messages.MessageField('ValuesValue', 10)


class Service(_messages.Message):
  r"""`Service` is the root object of Google service configuration schema. It
  describes basic information about a service, such as the name and the title,
  and delegates other aspects to sub-sections. Each sub-section is either a
  proto message or a repeated proto message that configures a specific aspect,
  such as auth. See each proto message definition for details.  Example:
  type: google.api.Service     config_version: 3     name:
  calendar.googleapis.com     title: Google Calendar API     apis:     - name:
  google.calendar.v3.Calendar     authentication:       providers:       - id:
  google_calendar_auth         jwks_uri:
  https://www.googleapis.com/oauth2/v1/certs         issuer:
  https://securetoken.google.com       rules:       - selector: "*"
  requirements:           provider_id: google_calendar_auth

  Fields:
    apis: A list of API interfaces exported by this service. Only the `name`
      field of the google.protobuf.Api needs to be provided by the
      configuration author, as the remaining fields will be derived from the
      IDL during the normalization process. It is an error to specify an API
      interface here which cannot be resolved against the associated IDL
      files.
    authentication: Auth configuration.
    backend: API backend configuration.
    billing: Billing configuration.
    configVersion: The semantic version of the service configuration. The
      config version affects the interpretation of the service configuration.
      For example, certain features are enabled by default for certain config
      versions. The latest config version is `3`.
    context: Context configuration.
    control: Configuration for the service control plane.
    customError: Custom error configuration.
    documentation: Additional API documentation.
    endpoints: Configuration for network endpoints.  If this is empty, then an
      endpoint with the same name as the service is automatically generated to
      service all defined APIs.
    enums: A list of all enum types included in this API service.  Enums
      referenced directly or indirectly by the `apis` are automatically
      included.  Enums which are not referenced but shall be included should
      be listed here by name. Example:      enums:     - name:
      google.someapi.v1.SomeEnum
    experimental: Experimental configuration.
    http: HTTP configuration.
    id: A unique ID for a specific instance of this message, typically
      assigned by the client for tracking purpose. If empty, the server may
      choose to generate one instead.
    logging: Logging configuration.
    logs: Defines the logs used by this service.
    metrics: Defines the metrics used by this service.
    monitoredResources: Defines the monitored resources used by this service.
      This is required by the Service.monitoring and Service.logging
      configurations.
    monitoring: Monitoring configuration.
    name: The DNS address at which this service is available, e.g.
      `calendar.googleapis.com`.
    producerProjectId: The Google project that owns this service.
    quota: Quota configuration.
    sourceInfo: Output only. The source information for this configuration if
      available.
    systemParameters: System parameter configuration.
    systemTypes: A list of all proto message types included in this API
      service. It serves similar purpose as [google.api.Service.types], except
      that these types are not needed by user-defined APIs. Therefore, they
      will not show up in the generated discovery doc. This field should only
      be used to define system APIs in ESF.
    title: The product title for this service.
    types: A list of all proto message types included in this API service.
      Types referenced directly or indirectly by the `apis` are automatically
      included.  Messages which are not referenced but shall be included, such
      as types used by the `google.protobuf.Any` type, should be listed here
      by name. Example:      types:     - name: google.protobuf.Int32
    usage: Configuration controlling usage of this service.
  """

  apis = _messages.MessageField('Api', 1, repeated=True)
  authentication = _messages.MessageField('Authentication', 2)
  backend = _messages.MessageField('Backend', 3)
  billing = _messages.MessageField('Billing', 4)
  configVersion = _messages.IntegerField(5, variant=_messages.Variant.UINT32)
  context = _messages.MessageField('Context', 6)
  control = _messages.MessageField('Control', 7)
  customError = _messages.MessageField('CustomError', 8)
  documentation = _messages.MessageField('Documentation', 9)
  endpoints = _messages.MessageField('Endpoint', 10, repeated=True)
  enums = _messages.MessageField('Enum', 11, repeated=True)
  experimental = _messages.MessageField('Experimental', 12)
  http = _messages.MessageField('Http', 13)
  id = _messages.StringField(14)
  logging = _messages.MessageField('Logging', 15)
  logs = _messages.MessageField('LogDescriptor', 16, repeated=True)
  metrics = _messages.MessageField('MetricDescriptor', 17, repeated=True)
  monitoredResources = _messages.MessageField('MonitoredResourceDescriptor', 18, repeated=True)
  monitoring = _messages.MessageField('Monitoring', 19)
  name = _messages.StringField(20)
  producerProjectId = _messages.StringField(21)
  quota = _messages.MessageField('Quota', 22)
  sourceInfo = _messages.MessageField('SourceInfo', 23)
  systemParameters = _messages.MessageField('SystemParameters', 24)
  systemTypes = _messages.MessageField('Type', 25, repeated=True)
  title = _messages.StringField(26)
  types = _messages.MessageField('Type', 27, repeated=True)
  usage = _messages.MessageField('Usage', 28)


class ServicenetworkingOperationsGetRequest(_messages.Message):
  r"""A ServicenetworkingOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class ServicenetworkingServicesAddSubnetworkRequest(_messages.Message):
  r"""A ServicenetworkingServicesAddSubnetworkRequest object.

  Fields:
    addSubnetworkRequest: A AddSubnetworkRequest resource to be passed as the
      request body.
    parent: This is a 'tenant' project in the service producer organization.
      services/{service}/{collection-id}/{resource-id} {collection id} is the
      cloud resource collection type representing the tenant project. Only
      'projects' are currently supported. {resource id} is the tenant project
      numeric id: '123456'. {service} the name of the peering service, for
      example 'service.googleapis.com'. This service must be activated in the
      consumer project.
  """

  addSubnetworkRequest = _messages.MessageField('AddSubnetworkRequest', 1)
  parent = _messages.StringField(2, required=True)


class ServicenetworkingServicesConnectionsCreateRequest(_messages.Message):
  r"""A ServicenetworkingServicesConnectionsCreateRequest object.

  Fields:
    connection: A Connection resource to be passed as the request body.
    parent: Provider peering service that is managing peering connectivity for
      a service provider organization. For Google services that support this
      functionality it is 'services/servicenetworking.googleapis.com'.
  """

  connection = _messages.MessageField('Connection', 1)
  parent = _messages.StringField(2, required=True)


class ServicenetworkingServicesConnectionsListRequest(_messages.Message):
  r"""A ServicenetworkingServicesConnectionsListRequest object.

  Fields:
    network: Network name in the consumer project.   This network must have
      been already peered with a shared VPC network using CreateConnection
      method. Must be in a form
      'projects/{project}/global/networks/{network}'. {project} is a project
      number, as in '12345' {network} is network name.
    parent: Provider peering service that is managing peering connectivity for
      a service provider organization. For Google services that support this
      functionality it is 'services/servicenetworking.googleapis.com'.
  """

  network = _messages.StringField(1)
  parent = _messages.StringField(2, required=True)


class SourceContext(_messages.Message):
  r"""`SourceContext` represents information about the source of a protobuf
  element, like the file in which it is defined.

  Fields:
    fileName: The path-qualified name of the .proto file that contained the
      associated protobuf element.  For example:
      `"google/protobuf/source_context.proto"`.
  """

  fileName = _messages.StringField(1)


class SourceInfo(_messages.Message):
  r"""Source information used to create a Service Config

  Messages:
    SourceFilesValueListEntry: A SourceFilesValueListEntry object.

  Fields:
    sourceFiles: All files used during config generation.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SourceFilesValueListEntry(_messages.Message):
    r"""A SourceFilesValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a
        SourceFilesValueListEntry object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SourceFilesValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  sourceFiles = _messages.MessageField('SourceFilesValueListEntry', 1, repeated=True)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). The error model is designed to be:
  - Simple to use and understand for most users - Flexible enough to meet
  unexpected needs  # Overview  The `Status` message contains three pieces of
  data: error code, error message, and error details. The error code should be
  an enum value of google.rpc.Code, but it may accept additional error codes
  if needed.  The error message should be a developer-facing English message
  that helps developers *understand* and *resolve* the error. If a localized
  user-facing error message is needed, put the localized message in the error
  details or localize it in the client. The optional error details may contain
  arbitrary information about the error. There is a predefined set of error
  detail types in the package `google.rpc` that can be used for common error
  conditions.  # Language mapping  The `Status` message is the logical
  representation of the error model, but it is not necessarily the actual wire
  format. When the `Status` message is exposed in different client libraries
  and different wire protocols, it can be mapped differently. For example, it
  will likely be mapped to some exceptions in Java, but more likely mapped to
  some error codes in C.  # Other uses  The error model and the `Status`
  message can be used in a variety of environments, either with or without
  APIs, to provide a consistent developer experience across different
  environments.  Example uses of this error model include:  - Partial errors.
  If a service needs to return partial errors to the client,     it may embed
  the `Status` in the normal response to indicate the partial     errors.  -
  Workflow errors. A typical workflow has multiple steps. Each step may
  have a `Status` message for error reporting.  - Batch operations. If a
  client uses batch request and batch response, the     `Status` message
  should be used directly inside batch response, one for     each error sub-
  response.  - Asynchronous operations. If an API call embeds asynchronous
  operation     results in its response, the status of those operations should
  be     represented directly using the `Status` message.  - Logging. If some
  API errors are stored in logs, the message `Status` could     be used
  directly after any stripping needed for security/privacy reasons.

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details.  There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class Subnetwork(_messages.Message):
  r"""Message returning the created service subnetwork.

  Fields:
    ipCidrRange: Subnetwork CIDR range in "10.x.x.x/y" format.
    name: Subnetwork name. See https://cloud.google.com/compute/docs/vpc/
  """

  ipCidrRange = _messages.StringField(1)
  name = _messages.StringField(2)


class SystemParameter(_messages.Message):
  r"""Define a parameter's name and location. The parameter may be passed as
  either an HTTP header or a URL query parameter, and if both are passed the
  behavior is implementation-dependent.

  Fields:
    httpHeader: Define the HTTP header name to use for the parameter. It is
      case insensitive.
    name: Define the name of the parameter, such as "api_key" . It is case
      sensitive.
    urlQueryParameter: Define the URL query parameter name to use for the
      parameter. It is case sensitive.
  """

  httpHeader = _messages.StringField(1)
  name = _messages.StringField(2)
  urlQueryParameter = _messages.StringField(3)


class SystemParameterRule(_messages.Message):
  r"""Define a system parameter rule mapping system parameter definitions to
  methods.

  Fields:
    parameters: Define parameters. Multiple names may be defined for a
      parameter. For a given method call, only one of them should be used. If
      multiple names are used the behavior is implementation-dependent. If
      none of the specified names are present the behavior is parameter-
      dependent.
    selector: Selects the methods to which this rule applies. Use '*' to
      indicate all methods in all APIs.  Refer to selector for syntax details.
  """

  parameters = _messages.MessageField('SystemParameter', 1, repeated=True)
  selector = _messages.StringField(2)


class SystemParameters(_messages.Message):
  r"""### System parameter configuration  A system parameter is a special kind
  of parameter defined by the API system, not by an individual API. It is
  typically mapped to an HTTP header and/or a URL query parameter. This
  configuration specifies which methods change the names of the system
  parameters.

  Fields:
    rules: Define system parameters.  The parameters defined here will
      override the default parameters implemented by the system. If this field
      is missing from the service config, default system parameters will be
      used. Default system parameters and names is implementation-dependent.
      Example: define api key for all methods      system_parameters
      rules:         - selector: "*"           parameters:             - name:
      api_key               url_query_parameter: api_key   Example: define 2
      api key names for a specific method.      system_parameters       rules:
      - selector: "/ListShelves"           parameters:             - name:
      api_key               http_header: Api-Key1             - name: api_key
      http_header: Api-Key2  **NOTE:** All service configuration rules follow
      "last one wins" order.
  """

  rules = _messages.MessageField('SystemParameterRule', 1, repeated=True)


class Type(_messages.Message):
  r"""A protocol buffer message type.

  Enums:
    SyntaxValueValuesEnum: The source syntax.

  Fields:
    fields: The list of fields.
    name: The fully qualified message name.
    oneofs: The list of types appearing in `oneof` definitions in this type.
    options: The protocol buffer options.
    sourceContext: The source context.
    syntax: The source syntax.
  """

  class SyntaxValueValuesEnum(_messages.Enum):
    r"""The source syntax.

    Values:
      SYNTAX_PROTO2: Syntax `proto2`.
      SYNTAX_PROTO3: Syntax `proto3`.
    """
    SYNTAX_PROTO2 = 0
    SYNTAX_PROTO3 = 1

  fields = _messages.MessageField('Field', 1, repeated=True)
  name = _messages.StringField(2)
  oneofs = _messages.StringField(3, repeated=True)
  options = _messages.MessageField('Option', 4, repeated=True)
  sourceContext = _messages.MessageField('SourceContext', 5)
  syntax = _messages.EnumField('SyntaxValueValuesEnum', 6)


class Usage(_messages.Message):
  r"""Configuration controlling usage of a service.

  Fields:
    producerNotificationChannel: The full resource name of a channel used for
      sending notifications to the service producer.  Google Service
      Management currently only supports [Google Cloud
      Pub/Sub](https://cloud.google.com/pubsub) as a notification channel. To
      use Google Cloud Pub/Sub as the channel, this must be the name of a
      Cloud Pub/Sub topic that uses the Cloud Pub/Sub topic name format
      documented in https://cloud.google.com/pubsub/docs/overview.
    requirements: Requirements that must be satisfied before a consumer
      project can use the service. Each requirement is of the form
      <service.name>/<requirement-id>; for example
      'serviceusage.googleapis.com/billing-enabled'.
    rules: A list of usage rules that apply to individual API methods.
      **NOTE:** All service configuration rules follow "last one wins" order.
  """

  producerNotificationChannel = _messages.StringField(1)
  requirements = _messages.StringField(2, repeated=True)
  rules = _messages.MessageField('UsageRule', 3, repeated=True)


class UsageRule(_messages.Message):
  r"""Usage configuration rules for the service.  NOTE: Under development.
  Use this rule to configure unregistered calls for the service. Unregistered
  calls are calls that do not contain consumer project identity. (Example:
  calls that do not contain an API key). By default, API methods do not allow
  unregistered calls, and each method call must be identified by a consumer
  project identity. Use this rule to allow/disallow unregistered calls.
  Example of an API that wants to allow unregistered calls for entire service.
  usage:       rules:       - selector: "*"         allow_unregistered_calls:
  true  Example of a method that wants to allow unregistered calls.
  usage:       rules:       - selector:
  "google.example.library.v1.LibraryService.CreateBook"
  allow_unregistered_calls: true

  Fields:
    allowUnregisteredCalls: If true, the selected method allows unregistered
      calls, e.g. calls that don't identify any user or application.
    selector: Selects the methods to which this rule applies. Use '*' to
      indicate all methods in all APIs.  Refer to selector for syntax details.
    skipServiceControl: If true, the selected method should skip service
      control and the control plane features, such as quota and billing, will
      not be available. This flag is used by Google Cloud Endpoints to bypass
      checks for internal methods, such as service health check methods.
  """

  allowUnregisteredCalls = _messages.BooleanField(1)
  selector = _messages.StringField(2)
  skipServiceControl = _messages.BooleanField(3)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
