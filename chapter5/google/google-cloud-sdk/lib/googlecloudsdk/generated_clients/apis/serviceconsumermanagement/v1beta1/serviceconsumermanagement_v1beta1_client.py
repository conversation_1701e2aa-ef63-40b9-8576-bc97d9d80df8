"""Generated client library for serviceconsumermanagement version v1beta1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.serviceconsumermanagement.v1beta1 import serviceconsumermanagement_v1beta1_messages as messages


class ServiceconsumermanagementV1beta1(base_api.BaseApiClient):
  """Generated client library for service serviceconsumermanagement version v1beta1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://serviceconsumermanagement.googleapis.com/'
  MTLS_BASE_URL = 'https://serviceconsumermanagement.mtls.googleapis.com/'

  _PACKAGE = 'serviceconsumermanagement'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1beta1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'ServiceconsumermanagementV1beta1'
  _URL_VERSION = 'v1beta1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new serviceconsumermanagement handle."""
    url = url or self.BASE_URL
    super(ServiceconsumermanagementV1beta1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.operations = self.OperationsService(self)
    self.services_consumerQuotaMetrics_limits_producerOverrides = self.ServicesConsumerQuotaMetricsLimitsProducerOverridesService(self)
    self.services_consumerQuotaMetrics_limits = self.ServicesConsumerQuotaMetricsLimitsService(self)
    self.services_consumerQuotaMetrics = self.ServicesConsumerQuotaMetricsService(self)
    self.services = self.ServicesService(self)

  class OperationsService(base_api.BaseApiService):
    """Service class for the operations resource."""

    _NAME = 'operations'

    def __init__(self, client):
      super(ServiceconsumermanagementV1beta1.OperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation.  Clients can use this.
method to poll the operation result at intervals as recommended by the API
service.

      Args:
        request: (ServiceconsumermanagementOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/operations/{operationsId}',
        http_method='GET',
        method_id='serviceconsumermanagement.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='ServiceconsumermanagementOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ServicesConsumerQuotaMetricsLimitsProducerOverridesService(base_api.BaseApiService):
    """Service class for the services_consumerQuotaMetrics_limits_producerOverrides resource."""

    _NAME = 'services_consumerQuotaMetrics_limits_producerOverrides'

    def __init__(self, client):
      super(ServiceconsumermanagementV1beta1.ServicesConsumerQuotaMetricsLimitsProducerOverridesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a producer override.
A producer override is applied by the owner or administrator of a service
to increase or decrease the amount of quota a consumer of the service is
allowed to use.
To create multiple overrides at once, use ImportProducerOverrides instead.
If an override with the specified dimensions already exists, this call will
fail. To overwrite an existing override if one is already present ("upsert"
semantics), use ImportProducerOverrides instead.

      Args:
        request: (ServiceconsumermanagementServicesConsumerQuotaMetricsLimitsProducerOverridesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/services/{servicesId}/{servicesId1}/{servicesId2}/consumerQuotaMetrics/{consumerQuotaMetricsId}/limits/{limitsId}/producerOverrides',
        http_method='POST',
        method_id='serviceconsumermanagement.services.consumerQuotaMetrics.limits.producerOverrides.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['force'],
        relative_path='v1beta1/{+parent}/producerOverrides',
        request_field='v1Beta1QuotaOverride',
        request_type_name='ServiceconsumermanagementServicesConsumerQuotaMetricsLimitsProducerOverridesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a producer override.

      Args:
        request: (ServiceconsumermanagementServicesConsumerQuotaMetricsLimitsProducerOverridesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/services/{servicesId}/{servicesId1}/{servicesId2}/consumerQuotaMetrics/{consumerQuotaMetricsId}/limits/{limitsId}/producerOverrides/{producerOverridesId}',
        http_method='DELETE',
        method_id='serviceconsumermanagement.services.consumerQuotaMetrics.limits.producerOverrides.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='ServiceconsumermanagementServicesConsumerQuotaMetricsLimitsProducerOverridesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all producer overrides on this limit.

      Args:
        request: (ServiceconsumermanagementServicesConsumerQuotaMetricsLimitsProducerOverridesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (V1Beta1ListProducerOverridesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/services/{servicesId}/{servicesId1}/{servicesId2}/consumerQuotaMetrics/{consumerQuotaMetricsId}/limits/{limitsId}/producerOverrides',
        http_method='GET',
        method_id='serviceconsumermanagement.services.consumerQuotaMetrics.limits.producerOverrides.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/producerOverrides',
        request_field='',
        request_type_name='ServiceconsumermanagementServicesConsumerQuotaMetricsLimitsProducerOverridesListRequest',
        response_type_name='V1Beta1ListProducerOverridesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a producer override.

      Args:
        request: (ServiceconsumermanagementServicesConsumerQuotaMetricsLimitsProducerOverridesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/services/{servicesId}/{servicesId1}/{servicesId2}/consumerQuotaMetrics/{consumerQuotaMetricsId}/limits/{limitsId}/producerOverrides/{producerOverridesId}',
        http_method='PATCH',
        method_id='serviceconsumermanagement.services.consumerQuotaMetrics.limits.producerOverrides.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force', 'updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='v1Beta1QuotaOverride',
        request_type_name='ServiceconsumermanagementServicesConsumerQuotaMetricsLimitsProducerOverridesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ServicesConsumerQuotaMetricsLimitsService(base_api.BaseApiService):
    """Service class for the services_consumerQuotaMetrics_limits resource."""

    _NAME = 'services_consumerQuotaMetrics_limits'

    def __init__(self, client):
      super(ServiceconsumermanagementV1beta1.ServicesConsumerQuotaMetricsLimitsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Retrieves a summary of quota information for a specific quota limit.

      Args:
        request: (ServiceconsumermanagementServicesConsumerQuotaMetricsLimitsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (V1Beta1ConsumerQuotaLimit) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/services/{servicesId}/{servicesId1}/{servicesId2}/consumerQuotaMetrics/{consumerQuotaMetricsId}/limits/{limitsId}',
        http_method='GET',
        method_id='serviceconsumermanagement.services.consumerQuotaMetrics.limits.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='ServiceconsumermanagementServicesConsumerQuotaMetricsLimitsGetRequest',
        response_type_name='V1Beta1ConsumerQuotaLimit',
        supports_download=False,
    )

  class ServicesConsumerQuotaMetricsService(base_api.BaseApiService):
    """Service class for the services_consumerQuotaMetrics resource."""

    _NAME = 'services_consumerQuotaMetrics'

    def __init__(self, client):
      super(ServiceconsumermanagementV1beta1.ServicesConsumerQuotaMetricsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Retrieves a summary of quota information for a specific quota metric.

      Args:
        request: (ServiceconsumermanagementServicesConsumerQuotaMetricsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (V1Beta1ConsumerQuotaMetric) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/services/{servicesId}/{servicesId1}/{servicesId2}/consumerQuotaMetrics/{consumerQuotaMetricsId}',
        http_method='GET',
        method_id='serviceconsumermanagement.services.consumerQuotaMetrics.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='ServiceconsumermanagementServicesConsumerQuotaMetricsGetRequest',
        response_type_name='V1Beta1ConsumerQuotaMetric',
        supports_download=False,
    )

    def ImportProducerOverrides(self, request, global_params=None):
      r"""Create or update multiple producer overrides atomically, all on the.
same consumer, but on many different metrics or limits.
The name field in the quota override message should not be set.

      Args:
        request: (ServiceconsumermanagementServicesConsumerQuotaMetricsImportProducerOverridesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('ImportProducerOverrides')
      return self._RunMethod(
          config, request, global_params=global_params)

    ImportProducerOverrides.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/services/{servicesId}/{servicesId1}/{servicesId2}/consumerQuotaMetrics:importProducerOverrides',
        http_method='POST',
        method_id='serviceconsumermanagement.services.consumerQuotaMetrics.importProducerOverrides',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/consumerQuotaMetrics:importProducerOverrides',
        request_field='v1Beta1ImportProducerOverridesRequest',
        request_type_name='ServiceconsumermanagementServicesConsumerQuotaMetricsImportProducerOverridesRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Retrieves a summary of all quota information about this consumer that is.
visible to the service producer, for each quota metric defined by the
service. Each metric includes information about all of its defined limits.
Each limit includes the limit configuration (quota unit, preciseness,
default value), the current effective limit value, and all of the overrides
applied to the limit.

      Args:
        request: (ServiceconsumermanagementServicesConsumerQuotaMetricsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (V1Beta1ListConsumerQuotaMetricsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/services/{servicesId}/{servicesId1}/{servicesId2}/consumerQuotaMetrics',
        http_method='GET',
        method_id='serviceconsumermanagement.services.consumerQuotaMetrics.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'view'],
        relative_path='v1beta1/{+parent}/consumerQuotaMetrics',
        request_field='',
        request_type_name='ServiceconsumermanagementServicesConsumerQuotaMetricsListRequest',
        response_type_name='V1Beta1ListConsumerQuotaMetricsResponse',
        supports_download=False,
    )

  class ServicesService(base_api.BaseApiService):
    """Service class for the services resource."""

    _NAME = 'services'

    def __init__(self, client):
      super(ServiceconsumermanagementV1beta1.ServicesService, self).__init__(client)
      self._upload_configs = {
          }
