"""Generated client library for monitoring version v3."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.monitoring.v3 import monitoring_v3_messages as messages


class MonitoringV3(base_api.BaseApiClient):
  """Generated client library for service monitoring version v3."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://monitoring.googleapis.com/'
  MTLS_BASE_URL = 'https://monitoring.mtls.googleapis.com/'

  _PACKAGE = 'monitoring'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform', 'https://www.googleapis.com/auth/monitoring', 'https://www.googleapis.com/auth/monitoring.read', 'https://www.googleapis.com/auth/monitoring.write']
  _VERSION = 'v3'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'MonitoringV3'
  _URL_VERSION = 'v3'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new monitoring handle."""
    url = url or self.BASE_URL
    super(MonitoringV3, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.folders_timeSeries = self.FoldersTimeSeriesService(self)
    self.folders = self.FoldersService(self)
    self.organizations_timeSeries = self.OrganizationsTimeSeriesService(self)
    self.organizations = self.OrganizationsService(self)
    self.projects_alertPolicies = self.ProjectsAlertPoliciesService(self)
    self.projects_alerts = self.ProjectsAlertsService(self)
    self.projects_collectdTimeSeries = self.ProjectsCollectdTimeSeriesService(self)
    self.projects_groups_members = self.ProjectsGroupsMembersService(self)
    self.projects_groups = self.ProjectsGroupsService(self)
    self.projects_metricDescriptors = self.ProjectsMetricDescriptorsService(self)
    self.projects_monitoredResourceDescriptors = self.ProjectsMonitoredResourceDescriptorsService(self)
    self.projects_notificationChannelDescriptors = self.ProjectsNotificationChannelDescriptorsService(self)
    self.projects_notificationChannels = self.ProjectsNotificationChannelsService(self)
    self.projects_snoozes = self.ProjectsSnoozesService(self)
    self.projects_timeSeries = self.ProjectsTimeSeriesService(self)
    self.projects_uptimeCheckConfigs = self.ProjectsUptimeCheckConfigsService(self)
    self.projects = self.ProjectsService(self)
    self.services_serviceLevelObjectives = self.ServicesServiceLevelObjectivesService(self)
    self.services = self.ServicesService(self)
    self.uptimeCheckIps = self.UptimeCheckIpsService(self)

  class FoldersTimeSeriesService(base_api.BaseApiService):
    """Service class for the folders_timeSeries resource."""

    _NAME = 'folders_timeSeries'

    def __init__(self, client):
      super(MonitoringV3.FoldersTimeSeriesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists time series that match a filter.

      Args:
        request: (MonitoringFoldersTimeSeriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListTimeSeriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/folders/{foldersId}/timeSeries',
        http_method='GET',
        method_id='monitoring.folders.timeSeries.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['aggregation_alignmentPeriod', 'aggregation_crossSeriesReducer', 'aggregation_groupByFields', 'aggregation_perSeriesAligner', 'filter', 'interval_endTime', 'interval_startTime', 'orderBy', 'pageSize', 'pageToken', 'secondaryAggregation_alignmentPeriod', 'secondaryAggregation_crossSeriesReducer', 'secondaryAggregation_groupByFields', 'secondaryAggregation_perSeriesAligner', 'view'],
        relative_path='v3/{+name}/timeSeries',
        request_field='',
        request_type_name='MonitoringFoldersTimeSeriesListRequest',
        response_type_name='ListTimeSeriesResponse',
        supports_download=False,
    )

  class FoldersService(base_api.BaseApiService):
    """Service class for the folders resource."""

    _NAME = 'folders'

    def __init__(self, client):
      super(MonitoringV3.FoldersService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsTimeSeriesService(base_api.BaseApiService):
    """Service class for the organizations_timeSeries resource."""

    _NAME = 'organizations_timeSeries'

    def __init__(self, client):
      super(MonitoringV3.OrganizationsTimeSeriesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists time series that match a filter.

      Args:
        request: (MonitoringOrganizationsTimeSeriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListTimeSeriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/organizations/{organizationsId}/timeSeries',
        http_method='GET',
        method_id='monitoring.organizations.timeSeries.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['aggregation_alignmentPeriod', 'aggregation_crossSeriesReducer', 'aggregation_groupByFields', 'aggregation_perSeriesAligner', 'filter', 'interval_endTime', 'interval_startTime', 'orderBy', 'pageSize', 'pageToken', 'secondaryAggregation_alignmentPeriod', 'secondaryAggregation_crossSeriesReducer', 'secondaryAggregation_groupByFields', 'secondaryAggregation_perSeriesAligner', 'view'],
        relative_path='v3/{+name}/timeSeries',
        request_field='',
        request_type_name='MonitoringOrganizationsTimeSeriesListRequest',
        response_type_name='ListTimeSeriesResponse',
        supports_download=False,
    )

  class OrganizationsService(base_api.BaseApiService):
    """Service class for the organizations resource."""

    _NAME = 'organizations'

    def __init__(self, client):
      super(MonitoringV3.OrganizationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsAlertPoliciesService(base_api.BaseApiService):
    """Service class for the projects_alertPolicies resource."""

    _NAME = 'projects_alertPolicies'

    def __init__(self, client):
      super(MonitoringV3.ProjectsAlertPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new alerting policy.Design your application to single-thread API calls that modify the state of alerting policies in a single project. This includes calls to CreateAlertPolicy, DeleteAlertPolicy and UpdateAlertPolicy.

      Args:
        request: (MonitoringProjectsAlertPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AlertPolicy) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/alertPolicies',
        http_method='POST',
        method_id='monitoring.projects.alertPolicies.create',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}/alertPolicies',
        request_field='alertPolicy',
        request_type_name='MonitoringProjectsAlertPoliciesCreateRequest',
        response_type_name='AlertPolicy',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an alerting policy.Design your application to single-thread API calls that modify the state of alerting policies in a single project. This includes calls to CreateAlertPolicy, DeleteAlertPolicy and UpdateAlertPolicy.

      Args:
        request: (MonitoringProjectsAlertPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/alertPolicies/{alertPoliciesId}',
        http_method='DELETE',
        method_id='monitoring.projects.alertPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='MonitoringProjectsAlertPoliciesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a single alerting policy.

      Args:
        request: (MonitoringProjectsAlertPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AlertPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/alertPolicies/{alertPoliciesId}',
        http_method='GET',
        method_id='monitoring.projects.alertPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='MonitoringProjectsAlertPoliciesGetRequest',
        response_type_name='AlertPolicy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the existing alerting policies for the workspace.

      Args:
        request: (MonitoringProjectsAlertPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAlertPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/alertPolicies',
        http_method='GET',
        method_id='monitoring.projects.alertPolicies.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v3/{+name}/alertPolicies',
        request_field='',
        request_type_name='MonitoringProjectsAlertPoliciesListRequest',
        response_type_name='ListAlertPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an alerting policy. You can either replace the entire policy with a new one or replace only certain fields in the current alerting policy by specifying the fields to be updated via updateMask. Returns the updated alerting policy.Design your application to single-thread API calls that modify the state of alerting policies in a single project. This includes calls to CreateAlertPolicy, DeleteAlertPolicy and UpdateAlertPolicy.

      Args:
        request: (MonitoringProjectsAlertPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AlertPolicy) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/alertPolicies/{alertPoliciesId}',
        http_method='PATCH',
        method_id='monitoring.projects.alertPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v3/{+name}',
        request_field='alertPolicy',
        request_type_name='MonitoringProjectsAlertPoliciesPatchRequest',
        response_type_name='AlertPolicy',
        supports_download=False,
    )

  class ProjectsAlertsService(base_api.BaseApiService):
    """Service class for the projects_alerts resource."""

    _NAME = 'projects_alerts'

    def __init__(self, client):
      super(MonitoringV3.ProjectsAlertsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets a single alert.

      Args:
        request: (MonitoringProjectsAlertsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Alert) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/alerts/{alertsId}',
        http_method='GET',
        method_id='monitoring.projects.alerts.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='MonitoringProjectsAlertsGetRequest',
        response_type_name='Alert',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the existing alerts for the Metrics scope.

      Args:
        request: (MonitoringProjectsAlertsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAlertsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/alerts',
        http_method='GET',
        method_id='monitoring.projects.alerts.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v3/{+parent}/alerts',
        request_field='',
        request_type_name='MonitoringProjectsAlertsListRequest',
        response_type_name='ListAlertsResponse',
        supports_download=False,
    )

  class ProjectsCollectdTimeSeriesService(base_api.BaseApiService):
    """Service class for the projects_collectdTimeSeries resource."""

    _NAME = 'projects_collectdTimeSeries'

    def __init__(self, client):
      super(MonitoringV3.ProjectsCollectdTimeSeriesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Cloud Monitoring Agent only: Creates a new time series.This method is only for use by the Cloud Monitoring Agent. Use projects.timeSeries.create instead.

      Args:
        request: (MonitoringProjectsCollectdTimeSeriesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CreateCollectdTimeSeriesResponse) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/collectdTimeSeries',
        http_method='POST',
        method_id='monitoring.projects.collectdTimeSeries.create',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}/collectdTimeSeries',
        request_field='createCollectdTimeSeriesRequest',
        request_type_name='MonitoringProjectsCollectdTimeSeriesCreateRequest',
        response_type_name='CreateCollectdTimeSeriesResponse',
        supports_download=False,
    )

  class ProjectsGroupsMembersService(base_api.BaseApiService):
    """Service class for the projects_groups_members resource."""

    _NAME = 'projects_groups_members'

    def __init__(self, client):
      super(MonitoringV3.ProjectsGroupsMembersService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the monitored resources that are members of a group.

      Args:
        request: (MonitoringProjectsGroupsMembersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGroupMembersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/groups/{groupsId}/members',
        http_method='GET',
        method_id='monitoring.projects.groups.members.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'interval_endTime', 'interval_startTime', 'pageSize', 'pageToken'],
        relative_path='v3/{+name}/members',
        request_field='',
        request_type_name='MonitoringProjectsGroupsMembersListRequest',
        response_type_name='ListGroupMembersResponse',
        supports_download=False,
    )

  class ProjectsGroupsService(base_api.BaseApiService):
    """Service class for the projects_groups resource."""

    _NAME = 'projects_groups'

    def __init__(self, client):
      super(MonitoringV3.ProjectsGroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new group.

      Args:
        request: (MonitoringProjectsGroupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Group) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/groups',
        http_method='POST',
        method_id='monitoring.projects.groups.create',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['validateOnly'],
        relative_path='v3/{+name}/groups',
        request_field='group',
        request_type_name='MonitoringProjectsGroupsCreateRequest',
        response_type_name='Group',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing group.

      Args:
        request: (MonitoringProjectsGroupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/groups/{groupsId}',
        http_method='DELETE',
        method_id='monitoring.projects.groups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['recursive'],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='MonitoringProjectsGroupsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a single group.

      Args:
        request: (MonitoringProjectsGroupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Group) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/groups/{groupsId}',
        http_method='GET',
        method_id='monitoring.projects.groups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='MonitoringProjectsGroupsGetRequest',
        response_type_name='Group',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the existing groups.

      Args:
        request: (MonitoringProjectsGroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/groups',
        http_method='GET',
        method_id='monitoring.projects.groups.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['ancestorsOfGroup', 'childrenOfGroup', 'descendantsOfGroup', 'pageSize', 'pageToken'],
        relative_path='v3/{+name}/groups',
        request_field='',
        request_type_name='MonitoringProjectsGroupsListRequest',
        response_type_name='ListGroupsResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates an existing group. You can change any group attributes except name.

      Args:
        request: (MonitoringProjectsGroupsUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Group) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/groups/{groupsId}',
        http_method='PUT',
        method_id='monitoring.projects.groups.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['validateOnly'],
        relative_path='v3/{+name}',
        request_field='group',
        request_type_name='MonitoringProjectsGroupsUpdateRequest',
        response_type_name='Group',
        supports_download=False,
    )

  class ProjectsMetricDescriptorsService(base_api.BaseApiService):
    """Service class for the projects_metricDescriptors resource."""

    _NAME = 'projects_metricDescriptors'

    def __init__(self, client):
      super(MonitoringV3.ProjectsMetricDescriptorsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new metric descriptor. The creation is executed asynchronously. User-created metric descriptors define custom metrics (https://cloud.google.com/monitoring/custom-metrics). The metric descriptor is updated if it already exists, except that metric labels are never removed.

      Args:
        request: (MetricDescriptor) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MetricDescriptor) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/metricDescriptors',
        http_method='POST',
        method_id='monitoring.projects.metricDescriptors.create',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}/metricDescriptors',
        request_field='<request>',
        request_type_name='MetricDescriptor',
        response_type_name='MetricDescriptor',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a metric descriptor. Only user-created custom metrics (https://cloud.google.com/monitoring/custom-metrics) can be deleted.

      Args:
        request: (MonitoringProjectsMetricDescriptorsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/metricDescriptors/{metricDescriptorsId}',
        http_method='DELETE',
        method_id='monitoring.projects.metricDescriptors.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='MonitoringProjectsMetricDescriptorsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a single metric descriptor.

      Args:
        request: (MonitoringProjectsMetricDescriptorsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MetricDescriptor) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/metricDescriptors/{metricDescriptorsId}',
        http_method='GET',
        method_id='monitoring.projects.metricDescriptors.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='MonitoringProjectsMetricDescriptorsGetRequest',
        response_type_name='MetricDescriptor',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists metric descriptors that match a filter.

      Args:
        request: (MonitoringProjectsMetricDescriptorsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMetricDescriptorsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/metricDescriptors',
        http_method='GET',
        method_id='monitoring.projects.metricDescriptors.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['activeOnly', 'filter', 'pageSize', 'pageToken'],
        relative_path='v3/{+name}/metricDescriptors',
        request_field='',
        request_type_name='MonitoringProjectsMetricDescriptorsListRequest',
        response_type_name='ListMetricDescriptorsResponse',
        supports_download=False,
    )

  class ProjectsMonitoredResourceDescriptorsService(base_api.BaseApiService):
    """Service class for the projects_monitoredResourceDescriptors resource."""

    _NAME = 'projects_monitoredResourceDescriptors'

    def __init__(self, client):
      super(MonitoringV3.ProjectsMonitoredResourceDescriptorsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets a single monitored resource descriptor.

      Args:
        request: (MonitoringProjectsMonitoredResourceDescriptorsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MonitoredResourceDescriptor) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/monitoredResourceDescriptors/{monitoredResourceDescriptorsId}',
        http_method='GET',
        method_id='monitoring.projects.monitoredResourceDescriptors.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='MonitoringProjectsMonitoredResourceDescriptorsGetRequest',
        response_type_name='MonitoredResourceDescriptor',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists monitored resource descriptors that match a filter.

      Args:
        request: (MonitoringProjectsMonitoredResourceDescriptorsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMonitoredResourceDescriptorsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/monitoredResourceDescriptors',
        http_method='GET',
        method_id='monitoring.projects.monitoredResourceDescriptors.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v3/{+name}/monitoredResourceDescriptors',
        request_field='',
        request_type_name='MonitoringProjectsMonitoredResourceDescriptorsListRequest',
        response_type_name='ListMonitoredResourceDescriptorsResponse',
        supports_download=False,
    )

  class ProjectsNotificationChannelDescriptorsService(base_api.BaseApiService):
    """Service class for the projects_notificationChannelDescriptors resource."""

    _NAME = 'projects_notificationChannelDescriptors'

    def __init__(self, client):
      super(MonitoringV3.ProjectsNotificationChannelDescriptorsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets a single channel descriptor. The descriptor indicates which fields are expected / permitted for a notification channel of the given type.

      Args:
        request: (MonitoringProjectsNotificationChannelDescriptorsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NotificationChannelDescriptor) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/notificationChannelDescriptors/{notificationChannelDescriptorsId}',
        http_method='GET',
        method_id='monitoring.projects.notificationChannelDescriptors.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='MonitoringProjectsNotificationChannelDescriptorsGetRequest',
        response_type_name='NotificationChannelDescriptor',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the descriptors for supported channel types. The use of descriptors makes it possible for new channel types to be dynamically added.

      Args:
        request: (MonitoringProjectsNotificationChannelDescriptorsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListNotificationChannelDescriptorsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/notificationChannelDescriptors',
        http_method='GET',
        method_id='monitoring.projects.notificationChannelDescriptors.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v3/{+name}/notificationChannelDescriptors',
        request_field='',
        request_type_name='MonitoringProjectsNotificationChannelDescriptorsListRequest',
        response_type_name='ListNotificationChannelDescriptorsResponse',
        supports_download=False,
    )

  class ProjectsNotificationChannelsService(base_api.BaseApiService):
    """Service class for the projects_notificationChannels resource."""

    _NAME = 'projects_notificationChannels'

    def __init__(self, client):
      super(MonitoringV3.ProjectsNotificationChannelsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new notification channel, representing a single notification endpoint such as an email address, SMS number, or PagerDuty service.Design your application to single-thread API calls that modify the state of notification channels in a single project. This includes calls to CreateNotificationChannel, DeleteNotificationChannel and UpdateNotificationChannel.

      Args:
        request: (MonitoringProjectsNotificationChannelsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NotificationChannel) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/notificationChannels',
        http_method='POST',
        method_id='monitoring.projects.notificationChannels.create',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}/notificationChannels',
        request_field='notificationChannel',
        request_type_name='MonitoringProjectsNotificationChannelsCreateRequest',
        response_type_name='NotificationChannel',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a notification channel.Design your application to single-thread API calls that modify the state of notification channels in a single project. This includes calls to CreateNotificationChannel, DeleteNotificationChannel and UpdateNotificationChannel.

      Args:
        request: (MonitoringProjectsNotificationChannelsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/notificationChannels/{notificationChannelsId}',
        http_method='DELETE',
        method_id='monitoring.projects.notificationChannels.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='MonitoringProjectsNotificationChannelsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a single notification channel. The channel includes the relevant configuration details with which the channel was created. However, the response may truncate or omit passwords, API keys, or other private key matter and thus the response may not be 100% identical to the information that was supplied in the call to the create method.

      Args:
        request: (MonitoringProjectsNotificationChannelsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NotificationChannel) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/notificationChannels/{notificationChannelsId}',
        http_method='GET',
        method_id='monitoring.projects.notificationChannels.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='MonitoringProjectsNotificationChannelsGetRequest',
        response_type_name='NotificationChannel',
        supports_download=False,
    )

    def GetVerificationCode(self, request, global_params=None):
      r"""Requests a verification code for an already verified channel that can then be used in a call to VerifyNotificationChannel() on a different channel with an equivalent identity in the same or in a different project. This makes it possible to copy a channel between projects without requiring manual reverification of the channel. If the channel is not in the verified state, this method will fail (in other words, this may only be used if the SendNotificationChannelVerificationCode and VerifyNotificationChannel paths have already been used to put the given channel into the verified state).There is no guarantee that the verification codes returned by this method will be of a similar structure or form as the ones that are delivered to the channel via SendNotificationChannelVerificationCode; while VerifyNotificationChannel() will recognize both the codes delivered via SendNotificationChannelVerificationCode() and returned from GetNotificationChannelVerificationCode(), it is typically the case that the verification codes delivered via SendNotificationChannelVerificationCode() will be shorter and also have a shorter expiration (e.g. codes such as "G-123456") whereas GetVerificationCode() will typically return a much longer, websafe base 64 encoded string that has a longer expiration time.

      Args:
        request: (MonitoringProjectsNotificationChannelsGetVerificationCodeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GetNotificationChannelVerificationCodeResponse) The response message.
      """
      config = self.GetMethodConfig('GetVerificationCode')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetVerificationCode.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/notificationChannels/{notificationChannelsId}:getVerificationCode',
        http_method='POST',
        method_id='monitoring.projects.notificationChannels.getVerificationCode',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}:getVerificationCode',
        request_field='getNotificationChannelVerificationCodeRequest',
        request_type_name='MonitoringProjectsNotificationChannelsGetVerificationCodeRequest',
        response_type_name='GetNotificationChannelVerificationCodeResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the notification channels that have been created for the project. To list the types of notification channels that are supported, use the ListNotificationChannelDescriptors method.

      Args:
        request: (MonitoringProjectsNotificationChannelsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListNotificationChannelsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/notificationChannels',
        http_method='GET',
        method_id='monitoring.projects.notificationChannels.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v3/{+name}/notificationChannels',
        request_field='',
        request_type_name='MonitoringProjectsNotificationChannelsListRequest',
        response_type_name='ListNotificationChannelsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a notification channel. Fields not specified in the field mask remain unchanged.Design your application to single-thread API calls that modify the state of notification channels in a single project. This includes calls to CreateNotificationChannel, DeleteNotificationChannel and UpdateNotificationChannel.

      Args:
        request: (MonitoringProjectsNotificationChannelsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NotificationChannel) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/notificationChannels/{notificationChannelsId}',
        http_method='PATCH',
        method_id='monitoring.projects.notificationChannels.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v3/{+name}',
        request_field='notificationChannel',
        request_type_name='MonitoringProjectsNotificationChannelsPatchRequest',
        response_type_name='NotificationChannel',
        supports_download=False,
    )

    def SendVerificationCode(self, request, global_params=None):
      r"""Causes a verification code to be delivered to the channel. The code can then be supplied in VerifyNotificationChannel to verify the channel.

      Args:
        request: (MonitoringProjectsNotificationChannelsSendVerificationCodeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('SendVerificationCode')
      return self._RunMethod(
          config, request, global_params=global_params)

    SendVerificationCode.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/notificationChannels/{notificationChannelsId}:sendVerificationCode',
        http_method='POST',
        method_id='monitoring.projects.notificationChannels.sendVerificationCode',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}:sendVerificationCode',
        request_field='sendNotificationChannelVerificationCodeRequest',
        request_type_name='MonitoringProjectsNotificationChannelsSendVerificationCodeRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Verify(self, request, global_params=None):
      r"""Verifies a NotificationChannel by proving receipt of the code delivered to the channel as a result of calling SendNotificationChannelVerificationCode.

      Args:
        request: (MonitoringProjectsNotificationChannelsVerifyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NotificationChannel) The response message.
      """
      config = self.GetMethodConfig('Verify')
      return self._RunMethod(
          config, request, global_params=global_params)

    Verify.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/notificationChannels/{notificationChannelsId}:verify',
        http_method='POST',
        method_id='monitoring.projects.notificationChannels.verify',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}:verify',
        request_field='verifyNotificationChannelRequest',
        request_type_name='MonitoringProjectsNotificationChannelsVerifyRequest',
        response_type_name='NotificationChannel',
        supports_download=False,
    )

  class ProjectsSnoozesService(base_api.BaseApiService):
    """Service class for the projects_snoozes resource."""

    _NAME = 'projects_snoozes'

    def __init__(self, client):
      super(MonitoringV3.ProjectsSnoozesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a Snooze that will prevent alerts, which match the provided criteria, from being opened. The Snooze applies for a specific time interval.

      Args:
        request: (MonitoringProjectsSnoozesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Snooze) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/snoozes',
        http_method='POST',
        method_id='monitoring.projects.snoozes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v3/{+parent}/snoozes',
        request_field='snooze',
        request_type_name='MonitoringProjectsSnoozesCreateRequest',
        response_type_name='Snooze',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a Snooze by name.

      Args:
        request: (MonitoringProjectsSnoozesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Snooze) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/snoozes/{snoozesId}',
        http_method='GET',
        method_id='monitoring.projects.snoozes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='MonitoringProjectsSnoozesGetRequest',
        response_type_name='Snooze',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the Snoozes associated with a project. Can optionally pass in filter, which specifies predicates to match Snoozes.

      Args:
        request: (MonitoringProjectsSnoozesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSnoozesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/snoozes',
        http_method='GET',
        method_id='monitoring.projects.snoozes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v3/{+parent}/snoozes',
        request_field='',
        request_type_name='MonitoringProjectsSnoozesListRequest',
        response_type_name='ListSnoozesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a Snooze, identified by its name, with the parameters in the given Snooze object.

      Args:
        request: (MonitoringProjectsSnoozesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Snooze) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/snoozes/{snoozesId}',
        http_method='PATCH',
        method_id='monitoring.projects.snoozes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v3/{+name}',
        request_field='snooze',
        request_type_name='MonitoringProjectsSnoozesPatchRequest',
        response_type_name='Snooze',
        supports_download=False,
    )

  class ProjectsTimeSeriesService(base_api.BaseApiService):
    """Service class for the projects_timeSeries resource."""

    _NAME = 'projects_timeSeries'

    def __init__(self, client):
      super(MonitoringV3.ProjectsTimeSeriesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates or adds data to one or more time series. The response is empty if all time series in the request were written. If any time series could not be written, a corresponding failure message is included in the error response. This method does not support resource locations constraint of an organization policy (https://cloud.google.com/resource-manager/docs/organization-policy/defining-locations#setting_the_organization_policy).

      Args:
        request: (MonitoringProjectsTimeSeriesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/timeSeries',
        http_method='POST',
        method_id='monitoring.projects.timeSeries.create',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}/timeSeries',
        request_field='createTimeSeriesRequest',
        request_type_name='MonitoringProjectsTimeSeriesCreateRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def CreateService(self, request, global_params=None):
      r"""Creates or adds data to one or more service time series. A service time series is a time series for a metric from a Google Cloud service. The response is empty if all time series in the request were written. If any time series could not be written, a corresponding failure message is included in the error response. This endpoint rejects writes to user-defined metrics. This method is only for use by Google Cloud services. Use projects.timeSeries.create instead.

      Args:
        request: (MonitoringProjectsTimeSeriesCreateServiceRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('CreateService')
      return self._RunMethod(
          config, request, global_params=global_params)

    CreateService.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/timeSeries:createService',
        http_method='POST',
        method_id='monitoring.projects.timeSeries.createService',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}/timeSeries:createService',
        request_field='createTimeSeriesRequest',
        request_type_name='MonitoringProjectsTimeSeriesCreateServiceRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists time series that match a filter.

      Args:
        request: (MonitoringProjectsTimeSeriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListTimeSeriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/timeSeries',
        http_method='GET',
        method_id='monitoring.projects.timeSeries.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['aggregation_alignmentPeriod', 'aggregation_crossSeriesReducer', 'aggregation_groupByFields', 'aggregation_perSeriesAligner', 'filter', 'interval_endTime', 'interval_startTime', 'orderBy', 'pageSize', 'pageToken', 'secondaryAggregation_alignmentPeriod', 'secondaryAggregation_crossSeriesReducer', 'secondaryAggregation_groupByFields', 'secondaryAggregation_perSeriesAligner', 'view'],
        relative_path='v3/{+name}/timeSeries',
        request_field='',
        request_type_name='MonitoringProjectsTimeSeriesListRequest',
        response_type_name='ListTimeSeriesResponse',
        supports_download=False,
    )

    def Query(self, request, global_params=None):
      r"""Queries time series by using Monitoring Query Language (MQL). We recommend using PromQL instead of MQL. For more information about the status of MQL, see the MQL deprecation notice (https://cloud.google.com/stackdriver/docs/deprecations/mql).

      Args:
        request: (MonitoringProjectsTimeSeriesQueryRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (QueryTimeSeriesResponse) The response message.
      """
      config = self.GetMethodConfig('Query')
      return self._RunMethod(
          config, request, global_params=global_params)

    Query.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/timeSeries:query',
        http_method='POST',
        method_id='monitoring.projects.timeSeries.query',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}/timeSeries:query',
        request_field='queryTimeSeriesRequest',
        request_type_name='MonitoringProjectsTimeSeriesQueryRequest',
        response_type_name='QueryTimeSeriesResponse',
        supports_download=False,
    )

  class ProjectsUptimeCheckConfigsService(base_api.BaseApiService):
    """Service class for the projects_uptimeCheckConfigs resource."""

    _NAME = 'projects_uptimeCheckConfigs'

    def __init__(self, client):
      super(MonitoringV3.ProjectsUptimeCheckConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Uptime check configuration.

      Args:
        request: (MonitoringProjectsUptimeCheckConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (UptimeCheckConfig) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/uptimeCheckConfigs',
        http_method='POST',
        method_id='monitoring.projects.uptimeCheckConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v3/{+parent}/uptimeCheckConfigs',
        request_field='uptimeCheckConfig',
        request_type_name='MonitoringProjectsUptimeCheckConfigsCreateRequest',
        response_type_name='UptimeCheckConfig',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an Uptime check configuration. Note that this method will fail if the Uptime check configuration is referenced by an alert policy or other dependent configs that would be rendered invalid by the deletion.

      Args:
        request: (MonitoringProjectsUptimeCheckConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/uptimeCheckConfigs/{uptimeCheckConfigsId}',
        http_method='DELETE',
        method_id='monitoring.projects.uptimeCheckConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='MonitoringProjectsUptimeCheckConfigsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a single Uptime check configuration.

      Args:
        request: (MonitoringProjectsUptimeCheckConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (UptimeCheckConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/uptimeCheckConfigs/{uptimeCheckConfigsId}',
        http_method='GET',
        method_id='monitoring.projects.uptimeCheckConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='MonitoringProjectsUptimeCheckConfigsGetRequest',
        response_type_name='UptimeCheckConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the existing valid Uptime check configurations for the project (leaving out any invalid configurations).

      Args:
        request: (MonitoringProjectsUptimeCheckConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListUptimeCheckConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/uptimeCheckConfigs',
        http_method='GET',
        method_id='monitoring.projects.uptimeCheckConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v3/{+parent}/uptimeCheckConfigs',
        request_field='',
        request_type_name='MonitoringProjectsUptimeCheckConfigsListRequest',
        response_type_name='ListUptimeCheckConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an Uptime check configuration. You can either replace the entire configuration with a new one or replace only certain fields in the current configuration by specifying the fields to be updated via updateMask. Returns the updated configuration.

      Args:
        request: (MonitoringProjectsUptimeCheckConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (UptimeCheckConfig) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/projects/{projectsId}/uptimeCheckConfigs/{uptimeCheckConfigsId}',
        http_method='PATCH',
        method_id='monitoring.projects.uptimeCheckConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v3/{+name}',
        request_field='uptimeCheckConfig',
        request_type_name='MonitoringProjectsUptimeCheckConfigsPatchRequest',
        response_type_name='UptimeCheckConfig',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(MonitoringV3.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

  class ServicesServiceLevelObjectivesService(base_api.BaseApiService):
    """Service class for the services_serviceLevelObjectives resource."""

    _NAME = 'services_serviceLevelObjectives'

    def __init__(self, client):
      super(MonitoringV3.ServicesServiceLevelObjectivesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a ServiceLevelObjective for the given Service.

      Args:
        request: (MonitoringServicesServiceLevelObjectivesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServiceLevelObjective) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/{v3Id}/{v3Id1}/services/{servicesId}/serviceLevelObjectives',
        http_method='POST',
        method_id='monitoring.services.serviceLevelObjectives.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['serviceLevelObjectiveId'],
        relative_path='v3/{+parent}/serviceLevelObjectives',
        request_field='serviceLevelObjective',
        request_type_name='MonitoringServicesServiceLevelObjectivesCreateRequest',
        response_type_name='ServiceLevelObjective',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete the given ServiceLevelObjective.

      Args:
        request: (MonitoringServicesServiceLevelObjectivesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/{v3Id}/{v3Id1}/services/{servicesId}/serviceLevelObjectives/{serviceLevelObjectivesId}',
        http_method='DELETE',
        method_id='monitoring.services.serviceLevelObjectives.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='MonitoringServicesServiceLevelObjectivesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get a ServiceLevelObjective by name.

      Args:
        request: (MonitoringServicesServiceLevelObjectivesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServiceLevelObjective) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/{v3Id}/{v3Id1}/services/{servicesId}/serviceLevelObjectives/{serviceLevelObjectivesId}',
        http_method='GET',
        method_id='monitoring.services.serviceLevelObjectives.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='MonitoringServicesServiceLevelObjectivesGetRequest',
        response_type_name='ServiceLevelObjective',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List the ServiceLevelObjectives for the given Service.

      Args:
        request: (MonitoringServicesServiceLevelObjectivesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListServiceLevelObjectivesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/{v3Id}/{v3Id1}/services/{servicesId}/serviceLevelObjectives',
        http_method='GET',
        method_id='monitoring.services.serviceLevelObjectives.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'view'],
        relative_path='v3/{+parent}/serviceLevelObjectives',
        request_field='',
        request_type_name='MonitoringServicesServiceLevelObjectivesListRequest',
        response_type_name='ListServiceLevelObjectivesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update the given ServiceLevelObjective.

      Args:
        request: (MonitoringServicesServiceLevelObjectivesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServiceLevelObjective) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/{v3Id}/{v3Id1}/services/{servicesId}/serviceLevelObjectives/{serviceLevelObjectivesId}',
        http_method='PATCH',
        method_id='monitoring.services.serviceLevelObjectives.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v3/{+name}',
        request_field='serviceLevelObjective',
        request_type_name='MonitoringServicesServiceLevelObjectivesPatchRequest',
        response_type_name='ServiceLevelObjective',
        supports_download=False,
    )

  class ServicesService(base_api.BaseApiService):
    """Service class for the services resource."""

    _NAME = 'services'

    def __init__(self, client):
      super(MonitoringV3.ServicesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a Service.

      Args:
        request: (MonitoringServicesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Service) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/{v3Id}/{v3Id1}/services',
        http_method='POST',
        method_id='monitoring.services.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['serviceId'],
        relative_path='v3/{+parent}/services',
        request_field='service',
        request_type_name='MonitoringServicesCreateRequest',
        response_type_name='Service',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Soft delete this Service.

      Args:
        request: (MonitoringServicesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/{v3Id}/{v3Id1}/services/{servicesId}',
        http_method='DELETE',
        method_id='monitoring.services.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='MonitoringServicesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get the named Service.

      Args:
        request: (MonitoringServicesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Service) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/{v3Id}/{v3Id1}/services/{servicesId}',
        http_method='GET',
        method_id='monitoring.services.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v3/{+name}',
        request_field='',
        request_type_name='MonitoringServicesGetRequest',
        response_type_name='Service',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List Services for this Metrics Scope.

      Args:
        request: (MonitoringServicesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListServicesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/{v3Id}/{v3Id1}/services',
        http_method='GET',
        method_id='monitoring.services.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v3/{+parent}/services',
        request_field='',
        request_type_name='MonitoringServicesListRequest',
        response_type_name='ListServicesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update this Service.

      Args:
        request: (MonitoringServicesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Service) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v3/{v3Id}/{v3Id1}/services/{servicesId}',
        http_method='PATCH',
        method_id='monitoring.services.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v3/{+name}',
        request_field='service',
        request_type_name='MonitoringServicesPatchRequest',
        response_type_name='Service',
        supports_download=False,
    )

  class UptimeCheckIpsService(base_api.BaseApiService):
    """Service class for the uptimeCheckIps resource."""

    _NAME = 'uptimeCheckIps'

    def __init__(self, client):
      super(MonitoringV3.UptimeCheckIpsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Returns the list of IP addresses that checkers run from.

      Args:
        request: (MonitoringUptimeCheckIpsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListUptimeCheckIpsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='monitoring.uptimeCheckIps.list',
        ordered_params=[],
        path_params=[],
        query_params=['pageSize', 'pageToken'],
        relative_path='v3/uptimeCheckIps',
        request_field='',
        request_type_name='MonitoringUptimeCheckIpsListRequest',
        response_type_name='ListUptimeCheckIpsResponse',
        supports_download=False,
    )
