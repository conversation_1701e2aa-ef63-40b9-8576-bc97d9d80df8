"""Generated message classes for monitoring version v1.

Manages your Cloud Monitoring data and configurations.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'monitoring'


class Aggregation(_messages.Message):
  r"""Describes how to combine multiple time series to provide a different
  view of the data. Aggregation of time series is done in two steps. First,
  each time series in the set is aligned to the same time interval boundaries,
  then the set of time series is optionally reduced in number.Alignment
  consists of applying the per_series_aligner operation to each time series
  after its data has been divided into regular alignment_period time
  intervals. This process takes all of the data points in an alignment period,
  applies a mathematical transformation such as averaging, minimum, maximum,
  delta, etc., and converts them into a single data point per period.Reduction
  is when the aligned and transformed time series can optionally be combined,
  reducing the number of time series through similar mathematical
  transformations. Reduction involves applying a cross_series_reducer to all
  the time series, optionally sorting the time series into subsets with
  group_by_fields, and applying the reducer to each subset.The raw time series
  data can contain a huge amount of information from multiple sources.
  Alignment and reduction transforms this mass of data into a more manageable
  and representative collection of data, for example "the 95% latency across
  the average of all tasks in a cluster". This representative data can be more
  easily graphed and comprehended, and the individual time series data is
  still available for later drilldown. For more details, see Filtering and
  aggregation (https://cloud.google.com/monitoring/api/v3/aggregation).

  Enums:
    CrossSeriesReducerValueValuesEnum: The reduction operation to be used to
      combine time series into a single time series, where the value of each
      data point in the resulting series is a function of all the already
      aligned values in the input time series.Not all reducer operations can
      be applied to all time series. The valid choices depend on the
      metric_kind and the value_type of the original time series. Reduction
      can yield a time series with a different metric_kind or value_type than
      the input time series.Time series data must first be aligned (see
      per_series_aligner) in order to perform cross-time series reduction. If
      cross_series_reducer is specified, then per_series_aligner must be
      specified, and must not be ALIGN_NONE. An alignment_period must also be
      specified; otherwise, an error is returned.
    PerSeriesAlignerValueValuesEnum: An Aligner describes how to bring the
      data points in a single time series into temporal alignment. Except for
      ALIGN_NONE, all alignments cause all the data points in an
      alignment_period to be mathematically grouped together, resulting in a
      single data point for each alignment_period with end timestamp at the
      end of the period.Not all alignment operations may be applied to all
      time series. The valid choices depend on the metric_kind and value_type
      of the original time series. Alignment can change the metric_kind or the
      value_type of the time series.Time series data must be aligned in order
      to perform cross-time series reduction. If cross_series_reducer is
      specified, then per_series_aligner must be specified and not equal to
      ALIGN_NONE and alignment_period must be specified; otherwise, an error
      is returned.

  Fields:
    alignmentPeriod: The alignment_period specifies a time interval, in
      seconds, that is used to divide the data in all the time series into
      consistent blocks of time. This will be done before the per-series
      aligner can be applied to the data.The value must be at least 60
      seconds. If a per-series aligner other than ALIGN_NONE is specified,
      this field is required or an error is returned. If no per-series aligner
      is specified, or the aligner ALIGN_NONE is specified, then this field is
      ignored.The maximum value of the alignment_period is 2 years, or 104
      weeks.
    crossSeriesReducer: The reduction operation to be used to combine time
      series into a single time series, where the value of each data point in
      the resulting series is a function of all the already aligned values in
      the input time series.Not all reducer operations can be applied to all
      time series. The valid choices depend on the metric_kind and the
      value_type of the original time series. Reduction can yield a time
      series with a different metric_kind or value_type than the input time
      series.Time series data must first be aligned (see per_series_aligner)
      in order to perform cross-time series reduction. If cross_series_reducer
      is specified, then per_series_aligner must be specified, and must not be
      ALIGN_NONE. An alignment_period must also be specified; otherwise, an
      error is returned.
    groupByFields: The set of fields to preserve when cross_series_reducer is
      specified. The group_by_fields determine how the time series are
      partitioned into subsets prior to applying the aggregation operation.
      Each subset contains time series that have the same value for each of
      the grouping fields. Each individual time series is a member of exactly
      one subset. The cross_series_reducer is applied to each subset of time
      series. It is not possible to reduce across different resource types, so
      this field implicitly contains resource.type. Fields not specified in
      group_by_fields are aggregated away. If group_by_fields is not specified
      and all the time series have the same resource type, then the time
      series are aggregated into a single output time series. If
      cross_series_reducer is not defined, this field is ignored.
    perSeriesAligner: An Aligner describes how to bring the data points in a
      single time series into temporal alignment. Except for ALIGN_NONE, all
      alignments cause all the data points in an alignment_period to be
      mathematically grouped together, resulting in a single data point for
      each alignment_period with end timestamp at the end of the period.Not
      all alignment operations may be applied to all time series. The valid
      choices depend on the metric_kind and value_type of the original time
      series. Alignment can change the metric_kind or the value_type of the
      time series.Time series data must be aligned in order to perform cross-
      time series reduction. If cross_series_reducer is specified, then
      per_series_aligner must be specified and not equal to ALIGN_NONE and
      alignment_period must be specified; otherwise, an error is returned.
  """

  class CrossSeriesReducerValueValuesEnum(_messages.Enum):
    r"""The reduction operation to be used to combine time series into a
    single time series, where the value of each data point in the resulting
    series is a function of all the already aligned values in the input time
    series.Not all reducer operations can be applied to all time series. The
    valid choices depend on the metric_kind and the value_type of the original
    time series. Reduction can yield a time series with a different
    metric_kind or value_type than the input time series.Time series data must
    first be aligned (see per_series_aligner) in order to perform cross-time
    series reduction. If cross_series_reducer is specified, then
    per_series_aligner must be specified, and must not be ALIGN_NONE. An
    alignment_period must also be specified; otherwise, an error is returned.

    Values:
      REDUCE_NONE: No cross-time series reduction. The output of the Aligner
        is returned.
      REDUCE_MEAN: Reduce by computing the mean value across time series for
        each alignment period. This reducer is valid for DELTA and GAUGE
        metrics with numeric or distribution values. The value_type of the
        output is DOUBLE.
      REDUCE_MIN: Reduce by computing the minimum value across time series for
        each alignment period. This reducer is valid for DELTA and GAUGE
        metrics with numeric values. The value_type of the output is the same
        as the value_type of the input.
      REDUCE_MAX: Reduce by computing the maximum value across time series for
        each alignment period. This reducer is valid for DELTA and GAUGE
        metrics with numeric values. The value_type of the output is the same
        as the value_type of the input.
      REDUCE_SUM: Reduce by computing the sum across time series for each
        alignment period. This reducer is valid for DELTA and GAUGE metrics
        with numeric and distribution values. The value_type of the output is
        the same as the value_type of the input.
      REDUCE_STDDEV: Reduce by computing the standard deviation across time
        series for each alignment period. This reducer is valid for DELTA and
        GAUGE metrics with numeric or distribution values. The value_type of
        the output is DOUBLE.
      REDUCE_COUNT: Reduce by computing the number of data points across time
        series for each alignment period. This reducer is valid for DELTA and
        GAUGE metrics of numeric, Boolean, distribution, and string
        value_type. The value_type of the output is INT64.
      REDUCE_COUNT_TRUE: Reduce by computing the number of True-valued data
        points across time series for each alignment period. This reducer is
        valid for DELTA and GAUGE metrics of Boolean value_type. The
        value_type of the output is INT64.
      REDUCE_COUNT_FALSE: Reduce by computing the number of False-valued data
        points across time series for each alignment period. This reducer is
        valid for DELTA and GAUGE metrics of Boolean value_type. The
        value_type of the output is INT64.
      REDUCE_FRACTION_TRUE: Reduce by computing the ratio of the number of
        True-valued data points to the total number of data points for each
        alignment period. This reducer is valid for DELTA and GAUGE metrics of
        Boolean value_type. The output value is in the range 0.0, 1.0 and has
        value_type DOUBLE.
      REDUCE_PERCENTILE_99: Reduce by computing the 99th percentile
        (https://en.wikipedia.org/wiki/Percentile) of data points across time
        series for each alignment period. This reducer is valid for GAUGE and
        DELTA metrics of numeric and distribution type. The value of the
        output is DOUBLE.
      REDUCE_PERCENTILE_95: Reduce by computing the 95th percentile
        (https://en.wikipedia.org/wiki/Percentile) of data points across time
        series for each alignment period. This reducer is valid for GAUGE and
        DELTA metrics of numeric and distribution type. The value of the
        output is DOUBLE.
      REDUCE_PERCENTILE_50: Reduce by computing the 50th percentile
        (https://en.wikipedia.org/wiki/Percentile) of data points across time
        series for each alignment period. This reducer is valid for GAUGE and
        DELTA metrics of numeric and distribution type. The value of the
        output is DOUBLE.
      REDUCE_PERCENTILE_05: Reduce by computing the 5th percentile
        (https://en.wikipedia.org/wiki/Percentile) of data points across time
        series for each alignment period. This reducer is valid for GAUGE and
        DELTA metrics of numeric and distribution type. The value of the
        output is DOUBLE.
    """
    REDUCE_NONE = 0
    REDUCE_MEAN = 1
    REDUCE_MIN = 2
    REDUCE_MAX = 3
    REDUCE_SUM = 4
    REDUCE_STDDEV = 5
    REDUCE_COUNT = 6
    REDUCE_COUNT_TRUE = 7
    REDUCE_COUNT_FALSE = 8
    REDUCE_FRACTION_TRUE = 9
    REDUCE_PERCENTILE_99 = 10
    REDUCE_PERCENTILE_95 = 11
    REDUCE_PERCENTILE_50 = 12
    REDUCE_PERCENTILE_05 = 13

  class PerSeriesAlignerValueValuesEnum(_messages.Enum):
    r"""An Aligner describes how to bring the data points in a single time
    series into temporal alignment. Except for ALIGN_NONE, all alignments
    cause all the data points in an alignment_period to be mathematically
    grouped together, resulting in a single data point for each
    alignment_period with end timestamp at the end of the period.Not all
    alignment operations may be applied to all time series. The valid choices
    depend on the metric_kind and value_type of the original time series.
    Alignment can change the metric_kind or the value_type of the time
    series.Time series data must be aligned in order to perform cross-time
    series reduction. If cross_series_reducer is specified, then
    per_series_aligner must be specified and not equal to ALIGN_NONE and
    alignment_period must be specified; otherwise, an error is returned.

    Values:
      ALIGN_NONE: No alignment. Raw data is returned. Not valid if cross-
        series reduction is requested. The value_type of the result is the
        same as the value_type of the input.
      ALIGN_DELTA: Align and convert to DELTA. The output is delta = y1 -
        y0.This alignment is valid for CUMULATIVE and DELTA metrics. If the
        selected alignment period results in periods with no data, then the
        aligned value for such a period is created by interpolation. The
        value_type of the aligned result is the same as the value_type of the
        input.
      ALIGN_RATE: Align and convert to a rate. The result is computed as rate
        = (y1 - y0)/(t1 - t0), or "delta over time". Think of this aligner as
        providing the slope of the line that passes through the value at the
        start and at the end of the alignment_period.This aligner is valid for
        CUMULATIVE and DELTA metrics with numeric values. If the selected
        alignment period results in periods with no data, then the aligned
        value for such a period is created by interpolation. The output is a
        GAUGE metric with value_type DOUBLE.If, by "rate", you mean
        "percentage change", see the ALIGN_PERCENT_CHANGE aligner instead.
      ALIGN_INTERPOLATE: Align by interpolating between adjacent points around
        the alignment period boundary. This aligner is valid for GAUGE metrics
        with numeric values. The value_type of the aligned result is the same
        as the value_type of the input.
      ALIGN_NEXT_OLDER: Align by moving the most recent data point before the
        end of the alignment period to the boundary at the end of the
        alignment period. This aligner is valid for GAUGE metrics. The
        value_type of the aligned result is the same as the value_type of the
        input.
      ALIGN_MIN: Align the time series by returning the minimum value in each
        alignment period. This aligner is valid for GAUGE and DELTA metrics
        with numeric values. The value_type of the aligned result is the same
        as the value_type of the input.
      ALIGN_MAX: Align the time series by returning the maximum value in each
        alignment period. This aligner is valid for GAUGE and DELTA metrics
        with numeric values. The value_type of the aligned result is the same
        as the value_type of the input.
      ALIGN_MEAN: Align the time series by returning the mean value in each
        alignment period. This aligner is valid for GAUGE and DELTA metrics
        with numeric values. The value_type of the aligned result is DOUBLE.
      ALIGN_COUNT: Align the time series by returning the number of values in
        each alignment period. This aligner is valid for GAUGE and DELTA
        metrics with numeric or Boolean values. The value_type of the aligned
        result is INT64.
      ALIGN_SUM: Align the time series by returning the sum of the values in
        each alignment period. This aligner is valid for GAUGE and DELTA
        metrics with numeric and distribution values. The value_type of the
        aligned result is the same as the value_type of the input.
      ALIGN_STDDEV: Align the time series by returning the standard deviation
        of the values in each alignment period. This aligner is valid for
        GAUGE and DELTA metrics with numeric values. The value_type of the
        output is DOUBLE.
      ALIGN_COUNT_TRUE: Align the time series by returning the number of True
        values in each alignment period. This aligner is valid for GAUGE
        metrics with Boolean values. The value_type of the output is INT64.
      ALIGN_COUNT_FALSE: Align the time series by returning the number of
        False values in each alignment period. This aligner is valid for GAUGE
        metrics with Boolean values. The value_type of the output is INT64.
      ALIGN_FRACTION_TRUE: Align the time series by returning the ratio of the
        number of True values to the total number of values in each alignment
        period. This aligner is valid for GAUGE metrics with Boolean values.
        The output value is in the range 0.0, 1.0 and has value_type DOUBLE.
      ALIGN_PERCENTILE_99: Align the time series by using percentile
        aggregation (https://en.wikipedia.org/wiki/Percentile). The resulting
        data point in each alignment period is the 99th percentile of all data
        points in the period. This aligner is valid for GAUGE and DELTA
        metrics with distribution values. The output is a GAUGE metric with
        value_type DOUBLE.
      ALIGN_PERCENTILE_95: Align the time series by using percentile
        aggregation (https://en.wikipedia.org/wiki/Percentile). The resulting
        data point in each alignment period is the 95th percentile of all data
        points in the period. This aligner is valid for GAUGE and DELTA
        metrics with distribution values. The output is a GAUGE metric with
        value_type DOUBLE.
      ALIGN_PERCENTILE_50: Align the time series by using percentile
        aggregation (https://en.wikipedia.org/wiki/Percentile). The resulting
        data point in each alignment period is the 50th percentile of all data
        points in the period. This aligner is valid for GAUGE and DELTA
        metrics with distribution values. The output is a GAUGE metric with
        value_type DOUBLE.
      ALIGN_PERCENTILE_05: Align the time series by using percentile
        aggregation (https://en.wikipedia.org/wiki/Percentile). The resulting
        data point in each alignment period is the 5th percentile of all data
        points in the period. This aligner is valid for GAUGE and DELTA
        metrics with distribution values. The output is a GAUGE metric with
        value_type DOUBLE.
      ALIGN_PERCENT_CHANGE: Align and convert to a percentage change. This
        aligner is valid for GAUGE and DELTA metrics with numeric values. This
        alignment returns ((current - previous)/previous) * 100, where the
        value of previous is determined based on the alignment_period.If the
        values of current and previous are both 0, then the returned value is
        0. If only previous is 0, the returned value is infinity.A 10-minute
        moving mean is computed at each point of the alignment period prior to
        the above calculation to smooth the metric and prevent false positives
        from very short-lived spikes. The moving mean is only applicable for
        data whose values are >= 0. Any values < 0 are treated as a missing
        datapoint, and are ignored. While DELTA metrics are accepted by this
        alignment, special care should be taken that the values for the metric
        will always be positive. The output is a GAUGE metric with value_type
        DOUBLE.
    """
    ALIGN_NONE = 0
    ALIGN_DELTA = 1
    ALIGN_RATE = 2
    ALIGN_INTERPOLATE = 3
    ALIGN_NEXT_OLDER = 4
    ALIGN_MIN = 5
    ALIGN_MAX = 6
    ALIGN_MEAN = 7
    ALIGN_COUNT = 8
    ALIGN_SUM = 9
    ALIGN_STDDEV = 10
    ALIGN_COUNT_TRUE = 11
    ALIGN_COUNT_FALSE = 12
    ALIGN_FRACTION_TRUE = 13
    ALIGN_PERCENTILE_99 = 14
    ALIGN_PERCENTILE_95 = 15
    ALIGN_PERCENTILE_50 = 16
    ALIGN_PERCENTILE_05 = 17
    ALIGN_PERCENT_CHANGE = 18

  alignmentPeriod = _messages.StringField(1)
  crossSeriesReducer = _messages.EnumField('CrossSeriesReducerValueValuesEnum', 2)
  groupByFields = _messages.StringField(3, repeated=True)
  perSeriesAligner = _messages.EnumField('PerSeriesAlignerValueValuesEnum', 4)


class AggregationFunction(_messages.Message):
  r"""Preview: An identifier for an aggregation function. Aggregation
  functions are SQL functions that group or transform data from multiple
  points to a single point. This is a preview feature and may be subject to
  change before final release.

  Fields:
    parameters: Optional. Parameters applied to the aggregation function. Only
      used for functions that require them.
    type: Required. The type of aggregation function, must be one of the
      following: "none" - no function. "percentile" - APPROX_QUANTILES() - 1
      parameter numeric value "average" - AVG() "count" - COUNT() "count-
      distinct" - COUNT(DISTINCT) "count-distinct-approx" -
      APPROX_COUNT_DISTINCT() "max" - MAX() "min" - MIN() "sum" - SUM()
  """

  parameters = _messages.MessageField('Parameter', 1, repeated=True)
  type = _messages.StringField(2)


class AlertChart(_messages.Message):
  r"""A chart that displays alert policy data.

  Fields:
    name: Required. The resource name of the alert policy. The format is:
      projects/[PROJECT_ID_OR_NUMBER]/alertPolicies/[ALERT_POLICY_ID]
  """

  name = _messages.StringField(1)


class Axis(_messages.Message):
  r"""A chart axis.

  Enums:
    ScaleValueValuesEnum: The axis scale. By default, a linear scale is used.

  Fields:
    label: The label of the axis.
    scale: The axis scale. By default, a linear scale is used.
  """

  class ScaleValueValuesEnum(_messages.Enum):
    r"""The axis scale. By default, a linear scale is used.

    Values:
      SCALE_UNSPECIFIED: Scale is unspecified. The view will default to
        LINEAR.
      LINEAR: Linear scale.
      LOG10: Logarithmic scale (base 10).
    """
    SCALE_UNSPECIFIED = 0
    LINEAR = 1
    LOG10 = 2

  label = _messages.StringField(1)
  scale = _messages.EnumField('ScaleValueValuesEnum', 2)


class Breakdown(_messages.Message):
  r"""Preview: A breakdown is an aggregation applied to the measures over a
  specified column. A breakdown can result in multiple series across a
  category for the provided measure. This is a preview feature and may be
  subject to change before final release.

  Enums:
    SortOrderValueValuesEnum: Required. The sort order is applied to the
      values of the breakdown column.

  Fields:
    aggregationFunction: Required. The Aggregation function is applied across
      all data in each breakdown created.
    column: Required. The name of the column in the dataset containing the
      breakdown values.
    limit: Required. A limit to the number of breakdowns. If set to zero then
      all possible breakdowns are applied. The list of breakdowns is dependent
      on the value of the sort_order field.
    sortOrder: Required. The sort order is applied to the values of the
      breakdown column.
  """

  class SortOrderValueValuesEnum(_messages.Enum):
    r"""Required. The sort order is applied to the values of the breakdown
    column.

    Values:
      SORT_ORDER_UNSPECIFIED: An unspecified sort order. This option is
        invalid when sorting is required.
      SORT_ORDER_NONE: No sorting is applied.
      SORT_ORDER_ASCENDING: The lowest-valued entries are selected first.
      SORT_ORDER_DESCENDING: The highest-valued entries are selected first.
    """
    SORT_ORDER_UNSPECIFIED = 0
    SORT_ORDER_NONE = 1
    SORT_ORDER_ASCENDING = 2
    SORT_ORDER_DESCENDING = 3

  aggregationFunction = _messages.MessageField('AggregationFunction', 1)
  column = _messages.StringField(2)
  limit = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  sortOrder = _messages.EnumField('SortOrderValueValuesEnum', 4)


class ChartOptions(_messages.Message):
  r"""Options to control visual rendering of a chart.

  Enums:
    ModeValueValuesEnum: The chart mode.

  Fields:
    displayHorizontal: Preview: Configures whether the charted values are
      shown on the horizontal or vertical axis. By default, values are
      represented the vertical axis. This is a preview feature and may be
      subject to change before final release.
    mode: The chart mode.
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""The chart mode.

    Values:
      MODE_UNSPECIFIED: Mode is unspecified. The view will default to COLOR.
      COLOR: The chart distinguishes data series using different color. Line
        colors may get reused when there are many lines in the chart.
      X_RAY: The chart uses the Stackdriver x-ray mode, in which each data set
        is plotted using the same semi-transparent color.
      STATS: The chart displays statistics such as average, median, 95th
        percentile, and more.
    """
    MODE_UNSPECIFIED = 0
    COLOR = 1
    X_RAY = 2
    STATS = 3

  displayHorizontal = _messages.BooleanField(1)
  mode = _messages.EnumField('ModeValueValuesEnum', 2)


class CollapsibleGroup(_messages.Message):
  r"""A widget that groups the other widgets. All widgets that are within the
  area spanned by the grouping widget are considered member widgets.

  Fields:
    collapsed: The collapsed state of the widget on first page load.
  """

  collapsed = _messages.BooleanField(1)


class Column(_messages.Message):
  r"""Defines the layout properties and content for a column.

  Fields:
    weight: The relative weight of this column. The column weight is used to
      adjust the width of columns on the screen (relative to peers). Greater
      the weight, greater the width of the column on the screen. If omitted, a
      value of 1 is used while rendering.
    widgets: The display widgets arranged vertically in this column.
  """

  weight = _messages.IntegerField(1)
  widgets = _messages.MessageField('Widget', 2, repeated=True)


class ColumnLayout(_messages.Message):
  r"""A simplified layout that divides the available space into vertical
  columns and arranges a set of widgets vertically in each column.

  Fields:
    columns: The columns of content to display.
  """

  columns = _messages.MessageField('Column', 1, repeated=True)


class ColumnSettings(_messages.Message):
  r"""The persistent settings for a table's columns.

  Enums:
    AlignmentValueValuesEnum: Optional. Whether the column should be left /
      middle / right aligned

  Fields:
    alignment: Optional. Whether the column should be left / middle / right
      aligned
    column: Required. The id of the column.
    displayName: Optional. Display name of the column
    thresholds: Optional. The thresholds used to determine how the table cell
      should be rendered given the time series' current value.
    visible: Required. Whether the column should be visible on page load.
  """

  class AlignmentValueValuesEnum(_messages.Enum):
    r"""Optional. Whether the column should be left / middle / right aligned

    Values:
      CELL_ALIGNMENT_UNSPECIFIED: No horizontal alignment specified; fall back
        to the default behavior. Label values are left aligned. Numeric values
        are right aligned.
      LEFT: Left-align
      CENTER: Center-align
      RIGHT: Right-align
    """
    CELL_ALIGNMENT_UNSPECIFIED = 0
    LEFT = 1
    CENTER = 2
    RIGHT = 3

  alignment = _messages.EnumField('AlignmentValueValuesEnum', 1)
  column = _messages.StringField(2)
  displayName = _messages.StringField(3)
  thresholds = _messages.MessageField('Threshold', 4, repeated=True)
  visible = _messages.BooleanField(5)


class ColumnSortingOptions(_messages.Message):
  r"""Data structure to storing column's sort strategy

  Enums:
    DirectionValueValuesEnum: Optional. A sorting direction that determines
      ascending or descending order. This is a legacy field kept for backwards
      compatibility with table.

  Fields:
    column: Optional. Column name to sort data by
    direction: Optional. A sorting direction that determines ascending or
      descending order. This is a legacy field kept for backwards
      compatibility with table.
  """

  class DirectionValueValuesEnum(_messages.Enum):
    r"""Optional. A sorting direction that determines ascending or descending
    order. This is a legacy field kept for backwards compatibility with table.

    Values:
      SORT_ORDER_UNSPECIFIED: An unspecified sort order. This option is
        invalid when sorting is required.
      SORT_ORDER_NONE: No sorting is applied.
      SORT_ORDER_ASCENDING: The lowest-valued entries are selected first.
      SORT_ORDER_DESCENDING: The highest-valued entries are selected first.
    """
    SORT_ORDER_UNSPECIFIED = 0
    SORT_ORDER_NONE = 1
    SORT_ORDER_ASCENDING = 2
    SORT_ORDER_DESCENDING = 3

  column = _messages.StringField(1)
  direction = _messages.EnumField('DirectionValueValuesEnum', 2)


class Dashboard(_messages.Message):
  r"""A Google Stackdriver dashboard. Dashboards define the content and layout
  of pages in the Stackdriver web application.

  Messages:
    LabelsValue: Labels applied to the dashboard

  Fields:
    annotations: Configuration for event annotations to display on this
      dashboard.
    columnLayout: The content is divided into equally spaced columns and the
      widgets are arranged vertically.
    dashboardFilters: Filters to reduce the amount of data charted based on
      the filter criteria.
    displayName: Required. The mutable, human-readable name.
    etag: etag is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. An
      etag is returned in the response to GetDashboard, and users are expected
      to put that etag in the request to UpdateDashboard to ensure that their
      change will be applied to the same version of the Dashboard
      configuration. The field should not be passed during dashboard creation.
    gridLayout: Content is arranged with a basic layout that re-flows a simple
      list of informational elements like widgets or tiles.
    labels: Labels applied to the dashboard
    mosaicLayout: The content is arranged as a grid of tiles, with each
      content widget occupying one or more grid blocks.
    name: Identifier. The resource name of the dashboard.
    rowLayout: The content is divided into equally spaced rows and the widgets
      are arranged horizontally.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels applied to the dashboard

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('DashboardAnnotations', 1)
  columnLayout = _messages.MessageField('ColumnLayout', 2)
  dashboardFilters = _messages.MessageField('DashboardFilter', 3, repeated=True)
  displayName = _messages.StringField(4)
  etag = _messages.StringField(5)
  gridLayout = _messages.MessageField('GridLayout', 6)
  labels = _messages.MessageField('LabelsValue', 7)
  mosaicLayout = _messages.MessageField('MosaicLayout', 8)
  name = _messages.StringField(9)
  rowLayout = _messages.MessageField('RowLayout', 10)


class DashboardAnnotations(_messages.Message):
  r"""Dashboard-level configuration for annotations

  Fields:
    defaultResourceNames: Dashboard level defaults for names of logging
      resources to search for events. Currently only projects are supported.
      Each individual EventAnnotation may have its own overrides. If both this
      field and the per annotation field is empty, then the scoping project is
      used. Limit: 50 projects. For example: "projects/some-project-id"
    eventAnnotations: List of annotation configurations for this dashboard.
      Each entry specifies one event type.
  """

  defaultResourceNames = _messages.StringField(1, repeated=True)
  eventAnnotations = _messages.MessageField('EventAnnotation', 2, repeated=True)


class DashboardFilter(_messages.Message):
  r"""A filter to reduce the amount of data charted in relevant widgets.

  Enums:
    FilterTypeValueValuesEnum: The specified filter type
    ValueTypeValueValuesEnum: The type of the filter value. If value_type is
      not provided, it will be inferred from the default_value. If neither
      value_type nor default_value is provided, value_type will be set to
      STRING by default.

  Fields:
    filterType: The specified filter type
    labelKey: Optional. The key for the label. This must be omitted if the
      filter_type is VALUE_ONLY but is required otherwise.
    stringArray: A list of possible string values for the filter
    stringArrayValue: An array of variable-length string values. If this field
      is set, value_type must be set to STRING_ARRAY or VALUE_TYPE_UNSPECIFIED
    stringValue: A variable-length string value. If this field is set,
      value_type must be set to STRING or VALUE_TYPE_UNSPECIFIED
    templateVariable: The placeholder text that can be referenced in a filter
      string or MQL query. If omitted, the dashboard filter will be applied to
      all relevant widgets in the dashboard.
    timeSeriesQuery: A query to run to fetch possible values for the filter.
      Only OpsAnalyticsQueries are supported
    valueType: The type of the filter value. If value_type is not provided, it
      will be inferred from the default_value. If neither value_type nor
      default_value is provided, value_type will be set to STRING by default.
  """

  class FilterTypeValueValuesEnum(_messages.Enum):
    r"""The specified filter type

    Values:
      FILTER_TYPE_UNSPECIFIED: Filter type is unspecified. This is not valid
        in a well-formed request.
      RESOURCE_LABEL: Filter on a resource label value
      METRIC_LABEL: Filter on a metrics label value
      USER_METADATA_LABEL: Filter on a user metadata label value
      SYSTEM_METADATA_LABEL: Filter on a system metadata label value
      GROUP: Filter on a group id
      VALUE_ONLY: Filter that only contains a value. The label_key field must
        be unset for filters of this type.
    """
    FILTER_TYPE_UNSPECIFIED = 0
    RESOURCE_LABEL = 1
    METRIC_LABEL = 2
    USER_METADATA_LABEL = 3
    SYSTEM_METADATA_LABEL = 4
    GROUP = 5
    VALUE_ONLY = 6

  class ValueTypeValueValuesEnum(_messages.Enum):
    r"""The type of the filter value. If value_type is not provided, it will
    be inferred from the default_value. If neither value_type nor
    default_value is provided, value_type will be set to STRING by default.

    Values:
      VALUE_TYPE_UNSPECIFIED: Value type is unspecified
      STRING: String type
      STRING_ARRAY: String array type
    """
    VALUE_TYPE_UNSPECIFIED = 0
    STRING = 1
    STRING_ARRAY = 2

  filterType = _messages.EnumField('FilterTypeValueValuesEnum', 1)
  labelKey = _messages.StringField(2)
  stringArray = _messages.MessageField('StringArray', 3)
  stringArrayValue = _messages.MessageField('StringArray', 4)
  stringValue = _messages.StringField(5)
  templateVariable = _messages.StringField(6)
  timeSeriesQuery = _messages.MessageField('TimeSeriesQuery', 7)
  valueType = _messages.EnumField('ValueTypeValueValuesEnum', 8)


class DataSet(_messages.Message):
  r"""Groups a time series query definition with charting options.

  Enums:
    PlotTypeValueValuesEnum: How this data should be plotted on the chart.
    TargetAxisValueValuesEnum: Optional. The target axis to use for plotting
      the metric.

  Fields:
    breakdowns: Optional. The collection of breakdowns to be applied to the
      dataset.
    dimensions: Optional. A collection of dimension columns.
    legendTemplate: A template string for naming TimeSeries in the resulting
      data set. This should be a string with interpolations of the form
      ${label_name}, which will resolve to the label's value.
    measures: Optional. A collection of measures.
    minAlignmentPeriod: Optional. The lower bound on data point frequency for
      this data set, implemented by specifying the minimum alignment period to
      use in a time series query For example, if the data is published once
      every 10 minutes, the min_alignment_period should be at least 10
      minutes. It would not make sense to fetch and align data at one minute
      intervals.
    plotType: How this data should be plotted on the chart.
    sort: Optional. A collection of sort options, affects the order of the
      data and legend.
    targetAxis: Optional. The target axis to use for plotting the metric.
    timeSeriesQuery: Required. Fields for querying time series data from the
      Stackdriver metrics API.
  """

  class PlotTypeValueValuesEnum(_messages.Enum):
    r"""How this data should be plotted on the chart.

    Values:
      PLOT_TYPE_UNSPECIFIED: Plot type is unspecified. The view will default
        to LINE.
      LINE: The data is plotted as a set of lines (one line per series).
      STACKED_AREA: The data is plotted as a set of filled areas (one area per
        series), with the areas stacked vertically (the base of each area is
        the top of its predecessor, and the base of the first area is the
        x-axis). Since the areas do not overlap, each is filled with a
        different opaque color.
      STACKED_BAR: The data is plotted as a set of rectangular boxes (one box
        per series), with the boxes stacked vertically (the base of each box
        is the top of its predecessor, and the base of the first box is the
        x-axis). Since the boxes do not overlap, each is filled with a
        different opaque color.
      HEATMAP: The data is plotted as a heatmap. The series being plotted must
        have a DISTRIBUTION value type. The value of each bucket in the
        distribution is displayed as a color. This type is not currently
        available in the Stackdriver Monitoring application.
    """
    PLOT_TYPE_UNSPECIFIED = 0
    LINE = 1
    STACKED_AREA = 2
    STACKED_BAR = 3
    HEATMAP = 4

  class TargetAxisValueValuesEnum(_messages.Enum):
    r"""Optional. The target axis to use for plotting the metric.

    Values:
      TARGET_AXIS_UNSPECIFIED: The target axis was not specified. Defaults to
        Y1.
      Y1: The y_axis (the right axis of chart).
      Y2: The y2_axis (the left axis of chart).
    """
    TARGET_AXIS_UNSPECIFIED = 0
    Y1 = 1
    Y2 = 2

  breakdowns = _messages.MessageField('Breakdown', 1, repeated=True)
  dimensions = _messages.MessageField('Dimension', 2, repeated=True)
  legendTemplate = _messages.StringField(3)
  measures = _messages.MessageField('Measure', 4, repeated=True)
  minAlignmentPeriod = _messages.StringField(5)
  plotType = _messages.EnumField('PlotTypeValueValuesEnum', 6)
  sort = _messages.MessageField('ColumnSortingOptions', 7, repeated=True)
  targetAxis = _messages.EnumField('TargetAxisValueValuesEnum', 8)
  timeSeriesQuery = _messages.MessageField('TimeSeriesQuery', 9)


class Dimension(_messages.Message):
  r"""A chart dimension. Dimensions are a structured label, class, or category
  for a set of measurements in your data.

  Enums:
    SortOrderValueValuesEnum: The sort order applied to the sort column.

  Fields:
    column: Required. The name of the column in the source SQL query that is
      used to chart the dimension.
    columnType: Optional. The type of the dimension column. This is relevant
      only if one of the bin_size fields is set. If it is empty, the type
      TIMESTAMP or INT64 will be assumed based on which bin_size field is set.
      If populated, this should be set to one of the following types: DATE,
      TIME, DATETIME, TIMESTAMP, BIGNUMERIC, INT64, NUMERIC, FLOAT64.
    floatBinSize: Optional. float_bin_size is used when the column type used
      for a dimension is a floating point numeric column.
    maxBinCount: A limit to the number of bins generated. When 0 is specified,
      the maximum count is not enforced.
    numericBinSize: numeric_bin_size is used when the column type used for a
      dimension is numeric or string.
    sortColumn: The column name to sort on for binning. This column can be the
      same column as this dimension or any other column used as a measure in
      the results. If sort_order is set to NONE, then this value is not used.
    sortOrder: The sort order applied to the sort column.
    timeBinSize: time_bin_size is used when the data type specified by column
      is a time type and the bin size is determined by a time duration. If
      column_type is DATE, this must be a whole value multiple of 1 day. If
      column_type is TIME, this must be less than or equal to 24 hours.
  """

  class SortOrderValueValuesEnum(_messages.Enum):
    r"""The sort order applied to the sort column.

    Values:
      SORT_ORDER_UNSPECIFIED: An unspecified sort order. This option is
        invalid when sorting is required.
      SORT_ORDER_NONE: No sorting is applied.
      SORT_ORDER_ASCENDING: The lowest-valued entries are selected first.
      SORT_ORDER_DESCENDING: The highest-valued entries are selected first.
    """
    SORT_ORDER_UNSPECIFIED = 0
    SORT_ORDER_NONE = 1
    SORT_ORDER_ASCENDING = 2
    SORT_ORDER_DESCENDING = 3

  column = _messages.StringField(1)
  columnType = _messages.StringField(2)
  floatBinSize = _messages.FloatField(3)
  maxBinCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  numericBinSize = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  sortColumn = _messages.StringField(6)
  sortOrder = _messages.EnumField('SortOrderValueValuesEnum', 7)
  timeBinSize = _messages.StringField(8)


class DroppedLabels(_messages.Message):
  r"""A set of (label, value) pairs that were removed from a Distribution time
  series during aggregation and then added as an attachment to a
  Distribution.Exemplar.The full label set for the exemplars is constructed by
  using the dropped pairs in combination with the label values that remain on
  the aggregated Distribution time series. The constructed full label set can
  be used to identify the specific entity, such as the instance or job, which
  might be contributing to a long-tail. However, with dropped labels, the
  storage requirements are reduced because only the aggregated distribution
  values for a large group of time series are stored.Note that there are no
  guarantees on ordering of the labels from exemplar-to-exemplar and from
  distribution-to-distribution in the same stream, and there may be
  duplicates. It is up to clients to resolve any ambiguities.

  Messages:
    LabelValue: Map from label to its value, for all labels dropped in any
      aggregation.

  Fields:
    label: Map from label to its value, for all labels dropped in any
      aggregation.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelValue(_messages.Message):
    r"""Map from label to its value, for all labels dropped in any
    aggregation.

    Messages:
      AdditionalProperty: An additional property for a LabelValue object.

    Fields:
      additionalProperties: Additional properties of type LabelValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  label = _messages.MessageField('LabelValue', 1)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class ErrorReportingPanel(_messages.Message):
  r"""A widget that displays a list of error groups.

  Fields:
    projectNames: The resource name of the Google Cloud Platform project.
      Written as projects/{projectID} or projects/{projectNumber}, where
      {projectID} and {projectNumber} can be found in the Google Cloud console
      (https://support.google.com/cloud/answer/6158840).Examples: projects/my-
      project-123, projects/5551234.
    services: An identifier of the service, such as the name of the
      executable, job, or Google App Engine service name. This field is
      expected to have a low number of values that are relatively stable over
      time, as opposed to version, which can be changed whenever new code is
      deployed.Contains the service name for error reports extracted from
      Google App Engine logs or default if the App Engine default service is
      used.
    versions: Represents the source code version that the developer provided,
      which could represent a version label or a Git SHA-1 hash, for example.
      For App Engine standard environment, the version is set to the version
      of the app.
  """

  projectNames = _messages.StringField(1, repeated=True)
  services = _messages.StringField(2, repeated=True)
  versions = _messages.StringField(3, repeated=True)


class EventAnnotation(_messages.Message):
  r"""Annotation configuration for one event type on a dashboard

  Enums:
    EventTypeValueValuesEnum: The type of event to display.

  Fields:
    displayName: Solely for UI display. Should not be used programmatically.
    enabled: Whether or not to show the events on the dashboard by default
    eventType: The type of event to display.
    filter: string filtering the events - event dependant. Example values:
      "resource.labels.pod_name = 'pod-1'"
      "protoPayload.authenticationInfo.principalEmail='<EMAIL>'"
    resourceNames: Per annotation level override for the names of logging
      resources to search for events. Currently only projects are supported.
      If both this field and the per annotation field is empty, it will
      default to the host project. Limit: 50 projects. For example:
      "projects/another-project-id"
  """

  class EventTypeValueValuesEnum(_messages.Enum):
    r"""The type of event to display.

    Values:
      EVENT_TYPE_UNSPECIFIED: No event type specified.
      GKE_WORKLOAD_DEPLOYMENT: Patch/update of GKE workload.
      GKE_POD_CRASH: Crash events for a GKE Pod.
      GKE_POD_UNSCHEDULABLE: Scheduling failures for GKE Pods.
      GKE_CONTAINER_CREATION_FAILED: Failure to create a GKE container.
      GKE_CLUSTER_CREATE_DELETE: Create/delete of a GKE cluster.
      GKE_CLUSTER_UPDATE: Update of a GKE cluster.
      GKE_NODE_POOL_UPDATE: Update of a GKE node pool.
      GKE_CLUSTER_AUTOSCALER: GKE cluster autoscaler event.
      GKE_POD_AUTOSCALER: GKE pod autoscaler event.
      VM_TERMINATION: Termination of a virtual machine.
      VM_GUEST_OS_ERROR: Guest OS error on a virtual machine.
      VM_START_FAILED: Start failure on a virtual machine.
      MIG_UPDATE: Update of a managed instance group.
      MIG_AUTOSCALER: Autoscaler event for a managed instance group.
      CLOUD_RUN_DEPLOYMENT: New deployment of a Cloud Run service.
      CLOUD_SQL_FAILOVER: Failover of a Cloud SQL instance.
      CLOUD_SQL_START_STOP: Start/stop of a Cloud SQL instance.
      CLOUD_SQL_STORAGE: Storage event for a Cloud SQL instance.
      UPTIME_CHECK_FAILURE: Failure of a Cloud Monitoring uptime check.
      CLOUD_ALERTING_ALERT: Alerts from Cloud Alerting
      SERVICE_HEALTH_INCIDENT: Incidents from Service Health
      SAP_BACKINT: Agent for SAP Backint related events.
      SAP_AVAILABILITY: Agent for SAP availability related events.
      SAP_OPERATIONS: Agent for SAP operations related events.
    """
    EVENT_TYPE_UNSPECIFIED = 0
    GKE_WORKLOAD_DEPLOYMENT = 1
    GKE_POD_CRASH = 2
    GKE_POD_UNSCHEDULABLE = 3
    GKE_CONTAINER_CREATION_FAILED = 4
    GKE_CLUSTER_CREATE_DELETE = 5
    GKE_CLUSTER_UPDATE = 6
    GKE_NODE_POOL_UPDATE = 7
    GKE_CLUSTER_AUTOSCALER = 8
    GKE_POD_AUTOSCALER = 9
    VM_TERMINATION = 10
    VM_GUEST_OS_ERROR = 11
    VM_START_FAILED = 12
    MIG_UPDATE = 13
    MIG_AUTOSCALER = 14
    CLOUD_RUN_DEPLOYMENT = 15
    CLOUD_SQL_FAILOVER = 16
    CLOUD_SQL_START_STOP = 17
    CLOUD_SQL_STORAGE = 18
    UPTIME_CHECK_FAILURE = 19
    CLOUD_ALERTING_ALERT = 20
    SERVICE_HEALTH_INCIDENT = 21
    SAP_BACKINT = 22
    SAP_AVAILABILITY = 23
    SAP_OPERATIONS = 24

  displayName = _messages.StringField(1)
  enabled = _messages.BooleanField(2)
  eventType = _messages.EnumField('EventTypeValueValuesEnum', 3)
  filter = _messages.StringField(4)
  resourceNames = _messages.StringField(5, repeated=True)


class Field(_messages.Message):
  r"""A single field of a message type.New usages of this message as an
  alternative to FieldDescriptorProto are strongly discouraged. This message
  does not reliability preserve all information necessary to model the schema
  and preserve semantics. Instead make use of FileDescriptorSet which
  preserves the necessary information.

  Enums:
    CardinalityValueValuesEnum: The field cardinality.
    KindValueValuesEnum: The field type.

  Fields:
    cardinality: The field cardinality.
    defaultValue: The string value of the default value of this field. Proto2
      syntax only.
    jsonName: The field JSON name.
    kind: The field type.
    name: The field name.
    number: The field number.
    oneofIndex: The index of the field type in Type.oneofs, for message or
      enumeration types. The first type has index 1; zero means the type is
      not in the list.
    options: The protocol buffer options.
    packed: Whether to use alternative packed wire representation.
    typeUrl: The field type URL, without the scheme, for message or
      enumeration types. Example:
      "type.googleapis.com/google.protobuf.Timestamp".
  """

  class CardinalityValueValuesEnum(_messages.Enum):
    r"""The field cardinality.

    Values:
      CARDINALITY_UNKNOWN: For fields with unknown cardinality.
      CARDINALITY_OPTIONAL: For optional fields.
      CARDINALITY_REQUIRED: For required fields. Proto2 syntax only.
      CARDINALITY_REPEATED: For repeated fields.
    """
    CARDINALITY_UNKNOWN = 0
    CARDINALITY_OPTIONAL = 1
    CARDINALITY_REQUIRED = 2
    CARDINALITY_REPEATED = 3

  class KindValueValuesEnum(_messages.Enum):
    r"""The field type.

    Values:
      TYPE_UNKNOWN: Field type unknown.
      TYPE_DOUBLE: Field type double.
      TYPE_FLOAT: Field type float.
      TYPE_INT64: Field type int64.
      TYPE_UINT64: Field type uint64.
      TYPE_INT32: Field type int32.
      TYPE_FIXED64: Field type fixed64.
      TYPE_FIXED32: Field type fixed32.
      TYPE_BOOL: Field type bool.
      TYPE_STRING: Field type string.
      TYPE_GROUP: Field type group. Proto2 syntax only, and deprecated.
      TYPE_MESSAGE: Field type message.
      TYPE_BYTES: Field type bytes.
      TYPE_UINT32: Field type uint32.
      TYPE_ENUM: Field type enum.
      TYPE_SFIXED32: Field type sfixed32.
      TYPE_SFIXED64: Field type sfixed64.
      TYPE_SINT32: Field type sint32.
      TYPE_SINT64: Field type sint64.
    """
    TYPE_UNKNOWN = 0
    TYPE_DOUBLE = 1
    TYPE_FLOAT = 2
    TYPE_INT64 = 3
    TYPE_UINT64 = 4
    TYPE_INT32 = 5
    TYPE_FIXED64 = 6
    TYPE_FIXED32 = 7
    TYPE_BOOL = 8
    TYPE_STRING = 9
    TYPE_GROUP = 10
    TYPE_MESSAGE = 11
    TYPE_BYTES = 12
    TYPE_UINT32 = 13
    TYPE_ENUM = 14
    TYPE_SFIXED32 = 15
    TYPE_SFIXED64 = 16
    TYPE_SINT32 = 17
    TYPE_SINT64 = 18

  cardinality = _messages.EnumField('CardinalityValueValuesEnum', 1)
  defaultValue = _messages.StringField(2)
  jsonName = _messages.StringField(3)
  kind = _messages.EnumField('KindValueValuesEnum', 4)
  name = _messages.StringField(5)
  number = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  oneofIndex = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  options = _messages.MessageField('Option', 8, repeated=True)
  packed = _messages.BooleanField(9)
  typeUrl = _messages.StringField(10)


class GaugeView(_messages.Message):
  r"""A gauge chart shows where the current value sits within a pre-defined
  range. The upper and lower bounds should define the possible range of values
  for the scorecard's query (inclusive).

  Fields:
    lowerBound: The lower bound for this gauge chart. The value of the chart
      should always be greater than or equal to this.
    upperBound: The upper bound for this gauge chart. The value of the chart
      should always be less than or equal to this.
  """

  lowerBound = _messages.FloatField(1)
  upperBound = _messages.FloatField(2)


class GridLayout(_messages.Message):
  r"""A basic layout divides the available space into vertical columns of
  equal width and arranges a list of widgets using a row-first strategy.

  Fields:
    columns: The number of columns into which the view's width is divided. If
      omitted or set to zero, a system default will be used while rendering.
    widgets: The informational elements that are arranged into the columns
      row-first.
  """

  columns = _messages.IntegerField(1)
  widgets = _messages.MessageField('Widget', 2, repeated=True)


class HttpBody(_messages.Message):
  r"""Message that represents an arbitrary HTTP body. It should only be used
  for payload formats that can't be represented as JSON, such as raw binary or
  an HTML page.This message can be used both in streaming and non-streaming
  API methods in the request as well as the response.It can be used as a top-
  level request field, which is convenient if one wants to extract parameters
  from either the URL or HTTP template into the request fields and also want
  access to the raw HTTP body.Example: message GetResourceRequest { // A
  unique request id. string request_id = 1; // The raw HTTP body is bound to
  this field. google.api.HttpBody http_body = 2; } service ResourceService {
  rpc GetResource(GetResourceRequest) returns (google.api.HttpBody); rpc
  UpdateResource(google.api.HttpBody) returns (google.protobuf.Empty); }
  Example with streaming methods: service CaldavService { rpc
  GetCalendar(stream google.api.HttpBody) returns (stream
  google.api.HttpBody); rpc UpdateCalendar(stream google.api.HttpBody) returns
  (stream google.api.HttpBody); } Use of this type only changes how the
  request and response bodies are handled, all other features will continue to
  work unchanged.

  Messages:
    ExtensionsValueListEntry: A ExtensionsValueListEntry object.

  Fields:
    contentType: The HTTP Content-Type header value specifying the content
      type of the body.
    data: The HTTP request/response body as raw binary.
    extensions: Application specific response metadata. Must be set in the
      first response for streaming APIs.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExtensionsValueListEntry(_messages.Message):
    r"""A ExtensionsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a
        ExtensionsValueListEntry object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExtensionsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  contentType = _messages.StringField(1)
  data = _messages.BytesField(2)
  extensions = _messages.MessageField('ExtensionsValueListEntry', 3, repeated=True)


class IncidentList(_messages.Message):
  r"""A widget that displays a list of incidents

  Fields:
    monitoredResources: Optional. The monitored resource for which incidents
      are listed. The resource doesn't need to be fully specified. That is,
      you can specify the resource type but not the values of the resource
      labels. The resource type and labels are used for filtering.
    policyNames: Optional. A list of alert policy names to filter the incident
      list by. Don't include the project ID prefix in the policy name. For
      example, use alertPolicies/utilization.
  """

  monitoredResources = _messages.MessageField('MonitoredResource', 1, repeated=True)
  policyNames = _messages.StringField(2, repeated=True)


class Interval(_messages.Message):
  r"""Represents a time interval, encoded as a Timestamp start (inclusive) and
  a Timestamp end (exclusive).The start must be less than or equal to the end.
  When the start equals the end, the interval is empty (matches no time). When
  both start and end are unspecified, the interval matches any time.

  Fields:
    endTime: Optional. Exclusive end of the interval.If specified, a Timestamp
      matching this interval will have to be before the end.
    startTime: Optional. Inclusive start of the interval.If specified, a
      Timestamp matching this interval will have to be the same or after the
      start.
  """

  endTime = _messages.StringField(1)
  startTime = _messages.StringField(2)


class ListDashboardsResponse(_messages.Message):
  r"""The ListDashboards request.

  Fields:
    dashboards: The list of requested dashboards.
    nextPageToken: If there are more results than have been returned, then
      this field is set to a non-empty value. To see the additional results,
      use that value as page_token in the next call to this method.
  """

  dashboards = _messages.MessageField('Dashboard', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListMetricsScopesByMonitoredProjectResponse(_messages.Message):
  r"""Response for the ListMetricsScopesByMonitoredProject method.

  Fields:
    metricsScopes: A set of all metrics scopes that the specified monitored
      project has been added to.
  """

  metricsScopes = _messages.MessageField('MetricsScope', 1, repeated=True)


class LogsPanel(_messages.Message):
  r"""A widget that displays a stream of log.

  Fields:
    filter: A filter that chooses which log entries to return. See Advanced
      Logs Queries (https://cloud.google.com/logging/docs/view/advanced-
      queries). Only log entries that match the filter are returned. An empty
      filter matches all log entries.
    resourceNames: The names of logging resources to collect logs for.
      Currently projects and storage views are supported. If empty, the widget
      will default to the host project.
  """

  filter = _messages.StringField(1)
  resourceNames = _messages.StringField(2, repeated=True)


class Measure(_messages.Message):
  r"""A chart measure. Measures represent a measured property in your chart
  data such as rainfall in inches, number of units sold, revenue gained, etc.

  Fields:
    aggregationFunction: Required. The aggregation function applied to the
      input column. This must not be set to "none" unless binning is disabled
      on the dimension. The aggregation function is used to group points on
      the dimension bins.
    column: Required. The column name within in the dataset used for the
      measure.
  """

  aggregationFunction = _messages.MessageField('AggregationFunction', 1)
  column = _messages.StringField(2)


class MetricsScope(_messages.Message):
  r"""Represents a Metrics Scope
  (https://cloud.google.com/monitoring/settings#concept-scope) in Cloud
  Monitoring, which specifies one or more Google projects and zero or more AWS
  accounts to monitor together.

  Fields:
    createTime: Output only. The time when this Metrics Scope was created.
    monitoredProjects: Output only. The list of projects monitored by this
      Metrics Scope.
    name: Immutable. The resource name of the Monitoring Metrics Scope. On
      input, the resource name can be specified with the scoping project ID or
      number. On output, the resource name is specified with the scoping
      project number. Example:
      locations/global/metricsScopes/{SCOPING_PROJECT_ID_OR_NUMBER}
    updateTime: Output only. The time when this Metrics Scope record was last
      updated.
  """

  createTime = _messages.StringField(1)
  monitoredProjects = _messages.MessageField('MonitoredProject', 2, repeated=True)
  name = _messages.StringField(3)
  updateTime = _messages.StringField(4)


class MonitoredProject(_messages.Message):
  r"""A project being monitored
  (https://cloud.google.com/monitoring/settings/multiple-projects#create-
  multi) by a Metrics Scope.

  Fields:
    createTime: Output only. The time when this MonitoredProject was created.
    isTombstoned: Output only. Set if the project has been tombstoned by the
      user.
    name: Immutable. The resource name of the MonitoredProject. On input, the
      resource name includes the scoping project ID and monitored project ID.
      On output, it contains the equivalent project numbers. Example: location
      s/global/metricsScopes/{SCOPING_PROJECT_ID_OR_NUMBER}/projects/{MONITORE
      D_PROJECT_ID_OR_NUMBER}
  """

  createTime = _messages.StringField(1)
  isTombstoned = _messages.BooleanField(2)
  name = _messages.StringField(3)


class MonitoredResource(_messages.Message):
  r"""An object representing a resource that can be used for monitoring,
  logging, billing, or other purposes. Examples include virtual machine
  instances, databases, and storage devices such as disks. The type field
  identifies a MonitoredResourceDescriptor object that describes the
  resource's schema. Information in the labels field identifies the actual
  resource and its attributes according to the schema. For example, a
  particular Compute Engine VM instance could be represented by the following
  object, because the MonitoredResourceDescriptor for "gce_instance" has
  labels "project_id", "instance_id" and "zone": { "type": "gce_instance",
  "labels": { "project_id": "my-project", "instance_id": "12345678901234",
  "zone": "us-central1-a" }}

  Messages:
    LabelsValue: Required. Values for all of the labels listed in the
      associated monitored resource descriptor. For example, Compute Engine VM
      instances use the labels "project_id", "instance_id", and "zone".

  Fields:
    labels: Required. Values for all of the labels listed in the associated
      monitored resource descriptor. For example, Compute Engine VM instances
      use the labels "project_id", "instance_id", and "zone".
    type: Required. The monitored resource type. This field must match the
      type field of a MonitoredResourceDescriptor object. For example, the
      type of a Compute Engine VM instance is gce_instance. For a list of
      types, see Monitoring resource types
      (https://cloud.google.com/monitoring/api/resources) and Logging resource
      types (https://cloud.google.com/logging/docs/api/v2/resource-list).
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Required. Values for all of the labels listed in the associated
    monitored resource descriptor. For example, Compute Engine VM instances
    use the labels "project_id", "instance_id", and "zone".

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)
  type = _messages.StringField(2)


class MonitoringLocationsGlobalMetricsScopesGetRequest(_messages.Message):
  r"""A MonitoringLocationsGlobalMetricsScopesGetRequest object.

  Fields:
    name: Required. The resource name of the Metrics Scope. Example:
      locations/global/metricsScopes/{SCOPING_PROJECT_ID_OR_NUMBER}
  """

  name = _messages.StringField(1, required=True)


class MonitoringLocationsGlobalMetricsScopesListMetricsScopesByMonitoredProjectRequest(_messages.Message):
  r"""A MonitoringLocationsGlobalMetricsScopesListMetricsScopesByMonitoredProj
  ectRequest object.

  Fields:
    monitoredResourceContainer: Required. The resource name of the Monitored
      Project being requested. Example:
      projects/{MONITORED_PROJECT_ID_OR_NUMBER}
  """

  monitoredResourceContainer = _messages.StringField(1)


class MonitoringLocationsGlobalMetricsScopesProjectsCreateRequest(_messages.Message):
  r"""A MonitoringLocationsGlobalMetricsScopesProjectsCreateRequest object.

  Fields:
    monitoredProject: A MonitoredProject resource to be passed as the request
      body.
    parent: Required. The resource name of the existing Metrics Scope that
      will monitor this project. Example:
      locations/global/metricsScopes/{SCOPING_PROJECT_ID_OR_NUMBER}
  """

  monitoredProject = _messages.MessageField('MonitoredProject', 1)
  parent = _messages.StringField(2, required=True)


class MonitoringLocationsGlobalMetricsScopesProjectsDeleteRequest(_messages.Message):
  r"""A MonitoringLocationsGlobalMetricsScopesProjectsDeleteRequest object.

  Fields:
    name: Required. The resource name of the MonitoredProject. Example: locati
      ons/global/metricsScopes/{SCOPING_PROJECT_ID_OR_NUMBER}/projects/{MONITO
      RED_PROJECT_ID_OR_NUMBER}Authorization requires the following Google IAM
      (https://cloud.google.com/iam) permissions on both the Metrics Scope and
      on the MonitoredProject: monitoring.metricsScopes.link
  """

  name = _messages.StringField(1, required=True)


class MonitoringOperationsGetRequest(_messages.Message):
  r"""A MonitoringOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class MonitoringProjectsDashboardsCreateRequest(_messages.Message):
  r"""A MonitoringProjectsDashboardsCreateRequest object.

  Fields:
    dashboard: A Dashboard resource to be passed as the request body.
    parent: Required. The project on which to execute the request. The format
      is: projects/[PROJECT_ID_OR_NUMBER] The [PROJECT_ID_OR_NUMBER] must
      match the dashboard resource name.
    validateOnly: If set, validate the request and preview the review, but do
      not actually save it.
  """

  dashboard = _messages.MessageField('Dashboard', 1)
  parent = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class MonitoringProjectsDashboardsDeleteRequest(_messages.Message):
  r"""A MonitoringProjectsDashboardsDeleteRequest object.

  Fields:
    name: Required. The resource name of the Dashboard. The format is:
      projects/[PROJECT_ID_OR_NUMBER]/dashboards/[DASHBOARD_ID]
  """

  name = _messages.StringField(1, required=True)


class MonitoringProjectsDashboardsGetRequest(_messages.Message):
  r"""A MonitoringProjectsDashboardsGetRequest object.

  Fields:
    name: Required. The resource name of the Dashboard. The format is one of:
      dashboards/[DASHBOARD_ID] (for system dashboards)
      projects/[PROJECT_ID_OR_NUMBER]/dashboards/[DASHBOARD_ID] (for custom
      dashboards).
  """

  name = _messages.StringField(1, required=True)


class MonitoringProjectsDashboardsListRequest(_messages.Message):
  r"""A MonitoringProjectsDashboardsListRequest object.

  Fields:
    pageSize: A positive number that is the maximum number of results to
      return. If unspecified, a default of 1000 is used.
    pageToken: Optional. If this field is not empty then it must contain the
      nextPageToken value returned by a previous call to this method. Using
      this field causes the method to return additional results from the
      previous method call.
    parent: Required. The scope of the dashboards to list. The format is:
      projects/[PROJECT_ID_OR_NUMBER]
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class MonitoringProjectsDashboardsPatchRequest(_messages.Message):
  r"""A MonitoringProjectsDashboardsPatchRequest object.

  Fields:
    dashboard: A Dashboard resource to be passed as the request body.
    name: Identifier. The resource name of the dashboard.
    validateOnly: If set, validate the request and preview the review, but do
      not actually save it.
  """

  dashboard = _messages.MessageField('Dashboard', 1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class MonitoringProjectsLocationPrometheusApiV1LabelValuesRequest(_messages.Message):
  r"""A MonitoringProjectsLocationPrometheusApiV1LabelValuesRequest object.

  Fields:
    end: The end time to evaluate the query for. Either floating point UNIX
      seconds or RFC3339 formatted timestamp.
    label: The label name for which values are queried.
    location: Location of the resource information. Has to be "global" now.
    match: A list of matchers encoded in the Prometheus label matcher format
      to constrain the values to series that satisfy them.
    name: Required. The workspace on which to execute the request. It is not
      part of the open source API but used as a request path prefix to
      distinguish different virtual Prometheus instances of Google Prometheus
      Engine. The format is: projects/PROJECT_ID_OR_NUMBER.
    start: The start time to evaluate the query for. Either floating point
      UNIX seconds or RFC3339 formatted timestamp.
  """

  end = _messages.StringField(1)
  label = _messages.StringField(2, required=True)
  location = _messages.StringField(3, required=True)
  match = _messages.StringField(4)
  name = _messages.StringField(5, required=True)
  start = _messages.StringField(6)


class MonitoringProjectsLocationPrometheusApiV1LabelsRequest(_messages.Message):
  r"""A MonitoringProjectsLocationPrometheusApiV1LabelsRequest object.

  Fields:
    location: Location of the resource information. Has to be "global" now.
    name: Required. The workspace on which to execute the request. It is not
      part of the open source API but used as a request path prefix to
      distinguish different virtual Prometheus instances of Google Prometheus
      Engine. The format is: projects/PROJECT_ID_OR_NUMBER.
    queryLabelsRequest: A QueryLabelsRequest resource to be passed as the
      request body.
  """

  location = _messages.StringField(1, required=True)
  name = _messages.StringField(2, required=True)
  queryLabelsRequest = _messages.MessageField('QueryLabelsRequest', 3)


class MonitoringProjectsLocationPrometheusApiV1MetadataListRequest(_messages.Message):
  r"""A MonitoringProjectsLocationPrometheusApiV1MetadataListRequest object.

  Fields:
    limit: Maximum number of metrics to return.
    location: Location of the resource information. Has to be "global" for
      now.
    metric: The metric name for which to query metadata. If unset, all metric
      metadata is returned.
    name: Required. The workspace on which to execute the request. It is not
      part of the open source API but used as a request path prefix to
      distinguish different virtual Prometheus instances of Google Prometheus
      Engine. The format is: projects/PROJECT_ID_OR_NUMBER.
  """

  limit = _messages.IntegerField(1)
  location = _messages.StringField(2, required=True)
  metric = _messages.StringField(3)
  name = _messages.StringField(4, required=True)


class MonitoringProjectsLocationPrometheusApiV1QueryExemplarsRequest(_messages.Message):
  r"""A MonitoringProjectsLocationPrometheusApiV1QueryExemplarsRequest object.

  Fields:
    location: Location of the resource information. Has to be "global" now.
    name: Required. The project on which to execute the request. Data
      associcated with the project's workspace stored under the The format is:
      projects/PROJECT_ID_OR_NUMBER. Open source API but used as a request
      path prefix to distinguish different virtual Prometheus instances of
      Google Prometheus Engine.
    queryExemplarsRequest: A QueryExemplarsRequest resource to be passed as
      the request body.
  """

  location = _messages.StringField(1, required=True)
  name = _messages.StringField(2, required=True)
  queryExemplarsRequest = _messages.MessageField('QueryExemplarsRequest', 3)


class MonitoringProjectsLocationPrometheusApiV1QueryRangeRequest(_messages.Message):
  r"""A MonitoringProjectsLocationPrometheusApiV1QueryRangeRequest object.

  Fields:
    location: Location of the resource information. Has to be "global" now.
    name: Required. The project on which to execute the request. Data
      associcated with the project's workspace stored under the The format is:
      projects/PROJECT_ID_OR_NUMBER. Open source API but used as a request
      path prefix to distinguish different virtual Prometheus instances of
      Google Prometheus Engine.
    queryRangeRequest: A QueryRangeRequest resource to be passed as the
      request body.
  """

  location = _messages.StringField(1, required=True)
  name = _messages.StringField(2, required=True)
  queryRangeRequest = _messages.MessageField('QueryRangeRequest', 3)


class MonitoringProjectsLocationPrometheusApiV1QueryRequest(_messages.Message):
  r"""A MonitoringProjectsLocationPrometheusApiV1QueryRequest object.

  Fields:
    location: Location of the resource information. Has to be "global" now.
    name: Required. The project on which to execute the request. Data
      associcated with the project's workspace stored under the The format is:
      projects/PROJECT_ID_OR_NUMBER. Open source API but used as a request
      path prefix to distinguish different virtual Prometheus instances of
      Google Prometheus Engine.
    queryInstantRequest: A QueryInstantRequest resource to be passed as the
      request body.
  """

  location = _messages.StringField(1, required=True)
  name = _messages.StringField(2, required=True)
  queryInstantRequest = _messages.MessageField('QueryInstantRequest', 3)


class MonitoringProjectsLocationPrometheusApiV1SeriesRequest(_messages.Message):
  r"""A MonitoringProjectsLocationPrometheusApiV1SeriesRequest object.

  Fields:
    location: Location of the resource information. Has to be "global" for
      now.
    name: Required. The workspace on which to execute the request. It is not
      part of the open source API but used as a request path prefix to
      distinguish different virtual Prometheus instances of Google Prometheus
      Engine. The format is: projects/PROJECT_ID_OR_NUMBER.
    querySeriesRequest: A QuerySeriesRequest resource to be passed as the
      request body.
  """

  location = _messages.StringField(1, required=True)
  name = _messages.StringField(2, required=True)
  querySeriesRequest = _messages.MessageField('QuerySeriesRequest', 3)


class MosaicLayout(_messages.Message):
  r"""A mosaic layout divides the available space into a grid of blocks, and
  overlays the grid with tiles. Unlike GridLayout, tiles may span multiple
  grid blocks and can be placed at arbitrary locations in the grid.

  Fields:
    columns: The number of columns in the mosaic grid. The number of columns
      must be between 1 and 48, inclusive.
    tiles: The tiles to display.
  """

  columns = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  tiles = _messages.MessageField('Tile', 2, repeated=True)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as Delete, the response
      is google.protobuf.Empty. If the original method is standard
      Get/Create/Update, the response should be the resource. For other
      methods, the response should have the type XxxResponse, where Xxx is the
      original method name. For example, if the original method name is
      TakeSnapshot(), the inferred response type is TakeSnapshotResponse.

  Fields:
    done: If the value is false, it means the operation is still in progress.
      If true, the operation is completed, and either error or response is
      available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the name should be a resource name ending with operations/{unique_id}.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as Delete, the response
      is google.protobuf.Empty. If the original method is standard
      Get/Create/Update, the response should be the resource. For other
      methods, the response should have the type XxxResponse, where Xxx is the
      original method name. For example, if the original method name is
      TakeSnapshot(), the inferred response type is TakeSnapshotResponse.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as Delete, the response is
    google.protobuf.Empty. If the original method is standard
    Get/Create/Update, the response should be the resource. For other methods,
    the response should have the type XxxResponse, where Xxx is the original
    method name. For example, if the original method name is TakeSnapshot(),
    the inferred response type is TakeSnapshotResponse.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Contains metadata for longrunning operation for the edit Metrics Scope
  endpoints.

  Enums:
    StateValueValuesEnum: Current state of the batch operation.

  Fields:
    createTime: The time when the batch request was received.
    state: Current state of the batch operation.
    updateTime: The time when the operation result was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Current state of the batch operation.

    Values:
      STATE_UNSPECIFIED: Invalid.
      CREATED: Request has been received.
      RUNNING: Request is actively being processed.
      DONE: The batch processing is done.
      CANCELLED: The batch processing was cancelled.
    """
    STATE_UNSPECIFIED = 0
    CREATED = 1
    RUNNING = 2
    DONE = 3
    CANCELLED = 4

  createTime = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)
  updateTime = _messages.StringField(3)


class OpsAnalyticsQuery(_messages.Message):
  r"""Preview: A query that produces an aggregated response and supporting
  data. This is a preview feature and may be subject to change before final
  release.

  Fields:
    sql: A SQL query to fetch time series, category series, or numeric series
      data.
  """

  sql = _messages.StringField(1)


class Option(_messages.Message):
  r"""A protocol buffer option, which can be attached to a message, field,
  enumeration, etc.New usages of this message as an alternative to
  FileOptions, MessageOptions, FieldOptions, EnumOptions, EnumValueOptions,
  ServiceOptions, or MethodOptions are strongly discouraged.

  Messages:
    ValueValue: The option's value packed in an Any message. If the value is a
      primitive, the corresponding wrapper type defined in
      google/protobuf/wrappers.proto should be used. If the value is an enum,
      it should be stored as an int32 value using the
      google.protobuf.Int32Value type.

  Fields:
    name: The option's name. For protobuf built-in options (options defined in
      descriptor.proto), this is the short name. For example, "map_entry". For
      custom options, it should be the fully-qualified name. For example,
      "google.api.http".
    value: The option's value packed in an Any message. If the value is a
      primitive, the corresponding wrapper type defined in
      google/protobuf/wrappers.proto should be used. If the value is an enum,
      it should be stored as an int32 value using the
      google.protobuf.Int32Value type.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ValueValue(_messages.Message):
    r"""The option's value packed in an Any message. If the value is a
    primitive, the corresponding wrapper type defined in
    google/protobuf/wrappers.proto should be used. If the value is an enum, it
    should be stored as an int32 value using the google.protobuf.Int32Value
    type.

    Messages:
      AdditionalProperty: An additional property for a ValueValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ValueValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  name = _messages.StringField(1)
  value = _messages.MessageField('ValueValue', 2)


class Parameter(_messages.Message):
  r"""Preview: Parameter value applied to the aggregation function. This is a
  preview feature and may be subject to change before final release.

  Fields:
    doubleValue: A floating-point parameter value.
    intValue: An integer parameter value.
  """

  doubleValue = _messages.FloatField(1)
  intValue = _messages.IntegerField(2)


class PickTimeSeriesFilter(_messages.Message):
  r"""Describes a ranking-based time series filter. Each input time series is
  ranked with an aligner. The filter will allow up to num_time_series time
  series to pass through it, selecting them based on the relative ranking.For
  example, if ranking_method is METHOD_MEAN,direction is BOTTOM, and
  num_time_series is 3, then the 3 times series with the lowest mean values
  will pass through the filter.

  Enums:
    DirectionValueValuesEnum: How to use the ranking to select time series
      that pass through the filter.
    RankingMethodValueValuesEnum: ranking_method is applied to each time
      series independently to produce the value which will be used to compare
      the time series to other time series.

  Fields:
    direction: How to use the ranking to select time series that pass through
      the filter.
    interval: Select the top N streams/time series within this time interval
    numTimeSeries: How many time series to allow to pass through the filter.
    rankingMethod: ranking_method is applied to each time series independently
      to produce the value which will be used to compare the time series to
      other time series.
  """

  class DirectionValueValuesEnum(_messages.Enum):
    r"""How to use the ranking to select time series that pass through the
    filter.

    Values:
      DIRECTION_UNSPECIFIED: Not allowed. You must specify a different
        Direction if you specify a PickTimeSeriesFilter.
      TOP: Pass the highest num_time_series ranking inputs.
      BOTTOM: Pass the lowest num_time_series ranking inputs.
    """
    DIRECTION_UNSPECIFIED = 0
    TOP = 1
    BOTTOM = 2

  class RankingMethodValueValuesEnum(_messages.Enum):
    r"""ranking_method is applied to each time series independently to produce
    the value which will be used to compare the time series to other time
    series.

    Values:
      METHOD_UNSPECIFIED: Not allowed. You must specify a different Method if
        you specify a PickTimeSeriesFilter.
      METHOD_MEAN: Select the mean of all values.
      METHOD_MAX: Select the maximum value.
      METHOD_MIN: Select the minimum value.
      METHOD_SUM: Compute the sum of all values.
      METHOD_LATEST: Select the most recent value.
    """
    METHOD_UNSPECIFIED = 0
    METHOD_MEAN = 1
    METHOD_MAX = 2
    METHOD_MIN = 3
    METHOD_SUM = 4
    METHOD_LATEST = 5

  direction = _messages.EnumField('DirectionValueValuesEnum', 1)
  interval = _messages.MessageField('Interval', 2)
  numTimeSeries = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  rankingMethod = _messages.EnumField('RankingMethodValueValuesEnum', 4)


class PieChart(_messages.Message):
  r"""A widget that displays timeseries data as a pie or a donut.

  Enums:
    ChartTypeValueValuesEnum: Required. Indicates the visualization type for
      the PieChart.

  Fields:
    chartType: Required. Indicates the visualization type for the PieChart.
    dataSets: Required. The queries for the chart's data.
    showLabels: Optional. Indicates whether or not the pie chart should show
      slices' labels
  """

  class ChartTypeValueValuesEnum(_messages.Enum):
    r"""Required. Indicates the visualization type for the PieChart.

    Values:
      PIE_CHART_TYPE_UNSPECIFIED: The zero value. No type specified. Do not
        use.
      PIE: A Pie type PieChart.
      DONUT: Similar to PIE, but the DONUT type PieChart has a hole in the
        middle.
    """
    PIE_CHART_TYPE_UNSPECIFIED = 0
    PIE = 1
    DONUT = 2

  chartType = _messages.EnumField('ChartTypeValueValuesEnum', 1)
  dataSets = _messages.MessageField('PieChartDataSet', 2, repeated=True)
  showLabels = _messages.BooleanField(3)


class PieChartDataSet(_messages.Message):
  r"""Groups a time series query definition.

  Fields:
    dimensions: A dimension is a structured label, class, or category for a
      set of measurements in your data.
    measures: A measure is a measured value of a property in your data. For
      example, rainfall in inches, number of units sold, revenue gained, etc.
    minAlignmentPeriod: Optional. The lower bound on data point frequency for
      this data set, implemented by specifying the minimum alignment period to
      use in a time series query. For example, if the data is published once
      every 10 minutes, the min_alignment_period should be at least 10
      minutes. It would not make sense to fetch and align data at one minute
      intervals.
    sliceNameTemplate: Optional. A template for the name of the slice. This
      name will be displayed in the legend and the tooltip of the pie chart.
      It replaces the auto-generated names for the slices. For example, if the
      template is set to ${resource.labels.zone}, the zone's value will be
      used for the name instead of the default name.
    timeSeriesQuery: Required. The query for the PieChart. See,
      google.monitoring.dashboard.v1.TimeSeriesQuery.
  """

  dimensions = _messages.MessageField('Dimension', 1, repeated=True)
  measures = _messages.MessageField('Measure', 2, repeated=True)
  minAlignmentPeriod = _messages.StringField(3)
  sliceNameTemplate = _messages.StringField(4)
  timeSeriesQuery = _messages.MessageField('TimeSeriesQuery', 5)


class QueryExemplarsRequest(_messages.Message):
  r"""QueryExemplarsRequest holds all parameters of the Prometheus upstream
  API for querying exemplars.

  Fields:
    end: The end time to evaluate the query for. Either floating point UNIX
      seconds or RFC3339 formatted timestamp.
    query: A PromQL query string. Query language documentation:
      https://prometheus.io/docs/prometheus/latest/querying/basics/.
    start: The start time to evaluate the query for. Either floating point
      UNIX seconds or RFC3339 formatted timestamp.
  """

  end = _messages.StringField(1)
  query = _messages.StringField(2)
  start = _messages.StringField(3)


class QueryInstantRequest(_messages.Message):
  r"""QueryInstantRequest holds all parameters of the Prometheus upstream
  instant query API plus GCM specific parameters.

  Fields:
    query: A PromQL query string. Query language documentation:
      https://prometheus.io/docs/prometheus/latest/querying/basics/.
    time: The single point in time to evaluate the query for. Either floating
      point UNIX seconds or RFC3339 formatted timestamp.
    timeout: An upper bound timeout for the query. Either a Prometheus
      duration string
      (https://prometheus.io/docs/prometheus/latest/querying/basics/#time-
      durations) or floating point seconds. This non-standard encoding must be
      used for compatibility with the open source API. Clients may still
      implement timeouts at the connection level while ignoring this field.
  """

  query = _messages.StringField(1)
  time = _messages.StringField(2)
  timeout = _messages.StringField(3)


class QueryLabelsRequest(_messages.Message):
  r"""QueryLabelsRequest holds all parameters of the Prometheus upstream API
  for returning a list of label names.

  Fields:
    end: The end time to evaluate the query for. Either floating point UNIX
      seconds or RFC3339 formatted timestamp.
    match: A list of matchers encoded in the Prometheus label matcher format
      to constrain the values to series that satisfy them.
    start: The start time to evaluate the query for. Either floating point
      UNIX seconds or RFC3339 formatted timestamp.
  """

  end = _messages.StringField(1)
  match = _messages.StringField(2)
  start = _messages.StringField(3)


class QueryRangeRequest(_messages.Message):
  r"""QueryRangeRequest holds all parameters of the Prometheus upstream range
  query API plus GCM specific parameters.

  Fields:
    end: The end time to evaluate the query for. Either floating point UNIX
      seconds or RFC3339 formatted timestamp.
    query: A PromQL query string. Query language documentation:
      https://prometheus.io/docs/prometheus/latest/querying/basics/.
    start: The start time to evaluate the query for. Either floating point
      UNIX seconds or RFC3339 formatted timestamp.
    step: The resolution of query result. Either a Prometheus duration string
      (https://prometheus.io/docs/prometheus/latest/querying/basics/#time-
      durations) or floating point seconds. This non-standard encoding must be
      used for compatibility with the open source API. Clients may still
      implement timeouts at the connection level while ignoring this field.
    timeout: An upper bound timeout for the query. Either a Prometheus
      duration string
      (https://prometheus.io/docs/prometheus/latest/querying/basics/#time-
      durations) or floating point seconds. This non-standard encoding must be
      used for compatibility with the open source API. Clients may still
      implement timeouts at the connection level while ignoring this field.
  """

  end = _messages.StringField(1)
  query = _messages.StringField(2)
  start = _messages.StringField(3)
  step = _messages.StringField(4)
  timeout = _messages.StringField(5)


class QuerySeriesRequest(_messages.Message):
  r"""QuerySeries holds all parameters of the Prometheus upstream API for
  querying series.

  Fields:
    end: The end time to evaluate the query for. Either floating point UNIX
      seconds or RFC3339 formatted timestamp.
    start: The start time to evaluate the query for. Either floating point
      UNIX seconds or RFC3339 formatted timestamp.
  """

  end = _messages.StringField(1)
  start = _messages.StringField(2)


class RatioPart(_messages.Message):
  r"""Describes a query to build the numerator or denominator of a
  TimeSeriesFilterRatio.

  Fields:
    aggregation: By default, the raw time series data is returned. Use this
      field to combine multiple time series for different views of the data.
    filter: Required. The monitoring filter
      (https://cloud.google.com/monitoring/api/v3/filters) that identifies the
      metric types, resources, and projects to query.
  """

  aggregation = _messages.MessageField('Aggregation', 1)
  filter = _messages.StringField(2)


class Row(_messages.Message):
  r"""Defines the layout properties and content for a row.

  Fields:
    weight: The relative weight of this row. The row weight is used to adjust
      the height of rows on the screen (relative to peers). Greater the
      weight, greater the height of the row on the screen. If omitted, a value
      of 1 is used while rendering.
    widgets: The display widgets arranged horizontally in this row.
  """

  weight = _messages.IntegerField(1)
  widgets = _messages.MessageField('Widget', 2, repeated=True)


class RowLayout(_messages.Message):
  r"""A simplified layout that divides the available space into rows and
  arranges a set of widgets horizontally in each row.

  Fields:
    rows: The rows of content to display.
  """

  rows = _messages.MessageField('Row', 1, repeated=True)


class Scorecard(_messages.Message):
  r"""A widget showing the latest value of a metric, and how this value
  relates to one or more thresholds.

  Fields:
    blankView: Will cause the Scorecard to show only the value, with no
      indicator to its value relative to its thresholds.
    breakdowns: Optional. The collection of breakdowns to be applied to the
      dataset. A breakdown is a way to slice the data. For example, you can
      break down the data by region.
    dimensions: Optional. A dimension is a structured label, class, or
      category for a set of measurements in your data.
    gaugeView: Will cause the scorecard to show a gauge chart.
    measures: Optional. A measure is a measured value of a property in your
      data. For example, rainfall in inches, number of units sold, revenue
      gained, etc.
    sparkChartView: Will cause the scorecard to show a spark chart.
    thresholds: The thresholds used to determine the state of the scorecard
      given the time series' current value. For an actual value x, the
      scorecard is in a danger state if x is less than or equal to a danger
      threshold that triggers below, or greater than or equal to a danger
      threshold that triggers above. Similarly, if x is above/below a warning
      threshold that triggers above/below, then the scorecard is in a warning
      state - unless x also puts it in a danger state. (Danger trumps
      warning.)As an example, consider a scorecard with the following four
      thresholds: { value: 90, category: 'DANGER', trigger: 'ABOVE', }, {
      value: 70, category: 'WARNING', trigger: 'ABOVE', }, { value: 10,
      category: 'DANGER', trigger: 'BELOW', }, { value: 20, category:
      'WARNING', trigger: 'BELOW', } Then: values less than or equal to 10
      would put the scorecard in a DANGER state, values greater than 10 but
      less than or equal to 20 a WARNING state, values strictly between 20 and
      70 an OK state, values greater than or equal to 70 but less than 90 a
      WARNING state, and values greater than or equal to 90 a DANGER state.
    timeSeriesQuery: Required. Fields for querying time series data from the
      Stackdriver metrics API.
  """

  blankView = _messages.MessageField('Empty', 1)
  breakdowns = _messages.MessageField('Breakdown', 2, repeated=True)
  dimensions = _messages.MessageField('Dimension', 3, repeated=True)
  gaugeView = _messages.MessageField('GaugeView', 4)
  measures = _messages.MessageField('Measure', 5, repeated=True)
  sparkChartView = _messages.MessageField('SparkChartView', 6)
  thresholds = _messages.MessageField('Threshold', 7, repeated=True)
  timeSeriesQuery = _messages.MessageField('TimeSeriesQuery', 8)


class SectionHeader(_messages.Message):
  r"""A widget that defines a new section header. Sections populate a table of
  contents and allow easier navigation of long-form content.

  Fields:
    dividerBelow: Whether to insert a divider below the section in the table
      of contents
    subtitle: The subtitle of the section
  """

  dividerBelow = _messages.BooleanField(1)
  subtitle = _messages.StringField(2)


class SingleViewGroup(_messages.Message):
  r"""A widget that groups the other widgets by using a dropdown menu. All
  widgets that are within the area spanned by the grouping widget are
  considered member widgets.

  Enums:
    DisplayTypeValueValuesEnum: Optional. Determines how the widget selector
      will be displayed.

  Fields:
    displayType: Optional. Determines how the widget selector will be
      displayed.
  """

  class DisplayTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Determines how the widget selector will be displayed.

    Values:
      DISPLAY_TYPE_UNSPECIFIED: Display type is not specified, defaults to
        DROPDOWN.
      DROPDOWN: Renders the widget selector as a dropdown.
      TAB: Renders the widget selector as a tab list.
    """
    DISPLAY_TYPE_UNSPECIFIED = 0
    DROPDOWN = 1
    TAB = 2

  displayType = _messages.EnumField('DisplayTypeValueValuesEnum', 1)


class SourceContext(_messages.Message):
  r"""SourceContext represents information about the source of a protobuf
  element, like the file in which it is defined.

  Fields:
    fileName: The path-qualified name of the .proto file that contained the
      associated protobuf element. For example:
      "google/protobuf/source_context.proto".
  """

  fileName = _messages.StringField(1)


class SpanContext(_messages.Message):
  r"""The context of a span. This is attached to an Exemplar in Distribution
  values during aggregation.It contains the name of a span with format:
  projects/[PROJECT_ID_OR_NUMBER]/traces/[TRACE_ID]/spans/[SPAN_ID]

  Fields:
    spanName: The resource name of the span. The format is:
      projects/[PROJECT_ID_OR_NUMBER]/traces/[TRACE_ID]/spans/[SPAN_ID]
      [TRACE_ID] is a unique identifier for a trace within a project; it is a
      32-character hexadecimal encoding of a 16-byte array.[SPAN_ID] is a
      unique identifier for a span within a trace; it is a 16-character
      hexadecimal encoding of an 8-byte array.
  """

  spanName = _messages.StringField(1)


class SparkChartView(_messages.Message):
  r"""A sparkChart is a small chart suitable for inclusion in a table-cell or
  inline in text. This message contains the configuration for a sparkChart to
  show up on a Scorecard, showing recent trends of the scorecard's timeseries.

  Enums:
    SparkChartTypeValueValuesEnum: Required. The type of sparkchart to show in
      this chartView.

  Fields:
    minAlignmentPeriod: The lower bound on data point frequency in the chart
      implemented by specifying the minimum alignment period to use in a time
      series query. For example, if the data is published once every 10
      minutes it would not make sense to fetch and align data at one minute
      intervals. This field is optional and exists only as a hint.
    sparkChartType: Required. The type of sparkchart to show in this
      chartView.
  """

  class SparkChartTypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of sparkchart to show in this chartView.

    Values:
      SPARK_CHART_TYPE_UNSPECIFIED: Not allowed in well-formed requests.
      SPARK_LINE: The sparkline will be rendered as a small line chart.
      SPARK_BAR: The sparkbar will be rendered as a small bar chart.
    """
    SPARK_CHART_TYPE_UNSPECIFIED = 0
    SPARK_LINE = 1
    SPARK_BAR = 2

  minAlignmentPeriod = _messages.StringField(1)
  sparkChartType = _messages.EnumField('SparkChartTypeValueValuesEnum', 2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StatisticalTimeSeriesFilter(_messages.Message):
  r"""A filter that ranks streams based on their statistical relation to other
  streams in a request. Note: This field is deprecated and completely ignored
  by the API.

  Enums:
    RankingMethodValueValuesEnum: rankingMethod is applied to a set of time
      series, and then the produced value for each individual time series is
      used to compare a given time series to others. These are methods that
      cannot be applied stream-by-stream, but rather require the full context
      of a request to evaluate time series.

  Fields:
    numTimeSeries: How many time series to output.
    rankingMethod: rankingMethod is applied to a set of time series, and then
      the produced value for each individual time series is used to compare a
      given time series to others. These are methods that cannot be applied
      stream-by-stream, but rather require the full context of a request to
      evaluate time series.
  """

  class RankingMethodValueValuesEnum(_messages.Enum):
    r"""rankingMethod is applied to a set of time series, and then the
    produced value for each individual time series is used to compare a given
    time series to others. These are methods that cannot be applied stream-by-
    stream, but rather require the full context of a request to evaluate time
    series.

    Values:
      METHOD_UNSPECIFIED: Not allowed in well-formed requests.
      METHOD_CLUSTER_OUTLIER: Compute the outlier score of each stream.
    """
    METHOD_UNSPECIFIED = 0
    METHOD_CLUSTER_OUTLIER = 1

  numTimeSeries = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  rankingMethod = _messages.EnumField('RankingMethodValueValuesEnum', 2)


class Status(_messages.Message):
  r"""The Status type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by gRPC (https://github.com/grpc). Each Status message contains three
  pieces of data: error code, error message, and error details.You can find
  out more about this error model and how to work with it in the API Design
  Guide (https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StringArray(_messages.Message):
  r"""An array of strings

  Fields:
    values: The values of the array
  """

  values = _messages.StringField(1, repeated=True)


class TableDataSet(_messages.Message):
  r"""Groups a time series query definition with table options.

  Fields:
    minAlignmentPeriod: Optional. The lower bound on data point frequency for
      this data set, implemented by specifying the minimum alignment period to
      use in a time series query For example, if the data is published once
      every 10 minutes, the min_alignment_period should be at least 10
      minutes. It would not make sense to fetch and align data at one minute
      intervals.
    tableDisplayOptions: Optional. Table display options for configuring how
      the table is rendered.
    tableTemplate: Optional. A template string for naming TimeSeries in the
      resulting data set. This should be a string with interpolations of the
      form ${label_name}, which will resolve to the label's value i.e.
      "${resource.labels.project_id}."
    timeSeriesQuery: Required. Fields for querying time series data from the
      Stackdriver metrics API.
  """

  minAlignmentPeriod = _messages.StringField(1)
  tableDisplayOptions = _messages.MessageField('TableDisplayOptions', 2)
  tableTemplate = _messages.StringField(3)
  timeSeriesQuery = _messages.MessageField('TimeSeriesQuery', 4)


class TableDisplayOptions(_messages.Message):
  r"""Table display options that can be reused.

  Fields:
    shownColumns: Optional. This field is unused and has been replaced by
      TimeSeriesTable.column_settings
  """

  shownColumns = _messages.StringField(1, repeated=True)


class TemplateVariableCondition(_messages.Message):
  r"""A condition whose evaluation is based on the value of a template
  variable.

  Enums:
    ComparatorValueValuesEnum: Comparator to use to evaluate whether the value
      of the template variable matches the template_variable_value. For
      example, if the comparator is REGEX_FULL_MATCH, template_variable_value
      would contain a regex that is matched against the value of the template
      variable.

  Fields:
    comparator: Comparator to use to evaluate whether the value of the
      template variable matches the template_variable_value. For example, if
      the comparator is REGEX_FULL_MATCH, template_variable_value would
      contain a regex that is matched against the value of the template
      variable.
    templateVariable: The template variable whose value is evaluated.
    templateVariableValue: The value to compare the template variable to. For
      example, if the comparator is REGEX_FULL_MATCH, this field should
      contain a regex.
  """

  class ComparatorValueValuesEnum(_messages.Enum):
    r"""Comparator to use to evaluate whether the value of the template
    variable matches the template_variable_value. For example, if the
    comparator is REGEX_FULL_MATCH, template_variable_value would contain a
    regex that is matched against the value of the template variable.

    Values:
      COMPARATOR_UNSPECIFIED: No comparator specified. Behavior defaults to
        REGEX_FULL_MATCH.
      REGEX_FULL_MATCH: Condition with this comparator evaluates to true when
        the value of the template variables matches the specified regex.
    """
    COMPARATOR_UNSPECIFIED = 0
    REGEX_FULL_MATCH = 1

  comparator = _messages.EnumField('ComparatorValueValuesEnum', 1)
  templateVariable = _messages.StringField(2)
  templateVariableValue = _messages.StringField(3)


class Text(_messages.Message):
  r"""A widget that displays textual content.

  Enums:
    FormatValueValuesEnum: How the text content is formatted.

  Fields:
    content: The text content to be displayed.
    format: How the text content is formatted.
    style: How the text is styled
  """

  class FormatValueValuesEnum(_messages.Enum):
    r"""How the text content is formatted.

    Values:
      FORMAT_UNSPECIFIED: Format is unspecified. Defaults to MARKDOWN.
      MARKDOWN: The text contains Markdown formatting.
      RAW: The text contains no special formatting.
    """
    FORMAT_UNSPECIFIED = 0
    MARKDOWN = 1
    RAW = 2

  content = _messages.StringField(1)
  format = _messages.EnumField('FormatValueValuesEnum', 2)
  style = _messages.MessageField('TextStyle', 3)


class TextStyle(_messages.Message):
  r"""Properties that determine how the title and content are styled

  Enums:
    FontSizeValueValuesEnum: Font sizes for both the title and content. The
      title will still be larger relative to the content.
    HorizontalAlignmentValueValuesEnum: The horizontal alignment of both the
      title and content
    PaddingValueValuesEnum: The amount of padding around the widget
    PointerLocationValueValuesEnum: The pointer location for this widget (also
      sometimes called a "tail")
    VerticalAlignmentValueValuesEnum: The vertical alignment of both the title
      and content

  Fields:
    backgroundColor: The background color as a hex string. "#RRGGBB" or "#RGB"
    fontSize: Font sizes for both the title and content. The title will still
      be larger relative to the content.
    horizontalAlignment: The horizontal alignment of both the title and
      content
    padding: The amount of padding around the widget
    pointerLocation: The pointer location for this widget (also sometimes
      called a "tail")
    textColor: The text color as a hex string. "#RRGGBB" or "#RGB"
    verticalAlignment: The vertical alignment of both the title and content
  """

  class FontSizeValueValuesEnum(_messages.Enum):
    r"""Font sizes for both the title and content. The title will still be
    larger relative to the content.

    Values:
      FONT_SIZE_UNSPECIFIED: No font size specified, will default to FS_LARGE
      FS_EXTRA_SMALL: Extra small font size
      FS_SMALL: Small font size
      FS_MEDIUM: Medium font size
      FS_LARGE: Large font size
      FS_EXTRA_LARGE: Extra large font size
    """
    FONT_SIZE_UNSPECIFIED = 0
    FS_EXTRA_SMALL = 1
    FS_SMALL = 2
    FS_MEDIUM = 3
    FS_LARGE = 4
    FS_EXTRA_LARGE = 5

  class HorizontalAlignmentValueValuesEnum(_messages.Enum):
    r"""The horizontal alignment of both the title and content

    Values:
      HORIZONTAL_ALIGNMENT_UNSPECIFIED: No horizontal alignment specified,
        will default to H_LEFT
      H_LEFT: Left-align
      H_CENTER: Center-align
      H_RIGHT: Right-align
    """
    HORIZONTAL_ALIGNMENT_UNSPECIFIED = 0
    H_LEFT = 1
    H_CENTER = 2
    H_RIGHT = 3

  class PaddingValueValuesEnum(_messages.Enum):
    r"""The amount of padding around the widget

    Values:
      PADDING_SIZE_UNSPECIFIED: No padding size specified, will default to
        P_EXTRA_SMALL
      P_EXTRA_SMALL: Extra small padding
      P_SMALL: Small padding
      P_MEDIUM: Medium padding
      P_LARGE: Large padding
      P_EXTRA_LARGE: Extra large padding
    """
    PADDING_SIZE_UNSPECIFIED = 0
    P_EXTRA_SMALL = 1
    P_SMALL = 2
    P_MEDIUM = 3
    P_LARGE = 4
    P_EXTRA_LARGE = 5

  class PointerLocationValueValuesEnum(_messages.Enum):
    r"""The pointer location for this widget (also sometimes called a "tail")

    Values:
      POINTER_LOCATION_UNSPECIFIED: No visual pointer
      PL_TOP: Placed in the middle of the top of the widget
      PL_RIGHT: Placed in the middle of the right side of the widget
      PL_BOTTOM: Placed in the middle of the bottom of the widget
      PL_LEFT: Placed in the middle of the left side of the widget
      PL_TOP_LEFT: Placed on the left side of the top of the widget
      PL_TOP_RIGHT: Placed on the right side of the top of the widget
      PL_RIGHT_TOP: Placed on the top of the right side of the widget
      PL_RIGHT_BOTTOM: Placed on the bottom of the right side of the widget
      PL_BOTTOM_RIGHT: Placed on the right side of the bottom of the widget
      PL_BOTTOM_LEFT: Placed on the left side of the bottom of the widget
      PL_LEFT_BOTTOM: Placed on the bottom of the left side of the widget
      PL_LEFT_TOP: Placed on the top of the left side of the widget
    """
    POINTER_LOCATION_UNSPECIFIED = 0
    PL_TOP = 1
    PL_RIGHT = 2
    PL_BOTTOM = 3
    PL_LEFT = 4
    PL_TOP_LEFT = 5
    PL_TOP_RIGHT = 6
    PL_RIGHT_TOP = 7
    PL_RIGHT_BOTTOM = 8
    PL_BOTTOM_RIGHT = 9
    PL_BOTTOM_LEFT = 10
    PL_LEFT_BOTTOM = 11
    PL_LEFT_TOP = 12

  class VerticalAlignmentValueValuesEnum(_messages.Enum):
    r"""The vertical alignment of both the title and content

    Values:
      VERTICAL_ALIGNMENT_UNSPECIFIED: No vertical alignment specified, will
        default to V_TOP
      V_TOP: Top-align
      V_CENTER: Center-align
      V_BOTTOM: Bottom-align
    """
    VERTICAL_ALIGNMENT_UNSPECIFIED = 0
    V_TOP = 1
    V_CENTER = 2
    V_BOTTOM = 3

  backgroundColor = _messages.StringField(1)
  fontSize = _messages.EnumField('FontSizeValueValuesEnum', 2)
  horizontalAlignment = _messages.EnumField('HorizontalAlignmentValueValuesEnum', 3)
  padding = _messages.EnumField('PaddingValueValuesEnum', 4)
  pointerLocation = _messages.EnumField('PointerLocationValueValuesEnum', 5)
  textColor = _messages.StringField(6)
  verticalAlignment = _messages.EnumField('VerticalAlignmentValueValuesEnum', 7)


class Threshold(_messages.Message):
  r"""Defines a threshold for categorizing time series values.

  Enums:
    ColorValueValuesEnum: The state color for this threshold. Color is not
      allowed in a XyChart.
    DirectionValueValuesEnum: The direction for the current threshold.
      Direction is not allowed in a XyChart.
    TargetAxisValueValuesEnum: The target axis to use for plotting the
      threshold. Target axis is not allowed in a Scorecard.

  Fields:
    color: The state color for this threshold. Color is not allowed in a
      XyChart.
    direction: The direction for the current threshold. Direction is not
      allowed in a XyChart.
    label: A label for the threshold.
    targetAxis: The target axis to use for plotting the threshold. Target axis
      is not allowed in a Scorecard.
    value: The value of the threshold. The value should be defined in the
      native scale of the metric.
  """

  class ColorValueValuesEnum(_messages.Enum):
    r"""The state color for this threshold. Color is not allowed in a XyChart.

    Values:
      COLOR_UNSPECIFIED: Color is unspecified. Not allowed in well-formed
        requests.
      YELLOW: Crossing the threshold is "concerning" behavior.
      RED: Crossing the threshold is "emergency" behavior.
    """
    COLOR_UNSPECIFIED = 0
    YELLOW = 1
    RED = 2

  class DirectionValueValuesEnum(_messages.Enum):
    r"""The direction for the current threshold. Direction is not allowed in a
    XyChart.

    Values:
      DIRECTION_UNSPECIFIED: Not allowed in well-formed requests.
      ABOVE: The threshold will be considered crossed if the actual value is
        above the threshold value.
      BELOW: The threshold will be considered crossed if the actual value is
        below the threshold value.
    """
    DIRECTION_UNSPECIFIED = 0
    ABOVE = 1
    BELOW = 2

  class TargetAxisValueValuesEnum(_messages.Enum):
    r"""The target axis to use for plotting the threshold. Target axis is not
    allowed in a Scorecard.

    Values:
      TARGET_AXIS_UNSPECIFIED: The target axis was not specified. Defaults to
        Y1.
      Y1: The y_axis (the right axis of chart).
      Y2: The y2_axis (the left axis of chart).
    """
    TARGET_AXIS_UNSPECIFIED = 0
    Y1 = 1
    Y2 = 2

  color = _messages.EnumField('ColorValueValuesEnum', 1)
  direction = _messages.EnumField('DirectionValueValuesEnum', 2)
  label = _messages.StringField(3)
  targetAxis = _messages.EnumField('TargetAxisValueValuesEnum', 4)
  value = _messages.FloatField(5)


class Tile(_messages.Message):
  r"""A single tile in the mosaic. The placement and size of the tile are
  configurable.

  Fields:
    height: The height of the tile, measured in grid blocks. Tiles must have a
      minimum height of 1.
    widget: The informational widget contained in the tile. For example an
      XyChart.
    width: The width of the tile, measured in grid blocks. Tiles must have a
      minimum width of 1.
    xPos: The zero-indexed position of the tile in grid blocks relative to the
      left edge of the grid. Tiles must be contained within the specified
      number of columns. x_pos cannot be negative.
    yPos: The zero-indexed position of the tile in grid blocks relative to the
      top edge of the grid. y_pos cannot be negative.
  """

  height = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  widget = _messages.MessageField('Widget', 2)
  width = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  xPos = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  yPos = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class TimeSeriesFilter(_messages.Message):
  r"""A filter that defines a subset of time series data that is displayed in
  a widget. Time series data is fetched using the ListTimeSeries (https://clou
  d.google.com/monitoring/api/ref_v3/rest/v3/projects.timeSeries/list) method.

  Fields:
    aggregation: By default, the raw time series data is returned. Use this
      field to combine multiple time series for different views of the data.
    filter: Required. The monitoring filter
      (https://cloud.google.com/monitoring/api/v3/filters) that identifies the
      metric types, resources, and projects to query.
    pickTimeSeriesFilter: Ranking based time series filter.
    secondaryAggregation: Apply a second aggregation after aggregation is
      applied.
    statisticalTimeSeriesFilter: Statistics based time series filter. Note:
      This field is deprecated and completely ignored by the API.
  """

  aggregation = _messages.MessageField('Aggregation', 1)
  filter = _messages.StringField(2)
  pickTimeSeriesFilter = _messages.MessageField('PickTimeSeriesFilter', 3)
  secondaryAggregation = _messages.MessageField('Aggregation', 4)
  statisticalTimeSeriesFilter = _messages.MessageField('StatisticalTimeSeriesFilter', 5)


class TimeSeriesFilterRatio(_messages.Message):
  r"""A pair of time series filters that define a ratio computation. The
  output time series is the pair-wise division of each aligned element from
  the numerator and denominator time series.

  Fields:
    denominator: The denominator of the ratio.
    numerator: The numerator of the ratio.
    pickTimeSeriesFilter: Ranking based time series filter.
    secondaryAggregation: Apply a second aggregation after the ratio is
      computed.
    statisticalTimeSeriesFilter: Statistics based time series filter. Note:
      This field is deprecated and completely ignored by the API.
  """

  denominator = _messages.MessageField('RatioPart', 1)
  numerator = _messages.MessageField('RatioPart', 2)
  pickTimeSeriesFilter = _messages.MessageField('PickTimeSeriesFilter', 3)
  secondaryAggregation = _messages.MessageField('Aggregation', 4)
  statisticalTimeSeriesFilter = _messages.MessageField('StatisticalTimeSeriesFilter', 5)


class TimeSeriesQuery(_messages.Message):
  r"""TimeSeriesQuery collects the set of supported methods for querying time
  series data from the Stackdriver metrics API.

  Fields:
    opsAnalyticsQuery: Preview: A query used to fetch a time series, category
      series, or numeric series with SQL. This is a preview feature and may be
      subject to change before final release.
    outputFullDuration: Optional. If set, Cloud Monitoring will treat the full
      query duration as the alignment period so that there will be only 1
      output value.*Note: This could override the configured alignment period
      except for the cases where a series of data points are expected, like -
      XyChart - Scorecard's spark chart
    prometheusQuery: A query used to fetch time series with PromQL.
    timeSeriesFilter: Filter parameters to fetch time series.
    timeSeriesFilterRatio: Parameters to fetch a ratio between two time series
      filters.
    timeSeriesQueryLanguage: A query used to fetch time series with MQL.
    unitOverride: The unit of data contained in fetched time series. If non-
      empty, this unit will override any unit that accompanies fetched data.
      The format is the same as the unit (https://cloud.google.com/monitoring/
      api/ref_v3/rest/v3/projects.metricDescriptors) field in
      MetricDescriptor.
  """

  opsAnalyticsQuery = _messages.MessageField('OpsAnalyticsQuery', 1)
  outputFullDuration = _messages.BooleanField(2)
  prometheusQuery = _messages.StringField(3)
  timeSeriesFilter = _messages.MessageField('TimeSeriesFilter', 4)
  timeSeriesFilterRatio = _messages.MessageField('TimeSeriesFilterRatio', 5)
  timeSeriesQueryLanguage = _messages.StringField(6)
  unitOverride = _messages.StringField(7)


class TimeSeriesTable(_messages.Message):
  r"""A table that displays time series data.

  Enums:
    MetricVisualizationValueValuesEnum: Optional. Store rendering strategy

  Fields:
    columnSettings: Optional. The list of the persistent column settings for
      the table.
    dataSets: Required. The data displayed in this table.
    metricVisualization: Optional. Store rendering strategy
  """

  class MetricVisualizationValueValuesEnum(_messages.Enum):
    r"""Optional. Store rendering strategy

    Values:
      METRIC_VISUALIZATION_UNSPECIFIED: Unspecified state
      NUMBER: Default text rendering
      BAR: Horizontal bar rendering
    """
    METRIC_VISUALIZATION_UNSPECIFIED = 0
    NUMBER = 1
    BAR = 2

  columnSettings = _messages.MessageField('ColumnSettings', 1, repeated=True)
  dataSets = _messages.MessageField('TableDataSet', 2, repeated=True)
  metricVisualization = _messages.EnumField('MetricVisualizationValueValuesEnum', 3)


class Treemap(_messages.Message):
  r"""A widget that displays hierarchical data as a treemap.

  Fields:
    dataSets: Required. The collection of datasets used to construct and
      populate the treemap. For the rendered treemap rectangles: Color is
      determined by the aggregated value for each grouping. Size is
      proportional to the count of time series aggregated within that
      rectangle's segment.
    treemapHierarchy: Required. Ordered labels representing the hierarchical
      treemap structure.
  """

  dataSets = _messages.MessageField('TreemapDataSet', 1, repeated=True)
  treemapHierarchy = _messages.StringField(2, repeated=True)


class TreemapDataSet(_messages.Message):
  r"""The data represented by the treemap. Needs to include the data itself,
  plus rules on how to organize it hierarchically.

  Fields:
    breakdowns: Optional. The collection of breakdowns to be applied to the
      dataset. A breakdown is a way to slice the data. For example, you can
      break down the data by region.
    measures: Optional. A collection of measures. A measure is a measured
      value of a property in your data. For example, rainfall in inches,
      number of units sold, revenue gained, etc.
    timeSeriesQuery: Required. The query that fetches the relevant data. See
      google.monitoring.dashboard.v1.TimeSeriesQuery
  """

  breakdowns = _messages.MessageField('Breakdown', 1, repeated=True)
  measures = _messages.MessageField('Measure', 2, repeated=True)
  timeSeriesQuery = _messages.MessageField('TimeSeriesQuery', 3)


class Type(_messages.Message):
  r"""A protocol buffer message type.New usages of this message as an
  alternative to DescriptorProto are strongly discouraged. This message does
  not reliability preserve all information necessary to model the schema and
  preserve semantics. Instead make use of FileDescriptorSet which preserves
  the necessary information.

  Enums:
    SyntaxValueValuesEnum: The source syntax.

  Fields:
    edition: The source edition string, only valid when syntax is
      SYNTAX_EDITIONS.
    fields: The list of fields.
    name: The fully qualified message name.
    oneofs: The list of types appearing in oneof definitions in this type.
    options: The protocol buffer options.
    sourceContext: The source context.
    syntax: The source syntax.
  """

  class SyntaxValueValuesEnum(_messages.Enum):
    r"""The source syntax.

    Values:
      SYNTAX_PROTO2: Syntax proto2.
      SYNTAX_PROTO3: Syntax proto3.
      SYNTAX_EDITIONS: Syntax editions.
    """
    SYNTAX_PROTO2 = 0
    SYNTAX_PROTO3 = 1
    SYNTAX_EDITIONS = 2

  edition = _messages.StringField(1)
  fields = _messages.MessageField('Field', 2, repeated=True)
  name = _messages.StringField(3)
  oneofs = _messages.StringField(4, repeated=True)
  options = _messages.MessageField('Option', 5, repeated=True)
  sourceContext = _messages.MessageField('SourceContext', 6)
  syntax = _messages.EnumField('SyntaxValueValuesEnum', 7)


class VisibilityCondition(_messages.Message):
  r"""Condition that determines whether the widget should be displayed.

  Fields:
    templateVariableCondition: A condition whose evaluation is based on the
      value of a template variable.
  """

  templateVariableCondition = _messages.MessageField('TemplateVariableCondition', 1)


class Widget(_messages.Message):
  r"""Widget contains a single dashboard component and configuration of how to
  present the component in the dashboard.

  Fields:
    alertChart: A chart of alert policy data.
    blank: A blank space.
    collapsibleGroup: A widget that groups the other widgets. All widgets that
      are within the area spanned by the grouping widget are considered member
      widgets.
    errorReportingPanel: A widget that displays a list of error groups.
    id: Optional. The widget id. Ids may be made up of alphanumerics, dashes
      and underscores. Widget ids are optional.
    incidentList: A widget that shows list of incidents.
    logsPanel: A widget that shows a stream of logs.
    pieChart: A widget that displays timeseries data as a pie chart.
    scorecard: A scorecard summarizing time series data.
    sectionHeader: A widget that defines a section header for easier
      navigation of the dashboard.
    singleViewGroup: A widget that groups the other widgets by using a
      dropdown menu.
    text: A raw string or markdown displaying textual content.
    timeSeriesTable: A widget that displays time series data in a tabular
      format.
    title: Optional. The title of the widget.
    treemap: A widget that displays data as a treemap.
    visibilityCondition: Optional. If set, this widget is rendered only when
      the condition is evaluated to true.
    xyChart: A chart of time series data.
  """

  alertChart = _messages.MessageField('AlertChart', 1)
  blank = _messages.MessageField('Empty', 2)
  collapsibleGroup = _messages.MessageField('CollapsibleGroup', 3)
  errorReportingPanel = _messages.MessageField('ErrorReportingPanel', 4)
  id = _messages.StringField(5)
  incidentList = _messages.MessageField('IncidentList', 6)
  logsPanel = _messages.MessageField('LogsPanel', 7)
  pieChart = _messages.MessageField('PieChart', 8)
  scorecard = _messages.MessageField('Scorecard', 9)
  sectionHeader = _messages.MessageField('SectionHeader', 10)
  singleViewGroup = _messages.MessageField('SingleViewGroup', 11)
  text = _messages.MessageField('Text', 12)
  timeSeriesTable = _messages.MessageField('TimeSeriesTable', 13)
  title = _messages.StringField(14)
  treemap = _messages.MessageField('Treemap', 15)
  visibilityCondition = _messages.MessageField('VisibilityCondition', 16)
  xyChart = _messages.MessageField('XyChart', 17)


class XyChart(_messages.Message):
  r"""A chart that displays data on a 2D (X and Y axes) plane.

  Fields:
    chartOptions: Display options for the chart.
    dataSets: Required. The data displayed in this chart.
    thresholds: Threshold lines drawn horizontally across the chart.
    timeshiftDuration: The duration used to display a comparison chart. A
      comparison chart simultaneously shows values from two similar-length
      time periods (e.g., week-over-week metrics). The duration must be
      positive, and it can only be applied to charts with data sets of LINE
      plot type.
    xAxis: The properties applied to the x-axis.
    y2Axis: The properties applied to the y2-axis.
    yAxis: The properties applied to the y-axis.
  """

  chartOptions = _messages.MessageField('ChartOptions', 1)
  dataSets = _messages.MessageField('DataSet', 2, repeated=True)
  thresholds = _messages.MessageField('Threshold', 3, repeated=True)
  timeshiftDuration = _messages.StringField(4)
  xAxis = _messages.MessageField('Axis', 5)
  y2Axis = _messages.MessageField('Axis', 6)
  yAxis = _messages.MessageField('Axis', 7)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
