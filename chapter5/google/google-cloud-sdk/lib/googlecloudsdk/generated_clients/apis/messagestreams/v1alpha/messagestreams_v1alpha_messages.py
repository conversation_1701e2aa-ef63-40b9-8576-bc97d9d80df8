"""Generated message classes for messagestreams version v1alpha.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'messagestreams'


class AuthenticationConfig(_messages.Message):
  r"""Represents a config used to authenticate message requests.

  Fields:
    googleOauth: If specified, an [OAuth
      token](https://developers.google.com/identity/protocols/OAuth2) will be
      generated and attached as an `Authorization` header in the HTTP request.
      This type of authorization should generally only be used when calling
      Google APIs hosted on *.googleapis.com.
    googleOidc: This authenticate method will apply Google OIDC tokens signed
      by a GCP service account to the requests.
  """

  googleOauth = _messages.MessageField('GoogleOAuth', 1)
  googleOidc = _messages.MessageField('GoogleOidc', 2)


class AvroFormat(_messages.Message):
  r"""The format of an AVRO message payload.

  Fields:
    schemaDefinition: Optional. The entire schema definition is stored in this
      field.
  """

  schemaDefinition = _messages.StringField(1)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CreateReferenceRequest(_messages.Message):
  r"""The CreateReferenceRequest request.

  Fields:
    parent: Required. The parent resource name (target_resource of this
      reference). For example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}`.
    reference: Required. The reference to be created.
    referenceId: The unique id of this resource. Must be unique within a scope
      of a target resource, but does not have to be globally unique. Reference
      ID is part of resource name of the reference. Resource name is generated
      in the following way: {parent}/references/{reference_id}. Reference ID
      field is currently required but id auto generation might be added in the
      future. It can be any arbitrary string, either GUID or any other string,
      however CLHs can use preprocess callbacks to perform a custom
      validation.
    requestId: Optional. Request ID is an idempotency ID of the request. It
      must be a valid UUID. Zero UUID (00000000-0000-0000-0000-000000000000)
      is not supported.
  """

  parent = _messages.StringField(1)
  reference = _messages.MessageField('Reference', 2)
  referenceId = _messages.StringField(3)
  requestId = _messages.StringField(4)


class DeleteReferenceRequest(_messages.Message):
  r"""The DeleteReferenceRequest request.

  Fields:
    name: Required. Full resource name of the reference, in the following
      format:
      `//{targer_service}/{target_resource}/references/{reference_id}`. For
      example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}/references/{xyz}`.
    requestId: Optional. Request ID is an idempotency ID of the request. It
      must be a valid UUID. Zero UUID (00000000-0000-0000-0000-000000000000)
      is not supported.
  """

  name = _messages.StringField(1)
  requestId = _messages.StringField(2)


class Destination(_messages.Message):
  r"""Specifications of a destination to which the request should be routed
  to.

  Fields:
    authenticationConfig: Optional. An authentication config used to
      authenticate message requests, such that destinations can verify the
      source. For example, this can be used with private GCP destinations that
      require GCP crendential to access like Cloud Run. This field is optional
      and should be set only by users interested in authenticated push.
    dynamicHttpHeadersEnabled: Optional. If true, the Stream will recognize a
      predefined set of HTTP headers for the destination routing and
      authentication. The dynamic HTTP headers can be generated by using the
      Message Stream destination binding expression, which would typically
      extract the necessary information from the CloudEvent payload, and
      encode it in the HTTP headers of the request. Once the Stream uses the
      headers to configure its behavior, all those headers are stripped from
      the ultimate request, so they do not get sent to the target of the
      message. Supported HTTP headers: - `X-Google-Destination-Http-Route`:
      The header is used to identify the destination of the message. -
      `X-Google-Destination-Http-Method`: The header is used to identify the
      HTTP method to be used when sending the message to the destination. It
      is only applicable when the destination is a HTTP endpoint. Available
      values are POST, GET, HEAD, PUT, DELETE, PATCH, OPTIONS. - `X-Google-
      Destination-PubSub-Topic`: The header is used to identify the Pub/Sub
      topic to be used when sending the message. - `X-Google-Destination-
      OAuth-ServiceAccount`: The header is used to identify the service
      account used to generate OAuth tokens to authenticate the requests. -
      `X-Google-Destination-OAuth-Scope`: The header is used to identify the
      OAuth scope used to generate OAuth tokens to authenticate the requests.
      - `X-Google-Destination-OIDC-ServiceAccount`: The header is used to
      identify the service account used to generate OIDC tokens to
      authenticate the requests. - `X-Google-Destination-OIDC-Audience`: The
      header is used to identify the audience used to generate OIDC tokens to
      authenticate the requests. - `X-Google-Retry-Policy-ID`: The header is
      used to identify the retry policy previously configured with the retry
      policy PDP service.
    messageBindingHttp: Optional. HTTP Binding.
    networkConfig: Optional. Network config is used to configure how Message
      Streams resolves and connect to a destination.
    outputPayloadFormat: Optional. The format of the payload before it's
      delivered to the destination. If not set, the message will be delivered
      in the format it was originally delivered to the Stream. This field can
      only be set if `Stream.input_payload_format` is also set.
    serviceEndpoint: Required. The URL of a endpoint to route traffic to. If
      this is a Multi-Single Tenant Stream (i.e. when use_shared_pool is set
      to false) then: If a DNS FQDN is provided as the endpoint, Message
      Streams will create a peering zone to the consumer VPC and forward DNS
      requests to the VPC specified by network config to resolve the service
      endpoint. See: https://cloud.google.com/dns/docs/zones/zones-
      overview#peering_zones If this is a Multi-Tenant Stream (i.e. when
      use_shared_pool is set to true) then: - If the service_endpoint starts
      with http:// or https:// then the Stream uses Harpoon to reach the HTTP
      endpoint. - If the service_endpoint is set to pubsub://* and the
      dynamic_http_headers_enabled is set to true then the stream uses the
      X-Google-Destination-Pubsub-Topic header to identify the Pub/Sub topic
      to be used when sending the message (see go/mi-ms-dynamic-stream-ug).
      NOTE: when setting service_endpoint to pubsub://* then the
      dynamic_http_headers_enabled MUST be set to true. - For stubby
      endpoints, we follow the format described in go/cloud-pubsub-on-
      borg#using-a-push-subscription-with-a-stubby-push-endpoint. The stubby
      endpoint must implement the [`CloudEventReceiver`](cs/symbol:orchestrati
      on.convoy.CloudEventReceiver) service.
  """

  authenticationConfig = _messages.MessageField('AuthenticationConfig', 1)
  dynamicHttpHeadersEnabled = _messages.BooleanField(2)
  messageBindingHttp = _messages.MessageField('MessageBindingHttp', 3)
  networkConfig = _messages.MessageField('NetworkConfig', 4)
  outputPayloadFormat = _messages.MessageField('MessagePayloadFormat', 5)
  serviceEndpoint = _messages.StringField(6)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class GetReferenceRequest(_messages.Message):
  r"""The GetReferenceRequest request.

  Fields:
    name: Required. Full resource name of the reference, in the following
      format:
      `//{target_service}/{target_resource}/references/{reference_id}`. For
      example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}/references/{xyz}`.
  """

  name = _messages.StringField(1)


class GoogleOAuth(_messages.Message):
  r"""Contains information needed for generating an [OAuth
  token](https://developers.google.com/identity/protocols/OAuth2). This type
  of authorization should generally only be used when calling Google APIs
  hosted on *.googleapis.com.

  Fields:
    scope: Optional. OAuth scope to be used for generating OAuth access token.
      If not specified, "https://www.googleapis.com/auth/cloud-platform" will
      be used.
    serviceAccount: Required. [Service account
      email](https://cloud.google.com/iam/docs/service-accounts) to be used
      for generating OAuth token. The service account must be within the same
      project as the job. The caller must have iam.serviceAccounts.actAs
      permission for the service account.
  """

  scope = _messages.StringField(1)
  serviceAccount = _messages.StringField(2)


class GoogleOidc(_messages.Message):
  r"""Represents a config used to authenticate with a Google OIDC token using
  a GCP service account. Use this authentication method to invoke your Cloud
  Run and Cloud Functions destinations or HTTP endpoints that support Google
  OIDC.

  Fields:
    audience: Optional. Audience to be used to generate the OIDC Token. The
      audience claim identifies the recipient that the JWT is intended for. If
      unspecified, the destination URI will be used.
    serviceAccount: Required. The IAM service account email used as the
      identity of the stream resource. The service account is used to generate
      OIDC tokens for the outbound messages. It's also used to read messages
      from the "source". In addition to service account email, the resource
      name of the service account can be used in the format of
      `projects/-/serviceAccounts/{ACCOUNT}`, ACCOUNT can be email address or
      uniqueId of the service account (see https://cloud.google.com/iam/refere
      nce/rest/v1/projects.serviceAccounts/get).
  """

  audience = _messages.StringField(1)
  serviceAccount = _messages.StringField(2)


class InlineRetryPolicy(_messages.Message):
  r"""The retry policy.

  Fields:
    truncatedExpBackoffRetryPolicy: Optional. Configuration for a
      TruncatedExponentialBackoff retry policy.
  """

  truncatedExpBackoffRetryPolicy = _messages.MessageField('TruncatedExponentialBackoff', 1)


class JsonFormat(_messages.Message):
  r"""The format of a JSON message payload."""


class KafkaAuthenticationConfig(_messages.Message):
  r"""Authentication configuration.

  Fields:
    mutualTlsAuth: Using mTLS.
    saslAuth: Using SASL/Plain or SASL/SCRAM.
  """

  mutualTlsAuth = _messages.MessageField('MutualTlsAuthConfig', 1)
  saslAuth = _messages.MessageField('SaslAuthConfig', 2)


class KafkaSource(_messages.Message):
  r"""Kafka Source configuration.

  Fields:
    brokerUris: Required. The Kafka broker URIs. e.g. 10.12.34.56:8080
    consumerGroupId: Required. The consumer group ID used by the Kafka broker
      to track the offsets of all topic partitions being read by this Stream.
    initialOffset: Required. The initial message offset from which to start
      streaming. Supported values: newest, oldest.
    kafkaAuthenticationConfig: Optional. Authentication configuration used to
      authenticate the Kafka client with the Kafka broker, and authorize to
      read the topic(s).
    topics: Required. The Kafka topics to read from.
  """

  brokerUris = _messages.StringField(1, repeated=True)
  consumerGroupId = _messages.StringField(2)
  initialOffset = _messages.StringField(3)
  kafkaAuthenticationConfig = _messages.MessageField('KafkaAuthenticationConfig', 4)
  topics = _messages.StringField(5, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListReferencesRequest(_messages.Message):
  r"""The ListResourceMetadataRequest request.

  Fields:
    pageSize: The maximum number of items to return. If unspecified, server
      will pick an appropriate default. Server may return fewer items than
      requested. A caller should only rely on response's next_page_token to
      determine if there are more References left to be queried.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: Required. The parent resource name (target_resource of this
      reference). For example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3)


class ListReferencesResponse(_messages.Message):
  r"""The ListReferencesResponse response.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    references: The list of references.
  """

  nextPageToken = _messages.StringField(1)
  references = _messages.MessageField('Reference', 2, repeated=True)


class ListRetryPoliciesResponse(_messages.Message):
  r"""Response for listing retry policies.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    retryPolicies: The retry policies id in spanner.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  retryPolicies = _messages.MessageField('RetryPolicy', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListStreamsResponse(_messages.Message):
  r"""Message for response to listing streams

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    streams: The list of Stream
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  streams = _messages.MessageField('Stream', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class LoggingConfig(_messages.Message):
  r"""The configuration for Platform Telemetry logging for the Stream
  resource.

  Enums:
    LogSeverityValueValuesEnum: Optional. The minimum severity of logs that
      will be sent to Stackdriver/Platform Telemetry. Logs at severitiy
      \\u2265 this value will be sent, unless it is NONE.

  Messages:
    LabelsValue: Optional. Labels store a set of opaque key-value pairs that
      are supplied by the client and optionally processed in the downstream
      systems, e.g., Cloud Logging.

  Fields:
    labels: Optional. Labels store a set of opaque key-value pairs that are
      supplied by the client and optionally processed in the downstream
      systems, e.g., Cloud Logging.
    logSeverity: Optional. The minimum severity of logs that will be sent to
      Stackdriver/Platform Telemetry. Logs at severitiy \u2265 this value will
      be sent, unless it is NONE.
  """

  class LogSeverityValueValuesEnum(_messages.Enum):
    r"""Optional. The minimum severity of logs that will be sent to
    Stackdriver/Platform Telemetry. Logs at severitiy \\u2265 this value will
    be sent, unless it is NONE.

    Values:
      LOG_SEVERITY_UNSPECIFIED: Log severity is not specified. This value is
        treated the same as NONE, but is used to distinguish between no update
        and update to NONE in update_masks.
      NONE: Default value at resource creation, presence of this value must be
        treated as no logging/disable logging.
      DEBUG: Debug or trace level logging.
      INFO: Routine information, such as ongoing status or performance.
      NOTICE: Normal but significant events, such as start up, shut down, or a
        configuration change.
      WARNING: Warning events might cause problems.
      ERROR: Error events are likely to cause problems.
      CRITICAL: Critical events cause more severe problems or outages.
      ALERT: A person must take action immediately.
      EMERGENCY: One or more systems are unusable.
    """
    LOG_SEVERITY_UNSPECIFIED = 0
    NONE = 1
    DEBUG = 2
    INFO = 3
    NOTICE = 4
    WARNING = 5
    ERROR = 6
    CRITICAL = 7
    ALERT = 8
    EMERGENCY = 9

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels store a set of opaque key-value pairs that are
    supplied by the client and optionally processed in the downstream systems,
    e.g., Cloud Logging.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)
  logSeverity = _messages.EnumField('LogSeverityValueValuesEnum', 2)


class Mediation(_messages.Message):
  r"""Mediation defines different ways to modify the stream.

  Fields:
    bindAttributesAsRawHeaders: Optional. If bind_attributes_as_raw_headers
      set true, we will bind the attributes of an incoming cloud event as raw
      HTTP headers.
    transformation: Optional. Transformation defines the way to transform an
      incoming message.
  """

  bindAttributesAsRawHeaders = _messages.BooleanField(1)
  transformation = _messages.MessageField('Transformation', 2)


class MessageBindingHttp(_messages.Message):
  r"""Represents a config used to bind a message in the internal
  representation of an event to the final HTTP request that will be sent to
  the destination. If a binding is not specified here, by default the message
  is treated as a CloudEvent and is mapped to the HTTP request, according to
  the CloudEvent HTTP Protocol Binding Binary Content Mode. The stream
  converts the data field of the message to the format provided in
  destination-output-payload-format and maps it to the body field of the
  result. It also sets the corresponding Content-Type header in the headers
  map field of the return value. The stream expects that the content of the
  message will adhere to the standard CloudEvent format. If it doesn't then
  the incoming message request will fail with a persistent error.

  Fields:
    celExpression: Required. The CEL expression used to modify how the
      destination-bound HTTP request is constructed. If a binding expression
      is not specified here, the message is treated as a CloudEvent and is
      mapped to the HTTP request according to the CloudEvent HTTP Protocol
      Binding Binary Content Mode (https://github.com/cloudevents/spec/blob/ma
      in/cloudevents/bindings/http-protocol-binding.md#31-binary-content-
      mode). In this representation, all fields except the `data` and
      `datacontenttype` field on the message are mapped to HTTP request
      headers with a prefix of `ce-`. To construct the HTTP request payload
      and the value of the content-type HTTP header, the payload format is
      defined as follows: 1) Use the output_payload_format on the
      Stream.Destination if it is set, else: 2) Use the input_payload_format
      on the Stream if it is set, else: 3) Treat the payload as opaque binary
      data. The `data` field of the message is converted to the payload format
      or left as-is for case 3) and then attached as the payload of the HTTP
      request. The `content-type` header on the HTTP request is set to the
      payload format type or left empty for case 3). However, if a mediation
      has updated the `datacontenttype` field on the message so that it is not
      the same as the payload format type but it is still a prefix of the
      payload format type, then the `content-type` header on the HTTP request
      is set to this `datacontenttype` value. For example, if the
      `datacontenttype` is "application/json" and the payload format type is
      "application/json; charset=utf-8", then the `content-type` header on the
      HTTP request is set to "application/json; charset=utf-8". If a non-empty
      binding expression is specified then this expression is used to modify
      the default CloudEvent HTTP Protocol Binding Binary Content
      representation. The result of the CEL expression must be a map of
      key/value pairs which is used as follows: - If a map named `headers`
      exists on the result of the expression, then its key/value pairs are
      directly mapped to the HTTP request headers. The headers values are
      constructed from the corresponding value type's canonical
      representation. If the `headers` field doesn't exist then the resulting
      HTTP request will be the headers of the CloudEvent HTTP Binding Binary
      Content Mode representation of the final message. Note: If the specified
      binding expression, has updated the `datacontenttype` field on the
      message so that it is not the same as the payload format type but it is
      still a prefix of the payload format type, then the `content-type`
      header in the `headers` map is set to this `datacontenttype` value. - If
      a field named `body` exists on the result of the expression then its
      value is directly mapped to the body of the request. If the value of the
      `body` field is of type bytes or string then it is used for the HTTP
      request body as-is, with no conversion. If the body field is of any
      other type then it is converted to a JSON string. If the body field does
      not exist then the resulting payload of the HTTP request will be data
      value of the CloudEvent HTTP Binding Binary Content Mode representation
      of the final message as described earlier. - Any other fields in the
      resulting expression will be ignored. The CEL expression may access the
      incoming CloudEvent message in its definition, as follows: - The `data`
      field of the incoming CloudEvent message can be accessed using the
      `message.data` value. Subfields of `message.data` may also be accessed
      if an input_payload_format has been specified on the Stream. - Each
      attribute of the incoming CloudEvent message can be accessed using the
      `message.` value, where is replaced with the name of the attribute. -
      Existing headers can be accessed in the CEL expression using the
      `headers` variable. The `headers` variable defines a map of key/value
      pairs corresponding to the HTTP headers of the CloudEvent HTTP Binding
      Binary Content Mode representation of the final message as described
      earlier. For example, the following CEL expression can be used to
      construct an HTTP request by adding an additional header to the HTTP
      headers of the CloudEvent HTTP Binding Binary Content Mode
      representation of the final message and by overwriting the body of the
      request: ``` { "headers": headers.merge({"new-header-key": "new-header-
      value"}), "body": "new-body" } ``` - The default binding for the message
      payload can be accessed using the `body` variable. It conatins a string
      representation of the message payload in the format specified by the
      `output_payload_format` field. If the `input_payload_format` field is
      not set, the `body` variable contains the same message payload bytes
      that were published. Additionally, the following CEL extension functions
      are provided for use in this CEL expression: - toBase64Url:
      map.toBase64Url() -> string - Converts a CelValue to a base64url encoded
      string - toJsonString: map.toJsonString() -> string - Converts a
      CelValue to a JSON string - merge: map1.merge(map2) -> map3 - Merges the
      passed CEL map with the existing CEL map the function is applied to. -
      If the same key exists in both maps, if the key's value is type map both
      maps are merged else the value from the passed map is used. -
      denormalize: map.denormalize() -> map - Denormalizes a CEL map such that
      every value of type map or key in the map is expanded to return a single
      level map. - The resulting keys are "." separated indices of the map
      keys. - For example: { "a": 1, "b": { "c": 2, "d": 3 } "e": [4, 5] }
      .denormalize() -> { "a": 1, "b.c": 2, "b.d": 3, "e.0": 4, "e.1": 5 } -
      setField: map.setField(key, value) -> message - Sets the field of the
      message with the given key to the given value. - If the field is not
      present it will be added. - If the field is present it will be
      overwritten. - The key can be a dot separated path to set a field in a
      nested message. - Key must be of type string. - Value may be any valid
      type. - removeFields: map.removeFields([key1, key2, ...]) -> message -
      Removes the fields of the map with the given keys. - The keys can be a
      dot separated path to remove a field in a nested message. - If a key is
      not found it will be ignored. - Keys must be of type string. - toMap:
      [map1, map2, ...].toMap() -> map - Converts a CEL list of CEL maps to a
      single CEL map - toCloudEventJsonWithPayloadFormat:
      message.toCloudEventJsonWithPayloadFormat() -> map - Converts a message
      to the corresponding structure of JSON format for CloudEvents. - It
      converts `data` to destination payload format specified in
      `output_payload_format`. If `output_payload_format` is not set, the data
      will remain unchanged. - It also sets the corresponding datacontenttype
      of the CloudEvent, as indicated by `output_payload_format`. If no
      `output_payload_format` is set it will use the value of the
      "datacontenttype" attribute on the CloudEvent if present, else remove
      "datacontenttype" attribute. - This function expects that the content of
      the message will adhere to the standard CloudEvent format. If it doesn't
      then this function will fail. - The result is a CEL map that corresponds
      to the JSON representation of the CloudEvent. To convert that data to a
      JSON string it can be chained with the toJsonString function. The Stream
      expects that the message it receives adheres to the standard CloudEvent
      format. If it doesn't then the outgoing message request may fail with a
      persistent error.
  """

  celExpression = _messages.StringField(1)


class MessagePayloadFormat(_messages.Message):
  r"""The format of a message payload.

  Fields:
    avro: Optional. AVRO format.
    json: Optional. JSON format.
    protobuf: Optional. PROTOBUF format.
  """

  avro = _messages.MessageField('AvroFormat', 1)
  json = _messages.MessageField('JsonFormat', 2)
  protobuf = _messages.MessageField('ProtobufFormat', 3)


class MessagestreamsProjectsLocationsGetRequest(_messages.Message):
  r"""A MessagestreamsProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class MessagestreamsProjectsLocationsListRequest(_messages.Message):
  r"""A MessagestreamsProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    includeUnrevealedLocations: If true, the returned list will include
      locations which are not yet revealed.
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  includeUnrevealedLocations = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  pageSize = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(6)


class MessagestreamsProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A MessagestreamsProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class MessagestreamsProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A MessagestreamsProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class MessagestreamsProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A MessagestreamsProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class MessagestreamsProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A MessagestreamsProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class MessagestreamsProjectsLocationsStreamsCreateRequest(_messages.Message):
  r"""A MessagestreamsProjectsLocationsStreamsCreateRequest object.

  Fields:
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    stream: A Stream resource to be passed as the request body.
    streamId: Required. Id of the requesting object If auto-generating Id
      server-side, remove this field and stream_id from the method_signature
      of Create RPC
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  stream = _messages.MessageField('Stream', 3)
  streamId = _messages.StringField(4)


class MessagestreamsProjectsLocationsStreamsDeleteRequest(_messages.Message):
  r"""A MessagestreamsProjectsLocationsStreamsDeleteRequest object.

  Fields:
    etag: Optional. If provided, the stream will only be deleted if the etag
      matches the current etag on the resource.
    force: Optional. If set to true, any retry policies from this stream will
      also be deleted. Followed the best practice from
      https://aip.dev/135#cascading-delete
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  etag = _messages.StringField(1)
  force = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class MessagestreamsProjectsLocationsStreamsGetRequest(_messages.Message):
  r"""A MessagestreamsProjectsLocationsStreamsGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class MessagestreamsProjectsLocationsStreamsListRequest(_messages.Message):
  r"""A MessagestreamsProjectsLocationsStreamsListRequest object.

  Fields:
    filter: Filtering results
    orderBy: Hint for how to order the results
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListStreamsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class MessagestreamsProjectsLocationsStreamsPatchRequest(_messages.Message):
  r"""A MessagestreamsProjectsLocationsStreamsPatchRequest object.

  Fields:
    name: The resource name of the stream. Must be unique within the location
      of the project and must be in
      `projects/{project}/locations/{location}/streams/{stream}` format.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    stream: A Stream resource to be passed as the request body.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Stream resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  stream = _messages.MessageField('Stream', 3)
  updateMask = _messages.StringField(4)


class MessagestreamsProjectsLocationsStreamsRetryPoliciesCreateRequest(_messages.Message):
  r"""A MessagestreamsProjectsLocationsStreamsRetryPoliciesCreateRequest
  object.

  Fields:
    parent: Required. Value for parent.
    retryPolicy: A RetryPolicy resource to be passed as the request body.
    retryPolicyId: Required. Id of the requesting object If auto-generating Id
      server-side, remove this field and stream_id from the method_signature
      of Create RPC
  """

  parent = _messages.StringField(1, required=True)
  retryPolicy = _messages.MessageField('RetryPolicy', 2)
  retryPolicyId = _messages.StringField(3)


class MessagestreamsProjectsLocationsStreamsRetryPoliciesDeleteRequest(_messages.Message):
  r"""A MessagestreamsProjectsLocationsStreamsRetryPoliciesDeleteRequest
  object.

  Fields:
    name: Required. The retry policy name in spanner. Name is a required field
      by ccfe.
  """

  name = _messages.StringField(1, required=True)


class MessagestreamsProjectsLocationsStreamsRetryPoliciesGetRequest(_messages.Message):
  r"""A MessagestreamsProjectsLocationsStreamsRetryPoliciesGetRequest object.

  Fields:
    name: Required. The retry policy id in spanner.
  """

  name = _messages.StringField(1, required=True)


class MessagestreamsProjectsLocationsStreamsRetryPoliciesListRequest(_messages.Message):
  r"""A MessagestreamsProjectsLocationsStreamsRetryPoliciesListRequest object.

  Fields:
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Valye for parent.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class MetricsConfig(_messages.Message):
  r"""The configuration for optional annotations on Metrics emitted by the
  Stream resource.

  Messages:
    LabelsValue: Optional. Labels store a set of opaque key-value pairs that
      are supplied by the client and optionally processed in the downstream
      systems depending on the Stream filter. These are alternatively known as
      System Labels, and are not visible to the end user without explicit
      configuration. These labels are used to annotate the metrics emitted by
      the Stream resource. Billing metrics will inherit these labels and be
      used in the billing pipeline via MTX Transformations to reroute the
      metrics to the correct billing resource.

  Fields:
    labels: Optional. Labels store a set of opaque key-value pairs that are
      supplied by the client and optionally processed in the downstream
      systems depending on the Stream filter. These are alternatively known as
      System Labels, and are not visible to the end user without explicit
      configuration. These labels are used to annotate the metrics emitted by
      the Stream resource. Billing metrics will inherit these labels and be
      used in the billing pipeline via MTX Transformations to reroute the
      metrics to the correct billing resource.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels store a set of opaque key-value pairs that are
    supplied by the client and optionally processed in the downstream systems
    depending on the Stream filter. These are alternatively known as System
    Labels, and are not visible to the end user without explicit
    configuration. These labels are used to annotate the metrics emitted by
    the Stream resource. Billing metrics will inherit these labels and be used
    in the billing pipeline via MTX Transformations to reroute the metrics to
    the correct billing resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)


class MutualTlsAuthConfig(_messages.Message):
  r"""Mutual TLS authentication mechanism configuration.

  Fields:
    secretManagerResources: mTLS auth config loaded from Secret Manager.
  """

  secretManagerResources = _messages.MessageField('MutualTlsSecrets', 1)


class MutualTlsSecrets(_messages.Message):
  r"""Mutual TLS payloads from Secret Manager.

  Fields:
    clientCertificate: Required. The client certificate for mTLS may be loaded
      from Secret Manager. Supported Formats:
      `projects/{project}/secrets/{secret}/versions/{version}` `projects/{proj
      ect}/locations/{location}/secrets/{secret}/versions/{version}`
    clientKey: Required. The client key for mTLS may be loaded from Secret
      Manager. Supported Formats:
      `projects/{project}/secrets/{secret}/versions/{version}` `projects/{proj
      ect}/locations/{location}/secrets/{secret}/versions/{version}`
  """

  clientCertificate = _messages.StringField(1)
  clientKey = _messages.StringField(2)


class NetworkConfig(_messages.Message):
  r"""Represents a network config to be used for destination resolution and
  connectivity.

  Fields:
    networkAttachment: Required. Name of the NetworkAttachment that allows
      access to the consumer VPC. The NetworkAttachment must be in the same
      region as the Stream. Format: `projects/{PROJECT_ID}/regions/{REGION}/ne
      tworkAttachments/{NETWORK_ATTACHMENT_NAME}`
  """

  networkAttachment = _messages.StringField(1)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class ProtobufFormat(_messages.Message):
  r"""The format of a Protobuf message payload.

  Fields:
    schemaDefinition: Optional. The entire schema definition is stored in this
      field.
  """

  schemaDefinition = _messages.StringField(1)


class Reference(_messages.Message):
  r"""Represents a reference to a resource.

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    createTime: Output only. The creation time.
    details: Details of the reference type with no implied semantics.
      Cumulative size of the field must not be more than 1KiB.
    name: Output only. Relative resource name of the reference. Includes
      target resource as a parent and reference uid
      `{target_resource}/references/{reference_id}`. For example,
      `projects/{my-project}/locations/{location}/instances/{my-
      instance}/references/{xyz}`.
    sourceResource: Required. Full resource name of the resource which refers
      the target resource. For example:
      //tpu.googleapis.com/projects/myproject/nodes/mynode
    targetUniqueId: Output only. The unique_id of the target resource. Example
      1: (For arcus resource) A-1-0-2-387420123-13-913517247483640811
      unique_id format defined in go/m11n-unique-id-as-resource-id Example 2:
      (For CCFE resource) 123e4567-e89b-12d3-a456-************
    type: Required. Type of the reference. A service might impose limits on
      number of references of a specific type. Note: It's recommended to use
      CAPITALS_WITH_UNDERSCORES style for a type name.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  name = _messages.StringField(3)
  sourceResource = _messages.StringField(4)
  targetUniqueId = _messages.StringField(5)
  type = _messages.StringField(6)


class RetryPolicy(_messages.Message):
  r"""The representation of the retry policy resource.

  Fields:
    name: Required. Identifier. The name of the retry policy. Format: projects
      /{project}/locations/{location}/streams/{stream}/retryPolicies/{retry_po
      licy}
    truncatedExpBackoffRetryPolicy: Optional. Configuration for a
      TruncatedExponentialBackoff retry policy.
  """

  name = _messages.StringField(1)
  truncatedExpBackoffRetryPolicy = _messages.MessageField('TruncatedExponentialBackoff', 2)


class SaslAuthConfig(_messages.Message):
  r"""SASL/Plain or SASL/SCRAM mechanism configuration.

  Enums:
    MechanismValueValuesEnum: Required. The authentication method used by the
      Kafka broker.

  Fields:
    mechanism: Required. The authentication method used by the Kafka broker.
    passwordSecret: Required. The password for the authentication identity
      loaded from Secret Manager. Supported Formats:
      `projects/{project}/secrets/{secret}/versions/{version}` `projects/{proj
      ect}/locations/{location}/secrets/{secret}/versions/{version}`
    username: Required. The SASL authentication identity (username).
    usernameSecret: Required. The SASL authentication identity (username)
      loaded from Secret Manager. Supported Formats:
      `projects/{project}/secrets/{secret}/versions/{version}` `projects/{proj
      ect}/locations/{location}/secrets/{secret}/versions/{version}`
  """

  class MechanismValueValuesEnum(_messages.Enum):
    r"""Required. The authentication method used by the Kafka broker.

    Values:
      AUTH_MECHANISM_UNSPECIFIED: No authentication mechanism was specified.
      PLAIN: SASL/Plain authentication mechanism.
      SHA_256: SASL/SCRAM-SHA-256 authentication mechanism.
      SHA_512: SASL/SCRAM-SHA-512 authentication mechanism.
    """
    AUTH_MECHANISM_UNSPECIFIED = 0
    PLAIN = 1
    SHA_256 = 2
    SHA_512 = 3

  mechanism = _messages.EnumField('MechanismValueValuesEnum', 1)
  passwordSecret = _messages.StringField(2)
  username = _messages.StringField(3)
  usernameSecret = _messages.StringField(4)


class Source(_messages.Message):
  r"""Represents the source where we stream data from.

  Fields:
    kafka: Configurations of the Kafka client streaming from a Kafka cluster.
    networkConfig: Optional. Network config is used to configure how Message
      Streams resolves and connect to a source.
    pubsubSubscription: A string attribute.
  """

  kafka = _messages.MessageField('KafkaSource', 1)
  networkConfig = _messages.MessageField('NetworkConfig', 2)
  pubsubSubscription = _messages.StringField(3)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class Stream(_messages.Message):
  r"""The representation of the stream resource.

  Enums:
    EventarcTransformationTypeValueValuesEnum: Optional.

  Messages:
    AnnotationsValue: Optional. User-defined annotations. See
      https://google.aip.dev/128#annotations
    LabelsValue: Optional. Labels as key value pairs

  Fields:
    annotations: Optional. User-defined annotations. See
      https://google.aip.dev/128#annotations
    createTime: Output only. [Output only] Create time stamp
    displayName: Optional. Display name of resource.
    etag: Output only. This checksum is computed by the server based on the
      value of other fields, and might be sent only on create requests to
      ensure that the client has an up-to-date value before proceeding.
    eventarcTransformationType: Optional.
    inputPayloadFormat: Optional. The data format expected for the messages
      received by the Stream. If input_payload_format is set then any messages
      not matching this format will be treated as persistent errors. If
      input_payload_format is not set, then the message data will be treated
      as an opaque binary and no output format can be set on the stream
      through the Stream.Destination.output_payload_format field. Any
      Mediations on the stream that involve access to the data field will fail
      as persistent errors.
    labels: Optional. Labels as key value pairs
    loggingConfig: Optional. Config to control Platform Logging for Streams.
    mediations: Optional. Mediations to define the way to modify the incoming
      message.
    metricsConfig: Optional. Config to control Metrics for Streams.
    name: The resource name of the stream. Must be unique within the location
      of the project and must be in
      `projects/{project}/locations/{location}/streams/{stream}` format.
    replyBus: Optional. The resource name of the Message Bus to send replies
      to. If this field is empty, then no replies will be generated. For
      example,
      `projects/{project}/locations/{location}/messageBuses/{messageBus}`.
    retryPolicy: Optional. Configuration for a SimpleRetryPolicy.
    source: Optional. Source specifies where the stream reads data from.
    streamAction: Required. The specifications for routing messaging traffic
      and applying associated policies.
    streamIdentityOverride: Optional.
    uid: Output only. Server-assigned unique identifier for the stream. The
      value is a UUID4 string and guaranteed to remain unchanged until the
      resource is deleted.
    updateTime: Output only. [Output only] Update time stamp
    useSharedPool: Optional. use_shared_pool specifies whether the Stream will
      run with dedicated resources or using the shared pool. Dedicated pools
      cost more but provide better workload isolation and peak performance
      guarantees. Certain functionality of Stream may be available only in
      dedicated pools.
  """

  class EventarcTransformationTypeValueValuesEnum(_messages.Enum):
    r"""Optional.

    Values:
      EVENTARC_TRANSFORMATION_CONFIG_UNSPECIFIED: The transformation type is
        unknown (e.g. the query parameter value is invalid). This type is only
        relevant for data plane metrics and should not be used by the CLH.
      EVENTARC_NOT_AN_EVENT: The request does not correspond to an event, so
        it should not be transformed. This type is only relevant for data
        plane metrics and should not be used by the CLH.
      EVENTARC_CE_PUBSUB_BINDING: The transformation in which a CloudEvent is
        extracted from the Pub/Sub message (i.e. the Pub/Sub Protocol Binding,
        see https://github.com/google/knative-gcp/blob/main/docs/spec/pubsub-
        protocol-binding.md). This transformation is very generic and should
        be used for any trigger where EventFlow creates the Pub/Sub messages.
        In practice, this means Audit Log events and events from Ingress
        Platform.
      EVENTARC_CUSTOM_PUBSUB: The transformation in which an arbitrary Pub/Sub
        message is converted into a Pub/Sub event, as specified in go/cloud-
        events-on-google-devx-design.
      EVENTARC_GCS_NOTIFICATION: The transformation in which a Cloud Storage
        Pub/Sub Notification (http://cloud/storage/docs/pubsub-notifications)
        is converted into a CloudEvent, as specified in go/gcs-event-
        conversion. This transformation is specific to the Cloud Storage stop-
        gap integration (go/eventarc-gcs-stopgap-detailed-design).
    """
    EVENTARC_TRANSFORMATION_CONFIG_UNSPECIFIED = 0
    EVENTARC_NOT_AN_EVENT = 1
    EVENTARC_CE_PUBSUB_BINDING = 2
    EVENTARC_CUSTOM_PUBSUB = 3
    EVENTARC_GCS_NOTIFICATION = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. User-defined annotations. See
    https://google.aip.dev/128#annotations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  displayName = _messages.StringField(3)
  etag = _messages.StringField(4)
  eventarcTransformationType = _messages.EnumField('EventarcTransformationTypeValueValuesEnum', 5)
  inputPayloadFormat = _messages.MessageField('MessagePayloadFormat', 6)
  labels = _messages.MessageField('LabelsValue', 7)
  loggingConfig = _messages.MessageField('LoggingConfig', 8)
  mediations = _messages.MessageField('Mediation', 9, repeated=True)
  metricsConfig = _messages.MessageField('MetricsConfig', 10)
  name = _messages.StringField(11)
  replyBus = _messages.StringField(12)
  retryPolicy = _messages.MessageField('InlineRetryPolicy', 13)
  source = _messages.MessageField('Source', 14)
  streamAction = _messages.MessageField('StreamAction', 15)
  streamIdentityOverride = _messages.StringField(16)
  uid = _messages.StringField(17)
  updateTime = _messages.StringField(18)
  useSharedPool = _messages.BooleanField(19)


class StreamAction(_messages.Message):
  r"""The specifications for routing messaging traffic and applying associated
  policies.

  Fields:
    destinations: Required. The destination to which traffic should be
      forwarded. Currently, only a single destination is supported.
  """

  destinations = _messages.MessageField('Destination', 1, repeated=True)


class Transformation(_messages.Message):
  r"""Transformation defines the way to transform an incoming message.

  Fields:
    transformationTemplate: Optional. The template to apply to transform
      messages.
  """

  transformationTemplate = _messages.StringField(1)


class TruncatedExponentialBackoff(_messages.Message):
  r"""Configuration for a TruncatedExponentialBackoff retry policy.

  Fields:
    maxBackoffDuration: Optional. The maximum amount of time to wait before
      retrying a job after it fails.
    maxDoublings: Optional. The maximum number of times the retry duration
      will double as part of the exponential backoff procedure. Valid range:
      [0, 60]
    maxRetryCount: Optional. The maximum number of retries to attempt. Not
      negative.
    maxRetryDuration: Optional. The time limit for retrying a failed job,
      measured from time when an execution was first attempted. If
      absl::ZeroDuration(), it means unlimited retry duration.
    minBackoffDuration: Optional. The minimum amount of time to wait before
      retrying a job after it fails. Greater than 0.
  """

  maxBackoffDuration = _messages.StringField(1)
  maxDoublings = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  maxRetryCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  maxRetryDuration = _messages.StringField(4)
  minBackoffDuration = _messages.StringField(5)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
