"""Generated message classes for remotebuildexecution version v1alpha.

Supplies a Remote Execution API service for tools such as bazel.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'remotebuildexecution'


class BuildBazelRemoteExecutionV2Action(_messages.Message):
  r"""An `Action` captures all the information about an execution which is
  required to reproduce it. `Action`s are the core component of the
  [Execution] service. A single `Action` represents a repeatable action that
  can be performed by the execution service. `Action`s can be succinctly
  identified by the digest of their wire format encoding and, once an `Action`
  has been executed, will be cached in the action cache. Future requests can
  then use the cached result rather than needing to run afresh. When a server
  completes execution of an Action, it MAY choose to cache the result in the
  ActionCache unless `do_not_cache` is `true`. Clients SHOULD expect the
  server to do so. By default, future calls to Execute the same `Action` will
  also serve their results from the cache. Clients must take care to
  understand the caching behaviour. Ideally, all `Action`s will be
  reproducible so that serving a result from cache is always desirable and
  correct.

  Fields:
    commandDigest: The digest of the Command to run, which MUST be present in
      the ContentAddressableStorage.
    doNotCache: If true, then the `Action`'s result cannot be cached, and in-
      flight requests for the same `Action` may not be merged.
    inputRootDigest: The digest of the root Directory for the input files. The
      files in the directory tree are available in the correct location on the
      build machine before the command is executed. The root directory, as
      well as every subdirectory and content blob referred to, MUST be in the
      ContentAddressableStorage.
    platform: The optional platform requirements for the execution
      environment. The server MAY choose to execute the action on any worker
      satisfying the requirements, so the client SHOULD ensure that running
      the action on any such worker will have the same result. A detailed
      lexicon for this can be found in the accompanying platform.md. New in
      version 2.2: clients SHOULD set these platform properties as well as
      those in the Command. Servers SHOULD prefer those set here.
    salt: An optional additional salt value used to place this `Action` into a
      separate cache namespace from other instances having the same field
      contents. This salt typically comes from operational configuration
      specific to sources such as repo and service configuration, and allows
      disowning an entire set of ActionResults that might have been poisoned
      by buggy software or tool failures.
    timeout: A timeout after which the execution should be killed. If the
      timeout is absent, then the client is specifying that the execution
      should continue as long as the server will let it. The server SHOULD
      impose a timeout if the client does not specify one, however, if the
      client does specify a timeout that is longer than the server's maximum
      timeout, the server MUST reject the request. The timeout is only
      intended to cover the "execution" of the specified action and not time
      in queue nor any overheads before or after execution such as marshalling
      inputs/outputs. The server SHOULD avoid including time spent the client
      doesn't have control over, and MAY extend or reduce the timeout to
      account for delays or speedups that occur during execution itself (e.g.,
      lazily loading data from the Content Addressable Storage, live migration
      of virtual machines, emulation overhead). The timeout is a part of the
      Action message, and therefore two `Actions` with different timeouts are
      different, even if they are otherwise identical. This is because, if
      they were not, running an `Action` with a lower timeout than is required
      might result in a cache hit from an execution run with a longer timeout,
      hiding the fact that the timeout is too short. By encoding it directly
      in the `Action`, a lower timeout will result in a cache miss and the
      execution timeout will fail immediately, rather than whenever the cache
      entry gets evicted.
  """

  commandDigest = _messages.MessageField('BuildBazelRemoteExecutionV2Digest', 1)
  doNotCache = _messages.BooleanField(2)
  inputRootDigest = _messages.MessageField('BuildBazelRemoteExecutionV2Digest', 3)
  platform = _messages.MessageField('BuildBazelRemoteExecutionV2Platform', 4)
  salt = _messages.BytesField(5)
  timeout = _messages.StringField(6)


class BuildBazelRemoteExecutionV2ActionResult(_messages.Message):
  r"""An ActionResult represents the result of an Action being run. It is
  advised that at least one field (for example
  `ActionResult.execution_metadata.Worker`) have a non-default value, to
  ensure that the serialized value is non-empty, which can then be used as a
  basic data sanity check.

  Fields:
    executionMetadata: The details of the execution that originally produced
      this result.
    exitCode: The exit code of the command.
    outputDirectories: The output directories of the action. For each output
      directory requested in the `output_directories` or `output_paths` field
      of the Action, if the corresponding directory existed after the action
      completed, a single entry will be present in the output list, which will
      contain the digest of a Tree message containing the directory tree, and
      the path equal exactly to the corresponding Action output_directories
      member. As an example, suppose the Action had an output directory
      `a/b/dir` and the execution produced the following contents in
      `a/b/dir`: a file named `bar` and a directory named `foo` with an
      executable file named `baz`. Then, output_directory will contain (hashes
      shortened for readability): ```json // OutputDirectory proto: { path:
      "a/b/dir" tree_digest: { hash: "4a73bc9d03...", size: 55 } } // Tree
      proto with hash "4a73bc9d03..." and size 55: { root: { files: [ { name:
      "bar", digest: { hash: "4a73bc9d03...", size: 65534 } } ], directories:
      [ { name: "foo", digest: { hash: "4cf2eda940...", size: 43 } } ] }
      children : { // (Directory proto with hash "4cf2eda940..." and size 43)
      files: [ { name: "baz", digest: { hash: "b2c941073e...", size: 1294, },
      is_executable: true } ] } } ``` If an output of the same name as listed
      in `output_files` of the Command was found in `output_directories`, but
      was not a directory, the server will return a FAILED_PRECONDITION.
    outputDirectorySymlinks: The output directories of the action that are
      symbolic links to other directories. Those may be links to other output
      directories, or input directories, or even absolute paths outside of the
      working directory, if the server supports
      SymlinkAbsolutePathStrategy.ALLOWED. For each output directory requested
      in the `output_directories` field of the Action, if the directory
      existed after the action completed, a single entry will be present
      either in this field, or in the `output_directories` field, if the
      directory was not a symbolic link. If an output of the same name was
      found, but was a symbolic link to a file instead of a directory, the
      server will return a FAILED_PRECONDITION. If the action does not produce
      the requested output, then that output will be omitted from the list.
      The server is free to arrange the output list as desired; clients MUST
      NOT assume that the output list is sorted. DEPRECATED as of v2.1.
      Servers that wish to be compatible with v2.0 API should still populate
      this field in addition to `output_symlinks`.
    outputFileSymlinks: The output files of the action that are symbolic links
      to other files. Those may be links to other output files, or input
      files, or even absolute paths outside of the working directory, if the
      server supports SymlinkAbsolutePathStrategy.ALLOWED. For each output
      file requested in the `output_files` or `output_paths` field of the
      Action, if the corresponding file existed after the action completed, a
      single entry will be present either in this field, or in the
      `output_files` field, if the file was not a symbolic link. If an output
      symbolic link of the same name as listed in `output_files` of the
      Command was found, but its target type was not a regular file, the
      server will return a FAILED_PRECONDITION. If the action does not produce
      the requested output, then that output will be omitted from the list.
      The server is free to arrange the output list as desired; clients MUST
      NOT assume that the output list is sorted. DEPRECATED as of v2.1.
      Servers that wish to be compatible with v2.0 API should still populate
      this field in addition to `output_symlinks`.
    outputFiles: The output files of the action. For each output file
      requested in the `output_files` or `output_paths` field of the Action,
      if the corresponding file existed after the action completed, a single
      entry will be present either in this field, or the
      `output_file_symlinks` field if the file was a symbolic link to another
      file (`output_symlinks` field after v2.1). If an output listed in
      `output_files` was found, but was a directory rather than a regular
      file, the server will return a FAILED_PRECONDITION. If the action does
      not produce the requested output, then that output will be omitted from
      the list. The server is free to arrange the output list as desired;
      clients MUST NOT assume that the output list is sorted.
    outputSymlinks: New in v2.1: this field will only be populated if the
      command `output_paths` field was used, and not the pre v2.1
      `output_files` or `output_directories` fields. The output paths of the
      action that are symbolic links to other paths. Those may be links to
      other outputs, or inputs, or even absolute paths outside of the working
      directory, if the server supports SymlinkAbsolutePathStrategy.ALLOWED. A
      single entry for each output requested in `output_paths` field of the
      Action, if the corresponding path existed after the action completed and
      was a symbolic link. If the action does not produce a requested output,
      then that output will be omitted from the list. The server is free to
      arrange the output list as desired; clients MUST NOT assume that the
      output list is sorted.
    stderrDigest: The digest for a blob containing the standard error of the
      action, which can be retrieved from the ContentAddressableStorage.
    stderrRaw: The standard error buffer of the action. The server SHOULD NOT
      inline stderr unless requested by the client in the
      GetActionResultRequest message. The server MAY omit inlining, even if
      requested, and MUST do so if inlining would cause the response to exceed
      message size limits. Clients SHOULD NOT populate this field when
      uploading to the cache.
    stdoutDigest: The digest for a blob containing the standard output of the
      action, which can be retrieved from the ContentAddressableStorage.
    stdoutRaw: The standard output buffer of the action. The server SHOULD NOT
      inline stdout unless requested by the client in the
      GetActionResultRequest message. The server MAY omit inlining, even if
      requested, and MUST do so if inlining would cause the response to exceed
      message size limits. Clients SHOULD NOT populate this field when
      uploading to the cache.
  """

  executionMetadata = _messages.MessageField('BuildBazelRemoteExecutionV2ExecutedActionMetadata', 1)
  exitCode = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  outputDirectories = _messages.MessageField('BuildBazelRemoteExecutionV2OutputDirectory', 3, repeated=True)
  outputDirectorySymlinks = _messages.MessageField('BuildBazelRemoteExecutionV2OutputSymlink', 4, repeated=True)
  outputFileSymlinks = _messages.MessageField('BuildBazelRemoteExecutionV2OutputSymlink', 5, repeated=True)
  outputFiles = _messages.MessageField('BuildBazelRemoteExecutionV2OutputFile', 6, repeated=True)
  outputSymlinks = _messages.MessageField('BuildBazelRemoteExecutionV2OutputSymlink', 7, repeated=True)
  stderrDigest = _messages.MessageField('BuildBazelRemoteExecutionV2Digest', 8)
  stderrRaw = _messages.BytesField(9)
  stdoutDigest = _messages.MessageField('BuildBazelRemoteExecutionV2Digest', 10)
  stdoutRaw = _messages.BytesField(11)


class BuildBazelRemoteExecutionV2Command(_messages.Message):
  r"""A `Command` is the actual command executed by a worker running an Action
  and specifications of its environment. Except as otherwise required, the
  environment (such as which system libraries or binaries are available, and
  what filesystems are mounted where) is defined by and specific to the
  implementation of the remote execution API.

  Enums:
    OutputDirectoryFormatValueValuesEnum: The format that the worker should
      use to store the contents of output directories. In case this field is
      set to a value that is not supported by the worker, the worker SHOULD
      interpret this field as TREE_ONLY. The worker MAY store output
      directories in formats that are a superset of what was requested (e.g.,
      interpreting DIRECTORY_ONLY as TREE_AND_DIRECTORY).

  Fields:
    arguments: The arguments to the command. The first argument specifies the
      command to run, which may be either an absolute path, a path relative to
      the working directory, or an unqualified path (without path separators)
      which will be resolved using the operating system's equivalent of the
      PATH environment variable. Path separators native to the operating
      system running on the worker SHOULD be used. If the
      `environment_variables` list contains an entry for the PATH environment
      variable, it SHOULD be respected. If not, the resolution process is
      implementation-defined. Changed in v2.3. v2.2 and older require that no
      PATH lookups are performed, and that relative paths are resolved
      relative to the input root. This behavior can, however, not be relied
      upon, as most implementations already followed the rules described
      above.
    environmentVariables: The environment variables to set when running the
      program. The worker may provide its own default environment variables;
      these defaults can be overridden using this field. Additional variables
      can also be specified. In order to ensure that equivalent Commands
      always hash to the same value, the environment variables MUST be
      lexicographically sorted by name. Sorting of strings is done by code
      point, equivalently, by the UTF-8 bytes.
    outputDirectories: A list of the output directories that the client
      expects to retrieve from the action. Only the listed directories will be
      returned (an entire directory structure will be returned as a Tree
      message digest, see OutputDirectory), as well as files listed in
      `output_files`. Other files or directories that may be created during
      command execution are discarded. The paths are relative to the working
      directory of the action execution. The paths are specified using a
      single forward slash (`/`) as a path separator, even if the execution
      platform natively uses a different separator. The path MUST NOT include
      a trailing slash, nor a leading slash, being a relative path. The
      special value of empty string is allowed, although not recommended, and
      can be used to capture the entire working directory tree, including
      inputs. In order to ensure consistent hashing of the same Action, the
      output paths MUST be sorted lexicographically by code point (or,
      equivalently, by UTF-8 bytes). An output directory cannot be duplicated
      or have the same path as any of the listed output files. An output
      directory is allowed to be a parent of another output directory.
      Directories leading up to the output directories (but not the output
      directories themselves) are created by the worker prior to execution,
      even if they are not explicitly part of the input root. DEPRECATED since
      2.1: Use `output_paths` instead.
    outputDirectoryFormat: The format that the worker should use to store the
      contents of output directories. In case this field is set to a value
      that is not supported by the worker, the worker SHOULD interpret this
      field as TREE_ONLY. The worker MAY store output directories in formats
      that are a superset of what was requested (e.g., interpreting
      DIRECTORY_ONLY as TREE_AND_DIRECTORY).
    outputFiles: A list of the output files that the client expects to
      retrieve from the action. Only the listed files, as well as directories
      listed in `output_directories`, will be returned to the client as
      output. Other files or directories that may be created during command
      execution are discarded. The paths are relative to the working directory
      of the action execution. The paths are specified using a single forward
      slash (`/`) as a path separator, even if the execution platform natively
      uses a different separator. The path MUST NOT include a trailing slash,
      nor a leading slash, being a relative path. In order to ensure
      consistent hashing of the same Action, the output paths MUST be sorted
      lexicographically by code point (or, equivalently, by UTF-8 bytes). An
      output file cannot be duplicated, be a parent of another output file, or
      have the same path as any of the listed output directories. Directories
      leading up to the output files are created by the worker prior to
      execution, even if they are not explicitly part of the input root.
      DEPRECATED since v2.1: Use `output_paths` instead.
    outputNodeProperties: A list of keys for node properties the client
      expects to retrieve for output files and directories. Keys are either
      names of string-based NodeProperty or names of fields in NodeProperties.
      In order to ensure that equivalent `Action`s always hash to the same
      value, the node properties MUST be lexicographically sorted by name.
      Sorting of strings is done by code point, equivalently, by the UTF-8
      bytes. The interpretation of string-based properties is server-
      dependent. If a property is not recognized by the server, the server
      will return an `INVALID_ARGUMENT`.
    outputPaths: A list of the output paths that the client expects to
      retrieve from the action. Only the listed paths will be returned to the
      client as output. The type of the output (file or directory) is not
      specified, and will be determined by the server after action execution.
      If the resulting path is a file, it will be returned in an OutputFile
      typed field. If the path is a directory, the entire directory structure
      will be returned as a Tree message digest, see OutputDirectory Other
      files or directories that may be created during command execution are
      discarded. The paths are relative to the working directory of the action
      execution. The paths are specified using a single forward slash (`/`) as
      a path separator, even if the execution platform natively uses a
      different separator. The path MUST NOT include a trailing slash, nor a
      leading slash, being a relative path. In order to ensure consistent
      hashing of the same Action, the output paths MUST be deduplicated and
      sorted lexicographically by code point (or, equivalently, by UTF-8
      bytes). Directories leading up to the output paths are created by the
      worker prior to execution, even if they are not explicitly part of the
      input root. New in v2.1: this field supersedes the DEPRECATED
      `output_files` and `output_directories` fields. If `output_paths` is
      used, `output_files` and `output_directories` will be ignored!
    platform: The platform requirements for the execution environment. The
      server MAY choose to execute the action on any worker satisfying the
      requirements, so the client SHOULD ensure that running the action on any
      such worker will have the same result. A detailed lexicon for this can
      be found in the accompanying platform.md. DEPRECATED as of v2.2:
      platform properties are now specified directly in the action. See
      documentation note in the Action for migration.
    workingDirectory: The working directory, relative to the input root, for
      the command to run in. It must be a directory which exists in the input
      tree. If it is left empty, then the action is run in the input root.
  """

  class OutputDirectoryFormatValueValuesEnum(_messages.Enum):
    r"""The format that the worker should use to store the contents of output
    directories. In case this field is set to a value that is not supported by
    the worker, the worker SHOULD interpret this field as TREE_ONLY. The
    worker MAY store output directories in formats that are a superset of what
    was requested (e.g., interpreting DIRECTORY_ONLY as TREE_AND_DIRECTORY).

    Values:
      TREE_ONLY: The client is only interested in receiving output directories
        in the form of a single Tree object, using the `tree_digest` field.
      DIRECTORY_ONLY: The client is only interested in receiving output
        directories in the form of a hierarchy of separately stored Directory
        objects, using the `root_directory_digest` field.
      TREE_AND_DIRECTORY: The client is interested in receiving output
        directories both in the form of a single Tree object and a hierarchy
        of separately stored Directory objects, using both the `tree_digest`
        and `root_directory_digest` fields.
    """
    TREE_ONLY = 0
    DIRECTORY_ONLY = 1
    TREE_AND_DIRECTORY = 2

  arguments = _messages.StringField(1, repeated=True)
  environmentVariables = _messages.MessageField('BuildBazelRemoteExecutionV2CommandEnvironmentVariable', 2, repeated=True)
  outputDirectories = _messages.StringField(3, repeated=True)
  outputDirectoryFormat = _messages.EnumField('OutputDirectoryFormatValueValuesEnum', 4)
  outputFiles = _messages.StringField(5, repeated=True)
  outputNodeProperties = _messages.StringField(6, repeated=True)
  outputPaths = _messages.StringField(7, repeated=True)
  platform = _messages.MessageField('BuildBazelRemoteExecutionV2Platform', 8)
  workingDirectory = _messages.StringField(9)


class BuildBazelRemoteExecutionV2CommandEnvironmentVariable(_messages.Message):
  r"""An `EnvironmentVariable` is one variable to set in the running program's
  environment.

  Fields:
    name: The variable name.
    value: The variable value.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class BuildBazelRemoteExecutionV2Digest(_messages.Message):
  r"""A content digest. A digest for a given blob consists of the size of the
  blob and its hash. The hash algorithm to use is defined by the server. The
  size is considered to be an integral part of the digest and cannot be
  separated. That is, even if the `hash` field is correctly specified but
  `size_bytes` is not, the server MUST reject the request. The reason for
  including the size in the digest is as follows: in a great many cases, the
  server needs to know the size of the blob it is about to work with prior to
  starting an operation with it, such as flattening Merkle tree structures or
  streaming it to a worker. Technically, the server could implement a separate
  metadata store, but this results in a significantly more complicated
  implementation as opposed to having the client specify the size up-front (or
  storing the size along with the digest in every message where digests are
  embedded). This does mean that the API leaks some implementation details of
  (what we consider to be) a reasonable server implementation, but we consider
  this to be a worthwhile tradeoff. When a `Digest` is used to refer to a
  proto message, it always refers to the message in binary encoded form. To
  ensure consistent hashing, clients and servers MUST ensure that they
  serialize messages according to the following rules, even if there are
  alternate valid encodings for the same message: * Fields are serialized in
  tag order. * There are no unknown fields. * There are no duplicate fields. *
  Fields are serialized according to the default semantics for their type.
  Most protocol buffer implementations will always follow these rules when
  serializing, but care should be taken to avoid shortcuts. For instance,
  concatenating two messages to merge them may produce duplicate fields.

  Fields:
    hash: The hash, represented as a lowercase hexadecimal string, padded with
      leading zeroes up to the hash function length.
    sizeBytes: The size of the blob, in bytes.
  """

  hash = _messages.StringField(1)
  sizeBytes = _messages.IntegerField(2)


class BuildBazelRemoteExecutionV2Directory(_messages.Message):
  r"""A `Directory` represents a directory node in a file tree, containing
  zero or more children FileNodes, DirectoryNodes and SymlinkNodes. Each
  `Node` contains its name in the directory, either the digest of its content
  (either a file blob or a `Directory` proto) or a symlink target, as well as
  possibly some metadata about the file or directory. In order to ensure that
  two equivalent directory trees hash to the same value, the following
  restrictions MUST be obeyed when constructing a a `Directory`: * Every child
  in the directory must have a path of exactly one segment. Multiple levels of
  directory hierarchy may not be collapsed. * Each child in the directory must
  have a unique path segment (file name). Note that while the API itself is
  case-sensitive, the environment where the Action is executed may or may not
  be case-sensitive. That is, it is legal to call the API with a Directory
  that has both "Foo" and "foo" as children, but the Action may be rejected by
  the remote system upon execution. * The files, directories and symlinks in
  the directory must each be sorted in lexicographical order by path. The path
  strings must be sorted by code point, equivalently, by UTF-8 bytes. * The
  NodeProperties of files, directories, and symlinks must be sorted in
  lexicographical order by property name. A `Directory` that obeys the
  restrictions is said to be in canonical form. As an example, the following
  could be used for a file named `bar` and a directory named `foo` with an
  executable file named `baz` (hashes shortened for readability): ```json //
  (Directory proto) { files: [ { name: "bar", digest: { hash: "4a73bc9d03...",
  size: 65534 }, node_properties: [ { "name": "MTime", "value":
  "2017-01-15T01:30:15.01Z" } ] } ], directories: [ { name: "foo", digest: {
  hash: "4cf2eda940...", size: 43 } } ] } // (Directory proto with hash
  "4cf2eda940..." and size 43) { files: [ { name: "baz", digest: { hash:
  "b2c941073e...", size: 1294, }, is_executable: true } ] } ```

  Fields:
    directories: The subdirectories in the directory.
    files: The files in the directory.
    nodeProperties: A BuildBazelRemoteExecutionV2NodeProperties attribute.
    symlinks: The symlinks in the directory.
  """

  directories = _messages.MessageField('BuildBazelRemoteExecutionV2DirectoryNode', 1, repeated=True)
  files = _messages.MessageField('BuildBazelRemoteExecutionV2FileNode', 2, repeated=True)
  nodeProperties = _messages.MessageField('BuildBazelRemoteExecutionV2NodeProperties', 3)
  symlinks = _messages.MessageField('BuildBazelRemoteExecutionV2SymlinkNode', 4, repeated=True)


class BuildBazelRemoteExecutionV2DirectoryNode(_messages.Message):
  r"""A `DirectoryNode` represents a child of a Directory which is itself a
  `Directory` and its associated metadata.

  Fields:
    digest: The digest of the Directory object represented. See Digest for
      information about how to take the digest of a proto message.
    name: The name of the directory.
  """

  digest = _messages.MessageField('BuildBazelRemoteExecutionV2Digest', 1)
  name = _messages.StringField(2)


class BuildBazelRemoteExecutionV2ExecuteOperationMetadata(_messages.Message):
  r"""Metadata about an ongoing execution, which will be contained in the
  metadata field of the Operation.

  Enums:
    StageValueValuesEnum: The current stage of execution.

  Fields:
    actionDigest: The digest of the Action being executed.
    partialExecutionMetadata: The client can read this field to view details
      about the ongoing execution.
    stage: The current stage of execution.
    stderrStreamName: If set, the client can use this resource name with
      ByteStream.Read to stream the standard error from the endpoint hosting
      streamed responses.
    stdoutStreamName: If set, the client can use this resource name with
      ByteStream.Read to stream the standard output from the endpoint hosting
      streamed responses.
  """

  class StageValueValuesEnum(_messages.Enum):
    r"""The current stage of execution.

    Values:
      UNKNOWN: Invalid value.
      CACHE_CHECK: Checking the result against the cache.
      QUEUED: Currently idle, awaiting a free machine to execute.
      EXECUTING: Currently being executed by a worker.
      COMPLETED: Finished execution.
    """
    UNKNOWN = 0
    CACHE_CHECK = 1
    QUEUED = 2
    EXECUTING = 3
    COMPLETED = 4

  actionDigest = _messages.MessageField('BuildBazelRemoteExecutionV2Digest', 1)
  partialExecutionMetadata = _messages.MessageField('BuildBazelRemoteExecutionV2ExecutedActionMetadata', 2)
  stage = _messages.EnumField('StageValueValuesEnum', 3)
  stderrStreamName = _messages.StringField(4)
  stdoutStreamName = _messages.StringField(5)


class BuildBazelRemoteExecutionV2ExecuteResponse(_messages.Message):
  r"""The response message for Execution.Execute, which will be contained in
  the response field of the Operation.

  Messages:
    ServerLogsValue: An optional list of additional log outputs the server
      wishes to provide. A server can use this to return execution-specific
      logs however it wishes. This is intended primarily to make it easier for
      users to debug issues that may be outside of the actual job execution,
      such as by identifying the worker executing the action or by providing
      logs from the worker's setup phase. The keys SHOULD be human readable so
      that a client can display them to a user.

  Fields:
    cachedResult: True if the result was served from cache, false if it was
      executed.
    message: Freeform informational message with details on the execution of
      the action that may be displayed to the user upon failure or when
      requested explicitly.
    result: The result of the action.
    serverLogs: An optional list of additional log outputs the server wishes
      to provide. A server can use this to return execution-specific logs
      however it wishes. This is intended primarily to make it easier for
      users to debug issues that may be outside of the actual job execution,
      such as by identifying the worker executing the action or by providing
      logs from the worker's setup phase. The keys SHOULD be human readable so
      that a client can display them to a user.
    status: If the status has a code other than `OK`, it indicates that the
      action did not finish execution. For example, if the operation times out
      during execution, the status will have a `DEADLINE_EXCEEDED` code.
      Servers MUST use this field for errors in execution, rather than the
      error field on the `Operation` object. If the status code is other than
      `OK`, then the result MUST NOT be cached. For an error status, the
      `result` field is optional; the server may populate the output-,
      stdout-, and stderr-related fields if it has any information available,
      such as the stdout and stderr of a timed-out action.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ServerLogsValue(_messages.Message):
    r"""An optional list of additional log outputs the server wishes to
    provide. A server can use this to return execution-specific logs however
    it wishes. This is intended primarily to make it easier for users to debug
    issues that may be outside of the actual job execution, such as by
    identifying the worker executing the action or by providing logs from the
    worker's setup phase. The keys SHOULD be human readable so that a client
    can display them to a user.

    Messages:
      AdditionalProperty: An additional property for a ServerLogsValue object.

    Fields:
      additionalProperties: Additional properties of type ServerLogsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ServerLogsValue object.

      Fields:
        key: Name of the additional property.
        value: A BuildBazelRemoteExecutionV2LogFile attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('BuildBazelRemoteExecutionV2LogFile', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  cachedResult = _messages.BooleanField(1)
  message = _messages.StringField(2)
  result = _messages.MessageField('BuildBazelRemoteExecutionV2ActionResult', 3)
  serverLogs = _messages.MessageField('ServerLogsValue', 4)
  status = _messages.MessageField('GoogleRpcStatus', 5)


class BuildBazelRemoteExecutionV2ExecutedActionMetadata(_messages.Message):
  r"""ExecutedActionMetadata contains details about a completed execution.

  Messages:
    AuxiliaryMetadataValueListEntry: A AuxiliaryMetadataValueListEntry object.

  Fields:
    auxiliaryMetadata: Details that are specific to the kind of worker used.
      For example, on POSIX-like systems this could contain a message with
      getrusage(2) statistics.
    executionCompletedTimestamp: When the worker completed executing the
      action command.
    executionStartTimestamp: When the worker started executing the action
      command.
    inputFetchCompletedTimestamp: When the worker finished fetching action
      inputs.
    inputFetchStartTimestamp: When the worker started fetching action inputs.
    outputUploadCompletedTimestamp: When the worker finished uploading action
      outputs.
    outputUploadStartTimestamp: When the worker started uploading action
      outputs.
    queuedTimestamp: When was the action added to the queue.
    virtualExecutionDuration: New in v2.3: the amount of time the worker spent
      executing the action command, potentially computed using a worker-
      specific virtual clock. The virtual execution duration is only intended
      to cover the "execution" of the specified action and not time in queue
      nor any overheads before or after execution such as marshalling
      inputs/outputs. The server SHOULD avoid including time spent the client
      doesn't have control over, and MAY extend or reduce the execution
      duration to account for delays or speedups that occur during execution
      itself (e.g., lazily loading data from the Content Addressable Storage,
      live migration of virtual machines, emulation overhead). The method of
      timekeeping used to compute the virtual execution duration MUST be
      consistent with what is used to enforce the Action's `timeout`. There is
      no relationship between the virtual execution duration and the values of
      `execution_start_timestamp` and `execution_completed_timestamp`.
    worker: The name of the worker which ran the execution.
    workerCompletedTimestamp: When the worker completed the action, including
      all stages.
    workerStartTimestamp: When the worker received the action.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AuxiliaryMetadataValueListEntry(_messages.Message):
    r"""A AuxiliaryMetadataValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a
        AuxiliaryMetadataValueListEntry object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AuxiliaryMetadataValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  auxiliaryMetadata = _messages.MessageField('AuxiliaryMetadataValueListEntry', 1, repeated=True)
  executionCompletedTimestamp = _messages.StringField(2)
  executionStartTimestamp = _messages.StringField(3)
  inputFetchCompletedTimestamp = _messages.StringField(4)
  inputFetchStartTimestamp = _messages.StringField(5)
  outputUploadCompletedTimestamp = _messages.StringField(6)
  outputUploadStartTimestamp = _messages.StringField(7)
  queuedTimestamp = _messages.StringField(8)
  virtualExecutionDuration = _messages.StringField(9)
  worker = _messages.StringField(10)
  workerCompletedTimestamp = _messages.StringField(11)
  workerStartTimestamp = _messages.StringField(12)


class BuildBazelRemoteExecutionV2FileNode(_messages.Message):
  r"""A `FileNode` represents a single file and associated metadata.

  Fields:
    digest: The digest of the file's content.
    isExecutable: True if file is executable, false otherwise.
    name: The name of the file.
    nodeProperties: A BuildBazelRemoteExecutionV2NodeProperties attribute.
  """

  digest = _messages.MessageField('BuildBazelRemoteExecutionV2Digest', 1)
  isExecutable = _messages.BooleanField(2)
  name = _messages.StringField(3)
  nodeProperties = _messages.MessageField('BuildBazelRemoteExecutionV2NodeProperties', 4)


class BuildBazelRemoteExecutionV2LogFile(_messages.Message):
  r"""A `LogFile` is a log stored in the CAS.

  Fields:
    digest: The digest of the log contents.
    humanReadable: This is a hint as to the purpose of the log, and is set to
      true if the log is human-readable text that can be usefully displayed to
      a user, and false otherwise. For instance, if a command-line client
      wishes to print the server logs to the terminal for a failed action,
      this allows it to avoid displaying a binary file.
  """

  digest = _messages.MessageField('BuildBazelRemoteExecutionV2Digest', 1)
  humanReadable = _messages.BooleanField(2)


class BuildBazelRemoteExecutionV2NodeProperties(_messages.Message):
  r"""Node properties for FileNodes, DirectoryNodes, and SymlinkNodes. The
  server is responsible for specifying the properties that it accepts.

  Fields:
    mtime: The file's last modification timestamp.
    properties: A list of string-based NodeProperties.
    unixMode: The UNIX file mode, e.g., 0755.
  """

  mtime = _messages.StringField(1)
  properties = _messages.MessageField('BuildBazelRemoteExecutionV2NodeProperty', 2, repeated=True)
  unixMode = _messages.IntegerField(3, variant=_messages.Variant.UINT32)


class BuildBazelRemoteExecutionV2NodeProperty(_messages.Message):
  r"""A single property for FileNodes, DirectoryNodes, and SymlinkNodes. The
  server is responsible for specifying the property `name`s that it accepts.
  If permitted by the server, the same `name` may occur multiple times.

  Fields:
    name: The property name.
    value: The property value.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class BuildBazelRemoteExecutionV2OutputDirectory(_messages.Message):
  r"""An `OutputDirectory` is the output in an `ActionResult` corresponding to
  a directory's full contents rather than a single file.

  Fields:
    isTopologicallySorted: If set, consumers MAY make the following
      assumptions about the directories contained in the the Tree, so that it
      may be instantiated on a local file system by scanning through it
      sequentially: - All directories with the same binary representation are
      stored exactly once. - All directories, apart from the root directory,
      are referenced by at least one parent directory. - Directories are
      stored in topological order, with parents being stored before the child.
      The root directory is thus the first to be stored. Additionally, the
      Tree MUST be encoded as a stream of records, where each record has the
      following format: - A tag byte, having one of the following two values:
      - (1 << 3) | 2 == 0x0a: First record (the root directory). - (2 << 3) |
      2 == 0x12: Any subsequent records (child directories). - The size of the
      directory, encoded as a base 128 varint. - The contents of the
      directory, encoded as a binary serialized Protobuf message. This
      encoding is a subset of the Protobuf wire format of the Tree message. As
      it is only permitted to store data associated with field numbers 1 and
      2, the tag MUST be encoded as a single byte. More details on the
      Protobuf wire format can be found here:
      https://developers.google.com/protocol-buffers/docs/encoding It is
      recommended that implementations using this feature construct Tree
      objects manually using the specification given above, as opposed to
      using a Protobuf library to marshal a full Tree message. As individual
      Directory messages already need to be marshaled to compute their
      digests, constructing the Tree object manually avoids redundant
      marshaling.
    path: The full path of the directory relative to the working directory.
      The path separator is a forward slash `/`. Since this is a relative
      path, it MUST NOT begin with a leading forward slash. The empty string
      value is allowed, and it denotes the entire working directory.
    rootDirectoryDigest: The digest of the encoded Directory proto containing
      the contents the directory's root. If both `tree_digest` and
      `root_directory_digest` are set, this field MUST match the digest of the
      root directory contained in the Tree message.
    treeDigest: The digest of the encoded Tree proto containing the
      directory's contents.
  """

  isTopologicallySorted = _messages.BooleanField(1)
  path = _messages.StringField(2)
  rootDirectoryDigest = _messages.MessageField('BuildBazelRemoteExecutionV2Digest', 3)
  treeDigest = _messages.MessageField('BuildBazelRemoteExecutionV2Digest', 4)


class BuildBazelRemoteExecutionV2OutputFile(_messages.Message):
  r"""An `OutputFile` is similar to a FileNode, but it is used as an output in
  an `ActionResult`. It allows a full file path rather than only a name.

  Fields:
    contents: The contents of the file if inlining was requested. The server
      SHOULD NOT inline file contents unless requested by the client in the
      GetActionResultRequest message. The server MAY omit inlining, even if
      requested, and MUST do so if inlining would cause the response to exceed
      message size limits. Clients SHOULD NOT populate this field when
      uploading to the cache.
    digest: The digest of the file's content.
    isExecutable: True if file is executable, false otherwise.
    nodeProperties: A BuildBazelRemoteExecutionV2NodeProperties attribute.
    path: The full path of the file relative to the working directory,
      including the filename. The path separator is a forward slash `/`. Since
      this is a relative path, it MUST NOT begin with a leading forward slash.
  """

  contents = _messages.BytesField(1)
  digest = _messages.MessageField('BuildBazelRemoteExecutionV2Digest', 2)
  isExecutable = _messages.BooleanField(3)
  nodeProperties = _messages.MessageField('BuildBazelRemoteExecutionV2NodeProperties', 4)
  path = _messages.StringField(5)


class BuildBazelRemoteExecutionV2OutputSymlink(_messages.Message):
  r"""An `OutputSymlink` is similar to a Symlink, but it is used as an output
  in an `ActionResult`. `OutputSymlink` is binary-compatible with
  `SymlinkNode`.

  Fields:
    nodeProperties: A BuildBazelRemoteExecutionV2NodeProperties attribute.
    path: The full path of the symlink relative to the working directory,
      including the filename. The path separator is a forward slash `/`. Since
      this is a relative path, it MUST NOT begin with a leading forward slash.
    target: The target path of the symlink. The path separator is a forward
      slash `/`. The target path can be relative to the parent directory of
      the symlink or it can be an absolute path starting with `/`. Support for
      absolute paths can be checked using the Capabilities API. `..`
      components are allowed anywhere in the target path.
  """

  nodeProperties = _messages.MessageField('BuildBazelRemoteExecutionV2NodeProperties', 1)
  path = _messages.StringField(2)
  target = _messages.StringField(3)


class BuildBazelRemoteExecutionV2Platform(_messages.Message):
  r"""A `Platform` is a set of requirements, such as hardware, operating
  system, or compiler toolchain, for an Action's execution environment. A
  `Platform` is represented as a series of key-value pairs representing the
  properties that are required of the platform.

  Fields:
    properties: The properties that make up this platform. In order to ensure
      that equivalent `Platform`s always hash to the same value, the
      properties MUST be lexicographically sorted by name, and then by value.
      Sorting of strings is done by code point, equivalently, by the UTF-8
      bytes.
  """

  properties = _messages.MessageField('BuildBazelRemoteExecutionV2PlatformProperty', 1, repeated=True)


class BuildBazelRemoteExecutionV2PlatformProperty(_messages.Message):
  r"""A single property for the environment. The server is responsible for
  specifying the property `name`s that it accepts. If an unknown `name` is
  provided in the requirements for an Action, the server SHOULD reject the
  execution request. If permitted by the server, the same `name` may occur
  multiple times. The server is also responsible for specifying the
  interpretation of property `value`s. For instance, a property describing how
  much RAM must be available may be interpreted as allowing a worker with 16GB
  to fulfill a request for 8GB, while a property describing the OS environment
  on which the action must be performed may require an exact match with the
  worker's OS. The server MAY use the `value` of one or more properties to
  determine how it sets up the execution environment, such as by making
  specific system files available to the worker. Both names and values are
  typically case-sensitive. Note that the platform is implicitly part of the
  action digest, so even tiny changes in the names or values (like changing
  case) may result in different action cache entries.

  Fields:
    name: The property name.
    value: The property value.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class BuildBazelRemoteExecutionV2RequestMetadata(_messages.Message):
  r"""An optional Metadata to attach to any RPC request to tell the server
  about an external context of the request. The server may use this for
  logging or other purposes. To use it, the client attaches the header to the
  call using the canonical proto serialization: * name:
  `build.bazel.remote.execution.v2.requestmetadata-bin` * contents: the base64
  encoded binary `RequestMetadata` message. Note: the gRPC library serializes
  binary headers encoded in base64 by default
  (https://github.com/grpc/grpc/blob/master/doc/PROTOCOL-HTTP2.md#requests).
  Therefore, if the gRPC library is used to pass/retrieve this metadata, the
  user may ignore the base64 encoding and assume it is simply serialized as a
  binary message.

  Fields:
    actionId: An identifier that ties multiple requests to the same action.
      For example, multiple requests to the CAS, Action Cache, and Execution
      API are used in order to compile foo.cc.
    actionMnemonic: A brief description of the kind of action, for example,
      CppCompile or GoLink. There is no standard agreed set of values for
      this, and they are expected to vary between different client tools.
    configurationId: An identifier for the configuration in which the target
      was built, e.g. for differentiating building host tools or different
      target platforms. There is no expectation that this value will have any
      particular structure, or equality across invocations, though some client
      tools may offer these guarantees.
    correlatedInvocationsId: An identifier to tie multiple tool invocations
      together. For example, runs of foo_test, bar_test and baz_test on a
      post-submit of a given patch.
    targetId: An identifier for the target which produced this action. No
      guarantees are made around how many actions may relate to a single
      target.
    toolDetails: The details for the tool invoking the requests.
    toolInvocationId: An identifier that ties multiple actions together to a
      final result. For example, multiple actions are required to build and
      run foo_test.
  """

  actionId = _messages.StringField(1)
  actionMnemonic = _messages.StringField(2)
  configurationId = _messages.StringField(3)
  correlatedInvocationsId = _messages.StringField(4)
  targetId = _messages.StringField(5)
  toolDetails = _messages.MessageField('BuildBazelRemoteExecutionV2ToolDetails', 6)
  toolInvocationId = _messages.StringField(7)


class BuildBazelRemoteExecutionV2SymlinkNode(_messages.Message):
  r"""A `SymlinkNode` represents a symbolic link.

  Fields:
    name: The name of the symlink.
    nodeProperties: A BuildBazelRemoteExecutionV2NodeProperties attribute.
    target: The target path of the symlink. The path separator is a forward
      slash `/`. The target path can be relative to the parent directory of
      the symlink or it can be an absolute path starting with `/`. Support for
      absolute paths can be checked using the Capabilities API. `..`
      components are allowed anywhere in the target path as logical
      canonicalization may lead to different behavior in the presence of
      directory symlinks (e.g. `foo/../bar` may not be the same as `bar`). To
      reduce potential cache misses, canonicalization is still recommended
      where this is possible without impacting correctness.
  """

  name = _messages.StringField(1)
  nodeProperties = _messages.MessageField('BuildBazelRemoteExecutionV2NodeProperties', 2)
  target = _messages.StringField(3)


class BuildBazelRemoteExecutionV2ToolDetails(_messages.Message):
  r"""Details for the tool used to call the API.

  Fields:
    toolName: Name of the tool, e.g. bazel.
    toolVersion: Version of the tool used for the request, e.g. 5.0.3.
  """

  toolName = _messages.StringField(1)
  toolVersion = _messages.StringField(2)


class BuildBazelRemoteExecutionV2Tree(_messages.Message):
  r"""A `Tree` contains all the Directory protos in a single directory Merkle
  tree, compressed into one message.

  Fields:
    children: All the child directories: the directories referred to by the
      root and, recursively, all its children. In order to reconstruct the
      directory tree, the client must take the digests of each of the child
      directories and then build up a tree starting from the `root`. Servers
      SHOULD ensure that these are ordered consistently such that two actions
      producing equivalent output directories on the same server
      implementation also produce Tree messages with matching digests.
    root: The root directory in the tree.
  """

  children = _messages.MessageField('BuildBazelRemoteExecutionV2Directory', 1, repeated=True)
  root = _messages.MessageField('BuildBazelRemoteExecutionV2Directory', 2)


class GoogleDevtoolsRemotebuildbotCommandDurations(_messages.Message):
  r"""CommandDuration contains the various duration metrics tracked when a bot
  performs a command.

  Fields:
    casRelease: The time spent to release the CAS blobs used by the task.
    cmWaitForAssignment: The time spent waiting for Container Manager to
      assign an asynchronous container for execution.
    dockerPrep: The time spent preparing the command to be run in a Docker
      container (includes pulling the Docker image, if necessary).
    dockerPrepStartTime: The timestamp when docker preparation begins.
    download: The time spent downloading the input files and constructing the
      working directory.
    downloadStartTime: The timestamp when downloading the input files begins.
    execStartTime: The timestamp when execution begins.
    execution: The time spent executing the command (i.e., doing useful work).
    isoPrepDone: The timestamp when preparation is done and bot starts
      downloading files.
    overall: The time spent completing the command, in total.
    upload: The time spent uploading the output files.
    uploadStartTime: The timestamp when uploading the output files begins.
  """

  casRelease = _messages.StringField(1)
  cmWaitForAssignment = _messages.StringField(2)
  dockerPrep = _messages.StringField(3)
  dockerPrepStartTime = _messages.StringField(4)
  download = _messages.StringField(5)
  downloadStartTime = _messages.StringField(6)
  execStartTime = _messages.StringField(7)
  execution = _messages.StringField(8)
  isoPrepDone = _messages.StringField(9)
  overall = _messages.StringField(10)
  upload = _messages.StringField(11)
  uploadStartTime = _messages.StringField(12)


class GoogleDevtoolsRemotebuildbotCommandEvents(_messages.Message):
  r"""CommandEvents contains counters for the number of warnings and errors
  that occurred during the execution of a command.

  Enums:
    CmUsageValueValuesEnum: Indicates if and how Container Manager is being
      used for task execution.
    InputMountTypeValueValuesEnum: Indicates how the input tree was mounted
      for the action.
    OutputLocationValueValuesEnum: Indicates whether output files and/or
      output directories were found relative to the execution root or to the
      user provided work directory or both or none.

  Fields:
    cmUsage: Indicates if and how Container Manager is being used for task
      execution.
    dockerCacheHit: Indicates whether we are using a cached Docker image
      (true) or had to pull the Docker image (false) for this command.
    dockerImageName: Docker Image name.
    inputCacheMissBytes: The input cache miss rate as a fraction of the total
      size of input files.
    inputCacheMissFiles: The input cache miss rate as a fraction of the number
      of input files.
    inputMountType: Indicates how the input tree was mounted for the action.
    inputTreeStats: Contains stats about the action input metadata tree that
      was fetched to determine the action's inputs.
    numErrors: The number of errors reported.
    numWarnings: The number of warnings reported.
    outputLocation: Indicates whether output files and/or output directories
      were found relative to the execution root or to the user provided work
      directory or both or none.
    usedAsyncContainer: Indicates whether an asynchronous container was used
      for execution.
  """

  class CmUsageValueValuesEnum(_messages.Enum):
    r"""Indicates if and how Container Manager is being used for task
    execution.

    Values:
      CONFIG_NONE: Container Manager is disabled or not running for this
        execution.
      CONFIG_MATCH: Container Manager is enabled and there was a matching
        container available for use during execution.
      CONFIG_MISMATCH: Container Manager is enabled, but there was no matching
        container available for execution.
    """
    CONFIG_NONE = 0
    CONFIG_MATCH = 1
    CONFIG_MISMATCH = 2

  class InputMountTypeValueValuesEnum(_messages.Enum):
    r"""Indicates how the input tree was mounted for the action.

    Values:
      MOUNT_UNSPECIFIED: The mechanism for mounting the input tree is
        unspecified.
      MOUNT_OVERLAY: The input tree was mounted from a read-write overlay
        filesystem.
      MOUNT_WRITABLE: The input tree was mounted as a read-write bind mount.
        The CAS proxy will use file watchers and/or post-action scanning of
        inputs to catch modfiications to the input blobs.
      MOUNT_READ_ONLY: The input tree was mounted as a read-only bind mount,
        with a separate writable directories mounted where outputs are
        expected.
    """
    MOUNT_UNSPECIFIED = 0
    MOUNT_OVERLAY = 1
    MOUNT_WRITABLE = 2
    MOUNT_READ_ONLY = 3

  class OutputLocationValueValuesEnum(_messages.Enum):
    r"""Indicates whether output files and/or output directories were found
    relative to the execution root or to the user provided work directory or
    both or none.

    Values:
      LOCATION_UNDEFINED: Location is set to LOCATION_UNDEFINED for tasks
        where the working directorty is not specified or is identical to the
        execution root directory.
      LOCATION_NONE: No output files or directories were found neither
        relative to the execution root directory nor relative to the working
        directory.
      LOCATION_EXEC_ROOT_RELATIVE: Output files or directories were found
        relative to the execution root directory but not relative to the
        working directory.
      LOCATION_WORKING_DIR_RELATIVE: Output files or directories were found
        relative to the working directory but not relative to the execution
        root directory.
      LOCATION_EXEC_ROOT_AND_WORKING_DIR_RELATIVE: Output files or directories
        were found both relative to the execution root directory and relative
        to the working directory.
      LOCATION_EXEC_ROOT_RELATIVE_OUTPUT_OUTSIDE_WORKING_DIR: Output files or
        directories were found relative to the execution root directory but
        not relative to the working directory. In addition at least one output
        file or directory was found outside of the working directory such that
        a working-directory-relative-path would have needed to start with a
        `..`.
      LOCATION_EXEC_ROOT_AND_WORKING_DIR_RELATIVE_OUTPUT_OUTSIDE_WORKING_DIR:
        Output files or directories were found both relative to the execution
        root directory and relative to the working directory. In addition at
        least one exec-root-relative output file or directory was found
        outside of the working directory such that a working-directory-
        relative-path would have needed to start with a `..`.
    """
    LOCATION_UNDEFINED = 0
    LOCATION_NONE = 1
    LOCATION_EXEC_ROOT_RELATIVE = 2
    LOCATION_WORKING_DIR_RELATIVE = 3
    LOCATION_EXEC_ROOT_AND_WORKING_DIR_RELATIVE = 4
    LOCATION_EXEC_ROOT_RELATIVE_OUTPUT_OUTSIDE_WORKING_DIR = 5
    LOCATION_EXEC_ROOT_AND_WORKING_DIR_RELATIVE_OUTPUT_OUTSIDE_WORKING_DIR = 6

  cmUsage = _messages.EnumField('CmUsageValueValuesEnum', 1)
  dockerCacheHit = _messages.BooleanField(2)
  dockerImageName = _messages.StringField(3)
  inputCacheMissBytes = _messages.FloatField(4, variant=_messages.Variant.FLOAT)
  inputCacheMissFiles = _messages.FloatField(5, variant=_messages.Variant.FLOAT)
  inputMountType = _messages.EnumField('InputMountTypeValueValuesEnum', 6)
  inputTreeStats = _messages.MessageField('GoogleDevtoolsRemotebuildbotInputTreeStats', 7)
  numErrors = _messages.IntegerField(8, variant=_messages.Variant.UINT64)
  numWarnings = _messages.IntegerField(9, variant=_messages.Variant.UINT64)
  outputLocation = _messages.EnumField('OutputLocationValueValuesEnum', 10)
  usedAsyncContainer = _messages.BooleanField(11)


class GoogleDevtoolsRemotebuildbotCommandStatus(_messages.Message):
  r"""The internal status of the command result.

  Enums:
    CodeValueValuesEnum: The status code.

  Fields:
    code: The status code.
    message: The error message.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""The status code.

    Values:
      OK: The command succeeded.
      INVALID_ARGUMENT: The command input was invalid.
      DEADLINE_EXCEEDED: The command had passed its expiry time while it was
        still running.
      NOT_FOUND: The resources requested by the command were not found.
      PERMISSION_DENIED: The command failed due to permission errors.
      INTERNAL: The command failed because of some invariants expected by the
        underlying system have been broken. This usually indicates a bug wit
        the system.
      ABORTED: The command was aborted.
      FAILED_PRECONDITION: The command failed because the system is not in a
        state required for the command, e.g. the command inputs cannot be
        found on the server.
      CLEANUP_ERROR: The bot failed to do the cleanup, e.g. unable to delete
        the command working directory or the command process.
      DOWNLOAD_INPUTS_ERROR: The bot failed to download the inputs.
      UNKNOWN: Unknown error.
      UPLOAD_OUTPUTS_ERROR: The bot failed to upload the outputs.
      UPLOAD_OUTPUTS_BYTES_LIMIT_EXCEEDED: The bot tried to upload files
        having a total size that is too large.
      DOCKER_LOGIN_ERROR: The bot failed to login to docker.
      DOCKER_IMAGE_PULL_ERROR: The bot failed to pull docker image.
      DOCKER_IMAGE_EXIST_ERROR: The bot failed to check docker images.
      DUPLICATE_INPUTS: The inputs contain duplicate files.
      DOCKER_IMAGE_PERMISSION_DENIED: The bot doesn't have the permissions to
        pull docker images.
      DOCKER_IMAGE_NOT_FOUND: The docker image cannot be found.
      WORKING_DIR_NOT_FOUND: Working directory is not found.
      WORKING_DIR_NOT_IN_BASE_DIR: Working directory is not under the base
        directory
      DOCKER_UNAVAILABLE: There are issues with docker service/runtime.
      NO_CUDA_CAPABLE_DEVICE: The command failed with "no cuda-capable device
        is detected" error.
      REMOTE_CAS_DOWNLOAD_ERROR: The bot encountered errors from remote CAS
        when downloading blobs.
      REMOTE_CAS_UPLOAD_ERROR: The bot encountered errors from remote CAS when
        uploading blobs.
      LOCAL_CASPROXY_NOT_RUNNING: The local casproxy is not running.
      DOCKER_CREATE_CONTAINER_ERROR: The bot couldn't start the container.
      DOCKER_INVALID_ULIMIT: The docker ulimit is not valid.
      DOCKER_UNKNOWN_RUNTIME: The docker runtime is unknown.
      DOCKER_UNKNOWN_CAPABILITY: The docker capability is unknown.
      DOCKER_UNKNOWN_ERROR: The command failed with unknown docker errors.
      DOCKER_CREATE_COMPUTE_SYSTEM_ERROR: Docker failed to run containers with
        CreateComputeSystem error.
      DOCKER_PREPARELAYER_ERROR: Docker failed to run containers with
        hcsshim::PrepareLayer error.
      DOCKER_INCOMPATIBLE_OS_ERROR: Docker incompatible operating system
        error.
      DOCKER_CREATE_RUNTIME_FILE_NOT_FOUND: Docker failed to create OCI
        runtime because of file not found.
      DOCKER_CREATE_RUNTIME_PERMISSION_DENIED: Docker failed to create OCI
        runtime because of permission denied.
      DOCKER_CREATE_PROCESS_FILE_NOT_FOUND: Docker failed to create process
        because of file not found.
      DOCKER_CREATE_COMPUTE_SYSTEM_INCORRECT_PARAMETER_ERROR: Docker failed to
        run containers with CreateComputeSystem error that involves an
        incorrect parameter (more specific version of
        DOCKER_CREATE_COMPUTE_SYSTEM_ERROR that is user-caused).
      DOCKER_TOO_MANY_SYMBOLIC_LINK_LEVELS: Docker failed to create an overlay
        mount because of too many levels of symbolic links.
      LOCAL_CONTAINER_MANAGER_NOT_RUNNING: The local Container Manager is not
        running.
      DOCKER_IMAGE_VPCSC_PERMISSION_DENIED: Docker failed because a request
        was denied by the organization's policy.
      WORKING_DIR_NOT_RELATIVE: Working directory is not relative
      DOCKER_MISSING_CONTAINER: Docker cannot find the container specified in
        the command. This error is likely to only occur if an asynchronous
        container is not running when the command is run.
      DOCKER_MISSING_BLOB_IN_IMAGE: Docker cannot pull an image because a blob
        is missing in the repo. May be due to a bad/incomplete image push or
        partial deletion of underlying blob layers.
      DOCKER_INVALID_VOLUME: The docker volume specification is invalid (e.g.
        root).
      DOCKER_CREATE_RUNTIME_CANNOT_MOUNT_TO_PROC: Docker failed to create OCI
        runtime because input root cannot be proc.
      DOCKER_START_RUNTIME_FILE_NOT_FOUND: Docker failed to start OCI runtime
        because of file not found.
      DOCKER_CREATE_INVALID_LAYERCHAIN_JSON: Docker failed to run because the
        layerchain json was invalid (see b/234782336).
      INCOMPATIBLE_CUDA_VERSION: Docker failed to create OCI runtime because
        of incompatible cuda version.
      LOCAL_WORKER_MANAGER_NOT_RUNNING: The local Worker Manager is not
        running.
      DOCKER_START_RUNTIME_FILE_FORMAT_ERROR: Docker failed to start OCI
        runtime because of file format error.
      DOCKER_START_RUNTIME_PERMISSION_DENIED: Docker failed to start OCI
        runtime because of permission denied.
      DOCKER_PERMISSION_DENIED: Docker failed because of permission denied.
      NETWORK_PROXY_UPDATE_POLICY_ERROR: The bot failed to update the network
        proxy policy.
      SERVICE_ACCOUNT_IMPERSONATION_DENIED: Failed to impersonate the service
        account.
      INVALID_FILENAME: Trying to write a file with a filename that is too
        long.
      NETWORK_PROXY_GET_NETWORK_ACTIVITY_LOG_ERROR: The bot failed to get the
        network activity log.
      NETWORK_PROXY_UPLOAD_NETWORK_ACTIVITY_LOG_ERROR: The bot failed to
        upload the network activity log.
    """
    OK = 0
    INVALID_ARGUMENT = 1
    DEADLINE_EXCEEDED = 2
    NOT_FOUND = 3
    PERMISSION_DENIED = 4
    INTERNAL = 5
    ABORTED = 6
    FAILED_PRECONDITION = 7
    CLEANUP_ERROR = 8
    DOWNLOAD_INPUTS_ERROR = 9
    UNKNOWN = 10
    UPLOAD_OUTPUTS_ERROR = 11
    UPLOAD_OUTPUTS_BYTES_LIMIT_EXCEEDED = 12
    DOCKER_LOGIN_ERROR = 13
    DOCKER_IMAGE_PULL_ERROR = 14
    DOCKER_IMAGE_EXIST_ERROR = 15
    DUPLICATE_INPUTS = 16
    DOCKER_IMAGE_PERMISSION_DENIED = 17
    DOCKER_IMAGE_NOT_FOUND = 18
    WORKING_DIR_NOT_FOUND = 19
    WORKING_DIR_NOT_IN_BASE_DIR = 20
    DOCKER_UNAVAILABLE = 21
    NO_CUDA_CAPABLE_DEVICE = 22
    REMOTE_CAS_DOWNLOAD_ERROR = 23
    REMOTE_CAS_UPLOAD_ERROR = 24
    LOCAL_CASPROXY_NOT_RUNNING = 25
    DOCKER_CREATE_CONTAINER_ERROR = 26
    DOCKER_INVALID_ULIMIT = 27
    DOCKER_UNKNOWN_RUNTIME = 28
    DOCKER_UNKNOWN_CAPABILITY = 29
    DOCKER_UNKNOWN_ERROR = 30
    DOCKER_CREATE_COMPUTE_SYSTEM_ERROR = 31
    DOCKER_PREPARELAYER_ERROR = 32
    DOCKER_INCOMPATIBLE_OS_ERROR = 33
    DOCKER_CREATE_RUNTIME_FILE_NOT_FOUND = 34
    DOCKER_CREATE_RUNTIME_PERMISSION_DENIED = 35
    DOCKER_CREATE_PROCESS_FILE_NOT_FOUND = 36
    DOCKER_CREATE_COMPUTE_SYSTEM_INCORRECT_PARAMETER_ERROR = 37
    DOCKER_TOO_MANY_SYMBOLIC_LINK_LEVELS = 38
    LOCAL_CONTAINER_MANAGER_NOT_RUNNING = 39
    DOCKER_IMAGE_VPCSC_PERMISSION_DENIED = 40
    WORKING_DIR_NOT_RELATIVE = 41
    DOCKER_MISSING_CONTAINER = 42
    DOCKER_MISSING_BLOB_IN_IMAGE = 43
    DOCKER_INVALID_VOLUME = 44
    DOCKER_CREATE_RUNTIME_CANNOT_MOUNT_TO_PROC = 45
    DOCKER_START_RUNTIME_FILE_NOT_FOUND = 46
    DOCKER_CREATE_INVALID_LAYERCHAIN_JSON = 47
    INCOMPATIBLE_CUDA_VERSION = 48
    LOCAL_WORKER_MANAGER_NOT_RUNNING = 49
    DOCKER_START_RUNTIME_FILE_FORMAT_ERROR = 50
    DOCKER_START_RUNTIME_PERMISSION_DENIED = 51
    DOCKER_PERMISSION_DENIED = 52
    NETWORK_PROXY_UPDATE_POLICY_ERROR = 53
    SERVICE_ACCOUNT_IMPERSONATION_DENIED = 54
    INVALID_FILENAME = 55
    NETWORK_PROXY_GET_NETWORK_ACTIVITY_LOG_ERROR = 56
    NETWORK_PROXY_UPLOAD_NETWORK_ACTIVITY_LOG_ERROR = 57

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  message = _messages.StringField(2)


class GoogleDevtoolsRemotebuildbotInputTreeStats(_messages.Message):
  r"""A GoogleDevtoolsRemotebuildbotInputTreeStats object.

  Fields:
    maxDepth: Tree depth.
    processingTimeMillis: Time spent retrieving and processing the tree.
    totalFiles: Overall number of files in the tree.
    totalNodes: Overall number of nodes in the tree.
    totalSize: Total size in bytes of all files in the tree.
    totalSymlinks: Overall number of symlinks in the tree.
  """

  maxDepth = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  processingTimeMillis = _messages.IntegerField(2)
  totalFiles = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  totalNodes = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  totalSize = _messages.IntegerField(5)
  totalSymlinks = _messages.IntegerField(6, variant=_messages.Variant.INT32)


class GoogleDevtoolsRemotebuildbotResourceUsage(_messages.Message):
  r"""ResourceUsage is the system resource usage of the host machine.

  Enums:
    BotStateValueValuesEnum:

  Fields:
    botState: A BotStateValueValuesEnum attribute.
    cpuUsedPercent: A number attribute.
    diskUsage: A GoogleDevtoolsRemotebuildbotResourceUsageStat attribute.
    dockerRootDiskUsage: A GoogleDevtoolsRemotebuildbotResourceUsageStat
      attribute.
    memoryUsage: A GoogleDevtoolsRemotebuildbotResourceUsageStat attribute.
    totalDiskIoStats: A GoogleDevtoolsRemotebuildbotResourceUsageIOStats
      attribute.
  """

  class BotStateValueValuesEnum(_messages.Enum):
    r"""BotStateValueValuesEnum enum type.

    Values:
      UNSPECIFIED: <no description>
      IDLE: <no description>
      BUSY: <no description>
    """
    UNSPECIFIED = 0
    IDLE = 1
    BUSY = 2

  botState = _messages.EnumField('BotStateValueValuesEnum', 1)
  cpuUsedPercent = _messages.FloatField(2)
  diskUsage = _messages.MessageField('GoogleDevtoolsRemotebuildbotResourceUsageStat', 3)
  dockerRootDiskUsage = _messages.MessageField('GoogleDevtoolsRemotebuildbotResourceUsageStat', 4)
  memoryUsage = _messages.MessageField('GoogleDevtoolsRemotebuildbotResourceUsageStat', 5)
  totalDiskIoStats = _messages.MessageField('GoogleDevtoolsRemotebuildbotResourceUsageIOStats', 6)


class GoogleDevtoolsRemotebuildbotResourceUsageIOStats(_messages.Message):
  r"""A GoogleDevtoolsRemotebuildbotResourceUsageIOStats object.

  Fields:
    readBytesCount: A string attribute.
    readCount: A string attribute.
    readTimeMs: A string attribute.
    writeBytesCount: A string attribute.
    writeCount: A string attribute.
    writeTimeMs: A string attribute.
  """

  readBytesCount = _messages.IntegerField(1, variant=_messages.Variant.UINT64)
  readCount = _messages.IntegerField(2, variant=_messages.Variant.UINT64)
  readTimeMs = _messages.IntegerField(3, variant=_messages.Variant.UINT64)
  writeBytesCount = _messages.IntegerField(4, variant=_messages.Variant.UINT64)
  writeCount = _messages.IntegerField(5, variant=_messages.Variant.UINT64)
  writeTimeMs = _messages.IntegerField(6, variant=_messages.Variant.UINT64)


class GoogleDevtoolsRemotebuildbotResourceUsageStat(_messages.Message):
  r"""A GoogleDevtoolsRemotebuildbotResourceUsageStat object.

  Fields:
    total: A string attribute.
    used: A string attribute.
  """

  total = _messages.IntegerField(1, variant=_messages.Variant.UINT64)
  used = _messages.IntegerField(2, variant=_messages.Variant.UINT64)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaAcceleratorConfig(_messages.Message):
  r"""AcceleratorConfig defines the accelerator cards to attach to the VM.

  Fields:
    acceleratorCount: The number of guest accelerator cards exposed to each
      VM.
    acceleratorType: The type of accelerator to attach to each VM, e.g.
      "nvidia-tesla-k80" for nVidia Tesla K80.
  """

  acceleratorCount = _messages.IntegerField(1)
  acceleratorType = _messages.StringField(2)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaAutoscale(_messages.Message):
  r"""Autoscale defines the autoscaling policy of a worker pool.

  Fields:
    maxSize: Optional. The maximal number of workers. Must be equal to or
      greater than min_size.
    minIdleWorkers: Optional. The minimum number of idle workers the
      autoscaler will aim to have in the pool at all times that are
      immediately available to accept a surge in build traffic. The pool size
      will still be constrained by min_size and max_size.
    minSize: Optional. The minimal number of workers. Must be greater than 0.
  """

  maxSize = _messages.IntegerField(1)
  minIdleWorkers = _messages.IntegerField(2)
  minSize = _messages.IntegerField(3)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaBackendIAMBinding(_messages.Message):
  r"""A representation of an individual backend IAM binding.

  Fields:
    principal: Required. The IAM principal (i.e. twosync or twosync-src group)
      this binding applies to. This should be prefixed with `group:`. Example
      format: group:<EMAIL>
    role: Required. The RBE-managed IAM role this binding applies to. The set
      of eligible roles depends on which instance allowlist(s) the parent
      instance is a member of, specifically with regards to the
      ENABLE_BE_IAM_BINDING_* entries. Format: roles/
  """

  principal = _messages.StringField(1)
  role = _messages.StringField(2)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaBackendProperties(_messages.Message):
  r"""Describes backend project configuration. As of 2024Q2, this consists of
  user- managed IAM bindings established for this instance as well as the
  identity of the backend project.

  Fields:
    backendProjectNumber: Output only. The backend project number is not
      normally exposed to the user but is required by the user for these IAM
      role bindings to be useful. For example, a role that grants SSH access
      to worker machines in the backend project is useless if those machines
      cannot then be discovered.
    bindings: Output only. List of the desired BE project bindings.
  """

  backendProjectNumber = _messages.StringField(1)
  bindings = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaBackendIAMBinding', 2, repeated=True)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaCreateAndroidCIInstanceRequest(_messages.Message):
  r"""The request used for `CreateAndroidCIInstance`.

  Fields:
    billingProjectManagerGroup: Optional. Billing Project Manager Group used
      for changing the billing account of the backend project as we will be
      bypassing the BE chargeback.
    instance: Required. Specifies the instance to create. The name in the
      instance, if specified in the instance, is ignored.
    instanceId: Required. ID of the created instance. A valid `instance_id`
      must: be 6-50 characters long, contain only lowercase letters, digits,
      hyphens and underscores, start with a lowercase letter, and end with a
      lowercase letter or a digit.
    macServiceAccount: Optional. Mac Service Account is the service account
      that will be used to run Mac VMs.
    parent: Required. Resource name of the project containing the instance.
      Format: `projects/[PROJECT_ID]`.
    vmServiceAccount: Optional. Service Account is the service account that
      will be used to run the VMs.
  """

  billingProjectManagerGroup = _messages.StringField(1)
  instance = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaInstance', 2)
  instanceId = _messages.StringField(3)
  macServiceAccount = _messages.StringField(4)
  parent = _messages.StringField(5)
  vmServiceAccount = _messages.StringField(6)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaCreateBackendIAMBindingRequest(_messages.Message):
  r"""The request used for `CreateBackendIAMBinding`.

  Fields:
    backendIamBinding: Required. The backend IAM binding to create.
  """

  backendIamBinding = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaBackendIAMBinding', 1)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaCreateBackendIAMBindingResponse(_messages.Message):
  r"""The response used for `CreateBackendIAMBinding`.

  Fields:
    backendProperties: The backend properties of the instance, which contains
      the list of backend IAM bindings, after creation of the most recent
      binding.
  """

  backendProperties = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaBackendProperties', 1)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaCreateGuitarInstanceRequest(_messages.Message):
  r"""The request used for `CreateGuitarInstance`.

  Fields:
    instance: Required. Specifies the instance to create. The name in the
      instance, if specified in the instance, is ignored.
    instanceId: Required. ID of the created instance. A valid `instance_id`
      must: be 6-50 characters long, contain only lowercase letters, digits,
      hyphens and underscores, start with a lowercase letter, and end with a
      lowercase letter or a digit.
    parent: Required. Resource name of the project containing the instance.
      Format: `projects/[PROJECT_ID]`.
  """

  instance = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaInstance', 1)
  instanceId = _messages.StringField(2)
  parent = _messages.StringField(3)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaCreateInstanceRequest(_messages.Message):
  r"""The request used for `CreateInstance`.

  Fields:
    instance: Required. Specifies the instance to create. The name in the
      instance, if specified in the instance, is ignored.
    instanceId: Required. ID of the created instance. A valid `instance_id`
      must: - Be 6-50 characters long - Contain only lowercase letters,
      digits, hyphens and underscores - Start with a lowercase letter - End
      with a lowercase letter or a digit
    parent: Required. Resource name of the project containing the instance.
      Format: `projects/[PROJECT_ID]`.
  """

  instance = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaInstance', 1)
  instanceId = _messages.StringField(2)
  parent = _messages.StringField(3)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaCreateKokoroInstanceRequest(_messages.Message):
  r"""The request used for `CreateKokoroInstance`.

  Enums:
    AdminOpsRestrictionsValueValuesEnum: Optional. If AdminOpsRestrictions is
      ADMIN_OPS_RESTRICTIONS_RESTRICTED then only predefined safe fields are
      allowed to set through internal APIs through toolproxy.
      AdminOpsRestrictions can be set to ADMIN_OPS_RESTRICTIONS_RESTRICTED
      only if ACTION_ISOLATION is enforced.
    TcaRestrictionsValueValuesEnum: Optional. If tca_restrictions is ENABLED
      then the instance will have restrictions needed for building Trusted
      Core Access (TCA) compliant builds. DO NOT USE: In-Development feature.

  Fields:
    adminOpsRestrictions: Optional. If AdminOpsRestrictions is
      ADMIN_OPS_RESTRICTIONS_RESTRICTED then only predefined safe fields are
      allowed to set through internal APIs through toolproxy.
      AdminOpsRestrictions can be set to ADMIN_OPS_RESTRICTIONS_RESTRICTED
      only if ACTION_ISOLATION is enforced.
    immutableMetapolicy: Enforces meta policy immutability if set to true.
      This field is required. We use 'optional' here to catch (and error on)
      the case where this field is unset.
    instance: Required. Specifies the instance to create. The name in the
      instance, if specified in the instance, is ignored.
    instanceId: Required. ID of the created instance. A valid `instance_id`
      must: be 6-50 characters long, contain only lowercase letters, digits,
      hyphens and underscores, start with a lowercase letter, and end with a
      lowercase letter or a digit.
    parent: Required. Resource name of the project containing the instance.
      Format: `projects/[PROJECT_ID]`.
    tcaRestrictions: Optional. If tca_restrictions is ENABLED then the
      instance will have restrictions needed for building Trusted Core Access
      (TCA) compliant builds. DO NOT USE: In-Development feature.
  """

  class AdminOpsRestrictionsValueValuesEnum(_messages.Enum):
    r"""Optional. If AdminOpsRestrictions is ADMIN_OPS_RESTRICTIONS_RESTRICTED
    then only predefined safe fields are allowed to set through internal APIs
    through toolproxy. AdminOpsRestrictions can be set to
    ADMIN_OPS_RESTRICTIONS_RESTRICTED only if ACTION_ISOLATION is enforced.

    Values:
      RESTRICTIONS_UNSPECIFIED: Default value; interpreted as
        RESTRICTIONS_ALLOWED.
      RESTRICTIONS_ALLOWED: Admin operations are allowed normally.
      RESTRICTIONS_RESTRICTED: Admin operations are restricted to known safe
        operations that do not affect build integrity.
    """
    RESTRICTIONS_UNSPECIFIED = 0
    RESTRICTIONS_ALLOWED = 1
    RESTRICTIONS_RESTRICTED = 2

  class TcaRestrictionsValueValuesEnum(_messages.Enum):
    r"""Optional. If tca_restrictions is ENABLED then the instance will have
    restrictions needed for building Trusted Core Access (TCA) compliant
    builds. DO NOT USE: In-Development feature.

    Values:
      RESTRICTIONS_UNSPECIFIED: TCA restrictions are not specified, defaulting
        to RESTRICTIONS_DISABLED.
      RESTRICTIONS_DISABLED: TCA restrictions are disabled for the instance.
      RESTRICTIONS_ENABLED_V1: V1 version of TCA restrictions are enabled for
        the instance. These include: 1. ActionHermeticity to be BEST_EFFORT or
        ENFORCED and ActionIsolation to be ENFORCED. 2. ImmutableMetaPolicy to
        be true. 3. AdminOpsRestrictions to be RESTRICTED. 4. Using Shielded,
        Confidential and Software Trusted VMs as RBE workers, which use custom
        RBE TCA COS VM images and go through TPM attestation verification for
        various boot parameters. 5. Inputs and outputs of the actions on these
        VMs requires TPM attestation verification.
    """
    RESTRICTIONS_UNSPECIFIED = 0
    RESTRICTIONS_DISABLED = 1
    RESTRICTIONS_ENABLED_V1 = 2

  adminOpsRestrictions = _messages.EnumField('AdminOpsRestrictionsValueValuesEnum', 1)
  immutableMetapolicy = _messages.BooleanField(2)
  instance = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaInstance', 3)
  instanceId = _messages.StringField(4)
  parent = _messages.StringField(5)
  tcaRestrictions = _messages.EnumField('TcaRestrictionsValueValuesEnum', 6)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaCreateWorkerPoolRequest(_messages.Message):
  r"""The request used for `CreateWorkerPool`.

  Fields:
    parent: Resource name of the instance in which to create the new worker
      pool. Format: `projects/[PROJECT_ID]/instances/[INSTANCE_ID]`.
    poolId: ID of the created worker pool. A valid pool ID must: be 6-50
      characters long, contain only lowercase letters, digits, hyphens and
      underscores, start with a lowercase letter, and end with a lowercase
      letter or a digit.
    workerPool: Specifies the worker pool to create. The name in the worker
      pool, if specified, is ignored.
  """

  parent = _messages.StringField(1)
  poolId = _messages.StringField(2)
  workerPool = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaWorkerPool', 3)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaDeleteBackendIAMBindingRequest(_messages.Message):
  r"""The request used for `DeleteBackendIAMBinding`.

  Fields:
    backendIamBinding: Required. The backend IAM binding to delete.
  """

  backendIamBinding = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaBackendIAMBinding', 1)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaDeleteInstanceRequest(_messages.Message):
  r"""The request used for `DeleteInstance`.

  Fields:
    name: Required. Name of the instance to delete. Format:
      `projects/[PROJECT_ID]/instances/[INSTANCE_ID]`.
  """

  name = _messages.StringField(1)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaDeleteWorkerPoolRequest(_messages.Message):
  r"""The request used for DeleteWorkerPool.

  Fields:
    name: Name of the worker pool to delete. Format:
      `projects/[PROJECT_ID]/instances/[INSTANCE_ID]/workerpools/[POOL_ID]`.
  """

  name = _messages.StringField(1)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaDisks(_messages.Message):
  r"""Disks defines the disks that would be attached to the workers.

  Fields:
    dockerRootDisk: Optional. Specifies the configuration for the docker root
      disk to be attached. If not specified, RBE will default to the RBE
      managed docker root disk.
    localSsd: Optional. Specifies the number of local SSDs to be attached. If
      specified, local SSDs will be used as the working directory.
  """

  dockerRootDisk = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaDisksPersistentDisk', 1)
  localSsd = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaDisksLocalSSD', 2)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaDisksLocalSSD(_messages.Message):
  r"""LocalSSD specifies how to attach local SSD to the workers.

  Fields:
    count: Optional. The number of Local SSDs to be attached.
    sizeGb: Output only. The size of the local SSD in gb. Intended for
      informational purposes only.
  """

  count = _messages.IntegerField(1)
  sizeGb = _messages.IntegerField(2)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaDisksPersistentDisk(_messages.Message):
  r"""PersistentDisk specifies how to attach a persistent disk to the workers.

  Fields:
    diskSizeGb: Required. Size of the disk in GB.
    diskType: Required. Type of disk attached (supported types are pd-standard
      and pd-ssd).
    sourceImage: Required. VM image to use for the disk.
  """

  diskSizeGb = _messages.IntegerField(1)
  diskType = _messages.StringField(2)
  sourceImage = _messages.StringField(3)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaFeaturePolicy(_messages.Message):
  r"""FeaturePolicy defines features allowed to be used on RBE instances, as
  well as instance-wide behavior changes that take effect without opt-in or
  opt-out at usage time.

  Enums:
    ActionHermeticityValueValuesEnum: Defines the hermeticity policy for
      actions on this instance. DO NOT USE: Experimental / unlaunched feature.
    ActionIsolationValueValuesEnum: Defines the isolation policy for actions
      on this instance. DO NOT USE: Experimental / unlaunched feature.
    LinuxExecutionValueValuesEnum: Defines how Linux actions are allowed to
      execute. DO NOT USE: Experimental / unlaunched feature.
    LinuxIsolationValueValuesEnum: linux_isolation allows overriding the
      docker runtime used for containers started on Linux.
    MacExecutionValueValuesEnum: Defines how Windows actions are allowed to
      execute. DO NOT USE: Experimental / unlaunched feature.
    NetworkAccessValueValuesEnum: Optional. Defines the network access policy
      for actions on this instance. DO NOT USE: Experimental / unlaunched
      feature.
    VmVerificationValueValuesEnum: Whether to verify CreateBotSession and
      UpdateBotSession from the bot.
    WindowsExecutionValueValuesEnum: Defines how Windows actions are allowed
      to execute. DO NOT USE: Experimental / unlaunched feature.

  Fields:
    actionHermeticity: Defines the hermeticity policy for actions on this
      instance. DO NOT USE: Experimental / unlaunched feature.
    actionIsolation: Defines the isolation policy for actions on this
      instance. DO NOT USE: Experimental / unlaunched feature.
    containerImageSources: Which container image sources are allowed.
      Currently only RBE-supported registry (gcr.io) is allowed. One can allow
      all repositories under a project or one specific repository only. E.g.
      container_image_sources { policy: RESTRICTED allowed_values: [
      "gcr.io/project-foo", "gcr.io/project-bar/repo-baz", ] } will allow any
      repositories under "gcr.io/project-foo" plus the repository
      "gcr.io/project-bar/repo-baz". Default (UNSPECIFIED) is equivalent to
      any source is allowed.
    dockerAddCapabilities: Whether dockerAddCapabilities can be used or what
      capabilities are allowed.
    dockerChrootPath: Whether dockerChrootPath can be used.
    dockerNetwork: Whether dockerNetwork can be used or what network modes are
      allowed. E.g. one may allow `off` value only via `allowed_values`.
    dockerPrivileged: Whether dockerPrivileged can be used.
    dockerRunAsContainerProvidedUser: Whether dockerRunAsContainerProvidedUser
      can be used.
    dockerRunAsRoot: Whether dockerRunAsRoot can be used.
    dockerRuntime: Whether dockerRuntime is allowed to be set or what runtimes
      are allowed. Note linux_isolation takes precedence, and if set,
      docker_runtime values may be rejected if they are incompatible with the
      selected isolation.
    dockerSiblingContainers: Whether dockerSiblingContainers can be used.
    dockerUlimits: Whether dockerUlimits are allowed to be set.
    linuxExecution: Defines how Linux actions are allowed to execute. DO NOT
      USE: Experimental / unlaunched feature.
    linuxIsolation: linux_isolation allows overriding the docker runtime used
      for containers started on Linux.
    macExecution: Defines how Windows actions are allowed to execute. DO NOT
      USE: Experimental / unlaunched feature.
    networkAccess: Optional. Defines the network access policy for actions on
      this instance. DO NOT USE: Experimental / unlaunched feature.
    vmVerification: Whether to verify CreateBotSession and UpdateBotSession
      from the bot.
    windowsExecution: Defines how Windows actions are allowed to execute. DO
      NOT USE: Experimental / unlaunched feature.
  """

  class ActionHermeticityValueValuesEnum(_messages.Enum):
    r"""Defines the hermeticity policy for actions on this instance. DO NOT
    USE: Experimental / unlaunched feature.

    Values:
      ACTION_HERMETICITY_UNSPECIFIED: Default value, if not explicitly set.
        Equivalent to OFF.
      ACTION_HERMETICITY_OFF: Disables enforcing feature policies that
        guarantee action hermeticity.
      ACTION_HERMETICITY_ENFORCED: Enforces hermeticity of actions by
        requiring feature policies to be set that prevent actions from gaining
        network access. The enforcement mechanism has been reviewed by ISE.
      ACTION_HERMETICITY_BEST_EFFORT: Requires feature policies to be set that
        provide best effort hermeticity for actions. Best effort hermeticity
        means network access will be disabled and not trivial to bypass.
        However, a determined and malicious action may still find a way to
        gain network access.
    """
    ACTION_HERMETICITY_UNSPECIFIED = 0
    ACTION_HERMETICITY_OFF = 1
    ACTION_HERMETICITY_ENFORCED = 2
    ACTION_HERMETICITY_BEST_EFFORT = 3

  class ActionIsolationValueValuesEnum(_messages.Enum):
    r"""Defines the isolation policy for actions on this instance. DO NOT USE:
    Experimental / unlaunched feature.

    Values:
      ACTION_ISOLATION_UNSPECIFIED: Default value, if not explicitly set.
        Equivalent to OFF.
      ACTION_ISOLATION_OFF: Disables enforcing feature policies that guarantee
        action isolation.
      ACTION_ISOLATION_ENFORCED: Enforces setting feature policies that
        ensures actions within the RBE Instance are isolated from each other
        in a way deemed sufficient by ISE reviewers.
    """
    ACTION_ISOLATION_UNSPECIFIED = 0
    ACTION_ISOLATION_OFF = 1
    ACTION_ISOLATION_ENFORCED = 2

  class LinuxExecutionValueValuesEnum(_messages.Enum):
    r"""Defines how Linux actions are allowed to execute. DO NOT USE:
    Experimental / unlaunched feature.

    Values:
      LINUX_EXECUTION_UNSPECIFIED: Default value, if not explicitly set.
        Equivalent to FORBIDDEN.
      LINUX_EXECUTION_FORBIDDEN: Linux actions and worker pools are forbidden.
      LINUX_EXECUTION_UNRESTRICTED: No restrictions on execution of Linux
        actions.
      LINUX_EXECUTION_HARDENED_GVISOR: Linux actions will be hardened using
        gVisor. Actions that specify a configuration incompatible with gVisor
        hardening will be rejected. Example per-action platform properties
        that are incompatible with gVisor hardening are: 1. dockerRuntime is
        set to a value other than "runsc". Leaving dockerRuntime unspecified
        *is* compatible with gVisor. 2. dockerPrivileged is set to "true".
        etc.
      LINUX_EXECUTION_HARDENED_GVISOR_OR_TERMINAL: Linux actions will be
        hardened using gVisor if their configuration is compatible with gVisor
        hardening. Otherwise, the action will be terminal, i.e., the worker VM
        that runs the action will be terminated after the action finishes.
    """
    LINUX_EXECUTION_UNSPECIFIED = 0
    LINUX_EXECUTION_FORBIDDEN = 1
    LINUX_EXECUTION_UNRESTRICTED = 2
    LINUX_EXECUTION_HARDENED_GVISOR = 3
    LINUX_EXECUTION_HARDENED_GVISOR_OR_TERMINAL = 4

  class LinuxIsolationValueValuesEnum(_messages.Enum):
    r"""linux_isolation allows overriding the docker runtime used for
    containers started on Linux.

    Values:
      LINUX_ISOLATION_UNSPECIFIED: Default value. Will be using Linux default
        runtime.
      GVISOR: Use gVisor runsc runtime.
      OFF: Use standard Linux runtime. This has the same behaviour as
        unspecified, but it can be used to revert back from gVisor.
    """
    LINUX_ISOLATION_UNSPECIFIED = 0
    GVISOR = 1
    OFF = 2

  class MacExecutionValueValuesEnum(_messages.Enum):
    r"""Defines how Windows actions are allowed to execute. DO NOT USE:
    Experimental / unlaunched feature.

    Values:
      MAC_EXECUTION_UNSPECIFIED: Default value, if not explicitly set.
        Equivalent to FORBIDDEN.
      MAC_EXECUTION_FORBIDDEN: Mac actions and worker pools are forbidden.
      MAC_EXECUTION_UNRESTRICTED: No restrictions on execution of Mac actions.
      MAC_EXECUTION_TERMINAL: Mac actions will always result in the worker VM
        being terminated after the action completes.
    """
    MAC_EXECUTION_UNSPECIFIED = 0
    MAC_EXECUTION_FORBIDDEN = 1
    MAC_EXECUTION_UNRESTRICTED = 2
    MAC_EXECUTION_TERMINAL = 3

  class NetworkAccessValueValuesEnum(_messages.Enum):
    r"""Optional. Defines the network access policy for actions on this
    instance. DO NOT USE: Experimental / unlaunched feature.

    Values:
      NETWORK_ACCESS_UNSPECIFIED: Default value, if not explicitly set.
        Equivalent to ALLOWED.
      NETWORK_ACCESS_ALLOWED: Disables enforcing feature policies related to
        network access.
      NETWORK_ACCESS_ENFORCED: Requires feature policies to be set that
        guarantee network access restrctions. Enforced means that network
        access will be limited and certain features will be disabled to
        prevent bypassing the filter. However, a determined and malicious
        actor may still find a way to gain full network access.
    """
    NETWORK_ACCESS_UNSPECIFIED = 0
    NETWORK_ACCESS_ALLOWED = 1
    NETWORK_ACCESS_ENFORCED = 2

  class VmVerificationValueValuesEnum(_messages.Enum):
    r"""Whether to verify CreateBotSession and UpdateBotSession from the bot.

    Values:
      VM_VERIFICATION_UNSPECIFIED: Default value. Same as GCP_TOKEN.
      VM_VERIFICATION_GCP_TOKEN: Verify the VM token and the nonce associated
        with the VM.
      VM_VERIFICATION_OFF: Don't verify VM token and nonce.
    """
    VM_VERIFICATION_UNSPECIFIED = 0
    VM_VERIFICATION_GCP_TOKEN = 1
    VM_VERIFICATION_OFF = 2

  class WindowsExecutionValueValuesEnum(_messages.Enum):
    r"""Defines how Windows actions are allowed to execute. DO NOT USE:
    Experimental / unlaunched feature.

    Values:
      WINDOWS_EXECUTION_UNSPECIFIED: Default value, if not explicitly set.
        Equivalent to FORBIDDEN.
      WINDOWS_EXECUTION_FORBIDDEN: Windows actions and worker pools are
        forbidden.
      WINDOWS_EXECUTION_UNRESTRICTED: No restrictions on execution of Windows
        actions.
      WINDOWS_EXECUTION_TERMINAL: Windows actions will always result in the
        worker VM being terminated after the action completes.
    """
    WINDOWS_EXECUTION_UNSPECIFIED = 0
    WINDOWS_EXECUTION_FORBIDDEN = 1
    WINDOWS_EXECUTION_UNRESTRICTED = 2
    WINDOWS_EXECUTION_TERMINAL = 3

  actionHermeticity = _messages.EnumField('ActionHermeticityValueValuesEnum', 1)
  actionIsolation = _messages.EnumField('ActionIsolationValueValuesEnum', 2)
  containerImageSources = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaFeaturePolicyFeature', 3)
  dockerAddCapabilities = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaFeaturePolicyFeature', 4)
  dockerChrootPath = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaFeaturePolicyFeature', 5)
  dockerNetwork = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaFeaturePolicyFeature', 6)
  dockerPrivileged = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaFeaturePolicyFeature', 7)
  dockerRunAsContainerProvidedUser = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaFeaturePolicyFeature', 8)
  dockerRunAsRoot = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaFeaturePolicyFeature', 9)
  dockerRuntime = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaFeaturePolicyFeature', 10)
  dockerSiblingContainers = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaFeaturePolicyFeature', 11)
  dockerUlimits = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaFeaturePolicyFeature', 12)
  linuxExecution = _messages.EnumField('LinuxExecutionValueValuesEnum', 13)
  linuxIsolation = _messages.EnumField('LinuxIsolationValueValuesEnum', 14)
  macExecution = _messages.EnumField('MacExecutionValueValuesEnum', 15)
  networkAccess = _messages.EnumField('NetworkAccessValueValuesEnum', 16)
  vmVerification = _messages.EnumField('VmVerificationValueValuesEnum', 17)
  windowsExecution = _messages.EnumField('WindowsExecutionValueValuesEnum', 18)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaFeaturePolicyFeature(_messages.Message):
  r"""Defines whether a feature can be used or what values are accepted.

  Enums:
    PolicyValueValuesEnum: The policy of the feature.

  Fields:
    allowedValues: A list of acceptable values. Only effective when the policy
      is `RESTRICTED`.
    policy: The policy of the feature.
  """

  class PolicyValueValuesEnum(_messages.Enum):
    r"""The policy of the feature.

    Values:
      POLICY_UNSPECIFIED: Default value, if not explicitly set. Equivalent to
        FORBIDDEN, unless otherwise documented on a specific Feature.
      ALLOWED: Feature is explicitly allowed.
      FORBIDDEN: Feature is forbidden. Requests attempting to leverage it will
        get an FailedPrecondition error, with a message like: "Feature
        forbidden by FeaturePolicy: Feature on instance "
      RESTRICTED: Only the values specified in the `allowed_values` are
        allowed.
    """
    POLICY_UNSPECIFIED = 0
    ALLOWED = 1
    FORBIDDEN = 2
    RESTRICTED = 3

  allowedValues = _messages.StringField(1, repeated=True)
  policy = _messages.EnumField('PolicyValueValuesEnum', 2)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaGetInstanceRequest(_messages.Message):
  r"""The request used for `GetInstance`.

  Fields:
    name: Required. Name of the instance to retrieve. Format:
      `projects/[PROJECT_ID]/instances/[INSTANCE_ID]`.
  """

  name = _messages.StringField(1)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaGetWorkerPoolRequest(_messages.Message):
  r"""The request used for GetWorkerPool.

  Fields:
    name: Name of the worker pool to retrieve. Format:
      `projects/[PROJECT_ID]/instances/[INSTANCE_ID]/workerpools/[POOL_ID]`.
  """

  name = _messages.StringField(1)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaIAMBinding(_messages.Message):
  r"""Represents an IAM binding.

  Fields:
    principal: Required. The IAM principal this binding applies to.
    role: Required. The IAM role this binding applies to. Format: `roles/`
  """

  principal = _messages.StringField(1)
  role = _messages.StringField(2)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaInstance(_messages.Message):
  r"""Instance conceptually encapsulates all Remote Build Execution resources
  for remote builds. An instance consists of storage and compute resources
  (for example, `ContentAddressableStorage`, `ActionCache`, `WorkerPools`)
  used for running remote builds. All Remote Build Execution API calls are
  scoped to an instance.

  Enums:
    StateValueValuesEnum: Output only. State of the instance.

  Fields:
    backendProperties: Output only. Describes the instance's backend project
      configuration. Currently, this includes the list of user-managed IAM
      bindings applied to the backend project, which will always be empty for
      instances not in one of the ENABLE_BE_IAM_BINDING_* feature allowlists.
    bindings: Optional. The list of IAM bindings that should be applied to
      this instance.
    casRelations: Specify parent or child instances of `this` instance.
      Configurations will be rejected if: -- If `this` instance is not
      allowlisted for `ENABLE_DATA_READS_FROM_PARENT` and this list specifies
      parent instances. -- If `this` instance is not allowlisted for
      `ENABLE_DATA_READS_BY_CHILDREN` and this list specifies child instances.
      -- If both parent and child instances are specified. -- If instance has
      more than allowed number of parent or child instances.
    featurePolicy: The policy to define whether or not RBE features can be
      used or how they can be used.
    location: The location is a GCP region. Currently only `us-central1` is
      supported.
    loggingEnabled: Output only. Whether stack driver logging is enabled for
      the instance.
    name: Output only. Instance resource name formatted as:
      `projects/[PROJECT_ID]/instances/[INSTANCE_ID]`. Name should not be
      populated when creating an instance since it is provided in the
      `instance_id` field.
    schedulerNotificationConfig: The instance's configuration for scheduler
      notifications. Absence implies that this feature is not enabled for this
      instance.
    state: Output only. State of the instance.
    storageSettings: User-specified storage settings for this instance.
    zoneDrains: Optional. The list of zones that should be drained (no new
      tasks should be assigned to all or part of the workers in these).
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the instance.

    Values:
      STATE_UNSPECIFIED: Not a valid state, but the default value of the enum.
      CREATING: The instance is in state `CREATING` once `CreateInstance` is
        called and before the instance is ready for use.
      RUNNING: The instance is in state `RUNNING` when it is ready for use.
      INACTIVE: An `INACTIVE` instance indicates that there is a problem that
        needs to be fixed. Such instances cannot be used for execution and
        instances that remain in this state for a significant period of time
        will be removed permanently.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    RUNNING = 2
    INACTIVE = 3

  backendProperties = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaBackendProperties', 1)
  bindings = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaIAMBinding', 2, repeated=True)
  casRelations = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaRelationship', 3, repeated=True)
  featurePolicy = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaFeaturePolicy', 4)
  location = _messages.StringField(5)
  loggingEnabled = _messages.BooleanField(6)
  name = _messages.StringField(7)
  schedulerNotificationConfig = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaSchedulerNotificationConfig', 8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  storageSettings = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaStorageSettings', 10)
  zoneDrains = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaZoneDrain', 11, repeated=True)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaListInstancesRequest(_messages.Message):
  r"""The request used for `ListInstances`.

  Fields:
    parent: Required. Resource name of the project. Format:
      `projects/[PROJECT_ID]`.
  """

  parent = _messages.StringField(1)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaListInstancesResponse(_messages.Message):
  r"""The response used for `ListInstances`.

  Fields:
    instances: The list of instances in a given project.
    unreachable: Unreachable regions.
  """

  instances = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaInstance', 1, repeated=True)
  unreachable = _messages.StringField(2, repeated=True)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaListWorkerPoolsRequest(_messages.Message):
  r"""A GoogleDevtoolsRemotebuildexecutionAdminV1alphaListWorkerPoolsRequest
  object.

  Fields:
    filter: Optional. A filter expression that filters resources listed in the
      response. The expression must specify the field name, a comparison
      operator, and the value that you want to use for filtering. The value
      must be a string, a number, or a boolean. String values are case-
      insensitive. The comparison operator must be either `:`, `=`, `!=`, `>`,
      `>=`, `<=` or `<`. The `:` operator can be used with string fields to
      match substrings. For non-string fields it is equivalent to the `=`
      operator. The `:*` comparison can be used to test whether a key has been
      defined. You can also filter on nested fields. To filter on multiple
      expressions, you can separate expression using `AND` and `OR` operators,
      using parentheses to specify precedence. If neither operator is
      specified, `AND` is assumed. Examples: Include only pools with more than
      100 reserved workers: `(worker_count > 100) (worker_config.reserved =
      true)` Include only pools with a certain label or machines of the
      e2-standard family: `worker_config.labels.key1 : * OR
      worker_config.machine_type: e2-standard`
    parent: Resource name of the instance. Format:
      `projects/[PROJECT_ID]/instances/[INSTANCE_ID]`.
  """

  filter = _messages.StringField(1)
  parent = _messages.StringField(2)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaListWorkerPoolsResponse(_messages.Message):
  r"""A GoogleDevtoolsRemotebuildexecutionAdminV1alphaListWorkerPoolsResponse
  object.

  Fields:
    workerPools: The list of worker pools in a given instance.
  """

  workerPools = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaWorkerPool', 1, repeated=True)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaRelationship(_messages.Message):
  r"""Defines the relationship with another RBE instance.

  Enums:
    RelationValueValuesEnum: Specify the relationship between `instance` and
      the RBE instance it is related to.

  Fields:
    instance: Instance resource name (e.g.
      `projects/[PROJECT_ID]/instances/[INSTANCE_ID]`) of an instance which is
      either a parent or child of another RBE instance.
    relation: Specify the relationship between `instance` and the RBE instance
      it is related to.
    status: Output only. The validation result of this relationship. Possible
      status codes are: - `OK`: a valid relationship - `FAILED_PRECONDITION`:
      missing allowlist or misconfigured instance (Note that we don't want to
      return `NOT_FOUND` which leaks information about whether or not a
      project exists)
  """

  class RelationValueValuesEnum(_messages.Enum):
    r"""Specify the relationship between `instance` and the RBE instance it is
    related to.

    Values:
      RELATION_UNSPECIFIED: Default value but not a valid value; it is an
        error to set this value.
      RELATION_CAS_PARENT: `instance` is a parent of another RBE instance.
      RELATION_CAS_CHILD: `instance` is a child of another RBE instance.
    """
    RELATION_UNSPECIFIED = 0
    RELATION_CAS_PARENT = 1
    RELATION_CAS_CHILD = 2

  instance = _messages.StringField(1)
  relation = _messages.EnumField('RelationValueValuesEnum', 2)
  status = _messages.MessageField('GoogleRpcStatus', 3)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaSchedulerNotificationConfig(_messages.Message):
  r"""Defines configurations for an instance's scheduler notifications, where
  a target Pub/Sub topic will be notified whenever a task (e.g. an action or
  reservation) completes on this instance.

  Fields:
    topic: The Pub/Sub topic resource name to issue notifications to. Note
      that the topic does not need to be owned by the same project as this
      instance. Format: projects//topics/
  """

  topic = _messages.StringField(1)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaStorageSettings(_messages.Message):
  r"""Describes the storage settings for an instance. This consists of user-
  specified TTL values for CAS and ActionResult entries.

  Fields:
    actionResultTtl: Defines how long after a cached execution result was
      uploaded or accessed (due to a cache hit) before it is eligible for
      garbage collection (TTL countdowns are reset upon access). RBE may
      remove it from the action cache at any time after this period has
      elapsed. Note that the action cache only concerns when an incoming
      execution to RBE results in a cache hit. Input and output artifacts of
      the action are stored in the RBE CAS.
    casTtl: Defines how long after a blob was uploaded or last accessed before
      it is eligible for garbage collection (TTL countdowns are reset upon
      access). RBE may remove it from the instance's CAS at any time after
      this period has elapsed.
  """

  actionResultTtl = _messages.StringField(1)
  casTtl = _messages.StringField(2)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaTestNotifyInstanceRequest(_messages.Message):
  r"""The request used for `TestNotifyInstance`."""


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaUpdateInstanceRequest(_messages.Message):
  r"""The request used for `UpdateInstance`.

  Fields:
    instance: Specifies the instance to update.
    loggingEnabled: Deprecated, use instance.logging_enabled instead. Whether
      to enable Stackdriver logging for this instance.
    name: Deprecated, use instance.Name instead. Name of the instance to
      update. Format: `projects/[PROJECT_ID]/instances/[INSTANCE_ID]`.
    updateMask: The update mask applies to instance. For the `FieldMask`
      definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask If an empty update_mask
      is provided, only the non-default valued field in the worker pool field
      will be updated. Note that in order to update a field to the default
      value (zero, false, empty string) an explicit update_mask must be
      provided.
  """

  instance = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaInstance', 1)
  loggingEnabled = _messages.BooleanField(2)
  name = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaUpdateWorkerPoolRequest(_messages.Message):
  r"""The request used for UpdateWorkerPool.

  Fields:
    updateMask: The update mask applies to worker_pool. For the `FieldMask`
      definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask If an empty update_mask
      is provided, only the non-default valued field in the worker pool field
      will be updated. Note that in order to update a field to the default
      value (zero, false, empty string) an explicit update_mask must be
      provided.
    workerPool: Specifies the worker pool to update.
  """

  updateMask = _messages.StringField(1)
  workerPool = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaWorkerPool', 2)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaWorkerConfig(_messages.Message):
  r"""Defines the configuration to be used for creating workers in the worker
  pool.

  Messages:
    LabelsValue: Labels associated with the workers. Label keys and values can
      be no longer than 63 characters, can only contain lowercase letters,
      numeric characters, underscores and dashes. International letters are
      permitted. Label keys must start with a letter. Label values are
      optional. There can not be more than 64 labels per resource.

  Fields:
    accelerator: The accelerator card attached to each VM.
    attachedDisks: Optional. Specifies the disks that will be attached.
    diskSizeGb: Required. Size of the disk attached to the worker, in GB. See
      https://cloud.google.com/compute/docs/disks/
    diskType: Required. Disk Type to use for the worker. See [Storage
      options](https://cloud.google.com/compute/docs/disks/#introduction).
      Currently only `pd-standard` and `pd-ssd` are supported.
    labels: Labels associated with the workers. Label keys and values can be
      no longer than 63 characters, can only contain lowercase letters,
      numeric characters, underscores and dashes. International letters are
      permitted. Label keys must start with a letter. Label values are
      optional. There can not be more than 64 labels per resource.
    machineType: Required. Machine type of the worker, such as
      `e2-standard-2`. See https://cloud.google.com/compute/docs/machine-types
      for a list of supported machine types. Note that `f1-micro` and
      `g1-small` are not yet supported.
    maxConcurrentActions: The maximum number of actions a worker can execute
      concurrently.
    minCpuPlatform: Minimum CPU platform to use when creating the worker. See
      [CPU Platforms](https://cloud.google.com/compute/docs/cpu-platforms).
    networkAccess: Determines the type of network access granted to workers.
      Possible values: - "public": Workers can connect to the public internet.
      - "private": Workers can only connect to Google APIs and services. -
      "restricted-private": Workers can only connect to Google APIs that are
      reachable through `restricted.googleapis.com` (`199.36.153.4/30`).
    networkAllowlist: Optional. Specifies the network allowlist to use for the
      pool. Possible values: - "": No network filtering takes place. Any
      action with an allowlist will be rejected since it cannot be enforced.
      This is the default. - "per_action": Network access will be filtered
      according to the allowlist provided by the action. If no allowlist is
      provided, the default is to block all requests.
    reserved: Determines whether the worker is reserved (equivalent to a
      Compute Engine on-demand VM and therefore won't be preempted). See
      [Preemptible VMs](https://cloud.google.com/preemptible-vms/) for more
      details.
    soleTenantNodeType: The node type name to be used for sole-tenant nodes.
    userServiceAccounts: Optional. List of user service accounts. The last
      service account in the list is what the user code will run as. The rest
      of the service accounts constitute the impersonation chain. For example,
      if len(user_service_accounts) == 2 and if the VM's service account is
      RBE's P4SA, then RBE'S P4SA should be granted the Service Account Token
      Creator role on user_service_accounts[0] and user_service_accounts[0]
      should be granted the Service Account Token Creator role on
      user_service_accounts[1].
    vmImage: The name of the image used by each VM.
    zones: Optional. Zones in the region where the pool VMs should be. Leave
      empty for no restrictions.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels associated with the workers. Label keys and values can be no
    longer than 63 characters, can only contain lowercase letters, numeric
    characters, underscores and dashes. International letters are permitted.
    Label keys must start with a letter. Label values are optional. There can
    not be more than 64 labels per resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  accelerator = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaAcceleratorConfig', 1)
  attachedDisks = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaDisks', 2)
  diskSizeGb = _messages.IntegerField(3)
  diskType = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  machineType = _messages.StringField(6)
  maxConcurrentActions = _messages.IntegerField(7)
  minCpuPlatform = _messages.StringField(8)
  networkAccess = _messages.StringField(9)
  networkAllowlist = _messages.StringField(10)
  reserved = _messages.BooleanField(11)
  soleTenantNodeType = _messages.StringField(12)
  userServiceAccounts = _messages.StringField(13, repeated=True)
  vmImage = _messages.StringField(14)
  zones = _messages.StringField(15, repeated=True)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaWorkerPool(_messages.Message):
  r"""A worker pool resource in the Remote Build Execution API.

  Enums:
    StateValueValuesEnum: Output only. State of the worker pool.

  Fields:
    autoscale: The autoscale policy to apply on a pool.
    channel: Channel specifies the release channel of the pool.
    hostOs: HostOS specifies the OS version of the image for the worker VMs.
    name: WorkerPool resource name formatted as:
      `projects/[PROJECT_ID]/instances/[INSTANCE_ID]/workerpools/[POOL_ID]`.
      name should not be populated when creating a worker pool since it is
      provided in the `poolId` field.
    state: Output only. State of the worker pool.
    workerConfig: Specifies the properties, such as machine type and disk
      size, used for creating workers in a worker pool.
    workerCount: The desired number of workers in the worker pool. Must be a
      value between 0 and 15000.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the worker pool.

    Values:
      STATE_UNSPECIFIED: Not a valid state, but the default value of the enum.
      CREATING: The worker pool is in state `CREATING` once `CreateWorkerPool`
        is called and before all requested workers are ready.
      RUNNING: The worker pool is in state `RUNNING` when all its workers are
        ready for use.
      UPDATING: The worker pool is in state `UPDATING` once `UpdateWorkerPool`
        is called and before the new configuration has all the requested
        workers ready for use, and no older configuration has any workers. At
        that point the state transitions to `RUNNING`.
      DELETING: The worker pool is in state `DELETING` once the `Delete`
        method is called and before the deletion completes.
      INACTIVE: The worker pool is in state `INACTIVE` when the instance
        hosting the worker pool in not running.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    RUNNING = 2
    UPDATING = 3
    DELETING = 4
    INACTIVE = 5

  autoscale = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaAutoscale', 1)
  channel = _messages.StringField(2)
  hostOs = _messages.StringField(3)
  name = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)
  workerConfig = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaWorkerConfig', 6)
  workerCount = _messages.IntegerField(7)


class GoogleDevtoolsRemotebuildexecutionAdminV1alphaZoneDrain(_messages.Message):
  r"""ZoneDrain indicates a zone that should be drained (no new tasks should
  be assigned to all or part of the workers in it).

  Enums:
    ZoneTypeValueValuesEnum: Optional. Type of zone for validation purposes.

  Fields:
    percent: Optional. The percentage of workers in the zone to drain, from 0
      (no workers drained) to 100 (all workers drained).
    zone: Required. The zone to drain.
    zoneType: Optional. Type of zone for validation purposes.
  """

  class ZoneTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Type of zone for validation purposes.

    Values:
      ZONE_TYPE_UNSPECIFIED: When unspecified, defaults to GCE zone type.
      ZONE_TYPE_GCE: Validate as GCE zone.
    """
    ZONE_TYPE_UNSPECIFIED = 0
    ZONE_TYPE_GCE = 1

  percent = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  zone = _messages.StringField(2)
  zoneType = _messages.EnumField('ZoneTypeValueValuesEnum', 3)


class GoogleDevtoolsRemoteworkersV1test2AdminTemp(_messages.Message):
  r"""AdminTemp is a prelimiary set of administration tasks. It's called
  "Temp" because we do not yet know the best way to represent admin tasks;
  it's possible that this will be entirely replaced in later versions of this
  API. If this message proves to be sufficient, it will be renamed in the
  alpha or beta release of this API. This message (suitably marshalled into a
  protobuf.Any) can be used as the inline_assignment field in a lease; the
  lease assignment field should simply be `"admin"` in these cases. This
  message is heavily based on Swarming administration tasks from the LUCI
  project (http://github.com/luci/luci-py/appengine/swarming).

  Enums:
    CommandValueValuesEnum: The admin action; see `Command` for legal values.

  Fields:
    arg: The argument to the admin action; see `Command` for semantics.
    command: The admin action; see `Command` for legal values.
  """

  class CommandValueValuesEnum(_messages.Enum):
    r"""The admin action; see `Command` for legal values.

    Values:
      UNSPECIFIED: Illegal value.
      BOT_UPDATE: Download and run a new version of the bot. `arg` will be a
        resource accessible via `ByteStream.Read` to obtain the new bot code.
      BOT_RESTART: Restart the bot without downloading a new version. `arg`
        will be a message to log.
      BOT_TERMINATE: Shut down the bot. `arg` will be a task resource name
        (similar to those in tasks.proto) that the bot can use to tell the
        server that it is terminating.
      HOST_RESTART: Restart the host computer. `arg` will be a message to log.
    """
    UNSPECIFIED = 0
    BOT_UPDATE = 1
    BOT_RESTART = 2
    BOT_TERMINATE = 3
    HOST_RESTART = 4

  arg = _messages.StringField(1)
  command = _messages.EnumField('CommandValueValuesEnum', 2)


class GoogleDevtoolsRemoteworkersV1test2Blob(_messages.Message):
  r"""Describes a blob of binary content with its digest.

  Fields:
    contents: The contents of the blob.
    digest: The digest of the blob. This should be verified by the receiver.
  """

  contents = _messages.BytesField(1)
  digest = _messages.MessageField('GoogleDevtoolsRemoteworkersV1test2Digest', 2)


class GoogleDevtoolsRemoteworkersV1test2CommandOutputs(_messages.Message):
  r"""DEPRECATED - use CommandResult instead. Describes the actual outputs
  from the task.

  Fields:
    exitCode: exit_code is only fully reliable if the status' code is OK. If
      the task exceeded its deadline or was cancelled, the process may still
      produce an exit code as it is cancelled, and this will be populated, but
      a successful (zero) is unlikely to be correct unless the status code is
      OK.
    outputs: The output files. The blob referenced by the digest should
      contain one of the following (implementation-dependent): * A marshalled
      DirectoryMetadata of the returned filesystem * A LUCI-style .isolated
      file
  """

  exitCode = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  outputs = _messages.MessageField('GoogleDevtoolsRemoteworkersV1test2Digest', 2)


class GoogleDevtoolsRemoteworkersV1test2CommandOverhead(_messages.Message):
  r"""DEPRECATED - use CommandResult instead. Can be used as part of
  CompleteRequest.metadata, or are part of a more sophisticated message.

  Fields:
    duration: The elapsed time between calling Accept and Complete. The server
      will also have its own idea of what this should be, but this excludes
      the overhead of the RPCs and the bot response time.
    overhead: The amount of time *not* spent executing the command (ie
      uploading/downloading files).
  """

  duration = _messages.StringField(1)
  overhead = _messages.StringField(2)


class GoogleDevtoolsRemoteworkersV1test2CommandResult(_messages.Message):
  r"""All information about the execution of a command, suitable for providing
  as the Bots interface's `Lease.result` field.

  Messages:
    MetadataValueListEntry: A MetadataValueListEntry object.

  Fields:
    duration: The elapsed time between calling Accept and Complete. The server
      will also have its own idea of what this should be, but this excludes
      the overhead of the RPCs and the bot response time.
    exitCode: The exit code of the process. An exit code of "0" should only be
      trusted if `status` has a code of OK (otherwise it may simply be unset).
    metadata: Implementation-dependent metadata about the task. Both servers
      and bots may define messages which can be encoded here; bots are free to
      provide metadata in multiple formats, and servers are free to choose one
      or more of the values to process and ignore others. In particular, it is
      *not* considered an error for the bot to provide the server with a field
      that it doesn't know about.
    outputs: The output files. The blob referenced by the digest should
      contain one of the following (implementation-dependent): * A marshalled
      DirectoryMetadata of the returned filesystem * A LUCI-style .isolated
      file
    overhead: The amount of time *not* spent executing the command (ie
      uploading/downloading files).
    status: An overall status for the command. For example, if the command
      timed out, this might have a code of DEADLINE_EXCEEDED; if it was killed
      by the OS for memory exhaustion, it might have a code of
      RESOURCE_EXHAUSTED.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValueListEntry(_messages.Message):
    r"""A MetadataValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a MetadataValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  duration = _messages.StringField(1)
  exitCode = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  metadata = _messages.MessageField('MetadataValueListEntry', 3, repeated=True)
  outputs = _messages.MessageField('GoogleDevtoolsRemoteworkersV1test2Digest', 4)
  overhead = _messages.StringField(5)
  status = _messages.MessageField('GoogleRpcStatus', 6)


class GoogleDevtoolsRemoteworkersV1test2CommandTask(_messages.Message):
  r"""Describes a shell-style task to execute, suitable for providing as the
  Bots interface's `Lease.payload` field.

  Fields:
    expectedOutputs: The expected outputs from the task.
    inputs: The inputs to the task.
    timeouts: The timeouts of this task.
  """

  expectedOutputs = _messages.MessageField('GoogleDevtoolsRemoteworkersV1test2CommandTaskOutputs', 1)
  inputs = _messages.MessageField('GoogleDevtoolsRemoteworkersV1test2CommandTaskInputs', 2)
  timeouts = _messages.MessageField('GoogleDevtoolsRemoteworkersV1test2CommandTaskTimeouts', 3)


class GoogleDevtoolsRemoteworkersV1test2CommandTaskInputs(_messages.Message):
  r"""Describes the inputs to a shell-style task.

  Fields:
    arguments: The command itself to run (e.g., argv). This field should be
      passed directly to the underlying operating system, and so it must be
      sensible to that operating system. For example, on Windows, the first
      argument might be "C:\Windows\System32\ping.exe" - that is, using drive
      letters and backslashes. A command for a *nix system, on the other hand,
      would use forward slashes. All other fields in the RWAPI must
      consistently use forward slashes, since those fields may be interpreted
      by both the service and the bot.
    environmentVariables: All environment variables required by the task.
    files: The input filesystem to be set up prior to the task beginning. The
      contents should be a repeated set of FileMetadata messages though other
      formats are allowed if better for the implementation (eg, a LUCI-style
      .isolated file). This field is repeated since implementations might want
      to cache the metadata, in which case it may be useful to break up
      portions of the filesystem that change frequently (eg, specific input
      files) from those that don't (eg, standard header files).
    inlineBlobs: Inline contents for blobs expected to be needed by the bot to
      execute the task. For example, contents of entries in `files` or blobs
      that are indirectly referenced by an entry there. The bot should check
      against this list before downloading required task inputs to reduce the
      number of communications between itself and the remote CAS server.
    inputRootDigest: The digest of the Merkle tree root of the inputs to the
      task. If present, the 'files' field should be empty.
    workingDirectory: Directory from which a command is executed. It is a
      relative directory with respect to the bot's working directory (i.e.,
      "./"). If it is non-empty, then it must exist under "./". Otherwise,
      "./" will be used.
  """

  arguments = _messages.StringField(1, repeated=True)
  environmentVariables = _messages.MessageField('GoogleDevtoolsRemoteworkersV1test2CommandTaskInputsEnvironmentVariable', 2, repeated=True)
  files = _messages.MessageField('GoogleDevtoolsRemoteworkersV1test2Digest', 3, repeated=True)
  inlineBlobs = _messages.MessageField('GoogleDevtoolsRemoteworkersV1test2Blob', 4, repeated=True)
  inputRootDigest = _messages.MessageField('GoogleDevtoolsRemoteworkersV1test2Digest', 5)
  workingDirectory = _messages.StringField(6)


class GoogleDevtoolsRemoteworkersV1test2CommandTaskInputsEnvironmentVariable(_messages.Message):
  r"""An environment variable required by this task.

  Fields:
    name: The envvar name.
    value: The envvar value.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class GoogleDevtoolsRemoteworkersV1test2CommandTaskOutputs(_messages.Message):
  r"""Describes the expected outputs of the command.

  Fields:
    directories: A list of expected directories, relative to the execution
      root. All paths MUST be delimited by forward slashes.
    files: A list of expected files, relative to the execution root. All paths
      MUST be delimited by forward slashes.
    stderrDestination: The destination to which any stderr should be sent. The
      method by which the bot should send the stream contents to that
      destination is not defined in this API. As examples, the destination
      could be a file referenced in the `files` field in this message, or it
      could be a URI that must be written via the ByteStream API.
    stdoutDestination: The destination to which any stdout should be sent. The
      method by which the bot should send the stream contents to that
      destination is not defined in this API. As examples, the destination
      could be a file referenced in the `files` field in this message, or it
      could be a URI that must be written via the ByteStream API.
  """

  directories = _messages.StringField(1, repeated=True)
  files = _messages.StringField(2, repeated=True)
  stderrDestination = _messages.StringField(3)
  stdoutDestination = _messages.StringField(4)


class GoogleDevtoolsRemoteworkersV1test2CommandTaskTimeouts(_messages.Message):
  r"""Describes the timeouts associated with this task.

  Fields:
    execution: This specifies the maximum time that the task can run,
      excluding the time required to download inputs or upload outputs. That
      is, the worker will terminate the task if it runs longer than this.
    idle: This specifies the maximum amount of time the task can be idle -
      that is, go without generating some output in either stdout or stderr.
      If the process is silent for more than the specified time, the worker
      will terminate the task.
    shutdown: If the execution or IO timeouts are exceeded, the worker will
      try to gracefully terminate the task and return any existing logs.
      However, tasks may be hard-frozen in which case this process will fail.
      This timeout specifies how long to wait for a terminated task to shut
      down gracefully (e.g. via SIGTERM) before we bring down the hammer (e.g.
      SIGKILL on *nix, CTRL_BREAK_EVENT on Windows).
  """

  execution = _messages.StringField(1)
  idle = _messages.StringField(2)
  shutdown = _messages.StringField(3)


class GoogleDevtoolsRemoteworkersV1test2Digest(_messages.Message):
  r"""The CommandTask and CommandResult messages assume the existence of a
  service that can serve blobs of content, identified by a hash and size known
  as a "digest." The method by which these blobs may be retrieved is not
  specified here, but a model implementation is in the Remote Execution API's
  "ContentAddressibleStorage" interface. In the context of the RWAPI, a Digest
  will virtually always refer to the contents of a file or a directory. The
  latter is represented by the byte-encoded Directory message.

  Fields:
    hash: A string-encoded hash (eg "1a2b3c", not the byte array [0x1a, 0x2b,
      0x3c]) using an implementation-defined hash algorithm (eg SHA-256).
    sizeBytes: The size of the contents. While this is not strictly required
      as part of an identifier (after all, any given hash will have exactly
      one canonical size), it's useful in almost all cases when one might want
      to send or retrieve blobs of content and is included here for this
      reason.
  """

  hash = _messages.StringField(1)
  sizeBytes = _messages.IntegerField(2)


class GoogleDevtoolsRemoteworkersV1test2Directory(_messages.Message):
  r"""The contents of a directory. Similar to the equivalent message in the
  Remote Execution API.

  Fields:
    directories: Any subdirectories
    files: The files in this directory
  """

  directories = _messages.MessageField('GoogleDevtoolsRemoteworkersV1test2DirectoryMetadata', 1, repeated=True)
  files = _messages.MessageField('GoogleDevtoolsRemoteworkersV1test2FileMetadata', 2, repeated=True)


class GoogleDevtoolsRemoteworkersV1test2DirectoryMetadata(_messages.Message):
  r"""The metadata for a directory. Similar to the equivalent message in the
  Remote Execution API.

  Fields:
    digest: A pointer to the contents of the directory, in the form of a
      marshalled Directory message.
    path: The path of the directory, as in FileMetadata.path.
  """

  digest = _messages.MessageField('GoogleDevtoolsRemoteworkersV1test2Digest', 1)
  path = _messages.StringField(2)


class GoogleDevtoolsRemoteworkersV1test2FileMetadata(_messages.Message):
  r"""The metadata for a file. Similar to the equivalent message in the Remote
  Execution API.

  Fields:
    contents: If the file is small enough, its contents may also or
      alternatively be listed here.
    digest: A pointer to the contents of the file. The method by which a
      client retrieves the contents from a CAS system is not defined here.
    isExecutable: Properties of the file
    path: The path of this file. If this message is part of the
      CommandOutputs.outputs fields, the path is relative to the execution
      root and must correspond to an entry in CommandTask.outputs.files. If
      this message is part of a Directory message, then the path is relative
      to the root of that directory. All paths MUST be delimited by forward
      slashes.
  """

  contents = _messages.BytesField(1)
  digest = _messages.MessageField('GoogleDevtoolsRemoteworkersV1test2Digest', 2)
  isExecutable = _messages.BooleanField(3)
  path = _messages.StringField(4)


class GoogleLongrunningOperation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('GoogleRpcStatus', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class GoogleProtobufEmpty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class GoogleRpcStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class RemotebuildexecutionProjectsInstancesCreateBackendIAMBindingRequest(_messages.Message):
  r"""A RemotebuildexecutionProjectsInstancesCreateBackendIAMBindingRequest
  object.

  Fields:
    googleDevtoolsRemotebuildexecutionAdminV1alphaCreateBackendIAMBindingReque
      st: A GoogleDevtoolsRemotebuildexecutionAdminV1alphaCreateBackendIAMBind
      ingRequest resource to be passed as the request body.
    parent: Required. Name of the instance to create a backend project IAM
      binding for. Format: projects/[PROJECT_ID]/instances/[INSTANCE_ID]
  """

  googleDevtoolsRemotebuildexecutionAdminV1alphaCreateBackendIAMBindingRequest = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaCreateBackendIAMBindingRequest', 1)
  parent = _messages.StringField(2, required=True)


class RemotebuildexecutionProjectsInstancesDeleteBackendIAMBindingRequest(_messages.Message):
  r"""A RemotebuildexecutionProjectsInstancesDeleteBackendIAMBindingRequest
  object.

  Fields:
    googleDevtoolsRemotebuildexecutionAdminV1alphaDeleteBackendIAMBindingReque
      st: A GoogleDevtoolsRemotebuildexecutionAdminV1alphaDeleteBackendIAMBind
      ingRequest resource to be passed as the request body.
    parent: Required. Name of the instance to delete a backend project IAM
      binding for. Format: projects/[PROJECT_ID]/instances/[INSTANCE_ID]
  """

  googleDevtoolsRemotebuildexecutionAdminV1alphaDeleteBackendIAMBindingRequest = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaDeleteBackendIAMBindingRequest', 1)
  parent = _messages.StringField(2, required=True)


class RemotebuildexecutionProjectsInstancesDeleteRequest(_messages.Message):
  r"""A RemotebuildexecutionProjectsInstancesDeleteRequest object.

  Fields:
    name: Required. Name of the instance to delete. Format:
      `projects/[PROJECT_ID]/instances/[INSTANCE_ID]`.
  """

  name = _messages.StringField(1, required=True)


class RemotebuildexecutionProjectsInstancesGetRequest(_messages.Message):
  r"""A RemotebuildexecutionProjectsInstancesGetRequest object.

  Fields:
    name: Required. Name of the instance to retrieve. Format:
      `projects/[PROJECT_ID]/instances/[INSTANCE_ID]`.
  """

  name = _messages.StringField(1, required=True)


class RemotebuildexecutionProjectsInstancesListRequest(_messages.Message):
  r"""A RemotebuildexecutionProjectsInstancesListRequest object.

  Fields:
    parent: Required. Resource name of the project. Format:
      `projects/[PROJECT_ID]`.
  """

  parent = _messages.StringField(1, required=True)


class RemotebuildexecutionProjectsInstancesPatchRequest(_messages.Message):
  r"""A RemotebuildexecutionProjectsInstancesPatchRequest object.

  Fields:
    googleDevtoolsRemotebuildexecutionAdminV1alphaInstance: A
      GoogleDevtoolsRemotebuildexecutionAdminV1alphaInstance resource to be
      passed as the request body.
    loggingEnabled: Deprecated, use instance.logging_enabled instead. Whether
      to enable Stackdriver logging for this instance.
    name: Output only. Instance resource name formatted as:
      `projects/[PROJECT_ID]/instances/[INSTANCE_ID]`. Name should not be
      populated when creating an instance since it is provided in the
      `instance_id` field.
    name1: Deprecated, use instance.Name instead. Name of the instance to
      update. Format: `projects/[PROJECT_ID]/instances/[INSTANCE_ID]`.
    updateMask: The update mask applies to instance. For the `FieldMask`
      definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask If an empty update_mask
      is provided, only the non-default valued field in the worker pool field
      will be updated. Note that in order to update a field to the default
      value (zero, false, empty string) an explicit update_mask must be
      provided.
  """

  googleDevtoolsRemotebuildexecutionAdminV1alphaInstance = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaInstance', 1)
  loggingEnabled = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)
  name1 = _messages.StringField(4)
  updateMask = _messages.StringField(5)


class RemotebuildexecutionProjectsInstancesTestNotifyRequest(_messages.Message):
  r"""A RemotebuildexecutionProjectsInstancesTestNotifyRequest object.

  Fields:
    googleDevtoolsRemotebuildexecutionAdminV1alphaTestNotifyInstanceRequest: A
      GoogleDevtoolsRemotebuildexecutionAdminV1alphaTestNotifyInstanceRequest
      resource to be passed as the request body.
    name: Name of the instance for which to send a test notification. Format:
      `projects/[PROJECT_ID]/instances/[INSTANCE_ID]`.
  """

  googleDevtoolsRemotebuildexecutionAdminV1alphaTestNotifyInstanceRequest = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaTestNotifyInstanceRequest', 1)
  name = _messages.StringField(2, required=True)


class RemotebuildexecutionProjectsInstancesWorkerpoolsDeleteRequest(_messages.Message):
  r"""A RemotebuildexecutionProjectsInstancesWorkerpoolsDeleteRequest object.

  Fields:
    name: Name of the worker pool to delete. Format:
      `projects/[PROJECT_ID]/instances/[INSTANCE_ID]/workerpools/[POOL_ID]`.
  """

  name = _messages.StringField(1, required=True)


class RemotebuildexecutionProjectsInstancesWorkerpoolsGetRequest(_messages.Message):
  r"""A RemotebuildexecutionProjectsInstancesWorkerpoolsGetRequest object.

  Fields:
    name: Name of the worker pool to retrieve. Format:
      `projects/[PROJECT_ID]/instances/[INSTANCE_ID]/workerpools/[POOL_ID]`.
  """

  name = _messages.StringField(1, required=True)


class RemotebuildexecutionProjectsInstancesWorkerpoolsListRequest(_messages.Message):
  r"""A RemotebuildexecutionProjectsInstancesWorkerpoolsListRequest object.

  Fields:
    filter: Optional. A filter expression that filters resources listed in the
      response. The expression must specify the field name, a comparison
      operator, and the value that you want to use for filtering. The value
      must be a string, a number, or a boolean. String values are case-
      insensitive. The comparison operator must be either `:`, `=`, `!=`, `>`,
      `>=`, `<=` or `<`. The `:` operator can be used with string fields to
      match substrings. For non-string fields it is equivalent to the `=`
      operator. The `:*` comparison can be used to test whether a key has been
      defined. You can also filter on nested fields. To filter on multiple
      expressions, you can separate expression using `AND` and `OR` operators,
      using parentheses to specify precedence. If neither operator is
      specified, `AND` is assumed. Examples: Include only pools with more than
      100 reserved workers: `(worker_count > 100) (worker_config.reserved =
      true)` Include only pools with a certain label or machines of the
      e2-standard family: `worker_config.labels.key1 : * OR
      worker_config.machine_type: e2-standard`
    parent: Resource name of the instance. Format:
      `projects/[PROJECT_ID]/instances/[INSTANCE_ID]`.
  """

  filter = _messages.StringField(1)
  parent = _messages.StringField(2, required=True)


class RemotebuildexecutionProjectsInstancesWorkerpoolsPatchRequest(_messages.Message):
  r"""A RemotebuildexecutionProjectsInstancesWorkerpoolsPatchRequest object.

  Fields:
    googleDevtoolsRemotebuildexecutionAdminV1alphaUpdateWorkerPoolRequest: A
      GoogleDevtoolsRemotebuildexecutionAdminV1alphaUpdateWorkerPoolRequest
      resource to be passed as the request body.
    name: WorkerPool resource name formatted as:
      `projects/[PROJECT_ID]/instances/[INSTANCE_ID]/workerpools/[POOL_ID]`.
      name should not be populated when creating a worker pool since it is
      provided in the `poolId` field.
  """

  googleDevtoolsRemotebuildexecutionAdminV1alphaUpdateWorkerPoolRequest = _messages.MessageField('GoogleDevtoolsRemotebuildexecutionAdminV1alphaUpdateWorkerPoolRequest', 1)
  name = _messages.StringField(2, required=True)


class RemotebuildexecutionProjectsOperationsGetRequest(_messages.Message):
  r"""A RemotebuildexecutionProjectsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
