"""Generated message classes for notebooks version v1.

Notebooks API is used to manage notebook resources in Google Cloud.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'notebooks'


class AcceleratorConfig(_messages.Message):
  r"""Definition of a hardware accelerator. Note that not all combinations of
  `type` and `core_count` are valid. See [GPUs on Compute
  Engine](https://cloud.google.com/compute/docs/gpus/#gpus-list) to find a
  valid combination. TPUs are not supported.

  Enums:
    TypeValueValuesEnum: Type of this accelerator.

  Fields:
    coreCount: Count of cores of this accelerator.
    type: Type of this accelerator.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of this accelerator.

    Values:
      ACCELERATOR_TYPE_UNSPECIFIED: Accelerator type is not specified.
      NVIDIA_TESLA_K80: Accelerator type is Nvidia Tesla K80.
      NVIDIA_TESLA_P100: Accelerator type is Nvidia Tesla P100.
      NVIDIA_TESLA_V100: Accelerator type is Nvidia Tesla V100.
      NVIDIA_TESLA_P4: Accelerator type is Nvidia Tesla P4.
      NVIDIA_TESLA_T4: Accelerator type is Nvidia Tesla T4.
      NVIDIA_TESLA_A100: Accelerator type is Nvidia Tesla A100.
      NVIDIA_L4: Accelerator type is Nvidia Tesla L4.
      NVIDIA_A100_80GB: Accelerator type is Nvidia Tesla A100 80GB.
      NVIDIA_TESLA_T4_VWS: Accelerator type is NVIDIA Tesla T4 Virtual
        Workstations.
      NVIDIA_TESLA_P100_VWS: Accelerator type is NVIDIA Tesla P100 Virtual
        Workstations.
      NVIDIA_TESLA_P4_VWS: Accelerator type is NVIDIA Tesla P4 Virtual
        Workstations.
      NVIDIA_H100_80GB: Accelerator type is NVIDIA H100 80GB.
      NVIDIA_H100_MEGA_80GB: Accelerator type is NVIDIA H100 Mega 80GB.
      TPU_V2: (Coming soon) Accelerator type is TPU V2.
      TPU_V3: (Coming soon) Accelerator type is TPU V3.
    """
    ACCELERATOR_TYPE_UNSPECIFIED = 0
    NVIDIA_TESLA_K80 = 1
    NVIDIA_TESLA_P100 = 2
    NVIDIA_TESLA_V100 = 3
    NVIDIA_TESLA_P4 = 4
    NVIDIA_TESLA_T4 = 5
    NVIDIA_TESLA_A100 = 6
    NVIDIA_L4 = 7
    NVIDIA_A100_80GB = 8
    NVIDIA_TESLA_T4_VWS = 9
    NVIDIA_TESLA_P100_VWS = 10
    NVIDIA_TESLA_P4_VWS = 11
    NVIDIA_H100_80GB = 12
    NVIDIA_H100_MEGA_80GB = 13
    TPU_V2 = 14
    TPU_V3 = 15

  coreCount = _messages.IntegerField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class BootImage(_messages.Message):
  r"""Definition of the boot image used by the Runtime. Used to facilitate
  runtime upgradeability.
  """



class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class ContainerImage(_messages.Message):
  r"""Definition of a container image for starting a notebook instance with
  the environment installed in a container.

  Fields:
    repository: Required. The path to the container image repository. For
      example: `gcr.io/{project_id}/{image_name}`
    tag: The tag of the container image. If not specified, this defaults to
      the latest tag.
  """

  repository = _messages.StringField(1)
  tag = _messages.StringField(2)


class DataprocParameters(_messages.Message):
  r"""Parameters used in Dataproc JobType executions.

  Fields:
    cluster: URI for cluster used to run Dataproc execution. Format:
      `projects/{PROJECT_ID}/regions/{REGION}/clusters/{CLUSTER_NAME}`
  """

  cluster = _messages.StringField(1)


class DiagnoseInstanceRequest(_messages.Message):
  r"""Request for creating a notebook instance diagnostic file.

  Fields:
    diagnosticConfig: Required. Defines flags that are used to run the
      diagnostic tool
    timeoutMinutes: Optional. Maximum amount of time in minutes before the
      operation times out.
  """

  diagnosticConfig = _messages.MessageField('DiagnosticConfig', 1)
  timeoutMinutes = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class DiagnoseRuntimeRequest(_messages.Message):
  r"""Request for creating a notebook instance diagnostic file.

  Fields:
    diagnosticConfig: Required. Defines flags that are used to run the
      diagnostic tool
    timeoutMinutes: Optional. Maximum amount of time in minutes before the
      operation times out.
  """

  diagnosticConfig = _messages.MessageField('DiagnosticConfig', 1)
  timeoutMinutes = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class DiagnosticConfig(_messages.Message):
  r"""Defines flags that are used to run the diagnostic tool

  Fields:
    copyHomeFilesFlagEnabled: Optional. Enables flag to copy all
      `/home/<USER>
    gcsBucket: Required. User Cloud Storage bucket location (REQUIRED). Must
      be formatted with path prefix (`gs://$GCS_BUCKET`). Permissions: User
      Managed Notebooks: - storage.buckets.writer: Must be given to the
      project's service account attached to VM. Google Managed Notebooks: -
      storage.buckets.writer: Must be given to the project's service account
      or user credentials attached to VM depending on authentication mode.
      Cloud Storage bucket Log file will be written to
      `gs://$GCS_BUCKET/$RELATIVE_PATH/$VM_DATE_$TIME.tar.gz`
    packetCaptureFlagEnabled: Optional. Enables flag to capture packets from
      the instance for 30 seconds
    relativePath: Optional. Defines the relative storage path in the Cloud
      Storage bucket where the diagnostic logs will be written: Default path
      will be the root directory of the Cloud Storage bucket
      (`gs://$GCS_BUCKET/$DATE_$TIME.tar.gz`) Example of full path where Log
      file will be written: `gs://$GCS_BUCKET/$RELATIVE_PATH/`
    repairFlagEnabled: Optional. Enables flag to repair service for instance
  """

  copyHomeFilesFlagEnabled = _messages.BooleanField(1)
  gcsBucket = _messages.StringField(2)
  packetCaptureFlagEnabled = _messages.BooleanField(3)
  relativePath = _messages.StringField(4)
  repairFlagEnabled = _messages.BooleanField(5)


class Disk(_messages.Message):
  r"""An instance-attached disk resource.

  Fields:
    autoDelete: Indicates whether the disk will be auto-deleted when the
      instance is deleted (but not when the disk is detached from the
      instance).
    boot: Indicates that this is a boot disk. The virtual machine will use the
      first partition of the disk for its root filesystem.
    deviceName: Indicates a unique device name of your choice that is
      reflected into the `/dev/disk/by-id/google-*` tree of a Linux operating
      system running within the instance. This name can be used to reference
      the device for mounting, resizing, and so on, from within the instance.
      If not specified, the server chooses a default device name to apply to
      this disk, in the form persistent-disk-x, where x is a number assigned
      by Google Compute Engine.This field is only applicable for persistent
      disks.
    diskSizeGb: Indicates the size of the disk in base-2 GB.
    guestOsFeatures: Indicates a list of features to enable on the guest
      operating system. Applicable only for bootable images. Read Enabling
      guest operating system features to see a list of available options.
    index: A zero-based index to this disk, where 0 is reserved for the boot
      disk. If you have many disks attached to an instance, each disk would
      have a unique index number.
    interface: Indicates the disk interface to use for attaching this disk,
      which is either SCSI or NVME. The default is SCSI. Persistent disks must
      always use SCSI and the request will fail if you attempt to attach a
      persistent disk in any other format than SCSI. Local SSDs can use either
      NVME or SCSI. For performance characteristics of SCSI over NVMe, see
      Local SSD performance. Valid values: * `NVME` * `SCSI`
    kind: Type of the resource. Always compute#attachedDisk for attached
      disks.
    licenses: A list of publicly visible licenses. Reserved for Google's use.
      A License represents billing and aggregate usage data for public and
      marketplace images.
    mode: The mode in which to attach this disk, either `READ_WRITE` or
      `READ_ONLY`. If not specified, the default is to attach the disk in
      `READ_WRITE` mode. Valid values: * `READ_ONLY` * `READ_WRITE`
    source: Indicates a valid partial or full URL to an existing Persistent
      Disk resource.
    type: Indicates the type of the disk, either `SCRATCH` or `PERSISTENT`.
      Valid values: * `PERSISTENT` * `SCRATCH`
  """

  autoDelete = _messages.BooleanField(1)
  boot = _messages.BooleanField(2)
  deviceName = _messages.StringField(3)
  diskSizeGb = _messages.IntegerField(4)
  guestOsFeatures = _messages.MessageField('GuestOsFeature', 5, repeated=True)
  index = _messages.IntegerField(6)
  interface = _messages.StringField(7)
  kind = _messages.StringField(8)
  licenses = _messages.StringField(9, repeated=True)
  mode = _messages.StringField(10)
  source = _messages.StringField(11)
  type = _messages.StringField(12)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EncryptionConfig(_messages.Message):
  r"""Represents a custom encryption key configuration that can be applied to
  a resource. This will encrypt all disks in Virtual Machine.

  Fields:
    kmsKey: The Cloud KMS resource identifier of the customer-managed
      encryption key used to protect a resource, such as a disks. It has the
      following format: `projects/{PROJECT_ID}/locations/{REGION}/keyRings/{KE
      Y_RING_NAME}/cryptoKeys/{KEY_NAME}`
  """

  kmsKey = _messages.StringField(1)


class Environment(_messages.Message):
  r"""Definition of a software environment that is used to start a notebook
  instance.

  Fields:
    containerImage: Use a container image to start the notebook instance.
    createTime: Output only. The time at which this environment was created.
    description: A brief description of this environment.
    displayName: Display name of this environment for the UI.
    name: Output only. Name of this environment. Format:
      `projects/{project_id}/locations/{location}/environments/{environment_id
      }`
    postStartupScript: Path to a Bash script that automatically runs after a
      notebook instance fully boots up. The path must be a URL or Cloud
      Storage path. Example: `"gs://path-to-file/file-name"`
    vmImage: Use a Compute Engine VM image to start the notebook instance.
  """

  containerImage = _messages.MessageField('ContainerImage', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  displayName = _messages.StringField(4)
  name = _messages.StringField(5)
  postStartupScript = _messages.StringField(6)
  vmImage = _messages.MessageField('VmImage', 7)


class Event(_messages.Message):
  r"""The definition of an Event for a managed / semi-managed notebook
  instance.

  Enums:
    TypeValueValuesEnum: Event type.

  Messages:
    DetailsValue: Optional. Event details. This field is used to pass event
      information.

  Fields:
    details: Optional. Event details. This field is used to pass event
      information.
    reportTime: Event report time.
    type: Event type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Event type.

    Values:
      EVENT_TYPE_UNSPECIFIED: Event is not specified.
      IDLE: The instance / runtime is idle
      HEARTBEAT: The instance / runtime is available. This event indicates
        that instance / runtime underlying compute is operational.
      HEALTH: The instance / runtime health is available. This event indicates
        that instance / runtime health information.
      MAINTENANCE: The instance / runtime is available. This event allows
        instance / runtime to send Host maintenance information to Control
        Plane. https://cloud.google.com/compute/docs/gpus/gpu-host-maintenance
    """
    EVENT_TYPE_UNSPECIFIED = 0
    IDLE = 1
    HEARTBEAT = 2
    HEALTH = 3
    MAINTENANCE = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValue(_messages.Message):
    r"""Optional. Event details. This field is used to pass event information.

    Messages:
      AdditionalProperty: An additional property for a DetailsValue object.

    Fields:
      additionalProperties: Additional properties of type DetailsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  details = _messages.MessageField('DetailsValue', 1)
  reportTime = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class Execution(_messages.Message):
  r"""The definition of a single executed notebook.

  Enums:
    StateValueValuesEnum: Output only. State of the underlying AI Platform
      job.

  Fields:
    createTime: Output only. Time the Execution was instantiated.
    description: A brief description of this execution.
    displayName: Output only. Name used for UI purposes. Name can only contain
      alphanumeric characters and underscores '_'.
    executionTemplate: execute metadata including name, hardware spec, region,
      labels, etc.
    jobUri: Output only. The URI of the external job used to execute the
      notebook.
    name: Output only. The resource name of the execute. Format:
      `projects/{project_id}/locations/{location}/executions/{execution_id}`
    outputNotebookFile: Output notebook file generated by this execution
    state: Output only. State of the underlying AI Platform job.
    updateTime: Output only. Time the Execution was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the underlying AI Platform job.

    Values:
      STATE_UNSPECIFIED: The job state is unspecified.
      QUEUED: The job has been just created and processing has not yet begun.
      PREPARING: The service is preparing to execution the job.
      RUNNING: The job is in progress.
      SUCCEEDED: The job completed successfully.
      FAILED: The job failed. `error_message` should contain the details of
        the failure.
      CANCELLING: The job is being cancelled. `error_message` should describe
        the reason for the cancellation.
      CANCELLED: The job has been cancelled. `error_message` should describe
        the reason for the cancellation.
      EXPIRED: The job has become expired (relevant to Vertex AI jobs)
        https://cloud.google.com/vertex-ai/docs/reference/rest/v1/JobState
      INITIALIZING: The Execution is being created.
    """
    STATE_UNSPECIFIED = 0
    QUEUED = 1
    PREPARING = 2
    RUNNING = 3
    SUCCEEDED = 4
    FAILED = 5
    CANCELLING = 6
    CANCELLED = 7
    EXPIRED = 8
    INITIALIZING = 9

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  executionTemplate = _messages.MessageField('ExecutionTemplate', 4)
  jobUri = _messages.StringField(5)
  name = _messages.StringField(6)
  outputNotebookFile = _messages.StringField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  updateTime = _messages.StringField(9)


class ExecutionTemplate(_messages.Message):
  r"""The description a notebook execution workload.

  Enums:
    JobTypeValueValuesEnum: The type of Job to be used on this execution.
    ScaleTierValueValuesEnum: Required. Scale tier of the hardware used for
      notebook execution. DEPRECATED Will be discontinued. As right now only
      CUSTOM is supported.

  Messages:
    LabelsValue: Labels for execution. If execution is scheduled, a field
      included will be 'nbs-scheduled'. Otherwise, it is an immediate
      execution, and an included field will be 'nbs-immediate'. Use fields to
      efficiently index between various types of executions.

  Fields:
    acceleratorConfig: Configuration (count and accelerator type) for hardware
      running notebook execution.
    containerImageUri: Container Image URI to a DLVM Example:
      'gcr.io/deeplearning-platform-release/base-cu100' More examples can be
      found at: https://cloud.google.com/ai-platform/deep-learning-
      containers/docs/choosing-container
    dataprocParameters: Parameters used in Dataproc JobType executions.
    inputNotebookFile: Path to the notebook file to execute. Must be in a
      Google Cloud Storage bucket. Format:
      `gs://{bucket_name}/{folder}/{notebook_file_name}` Ex:
      `gs://notebook_user/scheduled_notebooks/sentiment_notebook.ipynb`
    jobType: The type of Job to be used on this execution.
    kernelSpec: Name of the kernel spec to use. This must be specified if the
      kernel spec name on the execution target does not match the name in the
      input notebook file.
    labels: Labels for execution. If execution is scheduled, a field included
      will be 'nbs-scheduled'. Otherwise, it is an immediate execution, and an
      included field will be 'nbs-immediate'. Use fields to efficiently index
      between various types of executions.
    masterType: Specifies the type of virtual machine to use for your training
      job's master worker. You must specify this field when `scaleTier` is set
      to `CUSTOM`. You can use certain Compute Engine machine types directly
      in this field. The following types are supported: - `n1-standard-4` -
      `n1-standard-8` - `n1-standard-16` - `n1-standard-32` - `n1-standard-64`
      - `n1-standard-96` - `n1-highmem-2` - `n1-highmem-4` - `n1-highmem-8` -
      `n1-highmem-16` - `n1-highmem-32` - `n1-highmem-64` - `n1-highmem-96` -
      `n1-highcpu-16` - `n1-highcpu-32` - `n1-highcpu-64` - `n1-highcpu-96`
      Alternatively, you can use the following legacy machine types: -
      `standard` - `large_model` - `complex_model_s` - `complex_model_m` -
      `complex_model_l` - `standard_gpu` - `complex_model_m_gpu` -
      `complex_model_l_gpu` - `standard_p100` - `complex_model_m_p100` -
      `standard_v100` - `large_model_v100` - `complex_model_m_v100` -
      `complex_model_l_v100` Finally, if you want to use a TPU for training,
      specify `cloud_tpu` in this field. Learn more about the [special
      configuration options for training with
      TPU](https://cloud.google.com/ai-platform/training/docs/using-
      tpus#configuring_a_custom_tpu_machine).
    outputNotebookFolder: Path to the notebook folder to write to. Must be in
      a Google Cloud Storage bucket path. Format:
      `gs://{bucket_name}/{folder}` Ex:
      `gs://notebook_user/scheduled_notebooks`
    parameters: Parameters used within the 'input_notebook_file' notebook.
    paramsYamlFile: Parameters to be overridden in the notebook during
      execution. Ref https://papermill.readthedocs.io/en/latest/usage-
      parameterize.html on how to specifying parameters in the input notebook
      and pass them here in an YAML file. Ex:
      `gs://notebook_user/scheduled_notebooks/sentiment_notebook_params.yaml`
    scaleTier: Required. Scale tier of the hardware used for notebook
      execution. DEPRECATED Will be discontinued. As right now only CUSTOM is
      supported.
    serviceAccount: The email address of a service account to use when running
      the execution. You must have the `iam.serviceAccounts.actAs` permission
      for the specified service account.
    tensorboard: The name of a Vertex AI [Tensorboard] resource to which this
      execution will upload Tensorboard logs. Format:
      `projects/{project}/locations/{location}/tensorboards/{tensorboard}`
    vertexAiParameters: Parameters used in Vertex AI JobType executions.
  """

  class JobTypeValueValuesEnum(_messages.Enum):
    r"""The type of Job to be used on this execution.

    Values:
      JOB_TYPE_UNSPECIFIED: No type specified.
      VERTEX_AI: Custom Job in `aiplatform.googleapis.com`. Default value for
        an execution.
      DATAPROC: Run execution on a cluster with Dataproc as a job. https://clo
        ud.google.com/dataproc/docs/reference/rest/v1/projects.regions.jobs
    """
    JOB_TYPE_UNSPECIFIED = 0
    VERTEX_AI = 1
    DATAPROC = 2

  class ScaleTierValueValuesEnum(_messages.Enum):
    r"""Required. Scale tier of the hardware used for notebook execution.
    DEPRECATED Will be discontinued. As right now only CUSTOM is supported.

    Values:
      SCALE_TIER_UNSPECIFIED: Unspecified Scale Tier.
      BASIC: A single worker instance. This tier is suitable for learning how
        to use Cloud ML, and for experimenting with new models using small
        datasets.
      STANDARD_1: Many workers and a few parameter servers.
      PREMIUM_1: A large number of workers with many parameter servers.
      BASIC_GPU: A single worker instance with a K80 GPU.
      BASIC_TPU: A single worker instance with a Cloud TPU.
      CUSTOM: The CUSTOM tier is not a set tier, but rather enables you to use
        your own cluster specification. When you use this tier, set values to
        configure your processing cluster according to these guidelines: * You
        _must_ set `ExecutionTemplate.masterType` to specify the type of
        machine to use for your master node. This is the only required
        setting.
    """
    SCALE_TIER_UNSPECIFIED = 0
    BASIC = 1
    STANDARD_1 = 2
    PREMIUM_1 = 3
    BASIC_GPU = 4
    BASIC_TPU = 5
    CUSTOM = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels for execution. If execution is scheduled, a field included will
    be 'nbs-scheduled'. Otherwise, it is an immediate execution, and an
    included field will be 'nbs-immediate'. Use fields to efficiently index
    between various types of executions.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  acceleratorConfig = _messages.MessageField('SchedulerAcceleratorConfig', 1)
  containerImageUri = _messages.StringField(2)
  dataprocParameters = _messages.MessageField('DataprocParameters', 3)
  inputNotebookFile = _messages.StringField(4)
  jobType = _messages.EnumField('JobTypeValueValuesEnum', 5)
  kernelSpec = _messages.StringField(6)
  labels = _messages.MessageField('LabelsValue', 7)
  masterType = _messages.StringField(8)
  outputNotebookFolder = _messages.StringField(9)
  parameters = _messages.StringField(10)
  paramsYamlFile = _messages.StringField(11)
  scaleTier = _messages.EnumField('ScaleTierValueValuesEnum', 12)
  serviceAccount = _messages.StringField(13)
  tensorboard = _messages.StringField(14)
  vertexAiParameters = _messages.MessageField('VertexAIParameters', 15)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GetInstanceHealthResponse(_messages.Message):
  r"""Response for checking if a notebook instance is healthy.

  Enums:
    HealthStateValueValuesEnum: Output only. Runtime health_state.

  Messages:
    HealthInfoValue: Output only. Additional information about instance
      health. Example: healthInfo": { "docker_proxy_agent_status": "1",
      "docker_status": "1", "jupyterlab_api_status": "-1",
      "jupyterlab_status": "-1", "updated": "2020-10-18 09:40:03.573409" }

  Fields:
    healthInfo: Output only. Additional information about instance health.
      Example: healthInfo": { "docker_proxy_agent_status": "1",
      "docker_status": "1", "jupyterlab_api_status": "-1",
      "jupyterlab_status": "-1", "updated": "2020-10-18 09:40:03.573409" }
    healthState: Output only. Runtime health_state.
  """

  class HealthStateValueValuesEnum(_messages.Enum):
    r"""Output only. Runtime health_state.

    Values:
      HEALTH_STATE_UNSPECIFIED: The instance substate is unknown.
      HEALTHY: The instance is known to be in an healthy state (for example,
        critical daemons are running) Applies to ACTIVE state.
      UNHEALTHY: The instance is known to be in an unhealthy state (for
        example, critical daemons are not running) Applies to ACTIVE state.
      AGENT_NOT_INSTALLED: The instance has not installed health monitoring
        agent. Applies to ACTIVE state.
      AGENT_NOT_RUNNING: The instance health monitoring agent is not running.
        Applies to ACTIVE state.
    """
    HEALTH_STATE_UNSPECIFIED = 0
    HEALTHY = 1
    UNHEALTHY = 2
    AGENT_NOT_INSTALLED = 3
    AGENT_NOT_RUNNING = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class HealthInfoValue(_messages.Message):
    r"""Output only. Additional information about instance health. Example:
    healthInfo": { "docker_proxy_agent_status": "1", "docker_status": "1",
    "jupyterlab_api_status": "-1", "jupyterlab_status": "-1", "updated":
    "2020-10-18 09:40:03.573409" }

    Messages:
      AdditionalProperty: An additional property for a HealthInfoValue object.

    Fields:
      additionalProperties: Additional properties of type HealthInfoValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a HealthInfoValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  healthInfo = _messages.MessageField('HealthInfoValue', 1)
  healthState = _messages.EnumField('HealthStateValueValuesEnum', 2)


class GuestOsFeature(_messages.Message):
  r"""Guest OS features for boot disk.

  Fields:
    type: The ID of a supported feature. Read Enabling guest operating system
      features to see a list of available options. Valid values: *
      `FEATURE_TYPE_UNSPECIFIED` * `MULTI_IP_SUBNET` * `SECURE_BOOT` *
      `UEFI_COMPATIBLE` * `VIRTIO_SCSI_MULTIQUEUE` * `WINDOWS`
  """

  type = _messages.StringField(1)


class Instance(_messages.Message):
  r"""The definition of a notebook instance.

  Enums:
    BootDiskTypeValueValuesEnum: Input only. The type of the boot disk
      attached to this instance, defaults to standard persistent disk
      (`PD_STANDARD`).
    DataDiskTypeValueValuesEnum: Input only. The type of the data disk
      attached to this instance, defaults to standard persistent disk
      (`PD_STANDARD`).
    DiskEncryptionValueValuesEnum: Input only. Disk encryption method used on
      the boot and data disks, defaults to GMEK.
    NicTypeValueValuesEnum: Optional. The type of vNIC to be used on this
      interface. This may be gVNIC or VirtioNet.
    StateValueValuesEnum: Output only. The state of this instance.

  Messages:
    LabelsValue: Labels to apply to this instance. These can be later modified
      by the setLabels method.
    MetadataValue: Custom metadata to apply to this instance. For example, to
      specify a Cloud Storage bucket for automatic backup, you can use the
      `gcs-data-bucket` metadata tag. Format: `"--metadata=gcs-data-
      bucket=BUCKET"`.

  Fields:
    acceleratorConfig: The hardware accelerator used on this instance. If you
      use accelerators, make sure that your configuration has [enough vCPUs
      and memory to support the `machine_type` you have
      selected](https://cloud.google.com/compute/docs/gpus/#gpus-list).
    bootDiskSizeGb: Input only. The size of the boot disk in GB attached to
      this instance, up to a maximum of 64000 GB (64 TB). The minimum
      recommended value is 100 GB. If not specified, this defaults to 100.
    bootDiskType: Input only. The type of the boot disk attached to this
      instance, defaults to standard persistent disk (`PD_STANDARD`).
    canIpForward: Optional. Flag to enable ip forwarding or not, default
      false/off. https://cloud.google.com/vpc/docs/using-routes#canipforward
    containerImage: Use a container image to start the notebook instance.
    createTime: Output only. Instance creation time.
    creator: Output only. Email address of entity that sent original
      CreateInstance request.
    customGpuDriverPath: Specify a custom Cloud Storage path where the GPU
      driver is stored. If not specified, we'll automatically choose from
      official GPU drivers.
    dataDiskSizeGb: Input only. The size of the data disk in GB attached to
      this instance, up to a maximum of 64000 GB (64 TB). You can choose the
      size of the data disk based on how big your notebooks and data are. If
      not specified, this defaults to 100.
    dataDiskType: Input only. The type of the data disk attached to this
      instance, defaults to standard persistent disk (`PD_STANDARD`).
    diskEncryption: Input only. Disk encryption method used on the boot and
      data disks, defaults to GMEK.
    disks: Output only. Attached disks to notebook instance.
    installGpuDriver: Whether the end user authorizes Google Cloud to install
      GPU driver on this instance. If this field is empty or set to false, the
      GPU driver won't be installed. Only applicable to instances with GPUs.
    instanceMigrationEligibility: Output only. Checks how feasible a migration
      from UmN to WbI is.
    instanceOwners: Input only. The owner of this instance after creation.
      Format: `<EMAIL>` Currently supports one owner only. If not
      specified, all of the service account users of your VM instance's
      service account can use the instance.
    kmsKey: Input only. The KMS key used to encrypt the disks, only applicable
      if disk_encryption is CMEK. Format: `projects/{project_id}/locations/{lo
      cation}/keyRings/{key_ring_id}/cryptoKeys/{key_id}` Learn more about
      [using your own encryption keys](/kms/docs/quickstart).
    labels: Labels to apply to this instance. These can be later modified by
      the setLabels method.
    machineType: Required. The [Compute Engine machine
      type](https://cloud.google.com/compute/docs/machine-resource) of this
      instance.
    metadata: Custom metadata to apply to this instance. For example, to
      specify a Cloud Storage bucket for automatic backup, you can use the
      `gcs-data-bucket` metadata tag. Format: `"--metadata=gcs-data-
      bucket=BUCKET"`.
    migrated: Output only. Bool indicating whether this notebook has been
      migrated to a Workbench Instance
    name: Output only. The name of this notebook instance. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    network: The name of the VPC that this instance is in. Format:
      `projects/{project_id}/global/networks/{network_id}`
    nicType: Optional. The type of vNIC to be used on this interface. This may
      be gVNIC or VirtioNet.
    noProxyAccess: If true, the notebook instance will not register with the
      proxy.
    noPublicIp: If true, no external IP will be assigned to this instance.
    noRemoveDataDisk: Input only. If true, the data disk will not be auto
      deleted when deleting the instance.
    postStartupScript: Path to a Bash script that automatically runs after a
      notebook instance fully boots up. The path must be a URL or Cloud
      Storage path (`gs://path-to-file/file-name`).
    proxyUri: Output only. The proxy endpoint that is used to access the
      Jupyter notebook.
    reservationAffinity: Optional. The optional reservation affinity. Setting
      this field will apply the specified [Zonal Compute
      Reservation](https://cloud.google.com/compute/docs/instances/reserving-
      zonal-resources) to this notebook instance.
    serviceAccount: The service account on this instance, giving access to
      other Google Cloud services. You can use any service account within the
      same project, but you must have the service account user permission to
      use the instance. If not specified, the [Compute Engine default service
      account](https://cloud.google.com/compute/docs/access/service-
      accounts#default_service_account) is used.
    serviceAccountScopes: Optional. The URIs of service account scopes to be
      included in Compute Engine instances. If not specified, the following
      [scopes](https://cloud.google.com/compute/docs/access/service-
      accounts#accesscopesiam) are defined: -
      https://www.googleapis.com/auth/cloud-platform -
      https://www.googleapis.com/auth/userinfo.email If not using default
      scopes, you need at least: https://www.googleapis.com/auth/compute
    shieldedInstanceConfig: Optional. Shielded VM configuration. [Images using
      supported Shielded VM
      features](https://cloud.google.com/compute/docs/instances/modifying-
      shielded-vm).
    state: Output only. The state of this instance.
    subnet: The name of the subnet that this instance is in. Format:
      `projects/{project_id}/regions/{region}/subnetworks/{subnetwork_id}`
    tags: Optional. The Compute Engine network tags to add to runtime (see
      [Add network tags](https://cloud.google.com/vpc/docs/add-remove-network-
      tags)).
    updateTime: Output only. Instance update time.
    upgradeHistory: The upgrade history of this instance.
    vmImage: Use a Compute Engine VM image to start the notebook instance.
  """

  class BootDiskTypeValueValuesEnum(_messages.Enum):
    r"""Input only. The type of the boot disk attached to this instance,
    defaults to standard persistent disk (`PD_STANDARD`).

    Values:
      DISK_TYPE_UNSPECIFIED: Disk type not set.
      PD_STANDARD: Standard persistent disk type.
      PD_SSD: SSD persistent disk type.
      PD_BALANCED: Balanced persistent disk type.
      PD_EXTREME: Extreme persistent disk type.
    """
    DISK_TYPE_UNSPECIFIED = 0
    PD_STANDARD = 1
    PD_SSD = 2
    PD_BALANCED = 3
    PD_EXTREME = 4

  class DataDiskTypeValueValuesEnum(_messages.Enum):
    r"""Input only. The type of the data disk attached to this instance,
    defaults to standard persistent disk (`PD_STANDARD`).

    Values:
      DISK_TYPE_UNSPECIFIED: Disk type not set.
      PD_STANDARD: Standard persistent disk type.
      PD_SSD: SSD persistent disk type.
      PD_BALANCED: Balanced persistent disk type.
      PD_EXTREME: Extreme persistent disk type.
    """
    DISK_TYPE_UNSPECIFIED = 0
    PD_STANDARD = 1
    PD_SSD = 2
    PD_BALANCED = 3
    PD_EXTREME = 4

  class DiskEncryptionValueValuesEnum(_messages.Enum):
    r"""Input only. Disk encryption method used on the boot and data disks,
    defaults to GMEK.

    Values:
      DISK_ENCRYPTION_UNSPECIFIED: Disk encryption is not specified.
      GMEK: Use Google managed encryption keys to encrypt the boot disk.
      CMEK: Use customer managed encryption keys to encrypt the boot disk.
    """
    DISK_ENCRYPTION_UNSPECIFIED = 0
    GMEK = 1
    CMEK = 2

  class NicTypeValueValuesEnum(_messages.Enum):
    r"""Optional. The type of vNIC to be used on this interface. This may be
    gVNIC or VirtioNet.

    Values:
      UNSPECIFIED_NIC_TYPE: No type specified.
      VIRTIO_NET: VIRTIO
      GVNIC: GVNIC
    """
    UNSPECIFIED_NIC_TYPE = 0
    VIRTIO_NET = 1
    GVNIC = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of this instance.

    Values:
      STATE_UNSPECIFIED: State is not specified.
      STARTING: The control logic is starting the instance.
      PROVISIONING: The control logic is installing required frameworks and
        registering the instance with notebook proxy
      ACTIVE: The instance is running.
      STOPPING: The control logic is stopping the instance.
      STOPPED: The instance is stopped.
      DELETED: The instance is deleted.
      UPGRADING: The instance is upgrading.
      INITIALIZING: The instance is being created.
      REGISTERING: The instance is getting registered.
      SUSPENDING: The instance is suspending.
      SUSPENDED: The instance is suspended.
    """
    STATE_UNSPECIFIED = 0
    STARTING = 1
    PROVISIONING = 2
    ACTIVE = 3
    STOPPING = 4
    STOPPED = 5
    DELETED = 6
    UPGRADING = 7
    INITIALIZING = 8
    REGISTERING = 9
    SUSPENDING = 10
    SUSPENDED = 11

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels to apply to this instance. These can be later modified by the
    setLabels method.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Custom metadata to apply to this instance. For example, to specify a
    Cloud Storage bucket for automatic backup, you can use the `gcs-data-
    bucket` metadata tag. Format: `"--metadata=gcs-data-bucket=BUCKET"`.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Additional properties of type MetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  acceleratorConfig = _messages.MessageField('AcceleratorConfig', 1)
  bootDiskSizeGb = _messages.IntegerField(2)
  bootDiskType = _messages.EnumField('BootDiskTypeValueValuesEnum', 3)
  canIpForward = _messages.BooleanField(4)
  containerImage = _messages.MessageField('ContainerImage', 5)
  createTime = _messages.StringField(6)
  creator = _messages.StringField(7)
  customGpuDriverPath = _messages.StringField(8)
  dataDiskSizeGb = _messages.IntegerField(9)
  dataDiskType = _messages.EnumField('DataDiskTypeValueValuesEnum', 10)
  diskEncryption = _messages.EnumField('DiskEncryptionValueValuesEnum', 11)
  disks = _messages.MessageField('Disk', 12, repeated=True)
  installGpuDriver = _messages.BooleanField(13)
  instanceMigrationEligibility = _messages.MessageField('InstanceMigrationEligibility', 14)
  instanceOwners = _messages.StringField(15, repeated=True)
  kmsKey = _messages.StringField(16)
  labels = _messages.MessageField('LabelsValue', 17)
  machineType = _messages.StringField(18)
  metadata = _messages.MessageField('MetadataValue', 19)
  migrated = _messages.BooleanField(20)
  name = _messages.StringField(21)
  network = _messages.StringField(22)
  nicType = _messages.EnumField('NicTypeValueValuesEnum', 23)
  noProxyAccess = _messages.BooleanField(24)
  noPublicIp = _messages.BooleanField(25)
  noRemoveDataDisk = _messages.BooleanField(26)
  postStartupScript = _messages.StringField(27)
  proxyUri = _messages.StringField(28)
  reservationAffinity = _messages.MessageField('ReservationAffinity', 29)
  serviceAccount = _messages.StringField(30)
  serviceAccountScopes = _messages.StringField(31, repeated=True)
  shieldedInstanceConfig = _messages.MessageField('ShieldedInstanceConfig', 32)
  state = _messages.EnumField('StateValueValuesEnum', 33)
  subnet = _messages.StringField(34)
  tags = _messages.StringField(35, repeated=True)
  updateTime = _messages.StringField(36)
  upgradeHistory = _messages.MessageField('UpgradeHistoryEntry', 37, repeated=True)
  vmImage = _messages.MessageField('VmImage', 38)


class InstanceConfig(_messages.Message):
  r"""Notebook instance configurations that can be updated.

  Fields:
    enableHealthMonitoring: Verifies core internal services are running.
    notebookUpgradeSchedule: Cron expression in UTC timezone, used to schedule
      instance auto upgrade. Please follow the [cron
      format](https://en.wikipedia.org/wiki/Cron).
  """

  enableHealthMonitoring = _messages.BooleanField(1)
  notebookUpgradeSchedule = _messages.StringField(2)


class InstanceMigrationEligibility(_messages.Message):
  r"""InstanceMigrationEligibility represents the feasibility information of a
  migration from UmN to WbI.

  Enums:
    ErrorsValueListEntryValuesEnum:
    WarningsValueListEntryValuesEnum:

  Fields:
    errors: Output only. Certain configurations make the UmN ineligible for an
      automatic migration. A manual migration is required.
    warnings: Output only. Certain configurations will be defaulted during the
      migration.
  """

  class ErrorsValueListEntryValuesEnum(_messages.Enum):
    r"""ErrorsValueListEntryValuesEnum enum type.

    Values:
      ERROR_UNSPECIFIED: Default type.
      DATAPROC_HUB: The UmN uses Dataproc Hub and cannot be migrated.
    """
    ERROR_UNSPECIFIED = 0
    DATAPROC_HUB = 1

  class WarningsValueListEntryValuesEnum(_messages.Enum):
    r"""WarningsValueListEntryValuesEnum enum type.

    Values:
      WARNING_UNSPECIFIED: Default type.
      UNSUPPORTED_MACHINE_TYPE: The UmN uses an machine type that's
        unsupported in WbI. It will be migrated with the default machine type
        e2-standard-4. Users can change the machine type after the migration.
      UNSUPPORTED_ACCELERATOR_TYPE: The UmN uses an accelerator type that's
        unsupported in WbI. It will be migrated without an accelerator. User
        can attach an accelerator after the migration.
      UNSUPPORTED_OS: The UmN uses an operating system that's unsupported in
        WbI (e.g. Debian 10, Ubuntu). It will be replaced with Debian 11 in
        WbI.
      NO_REMOVE_DATA_DISK: This UmN is configured with no_remove_data_disk,
        which is no longer available in WbI.
      GCS_BACKUP: This UmN is configured with the Cloud Storage backup
        feature, which is no longer available in WbI.
      POST_STARTUP_SCRIPT: This UmN is configured with a post startup script.
        Please optionally provide the `post_startup_script_option` for the
        migration.
    """
    WARNING_UNSPECIFIED = 0
    UNSUPPORTED_MACHINE_TYPE = 1
    UNSUPPORTED_ACCELERATOR_TYPE = 2
    UNSUPPORTED_OS = 3
    NO_REMOVE_DATA_DISK = 4
    GCS_BACKUP = 5
    POST_STARTUP_SCRIPT = 6

  errors = _messages.EnumField('ErrorsValueListEntryValuesEnum', 1, repeated=True)
  warnings = _messages.EnumField('WarningsValueListEntryValuesEnum', 2, repeated=True)


class IsInstanceUpgradeableResponse(_messages.Message):
  r"""Response for checking if a notebook instance is upgradeable.

  Fields:
    upgradeImage: The new image self link this instance will be upgraded to if
      calling the upgrade endpoint. This field will only be populated if field
      upgradeable is true.
    upgradeInfo: Additional information about upgrade.
    upgradeVersion: The version this instance will be upgraded to if calling
      the upgrade endpoint. This field will only be populated if field
      upgradeable is true.
    upgradeable: If an instance is upgradeable.
  """

  upgradeImage = _messages.StringField(1)
  upgradeInfo = _messages.StringField(2)
  upgradeVersion = _messages.StringField(3)
  upgradeable = _messages.BooleanField(4)


class ListEnvironmentsResponse(_messages.Message):
  r"""Response for listing environments.

  Fields:
    environments: A list of returned environments.
    nextPageToken: A page token that can be used to continue listing from the
      last result in the next list call.
    unreachable: Locations that could not be reached.
  """

  environments = _messages.MessageField('Environment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListExecutionsResponse(_messages.Message):
  r"""Response for listing scheduled notebook executions

  Fields:
    executions: A list of returned instances.
    nextPageToken: Page token that can be used to continue listing from the
      last result in the next list call.
    unreachable: Executions IDs that could not be reached. For example:
      ['projects/{project_id}/location/{location}/executions/imagenet_test1',
      'projects/{project_id}/location/{location}/executions/classifier_train1'
      ]
  """

  executions = _messages.MessageField('Execution', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListInstancesResponse(_messages.Message):
  r"""Response for listing notebook instances.

  Fields:
    instances: A list of returned instances.
    nextPageToken: Page token that can be used to continue listing from the
      last result in the next list call.
    unreachable: Locations that could not be reached. For example, `['us-
      west1-a', 'us-central1-b']`. A ListInstancesResponse will only contain
      either instances or unreachables,
  """

  instances = _messages.MessageField('Instance', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListRuntimesResponse(_messages.Message):
  r"""Response for listing Managed Notebook Runtimes.

  Fields:
    nextPageToken: Page token that can be used to continue listing from the
      last result in the next list call.
    runtimes: A list of returned Runtimes.
    unreachable: Locations that could not be reached. For example, `['us-
      west1', 'us-central1']`. A ListRuntimesResponse will only contain either
      runtimes or unreachables,
  """

  nextPageToken = _messages.StringField(1)
  runtimes = _messages.MessageField('Runtime', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListSchedulesResponse(_messages.Message):
  r"""Response for listing scheduled notebook job.

  Fields:
    nextPageToken: Page token that can be used to continue listing from the
      last result in the next list call.
    schedules: A list of returned instances.
    unreachable: Schedules that could not be reached. For example:
      ['projects/{project_id}/location/{location}/schedules/monthly_digest',
      'projects/{project_id}/location/{location}/schedules/weekly_sentiment']
  """

  nextPageToken = _messages.StringField(1)
  schedules = _messages.MessageField('Schedule', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class LocalDisk(_messages.Message):
  r"""A Local attached disk resource.

  Fields:
    autoDelete: Optional. Output only. Specifies whether the disk will be
      auto-deleted when the instance is deleted (but not when the disk is
      detached from the instance).
    boot: Optional. Output only. Indicates that this is a boot disk. The
      virtual machine will use the first partition of the disk for its root
      filesystem.
    deviceName: Optional. Output only. Specifies a unique device name of your
      choice that is reflected into the `/dev/disk/by-id/google-*` tree of a
      Linux operating system running within the instance. This name can be
      used to reference the device for mounting, resizing, and so on, from
      within the instance. If not specified, the server chooses a default
      device name to apply to this disk, in the form persistent-disk-x, where
      x is a number assigned by Google Compute Engine. This field is only
      applicable for persistent disks.
    guestOsFeatures: Output only. Indicates a list of features to enable on
      the guest operating system. Applicable only for bootable images. Read
      Enabling guest operating system features to see a list of available
      options.
    index: Output only. A zero-based index to this disk, where 0 is reserved
      for the boot disk. If you have many disks attached to an instance, each
      disk would have a unique index number.
    initializeParams: Input only. Specifies the parameters for a new disk that
      will be created alongside the new instance. Use initialization
      parameters to create boot disks or local SSDs attached to the new
      instance. This property is mutually exclusive with the source property;
      you can only define one or the other, but not both.
    interface: Specifies the disk interface to use for attaching this disk,
      which is either SCSI or NVME. The default is SCSI. Persistent disks must
      always use SCSI and the request will fail if you attempt to attach a
      persistent disk in any other format than SCSI. Local SSDs can use either
      NVME or SCSI. For performance characteristics of SCSI over NVMe, see
      Local SSD performance. Valid values: * `NVME` * `SCSI`
    kind: Output only. Type of the resource. Always compute#attachedDisk for
      attached disks.
    licenses: Output only. Any valid publicly visible licenses.
    mode: The mode in which to attach this disk, either `READ_WRITE` or
      `READ_ONLY`. If not specified, the default is to attach the disk in
      `READ_WRITE` mode. Valid values: * `READ_ONLY` * `READ_WRITE`
    source: Specifies a valid partial or full URL to an existing Persistent
      Disk resource.
    type: Specifies the type of the disk, either `SCRATCH` or `PERSISTENT`. If
      not specified, the default is `PERSISTENT`. Valid values: * `PERSISTENT`
      * `SCRATCH`
  """

  autoDelete = _messages.BooleanField(1)
  boot = _messages.BooleanField(2)
  deviceName = _messages.StringField(3)
  guestOsFeatures = _messages.MessageField('RuntimeGuestOsFeature', 4, repeated=True)
  index = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  initializeParams = _messages.MessageField('LocalDiskInitializeParams', 6)
  interface = _messages.StringField(7)
  kind = _messages.StringField(8)
  licenses = _messages.StringField(9, repeated=True)
  mode = _messages.StringField(10)
  source = _messages.StringField(11)
  type = _messages.StringField(12)


class LocalDiskInitializeParams(_messages.Message):
  r"""Input only. Specifies the parameters for a new disk that will be created
  alongside the new instance. Use initialization parameters to create boot
  disks or local SSDs attached to the new runtime. This property is mutually
  exclusive with the source property; you can only define one or the other,
  but not both.

  Enums:
    DiskTypeValueValuesEnum: Input only. The type of the boot disk attached to
      this instance, defaults to standard persistent disk (`PD_STANDARD`).

  Messages:
    LabelsValue: Optional. Labels to apply to this disk. These can be later
      modified by the disks.setLabels method. This field is only applicable
      for persistent disks.

  Fields:
    description: Optional. Provide this property when creating the disk.
    diskName: Optional. Specifies the disk name. If not specified, the default
      is to use the name of the instance. If the disk with the instance name
      exists already in the given zone/region, a new name will be
      automatically generated.
    diskSizeGb: Optional. Specifies the size of the disk in base-2 GB. If not
      specified, the disk will be the same size as the image (usually 10GB).
      If specified, the size must be equal to or larger than 10GB. Default 100
      GB.
    diskType: Input only. The type of the boot disk attached to this instance,
      defaults to standard persistent disk (`PD_STANDARD`).
    labels: Optional. Labels to apply to this disk. These can be later
      modified by the disks.setLabels method. This field is only applicable
      for persistent disks.
  """

  class DiskTypeValueValuesEnum(_messages.Enum):
    r"""Input only. The type of the boot disk attached to this instance,
    defaults to standard persistent disk (`PD_STANDARD`).

    Values:
      DISK_TYPE_UNSPECIFIED: Disk type not set.
      PD_STANDARD: Standard persistent disk type.
      PD_SSD: SSD persistent disk type.
      PD_BALANCED: Balanced persistent disk type.
      PD_EXTREME: Extreme persistent disk type.
    """
    DISK_TYPE_UNSPECIFIED = 0
    PD_STANDARD = 1
    PD_SSD = 2
    PD_BALANCED = 3
    PD_EXTREME = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels to apply to this disk. These can be later modified by
    the disks.setLabels method. This field is only applicable for persistent
    disks.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  description = _messages.StringField(1)
  diskName = _messages.StringField(2)
  diskSizeGb = _messages.IntegerField(3)
  diskType = _messages.EnumField('DiskTypeValueValuesEnum', 4)
  labels = _messages.MessageField('LabelsValue', 5)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class MigrateInstanceRequest(_messages.Message):
  r"""Request for migrating a User-Managed Notebook to Workbench Instances.

  Enums:
    PostStartupScriptOptionValueValuesEnum: Optional. Specifies the behavior
      of post startup script during migration.

  Fields:
    postStartupScriptOption: Optional. Specifies the behavior of post startup
      script during migration.
  """

  class PostStartupScriptOptionValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies the behavior of post startup script during
    migration.

    Values:
      POST_STARTUP_SCRIPT_OPTION_UNSPECIFIED: Post startup script option is
        not specified. Default is POST_STARTUP_SCRIPT_OPTION_SKIP.
      POST_STARTUP_SCRIPT_OPTION_SKIP: Not migrate the post startup script to
        the new Workbench Instance.
      POST_STARTUP_SCRIPT_OPTION_RERUN: Redownload and rerun the same post
        startup script as the User-Managed Notebook.
    """
    POST_STARTUP_SCRIPT_OPTION_UNSPECIFIED = 0
    POST_STARTUP_SCRIPT_OPTION_SKIP = 1
    POST_STARTUP_SCRIPT_OPTION_RERUN = 2

  postStartupScriptOption = _messages.EnumField('PostStartupScriptOptionValueValuesEnum', 1)


class MigrateRuntimeRequest(_messages.Message):
  r"""Request for migrating a Runtime to a Workbench Instance.

  Enums:
    PostStartupScriptOptionValueValuesEnum: Optional. Specifies the behavior
      of post startup script during migration.

  Fields:
    network: Optional. Name of the VPC that the new Instance is in. This is
      required if the Runtime uses google-managed network. If the Runtime uses
      customer-owned network, it will reuse the same VPC, and this field must
      be empty. Format: `projects/{project_id}/global/networks/{network_id}`
    postStartupScriptOption: Optional. Specifies the behavior of post startup
      script during migration.
    requestId: Optional. Idempotent request UUID.
    serviceAccount: Optional. The service account to be included in the
      Compute Engine instance of the new Workbench Instance when the Runtime
      uses "single user only" mode for permission. If not specified, the
      [Compute Engine default service
      account](https://cloud.google.com/compute/docs/access/service-
      accounts#default_service_account) is used. When the Runtime uses service
      account mode for permission, it will reuse the same service account, and
      this field must be empty.
    subnet: Optional. Name of the subnet that the new Instance is in. This is
      required if the Runtime uses google-managed network. If the Runtime uses
      customer-owned network, it will reuse the same subnet, and this field
      must be empty. Format:
      `projects/{project_id}/regions/{region}/subnetworks/{subnetwork_id}`
  """

  class PostStartupScriptOptionValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies the behavior of post startup script during
    migration.

    Values:
      POST_STARTUP_SCRIPT_OPTION_UNSPECIFIED: Post startup script option is
        not specified. Default is POST_STARTUP_SCRIPT_OPTION_SKIP.
      POST_STARTUP_SCRIPT_OPTION_SKIP: Not migrate the post startup script to
        the new Workbench Instance.
      POST_STARTUP_SCRIPT_OPTION_RERUN: Redownload and rerun the same post
        startup script as the Google-Managed Notebook.
    """
    POST_STARTUP_SCRIPT_OPTION_UNSPECIFIED = 0
    POST_STARTUP_SCRIPT_OPTION_SKIP = 1
    POST_STARTUP_SCRIPT_OPTION_RERUN = 2

  network = _messages.StringField(1)
  postStartupScriptOption = _messages.EnumField('PostStartupScriptOptionValueValuesEnum', 2)
  requestId = _messages.StringField(3)
  serviceAccount = _messages.StringField(4)
  subnet = _messages.StringField(5)


class NotebooksProjectsLocationsEnvironmentsCreateRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsEnvironmentsCreateRequest object.

  Fields:
    environment: A Environment resource to be passed as the request body.
    environmentId: Required. User-defined unique ID of this environment. The
      `environment_id` must be 1 to 63 characters long and contain only
      lowercase letters, numeric characters, and dashes. The first character
      must be a lowercase letter and the last character cannot be a dash.
    parent: Required. Format: `projects/{project_id}/locations/{location}`
  """

  environment = _messages.MessageField('Environment', 1)
  environmentId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NotebooksProjectsLocationsEnvironmentsDeleteRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsEnvironmentsDeleteRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/environments/{environment_id
      }`
  """

  name = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsEnvironmentsGetRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsEnvironmentsGetRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/environments/{environment_id
      }`
  """

  name = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsEnvironmentsListRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsEnvironmentsListRequest object.

  Fields:
    pageSize: Maximum return size of the list call.
    pageToken: A previous returned page token that can be used to continue
      listing from the last result.
    parent: Required. Format: `projects/{project_id}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NotebooksProjectsLocationsExecutionsCreateRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsExecutionsCreateRequest object.

  Fields:
    execution: A Execution resource to be passed as the request body.
    executionId: Required. User-defined unique ID of this execution.
    parent: Required. Format:
      `parent=projects/{project_id}/locations/{location}`
  """

  execution = _messages.MessageField('Execution', 1)
  executionId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NotebooksProjectsLocationsExecutionsDeleteRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsExecutionsDeleteRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/executions/{execution_id}`
  """

  name = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsExecutionsGetRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsExecutionsGetRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/executions/{execution_id}`
  """

  name = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsExecutionsListRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsExecutionsListRequest object.

  Fields:
    filter: Filter applied to resulting executions. Currently only supports
      filtering executions by a specified `schedule_id`. Format:
      `schedule_id=`
    orderBy: Sort by field.
    pageSize: Maximum return size of the list call.
    pageToken: A previous returned page token that can be used to continue
      listing from the last result.
    parent: Required. Format:
      `parent=projects/{project_id}/locations/{location}`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NotebooksProjectsLocationsGetRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsInstancesCreateRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesCreateRequest object.

  Fields:
    instance: A Instance resource to be passed as the request body.
    instanceId: Required. User-defined unique ID of this instance.
    parent: Required. Format:
      `parent=projects/{project_id}/locations/{location}`
  """

  instance = _messages.MessageField('Instance', 1)
  instanceId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NotebooksProjectsLocationsInstancesDeleteRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesDeleteRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
  """

  name = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsInstancesDiagnoseRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesDiagnoseRequest object.

  Fields:
    diagnoseInstanceRequest: A DiagnoseInstanceRequest resource to be passed
      as the request body.
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
  """

  diagnoseInstanceRequest = _messages.MessageField('DiagnoseInstanceRequest', 1)
  name = _messages.StringField(2, required=True)


class NotebooksProjectsLocationsInstancesGetIamPolicyRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NotebooksProjectsLocationsInstancesGetInstanceHealthRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesGetInstanceHealthRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
  """

  name = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsInstancesGetRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesGetRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
  """

  name = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsInstancesIsUpgradeableRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesIsUpgradeableRequest object.

  Enums:
    TypeValueValuesEnum: Optional. The optional UpgradeType. Setting this
      field will search for additional compute images to upgrade this
      instance.

  Fields:
    notebookInstance: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    type: Optional. The optional UpgradeType. Setting this field will search
      for additional compute images to upgrade this instance.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Optional. The optional UpgradeType. Setting this field will search for
    additional compute images to upgrade this instance.

    Values:
      UPGRADE_TYPE_UNSPECIFIED: Upgrade type is not specified.
      UPGRADE_FRAMEWORK: Upgrade ML framework.
      UPGRADE_OS: Upgrade Operating System.
      UPGRADE_CUDA: Upgrade CUDA.
      UPGRADE_ALL: Upgrade All (OS, Framework and CUDA).
    """
    UPGRADE_TYPE_UNSPECIFIED = 0
    UPGRADE_FRAMEWORK = 1
    UPGRADE_OS = 2
    UPGRADE_CUDA = 3
    UPGRADE_ALL = 4

  notebookInstance = _messages.StringField(1, required=True)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class NotebooksProjectsLocationsInstancesListRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesListRequest object.

  Fields:
    filter: Optional. List filter.
    orderBy: Optional. Sort results. Supported values are "name", "name desc"
      or "" (unsorted).
    pageSize: Maximum return size of the list call.
    pageToken: A previous returned page token that can be used to continue
      listing from the last result.
    parent: Required. Format:
      `parent=projects/{project_id}/locations/{location}`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NotebooksProjectsLocationsInstancesMigrateRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesMigrateRequest object.

  Fields:
    migrateInstanceRequest: A MigrateInstanceRequest resource to be passed as
      the request body.
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
  """

  migrateInstanceRequest = _messages.MessageField('MigrateInstanceRequest', 1)
  name = _messages.StringField(2, required=True)


class NotebooksProjectsLocationsInstancesRegisterRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesRegisterRequest object.

  Fields:
    parent: Required. Format:
      `parent=projects/{project_id}/locations/{location}`
    registerInstanceRequest: A RegisterInstanceRequest resource to be passed
      as the request body.
  """

  parent = _messages.StringField(1, required=True)
  registerInstanceRequest = _messages.MessageField('RegisterInstanceRequest', 2)


class NotebooksProjectsLocationsInstancesReportEventRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesReportEventRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    reportInstanceEventRequest: A ReportInstanceEventRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  reportInstanceEventRequest = _messages.MessageField('ReportInstanceEventRequest', 2)


class NotebooksProjectsLocationsInstancesReportRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesReportRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    reportInstanceInfoRequest: A ReportInstanceInfoRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  reportInstanceInfoRequest = _messages.MessageField('ReportInstanceInfoRequest', 2)


class NotebooksProjectsLocationsInstancesResetRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesResetRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    resetInstanceRequest: A ResetInstanceRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  resetInstanceRequest = _messages.MessageField('ResetInstanceRequest', 2)


class NotebooksProjectsLocationsInstancesRollbackRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesRollbackRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    rollbackInstanceRequest: A RollbackInstanceRequest resource to be passed
      as the request body.
  """

  name = _messages.StringField(1, required=True)
  rollbackInstanceRequest = _messages.MessageField('RollbackInstanceRequest', 2)


class NotebooksProjectsLocationsInstancesSetAcceleratorRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesSetAcceleratorRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    setInstanceAcceleratorRequest: A SetInstanceAcceleratorRequest resource to
      be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  setInstanceAcceleratorRequest = _messages.MessageField('SetInstanceAcceleratorRequest', 2)


class NotebooksProjectsLocationsInstancesSetIamPolicyRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class NotebooksProjectsLocationsInstancesSetLabelsRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesSetLabelsRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    setInstanceLabelsRequest: A SetInstanceLabelsRequest resource to be passed
      as the request body.
  """

  name = _messages.StringField(1, required=True)
  setInstanceLabelsRequest = _messages.MessageField('SetInstanceLabelsRequest', 2)


class NotebooksProjectsLocationsInstancesSetMachineTypeRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesSetMachineTypeRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    setInstanceMachineTypeRequest: A SetInstanceMachineTypeRequest resource to
      be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  setInstanceMachineTypeRequest = _messages.MessageField('SetInstanceMachineTypeRequest', 2)


class NotebooksProjectsLocationsInstancesStartRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesStartRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    startInstanceRequest: A StartInstanceRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  startInstanceRequest = _messages.MessageField('StartInstanceRequest', 2)


class NotebooksProjectsLocationsInstancesStopRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesStopRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    stopInstanceRequest: A StopInstanceRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  stopInstanceRequest = _messages.MessageField('StopInstanceRequest', 2)


class NotebooksProjectsLocationsInstancesTestIamPermissionsRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class NotebooksProjectsLocationsInstancesUpdateConfigRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesUpdateConfigRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    updateInstanceConfigRequest: A UpdateInstanceConfigRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateInstanceConfigRequest = _messages.MessageField('UpdateInstanceConfigRequest', 2)


class NotebooksProjectsLocationsInstancesUpdateMetadataItemsRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesUpdateMetadataItemsRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    updateInstanceMetadataItemsRequest: A UpdateInstanceMetadataItemsRequest
      resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateInstanceMetadataItemsRequest = _messages.MessageField('UpdateInstanceMetadataItemsRequest', 2)


class NotebooksProjectsLocationsInstancesUpdateShieldedInstanceConfigRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesUpdateShieldedInstanceConfigRequest
  object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    updateShieldedInstanceConfigRequest: A UpdateShieldedInstanceConfigRequest
      resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateShieldedInstanceConfigRequest = _messages.MessageField('UpdateShieldedInstanceConfigRequest', 2)


class NotebooksProjectsLocationsInstancesUpgradeInternalRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesUpgradeInternalRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    upgradeInstanceInternalRequest: A UpgradeInstanceInternalRequest resource
      to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  upgradeInstanceInternalRequest = _messages.MessageField('UpgradeInstanceInternalRequest', 2)


class NotebooksProjectsLocationsInstancesUpgradeRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesUpgradeRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    upgradeInstanceRequest: A UpgradeInstanceRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  upgradeInstanceRequest = _messages.MessageField('UpgradeInstanceRequest', 2)


class NotebooksProjectsLocationsListRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class NotebooksProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class NotebooksProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class NotebooksProjectsLocationsRuntimesCreateRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsRuntimesCreateRequest object.

  Fields:
    parent: Required. Format:
      `parent=projects/{project_id}/locations/{location}`
    requestId: Idempotent request UUID.
    runtime: A Runtime resource to be passed as the request body.
    runtimeId: Required. User-defined unique ID of this Runtime.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  runtime = _messages.MessageField('Runtime', 3)
  runtimeId = _messages.StringField(4)


class NotebooksProjectsLocationsRuntimesDeleteRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsRuntimesDeleteRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/runtimes/{runtime_id}`
    requestId: Idempotent request UUID.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NotebooksProjectsLocationsRuntimesDiagnoseRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsRuntimesDiagnoseRequest object.

  Fields:
    diagnoseRuntimeRequest: A DiagnoseRuntimeRequest resource to be passed as
      the request body.
    name: Required. Format:
      `projects/{project_id}/locations/{location}/runtimes/{runtimes_id}`
  """

  diagnoseRuntimeRequest = _messages.MessageField('DiagnoseRuntimeRequest', 1)
  name = _messages.StringField(2, required=True)


class NotebooksProjectsLocationsRuntimesGetIamPolicyRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsRuntimesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NotebooksProjectsLocationsRuntimesGetRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsRuntimesGetRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/runtimes/{runtime_id}`
  """

  name = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsRuntimesListRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsRuntimesListRequest object.

  Fields:
    filter: Optional. List filter.
    orderBy: Optional. Sort results. Supported values are "name", "name desc"
      or "" (unsorted).
    pageSize: Maximum return size of the list call.
    pageToken: A previous returned page token that can be used to continue
      listing from the last result.
    parent: Required. Format:
      `parent=projects/{project_id}/locations/{location}`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NotebooksProjectsLocationsRuntimesMigrateRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsRuntimesMigrateRequest object.

  Fields:
    migrateRuntimeRequest: A MigrateRuntimeRequest resource to be passed as
      the request body.
    name: Required. Format:
      `projects/{project_id}/locations/{location}/runtimes/{runtime_id}`
  """

  migrateRuntimeRequest = _messages.MessageField('MigrateRuntimeRequest', 1)
  name = _messages.StringField(2, required=True)


class NotebooksProjectsLocationsRuntimesPatchRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsRuntimesPatchRequest object.

  Fields:
    name: Output only. The resource name of the runtime. Format:
      `projects/{project}/locations/{location}/runtimes/{runtimeId}`
    requestId: Idempotent request UUID.
    runtime: A Runtime resource to be passed as the request body.
    updateMask: Required. Specifies the path, relative to `Runtime`, of the
      field to update. For example, to change the software configuration
      kernels, the `update_mask` parameter would be specified as
      `software_config.kernels`, and the `PATCH` request body would specify
      the new value, as follows: { "software_config":{ "kernels": [{
      'repository': 'gcr.io/deeplearning-platform-release/pytorch-gpu', 'tag':
      'latest' }], } } Currently, only the following fields can be updated: -
      `software_config.kernels` - `software_config.post_startup_script` -
      `software_config.custom_gpu_driver_path` -
      `software_config.idle_shutdown` -
      `software_config.idle_shutdown_timeout` -
      `software_config.disable_terminal` - `labels`
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  runtime = _messages.MessageField('Runtime', 3)
  updateMask = _messages.StringField(4)


class NotebooksProjectsLocationsRuntimesRefreshRuntimeTokenInternalRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsRuntimesRefreshRuntimeTokenInternalRequest
  object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/runtimes/{runtime_id}`
    refreshRuntimeTokenInternalRequest: A RefreshRuntimeTokenInternalRequest
      resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  refreshRuntimeTokenInternalRequest = _messages.MessageField('RefreshRuntimeTokenInternalRequest', 2)


class NotebooksProjectsLocationsRuntimesReportEventRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsRuntimesReportEventRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/runtimes/{runtime_id}`
    reportRuntimeEventRequest: A ReportRuntimeEventRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  reportRuntimeEventRequest = _messages.MessageField('ReportRuntimeEventRequest', 2)


class NotebooksProjectsLocationsRuntimesResetRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsRuntimesResetRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/runtimes/{runtime_id}`
    resetRuntimeRequest: A ResetRuntimeRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  resetRuntimeRequest = _messages.MessageField('ResetRuntimeRequest', 2)


class NotebooksProjectsLocationsRuntimesSetIamPolicyRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsRuntimesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class NotebooksProjectsLocationsRuntimesStartRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsRuntimesStartRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/runtimes/{runtime_id}`
    startRuntimeRequest: A StartRuntimeRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  startRuntimeRequest = _messages.MessageField('StartRuntimeRequest', 2)


class NotebooksProjectsLocationsRuntimesStopRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsRuntimesStopRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/runtimes/{runtime_id}`
    stopRuntimeRequest: A StopRuntimeRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  stopRuntimeRequest = _messages.MessageField('StopRuntimeRequest', 2)


class NotebooksProjectsLocationsRuntimesSwitchRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsRuntimesSwitchRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/runtimes/{runtime_id}`
    switchRuntimeRequest: A SwitchRuntimeRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  switchRuntimeRequest = _messages.MessageField('SwitchRuntimeRequest', 2)


class NotebooksProjectsLocationsRuntimesTestIamPermissionsRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsRuntimesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class NotebooksProjectsLocationsRuntimesUpgradeRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsRuntimesUpgradeRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/runtimes/{runtime_id}`
    upgradeRuntimeRequest: A UpgradeRuntimeRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  upgradeRuntimeRequest = _messages.MessageField('UpgradeRuntimeRequest', 2)


class NotebooksProjectsLocationsSchedulesCreateRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsSchedulesCreateRequest object.

  Fields:
    parent: Required. Format:
      `parent=projects/{project_id}/locations/{location}`
    schedule: A Schedule resource to be passed as the request body.
    scheduleId: Required. User-defined unique ID of this schedule.
  """

  parent = _messages.StringField(1, required=True)
  schedule = _messages.MessageField('Schedule', 2)
  scheduleId = _messages.StringField(3)


class NotebooksProjectsLocationsSchedulesDeleteRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsSchedulesDeleteRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/schedules/{schedule_id}`
  """

  name = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsSchedulesGetRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsSchedulesGetRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/schedules/{schedule_id}`
  """

  name = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsSchedulesListRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsSchedulesListRequest object.

  Fields:
    filter: Filter applied to resulting schedules.
    orderBy: Field to order results by.
    pageSize: Maximum return size of the list call.
    pageToken: A previous returned page token that can be used to continue
      listing from the last result.
    parent: Required. Format:
      `parent=projects/{project_id}/locations/{location}`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NotebooksProjectsLocationsSchedulesTriggerRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsSchedulesTriggerRequest object.

  Fields:
    name: Required. Format: `parent=projects/{project_id}/locations/{location}
      /schedules/{schedule_id}`
    triggerScheduleRequest: A TriggerScheduleRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  triggerScheduleRequest = _messages.MessageField('TriggerScheduleRequest', 2)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: API version used to start the operation.
    createTime: The time the operation was created.
    endTime: The time the operation finished running.
    endpoint: API endpoint name of this operation.
    requestedCancellation: Identifies whether the user has requested
      cancellation of the operation. Operations that have successfully been
      cancelled have google.longrunning.Operation.error value with a
      google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
    statusMessage: Human-readable status of the operation, if any.
    target: Server-defined resource path for the target of the operation.
    verb: Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  endpoint = _messages.StringField(4)
  requestedCancellation = _messages.BooleanField(5)
  statusMessage = _messages.StringField(6)
  target = _messages.StringField(7)
  verb = _messages.StringField(8)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  bindings = _messages.MessageField('Binding', 1, repeated=True)
  etag = _messages.BytesField(2)
  version = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class RefreshRuntimeTokenInternalRequest(_messages.Message):
  r"""Request for getting a new access token.

  Fields:
    vmId: Required. The VM hardware token for authenticating the VM.
      https://cloud.google.com/compute/docs/instances/verifying-instance-
      identity
  """

  vmId = _messages.StringField(1)


class RefreshRuntimeTokenInternalResponse(_messages.Message):
  r"""Response with a new access token.

  Fields:
    accessToken: The OAuth 2.0 access token.
    expireTime: Output only. Token expiration time.
  """

  accessToken = _messages.StringField(1)
  expireTime = _messages.StringField(2)


class RegisterInstanceRequest(_messages.Message):
  r"""Request for registering a notebook instance.

  Fields:
    instanceId: Required. User defined unique ID of this instance. The
      `instance_id` must be 1 to 63 characters long and contain only lowercase
      letters, numeric characters, and dashes. The first character must be a
      lowercase letter and the last character cannot be a dash.
  """

  instanceId = _messages.StringField(1)


class ReportInstanceEventRequest(_messages.Message):
  r"""Request for reporting a Managed Notebook Event.

  Fields:
    event: Required. The Event to be reported.
    vmId: Required. The VM hardware token for authenticating the VM.
      https://cloud.google.com/compute/docs/instances/verifying-instance-
      identity
  """

  event = _messages.MessageField('Event', 1)
  vmId = _messages.StringField(2)


class ReportInstanceInfoRequest(_messages.Message):
  r"""Request for notebook instances to report information to Notebooks API.

  Messages:
    MetadataValue: The metadata reported to Notebooks API. This will be merged
      to the instance metadata store

  Fields:
    metadata: The metadata reported to Notebooks API. This will be merged to
      the instance metadata store
    vmId: Required. The VM hardware token for authenticating the VM.
      https://cloud.google.com/compute/docs/instances/verifying-instance-
      identity
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""The metadata reported to Notebooks API. This will be merged to the
    instance metadata store

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Additional properties of type MetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  metadata = _messages.MessageField('MetadataValue', 1)
  vmId = _messages.StringField(2)


class ReportRuntimeEventRequest(_messages.Message):
  r"""Request for reporting a Managed Notebook Event.

  Fields:
    event: Required. The Event to be reported.
    vmId: Required. The VM hardware token for authenticating the VM.
      https://cloud.google.com/compute/docs/instances/verifying-instance-
      identity
  """

  event = _messages.MessageField('Event', 1)
  vmId = _messages.StringField(2)


class ReservationAffinity(_messages.Message):
  r"""Reservation Affinity for consuming Zonal reservation.

  Enums:
    ConsumeReservationTypeValueValuesEnum: Optional. Type of reservation to
      consume

  Fields:
    consumeReservationType: Optional. Type of reservation to consume
    key: Optional. Corresponds to the label key of reservation resource.
    values: Optional. Corresponds to the label values of reservation resource.
  """

  class ConsumeReservationTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Type of reservation to consume

    Values:
      TYPE_UNSPECIFIED: Default type.
      NO_RESERVATION: Do not consume from any allocated capacity.
      ANY_RESERVATION: Consume any reservation available.
      SPECIFIC_RESERVATION: Must consume from a specific reservation. Must
        specify key value fields for specifying the reservations.
    """
    TYPE_UNSPECIFIED = 0
    NO_RESERVATION = 1
    ANY_RESERVATION = 2
    SPECIFIC_RESERVATION = 3

  consumeReservationType = _messages.EnumField('ConsumeReservationTypeValueValuesEnum', 1)
  key = _messages.StringField(2)
  values = _messages.StringField(3, repeated=True)


class ResetInstanceRequest(_messages.Message):
  r"""Request for resetting a notebook instance"""


class ResetRuntimeRequest(_messages.Message):
  r"""Request for resetting a Managed Notebook Runtime.

  Fields:
    requestId: Idempotent request UUID.
  """

  requestId = _messages.StringField(1)


class RollbackInstanceRequest(_messages.Message):
  r"""Request for rollbacking a notebook instance

  Fields:
    targetSnapshot: Required. The snapshot for rollback. Example:
      `projects/test-project/global/snapshots/krwlzipynril`.
  """

  targetSnapshot = _messages.StringField(1)


class Runtime(_messages.Message):
  r"""The definition of a Runtime for a managed notebook instance.

  Enums:
    HealthStateValueValuesEnum: Output only. Runtime health_state.
    StateValueValuesEnum: Output only. Runtime state.

  Messages:
    LabelsValue: Optional. The labels to associate with this Managed Notebook
      or Runtime. Label **keys** must contain 1 to 63 characters, and must
      conform to [RFC 1035](https://www.ietf.org/rfc/rfc1035.txt). Label
      **values** may be empty, but, if present, must contain 1 to 63
      characters, and must conform to [RFC
      1035](https://www.ietf.org/rfc/rfc1035.txt). No more than 32 labels can
      be associated with a cluster.

  Fields:
    accessConfig: The config settings for accessing runtime.
    createTime: Output only. Runtime creation time.
    healthState: Output only. Runtime health_state.
    labels: Optional. The labels to associate with this Managed Notebook or
      Runtime. Label **keys** must contain 1 to 63 characters, and must
      conform to [RFC 1035](https://www.ietf.org/rfc/rfc1035.txt). Label
      **values** may be empty, but, if present, must contain 1 to 63
      characters, and must conform to [RFC
      1035](https://www.ietf.org/rfc/rfc1035.txt). No more than 32 labels can
      be associated with a cluster.
    metrics: Output only. Contains Runtime daemon metrics such as Service
      status and JupyterLab stats.
    migrated: Output only. Bool indicating whether this notebook has been
      migrated to a Workbench Instance
    name: Output only. The resource name of the runtime. Format:
      `projects/{project}/locations/{location}/runtimes/{runtimeId}`
    runtimeMigrationEligibility: Output only. Checks how feasible a migration
      from GmN to WbI is.
    softwareConfig: The config settings for software inside the runtime.
    state: Output only. Runtime state.
    updateTime: Output only. Runtime update time.
    virtualMachine: Use a Compute Engine VM image to start the managed
      notebook instance.
  """

  class HealthStateValueValuesEnum(_messages.Enum):
    r"""Output only. Runtime health_state.

    Values:
      HEALTH_STATE_UNSPECIFIED: The runtime substate is unknown.
      HEALTHY: The runtime is known to be in an healthy state (for example,
        critical daemons are running) Applies to ACTIVE state.
      UNHEALTHY: The runtime is known to be in an unhealthy state (for
        example, critical daemons are not running) Applies to ACTIVE state.
      AGENT_NOT_INSTALLED: The runtime has not installed health monitoring
        agent. Applies to ACTIVE state.
      AGENT_NOT_RUNNING: The runtime health monitoring agent is not running.
        Applies to ACTIVE state.
    """
    HEALTH_STATE_UNSPECIFIED = 0
    HEALTHY = 1
    UNHEALTHY = 2
    AGENT_NOT_INSTALLED = 3
    AGENT_NOT_RUNNING = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Runtime state.

    Values:
      STATE_UNSPECIFIED: State is not specified.
      STARTING: The compute layer is starting the runtime. It is not ready for
        use.
      PROVISIONING: The compute layer is installing required frameworks and
        registering the runtime with notebook proxy. It cannot be used.
      ACTIVE: The runtime is currently running. It is ready for use.
      STOPPING: The control logic is stopping the runtime. It cannot be used.
      STOPPED: The runtime is stopped. It cannot be used.
      DELETING: The runtime is being deleted. It cannot be used.
      UPGRADING: The runtime is upgrading. It cannot be used.
      INITIALIZING: The runtime is being created and set up. It is not ready
        for use.
    """
    STATE_UNSPECIFIED = 0
    STARTING = 1
    PROVISIONING = 2
    ACTIVE = 3
    STOPPING = 4
    STOPPED = 5
    DELETING = 6
    UPGRADING = 7
    INITIALIZING = 8

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels to associate with this Managed Notebook or
    Runtime. Label **keys** must contain 1 to 63 characters, and must conform
    to [RFC 1035](https://www.ietf.org/rfc/rfc1035.txt). Label **values** may
    be empty, but, if present, must contain 1 to 63 characters, and must
    conform to [RFC 1035](https://www.ietf.org/rfc/rfc1035.txt). No more than
    32 labels can be associated with a cluster.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  accessConfig = _messages.MessageField('RuntimeAccessConfig', 1)
  createTime = _messages.StringField(2)
  healthState = _messages.EnumField('HealthStateValueValuesEnum', 3)
  labels = _messages.MessageField('LabelsValue', 4)
  metrics = _messages.MessageField('RuntimeMetrics', 5)
  migrated = _messages.BooleanField(6)
  name = _messages.StringField(7)
  runtimeMigrationEligibility = _messages.MessageField('RuntimeMigrationEligibility', 8)
  softwareConfig = _messages.MessageField('RuntimeSoftwareConfig', 9)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  updateTime = _messages.StringField(11)
  virtualMachine = _messages.MessageField('VirtualMachine', 12)


class RuntimeAcceleratorConfig(_messages.Message):
  r"""Definition of the types of hardware accelerators that can be used. See
  [Compute Engine AcceleratorTypes](https://cloud.google.com/compute/docs/refe
  rence/beta/acceleratorTypes). Examples: * `nvidia-tesla-k80` * `nvidia-
  tesla-p100` * `nvidia-tesla-v100` * `nvidia-tesla-p4` * `nvidia-tesla-t4` *
  `nvidia-tesla-a100`

  Enums:
    TypeValueValuesEnum: Accelerator model.

  Fields:
    coreCount: Count of cores of this accelerator.
    type: Accelerator model.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Accelerator model.

    Values:
      ACCELERATOR_TYPE_UNSPECIFIED: Accelerator type is not specified.
      NVIDIA_TESLA_K80: Accelerator type is Nvidia Tesla K80.
      NVIDIA_TESLA_P100: Accelerator type is Nvidia Tesla P100.
      NVIDIA_TESLA_V100: Accelerator type is Nvidia Tesla V100.
      NVIDIA_TESLA_P4: Accelerator type is Nvidia Tesla P4.
      NVIDIA_TESLA_T4: Accelerator type is Nvidia Tesla T4.
      NVIDIA_TESLA_A100: Accelerator type is Nvidia Tesla A100 - 40GB.
      NVIDIA_L4: Accelerator type is Nvidia L4.
      TPU_V2: (Coming soon) Accelerator type is TPU V2.
      TPU_V3: (Coming soon) Accelerator type is TPU V3.
      NVIDIA_TESLA_T4_VWS: Accelerator type is NVIDIA Tesla T4 Virtual
        Workstations.
      NVIDIA_TESLA_P100_VWS: Accelerator type is NVIDIA Tesla P100 Virtual
        Workstations.
      NVIDIA_TESLA_P4_VWS: Accelerator type is NVIDIA Tesla P4 Virtual
        Workstations.
    """
    ACCELERATOR_TYPE_UNSPECIFIED = 0
    NVIDIA_TESLA_K80 = 1
    NVIDIA_TESLA_P100 = 2
    NVIDIA_TESLA_V100 = 3
    NVIDIA_TESLA_P4 = 4
    NVIDIA_TESLA_T4 = 5
    NVIDIA_TESLA_A100 = 6
    NVIDIA_L4 = 7
    TPU_V2 = 8
    TPU_V3 = 9
    NVIDIA_TESLA_T4_VWS = 10
    NVIDIA_TESLA_P100_VWS = 11
    NVIDIA_TESLA_P4_VWS = 12

  coreCount = _messages.IntegerField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class RuntimeAccessConfig(_messages.Message):
  r"""Specifies the login configuration for Runtime

  Enums:
    AccessTypeValueValuesEnum: The type of access mode this instance.

  Fields:
    accessType: The type of access mode this instance.
    proxyUri: Output only. The proxy endpoint that is used to access the
      runtime.
    runtimeOwner: The owner of this runtime after creation. Format:
      `<EMAIL>` Currently supports one owner only.
  """

  class AccessTypeValueValuesEnum(_messages.Enum):
    r"""The type of access mode this instance.

    Values:
      RUNTIME_ACCESS_TYPE_UNSPECIFIED: Unspecified access.
      SINGLE_USER: Single user login.
      SERVICE_ACCOUNT: Service Account mode. In Service Account mode, Runtime
        creator will specify a SA that exists in the consumer project. Using
        Runtime Service Account field. Users accessing the Runtime need ActAs
        (Service Account User) permission.
    """
    RUNTIME_ACCESS_TYPE_UNSPECIFIED = 0
    SINGLE_USER = 1
    SERVICE_ACCOUNT = 2

  accessType = _messages.EnumField('AccessTypeValueValuesEnum', 1)
  proxyUri = _messages.StringField(2)
  runtimeOwner = _messages.StringField(3)


class RuntimeGuestOsFeature(_messages.Message):
  r"""Optional. A list of features to enable on the guest operating system.
  Applicable only for bootable images. Read [Enabling guest operating system
  features](https://cloud.google.com/compute/docs/images/create-delete-
  deprecate-private-images#guest-os-features) to see a list of available
  options. Guest OS features for boot disk.

  Fields:
    type: The ID of a supported feature. Read [Enabling guest operating system
      features](https://cloud.google.com/compute/docs/images/create-delete-
      deprecate-private-images#guest-os-features) to see a list of available
      options. Valid values: * `FEATURE_TYPE_UNSPECIFIED` * `MULTI_IP_SUBNET`
      * `SECURE_BOOT` * `UEFI_COMPATIBLE` * `VIRTIO_SCSI_MULTIQUEUE` *
      `WINDOWS`
  """

  type = _messages.StringField(1)


class RuntimeMetrics(_messages.Message):
  r"""Contains runtime daemon metrics, such as OS and kernels and sessions
  stats.

  Messages:
    SystemMetricsValue: Output only. The system metrics.

  Fields:
    systemMetrics: Output only. The system metrics.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SystemMetricsValue(_messages.Message):
    r"""Output only. The system metrics.

    Messages:
      AdditionalProperty: An additional property for a SystemMetricsValue
        object.

    Fields:
      additionalProperties: Additional properties of type SystemMetricsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SystemMetricsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  systemMetrics = _messages.MessageField('SystemMetricsValue', 1)


class RuntimeMigrationEligibility(_messages.Message):
  r"""RuntimeMigrationEligibility represents the feasibility information of a
  migration from GmN to WbI.

  Enums:
    ErrorsValueListEntryValuesEnum:
    WarningsValueListEntryValuesEnum:

  Fields:
    errors: Output only. Certain configurations make the GmN ineligible for an
      automatic migration. A manual migration is required.
    warnings: Output only. Certain configurations will be defaulted during the
      migration.
  """

  class ErrorsValueListEntryValuesEnum(_messages.Enum):
    r"""ErrorsValueListEntryValuesEnum enum type.

    Values:
      ERROR_UNSPECIFIED: Default type.
      CUSTOM_CONTAINER: The GmN is configured with custom container(s) and
        cannot be migrated.
    """
    ERROR_UNSPECIFIED = 0
    CUSTOM_CONTAINER = 1

  class WarningsValueListEntryValuesEnum(_messages.Enum):
    r"""WarningsValueListEntryValuesEnum enum type.

    Values:
      WARNING_UNSPECIFIED: Default type.
      UNSUPPORTED_ACCELERATOR_TYPE: The GmN uses an accelerator type that's
        unsupported in WbI. It will be migrated without an accelerator. Users
        can attach an accelerator after the migration.
      UNSUPPORTED_OS: The GmN uses an operating system that's unsupported in
        WbI (e.g. Debian 10). It will be replaced with Debian 11 in WbI.
      RESERVED_IP_RANGE: This GmN is configured with reserved IP range, which
        is no longer applicable in WbI.
      GOOGLE_MANAGED_NETWORK: This GmN is configured with a Google managed
        network. Please provide the `network` and `subnet` options for the
        migration.
      POST_STARTUP_SCRIPT: This GmN is configured with a post startup script.
        Please optionally provide the `post_startup_script_option` for the
        migration.
      SINGLE_USER: This GmN is configured with single user mode. Please
        optionally provide the `service_account` option for the migration.
    """
    WARNING_UNSPECIFIED = 0
    UNSUPPORTED_ACCELERATOR_TYPE = 1
    UNSUPPORTED_OS = 2
    RESERVED_IP_RANGE = 3
    GOOGLE_MANAGED_NETWORK = 4
    POST_STARTUP_SCRIPT = 5
    SINGLE_USER = 6

  errors = _messages.EnumField('ErrorsValueListEntryValuesEnum', 1, repeated=True)
  warnings = _messages.EnumField('WarningsValueListEntryValuesEnum', 2, repeated=True)


class RuntimeShieldedInstanceConfig(_messages.Message):
  r"""A set of Shielded Instance options. See [Images using supported Shielded
  VM features](https://cloud.google.com/compute/docs/instances/modifying-
  shielded-vm). Not all combinations are valid.

  Fields:
    enableIntegrityMonitoring: Defines whether the instance has integrity
      monitoring enabled. Enables monitoring and attestation of the boot
      integrity of the instance. The attestation is performed against the
      integrity policy baseline. This baseline is initially derived from the
      implicitly trusted boot image when the instance is created. Enabled by
      default.
    enableSecureBoot: Defines whether the instance has Secure Boot enabled.
      Secure Boot helps ensure that the system only runs authentic software by
      verifying the digital signature of all boot components, and halting the
      boot process if signature verification fails. Disabled by default.
    enableVtpm: Defines whether the instance has the vTPM enabled. Enabled by
      default.
  """

  enableIntegrityMonitoring = _messages.BooleanField(1)
  enableSecureBoot = _messages.BooleanField(2)
  enableVtpm = _messages.BooleanField(3)


class RuntimeSoftwareConfig(_messages.Message):
  r"""Specifies the selection and configuration of software inside the
  runtime. The properties to set on runtime. Properties keys are specified in
  `key:value` format, for example: * `idle_shutdown: true` *
  `idle_shutdown_timeout: 180` * `enable_health_monitoring: true`

  Enums:
    PostStartupScriptBehaviorValueValuesEnum: Behavior for the post startup
      script.

  Fields:
    customGpuDriverPath: Specify a custom Cloud Storage path where the GPU
      driver is stored. If not specified, we'll automatically choose from
      official GPU drivers.
    disableTerminal: Bool indicating whether JupyterLab terminal will be
      available or not. Default: False
    enableHealthMonitoring: Verifies core internal services are running.
      Default: True
    idleShutdown: Runtime will automatically shutdown after
      idle_shutdown_time. Default: True
    idleShutdownTimeout: Time in minutes to wait before shutting down runtime.
      Default: 180 minutes
    installGpuDriver: Install Nvidia Driver automatically. Default: True
    kernels: Optional. Use a list of container images to use as Kernels in the
      notebook instance.
    mixerDisabled: Bool indicating whether mixer client should be disabled.
      Default: False
    notebookUpgradeSchedule: Cron expression in UTC timezone, used to schedule
      instance auto upgrade. Please follow the [cron
      format](https://en.wikipedia.org/wiki/Cron).
    postStartupScript: Path to a Bash script that automatically runs after a
      notebook instance fully boots up. The path must be a URL or Cloud
      Storage path (`gs://path-to-file/file-name`).
    postStartupScriptBehavior: Behavior for the post startup script.
    upgradeable: Output only. Bool indicating whether an newer image is
      available in an image family.
    version: Output only. version of boot image such as M100, from release
      label of the image.
  """

  class PostStartupScriptBehaviorValueValuesEnum(_messages.Enum):
    r"""Behavior for the post startup script.

    Values:
      POST_STARTUP_SCRIPT_BEHAVIOR_UNSPECIFIED: Unspecified post startup
        script behavior. Will run only once at creation.
      RUN_EVERY_START: Runs the post startup script provided during creation
        at every start.
      DOWNLOAD_AND_RUN_EVERY_START: Downloads and runs the provided post
        startup script at every start.
    """
    POST_STARTUP_SCRIPT_BEHAVIOR_UNSPECIFIED = 0
    RUN_EVERY_START = 1
    DOWNLOAD_AND_RUN_EVERY_START = 2

  customGpuDriverPath = _messages.StringField(1)
  disableTerminal = _messages.BooleanField(2)
  enableHealthMonitoring = _messages.BooleanField(3)
  idleShutdown = _messages.BooleanField(4)
  idleShutdownTimeout = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  installGpuDriver = _messages.BooleanField(6)
  kernels = _messages.MessageField('ContainerImage', 7, repeated=True)
  mixerDisabled = _messages.BooleanField(8)
  notebookUpgradeSchedule = _messages.StringField(9)
  postStartupScript = _messages.StringField(10)
  postStartupScriptBehavior = _messages.EnumField('PostStartupScriptBehaviorValueValuesEnum', 11)
  upgradeable = _messages.BooleanField(12)
  version = _messages.StringField(13)


class Schedule(_messages.Message):
  r"""The definition of a schedule.

  Enums:
    StateValueValuesEnum:

  Fields:
    createTime: Output only. Time the schedule was created.
    cronSchedule: Cron-tab formatted schedule by which the job will execute.
      Format: minute, hour, day of month, month, day of week, e.g. `0 0 * *
      WED` = every Wednesday More examples: https://crontab.guru/examples.html
    description: A brief description of this environment.
    displayName: Output only. Display name used for UI purposes. Name can only
      contain alphanumeric characters, hyphens `-`, and underscores `_`.
    executionTemplate: Notebook Execution Template corresponding to this
      schedule.
    name: Output only. The name of this schedule. Format:
      `projects/{project_id}/locations/{location}/schedules/{schedule_id}`
    recentExecutions: Output only. The most recent execution names triggered
      from this schedule and their corresponding states.
    state: A StateValueValuesEnum attribute.
    timeZone: Timezone on which the cron_schedule. The value of this field
      must be a time zone name from the tz database. TZ Database:
      https://en.wikipedia.org/wiki/List_of_tz_database_time_zones Note that
      some time zones include a provision for daylight savings time. The rules
      for daylight saving time are determined by the chosen tz. For UTC use
      the string "utc". If a time zone is not specified, the default will be
      in UTC (also known as GMT).
    updateTime: Output only. Time the schedule was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""StateValueValuesEnum enum type.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      ENABLED: The job is executing normally.
      PAUSED: The job is paused by the user. It will not execute. A user can
        intentionally pause the job using [Cloud
        Scheduler](https://cloud.google.com/scheduler/docs/creating#pause).
      DISABLED: The job is disabled by the system due to error. The user
        cannot directly set a job to be disabled.
      UPDATE_FAILED: The job state resulting from a failed [CloudScheduler.Upd
        ateJob](https://cloud.google.com/scheduler/docs/creating#edit)
        operation. To recover a job from this state, retry [CloudScheduler.Upd
        ateJob](https://cloud.google.com/scheduler/docs/creating#edit) until a
        successful response is received.
      INITIALIZING: The schedule resource is being created.
      DELETING: The schedule resource is being deleted.
    """
    STATE_UNSPECIFIED = 0
    ENABLED = 1
    PAUSED = 2
    DISABLED = 3
    UPDATE_FAILED = 4
    INITIALIZING = 5
    DELETING = 6

  createTime = _messages.StringField(1)
  cronSchedule = _messages.StringField(2)
  description = _messages.StringField(3)
  displayName = _messages.StringField(4)
  executionTemplate = _messages.MessageField('ExecutionTemplate', 5)
  name = _messages.StringField(6)
  recentExecutions = _messages.MessageField('Execution', 7, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  timeZone = _messages.StringField(9)
  updateTime = _messages.StringField(10)


class SchedulerAcceleratorConfig(_messages.Message):
  r"""Definition of a hardware accelerator. Note that not all combinations of
  `type` and `core_count` are valid. See [GPUs on Compute
  Engine](https://cloud.google.com/compute/docs/gpus) to find a valid
  combination. TPUs are not supported.

  Enums:
    TypeValueValuesEnum: Type of this accelerator.

  Fields:
    coreCount: Count of cores of this accelerator.
    type: Type of this accelerator.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of this accelerator.

    Values:
      SCHEDULER_ACCELERATOR_TYPE_UNSPECIFIED: Unspecified accelerator type.
        Default to no GPU.
      NVIDIA_TESLA_K80: Nvidia Tesla K80 GPU.
      NVIDIA_TESLA_P100: Nvidia Tesla P100 GPU.
      NVIDIA_TESLA_V100: Nvidia Tesla V100 GPU.
      NVIDIA_TESLA_P4: Nvidia Tesla P4 GPU.
      NVIDIA_TESLA_T4: Nvidia Tesla T4 GPU.
      NVIDIA_TESLA_A100: Nvidia Tesla A100 GPU.
      TPU_V2: TPU v2.
      TPU_V3: TPU v3.
    """
    SCHEDULER_ACCELERATOR_TYPE_UNSPECIFIED = 0
    NVIDIA_TESLA_K80 = 1
    NVIDIA_TESLA_P100 = 2
    NVIDIA_TESLA_V100 = 3
    NVIDIA_TESLA_P4 = 4
    NVIDIA_TESLA_T4 = 5
    NVIDIA_TESLA_A100 = 6
    TPU_V2 = 7
    TPU_V3 = 8

  coreCount = _messages.IntegerField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
  """

  policy = _messages.MessageField('Policy', 1)


class SetInstanceAcceleratorRequest(_messages.Message):
  r"""Request for setting instance accelerator.

  Enums:
    TypeValueValuesEnum: Required. Type of this accelerator.

  Fields:
    coreCount: Required. Count of cores of this accelerator. Note that not all
      combinations of `type` and `core_count` are valid. See [GPUs on Compute
      Engine](https://cloud.google.com/compute/docs/gpus/#gpus-list) to find a
      valid combination. TPUs are not supported.
    type: Required. Type of this accelerator.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. Type of this accelerator.

    Values:
      ACCELERATOR_TYPE_UNSPECIFIED: Accelerator type is not specified.
      NVIDIA_TESLA_K80: Accelerator type is Nvidia Tesla K80.
      NVIDIA_TESLA_P100: Accelerator type is Nvidia Tesla P100.
      NVIDIA_TESLA_V100: Accelerator type is Nvidia Tesla V100.
      NVIDIA_TESLA_P4: Accelerator type is Nvidia Tesla P4.
      NVIDIA_TESLA_T4: Accelerator type is Nvidia Tesla T4.
      NVIDIA_TESLA_A100: Accelerator type is Nvidia Tesla A100.
      NVIDIA_L4: Accelerator type is Nvidia Tesla L4.
      NVIDIA_A100_80GB: Accelerator type is Nvidia Tesla A100 80GB.
      NVIDIA_TESLA_T4_VWS: Accelerator type is NVIDIA Tesla T4 Virtual
        Workstations.
      NVIDIA_TESLA_P100_VWS: Accelerator type is NVIDIA Tesla P100 Virtual
        Workstations.
      NVIDIA_TESLA_P4_VWS: Accelerator type is NVIDIA Tesla P4 Virtual
        Workstations.
      NVIDIA_H100_80GB: Accelerator type is NVIDIA H100 80GB.
      NVIDIA_H100_MEGA_80GB: Accelerator type is NVIDIA H100 Mega 80GB.
      TPU_V2: (Coming soon) Accelerator type is TPU V2.
      TPU_V3: (Coming soon) Accelerator type is TPU V3.
    """
    ACCELERATOR_TYPE_UNSPECIFIED = 0
    NVIDIA_TESLA_K80 = 1
    NVIDIA_TESLA_P100 = 2
    NVIDIA_TESLA_V100 = 3
    NVIDIA_TESLA_P4 = 4
    NVIDIA_TESLA_T4 = 5
    NVIDIA_TESLA_A100 = 6
    NVIDIA_L4 = 7
    NVIDIA_A100_80GB = 8
    NVIDIA_TESLA_T4_VWS = 9
    NVIDIA_TESLA_P100_VWS = 10
    NVIDIA_TESLA_P4_VWS = 11
    NVIDIA_H100_80GB = 12
    NVIDIA_H100_MEGA_80GB = 13
    TPU_V2 = 14
    TPU_V3 = 15

  coreCount = _messages.IntegerField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class SetInstanceLabelsRequest(_messages.Message):
  r"""Request for setting instance labels.

  Messages:
    LabelsValue: Labels to apply to this instance. These can be later modified
      by the setLabels method

  Fields:
    labels: Labels to apply to this instance. These can be later modified by
      the setLabels method
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels to apply to this instance. These can be later modified by the
    setLabels method

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)


class SetInstanceMachineTypeRequest(_messages.Message):
  r"""Request for setting instance machine type.

  Fields:
    machineType: Required. The [Compute Engine machine
      type](https://cloud.google.com/compute/docs/machine-resource).
  """

  machineType = _messages.StringField(1)


class ShieldedInstanceConfig(_messages.Message):
  r"""A set of Shielded Instance options. See [Images using supported Shielded
  VM features](https://cloud.google.com/compute/docs/instances/modifying-
  shielded-vm). Not all combinations are valid.

  Fields:
    enableIntegrityMonitoring: Defines whether the instance has integrity
      monitoring enabled. Enables monitoring and attestation of the boot
      integrity of the instance. The attestation is performed against the
      integrity policy baseline. This baseline is initially derived from the
      implicitly trusted boot image when the instance is created. Enabled by
      default.
    enableSecureBoot: Defines whether the instance has Secure Boot enabled.
      Secure Boot helps ensure that the system only runs authentic software by
      verifying the digital signature of all boot components, and halting the
      boot process if signature verification fails. Disabled by default.
    enableVtpm: Defines whether the instance has the vTPM enabled. Enabled by
      default.
  """

  enableIntegrityMonitoring = _messages.BooleanField(1)
  enableSecureBoot = _messages.BooleanField(2)
  enableVtpm = _messages.BooleanField(3)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StartInstanceRequest(_messages.Message):
  r"""Request for starting a notebook instance"""


class StartRuntimeRequest(_messages.Message):
  r"""Request for starting a Managed Notebook Runtime.

  Fields:
    requestId: Idempotent request UUID.
  """

  requestId = _messages.StringField(1)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StopInstanceRequest(_messages.Message):
  r"""Request for stopping a notebook instance"""


class StopRuntimeRequest(_messages.Message):
  r"""Request for stopping a Managed Notebook Runtime.

  Fields:
    requestId: Idempotent request UUID.
  """

  requestId = _messages.StringField(1)


class SwitchRuntimeRequest(_messages.Message):
  r"""Request for switching a Managed Notebook Runtime.

  Fields:
    acceleratorConfig: accelerator config.
    machineType: machine type.
    requestId: Idempotent request UUID.
  """

  acceleratorConfig = _messages.MessageField('RuntimeAcceleratorConfig', 1)
  machineType = _messages.StringField(2)
  requestId = _messages.StringField(3)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TriggerScheduleRequest(_messages.Message):
  r"""Request for created scheduled notebooks"""


class UpdateInstanceConfigRequest(_messages.Message):
  r"""Request for updating instance configurations.

  Fields:
    config: The instance configurations to be updated.
  """

  config = _messages.MessageField('InstanceConfig', 1)


class UpdateInstanceMetadataItemsRequest(_messages.Message):
  r"""Request for adding/changing metadata items for an instance.

  Messages:
    ItemsValue: Metadata items to add/update for the instance.

  Fields:
    items: Metadata items to add/update for the instance.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ItemsValue(_messages.Message):
    r"""Metadata items to add/update for the instance.

    Messages:
      AdditionalProperty: An additional property for a ItemsValue object.

    Fields:
      additionalProperties: Additional properties of type ItemsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ItemsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  items = _messages.MessageField('ItemsValue', 1)


class UpdateInstanceMetadataItemsResponse(_messages.Message):
  r"""Response for adding/changing metadata items for an instance.

  Messages:
    ItemsValue: Map of items that were added/updated to/in the metadata.

  Fields:
    items: Map of items that were added/updated to/in the metadata.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ItemsValue(_messages.Message):
    r"""Map of items that were added/updated to/in the metadata.

    Messages:
      AdditionalProperty: An additional property for a ItemsValue object.

    Fields:
      additionalProperties: Additional properties of type ItemsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ItemsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  items = _messages.MessageField('ItemsValue', 1)


class UpdateShieldedInstanceConfigRequest(_messages.Message):
  r"""Request for updating the Shielded Instance config for a notebook
  instance. You can only use this method on a stopped instance

  Fields:
    shieldedInstanceConfig: ShieldedInstance configuration to be updated.
  """

  shieldedInstanceConfig = _messages.MessageField('ShieldedInstanceConfig', 1)


class UpgradeHistoryEntry(_messages.Message):
  r"""The entry of VM image upgrade history.

  Enums:
    ActionValueValuesEnum: Action. Rolloback or Upgrade.
    StateValueValuesEnum: The state of this instance upgrade history entry.

  Fields:
    action: Action. Rolloback or Upgrade.
    containerImage: The container image before this instance upgrade.
    createTime: The time that this instance upgrade history entry is created.
    framework: The framework of this notebook instance.
    snapshot: The snapshot of the boot disk of this notebook instance before
      upgrade.
    state: The state of this instance upgrade history entry.
    targetImage: Target VM Image. Format: `ainotebooks-vm/project/image-
      name/name`.
    targetVersion: Target VM Version, like m63.
    version: The version of the notebook instance before this upgrade.
    vmImage: The VM image before this instance upgrade.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Action. Rolloback or Upgrade.

    Values:
      ACTION_UNSPECIFIED: Operation is not specified.
      UPGRADE: Upgrade.
      ROLLBACK: Rollback.
    """
    ACTION_UNSPECIFIED = 0
    UPGRADE = 1
    ROLLBACK = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of this instance upgrade history entry.

    Values:
      STATE_UNSPECIFIED: State is not specified.
      STARTED: The instance upgrade is started.
      SUCCEEDED: The instance upgrade is succeeded.
      FAILED: The instance upgrade is failed.
    """
    STATE_UNSPECIFIED = 0
    STARTED = 1
    SUCCEEDED = 2
    FAILED = 3

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  containerImage = _messages.StringField(2)
  createTime = _messages.StringField(3)
  framework = _messages.StringField(4)
  snapshot = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  targetImage = _messages.StringField(7)
  targetVersion = _messages.StringField(8)
  version = _messages.StringField(9)
  vmImage = _messages.StringField(10)


class UpgradeInstanceInternalRequest(_messages.Message):
  r"""Request for upgrading a notebook instance from within the VM

  Enums:
    TypeValueValuesEnum: Optional. The optional UpgradeType. Setting this
      field will search for additional compute images to upgrade this
      instance.

  Fields:
    type: Optional. The optional UpgradeType. Setting this field will search
      for additional compute images to upgrade this instance.
    vmId: Required. The VM hardware token for authenticating the VM.
      https://cloud.google.com/compute/docs/instances/verifying-instance-
      identity
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Optional. The optional UpgradeType. Setting this field will search for
    additional compute images to upgrade this instance.

    Values:
      UPGRADE_TYPE_UNSPECIFIED: Upgrade type is not specified.
      UPGRADE_FRAMEWORK: Upgrade ML framework.
      UPGRADE_OS: Upgrade Operating System.
      UPGRADE_CUDA: Upgrade CUDA.
      UPGRADE_ALL: Upgrade All (OS, Framework and CUDA).
    """
    UPGRADE_TYPE_UNSPECIFIED = 0
    UPGRADE_FRAMEWORK = 1
    UPGRADE_OS = 2
    UPGRADE_CUDA = 3
    UPGRADE_ALL = 4

  type = _messages.EnumField('TypeValueValuesEnum', 1)
  vmId = _messages.StringField(2)


class UpgradeInstanceRequest(_messages.Message):
  r"""Request for upgrading a notebook instance

  Enums:
    TypeValueValuesEnum: Optional. The optional UpgradeType. Setting this
      field will search for additional compute images to upgrade this
      instance.

  Fields:
    type: Optional. The optional UpgradeType. Setting this field will search
      for additional compute images to upgrade this instance.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Optional. The optional UpgradeType. Setting this field will search for
    additional compute images to upgrade this instance.

    Values:
      UPGRADE_TYPE_UNSPECIFIED: Upgrade type is not specified.
      UPGRADE_FRAMEWORK: Upgrade ML framework.
      UPGRADE_OS: Upgrade Operating System.
      UPGRADE_CUDA: Upgrade CUDA.
      UPGRADE_ALL: Upgrade All (OS, Framework and CUDA).
    """
    UPGRADE_TYPE_UNSPECIFIED = 0
    UPGRADE_FRAMEWORK = 1
    UPGRADE_OS = 2
    UPGRADE_CUDA = 3
    UPGRADE_ALL = 4

  type = _messages.EnumField('TypeValueValuesEnum', 1)


class UpgradeRuntimeRequest(_messages.Message):
  r"""Request for upgrading a Managed Notebook Runtime to the latest version.
  option (google.api.message_visibility).restriction =
  "TRUSTED_TESTER,SPECIAL_TESTER";

  Fields:
    requestId: Idempotent request UUID.
  """

  requestId = _messages.StringField(1)


class VertexAIParameters(_messages.Message):
  r"""Parameters used in Vertex AI JobType executions.

  Messages:
    EnvValue: Environment variables. At most 100 environment variables can be
      specified and unique. Example: `GCP_BUCKET=gs://my-bucket/samples/`

  Fields:
    env: Environment variables. At most 100 environment variables can be
      specified and unique. Example: `GCP_BUCKET=gs://my-bucket/samples/`
    network: The full name of the Compute Engine
      [network](https://cloud.google.com/compute/docs/networks-and-
      firewalls#networks) to which the Job should be peered. For example,
      `projects/12345/global/networks/myVPC`. [Format](https://cloud.google.co
      m/compute/docs/reference/rest/v1/networks/insert) is of the form
      `projects/{project}/global/networks/{network}`. Where `{project}` is a
      project number, as in `12345`, and `{network}` is a network name.
      Private services access must already be configured for the network. If
      left unspecified, the job is not peered with any network.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvValue(_messages.Message):
    r"""Environment variables. At most 100 environment variables can be
    specified and unique. Example: `GCP_BUCKET=gs://my-bucket/samples/`

    Messages:
      AdditionalProperty: An additional property for a EnvValue object.

    Fields:
      additionalProperties: Additional properties of type EnvValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  env = _messages.MessageField('EnvValue', 1)
  network = _messages.StringField(2)


class VirtualMachine(_messages.Message):
  r"""Runtime using Virtual Machine for computing.

  Fields:
    instanceId: Output only. The unique identifier of the Managed Compute
      Engine instance.
    instanceName: Output only. The user-friendly name of the Managed Compute
      Engine instance.
    virtualMachineConfig: Virtual Machine configuration settings.
  """

  instanceId = _messages.StringField(1)
  instanceName = _messages.StringField(2)
  virtualMachineConfig = _messages.MessageField('VirtualMachineConfig', 3)


class VirtualMachineConfig(_messages.Message):
  r"""The config settings for virtual machine.

  Enums:
    NicTypeValueValuesEnum: Optional. The type of vNIC to be used on this
      interface. This may be gVNIC or VirtioNet.

  Messages:
    GuestAttributesValue: Output only. The Compute Engine guest attributes.
      (see [Project and instance guest
      attributes](https://cloud.google.com/compute/docs/storing-retrieving-
      metadata#guest_attributes)).
    LabelsValue: Optional. The labels to associate with this runtime. Label
      **keys** must contain 1 to 63 characters, and must conform to [RFC
      1035](https://www.ietf.org/rfc/rfc1035.txt). Label **values** may be
      empty, but, if present, must contain 1 to 63 characters, and must
      conform to [RFC 1035](https://www.ietf.org/rfc/rfc1035.txt). No more
      than 32 labels can be associated with a cluster.
    MetadataValue: Optional. The Compute Engine metadata entries to add to
      virtual machine. (see [Project and instance
      metadata](https://cloud.google.com/compute/docs/storing-retrieving-
      metadata#project_and_instance_metadata)).

  Fields:
    acceleratorConfig: Optional. The Compute Engine accelerator configuration
      for this runtime.
    bootImage: Optional. Boot image metadata used for runtime upgradeability.
    containerImages: Optional. Use a list of container images to use as
      Kernels in the notebook instance.
    dataDisk: Required. Data disk option configuration settings.
    encryptionConfig: Optional. Encryption settings for virtual machine data
      disk.
    guestAttributes: Output only. The Compute Engine guest attributes. (see
      [Project and instance guest
      attributes](https://cloud.google.com/compute/docs/storing-retrieving-
      metadata#guest_attributes)).
    internalIpOnly: Optional. If true, runtime will only have internal IP
      addresses. By default, runtimes are not restricted to internal IP
      addresses, and will have ephemeral external IP addresses assigned to
      each vm. This `internal_ip_only` restriction can only be enabled for
      subnetwork enabled networks, and all dependencies must be configured to
      be accessible without external IP addresses.
    labels: Optional. The labels to associate with this runtime. Label
      **keys** must contain 1 to 63 characters, and must conform to [RFC
      1035](https://www.ietf.org/rfc/rfc1035.txt). Label **values** may be
      empty, but, if present, must contain 1 to 63 characters, and must
      conform to [RFC 1035](https://www.ietf.org/rfc/rfc1035.txt). No more
      than 32 labels can be associated with a cluster.
    machineType: Required. The Compute Engine machine type used for runtimes.
      Short name is valid. Examples: * `n1-standard-2` * `e2-standard-8`
    metadata: Optional. The Compute Engine metadata entries to add to virtual
      machine. (see [Project and instance
      metadata](https://cloud.google.com/compute/docs/storing-retrieving-
      metadata#project_and_instance_metadata)).
    network: Optional. The Compute Engine network to be used for machine
      communications. Cannot be specified with subnetwork. If neither
      `network` nor `subnet` is specified, the "default" network of the
      project is used, if it exists. A full URL or partial URI. Examples: * `h
      ttps://www.googleapis.com/compute/v1/projects/[project_id]/global/networ
      ks/default` * `projects/[project_id]/global/networks/default` Runtimes
      are managed resources inside Google Infrastructure. Runtimes support the
      following network configurations: * Google Managed Network (Network &
      subnet are empty) * Consumer Project VPC (network & subnet are
      required). Requires configuring Private Service Access. * Shared VPC
      (network & subnet are required). Requires configuring Private Service
      Access.
    nicType: Optional. The type of vNIC to be used on this interface. This may
      be gVNIC or VirtioNet.
    reservedIpRange: Optional. Reserved IP Range name is used for VPC Peering.
      The subnetwork allocation will use the range *name* if it's assigned.
      Example: managed-notebooks-range-c PEERING_RANGE_NAME_3=managed-
      notebooks-range-c gcloud compute addresses create $PEERING_RANGE_NAME_3
      \ --global \ --prefix-length=24 \ --description="Google Cloud Managed
      Notebooks Range 24 c" \ --network=$NETWORK \ --addresses=*********** \
      --purpose=VPC_PEERING Field value will be: `managed-notebooks-range-c`
    shieldedInstanceConfig: Optional. Shielded VM Instance configuration
      settings.
    subnet: Optional. The Compute Engine subnetwork to be used for machine
      communications. Cannot be specified with network. A full URL or partial
      URI are valid. Examples: *
      `https://www.googleapis.com/compute/v1/projects/[project_id]/regions/us-
      east1/subnetworks/sub0` * `projects/[project_id]/regions/us-
      east1/subnetworks/sub0`
    tags: Optional. The Compute Engine network tags to add to runtime (see
      [Add network tags](https://cloud.google.com/vpc/docs/add-remove-network-
      tags)).
    zone: Output only. The zone where the virtual machine is located. If using
      regional request, the notebooks service will pick a location in the
      corresponding runtime region. On a get request, zone will always be
      present. Example: * `us-central1-b`
  """

  class NicTypeValueValuesEnum(_messages.Enum):
    r"""Optional. The type of vNIC to be used on this interface. This may be
    gVNIC or VirtioNet.

    Values:
      UNSPECIFIED_NIC_TYPE: No type specified.
      VIRTIO_NET: VIRTIO
      GVNIC: GVNIC
    """
    UNSPECIFIED_NIC_TYPE = 0
    VIRTIO_NET = 1
    GVNIC = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class GuestAttributesValue(_messages.Message):
    r"""Output only. The Compute Engine guest attributes. (see [Project and
    instance guest attributes](https://cloud.google.com/compute/docs/storing-
    retrieving-metadata#guest_attributes)).

    Messages:
      AdditionalProperty: An additional property for a GuestAttributesValue
        object.

    Fields:
      additionalProperties: Additional properties of type GuestAttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a GuestAttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The labels to associate with this runtime. Label **keys**
    must contain 1 to 63 characters, and must conform to [RFC
    1035](https://www.ietf.org/rfc/rfc1035.txt). Label **values** may be
    empty, but, if present, must contain 1 to 63 characters, and must conform
    to [RFC 1035](https://www.ietf.org/rfc/rfc1035.txt). No more than 32
    labels can be associated with a cluster.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Optional. The Compute Engine metadata entries to add to virtual
    machine. (see [Project and instance
    metadata](https://cloud.google.com/compute/docs/storing-retrieving-
    metadata#project_and_instance_metadata)).

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Additional properties of type MetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  acceleratorConfig = _messages.MessageField('RuntimeAcceleratorConfig', 1)
  bootImage = _messages.MessageField('BootImage', 2)
  containerImages = _messages.MessageField('ContainerImage', 3, repeated=True)
  dataDisk = _messages.MessageField('LocalDisk', 4)
  encryptionConfig = _messages.MessageField('EncryptionConfig', 5)
  guestAttributes = _messages.MessageField('GuestAttributesValue', 6)
  internalIpOnly = _messages.BooleanField(7)
  labels = _messages.MessageField('LabelsValue', 8)
  machineType = _messages.StringField(9)
  metadata = _messages.MessageField('MetadataValue', 10)
  network = _messages.StringField(11)
  nicType = _messages.EnumField('NicTypeValueValuesEnum', 12)
  reservedIpRange = _messages.StringField(13)
  shieldedInstanceConfig = _messages.MessageField('RuntimeShieldedInstanceConfig', 14)
  subnet = _messages.StringField(15)
  tags = _messages.StringField(16, repeated=True)
  zone = _messages.StringField(17)


class VmImage(_messages.Message):
  r"""Definition of a custom Compute Engine virtual machine image for starting
  a notebook instance with the environment installed directly on the VM.

  Fields:
    imageFamily: Use this VM image family to find the image; the newest image
      in this family will be used.
    imageName: Use VM image name to find the image.
    project: Required. The name of the Google Cloud project that this VM image
      belongs to. Format: `{project_id}`
  """

  imageFamily = _messages.StringField(1)
  imageName = _messages.StringField(2)
  project = _messages.StringField(3)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    NotebooksProjectsLocationsInstancesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    NotebooksProjectsLocationsRuntimesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
