"""Generated client library for notebooks version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.notebooks.v1 import notebooks_v1_messages as messages


class NotebooksV1(base_api.BaseApiClient):
  """Generated client library for service notebooks version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://notebooks.googleapis.com/'
  MTLS_BASE_URL = 'https://notebooks.mtls.googleapis.com/'

  _PACKAGE = 'notebooks'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'NotebooksV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new notebooks handle."""
    url = url or self.BASE_URL
    super(NotebooksV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_environments = self.ProjectsLocationsEnvironmentsService(self)
    self.projects_locations_executions = self.ProjectsLocationsExecutionsService(self)
    self.projects_locations_instances = self.ProjectsLocationsInstancesService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_runtimes = self.ProjectsLocationsRuntimesService(self)
    self.projects_locations_schedules = self.ProjectsLocationsSchedulesService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsEnvironmentsService(base_api.BaseApiService):
    """Service class for the projects_locations_environments resource."""

    _NAME = 'projects_locations_environments'

    def __init__(self, client):
      super(NotebooksV1.ProjectsLocationsEnvironmentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Environment.

      Args:
        request: (NotebooksProjectsLocationsEnvironmentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/environments',
        http_method='POST',
        method_id='notebooks.projects.locations.environments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['environmentId'],
        relative_path='v1/{+parent}/environments',
        request_field='environment',
        request_type_name='NotebooksProjectsLocationsEnvironmentsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Environment.

      Args:
        request: (NotebooksProjectsLocationsEnvironmentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/environments/{environmentsId}',
        http_method='DELETE',
        method_id='notebooks.projects.locations.environments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NotebooksProjectsLocationsEnvironmentsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Environment.

      Args:
        request: (NotebooksProjectsLocationsEnvironmentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Environment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/environments/{environmentsId}',
        http_method='GET',
        method_id='notebooks.projects.locations.environments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NotebooksProjectsLocationsEnvironmentsGetRequest',
        response_type_name='Environment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists environments in a project.

      Args:
        request: (NotebooksProjectsLocationsEnvironmentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEnvironmentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/environments',
        http_method='GET',
        method_id='notebooks.projects.locations.environments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/environments',
        request_field='',
        request_type_name='NotebooksProjectsLocationsEnvironmentsListRequest',
        response_type_name='ListEnvironmentsResponse',
        supports_download=False,
    )

  class ProjectsLocationsExecutionsService(base_api.BaseApiService):
    """Service class for the projects_locations_executions resource."""

    _NAME = 'projects_locations_executions'

    def __init__(self, client):
      super(NotebooksV1.ProjectsLocationsExecutionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Execution in a given project and location.

      Args:
        request: (NotebooksProjectsLocationsExecutionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/executions',
        http_method='POST',
        method_id='notebooks.projects.locations.executions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['executionId'],
        relative_path='v1/{+parent}/executions',
        request_field='execution',
        request_type_name='NotebooksProjectsLocationsExecutionsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes execution.

      Args:
        request: (NotebooksProjectsLocationsExecutionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/executions/{executionsId}',
        http_method='DELETE',
        method_id='notebooks.projects.locations.executions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NotebooksProjectsLocationsExecutionsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of executions.

      Args:
        request: (NotebooksProjectsLocationsExecutionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Execution) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/executions/{executionsId}',
        http_method='GET',
        method_id='notebooks.projects.locations.executions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NotebooksProjectsLocationsExecutionsGetRequest',
        response_type_name='Execution',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists executions in a given project and location.

      Args:
        request: (NotebooksProjectsLocationsExecutionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListExecutionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/executions',
        http_method='GET',
        method_id='notebooks.projects.locations.executions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/executions',
        request_field='',
        request_type_name='NotebooksProjectsLocationsExecutionsListRequest',
        response_type_name='ListExecutionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsInstancesService(base_api.BaseApiService):
    """Service class for the projects_locations_instances resource."""

    _NAME = 'projects_locations_instances'

    def __init__(self, client):
      super(NotebooksV1.ProjectsLocationsInstancesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Instance in a given project and location.

      Args:
        request: (NotebooksProjectsLocationsInstancesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances',
        http_method='POST',
        method_id='notebooks.projects.locations.instances.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['instanceId'],
        relative_path='v1/{+parent}/instances',
        request_field='instance',
        request_type_name='NotebooksProjectsLocationsInstancesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Instance.

      Args:
        request: (NotebooksProjectsLocationsInstancesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}',
        http_method='DELETE',
        method_id='notebooks.projects.locations.instances.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NotebooksProjectsLocationsInstancesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Diagnose(self, request, global_params=None):
      r"""Creates a Diagnostic File and runs Diagnostic Tool given an Instance.

      Args:
        request: (NotebooksProjectsLocationsInstancesDiagnoseRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Diagnose')
      return self._RunMethod(
          config, request, global_params=global_params)

    Diagnose.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:diagnose',
        http_method='POST',
        method_id='notebooks.projects.locations.instances.diagnose',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:diagnose',
        request_field='diagnoseInstanceRequest',
        request_type_name='NotebooksProjectsLocationsInstancesDiagnoseRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Instance.

      Args:
        request: (NotebooksProjectsLocationsInstancesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Instance) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}',
        http_method='GET',
        method_id='notebooks.projects.locations.instances.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NotebooksProjectsLocationsInstancesGetRequest',
        response_type_name='Instance',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NotebooksProjectsLocationsInstancesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:getIamPolicy',
        http_method='GET',
        method_id='notebooks.projects.locations.instances.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NotebooksProjectsLocationsInstancesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def GetInstanceHealth(self, request, global_params=None):
      r"""Checks whether a notebook instance is healthy.

      Args:
        request: (NotebooksProjectsLocationsInstancesGetInstanceHealthRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GetInstanceHealthResponse) The response message.
      """
      config = self.GetMethodConfig('GetInstanceHealth')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetInstanceHealth.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:getInstanceHealth',
        http_method='GET',
        method_id='notebooks.projects.locations.instances.getInstanceHealth',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:getInstanceHealth',
        request_field='',
        request_type_name='NotebooksProjectsLocationsInstancesGetInstanceHealthRequest',
        response_type_name='GetInstanceHealthResponse',
        supports_download=False,
    )

    def IsUpgradeable(self, request, global_params=None):
      r"""Checks whether a notebook instance is upgradable.

      Args:
        request: (NotebooksProjectsLocationsInstancesIsUpgradeableRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (IsInstanceUpgradeableResponse) The response message.
      """
      config = self.GetMethodConfig('IsUpgradeable')
      return self._RunMethod(
          config, request, global_params=global_params)

    IsUpgradeable.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:isUpgradeable',
        http_method='GET',
        method_id='notebooks.projects.locations.instances.isUpgradeable',
        ordered_params=['notebookInstance'],
        path_params=['notebookInstance'],
        query_params=['type'],
        relative_path='v1/{+notebookInstance}:isUpgradeable',
        request_field='',
        request_type_name='NotebooksProjectsLocationsInstancesIsUpgradeableRequest',
        response_type_name='IsInstanceUpgradeableResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists instances in a given project and location.

      Args:
        request: (NotebooksProjectsLocationsInstancesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListInstancesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances',
        http_method='GET',
        method_id='notebooks.projects.locations.instances.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/instances',
        request_field='',
        request_type_name='NotebooksProjectsLocationsInstancesListRequest',
        response_type_name='ListInstancesResponse',
        supports_download=False,
    )

    def Migrate(self, request, global_params=None):
      r"""Migrates an existing User-Managed Notebook to Workbench Instances.

      Args:
        request: (NotebooksProjectsLocationsInstancesMigrateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Migrate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Migrate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:migrate',
        http_method='POST',
        method_id='notebooks.projects.locations.instances.migrate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:migrate',
        request_field='migrateInstanceRequest',
        request_type_name='NotebooksProjectsLocationsInstancesMigrateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Register(self, request, global_params=None):
      r"""Registers an existing legacy notebook instance to the Notebooks API server. Legacy instances are instances created with the legacy Compute Engine calls. They are not manageable by the Notebooks API out of the box. This call makes these instances manageable by the Notebooks API.

      Args:
        request: (NotebooksProjectsLocationsInstancesRegisterRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Register')
      return self._RunMethod(
          config, request, global_params=global_params)

    Register.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances:register',
        http_method='POST',
        method_id='notebooks.projects.locations.instances.register',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/instances:register',
        request_field='registerInstanceRequest',
        request_type_name='NotebooksProjectsLocationsInstancesRegisterRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Report(self, request, global_params=None):
      r"""Allows notebook instances to report their latest instance information to the Notebooks API server. The server will merge the reported information to the instance metadata store. Do not use this method directly.

      Args:
        request: (NotebooksProjectsLocationsInstancesReportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Report')
      return self._RunMethod(
          config, request, global_params=global_params)

    Report.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:report',
        http_method='POST',
        method_id='notebooks.projects.locations.instances.report',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:report',
        request_field='reportInstanceInfoRequest',
        request_type_name='NotebooksProjectsLocationsInstancesReportRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def ReportEvent(self, request, global_params=None):
      r"""Reports and processes an instance event.

      Args:
        request: (NotebooksProjectsLocationsInstancesReportEventRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('ReportEvent')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReportEvent.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:reportEvent',
        http_method='POST',
        method_id='notebooks.projects.locations.instances.reportEvent',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:reportEvent',
        request_field='reportInstanceEventRequest',
        request_type_name='NotebooksProjectsLocationsInstancesReportEventRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Reset(self, request, global_params=None):
      r"""Resets a notebook instance.

      Args:
        request: (NotebooksProjectsLocationsInstancesResetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Reset')
      return self._RunMethod(
          config, request, global_params=global_params)

    Reset.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:reset',
        http_method='POST',
        method_id='notebooks.projects.locations.instances.reset',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:reset',
        request_field='resetInstanceRequest',
        request_type_name='NotebooksProjectsLocationsInstancesResetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Rollback(self, request, global_params=None):
      r"""Rollbacks a notebook instance to the previous version.

      Args:
        request: (NotebooksProjectsLocationsInstancesRollbackRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Rollback')
      return self._RunMethod(
          config, request, global_params=global_params)

    Rollback.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:rollback',
        http_method='POST',
        method_id='notebooks.projects.locations.instances.rollback',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:rollback',
        request_field='rollbackInstanceRequest',
        request_type_name='NotebooksProjectsLocationsInstancesRollbackRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetAccelerator(self, request, global_params=None):
      r"""Updates the guest accelerators of a single Instance.

      Args:
        request: (NotebooksProjectsLocationsInstancesSetAcceleratorRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('SetAccelerator')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetAccelerator.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:setAccelerator',
        http_method='PATCH',
        method_id='notebooks.projects.locations.instances.setAccelerator',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:setAccelerator',
        request_field='setInstanceAcceleratorRequest',
        request_type_name='NotebooksProjectsLocationsInstancesSetAcceleratorRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NotebooksProjectsLocationsInstancesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:setIamPolicy',
        http_method='POST',
        method_id='notebooks.projects.locations.instances.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='NotebooksProjectsLocationsInstancesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def SetLabels(self, request, global_params=None):
      r"""Replaces all the labels of an Instance.

      Args:
        request: (NotebooksProjectsLocationsInstancesSetLabelsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('SetLabels')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetLabels.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:setLabels',
        http_method='PATCH',
        method_id='notebooks.projects.locations.instances.setLabels',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:setLabels',
        request_field='setInstanceLabelsRequest',
        request_type_name='NotebooksProjectsLocationsInstancesSetLabelsRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetMachineType(self, request, global_params=None):
      r"""Updates the machine type of a single Instance.

      Args:
        request: (NotebooksProjectsLocationsInstancesSetMachineTypeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('SetMachineType')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetMachineType.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:setMachineType',
        http_method='PATCH',
        method_id='notebooks.projects.locations.instances.setMachineType',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:setMachineType',
        request_field='setInstanceMachineTypeRequest',
        request_type_name='NotebooksProjectsLocationsInstancesSetMachineTypeRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Start(self, request, global_params=None):
      r"""Starts a notebook instance.

      Args:
        request: (NotebooksProjectsLocationsInstancesStartRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Start')
      return self._RunMethod(
          config, request, global_params=global_params)

    Start.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:start',
        http_method='POST',
        method_id='notebooks.projects.locations.instances.start',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:start',
        request_field='startInstanceRequest',
        request_type_name='NotebooksProjectsLocationsInstancesStartRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Stop(self, request, global_params=None):
      r"""Stops a notebook instance.

      Args:
        request: (NotebooksProjectsLocationsInstancesStopRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Stop')
      return self._RunMethod(
          config, request, global_params=global_params)

    Stop.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:stop',
        http_method='POST',
        method_id='notebooks.projects.locations.instances.stop',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:stop',
        request_field='stopInstanceRequest',
        request_type_name='NotebooksProjectsLocationsInstancesStopRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NotebooksProjectsLocationsInstancesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:testIamPermissions',
        http_method='POST',
        method_id='notebooks.projects.locations.instances.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='NotebooksProjectsLocationsInstancesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def UpdateConfig(self, request, global_params=None):
      r"""Update Notebook Instance configurations.

      Args:
        request: (NotebooksProjectsLocationsInstancesUpdateConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('UpdateConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:updateConfig',
        http_method='PATCH',
        method_id='notebooks.projects.locations.instances.updateConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:updateConfig',
        request_field='updateInstanceConfigRequest',
        request_type_name='NotebooksProjectsLocationsInstancesUpdateConfigRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def UpdateMetadataItems(self, request, global_params=None):
      r"""Add/update metadata items for an instance.

      Args:
        request: (NotebooksProjectsLocationsInstancesUpdateMetadataItemsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (UpdateInstanceMetadataItemsResponse) The response message.
      """
      config = self.GetMethodConfig('UpdateMetadataItems')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateMetadataItems.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:updateMetadataItems',
        http_method='PATCH',
        method_id='notebooks.projects.locations.instances.updateMetadataItems',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:updateMetadataItems',
        request_field='updateInstanceMetadataItemsRequest',
        request_type_name='NotebooksProjectsLocationsInstancesUpdateMetadataItemsRequest',
        response_type_name='UpdateInstanceMetadataItemsResponse',
        supports_download=False,
    )

    def UpdateShieldedInstanceConfig(self, request, global_params=None):
      r"""Updates the Shielded instance configuration of a single Instance.

      Args:
        request: (NotebooksProjectsLocationsInstancesUpdateShieldedInstanceConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('UpdateShieldedInstanceConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateShieldedInstanceConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:updateShieldedInstanceConfig',
        http_method='PATCH',
        method_id='notebooks.projects.locations.instances.updateShieldedInstanceConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:updateShieldedInstanceConfig',
        request_field='updateShieldedInstanceConfigRequest',
        request_type_name='NotebooksProjectsLocationsInstancesUpdateShieldedInstanceConfigRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Upgrade(self, request, global_params=None):
      r"""Upgrades a notebook instance to the latest version.

      Args:
        request: (NotebooksProjectsLocationsInstancesUpgradeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Upgrade')
      return self._RunMethod(
          config, request, global_params=global_params)

    Upgrade.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:upgrade',
        http_method='POST',
        method_id='notebooks.projects.locations.instances.upgrade',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:upgrade',
        request_field='upgradeInstanceRequest',
        request_type_name='NotebooksProjectsLocationsInstancesUpgradeRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def UpgradeInternal(self, request, global_params=None):
      r"""Allows notebook instances to call this endpoint to upgrade themselves. Do not use this method directly.

      Args:
        request: (NotebooksProjectsLocationsInstancesUpgradeInternalRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('UpgradeInternal')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpgradeInternal.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/instances/{instancesId}:upgradeInternal',
        http_method='POST',
        method_id='notebooks.projects.locations.instances.upgradeInternal',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:upgradeInternal',
        request_field='upgradeInstanceInternalRequest',
        request_type_name='NotebooksProjectsLocationsInstancesUpgradeInternalRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(NotebooksV1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (NotebooksProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='notebooks.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='NotebooksProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (NotebooksProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='notebooks.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NotebooksProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (NotebooksProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='notebooks.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NotebooksProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (NotebooksProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='notebooks.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='NotebooksProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsRuntimesService(base_api.BaseApiService):
    """Service class for the projects_locations_runtimes resource."""

    _NAME = 'projects_locations_runtimes'

    def __init__(self, client):
      super(NotebooksV1.ProjectsLocationsRuntimesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Runtime in a given project and location.

      Args:
        request: (NotebooksProjectsLocationsRuntimesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/runtimes',
        http_method='POST',
        method_id='notebooks.projects.locations.runtimes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'runtimeId'],
        relative_path='v1/{+parent}/runtimes',
        request_field='runtime',
        request_type_name='NotebooksProjectsLocationsRuntimesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Runtime.

      Args:
        request: (NotebooksProjectsLocationsRuntimesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/runtimes/{runtimesId}',
        http_method='DELETE',
        method_id='notebooks.projects.locations.runtimes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NotebooksProjectsLocationsRuntimesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Diagnose(self, request, global_params=None):
      r"""Creates a Diagnostic File and runs Diagnostic Tool given a Runtime.

      Args:
        request: (NotebooksProjectsLocationsRuntimesDiagnoseRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Diagnose')
      return self._RunMethod(
          config, request, global_params=global_params)

    Diagnose.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/runtimes/{runtimesId}:diagnose',
        http_method='POST',
        method_id='notebooks.projects.locations.runtimes.diagnose',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:diagnose',
        request_field='diagnoseRuntimeRequest',
        request_type_name='NotebooksProjectsLocationsRuntimesDiagnoseRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Runtime. The location must be a regional endpoint rather than zonal.

      Args:
        request: (NotebooksProjectsLocationsRuntimesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Runtime) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/runtimes/{runtimesId}',
        http_method='GET',
        method_id='notebooks.projects.locations.runtimes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NotebooksProjectsLocationsRuntimesGetRequest',
        response_type_name='Runtime',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NotebooksProjectsLocationsRuntimesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/runtimes/{runtimesId}:getIamPolicy',
        http_method='GET',
        method_id='notebooks.projects.locations.runtimes.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NotebooksProjectsLocationsRuntimesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Runtimes in a given project and location.

      Args:
        request: (NotebooksProjectsLocationsRuntimesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRuntimesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/runtimes',
        http_method='GET',
        method_id='notebooks.projects.locations.runtimes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/runtimes',
        request_field='',
        request_type_name='NotebooksProjectsLocationsRuntimesListRequest',
        response_type_name='ListRuntimesResponse',
        supports_download=False,
    )

    def Migrate(self, request, global_params=None):
      r"""Migrate an existing Runtime to a new Workbench Instance.

      Args:
        request: (NotebooksProjectsLocationsRuntimesMigrateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Migrate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Migrate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/runtimes/{runtimesId}:migrate',
        http_method='POST',
        method_id='notebooks.projects.locations.runtimes.migrate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:migrate',
        request_field='migrateRuntimeRequest',
        request_type_name='NotebooksProjectsLocationsRuntimesMigrateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update Notebook Runtime configuration.

      Args:
        request: (NotebooksProjectsLocationsRuntimesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/runtimes/{runtimesId}',
        http_method='PATCH',
        method_id='notebooks.projects.locations.runtimes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='runtime',
        request_type_name='NotebooksProjectsLocationsRuntimesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def RefreshRuntimeTokenInternal(self, request, global_params=None):
      r"""Gets an access token for the consumer service account that the customer attached to the runtime. Only accessible from the tenant instance.

      Args:
        request: (NotebooksProjectsLocationsRuntimesRefreshRuntimeTokenInternalRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RefreshRuntimeTokenInternalResponse) The response message.
      """
      config = self.GetMethodConfig('RefreshRuntimeTokenInternal')
      return self._RunMethod(
          config, request, global_params=global_params)

    RefreshRuntimeTokenInternal.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/runtimes/{runtimesId}:refreshRuntimeTokenInternal',
        http_method='POST',
        method_id='notebooks.projects.locations.runtimes.refreshRuntimeTokenInternal',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:refreshRuntimeTokenInternal',
        request_field='refreshRuntimeTokenInternalRequest',
        request_type_name='NotebooksProjectsLocationsRuntimesRefreshRuntimeTokenInternalRequest',
        response_type_name='RefreshRuntimeTokenInternalResponse',
        supports_download=False,
    )

    def ReportEvent(self, request, global_params=None):
      r"""Reports and processes a runtime event.

      Args:
        request: (NotebooksProjectsLocationsRuntimesReportEventRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('ReportEvent')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReportEvent.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/runtimes/{runtimesId}:reportEvent',
        http_method='POST',
        method_id='notebooks.projects.locations.runtimes.reportEvent',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:reportEvent',
        request_field='reportRuntimeEventRequest',
        request_type_name='NotebooksProjectsLocationsRuntimesReportEventRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Reset(self, request, global_params=None):
      r"""Resets a Managed Notebook Runtime.

      Args:
        request: (NotebooksProjectsLocationsRuntimesResetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Reset')
      return self._RunMethod(
          config, request, global_params=global_params)

    Reset.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/runtimes/{runtimesId}:reset',
        http_method='POST',
        method_id='notebooks.projects.locations.runtimes.reset',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:reset',
        request_field='resetRuntimeRequest',
        request_type_name='NotebooksProjectsLocationsRuntimesResetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NotebooksProjectsLocationsRuntimesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/runtimes/{runtimesId}:setIamPolicy',
        http_method='POST',
        method_id='notebooks.projects.locations.runtimes.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='NotebooksProjectsLocationsRuntimesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def Start(self, request, global_params=None):
      r"""Starts a Managed Notebook Runtime. Perform "Start" on GPU instances; "Resume" on CPU instances See: https://cloud.google.com/compute/docs/instances/stop-start-instance https://cloud.google.com/compute/docs/instances/suspend-resume-instance.

      Args:
        request: (NotebooksProjectsLocationsRuntimesStartRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Start')
      return self._RunMethod(
          config, request, global_params=global_params)

    Start.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/runtimes/{runtimesId}:start',
        http_method='POST',
        method_id='notebooks.projects.locations.runtimes.start',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:start',
        request_field='startRuntimeRequest',
        request_type_name='NotebooksProjectsLocationsRuntimesStartRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Stop(self, request, global_params=None):
      r"""Stops a Managed Notebook Runtime. Perform "Stop" on GPU instances; "Suspend" on CPU instances See: https://cloud.google.com/compute/docs/instances/stop-start-instance https://cloud.google.com/compute/docs/instances/suspend-resume-instance.

      Args:
        request: (NotebooksProjectsLocationsRuntimesStopRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Stop')
      return self._RunMethod(
          config, request, global_params=global_params)

    Stop.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/runtimes/{runtimesId}:stop',
        http_method='POST',
        method_id='notebooks.projects.locations.runtimes.stop',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:stop',
        request_field='stopRuntimeRequest',
        request_type_name='NotebooksProjectsLocationsRuntimesStopRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Switch(self, request, global_params=None):
      r"""Switch a Managed Notebook Runtime.

      Args:
        request: (NotebooksProjectsLocationsRuntimesSwitchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Switch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Switch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/runtimes/{runtimesId}:switch',
        http_method='POST',
        method_id='notebooks.projects.locations.runtimes.switch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:switch',
        request_field='switchRuntimeRequest',
        request_type_name='NotebooksProjectsLocationsRuntimesSwitchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NotebooksProjectsLocationsRuntimesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/runtimes/{runtimesId}:testIamPermissions',
        http_method='POST',
        method_id='notebooks.projects.locations.runtimes.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='NotebooksProjectsLocationsRuntimesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def Upgrade(self, request, global_params=None):
      r"""Upgrades a Managed Notebook Runtime to the latest version.

      Args:
        request: (NotebooksProjectsLocationsRuntimesUpgradeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Upgrade')
      return self._RunMethod(
          config, request, global_params=global_params)

    Upgrade.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/runtimes/{runtimesId}:upgrade',
        http_method='POST',
        method_id='notebooks.projects.locations.runtimes.upgrade',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:upgrade',
        request_field='upgradeRuntimeRequest',
        request_type_name='NotebooksProjectsLocationsRuntimesUpgradeRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsSchedulesService(base_api.BaseApiService):
    """Service class for the projects_locations_schedules resource."""

    _NAME = 'projects_locations_schedules'

    def __init__(self, client):
      super(NotebooksV1.ProjectsLocationsSchedulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Scheduled Notebook in a given project and location.

      Args:
        request: (NotebooksProjectsLocationsSchedulesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schedules',
        http_method='POST',
        method_id='notebooks.projects.locations.schedules.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['scheduleId'],
        relative_path='v1/{+parent}/schedules',
        request_field='schedule',
        request_type_name='NotebooksProjectsLocationsSchedulesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes schedule and all underlying jobs.

      Args:
        request: (NotebooksProjectsLocationsSchedulesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schedules/{schedulesId}',
        http_method='DELETE',
        method_id='notebooks.projects.locations.schedules.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NotebooksProjectsLocationsSchedulesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of schedule.

      Args:
        request: (NotebooksProjectsLocationsSchedulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Schedule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schedules/{schedulesId}',
        http_method='GET',
        method_id='notebooks.projects.locations.schedules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NotebooksProjectsLocationsSchedulesGetRequest',
        response_type_name='Schedule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists schedules in a given project and location.

      Args:
        request: (NotebooksProjectsLocationsSchedulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSchedulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schedules',
        http_method='GET',
        method_id='notebooks.projects.locations.schedules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/schedules',
        request_field='',
        request_type_name='NotebooksProjectsLocationsSchedulesListRequest',
        response_type_name='ListSchedulesResponse',
        supports_download=False,
    )

    def Trigger(self, request, global_params=None):
      r"""Triggers execution of an existing schedule.

      Args:
        request: (NotebooksProjectsLocationsSchedulesTriggerRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Trigger')
      return self._RunMethod(
          config, request, global_params=global_params)

    Trigger.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/schedules/{schedulesId}:trigger',
        http_method='POST',
        method_id='notebooks.projects.locations.schedules.trigger',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:trigger',
        request_field='triggerScheduleRequest',
        request_type_name='NotebooksProjectsLocationsSchedulesTriggerRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(NotebooksV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (NotebooksProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='notebooks.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NotebooksProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (NotebooksProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='notebooks.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/locations',
        request_field='',
        request_type_name='NotebooksProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(NotebooksV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
