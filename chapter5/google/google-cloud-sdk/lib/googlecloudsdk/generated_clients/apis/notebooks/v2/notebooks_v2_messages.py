"""Generated message classes for notebooks version v2.

Notebooks API is used to manage notebook resources in Google Cloud.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'notebooks'


class AcceleratorConfig(_messages.Message):
  r"""An accelerator configuration for a VM instance Definition of a hardware
  accelerator. Note that there is no check on `type` and `core_count`
  combinations. TPUs are not supported. See [GPUs on Compute
  Engine](https://cloud.google.com/compute/docs/gpus/#gpus-list) to find a
  valid combination.

  Enums:
    TypeValueValuesEnum: Optional. Type of this accelerator.

  Fields:
    coreCount: Optional. Count of cores of this accelerator.
    type: Optional. Type of this accelerator.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Optional. Type of this accelerator.

    Values:
      ACCELERATOR_TYPE_UNSPECIFIED: Accelerator type is not specified.
      NVIDIA_TESLA_P100: Accelerator type is Nvidia Tesla P100.
      NVIDIA_TESLA_V100: Accelerator type is Nvidia Tesla V100.
      NVIDIA_TESLA_P4: Accelerator type is Nvidia Tesla P4.
      NVIDIA_TESLA_T4: Accelerator type is Nvidia Tesla T4.
      NVIDIA_TESLA_A100: Accelerator type is Nvidia Tesla A100 - 40GB.
      NVIDIA_A100_80GB: Accelerator type is Nvidia Tesla A100 - 80GB.
      NVIDIA_L4: Accelerator type is Nvidia Tesla L4.
      NVIDIA_H100_80GB: Accelerator type is Nvidia Tesla H100 - 80GB.
      NVIDIA_H100_MEGA_80GB: Accelerator type is Nvidia Tesla H100 - MEGA
        80GB.
      NVIDIA_TESLA_T4_VWS: Accelerator type is NVIDIA Tesla T4 Virtual
        Workstations.
      NVIDIA_TESLA_P100_VWS: Accelerator type is NVIDIA Tesla P100 Virtual
        Workstations.
      NVIDIA_TESLA_P4_VWS: Accelerator type is NVIDIA Tesla P4 Virtual
        Workstations.
    """
    ACCELERATOR_TYPE_UNSPECIFIED = 0
    NVIDIA_TESLA_P100 = 1
    NVIDIA_TESLA_V100 = 2
    NVIDIA_TESLA_P4 = 3
    NVIDIA_TESLA_T4 = 4
    NVIDIA_TESLA_A100 = 5
    NVIDIA_A100_80GB = 6
    NVIDIA_L4 = 7
    NVIDIA_H100_80GB = 8
    NVIDIA_H100_MEGA_80GB = 9
    NVIDIA_TESLA_T4_VWS = 10
    NVIDIA_TESLA_P100_VWS = 11
    NVIDIA_TESLA_P4_VWS = 12

  coreCount = _messages.IntegerField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class AccessConfig(_messages.Message):
  r"""An access configuration attached to an instance's network interface.

  Fields:
    externalIp: An external IP address associated with this instance. Specify
      an unused static external IP address available to the project or leave
      this field undefined to use an IP from a shared ephemeral IP address
      pool. If you specify a static external IP address, it must live in the
      same region as the zone of the instance.
  """

  externalIp = _messages.StringField(1)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class BootDisk(_messages.Message):
  r"""The definition of a boot disk.

  Enums:
    DiskEncryptionValueValuesEnum: Optional. Input only. Disk encryption
      method used on the boot and data disks, defaults to GMEK.
    DiskTypeValueValuesEnum: Optional. Indicates the type of the disk.

  Fields:
    diskEncryption: Optional. Input only. Disk encryption method used on the
      boot and data disks, defaults to GMEK.
    diskSizeGb: Optional. The size of the boot disk in GB attached to this
      instance, up to a maximum of 64000 GB (64 TB). If not specified, this
      defaults to the recommended value of 150GB.
    diskType: Optional. Indicates the type of the disk.
    kmsKey: Optional. Input only. The KMS key used to encrypt the disks, only
      applicable if disk_encryption is CMEK. Format: `projects/{project_id}/lo
      cations/{location}/keyRings/{key_ring_id}/cryptoKeys/{key_id}` Learn
      more about using your own encryption keys.
  """

  class DiskEncryptionValueValuesEnum(_messages.Enum):
    r"""Optional. Input only. Disk encryption method used on the boot and data
    disks, defaults to GMEK.

    Values:
      DISK_ENCRYPTION_UNSPECIFIED: Disk encryption is not specified.
      GMEK: Use Google managed encryption keys to encrypt the boot disk.
      CMEK: Use customer managed encryption keys to encrypt the boot disk.
    """
    DISK_ENCRYPTION_UNSPECIFIED = 0
    GMEK = 1
    CMEK = 2

  class DiskTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Indicates the type of the disk.

    Values:
      DISK_TYPE_UNSPECIFIED: Disk type not set.
      PD_STANDARD: Standard persistent disk type.
      PD_SSD: SSD persistent disk type.
      PD_BALANCED: Balanced persistent disk type.
      PD_EXTREME: Extreme persistent disk type.
    """
    DISK_TYPE_UNSPECIFIED = 0
    PD_STANDARD = 1
    PD_SSD = 2
    PD_BALANCED = 3
    PD_EXTREME = 4

  diskEncryption = _messages.EnumField('DiskEncryptionValueValuesEnum', 1)
  diskSizeGb = _messages.IntegerField(2)
  diskType = _messages.EnumField('DiskTypeValueValuesEnum', 3)
  kmsKey = _messages.StringField(4)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CheckAuthorizationRequest(_messages.Message):
  r"""Request message for checking authorization for the instance owner.

  Messages:
    AuthorizationDetailsValue: Optional. The details of the OAuth
      authorization response. This may include additional params such as
      dry_run, version_info, origin, propagate, etc.

  Fields:
    authorizationDetails: Optional. The details of the OAuth authorization
      response. This may include additional params such as dry_run,
      version_info, origin, propagate, etc.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AuthorizationDetailsValue(_messages.Message):
    r"""Optional. The details of the OAuth authorization response. This may
    include additional params such as dry_run, version_info, origin,
    propagate, etc.

    Messages:
      AdditionalProperty: An additional property for a
        AuthorizationDetailsValue object.

    Fields:
      additionalProperties: Additional properties of type
        AuthorizationDetailsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AuthorizationDetailsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  authorizationDetails = _messages.MessageField('AuthorizationDetailsValue', 1)


class CheckAuthorizationResponse(_messages.Message):
  r"""Response message for checking authorization for the instance owner.

  Fields:
    createTime: Output only. Timestamp when this Authorization request was
      created.
    oauth_uri: If the user has not completed OAuth consent, then the oauth_url
      is returned. Otherwise, this field is not set.
    success: Success indicates that the user completed OAuth consent and
      access tokens can be generated.
  """

  createTime = _messages.StringField(1)
  oauth_uri = _messages.StringField(2)
  success = _messages.BooleanField(3)


class CheckInstanceUpgradabilityResponse(_messages.Message):
  r"""Response for checking if a notebook instance is upgradeable.

  Fields:
    upgradeImage: The new image self link this instance will be upgraded to if
      calling the upgrade endpoint. This field will only be populated if field
      upgradeable is true.
    upgradeInfo: Additional information about upgrade.
    upgradeVersion: The version this instance will be upgraded to if calling
      the upgrade endpoint. This field will only be populated if field
      upgradeable is true.
    upgradeable: If an instance is upgradeable.
  """

  upgradeImage = _messages.StringField(1)
  upgradeInfo = _messages.StringField(2)
  upgradeVersion = _messages.StringField(3)
  upgradeable = _messages.BooleanField(4)


class ConfidentialInstanceConfig(_messages.Message):
  r"""A set of Confidential Instance options.

  Enums:
    ConfidentialInstanceTypeValueValuesEnum: Optional. Defines the type of
      technology used by the confidential instance.

  Fields:
    confidentialInstanceType: Optional. Defines the type of technology used by
      the confidential instance.
  """

  class ConfidentialInstanceTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Defines the type of technology used by the confidential
    instance.

    Values:
      CONFIDENTIAL_INSTANCE_TYPE_UNSPECIFIED: No type specified. Do not use
        this value.
      SEV: AMD Secure Encrypted Virtualization.
    """
    CONFIDENTIAL_INSTANCE_TYPE_UNSPECIFIED = 0
    SEV = 1

  confidentialInstanceType = _messages.EnumField('ConfidentialInstanceTypeValueValuesEnum', 1)


class Config(_messages.Message):
  r"""Response for getting WbI configurations in a location

  Fields:
    availableImages: Output only. The list of available images to create a
      WbI.
    defaultValues: Output only. The default values for configuration.
    disableWorkbenchLegacyCreation: Output only. Flag to disable the creation
      of legacy Workbench notebooks (User-managed notebooks and Google-managed
      notebooks).
    supportedValues: Output only. The supported values for configuration.
  """

  availableImages = _messages.MessageField('ImageRelease', 1, repeated=True)
  defaultValues = _messages.MessageField('DefaultValues', 2)
  disableWorkbenchLegacyCreation = _messages.BooleanField(3)
  supportedValues = _messages.MessageField('SupportedValues', 4)


class ContainerImage(_messages.Message):
  r"""Definition of a container image for starting a notebook instance with
  the environment installed in a container.

  Fields:
    repository: Required. The path to the container image repository. For
      example: `gcr.io/{project_id}/{image_name}`
    tag: Optional. The tag of the container image. If not specified, this
      defaults to the latest tag.
  """

  repository = _messages.StringField(1)
  tag = _messages.StringField(2)


class DataDisk(_messages.Message):
  r"""An instance-attached disk resource.

  Enums:
    DiskEncryptionValueValuesEnum: Optional. Input only. Disk encryption
      method used on the boot and data disks, defaults to GMEK.
    DiskTypeValueValuesEnum: Optional. Input only. Indicates the type of the
      disk.

  Fields:
    diskEncryption: Optional. Input only. Disk encryption method used on the
      boot and data disks, defaults to GMEK.
    diskSizeGb: Optional. The size of the disk in GB attached to this VM
      instance, up to a maximum of 64000 GB (64 TB). If not specified, this
      defaults to 100.
    diskType: Optional. Input only. Indicates the type of the disk.
    kmsKey: Optional. Input only. The KMS key used to encrypt the disks, only
      applicable if disk_encryption is CMEK. Format: `projects/{project_id}/lo
      cations/{location}/keyRings/{key_ring_id}/cryptoKeys/{key_id}` Learn
      more about using your own encryption keys.
    resourcePolicies: Optional. The resource policies to apply to the data
      disk.
  """

  class DiskEncryptionValueValuesEnum(_messages.Enum):
    r"""Optional. Input only. Disk encryption method used on the boot and data
    disks, defaults to GMEK.

    Values:
      DISK_ENCRYPTION_UNSPECIFIED: Disk encryption is not specified.
      GMEK: Use Google managed encryption keys to encrypt the boot disk.
      CMEK: Use customer managed encryption keys to encrypt the boot disk.
    """
    DISK_ENCRYPTION_UNSPECIFIED = 0
    GMEK = 1
    CMEK = 2

  class DiskTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Input only. Indicates the type of the disk.

    Values:
      DISK_TYPE_UNSPECIFIED: Disk type not set.
      PD_STANDARD: Standard persistent disk type.
      PD_SSD: SSD persistent disk type.
      PD_BALANCED: Balanced persistent disk type.
      PD_EXTREME: Extreme persistent disk type.
    """
    DISK_TYPE_UNSPECIFIED = 0
    PD_STANDARD = 1
    PD_SSD = 2
    PD_BALANCED = 3
    PD_EXTREME = 4

  diskEncryption = _messages.EnumField('DiskEncryptionValueValuesEnum', 1)
  diskSizeGb = _messages.IntegerField(2)
  diskType = _messages.EnumField('DiskTypeValueValuesEnum', 3)
  kmsKey = _messages.StringField(4)
  resourcePolicies = _messages.StringField(5, repeated=True)


class DefaultValues(_messages.Message):
  r"""DefaultValues represents the default configuration values.

  Fields:
    machineType: Output only. The default machine type used by the backend if
      not provided by the user.
  """

  machineType = _messages.StringField(1)


class DiagnoseInstanceRequest(_messages.Message):
  r"""Request for creating a notebook instance diagnostic file.

  Fields:
    diagnosticConfig: Required. Defines flags that are used to run the
      diagnostic tool
    timeoutMinutes: Optional. Maximum amount of time in minutes before the
      operation times out.
  """

  diagnosticConfig = _messages.MessageField('DiagnosticConfig', 1)
  timeoutMinutes = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class DiagnosticConfig(_messages.Message):
  r"""Defines flags that are used to run the diagnostic tool

  Fields:
    enableCopyHomeFilesFlag: Optional. Enables flag to copy all
      `/home/<USER>
    enablePacketCaptureFlag: Optional. Enables flag to capture packets from
      the instance for 30 seconds
    enableRepairFlag: Optional. Enables flag to repair service for instance
    gcsBucket: Required. User Cloud Storage bucket location (REQUIRED). Must
      be formatted with path prefix (`gs://$GCS_BUCKET`). Permissions: User
      Managed Notebooks: - storage.buckets.writer: Must be given to the
      project's service account attached to VM. Google Managed Notebooks: -
      storage.buckets.writer: Must be given to the project's service account
      or user credentials attached to VM depending on authentication mode.
      Cloud Storage bucket Log file will be written to
      `gs://$GCS_BUCKET/$RELATIVE_PATH/$VM_DATE_$TIME.tar.gz`
    relativePath: Optional. Defines the relative storage path in the Cloud
      Storage bucket where the diagnostic logs will be written: Default path
      will be the root directory of the Cloud Storage bucket
      (`gs://$GCS_BUCKET/$DATE_$TIME.tar.gz`) Example of full path where Log
      file will be written: `gs://$GCS_BUCKET/$RELATIVE_PATH/`
  """

  enableCopyHomeFilesFlag = _messages.BooleanField(1)
  enablePacketCaptureFlag = _messages.BooleanField(2)
  enableRepairFlag = _messages.BooleanField(3)
  gcsBucket = _messages.StringField(4)
  relativePath = _messages.StringField(5)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Event(_messages.Message):
  r"""The definition of an Event for a managed / semi-managed notebook
  instance.

  Enums:
    TypeValueValuesEnum: Optional. Event type.

  Messages:
    DetailsValue: Optional. Event details. This field is used to pass event
      information.

  Fields:
    details: Optional. Event details. This field is used to pass event
      information.
    reportTime: Optional. Event report time.
    type: Optional. Event type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Optional. Event type.

    Values:
      EVENT_TYPE_UNSPECIFIED: Event is not specified.
      IDLE: The instance / runtime is idle
      HEARTBEAT: The instance / runtime is available. This event indicates
        that instance / runtime underlying compute is operational.
      HEALTH: The instance / runtime health is available. This event indicates
        that instance / runtime health information.
      MAINTENANCE: The instance / runtime is available. This event allows
        instance / runtime to send Host maintenance information to Control
        Plane. https://cloud.google.com/compute/docs/gpus/gpu-host-maintenance
      METADATA_CHANGE: The instance / runtime is available. This event
        indicates that the instance had metadata that needs to be modified.
    """
    EVENT_TYPE_UNSPECIFIED = 0
    IDLE = 1
    HEARTBEAT = 2
    HEALTH = 3
    MAINTENANCE = 4
    METADATA_CHANGE = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValue(_messages.Message):
    r"""Optional. Event details. This field is used to pass event information.

    Messages:
      AdditionalProperty: An additional property for a DetailsValue object.

    Fields:
      additionalProperties: Additional properties of type DetailsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  details = _messages.MessageField('DetailsValue', 1)
  reportTime = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GPUDriverConfig(_messages.Message):
  r"""A GPU driver configuration

  Fields:
    customGpuDriverPath: Optional. Specify a custom Cloud Storage path where
      the GPU driver is stored. If not specified, we'll automatically choose
      from official GPU drivers.
    enableGpuDriver: Optional. Whether the end user authorizes Google Cloud to
      install GPU driver on this VM instance. If this field is empty or set to
      false, the GPU driver won't be installed. Only applicable to instances
      with GPUs.
  """

  customGpuDriverPath = _messages.StringField(1)
  enableGpuDriver = _messages.BooleanField(2)


class GceSetup(_messages.Message):
  r"""The definition of how to configure a VM instance outside of Resources
  and Identity.

  Messages:
    MetadataValue: Optional. Custom metadata to apply to this instance.

  Fields:
    acceleratorConfigs: Optional. The hardware accelerators used on this
      instance. If you use accelerators, make sure that your configuration has
      [enough vCPUs and memory to support the `machine_type` you have
      selected](https://cloud.google.com/compute/docs/gpus/#gpus-list).
      Currently supports only one accelerator configuration.
    bootDisk: Optional. The boot disk for the VM.
    confidentialInstanceConfig: Optional. Confidential instance configuration.
    containerImage: Optional. Use a container image to start the notebook
      instance.
    dataDisks: Optional. Data disks attached to the VM instance. Currently
      supports only one data disk.
    disablePublicIp: Optional. If true, no external IP will be assigned to
      this VM instance.
    enableIpForwarding: Optional. Flag to enable ip forwarding or not, default
      false/off. https://cloud.google.com/vpc/docs/using-routes#canipforward
    gpuDriverConfig: Optional. Configuration for GPU drivers.
    machineType: Optional. The machine type of the VM instance.
      https://cloud.google.com/compute/docs/machine-resource
    metadata: Optional. Custom metadata to apply to this instance.
    minCpuPlatform: Optional. The minimum CPU platform to use for this
      instance. The list of valid values can be found in
      https://cloud.google.com/compute/docs/instances/specify-min-cpu-
      platform#availablezones
    networkInterfaces: Optional. The network interfaces for the VM. Supports
      only one interface.
    reservationAffinity: Optional. Specifies the reservations that this
      instance can consume from.
    serviceAccounts: Optional. The service account that serves as an identity
      for the VM instance. Currently supports only one service account.
    shieldedInstanceConfig: Optional. Shielded VM configuration. [Images using
      supported Shielded VM
      features](https://cloud.google.com/compute/docs/instances/modifying-
      shielded-vm).
    tags: Optional. The Compute Engine network tags to add to runtime (see
      [Add network tags](https://cloud.google.com/vpc/docs/add-remove-network-
      tags)).
    vmImage: Optional. Use a Compute Engine VM image to start the notebook
      instance.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Optional. Custom metadata to apply to this instance.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Additional properties of type MetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  acceleratorConfigs = _messages.MessageField('AcceleratorConfig', 1, repeated=True)
  bootDisk = _messages.MessageField('BootDisk', 2)
  confidentialInstanceConfig = _messages.MessageField('ConfidentialInstanceConfig', 3)
  containerImage = _messages.MessageField('ContainerImage', 4)
  dataDisks = _messages.MessageField('DataDisk', 5, repeated=True)
  disablePublicIp = _messages.BooleanField(6)
  enableIpForwarding = _messages.BooleanField(7)
  gpuDriverConfig = _messages.MessageField('GPUDriverConfig', 8)
  machineType = _messages.StringField(9)
  metadata = _messages.MessageField('MetadataValue', 10)
  minCpuPlatform = _messages.StringField(11)
  networkInterfaces = _messages.MessageField('NetworkInterface', 12, repeated=True)
  reservationAffinity = _messages.MessageField('ReservationAffinity', 13)
  serviceAccounts = _messages.MessageField('ServiceAccount', 14, repeated=True)
  shieldedInstanceConfig = _messages.MessageField('ShieldedInstanceConfig', 15)
  tags = _messages.StringField(16, repeated=True)
  vmImage = _messages.MessageField('VmImage', 17)


class GenerateAccessTokenRequest(_messages.Message):
  r"""Request message for generating an EUC for the instance owner.

  Fields:
    vmToken: Required. The VM identity token (a JWT) for authenticating the
      VM. https://cloud.google.com/compute/docs/instances/verifying-instance-
      identity
  """

  vmToken = _messages.StringField(1)


class GenerateAccessTokenResponse(_messages.Message):
  r"""Response message for generating an EUC for the instance owner.

  Fields:
    access_token: Short-lived access token string which may be used to access
      Google APIs.
    expires_in: The time in seconds when the access token expires. Typically
      that's 3600.
    scope: Space-separated list of scopes contained in the returned token.
      https://cloud.google.com/docs/authentication/token-types#access-contents
    token_type: Type of the returned access token (e.g. "Bearer"). It
      specifies how the token must be used. Bearer tokens may be used by any
      entity without proof of identity.
  """

  access_token = _messages.StringField(1)
  expires_in = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  scope = _messages.StringField(3)
  token_type = _messages.StringField(4)


class ImageRelease(_messages.Message):
  r"""ConfigImage represents an image release available to create a WbI

  Fields:
    imageName: Output only. The name of the image of the form workbench-
      instances-vYYYYmmdd--
    releaseName: Output only. The release of the image of the form m123
  """

  imageName = _messages.StringField(1)
  releaseName = _messages.StringField(2)


class Instance(_messages.Message):
  r"""The definition of a notebook instance.

  Enums:
    HealthStateValueValuesEnum: Output only. Instance health_state.
    StateValueValuesEnum: Output only. The state of this instance.

  Messages:
    HealthInfoValue: Output only. Additional information about instance
      health. Example: healthInfo": { "docker_proxy_agent_status": "1",
      "docker_status": "1", "jupyterlab_api_status": "-1",
      "jupyterlab_status": "-1", "updated": "2020-10-18 09:40:03.573409" }
    LabelsValue: Optional. Labels to apply to this instance. These can be
      later modified by the UpdateInstance method.

  Fields:
    createTime: Output only. Instance creation time.
    creator: Output only. Email address of entity that sent original
      CreateInstance request.
    disableProxyAccess: Optional. If true, the notebook instance will not
      register with the proxy.
    enableDeletionProtection: Optional. If true, deletion protection will be
      enabled for this Workbench Instance. If false, deletion protection will
      be disabled for this Workbench Instance.
    enableManagedEuc: Optional. Flag to enable managed end user credentials
      for the instance.
    enableThirdPartyIdentity: Optional. Flag that specifies that a notebook
      can be accessed with third party identity provider.
    gceSetup: Optional. Compute Engine setup for the notebook. Uses notebook-
      defined fields.
    healthInfo: Output only. Additional information about instance health.
      Example: healthInfo": { "docker_proxy_agent_status": "1",
      "docker_status": "1", "jupyterlab_api_status": "-1",
      "jupyterlab_status": "-1", "updated": "2020-10-18 09:40:03.573409" }
    healthState: Output only. Instance health_state.
    id: Output only. Unique ID of the resource.
    instanceOwners: Optional. The owner of this instance after creation.
      Format: `<EMAIL>` Currently supports one owner only. If not
      specified, all of the service account users of your VM instance's
      service account can use the instance.
    labels: Optional. Labels to apply to this instance. These can be later
      modified by the UpdateInstance method.
    name: Output only. The name of this notebook instance. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    proxyUri: Output only. The proxy endpoint that is used to access the
      Jupyter notebook.
    satisfiesPzi: Output only. Reserved for future use for Zone Isolation.
    satisfiesPzs: Output only. Reserved for future use for Zone Separation.
    state: Output only. The state of this instance.
    thirdPartyProxyUrl: Output only. The workforce pools proxy endpoint that
      is used to access the Jupyter notebook.
    updateTime: Output only. Instance update time.
    upgradeHistory: Output only. The upgrade history of this instance.
  """

  class HealthStateValueValuesEnum(_messages.Enum):
    r"""Output only. Instance health_state.

    Values:
      HEALTH_STATE_UNSPECIFIED: The instance substate is unknown.
      HEALTHY: The instance is known to be in an healthy state (for example,
        critical daemons are running) Applies to ACTIVE state.
      UNHEALTHY: The instance is known to be in an unhealthy state (for
        example, critical daemons are not running) Applies to ACTIVE state.
      AGENT_NOT_INSTALLED: The instance has not installed health monitoring
        agent. Applies to ACTIVE state.
      AGENT_NOT_RUNNING: The instance health monitoring agent is not running.
        Applies to ACTIVE state.
    """
    HEALTH_STATE_UNSPECIFIED = 0
    HEALTHY = 1
    UNHEALTHY = 2
    AGENT_NOT_INSTALLED = 3
    AGENT_NOT_RUNNING = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of this instance.

    Values:
      STATE_UNSPECIFIED: State is not specified.
      STARTING: The control logic is starting the instance.
      PROVISIONING: The control logic is installing required frameworks and
        registering the instance with notebook proxy
      ACTIVE: The instance is running.
      STOPPING: The control logic is stopping the instance.
      STOPPED: The instance is stopped.
      DELETED: The instance is deleted.
      UPGRADING: The instance is upgrading.
      INITIALIZING: The instance is being created.
      SUSPENDING: The instance is suspending.
      SUSPENDED: The instance is suspended.
    """
    STATE_UNSPECIFIED = 0
    STARTING = 1
    PROVISIONING = 2
    ACTIVE = 3
    STOPPING = 4
    STOPPED = 5
    DELETED = 6
    UPGRADING = 7
    INITIALIZING = 8
    SUSPENDING = 9
    SUSPENDED = 10

  @encoding.MapUnrecognizedFields('additionalProperties')
  class HealthInfoValue(_messages.Message):
    r"""Output only. Additional information about instance health. Example:
    healthInfo": { "docker_proxy_agent_status": "1", "docker_status": "1",
    "jupyterlab_api_status": "-1", "jupyterlab_status": "-1", "updated":
    "2020-10-18 09:40:03.573409" }

    Messages:
      AdditionalProperty: An additional property for a HealthInfoValue object.

    Fields:
      additionalProperties: Additional properties of type HealthInfoValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a HealthInfoValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels to apply to this instance. These can be later
    modified by the UpdateInstance method.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  creator = _messages.StringField(2)
  disableProxyAccess = _messages.BooleanField(3)
  enableDeletionProtection = _messages.BooleanField(4)
  enableManagedEuc = _messages.BooleanField(5)
  enableThirdPartyIdentity = _messages.BooleanField(6)
  gceSetup = _messages.MessageField('GceSetup', 7)
  healthInfo = _messages.MessageField('HealthInfoValue', 8)
  healthState = _messages.EnumField('HealthStateValueValuesEnum', 9)
  id = _messages.StringField(10)
  instanceOwners = _messages.StringField(11, repeated=True)
  labels = _messages.MessageField('LabelsValue', 12)
  name = _messages.StringField(13)
  proxyUri = _messages.StringField(14)
  satisfiesPzi = _messages.BooleanField(15)
  satisfiesPzs = _messages.BooleanField(16)
  state = _messages.EnumField('StateValueValuesEnum', 17)
  thirdPartyProxyUrl = _messages.StringField(18)
  updateTime = _messages.StringField(19)
  upgradeHistory = _messages.MessageField('UpgradeHistoryEntry', 20, repeated=True)


class ListInstancesResponse(_messages.Message):
  r"""Response for listing notebook instances.

  Fields:
    instances: A list of returned instances.
    nextPageToken: Page token that can be used to continue listing from the
      last result in the next list call.
    unreachable: Locations that could not be reached. For example, ['us-
      west1-a', 'us-central1-b']. A ListInstancesResponse will only contain
      either instances or unreachables,
  """

  instances = _messages.MessageField('Instance', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class NetworkInterface(_messages.Message):
  r"""The definition of a network interface resource attached to a VM.

  Enums:
    NicTypeValueValuesEnum: Optional. The type of vNIC to be used on this
      interface. This may be gVNIC or VirtioNet.

  Fields:
    accessConfigs: Optional. An array of configurations for this interface.
      Currently, only one access config, ONE_TO_ONE_NAT, is supported. If no
      accessConfigs specified, the instance will have an external internet
      access through an ephemeral external IP address.
    network: Optional. The name of the VPC that this VM instance is in.
      Format: `projects/{project_id}/global/networks/{network_id}`
    nicType: Optional. The type of vNIC to be used on this interface. This may
      be gVNIC or VirtioNet.
    subnet: Optional. The name of the subnet that this VM instance is in.
      Format:
      `projects/{project_id}/regions/{region}/subnetworks/{subnetwork_id}`
  """

  class NicTypeValueValuesEnum(_messages.Enum):
    r"""Optional. The type of vNIC to be used on this interface. This may be
    gVNIC or VirtioNet.

    Values:
      NIC_TYPE_UNSPECIFIED: No type specified.
      VIRTIO_NET: VIRTIO
      GVNIC: GVNIC
    """
    NIC_TYPE_UNSPECIFIED = 0
    VIRTIO_NET = 1
    GVNIC = 2

  accessConfigs = _messages.MessageField('AccessConfig', 1, repeated=True)
  network = _messages.StringField(2)
  nicType = _messages.EnumField('NicTypeValueValuesEnum', 3)
  subnet = _messages.StringField(4)


class NotebooksProjectsLocationsGetRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsInstancesCheckAuthorizationRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesCheckAuthorizationRequest object.

  Fields:
    checkAuthorizationRequest: A CheckAuthorizationRequest resource to be
      passed as the request body.
    name: Required. The name of the Notebook Instance resource. Format:
      `projects/{project}/locations/{location}/instances/{instance}`
  """

  checkAuthorizationRequest = _messages.MessageField('CheckAuthorizationRequest', 1)
  name = _messages.StringField(2, required=True)


class NotebooksProjectsLocationsInstancesCheckUpgradabilityRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesCheckUpgradabilityRequest object.

  Fields:
    notebookInstance: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
  """

  notebookInstance = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsInstancesCreateRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesCreateRequest object.

  Fields:
    instance: A Instance resource to be passed as the request body.
    instanceId: Required. User-defined unique ID of this instance.
    parent: Required. Format:
      `parent=projects/{project_id}/locations/{location}`
    requestId: Optional. Idempotent request UUID.
  """

  instance = _messages.MessageField('Instance', 1)
  instanceId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NotebooksProjectsLocationsInstancesDeleteRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesDeleteRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    requestId: Optional. Idempotent request UUID.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NotebooksProjectsLocationsInstancesDiagnoseRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesDiagnoseRequest object.

  Fields:
    diagnoseInstanceRequest: A DiagnoseInstanceRequest resource to be passed
      as the request body.
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
  """

  diagnoseInstanceRequest = _messages.MessageField('DiagnoseInstanceRequest', 1)
  name = _messages.StringField(2, required=True)


class NotebooksProjectsLocationsInstancesGenerateAccessTokenRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesGenerateAccessTokenRequest object.

  Fields:
    generateAccessTokenRequest: A GenerateAccessTokenRequest resource to be
      passed as the request body.
    name: Required. Format:
      `projects/{project}/locations/{location}/instances/{instance_id}`
  """

  generateAccessTokenRequest = _messages.MessageField('GenerateAccessTokenRequest', 1)
  name = _messages.StringField(2, required=True)


class NotebooksProjectsLocationsInstancesGetConfigRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesGetConfigRequest object.

  Fields:
    name: Required. Format: `projects/{project_id}/locations/{location}`
  """

  name = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsInstancesGetIamPolicyRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NotebooksProjectsLocationsInstancesGetRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesGetRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
  """

  name = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsInstancesListRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesListRequest object.

  Fields:
    filter: Optional. List filter.
    orderBy: Optional. Sort results. Supported values are "name", "name desc"
      or "" (unsorted).
    pageSize: Optional. Maximum return size of the list call.
    pageToken: Optional. A previous returned page token that can be used to
      continue listing from the last result.
    parent: Required. Format:
      `parent=projects/{project_id}/locations/{location}`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NotebooksProjectsLocationsInstancesPatchRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesPatchRequest object.

  Fields:
    instance: A Instance resource to be passed as the request body.
    name: Output only. The name of this notebook instance. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    requestId: Optional. Idempotent request UUID.
    updateMask: Required. Mask used to update an instance. Updatable fields: *
      `labels` * `gce_setup.min_cpu_platform` * `gce_setup.metadata` *
      `gce_setup.machine_type` * `gce_setup.accelerator_configs` *
      `gce_setup.accelerator_configs.type` *
      `gce_setup.accelerator_configs.core_count` *
      `gce_setup.gpu_driver_config` *
      `gce_setup.gpu_driver_config.enable_gpu_driver` *
      `gce_setup.gpu_driver_config.custom_gpu_driver_path` *
      `gce_setup.shielded_instance_config` *
      `gce_setup.shielded_instance_config.enable_secure_boot` *
      `gce_setup.shielded_instance_config.enable_vtpm` *
      `gce_setup.shielded_instance_config.enable_integrity_monitoring` *
      `gce_setup.reservation_affinity` *
      `gce_setup.reservation_affinity.consume_reservation_type` *
      `gce_setup.reservation_affinity.key` *
      `gce_setup.reservation_affinity.values` * `gce_setup.tags` *
      `gce_setup.container_image` * `gce_setup.container_image.repository` *
      `gce_setup.container_image.tag` * `gce_setup.disable_public_ip` *
      `disable_proxy_access`
  """

  instance = _messages.MessageField('Instance', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NotebooksProjectsLocationsInstancesReportInfoSystemRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesReportInfoSystemRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    reportInstanceInfoSystemRequest: A ReportInstanceInfoSystemRequest
      resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  reportInstanceInfoSystemRequest = _messages.MessageField('ReportInstanceInfoSystemRequest', 2)


class NotebooksProjectsLocationsInstancesResetRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesResetRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    resetInstanceRequest: A ResetInstanceRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  resetInstanceRequest = _messages.MessageField('ResetInstanceRequest', 2)


class NotebooksProjectsLocationsInstancesResizeDiskRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesResizeDiskRequest object.

  Fields:
    notebookInstance: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    resizeDiskRequest: A ResizeDiskRequest resource to be passed as the
      request body.
  """

  notebookInstance = _messages.StringField(1, required=True)
  resizeDiskRequest = _messages.MessageField('ResizeDiskRequest', 2)


class NotebooksProjectsLocationsInstancesRestoreRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesRestoreRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    restoreInstanceRequest: A RestoreInstanceRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  restoreInstanceRequest = _messages.MessageField('RestoreInstanceRequest', 2)


class NotebooksProjectsLocationsInstancesRollbackRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesRollbackRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    rollbackInstanceRequest: A RollbackInstanceRequest resource to be passed
      as the request body.
  """

  name = _messages.StringField(1, required=True)
  rollbackInstanceRequest = _messages.MessageField('RollbackInstanceRequest', 2)


class NotebooksProjectsLocationsInstancesSetIamPolicyRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class NotebooksProjectsLocationsInstancesStartRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesStartRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    startInstanceRequest: A StartInstanceRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  startInstanceRequest = _messages.MessageField('StartInstanceRequest', 2)


class NotebooksProjectsLocationsInstancesStopRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesStopRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    stopInstanceRequest: A StopInstanceRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  stopInstanceRequest = _messages.MessageField('StopInstanceRequest', 2)


class NotebooksProjectsLocationsInstancesTestIamPermissionsRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class NotebooksProjectsLocationsInstancesUpgradeRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesUpgradeRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    upgradeInstanceRequest: A UpgradeInstanceRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  upgradeInstanceRequest = _messages.MessageField('UpgradeInstanceRequest', 2)


class NotebooksProjectsLocationsInstancesUpgradeSystemRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesUpgradeSystemRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    upgradeInstanceSystemRequest: A UpgradeInstanceSystemRequest resource to
      be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  upgradeInstanceSystemRequest = _messages.MessageField('UpgradeInstanceSystemRequest', 2)


class NotebooksProjectsLocationsListRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class NotebooksProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class NotebooksProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: API version used to start the operation.
    createTime: The time the operation was created.
    endTime: The time the operation finished running.
    endpoint: API endpoint name of this operation.
    requestedCancellation: Identifies whether the user has requested
      cancellation of the operation. Operations that have successfully been
      cancelled have google.longrunning.Operation.error value with a
      google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
    statusMessage: Human-readable status of the operation, if any.
    target: Server-defined resource path for the target of the operation.
    verb: Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  endpoint = _messages.StringField(4)
  requestedCancellation = _messages.BooleanField(5)
  statusMessage = _messages.StringField(6)
  target = _messages.StringField(7)
  verb = _messages.StringField(8)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  bindings = _messages.MessageField('Binding', 1, repeated=True)
  etag = _messages.BytesField(2)
  version = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class ReportInstanceInfoSystemRequest(_messages.Message):
  r"""Request for notebook instances to report information to Notebooks API.

  Fields:
    event: Required. The Event to be reported.
    vmId: Required. The VM hardware token for authenticating the VM.
      https://cloud.google.com/compute/docs/instances/verifying-instance-
      identity
  """

  event = _messages.MessageField('Event', 1)
  vmId = _messages.StringField(2)


class ReservationAffinity(_messages.Message):
  r"""A reservation that an instance can consume from.

  Enums:
    ConsumeReservationTypeValueValuesEnum: Required. Specifies the type of
      reservation from which this instance can consume resources:
      RESERVATION_ANY (default), RESERVATION_SPECIFIC, or RESERVATION_NONE.
      See Consuming reserved instances for examples.

  Fields:
    consumeReservationType: Required. Specifies the type of reservation from
      which this instance can consume resources: RESERVATION_ANY (default),
      RESERVATION_SPECIFIC, or RESERVATION_NONE. See Consuming reserved
      instances for examples.
    key: Optional. Corresponds to the label key of a reservation resource. To
      target a RESERVATION_SPECIFIC by name, use
      compute.googleapis.com/reservation-name as the key and specify the name
      of your reservation as its value.
    values: Optional. Corresponds to the label values of a reservation
      resource. This can be either a name to a reservation in the same project
      or "projects/different-project/reservations/some-reservation-name" to
      target a shared reservation in the same zone but in a different project.
  """

  class ConsumeReservationTypeValueValuesEnum(_messages.Enum):
    r"""Required. Specifies the type of reservation from which this instance
    can consume resources: RESERVATION_ANY (default), RESERVATION_SPECIFIC, or
    RESERVATION_NONE. See Consuming reserved instances for examples.

    Values:
      RESERVATION_UNSPECIFIED: Default type.
      RESERVATION_NONE: Do not consume from any allocated capacity.
      RESERVATION_ANY: Consume any reservation available.
      RESERVATION_SPECIFIC: Must consume from a specific reservation. Must
        specify key value fields for specifying the reservations.
    """
    RESERVATION_UNSPECIFIED = 0
    RESERVATION_NONE = 1
    RESERVATION_ANY = 2
    RESERVATION_SPECIFIC = 3

  consumeReservationType = _messages.EnumField('ConsumeReservationTypeValueValuesEnum', 1)
  key = _messages.StringField(2)
  values = _messages.StringField(3, repeated=True)


class ResetInstanceRequest(_messages.Message):
  r"""Request for resetting a notebook instance"""


class ResizeDiskRequest(_messages.Message):
  r"""Request for resizing the notebook instance disks

  Fields:
    bootDisk: Required. The boot disk to be resized. Only disk_size_gb will be
      used.
    dataDisk: Required. The data disk to be resized. Only disk_size_gb will be
      used.
  """

  bootDisk = _messages.MessageField('BootDisk', 1)
  dataDisk = _messages.MessageField('DataDisk', 2)


class RestoreInstanceRequest(_messages.Message):
  r"""Request for restoring the notebook instance from a BackupSource.

  Fields:
    snapshot: Snapshot to be used for restore.
  """

  snapshot = _messages.MessageField('Snapshot', 1)


class RollbackInstanceRequest(_messages.Message):
  r"""Request for rollbacking a notebook instance

  Fields:
    revisionId: Required. Output only. Revision Id
    targetSnapshot: Required. The snapshot for rollback. Example:
      "projects/test-project/global/snapshots/krwlzipynril".
  """

  revisionId = _messages.StringField(1)
  targetSnapshot = _messages.StringField(2)


class ServiceAccount(_messages.Message):
  r"""A service account that acts as an identity.

  Fields:
    email: Optional. Email address of the service account.
    scopes: Output only. The list of scopes to be made available for this
      service account. Set by the CLH to
      https://www.googleapis.com/auth/cloud-platform
  """

  email = _messages.StringField(1)
  scopes = _messages.StringField(2, repeated=True)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
  """

  policy = _messages.MessageField('Policy', 1)


class ShieldedInstanceConfig(_messages.Message):
  r"""A set of Shielded Instance options. See [Images using supported Shielded
  VM features](https://cloud.google.com/compute/docs/instances/modifying-
  shielded-vm). Not all combinations are valid.

  Fields:
    enableIntegrityMonitoring: Optional. Defines whether the VM instance has
      integrity monitoring enabled. Enables monitoring and attestation of the
      boot integrity of the VM instance. The attestation is performed against
      the integrity policy baseline. This baseline is initially derived from
      the implicitly trusted boot image when the VM instance is created.
    enableSecureBoot: Optional. Defines whether the VM instance has Secure
      Boot enabled. Secure Boot helps ensure that the system only runs
      authentic software by verifying the digital signature of all boot
      components, and halting the boot process if signature verification
      fails. Disabled by default.
    enableVtpm: Optional. Defines whether the VM instance has the vTPM
      enabled.
  """

  enableIntegrityMonitoring = _messages.BooleanField(1)
  enableSecureBoot = _messages.BooleanField(2)
  enableVtpm = _messages.BooleanField(3)


class Snapshot(_messages.Message):
  r"""Snapshot represents the snapshot of the data disk used to restore the
  Workbench Instance from. Refers to:
  compute/v1/projects/{project_id}/global/snapshots/{snapshot_id}

  Fields:
    projectId: Required. The project ID of the snapshot.
    snapshotId: Required. The ID of the snapshot.
  """

  projectId = _messages.StringField(1)
  snapshotId = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StartInstanceRequest(_messages.Message):
  r"""Request for starting a notebook instance"""


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StopInstanceRequest(_messages.Message):
  r"""Request for stopping a notebook instance"""


class SupportedValues(_messages.Message):
  r"""SupportedValues represents the values supported by the configuration.

  Fields:
    acceleratorTypes: Output only. The accelerator types supported by WbI.
    machineTypes: Output only. The machine types supported by WbI.
  """

  acceleratorTypes = _messages.StringField(1, repeated=True)
  machineTypes = _messages.StringField(2, repeated=True)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class UpgradeHistoryEntry(_messages.Message):
  r"""The entry of VM image upgrade history.

  Enums:
    ActionValueValuesEnum: Optional. Action. Rolloback or Upgrade.
    StateValueValuesEnum: Output only. The state of this instance upgrade
      history entry.

  Fields:
    action: Optional. Action. Rolloback or Upgrade.
    containerImage: Optional. The container image before this instance
      upgrade.
    createTime: Immutable. The time that this instance upgrade history entry
      is created.
    framework: Optional. The framework of this notebook instance.
    snapshot: Optional. The snapshot of the boot disk of this notebook
      instance before upgrade.
    state: Output only. The state of this instance upgrade history entry.
    targetVersion: Optional. Target VM Version, like m63.
    version: Optional. The version of the notebook instance before this
      upgrade.
    vmImage: Optional. The VM image before this instance upgrade.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Optional. Action. Rolloback or Upgrade.

    Values:
      ACTION_UNSPECIFIED: Operation is not specified.
      UPGRADE: Upgrade.
      ROLLBACK: Rollback.
    """
    ACTION_UNSPECIFIED = 0
    UPGRADE = 1
    ROLLBACK = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of this instance upgrade history entry.

    Values:
      STATE_UNSPECIFIED: State is not specified.
      STARTED: The instance upgrade is started.
      SUCCEEDED: The instance upgrade is succeeded.
      FAILED: The instance upgrade is failed.
    """
    STATE_UNSPECIFIED = 0
    STARTED = 1
    SUCCEEDED = 2
    FAILED = 3

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  containerImage = _messages.StringField(2)
  createTime = _messages.StringField(3)
  framework = _messages.StringField(4)
  snapshot = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  targetVersion = _messages.StringField(7)
  version = _messages.StringField(8)
  vmImage = _messages.StringField(9)


class UpgradeInstanceRequest(_messages.Message):
  r"""Request for upgrading a notebook instance"""


class UpgradeInstanceSystemRequest(_messages.Message):
  r"""Request for upgrading a notebook instance from within the VM

  Fields:
    vmId: Required. The VM hardware token for authenticating the VM.
      https://cloud.google.com/compute/docs/instances/verifying-instance-
      identity
  """

  vmId = _messages.StringField(1)


class VmImage(_messages.Message):
  r"""Definition of a custom Compute Engine virtual machine image for starting
  a notebook instance with the environment installed directly on the VM.

  Fields:
    family: Optional. Use this VM image family to find the image; the newest
      image in this family will be used.
    name: Optional. Use VM image name to find the image.
    project: Required. The name of the Google Cloud project that this VM image
      belongs to. Format: `{project_id}`
  """

  family = _messages.StringField(1)
  name = _messages.StringField(2)
  project = _messages.StringField(3)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    NotebooksProjectsLocationsInstancesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
