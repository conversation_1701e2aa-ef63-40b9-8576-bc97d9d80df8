"""Generated message classes for notebooks version v1beta1.

Notebooks API is used to manage notebook resources in Google Cloud.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'notebooks'


class AcceleratorConfig(_messages.Message):
  r"""Definition of a hardware accelerator. Note that not all combinations of
  `type` and `core_count` are valid. See [GPUs on Compute
  Engine](https://cloud.google.com/compute/docs/gpus/#gpus-list) to find a
  valid combination. TPUs are not supported.

  Enums:
    TypeValueValuesEnum: Type of this accelerator.

  Fields:
    coreCount: Count of cores of this accelerator.
    type: Type of this accelerator.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of this accelerator.

    Values:
      ACCELERATOR_TYPE_UNSPECIFIED: Accelerator type is not specified.
      NVIDIA_TESLA_K80: Accelerator type is Nvidia Tesla K80.
      NVIDIA_TESLA_P100: Accelerator type is Nvidia Tesla P100.
      NVIDIA_TESLA_V100: Accelerator type is Nvidia Tesla V100.
      NVIDIA_TESLA_P4: Accelerator type is Nvidia Tesla P4.
      NVIDIA_TESLA_T4: Accelerator type is Nvidia Tesla T4.
      NVIDIA_TESLA_T4_VWS: Accelerator type is NVIDIA Tesla T4 Virtual
        Workstations.
      NVIDIA_TESLA_P100_VWS: Accelerator type is NVIDIA Tesla P100 Virtual
        Workstations.
      NVIDIA_TESLA_P4_VWS: Accelerator type is NVIDIA Tesla P4 Virtual
        Workstations.
      TPU_V2: (Coming soon) Accelerator type is TPU V2.
      TPU_V3: (Coming soon) Accelerator type is TPU V3.
    """
    ACCELERATOR_TYPE_UNSPECIFIED = 0
    NVIDIA_TESLA_K80 = 1
    NVIDIA_TESLA_P100 = 2
    NVIDIA_TESLA_V100 = 3
    NVIDIA_TESLA_P4 = 4
    NVIDIA_TESLA_T4 = 5
    NVIDIA_TESLA_T4_VWS = 6
    NVIDIA_TESLA_P100_VWS = 7
    NVIDIA_TESLA_P4_VWS = 8
    TPU_V2 = 9
    TPU_V3 = 10

  coreCount = _messages.IntegerField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class ContainerImage(_messages.Message):
  r"""Definition of a container image for starting a notebook instance with
  the environment installed in a container.

  Fields:
    repository: Required. The path to the container image repository. For
      example: `gcr.io/{project_id}/{image_name}`
    tag: The tag of the container image. If not specified, this defaults to
      the latest tag.
  """

  repository = _messages.StringField(1)
  tag = _messages.StringField(2)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Environment(_messages.Message):
  r"""Definition of a software environment that is used to start a notebook
  instance.

  Fields:
    containerImage: Use a container image to start the notebook instance.
    createTime: Output only. The time at which this environment was created.
    description: A brief description of this environment.
    displayName: Display name of this environment for the UI.
    name: Output only. Name of this environment. Format:
      `projects/{project_id}/locations/{location}/environments/{environment_id
      }`
    postStartupScript: Path to a Bash script that automatically runs after a
      notebook instance fully boots up. The path must be a URL or Cloud
      Storage path. Example: `"gs://path-to-file/file-name"`
    vmImage: Use a Compute Engine VM image to start the notebook instance.
  """

  containerImage = _messages.MessageField('ContainerImage', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  displayName = _messages.StringField(4)
  name = _messages.StringField(5)
  postStartupScript = _messages.StringField(6)
  vmImage = _messages.MessageField('VmImage', 7)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class Instance(_messages.Message):
  r"""The definition of a notebook instance.

  Enums:
    BootDiskTypeValueValuesEnum: Input only. The type of the boot disk
      attached to this instance, defaults to standard persistent disk
      (`PD_STANDARD`).
    DataDiskTypeValueValuesEnum: Input only. The type of the data disk
      attached to this instance, defaults to standard persistent disk
      (`PD_STANDARD`).
    DiskEncryptionValueValuesEnum: Input only. Disk encryption method used on
      the boot and data disks, defaults to GMEK.
    NicTypeValueValuesEnum: Optional. The type of vNIC to be used on this
      interface. This may be gVNIC or VirtioNet.
    StateValueValuesEnum: Output only. The state of this instance.

  Messages:
    LabelsValue: Labels to apply to this instance. These can be later modified
      by the setLabels method.
    MetadataValue: Custom metadata to apply to this instance. For example, to
      specify a Cloud Storage bucket for automatic backup, you can use the
      `gcs-data-bucket` metadata tag. Format: `"--metadata=gcs-data-
      bucket=BUCKET"`.

  Fields:
    acceleratorConfig: The hardware accelerator used on this instance. If you
      use accelerators, make sure that your configuration has [enough vCPUs
      and memory to support the `machine_type` you have
      selected](https://cloud.google.com/compute/docs/gpus/#gpus-list).
    bootDiskSizeGb: Input only. The size of the boot disk in GB attached to
      this instance, up to a maximum of 64000 GB (64 TB). The minimum
      recommended value is 100 GB. If not specified, this defaults to 100.
    bootDiskType: Input only. The type of the boot disk attached to this
      instance, defaults to standard persistent disk (`PD_STANDARD`).
    canIpForward: Optional. Flag to enable ip forwarding or not, default
      false/off. https://cloud.google.com/vpc/docs/using-routes#canipforward
    containerImage: Use a container image to start the notebook instance.
    createTime: Output only. Instance creation time.
    customGpuDriverPath: Specify a custom Cloud Storage path where the GPU
      driver is stored. If not specified, we'll automatically choose from
      official GPU drivers.
    dataDiskSizeGb: Input only. The size of the data disk in GB attached to
      this instance, up to a maximum of 64000 GB (64 TB). You can choose the
      size of the data disk based on how big your notebooks and data are. If
      not specified, this defaults to 100.
    dataDiskType: Input only. The type of the data disk attached to this
      instance, defaults to standard persistent disk (`PD_STANDARD`).
    diskEncryption: Input only. Disk encryption method used on the boot and
      data disks, defaults to GMEK.
    installGpuDriver: Whether the end user authorizes Google Cloud to install
      GPU driver on this instance. If this field is empty or set to false, the
      GPU driver won't be installed. Only applicable to instances with GPUs.
    instanceOwners: Input only. The owner of this instance after creation.
      Format: `<EMAIL>` Currently supports one owner only. If not
      specified, all of the service account users of your VM instance's
      service account can use the instance.
    kmsKey: Input only. The KMS key used to encrypt the disks, only applicable
      if disk_encryption is CMEK. Format: `projects/{project_id}/locations/{lo
      cation}/keyRings/{key_ring_id}/cryptoKeys/{key_id}` Learn more about
      [using your own encryption
      keys](https://cloud.google.com/kms/docs/quickstart).
    labels: Labels to apply to this instance. These can be later modified by
      the setLabels method.
    machineType: Required. The [Compute Engine machine
      type](https://cloud.google.com/compute/docs/machine-resource) of this
      instance.
    metadata: Custom metadata to apply to this instance. For example, to
      specify a Cloud Storage bucket for automatic backup, you can use the
      `gcs-data-bucket` metadata tag. Format: `"--metadata=gcs-data-
      bucket=BUCKET"`.
    name: Output only. The name of this notebook instance. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    network: The name of the VPC that this instance is in. Format:
      `projects/{project_id}/global/networks/{network_id}`
    nicType: Optional. The type of vNIC to be used on this interface. This may
      be gVNIC or VirtioNet.
    noProxyAccess: If true, the notebook instance will not register with the
      proxy.
    noPublicIp: If true, no external IP will be assigned to this instance.
    noRemoveDataDisk: Input only. If true, the data disk will not be auto
      deleted when deleting the instance.
    postStartupScript: Path to a Bash script that automatically runs after a
      notebook instance fully boots up. The path must be a URL or Cloud
      Storage path (`gs://path-to-file/file-name`).
    proxyUri: Output only. The proxy endpoint that is used to access the
      Jupyter notebook.
    reservationAffinity: Optional. The optional reservation affinity. Setting
      this field will apply the specified [Zonal Compute
      Reservation](https://cloud.google.com/compute/docs/instances/reserving-
      zonal-resources) to this notebook instance.
    serviceAccount: The service account on this instance, giving access to
      other Google Cloud services. You can use any service account within the
      same project, but you must have the service account user permission to
      use the instance. If not specified, the [Compute Engine default service
      account](https://cloud.google.com/compute/docs/access/service-
      accounts#default_service_account) is used.
    state: Output only. The state of this instance.
    subnet: The name of the subnet that this instance is in. Format:
      `projects/{project_id}/regions/{region}/subnetworks/{subnetwork_id}`
    updateTime: Output only. Instance update time.
    vmImage: Use a Compute Engine VM image to start the notebook instance.
  """

  class BootDiskTypeValueValuesEnum(_messages.Enum):
    r"""Input only. The type of the boot disk attached to this instance,
    defaults to standard persistent disk (`PD_STANDARD`).

    Values:
      DISK_TYPE_UNSPECIFIED: Disk type not set.
      PD_STANDARD: Standard persistent disk type.
      PD_SSD: SSD persistent disk type.
      PD_BALANCED: Balanced persistent disk type.
    """
    DISK_TYPE_UNSPECIFIED = 0
    PD_STANDARD = 1
    PD_SSD = 2
    PD_BALANCED = 3

  class DataDiskTypeValueValuesEnum(_messages.Enum):
    r"""Input only. The type of the data disk attached to this instance,
    defaults to standard persistent disk (`PD_STANDARD`).

    Values:
      DISK_TYPE_UNSPECIFIED: Disk type not set.
      PD_STANDARD: Standard persistent disk type.
      PD_SSD: SSD persistent disk type.
      PD_BALANCED: Balanced persistent disk type.
    """
    DISK_TYPE_UNSPECIFIED = 0
    PD_STANDARD = 1
    PD_SSD = 2
    PD_BALANCED = 3

  class DiskEncryptionValueValuesEnum(_messages.Enum):
    r"""Input only. Disk encryption method used on the boot and data disks,
    defaults to GMEK.

    Values:
      DISK_ENCRYPTION_UNSPECIFIED: Disk encryption is not specified.
      GMEK: Use Google managed encryption keys to encrypt the boot disk.
      CMEK: Use customer managed encryption keys to encrypt the boot disk.
    """
    DISK_ENCRYPTION_UNSPECIFIED = 0
    GMEK = 1
    CMEK = 2

  class NicTypeValueValuesEnum(_messages.Enum):
    r"""Optional. The type of vNIC to be used on this interface. This may be
    gVNIC or VirtioNet.

    Values:
      UNSPECIFIED_NIC_TYPE: No type specified. Default should be
        UNSPECIFIED_NIC_TYPE.
      VIRTIO_NET: VIRTIO. Default in Notebooks DLVM.
      GVNIC: GVNIC. Alternative to VIRTIO.
        https://github.com/GoogleCloudPlatform/compute-virtual-ethernet-linux
    """
    UNSPECIFIED_NIC_TYPE = 0
    VIRTIO_NET = 1
    GVNIC = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of this instance.

    Values:
      STATE_UNSPECIFIED: State is not specified.
      STARTING: The control logic is starting the instance.
      PROVISIONING: The control logic is installing required frameworks and
        registering the instance with notebook proxy
      ACTIVE: The instance is running.
      STOPPING: The control logic is stopping the instance.
      STOPPED: The instance is stopped.
      DELETED: The instance is deleted.
      UPGRADING: The instance is upgrading.
      INITIALIZING: The instance is being created.
      REGISTERING: The instance is getting registered.
      SUSPENDING: The instance is suspending.
      SUSPENDED: The instance is suspended.
    """
    STATE_UNSPECIFIED = 0
    STARTING = 1
    PROVISIONING = 2
    ACTIVE = 3
    STOPPING = 4
    STOPPED = 5
    DELETED = 6
    UPGRADING = 7
    INITIALIZING = 8
    REGISTERING = 9
    SUSPENDING = 10
    SUSPENDED = 11

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels to apply to this instance. These can be later modified by the
    setLabels method.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Custom metadata to apply to this instance. For example, to specify a
    Cloud Storage bucket for automatic backup, you can use the `gcs-data-
    bucket` metadata tag. Format: `"--metadata=gcs-data-bucket=BUCKET"`.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Additional properties of type MetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  acceleratorConfig = _messages.MessageField('AcceleratorConfig', 1)
  bootDiskSizeGb = _messages.IntegerField(2)
  bootDiskType = _messages.EnumField('BootDiskTypeValueValuesEnum', 3)
  canIpForward = _messages.BooleanField(4)
  containerImage = _messages.MessageField('ContainerImage', 5)
  createTime = _messages.StringField(6)
  customGpuDriverPath = _messages.StringField(7)
  dataDiskSizeGb = _messages.IntegerField(8)
  dataDiskType = _messages.EnumField('DataDiskTypeValueValuesEnum', 9)
  diskEncryption = _messages.EnumField('DiskEncryptionValueValuesEnum', 10)
  installGpuDriver = _messages.BooleanField(11)
  instanceOwners = _messages.StringField(12, repeated=True)
  kmsKey = _messages.StringField(13)
  labels = _messages.MessageField('LabelsValue', 14)
  machineType = _messages.StringField(15)
  metadata = _messages.MessageField('MetadataValue', 16)
  name = _messages.StringField(17)
  network = _messages.StringField(18)
  nicType = _messages.EnumField('NicTypeValueValuesEnum', 19)
  noProxyAccess = _messages.BooleanField(20)
  noPublicIp = _messages.BooleanField(21)
  noRemoveDataDisk = _messages.BooleanField(22)
  postStartupScript = _messages.StringField(23)
  proxyUri = _messages.StringField(24)
  reservationAffinity = _messages.MessageField('ReservationAffinity', 25)
  serviceAccount = _messages.StringField(26)
  state = _messages.EnumField('StateValueValuesEnum', 27)
  subnet = _messages.StringField(28)
  updateTime = _messages.StringField(29)
  vmImage = _messages.MessageField('VmImage', 30)


class IsInstanceUpgradeableResponse(_messages.Message):
  r"""Response for checking if a notebook instance is upgradeable.

  Fields:
    upgradeImage: The new image self link this instance will be upgraded to if
      calling the upgrade endpoint. This field will only be populated if field
      upgradeable is true.
    upgradeInfo: Additional information about upgrade.
    upgradeVersion: The version this instance will be upgraded to if calling
      the upgrade endpoint. This field will only be populated if field
      upgradeable is true.
    upgradeable: If an instance is upgradeable.
  """

  upgradeImage = _messages.StringField(1)
  upgradeInfo = _messages.StringField(2)
  upgradeVersion = _messages.StringField(3)
  upgradeable = _messages.BooleanField(4)


class ListEnvironmentsResponse(_messages.Message):
  r"""Response for listing environments.

  Fields:
    environments: A list of returned environments.
    nextPageToken: A page token that can be used to continue listing from the
      last result in the next list call.
    unreachable: Locations that could not be reached.
  """

  environments = _messages.MessageField('Environment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListInstancesResponse(_messages.Message):
  r"""Response for listing notebook instances.

  Fields:
    instances: A list of returned instances.
    nextPageToken: Page token that can be used to continue listing from the
      last result in the next list call.
    unreachable: Locations that could not be reached. For example, `['us-
      west1-a', 'us-central1-b']`. A ListInstancesResponse will only contain
      either instances or unreachables,
  """

  instances = _messages.MessageField('Instance', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class NotebooksProjectsLocationsEnvironmentsCreateRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsEnvironmentsCreateRequest object.

  Fields:
    environment: A Environment resource to be passed as the request body.
    environmentId: Required. User-defined unique ID of this environment. The
      `environment_id` must be 1 to 63 characters long and contain only
      lowercase letters, numeric characters, and dashes. The first character
      must be a lowercase letter and the last character cannot be a dash.
    parent: Required. Format: `projects/{project_id}/locations/{location}`
  """

  environment = _messages.MessageField('Environment', 1)
  environmentId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NotebooksProjectsLocationsEnvironmentsDeleteRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsEnvironmentsDeleteRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/environments/{environment_id
      }`
  """

  name = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsEnvironmentsGetRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsEnvironmentsGetRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/environments/{environment_id
      }`
  """

  name = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsEnvironmentsListRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsEnvironmentsListRequest object.

  Fields:
    pageSize: Maximum return size of the list call.
    pageToken: A previous returned page token that can be used to continue
      listing from the last result.
    parent: Required. Format: `projects/{project_id}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NotebooksProjectsLocationsGetRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsInstancesCreateRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesCreateRequest object.

  Fields:
    instance: A Instance resource to be passed as the request body.
    instanceId: Required. User-defined unique ID of this instance.
    parent: Required. Format:
      `parent=projects/{project_id}/locations/{location}`
  """

  instance = _messages.MessageField('Instance', 1)
  instanceId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NotebooksProjectsLocationsInstancesDeleteRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesDeleteRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
  """

  name = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsInstancesGetIamPolicyRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NotebooksProjectsLocationsInstancesGetRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesGetRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
  """

  name = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsInstancesIsUpgradeableRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesIsUpgradeableRequest object.

  Fields:
    notebookInstance: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
  """

  notebookInstance = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsInstancesListRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesListRequest object.

  Fields:
    pageSize: Maximum return size of the list call.
    pageToken: A previous returned page token that can be used to continue
      listing from the last result.
    parent: Required. Format:
      `parent=projects/{project_id}/locations/{location}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NotebooksProjectsLocationsInstancesRegisterRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesRegisterRequest object.

  Fields:
    parent: Required. Format:
      `parent=projects/{project_id}/locations/{location}`
    registerInstanceRequest: A RegisterInstanceRequest resource to be passed
      as the request body.
  """

  parent = _messages.StringField(1, required=True)
  registerInstanceRequest = _messages.MessageField('RegisterInstanceRequest', 2)


class NotebooksProjectsLocationsInstancesReportRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesReportRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    reportInstanceInfoRequest: A ReportInstanceInfoRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  reportInstanceInfoRequest = _messages.MessageField('ReportInstanceInfoRequest', 2)


class NotebooksProjectsLocationsInstancesResetRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesResetRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    resetInstanceRequest: A ResetInstanceRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  resetInstanceRequest = _messages.MessageField('ResetInstanceRequest', 2)


class NotebooksProjectsLocationsInstancesSetAcceleratorRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesSetAcceleratorRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    setInstanceAcceleratorRequest: A SetInstanceAcceleratorRequest resource to
      be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  setInstanceAcceleratorRequest = _messages.MessageField('SetInstanceAcceleratorRequest', 2)


class NotebooksProjectsLocationsInstancesSetIamPolicyRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class NotebooksProjectsLocationsInstancesSetLabelsRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesSetLabelsRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    setInstanceLabelsRequest: A SetInstanceLabelsRequest resource to be passed
      as the request body.
  """

  name = _messages.StringField(1, required=True)
  setInstanceLabelsRequest = _messages.MessageField('SetInstanceLabelsRequest', 2)


class NotebooksProjectsLocationsInstancesSetMachineTypeRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesSetMachineTypeRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    setInstanceMachineTypeRequest: A SetInstanceMachineTypeRequest resource to
      be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  setInstanceMachineTypeRequest = _messages.MessageField('SetInstanceMachineTypeRequest', 2)


class NotebooksProjectsLocationsInstancesStartRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesStartRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    startInstanceRequest: A StartInstanceRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  startInstanceRequest = _messages.MessageField('StartInstanceRequest', 2)


class NotebooksProjectsLocationsInstancesStopRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesStopRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    stopInstanceRequest: A StopInstanceRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  stopInstanceRequest = _messages.MessageField('StopInstanceRequest', 2)


class NotebooksProjectsLocationsInstancesTestIamPermissionsRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class NotebooksProjectsLocationsInstancesUpgradeInternalRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesUpgradeInternalRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    upgradeInstanceInternalRequest: A UpgradeInstanceInternalRequest resource
      to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  upgradeInstanceInternalRequest = _messages.MessageField('UpgradeInstanceInternalRequest', 2)


class NotebooksProjectsLocationsInstancesUpgradeRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsInstancesUpgradeRequest object.

  Fields:
    name: Required. Format:
      `projects/{project_id}/locations/{location}/instances/{instance_id}`
    upgradeInstanceRequest: A UpgradeInstanceRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  upgradeInstanceRequest = _messages.MessageField('UpgradeInstanceRequest', 2)


class NotebooksProjectsLocationsListRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class NotebooksProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class NotebooksProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class NotebooksProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A NotebooksProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: API version used to start the operation.
    createTime: The time the operation was created.
    endTime: The time the operation finished running.
    endpoint: API endpoint name of this operation.
    requestedCancellation: Identifies whether the user has requested
      cancellation of the operation. Operations that have successfully been
      cancelled have Operation.error value with a google.rpc.Status.code of 1,
      corresponding to `Code.CANCELLED`.
    statusMessage: Human-readable status of the operation, if any.
    target: Server-defined resource path for the target of the operation.
    verb: Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  endpoint = _messages.StringField(4)
  requestedCancellation = _messages.BooleanField(5)
  statusMessage = _messages.StringField(6)
  target = _messages.StringField(7)
  verb = _messages.StringField(8)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  bindings = _messages.MessageField('Binding', 1, repeated=True)
  etag = _messages.BytesField(2)
  version = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class RegisterInstanceRequest(_messages.Message):
  r"""Request for registering a notebook instance.

  Fields:
    instanceId: Required. User defined unique ID of this instance. The
      `instance_id` must be 1 to 63 characters long and contain only lowercase
      letters, numeric characters, and dashes. The first character must be a
      lowercase letter and the last character cannot be a dash.
  """

  instanceId = _messages.StringField(1)


class ReportInstanceInfoRequest(_messages.Message):
  r"""Request for notebook instances to report information to Notebooks API.

  Messages:
    MetadataValue: The metadata reported to Notebooks API. This will be merged
      to the instance metadata store

  Fields:
    metadata: The metadata reported to Notebooks API. This will be merged to
      the instance metadata store
    vmId: Required. The VM hardware token for authenticating the VM.
      https://cloud.google.com/compute/docs/instances/verifying-instance-
      identity
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""The metadata reported to Notebooks API. This will be merged to the
    instance metadata store

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Additional properties of type MetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  metadata = _messages.MessageField('MetadataValue', 1)
  vmId = _messages.StringField(2)


class ReservationAffinity(_messages.Message):
  r"""Reservation Affinity for consuming Zonal reservation.

  Enums:
    ConsumeReservationTypeValueValuesEnum: Optional. Type of reservation to
      consume

  Fields:
    consumeReservationType: Optional. Type of reservation to consume
    key: Optional. Corresponds to the label key of reservation resource.
    values: Optional. Corresponds to the label values of reservation resource.
  """

  class ConsumeReservationTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Type of reservation to consume

    Values:
      TYPE_UNSPECIFIED: Default type.
      NO_RESERVATION: Do not consume from any allocated capacity.
      ANY_RESERVATION: Consume any reservation available.
      SPECIFIC_RESERVATION: Must consume from a specific reservation. Must
        specify key value fields for specifying the reservations.
    """
    TYPE_UNSPECIFIED = 0
    NO_RESERVATION = 1
    ANY_RESERVATION = 2
    SPECIFIC_RESERVATION = 3

  consumeReservationType = _messages.EnumField('ConsumeReservationTypeValueValuesEnum', 1)
  key = _messages.StringField(2)
  values = _messages.StringField(3, repeated=True)


class ResetInstanceRequest(_messages.Message):
  r"""Request for resetting a notebook instance"""


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
  """

  policy = _messages.MessageField('Policy', 1)


class SetInstanceAcceleratorRequest(_messages.Message):
  r"""Request for setting instance accelerator.

  Enums:
    TypeValueValuesEnum: Required. Type of this accelerator.

  Fields:
    coreCount: Required. Count of cores of this accelerator. Note that not all
      combinations of `type` and `core_count` are valid. See [GPUs on Compute
      Engine](https://cloud.google.com/compute/docs/gpus/#gpus-list) to find a
      valid combination. TPUs are not supported.
    type: Required. Type of this accelerator.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. Type of this accelerator.

    Values:
      ACCELERATOR_TYPE_UNSPECIFIED: Accelerator type is not specified.
      NVIDIA_TESLA_K80: Accelerator type is Nvidia Tesla K80.
      NVIDIA_TESLA_P100: Accelerator type is Nvidia Tesla P100.
      NVIDIA_TESLA_V100: Accelerator type is Nvidia Tesla V100.
      NVIDIA_TESLA_P4: Accelerator type is Nvidia Tesla P4.
      NVIDIA_TESLA_T4: Accelerator type is Nvidia Tesla T4.
      NVIDIA_TESLA_T4_VWS: Accelerator type is NVIDIA Tesla T4 Virtual
        Workstations.
      NVIDIA_TESLA_P100_VWS: Accelerator type is NVIDIA Tesla P100 Virtual
        Workstations.
      NVIDIA_TESLA_P4_VWS: Accelerator type is NVIDIA Tesla P4 Virtual
        Workstations.
      TPU_V2: (Coming soon) Accelerator type is TPU V2.
      TPU_V3: (Coming soon) Accelerator type is TPU V3.
    """
    ACCELERATOR_TYPE_UNSPECIFIED = 0
    NVIDIA_TESLA_K80 = 1
    NVIDIA_TESLA_P100 = 2
    NVIDIA_TESLA_V100 = 3
    NVIDIA_TESLA_P4 = 4
    NVIDIA_TESLA_T4 = 5
    NVIDIA_TESLA_T4_VWS = 6
    NVIDIA_TESLA_P100_VWS = 7
    NVIDIA_TESLA_P4_VWS = 8
    TPU_V2 = 9
    TPU_V3 = 10

  coreCount = _messages.IntegerField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class SetInstanceLabelsRequest(_messages.Message):
  r"""Request for setting instance labels.

  Messages:
    LabelsValue: Labels to apply to this instance. These can be later modified
      by the setLabels method

  Fields:
    labels: Labels to apply to this instance. These can be later modified by
      the setLabels method
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels to apply to this instance. These can be later modified by the
    setLabels method

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)


class SetInstanceMachineTypeRequest(_messages.Message):
  r"""Request for setting instance machine type.

  Fields:
    machineType: Required. The [Compute Engine machine
      type](https://cloud.google.com/compute/docs/machine-resource).
  """

  machineType = _messages.StringField(1)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StartInstanceRequest(_messages.Message):
  r"""Request for starting a notebook instance"""


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StopInstanceRequest(_messages.Message):
  r"""Request for stopping a notebook instance"""


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class UpgradeInstanceInternalRequest(_messages.Message):
  r"""Request for upgrading a notebook instance from within the VM

  Fields:
    vmId: Required. The VM hardware token for authenticating the VM.
      https://cloud.google.com/compute/docs/instances/verifying-instance-
      identity
  """

  vmId = _messages.StringField(1)


class UpgradeInstanceRequest(_messages.Message):
  r"""Request for upgrading a notebook instance"""


class VmImage(_messages.Message):
  r"""Definition of a custom Compute Engine virtual machine image for starting
  a notebook instance with the environment installed directly on the VM.

  Fields:
    imageFamily: Use this VM image family to find the image; the newest image
      in this family will be used.
    imageName: Use VM image name to find the image.
    project: Required. The name of the Google Cloud project that this VM image
      belongs to. Format: `projects/{project_id}`
  """

  imageFamily = _messages.StringField(1)
  imageName = _messages.StringField(2)
  project = _messages.StringField(3)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    NotebooksProjectsLocationsInstancesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
