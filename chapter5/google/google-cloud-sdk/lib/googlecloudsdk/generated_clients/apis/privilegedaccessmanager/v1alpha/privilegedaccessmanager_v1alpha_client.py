"""Generated client library for privilegedaccessmanager version v1alpha."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.privilegedaccessmanager.v1alpha import privilegedaccessmanager_v1alpha_messages as messages


class PrivilegedaccessmanagerV1alpha(base_api.BaseApiClient):
  """Generated client library for service privilegedaccessmanager version v1alpha."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://privilegedaccessmanager.googleapis.com/'
  MTLS_BASE_URL = 'https://privilegedaccessmanager.mtls.googleapis.com/'

  _PACKAGE = 'privilegedaccessmanager'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1alpha'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'PrivilegedaccessmanagerV1alpha'
  _URL_VERSION = 'v1alpha'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new privilegedaccessmanager handle."""
    url = url or self.BASE_URL
    super(PrivilegedaccessmanagerV1alpha, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.folders_locations_entitlements_grants = self.FoldersLocationsEntitlementsGrantsService(self)
    self.folders_locations_entitlements = self.FoldersLocationsEntitlementsService(self)
    self.folders_locations_operations = self.FoldersLocationsOperationsService(self)
    self.folders_locations = self.FoldersLocationsService(self)
    self.folders = self.FoldersService(self)
    self.organizations_locations_entitlements_grants = self.OrganizationsLocationsEntitlementsGrantsService(self)
    self.organizations_locations_entitlements = self.OrganizationsLocationsEntitlementsService(self)
    self.organizations_locations_operations = self.OrganizationsLocationsOperationsService(self)
    self.organizations_locations = self.OrganizationsLocationsService(self)
    self.organizations = self.OrganizationsService(self)
    self.projects_locations_entitlements_grants = self.ProjectsLocationsEntitlementsGrantsService(self)
    self.projects_locations_entitlements = self.ProjectsLocationsEntitlementsService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class FoldersLocationsEntitlementsGrantsService(base_api.BaseApiService):
    """Service class for the folders_locations_entitlements_grants resource."""

    _NAME = 'folders_locations_entitlements_grants'

    def __init__(self, client):
      super(PrivilegedaccessmanagerV1alpha.FoldersLocationsEntitlementsGrantsService, self).__init__(client)
      self._upload_configs = {
          }

    def Approve(self, request, global_params=None):
      r"""`ApproveGrant` is used to approve a grant. This method can only be called on a grant when it's in the `APPROVAL_AWAITED` state. This operation can't be undone.

      Args:
        request: (PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsApproveRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Grant) The response message.
      """
      config = self.GetMethodConfig('Approve')
      return self._RunMethod(
          config, request, global_params=global_params)

    Approve.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/entitlements/{entitlementsId}/grants/{grantsId}:approve',
        http_method='POST',
        method_id='privilegedaccessmanager.folders.locations.entitlements.grants.approve',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:approve',
        request_field='approveGrantRequest',
        request_type_name='PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsApproveRequest',
        response_type_name='Grant',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new grant in a given project/folder/organization and location.

      Args:
        request: (PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Grant) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/entitlements/{entitlementsId}/grants',
        http_method='POST',
        method_id='privilegedaccessmanager.folders.locations.entitlements.grants.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId'],
        relative_path='v1alpha/{+parent}/grants',
        request_field='grant',
        request_type_name='PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsCreateRequest',
        response_type_name='Grant',
        supports_download=False,
    )

    def Deny(self, request, global_params=None):
      r"""`DenyGrant` is used to deny a grant. This method can only be called on a grant when it's in the `APPROVAL_AWAITED` state. This operation can't be undone.

      Args:
        request: (PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsDenyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Grant) The response message.
      """
      config = self.GetMethodConfig('Deny')
      return self._RunMethod(
          config, request, global_params=global_params)

    Deny.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/entitlements/{entitlementsId}/grants/{grantsId}:deny',
        http_method='POST',
        method_id='privilegedaccessmanager.folders.locations.entitlements.grants.deny',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:deny',
        request_field='denyGrantRequest',
        request_type_name='PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsDenyRequest',
        response_type_name='Grant',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get details of a single grant.

      Args:
        request: (PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Grant) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/entitlements/{entitlementsId}/grants/{grantsId}',
        http_method='GET',
        method_id='privilegedaccessmanager.folders.locations.entitlements.grants.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsGetRequest',
        response_type_name='Grant',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists grants for a given entitlement.

      Args:
        request: (PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGrantsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/entitlements/{entitlementsId}/grants',
        http_method='GET',
        method_id='privilegedaccessmanager.folders.locations.entitlements.grants.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/grants',
        request_field='',
        request_type_name='PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsListRequest',
        response_type_name='ListGrantsResponse',
        supports_download=False,
    )

    def Revoke(self, request, global_params=None):
      r"""`RevokeGrant` is used to immediately revoke access for a grant. This method can be called when the grant is in a non-terminal state.

      Args:
        request: (PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsRevokeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Revoke')
      return self._RunMethod(
          config, request, global_params=global_params)

    Revoke.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/entitlements/{entitlementsId}/grants/{grantsId}:revoke',
        http_method='POST',
        method_id='privilegedaccessmanager.folders.locations.entitlements.grants.revoke',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:revoke',
        request_field='revokeGrantRequest',
        request_type_name='PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsRevokeRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Search(self, request, global_params=None):
      r"""`SearchGrants` returns grants that are related to the calling user in the specified way.

      Args:
        request: (PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsSearchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SearchGrantsResponse) The response message.
      """
      config = self.GetMethodConfig('Search')
      return self._RunMethod(
          config, request, global_params=global_params)

    Search.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/entitlements/{entitlementsId}/grants:search',
        http_method='GET',
        method_id='privilegedaccessmanager.folders.locations.entitlements.grants.search',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['callerRelationship', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/grants:search',
        request_field='',
        request_type_name='PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsSearchRequest',
        response_type_name='SearchGrantsResponse',
        supports_download=False,
    )

    def Withdraw(self, request, global_params=None):
      r"""`WithdrawGrant` is used to immediately withdraw the grant. This method can be called when the grant is in a non-terminal state.

      Args:
        request: (PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsWithdrawRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Withdraw')
      return self._RunMethod(
          config, request, global_params=global_params)

    Withdraw.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/entitlements/{entitlementsId}/grants/{grantsId}:withdraw',
        http_method='POST',
        method_id='privilegedaccessmanager.folders.locations.entitlements.grants.withdraw',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:withdraw',
        request_field='withdrawGrantRequest',
        request_type_name='PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsWithdrawRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class FoldersLocationsEntitlementsService(base_api.BaseApiService):
    """Service class for the folders_locations_entitlements resource."""

    _NAME = 'folders_locations_entitlements'

    def __init__(self, client):
      super(PrivilegedaccessmanagerV1alpha.FoldersLocationsEntitlementsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new entitlement in a given project/folder/organization and location.

      Args:
        request: (PrivilegedaccessmanagerFoldersLocationsEntitlementsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/entitlements',
        http_method='POST',
        method_id='privilegedaccessmanager.folders.locations.entitlements.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['entitlementId', 'requestId'],
        relative_path='v1alpha/{+parent}/entitlements',
        request_field='entitlement',
        request_type_name='PrivilegedaccessmanagerFoldersLocationsEntitlementsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single entitlement. This method can only be called when there are no in-progress (`ACTIVE`/`ACTIVATING`/`REVOKING`) grants under the entitlement.

      Args:
        request: (PrivilegedaccessmanagerFoldersLocationsEntitlementsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/entitlements/{entitlementsId}',
        http_method='DELETE',
        method_id='privilegedaccessmanager.folders.locations.entitlements.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force', 'requestId'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PrivilegedaccessmanagerFoldersLocationsEntitlementsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single entitlement.

      Args:
        request: (PrivilegedaccessmanagerFoldersLocationsEntitlementsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Entitlement) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/entitlements/{entitlementsId}',
        http_method='GET',
        method_id='privilegedaccessmanager.folders.locations.entitlements.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PrivilegedaccessmanagerFoldersLocationsEntitlementsGetRequest',
        response_type_name='Entitlement',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists entitlements in a given project/folder/organization and location.

      Args:
        request: (PrivilegedaccessmanagerFoldersLocationsEntitlementsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEntitlementsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/entitlements',
        http_method='GET',
        method_id='privilegedaccessmanager.folders.locations.entitlements.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/entitlements',
        request_field='',
        request_type_name='PrivilegedaccessmanagerFoldersLocationsEntitlementsListRequest',
        response_type_name='ListEntitlementsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the entitlement specified in the request. Updated fields in the entitlement need to be specified in an update mask. The changes made to an entitlement are applicable only on future grants of the entitlement. However, if new approvers are added or existing approvers are removed from the approval workflow, the changes are effective on existing grants. The following fields are not supported for updates: * All immutable fields * Entitlement name * Resource name * Resource type * Adding an approval workflow in an entitlement which previously had no approval workflow. * Deleting the approval workflow from an entitlement. * Adding or deleting a step in the approval workflow (only one step is supported) Note that updates are allowed on the list of approvers in an approval workflow step.

      Args:
        request: (PrivilegedaccessmanagerFoldersLocationsEntitlementsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/entitlements/{entitlementsId}',
        http_method='PATCH',
        method_id='privilegedaccessmanager.folders.locations.entitlements.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='entitlement',
        request_type_name='PrivilegedaccessmanagerFoldersLocationsEntitlementsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Search(self, request, global_params=None):
      r"""`SearchEntitlements` returns entitlements on which the caller has the specified access.

      Args:
        request: (PrivilegedaccessmanagerFoldersLocationsEntitlementsSearchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SearchEntitlementsResponse) The response message.
      """
      config = self.GetMethodConfig('Search')
      return self._RunMethod(
          config, request, global_params=global_params)

    Search.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/entitlements:search',
        http_method='GET',
        method_id='privilegedaccessmanager.folders.locations.entitlements.search',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['callerAccessType', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/entitlements:search',
        request_field='',
        request_type_name='PrivilegedaccessmanagerFoldersLocationsEntitlementsSearchRequest',
        response_type_name='SearchEntitlementsResponse',
        supports_download=False,
    )

  class FoldersLocationsOperationsService(base_api.BaseApiService):
    """Service class for the folders_locations_operations resource."""

    _NAME = 'folders_locations_operations'

    def __init__(self, client):
      super(PrivilegedaccessmanagerV1alpha.FoldersLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (PrivilegedaccessmanagerFoldersLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='privilegedaccessmanager.folders.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PrivilegedaccessmanagerFoldersLocationsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (PrivilegedaccessmanagerFoldersLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='privilegedaccessmanager.folders.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PrivilegedaccessmanagerFoldersLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (PrivilegedaccessmanagerFoldersLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='privilegedaccessmanager.folders.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+name}/operations',
        request_field='',
        request_type_name='PrivilegedaccessmanagerFoldersLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class FoldersLocationsService(base_api.BaseApiService):
    """Service class for the folders_locations resource."""

    _NAME = 'folders_locations'

    def __init__(self, client):
      super(PrivilegedaccessmanagerV1alpha.FoldersLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def CheckOnboardingStatus(self, request, global_params=None):
      r"""`CheckOnboardingStatus` reports the onboarding status for a project/folder/organization. Any findings reported by this API need to be fixed before PAM can be used on the resource.

      Args:
        request: (PrivilegedaccessmanagerFoldersLocationsCheckOnboardingStatusRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CheckOnboardingStatusResponse) The response message.
      """
      config = self.GetMethodConfig('CheckOnboardingStatus')
      return self._RunMethod(
          config, request, global_params=global_params)

    CheckOnboardingStatus.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}:checkOnboardingStatus',
        http_method='GET',
        method_id='privilegedaccessmanager.folders.locations.checkOnboardingStatus',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha/{+parent}:checkOnboardingStatus',
        request_field='',
        request_type_name='PrivilegedaccessmanagerFoldersLocationsCheckOnboardingStatusRequest',
        response_type_name='CheckOnboardingStatusResponse',
        supports_download=False,
    )

    def EffectiveSettings(self, request, global_params=None):
      r"""`FetchEffectiveSettings` returns the effective PAM Settings for the given project/folder/organization.

      Args:
        request: (PrivilegedaccessmanagerFoldersLocationsEffectiveSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FetchEffectiveSettingsResponse) The response message.
      """
      config = self.GetMethodConfig('EffectiveSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    EffectiveSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}:effectiveSettings',
        http_method='GET',
        method_id='privilegedaccessmanager.folders.locations.effectiveSettings',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha/{+parent}:effectiveSettings',
        request_field='',
        request_type_name='PrivilegedaccessmanagerFoldersLocationsEffectiveSettingsRequest',
        response_type_name='FetchEffectiveSettingsResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (PrivilegedaccessmanagerFoldersLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}',
        http_method='GET',
        method_id='privilegedaccessmanager.folders.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PrivilegedaccessmanagerFoldersLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def GetSettings(self, request, global_params=None):
      r"""`GetSettings` returns the PAM Settings for the given project/folder/ organization.

      Args:
        request: (PrivilegedaccessmanagerFoldersLocationsGetSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Settings) The response message.
      """
      config = self.GetMethodConfig('GetSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/settings',
        http_method='GET',
        method_id='privilegedaccessmanager.folders.locations.getSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PrivilegedaccessmanagerFoldersLocationsGetSettingsRequest',
        response_type_name='Settings',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (PrivilegedaccessmanagerFoldersLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations',
        http_method='GET',
        method_id='privilegedaccessmanager.folders.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+name}/locations',
        request_field='',
        request_type_name='PrivilegedaccessmanagerFoldersLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

    def UpdateSettings(self, request, global_params=None):
      r"""`UpdateSettings` updates the PAM Settings resource specified in the request. Updated fields in the settings need to be specified in an update mask. The following fields are not supported for updates: * Settings name * Create time * Update time * Etag.

      Args:
        request: (PrivilegedaccessmanagerFoldersLocationsUpdateSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('UpdateSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/settings',
        http_method='PATCH',
        method_id='privilegedaccessmanager.folders.locations.updateSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='settings',
        request_type_name='PrivilegedaccessmanagerFoldersLocationsUpdateSettingsRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class FoldersService(base_api.BaseApiService):
    """Service class for the folders resource."""

    _NAME = 'folders'

    def __init__(self, client):
      super(PrivilegedaccessmanagerV1alpha.FoldersService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsLocationsEntitlementsGrantsService(base_api.BaseApiService):
    """Service class for the organizations_locations_entitlements_grants resource."""

    _NAME = 'organizations_locations_entitlements_grants'

    def __init__(self, client):
      super(PrivilegedaccessmanagerV1alpha.OrganizationsLocationsEntitlementsGrantsService, self).__init__(client)
      self._upload_configs = {
          }

    def Approve(self, request, global_params=None):
      r"""`ApproveGrant` is used to approve a grant. This method can only be called on a grant when it's in the `APPROVAL_AWAITED` state. This operation can't be undone.

      Args:
        request: (PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsApproveRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Grant) The response message.
      """
      config = self.GetMethodConfig('Approve')
      return self._RunMethod(
          config, request, global_params=global_params)

    Approve.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/entitlements/{entitlementsId}/grants/{grantsId}:approve',
        http_method='POST',
        method_id='privilegedaccessmanager.organizations.locations.entitlements.grants.approve',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:approve',
        request_field='approveGrantRequest',
        request_type_name='PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsApproveRequest',
        response_type_name='Grant',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new grant in a given project/folder/organization and location.

      Args:
        request: (PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Grant) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/entitlements/{entitlementsId}/grants',
        http_method='POST',
        method_id='privilegedaccessmanager.organizations.locations.entitlements.grants.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId'],
        relative_path='v1alpha/{+parent}/grants',
        request_field='grant',
        request_type_name='PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsCreateRequest',
        response_type_name='Grant',
        supports_download=False,
    )

    def Deny(self, request, global_params=None):
      r"""`DenyGrant` is used to deny a grant. This method can only be called on a grant when it's in the `APPROVAL_AWAITED` state. This operation can't be undone.

      Args:
        request: (PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsDenyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Grant) The response message.
      """
      config = self.GetMethodConfig('Deny')
      return self._RunMethod(
          config, request, global_params=global_params)

    Deny.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/entitlements/{entitlementsId}/grants/{grantsId}:deny',
        http_method='POST',
        method_id='privilegedaccessmanager.organizations.locations.entitlements.grants.deny',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:deny',
        request_field='denyGrantRequest',
        request_type_name='PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsDenyRequest',
        response_type_name='Grant',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get details of a single grant.

      Args:
        request: (PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Grant) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/entitlements/{entitlementsId}/grants/{grantsId}',
        http_method='GET',
        method_id='privilegedaccessmanager.organizations.locations.entitlements.grants.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsGetRequest',
        response_type_name='Grant',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists grants for a given entitlement.

      Args:
        request: (PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGrantsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/entitlements/{entitlementsId}/grants',
        http_method='GET',
        method_id='privilegedaccessmanager.organizations.locations.entitlements.grants.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/grants',
        request_field='',
        request_type_name='PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsListRequest',
        response_type_name='ListGrantsResponse',
        supports_download=False,
    )

    def Revoke(self, request, global_params=None):
      r"""`RevokeGrant` is used to immediately revoke access for a grant. This method can be called when the grant is in a non-terminal state.

      Args:
        request: (PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsRevokeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Revoke')
      return self._RunMethod(
          config, request, global_params=global_params)

    Revoke.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/entitlements/{entitlementsId}/grants/{grantsId}:revoke',
        http_method='POST',
        method_id='privilegedaccessmanager.organizations.locations.entitlements.grants.revoke',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:revoke',
        request_field='revokeGrantRequest',
        request_type_name='PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsRevokeRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Search(self, request, global_params=None):
      r"""`SearchGrants` returns grants that are related to the calling user in the specified way.

      Args:
        request: (PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsSearchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SearchGrantsResponse) The response message.
      """
      config = self.GetMethodConfig('Search')
      return self._RunMethod(
          config, request, global_params=global_params)

    Search.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/entitlements/{entitlementsId}/grants:search',
        http_method='GET',
        method_id='privilegedaccessmanager.organizations.locations.entitlements.grants.search',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['callerRelationship', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/grants:search',
        request_field='',
        request_type_name='PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsSearchRequest',
        response_type_name='SearchGrantsResponse',
        supports_download=False,
    )

    def Withdraw(self, request, global_params=None):
      r"""`WithdrawGrant` is used to immediately withdraw the grant. This method can be called when the grant is in a non-terminal state.

      Args:
        request: (PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsWithdrawRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Withdraw')
      return self._RunMethod(
          config, request, global_params=global_params)

    Withdraw.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/entitlements/{entitlementsId}/grants/{grantsId}:withdraw',
        http_method='POST',
        method_id='privilegedaccessmanager.organizations.locations.entitlements.grants.withdraw',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:withdraw',
        request_field='withdrawGrantRequest',
        request_type_name='PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsWithdrawRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class OrganizationsLocationsEntitlementsService(base_api.BaseApiService):
    """Service class for the organizations_locations_entitlements resource."""

    _NAME = 'organizations_locations_entitlements'

    def __init__(self, client):
      super(PrivilegedaccessmanagerV1alpha.OrganizationsLocationsEntitlementsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new entitlement in a given project/folder/organization and location.

      Args:
        request: (PrivilegedaccessmanagerOrganizationsLocationsEntitlementsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/entitlements',
        http_method='POST',
        method_id='privilegedaccessmanager.organizations.locations.entitlements.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['entitlementId', 'requestId'],
        relative_path='v1alpha/{+parent}/entitlements',
        request_field='entitlement',
        request_type_name='PrivilegedaccessmanagerOrganizationsLocationsEntitlementsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single entitlement. This method can only be called when there are no in-progress (`ACTIVE`/`ACTIVATING`/`REVOKING`) grants under the entitlement.

      Args:
        request: (PrivilegedaccessmanagerOrganizationsLocationsEntitlementsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/entitlements/{entitlementsId}',
        http_method='DELETE',
        method_id='privilegedaccessmanager.organizations.locations.entitlements.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force', 'requestId'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PrivilegedaccessmanagerOrganizationsLocationsEntitlementsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single entitlement.

      Args:
        request: (PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Entitlement) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/entitlements/{entitlementsId}',
        http_method='GET',
        method_id='privilegedaccessmanager.organizations.locations.entitlements.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGetRequest',
        response_type_name='Entitlement',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists entitlements in a given project/folder/organization and location.

      Args:
        request: (PrivilegedaccessmanagerOrganizationsLocationsEntitlementsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEntitlementsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/entitlements',
        http_method='GET',
        method_id='privilegedaccessmanager.organizations.locations.entitlements.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/entitlements',
        request_field='',
        request_type_name='PrivilegedaccessmanagerOrganizationsLocationsEntitlementsListRequest',
        response_type_name='ListEntitlementsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the entitlement specified in the request. Updated fields in the entitlement need to be specified in an update mask. The changes made to an entitlement are applicable only on future grants of the entitlement. However, if new approvers are added or existing approvers are removed from the approval workflow, the changes are effective on existing grants. The following fields are not supported for updates: * All immutable fields * Entitlement name * Resource name * Resource type * Adding an approval workflow in an entitlement which previously had no approval workflow. * Deleting the approval workflow from an entitlement. * Adding or deleting a step in the approval workflow (only one step is supported) Note that updates are allowed on the list of approvers in an approval workflow step.

      Args:
        request: (PrivilegedaccessmanagerOrganizationsLocationsEntitlementsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/entitlements/{entitlementsId}',
        http_method='PATCH',
        method_id='privilegedaccessmanager.organizations.locations.entitlements.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='entitlement',
        request_type_name='PrivilegedaccessmanagerOrganizationsLocationsEntitlementsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Search(self, request, global_params=None):
      r"""`SearchEntitlements` returns entitlements on which the caller has the specified access.

      Args:
        request: (PrivilegedaccessmanagerOrganizationsLocationsEntitlementsSearchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SearchEntitlementsResponse) The response message.
      """
      config = self.GetMethodConfig('Search')
      return self._RunMethod(
          config, request, global_params=global_params)

    Search.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/entitlements:search',
        http_method='GET',
        method_id='privilegedaccessmanager.organizations.locations.entitlements.search',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['callerAccessType', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/entitlements:search',
        request_field='',
        request_type_name='PrivilegedaccessmanagerOrganizationsLocationsEntitlementsSearchRequest',
        response_type_name='SearchEntitlementsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the organizations_locations_operations resource."""

    _NAME = 'organizations_locations_operations'

    def __init__(self, client):
      super(PrivilegedaccessmanagerV1alpha.OrganizationsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (PrivilegedaccessmanagerOrganizationsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='privilegedaccessmanager.organizations.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PrivilegedaccessmanagerOrganizationsLocationsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (PrivilegedaccessmanagerOrganizationsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='privilegedaccessmanager.organizations.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PrivilegedaccessmanagerOrganizationsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (PrivilegedaccessmanagerOrganizationsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='privilegedaccessmanager.organizations.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+name}/operations',
        request_field='',
        request_type_name='PrivilegedaccessmanagerOrganizationsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsService(base_api.BaseApiService):
    """Service class for the organizations_locations resource."""

    _NAME = 'organizations_locations'

    def __init__(self, client):
      super(PrivilegedaccessmanagerV1alpha.OrganizationsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def CheckOnboardingStatus(self, request, global_params=None):
      r"""`CheckOnboardingStatus` reports the onboarding status for a project/folder/organization. Any findings reported by this API need to be fixed before PAM can be used on the resource.

      Args:
        request: (PrivilegedaccessmanagerOrganizationsLocationsCheckOnboardingStatusRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CheckOnboardingStatusResponse) The response message.
      """
      config = self.GetMethodConfig('CheckOnboardingStatus')
      return self._RunMethod(
          config, request, global_params=global_params)

    CheckOnboardingStatus.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}:checkOnboardingStatus',
        http_method='GET',
        method_id='privilegedaccessmanager.organizations.locations.checkOnboardingStatus',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha/{+parent}:checkOnboardingStatus',
        request_field='',
        request_type_name='PrivilegedaccessmanagerOrganizationsLocationsCheckOnboardingStatusRequest',
        response_type_name='CheckOnboardingStatusResponse',
        supports_download=False,
    )

    def EffectiveSettings(self, request, global_params=None):
      r"""`FetchEffectiveSettings` returns the effective PAM Settings for the given project/folder/organization.

      Args:
        request: (PrivilegedaccessmanagerOrganizationsLocationsEffectiveSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FetchEffectiveSettingsResponse) The response message.
      """
      config = self.GetMethodConfig('EffectiveSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    EffectiveSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}:effectiveSettings',
        http_method='GET',
        method_id='privilegedaccessmanager.organizations.locations.effectiveSettings',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha/{+parent}:effectiveSettings',
        request_field='',
        request_type_name='PrivilegedaccessmanagerOrganizationsLocationsEffectiveSettingsRequest',
        response_type_name='FetchEffectiveSettingsResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (PrivilegedaccessmanagerOrganizationsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}',
        http_method='GET',
        method_id='privilegedaccessmanager.organizations.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PrivilegedaccessmanagerOrganizationsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def GetSettings(self, request, global_params=None):
      r"""`GetSettings` returns the PAM Settings for the given project/folder/ organization.

      Args:
        request: (PrivilegedaccessmanagerOrganizationsLocationsGetSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Settings) The response message.
      """
      config = self.GetMethodConfig('GetSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/settings',
        http_method='GET',
        method_id='privilegedaccessmanager.organizations.locations.getSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PrivilegedaccessmanagerOrganizationsLocationsGetSettingsRequest',
        response_type_name='Settings',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (PrivilegedaccessmanagerOrganizationsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations',
        http_method='GET',
        method_id='privilegedaccessmanager.organizations.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+name}/locations',
        request_field='',
        request_type_name='PrivilegedaccessmanagerOrganizationsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

    def UpdateSettings(self, request, global_params=None):
      r"""`UpdateSettings` updates the PAM Settings resource specified in the request. Updated fields in the settings need to be specified in an update mask. The following fields are not supported for updates: * Settings name * Create time * Update time * Etag.

      Args:
        request: (PrivilegedaccessmanagerOrganizationsLocationsUpdateSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('UpdateSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/settings',
        http_method='PATCH',
        method_id='privilegedaccessmanager.organizations.locations.updateSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='settings',
        request_type_name='PrivilegedaccessmanagerOrganizationsLocationsUpdateSettingsRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class OrganizationsService(base_api.BaseApiService):
    """Service class for the organizations resource."""

    _NAME = 'organizations'

    def __init__(self, client):
      super(PrivilegedaccessmanagerV1alpha.OrganizationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsEntitlementsGrantsService(base_api.BaseApiService):
    """Service class for the projects_locations_entitlements_grants resource."""

    _NAME = 'projects_locations_entitlements_grants'

    def __init__(self, client):
      super(PrivilegedaccessmanagerV1alpha.ProjectsLocationsEntitlementsGrantsService, self).__init__(client)
      self._upload_configs = {
          }

    def Approve(self, request, global_params=None):
      r"""`ApproveGrant` is used to approve a grant. This method can only be called on a grant when it's in the `APPROVAL_AWAITED` state. This operation can't be undone.

      Args:
        request: (PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsApproveRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Grant) The response message.
      """
      config = self.GetMethodConfig('Approve')
      return self._RunMethod(
          config, request, global_params=global_params)

    Approve.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/entitlements/{entitlementsId}/grants/{grantsId}:approve',
        http_method='POST',
        method_id='privilegedaccessmanager.projects.locations.entitlements.grants.approve',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:approve',
        request_field='approveGrantRequest',
        request_type_name='PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsApproveRequest',
        response_type_name='Grant',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new grant in a given project/folder/organization and location.

      Args:
        request: (PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Grant) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/entitlements/{entitlementsId}/grants',
        http_method='POST',
        method_id='privilegedaccessmanager.projects.locations.entitlements.grants.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId'],
        relative_path='v1alpha/{+parent}/grants',
        request_field='grant',
        request_type_name='PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsCreateRequest',
        response_type_name='Grant',
        supports_download=False,
    )

    def Deny(self, request, global_params=None):
      r"""`DenyGrant` is used to deny a grant. This method can only be called on a grant when it's in the `APPROVAL_AWAITED` state. This operation can't be undone.

      Args:
        request: (PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsDenyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Grant) The response message.
      """
      config = self.GetMethodConfig('Deny')
      return self._RunMethod(
          config, request, global_params=global_params)

    Deny.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/entitlements/{entitlementsId}/grants/{grantsId}:deny',
        http_method='POST',
        method_id='privilegedaccessmanager.projects.locations.entitlements.grants.deny',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:deny',
        request_field='denyGrantRequest',
        request_type_name='PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsDenyRequest',
        response_type_name='Grant',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get details of a single grant.

      Args:
        request: (PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Grant) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/entitlements/{entitlementsId}/grants/{grantsId}',
        http_method='GET',
        method_id='privilegedaccessmanager.projects.locations.entitlements.grants.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsGetRequest',
        response_type_name='Grant',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists grants for a given entitlement.

      Args:
        request: (PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGrantsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/entitlements/{entitlementsId}/grants',
        http_method='GET',
        method_id='privilegedaccessmanager.projects.locations.entitlements.grants.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/grants',
        request_field='',
        request_type_name='PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsListRequest',
        response_type_name='ListGrantsResponse',
        supports_download=False,
    )

    def Revoke(self, request, global_params=None):
      r"""`RevokeGrant` is used to immediately revoke access for a grant. This method can be called when the grant is in a non-terminal state.

      Args:
        request: (PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsRevokeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Revoke')
      return self._RunMethod(
          config, request, global_params=global_params)

    Revoke.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/entitlements/{entitlementsId}/grants/{grantsId}:revoke',
        http_method='POST',
        method_id='privilegedaccessmanager.projects.locations.entitlements.grants.revoke',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:revoke',
        request_field='revokeGrantRequest',
        request_type_name='PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsRevokeRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Search(self, request, global_params=None):
      r"""`SearchGrants` returns grants that are related to the calling user in the specified way.

      Args:
        request: (PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsSearchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SearchGrantsResponse) The response message.
      """
      config = self.GetMethodConfig('Search')
      return self._RunMethod(
          config, request, global_params=global_params)

    Search.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/entitlements/{entitlementsId}/grants:search',
        http_method='GET',
        method_id='privilegedaccessmanager.projects.locations.entitlements.grants.search',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['callerRelationship', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/grants:search',
        request_field='',
        request_type_name='PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsSearchRequest',
        response_type_name='SearchGrantsResponse',
        supports_download=False,
    )

    def Withdraw(self, request, global_params=None):
      r"""`WithdrawGrant` is used to immediately withdraw the grant. This method can be called when the grant is in a non-terminal state.

      Args:
        request: (PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsWithdrawRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Withdraw')
      return self._RunMethod(
          config, request, global_params=global_params)

    Withdraw.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/entitlements/{entitlementsId}/grants/{grantsId}:withdraw',
        http_method='POST',
        method_id='privilegedaccessmanager.projects.locations.entitlements.grants.withdraw',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:withdraw',
        request_field='withdrawGrantRequest',
        request_type_name='PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsWithdrawRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsEntitlementsService(base_api.BaseApiService):
    """Service class for the projects_locations_entitlements resource."""

    _NAME = 'projects_locations_entitlements'

    def __init__(self, client):
      super(PrivilegedaccessmanagerV1alpha.ProjectsLocationsEntitlementsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new entitlement in a given project/folder/organization and location.

      Args:
        request: (PrivilegedaccessmanagerProjectsLocationsEntitlementsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/entitlements',
        http_method='POST',
        method_id='privilegedaccessmanager.projects.locations.entitlements.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['entitlementId', 'requestId'],
        relative_path='v1alpha/{+parent}/entitlements',
        request_field='entitlement',
        request_type_name='PrivilegedaccessmanagerProjectsLocationsEntitlementsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single entitlement. This method can only be called when there are no in-progress (`ACTIVE`/`ACTIVATING`/`REVOKING`) grants under the entitlement.

      Args:
        request: (PrivilegedaccessmanagerProjectsLocationsEntitlementsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/entitlements/{entitlementsId}',
        http_method='DELETE',
        method_id='privilegedaccessmanager.projects.locations.entitlements.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force', 'requestId'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PrivilegedaccessmanagerProjectsLocationsEntitlementsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single entitlement.

      Args:
        request: (PrivilegedaccessmanagerProjectsLocationsEntitlementsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Entitlement) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/entitlements/{entitlementsId}',
        http_method='GET',
        method_id='privilegedaccessmanager.projects.locations.entitlements.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PrivilegedaccessmanagerProjectsLocationsEntitlementsGetRequest',
        response_type_name='Entitlement',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists entitlements in a given project/folder/organization and location.

      Args:
        request: (PrivilegedaccessmanagerProjectsLocationsEntitlementsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEntitlementsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/entitlements',
        http_method='GET',
        method_id='privilegedaccessmanager.projects.locations.entitlements.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/entitlements',
        request_field='',
        request_type_name='PrivilegedaccessmanagerProjectsLocationsEntitlementsListRequest',
        response_type_name='ListEntitlementsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the entitlement specified in the request. Updated fields in the entitlement need to be specified in an update mask. The changes made to an entitlement are applicable only on future grants of the entitlement. However, if new approvers are added or existing approvers are removed from the approval workflow, the changes are effective on existing grants. The following fields are not supported for updates: * All immutable fields * Entitlement name * Resource name * Resource type * Adding an approval workflow in an entitlement which previously had no approval workflow. * Deleting the approval workflow from an entitlement. * Adding or deleting a step in the approval workflow (only one step is supported) Note that updates are allowed on the list of approvers in an approval workflow step.

      Args:
        request: (PrivilegedaccessmanagerProjectsLocationsEntitlementsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/entitlements/{entitlementsId}',
        http_method='PATCH',
        method_id='privilegedaccessmanager.projects.locations.entitlements.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='entitlement',
        request_type_name='PrivilegedaccessmanagerProjectsLocationsEntitlementsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Search(self, request, global_params=None):
      r"""`SearchEntitlements` returns entitlements on which the caller has the specified access.

      Args:
        request: (PrivilegedaccessmanagerProjectsLocationsEntitlementsSearchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SearchEntitlementsResponse) The response message.
      """
      config = self.GetMethodConfig('Search')
      return self._RunMethod(
          config, request, global_params=global_params)

    Search.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/entitlements:search',
        http_method='GET',
        method_id='privilegedaccessmanager.projects.locations.entitlements.search',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['callerAccessType', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/entitlements:search',
        request_field='',
        request_type_name='PrivilegedaccessmanagerProjectsLocationsEntitlementsSearchRequest',
        response_type_name='SearchEntitlementsResponse',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(PrivilegedaccessmanagerV1alpha.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (PrivilegedaccessmanagerProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='privilegedaccessmanager.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PrivilegedaccessmanagerProjectsLocationsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (PrivilegedaccessmanagerProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='privilegedaccessmanager.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PrivilegedaccessmanagerProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (PrivilegedaccessmanagerProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='privilegedaccessmanager.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+name}/operations',
        request_field='',
        request_type_name='PrivilegedaccessmanagerProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(PrivilegedaccessmanagerV1alpha.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def CheckOnboardingStatus(self, request, global_params=None):
      r"""`CheckOnboardingStatus` reports the onboarding status for a project/folder/organization. Any findings reported by this API need to be fixed before PAM can be used on the resource.

      Args:
        request: (PrivilegedaccessmanagerProjectsLocationsCheckOnboardingStatusRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CheckOnboardingStatusResponse) The response message.
      """
      config = self.GetMethodConfig('CheckOnboardingStatus')
      return self._RunMethod(
          config, request, global_params=global_params)

    CheckOnboardingStatus.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}:checkOnboardingStatus',
        http_method='GET',
        method_id='privilegedaccessmanager.projects.locations.checkOnboardingStatus',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha/{+parent}:checkOnboardingStatus',
        request_field='',
        request_type_name='PrivilegedaccessmanagerProjectsLocationsCheckOnboardingStatusRequest',
        response_type_name='CheckOnboardingStatusResponse',
        supports_download=False,
    )

    def EffectiveSettings(self, request, global_params=None):
      r"""`FetchEffectiveSettings` returns the effective PAM Settings for the given project/folder/organization.

      Args:
        request: (PrivilegedaccessmanagerProjectsLocationsEffectiveSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FetchEffectiveSettingsResponse) The response message.
      """
      config = self.GetMethodConfig('EffectiveSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    EffectiveSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}:effectiveSettings',
        http_method='GET',
        method_id='privilegedaccessmanager.projects.locations.effectiveSettings',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha/{+parent}:effectiveSettings',
        request_field='',
        request_type_name='PrivilegedaccessmanagerProjectsLocationsEffectiveSettingsRequest',
        response_type_name='FetchEffectiveSettingsResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (PrivilegedaccessmanagerProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='privilegedaccessmanager.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PrivilegedaccessmanagerProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def GetSettings(self, request, global_params=None):
      r"""`GetSettings` returns the PAM Settings for the given project/folder/ organization.

      Args:
        request: (PrivilegedaccessmanagerProjectsLocationsGetSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Settings) The response message.
      """
      config = self.GetMethodConfig('GetSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/settings',
        http_method='GET',
        method_id='privilegedaccessmanager.projects.locations.getSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='PrivilegedaccessmanagerProjectsLocationsGetSettingsRequest',
        response_type_name='Settings',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (PrivilegedaccessmanagerProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations',
        http_method='GET',
        method_id='privilegedaccessmanager.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+name}/locations',
        request_field='',
        request_type_name='PrivilegedaccessmanagerProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

    def UpdateSettings(self, request, global_params=None):
      r"""`UpdateSettings` updates the PAM Settings resource specified in the request. Updated fields in the settings need to be specified in an update mask. The following fields are not supported for updates: * Settings name * Create time * Update time * Etag.

      Args:
        request: (PrivilegedaccessmanagerProjectsLocationsUpdateSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('UpdateSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/settings',
        http_method='PATCH',
        method_id='privilegedaccessmanager.projects.locations.updateSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='settings',
        request_type_name='PrivilegedaccessmanagerProjectsLocationsUpdateSettingsRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(PrivilegedaccessmanagerV1alpha.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
