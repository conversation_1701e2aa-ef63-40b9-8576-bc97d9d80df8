"""Generated message classes for privilegedaccessmanager version v1alpha.

Privileged Access Manager (PAM) helps you on your journey towards least
privilege and helps mitigate risks tied to privileged access misuse or abuse.
PAM allows you to shift from always-on standing privileges towards on-demand
access with just-in-time, time-bound, and approval-based access elevations.
PAM allows IAM administrators to create entitlements that can grant just-in-
time, temporary access to any resource scope. Requesters can explore eligible
entitlements and request the access needed for their task. Approvers are
notified when approvals await their decision. Streamlined workflows
facilitated by using PAM can support various use cases, including emergency
access for incident responders, time-boxed access for developers for critical
deployment or maintenance, temporary access for operators for data ingestion
and audits, JIT access to service accounts for automated tasks, and more.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'privilegedaccessmanager'


class AccessControlEntry(_messages.Message):
  r"""`AccessControlEntry` is used to control who can do some operation.

  Fields:
    principals: Optional. Users who are allowed for the operation. Each entry
      should be a valid v1 IAM principal identifier. The format for these is
      documented at: https://cloud.google.com/iam/docs/principal-
      identifiers#v1
  """

  principals = _messages.StringField(1, repeated=True)


class Activated(_messages.Message):
  r"""An event representing that the grant was successfully activated."""


class ActivationFailed(_messages.Message):
  r"""An event representing that the grant activation failed.

  Fields:
    error: Output only. The error that occurred while activating the grant.
  """

  error = _messages.MessageField('Status', 1)


class AdditionalNotificationTargets(_messages.Message):
  r"""`AdditionalNotificationTargets` includes email addresses to be notified.

  Fields:
    adminEmailRecipients: Optional. Additional email addresses to be notified
      when a principal (requester) is granted access.
    requesterEmailRecipients: Optional. Additional email address to be
      notified about an eligible entitlement.
  """

  adminEmailRecipients = _messages.StringField(1, repeated=True)
  requesterEmailRecipients = _messages.StringField(2, repeated=True)


class ApprovalWorkflow(_messages.Message):
  r"""Different types of approval workflows that can be used to gate
  privileged access granting.

  Fields:
    manualApprovals: An approval workflow where users designated as approvers
      review and act on the grants.
  """

  manualApprovals = _messages.MessageField('ManualApprovals', 1)


class ApproveGrantRequest(_messages.Message):
  r"""Request message for `ApproveGrant` method.

  Fields:
    reason: Optional. The reason for approving this grant. This is required if
      the `require_approver_justification` field of the `ManualApprovals`
      workflow used in this grant is true.
  """

  reason = _messages.StringField(1)


class Approved(_messages.Message):
  r"""An event representing that the grant was approved.

  Fields:
    actor: Output only. Username of the user who approved the grant.
    reason: Output only. The reason provided by the approver for approving the
      grant.
    stepId: Output only. The ID of the step that was approved.
  """

  actor = _messages.StringField(1)
  reason = _messages.StringField(2)
  stepId = _messages.StringField(3)


class AuditTrail(_messages.Message):
  r"""Audit trail for the access provided by this grant.

  Fields:
    accessGrantTime: Output only. The time at which access was given.
    accessRemoveTime: Output only. The time at which the system removed
      access. This could be because of an automatic expiry or because of a
      revocation. If unspecified, then access hasn't been removed yet.
  """

  accessGrantTime = _messages.StringField(1)
  accessRemoveTime = _messages.StringField(2)


class CheckOnboardingStatusResponse(_messages.Message):
  r"""Response message for `CheckOnboardingStatus` method.

  Fields:
    findings: List of issues that are preventing PAM from functioning for this
      resource and need to be fixed to complete onboarding. Some issues might
      not be detected or reported.
    serviceAccount: The service account that PAM uses to act on this resource.
  """

  findings = _messages.MessageField('Finding', 1, repeated=True)
  serviceAccount = _messages.StringField(2)


class Denied(_messages.Message):
  r"""An event representing that the grant was denied.

  Fields:
    actor: Output only. Username of the user who denied the grant.
    reason: Output only. The reason provided by the approver for denying the
      grant.
    stepId: Output only. The ID of the step that was denied.
  """

  actor = _messages.StringField(1)
  reason = _messages.StringField(2)
  stepId = _messages.StringField(3)


class DenyGrantRequest(_messages.Message):
  r"""Request message for `DenyGrant` method.

  Fields:
    reason: Optional. The reason for denying this grant. This is required if
      `require_approver_justification` field of the `ManualApprovals` workflow
      used in this grant is true.
  """

  reason = _messages.StringField(1)


class Ended(_messages.Message):
  r"""An event representing that the grant has ended."""


class Entitlement(_messages.Message):
  r"""An entitlement defines the eligibility of a set of users to obtain
  predefined access for some time possibly after going through an approval
  workflow.

  Enums:
    StateValueValuesEnum: Output only. Current state of this entitlement.

  Fields:
    additionalNotificationTargets: Optional. Additional email addresses to be
      notified based on actions taken.
    approvalWorkflow: Optional. The approvals needed before access are granted
      to a requester. No approvals are needed if this field is null.
    createTime: Output only. Create time stamp.
    eligibleUsers: Optional. Who can create grants using this entitlement.
      This list should contain at most one entry.
    etag: An `etag` is used for optimistic concurrency control as a way to
      prevent simultaneous updates to the same entitlement. An `etag` is
      returned in the response to `GetEntitlement` and the caller should put
      the `etag` in the request to `UpdateEntitlement` so that their change is
      applied on the same version. If this field is omitted or if there is a
      mismatch while updating an entitlement, then the server rejects the
      request.
    maxRequestDuration: Required. The maximum amount of time that access is
      granted for a request. A requester can ask for a duration less than
      this, but never more.
    name: Identifier. Name of the entitlement. Possible formats: *
      `organizations/{organization-
      number}/locations/{region}/entitlements/{entitlement-id}` *
      `folders/{folder-number}/locations/{region}/entitlements/{entitlement-
      id}` * `projects/{project-id|project-
      number}/locations/{region}/entitlements/{entitlement-id}`
    privilegedAccess: The access granted to a requester on successful
      approval.
    requesterJustificationConfig: Required. The manner in which the requester
      should provide a justification for requesting access.
    state: Output only. Current state of this entitlement.
    updateTime: Output only. Update time stamp.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of this entitlement.

    Values:
      STATE_UNSPECIFIED: Unspecified state. This value is never returned by
        the server.
      CREATING: The entitlement is being created.
      AVAILABLE: The entitlement is available for requesting access.
      DELETING: The entitlement is being deleted.
      DELETED: The entitlement has been deleted.
      UPDATING: The entitlement is being updated.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    AVAILABLE = 2
    DELETING = 3
    DELETED = 4
    UPDATING = 5

  additionalNotificationTargets = _messages.MessageField('AdditionalNotificationTargets', 1)
  approvalWorkflow = _messages.MessageField('ApprovalWorkflow', 2)
  createTime = _messages.StringField(3)
  eligibleUsers = _messages.MessageField('AccessControlEntry', 4, repeated=True)
  etag = _messages.StringField(5)
  maxRequestDuration = _messages.StringField(6)
  name = _messages.StringField(7)
  privilegedAccess = _messages.MessageField('PrivilegedAccess', 8)
  requesterJustificationConfig = _messages.MessageField('RequesterJustificationConfig', 9)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  updateTime = _messages.StringField(11)


class Event(_messages.Message):
  r"""A single operation on the grant.

  Fields:
    activated: The grant was successfully activated to give access.
    activationFailed: There was a non-retriable error while trying to give
      access.
    approved: The grant was approved.
    denied: The grant was denied.
    ended: Access given by the grant ended automatically as the approved
      duration was over.
    eventTime: Output only. The time (as recorded at server) when this event
      occurred.
    expired: The approval workflow did not complete in the necessary duration,
      and so the grant is expired.
    externallyModified: The policy bindings made by grant have been modified
      outside of PAM.
    requested: The grant was requested.
    revoked: The grant was revoked.
    scheduled: The grant has been scheduled to give access.
    withdrawn: The grant was withdrawn.
  """

  activated = _messages.MessageField('Activated', 1)
  activationFailed = _messages.MessageField('ActivationFailed', 2)
  approved = _messages.MessageField('Approved', 3)
  denied = _messages.MessageField('Denied', 4)
  ended = _messages.MessageField('Ended', 5)
  eventTime = _messages.StringField(6)
  expired = _messages.MessageField('Expired', 7)
  externallyModified = _messages.MessageField('ExternallyModified', 8)
  requested = _messages.MessageField('Requested', 9)
  revoked = _messages.MessageField('Revoked', 10)
  scheduled = _messages.MessageField('Scheduled', 11)
  withdrawn = _messages.MessageField('Withdrawn', 12)


class Expired(_messages.Message):
  r"""An event representing that the grant was expired."""


class ExternallyModified(_messages.Message):
  r"""An event representing that the policy bindings made by this grant were
  modified externally.
  """



class FetchEffectiveSettingsResponse(_messages.Message):
  r"""The effective value of the settings at the given location resource,
  evaluated based on the crm resource hierarchy.

  Fields:
    emailNotificationSettings: Output only. `EmailNotificationSettings`
      defines effective node-wide email notification preferences for various
      PAM events.
    parent: Output only. The resource on which the settings are effective.
      Possible formats: * `projects/{project-number|project-
      id}/locations/{region}` * `folders/{folder-number}/locations/{region}` *
      `organizations/{organization-number}/locations/{region}`
    serviceAccountApproverSettings: Output only. Effective settings for
      allowing service account as approvers.
  """

  emailNotificationSettings = _messages.MessageField('FetchEffectiveSettingsResponseEmailNotificationSettings', 1)
  parent = _messages.StringField(2)
  serviceAccountApproverSettings = _messages.MessageField('FetchEffectiveSettingsResponseServiceAccountApproverSettings', 3)


class FetchEffectiveSettingsResponseEmailNotificationSettings(_messages.Message):
  r"""`EmailNotificationSettings` reflects the effective node-wide email
  notification settings.

  Fields:
    customNotificationBehavior: Output only. Granular settings of
      notifications.
    disableAllNotifications: Output only. Disable all notifications.
    source: Output only. The name of the resource from which the notification
      behavior is inherited. This field remains empty if the setting is not
      defined at either the parent or resource level, in which case PAM's
      default behavior is applied.
  """

  customNotificationBehavior = _messages.MessageField('FetchEffectiveSettingsResponseEmailNotificationSettingsCustomNotificationBehavior', 1)
  disableAllNotifications = _messages.MessageField('FetchEffectiveSettingsResponseEmailNotificationSettingsDisableAllNotifications', 2)
  source = _messages.StringField(3)


class FetchEffectiveSettingsResponseEmailNotificationSettingsCustomNotificationBehavior(_messages.Message):
  r"""`CustomNotificationBehavior` reflects the granular notification delivery
  settings for specific events and personas, as configured by the admin.

  Fields:
    adminNotifications: Output only. Admin email notifications.
    approverNotifications: Output only. Approver email notifications.
    requesterNotifications: Output only. Requester email notifications.
  """

  adminNotifications = _messages.MessageField('FetchEffectiveSettingsResponseEmailNotificationSettingsCustomNotificationBehaviorAdminNotifications', 1)
  approverNotifications = _messages.MessageField('FetchEffectiveSettingsResponseEmailNotificationSettingsCustomNotificationBehaviorApproverNotifications', 2)
  requesterNotifications = _messages.MessageField('FetchEffectiveSettingsResponseEmailNotificationSettingsCustomNotificationBehaviorRequesterNotifications', 3)


class FetchEffectiveSettingsResponseEmailNotificationSettingsCustomNotificationBehaviorAdminNotifications(_messages.Message):
  r"""Email notifications specific to Admins.

  Fields:
    notifyGrantActivated: Output only. Notification delivery for grant
      activated.
    notifyGrantActivationFailed: Output only. Notification delivery for grant
      activation failed.
    notifyGrantEnded: Output only. Notification delivery for grant ended.
    notifyGrantExternallyModified: Output only. Notification delivery for
      grant externally modified.
  """

  notifyGrantActivated = _messages.BooleanField(1)
  notifyGrantActivationFailed = _messages.BooleanField(2)
  notifyGrantEnded = _messages.BooleanField(3)
  notifyGrantExternallyModified = _messages.BooleanField(4)


class FetchEffectiveSettingsResponseEmailNotificationSettingsCustomNotificationBehaviorApproverNotifications(_messages.Message):
  r"""Email notifications specific to Approvers.

  Fields:
    notifyPendingApproval: Output only. Notification delivery for pending
      approval.
  """

  notifyPendingApproval = _messages.BooleanField(1)


class FetchEffectiveSettingsResponseEmailNotificationSettingsCustomNotificationBehaviorRequesterNotifications(_messages.Message):
  r"""Email notifications specific to Requesters.

  Fields:
    notifyEntitlementAssigned: Output only. Notification delivery for
      entitlement assigned.
    notifyGrantActivated: Output only. Notification delivery for grant
      activated.
    notifyGrantActivationFailed: Output only. Notification delivery for grant
      activation failed.
    notifyGrantDenied: Output only. Notification delivery for grant denied.
    notifyGrantEnded: Output only. Notification delivery for grant ended.
    notifyGrantExpired: Output only. Notification delivery for grant request
      expired.
    notifyGrantExternallyModified: Output only. Notification delivery for
      grant externally modified.
    notifyGrantRevoked: Output only. Notification delivery for grant revoked.
  """

  notifyEntitlementAssigned = _messages.BooleanField(1)
  notifyGrantActivated = _messages.BooleanField(2)
  notifyGrantActivationFailed = _messages.BooleanField(3)
  notifyGrantDenied = _messages.BooleanField(4)
  notifyGrantEnded = _messages.BooleanField(5)
  notifyGrantExpired = _messages.BooleanField(6)
  notifyGrantExternallyModified = _messages.BooleanField(7)
  notifyGrantRevoked = _messages.BooleanField(8)


class FetchEffectiveSettingsResponseEmailNotificationSettingsDisableAllNotifications(_messages.Message):
  r"""This option indicates that all email notifications are disabled."""


class FetchEffectiveSettingsResponseServiceAccountApproverSettings(_messages.Message):
  r"""This controls whether service accounts are allowed to approve grants or
  can be designated as approvers within PAM entitlements.

  Fields:
    disablementReason: Output only. The reason why service account approvals
      are disabled.
    enabled: Output only. Indicates whether service account is allowed to
      grant approvals.
    source: Output only. The resource from which the service account approver
      setting is inherited. This field remains empty if the setting is not
      defined at either the parent or resource level, in which case PAM's
      default behavior is applied.
  """

  disablementReason = _messages.MessageField('FetchEffectiveSettingsResponseServiceAccountApproverSettingsDisablementReason', 1)
  enabled = _messages.BooleanField(2)
  source = _messages.StringField(3)


class FetchEffectiveSettingsResponseServiceAccountApproverSettingsDisablementReason(_messages.Message):
  r"""The reason for disabling service account approvals.

  Enums:
    TypeValueValuesEnum: Output only. The specific reason type for disabling
      service account approvals.

  Fields:
    type: Output only. The specific reason type for disabling service account
      approvals.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. The specific reason type for disabling service account
    approvals.

    Values:
      REASON_TYPE_UNSPECIFIED: No specific setting was applied, default PAM
        behavior in effect.
      PAM_ADVANCED_TIER_DISABLED: Approval disabled because PAM Advanced tier
        is disabled.
    """
    REASON_TYPE_UNSPECIFIED = 0
    PAM_ADVANCED_TIER_DISABLED = 1

  type = _messages.EnumField('TypeValueValuesEnum', 1)


class Finding(_messages.Message):
  r"""Finding represents an issue which prevents PAM from functioning properly
  for this resource.

  Fields:
    iamAccessDenied: PAM's service account is being denied access by Cloud
      IAM.
  """

  iamAccessDenied = _messages.MessageField('IAMAccessDenied', 1)


class GcpIamAccess(_messages.Message):
  r"""`GcpIamAccess` represents IAM based access control on a Google Cloud
  resource. Refer to https://cloud.google.com/iam/docs to understand more
  about IAM.

  Fields:
    resource: Required. Name of the resource.
    resourceType: Required. The type of this resource.
    roleBindings: Required. Role bindings that are created on successful
      grant.
  """

  resource = _messages.StringField(1)
  resourceType = _messages.StringField(2)
  roleBindings = _messages.MessageField('RoleBinding', 3, repeated=True)


class GoogleProtobufEmpty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Grant(_messages.Message):
  r"""A grant represents a request from a user for obtaining the access
  specified in an entitlement they are eligible for.

  Enums:
    StateValueValuesEnum: Output only. Current state of this grant.

  Fields:
    additionalEmailRecipients: Optional. Additional email addresses to notify
      for all the actions performed on the grant.
    auditTrail: Output only. Audit trail of access provided by this grant. If
      unspecified then access was never granted.
    createTime: Output only. Create time stamp.
    externallyModified: Output only. Flag set by the PAM system to indicate
      that policy bindings made by this grant have been modified from outside
      PAM. After it is set, this flag remains set forever irrespective of the
      grant state. A `true` value here indicates that PAM no longer has any
      certainty on the access a user has because of this grant.
    justification: Optional. Justification of why this access is needed.
    name: Identifier. Name of this grant. Possible formats: *
      `organizations/{organization-
      number}/locations/{region}/entitlements/{entitlement-id}/grants/{grant-
      id}` * `folders/{folder-
      number}/locations/{region}/entitlements/{entitlement-id}/grants/{grant-
      id}` * `projects/{project-id|project-
      number}/locations/{region}/entitlements/{entitlement-id}/grants/{grant-
      id}` The last segment of this name (`{grant-id}`) is autogenerated.
    privilegedAccess: Output only. The access that would be granted by this
      grant.
    requestedDuration: Required. The amount of time access is needed for. This
      value should be less than the `max_request_duration` value of the
      entitlement.
    requestedPrivilegedAccess: Optional. The accesses requested to be granted
      by this grant.
    requester: Output only. Username of the user who created this grant.
    state: Output only. Current state of this grant.
    timeline: Output only. Timeline of this grant.
    updateTime: Output only. Update time stamp.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of this grant.

    Values:
      STATE_UNSPECIFIED: Unspecified state. This value is never returned by
        the server.
      APPROVAL_AWAITED: The entitlement had an approval workflow configured
        and this grant is waiting for the workflow to complete.
      DENIED: The approval workflow completed with a denied result. No access
        is granted for this grant. This is a terminal state.
      SCHEDULED: The approval workflow completed successfully with an approved
        result or none was configured. Access is provided at an appropriate
        time.
      ACTIVATING: Access is being given.
      ACTIVE: Access was successfully given and is currently active.
      ACTIVATION_FAILED: The system could not give access due to a non-
        retriable error. This is a terminal state.
      EXPIRED: Expired after waiting for the approval workflow to complete.
        This is a terminal state.
      REVOKING: Access is being revoked.
      REVOKED: Access was revoked by a user. This is a terminal state.
      ENDED: System took back access as the requested duration was over. This
        is a terminal state.
      WITHDRAWING: Access is being withdrawn.
      WITHDRAWN: Grant was withdrawn by the grant owner. This is a terminal
        state.
    """
    STATE_UNSPECIFIED = 0
    APPROVAL_AWAITED = 1
    DENIED = 2
    SCHEDULED = 3
    ACTIVATING = 4
    ACTIVE = 5
    ACTIVATION_FAILED = 6
    EXPIRED = 7
    REVOKING = 8
    REVOKED = 9
    ENDED = 10
    WITHDRAWING = 11
    WITHDRAWN = 12

  additionalEmailRecipients = _messages.StringField(1, repeated=True)
  auditTrail = _messages.MessageField('AuditTrail', 2)
  createTime = _messages.StringField(3)
  externallyModified = _messages.BooleanField(4)
  justification = _messages.MessageField('Justification', 5)
  name = _messages.StringField(6)
  privilegedAccess = _messages.MessageField('PrivilegedAccess', 7)
  requestedDuration = _messages.StringField(8)
  requestedPrivilegedAccess = _messages.MessageField('RequestedPrivilegedAccess', 9, repeated=True)
  requester = _messages.StringField(10)
  state = _messages.EnumField('StateValueValuesEnum', 11)
  timeline = _messages.MessageField('Timeline', 12)
  updateTime = _messages.StringField(13)


class IAMAccessDenied(_messages.Message):
  r"""PAM's service account is being denied access by Cloud IAM. This can be
  fixed by granting a role that contains the missing permissions to the
  service account or exempting it from deny policies if they are blocking the
  access.

  Fields:
    missingPermissions: List of permissions that are being denied.
  """

  missingPermissions = _messages.StringField(1, repeated=True)


class Justification(_messages.Message):
  r"""Justification represents a justification for requesting access.

  Fields:
    unstructuredJustification: A free form textual justification. The system
      only ensures that this is not empty. No other kind of validation is
      performed on the string.
  """

  unstructuredJustification = _messages.StringField(1)


class ListEntitlementsResponse(_messages.Message):
  r"""Message for response to listing entitlements.

  Fields:
    entitlements: The list of entitlements.
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  entitlements = _messages.MessageField('Entitlement', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListGrantsResponse(_messages.Message):
  r"""Message for response to listing grants.

  Fields:
    grants: The list of grants.
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  grants = _messages.MessageField('Grant', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class ManualApprovals(_messages.Message):
  r"""A manual approval workflow where users who are designated as approvers
  need to call the `ApproveGrant`/`DenyGrant` APIs for a grant. The workflow
  can consist of multiple serial steps where each step defines who can act as
  approver in that step and how many of those users should approve before the
  workflow moves to the next step. This can be used to create approval
  workflows such as: * Require an approval from any user in a group G. *
  Require an approval from any k number of users from a Group G. * Require an
  approval from any user in a group G and then from a user U. A single user
  might be part of the `approvers` ACL for multiple steps in this workflow,
  but they can only approve once and that approval is only considered to
  satisfy the approval step at which it was granted.

  Fields:
    requireApproverJustification: Optional. Do the approvers need to provide a
      justification for their actions?
    steps: Optional. List of approval steps in this workflow. These steps are
      followed in the specified order sequentially. Only 1 step is supported.
  """

  requireApproverJustification = _messages.BooleanField(1)
  steps = _messages.MessageField('Step', 2, repeated=True)


class NotMandatory(_messages.Message):
  r"""The justification is not mandatory but can be provided in any of the
  supported formats.
  """



class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class PrivilegedAccess(_messages.Message):
  r"""Privileged access that this service can be used to gate.

  Fields:
    gcpIamAccess: Access to a Google Cloud resource through IAM.
  """

  gcpIamAccess = _messages.MessageField('GcpIamAccess', 1)


class PrivilegedaccessmanagerFoldersLocationsCheckOnboardingStatusRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerFoldersLocationsCheckOnboardingStatusRequest
  object.

  Fields:
    parent: Required. The resource for which the onboarding status should be
      checked. Should be in one of the following formats: *
      `projects/{project-number|project-id}/locations/{region}` *
      `folders/{folder-number}/locations/{region}` *
      `organizations/{organization-number}/locations/{region}`
  """

  parent = _messages.StringField(1, required=True)


class PrivilegedaccessmanagerFoldersLocationsEffectiveSettingsRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerFoldersLocationsEffectiveSettingsRequest
  object.

  Fields:
    parent: Required. The resource for which the effective settings should be
      fetched. Should be in one of the following formats: *
      `projects/{project-number|project-id}/locations/{region}` *
      `folders/{folder-number}/locations/{region}` *
      `organizations/{organization-number}/locations/{region}`
  """

  parent = _messages.StringField(1, required=True)


class PrivilegedaccessmanagerFoldersLocationsEntitlementsCreateRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerFoldersLocationsEntitlementsCreateRequest
  object.

  Fields:
    entitlement: A Entitlement resource to be passed as the request body.
    entitlementId: Required. The ID to use for this entitlement. This becomes
      the last part of the resource name. This value should be 4-63 characters
      in length, and valid characters are "[a-z]", "[0-9]", and "-". The first
      character should be from [a-z]. This value should be unique among all
      other entitlements under the specified `parent`.
    parent: Required. Name of the parent resource for the entitlement.
      Possible formats: * `organizations/{organization-
      number}/locations/{region}` * `folders/{folder-
      number}/locations/{region}` * `projects/{project-id|project-
      number}/locations/{region}`
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      knows to ignore the request if it has already been completed. The server
      guarantees this for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, ignores the second request and returns the
      previous operation's response. This prevents clients from accidentally
      creating duplicate entitlements. The request ID must be a valid UUID
      with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  entitlement = _messages.MessageField('Entitlement', 1)
  entitlementId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class PrivilegedaccessmanagerFoldersLocationsEntitlementsDeleteRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerFoldersLocationsEntitlementsDeleteRequest
  object.

  Fields:
    force: Optional. If set to true, any child grant under this entitlement is
      also deleted. (Otherwise, the request only works if the entitlement has
      no child grant.)
    name: Required. Name of the resource.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      knows to ignore the request if it has already been completed. The server
      guarantees this for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, ignores the second request. The request ID must
      be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class PrivilegedaccessmanagerFoldersLocationsEntitlementsGetRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerFoldersLocationsEntitlementsGetRequest object.

  Fields:
    name: Required. Name of the resource.
  """

  name = _messages.StringField(1, required=True)


class PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsApproveRequest(_messages.Message):
  r"""A
  PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsApproveRequest
  object.

  Fields:
    approveGrantRequest: A ApproveGrantRequest resource to be passed as the
      request body.
    name: Required. Name of the grant resource which is being approved.
  """

  approveGrantRequest = _messages.MessageField('ApproveGrantRequest', 1)
  name = _messages.StringField(2, required=True)


class PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsCreateRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsCreateRequest
  object.

  Fields:
    grant: A Grant resource to be passed as the request body.
    parent: Required. Name of the parent entitlement for which this grant is
      being requested.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      knows to ignore the request if it has already been completed. The server
      guarantees this for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate grants. The request ID must
      be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  grant = _messages.MessageField('Grant', 1)
  parent = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsDenyRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsDenyRequest
  object.

  Fields:
    denyGrantRequest: A DenyGrantRequest resource to be passed as the request
      body.
    name: Required. Name of the grant resource which is being denied.
  """

  denyGrantRequest = _messages.MessageField('DenyGrantRequest', 1)
  name = _messages.StringField(2, required=True)


class PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsGetRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsGetRequest
  object.

  Fields:
    name: Required. Name of the resource.
  """

  name = _messages.StringField(1, required=True)


class PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsListRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsListRequest
  object.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. The server may return fewer items
      than requested. If unspecified, the server picks an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent resource which owns the grants.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsRevokeRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsRevokeRequest
  object.

  Fields:
    name: Required. Name of the grant resource which is being revoked.
    revokeGrantRequest: A RevokeGrantRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  revokeGrantRequest = _messages.MessageField('RevokeGrantRequest', 2)


class PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsSearchRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsSearchRequest
  object.

  Enums:
    CallerRelationshipValueValuesEnum: Required. Only grants which the caller
      is related to by this relationship are returned in the response.

  Fields:
    callerRelationship: Required. Only grants which the caller is related to
      by this relationship are returned in the response.
    filter: Optional. Only grants matching this filter are returned in the
      response.
    pageSize: Optional. Requested page size. The server may return fewer items
      than requested. If unspecified, server picks an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent which owns the grant resources.
  """

  class CallerRelationshipValueValuesEnum(_messages.Enum):
    r"""Required. Only grants which the caller is related to by this
    relationship are returned in the response.

    Values:
      CALLER_RELATIONSHIP_TYPE_UNSPECIFIED: Unspecified caller relationship
        type.
      HAD_CREATED: The user created this grant by calling `CreateGrant`
        earlier.
      CAN_APPROVE: The user is an approver for the entitlement that this grant
        is parented under and can currently approve/deny it.
      HAD_APPROVED: The caller had successfully approved/denied this grant
        earlier.
    """
    CALLER_RELATIONSHIP_TYPE_UNSPECIFIED = 0
    HAD_CREATED = 1
    CAN_APPROVE = 2
    HAD_APPROVED = 3

  callerRelationship = _messages.EnumField('CallerRelationshipValueValuesEnum', 1)
  filter = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsWithdrawRequest(_messages.Message):
  r"""A
  PrivilegedaccessmanagerFoldersLocationsEntitlementsGrantsWithdrawRequest
  object.

  Fields:
    name: Required. Name of the grant resource which is being withdrawn.
    withdrawGrantRequest: A WithdrawGrantRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  withdrawGrantRequest = _messages.MessageField('WithdrawGrantRequest', 2)


class PrivilegedaccessmanagerFoldersLocationsEntitlementsListRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerFoldersLocationsEntitlementsListRequest object.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint for how to order the results.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, the server picks an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent which owns the entitlement resources.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class PrivilegedaccessmanagerFoldersLocationsEntitlementsPatchRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerFoldersLocationsEntitlementsPatchRequest
  object.

  Fields:
    entitlement: A Entitlement resource to be passed as the request body.
    name: Identifier. Name of the entitlement. Possible formats: *
      `organizations/{organization-
      number}/locations/{region}/entitlements/{entitlement-id}` *
      `folders/{folder-number}/locations/{region}/entitlements/{entitlement-
      id}` * `projects/{project-id|project-
      number}/locations/{region}/entitlements/{entitlement-id}`
    updateMask: Required. The list of fields to update. A field is overwritten
      if, and only if, it is in the mask. Any immutable fields set in the mask
      are ignored by the server. Repeated fields and map fields are only
      allowed in the last position of a `paths` string and overwrite the
      existing values. Hence an update to a repeated field or a map should
      contain the entire list of values. The fields specified in the
      update_mask are relative to the resource and not to the request. (e.g.
      `MaxRequestDuration`; *not* `entitlement.MaxRequestDuration`) A value of
      '*' for this field refers to full replacement of the resource.
  """

  entitlement = _messages.MessageField('Entitlement', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class PrivilegedaccessmanagerFoldersLocationsEntitlementsSearchRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerFoldersLocationsEntitlementsSearchRequest
  object.

  Enums:
    CallerAccessTypeValueValuesEnum: Required. Only entitlements where the
      calling user has this access are returned.

  Fields:
    callerAccessType: Required. Only entitlements where the calling user has
      this access are returned.
    filter: Optional. Only entitlements matching this filter are returned in
      the response.
    pageSize: Optional. Requested page size. The server may return fewer items
      than requested. If unspecified, the server picks an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent which owns the entitlement resources.
  """

  class CallerAccessTypeValueValuesEnum(_messages.Enum):
    r"""Required. Only entitlements where the calling user has this access are
    returned.

    Values:
      CALLER_ACCESS_TYPE_UNSPECIFIED: Unspecified access type.
      GRANT_REQUESTER: The user has access to create grants using this
        entitlement.
      GRANT_APPROVER: The user has access to approve/deny grants created under
        this entitlement.
    """
    CALLER_ACCESS_TYPE_UNSPECIFIED = 0
    GRANT_REQUESTER = 1
    GRANT_APPROVER = 2

  callerAccessType = _messages.EnumField('CallerAccessTypeValueValuesEnum', 1)
  filter = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class PrivilegedaccessmanagerFoldersLocationsGetRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerFoldersLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class PrivilegedaccessmanagerFoldersLocationsGetSettingsRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerFoldersLocationsGetSettingsRequest object.

  Fields:
    name: Required. The name of the settings resource to be fetched.
  """

  name = _messages.StringField(1, required=True)


class PrivilegedaccessmanagerFoldersLocationsListRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerFoldersLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class PrivilegedaccessmanagerFoldersLocationsOperationsDeleteRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerFoldersLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class PrivilegedaccessmanagerFoldersLocationsOperationsGetRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerFoldersLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class PrivilegedaccessmanagerFoldersLocationsOperationsListRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerFoldersLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class PrivilegedaccessmanagerFoldersLocationsUpdateSettingsRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerFoldersLocationsUpdateSettingsRequest object.

  Fields:
    name: Identifier. Name of the settings resource. Possible formats:
      projects/{project-id|project-number}/locations/{location}/settings
      folders/{folder-number}/locations/{location}/settings
      organizations/{organization-number}/locations/{location}/settings
    settings: A Settings resource to be passed as the request body.
    updateMask: Required. The list of fields to update. A field is overwritten
      if, and only if, it is in the mask. Any immutable fields set in the mask
      are ignored by the server. Repeated fields and map fields are only
      allowed in the last position of a `paths` string and overwrite the
      existing values. Hence an update to a repeated field or a map should
      contain the entire list of values. The fields specified in the
      update_mask are relative to the resource and not to the request. A value
      of '*' for this field refers to full replacement of the resource.
  """

  name = _messages.StringField(1, required=True)
  settings = _messages.MessageField('Settings', 2)
  updateMask = _messages.StringField(3)


class PrivilegedaccessmanagerOrganizationsLocationsCheckOnboardingStatusRequest(_messages.Message):
  r"""A
  PrivilegedaccessmanagerOrganizationsLocationsCheckOnboardingStatusRequest
  object.

  Fields:
    parent: Required. The resource for which the onboarding status should be
      checked. Should be in one of the following formats: *
      `projects/{project-number|project-id}/locations/{region}` *
      `folders/{folder-number}/locations/{region}` *
      `organizations/{organization-number}/locations/{region}`
  """

  parent = _messages.StringField(1, required=True)


class PrivilegedaccessmanagerOrganizationsLocationsEffectiveSettingsRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerOrganizationsLocationsEffectiveSettingsRequest
  object.

  Fields:
    parent: Required. The resource for which the effective settings should be
      fetched. Should be in one of the following formats: *
      `projects/{project-number|project-id}/locations/{region}` *
      `folders/{folder-number}/locations/{region}` *
      `organizations/{organization-number}/locations/{region}`
  """

  parent = _messages.StringField(1, required=True)


class PrivilegedaccessmanagerOrganizationsLocationsEntitlementsCreateRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerOrganizationsLocationsEntitlementsCreateRequest
  object.

  Fields:
    entitlement: A Entitlement resource to be passed as the request body.
    entitlementId: Required. The ID to use for this entitlement. This becomes
      the last part of the resource name. This value should be 4-63 characters
      in length, and valid characters are "[a-z]", "[0-9]", and "-". The first
      character should be from [a-z]. This value should be unique among all
      other entitlements under the specified `parent`.
    parent: Required. Name of the parent resource for the entitlement.
      Possible formats: * `organizations/{organization-
      number}/locations/{region}` * `folders/{folder-
      number}/locations/{region}` * `projects/{project-id|project-
      number}/locations/{region}`
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      knows to ignore the request if it has already been completed. The server
      guarantees this for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, ignores the second request and returns the
      previous operation's response. This prevents clients from accidentally
      creating duplicate entitlements. The request ID must be a valid UUID
      with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  entitlement = _messages.MessageField('Entitlement', 1)
  entitlementId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class PrivilegedaccessmanagerOrganizationsLocationsEntitlementsDeleteRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerOrganizationsLocationsEntitlementsDeleteRequest
  object.

  Fields:
    force: Optional. If set to true, any child grant under this entitlement is
      also deleted. (Otherwise, the request only works if the entitlement has
      no child grant.)
    name: Required. Name of the resource.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      knows to ignore the request if it has already been completed. The server
      guarantees this for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, ignores the second request. The request ID must
      be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGetRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGetRequest
  object.

  Fields:
    name: Required. Name of the resource.
  """

  name = _messages.StringField(1, required=True)


class PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsApproveRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsApprove
  Request object.

  Fields:
    approveGrantRequest: A ApproveGrantRequest resource to be passed as the
      request body.
    name: Required. Name of the grant resource which is being approved.
  """

  approveGrantRequest = _messages.MessageField('ApproveGrantRequest', 1)
  name = _messages.StringField(2, required=True)


class PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsCreateRequest(_messages.Message):
  r"""A
  PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsCreateRequest
  object.

  Fields:
    grant: A Grant resource to be passed as the request body.
    parent: Required. Name of the parent entitlement for which this grant is
      being requested.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      knows to ignore the request if it has already been completed. The server
      guarantees this for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate grants. The request ID must
      be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  grant = _messages.MessageField('Grant', 1)
  parent = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsDenyRequest(_messages.Message):
  r"""A
  PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsDenyRequest
  object.

  Fields:
    denyGrantRequest: A DenyGrantRequest resource to be passed as the request
      body.
    name: Required. Name of the grant resource which is being denied.
  """

  denyGrantRequest = _messages.MessageField('DenyGrantRequest', 1)
  name = _messages.StringField(2, required=True)


class PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsGetRequest(_messages.Message):
  r"""A
  PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsGetRequest
  object.

  Fields:
    name: Required. Name of the resource.
  """

  name = _messages.StringField(1, required=True)


class PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsListRequest(_messages.Message):
  r"""A
  PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsListRequest
  object.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. The server may return fewer items
      than requested. If unspecified, the server picks an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent resource which owns the grants.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsRevokeRequest(_messages.Message):
  r"""A
  PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsRevokeRequest
  object.

  Fields:
    name: Required. Name of the grant resource which is being revoked.
    revokeGrantRequest: A RevokeGrantRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  revokeGrantRequest = _messages.MessageField('RevokeGrantRequest', 2)


class PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsSearchRequest(_messages.Message):
  r"""A
  PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsSearchRequest
  object.

  Enums:
    CallerRelationshipValueValuesEnum: Required. Only grants which the caller
      is related to by this relationship are returned in the response.

  Fields:
    callerRelationship: Required. Only grants which the caller is related to
      by this relationship are returned in the response.
    filter: Optional. Only grants matching this filter are returned in the
      response.
    pageSize: Optional. Requested page size. The server may return fewer items
      than requested. If unspecified, server picks an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent which owns the grant resources.
  """

  class CallerRelationshipValueValuesEnum(_messages.Enum):
    r"""Required. Only grants which the caller is related to by this
    relationship are returned in the response.

    Values:
      CALLER_RELATIONSHIP_TYPE_UNSPECIFIED: Unspecified caller relationship
        type.
      HAD_CREATED: The user created this grant by calling `CreateGrant`
        earlier.
      CAN_APPROVE: The user is an approver for the entitlement that this grant
        is parented under and can currently approve/deny it.
      HAD_APPROVED: The caller had successfully approved/denied this grant
        earlier.
    """
    CALLER_RELATIONSHIP_TYPE_UNSPECIFIED = 0
    HAD_CREATED = 1
    CAN_APPROVE = 2
    HAD_APPROVED = 3

  callerRelationship = _messages.EnumField('CallerRelationshipValueValuesEnum', 1)
  filter = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsWithdrawRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerOrganizationsLocationsEntitlementsGrantsWithdra
  wRequest object.

  Fields:
    name: Required. Name of the grant resource which is being withdrawn.
    withdrawGrantRequest: A WithdrawGrantRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  withdrawGrantRequest = _messages.MessageField('WithdrawGrantRequest', 2)


class PrivilegedaccessmanagerOrganizationsLocationsEntitlementsListRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerOrganizationsLocationsEntitlementsListRequest
  object.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint for how to order the results.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, the server picks an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent which owns the entitlement resources.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class PrivilegedaccessmanagerOrganizationsLocationsEntitlementsPatchRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerOrganizationsLocationsEntitlementsPatchRequest
  object.

  Fields:
    entitlement: A Entitlement resource to be passed as the request body.
    name: Identifier. Name of the entitlement. Possible formats: *
      `organizations/{organization-
      number}/locations/{region}/entitlements/{entitlement-id}` *
      `folders/{folder-number}/locations/{region}/entitlements/{entitlement-
      id}` * `projects/{project-id|project-
      number}/locations/{region}/entitlements/{entitlement-id}`
    updateMask: Required. The list of fields to update. A field is overwritten
      if, and only if, it is in the mask. Any immutable fields set in the mask
      are ignored by the server. Repeated fields and map fields are only
      allowed in the last position of a `paths` string and overwrite the
      existing values. Hence an update to a repeated field or a map should
      contain the entire list of values. The fields specified in the
      update_mask are relative to the resource and not to the request. (e.g.
      `MaxRequestDuration`; *not* `entitlement.MaxRequestDuration`) A value of
      '*' for this field refers to full replacement of the resource.
  """

  entitlement = _messages.MessageField('Entitlement', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class PrivilegedaccessmanagerOrganizationsLocationsEntitlementsSearchRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerOrganizationsLocationsEntitlementsSearchRequest
  object.

  Enums:
    CallerAccessTypeValueValuesEnum: Required. Only entitlements where the
      calling user has this access are returned.

  Fields:
    callerAccessType: Required. Only entitlements where the calling user has
      this access are returned.
    filter: Optional. Only entitlements matching this filter are returned in
      the response.
    pageSize: Optional. Requested page size. The server may return fewer items
      than requested. If unspecified, the server picks an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent which owns the entitlement resources.
  """

  class CallerAccessTypeValueValuesEnum(_messages.Enum):
    r"""Required. Only entitlements where the calling user has this access are
    returned.

    Values:
      CALLER_ACCESS_TYPE_UNSPECIFIED: Unspecified access type.
      GRANT_REQUESTER: The user has access to create grants using this
        entitlement.
      GRANT_APPROVER: The user has access to approve/deny grants created under
        this entitlement.
    """
    CALLER_ACCESS_TYPE_UNSPECIFIED = 0
    GRANT_REQUESTER = 1
    GRANT_APPROVER = 2

  callerAccessType = _messages.EnumField('CallerAccessTypeValueValuesEnum', 1)
  filter = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class PrivilegedaccessmanagerOrganizationsLocationsGetRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerOrganizationsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class PrivilegedaccessmanagerOrganizationsLocationsGetSettingsRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerOrganizationsLocationsGetSettingsRequest
  object.

  Fields:
    name: Required. The name of the settings resource to be fetched.
  """

  name = _messages.StringField(1, required=True)


class PrivilegedaccessmanagerOrganizationsLocationsListRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerOrganizationsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class PrivilegedaccessmanagerOrganizationsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerOrganizationsLocationsOperationsDeleteRequest
  object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class PrivilegedaccessmanagerOrganizationsLocationsOperationsGetRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerOrganizationsLocationsOperationsGetRequest
  object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class PrivilegedaccessmanagerOrganizationsLocationsOperationsListRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerOrganizationsLocationsOperationsListRequest
  object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class PrivilegedaccessmanagerOrganizationsLocationsUpdateSettingsRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerOrganizationsLocationsUpdateSettingsRequest
  object.

  Fields:
    name: Identifier. Name of the settings resource. Possible formats:
      projects/{project-id|project-number}/locations/{location}/settings
      folders/{folder-number}/locations/{location}/settings
      organizations/{organization-number}/locations/{location}/settings
    settings: A Settings resource to be passed as the request body.
    updateMask: Required. The list of fields to update. A field is overwritten
      if, and only if, it is in the mask. Any immutable fields set in the mask
      are ignored by the server. Repeated fields and map fields are only
      allowed in the last position of a `paths` string and overwrite the
      existing values. Hence an update to a repeated field or a map should
      contain the entire list of values. The fields specified in the
      update_mask are relative to the resource and not to the request. A value
      of '*' for this field refers to full replacement of the resource.
  """

  name = _messages.StringField(1, required=True)
  settings = _messages.MessageField('Settings', 2)
  updateMask = _messages.StringField(3)


class PrivilegedaccessmanagerProjectsLocationsCheckOnboardingStatusRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerProjectsLocationsCheckOnboardingStatusRequest
  object.

  Fields:
    parent: Required. The resource for which the onboarding status should be
      checked. Should be in one of the following formats: *
      `projects/{project-number|project-id}/locations/{region}` *
      `folders/{folder-number}/locations/{region}` *
      `organizations/{organization-number}/locations/{region}`
  """

  parent = _messages.StringField(1, required=True)


class PrivilegedaccessmanagerProjectsLocationsEffectiveSettingsRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerProjectsLocationsEffectiveSettingsRequest
  object.

  Fields:
    parent: Required. The resource for which the effective settings should be
      fetched. Should be in one of the following formats: *
      `projects/{project-number|project-id}/locations/{region}` *
      `folders/{folder-number}/locations/{region}` *
      `organizations/{organization-number}/locations/{region}`
  """

  parent = _messages.StringField(1, required=True)


class PrivilegedaccessmanagerProjectsLocationsEntitlementsCreateRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerProjectsLocationsEntitlementsCreateRequest
  object.

  Fields:
    entitlement: A Entitlement resource to be passed as the request body.
    entitlementId: Required. The ID to use for this entitlement. This becomes
      the last part of the resource name. This value should be 4-63 characters
      in length, and valid characters are "[a-z]", "[0-9]", and "-". The first
      character should be from [a-z]. This value should be unique among all
      other entitlements under the specified `parent`.
    parent: Required. Name of the parent resource for the entitlement.
      Possible formats: * `organizations/{organization-
      number}/locations/{region}` * `folders/{folder-
      number}/locations/{region}` * `projects/{project-id|project-
      number}/locations/{region}`
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      knows to ignore the request if it has already been completed. The server
      guarantees this for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, ignores the second request and returns the
      previous operation's response. This prevents clients from accidentally
      creating duplicate entitlements. The request ID must be a valid UUID
      with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  entitlement = _messages.MessageField('Entitlement', 1)
  entitlementId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class PrivilegedaccessmanagerProjectsLocationsEntitlementsDeleteRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerProjectsLocationsEntitlementsDeleteRequest
  object.

  Fields:
    force: Optional. If set to true, any child grant under this entitlement is
      also deleted. (Otherwise, the request only works if the entitlement has
      no child grant.)
    name: Required. Name of the resource.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      knows to ignore the request if it has already been completed. The server
      guarantees this for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, ignores the second request. The request ID must
      be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class PrivilegedaccessmanagerProjectsLocationsEntitlementsGetRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerProjectsLocationsEntitlementsGetRequest object.

  Fields:
    name: Required. Name of the resource.
  """

  name = _messages.StringField(1, required=True)


class PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsApproveRequest(_messages.Message):
  r"""A
  PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsApproveRequest
  object.

  Fields:
    approveGrantRequest: A ApproveGrantRequest resource to be passed as the
      request body.
    name: Required. Name of the grant resource which is being approved.
  """

  approveGrantRequest = _messages.MessageField('ApproveGrantRequest', 1)
  name = _messages.StringField(2, required=True)


class PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsCreateRequest(_messages.Message):
  r"""A
  PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsCreateRequest
  object.

  Fields:
    grant: A Grant resource to be passed as the request body.
    parent: Required. Name of the parent entitlement for which this grant is
      being requested.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      knows to ignore the request if it has already been completed. The server
      guarantees this for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate grants. The request ID must
      be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  grant = _messages.MessageField('Grant', 1)
  parent = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsDenyRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsDenyRequest
  object.

  Fields:
    denyGrantRequest: A DenyGrantRequest resource to be passed as the request
      body.
    name: Required. Name of the grant resource which is being denied.
  """

  denyGrantRequest = _messages.MessageField('DenyGrantRequest', 1)
  name = _messages.StringField(2, required=True)


class PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsGetRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsGetRequest
  object.

  Fields:
    name: Required. Name of the resource.
  """

  name = _messages.StringField(1, required=True)


class PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsListRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsListRequest
  object.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. The server may return fewer items
      than requested. If unspecified, the server picks an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent resource which owns the grants.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsRevokeRequest(_messages.Message):
  r"""A
  PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsRevokeRequest
  object.

  Fields:
    name: Required. Name of the grant resource which is being revoked.
    revokeGrantRequest: A RevokeGrantRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  revokeGrantRequest = _messages.MessageField('RevokeGrantRequest', 2)


class PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsSearchRequest(_messages.Message):
  r"""A
  PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsSearchRequest
  object.

  Enums:
    CallerRelationshipValueValuesEnum: Required. Only grants which the caller
      is related to by this relationship are returned in the response.

  Fields:
    callerRelationship: Required. Only grants which the caller is related to
      by this relationship are returned in the response.
    filter: Optional. Only grants matching this filter are returned in the
      response.
    pageSize: Optional. Requested page size. The server may return fewer items
      than requested. If unspecified, server picks an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent which owns the grant resources.
  """

  class CallerRelationshipValueValuesEnum(_messages.Enum):
    r"""Required. Only grants which the caller is related to by this
    relationship are returned in the response.

    Values:
      CALLER_RELATIONSHIP_TYPE_UNSPECIFIED: Unspecified caller relationship
        type.
      HAD_CREATED: The user created this grant by calling `CreateGrant`
        earlier.
      CAN_APPROVE: The user is an approver for the entitlement that this grant
        is parented under and can currently approve/deny it.
      HAD_APPROVED: The caller had successfully approved/denied this grant
        earlier.
    """
    CALLER_RELATIONSHIP_TYPE_UNSPECIFIED = 0
    HAD_CREATED = 1
    CAN_APPROVE = 2
    HAD_APPROVED = 3

  callerRelationship = _messages.EnumField('CallerRelationshipValueValuesEnum', 1)
  filter = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsWithdrawRequest(_messages.Message):
  r"""A
  PrivilegedaccessmanagerProjectsLocationsEntitlementsGrantsWithdrawRequest
  object.

  Fields:
    name: Required. Name of the grant resource which is being withdrawn.
    withdrawGrantRequest: A WithdrawGrantRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  withdrawGrantRequest = _messages.MessageField('WithdrawGrantRequest', 2)


class PrivilegedaccessmanagerProjectsLocationsEntitlementsListRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerProjectsLocationsEntitlementsListRequest
  object.

  Fields:
    filter: Optional. Filtering results.
    orderBy: Optional. Hint for how to order the results.
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, the server picks an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent which owns the entitlement resources.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class PrivilegedaccessmanagerProjectsLocationsEntitlementsPatchRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerProjectsLocationsEntitlementsPatchRequest
  object.

  Fields:
    entitlement: A Entitlement resource to be passed as the request body.
    name: Identifier. Name of the entitlement. Possible formats: *
      `organizations/{organization-
      number}/locations/{region}/entitlements/{entitlement-id}` *
      `folders/{folder-number}/locations/{region}/entitlements/{entitlement-
      id}` * `projects/{project-id|project-
      number}/locations/{region}/entitlements/{entitlement-id}`
    updateMask: Required. The list of fields to update. A field is overwritten
      if, and only if, it is in the mask. Any immutable fields set in the mask
      are ignored by the server. Repeated fields and map fields are only
      allowed in the last position of a `paths` string and overwrite the
      existing values. Hence an update to a repeated field or a map should
      contain the entire list of values. The fields specified in the
      update_mask are relative to the resource and not to the request. (e.g.
      `MaxRequestDuration`; *not* `entitlement.MaxRequestDuration`) A value of
      '*' for this field refers to full replacement of the resource.
  """

  entitlement = _messages.MessageField('Entitlement', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class PrivilegedaccessmanagerProjectsLocationsEntitlementsSearchRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerProjectsLocationsEntitlementsSearchRequest
  object.

  Enums:
    CallerAccessTypeValueValuesEnum: Required. Only entitlements where the
      calling user has this access are returned.

  Fields:
    callerAccessType: Required. Only entitlements where the calling user has
      this access are returned.
    filter: Optional. Only entitlements matching this filter are returned in
      the response.
    pageSize: Optional. Requested page size. The server may return fewer items
      than requested. If unspecified, the server picks an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. The parent which owns the entitlement resources.
  """

  class CallerAccessTypeValueValuesEnum(_messages.Enum):
    r"""Required. Only entitlements where the calling user has this access are
    returned.

    Values:
      CALLER_ACCESS_TYPE_UNSPECIFIED: Unspecified access type.
      GRANT_REQUESTER: The user has access to create grants using this
        entitlement.
      GRANT_APPROVER: The user has access to approve/deny grants created under
        this entitlement.
    """
    CALLER_ACCESS_TYPE_UNSPECIFIED = 0
    GRANT_REQUESTER = 1
    GRANT_APPROVER = 2

  callerAccessType = _messages.EnumField('CallerAccessTypeValueValuesEnum', 1)
  filter = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class PrivilegedaccessmanagerProjectsLocationsGetRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class PrivilegedaccessmanagerProjectsLocationsGetSettingsRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerProjectsLocationsGetSettingsRequest object.

  Fields:
    name: Required. The name of the settings resource to be fetched.
  """

  name = _messages.StringField(1, required=True)


class PrivilegedaccessmanagerProjectsLocationsListRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class PrivilegedaccessmanagerProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerProjectsLocationsOperationsDeleteRequest
  object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class PrivilegedaccessmanagerProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class PrivilegedaccessmanagerProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class PrivilegedaccessmanagerProjectsLocationsUpdateSettingsRequest(_messages.Message):
  r"""A PrivilegedaccessmanagerProjectsLocationsUpdateSettingsRequest object.

  Fields:
    name: Identifier. Name of the settings resource. Possible formats:
      projects/{project-id|project-number}/locations/{location}/settings
      folders/{folder-number}/locations/{location}/settings
      organizations/{organization-number}/locations/{location}/settings
    settings: A Settings resource to be passed as the request body.
    updateMask: Required. The list of fields to update. A field is overwritten
      if, and only if, it is in the mask. Any immutable fields set in the mask
      are ignored by the server. Repeated fields and map fields are only
      allowed in the last position of a `paths` string and overwrite the
      existing values. Hence an update to a repeated field or a map should
      contain the entire list of values. The fields specified in the
      update_mask are relative to the resource and not to the request. A value
      of '*' for this field refers to full replacement of the resource.
  """

  name = _messages.StringField(1, required=True)
  settings = _messages.MessageField('Settings', 2)
  updateMask = _messages.StringField(3)


class Requested(_messages.Message):
  r"""An event representing that a grant was requested.

  Fields:
    expireTime: Output only. The time at which this grant expires unless the
      approval workflow completes. If omitted, then the request never expires.
  """

  expireTime = _messages.StringField(1)


class RequestedPrivilegedAccess(_messages.Message):
  r"""Privileged access that is requested by a user via a grant.

  Fields:
    gcpIamAccess: Access to a Google Cloud resource through IAM.
  """

  gcpIamAccess = _messages.MessageField('RequestedPrivilegedAccessGcpIamAccess', 1)


class RequestedPrivilegedAccessGcpIamAccess(_messages.Message):
  r"""`GcpIamAccess` represents IAM based access control on a Google Cloud
  resource. Refer to https://cloud.google.com/iam/docs to understand more
  about IAM.

  Fields:
    resource: Required. Name of the resource.
    resourceType: Required. The type of this resource.
    roleBindings: Optional. Role bindings that are requested as part of the
      grant.
  """

  resource = _messages.StringField(1)
  resourceType = _messages.StringField(2)
  roleBindings = _messages.MessageField('RequestedPrivilegedAccessGcpIamAccessRoleBinding', 3, repeated=True)


class RequestedPrivilegedAccessGcpIamAccessAccessRestrictions(_messages.Message):
  r"""AccessRestrictions represents a set of resources to further restrict the
  access to. This is used to get finer grained access as part of a grant. All
  restrictions are OR-ed with each other.

  Fields:
    resourceNamePrefixes: Optional. The resource name prefixes to restrict the
      access to. Follow https://cloud.google.com/iam/docs/conditions-resource-
      attributes#resource-name format.
    resourceNames: Optional. The resource names to restrict the access to.
      Follow https://cloud.google.com/iam/docs/conditions-resource-
      attributes#resource-name format.
  """

  resourceNamePrefixes = _messages.StringField(1, repeated=True)
  resourceNames = _messages.StringField(2, repeated=True)


class RequestedPrivilegedAccessGcpIamAccessRoleBinding(_messages.Message):
  r"""IAM role bindings that are requested as part of the grant.

  Fields:
    accessRestrictions: Optional. The access restrictions to be applied to the
      role binding. This further restricts the access of this role binding to
      specific resources.
    entitlementConditionExpression: Output only. The IAM condition expression
      associated with the role at the time of grant request.
    entitlementRoleBindingId: Required. The role binding id of the role to be
      granted from the entitlement.
    role: Output only. The IAM role requested as part of the grant.
  """

  accessRestrictions = _messages.MessageField('RequestedPrivilegedAccessGcpIamAccessAccessRestrictions', 1)
  entitlementConditionExpression = _messages.StringField(2)
  entitlementRoleBindingId = _messages.StringField(3)
  role = _messages.StringField(4)


class RequesterJustificationConfig(_messages.Message):
  r"""Defines how a requester must provide a justification when requesting
  access.

  Fields:
    notMandatory: This option means the requester isn't required to provide a
      justification.
    unstructured: This option means the requester must provide a string as
      justification. If this is selected, the server allows the requester to
      provide a justification but doesn't validate it.
  """

  notMandatory = _messages.MessageField('NotMandatory', 1)
  unstructured = _messages.MessageField('Unstructured', 2)


class RevokeGrantRequest(_messages.Message):
  r"""Request message for `RevokeGrant` method.

  Fields:
    reason: Optional. The reason for revoking this grant.
  """

  reason = _messages.StringField(1)


class Revoked(_messages.Message):
  r"""An event representing that the grant was revoked.

  Fields:
    actor: Output only. Username of the user who revoked the grant.
    reason: Output only. The reason provided by the user for revoking the
      grant.
  """

  actor = _messages.StringField(1)
  reason = _messages.StringField(2)


class RoleBinding(_messages.Message):
  r"""IAM role bindings that are created after a successful grant.

  Fields:
    conditionExpression: Optional. The expression field of the IAM condition
      to be associated with the role. If specified, a user with an active
      grant for this entitlement is able to access the resource only if this
      condition evaluates to true for their request. This field uses the same
      CEL format as IAM and supports all attributes that IAM supports, except
      tags. https://cloud.google.com/iam/docs/conditions-overview#attributes.
    id: Output only. The ID corresponding to this role binding in the policy
      binding. This will be unique within an entitlement across time. Gets re-
      generated each time the entitlement is updated.
    role: Required. IAM role to be granted.
      https://cloud.google.com/iam/docs/roles-overview.
  """

  conditionExpression = _messages.StringField(1)
  id = _messages.StringField(2)
  role = _messages.StringField(3)


class Scheduled(_messages.Message):
  r"""An event representing that the grant has been scheduled to be activated
  later.

  Fields:
    scheduledActivationTime: Output only. The time at which the access is
      granted.
  """

  scheduledActivationTime = _messages.StringField(1)


class SearchEntitlementsResponse(_messages.Message):
  r"""Response message for `SearchEntitlements` method.

  Fields:
    entitlements: The list of entitlements.
    nextPageToken: A token identifying a page of results the server should
      return.
  """

  entitlements = _messages.MessageField('Entitlement', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class SearchGrantsResponse(_messages.Message):
  r"""Response message for `SearchGrants` method.

  Fields:
    grants: The list of grants.
    nextPageToken: A token identifying a page of results the server should
      return.
  """

  grants = _messages.MessageField('Grant', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class Settings(_messages.Message):
  r"""`Settings` resource defines the properties, applied directly to the
  resource or inherited through the hierarchy, to enable consistent, federated
  use of PAM. The behavior is as follows: 1. If explicitly set to empty at the
  node level, PAM's default settings are applied for that node. 2. If not set
  at the node level, settings are inherited from the closest ancestor with a
  non-empty value. If none of the ancestors has the field set, PAM's default
  settings are applied. 3. If explicitly set to a non-empty value at the node
  level, the specified settings are applied for that node.

  Fields:
    createTime: Output only. Create timestamp.
    emailNotificationSettings: Optional. `EmailNotificationSettings` defines
      node-wide email notification preferences for various PAM events.
    etag: Fingerprint for optimistic concurrency returned in the response of
      `GetSettings`. Must be provided in the requests to `UpdateSettings`. If
      the value provided does not match the value known to the server, ABORTED
      will be thrown, and the client should retry the read-modify-write cycle.
    name: Identifier. Name of the settings resource. Possible formats:
      projects/{project-id|project-number}/locations/{location}/settings
      folders/{folder-number}/locations/{location}/settings
      organizations/{organization-number}/locations/{location}/settings
    serviceAccountApproverSettings: Optional. This controls the node-level
      settings for allowing service accounts as approvers.
    updateTime: Output only. Update timestamp.
  """

  createTime = _messages.StringField(1)
  emailNotificationSettings = _messages.MessageField('SettingsEmailNotificationSettings', 2)
  etag = _messages.StringField(3)
  name = _messages.StringField(4)
  serviceAccountApproverSettings = _messages.MessageField('SettingsServiceAccountApproverSettings', 5)
  updateTime = _messages.StringField(6)


class SettingsEmailNotificationSettings(_messages.Message):
  r"""`EmailNotificationSettings` defines the node-wide email notification
  settings.

  Fields:
    customNotificationBehavior: Granular settings of notifications.
    disableAllNotifications: Disable all notifications.
  """

  customNotificationBehavior = _messages.MessageField('SettingsEmailNotificationSettingsCustomNotificationBehavior', 1)
  disableAllNotifications = _messages.MessageField('SettingsEmailNotificationSettingsDisableAllNotifications', 2)


class SettingsEmailNotificationSettingsCustomNotificationBehavior(_messages.Message):
  r"""`CustomNotificationBehavior` provides granular control over email
  notification delivery. Allows admins to selectively enable/disable
  notifications for specific events and specific personas.

  Fields:
    adminNotifications: Optional. Admin email notifications.
    approverNotifications: Optional. Approver email notifications.
    requesterNotifications: Optional. Requester email notifications.
  """

  adminNotifications = _messages.MessageField('SettingsEmailNotificationSettingsCustomNotificationBehaviorAdminNotifications', 1)
  approverNotifications = _messages.MessageField('SettingsEmailNotificationSettingsCustomNotificationBehaviorApproverNotifications', 2)
  requesterNotifications = _messages.MessageField('SettingsEmailNotificationSettingsCustomNotificationBehaviorRequesterNotifications', 3)


class SettingsEmailNotificationSettingsCustomNotificationBehaviorAdminNotifications(_messages.Message):
  r"""Email notifications specific to Admins.

  Enums:
    GrantActivatedValueValuesEnum: Optional. Notification mode for grant
      activated.
    GrantActivationFailedValueValuesEnum: Optional. Notification mode for
      grant activation failed.
    GrantEndedValueValuesEnum: Optional. Notification mode for grant ended.
    GrantExternallyModifiedValueValuesEnum: Optional. Notification mode for
      grant externally modified.

  Fields:
    grantActivated: Optional. Notification mode for grant activated.
    grantActivationFailed: Optional. Notification mode for grant activation
      failed.
    grantEnded: Optional. Notification mode for grant ended.
    grantExternallyModified: Optional. Notification mode for grant externally
      modified.
  """

  class GrantActivatedValueValuesEnum(_messages.Enum):
    r"""Optional. Notification mode for grant activated.

    Values:
      NOTIFICATION_MODE_UNSPECIFIED: Default notification behavior following
        PAM's standard settings.
      ENABLED: Notifications are enabled.
      DISABLED: Notifications are disabled.
    """
    NOTIFICATION_MODE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  class GrantActivationFailedValueValuesEnum(_messages.Enum):
    r"""Optional. Notification mode for grant activation failed.

    Values:
      NOTIFICATION_MODE_UNSPECIFIED: Default notification behavior following
        PAM's standard settings.
      ENABLED: Notifications are enabled.
      DISABLED: Notifications are disabled.
    """
    NOTIFICATION_MODE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  class GrantEndedValueValuesEnum(_messages.Enum):
    r"""Optional. Notification mode for grant ended.

    Values:
      NOTIFICATION_MODE_UNSPECIFIED: Default notification behavior following
        PAM's standard settings.
      ENABLED: Notifications are enabled.
      DISABLED: Notifications are disabled.
    """
    NOTIFICATION_MODE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  class GrantExternallyModifiedValueValuesEnum(_messages.Enum):
    r"""Optional. Notification mode for grant externally modified.

    Values:
      NOTIFICATION_MODE_UNSPECIFIED: Default notification behavior following
        PAM's standard settings.
      ENABLED: Notifications are enabled.
      DISABLED: Notifications are disabled.
    """
    NOTIFICATION_MODE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  grantActivated = _messages.EnumField('GrantActivatedValueValuesEnum', 1)
  grantActivationFailed = _messages.EnumField('GrantActivationFailedValueValuesEnum', 2)
  grantEnded = _messages.EnumField('GrantEndedValueValuesEnum', 3)
  grantExternallyModified = _messages.EnumField('GrantExternallyModifiedValueValuesEnum', 4)


class SettingsEmailNotificationSettingsCustomNotificationBehaviorApproverNotifications(_messages.Message):
  r"""Email notifications specific to Approvers.

  Enums:
    PendingApprovalValueValuesEnum: Optional. Notification mode for pending
      approval.

  Fields:
    pendingApproval: Optional. Notification mode for pending approval.
  """

  class PendingApprovalValueValuesEnum(_messages.Enum):
    r"""Optional. Notification mode for pending approval.

    Values:
      NOTIFICATION_MODE_UNSPECIFIED: Default notification behavior following
        PAM's standard settings.
      ENABLED: Notifications are enabled.
      DISABLED: Notifications are disabled.
    """
    NOTIFICATION_MODE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  pendingApproval = _messages.EnumField('PendingApprovalValueValuesEnum', 1)


class SettingsEmailNotificationSettingsCustomNotificationBehaviorRequesterNotifications(_messages.Message):
  r"""Email notifications specific to Requesters.

  Enums:
    EntitlementAssignedValueValuesEnum: Optional. Notification mode for
      entitlement assigned.
    GrantActivatedValueValuesEnum: Optional. Notification mode for grant
      activated.
    GrantActivationFailedValueValuesEnum: Optional. Notification mode for
      grant activation failed.
    GrantDeniedValueValuesEnum: Optional. Notification mode for grant denied.
    GrantEndedValueValuesEnum: Optional. Notification mode for grant ended.
    GrantExpiredValueValuesEnum: Optional. Notification mode for grant request
      expired.
    GrantExternallyModifiedValueValuesEnum: Optional. Notification mode for
      grant externally modified.
    GrantRevokedValueValuesEnum: Optional. Notification mode for grant
      revoked.

  Fields:
    entitlementAssigned: Optional. Notification mode for entitlement assigned.
    grantActivated: Optional. Notification mode for grant activated.
    grantActivationFailed: Optional. Notification mode for grant activation
      failed.
    grantDenied: Optional. Notification mode for grant denied.
    grantEnded: Optional. Notification mode for grant ended.
    grantExpired: Optional. Notification mode for grant request expired.
    grantExternallyModified: Optional. Notification mode for grant externally
      modified.
    grantRevoked: Optional. Notification mode for grant revoked.
  """

  class EntitlementAssignedValueValuesEnum(_messages.Enum):
    r"""Optional. Notification mode for entitlement assigned.

    Values:
      NOTIFICATION_MODE_UNSPECIFIED: Default notification behavior following
        PAM's standard settings.
      ENABLED: Notifications are enabled.
      DISABLED: Notifications are disabled.
    """
    NOTIFICATION_MODE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  class GrantActivatedValueValuesEnum(_messages.Enum):
    r"""Optional. Notification mode for grant activated.

    Values:
      NOTIFICATION_MODE_UNSPECIFIED: Default notification behavior following
        PAM's standard settings.
      ENABLED: Notifications are enabled.
      DISABLED: Notifications are disabled.
    """
    NOTIFICATION_MODE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  class GrantActivationFailedValueValuesEnum(_messages.Enum):
    r"""Optional. Notification mode for grant activation failed.

    Values:
      NOTIFICATION_MODE_UNSPECIFIED: Default notification behavior following
        PAM's standard settings.
      ENABLED: Notifications are enabled.
      DISABLED: Notifications are disabled.
    """
    NOTIFICATION_MODE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  class GrantDeniedValueValuesEnum(_messages.Enum):
    r"""Optional. Notification mode for grant denied.

    Values:
      NOTIFICATION_MODE_UNSPECIFIED: Default notification behavior following
        PAM's standard settings.
      ENABLED: Notifications are enabled.
      DISABLED: Notifications are disabled.
    """
    NOTIFICATION_MODE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  class GrantEndedValueValuesEnum(_messages.Enum):
    r"""Optional. Notification mode for grant ended.

    Values:
      NOTIFICATION_MODE_UNSPECIFIED: Default notification behavior following
        PAM's standard settings.
      ENABLED: Notifications are enabled.
      DISABLED: Notifications are disabled.
    """
    NOTIFICATION_MODE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  class GrantExpiredValueValuesEnum(_messages.Enum):
    r"""Optional. Notification mode for grant request expired.

    Values:
      NOTIFICATION_MODE_UNSPECIFIED: Default notification behavior following
        PAM's standard settings.
      ENABLED: Notifications are enabled.
      DISABLED: Notifications are disabled.
    """
    NOTIFICATION_MODE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  class GrantExternallyModifiedValueValuesEnum(_messages.Enum):
    r"""Optional. Notification mode for grant externally modified.

    Values:
      NOTIFICATION_MODE_UNSPECIFIED: Default notification behavior following
        PAM's standard settings.
      ENABLED: Notifications are enabled.
      DISABLED: Notifications are disabled.
    """
    NOTIFICATION_MODE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  class GrantRevokedValueValuesEnum(_messages.Enum):
    r"""Optional. Notification mode for grant revoked.

    Values:
      NOTIFICATION_MODE_UNSPECIFIED: Default notification behavior following
        PAM's standard settings.
      ENABLED: Notifications are enabled.
      DISABLED: Notifications are disabled.
    """
    NOTIFICATION_MODE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  entitlementAssigned = _messages.EnumField('EntitlementAssignedValueValuesEnum', 1)
  grantActivated = _messages.EnumField('GrantActivatedValueValuesEnum', 2)
  grantActivationFailed = _messages.EnumField('GrantActivationFailedValueValuesEnum', 3)
  grantDenied = _messages.EnumField('GrantDeniedValueValuesEnum', 4)
  grantEnded = _messages.EnumField('GrantEndedValueValuesEnum', 5)
  grantExpired = _messages.EnumField('GrantExpiredValueValuesEnum', 6)
  grantExternallyModified = _messages.EnumField('GrantExternallyModifiedValueValuesEnum', 7)
  grantRevoked = _messages.EnumField('GrantRevokedValueValuesEnum', 8)


class SettingsEmailNotificationSettingsDisableAllNotifications(_messages.Message):
  r"""This option indicates that all email notifications are disabled."""


class SettingsServiceAccountApproverSettings(_messages.Message):
  r"""This controls whether service accounts are allowed to approve grants or
  can be designated as approvers within PAM entitlements.

  Fields:
    enabled: Optional. Indicates whether service account is allowed to grant
      approvals.
  """

  enabled = _messages.BooleanField(1)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class Step(_messages.Message):
  r"""Step represents a logical step in a manual approval workflow.

  Fields:
    approvalsNeeded: Required. How many users from the above list need to
      approve. If there aren't enough distinct users in the list, then the
      workflow indefinitely blocks. Should always be greater than 0. 1 is the
      only supported value.
    approverEmailRecipients: Optional. Additional email addresses to be
      notified when a grant is pending approval.
    approvers: Optional. The potential set of approvers in this step. This
      list must contain at most one entry.
    id: Output only. Step ID used to identify the step in the workflow.
  """

  approvalsNeeded = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  approverEmailRecipients = _messages.StringField(2, repeated=True)
  approvers = _messages.MessageField('AccessControlEntry', 3, repeated=True)
  id = _messages.StringField(4)


class Timeline(_messages.Message):
  r"""Timeline of a grant describing what happened to it and when.

  Fields:
    events: Output only. The events that have occurred on this grant. This
      list contains entries in the same order as they occurred. The first
      entry is always be of type `Requested` and there is always at least one
      entry in this array.
  """

  events = _messages.MessageField('Event', 1, repeated=True)


class Unstructured(_messages.Message):
  r"""The requester has to provide a justification in the form of a string."""


class WithdrawGrantRequest(_messages.Message):
  r"""Request message for `WithdrawGrant` method."""


class Withdrawn(_messages.Message):
  r"""An event representing that the grant was withdrawn."""


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
