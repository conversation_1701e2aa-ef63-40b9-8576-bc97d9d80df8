"""Generated message classes for ids version v1.

Cloud IDS (Cloud Intrusion Detection System) detects malware, spyware,
command-and-control attacks, and other network-based threats. Its security
efficacy is industry leading, built with Palo Alto Networks technologies. When
you use this product, your organization name and consumption levels will be
shared with Palo Alto Networks.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'ids'


class BillingView(_messages.Message):
  r"""Message for reporting billing requests through Eventstream.

  Fields:
    reportRequests: Billing requests to be reported for
      cloud.eventstream.v2.ResourceEvent
  """

  reportRequests = _messages.MessageField('GoogleApiServicecontrolV1ReportRequest', 1, repeated=True)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Endpoint(_messages.Message):
  r"""Endpoint describes a single IDS endpoint. It defines a forwarding rule
  to which packets can be sent for IDS inspection.

  Enums:
    SeverityValueValuesEnum: Required. Lowest threat severity that this
      endpoint will alert on.
    StateValueValuesEnum: Output only. Current state of the endpoint.

  Messages:
    LabelsValue: The labels of the endpoint.

  Fields:
    createTime: Output only. The create time timestamp.
    description: User-provided description of the endpoint
    endpointForwardingRule: Output only. The fully qualified URL of the
      endpoint's ILB Forwarding Rule.
    endpointIp: Output only. The IP address of the IDS Endpoint's ILB.
    labels: The labels of the endpoint.
    name: Output only. The name of the endpoint.
    network: Required. The fully qualified URL of the network to which the IDS
      Endpoint is attached.
    satisfiesPzi: Output only. [Output Only] Reserved for future use.
    satisfiesPzs: Output only. [Output Only] Reserved for future use.
    severity: Required. Lowest threat severity that this endpoint will alert
      on.
    state: Output only. Current state of the endpoint.
    threatExceptions: List of threat IDs to be excepted from generating
      alerts.
    trafficLogs: Whether the endpoint should report traffic logs in addition
      to threat logs.
    updateTime: Output only. The update time timestamp.
  """

  class SeverityValueValuesEnum(_messages.Enum):
    r"""Required. Lowest threat severity that this endpoint will alert on.

    Values:
      SEVERITY_UNSPECIFIED: Not set.
      INFORMATIONAL: Informational alerts.
      LOW: Low severity alerts.
      MEDIUM: Medium severity alerts.
      HIGH: High severity alerts.
      CRITICAL: Critical severity alerts.
    """
    SEVERITY_UNSPECIFIED = 0
    INFORMATIONAL = 1
    LOW = 2
    MEDIUM = 3
    HIGH = 4
    CRITICAL = 5

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the endpoint.

    Values:
      STATE_UNSPECIFIED: Not set.
      CREATING: Being created.
      READY: Active and ready for traffic.
      DELETING: Being deleted.
      UPDATING: Being updated.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    DELETING = 3
    UPDATING = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels of the endpoint.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  endpointForwardingRule = _messages.StringField(3)
  endpointIp = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  network = _messages.StringField(7)
  satisfiesPzi = _messages.BooleanField(8)
  satisfiesPzs = _messages.BooleanField(9)
  severity = _messages.EnumField('SeverityValueValuesEnum', 10)
  state = _messages.EnumField('StateValueValuesEnum', 11)
  threatExceptions = _messages.StringField(12, repeated=True)
  trafficLogs = _messages.BooleanField(13)
  updateTime = _messages.StringField(14)


class Exemplar(_messages.Message):
  r"""Exemplars are example points that may be used to annotate aggregated
  distribution values. They are metadata that gives information about a
  particular value added to a Distribution bucket, such as a trace ID that was
  active when a value was added. They may contain further information, such as
  a example values and timestamps, origin, etc.

  Messages:
    AttachmentsValueListEntry: A AttachmentsValueListEntry object.

  Fields:
    attachments: Contextual information about the example value. Examples are:
      Trace: type.googleapis.com/google.monitoring.v3.SpanContext Literal
      string: type.googleapis.com/google.protobuf.StringValue Labels dropped
      during aggregation:
      type.googleapis.com/google.monitoring.v3.DroppedLabels There may be only
      a single attachment of any given message type in a single exemplar, and
      this is enforced by the system.
    timestamp: The observation (sampling) time of the above value.
    value: Value of the exemplar point. This value determines to which bucket
      the exemplar belongs.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AttachmentsValueListEntry(_messages.Message):
    r"""A AttachmentsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a
        AttachmentsValueListEntry object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AttachmentsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attachments = _messages.MessageField('AttachmentsValueListEntry', 1, repeated=True)
  timestamp = _messages.StringField(2)
  value = _messages.FloatField(3)


class GoogleApiServicecontrolV1AttributeValue(_messages.Message):
  r"""The allowed types for [VALUE] in a `[KEY]:[VALUE]` attribute.

  Fields:
    boolValue: A Boolean value represented by `true` or `false`.
    intValue: A 64-bit signed integer.
    stringValue: A string up to 256 bytes long.
  """

  boolValue = _messages.BooleanField(1)
  intValue = _messages.IntegerField(2)
  stringValue = _messages.MessageField('GoogleApiServicecontrolV1TruncatableString', 3)


class GoogleApiServicecontrolV1Attributes(_messages.Message):
  r"""A set of attributes, each in the format `[KEY]:[VALUE]`.

  Messages:
    AttributeMapValue: The set of attributes. Each attribute's key can be up
      to 128 bytes long. The value can be a string up to 256 bytes, a signed
      64-bit integer, or the Boolean values `true` and `false`. For example:
      "/instance_id": "my-instance" "/http/user_agent": ""
      "/http/request_bytes": 300 "example.com/myattribute": true

  Fields:
    attributeMap: The set of attributes. Each attribute's key can be up to 128
      bytes long. The value can be a string up to 256 bytes, a signed 64-bit
      integer, or the Boolean values `true` and `false`. For example:
      "/instance_id": "my-instance" "/http/user_agent": ""
      "/http/request_bytes": 300 "example.com/myattribute": true
    droppedAttributesCount: The number of attributes that were discarded.
      Attributes can be discarded because their keys are too long or because
      there are too many attributes. If this value is 0 then all attributes
      are valid.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AttributeMapValue(_messages.Message):
    r"""The set of attributes. Each attribute's key can be up to 128 bytes
    long. The value can be a string up to 256 bytes, a signed 64-bit integer,
    or the Boolean values `true` and `false`. For example: "/instance_id":
    "my-instance" "/http/user_agent": "" "/http/request_bytes": 300
    "example.com/myattribute": true

    Messages:
      AdditionalProperty: An additional property for a AttributeMapValue
        object.

    Fields:
      additionalProperties: Additional properties of type AttributeMapValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AttributeMapValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleApiServicecontrolV1AttributeValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleApiServicecontrolV1AttributeValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attributeMap = _messages.MessageField('AttributeMapValue', 1)
  droppedAttributesCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleApiServicecontrolV1Distribution(_messages.Message):
  r"""Distribution represents a frequency distribution of double-valued sample
  points. It contains the size of the population of sample points plus
  additional optional information: * the arithmetic mean of the samples * the
  minimum and maximum of the samples * the sum-squared-deviation of the
  samples, used to compute variance * a histogram of the values of the sample
  points

  Fields:
    bucketCounts: The number of samples in each histogram bucket.
      `bucket_counts` are optional. If present, they must sum to the `count`
      value. The buckets are defined below in `bucket_option`. There are N
      buckets. `bucket_counts[0]` is the number of samples in the underflow
      bucket. `bucket_counts[1]` to `bucket_counts[N-1]` are the numbers of
      samples in each of the finite buckets. And `bucket_counts[N]` is the
      number of samples in the overflow bucket. See the comments of
      `bucket_option` below for more details. Any suffix of trailing zeros may
      be omitted.
    count: The total number of samples in the distribution. Must be >= 0.
    exemplars: Example points. Must be in increasing order of `value` field.
    explicitBuckets: Buckets with arbitrary user-provided width.
    exponentialBuckets: Buckets with exponentially growing width.
    linearBuckets: Buckets with constant width.
    maximum: The maximum of the population of values. Ignored if `count` is
      zero.
    mean: The arithmetic mean of the samples in the distribution. If `count`
      is zero then this field must be zero.
    minimum: The minimum of the population of values. Ignored if `count` is
      zero.
    sumOfSquaredDeviation: The sum of squared deviations from the mean:
      Sum[i=1..count]((x_i - mean)^2) where each x_i is a sample values. If
      `count` is zero then this field must be zero, otherwise validation of
      the request fails.
  """

  bucketCounts = _messages.IntegerField(1, repeated=True)
  count = _messages.IntegerField(2)
  exemplars = _messages.MessageField('Exemplar', 3, repeated=True)
  explicitBuckets = _messages.MessageField('GoogleApiServicecontrolV1ExplicitBuckets', 4)
  exponentialBuckets = _messages.MessageField('GoogleApiServicecontrolV1ExponentialBuckets', 5)
  linearBuckets = _messages.MessageField('GoogleApiServicecontrolV1LinearBuckets', 6)
  maximum = _messages.FloatField(7)
  mean = _messages.FloatField(8)
  minimum = _messages.FloatField(9)
  sumOfSquaredDeviation = _messages.FloatField(10)


class GoogleApiServicecontrolV1ExplicitBuckets(_messages.Message):
  r"""Describing buckets with arbitrary user-provided width.

  Fields:
    bounds: 'bound' is a list of strictly increasing boundaries between
      buckets. Note that a list of length N-1 defines N buckets because of
      fenceposting. See comments on `bucket_options` for details. The i'th
      finite bucket covers the interval [bound[i-1], bound[i]) where i ranges
      from 1 to bound_size() - 1. Note that there are no finite buckets at all
      if 'bound' only contains a single element; in that special case the
      single bound defines the boundary between the underflow and overflow
      buckets. bucket number lower bound upper bound i == 0 (underflow) -inf
      bound[i] 0 < i < bound_size() bound[i-1] bound[i] i == bound_size()
      (overflow) bound[i-1] +inf
  """

  bounds = _messages.FloatField(1, repeated=True)


class GoogleApiServicecontrolV1ExponentialBuckets(_messages.Message):
  r"""Describing buckets with exponentially growing width.

  Fields:
    growthFactor: The i'th exponential bucket covers the interval [scale *
      growth_factor^(i-1), scale * growth_factor^i) where i ranges from 1 to
      num_finite_buckets inclusive. Must be larger than 1.0.
    numFiniteBuckets: The number of finite buckets. With the underflow and
      overflow buckets, the total number of buckets is `num_finite_buckets` +
      2. See comments on `bucket_options` for details.
    scale: The i'th exponential bucket covers the interval [scale *
      growth_factor^(i-1), scale * growth_factor^i) where i ranges from 1 to
      num_finite_buckets inclusive. Must be > 0.
  """

  growthFactor = _messages.FloatField(1)
  numFiniteBuckets = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  scale = _messages.FloatField(3)


class GoogleApiServicecontrolV1HttpRequest(_messages.Message):
  r"""A common proto for logging HTTP requests. Only contains semantics
  defined by the HTTP specification. Product-specific logging information MUST
  be defined in a separate message.

  Fields:
    cacheFillBytes: The number of HTTP response bytes inserted into cache. Set
      only when a cache fill was attempted.
    cacheHit: Whether or not an entity was served from cache (with or without
      validation).
    cacheLookup: Whether or not a cache lookup was attempted.
    cacheValidatedWithOriginServer: Whether or not the response was validated
      with the origin server before being served from cache. This field is
      only meaningful if `cache_hit` is True.
    latency: The request processing latency on the server, from the time the
      request was received until the response was sent.
    protocol: Protocol used for the request. Examples: "HTTP/1.1", "HTTP/2",
      "websocket"
    referer: The referer URL of the request, as defined in [HTTP/1.1 Header
      Field
      Definitions](https://www.w3.org/Protocols/rfc2616/rfc2616-sec14.html).
    remoteIp: The IP address (IPv4 or IPv6) of the client that issued the HTTP
      request. Examples: `"***********"`, `"FE80::0202:B3FF:FE1E:8329"`.
    requestMethod: The request method. Examples: `"GET"`, `"HEAD"`, `"PUT"`,
      `"POST"`.
    requestSize: The size of the HTTP request message in bytes, including the
      request headers and the request body.
    requestUrl: The scheme (http, https), the host name, the path, and the
      query portion of the URL that was requested. Example:
      `"http://example.com/some/info?color=red"`.
    responseSize: The size of the HTTP response message sent back to the
      client, in bytes, including the response headers and the response body.
    serverIp: The IP address (IPv4 or IPv6) of the origin server that the
      request was sent to.
    status: The response code indicating the status of the response. Examples:
      200, 404.
    userAgent: The user agent sent by the client. Example: `"Mozilla/4.0
      (compatible; MSIE 6.0; Windows 98; Q312461; .NET CLR 1.0.3705)"`.
  """

  cacheFillBytes = _messages.IntegerField(1)
  cacheHit = _messages.BooleanField(2)
  cacheLookup = _messages.BooleanField(3)
  cacheValidatedWithOriginServer = _messages.BooleanField(4)
  latency = _messages.StringField(5)
  protocol = _messages.StringField(6)
  referer = _messages.StringField(7)
  remoteIp = _messages.StringField(8)
  requestMethod = _messages.StringField(9)
  requestSize = _messages.IntegerField(10)
  requestUrl = _messages.StringField(11)
  responseSize = _messages.IntegerField(12)
  serverIp = _messages.StringField(13)
  status = _messages.IntegerField(14, variant=_messages.Variant.INT32)
  userAgent = _messages.StringField(15)


class GoogleApiServicecontrolV1LinearBuckets(_messages.Message):
  r"""Describing buckets with constant width.

  Fields:
    numFiniteBuckets: The number of finite buckets. With the underflow and
      overflow buckets, the total number of buckets is `num_finite_buckets` +
      2. See comments on `bucket_options` for details.
    offset: The i'th linear bucket covers the interval [offset + (i-1) *
      width, offset + i * width) where i ranges from 1 to num_finite_buckets,
      inclusive.
    width: The i'th linear bucket covers the interval [offset + (i-1) * width,
      offset + i * width) where i ranges from 1 to num_finite_buckets,
      inclusive. Must be strictly positive.
  """

  numFiniteBuckets = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  offset = _messages.FloatField(2)
  width = _messages.FloatField(3)


class GoogleApiServicecontrolV1LogEntry(_messages.Message):
  r"""An individual log entry.

  Enums:
    SeverityValueValuesEnum: The severity of the log entry. The default value
      is `LogSeverity.DEFAULT`.

  Messages:
    LabelsValue: A set of user-defined (key, value) data that provides
      additional information about the log entry.
    ProtoPayloadValue: The log entry payload, represented as a protocol buffer
      that is expressed as a JSON object. The only accepted type currently is
      AuditLog.
    StructPayloadValue: The log entry payload, represented as a structure that
      is expressed as a JSON object.

  Fields:
    httpRequest: Optional. Information about the HTTP request associated with
      this log entry, if applicable.
    insertId: A unique ID for the log entry used for deduplication. If
      omitted, the implementation will generate one based on operation_id.
    labels: A set of user-defined (key, value) data that provides additional
      information about the log entry.
    name: Required. The log to which this log entry belongs. Examples:
      `"syslog"`, `"book_log"`.
    operation: Optional. Information about an operation associated with the
      log entry, if applicable.
    protoPayload: The log entry payload, represented as a protocol buffer that
      is expressed as a JSON object. The only accepted type currently is
      AuditLog.
    severity: The severity of the log entry. The default value is
      `LogSeverity.DEFAULT`.
    sourceLocation: Optional. Source code location information associated with
      the log entry, if any.
    structPayload: The log entry payload, represented as a structure that is
      expressed as a JSON object.
    textPayload: The log entry payload, represented as a Unicode string
      (UTF-8).
    timestamp: The time the event described by the log entry occurred. If
      omitted, defaults to operation start time.
    trace: Optional. Resource name of the trace associated with the log entry,
      if any. If this field contains a relative resource name, you can assume
      the name is relative to `//tracing.googleapis.com`. Example:
      `projects/my-projectid/traces/06796866738c859f2f19b7cfb3214824`
  """

  class SeverityValueValuesEnum(_messages.Enum):
    r"""The severity of the log entry. The default value is
    `LogSeverity.DEFAULT`.

    Values:
      DEFAULT: (0) The log entry has no assigned severity level.
      DEBUG: (100) Debug or trace information.
      INFO: (200) Routine information, such as ongoing status or performance.
      NOTICE: (300) Normal but significant events, such as start up, shut
        down, or a configuration change.
      WARNING: (400) Warning events might cause problems.
      ERROR: (500) Error events are likely to cause problems.
      CRITICAL: (600) Critical events cause more severe problems or outages.
      ALERT: (700) A person must take an action immediately.
      EMERGENCY: (800) One or more systems are unusable.
    """
    DEFAULT = 0
    DEBUG = 1
    INFO = 2
    NOTICE = 3
    WARNING = 4
    ERROR = 5
    CRITICAL = 6
    ALERT = 7
    EMERGENCY = 8

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""A set of user-defined (key, value) data that provides additional
    information about the log entry.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ProtoPayloadValue(_messages.Message):
    r"""The log entry payload, represented as a protocol buffer that is
    expressed as a JSON object. The only accepted type currently is AuditLog.

    Messages:
      AdditionalProperty: An additional property for a ProtoPayloadValue
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ProtoPayloadValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class StructPayloadValue(_messages.Message):
    r"""The log entry payload, represented as a structure that is expressed as
    a JSON object.

    Messages:
      AdditionalProperty: An additional property for a StructPayloadValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a StructPayloadValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  httpRequest = _messages.MessageField('GoogleApiServicecontrolV1HttpRequest', 1)
  insertId = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  operation = _messages.MessageField('GoogleApiServicecontrolV1LogEntryOperation', 5)
  protoPayload = _messages.MessageField('ProtoPayloadValue', 6)
  severity = _messages.EnumField('SeverityValueValuesEnum', 7)
  sourceLocation = _messages.MessageField('GoogleApiServicecontrolV1LogEntrySourceLocation', 8)
  structPayload = _messages.MessageField('StructPayloadValue', 9)
  textPayload = _messages.StringField(10)
  timestamp = _messages.StringField(11)
  trace = _messages.StringField(12)


class GoogleApiServicecontrolV1LogEntryOperation(_messages.Message):
  r"""Additional information about a potentially long-running operation with
  which a log entry is associated.

  Fields:
    first: Optional. Set this to True if this is the first log entry in the
      operation.
    id: Optional. An arbitrary operation identifier. Log entries with the same
      identifier are assumed to be part of the same operation.
    last: Optional. Set this to True if this is the last log entry in the
      operation.
    producer: Optional. An arbitrary producer identifier. The combination of
      `id` and `producer` must be globally unique. Examples for `producer`:
      `"MyDivision.MyBigCompany.com"`, `"github.com/MyProject/MyApplication"`.
  """

  first = _messages.BooleanField(1)
  id = _messages.StringField(2)
  last = _messages.BooleanField(3)
  producer = _messages.StringField(4)


class GoogleApiServicecontrolV1LogEntrySourceLocation(_messages.Message):
  r"""Additional information about the source code location that produced the
  log entry.

  Fields:
    file: Optional. Source file name. Depending on the runtime environment,
      this might be a simple name or a fully-qualified name.
    function: Optional. Human-readable name of the function or method being
      invoked, with optional context such as the class or package name. This
      information may be used in contexts such as the logs viewer, where a
      file and line number are less meaningful. The format can vary by
      language. For example: `qual.if.ied.Class.method` (Java),
      `dir/package.func` (Go), `function` (Python).
    line: Optional. Line within the source file. 1-based; 0 indicates no line
      number available.
  """

  file = _messages.StringField(1)
  function = _messages.StringField(2)
  line = _messages.IntegerField(3)


class GoogleApiServicecontrolV1MetricValue(_messages.Message):
  r"""Represents a single metric value.

  Messages:
    LabelsValue: The labels describing the metric value. See comments on
      google.api.servicecontrol.v1.Operation.labels for the overriding
      relationship. Note that this map must not contain monitored resource
      labels.

  Fields:
    boolValue: A boolean value.
    distributionValue: A distribution value.
    doubleValue: A double precision floating point value.
    endTime: The end of the time period over which this metric value's
      measurement applies. If not specified,
      google.api.servicecontrol.v1.Operation.end_time will be used.
    int64Value: A signed 64-bit integer value.
    labels: The labels describing the metric value. See comments on
      google.api.servicecontrol.v1.Operation.labels for the overriding
      relationship. Note that this map must not contain monitored resource
      labels.
    moneyValue: A money value.
    startTime: The start of the time period over which this metric value's
      measurement applies. The time period has different semantics for
      different metric types (cumulative, delta, and gauge). See the metric
      definition documentation in the service configuration for details. If
      not specified, google.api.servicecontrol.v1.Operation.start_time will be
      used.
    stringValue: A text string value.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels describing the metric value. See comments on
    google.api.servicecontrol.v1.Operation.labels for the overriding
    relationship. Note that this map must not contain monitored resource
    labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  boolValue = _messages.BooleanField(1)
  distributionValue = _messages.MessageField('GoogleApiServicecontrolV1Distribution', 2)
  doubleValue = _messages.FloatField(3)
  endTime = _messages.StringField(4)
  int64Value = _messages.IntegerField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  moneyValue = _messages.MessageField('Money', 7)
  startTime = _messages.StringField(8)
  stringValue = _messages.StringField(9)


class GoogleApiServicecontrolV1MetricValueSet(_messages.Message):
  r"""Represents a set of metric values in the same metric. Each metric value
  in the set should have a unique combination of start time, end time, and
  label values.

  Fields:
    metricName: The metric name defined in the service configuration.
    metricValues: The values in this metric.
  """

  metricName = _messages.StringField(1)
  metricValues = _messages.MessageField('GoogleApiServicecontrolV1MetricValue', 2, repeated=True)


class GoogleApiServicecontrolV1Operation(_messages.Message):
  r"""Represents information regarding an operation.

  Enums:
    ImportanceValueValuesEnum: DO NOT USE. This is an experimental field.

  Messages:
    LabelsValue: Labels describing the operation. Only the following labels
      are allowed: - Labels describing monitored resources as defined in the
      service configuration. - Default labels of metric values. When
      specified, labels defined in the metric value override these default. -
      The following labels defined by Google Cloud Platform: -
      `cloud.googleapis.com/location` describing the location where the
      operation happened, - `servicecontrol.googleapis.com/user_agent`
      describing the user agent of the API request, -
      `servicecontrol.googleapis.com/service_agent` describing the service
      used to handle the API request (e.g. ESP), -
      `servicecontrol.googleapis.com/platform` describing the platform where
      the API is served, such as App Engine, Compute Engine, or Kubernetes
      Engine.
    UserLabelsValue: Private Preview. This feature is only available for
      approved services. User defined labels for the resource that this
      operation is associated with.

  Fields:
    consumerId: Identity of the consumer who is using the service. This field
      should be filled in for the operations initiated by a consumer, but not
      for service-initiated operations that are not related to a specific
      consumer. - This can be in one of the following formats: -
      project:PROJECT_ID, - project`_`number:PROJECT_NUMBER, -
      projects/PROJECT_ID or PROJECT_NUMBER, - folders/FOLDER_NUMBER, -
      organizations/ORGANIZATION_NUMBER, - api`_`key:API_KEY.
    endTime: End time of the operation. Required when the operation is used in
      ServiceController.Report, but optional when the operation is used in
      ServiceController.Check.
    importance: DO NOT USE. This is an experimental field.
    labels: Labels describing the operation. Only the following labels are
      allowed: - Labels describing monitored resources as defined in the
      service configuration. - Default labels of metric values. When
      specified, labels defined in the metric value override these default. -
      The following labels defined by Google Cloud Platform: -
      `cloud.googleapis.com/location` describing the location where the
      operation happened, - `servicecontrol.googleapis.com/user_agent`
      describing the user agent of the API request, -
      `servicecontrol.googleapis.com/service_agent` describing the service
      used to handle the API request (e.g. ESP), -
      `servicecontrol.googleapis.com/platform` describing the platform where
      the API is served, such as App Engine, Compute Engine, or Kubernetes
      Engine.
    logEntries: Represents information to be logged.
    metricValueSets: Represents information about this operation. Each
      MetricValueSet corresponds to a metric defined in the service
      configuration. The data type used in the MetricValueSet must agree with
      the data type specified in the metric definition. Within a single
      operation, it is not allowed to have more than one MetricValue instances
      that have the same metric names and identical label value combinations.
      If a request has such duplicated MetricValue instances, the entire
      request is rejected with an invalid argument error.
    operationId: Identity of the operation. This must be unique within the
      scope of the service that generated the operation. If the service calls
      Check() and Report() on the same operation, the two calls should carry
      the same id. UUID version 4 is recommended, though not required. In
      scenarios where an operation is computed from existing information and
      an idempotent id is desirable for deduplication purpose, UUID version 5
      is recommended. See RFC 4122 for details.
    operationName: Fully qualified name of the operation. Reserved for future
      use.
    quotaProperties: Represents the properties needed for quota check.
      Applicable only if this operation is for a quota check request. If this
      is not specified, no quota check will be performed.
    resources: The resources that are involved in the operation. The maximum
      supported number of entries in this field is 100.
    startTime: Required. Start time of the operation.
    traceSpans: Unimplemented. A list of Cloud Trace spans. The span names
      shall contain the id of the destination project which can be either the
      produce or the consumer project.
    userLabels: Private Preview. This feature is only available for approved
      services. User defined labels for the resource that this operation is
      associated with.
  """

  class ImportanceValueValuesEnum(_messages.Enum):
    r"""DO NOT USE. This is an experimental field.

    Values:
      LOW: Allows data caching, batching, and aggregation. It provides higher
        performance with higher data loss risk.
      HIGH: Disables data aggregation to minimize data loss. It is for
        operations that contains significant monetary value or audit trail.
        This feature only applies to the client libraries.
      DEBUG: Deprecated. Do not use. Disables data aggregation and enables
        additional validation logic. It should only be used during the
        onboarding process. It is only available to Google internal services,
        and the service must be <NAME_EMAIL> in order to
        use this level.
      PROMOTED: Used internally by Chemist.
    """
    LOW = 0
    HIGH = 1
    DEBUG = 2
    PROMOTED = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels describing the operation. Only the following labels are
    allowed: - Labels describing monitored resources as defined in the service
    configuration. - Default labels of metric values. When specified, labels
    defined in the metric value override these default. - The following labels
    defined by Google Cloud Platform: - `cloud.googleapis.com/location`
    describing the location where the operation happened, -
    `servicecontrol.googleapis.com/user_agent` describing the user agent of
    the API request, - `servicecontrol.googleapis.com/service_agent`
    describing the service used to handle the API request (e.g. ESP), -
    `servicecontrol.googleapis.com/platform` describing the platform where the
    API is served, such as App Engine, Compute Engine, or Kubernetes Engine.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class UserLabelsValue(_messages.Message):
    r"""Private Preview. This feature is only available for approved services.
    User defined labels for the resource that this operation is associated
    with.

    Messages:
      AdditionalProperty: An additional property for a UserLabelsValue object.

    Fields:
      additionalProperties: Additional properties of type UserLabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a UserLabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  consumerId = _messages.StringField(1)
  endTime = _messages.StringField(2)
  importance = _messages.EnumField('ImportanceValueValuesEnum', 3)
  labels = _messages.MessageField('LabelsValue', 4)
  logEntries = _messages.MessageField('GoogleApiServicecontrolV1LogEntry', 5, repeated=True)
  metricValueSets = _messages.MessageField('GoogleApiServicecontrolV1MetricValueSet', 6, repeated=True)
  operationId = _messages.StringField(7)
  operationName = _messages.StringField(8)
  quotaProperties = _messages.MessageField('GoogleApiServicecontrolV1QuotaProperties', 9)
  resources = _messages.MessageField('GoogleApiServicecontrolV1ResourceInfo', 10, repeated=True)
  startTime = _messages.StringField(11)
  traceSpans = _messages.MessageField('GoogleApiServicecontrolV1TraceSpan', 12, repeated=True)
  userLabels = _messages.MessageField('UserLabelsValue', 13)


class GoogleApiServicecontrolV1QuotaProperties(_messages.Message):
  r"""Represents the properties needed for quota operations.

  Enums:
    QuotaModeValueValuesEnum: Quota mode for this operation.

  Fields:
    quotaMode: Quota mode for this operation.
  """

  class QuotaModeValueValuesEnum(_messages.Enum):
    r"""Quota mode for this operation.

    Values:
      ACQUIRE: Decreases available quota by the cost specified for the
        operation. If cost is higher than available quota, operation fails and
        returns error.
      ACQUIRE_BEST_EFFORT: Decreases available quota by the cost specified for
        the operation. If cost is higher than available quota, operation does
        not fail and available quota goes down to zero but it returns error.
      CHECK: Does not change any available quota. Only checks if there is
        enough quota. No lock is placed on the checked tokens neither.
    """
    ACQUIRE = 0
    ACQUIRE_BEST_EFFORT = 1
    CHECK = 2

  quotaMode = _messages.EnumField('QuotaModeValueValuesEnum', 1)


class GoogleApiServicecontrolV1ReportRequest(_messages.Message):
  r"""Request message for the Report method.

  Fields:
    operations: Operations to be reported. Typically the service should report
      one operation per request. Putting multiple operations into a single
      request is allowed, but should be used only when multiple operations are
      natually available at the time of the report. There is no limit on the
      number of operations in the same ReportRequest, however the
      ReportRequest size should be no larger than 1MB. See
      ReportResponse.report_errors for partial failure behavior.
    serviceConfigId: Specifies which version of service config should be used
      to process the request. If unspecified or no matching version can be
      found, the latest one will be used.
    serviceName: The service name as specified in its service configuration.
      For example, `"pubsub.googleapis.com"`. See
      [google.api.Service](https://cloud.google.com/service-
      management/reference/rpc/google.api#google.api.Service) for the
      definition of a service name.
  """

  operations = _messages.MessageField('GoogleApiServicecontrolV1Operation', 1, repeated=True)
  serviceConfigId = _messages.StringField(2)
  serviceName = _messages.StringField(3)


class GoogleApiServicecontrolV1ResourceInfo(_messages.Message):
  r"""Describes a resource associated with this operation.

  Fields:
    permission: The resource permission required for this request.
    resourceContainer: The identifier of the parent of this resource instance.
      Must be in one of the following formats: - `projects/` - `folders/` -
      `organizations/`
    resourceLocation: The location of the resource. If not empty, the resource
      will be checked against location policy. The value must be a valid zone,
      region or multiregion. For example: "europe-west4" or "northamerica-
      northeast1-a"
    resourceName: Name of the resource. This is used for auditing purposes.
  """

  permission = _messages.StringField(1)
  resourceContainer = _messages.StringField(2)
  resourceLocation = _messages.StringField(3)
  resourceName = _messages.StringField(4)


class GoogleApiServicecontrolV1TraceSpan(_messages.Message):
  r"""A span represents a single operation within a trace. Spans can be nested
  to form a trace tree. Often, a trace contains a root span that describes the
  end-to-end latency, and one or more subspans for its sub-operations. A trace
  can also contain multiple root spans, or none at all. Spans do not need to
  be contiguous-there may be gaps or overlaps between spans in a trace.

  Enums:
    SpanKindValueValuesEnum: Distinguishes between spans generated in a
      particular context. For example, two spans with the same name may be
      distinguished using `CLIENT` (caller) and `SERVER` (callee) to identify
      an RPC call.

  Fields:
    attributes: A set of attributes on the span. You can have up to 32
      attributes per span.
    childSpanCount: An optional number of child spans that were generated
      while this span was active. If set, allows implementation to detect
      missing child spans.
    displayName: A description of the span's operation (up to 128 bytes).
      Stackdriver Trace displays the description in the Google Cloud Platform
      Console. For example, the display name can be a qualified method name or
      a file name and a line number where the operation is called. A best
      practice is to use the same display name within an application and at
      the same call point. This makes it easier to correlate spans in
      different traces.
    endTime: The end time of the span. On the client side, this is the time
      kept by the local machine where the span execution ends. On the server
      side, this is the time when the server application handler stops
      running.
    name: The resource name of the span in the following format:
      projects/[PROJECT_ID]/traces/[TRACE_ID]/spans/SPAN_ID is a unique
      identifier for a trace within a project; it is a 32-character
      hexadecimal encoding of a 16-byte array. [SPAN_ID] is a unique
      identifier for a span within a trace; it is a 16-character hexadecimal
      encoding of an 8-byte array.
    parentSpanId: The [SPAN_ID] of this span's parent span. If this is a root
      span, then this field must be empty.
    sameProcessAsParentSpan: (Optional) Set this parameter to indicate whether
      this span is in the same process as its parent. If you do not set this
      parameter, Stackdriver Trace is unable to take advantage of this helpful
      information.
    spanId: The [SPAN_ID] portion of the span's resource name.
    spanKind: Distinguishes between spans generated in a particular context.
      For example, two spans with the same name may be distinguished using
      `CLIENT` (caller) and `SERVER` (callee) to identify an RPC call.
    startTime: The start time of the span. On the client side, this is the
      time kept by the local machine where the span execution starts. On the
      server side, this is the time when the server's application handler
      starts running.
    status: An optional final status for this span.
  """

  class SpanKindValueValuesEnum(_messages.Enum):
    r"""Distinguishes between spans generated in a particular context. For
    example, two spans with the same name may be distinguished using `CLIENT`
    (caller) and `SERVER` (callee) to identify an RPC call.

    Values:
      SPAN_KIND_UNSPECIFIED: Unspecified. Do NOT use as default.
        Implementations MAY assume SpanKind.INTERNAL to be default.
      INTERNAL: Indicates that the span is used internally. Default value.
      SERVER: Indicates that the span covers server-side handling of an RPC or
        other remote network request.
      CLIENT: Indicates that the span covers the client-side wrapper around an
        RPC or other remote request.
      PRODUCER: Indicates that the span describes producer sending a message
        to a broker. Unlike client and server, there is no direct critical
        path latency relationship between producer and consumer spans (e.g.
        publishing a message to a pubsub service).
      CONSUMER: Indicates that the span describes consumer receiving a message
        from a broker. Unlike client and server, there is no direct critical
        path latency relationship between producer and consumer spans (e.g.
        receiving a message from a pubsub service subscription).
    """
    SPAN_KIND_UNSPECIFIED = 0
    INTERNAL = 1
    SERVER = 2
    CLIENT = 3
    PRODUCER = 4
    CONSUMER = 5

  attributes = _messages.MessageField('GoogleApiServicecontrolV1Attributes', 1)
  childSpanCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  displayName = _messages.MessageField('GoogleApiServicecontrolV1TruncatableString', 3)
  endTime = _messages.StringField(4)
  name = _messages.StringField(5)
  parentSpanId = _messages.StringField(6)
  sameProcessAsParentSpan = _messages.BooleanField(7)
  spanId = _messages.StringField(8)
  spanKind = _messages.EnumField('SpanKindValueValuesEnum', 9)
  startTime = _messages.StringField(10)
  status = _messages.MessageField('Status', 11)


class GoogleApiServicecontrolV1TruncatableString(_messages.Message):
  r"""Represents a string that might be shortened to a specified length.

  Fields:
    truncatedByteCount: The number of bytes removed from the original string.
      If this value is 0, then the string was not shortened.
    value: The shortened string. For example, if the original string is 500
      bytes long and the limit of the string is 128 bytes, then `value`
      contains the first 128 bytes of the 500-byte string. Truncation always
      happens on a UTF8 character boundary. If there are multi-byte characters
      in the string, then the length of the shortened string might be less
      than the size limit.
  """

  truncatedByteCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  value = _messages.StringField(2)


class IdsProjectsLocationsEndpointsCreateRequest(_messages.Message):
  r"""A IdsProjectsLocationsEndpointsCreateRequest object.

  Fields:
    endpoint: A Endpoint resource to be passed as the request body.
    endpointId: Required. The endpoint identifier. This will be part of the
      endpoint's resource name. This value must start with a lowercase letter
      followed by up to 62 lowercase letters, numbers, or hyphens, and cannot
      end with a hyphen. Values that do not match this pattern will trigger an
      INVALID_ARGUMENT error.
    parent: Required. The endpoint's parent.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  endpoint = _messages.MessageField('Endpoint', 1)
  endpointId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class IdsProjectsLocationsEndpointsDeleteRequest(_messages.Message):
  r"""A IdsProjectsLocationsEndpointsDeleteRequest object.

  Fields:
    name: Required. The name of the endpoint to delete.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class IdsProjectsLocationsEndpointsGetRequest(_messages.Message):
  r"""A IdsProjectsLocationsEndpointsGetRequest object.

  Fields:
    name: Required. The name of the endpoint to retrieve. Format:
      projects/{project}/locations/{location}/endpoints/{endpoint}
  """

  name = _messages.StringField(1, required=True)


class IdsProjectsLocationsEndpointsListRequest(_messages.Message):
  r"""A IdsProjectsLocationsEndpointsListRequest object.

  Fields:
    filter: Optional. The filter expression, following the syntax outlined in
      https://google.aip.dev/160.
    orderBy: Optional. One or more fields to compare and use to sort the
      output. See https://google.aip.dev/132#ordering.
    pageSize: Optional. The maximum number of endpoints to return. The service
      may return fewer than this value.
    pageToken: Optional. A page token, received from a previous
      `ListEndpoints` call. Provide this to retrieve the subsequent page. When
      paginating, all other parameters provided to `ListEndpoints` must match
      the call that provided the page token.
    parent: Required. The parent, which owns this collection of endpoints.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class IdsProjectsLocationsEndpointsPatchRequest(_messages.Message):
  r"""A IdsProjectsLocationsEndpointsPatchRequest object.

  Fields:
    endpoint: A Endpoint resource to be passed as the request body.
    name: Output only. The name of the endpoint.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Field mask is used to specify the fields to be overwritten in
      the Endpoint resource by the update. The fields specified in the
      update_mask are relative to the resource, not the full request. A field
      will be overwritten if it is in the mask. If the user does not provide a
      mask then all fields will be overwritten.
  """

  endpoint = _messages.MessageField('Endpoint', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class IdsProjectsLocationsGetRequest(_messages.Message):
  r"""A IdsProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class IdsProjectsLocationsListRequest(_messages.Message):
  r"""A IdsProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    includeUnrevealedLocations: If true, the returned list will include
      locations which are not yet revealed.
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  includeUnrevealedLocations = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  pageSize = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(6)


class IdsProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A IdsProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class IdsProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A IdsProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class IdsProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A IdsProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class IdsProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A IdsProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
    returnPartialSuccess: When set to `true`, operations that are reachable
      are returned as normal, and those that are unreachable are returned in
      the [ListOperationsResponse.unreachable] field. This can only be `true`
      when reading across collections e.g. when `parent` is set to
      `"projects/example/locations/-"`. This field is not by default supported
      and will result in an `UNIMPLEMENTED` error if set unless explicitly
      documented otherwise in service or product specific documentation.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  returnPartialSuccess = _messages.BooleanField(5)


class ListEndpointsResponse(_messages.Message):
  r"""A ListEndpointsResponse object.

  Fields:
    endpoints: The list of endpoints response.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached.
  """

  endpoints = _messages.MessageField('Endpoint', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
    unreachable: Unordered list. Unreachable resources. Populated when the
      request sets `ListOperationsRequest.return_partial_success` and reads
      across collections e.g. when attempting to list all resources across all
      supported locations.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class Money(_messages.Message):
  r"""Represents an amount of money with its currency type.

  Fields:
    currencyCode: The three-letter currency code defined in ISO 4217.
    nanos: Number of nano (10^-9) units of the amount. The value must be
      between -999,999,999 and +999,999,999 inclusive. If `units` is positive,
      `nanos` must be positive or zero. If `units` is zero, `nanos` can be
      positive, zero, or negative. If `units` is negative, `nanos` must be
      negative or zero. For example $-1.75 is represented as `units`=-1 and
      `nanos`=-750,000,000.
    units: The whole units of the amount. For example if `currencyCode` is
      `"USD"`, then 1 unit is one US dollar.
  """

  currencyCode = _messages.StringField(1)
  nanos = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  units = _messages.IntegerField(3)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have google.longrunning.Operation.error
      value with a google.rpc.Status.code of 1, corresponding to
      `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
