"""Generated client library for pubsub version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.pubsub.v1 import pubsub_v1_messages as messages


class PubsubV1(base_api.BaseApiClient):
  """Generated client library for service pubsub version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://pubsub.googleapis.com/'
  MTLS_BASE_URL = 'https://pubsub.mtls.googleapis.com/'

  _PACKAGE = 'pubsub'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform', 'https://www.googleapis.com/auth/pubsub']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'PubsubV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new pubsub handle."""
    url = url or self.BASE_URL
    super(PubsubV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_schemas = self.ProjectsSchemasService(self)
    self.projects_snapshots = self.ProjectsSnapshotsService(self)
    self.projects_subscriptions = self.ProjectsSubscriptionsService(self)
    self.projects_topics_snapshots = self.ProjectsTopicsSnapshotsService(self)
    self.projects_topics_subscriptions = self.ProjectsTopicsSubscriptionsService(self)
    self.projects_topics = self.ProjectsTopicsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsSchemasService(base_api.BaseApiService):
    """Service class for the projects_schemas resource."""

    _NAME = 'projects_schemas'

    def __init__(self, client):
      super(PubsubV1.ProjectsSchemasService, self).__init__(client)
      self._upload_configs = {
          }

    def Commit(self, request, global_params=None):
      r"""Commits a new schema revision to an existing schema.

      Args:
        request: (PubsubProjectsSchemasCommitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Schema) The response message.
      """
      config = self.GetMethodConfig('Commit')
      return self._RunMethod(
          config, request, global_params=global_params)

    Commit.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/schemas/{schemasId}:commit',
        http_method='POST',
        method_id='pubsub.projects.schemas.commit',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:commit',
        request_field='commitSchemaRequest',
        request_type_name='PubsubProjectsSchemasCommitRequest',
        response_type_name='Schema',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a schema.

      Args:
        request: (PubsubProjectsSchemasCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Schema) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/schemas',
        http_method='POST',
        method_id='pubsub.projects.schemas.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['schemaId'],
        relative_path='v1/{+parent}/schemas',
        request_field='schema',
        request_type_name='PubsubProjectsSchemasCreateRequest',
        response_type_name='Schema',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a schema.

      Args:
        request: (PubsubProjectsSchemasDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/schemas/{schemasId}',
        http_method='DELETE',
        method_id='pubsub.projects.schemas.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='PubsubProjectsSchemasDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def DeleteRevision(self, request, global_params=None):
      r"""Deletes a specific schema revision.

      Args:
        request: (PubsubProjectsSchemasDeleteRevisionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Schema) The response message.
      """
      config = self.GetMethodConfig('DeleteRevision')
      return self._RunMethod(
          config, request, global_params=global_params)

    DeleteRevision.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/schemas/{schemasId}:deleteRevision',
        http_method='DELETE',
        method_id='pubsub.projects.schemas.deleteRevision',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['revisionId'],
        relative_path='v1/{+name}:deleteRevision',
        request_field='',
        request_type_name='PubsubProjectsSchemasDeleteRevisionRequest',
        response_type_name='Schema',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a schema.

      Args:
        request: (PubsubProjectsSchemasGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Schema) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/schemas/{schemasId}',
        http_method='GET',
        method_id='pubsub.projects.schemas.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='PubsubProjectsSchemasGetRequest',
        response_type_name='Schema',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (PubsubProjectsSchemasGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/schemas/{schemasId}:getIamPolicy',
        http_method='GET',
        method_id='pubsub.projects.schemas.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='PubsubProjectsSchemasGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists schemas in a project.

      Args:
        request: (PubsubProjectsSchemasListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSchemasResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/schemas',
        http_method='GET',
        method_id='pubsub.projects.schemas.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'view'],
        relative_path='v1/{+parent}/schemas',
        request_field='',
        request_type_name='PubsubProjectsSchemasListRequest',
        response_type_name='ListSchemasResponse',
        supports_download=False,
    )

    def ListRevisions(self, request, global_params=None):
      r"""Lists all schema revisions for the named schema.

      Args:
        request: (PubsubProjectsSchemasListRevisionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSchemaRevisionsResponse) The response message.
      """
      config = self.GetMethodConfig('ListRevisions')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListRevisions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/schemas/{schemasId}:listRevisions',
        http_method='GET',
        method_id='pubsub.projects.schemas.listRevisions',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['pageSize', 'pageToken', 'view'],
        relative_path='v1/{+name}:listRevisions',
        request_field='',
        request_type_name='PubsubProjectsSchemasListRevisionsRequest',
        response_type_name='ListSchemaRevisionsResponse',
        supports_download=False,
    )

    def Rollback(self, request, global_params=None):
      r"""Creates a new schema revision that is a copy of the provided revision_id.

      Args:
        request: (PubsubProjectsSchemasRollbackRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Schema) The response message.
      """
      config = self.GetMethodConfig('Rollback')
      return self._RunMethod(
          config, request, global_params=global_params)

    Rollback.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/schemas/{schemasId}:rollback',
        http_method='POST',
        method_id='pubsub.projects.schemas.rollback',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:rollback',
        request_field='rollbackSchemaRequest',
        request_type_name='PubsubProjectsSchemasRollbackRequest',
        response_type_name='Schema',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (PubsubProjectsSchemasSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/schemas/{schemasId}:setIamPolicy',
        http_method='POST',
        method_id='pubsub.projects.schemas.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='PubsubProjectsSchemasSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (PubsubProjectsSchemasTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/schemas/{schemasId}:testIamPermissions',
        http_method='POST',
        method_id='pubsub.projects.schemas.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='PubsubProjectsSchemasTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def Validate(self, request, global_params=None):
      r"""Validates a schema.

      Args:
        request: (PubsubProjectsSchemasValidateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ValidateSchemaResponse) The response message.
      """
      config = self.GetMethodConfig('Validate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Validate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/schemas:validate',
        http_method='POST',
        method_id='pubsub.projects.schemas.validate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/schemas:validate',
        request_field='validateSchemaRequest',
        request_type_name='PubsubProjectsSchemasValidateRequest',
        response_type_name='ValidateSchemaResponse',
        supports_download=False,
    )

    def ValidateMessage(self, request, global_params=None):
      r"""Validates a message against a schema.

      Args:
        request: (PubsubProjectsSchemasValidateMessageRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ValidateMessageResponse) The response message.
      """
      config = self.GetMethodConfig('ValidateMessage')
      return self._RunMethod(
          config, request, global_params=global_params)

    ValidateMessage.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/schemas:validateMessage',
        http_method='POST',
        method_id='pubsub.projects.schemas.validateMessage',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/schemas:validateMessage',
        request_field='validateMessageRequest',
        request_type_name='PubsubProjectsSchemasValidateMessageRequest',
        response_type_name='ValidateMessageResponse',
        supports_download=False,
    )

  class ProjectsSnapshotsService(base_api.BaseApiService):
    """Service class for the projects_snapshots resource."""

    _NAME = 'projects_snapshots'

    def __init__(self, client):
      super(PubsubV1.ProjectsSnapshotsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a snapshot from the requested subscription. Snapshots are used in [Seek](https://cloud.google.com/pubsub/docs/replay-overview) operations, which allow you to manage message acknowledgments in bulk. That is, you can set the acknowledgment state of messages in an existing subscription to the state captured by a snapshot. If the snapshot already exists, returns `ALREADY_EXISTS`. If the requested subscription doesn't exist, returns `NOT_FOUND`. If the backlog in the subscription is too old -- and the resulting snapshot would expire in less than 1 hour -- then `FAILED_PRECONDITION` is returned. See also the `Snapshot.expire_time` field. If the name is not provided in the request, the server will assign a random name for this snapshot on the same project as the subscription, conforming to the [resource name format] (https://cloud.google.com/pubsub/docs/pubsub-basics#resource_names). The generated name is populated in the returned Snapshot object. Note that for REST API requests, you must specify a name in the request.

      Args:
        request: (PubsubProjectsSnapshotsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Snapshot) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/snapshots/{snapshotsId}',
        http_method='PUT',
        method_id='pubsub.projects.snapshots.create',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='createSnapshotRequest',
        request_type_name='PubsubProjectsSnapshotsCreateRequest',
        response_type_name='Snapshot',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Removes an existing snapshot. Snapshots are used in [Seek] (https://cloud.google.com/pubsub/docs/replay-overview) operations, which allow you to manage message acknowledgments in bulk. That is, you can set the acknowledgment state of messages in an existing subscription to the state captured by a snapshot. When the snapshot is deleted, all messages retained in the snapshot are immediately dropped. After a snapshot is deleted, a new one may be created with the same name, but the new one has no association with the old snapshot or its subscription, unless the same subscription is specified.

      Args:
        request: (PubsubProjectsSnapshotsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/snapshots/{snapshotsId}',
        http_method='DELETE',
        method_id='pubsub.projects.snapshots.delete',
        ordered_params=['snapshot'],
        path_params=['snapshot'],
        query_params=[],
        relative_path='v1/{+snapshot}',
        request_field='',
        request_type_name='PubsubProjectsSnapshotsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the configuration details of a snapshot. Snapshots are used in [Seek](https://cloud.google.com/pubsub/docs/replay-overview) operations, which allow you to manage message acknowledgments in bulk. That is, you can set the acknowledgment state of messages in an existing subscription to the state captured by a snapshot.

      Args:
        request: (PubsubProjectsSnapshotsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Snapshot) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/snapshots/{snapshotsId}',
        http_method='GET',
        method_id='pubsub.projects.snapshots.get',
        ordered_params=['snapshot'],
        path_params=['snapshot'],
        query_params=[],
        relative_path='v1/{+snapshot}',
        request_field='',
        request_type_name='PubsubProjectsSnapshotsGetRequest',
        response_type_name='Snapshot',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (PubsubProjectsSnapshotsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/snapshots/{snapshotsId}:getIamPolicy',
        http_method='GET',
        method_id='pubsub.projects.snapshots.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='PubsubProjectsSnapshotsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the existing snapshots. Snapshots are used in [Seek]( https://cloud.google.com/pubsub/docs/replay-overview) operations, which allow you to manage message acknowledgments in bulk. That is, you can set the acknowledgment state of messages in an existing subscription to the state captured by a snapshot.

      Args:
        request: (PubsubProjectsSnapshotsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSnapshotsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/snapshots',
        http_method='GET',
        method_id='pubsub.projects.snapshots.list',
        ordered_params=['project'],
        path_params=['project'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+project}/snapshots',
        request_field='',
        request_type_name='PubsubProjectsSnapshotsListRequest',
        response_type_name='ListSnapshotsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing snapshot by updating the fields specified in the update mask. Snapshots are used in [Seek](https://cloud.google.com/pubsub/docs/replay-overview) operations, which allow you to manage message acknowledgments in bulk. That is, you can set the acknowledgment state of messages in an existing subscription to the state captured by a snapshot.

      Args:
        request: (PubsubProjectsSnapshotsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Snapshot) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/snapshots/{snapshotsId}',
        http_method='PATCH',
        method_id='pubsub.projects.snapshots.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='updateSnapshotRequest',
        request_type_name='PubsubProjectsSnapshotsPatchRequest',
        response_type_name='Snapshot',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (PubsubProjectsSnapshotsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/snapshots/{snapshotsId}:setIamPolicy',
        http_method='POST',
        method_id='pubsub.projects.snapshots.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='PubsubProjectsSnapshotsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (PubsubProjectsSnapshotsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/snapshots/{snapshotsId}:testIamPermissions',
        http_method='POST',
        method_id='pubsub.projects.snapshots.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='PubsubProjectsSnapshotsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsSubscriptionsService(base_api.BaseApiService):
    """Service class for the projects_subscriptions resource."""

    _NAME = 'projects_subscriptions'

    def __init__(self, client):
      super(PubsubV1.ProjectsSubscriptionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Acknowledge(self, request, global_params=None):
      r"""Acknowledges the messages associated with the `ack_ids` in the `AcknowledgeRequest`. The Pub/Sub system can remove the relevant messages from the subscription. Acknowledging a message whose ack deadline has expired may succeed, but such a message may be redelivered later. Acknowledging a message more than once will not result in an error.

      Args:
        request: (PubsubProjectsSubscriptionsAcknowledgeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Acknowledge')
      return self._RunMethod(
          config, request, global_params=global_params)

    Acknowledge.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/subscriptions/{subscriptionsId}:acknowledge',
        http_method='POST',
        method_id='pubsub.projects.subscriptions.acknowledge',
        ordered_params=['subscription'],
        path_params=['subscription'],
        query_params=[],
        relative_path='v1/{+subscription}:acknowledge',
        request_field='acknowledgeRequest',
        request_type_name='PubsubProjectsSubscriptionsAcknowledgeRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a subscription to a given topic. See the [resource name rules] (https://cloud.google.com/pubsub/docs/pubsub-basics#resource_names). If the subscription already exists, returns `ALREADY_EXISTS`. If the corresponding topic doesn't exist, returns `NOT_FOUND`. If the name is not provided in the request, the server will assign a random name for this subscription on the same project as the topic, conforming to the [resource name format] (https://cloud.google.com/pubsub/docs/pubsub-basics#resource_names). The generated name is populated in the returned Subscription object. Note that for REST API requests, you must specify a name in the request.

      Args:
        request: (Subscription) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Subscription) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/subscriptions/{subscriptionsId}',
        http_method='PUT',
        method_id='pubsub.projects.subscriptions.create',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='<request>',
        request_type_name='Subscription',
        response_type_name='Subscription',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing subscription. All messages retained in the subscription are immediately dropped. Calls to `Pull` after deletion will return `NOT_FOUND`. After a subscription is deleted, a new one may be created with the same name, but the new one has no association with the old subscription or its topic unless the same topic is specified.

      Args:
        request: (PubsubProjectsSubscriptionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/subscriptions/{subscriptionsId}',
        http_method='DELETE',
        method_id='pubsub.projects.subscriptions.delete',
        ordered_params=['subscription'],
        path_params=['subscription'],
        query_params=[],
        relative_path='v1/{+subscription}',
        request_field='',
        request_type_name='PubsubProjectsSubscriptionsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Detach(self, request, global_params=None):
      r"""Detaches a subscription from this topic. All messages retained in the subscription are dropped. Subsequent `Pull` and `StreamingPull` requests will return FAILED_PRECONDITION. If the subscription is a push subscription, pushes to the endpoint will stop.

      Args:
        request: (PubsubProjectsSubscriptionsDetachRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DetachSubscriptionResponse) The response message.
      """
      config = self.GetMethodConfig('Detach')
      return self._RunMethod(
          config, request, global_params=global_params)

    Detach.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/subscriptions/{subscriptionsId}:detach',
        http_method='POST',
        method_id='pubsub.projects.subscriptions.detach',
        ordered_params=['subscription'],
        path_params=['subscription'],
        query_params=[],
        relative_path='v1/{+subscription}:detach',
        request_field='',
        request_type_name='PubsubProjectsSubscriptionsDetachRequest',
        response_type_name='DetachSubscriptionResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the configuration details of a subscription.

      Args:
        request: (PubsubProjectsSubscriptionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Subscription) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/subscriptions/{subscriptionsId}',
        http_method='GET',
        method_id='pubsub.projects.subscriptions.get',
        ordered_params=['subscription'],
        path_params=['subscription'],
        query_params=[],
        relative_path='v1/{+subscription}',
        request_field='',
        request_type_name='PubsubProjectsSubscriptionsGetRequest',
        response_type_name='Subscription',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (PubsubProjectsSubscriptionsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/subscriptions/{subscriptionsId}:getIamPolicy',
        http_method='GET',
        method_id='pubsub.projects.subscriptions.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='PubsubProjectsSubscriptionsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists matching subscriptions.

      Args:
        request: (PubsubProjectsSubscriptionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSubscriptionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/subscriptions',
        http_method='GET',
        method_id='pubsub.projects.subscriptions.list',
        ordered_params=['project'],
        path_params=['project'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+project}/subscriptions',
        request_field='',
        request_type_name='PubsubProjectsSubscriptionsListRequest',
        response_type_name='ListSubscriptionsResponse',
        supports_download=False,
    )

    def ModifyAckDeadline(self, request, global_params=None):
      r"""Modifies the ack deadline for a specific message. This method is useful to indicate that more time is needed to process a message by the subscriber, or to make the message available for redelivery if the processing was interrupted. Note that this does not modify the subscription-level `ackDeadlineSeconds` used for subsequent messages.

      Args:
        request: (PubsubProjectsSubscriptionsModifyAckDeadlineRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('ModifyAckDeadline')
      return self._RunMethod(
          config, request, global_params=global_params)

    ModifyAckDeadline.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/subscriptions/{subscriptionsId}:modifyAckDeadline',
        http_method='POST',
        method_id='pubsub.projects.subscriptions.modifyAckDeadline',
        ordered_params=['subscription'],
        path_params=['subscription'],
        query_params=[],
        relative_path='v1/{+subscription}:modifyAckDeadline',
        request_field='modifyAckDeadlineRequest',
        request_type_name='PubsubProjectsSubscriptionsModifyAckDeadlineRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def ModifyPushConfig(self, request, global_params=None):
      r"""Modifies the `PushConfig` for a specified subscription. This may be used to change a push subscription to a pull one (signified by an empty `PushConfig`) or vice versa, or change the endpoint URL and other attributes of a push subscription. Messages will accumulate for delivery continuously through the call regardless of changes to the `PushConfig`.

      Args:
        request: (PubsubProjectsSubscriptionsModifyPushConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('ModifyPushConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    ModifyPushConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/subscriptions/{subscriptionsId}:modifyPushConfig',
        http_method='POST',
        method_id='pubsub.projects.subscriptions.modifyPushConfig',
        ordered_params=['subscription'],
        path_params=['subscription'],
        query_params=[],
        relative_path='v1/{+subscription}:modifyPushConfig',
        request_field='modifyPushConfigRequest',
        request_type_name='PubsubProjectsSubscriptionsModifyPushConfigRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing subscription by updating the fields specified in the update mask. Note that certain properties of a subscription, such as its topic, are not modifiable.

      Args:
        request: (PubsubProjectsSubscriptionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Subscription) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/subscriptions/{subscriptionsId}',
        http_method='PATCH',
        method_id='pubsub.projects.subscriptions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='updateSubscriptionRequest',
        request_type_name='PubsubProjectsSubscriptionsPatchRequest',
        response_type_name='Subscription',
        supports_download=False,
    )

    def Pull(self, request, global_params=None):
      r"""Pulls messages from the server.

      Args:
        request: (PubsubProjectsSubscriptionsPullRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PullResponse) The response message.
      """
      config = self.GetMethodConfig('Pull')
      return self._RunMethod(
          config, request, global_params=global_params)

    Pull.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/subscriptions/{subscriptionsId}:pull',
        http_method='POST',
        method_id='pubsub.projects.subscriptions.pull',
        ordered_params=['subscription'],
        path_params=['subscription'],
        query_params=[],
        relative_path='v1/{+subscription}:pull',
        request_field='pullRequest',
        request_type_name='PubsubProjectsSubscriptionsPullRequest',
        response_type_name='PullResponse',
        supports_download=False,
    )

    def Seek(self, request, global_params=None):
      r"""Seeks an existing subscription to a point in time or to a given snapshot, whichever is provided in the request. Snapshots are used in [Seek] (https://cloud.google.com/pubsub/docs/replay-overview) operations, which allow you to manage message acknowledgments in bulk. That is, you can set the acknowledgment state of messages in an existing subscription to the state captured by a snapshot. Note that both the subscription and the snapshot must be on the same topic.

      Args:
        request: (PubsubProjectsSubscriptionsSeekRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SeekResponse) The response message.
      """
      config = self.GetMethodConfig('Seek')
      return self._RunMethod(
          config, request, global_params=global_params)

    Seek.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/subscriptions/{subscriptionsId}:seek',
        http_method='POST',
        method_id='pubsub.projects.subscriptions.seek',
        ordered_params=['subscription'],
        path_params=['subscription'],
        query_params=[],
        relative_path='v1/{+subscription}:seek',
        request_field='seekRequest',
        request_type_name='PubsubProjectsSubscriptionsSeekRequest',
        response_type_name='SeekResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (PubsubProjectsSubscriptionsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/subscriptions/{subscriptionsId}:setIamPolicy',
        http_method='POST',
        method_id='pubsub.projects.subscriptions.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='PubsubProjectsSubscriptionsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (PubsubProjectsSubscriptionsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/subscriptions/{subscriptionsId}:testIamPermissions',
        http_method='POST',
        method_id='pubsub.projects.subscriptions.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='PubsubProjectsSubscriptionsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsTopicsSnapshotsService(base_api.BaseApiService):
    """Service class for the projects_topics_snapshots resource."""

    _NAME = 'projects_topics_snapshots'

    def __init__(self, client):
      super(PubsubV1.ProjectsTopicsSnapshotsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the names of the snapshots on this topic. Snapshots are used in [Seek](https://cloud.google.com/pubsub/docs/replay-overview) operations, which allow you to manage message acknowledgments in bulk. That is, you can set the acknowledgment state of messages in an existing subscription to the state captured by a snapshot.

      Args:
        request: (PubsubProjectsTopicsSnapshotsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListTopicSnapshotsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/topics/{topicsId}/snapshots',
        http_method='GET',
        method_id='pubsub.projects.topics.snapshots.list',
        ordered_params=['topic'],
        path_params=['topic'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+topic}/snapshots',
        request_field='',
        request_type_name='PubsubProjectsTopicsSnapshotsListRequest',
        response_type_name='ListTopicSnapshotsResponse',
        supports_download=False,
    )

  class ProjectsTopicsSubscriptionsService(base_api.BaseApiService):
    """Service class for the projects_topics_subscriptions resource."""

    _NAME = 'projects_topics_subscriptions'

    def __init__(self, client):
      super(PubsubV1.ProjectsTopicsSubscriptionsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the names of the attached subscriptions on this topic.

      Args:
        request: (PubsubProjectsTopicsSubscriptionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListTopicSubscriptionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/topics/{topicsId}/subscriptions',
        http_method='GET',
        method_id='pubsub.projects.topics.subscriptions.list',
        ordered_params=['topic'],
        path_params=['topic'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+topic}/subscriptions',
        request_field='',
        request_type_name='PubsubProjectsTopicsSubscriptionsListRequest',
        response_type_name='ListTopicSubscriptionsResponse',
        supports_download=False,
    )

  class ProjectsTopicsService(base_api.BaseApiService):
    """Service class for the projects_topics resource."""

    _NAME = 'projects_topics'

    def __init__(self, client):
      super(PubsubV1.ProjectsTopicsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates the given topic with the given name. See the [resource name rules] (https://cloud.google.com/pubsub/docs/pubsub-basics#resource_names).

      Args:
        request: (Topic) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Topic) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/topics/{topicsId}',
        http_method='PUT',
        method_id='pubsub.projects.topics.create',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='<request>',
        request_type_name='Topic',
        response_type_name='Topic',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the topic with the given name. Returns `NOT_FOUND` if the topic does not exist. After a topic is deleted, a new topic may be created with the same name; this is an entirely new topic with none of the old configuration or subscriptions. Existing subscriptions to this topic are not deleted, but their `topic` field is set to `_deleted-topic_`.

      Args:
        request: (PubsubProjectsTopicsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/topics/{topicsId}',
        http_method='DELETE',
        method_id='pubsub.projects.topics.delete',
        ordered_params=['topic'],
        path_params=['topic'],
        query_params=[],
        relative_path='v1/{+topic}',
        request_field='',
        request_type_name='PubsubProjectsTopicsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the configuration of a topic.

      Args:
        request: (PubsubProjectsTopicsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Topic) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/topics/{topicsId}',
        http_method='GET',
        method_id='pubsub.projects.topics.get',
        ordered_params=['topic'],
        path_params=['topic'],
        query_params=[],
        relative_path='v1/{+topic}',
        request_field='',
        request_type_name='PubsubProjectsTopicsGetRequest',
        response_type_name='Topic',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (PubsubProjectsTopicsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/topics/{topicsId}:getIamPolicy',
        http_method='GET',
        method_id='pubsub.projects.topics.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='PubsubProjectsTopicsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists matching topics.

      Args:
        request: (PubsubProjectsTopicsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListTopicsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/topics',
        http_method='GET',
        method_id='pubsub.projects.topics.list',
        ordered_params=['project'],
        path_params=['project'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+project}/topics',
        request_field='',
        request_type_name='PubsubProjectsTopicsListRequest',
        response_type_name='ListTopicsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing topic by updating the fields specified in the update mask. Note that certain properties of a topic are not modifiable.

      Args:
        request: (PubsubProjectsTopicsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Topic) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/topics/{topicsId}',
        http_method='PATCH',
        method_id='pubsub.projects.topics.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='updateTopicRequest',
        request_type_name='PubsubProjectsTopicsPatchRequest',
        response_type_name='Topic',
        supports_download=False,
    )

    def Publish(self, request, global_params=None):
      r"""Adds one or more messages to the topic. Returns `NOT_FOUND` if the topic does not exist.

      Args:
        request: (PubsubProjectsTopicsPublishRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PublishResponse) The response message.
      """
      config = self.GetMethodConfig('Publish')
      return self._RunMethod(
          config, request, global_params=global_params)

    Publish.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/topics/{topicsId}:publish',
        http_method='POST',
        method_id='pubsub.projects.topics.publish',
        ordered_params=['topic'],
        path_params=['topic'],
        query_params=[],
        relative_path='v1/{+topic}:publish',
        request_field='publishRequest',
        request_type_name='PubsubProjectsTopicsPublishRequest',
        response_type_name='PublishResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (PubsubProjectsTopicsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/topics/{topicsId}:setIamPolicy',
        http_method='POST',
        method_id='pubsub.projects.topics.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='PubsubProjectsTopicsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (PubsubProjectsTopicsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/topics/{topicsId}:testIamPermissions',
        http_method='POST',
        method_id='pubsub.projects.topics.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='PubsubProjectsTopicsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(PubsubV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

    def TestMessageTransforms(self, request, global_params=None):
      r"""Tests applying MessageTransforms to a Pub/Sub message.

      Args:
        request: (PubsubProjectsTestMessageTransformsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestMessageTransformsResponse) The response message.
      """
      config = self.GetMethodConfig('TestMessageTransforms')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestMessageTransforms.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}:testMessageTransforms',
        http_method='POST',
        method_id='pubsub.projects.testMessageTransforms',
        ordered_params=['project'],
        path_params=['project'],
        query_params=[],
        relative_path='v1/{+project}:testMessageTransforms',
        request_field='testMessageTransformsRequest',
        request_type_name='PubsubProjectsTestMessageTransformsRequest',
        response_type_name='TestMessageTransformsResponse',
        supports_download=False,
    )

    def ValidateMessageTransform(self, request, global_params=None):
      r"""Validates a MessageTransform.

      Args:
        request: (PubsubProjectsValidateMessageTransformRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ValidateMessageTransformResponse) The response message.
      """
      config = self.GetMethodConfig('ValidateMessageTransform')
      return self._RunMethod(
          config, request, global_params=global_params)

    ValidateMessageTransform.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}:validateMessageTransform',
        http_method='POST',
        method_id='pubsub.projects.validateMessageTransform',
        ordered_params=['project'],
        path_params=['project'],
        query_params=[],
        relative_path='v1/{+project}:validateMessageTransform',
        request_field='validateMessageTransformRequest',
        request_type_name='PubsubProjectsValidateMessageTransformRequest',
        response_type_name='ValidateMessageTransformResponse',
        supports_download=False,
    )
