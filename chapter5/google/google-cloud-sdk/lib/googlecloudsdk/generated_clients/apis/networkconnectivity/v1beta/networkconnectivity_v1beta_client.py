"""Generated client library for networkconnectivity version v1beta."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.networkconnectivity.v1beta import networkconnectivity_v1beta_messages as messages


class NetworkconnectivityV1beta(base_api.BaseApiClient):
  """Generated client library for service networkconnectivity version v1beta."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://networkconnectivity.googleapis.com/'
  MTLS_BASE_URL = 'https://networkconnectivity.mtls.googleapis.com/'

  _PACKAGE = 'networkconnectivity'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1beta'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'NetworkconnectivityV1beta'
  _URL_VERSION = 'v1beta'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new networkconnectivity handle."""
    url = url or self.BASE_URL
    super(NetworkconnectivityV1beta, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_global_hubs_groups = self.ProjectsLocationsGlobalHubsGroupsService(self)
    self.projects_locations_global_hubs_routeTables_routes = self.ProjectsLocationsGlobalHubsRouteTablesRoutesService(self)
    self.projects_locations_global_hubs_routeTables = self.ProjectsLocationsGlobalHubsRouteTablesService(self)
    self.projects_locations_global_hubs = self.ProjectsLocationsGlobalHubsService(self)
    self.projects_locations_global_policyBasedRoutes = self.ProjectsLocationsGlobalPolicyBasedRoutesService(self)
    self.projects_locations_global = self.ProjectsLocationsGlobalService(self)
    self.projects_locations_multicloudDataTransferConfigs_destinations = self.ProjectsLocationsMulticloudDataTransferConfigsDestinationsService(self)
    self.projects_locations_multicloudDataTransferConfigs = self.ProjectsLocationsMulticloudDataTransferConfigsService(self)
    self.projects_locations_multicloudDataTransferSupportedServices = self.ProjectsLocationsMulticloudDataTransferSupportedServicesService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_regionalEndpoints = self.ProjectsLocationsRegionalEndpointsService(self)
    self.projects_locations_spokes_gatewayAdvertisedRoutes = self.ProjectsLocationsSpokesGatewayAdvertisedRoutesService(self)
    self.projects_locations_spokes = self.ProjectsLocationsSpokesService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsGlobalHubsGroupsService(base_api.BaseApiService):
    """Service class for the projects_locations_global_hubs_groups resource."""

    _NAME = 'projects_locations_global_hubs_groups'

    def __init__(self, client):
      super(NetworkconnectivityV1beta.ProjectsLocationsGlobalHubsGroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details about a Network Connectivity Center group.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsGroupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudNetworkconnectivityV1betaGroup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/hubs/{hubsId}/groups/{groupsId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.hubs.groups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsGroupsGetRequest',
        response_type_name='GoogleCloudNetworkconnectivityV1betaGroup',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsGroupsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/hubs/{hubsId}/groups/{groupsId}:getIamPolicy',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.hubs.groups.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1beta/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsGroupsGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists groups in a given hub.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsGroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudNetworkconnectivityV1betaListGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/hubs/{hubsId}/groups',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.hubs.groups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta/{+parent}/groups',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsGroupsListRequest',
        response_type_name='GoogleCloudNetworkconnectivityV1betaListGroupsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a Network Connectivity Center group.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsGroupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/hubs/{hubsId}/groups/{groupsId}',
        http_method='PATCH',
        method_id='networkconnectivity.projects.locations.global.hubs.groups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1beta/{+name}',
        request_field='googleCloudNetworkconnectivityV1betaGroup',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsGroupsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsGroupsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/hubs/{hubsId}/groups/{groupsId}:setIamPolicy',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.global.hubs.groups.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1beta/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsGroupsSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsGroupsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/hubs/{hubsId}/groups/{groupsId}:testIamPermissions',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.global.hubs.groups.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1beta/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsGroupsTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsGlobalHubsRouteTablesRoutesService(base_api.BaseApiService):
    """Service class for the projects_locations_global_hubs_routeTables_routes resource."""

    _NAME = 'projects_locations_global_hubs_routeTables_routes'

    def __init__(self, client):
      super(NetworkconnectivityV1beta.ProjectsLocationsGlobalHubsRouteTablesRoutesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details about the specified route.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsRouteTablesRoutesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudNetworkconnectivityV1betaRoute) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/hubs/{hubsId}/routeTables/{routeTablesId}/routes/{routesId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.hubs.routeTables.routes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsRouteTablesRoutesGetRequest',
        response_type_name='GoogleCloudNetworkconnectivityV1betaRoute',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists routes in a given route table.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsRouteTablesRoutesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudNetworkconnectivityV1betaListRoutesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/hubs/{hubsId}/routeTables/{routeTablesId}/routes',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.hubs.routeTables.routes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta/{+parent}/routes',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsRouteTablesRoutesListRequest',
        response_type_name='GoogleCloudNetworkconnectivityV1betaListRoutesResponse',
        supports_download=False,
    )

  class ProjectsLocationsGlobalHubsRouteTablesService(base_api.BaseApiService):
    """Service class for the projects_locations_global_hubs_routeTables resource."""

    _NAME = 'projects_locations_global_hubs_routeTables'

    def __init__(self, client):
      super(NetworkconnectivityV1beta.ProjectsLocationsGlobalHubsRouteTablesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details about a Network Connectivity Center route table.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsRouteTablesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudNetworkconnectivityV1betaRouteTable) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/hubs/{hubsId}/routeTables/{routeTablesId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.hubs.routeTables.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsRouteTablesGetRequest',
        response_type_name='GoogleCloudNetworkconnectivityV1betaRouteTable',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists route tables in a given hub.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsRouteTablesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudNetworkconnectivityV1betaListRouteTablesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/hubs/{hubsId}/routeTables',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.hubs.routeTables.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta/{+parent}/routeTables',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsRouteTablesListRequest',
        response_type_name='GoogleCloudNetworkconnectivityV1betaListRouteTablesResponse',
        supports_download=False,
    )

  class ProjectsLocationsGlobalHubsService(base_api.BaseApiService):
    """Service class for the projects_locations_global_hubs resource."""

    _NAME = 'projects_locations_global_hubs'

    def __init__(self, client):
      super(NetworkconnectivityV1beta.ProjectsLocationsGlobalHubsService, self).__init__(client)
      self._upload_configs = {
          }

    def AcceptSpoke(self, request, global_params=None):
      r"""Accepts a proposal to attach a Network Connectivity Center spoke to a hub.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsAcceptSpokeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('AcceptSpoke')
      return self._RunMethod(
          config, request, global_params=global_params)

    AcceptSpoke.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/hubs/{hubsId}:acceptSpoke',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.global.hubs.acceptSpoke',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}:acceptSpoke',
        request_field='googleCloudNetworkconnectivityV1betaAcceptHubSpokeRequest',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsAcceptSpokeRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def AcceptSpokeUpdate(self, request, global_params=None):
      r"""Accepts a proposal to update a Network Connectivity Center spoke in a hub.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsAcceptSpokeUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('AcceptSpokeUpdate')
      return self._RunMethod(
          config, request, global_params=global_params)

    AcceptSpokeUpdate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/hubs/{hubsId}:acceptSpokeUpdate',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.global.hubs.acceptSpokeUpdate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}:acceptSpokeUpdate',
        request_field='googleCloudNetworkconnectivityV1betaAcceptSpokeUpdateRequest',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsAcceptSpokeUpdateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new Network Connectivity Center hub in the specified project.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/hubs',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.global.hubs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['hubId', 'requestId'],
        relative_path='v1beta/{+parent}/hubs',
        request_field='googleCloudNetworkconnectivityV1betaHub',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Network Connectivity Center hub.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/hubs/{hubsId}',
        http_method='DELETE',
        method_id='networkconnectivity.projects.locations.global.hubs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details about a Network Connectivity Center hub.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudNetworkconnectivityV1betaHub) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/hubs/{hubsId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.hubs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsGetRequest',
        response_type_name='GoogleCloudNetworkconnectivityV1betaHub',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/hubs/{hubsId}:getIamPolicy',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.hubs.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1beta/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the Network Connectivity Center hubs associated with a given project.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudNetworkconnectivityV1betaListHubsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/hubs',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.hubs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta/{+parent}/hubs',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsListRequest',
        response_type_name='GoogleCloudNetworkconnectivityV1betaListHubsResponse',
        supports_download=False,
    )

    def ListSpokes(self, request, global_params=None):
      r"""Lists the Network Connectivity Center spokes associated with a specified hub and location. The list includes both spokes that are attached to the hub and spokes that have been proposed but not yet accepted.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsListSpokesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudNetworkconnectivityV1betaListHubSpokesResponse) The response message.
      """
      config = self.GetMethodConfig('ListSpokes')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListSpokes.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/hubs/{hubsId}:listSpokes',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.hubs.listSpokes',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'spokeLocations', 'view'],
        relative_path='v1beta/{+name}:listSpokes',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsListSpokesRequest',
        response_type_name='GoogleCloudNetworkconnectivityV1betaListHubSpokesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the description and/or labels of a Network Connectivity Center hub.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/hubs/{hubsId}',
        http_method='PATCH',
        method_id='networkconnectivity.projects.locations.global.hubs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1beta/{+name}',
        request_field='googleCloudNetworkconnectivityV1betaHub',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def QueryStatus(self, request, global_params=None):
      r"""Query the Private Service Connect propagation status of a Network Connectivity Center hub.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsQueryStatusRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudNetworkconnectivityV1betaQueryHubStatusResponse) The response message.
      """
      config = self.GetMethodConfig('QueryStatus')
      return self._RunMethod(
          config, request, global_params=global_params)

    QueryStatus.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/hubs/{hubsId}:queryStatus',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.hubs.queryStatus',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'groupBy', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta/{+name}:queryStatus',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsQueryStatusRequest',
        response_type_name='GoogleCloudNetworkconnectivityV1betaQueryHubStatusResponse',
        supports_download=False,
    )

    def RejectSpoke(self, request, global_params=None):
      r"""Rejects a Network Connectivity Center spoke from being attached to a hub. If the spoke was previously in the `ACTIVE` state, it transitions to the `INACTIVE` state and is no longer able to connect to other spokes that are attached to the hub.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsRejectSpokeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('RejectSpoke')
      return self._RunMethod(
          config, request, global_params=global_params)

    RejectSpoke.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/hubs/{hubsId}:rejectSpoke',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.global.hubs.rejectSpoke',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}:rejectSpoke',
        request_field='googleCloudNetworkconnectivityV1betaRejectHubSpokeRequest',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsRejectSpokeRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def RejectSpokeUpdate(self, request, global_params=None):
      r"""Rejects a proposal to update a Network Connectivity Center spoke in a hub.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsRejectSpokeUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('RejectSpokeUpdate')
      return self._RunMethod(
          config, request, global_params=global_params)

    RejectSpokeUpdate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/hubs/{hubsId}:rejectSpokeUpdate',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.global.hubs.rejectSpokeUpdate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}:rejectSpokeUpdate',
        request_field='googleCloudNetworkconnectivityV1betaRejectSpokeUpdateRequest',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsRejectSpokeUpdateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/hubs/{hubsId}:setIamPolicy',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.global.hubs.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1beta/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/hubs/{hubsId}:testIamPermissions',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.global.hubs.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1beta/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsGlobalPolicyBasedRoutesService(base_api.BaseApiService):
    """Service class for the projects_locations_global_policyBasedRoutes resource."""

    _NAME = 'projects_locations_global_policyBasedRoutes'

    def __init__(self, client):
      super(NetworkconnectivityV1beta.ProjectsLocationsGlobalPolicyBasedRoutesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new policy-based route in a given project and location.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/policyBasedRoutes',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.global.policyBasedRoutes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['policyBasedRouteId', 'requestId'],
        relative_path='v1beta/{+parent}/policyBasedRoutes',
        request_field='googleCloudNetworkconnectivityV1betaPolicyBasedRoute',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single policy-based route.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/policyBasedRoutes/{policyBasedRoutesId}',
        http_method='DELETE',
        method_id='networkconnectivity.projects.locations.global.policyBasedRoutes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single policy-based route.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudNetworkconnectivityV1betaPolicyBasedRoute) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/policyBasedRoutes/{policyBasedRoutesId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.policyBasedRoutes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesGetRequest',
        response_type_name='GoogleCloudNetworkconnectivityV1betaPolicyBasedRoute',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/policyBasedRoutes/{policyBasedRoutesId}:getIamPolicy',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.policyBasedRoutes.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1beta/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists policy-based routes in a given project and location.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudNetworkconnectivityV1betaListPolicyBasedRoutesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/policyBasedRoutes',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.policyBasedRoutes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta/{+parent}/policyBasedRoutes',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesListRequest',
        response_type_name='GoogleCloudNetworkconnectivityV1betaListPolicyBasedRoutesResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/policyBasedRoutes/{policyBasedRoutesId}:setIamPolicy',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.global.policyBasedRoutes.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1beta/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/global/policyBasedRoutes/{policyBasedRoutesId}:testIamPermissions',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.global.policyBasedRoutes.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1beta/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsGlobalService(base_api.BaseApiService):
    """Service class for the projects_locations_global resource."""

    _NAME = 'projects_locations_global'

    def __init__(self, client):
      super(NetworkconnectivityV1beta.ProjectsLocationsGlobalService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsMulticloudDataTransferConfigsDestinationsService(base_api.BaseApiService):
    """Service class for the projects_locations_multicloudDataTransferConfigs_destinations resource."""

    _NAME = 'projects_locations_multicloudDataTransferConfigs_destinations'

    def __init__(self, client):
      super(NetworkconnectivityV1beta.ProjectsLocationsMulticloudDataTransferConfigsDestinationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a Destination in a given project and location.

      Args:
        request: (NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsDestinationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/multicloudDataTransferConfigs/{multicloudDataTransferConfigsId}/destinations',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.multicloudDataTransferConfigs.destinations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['destinationId', 'requestId'],
        relative_path='v1beta/{+parent}/destinations',
        request_field='googleCloudNetworkconnectivityV1betaDestination',
        request_type_name='NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsDestinationsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Destination.

      Args:
        request: (NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsDestinationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/multicloudDataTransferConfigs/{multicloudDataTransferConfigsId}/destinations/{destinationsId}',
        http_method='DELETE',
        method_id='networkconnectivity.projects.locations.multicloudDataTransferConfigs.destinations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId'],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsDestinationsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Destination.

      Args:
        request: (NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsDestinationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudNetworkconnectivityV1betaDestination) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/multicloudDataTransferConfigs/{multicloudDataTransferConfigsId}/destinations/{destinationsId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.multicloudDataTransferConfigs.destinations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsDestinationsGetRequest',
        response_type_name='GoogleCloudNetworkconnectivityV1betaDestination',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Destinations in a given project and location.

      Args:
        request: (NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsDestinationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudNetworkconnectivityV*****************************) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/multicloudDataTransferConfigs/{multicloudDataTransferConfigsId}/destinations',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.multicloudDataTransferConfigs.destinations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'returnPartialSuccess'],
        relative_path='v1beta/{+parent}/destinations',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsDestinationsListRequest',
        response_type_name='GoogleCloudNetworkconnectivityV*****************************',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a Destination in a given project and location.

      Args:
        request: (NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsDestinationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/multicloudDataTransferConfigs/{multicloudDataTransferConfigsId}/destinations/{destinationsId}',
        http_method='PATCH',
        method_id='networkconnectivity.projects.locations.multicloudDataTransferConfigs.destinations.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1beta/{+name}',
        request_field='googleCloudNetworkconnectivityV1betaDestination',
        request_type_name='NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsDestinationsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsMulticloudDataTransferConfigsService(base_api.BaseApiService):
    """Service class for the projects_locations_multicloudDataTransferConfigs resource."""

    _NAME = 'projects_locations_multicloudDataTransferConfigs'

    def __init__(self, client):
      super(NetworkconnectivityV1beta.ProjectsLocationsMulticloudDataTransferConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a MulticloudDataTransferConfig in a given project and location.

      Args:
        request: (NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/multicloudDataTransferConfigs',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.multicloudDataTransferConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['multicloudDataTransferConfigId', 'requestId'],
        relative_path='v1beta/{+parent}/multicloudDataTransferConfigs',
        request_field='googleCloudNetworkconnectivityV1betaMulticloudDataTransferConfig',
        request_type_name='NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single MulticloudDataTransferConfig.

      Args:
        request: (NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/multicloudDataTransferConfigs/{multicloudDataTransferConfigsId}',
        http_method='DELETE',
        method_id='networkconnectivity.projects.locations.multicloudDataTransferConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId'],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single MulticloudDataTransferConfig.

      Args:
        request: (NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudNetworkconnectivityV1betaMulticloudDataTransferConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/multicloudDataTransferConfigs/{multicloudDataTransferConfigsId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.multicloudDataTransferConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsGetRequest',
        response_type_name='GoogleCloudNetworkconnectivityV1betaMulticloudDataTransferConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists MulticloudDataTransferConfigs in a given project and location.

      Args:
        request: (NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudNetworkconnectivityV1betaListMulticloudDataTransferConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/multicloudDataTransferConfigs',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.multicloudDataTransferConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'returnPartialSuccess'],
        relative_path='v1beta/{+parent}/multicloudDataTransferConfigs',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsListRequest',
        response_type_name='GoogleCloudNetworkconnectivityV1betaListMulticloudDataTransferConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a MulticloudDataTransferConfig in a given project and location.

      Args:
        request: (NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/multicloudDataTransferConfigs/{multicloudDataTransferConfigsId}',
        http_method='PATCH',
        method_id='networkconnectivity.projects.locations.multicloudDataTransferConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1beta/{+name}',
        request_field='googleCloudNetworkconnectivityV1betaMulticloudDataTransferConfig',
        request_type_name='NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsMulticloudDataTransferSupportedServicesService(base_api.BaseApiService):
    """Service class for the projects_locations_multicloudDataTransferSupportedServices resource."""

    _NAME = 'projects_locations_multicloudDataTransferSupportedServices'

    def __init__(self, client):
      super(NetworkconnectivityV1beta.ProjectsLocationsMulticloudDataTransferSupportedServicesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details of a single MulticloudDataTransferSupportedServices.

      Args:
        request: (NetworkconnectivityProjectsLocationsMulticloudDataTransferSupportedServicesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudNetworkconnectivityV1betaMulticloudDataTransferSupportedService) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/multicloudDataTransferSupportedServices/{multicloudDataTransferSupportedServicesId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.multicloudDataTransferSupportedServices.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsMulticloudDataTransferSupportedServicesGetRequest',
        response_type_name='GoogleCloudNetworkconnectivityV1betaMulticloudDataTransferSupportedService',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the supported services for Multicloud Data Transfer. This is a passthrough method.

      Args:
        request: (NetworkconnectivityProjectsLocationsMulticloudDataTransferSupportedServicesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudNetworkconnectivityV1betaListMulticloudDataTransferSupportedServicesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/multicloudDataTransferSupportedServices',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.multicloudDataTransferSupportedServices.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1beta/{+parent}/multicloudDataTransferSupportedServices',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsMulticloudDataTransferSupportedServicesListRequest',
        response_type_name='GoogleCloudNetworkconnectivityV1betaListMulticloudDataTransferSupportedServicesResponse',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(NetworkconnectivityV1beta.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (NetworkconnectivityProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}:cancel',
        request_field='googleLongrunningCancelOperationRequest',
        request_type_name='NetworkconnectivityProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (NetworkconnectivityProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='networkconnectivity.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (NetworkconnectivityProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (NetworkconnectivityProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta/{+name}/operations',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsRegionalEndpointsService(base_api.BaseApiService):
    """Service class for the projects_locations_regionalEndpoints resource."""

    _NAME = 'projects_locations_regionalEndpoints'

    def __init__(self, client):
      super(NetworkconnectivityV1beta.ProjectsLocationsRegionalEndpointsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new RegionalEndpoint in a given project and location.

      Args:
        request: (NetworkconnectivityProjectsLocationsRegionalEndpointsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/regionalEndpoints',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.regionalEndpoints.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['regionalEndpointId', 'requestId'],
        relative_path='v1beta/{+parent}/regionalEndpoints',
        request_field='googleCloudNetworkconnectivityV1betaRegionalEndpoint',
        request_type_name='NetworkconnectivityProjectsLocationsRegionalEndpointsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single RegionalEndpoint.

      Args:
        request: (NetworkconnectivityProjectsLocationsRegionalEndpointsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/regionalEndpoints/{regionalEndpointsId}',
        http_method='DELETE',
        method_id='networkconnectivity.projects.locations.regionalEndpoints.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsRegionalEndpointsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single RegionalEndpoint.

      Args:
        request: (NetworkconnectivityProjectsLocationsRegionalEndpointsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudNetworkconnectivityV1betaRegionalEndpoint) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/regionalEndpoints/{regionalEndpointsId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.regionalEndpoints.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsRegionalEndpointsGetRequest',
        response_type_name='GoogleCloudNetworkconnectivityV1betaRegionalEndpoint',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists RegionalEndpoints in a given project and location.

      Args:
        request: (NetworkconnectivityProjectsLocationsRegionalEndpointsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudNetworkconnectivityV1betaListRegionalEndpointsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/regionalEndpoints',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.regionalEndpoints.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta/{+parent}/regionalEndpoints',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsRegionalEndpointsListRequest',
        response_type_name='GoogleCloudNetworkconnectivityV1betaListRegionalEndpointsResponse',
        supports_download=False,
    )

  class ProjectsLocationsSpokesGatewayAdvertisedRoutesService(base_api.BaseApiService):
    """Service class for the projects_locations_spokes_gatewayAdvertisedRoutes resource."""

    _NAME = 'projects_locations_spokes_gatewayAdvertisedRoutes'

    def __init__(self, client):
      super(NetworkconnectivityV1beta.ProjectsLocationsSpokesGatewayAdvertisedRoutesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a GatewayAdvertisedRoute.

      Args:
        request: (NetworkconnectivityProjectsLocationsSpokesGatewayAdvertisedRoutesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}/gatewayAdvertisedRoutes',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.spokes.gatewayAdvertisedRoutes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['gatewayAdvertisedRouteId', 'requestId'],
        relative_path='v1beta/{+parent}/gatewayAdvertisedRoutes',
        request_field='googleCloudNetworkconnectivityV1betaGatewayAdvertisedRoute',
        request_type_name='NetworkconnectivityProjectsLocationsSpokesGatewayAdvertisedRoutesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a GatewayAdvertisedRoute.

      Args:
        request: (NetworkconnectivityProjectsLocationsSpokesGatewayAdvertisedRoutesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}/gatewayAdvertisedRoutes/{gatewayAdvertisedRoutesId}',
        http_method='DELETE',
        method_id='networkconnectivity.projects.locations.spokes.gatewayAdvertisedRoutes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsSpokesGatewayAdvertisedRoutesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get a GatewayAdvertisedRoute.

      Args:
        request: (NetworkconnectivityProjectsLocationsSpokesGatewayAdvertisedRoutesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudNetworkconnectivityV1betaGatewayAdvertisedRoute) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}/gatewayAdvertisedRoutes/{gatewayAdvertisedRoutesId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.spokes.gatewayAdvertisedRoutes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsSpokesGatewayAdvertisedRoutesGetRequest',
        response_type_name='GoogleCloudNetworkconnectivityV1betaGatewayAdvertisedRoute',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List GatewayAdvertisedRoutes.

      Args:
        request: (NetworkconnectivityProjectsLocationsSpokesGatewayAdvertisedRoutesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudNetworkconnectivityV1betaListGatewayAdvertisedRoutesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}/gatewayAdvertisedRoutes',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.spokes.gatewayAdvertisedRoutes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta/{+parent}/gatewayAdvertisedRoutes',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsSpokesGatewayAdvertisedRoutesListRequest',
        response_type_name='GoogleCloudNetworkconnectivityV1betaListGatewayAdvertisedRoutesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a GatewayAdvertisedRoute.

      Args:
        request: (NetworkconnectivityProjectsLocationsSpokesGatewayAdvertisedRoutesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}/gatewayAdvertisedRoutes/{gatewayAdvertisedRoutesId}',
        http_method='PATCH',
        method_id='networkconnectivity.projects.locations.spokes.gatewayAdvertisedRoutes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1beta/{+name}',
        request_field='googleCloudNetworkconnectivityV1betaGatewayAdvertisedRoute',
        request_type_name='NetworkconnectivityProjectsLocationsSpokesGatewayAdvertisedRoutesPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsSpokesService(base_api.BaseApiService):
    """Service class for the projects_locations_spokes resource."""

    _NAME = 'projects_locations_spokes'

    def __init__(self, client):
      super(NetworkconnectivityV1beta.ProjectsLocationsSpokesService, self).__init__(client)
      self._upload_configs = {
          }

    def Activate(self, request, global_params=None):
      r"""Activates a Network Connectivity Center spoke. By activating a spoke, you permit connectivity between it and other spokes that are attached to the same hub.

      Args:
        request: (NetworkconnectivityProjectsLocationsSpokesActivateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Activate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Activate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}:activate',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.spokes.activate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}:activate',
        request_field='googleCloudNetworkconnectivityV1betaActivateSpokeRequest',
        request_type_name='NetworkconnectivityProjectsLocationsSpokesActivateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a Network Connectivity Center spoke.

      Args:
        request: (NetworkconnectivityProjectsLocationsSpokesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/spokes',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.spokes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'spokeId'],
        relative_path='v1beta/{+parent}/spokes',
        request_field='googleCloudNetworkconnectivityV1betaSpoke',
        request_type_name='NetworkconnectivityProjectsLocationsSpokesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Deactivate(self, request, global_params=None):
      r"""Deactivates a Network Connectivity Center spoke. When you deactivate a spoke, it can't connect to other spokes that are attached to the same hub.

      Args:
        request: (NetworkconnectivityProjectsLocationsSpokesDeactivateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Deactivate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Deactivate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}:deactivate',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.spokes.deactivate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}:deactivate',
        request_field='googleCloudNetworkconnectivityV1betaDeactivateSpokeRequest',
        request_type_name='NetworkconnectivityProjectsLocationsSpokesDeactivateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Network Connectivity Center spoke.

      Args:
        request: (NetworkconnectivityProjectsLocationsSpokesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}',
        http_method='DELETE',
        method_id='networkconnectivity.projects.locations.spokes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsSpokesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details about a Network Connectivity Center spoke.

      Args:
        request: (NetworkconnectivityProjectsLocationsSpokesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudNetworkconnectivityV1betaSpoke) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.spokes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsSpokesGetRequest',
        response_type_name='GoogleCloudNetworkconnectivityV1betaSpoke',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworkconnectivityProjectsLocationsSpokesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}:getIamPolicy',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.spokes.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1beta/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsSpokesGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the Network Connectivity Center spokes in a specified project and location.

      Args:
        request: (NetworkconnectivityProjectsLocationsSpokesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudNetworkconnectivityV1betaListSpokesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/spokes',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.spokes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1beta/{+parent}/spokes',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsSpokesListRequest',
        response_type_name='GoogleCloudNetworkconnectivityV1betaListSpokesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a Network Connectivity Center spoke.

      Args:
        request: (NetworkconnectivityProjectsLocationsSpokesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}',
        http_method='PATCH',
        method_id='networkconnectivity.projects.locations.spokes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1beta/{+name}',
        request_field='googleCloudNetworkconnectivityV1betaSpoke',
        request_type_name='NetworkconnectivityProjectsLocationsSpokesPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworkconnectivityProjectsLocationsSpokesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}:setIamPolicy',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.spokes.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1beta/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='NetworkconnectivityProjectsLocationsSpokesSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkconnectivityProjectsLocationsSpokesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}:testIamPermissions',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.spokes.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1beta/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='NetworkconnectivityProjectsLocationsSpokesTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(NetworkconnectivityV1beta.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (NetworkconnectivityProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudLocationLocation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGetRequest',
        response_type_name='GoogleCloudLocationLocation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (NetworkconnectivityProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudLocationListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/locations',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1beta/{+name}/locations',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsListRequest',
        response_type_name='GoogleCloudLocationListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(NetworkconnectivityV1beta.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
