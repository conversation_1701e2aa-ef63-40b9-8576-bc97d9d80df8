"""Generated message classes for networkconnectivity version v1alpha1.

This API enables connectivity with and between Google Cloud resources.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'networkconnectivity'


class AllocationOptions(_messages.Message):
  r"""Range auto-allocation options, to be optionally used when CIDR block is
  not explicitly set.

  Enums:
    AllocationStrategyValueValuesEnum: Optional. Allocation strategy. Not
      setting this field when the allocation is requested means an
      implementation defined strategy is used.

  Fields:
    allocationStrategy: Optional. Allocation strategy. Not setting this field
      when the allocation is requested means an implementation defined
      strategy is used.
    firstAvailableRangesLookupSize: Optional. This field must be set only when
      allocation_strategy is set to RANDOM_FIRST_N_AVAILABLE. The value should
      be the maximum expected parallelism of range creation requests issued to
      the same space of peered netwroks.
  """

  class AllocationStrategyValueValuesEnum(_messages.Enum):
    r"""Optional. Allocation strategy. Not setting this field when the
    allocation is requested means an implementation defined strategy is used.

    Values:
      ALLOCATION_STRATEGY_UNSPECIFIED: Unspecified strategy must be used when
        the range is specified explicitly using ip_cidr_range field.
        Othherwise unspefified means using the default strategy.
      RANDOM: Random strategy, the legacy algorithm, used for backwards
        compatibility. This allocation strategy remains efficient in the case
        of concurrent allocation requests in the same peered network space and
        doesn't require providing the level of concurrency in an explicit
        parameter, but it is prone to fragmenting available address space.
      FIRST_AVAILABLE: Pick the first available address range. This strategy
        is deterministic and the result is easy to predict.
      RANDOM_FIRST_N_AVAILABLE: Pick an arbitrary range out of the first N
        available ones. The N will be set in the
        first_available_ranges_lookup_size field. This strategy should be used
        when concurrent allocation requests are made in the same space of
        peered networks while the fragmentation of the addrress space is
        reduced.
      FIRST_SMALLEST_FITTING: Pick the smallest but fitting available range.
        This deterministic strategy minimizes fragmentation of the address
        space.
    """
    ALLOCATION_STRATEGY_UNSPECIFIED = 0
    RANDOM = 1
    FIRST_AVAILABLE = 2
    RANDOM_FIRST_N_AVAILABLE = 3
    FIRST_SMALLEST_FITTING = 4

  allocationStrategy = _messages.EnumField('AllocationStrategyValueValuesEnum', 1)
  firstAvailableRangesLookupSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GoogleLongrunningCancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class GoogleLongrunningListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('GoogleLongrunningOperation', 2, repeated=True)


class GoogleLongrunningOperation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('GoogleRpcStatus', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class GoogleRpcStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class Hub(_messages.Message):
  r"""Network Connectivity Center is a hub-and-spoke abstraction for network
  connectivity management in Google Cloud. It reduces operational complexity
  through a simple, centralized connectivity management model. Following is
  the resource message of a hub.

  Enums:
    StateValueValuesEnum: Output only. The current lifecycle state of this
      Hub.

  Messages:
    LabelsValue: User-defined labels.

  Fields:
    createTime: Time when the Hub was created.
    description: Short description of the hub resource.
    labels: User-defined labels.
    name: Immutable. The name of a Hub resource.
    spokes: Output only. A list of the URIs of all attached spokes. This field
      is deprecated and will not be included in future API versions. Call
      ListSpokes on each region instead.
    state: Output only. The current lifecycle state of this Hub.
    uniqueId: Output only. Google-generated UUID for this resource. This is
      unique across all Hub resources. If a Hub resource is deleted and
      another with the same name is created, it gets a different unique_id.
    updateTime: Time when the Hub was updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current lifecycle state of this Hub.

    Values:
      STATE_UNSPECIFIED: No state information available
      CREATING: The resource's create operation is in progress
      ACTIVE: The resource is active
      DELETING: The resource's Delete operation is in progress
      UPDATING: The resource's Update operation is in progress
      FAILED: The resource is in an undefined state due to resource creation
        or deletion failure. You can try to delete the resource later or
        contact support for help.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    UPDATING = 4
    FAILED = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""User-defined labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  spokes = _messages.StringField(5, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  uniqueId = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class InternalRange(_messages.Message):
  r"""The internal range resource for IPAM operations within a VPC network.
  Used to represent a private address range along with behavioral
  characteristics of that range (its usage and peering behavior). Networking
  resources can link to this range if they are created as belonging to it.

  Enums:
    OverlapsValueListEntryValuesEnum:
    PeeringValueValuesEnum: The type of peering set for this internal range.
    UsageValueValuesEnum: The type of usage set for this internal range.

  Messages:
    LabelsValue: User-defined labels.

  Fields:
    allocationOptions: Optional. Range auto-allocation options, may be set
      only when auto-allocation is selected by not setting ip_cidr_range (and
      setting prefix_length).
    createTime: Time when the internal range was created.
    description: A description of this resource.
    excludeCidrRanges: Optional. ExcludeCidrRanges flag. Specifies a set of
      CIDR blocks that allows exclusion of particular CIDR ranges from the
      auto-allocation process, without having to reserve these blocks
    immutable: Optional. Immutable ranges cannot have their fields modified,
      except for labels and description.
    ipCidrRange: IP range that this internal range defines. NOTE: IPv6 ranges
      are limited to usage=EXTERNAL_TO_VPC and peering=FOR_SELF. NOTE: For
      IPv6 Ranges this field is compulsory, i.e. the address range must be
      specified explicitly.
    labels: User-defined labels.
    migration: Optional. Must be present if usage is set to FOR_MIGRATION.
    name: Immutable. The name of an internal range. Format:
      projects/{project}/locations/{location}/internalRanges/{internal_range}
      See: https://google.aip.dev/122#fields-representing-resource-names
    network: The URL or resource ID of the network in which to reserve the
      internal range. The network cannot be deleted if there are any reserved
      internal ranges referring to it. Legacy networks are not supported. For
      example: https://www.googleapis.com/compute/v1/projects/{project}/locati
      ons/global/networks/{network}
      projects/{project}/locations/global/networks/{network} {network}
    overlaps: Optional. Types of resources that are allowed to overlap with
      the current internal range.
    peering: The type of peering set for this internal range.
    prefixLength: An alternative to ip_cidr_range. Can be set when trying to
      create an IPv4 reservation that automatically finds a free range of the
      given size. If both ip_cidr_range and prefix_length are set, there is an
      error if the range sizes do not match. Can also be used during updates
      to change the range size. NOTE: For IPv6 this field only works if
      ip_cidr_range is set as well, and both fields must match. In other
      words, with IPv6 this field only works as a redundant parameter.
    targetCidrRange: Optional. Can be set to narrow down or pick a different
      address space while searching for a free range. If not set, defaults to
      the "10.0.0.0/8" address space. This can be used to search in other
      rfc-1918 address spaces like "**********/12" and "***********/16" or
      non-rfc-1918 address spaces used in the VPC.
    updateTime: Time when the internal range was updated.
    usage: The type of usage set for this internal range.
    users: Output only. The list of resources that refer to this internal
      range. Resources that use the internal range for their range allocation
      are referred to as users of the range. Other resources mark themselves
      as users while doing so by creating a reference to this internal range.
      Having a user, based on this reference, prevents deletion of the
      internal range that is referred to. Can be empty.
  """

  class OverlapsValueListEntryValuesEnum(_messages.Enum):
    r"""OverlapsValueListEntryValuesEnum enum type.

    Values:
      OVERLAP_UNSPECIFIED: No overlap overrides.
      OVERLAP_ROUTE_RANGE: Allow creation of static routes more specific than
        the current internal range.
      OVERLAP_EXISTING_SUBNET_RANGE: Allow creation of internal ranges that
        overlap with existing subnets.
    """
    OVERLAP_UNSPECIFIED = 0
    OVERLAP_ROUTE_RANGE = 1
    OVERLAP_EXISTING_SUBNET_RANGE = 2

  class PeeringValueValuesEnum(_messages.Enum):
    r"""The type of peering set for this internal range.

    Values:
      PEERING_UNSPECIFIED: If Peering is left unspecified in
        CreateInternalRange or UpdateInternalRange, it will be defaulted to
        FOR_SELF.
      FOR_SELF: This is the default behavior and represents the case that this
        internal range is intended to be used in the VPC in which it is
        created and is accessible from its peers. This implies that peers or
        peers-of-peers cannot use this range.
      FOR_PEER: This behavior can be set when the internal range is being
        reserved for usage by the peers. This means that no resource within
        the VPC in which it is being created can use this to associate with a
        VPC resource, but one of the peers can. This represents donating a
        range for peers to use.
      NOT_SHARED: This behavior can be set when the internal range is being
        reserved for usage by the VPC in which it is created but not shared
        with the peers. In a sense it is local to the VPC. This can be used to
        create internal ranges for various purposes like
        HTTP_INTERNAL_LOAD_BALANCER or for Interconnect routes that are not
        shared with peers. This also implies that peers cannot use this range
        in a way that is visible to this VPC, but can re-use this range as
        long as it is NOT_SHARED from the peer VPC, too.
    """
    PEERING_UNSPECIFIED = 0
    FOR_SELF = 1
    FOR_PEER = 2
    NOT_SHARED = 3

  class UsageValueValuesEnum(_messages.Enum):
    r"""The type of usage set for this internal range.

    Values:
      USAGE_UNSPECIFIED: Unspecified usage is allowed in calls which identify
        the resource by other fields and do not need Usage set to complete.
        These are, i.e.: GetInternalRange and DeleteInternalRange. Usage needs
        to be specified explicitly in CreateInternalRange or
        UpdateInternalRange calls.
      FOR_VPC: A VPC resource can use the reserved CIDR block by associating
        it with the internal range resource if usage is set to FOR_VPC.
      EXTERNAL_TO_VPC: Ranges created with EXTERNAL_TO_VPC cannot be
        associated with VPC resources and are meant to block out address
        ranges for various use cases such as usage on-premises, with dynamic
        route announcements via Interconnect.
      FOR_MIGRATION: Ranges created FOR_MIGRATION can be used to lock a CIDR
        range between a source and target subnet. If usage is set to
        FOR_MIGRATION the peering value has to be set to FOR_SELF or default
        to FOR_SELF when unset.
    """
    USAGE_UNSPECIFIED = 0
    FOR_VPC = 1
    EXTERNAL_TO_VPC = 2
    FOR_MIGRATION = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""User-defined labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  allocationOptions = _messages.MessageField('AllocationOptions', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  excludeCidrRanges = _messages.StringField(4, repeated=True)
  immutable = _messages.BooleanField(5)
  ipCidrRange = _messages.StringField(6)
  labels = _messages.MessageField('LabelsValue', 7)
  migration = _messages.MessageField('Migration', 8)
  name = _messages.StringField(9)
  network = _messages.StringField(10)
  overlaps = _messages.EnumField('OverlapsValueListEntryValuesEnum', 11, repeated=True)
  peering = _messages.EnumField('PeeringValueValuesEnum', 12)
  prefixLength = _messages.IntegerField(13, variant=_messages.Variant.INT32)
  targetCidrRange = _messages.StringField(14, repeated=True)
  updateTime = _messages.StringField(15)
  usage = _messages.EnumField('UsageValueValuesEnum', 16)
  users = _messages.StringField(17, repeated=True)


class ListHubsResponse(_messages.Message):
  r"""Response for HubService.ListHubs method.

  Fields:
    hubs: Hubs to be returned.
    nextPageToken: The next pagination token in the List response. It should
      be used as page_token for the following request. An empty value means no
      more result.
    unreachable: Locations that could not be reached.
  """

  hubs = _messages.MessageField('Hub', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListInternalRangesResponse(_messages.Message):
  r"""Response for InternalRange.ListInternalRanges

  Fields:
    internalRanges: Internal range to be returned.
    nextPageToken: The next pagination token in the List response. It should
      be used as page_token for the following request. An empty value means no
      more result.
    unreachable: Locations that could not be reached.
  """

  internalRanges = _messages.MessageField('InternalRange', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListSpokesResponse(_messages.Message):
  r"""The response for HubService.ListSpokes.

  Fields:
    nextPageToken: The next pagination token in the List response. It should
      be used as page_token for the following request. An empty value means no
      more result.
    spokes: Spokes to be returned.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  spokes = _messages.MessageField('Spoke', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class Migration(_messages.Message):
  r"""Specification for migration with source and target resource names.

  Fields:
    source: Immutable. Resource path as an URI of the source resource, for
      example a subnet. The project for the source resource should match the
      project for the InternalRange. An example:
      /projects/{project}/regions/{region}/subnetworks/{subnet}
    target: Immutable. Resource path of the target resource. The target
      project can be different, as in the cases when migrating to peer
      networks. For example:
      /projects/{project}/regions/{region}/subnetworks/{subnet}
  """

  source = _messages.StringField(1)
  target = _messages.StringField(2)


class NetworkconnectivityProjectsLocationsGetRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class NetworkconnectivityProjectsLocationsGlobalHubsCreateRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalHubsCreateRequest object.

  Fields:
    hub: A Hub resource to be passed as the request body.
    hubId: Optional. Unique id for the Hub to create.
    parent: Required. The parent resource's name of the Hub.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  hub = _messages.MessageField('Hub', 1)
  hubId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworkconnectivityProjectsLocationsGlobalHubsDeleteRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalHubsDeleteRequest object.

  Fields:
    name: Required. The name of the Hub to delete.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworkconnectivityProjectsLocationsGlobalHubsGetIamPolicyRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalHubsGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NetworkconnectivityProjectsLocationsGlobalHubsGetRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalHubsGetRequest object.

  Fields:
    name: Required. Name of the Hub resource to get.
  """

  name = _messages.StringField(1, required=True)


class NetworkconnectivityProjectsLocationsGlobalHubsListRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalHubsListRequest object.

  Fields:
    filter: A filter expression that filters the results listed in the
      response.
    orderBy: Sort the results by a certain order.
    pageSize: The maximum number of results per page that should be returned.
    pageToken: The page token.
    parent: Required. The parent resource's name.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkconnectivityProjectsLocationsGlobalHubsPatchRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalHubsPatchRequest object.

  Fields:
    hub: A Hub resource to be passed as the request body.
    name: Immutable. The name of a Hub resource.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the Hub resource by the update. The fields specified in
      the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
  """

  hub = _messages.MessageField('Hub', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworkconnectivityProjectsLocationsGlobalHubsSetIamPolicyRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalHubsSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class NetworkconnectivityProjectsLocationsGlobalHubsTestIamPermissionsRequest(_messages.Message):
  r"""A
  NetworkconnectivityProjectsLocationsGlobalHubsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class NetworkconnectivityProjectsLocationsInternalRangesCreateRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsInternalRangesCreateRequest
  object.

  Fields:
    internalRange: A InternalRange resource to be passed as the request body.
    internalRangeId: Optional. Resource ID (i.e. 'foo' in
      '[...]/projects/p/locations/l/internalRanges/foo') See
      https://google.aip.dev/122#resource-id-segments Unique per location.
    parent: Required. The parent resource's name of the InternalRange.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if the original operation with
      the same request ID was received, and if so, will ignore the second
      request. This prevents clients from accidentally creating duplicate
      commitments. The request ID must be a valid UUID with the exception that
      zero UUID is not supported (00000000-0000-0000-0000-000000000000).
  """

  internalRange = _messages.MessageField('InternalRange', 1)
  internalRangeId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworkconnectivityProjectsLocationsInternalRangesDeleteRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsInternalRangesDeleteRequest
  object.

  Fields:
    name: Required. The name of the InternalRange to delete.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if the original operation with
      the same request ID was received, and if so, will ignore the second
      request. This prevents clients from accidentally creating duplicate
      commitments. The request ID must be a valid UUID with the exception that
      zero UUID is not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworkconnectivityProjectsLocationsInternalRangesGetIamPolicyRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsInternalRangesGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NetworkconnectivityProjectsLocationsInternalRangesGetRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsInternalRangesGetRequest object.

  Fields:
    name: Required. Name of the InternalRange to get.
  """

  name = _messages.StringField(1, required=True)


class NetworkconnectivityProjectsLocationsInternalRangesListRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsInternalRangesListRequest object.

  Fields:
    filter: A filter expression that filters the results listed in the
      response.
    orderBy: Sort the results by a certain order.
    pageSize: The maximum number of results per page that should be returned.
    pageToken: The page token.
    parent: Required. The parent resource's name.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkconnectivityProjectsLocationsInternalRangesPatchRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsInternalRangesPatchRequest object.

  Fields:
    internalRange: A InternalRange resource to be passed as the request body.
    name: Immutable. The name of an internal range. Format:
      projects/{project}/locations/{location}/internalRanges/{internal_range}
      See: https://google.aip.dev/122#fields-representing-resource-names
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if the original operation with
      the same request ID was received, and if so, will ignore the second
      request. This prevents clients from accidentally creating duplicate
      commitments. The request ID must be a valid UUID with the exception that
      zero UUID is not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the internal range resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  internalRange = _messages.MessageField('InternalRange', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworkconnectivityProjectsLocationsInternalRangesSetIamPolicyRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsInternalRangesSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class NetworkconnectivityProjectsLocationsInternalRangesTestIamPermissionsRequest(_messages.Message):
  r"""A
  NetworkconnectivityProjectsLocationsInternalRangesTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class NetworkconnectivityProjectsLocationsListRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class NetworkconnectivityProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsOperationsCancelRequest object.

  Fields:
    googleLongrunningCancelOperationRequest: A
      GoogleLongrunningCancelOperationRequest resource to be passed as the
      request body.
    name: The name of the operation resource to be cancelled.
  """

  googleLongrunningCancelOperationRequest = _messages.MessageField('GoogleLongrunningCancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class NetworkconnectivityProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class NetworkconnectivityProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class NetworkconnectivityProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class NetworkconnectivityProjectsLocationsSpokesCreateRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsSpokesCreateRequest object.

  Fields:
    parent: Required. The parent's resource name of the Spoke.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    spoke: A Spoke resource to be passed as the request body.
    spokeId: Optional. Unique id for the Spoke to create.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  spoke = _messages.MessageField('Spoke', 3)
  spokeId = _messages.StringField(4)


class NetworkconnectivityProjectsLocationsSpokesDeleteRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsSpokesDeleteRequest object.

  Fields:
    name: Required. The name of the Spoke to delete.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworkconnectivityProjectsLocationsSpokesGetIamPolicyRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsSpokesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NetworkconnectivityProjectsLocationsSpokesGetRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsSpokesGetRequest object.

  Fields:
    name: Required. The name of Spoke resource.
  """

  name = _messages.StringField(1, required=True)


class NetworkconnectivityProjectsLocationsSpokesListRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsSpokesListRequest object.

  Fields:
    filter: A filter expression that filters the results listed in the
      response.
    orderBy: Sort the results by a certain order.
    pageSize: The maximum number of results per page that should be returned.
    pageToken: The page token.
    parent: Required. The parent's resource name.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkconnectivityProjectsLocationsSpokesPatchRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsSpokesPatchRequest object.

  Fields:
    name: Immutable. The name of a Spoke resource.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    spoke: A Spoke resource to be passed as the request body.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the Spoke resource by the update. The fields specified in
      the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  spoke = _messages.MessageField('Spoke', 3)
  updateMask = _messages.StringField(4)


class NetworkconnectivityProjectsLocationsSpokesSetIamPolicyRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsSpokesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class NetworkconnectivityProjectsLocationsSpokesTestIamPermissionsRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsSpokesTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class RouterApplianceInstance(_messages.Message):
  r"""RouterAppliance represents a Router appliance which is specified by a VM
  URI and a NIC address.

  Fields:
    ipAddress: The IP address of the network interface to use for peering.
    networkInterface: A string attribute.
    virtualMachine: The URI of the virtual machine resource
  """

  ipAddress = _messages.StringField(1)
  networkInterface = _messages.StringField(2)
  virtualMachine = _messages.StringField(3)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class Spoke(_messages.Message):
  r"""A Spoke is an abstraction of a network attachment being attached to a
  Hub. A Spoke can be underlying a VPN tunnel, a VLAN (interconnect)
  attachment, a Router appliance, etc.

  Enums:
    StateValueValuesEnum: Output only. The current lifecycle state of this
      Hub.

  Messages:
    LabelsValue: User-defined labels.

  Fields:
    createTime: The time when the Spoke was created.
    description: Short description of the spoke resource
    hub: The resource URL of the hub resource that the spoke is attached to
    labels: User-defined labels.
    linkedInterconnectAttachments: The URIs of linked interconnect attachment
      resources
    linkedRouterApplianceInstances: The URIs of linked Router appliance
      resources
    linkedVpnTunnels: The URIs of linked VPN tunnel resources
    name: Immutable. The name of a Spoke resource.
    state: Output only. The current lifecycle state of this Hub.
    uniqueId: Output only. Google-generated UUID for this resource. This is
      unique across all Spoke resources. If a Spoke resource is deleted and
      another with the same name is created, it gets a different unique_id.
    updateTime: The time when the Spoke was updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current lifecycle state of this Hub.

    Values:
      STATE_UNSPECIFIED: No state information available
      CREATING: The resource's create operation is in progress
      ACTIVE: The resource is active
      DELETING: The resource's Delete operation is in progress
      UPDATING: The resource's Update operation is in progress
      FAILED: The resource is in an undefined state due to resource creation
        or deletion failure. You can try to delete the resource later or
        contact support for help.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    UPDATING = 4
    FAILED = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""User-defined labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  hub = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  linkedInterconnectAttachments = _messages.StringField(5, repeated=True)
  linkedRouterApplianceInstances = _messages.MessageField('RouterApplianceInstance', 6, repeated=True)
  linkedVpnTunnels = _messages.StringField(7, repeated=True)
  name = _messages.StringField(8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  uniqueId = _messages.StringField(10)
  updateTime = _messages.StringField(11)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    NetworkconnectivityProjectsLocationsGlobalHubsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    NetworkconnectivityProjectsLocationsInternalRangesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    NetworkconnectivityProjectsLocationsSpokesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
