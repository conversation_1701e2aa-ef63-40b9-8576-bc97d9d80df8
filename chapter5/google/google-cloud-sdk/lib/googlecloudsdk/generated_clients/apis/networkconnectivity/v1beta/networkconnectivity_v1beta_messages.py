"""Generated message classes for networkconnectivity version v1beta.

This API enables connectivity with and between Google Cloud resources.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'networkconnectivity'


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class GoogleCloudLocationListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('GoogleCloudLocationLocation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudLocationLocation(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class GoogleCloudNetworkconnectivityV1betaAcceptHubSpokeRequest(_messages.Message):
  r"""The request for HubService.AcceptHubSpoke.

  Fields:
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check to see
      whether the original operation was received. If it was, the server
      ignores the second request. This behavior prevents clients from
      mistakenly creating duplicate commitments. The request ID must be a
      valid UUID, with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    spokeUri: Required. The URI of the spoke to accept into the hub.
  """

  requestId = _messages.StringField(1)
  spokeUri = _messages.StringField(2)


class GoogleCloudNetworkconnectivityV1betaAcceptHubSpokeResponse(_messages.Message):
  r"""The response for HubService.AcceptHubSpoke.

  Fields:
    spoke: The spoke that was operated on.
  """

  spoke = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaSpoke', 1)


class GoogleCloudNetworkconnectivityV1betaAcceptSpokeUpdateRequest(_messages.Message):
  r"""The request for HubService.AcceptSpokeUpdate.

  Fields:
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check to see
      whether the original operation was received. If it was, the server
      ignores the second request. This behavior prevents clients from
      mistakenly creating duplicate commitments. The request ID must be a
      valid UUID, with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    spokeEtag: Required. The etag of the spoke to accept update.
    spokeUri: Required. The URI of the spoke to accept update.
  """

  requestId = _messages.StringField(1)
  spokeEtag = _messages.StringField(2)
  spokeUri = _messages.StringField(3)


class GoogleCloudNetworkconnectivityV1betaActivateSpokeRequest(_messages.Message):
  r"""The request for HubService.ActivateSpoke.

  Fields:
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check to see
      whether the original operation was received. If it was, the server
      ignores the second request. This behavior prevents clients from
      mistakenly creating duplicate commitments. The request ID must be a
      valid UUID, with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  requestId = _messages.StringField(1)


class GoogleCloudNetworkconnectivityV1betaAutoAccept(_messages.Message):
  r"""The auto-accept setting for a group controls whether proposed spokes are
  automatically attached to the hub. If auto-accept is enabled, the spoke
  immediately is attached to the hub and becomes part of the group. In this
  case, the new spoke is in the ACTIVE state. If auto-accept is disabled, the
  spoke goes to the INACTIVE state, and it must be reviewed and accepted by a
  hub administrator.

  Fields:
    autoAcceptProjects: Optional. A list of project ids or project numbers for
      which you want to enable auto-accept. The auto-accept setting is applied
      to spokes being created or updated in these projects.
  """

  autoAcceptProjects = _messages.StringField(1, repeated=True)


class GoogleCloudNetworkconnectivityV1betaDeactivateSpokeRequest(_messages.Message):
  r"""The request for HubService.DeactivateSpoke.

  Fields:
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check to see
      whether the original operation was received. If it was, the server
      ignores the second request. This behavior prevents clients from
      mistakenly creating duplicate commitments. The request ID must be a
      valid UUID, with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  requestId = _messages.StringField(1)


class GoogleCloudNetworkconnectivityV1betaDestination(_messages.Message):
  r"""The Destination resource.

  Messages:
    LabelsValue: Optional. User-defined labels.

  Fields:
    createTime: Output only. Time when the Destination was created.
    description: Optional. An optional field to provide a description of this
      resource.
    endpoints: Required. Unordered list. The list of Endpoints configured for
      the IP Prefix.
    etag: The etag is computed by the server, and may be sent on update and
      delete requests to ensure the client has an up-to-date value before
      proceeding.
    ipPrefix: Required. Immutable. Remote IP Prefix in the remote CSP, where
      the customer's workload is located
    labels: Optional. User-defined labels.
    name: Identifier. The name of the Destination resource. Format: `projects/
      {project}/locations/{location}/multicloudDataTransferConfigs/{multicloud
      _data_transfer_config}/destinations/{destination}`.
    stateTimeline: Output only. The timeline of the expected Destination
      states or the current rest state. If a state change is expected, the
      value will be the list of ADDING, DELETING or SUSPENDING statesdepending
      on the actions taken. Example: "state_timeline": { "states": [ {
      "state": "ADDING", // The time when the Destination will be activated.
      "effective_time": "2024-12-01T08:00:00Z" }, { "state": "SUSPENDING", //
      The time when the Destination will be suspended. "effective_time":
      "2024-12-01T20:00:00Z" } ] }
    uid: Output only. The Google-generated UUID for the destination. This
      value is unique across all destination resources. If a destination is
      deleted and another with the same name is created, the new destination
      is assigned a different uid.
    updateTime: Output only. Time when the Destination was updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. User-defined labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  endpoints = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaDestinationEndpoint', 3, repeated=True)
  etag = _messages.StringField(4)
  ipPrefix = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  stateTimeline = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaStateTimeline', 8)
  uid = _messages.StringField(9)
  updateTime = _messages.StringField(10)


class GoogleCloudNetworkconnectivityV1betaDestinationEndpoint(_messages.Message):
  r"""The metadata for a DestinationEndpoint.

  Enums:
    StateValueValuesEnum: Output only. The state of the Endpoint.

  Fields:
    asn: Required. The ASN of the remote IP Prefix.
    csp: Required. The name of the CSP of the remote IP Prefix.
    state: Output only. The state of the Endpoint.
    updateTime: Output only. Time when the DestinationEndpoint was updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the Endpoint.

    Values:
      STATE_UNSPECIFIED: An invalid state as the default case.
      VALID: The Endpoint is valid.
      INVALID: The Endpoint is invalid.
    """
    STATE_UNSPECIFIED = 0
    VALID = 1
    INVALID = 2

  asn = _messages.IntegerField(1)
  csp = _messages.StringField(2)
  state = _messages.EnumField('StateValueValuesEnum', 3)
  updateTime = _messages.StringField(4)


class GoogleCloudNetworkconnectivityV1betaExportPscConfig(_messages.Message):
  r"""Configuration for more granular control of Private Service Connect
  connection propagation. This allows enabling or disabling connection
  propagation for specific types of Private Service Connect endpoints.

  Fields:
    globalGoogleApis: Optional. Controls whether Private Service Connect
      endpoints for global Google APIs should be propagated. The default value
      is false.
    publishedServicesAndRegionalGoogleApis: Optional. Controls whether Private
      Service Connect endpoints for regional ILBs and regional Google APIs
      should be propagated. Default value is true if export_psc is true.
      Otherwise, the default value is false.
  """

  globalGoogleApis = _messages.BooleanField(1)
  publishedServicesAndRegionalGoogleApis = _messages.BooleanField(2)


class GoogleCloudNetworkconnectivityV1betaFilter(_messages.Message):
  r"""Filter matches L4 traffic.

  Enums:
    ProtocolVersionValueValuesEnum: Required. Internet protocol versions this
      policy-based route applies to. IPV4 and IPV6 is supported.

  Fields:
    destRange: Optional. The destination IP range of outgoing packets that
      this policy-based route applies to. Default is "0.0.0.0/0" if protocol
      version is IPv4 and "::/0" if protocol version is IPv6.
    ipProtocol: Optional. The IP protocol that this policy-based route applies
      to. Valid values are 'TCP', 'UDP', and 'ALL'. Default is 'ALL'.
    protocolVersion: Required. Internet protocol versions this policy-based
      route applies to. IPV4 and IPV6 is supported.
    srcRange: Optional. The source IP range of outgoing packets that this
      policy-based route applies to. Default is "0.0.0.0/0" if protocol
      version is IPv4 and "::/0" if protocol version is IPv6.
  """

  class ProtocolVersionValueValuesEnum(_messages.Enum):
    r"""Required. Internet protocol versions this policy-based route applies
    to. IPV4 and IPV6 is supported.

    Values:
      PROTOCOL_VERSION_UNSPECIFIED: Default value.
      IPV4: The PBR is for IPv4 internet protocol traffic.
      IPV6: The PBR is for IPv6 internet protocol traffic.
    """
    PROTOCOL_VERSION_UNSPECIFIED = 0
    IPV4 = 1
    IPV6 = 2

  destRange = _messages.StringField(1)
  ipProtocol = _messages.StringField(2)
  protocolVersion = _messages.EnumField('ProtocolVersionValueValuesEnum', 3)
  srcRange = _messages.StringField(4)


class GoogleCloudNetworkconnectivityV1betaGateway(_messages.Message):
  r"""A gateway that can apply specialized traffic processing.

  Enums:
    CapacityValueValuesEnum: Optional. The aggregate processing capacity of
      this gateway.

  Fields:
    capacity: Optional. The aggregate processing capacity of this gateway.
    cloudRouters: Output only. The list of Cloud Routers that are connected to
      this gateway. Should be in the form: https://www.googleapis.com/compute/
      v1/projects/{project}/regions/{region}/routers/{router}
    ipRangeReservations: Optional. A list of IP ranges that are reserved for
      this gateway's internal intfrastructure.
    landingNetwork: Optional. This field will be deprecated and replaced
      before gateway spokes reach General Availability.
  """

  class CapacityValueValuesEnum(_messages.Enum):
    r"""Optional. The aggregate processing capacity of this gateway.

    Values:
      GATEWAY_CAPACITY_UNSPECIFIED: The gateway capacity is unspecified.
      CAPACITY_1_GBPS: The gateway has 1 Gbps of aggregate processing capacity
      CAPACITY_10_GBPS: The gateway has 10 Gbps of aggregate processing
        capacity
      CAPACITY_100_GBPS: The gateway has 100 Gbps of aggregate processing
        capacity
    """
    GATEWAY_CAPACITY_UNSPECIFIED = 0
    CAPACITY_1_GBPS = 1
    CAPACITY_10_GBPS = 2
    CAPACITY_100_GBPS = 3

  capacity = _messages.EnumField('CapacityValueValuesEnum', 1)
  cloudRouters = _messages.StringField(2, repeated=True)
  ipRangeReservations = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaIpRangeReservation', 3, repeated=True)
  landingNetwork = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaLandingNetwork', 4)


class GoogleCloudNetworkconnectivityV1betaGatewayAdvertisedRoute(_messages.Message):
  r"""A gateway advertised route is a route that a gateway spoke advertises
  somewhere.

  Enums:
    RecipientValueValuesEnum: Optional. The recipient of this advertised
      route.
    StateValueValuesEnum: Output only. The current lifecycle state of this
      gateway advertised route.

  Messages:
    LabelsValue: Optional labels in key-value pair format. For more
      information about labels, see [Requirements for
      labels](https://cloud.google.com/resource-manager/docs/creating-
      managing-labels#requirements).

  Fields:
    createTime: Output only. The time the gateway advertised route was
      created.
    description: An optional description of the gateway advertised route.
    ipRange: Immutable. This route's advertised IP address range. Must be a
      valid CIDR-formatted prefix. If an IP address is provided without a
      subnet mask, it is interpreted as, for IPv4, a `/32` singular IP address
      range, and, for IPv6, `/128`.
    labels: Optional labels in key-value pair format. For more information
      about labels, see [Requirements for
      labels](https://cloud.google.com/resource-manager/docs/creating-
      managing-labels#requirements).
    name: Identifier. The name of the gateway advertised route. Route names
      must be unique and use the following form: `projects/{project_number}/lo
      cations/{region}/spokes/{spoke}/gatewayAdvertisedRoutes/{gateway_adverti
      sed_route_id}`
    priority: Optional. The priority of this advertised route. You can choose
      a value from `0` to `65335`. If you don't provide a value, Google Cloud
      assigns a priority of `100` to the ranges.
    recipient: Optional. The recipient of this advertised route.
    state: Output only. The current lifecycle state of this gateway advertised
      route.
    uniqueId: Output only. The Google-generated UUID for the gateway
      advertised route. This value is unique across all gateway advertised
      route resources. If a gateway advertised route is deleted and another
      with the same name is created, the new route is assigned a different
      `unique_id`.
    updateTime: Output only. The time the gateway advertised route was last
      updated.
  """

  class RecipientValueValuesEnum(_messages.Enum):
    r"""Optional. The recipient of this advertised route.

    Values:
      RECIPIENT_UNSPECIFIED: No recipient specified. By default routes are
        advertised to the hub.
      ADVERTISE_TO_HUB: Advertises a route toward the hub. Other spokes
        reachable from this spoke will receive the route.
    """
    RECIPIENT_UNSPECIFIED = 0
    ADVERTISE_TO_HUB = 1

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current lifecycle state of this gateway advertised
    route.

    Values:
      STATE_UNSPECIFIED: No state information available
      CREATING: The resource's create operation is in progress.
      ACTIVE: The resource is active
      DELETING: The resource's delete operation is in progress.
      ACTIVATING: The resource's activate operation is in progress.
      DEACTIVATING: The resource's deactivate operation is in progress.
      ACCEPTING: The resource's accept operation is in progress.
      REJECTING: The resource's reject operation is in progress.
      UPDATING: The resource's update operation is in progress.
      INACTIVE: The resource is inactive.
      OBSOLETE: The hub associated with this spoke resource has been deleted.
        This state applies to spoke resources only.
      FAILED: The resource is in an undefined state due to resource creation
        or deletion failure. You can try to delete the resource later or
        contact support for help.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    ACTIVATING = 4
    DEACTIVATING = 5
    ACCEPTING = 6
    REJECTING = 7
    UPDATING = 8
    INACTIVE = 9
    OBSOLETE = 10
    FAILED = 11

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional labels in key-value pair format. For more information about
    labels, see [Requirements for labels](https://cloud.google.com/resource-
    manager/docs/creating-managing-labels#requirements).

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  ipRange = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  priority = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  recipient = _messages.EnumField('RecipientValueValuesEnum', 7)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  uniqueId = _messages.StringField(9)
  updateTime = _messages.StringField(10)


class GoogleCloudNetworkconnectivityV1betaGroup(_messages.Message):
  r"""A group represents a subset of spokes attached to a hub.

  Enums:
    StateValueValuesEnum: Output only. The current lifecycle state of this
      group.

  Messages:
    LabelsValue: Optional. Labels in key-value pair format. For more
      information about labels, see [Requirements for
      labels](https://cloud.google.com/resource-manager/docs/creating-
      managing-labels#requirements).

  Fields:
    autoAccept: Optional. The auto-accept setting for this group.
    createTime: Output only. The time the group was created.
    description: Optional. The description of the group.
    labels: Optional. Labels in key-value pair format. For more information
      about labels, see [Requirements for
      labels](https://cloud.google.com/resource-manager/docs/creating-
      managing-labels#requirements).
    name: Immutable. The name of the group. Group names must be unique. They
      use the following form: `projects/{project_number}/locations/global/hubs
      /{hub}/groups/{group_id}`
    routeTable: Output only. The name of the route table that corresponds to
      this group. They use the following form: `projects/{project_number}/loca
      tions/global/hubs/{hub_id}/routeTables/{route_table_id}`
    state: Output only. The current lifecycle state of this group.
    uid: Output only. The Google-generated UUID for the group. This value is
      unique across all group resources. If a group is deleted and another
      with the same name is created, the new route table is assigned a
      different unique_id.
    updateTime: Output only. The time the group was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current lifecycle state of this group.

    Values:
      STATE_UNSPECIFIED: No state information available
      CREATING: The resource's create operation is in progress.
      ACTIVE: The resource is active
      DELETING: The resource's delete operation is in progress.
      ACTIVATING: The resource's activate operation is in progress.
      DEACTIVATING: The resource's deactivate operation is in progress.
      ACCEPTING: The resource's accept operation is in progress.
      REJECTING: The resource's reject operation is in progress.
      UPDATING: The resource's update operation is in progress.
      INACTIVE: The resource is inactive.
      OBSOLETE: The hub associated with this spoke resource has been deleted.
        This state applies to spoke resources only.
      FAILED: The resource is in an undefined state due to resource creation
        or deletion failure. You can try to delete the resource later or
        contact support for help.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    ACTIVATING = 4
    DEACTIVATING = 5
    ACCEPTING = 6
    REJECTING = 7
    UPDATING = 8
    INACTIVE = 9
    OBSOLETE = 10
    FAILED = 11

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels in key-value pair format. For more information about
    labels, see [Requirements for labels](https://cloud.google.com/resource-
    manager/docs/creating-managing-labels#requirements).

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  autoAccept = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaAutoAccept', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  routeTable = _messages.StringField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  uid = _messages.StringField(8)
  updateTime = _messages.StringField(9)


class GoogleCloudNetworkconnectivityV1betaHub(_messages.Message):
  r"""A Network Connectivity Center hub is a global management resource to
  which you attach spokes. A single hub can contain spokes from multiple
  regions. However, if any of a hub's spokes use the site-to-site data
  transfer feature, the resources associated with those spokes must all be in
  the same VPC network. Spokes that do not use site-to-site data transfer can
  be associated with any VPC network in your project.

  Enums:
    PolicyModeValueValuesEnum: Optional. The policy mode of this hub. This
      field can be either PRESET or CUSTOM. If unspecified, the policy_mode
      defaults to PRESET.
    PresetTopologyValueValuesEnum: Optional. The topology implemented in this
      hub. Currently, this field is only used when policy_mode = PRESET. The
      available preset topologies are MESH and STAR. If preset_topology is
      unspecified and policy_mode = PRESET, the preset_topology defaults to
      MESH. When policy_mode = CUSTOM, the preset_topology is set to
      PRESET_TOPOLOGY_UNSPECIFIED.
    StateValueValuesEnum: Output only. The current lifecycle state of this
      hub.

  Messages:
    LabelsValue: Optional labels in key-value pair format. For more
      information about labels, see [Requirements for
      labels](https://cloud.google.com/resource-manager/docs/creating-
      managing-labels#requirements).

  Fields:
    createTime: Output only. The time the hub was created.
    description: Optional. An optional description of the hub.
    exchangePupi: Optional. Whether Privately Used Public IP (PUPI) exchange
      is enabled for the hub. If true, PUPI exchange will be allowed in VPC
      spokes attached to the hub. The default value is false.
    exportPsc: Optional. Whether Private Service Connect connection
      propagation is enabled for the hub. If true, Private Service Connect
      endpoints in VPC spokes attached to the hub are made accessible to other
      VPC spokes attached to the hub. The default value is false.
    exportPscConfig: Optional. Config for more granular control of Private
      Service Connect transitivity.
    labels: Optional labels in key-value pair format. For more information
      about labels, see [Requirements for
      labels](https://cloud.google.com/resource-manager/docs/creating-
      managing-labels#requirements).
    name: Immutable. The name of the hub. Hub names must be unique. They use
      the following form:
      `projects/{project_number}/locations/global/hubs/{hub_id}`
    policyMode: Optional. The policy mode of this hub. This field can be
      either PRESET or CUSTOM. If unspecified, the policy_mode defaults to
      PRESET.
    presetTopology: Optional. The topology implemented in this hub. Currently,
      this field is only used when policy_mode = PRESET. The available preset
      topologies are MESH and STAR. If preset_topology is unspecified and
      policy_mode = PRESET, the preset_topology defaults to MESH. When
      policy_mode = CUSTOM, the preset_topology is set to
      PRESET_TOPOLOGY_UNSPECIFIED.
    routeTables: Output only. The route tables that belong to this hub. They
      use the following form: `projects/{project_number}/locations/global/hubs
      /{hub_id}/routeTables/{route_table_id}` This field is read-only. Network
      Connectivity Center automatically populates it based on the route tables
      nested under the hub.
    routingVpcs: Output only. The VPC networks associated with this hub's
      spokes. This field is read-only. Network Connectivity Center
      automatically populates it based on the set of spokes attached to the
      hub.
    spokeSummary: Output only. A summary of the spokes associated with a hub.
      The summary includes a count of spokes according to type and according
      to state. If any spokes are inactive, the summary also lists the reasons
      they are inactive, including a count for each reason.
    state: Output only. The current lifecycle state of this hub.
    uniqueId: Output only. The Google-generated UUID for the hub. This value
      is unique across all hub resources. If a hub is deleted and another with
      the same name is created, the new hub is assigned a different unique_id.
    updateTime: Output only. The time the hub was last updated.
  """

  class PolicyModeValueValuesEnum(_messages.Enum):
    r"""Optional. The policy mode of this hub. This field can be either PRESET
    or CUSTOM. If unspecified, the policy_mode defaults to PRESET.

    Values:
      POLICY_MODE_UNSPECIFIED: Policy mode is unspecified. It defaults to
        PRESET with preset_topology = MESH.
      PRESET: Hub uses one of the preset topologies.
    """
    POLICY_MODE_UNSPECIFIED = 0
    PRESET = 1

  class PresetTopologyValueValuesEnum(_messages.Enum):
    r"""Optional. The topology implemented in this hub. Currently, this field
    is only used when policy_mode = PRESET. The available preset topologies
    are MESH and STAR. If preset_topology is unspecified and policy_mode =
    PRESET, the preset_topology defaults to MESH. When policy_mode = CUSTOM,
    the preset_topology is set to PRESET_TOPOLOGY_UNSPECIFIED.

    Values:
      PRESET_TOPOLOGY_UNSPECIFIED: Preset topology is unspecified. When
        policy_mode = PRESET, it defaults to MESH.
      MESH: Mesh topology is implemented. Group `default` is automatically
        created. All spokes in the hub are added to group `default`.
      STAR: Star topology is implemented. Two groups, `center` and `edge`, are
        automatically created along with hub creation. Spokes have to join one
        of the groups during creation.
      HYBRID_INSPECTION: Hybrid inspection has 4 groups ('non-prod', 'prod',
        'services', and 'untrusted') that are automatically created along with
        hub creation.
    """
    PRESET_TOPOLOGY_UNSPECIFIED = 0
    MESH = 1
    STAR = 2
    HYBRID_INSPECTION = 3

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current lifecycle state of this hub.

    Values:
      STATE_UNSPECIFIED: No state information available
      CREATING: The resource's create operation is in progress.
      ACTIVE: The resource is active
      DELETING: The resource's delete operation is in progress.
      ACTIVATING: The resource's activate operation is in progress.
      DEACTIVATING: The resource's deactivate operation is in progress.
      ACCEPTING: The resource's accept operation is in progress.
      REJECTING: The resource's reject operation is in progress.
      UPDATING: The resource's update operation is in progress.
      INACTIVE: The resource is inactive.
      OBSOLETE: The hub associated with this spoke resource has been deleted.
        This state applies to spoke resources only.
      FAILED: The resource is in an undefined state due to resource creation
        or deletion failure. You can try to delete the resource later or
        contact support for help.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    ACTIVATING = 4
    DEACTIVATING = 5
    ACCEPTING = 6
    REJECTING = 7
    UPDATING = 8
    INACTIVE = 9
    OBSOLETE = 10
    FAILED = 11

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional labels in key-value pair format. For more information about
    labels, see [Requirements for labels](https://cloud.google.com/resource-
    manager/docs/creating-managing-labels#requirements).

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  exchangePupi = _messages.BooleanField(3)
  exportPsc = _messages.BooleanField(4)
  exportPscConfig = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaExportPscConfig', 5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  policyMode = _messages.EnumField('PolicyModeValueValuesEnum', 8)
  presetTopology = _messages.EnumField('PresetTopologyValueValuesEnum', 9)
  routeTables = _messages.StringField(10, repeated=True)
  routingVpcs = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaRoutingVPC', 11, repeated=True)
  spokeSummary = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaSpokeSummary', 12)
  state = _messages.EnumField('StateValueValuesEnum', 13)
  uniqueId = _messages.StringField(14)
  updateTime = _messages.StringField(15)


class GoogleCloudNetworkconnectivityV1betaHubStatusEntry(_messages.Message):
  r"""A hub status entry represents the status of a set of propagated Private
  Service Connect connections grouped by certain fields.

  Fields:
    count: The number of propagated Private Service Connect connections with
      this status. If the `group_by` field was not set in the request message,
      the value of this field is 1.
    groupBy: The fields that this entry is grouped by. This has the same value
      as the `group_by` field in the request message.
    pscPropagationStatus: The Private Service Connect propagation status.
  """

  count = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  groupBy = _messages.StringField(2)
  pscPropagationStatus = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaPscPropagationStatus', 3)


class GoogleCloudNetworkconnectivityV1betaInterconnectAttachment(_messages.Message):
  r"""InterconnectAttachment that this route applies to.

  Fields:
    region: Optional. Cloud region to install this policy-based route on
      interconnect attachment. Use `all` to install it on all interconnect
      attachments.
  """

  region = _messages.StringField(1)


class GoogleCloudNetworkconnectivityV1betaIpRangeReservation(_messages.Message):
  r"""A list of IP ranges that are reserved for this gateway's internal
  intfrastructure.

  Fields:
    ipRange: Required. A block of IP addresses used to allocate supporting
      infrastructure for this gateway. This block must not overlap with
      subnets in any spokes or peer VPC networks that the gateway can
      communicate with. Example: "********/24"
  """

  ipRange = _messages.StringField(1)


class GoogleCloudNetworkconnectivityV1betaLandingNetwork(_messages.Message):
  r"""Information about the landing network connected to this gateway.

  Fields:
    network: Optional. A VPC network containing Interconnect VLAN attachments.
      We will initiate peering to this network; you probably want to
      reciprocate by peering `network` with `peer_network`.
    peerNetwork: Optional. We'll initiate peering to `landing_network_uri`
      from this VPC network. You should reciprocate peering to this network.
    targetIp: Optional. To egress traffic to the Internet, you should create a
      static route in the landing network that directs traffic toward this IP
      address. We will pass traffic through any services attached to this
      gateway en route to or from the Internet.
  """

  network = _messages.StringField(1)
  peerNetwork = _messages.StringField(2)
  targetIp = _messages.StringField(3)


class GoogleCloudNetworkconnectivityV1betaLinkedInterconnectAttachments(_messages.Message):
  r"""A collection of VLAN attachment resources. These resources should be
  redundant attachments that all advertise the same prefixes to Google Cloud.
  Alternatively, in active/passive configurations, all attachments should be
  capable of advertising the same prefixes.

  Fields:
    includeImportRanges: Optional. Hub routes fully encompassed by include
      import ranges are included during import from hub.
    siteToSiteDataTransfer: A value that controls whether site-to-site data
      transfer is enabled for these resources. Data transfer is available only
      in [supported locations](https://cloud.google.com/network-
      connectivity/docs/network-connectivity-center/concepts/locations).
    uris: The URIs of linked interconnect attachment resources
    vpcNetwork: Output only. The VPC network where these VLAN attachments are
      located.
  """

  includeImportRanges = _messages.StringField(1, repeated=True)
  siteToSiteDataTransfer = _messages.BooleanField(2)
  uris = _messages.StringField(3, repeated=True)
  vpcNetwork = _messages.StringField(4)


class GoogleCloudNetworkconnectivityV1betaLinkedProducerVpcNetwork(_messages.Message):
  r"""A GoogleCloudNetworkconnectivityV1betaLinkedProducerVpcNetwork object.

  Fields:
    excludeExportRanges: Optional. IP ranges encompassing the subnets to be
      excluded from peering.
    includeExportRanges: Optional. IP ranges allowed to be included from
      peering.
    network: Immutable. The URI of the Service Consumer VPC that the Producer
      VPC is peered with.
    peering: Immutable. The name of the VPC peering between the Service
      Consumer VPC and the Producer VPC (defined in the Tenant project) which
      is added to the NCC hub. This peering must be in ACTIVE state.
    producerNetwork: Output only. The URI of the Producer VPC.
    proposedExcludeExportRanges: Output only. The proposed exclude export IP
      ranges waiting for hub administration's approval.
    proposedIncludeExportRanges: Output only. The proposed include export IP
      ranges waiting for hub administration's approval.
    serviceConsumerVpcSpoke: Output only. The Service Consumer Network spoke.
  """

  excludeExportRanges = _messages.StringField(1, repeated=True)
  includeExportRanges = _messages.StringField(2, repeated=True)
  network = _messages.StringField(3)
  peering = _messages.StringField(4)
  producerNetwork = _messages.StringField(5)
  proposedExcludeExportRanges = _messages.StringField(6, repeated=True)
  proposedIncludeExportRanges = _messages.StringField(7, repeated=True)
  serviceConsumerVpcSpoke = _messages.StringField(8)


class GoogleCloudNetworkconnectivityV1betaLinkedRouterApplianceInstances(_messages.Message):
  r"""A collection of router appliance instances. If you configure multiple
  router appliance instances to receive data from the same set of sites
  outside of Google Cloud, we recommend that you associate those instances
  with the same spoke.

  Fields:
    includeImportRanges: Optional. Hub routes fully encompassed by include
      import ranges are included during import from hub.
    instances: The list of router appliance instances.
    siteToSiteDataTransfer: A value that controls whether site-to-site data
      transfer is enabled for these resources. Data transfer is available only
      in [supported locations](https://cloud.google.com/network-
      connectivity/docs/network-connectivity-center/concepts/locations).
    vpcNetwork: Output only. The VPC network where these router appliance
      instances are located.
  """

  includeImportRanges = _messages.StringField(1, repeated=True)
  instances = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaRouterApplianceInstance', 2, repeated=True)
  siteToSiteDataTransfer = _messages.BooleanField(3)
  vpcNetwork = _messages.StringField(4)


class GoogleCloudNetworkconnectivityV1betaLinkedVpcNetwork(_messages.Message):
  r"""An existing VPC network.

  Fields:
    excludeExportRanges: Optional. IP ranges encompassing the subnets to be
      excluded from peering.
    includeExportRanges: Optional. IP ranges allowed to be included from
      peering.
    producerVpcSpokes: Output only. The list of Producer VPC spokes that this
      VPC spoke is a service consumer VPC spoke for. These producer VPCs are
      connected through VPC peering to this spoke's backing VPC network.
      Because they are directly connected through VPC peering, NCC export
      filters do not apply between the service consumer VPC spoke and any of
      its producer VPC spokes. This VPC spoke cannot be deleted as long as any
      of these producer VPC spokes are connected to the NCC Hub.
    proposedExcludeExportRanges: Output only. The proposed exclude export IP
      ranges waiting for hub administration's approval.
    proposedIncludeExportRanges: Output only. The proposed include export IP
      ranges waiting for hub administration's approval.
    uri: Required. The URI of the VPC network resource.
  """

  excludeExportRanges = _messages.StringField(1, repeated=True)
  includeExportRanges = _messages.StringField(2, repeated=True)
  producerVpcSpokes = _messages.StringField(3, repeated=True)
  proposedExcludeExportRanges = _messages.StringField(4, repeated=True)
  proposedIncludeExportRanges = _messages.StringField(5, repeated=True)
  uri = _messages.StringField(6)


class GoogleCloudNetworkconnectivityV1betaLinkedVpnTunnels(_messages.Message):
  r"""A collection of Cloud VPN tunnel resources. These resources should be
  redundant HA VPN tunnels that all advertise the same prefixes to Google
  Cloud. Alternatively, in a passive/active configuration, all tunnels should
  be capable of advertising the same prefixes.

  Fields:
    includeImportRanges: Optional. Hub routes fully encompassed by include
      import ranges are included during import from hub.
    siteToSiteDataTransfer: A value that controls whether site-to-site data
      transfer is enabled for these resources. Data transfer is available only
      in [supported locations](https://cloud.google.com/network-
      connectivity/docs/network-connectivity-center/concepts/locations).
    uris: The URIs of linked VPN tunnel resources.
    vpcNetwork: Output only. The VPC network where these VPN tunnels are
      located.
  """

  includeImportRanges = _messages.StringField(1, repeated=True)
  siteToSiteDataTransfer = _messages.BooleanField(2)
  uris = _messages.StringField(3, repeated=True)
  vpcNetwork = _messages.StringField(4)


class GoogleCloudNetworkconnectivityV1betaListDestinationsResponse(_messages.Message):
  r"""Response message for ListDestinations.

  Fields:
    destinations: Destinations to be returned.
    nextPageToken: The next page token.
    unreachable: Locations that could not be reached.
  """

  destinations = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaDestination', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class GoogleCloudNetworkconnectivityV1betaListGatewayAdvertisedRoutesResponse(_messages.Message):
  r"""Response for HubService.ListGatewayAdvertisedRoutes method.

  Fields:
    gatewayAdvertisedRoutes: The requested gateway advertised routes.
    nextPageToken: The token for the next page of the response. To see more
      results, use this value as the page_token for your next request. If this
      value is empty, there are no more results.
    unreachable: Hubs that could not be reached.
  """

  gatewayAdvertisedRoutes = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaGatewayAdvertisedRoute', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class GoogleCloudNetworkconnectivityV1betaListGroupsResponse(_messages.Message):
  r"""Response for HubService.ListGroups method.

  Fields:
    groups: The requested groups.
    nextPageToken: The token for the next page of the response. To see more
      results, use this value as the page_token for your next request. If this
      value is empty, there are no more results.
    unreachable: Hubs that could not be reached.
  """

  groups = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaGroup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class GoogleCloudNetworkconnectivityV1betaListHubSpokesResponse(_messages.Message):
  r"""The response for HubService.ListHubSpokes.

  Fields:
    nextPageToken: The token for the next page of the response. To see more
      results, use this value as the page_token for your next request. If this
      value is empty, there are no more results.
    spokes: The requested spokes. The spoke fields can be partially populated
      based on the `view` field in the request message.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  spokes = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaSpoke', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class GoogleCloudNetworkconnectivityV1betaListHubsResponse(_messages.Message):
  r"""Response for HubService.ListHubs method.

  Fields:
    hubs: The requested hubs.
    nextPageToken: The token for the next page of the response. To see more
      results, use this value as the page_token for your next request. If this
      value is empty, there are no more results.
    unreachable: Locations that could not be reached.
  """

  hubs = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaHub', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class GoogleCloudNetworkconnectivityV1betaListMulticloudDataTransferConfigsResponse(_messages.Message):
  r"""Response message for ListMulticloudDataTransferConfigs.

  Fields:
    multicloudDataTransferConfigs: MulticloudDataTransferConfigs to be
      returned.
    nextPageToken: The next page token.
    unreachable: Locations that could not be reached.
  """

  multicloudDataTransferConfigs = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaMulticloudDataTransferConfig', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class GoogleCloudNetworkconnectivityV1betaListMulticloudDataTransferSupportedServicesResponse(_messages.Message):
  r"""Response message for ListMulticloudDataTransferSupportedServices.

  Fields:
    multicloudDataTransferSupportedServices: The list of supported services.
    nextPageToken: The next page token.
  """

  multicloudDataTransferSupportedServices = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaMulticloudDataTransferSupportedService', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudNetworkconnectivityV1betaListPolicyBasedRoutesResponse(_messages.Message):
  r"""Response for PolicyBasedRoutingService.ListPolicyBasedRoutes method.

  Fields:
    nextPageToken: The next pagination token in the List response. It should
      be used as page_token for the following request. An empty value means no
      more result.
    policyBasedRoutes: Policy-based routes to be returned.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  policyBasedRoutes = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaPolicyBasedRoute', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class GoogleCloudNetworkconnectivityV1betaListRegionalEndpointsResponse(_messages.Message):
  r"""Response for ListRegionalEndpoints.

  Fields:
    nextPageToken: The next pagination token in the List response. It should
      be used as page_token for the following request. An empty value means no
      more result.
    regionalEndpoints: Regional endpoints to be returned.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  regionalEndpoints = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaRegionalEndpoint', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class GoogleCloudNetworkconnectivityV1betaListRouteTablesResponse(_messages.Message):
  r"""Response for HubService.ListRouteTables method.

  Fields:
    nextPageToken: The token for the next page of the response. To see more
      results, use this value as the page_token for your next request. If this
      value is empty, there are no more results.
    routeTables: The requested route tables.
    unreachable: Hubs that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  routeTables = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaRouteTable', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class GoogleCloudNetworkconnectivityV1betaListRoutesResponse(_messages.Message):
  r"""Response for HubService.ListRoutes method.

  Fields:
    nextPageToken: The token for the next page of the response. To see more
      results, use this value as the page_token for your next request. If this
      value is empty, there are no more results.
    routes: The requested routes.
    unreachable: RouteTables that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  routes = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaRoute', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class GoogleCloudNetworkconnectivityV1betaListSpokesResponse(_messages.Message):
  r"""The response for HubService.ListSpokes.

  Fields:
    nextPageToken: The token for the next page of the response. To see more
      results, use this value as the page_token for your next request. If this
      value is empty, there are no more results.
    spokes: The requested spokes.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  spokes = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaSpoke', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class GoogleCloudNetworkconnectivityV1betaLocationMetadata(_messages.Message):
  r"""Metadata about locations

  Enums:
    LocationFeaturesValueListEntryValuesEnum:

  Fields:
    locationFeatures: List of supported features
  """

  class LocationFeaturesValueListEntryValuesEnum(_messages.Enum):
    r"""LocationFeaturesValueListEntryValuesEnum enum type.

    Values:
      LOCATION_FEATURE_UNSPECIFIED: No publicly supported feature in this
        location
      SITE_TO_CLOUD_SPOKES: Site-to-cloud spokes are supported in this
        location
      SITE_TO_SITE_SPOKES: Site-to-site spokes are supported in this location
      GATEWAY_SPOKES: Gateway spokes are supported in this location.
    """
    LOCATION_FEATURE_UNSPECIFIED = 0
    SITE_TO_CLOUD_SPOKES = 1
    SITE_TO_SITE_SPOKES = 2
    GATEWAY_SPOKES = 3

  locationFeatures = _messages.EnumField('LocationFeaturesValueListEntryValuesEnum', 1, repeated=True)


class GoogleCloudNetworkconnectivityV1betaMulticloudDataTransferConfig(_messages.Message):
  r"""The MulticloudDataTransferConfig resource. This lists the services for
  which customer is opting in for Multicloud Data Transfer.

  Messages:
    LabelsValue: Optional. User-defined labels.
    ServicesValue: Optional. This map services to either their current or
      planned states. Service names are keys, and the associated values
      describe the service's state. If a state change is expected, the value
      will be the list of ADDING or DELETING states depending on the actions
      taken. Example: "services": { "big-query": { "states": [ { "state":
      "ADDING", "effective_time": "2024-12-12T08:00:00Z" }, ] }, "cloud-
      storage": { "states": [ { "state": "ACTIVE", } ] } }

  Fields:
    createTime: Output only. Time when the MulticloudDataTransferConfig was
      created.
    description: Optional. An optional field to provide a description of this
      resource.
    destinationsActiveCount: Output only. The number of Destinations in use
      under the MulticloudDataTransferConfig resource.
    destinationsCount: Output only. The number of Destinations configured
      under the MulticloudDataTransferConfig resource.
    etag: The etag is computed by the server, and may be sent on update and
      delete requests to ensure the client has an up-to-date value before
      proceeding.
    labels: Optional. User-defined labels.
    name: Identifier. The name of the MulticloudDataTransferConfig resource.
      Format: `projects/{project}/locations/{location}/multicloudDataTransferC
      onfigs/{multicloud_data_transfer_config}`.
    services: Optional. This map services to either their current or planned
      states. Service names are keys, and the associated values describe the
      service's state. If a state change is expected, the value will be the
      list of ADDING or DELETING states depending on the actions taken.
      Example: "services": { "big-query": { "states": [ { "state": "ADDING",
      "effective_time": "2024-12-12T08:00:00Z" }, ] }, "cloud-storage": {
      "states": [ { "state": "ACTIVE", } ] } }
    uid: Output only. The Google-generated UUID for the
      MulticloudDataTransferConfig. This value is unique across all
      MulticloudDataTransferConfig resources. If a
      MulticloudDataTransferConfig is deleted and another with the same name
      is created, the new MulticloudDataTransferConfig is assigned a different
      uid.
    updateTime: Output only. Time when the MulticloudDataTransferConfig was
      updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. User-defined labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ServicesValue(_messages.Message):
    r"""Optional. This map services to either their current or planned states.
    Service names are keys, and the associated values describe the service's
    state. If a state change is expected, the value will be the list of ADDING
    or DELETING states depending on the actions taken. Example: "services": {
    "big-query": { "states": [ { "state": "ADDING", "effective_time":
    "2024-12-12T08:00:00Z" }, ] }, "cloud-storage": { "states": [ { "state":
    "ACTIVE", } ] } }

    Messages:
      AdditionalProperty: An additional property for a ServicesValue object.

    Fields:
      additionalProperties: Additional properties of type ServicesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ServicesValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudNetworkconnectivityV1betaStateTimeline attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaStateTimeline', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  destinationsActiveCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  destinationsCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  etag = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  services = _messages.MessageField('ServicesValue', 8)
  uid = _messages.StringField(9)
  updateTime = _messages.StringField(10)


class GoogleCloudNetworkconnectivityV1betaMulticloudDataTransferSupportedService(_messages.Message):
  r"""The supported service for Multicloud Data Transfer.

  Fields:
    name: Identifier. The name of the service.
    serviceConfigs: Output only. The network service tiers supported for the
      service.
  """

  name = _messages.StringField(1)
  serviceConfigs = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaServiceConfig', 2, repeated=True)


class GoogleCloudNetworkconnectivityV1betaNextHopInterconnectAttachment(_messages.Message):
  r"""A route next hop that leads to an interconnect attachment resource.

  Fields:
    siteToSiteDataTransfer: Indicates whether site-to-site data transfer is
      allowed for this interconnect attachment resource. Data transfer is
      available only in [supported
      locations](https://cloud.google.com/network-connectivity/docs/network-
      connectivity-center/concepts/locations).
    uri: The URI of the interconnect attachment resource.
    vpcNetwork: The VPC network where this interconnect attachment is located.
  """

  siteToSiteDataTransfer = _messages.BooleanField(1)
  uri = _messages.StringField(2)
  vpcNetwork = _messages.StringField(3)


class GoogleCloudNetworkconnectivityV1betaNextHopRouterApplianceInstance(_messages.Message):
  r"""A route next hop that leads to a Router appliance instance.

  Fields:
    siteToSiteDataTransfer: Indicates whether site-to-site data transfer is
      allowed for this Router appliance instance resource. Data transfer is
      available only in [supported
      locations](https://cloud.google.com/network-connectivity/docs/network-
      connectivity-center/concepts/locations).
    uri: The URI of the Router appliance instance.
    vpcNetwork: The VPC network where this VM is located.
  """

  siteToSiteDataTransfer = _messages.BooleanField(1)
  uri = _messages.StringField(2)
  vpcNetwork = _messages.StringField(3)


class GoogleCloudNetworkconnectivityV1betaNextHopSpoke(_messages.Message):
  r"""A route next hop that leads to a spoke resource.

  Fields:
    siteToSiteDataTransfer: Indicates whether site-to-site data transfer is
      allowed for this spoke resource. Data transfer is available only in
      [supported locations](https://cloud.google.com/network-
      connectivity/docs/network-connectivity-center/concepts/locations).
      Whether this route is accessible to other hybrid spokes with site-to-
      site data transfer enabled. If this is false, the route is only
      accessible to VPC spokes of the connected Hub.
    uri: The URI of the spoke resource.
  """

  siteToSiteDataTransfer = _messages.BooleanField(1)
  uri = _messages.StringField(2)


class GoogleCloudNetworkconnectivityV1betaNextHopVPNTunnel(_messages.Message):
  r"""A route next hop that leads to a VPN tunnel resource.

  Fields:
    siteToSiteDataTransfer: Indicates whether site-to-site data transfer is
      allowed for this VPN tunnel resource. Data transfer is available only in
      [supported locations](https://cloud.google.com/network-
      connectivity/docs/network-connectivity-center/concepts/locations).
    uri: The URI of the VPN tunnel resource.
    vpcNetwork: The VPC network where this VPN tunnel is located.
  """

  siteToSiteDataTransfer = _messages.BooleanField(1)
  uri = _messages.StringField(2)
  vpcNetwork = _messages.StringField(3)


class GoogleCloudNetworkconnectivityV1betaNextHopVpcNetwork(_messages.Message):
  r"""A GoogleCloudNetworkconnectivityV1betaNextHopVpcNetwork object.

  Fields:
    uri: The URI of the VPC network resource
  """

  uri = _messages.StringField(1)


class GoogleCloudNetworkconnectivityV1betaOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have google.longrunning.Operation.error value
      with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudNetworkconnectivityV1betaPolicyBasedRoute(_messages.Message):
  r"""Policy-based routes route L4 network traffic based on not just
  destination IP address, but also source IP address, protocol, and more. If a
  policy-based route conflicts with other types of routes, the policy-based
  route always takes precedence.

  Enums:
    NextHopOtherRoutesValueValuesEnum: Optional. Other routes that will be
      referenced to determine the next hop of the packet.

  Messages:
    LabelsValue: User-defined labels.

  Fields:
    createTime: Output only. Time when the policy-based route was created.
    description: Optional. An optional description of this resource. Provide
      this field when you create the resource.
    filter: Required. The filter to match L4 traffic.
    interconnectAttachment: Optional. The interconnect attachments that this
      policy-based route applies to.
    kind: Output only. Type of this resource. Always
      networkconnectivity#policyBasedRoute for policy-based Route resources.
    labels: User-defined labels.
    name: Immutable. A unique name of the resource in the form of `projects/{p
      roject_number}/locations/global/PolicyBasedRoutes/{policy_based_route_id
      }`
    network: Required. Fully-qualified URL of the network that this route
      applies to, for example: projects/my-project/global/networks/my-network.
    nextHopIlbIp: Optional. The IP address of a global-access-enabled L4 ILB
      that is the next hop for matching packets. For this version, only
      nextHopIlbIp is supported.
    nextHopOtherRoutes: Optional. Other routes that will be referenced to
      determine the next hop of the packet.
    priority: Optional. The priority of this policy-based route. Priority is
      used to break ties in cases where there are more than one matching
      policy-based routes found. In cases where multiple policy-based routes
      are matched, the one with the lowest-numbered priority value wins. The
      default value is 1000. The priority value must be from 1 to 65535,
      inclusive.
    selfLink: Output only. Server-defined fully-qualified URL for this
      resource.
    updateTime: Output only. Time when the policy-based route was updated.
    virtualMachine: Optional. VM instances that this policy-based route
      applies to.
    warnings: Output only. If potential misconfigurations are detected for
      this route, this field will be populated with warning messages.
  """

  class NextHopOtherRoutesValueValuesEnum(_messages.Enum):
    r"""Optional. Other routes that will be referenced to determine the next
    hop of the packet.

    Values:
      OTHER_ROUTES_UNSPECIFIED: Default value.
      DEFAULT_ROUTING: Use the routes from the default routing tables (system-
        generated routes, custom routes, peering route) to determine the next
        hop. This effectively excludes matching packets being applied on other
        PBRs with a lower priority.
    """
    OTHER_ROUTES_UNSPECIFIED = 0
    DEFAULT_ROUTING = 1

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""User-defined labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  filter = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaFilter', 3)
  interconnectAttachment = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaInterconnectAttachment', 4)
  kind = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  network = _messages.StringField(8)
  nextHopIlbIp = _messages.StringField(9)
  nextHopOtherRoutes = _messages.EnumField('NextHopOtherRoutesValueValuesEnum', 10)
  priority = _messages.IntegerField(11, variant=_messages.Variant.INT32)
  selfLink = _messages.StringField(12)
  updateTime = _messages.StringField(13)
  virtualMachine = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaVirtualMachine', 14)
  warnings = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaWarnings', 15, repeated=True)


class GoogleCloudNetworkconnectivityV1betaPscPropagationStatus(_messages.Message):
  r"""The status of one or more propagated Private Service Connect connections
  in a hub.

  Enums:
    CodeValueValuesEnum: The propagation status.

  Fields:
    code: The propagation status.
    message: The human-readable summary of the Private Service Connect
      connection propagation status.
    sourceForwardingRule: The name of the forwarding rule exported to the hub.
    sourceGroup: The name of the group that the source spoke belongs to.
    sourceSpoke: The name of the spoke that the source forwarding rule belongs
      to.
    targetGroup: The name of the group that the target spoke belongs to.
    targetSpoke: The name of the spoke that the source forwarding rule
      propagates to.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""The propagation status.

    Values:
      CODE_UNSPECIFIED: The code is unspecified.
      READY: The propagated Private Service Connect connection is ready.
      PROPAGATING: The Private Service Connect connection is propagating. This
        is a transient state.
      ERROR_PRODUCER_PROPAGATED_CONNECTION_LIMIT_EXCEEDED: The Private Service
        Connect connection propagation failed because the VPC network or the
        project of the target spoke has exceeded the connection limit set by
        the producer.
      ERROR_PRODUCER_NAT_IP_SPACE_EXHAUSTED: The Private Service Connect
        connection propagation failed because the NAT IP subnet space has been
        exhausted. It is equivalent to the `Needs attention` status of the
        Private Service Connect connection. See
        https://cloud.google.com/vpc/docs/about-accessing-vpc-hosted-services-
        endpoints#connection-statuses.
      ERROR_PRODUCER_QUOTA_EXCEEDED: The Private Service Connect connection
        propagation failed because the
        `PSC_ILB_CONSUMER_FORWARDING_RULES_PER_PRODUCER_NETWORK` quota in the
        producer VPC network has been exceeded.
      ERROR_CONSUMER_QUOTA_EXCEEDED: The Private Service Connect connection
        propagation failed because the
        `PSC_PROPAGATED_CONNECTIONS_PER_VPC_NETWORK` quota in the consumer VPC
        network has been exceeded.
    """
    CODE_UNSPECIFIED = 0
    READY = 1
    PROPAGATING = 2
    ERROR_PRODUCER_PROPAGATED_CONNECTION_LIMIT_EXCEEDED = 3
    ERROR_PRODUCER_NAT_IP_SPACE_EXHAUSTED = 4
    ERROR_PRODUCER_QUOTA_EXCEEDED = 5
    ERROR_CONSUMER_QUOTA_EXCEEDED = 6

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  message = _messages.StringField(2)
  sourceForwardingRule = _messages.StringField(3)
  sourceGroup = _messages.StringField(4)
  sourceSpoke = _messages.StringField(5)
  targetGroup = _messages.StringField(6)
  targetSpoke = _messages.StringField(7)


class GoogleCloudNetworkconnectivityV1betaQueryHubStatusResponse(_messages.Message):
  r"""The response for HubService.QueryHubStatus.

  Fields:
    hubStatusEntries: The list of hub status.
    nextPageToken: The token for the next page of the response. To see more
      results, use this value as the page_token for your next request. If this
      value is empty, there are no more results.
  """

  hubStatusEntries = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaHubStatusEntry', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudNetworkconnectivityV1betaRegionalEndpoint(_messages.Message):
  r"""The RegionalEndpoint resource.

  Enums:
    AccessTypeValueValuesEnum: Required. The access type of this regional
      endpoint. This field is reflected in the PSC Forwarding Rule
      configuration to enable global access.

  Messages:
    LabelsValue: User-defined labels.

  Fields:
    accessType: Required. The access type of this regional endpoint. This
      field is reflected in the PSC Forwarding Rule configuration to enable
      global access.
    address: Optional. The IP Address of the Regional Endpoint. When no
      address is provided, an IP from the subnetwork is allocated. Use one of
      the following formats: * IPv4 address as in `********` * Address
      resource URI as in
      `projects/{project}/regions/{region}/addresses/{address_name}` for an
      IPv4 or IPv6 address.
    createTime: Output only. Time when the RegionalEndpoint was created.
    description: Optional. A description of this resource.
    ipAddress: Output only. The literal IP address of the PSC Forwarding Rule
      created on behalf of the customer. This field is deprecated. Use address
      instead.
    labels: User-defined labels.
    name: Output only. The name of a RegionalEndpoint. Pattern: `projects/{pro
      ject}/locations/{location}/regionalEndpoints/^[-a-z0-9](?:[-a-z0-
      9]{0,44})[a-z0-9]$`.
    network: The name of the VPC network for this private regional endpoint.
      Format: `projects/{project}/global/networks/{network}`
    pscForwardingRule: Output only. The resource reference of the PSC
      Forwarding Rule created on behalf of the customer. Format: `//compute.go
      ogleapis.com/projects/{project}/regions/{region}/forwardingRules/{forwar
      ding_rule_name}`
    subnetwork: The name of the subnetwork from which the IP address will be
      allocated. Format:
      `projects/{project}/regions/{region}/subnetworks/{subnetwork}`
    targetGoogleApi: Required. The service endpoint this private regional
      endpoint connects to. Format: `{apiname}.{region}.p.rep.googleapis.com`
      Example: "cloudkms.us-central1.p.rep.googleapis.com".
    updateTime: Output only. Time when the RegionalEndpoint was updated.
  """

  class AccessTypeValueValuesEnum(_messages.Enum):
    r"""Required. The access type of this regional endpoint. This field is
    reflected in the PSC Forwarding Rule configuration to enable global
    access.

    Values:
      ACCESS_TYPE_UNSPECIFIED: An invalid type as the default case.
      GLOBAL: This regional endpoint is accessible from all regions.
      REGIONAL: This regional endpoint is only accessible from the same region
        where it resides.
    """
    ACCESS_TYPE_UNSPECIFIED = 0
    GLOBAL = 1
    REGIONAL = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""User-defined labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  accessType = _messages.EnumField('AccessTypeValueValuesEnum', 1)
  address = _messages.StringField(2)
  createTime = _messages.StringField(3)
  description = _messages.StringField(4)
  ipAddress = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  network = _messages.StringField(8)
  pscForwardingRule = _messages.StringField(9)
  subnetwork = _messages.StringField(10)
  targetGoogleApi = _messages.StringField(11)
  updateTime = _messages.StringField(12)


class GoogleCloudNetworkconnectivityV1betaRejectHubSpokeRequest(_messages.Message):
  r"""The request for HubService.RejectHubSpoke.

  Fields:
    details: Optional. Additional information provided by the hub
      administrator.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check to see
      whether the original operation was received. If it was, the server
      ignores the second request. This behavior prevents clients from
      mistakenly creating duplicate commitments. The request ID must be a
      valid UUID, with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    spokeUri: Required. The URI of the spoke to reject from the hub.
  """

  details = _messages.StringField(1)
  requestId = _messages.StringField(2)
  spokeUri = _messages.StringField(3)


class GoogleCloudNetworkconnectivityV1betaRejectHubSpokeResponse(_messages.Message):
  r"""The response for HubService.RejectHubSpoke.

  Fields:
    spoke: The spoke that was operated on.
  """

  spoke = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaSpoke', 1)


class GoogleCloudNetworkconnectivityV1betaRejectSpokeUpdateRequest(_messages.Message):
  r"""The request for HubService.RejectSpokeUpdate.

  Fields:
    details: Optional. Additional information provided by the hub
      administrator.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check to see
      whether the original operation was received. If it was, the server
      ignores the second request. This behavior prevents clients from
      mistakenly creating duplicate commitments. The request ID must be a
      valid UUID, with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    spokeEtag: Required. The etag of the spoke to reject update.
    spokeUri: Required. The URI of the spoke to reject update.
  """

  details = _messages.StringField(1)
  requestId = _messages.StringField(2)
  spokeEtag = _messages.StringField(3)
  spokeUri = _messages.StringField(4)


class GoogleCloudNetworkconnectivityV1betaRoute(_messages.Message):
  r"""A route defines a path from VM instances within a spoke to a specific
  destination resource. Only VPC spokes have routes.

  Enums:
    StateValueValuesEnum: Output only. The current lifecycle state of the
      route.
    TypeValueValuesEnum: Output only. The route's type. Its type is determined
      by the properties of its IP address range.

  Messages:
    LabelsValue: Optional labels in key-value pair format. For more
      information about labels, see [Requirements for
      labels](https://cloud.google.com/resource-manager/docs/creating-
      managing-labels#requirements).

  Fields:
    createTime: Output only. The time the route was created.
    description: An optional description of the route.
    ipCidrRange: The destination IP address range.
    labels: Optional labels in key-value pair format. For more information
      about labels, see [Requirements for
      labels](https://cloud.google.com/resource-manager/docs/creating-
      managing-labels#requirements).
    location: Output only. The origin location of the route. Uses the
      following form: "projects/{project}/locations/{location}" Example:
      projects/1234/locations/us-central1
    name: Immutable. The name of the route. Route names must be unique. Route
      names use the following form: `projects/{project_number}/locations/globa
      l/hubs/{hub}/routeTables/{route_table_id}/routes/{route_id}`
    nextHopInterconnectAttachment: Immutable. The next-hop VLAN attachment for
      packets on this route.
    nextHopRouterApplianceInstance: Immutable. The next-hop Router appliance
      instance for packets on this route.
    nextHopSpoke: Immutable. The next-hop spoke for packets on this route.
    nextHopVpcNetwork: Immutable. The destination VPC network for packets on
      this route.
    nextHopVpnTunnel: Immutable. The next-hop VPN tunnel for packets on this
      route.
    priority: Output only. The priority of this route. Priority is used to
      break ties in cases where a destination matches more than one route. In
      these cases the route with the lowest-numbered priority value wins.
    spoke: Immutable. The spoke that this route leads to. Example:
      projects/12345/locations/global/spokes/SPOKE
    state: Output only. The current lifecycle state of the route.
    type: Output only. The route's type. Its type is determined by the
      properties of its IP address range.
    uid: Output only. The Google-generated UUID for the route. This value is
      unique across all Network Connectivity Center route resources. If a
      route is deleted and another with the same name is created, the new
      route is assigned a different `uid`.
    updateTime: Output only. The time the route was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current lifecycle state of the route.

    Values:
      STATE_UNSPECIFIED: No state information available
      CREATING: The resource's create operation is in progress.
      ACTIVE: The resource is active
      DELETING: The resource's delete operation is in progress.
      ACTIVATING: The resource's activate operation is in progress.
      DEACTIVATING: The resource's deactivate operation is in progress.
      ACCEPTING: The resource's accept operation is in progress.
      REJECTING: The resource's reject operation is in progress.
      UPDATING: The resource's update operation is in progress.
      INACTIVE: The resource is inactive.
      OBSOLETE: The hub associated with this spoke resource has been deleted.
        This state applies to spoke resources only.
      FAILED: The resource is in an undefined state due to resource creation
        or deletion failure. You can try to delete the resource later or
        contact support for help.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    ACTIVATING = 4
    DEACTIVATING = 5
    ACCEPTING = 6
    REJECTING = 7
    UPDATING = 8
    INACTIVE = 9
    OBSOLETE = 10
    FAILED = 11

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. The route's type. Its type is determined by the
    properties of its IP address range.

    Values:
      ROUTE_TYPE_UNSPECIFIED: No route type information specified
      VPC_PRIMARY_SUBNET: The route leads to a destination within the primary
        address range of the VPC network's subnet.
      VPC_SECONDARY_SUBNET: The route leads to a destination within the
        secondary address range of the VPC network's subnet.
      DYNAMIC_ROUTE: The route leads to a destination in a dynamic route.
        Dynamic routes are derived from Border Gateway Protocol (BGP)
        advertisements received from an NCC hybrid spoke.
      PSC_GLOBAL_GAPI: The route leads to a destination within the Private
        Service Connect Global Google API range of the VPC network.
    """
    ROUTE_TYPE_UNSPECIFIED = 0
    VPC_PRIMARY_SUBNET = 1
    VPC_SECONDARY_SUBNET = 2
    DYNAMIC_ROUTE = 3
    PSC_GLOBAL_GAPI = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional labels in key-value pair format. For more information about
    labels, see [Requirements for labels](https://cloud.google.com/resource-
    manager/docs/creating-managing-labels#requirements).

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  ipCidrRange = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  location = _messages.StringField(5)
  name = _messages.StringField(6)
  nextHopInterconnectAttachment = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaNextHopInterconnectAttachment', 7)
  nextHopRouterApplianceInstance = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaNextHopRouterApplianceInstance', 8)
  nextHopSpoke = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaNextHopSpoke', 9)
  nextHopVpcNetwork = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaNextHopVpcNetwork', 10)
  nextHopVpnTunnel = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaNextHopVPNTunnel', 11)
  priority = _messages.IntegerField(12)
  spoke = _messages.StringField(13)
  state = _messages.EnumField('StateValueValuesEnum', 14)
  type = _messages.EnumField('TypeValueValuesEnum', 15)
  uid = _messages.StringField(16)
  updateTime = _messages.StringField(17)


class GoogleCloudNetworkconnectivityV1betaRouteTable(_messages.Message):
  r"""A GoogleCloudNetworkconnectivityV1betaRouteTable object.

  Enums:
    StateValueValuesEnum: Output only. The current lifecycle state of this
      route table.

  Messages:
    LabelsValue: Optional labels in key-value pair format. For more
      information about labels, see [Requirements for
      labels](https://cloud.google.com/resource-manager/docs/creating-
      managing-labels#requirements).

  Fields:
    createTime: Output only. The time the route table was created.
    description: An optional description of the route table.
    labels: Optional labels in key-value pair format. For more information
      about labels, see [Requirements for
      labels](https://cloud.google.com/resource-manager/docs/creating-
      managing-labels#requirements).
    name: Immutable. The name of the route table. Route table names must be
      unique. They use the following form: `projects/{project_number}/location
      s/global/hubs/{hub}/routeTables/{route_table_id}`
    state: Output only. The current lifecycle state of this route table.
    uid: Output only. The Google-generated UUID for the route table. This
      value is unique across all route table resources. If a route table is
      deleted and another with the same name is created, the new route table
      is assigned a different `uid`.
    updateTime: Output only. The time the route table was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current lifecycle state of this route table.

    Values:
      STATE_UNSPECIFIED: No state information available
      CREATING: The resource's create operation is in progress.
      ACTIVE: The resource is active
      DELETING: The resource's delete operation is in progress.
      ACTIVATING: The resource's activate operation is in progress.
      DEACTIVATING: The resource's deactivate operation is in progress.
      ACCEPTING: The resource's accept operation is in progress.
      REJECTING: The resource's reject operation is in progress.
      UPDATING: The resource's update operation is in progress.
      INACTIVE: The resource is inactive.
      OBSOLETE: The hub associated with this spoke resource has been deleted.
        This state applies to spoke resources only.
      FAILED: The resource is in an undefined state due to resource creation
        or deletion failure. You can try to delete the resource later or
        contact support for help.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    ACTIVATING = 4
    DEACTIVATING = 5
    ACCEPTING = 6
    REJECTING = 7
    UPDATING = 8
    INACTIVE = 9
    OBSOLETE = 10
    FAILED = 11

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional labels in key-value pair format. For more information about
    labels, see [Requirements for labels](https://cloud.google.com/resource-
    manager/docs/creating-managing-labels#requirements).

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)
  uid = _messages.StringField(6)
  updateTime = _messages.StringField(7)


class GoogleCloudNetworkconnectivityV1betaRouterApplianceInstance(_messages.Message):
  r"""A router appliance instance is a Compute Engine virtual machine (VM)
  instance that acts as a BGP speaker. A router appliance instance is
  specified by the URI of the VM and the internal IP address of one of the
  VM's network interfaces.

  Fields:
    ipAddress: The IP address on the VM to use for peering.
    virtualMachine: The URI of the VM.
  """

  ipAddress = _messages.StringField(1)
  virtualMachine = _messages.StringField(2)


class GoogleCloudNetworkconnectivityV1betaRoutingVPC(_messages.Message):
  r"""RoutingVPC contains information about the VPC networks associated with
  the spokes of a Network Connectivity Center hub.

  Fields:
    requiredForNewSiteToSiteDataTransferSpokes: Output only. If true,
      indicates that this VPC network is currently associated with spokes that
      use the data transfer feature (spokes where the
      site_to_site_data_transfer field is set to true). If you create new
      spokes that use data transfer, they must be associated with this VPC
      network. At most, one VPC network will have this field set to true.
    uri: The URI of the VPC network.
  """

  requiredForNewSiteToSiteDataTransferSpokes = _messages.BooleanField(1)
  uri = _messages.StringField(2)


class GoogleCloudNetworkconnectivityV1betaServiceConfig(_messages.Message):
  r"""Specifies the Multicloud Data Transfer supported services configuration.
  This includes either the network tier or the request endpoint. If end of
  support for multicloud data transfer is planned for a service's network tier
  or request endpoint, the end time will be provided.

  Enums:
    EligibilityCriteriaValueValuesEnum: Output only. The eligibility criteria
      for the service. The user has to meet the eligibility criteria specified
      here for the service to qualify for multicloud data transfer.

  Fields:
    eligibilityCriteria: Output only. The eligibility criteria for the
      service. The user has to meet the eligibility criteria specified here
      for the service to qualify for multicloud data transfer.
    supportEndTime: Output only. The eligibility criteria support end time. If
      the end time is not specified, no planned end time is available.
  """

  class EligibilityCriteriaValueValuesEnum(_messages.Enum):
    r"""Output only. The eligibility criteria for the service. The user has to
    meet the eligibility criteria specified here for the service to qualify
    for multicloud data transfer.

    Values:
      ELIGIBILITY_CRITERIA_UNSPECIFIED: An invalid eligibility criteria as the
        default case.
      NETWORK_SERVICE_TIER_PREMIUM_ONLY: The service is eligible for
        multicloud data transfer only for the premium network tier.
      NETWORK_SERVICE_TIER_STANDARD_ONLY: The service is eligible for
        multicloud data transfer only for the standard network tier.
      REQUEST_ENDPOINT_REGIONAL_ENDPOINT_ONLY: The service is eligible for
        multicloud data transfer only for the regional endpoint.
    """
    ELIGIBILITY_CRITERIA_UNSPECIFIED = 0
    NETWORK_SERVICE_TIER_PREMIUM_ONLY = 1
    NETWORK_SERVICE_TIER_STANDARD_ONLY = 2
    REQUEST_ENDPOINT_REGIONAL_ENDPOINT_ONLY = 3

  eligibilityCriteria = _messages.EnumField('EligibilityCriteriaValueValuesEnum', 1)
  supportEndTime = _messages.StringField(2)


class GoogleCloudNetworkconnectivityV1betaSpoke(_messages.Message):
  r"""A Network Connectivity Center spoke represents one or more network
  connectivity resources. When you create a spoke, you associate it with a
  hub. You must also identify a value for exactly one of the following fields:
  * linked_vpn_tunnels * linked_interconnect_attachments *
  linked_router_appliance_instances * linked_vpc_network

  Enums:
    SpokeTypeValueValuesEnum: Output only. The type of resource associated
      with the spoke.
    StateValueValuesEnum: Output only. The current lifecycle state of this
      spoke.

  Messages:
    LabelsValue: Optional labels in key-value pair format. For more
      information about labels, see [Requirements for
      labels](https://cloud.google.com/resource-manager/docs/creating-
      managing-labels#requirements).

  Fields:
    createTime: Output only. The time the spoke was created.
    description: Optional. An optional description of the spoke.
    etag: Optional. This checksum is computed by the server based on the value
      of other fields, and may be sent on update and delete requests to ensure
      the client has an up-to-date value before proceeding.
    fieldPathsPendingUpdate: Optional. The list of fields waiting for hub
      administration's approval.
    gateway: Optional. This is a gateway that can apply specialized processing
      to traffic going through it.
    group: Optional. The name of the group that this spoke is associated with.
    hub: Immutable. The name of the hub that this spoke is attached to.
    labels: Optional labels in key-value pair format. For more information
      about labels, see [Requirements for
      labels](https://cloud.google.com/resource-manager/docs/creating-
      managing-labels#requirements).
    linkedInterconnectAttachments: Optional. VLAN attachments that are
      associated with the spoke.
    linkedProducerVpcNetwork: Optional. The linked producer VPC that is
      associated with the spoke.
    linkedRouterApplianceInstances: Optional. Router appliance instances that
      are associated with the spoke.
    linkedVpcNetwork: Optional. VPC network that is associated with the spoke.
    linkedVpnTunnels: Optional. VPN tunnels that are associated with the
      spoke.
    name: Immutable. The name of the spoke. Spoke names must be unique. They
      use the following form:
      `projects/{project_number}/locations/{region}/spokes/{spoke_id}`
    reasons: Output only. The reasons for current state of the spoke.
    spokeType: Output only. The type of resource associated with the spoke.
    state: Output only. The current lifecycle state of this spoke.
    uniqueId: Output only. The Google-generated UUID for the spoke. This value
      is unique across all spoke resources. If a spoke is deleted and another
      with the same name is created, the new spoke is assigned a different
      `unique_id`.
    updateTime: Output only. The time the spoke was last updated.
  """

  class SpokeTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of resource associated with the spoke.

    Values:
      SPOKE_TYPE_UNSPECIFIED: Unspecified spoke type.
      VPN_TUNNEL: Spokes associated with VPN tunnels.
      INTERCONNECT_ATTACHMENT: Spokes associated with VLAN attachments.
      ROUTER_APPLIANCE: Spokes associated with router appliance instances.
      VPC_NETWORK: Spokes associated with VPC networks.
      GATEWAY: Spokes that are NCC gateways.
      PRODUCER_VPC_NETWORK: Spokes that are backed by a producer VPC network.
    """
    SPOKE_TYPE_UNSPECIFIED = 0
    VPN_TUNNEL = 1
    INTERCONNECT_ATTACHMENT = 2
    ROUTER_APPLIANCE = 3
    VPC_NETWORK = 4
    GATEWAY = 5
    PRODUCER_VPC_NETWORK = 6

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current lifecycle state of this spoke.

    Values:
      STATE_UNSPECIFIED: No state information available
      CREATING: The resource's create operation is in progress.
      ACTIVE: The resource is active
      DELETING: The resource's delete operation is in progress.
      ACTIVATING: The resource's activate operation is in progress.
      DEACTIVATING: The resource's deactivate operation is in progress.
      ACCEPTING: The resource's accept operation is in progress.
      REJECTING: The resource's reject operation is in progress.
      UPDATING: The resource's update operation is in progress.
      INACTIVE: The resource is inactive.
      OBSOLETE: The hub associated with this spoke resource has been deleted.
        This state applies to spoke resources only.
      FAILED: The resource is in an undefined state due to resource creation
        or deletion failure. You can try to delete the resource later or
        contact support for help.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    ACTIVATING = 4
    DEACTIVATING = 5
    ACCEPTING = 6
    REJECTING = 7
    UPDATING = 8
    INACTIVE = 9
    OBSOLETE = 10
    FAILED = 11

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional labels in key-value pair format. For more information about
    labels, see [Requirements for labels](https://cloud.google.com/resource-
    manager/docs/creating-managing-labels#requirements).

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  etag = _messages.StringField(3)
  fieldPathsPendingUpdate = _messages.StringField(4, repeated=True)
  gateway = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaGateway', 5)
  group = _messages.StringField(6)
  hub = _messages.StringField(7)
  labels = _messages.MessageField('LabelsValue', 8)
  linkedInterconnectAttachments = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaLinkedInterconnectAttachments', 9)
  linkedProducerVpcNetwork = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaLinkedProducerVpcNetwork', 10)
  linkedRouterApplianceInstances = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaLinkedRouterApplianceInstances', 11)
  linkedVpcNetwork = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaLinkedVpcNetwork', 12)
  linkedVpnTunnels = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaLinkedVpnTunnels', 13)
  name = _messages.StringField(14)
  reasons = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaStateReason', 15, repeated=True)
  spokeType = _messages.EnumField('SpokeTypeValueValuesEnum', 16)
  state = _messages.EnumField('StateValueValuesEnum', 17)
  uniqueId = _messages.StringField(18)
  updateTime = _messages.StringField(19)


class GoogleCloudNetworkconnectivityV1betaSpokeStateCount(_messages.Message):
  r"""The number of spokes that are in a particular state and associated with
  a given hub.

  Enums:
    StateValueValuesEnum: Output only. The state of the spokes.

  Fields:
    count: Output only. The total number of spokes that are in this state and
      associated with a given hub.
    state: Output only. The state of the spokes.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the spokes.

    Values:
      STATE_UNSPECIFIED: No state information available
      CREATING: The resource's create operation is in progress.
      ACTIVE: The resource is active
      DELETING: The resource's delete operation is in progress.
      ACTIVATING: The resource's activate operation is in progress.
      DEACTIVATING: The resource's deactivate operation is in progress.
      ACCEPTING: The resource's accept operation is in progress.
      REJECTING: The resource's reject operation is in progress.
      UPDATING: The resource's update operation is in progress.
      INACTIVE: The resource is inactive.
      OBSOLETE: The hub associated with this spoke resource has been deleted.
        This state applies to spoke resources only.
      FAILED: The resource is in an undefined state due to resource creation
        or deletion failure. You can try to delete the resource later or
        contact support for help.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    ACTIVATING = 4
    DEACTIVATING = 5
    ACCEPTING = 6
    REJECTING = 7
    UPDATING = 8
    INACTIVE = 9
    OBSOLETE = 10
    FAILED = 11

  count = _messages.IntegerField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class GoogleCloudNetworkconnectivityV1betaSpokeStateReasonCount(_messages.Message):
  r"""The number of spokes in the hub that are inactive for this reason.

  Enums:
    StateReasonCodeValueValuesEnum: Output only. The reason that a spoke is
      inactive.

  Fields:
    count: Output only. The total number of spokes that are inactive for a
      particular reason and associated with a given hub.
    stateReasonCode: Output only. The reason that a spoke is inactive.
  """

  class StateReasonCodeValueValuesEnum(_messages.Enum):
    r"""Output only. The reason that a spoke is inactive.

    Values:
      CODE_UNSPECIFIED: No information available.
      PENDING_REVIEW: The proposed spoke is pending review.
      REJECTED: The proposed spoke has been rejected by the hub administrator.
      PAUSED: The spoke has been deactivated internally.
      FAILED: Network Connectivity Center encountered errors while accepting
        the spoke.
      UPDATE_PENDING_REVIEW: The proposed spoke update is pending review.
      UPDATE_REJECTED: The proposed spoke update has been rejected by the hub
        administrator.
      UPDATE_FAILED: Network Connectivity Center encountered errors while
        accepting the spoke update.
    """
    CODE_UNSPECIFIED = 0
    PENDING_REVIEW = 1
    REJECTED = 2
    PAUSED = 3
    FAILED = 4
    UPDATE_PENDING_REVIEW = 5
    UPDATE_REJECTED = 6
    UPDATE_FAILED = 7

  count = _messages.IntegerField(1)
  stateReasonCode = _messages.EnumField('StateReasonCodeValueValuesEnum', 2)


class GoogleCloudNetworkconnectivityV1betaSpokeSummary(_messages.Message):
  r"""Summarizes information about the spokes associated with a hub. The
  summary includes a count of spokes according to type and according to state.
  If any spokes are inactive, the summary also lists the reasons they are
  inactive, including a count for each reason.

  Fields:
    spokeStateCounts: Output only. Counts the number of spokes that are in
      each state and associated with a given hub.
    spokeStateReasonCounts: Output only. Counts the number of spokes that are
      inactive for each possible reason and associated with a given hub.
    spokeTypeCounts: Output only. Counts the number of spokes of each type
      that are associated with a specific hub.
  """

  spokeStateCounts = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaSpokeStateCount', 1, repeated=True)
  spokeStateReasonCounts = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaSpokeStateReasonCount', 2, repeated=True)
  spokeTypeCounts = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaSpokeTypeCount', 3, repeated=True)


class GoogleCloudNetworkconnectivityV1betaSpokeTypeCount(_messages.Message):
  r"""The number of spokes of a given type that are associated with a specific
  hub. The type indicates what kind of resource is associated with the spoke.

  Enums:
    SpokeTypeValueValuesEnum: Output only. The type of the spokes.

  Fields:
    count: Output only. The total number of spokes of this type that are
      associated with the hub.
    spokeType: Output only. The type of the spokes.
  """

  class SpokeTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the spokes.

    Values:
      SPOKE_TYPE_UNSPECIFIED: Unspecified spoke type.
      VPN_TUNNEL: Spokes associated with VPN tunnels.
      INTERCONNECT_ATTACHMENT: Spokes associated with VLAN attachments.
      ROUTER_APPLIANCE: Spokes associated with router appliance instances.
      VPC_NETWORK: Spokes associated with VPC networks.
      GATEWAY: Spokes that are NCC gateways.
      PRODUCER_VPC_NETWORK: Spokes that are backed by a producer VPC network.
    """
    SPOKE_TYPE_UNSPECIFIED = 0
    VPN_TUNNEL = 1
    INTERCONNECT_ATTACHMENT = 2
    ROUTER_APPLIANCE = 3
    VPC_NETWORK = 4
    GATEWAY = 5
    PRODUCER_VPC_NETWORK = 6

  count = _messages.IntegerField(1)
  spokeType = _messages.EnumField('SpokeTypeValueValuesEnum', 2)


class GoogleCloudNetworkconnectivityV1betaStateMetadata(_messages.Message):
  r"""The state and activation time details of the resource state.

  Enums:
    StateValueValuesEnum: Output only. The state of the resource.

  Fields:
    effectiveTime: Output only. This field will be accompanied only with
      transient states (PENDING_ADD, PENDING_DELETE, PENDING_SUSPENSION) and
      denotes the time when the transient state of the resource will be
      effective. For instance, if the state is "ADDING," this field will show
      the time the resource transitions to "ACTIVE." Similarly, if the state
      is "PENDING_DELETE," it will show the deletion time.
    state: Output only. The state of the resource.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the resource.

    Values:
      STATE_UNSPECIFIED: An invalid state as the default case.
      ADDING: The resource is being added.
      ACTIVE: The resource is in use.
      DELETING: The resource is being deleted.
      SUSPENDING: The resource is being suspended.
      SUSPENDED: The resource is not in use for billing and is suspended.
    """
    STATE_UNSPECIFIED = 0
    ADDING = 1
    ACTIVE = 2
    DELETING = 3
    SUSPENDING = 4
    SUSPENDED = 5

  effectiveTime = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class GoogleCloudNetworkconnectivityV1betaStateReason(_messages.Message):
  r"""The reason a spoke is inactive.

  Enums:
    CodeValueValuesEnum: The code associated with this reason.

  Fields:
    code: The code associated with this reason.
    message: Human-readable details about this reason.
    userDetails: Additional information provided by the user in the
      RejectSpoke call.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""The code associated with this reason.

    Values:
      CODE_UNSPECIFIED: No information available.
      PENDING_REVIEW: The proposed spoke is pending review.
      REJECTED: The proposed spoke has been rejected by the hub administrator.
      PAUSED: The spoke has been deactivated internally.
      FAILED: Network Connectivity Center encountered errors while accepting
        the spoke.
      UPDATE_PENDING_REVIEW: The proposed spoke update is pending review.
      UPDATE_REJECTED: The proposed spoke update has been rejected by the hub
        administrator.
      UPDATE_FAILED: Network Connectivity Center encountered errors while
        accepting the spoke update.
    """
    CODE_UNSPECIFIED = 0
    PENDING_REVIEW = 1
    REJECTED = 2
    PAUSED = 3
    FAILED = 4
    UPDATE_PENDING_REVIEW = 5
    UPDATE_REJECTED = 6
    UPDATE_FAILED = 7

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  message = _messages.StringField(2)
  userDetails = _messages.StringField(3)


class GoogleCloudNetworkconnectivityV1betaStateTimeline(_messages.Message):
  r"""The timeline of pending states for a resource.

  Fields:
    states: Output only. The state and activation time details of the resource
      state.
  """

  states = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaStateMetadata', 1, repeated=True)


class GoogleCloudNetworkconnectivityV1betaVirtualMachine(_messages.Message):
  r"""VM instances that this policy-based route applies to.

  Fields:
    tags: Optional. A list of VM instance tags that this policy-based route
      applies to. VM instances that have ANY of tags specified here installs
      this PBR.
  """

  tags = _messages.StringField(1, repeated=True)


class GoogleCloudNetworkconnectivityV1betaWarnings(_messages.Message):
  r"""Informational warning message.

  Enums:
    CodeValueValuesEnum: Output only. A warning code, if applicable.

  Messages:
    DataValue: Output only. Metadata about this warning in key: value format.
      The key should provides more detail on the warning being returned. For
      example, for warnings where there are no results in a list request for a
      particular zone, this key might be scope and the key value might be the
      zone name. Other examples might be a key indicating a deprecated
      resource and a suggested replacement.

  Fields:
    code: Output only. A warning code, if applicable.
    data: Output only. Metadata about this warning in key: value format. The
      key should provides more detail on the warning being returned. For
      example, for warnings where there are no results in a list request for a
      particular zone, this key might be scope and the key value might be the
      zone name. Other examples might be a key indicating a deprecated
      resource and a suggested replacement.
    warningMessage: Output only. A human-readable description of the warning
      code.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Output only. A warning code, if applicable.

    Values:
      WARNING_UNSPECIFIED: Default value.
      RESOURCE_NOT_ACTIVE: The policy-based route is not active and
        functioning. Common causes are that the dependent network was deleted
        or the resource project was turned off.
      RESOURCE_BEING_MODIFIED: The policy-based route is being modified (e.g.
        created/deleted) at this time.
    """
    WARNING_UNSPECIFIED = 0
    RESOURCE_NOT_ACTIVE = 1
    RESOURCE_BEING_MODIFIED = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DataValue(_messages.Message):
    r"""Output only. Metadata about this warning in key: value format. The key
    should provides more detail on the warning being returned. For example,
    for warnings where there are no results in a list request for a particular
    zone, this key might be scope and the key value might be the zone name.
    Other examples might be a key indicating a deprecated resource and a
    suggested replacement.

    Messages:
      AdditionalProperty: An additional property for a DataValue object.

    Fields:
      additionalProperties: Additional properties of type DataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  data = _messages.MessageField('DataValue', 2)
  warningMessage = _messages.StringField(3)


class GoogleIamV1AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('GoogleIamV1AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class GoogleIamV1AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class GoogleIamV1Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('GoogleTypeExpr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class GoogleIamV1Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('GoogleIamV1AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('GoogleIamV1Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleIamV1SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('GoogleIamV1Policy', 1)
  updateMask = _messages.StringField(2)


class GoogleIamV1TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class GoogleIamV1TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class GoogleLongrunningCancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class GoogleLongrunningListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('GoogleLongrunningOperation', 2, repeated=True)


class GoogleLongrunningOperation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('GoogleRpcStatus', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class GoogleRpcStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class GoogleTypeExpr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class NetworkconnectivityProjectsLocationsGetRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class NetworkconnectivityProjectsLocationsGlobalHubsAcceptSpokeRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalHubsAcceptSpokeRequest
  object.

  Fields:
    googleCloudNetworkconnectivityV1betaAcceptHubSpokeRequest: A
      GoogleCloudNetworkconnectivityV1betaAcceptHubSpokeRequest resource to be
      passed as the request body.
    name: Required. The name of the hub into which to accept the spoke.
  """

  googleCloudNetworkconnectivityV1betaAcceptHubSpokeRequest = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaAcceptHubSpokeRequest', 1)
  name = _messages.StringField(2, required=True)


class NetworkconnectivityProjectsLocationsGlobalHubsAcceptSpokeUpdateRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalHubsAcceptSpokeUpdateRequest
  object.

  Fields:
    googleCloudNetworkconnectivityV1betaAcceptSpokeUpdateRequest: A
      GoogleCloudNetworkconnectivityV1betaAcceptSpokeUpdateRequest resource to
      be passed as the request body.
    name: Required. The name of the hub to accept spoke update.
  """

  googleCloudNetworkconnectivityV1betaAcceptSpokeUpdateRequest = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaAcceptSpokeUpdateRequest', 1)
  name = _messages.StringField(2, required=True)


class NetworkconnectivityProjectsLocationsGlobalHubsCreateRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalHubsCreateRequest object.

  Fields:
    googleCloudNetworkconnectivityV1betaHub: A
      GoogleCloudNetworkconnectivityV1betaHub resource to be passed as the
      request body.
    hubId: Required. A unique identifier for the hub.
    parent: Required. The parent resource.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check to see
      whether the original operation was received. If it was, the server
      ignores the second request. This behavior prevents clients from
      mistakenly creating duplicate commitments. The request ID must be a
      valid UUID, with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  googleCloudNetworkconnectivityV1betaHub = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaHub', 1)
  hubId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworkconnectivityProjectsLocationsGlobalHubsDeleteRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalHubsDeleteRequest object.

  Fields:
    name: Required. The name of the hub to delete.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check to see
      whether the original operation was received. If it was, the server
      ignores the second request. This behavior prevents clients from
      mistakenly creating duplicate commitments. The request ID must be a
      valid UUID, with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworkconnectivityProjectsLocationsGlobalHubsGetIamPolicyRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalHubsGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NetworkconnectivityProjectsLocationsGlobalHubsGetRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalHubsGetRequest object.

  Fields:
    name: Required. The name of the hub resource to get.
  """

  name = _messages.StringField(1, required=True)


class NetworkconnectivityProjectsLocationsGlobalHubsGroupsGetIamPolicyRequest(_messages.Message):
  r"""A
  NetworkconnectivityProjectsLocationsGlobalHubsGroupsGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NetworkconnectivityProjectsLocationsGlobalHubsGroupsGetRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalHubsGroupsGetRequest object.

  Fields:
    name: Required. The name of the route table resource.
  """

  name = _messages.StringField(1, required=True)


class NetworkconnectivityProjectsLocationsGlobalHubsGroupsListRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalHubsGroupsListRequest
  object.

  Fields:
    filter: An expression that filters the list of results.
    orderBy: Sort the results by a certain order.
    pageSize: The maximum number of results to return per page.
    pageToken: The page token.
    parent: Required. The parent resource's name.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkconnectivityProjectsLocationsGlobalHubsGroupsPatchRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalHubsGroupsPatchRequest
  object.

  Fields:
    googleCloudNetworkconnectivityV1betaGroup: A
      GoogleCloudNetworkconnectivityV1betaGroup resource to be passed as the
      request body.
    name: Immutable. The name of the group. Group names must be unique. They
      use the following form: `projects/{project_number}/locations/global/hubs
      /{hub}/groups/{group_id}`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check to see
      whether the original operation was received. If it was, the server
      ignores the second request. This behavior prevents clients from
      mistakenly creating duplicate commitments. The request ID must be a
      valid UUID, with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. In the case of an update to an existing group, field
      mask is used to specify the fields to be overwritten. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field is overwritten if it is in the mask. If the user does
      not provide a mask, then all fields are overwritten.
  """

  googleCloudNetworkconnectivityV1betaGroup = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaGroup', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworkconnectivityProjectsLocationsGlobalHubsGroupsSetIamPolicyRequest(_messages.Message):
  r"""A
  NetworkconnectivityProjectsLocationsGlobalHubsGroupsSetIamPolicyRequest
  object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class NetworkconnectivityProjectsLocationsGlobalHubsGroupsTestIamPermissionsRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalHubsGroupsTestIamPermissions
  Request object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class NetworkconnectivityProjectsLocationsGlobalHubsListRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalHubsListRequest object.

  Fields:
    filter: An expression that filters the list of results.
    orderBy: Sort the results by a certain order.
    pageSize: The maximum number of results per page to return.
    pageToken: The page token.
    parent: Required. The parent resource's name.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkconnectivityProjectsLocationsGlobalHubsListSpokesRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalHubsListSpokesRequest
  object.

  Enums:
    ViewValueValuesEnum: The view of the spoke to return. The view that you
      use determines which spoke fields are included in the response.

  Fields:
    filter: An expression that filters the list of results.
    name: Required. The name of the hub.
    orderBy: Sort the results by name or create_time.
    pageSize: The maximum number of results to return per page.
    pageToken: The page token.
    spokeLocations: A list of locations. Specify one of the following:
      `[global]`, a single region (for example, `[us-central1]`), or a
      combination of values (for example, `[global, us-central1, us-west1]`).
      If the spoke_locations field is populated, the list of results includes
      only spokes in the specified location. If the spoke_locations field is
      not populated, the list of results includes spokes in all locations.
    view: The view of the spoke to return. The view that you use determines
      which spoke fields are included in the response.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""The view of the spoke to return. The view that you use determines
    which spoke fields are included in the response.

    Values:
      SPOKE_VIEW_UNSPECIFIED: The spoke view is unspecified. When the spoke
        view is unspecified, the API returns the same fields as the `BASIC`
        view.
      BASIC: Includes `name`, `create_time`, `hub`, `unique_id`, `state`,
        `reasons`, and `spoke_type`. This is the default value.
      DETAILED: Includes all spoke fields except `labels`. You can use the
        `DETAILED` view only when you set the `spoke_locations` field to
        `[global]`.
    """
    SPOKE_VIEW_UNSPECIFIED = 0
    BASIC = 1
    DETAILED = 2

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  orderBy = _messages.StringField(3)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)
  spokeLocations = _messages.StringField(6, repeated=True)
  view = _messages.EnumField('ViewValueValuesEnum', 7)


class NetworkconnectivityProjectsLocationsGlobalHubsPatchRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalHubsPatchRequest object.

  Fields:
    googleCloudNetworkconnectivityV1betaHub: A
      GoogleCloudNetworkconnectivityV1betaHub resource to be passed as the
      request body.
    name: Immutable. The name of the hub. Hub names must be unique. They use
      the following form:
      `projects/{project_number}/locations/global/hubs/{hub_id}`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check to see
      whether the original operation was received. If it was, the server
      ignores the second request. This behavior prevents clients from
      mistakenly creating duplicate commitments. The request ID must be a
      valid UUID, with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. In the case of an update to an existing hub, field
      mask is used to specify the fields to be overwritten. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field is overwritten if it is in the mask. If the user does
      not provide a mask, then all fields are overwritten.
  """

  googleCloudNetworkconnectivityV1betaHub = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaHub', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworkconnectivityProjectsLocationsGlobalHubsQueryStatusRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalHubsQueryStatusRequest
  object.

  Fields:
    filter: Optional. An expression that filters the list of results. The
      filter can be used to filter the results by the following fields: *
      `psc_propagation_status.source_spoke` *
      `psc_propagation_status.source_group` *
      `psc_propagation_status.source_forwarding_rule` *
      `psc_propagation_status.target_spoke` *
      `psc_propagation_status.target_group` * `psc_propagation_status.code` *
      `psc_propagation_status.message`
    groupBy: Optional. Aggregate the results by the specified fields. A comma-
      separated list of any of these fields: *
      `psc_propagation_status.source_spoke` *
      `psc_propagation_status.source_group` *
      `psc_propagation_status.source_forwarding_rule` *
      `psc_propagation_status.target_spoke` *
      `psc_propagation_status.target_group` * `psc_propagation_status.code`
    name: Required. The name of the hub.
    orderBy: Optional. Sort the results in ascending order by the specified
      fields. A comma-separated list of any of these fields: *
      `psc_propagation_status.source_spoke` *
      `psc_propagation_status.source_group` *
      `psc_propagation_status.source_forwarding_rule` *
      `psc_propagation_status.target_spoke` *
      `psc_propagation_status.target_group` * `psc_propagation_status.code` If
      `group_by` is set, the value of the `order_by` field must be the same as
      or a subset of the `group_by` field.
    pageSize: Optional. The maximum number of results to return per page.
    pageToken: Optional. The page token.
  """

  filter = _messages.StringField(1)
  groupBy = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  orderBy = _messages.StringField(4)
  pageSize = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(6)


class NetworkconnectivityProjectsLocationsGlobalHubsRejectSpokeRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalHubsRejectSpokeRequest
  object.

  Fields:
    googleCloudNetworkconnectivityV1betaRejectHubSpokeRequest: A
      GoogleCloudNetworkconnectivityV1betaRejectHubSpokeRequest resource to be
      passed as the request body.
    name: Required. The name of the hub from which to reject the spoke.
  """

  googleCloudNetworkconnectivityV1betaRejectHubSpokeRequest = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaRejectHubSpokeRequest', 1)
  name = _messages.StringField(2, required=True)


class NetworkconnectivityProjectsLocationsGlobalHubsRejectSpokeUpdateRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalHubsRejectSpokeUpdateRequest
  object.

  Fields:
    googleCloudNetworkconnectivityV1betaRejectSpokeUpdateRequest: A
      GoogleCloudNetworkconnectivityV1betaRejectSpokeUpdateRequest resource to
      be passed as the request body.
    name: Required. The name of the hub to reject spoke update.
  """

  googleCloudNetworkconnectivityV1betaRejectSpokeUpdateRequest = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaRejectSpokeUpdateRequest', 1)
  name = _messages.StringField(2, required=True)


class NetworkconnectivityProjectsLocationsGlobalHubsRouteTablesGetRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalHubsRouteTablesGetRequest
  object.

  Fields:
    name: Required. The name of the route table resource.
  """

  name = _messages.StringField(1, required=True)


class NetworkconnectivityProjectsLocationsGlobalHubsRouteTablesListRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalHubsRouteTablesListRequest
  object.

  Fields:
    filter: An expression that filters the list of results.
    orderBy: Sort the results by a certain order.
    pageSize: The maximum number of results to return per page.
    pageToken: The page token.
    parent: Required. The parent resource's name.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkconnectivityProjectsLocationsGlobalHubsRouteTablesRoutesGetRequest(_messages.Message):
  r"""A
  NetworkconnectivityProjectsLocationsGlobalHubsRouteTablesRoutesGetRequest
  object.

  Fields:
    name: Required. The name of the route resource.
  """

  name = _messages.StringField(1, required=True)


class NetworkconnectivityProjectsLocationsGlobalHubsRouteTablesRoutesListRequest(_messages.Message):
  r"""A
  NetworkconnectivityProjectsLocationsGlobalHubsRouteTablesRoutesListRequest
  object.

  Fields:
    filter: An expression that filters the list of results.
    orderBy: Sort the results by a certain order.
    pageSize: The maximum number of results to return per page.
    pageToken: The page token.
    parent: Required. The parent resource's name.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkconnectivityProjectsLocationsGlobalHubsSetIamPolicyRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalHubsSetIamPolicyRequest
  object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class NetworkconnectivityProjectsLocationsGlobalHubsTestIamPermissionsRequest(_messages.Message):
  r"""A
  NetworkconnectivityProjectsLocationsGlobalHubsTestIamPermissionsRequest
  object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesCreateRequest(_messages.Message):
  r"""A
  NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesCreateRequest
  object.

  Fields:
    googleCloudNetworkconnectivityV1betaPolicyBasedRoute: A
      GoogleCloudNetworkconnectivityV1betaPolicyBasedRoute resource to be
      passed as the request body.
    parent: Required. The parent resource's name of the PolicyBasedRoute.
    policyBasedRouteId: Required. Unique id for the policy-based route to
      create. Provided by the client when the resource is created. The name
      must comply with https://google.aip.dev/122#resource-id-segments.
      Specifically, the name must be 1-63 characters long and match the
      regular expression [a-z]([a-z0-9-]*[a-z0-9])?. The first character must
      be a lowercase letter, and all following characters (except for the last
      character) must be a dash, lowercase letter, or digit. The last
      character must be a lowercase letter or digit.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      knows to ignore the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  googleCloudNetworkconnectivityV1betaPolicyBasedRoute = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaPolicyBasedRoute', 1)
  parent = _messages.StringField(2, required=True)
  policyBasedRouteId = _messages.StringField(3)
  requestId = _messages.StringField(4)


class NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesDeleteRequest(_messages.Message):
  r"""A
  NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesDeleteRequest
  object.

  Fields:
    name: Required. Name of the policy-based route resource to delete.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      knows to ignore the request if it has already been completed. The server
      guarantees that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesGetIamPolicyRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesGetIamPolic
  yRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesGetRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesGetRequest
  object.

  Fields:
    name: Required. Name of the PolicyBasedRoute resource to get.
  """

  name = _messages.StringField(1, required=True)


class NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesListRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesListRequest
  object.

  Fields:
    filter: A filter expression that filters the results listed in the
      response.
    orderBy: Sort the results by a certain order.
    pageSize: The maximum number of results per page that should be returned.
    pageToken: The page token.
    parent: Required. The parent resource's name.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesSetIamPolicyRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesSetIamPolic
  yRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesTestIamPermissionsRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesTestIamPerm
  issionsRequest object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class NetworkconnectivityProjectsLocationsListRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsCreateRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsCreat
  eRequest object.

  Fields:
    googleCloudNetworkconnectivityV1betaMulticloudDataTransferConfig: A
      GoogleCloudNetworkconnectivityV1betaMulticloudDataTransferConfig
      resource to be passed as the request body.
    multicloudDataTransferConfigId: Required. The ID to use for the
      MulticloudDataTransferConfig, which will become the final component of
      the MulticloudDataTransferConfig's resource name.
    parent: Required. The parent resource's name
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate
      MulticloudDataTransferConfigs. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  googleCloudNetworkconnectivityV1betaMulticloudDataTransferConfig = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaMulticloudDataTransferConfig', 1)
  multicloudDataTransferConfigId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsDeleteRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsDelet
  eRequest object.

  Fields:
    etag: Optional. The etag is computed by the server, and may be sent on
      update and delete requests to ensure the client has an up-to-date value
      before proceeding.
    name: Required. The name of the MulticloudDataTransferConfig resource to
      delete.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate
      MulticloudDataTransferConfigs. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsDestinationsCreateRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsDesti
  nationsCreateRequest object.

  Fields:
    destinationId: Required. The ID to use for the Destination, which will
      become the final component of the Destination's resource name.
    googleCloudNetworkconnectivityV1betaDestination: A
      GoogleCloudNetworkconnectivityV1betaDestination resource to be passed as
      the request body.
    parent: Required. The parent resource's name
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate Destinations.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  destinationId = _messages.StringField(1)
  googleCloudNetworkconnectivityV1betaDestination = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaDestination', 2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsDestinationsDeleteRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsDesti
  nationsDeleteRequest object.

  Fields:
    etag: Optional. The etag is computed by the server, and may be sent on
      update and delete requests to ensure the client has an up-to-date value
      before proceeding.
    name: Required. The name of the Destination resource to delete.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsDestinationsGetRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsDesti
  nationsGetRequest object.

  Fields:
    name: Required. Name of the Destination to get.
  """

  name = _messages.StringField(1, required=True)


class NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsDestinationsListRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsDesti
  nationsListRequest object.

  Fields:
    filter: Optional. A filter expression that filters the results listed in
      the response.
    orderBy: Optional. Sort the results by a certain order.
    pageSize: Optional. The maximum number of results per page that should be
      returned.
    pageToken: Optional. The page token.
    parent: Required. The parent resource's name
    returnPartialSuccess: Optional. If true, allow partial responses for
      multi-regional Aggregated List requests.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)
  returnPartialSuccess = _messages.BooleanField(6)


class NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsDestinationsPatchRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsDesti
  nationsPatchRequest object.

  Fields:
    googleCloudNetworkconnectivityV1betaDestination: A
      GoogleCloudNetworkconnectivityV1betaDestination resource to be passed as
      the request body.
    name: Identifier. The name of the Destination resource. Format: `projects/
      {project}/locations/{location}/multicloudDataTransferConfigs/{multicloud
      _data_transfer_config}/destinations/{destination}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the Destination resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  googleCloudNetworkconnectivityV1betaDestination = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaDestination', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsGetRequest(_messages.Message):
  r"""A
  NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsGetRequest
  object.

  Fields:
    name: Required. Name of the MulticloudDataTransferConfig to get.
  """

  name = _messages.StringField(1, required=True)


class NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsListRequest(_messages.Message):
  r"""A
  NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsListRequest
  object.

  Fields:
    filter: Optional. A filter expression that filters the results listed in
      the response.
    orderBy: Optional. Sort the results by a certain order.
    pageSize: Optional. The maximum number of results per page that should be
      returned.
    pageToken: Optional. The page token.
    parent: Required. The parent resource's name
    returnPartialSuccess: Optional. If true, allow partial responses for
      multi-regional Aggregated List requests.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)
  returnPartialSuccess = _messages.BooleanField(6)


class NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsPatchRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsMulticloudDataTransferConfigsPatch
  Request object.

  Fields:
    googleCloudNetworkconnectivityV1betaMulticloudDataTransferConfig: A
      GoogleCloudNetworkconnectivityV1betaMulticloudDataTransferConfig
      resource to be passed as the request body.
    name: Identifier. The name of the MulticloudDataTransferConfig resource.
      Format: `projects/{project}/locations/{location}/multicloudDataTransferC
      onfigs/{multicloud_data_transfer_config}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate
      MulticloudDataTransferConfigs. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the MulticloudDataTransferConfig resource by the update.
      The fields specified in the update_mask are relative to the resource,
      not the full request. A field will be overwritten if it is in the mask.
      If the user does not provide a mask then all fields will be overwritten.
  """

  googleCloudNetworkconnectivityV1betaMulticloudDataTransferConfig = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaMulticloudDataTransferConfig', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworkconnectivityProjectsLocationsMulticloudDataTransferSupportedServicesGetRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsMulticloudDataTransferSupportedSer
  vicesGetRequest object.

  Fields:
    name: Required. The name of the service.
  """

  name = _messages.StringField(1, required=True)


class NetworkconnectivityProjectsLocationsMulticloudDataTransferSupportedServicesListRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsMulticloudDataTransferSupportedSer
  vicesListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results per page that should be
      returned.
    pageToken: Optional. The page token.
    parent: Required. The parent resource's name
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkconnectivityProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsOperationsCancelRequest object.

  Fields:
    googleLongrunningCancelOperationRequest: A
      GoogleLongrunningCancelOperationRequest resource to be passed as the
      request body.
    name: The name of the operation resource to be cancelled.
  """

  googleLongrunningCancelOperationRequest = _messages.MessageField('GoogleLongrunningCancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class NetworkconnectivityProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class NetworkconnectivityProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class NetworkconnectivityProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class NetworkconnectivityProjectsLocationsRegionalEndpointsCreateRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsRegionalEndpointsCreateRequest
  object.

  Fields:
    googleCloudNetworkconnectivityV1betaRegionalEndpoint: A
      GoogleCloudNetworkconnectivityV1betaRegionalEndpoint resource to be
      passed as the request body.
    parent: Required. The parent resource's name of the RegionalEndpoint.
    regionalEndpointId: Required. Unique id of the Regional Endpoint to be
      created. @pattern: ^[-a-z0-9](?:[-a-z0-9]{0,44})[a-z0-9]$
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      knows to ignore the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if the original operation with the same request
      ID was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  googleCloudNetworkconnectivityV1betaRegionalEndpoint = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaRegionalEndpoint', 1)
  parent = _messages.StringField(2, required=True)
  regionalEndpointId = _messages.StringField(3)
  requestId = _messages.StringField(4)


class NetworkconnectivityProjectsLocationsRegionalEndpointsDeleteRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsRegionalEndpointsDeleteRequest
  object.

  Fields:
    name: Required. The name of the RegionalEndpoint to delete.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      knows to ignore the request if it has already been completed. The server
      guarantees that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if the original operation with the same request
      ID was received, and if so, ignores the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworkconnectivityProjectsLocationsRegionalEndpointsGetRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsRegionalEndpointsGetRequest
  object.

  Fields:
    name: Required. Name of the RegionalEndpoint resource to get. Format: `pro
      jects/{project}/locations/{location}/regionalEndpoints/{regional_endpoin
      t}`
  """

  name = _messages.StringField(1, required=True)


class NetworkconnectivityProjectsLocationsRegionalEndpointsListRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsRegionalEndpointsListRequest
  object.

  Fields:
    filter: A filter expression that filters the results listed in the
      response.
    orderBy: Sort the results by a certain order.
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A page token.
    parent: Required. The parent resource's name of the RegionalEndpoint.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkconnectivityProjectsLocationsSpokesActivateRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsSpokesActivateRequest object.

  Fields:
    googleCloudNetworkconnectivityV1betaActivateSpokeRequest: A
      GoogleCloudNetworkconnectivityV1betaActivateSpokeRequest resource to be
      passed as the request body.
    name: Required. The name of the spoke to activate.
  """

  googleCloudNetworkconnectivityV1betaActivateSpokeRequest = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaActivateSpokeRequest', 1)
  name = _messages.StringField(2, required=True)


class NetworkconnectivityProjectsLocationsSpokesCreateRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsSpokesCreateRequest object.

  Fields:
    googleCloudNetworkconnectivityV1betaSpoke: A
      GoogleCloudNetworkconnectivityV1betaSpoke resource to be passed as the
      request body.
    parent: Required. The parent resource.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check to see
      whether the original operation was received. If it was, the server
      ignores the second request. This behavior prevents clients from
      mistakenly creating duplicate commitments. The request ID must be a
      valid UUID, with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    spokeId: Required. Unique id for the spoke to create.
  """

  googleCloudNetworkconnectivityV1betaSpoke = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaSpoke', 1)
  parent = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  spokeId = _messages.StringField(4)


class NetworkconnectivityProjectsLocationsSpokesDeactivateRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsSpokesDeactivateRequest object.

  Fields:
    googleCloudNetworkconnectivityV1betaDeactivateSpokeRequest: A
      GoogleCloudNetworkconnectivityV1betaDeactivateSpokeRequest resource to
      be passed as the request body.
    name: Required. The name of the spoke to deactivate.
  """

  googleCloudNetworkconnectivityV1betaDeactivateSpokeRequest = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaDeactivateSpokeRequest', 1)
  name = _messages.StringField(2, required=True)


class NetworkconnectivityProjectsLocationsSpokesDeleteRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsSpokesDeleteRequest object.

  Fields:
    name: Required. The name of the spoke to delete.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check to see
      whether the original operation was received. If it was, the server
      ignores the second request. This behavior prevents clients from
      mistakenly creating duplicate commitments. The request ID must be a
      valid UUID, with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworkconnectivityProjectsLocationsSpokesGatewayAdvertisedRoutesCreateRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsSpokesGatewayAdvertisedRoutesCreat
  eRequest object.

  Fields:
    gatewayAdvertisedRouteId: Required. Unique id for the route to create.
    googleCloudNetworkconnectivityV1betaGatewayAdvertisedRoute: A
      GoogleCloudNetworkconnectivityV1betaGatewayAdvertisedRoute resource to
      be passed as the request body.
    parent: Required. The parent resource.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check to see
      whether the original operation was received. If it was, the server
      ignores the second request. This behavior prevents clients from
      mistakenly creating duplicate commitments. The request ID must be a
      valid UUID, with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  gatewayAdvertisedRouteId = _messages.StringField(1)
  googleCloudNetworkconnectivityV1betaGatewayAdvertisedRoute = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaGatewayAdvertisedRoute', 2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworkconnectivityProjectsLocationsSpokesGatewayAdvertisedRoutesDeleteRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsSpokesGatewayAdvertisedRoutesDelet
  eRequest object.

  Fields:
    name: Required. The name of the gateway advertised route to delete.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check to see
      whether the original operation was received. If it was, the server
      ignores the second request. This behavior prevents clients from
      mistakenly creating duplicate commitments. The request ID must be a
      valid UUID, with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworkconnectivityProjectsLocationsSpokesGatewayAdvertisedRoutesGetRequest(_messages.Message):
  r"""A
  NetworkconnectivityProjectsLocationsSpokesGatewayAdvertisedRoutesGetRequest
  object.

  Fields:
    name: Required. The name of the gateway advertised route to get.
  """

  name = _messages.StringField(1, required=True)


class NetworkconnectivityProjectsLocationsSpokesGatewayAdvertisedRoutesListRequest(_messages.Message):
  r"""A
  NetworkconnectivityProjectsLocationsSpokesGatewayAdvertisedRoutesListRequest
  object.

  Fields:
    filter: An expression that filters the list of results.
    orderBy: Sort the results by a certain order.
    pageSize: Optional. The maximum number of results per page that should be
      returned.
    pageToken: Optional. A page token, received from a previous
      `ListGatewayAdvertisedRoutes` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `ListGatewayAdvertisedRoutes` must match the call that provided the page
      token.
    parent: Required. The parent resource's name.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkconnectivityProjectsLocationsSpokesGatewayAdvertisedRoutesPatchRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsSpokesGatewayAdvertisedRoutesPatch
  Request object.

  Fields:
    googleCloudNetworkconnectivityV1betaGatewayAdvertisedRoute: A
      GoogleCloudNetworkconnectivityV1betaGatewayAdvertisedRoute resource to
      be passed as the request body.
    name: Identifier. The name of the gateway advertised route. Route names
      must be unique and use the following form: `projects/{project_number}/lo
      cations/{region}/spokes/{spoke}/gatewayAdvertisedRoutes/{gateway_adverti
      sed_route_id}`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check to see
      whether the original operation was received. If it was, the server
      ignores the second request. This behavior prevents clients from
      mistakenly creating duplicate commitments. The request ID must be a
      valid UUID, with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. In the case of an update to an existing group, field
      mask is used to specify the fields to be overwritten. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field is overwritten if it is in the mask. If the user does
      not provide a mask, then all fields are overwritten.
  """

  googleCloudNetworkconnectivityV1betaGatewayAdvertisedRoute = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaGatewayAdvertisedRoute', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworkconnectivityProjectsLocationsSpokesGetIamPolicyRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsSpokesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NetworkconnectivityProjectsLocationsSpokesGetRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsSpokesGetRequest object.

  Fields:
    name: Required. The name of the spoke resource.
  """

  name = _messages.StringField(1, required=True)


class NetworkconnectivityProjectsLocationsSpokesListRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsSpokesListRequest object.

  Fields:
    filter: An expression that filters the list of results.
    orderBy: Sort the results by a certain order.
    pageSize: The maximum number of results to return per page.
    pageToken: The page token.
    parent: Required. The parent resource.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkconnectivityProjectsLocationsSpokesPatchRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsSpokesPatchRequest object.

  Fields:
    googleCloudNetworkconnectivityV1betaSpoke: A
      GoogleCloudNetworkconnectivityV1betaSpoke resource to be passed as the
      request body.
    name: Immutable. The name of the spoke. Spoke names must be unique. They
      use the following form:
      `projects/{project_number}/locations/{region}/spokes/{spoke_id}`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server knows to
      ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check to see
      whether the original operation was received. If it was, the server
      ignores the second request. This behavior prevents clients from
      mistakenly creating duplicate commitments. The request ID must be a
      valid UUID, with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. In the case of an update to an existing spoke, field
      mask is used to specify the fields to be overwritten. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field is overwritten if it is in the mask. If the user does
      not provide a mask, then all fields are overwritten.
  """

  googleCloudNetworkconnectivityV1betaSpoke = _messages.MessageField('GoogleCloudNetworkconnectivityV1betaSpoke', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworkconnectivityProjectsLocationsSpokesSetIamPolicyRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsSpokesSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class NetworkconnectivityProjectsLocationsSpokesTestIamPermissionsRequest(_messages.Message):
  r"""A NetworkconnectivityProjectsLocationsSpokesTestIamPermissionsRequest
  object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    NetworkconnectivityProjectsLocationsGlobalHubsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    NetworkconnectivityProjectsLocationsGlobalHubsGroupsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    NetworkconnectivityProjectsLocationsSpokesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
