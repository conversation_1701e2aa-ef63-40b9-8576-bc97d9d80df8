"""Generated client library for networkconnectivity version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.networkconnectivity.v1 import networkconnectivity_v1_messages as messages


class NetworkconnectivityV1(base_api.BaseApiClient):
  """Generated client library for service networkconnectivity version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://networkconnectivity.googleapis.com/'
  MTLS_BASE_URL = 'https://networkconnectivity.mtls.googleapis.com/'

  _PACKAGE = 'networkconnectivity'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'NetworkconnectivityV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new networkconnectivity handle."""
    url = url or self.BASE_URL
    super(NetworkconnectivityV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_global_hubs_groups = self.ProjectsLocationsGlobalHubsGroupsService(self)
    self.projects_locations_global_hubs_routeTables_routes = self.ProjectsLocationsGlobalHubsRouteTablesRoutesService(self)
    self.projects_locations_global_hubs_routeTables = self.ProjectsLocationsGlobalHubsRouteTablesService(self)
    self.projects_locations_global_hubs = self.ProjectsLocationsGlobalHubsService(self)
    self.projects_locations_global_policyBasedRoutes = self.ProjectsLocationsGlobalPolicyBasedRoutesService(self)
    self.projects_locations_global = self.ProjectsLocationsGlobalService(self)
    self.projects_locations_internalRanges = self.ProjectsLocationsInternalRangesService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_regionalEndpoints = self.ProjectsLocationsRegionalEndpointsService(self)
    self.projects_locations_serviceClasses = self.ProjectsLocationsServiceClassesService(self)
    self.projects_locations_serviceConnectionMaps = self.ProjectsLocationsServiceConnectionMapsService(self)
    self.projects_locations_serviceConnectionPolicies = self.ProjectsLocationsServiceConnectionPoliciesService(self)
    self.projects_locations_serviceConnectionTokens = self.ProjectsLocationsServiceConnectionTokensService(self)
    self.projects_locations_spokes = self.ProjectsLocationsSpokesService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsGlobalHubsGroupsService(base_api.BaseApiService):
    """Service class for the projects_locations_global_hubs_groups resource."""

    _NAME = 'projects_locations_global_hubs_groups'

    def __init__(self, client):
      super(NetworkconnectivityV1.ProjectsLocationsGlobalHubsGroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details about a Network Connectivity Center group.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsGroupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Group) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/hubs/{hubsId}/groups/{groupsId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.hubs.groups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsGroupsGetRequest',
        response_type_name='Group',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsGroupsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/hubs/{hubsId}/groups/{groupsId}:getIamPolicy',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.hubs.groups.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsGroupsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists groups in a given hub.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsGroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/hubs/{hubsId}/groups',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.hubs.groups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/groups',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsGroupsListRequest',
        response_type_name='ListGroupsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a Network Connectivity Center group.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsGroupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/hubs/{hubsId}/groups/{groupsId}',
        http_method='PATCH',
        method_id='networkconnectivity.projects.locations.global.hubs.groups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='group',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsGroupsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsGroupsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/hubs/{hubsId}/groups/{groupsId}:setIamPolicy',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.global.hubs.groups.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsGroupsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsGroupsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/hubs/{hubsId}/groups/{groupsId}:testIamPermissions',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.global.hubs.groups.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsGroupsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsGlobalHubsRouteTablesRoutesService(base_api.BaseApiService):
    """Service class for the projects_locations_global_hubs_routeTables_routes resource."""

    _NAME = 'projects_locations_global_hubs_routeTables_routes'

    def __init__(self, client):
      super(NetworkconnectivityV1.ProjectsLocationsGlobalHubsRouteTablesRoutesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details about the specified route.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsRouteTablesRoutesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Route) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/hubs/{hubsId}/routeTables/{routeTablesId}/routes/{routesId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.hubs.routeTables.routes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsRouteTablesRoutesGetRequest',
        response_type_name='Route',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists routes in a given route table.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsRouteTablesRoutesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRoutesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/hubs/{hubsId}/routeTables/{routeTablesId}/routes',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.hubs.routeTables.routes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/routes',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsRouteTablesRoutesListRequest',
        response_type_name='ListRoutesResponse',
        supports_download=False,
    )

  class ProjectsLocationsGlobalHubsRouteTablesService(base_api.BaseApiService):
    """Service class for the projects_locations_global_hubs_routeTables resource."""

    _NAME = 'projects_locations_global_hubs_routeTables'

    def __init__(self, client):
      super(NetworkconnectivityV1.ProjectsLocationsGlobalHubsRouteTablesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details about a Network Connectivity Center route table.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsRouteTablesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RouteTable) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/hubs/{hubsId}/routeTables/{routeTablesId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.hubs.routeTables.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsRouteTablesGetRequest',
        response_type_name='RouteTable',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists route tables in a given hub.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsRouteTablesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRouteTablesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/hubs/{hubsId}/routeTables',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.hubs.routeTables.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/routeTables',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsRouteTablesListRequest',
        response_type_name='ListRouteTablesResponse',
        supports_download=False,
    )

  class ProjectsLocationsGlobalHubsService(base_api.BaseApiService):
    """Service class for the projects_locations_global_hubs resource."""

    _NAME = 'projects_locations_global_hubs'

    def __init__(self, client):
      super(NetworkconnectivityV1.ProjectsLocationsGlobalHubsService, self).__init__(client)
      self._upload_configs = {
          }

    def AcceptSpoke(self, request, global_params=None):
      r"""Accepts a proposal to attach a Network Connectivity Center spoke to a hub.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsAcceptSpokeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('AcceptSpoke')
      return self._RunMethod(
          config, request, global_params=global_params)

    AcceptSpoke.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/hubs/{hubsId}:acceptSpoke',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.global.hubs.acceptSpoke',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:acceptSpoke',
        request_field='acceptHubSpokeRequest',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsAcceptSpokeRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def AcceptSpokeUpdate(self, request, global_params=None):
      r"""Accepts a proposal to update a Network Connectivity Center spoke in a hub.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsAcceptSpokeUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('AcceptSpokeUpdate')
      return self._RunMethod(
          config, request, global_params=global_params)

    AcceptSpokeUpdate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/hubs/{hubsId}:acceptSpokeUpdate',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.global.hubs.acceptSpokeUpdate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:acceptSpokeUpdate',
        request_field='acceptSpokeUpdateRequest',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsAcceptSpokeUpdateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new Network Connectivity Center hub in the specified project.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/hubs',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.global.hubs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['hubId', 'requestId'],
        relative_path='v1/{+parent}/hubs',
        request_field='hub',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Network Connectivity Center hub.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/hubs/{hubsId}',
        http_method='DELETE',
        method_id='networkconnectivity.projects.locations.global.hubs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details about a Network Connectivity Center hub.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Hub) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/hubs/{hubsId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.hubs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsGetRequest',
        response_type_name='Hub',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/hubs/{hubsId}:getIamPolicy',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.hubs.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the Network Connectivity Center hubs associated with a given project.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListHubsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/hubs',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.hubs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/hubs',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsListRequest',
        response_type_name='ListHubsResponse',
        supports_download=False,
    )

    def ListSpokes(self, request, global_params=None):
      r"""Lists the Network Connectivity Center spokes associated with a specified hub and location. The list includes both spokes that are attached to the hub and spokes that have been proposed but not yet accepted.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsListSpokesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListHubSpokesResponse) The response message.
      """
      config = self.GetMethodConfig('ListSpokes')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListSpokes.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/hubs/{hubsId}:listSpokes',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.hubs.listSpokes',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'spokeLocations', 'view'],
        relative_path='v1/{+name}:listSpokes',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsListSpokesRequest',
        response_type_name='ListHubSpokesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the description and/or labels of a Network Connectivity Center hub.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/hubs/{hubsId}',
        http_method='PATCH',
        method_id='networkconnectivity.projects.locations.global.hubs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='hub',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def QueryStatus(self, request, global_params=None):
      r"""Query the Private Service Connect propagation status of a Network Connectivity Center hub.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsQueryStatusRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (QueryHubStatusResponse) The response message.
      """
      config = self.GetMethodConfig('QueryStatus')
      return self._RunMethod(
          config, request, global_params=global_params)

    QueryStatus.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/hubs/{hubsId}:queryStatus',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.hubs.queryStatus',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'groupBy', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}:queryStatus',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsQueryStatusRequest',
        response_type_name='QueryHubStatusResponse',
        supports_download=False,
    )

    def RejectSpoke(self, request, global_params=None):
      r"""Rejects a Network Connectivity Center spoke from being attached to a hub. If the spoke was previously in the `ACTIVE` state, it transitions to the `INACTIVE` state and is no longer able to connect to other spokes that are attached to the hub.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsRejectSpokeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('RejectSpoke')
      return self._RunMethod(
          config, request, global_params=global_params)

    RejectSpoke.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/hubs/{hubsId}:rejectSpoke',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.global.hubs.rejectSpoke',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:rejectSpoke',
        request_field='rejectHubSpokeRequest',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsRejectSpokeRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def RejectSpokeUpdate(self, request, global_params=None):
      r"""Rejects a proposal to update a Network Connectivity Center spoke in a hub.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsRejectSpokeUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('RejectSpokeUpdate')
      return self._RunMethod(
          config, request, global_params=global_params)

    RejectSpokeUpdate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/hubs/{hubsId}:rejectSpokeUpdate',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.global.hubs.rejectSpokeUpdate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:rejectSpokeUpdate',
        request_field='rejectSpokeUpdateRequest',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsRejectSpokeUpdateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/hubs/{hubsId}:setIamPolicy',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.global.hubs.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalHubsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/hubs/{hubsId}:testIamPermissions',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.global.hubs.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalHubsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsGlobalPolicyBasedRoutesService(base_api.BaseApiService):
    """Service class for the projects_locations_global_policyBasedRoutes resource."""

    _NAME = 'projects_locations_global_policyBasedRoutes'

    def __init__(self, client):
      super(NetworkconnectivityV1.ProjectsLocationsGlobalPolicyBasedRoutesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new policy-based route in a given project and location.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/policyBasedRoutes',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.global.policyBasedRoutes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['policyBasedRouteId', 'requestId'],
        relative_path='v1/{+parent}/policyBasedRoutes',
        request_field='policyBasedRoute',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single policy-based route.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/policyBasedRoutes/{policyBasedRoutesId}',
        http_method='DELETE',
        method_id='networkconnectivity.projects.locations.global.policyBasedRoutes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single policy-based route.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PolicyBasedRoute) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/policyBasedRoutes/{policyBasedRoutesId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.policyBasedRoutes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesGetRequest',
        response_type_name='PolicyBasedRoute',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/policyBasedRoutes/{policyBasedRoutesId}:getIamPolicy',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.policyBasedRoutes.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists policy-based routes in a given project and location.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPolicyBasedRoutesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/policyBasedRoutes',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.global.policyBasedRoutes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/policyBasedRoutes',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesListRequest',
        response_type_name='ListPolicyBasedRoutesResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/policyBasedRoutes/{policyBasedRoutesId}:setIamPolicy',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.global.policyBasedRoutes.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/policyBasedRoutes/{policyBasedRoutesId}:testIamPermissions',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.global.policyBasedRoutes.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='NetworkconnectivityProjectsLocationsGlobalPolicyBasedRoutesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsGlobalService(base_api.BaseApiService):
    """Service class for the projects_locations_global resource."""

    _NAME = 'projects_locations_global'

    def __init__(self, client):
      super(NetworkconnectivityV1.ProjectsLocationsGlobalService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsInternalRangesService(base_api.BaseApiService):
    """Service class for the projects_locations_internalRanges resource."""

    _NAME = 'projects_locations_internalRanges'

    def __init__(self, client):
      super(NetworkconnectivityV1.ProjectsLocationsInternalRangesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new internal range in a given project and location.

      Args:
        request: (NetworkconnectivityProjectsLocationsInternalRangesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/internalRanges',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.internalRanges.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['internalRangeId', 'requestId'],
        relative_path='v1/{+parent}/internalRanges',
        request_field='internalRange',
        request_type_name='NetworkconnectivityProjectsLocationsInternalRangesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single internal range.

      Args:
        request: (NetworkconnectivityProjectsLocationsInternalRangesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/internalRanges/{internalRangesId}',
        http_method='DELETE',
        method_id='networkconnectivity.projects.locations.internalRanges.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsInternalRangesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single internal range.

      Args:
        request: (NetworkconnectivityProjectsLocationsInternalRangesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (InternalRange) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/internalRanges/{internalRangesId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.internalRanges.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsInternalRangesGetRequest',
        response_type_name='InternalRange',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists internal ranges in a given project and location.

      Args:
        request: (NetworkconnectivityProjectsLocationsInternalRangesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListInternalRangesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/internalRanges',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.internalRanges.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/internalRanges',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsInternalRangesListRequest',
        response_type_name='ListInternalRangesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single internal range.

      Args:
        request: (NetworkconnectivityProjectsLocationsInternalRangesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/internalRanges/{internalRangesId}',
        http_method='PATCH',
        method_id='networkconnectivity.projects.locations.internalRanges.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='internalRange',
        request_type_name='NetworkconnectivityProjectsLocationsInternalRangesPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(NetworkconnectivityV1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (NetworkconnectivityProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='googleLongrunningCancelOperationRequest',
        request_type_name='NetworkconnectivityProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (NetworkconnectivityProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='networkconnectivity.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (NetworkconnectivityProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (NetworkconnectivityProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsRegionalEndpointsService(base_api.BaseApiService):
    """Service class for the projects_locations_regionalEndpoints resource."""

    _NAME = 'projects_locations_regionalEndpoints'

    def __init__(self, client):
      super(NetworkconnectivityV1.ProjectsLocationsRegionalEndpointsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new RegionalEndpoint in a given project and location.

      Args:
        request: (NetworkconnectivityProjectsLocationsRegionalEndpointsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/regionalEndpoints',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.regionalEndpoints.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['regionalEndpointId', 'requestId'],
        relative_path='v1/{+parent}/regionalEndpoints',
        request_field='regionalEndpoint',
        request_type_name='NetworkconnectivityProjectsLocationsRegionalEndpointsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single RegionalEndpoint.

      Args:
        request: (NetworkconnectivityProjectsLocationsRegionalEndpointsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/regionalEndpoints/{regionalEndpointsId}',
        http_method='DELETE',
        method_id='networkconnectivity.projects.locations.regionalEndpoints.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsRegionalEndpointsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single RegionalEndpoint.

      Args:
        request: (NetworkconnectivityProjectsLocationsRegionalEndpointsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (RegionalEndpoint) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/regionalEndpoints/{regionalEndpointsId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.regionalEndpoints.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsRegionalEndpointsGetRequest',
        response_type_name='RegionalEndpoint',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists RegionalEndpoints in a given project and location.

      Args:
        request: (NetworkconnectivityProjectsLocationsRegionalEndpointsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRegionalEndpointsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/regionalEndpoints',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.regionalEndpoints.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/regionalEndpoints',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsRegionalEndpointsListRequest',
        response_type_name='ListRegionalEndpointsResponse',
        supports_download=False,
    )

  class ProjectsLocationsServiceClassesService(base_api.BaseApiService):
    """Service class for the projects_locations_serviceClasses resource."""

    _NAME = 'projects_locations_serviceClasses'

    def __init__(self, client):
      super(NetworkconnectivityV1.ProjectsLocationsServiceClassesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new ServiceClass in a given project and location.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceClassesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceClasses',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.serviceClasses.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'serviceClassId'],
        relative_path='v1/{+parent}/serviceClasses',
        request_field='serviceClass',
        request_type_name='NetworkconnectivityProjectsLocationsServiceClassesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single ServiceClass.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceClassesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceClasses/{serviceClassesId}',
        http_method='DELETE',
        method_id='networkconnectivity.projects.locations.serviceClasses.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsServiceClassesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single ServiceClass.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceClassesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServiceClass) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceClasses/{serviceClassesId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.serviceClasses.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsServiceClassesGetRequest',
        response_type_name='ServiceClass',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceClassesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceClasses/{serviceClassesId}:getIamPolicy',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.serviceClasses.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsServiceClassesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ServiceClasses in a given project and location.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceClassesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListServiceClassesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceClasses',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.serviceClasses.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/serviceClasses',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsServiceClassesListRequest',
        response_type_name='ListServiceClassesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single ServiceClass.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceClassesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceClasses/{serviceClassesId}',
        http_method='PATCH',
        method_id='networkconnectivity.projects.locations.serviceClasses.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='serviceClass',
        request_type_name='NetworkconnectivityProjectsLocationsServiceClassesPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceClassesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceClasses/{serviceClassesId}:setIamPolicy',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.serviceClasses.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='NetworkconnectivityProjectsLocationsServiceClassesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceClassesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceClasses/{serviceClassesId}:testIamPermissions',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.serviceClasses.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='NetworkconnectivityProjectsLocationsServiceClassesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsServiceConnectionMapsService(base_api.BaseApiService):
    """Service class for the projects_locations_serviceConnectionMaps resource."""

    _NAME = 'projects_locations_serviceConnectionMaps'

    def __init__(self, client):
      super(NetworkconnectivityV1.ProjectsLocationsServiceConnectionMapsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new ServiceConnectionMap in a given project and location.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceConnectionMapsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionMaps',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.serviceConnectionMaps.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'serviceConnectionMapId'],
        relative_path='v1/{+parent}/serviceConnectionMaps',
        request_field='serviceConnectionMap',
        request_type_name='NetworkconnectivityProjectsLocationsServiceConnectionMapsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single ServiceConnectionMap.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceConnectionMapsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionMaps/{serviceConnectionMapsId}',
        http_method='DELETE',
        method_id='networkconnectivity.projects.locations.serviceConnectionMaps.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsServiceConnectionMapsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single ServiceConnectionMap.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceConnectionMapsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServiceConnectionMap) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionMaps/{serviceConnectionMapsId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.serviceConnectionMaps.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsServiceConnectionMapsGetRequest',
        response_type_name='ServiceConnectionMap',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceConnectionMapsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionMaps/{serviceConnectionMapsId}:getIamPolicy',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.serviceConnectionMaps.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsServiceConnectionMapsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ServiceConnectionMaps in a given project and location.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceConnectionMapsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListServiceConnectionMapsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionMaps',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.serviceConnectionMaps.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/serviceConnectionMaps',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsServiceConnectionMapsListRequest',
        response_type_name='ListServiceConnectionMapsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single ServiceConnectionMap.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceConnectionMapsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionMaps/{serviceConnectionMapsId}',
        http_method='PATCH',
        method_id='networkconnectivity.projects.locations.serviceConnectionMaps.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='serviceConnectionMap',
        request_type_name='NetworkconnectivityProjectsLocationsServiceConnectionMapsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceConnectionMapsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionMaps/{serviceConnectionMapsId}:setIamPolicy',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.serviceConnectionMaps.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='NetworkconnectivityProjectsLocationsServiceConnectionMapsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceConnectionMapsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionMaps/{serviceConnectionMapsId}:testIamPermissions',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.serviceConnectionMaps.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='NetworkconnectivityProjectsLocationsServiceConnectionMapsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsServiceConnectionPoliciesService(base_api.BaseApiService):
    """Service class for the projects_locations_serviceConnectionPolicies resource."""

    _NAME = 'projects_locations_serviceConnectionPolicies'

    def __init__(self, client):
      super(NetworkconnectivityV1.ProjectsLocationsServiceConnectionPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new ServiceConnectionPolicy in a given project and location.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceConnectionPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionPolicies',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.serviceConnectionPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'serviceConnectionPolicyId'],
        relative_path='v1/{+parent}/serviceConnectionPolicies',
        request_field='serviceConnectionPolicy',
        request_type_name='NetworkconnectivityProjectsLocationsServiceConnectionPoliciesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single ServiceConnectionPolicy.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceConnectionPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionPolicies/{serviceConnectionPoliciesId}',
        http_method='DELETE',
        method_id='networkconnectivity.projects.locations.serviceConnectionPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsServiceConnectionPoliciesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single ServiceConnectionPolicy.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceConnectionPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServiceConnectionPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionPolicies/{serviceConnectionPoliciesId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.serviceConnectionPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsServiceConnectionPoliciesGetRequest',
        response_type_name='ServiceConnectionPolicy',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceConnectionPoliciesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionPolicies/{serviceConnectionPoliciesId}:getIamPolicy',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.serviceConnectionPolicies.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsServiceConnectionPoliciesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ServiceConnectionPolicies in a given project and location.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceConnectionPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListServiceConnectionPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionPolicies',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.serviceConnectionPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/serviceConnectionPolicies',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsServiceConnectionPoliciesListRequest',
        response_type_name='ListServiceConnectionPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single ServiceConnectionPolicy.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceConnectionPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionPolicies/{serviceConnectionPoliciesId}',
        http_method='PATCH',
        method_id='networkconnectivity.projects.locations.serviceConnectionPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='serviceConnectionPolicy',
        request_type_name='NetworkconnectivityProjectsLocationsServiceConnectionPoliciesPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceConnectionPoliciesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionPolicies/{serviceConnectionPoliciesId}:setIamPolicy',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.serviceConnectionPolicies.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='NetworkconnectivityProjectsLocationsServiceConnectionPoliciesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceConnectionPoliciesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionPolicies/{serviceConnectionPoliciesId}:testIamPermissions',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.serviceConnectionPolicies.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='NetworkconnectivityProjectsLocationsServiceConnectionPoliciesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsServiceConnectionTokensService(base_api.BaseApiService):
    """Service class for the projects_locations_serviceConnectionTokens resource."""

    _NAME = 'projects_locations_serviceConnectionTokens'

    def __init__(self, client):
      super(NetworkconnectivityV1.ProjectsLocationsServiceConnectionTokensService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new ServiceConnectionToken in a given project and location.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceConnectionTokensCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionTokens',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.serviceConnectionTokens.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'serviceConnectionTokenId'],
        relative_path='v1/{+parent}/serviceConnectionTokens',
        request_field='serviceConnectionToken',
        request_type_name='NetworkconnectivityProjectsLocationsServiceConnectionTokensCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single ServiceConnectionToken.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceConnectionTokensDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionTokens/{serviceConnectionTokensId}',
        http_method='DELETE',
        method_id='networkconnectivity.projects.locations.serviceConnectionTokens.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsServiceConnectionTokensDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single ServiceConnectionToken.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceConnectionTokensGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServiceConnectionToken) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionTokens/{serviceConnectionTokensId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.serviceConnectionTokens.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsServiceConnectionTokensGetRequest',
        response_type_name='ServiceConnectionToken',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ServiceConnectionTokens in a given project and location.

      Args:
        request: (NetworkconnectivityProjectsLocationsServiceConnectionTokensListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListServiceConnectionTokensResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceConnectionTokens',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.serviceConnectionTokens.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/serviceConnectionTokens',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsServiceConnectionTokensListRequest',
        response_type_name='ListServiceConnectionTokensResponse',
        supports_download=False,
    )

  class ProjectsLocationsSpokesService(base_api.BaseApiService):
    """Service class for the projects_locations_spokes resource."""

    _NAME = 'projects_locations_spokes'

    def __init__(self, client):
      super(NetworkconnectivityV1.ProjectsLocationsSpokesService, self).__init__(client)
      self._upload_configs = {
          }

    def Activate(self, request, global_params=None):
      r"""Activates a Network Connectivity Center spoke. By activating a spoke, you permit connectivity between it and other spokes that are attached to the same hub.

      Args:
        request: (NetworkconnectivityProjectsLocationsSpokesActivateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Activate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Activate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}:activate',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.spokes.activate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:activate',
        request_field='activateSpokeRequest',
        request_type_name='NetworkconnectivityProjectsLocationsSpokesActivateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a Network Connectivity Center spoke.

      Args:
        request: (NetworkconnectivityProjectsLocationsSpokesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/spokes',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.spokes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'spokeId'],
        relative_path='v1/{+parent}/spokes',
        request_field='spoke',
        request_type_name='NetworkconnectivityProjectsLocationsSpokesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Deactivate(self, request, global_params=None):
      r"""Deactivates a Network Connectivity Center spoke. When you deactivate a spoke, it can't connect to other spokes that are attached to the same hub.

      Args:
        request: (NetworkconnectivityProjectsLocationsSpokesDeactivateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Deactivate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Deactivate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}:deactivate',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.spokes.deactivate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:deactivate',
        request_field='deactivateSpokeRequest',
        request_type_name='NetworkconnectivityProjectsLocationsSpokesDeactivateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Network Connectivity Center spoke.

      Args:
        request: (NetworkconnectivityProjectsLocationsSpokesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}',
        http_method='DELETE',
        method_id='networkconnectivity.projects.locations.spokes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsSpokesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details about a Network Connectivity Center spoke.

      Args:
        request: (NetworkconnectivityProjectsLocationsSpokesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Spoke) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.spokes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsSpokesGetRequest',
        response_type_name='Spoke',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworkconnectivityProjectsLocationsSpokesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}:getIamPolicy',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.spokes.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsSpokesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the Network Connectivity Center spokes in a specified project and location.

      Args:
        request: (NetworkconnectivityProjectsLocationsSpokesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSpokesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/spokes',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.spokes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/spokes',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsSpokesListRequest',
        response_type_name='ListSpokesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a Network Connectivity Center spoke.

      Args:
        request: (NetworkconnectivityProjectsLocationsSpokesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}',
        http_method='PATCH',
        method_id='networkconnectivity.projects.locations.spokes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='spoke',
        request_type_name='NetworkconnectivityProjectsLocationsSpokesPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworkconnectivityProjectsLocationsSpokesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}:setIamPolicy',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.spokes.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='NetworkconnectivityProjectsLocationsSpokesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkconnectivityProjectsLocationsSpokesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/spokes/{spokesId}:testIamPermissions',
        http_method='POST',
        method_id='networkconnectivity.projects.locations.spokes.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='NetworkconnectivityProjectsLocationsSpokesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(NetworkconnectivityV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (NetworkconnectivityProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (NetworkconnectivityProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='networkconnectivity.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/locations',
        request_field='',
        request_type_name='NetworkconnectivityProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(NetworkconnectivityV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
