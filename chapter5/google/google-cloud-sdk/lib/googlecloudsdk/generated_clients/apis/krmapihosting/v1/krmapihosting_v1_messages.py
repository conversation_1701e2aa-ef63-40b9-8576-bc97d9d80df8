"""Generated message classes for krmapihosting version v1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'krmapihosting'


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class BundlesConfig(_messages.Message):
  r"""Configuration for the bundles that can be enabled on the KrmApiHost.
  Bundles not ready for public consumption must have a visibility label. e.g.:
  YakimaConfig yakima_config = 2 [(google.api.field_visibility).restriction =
  "GOOGLE_INTERNAL, YAKIMA_TRUSTED_TESTER, GCLOUD_TESTER"];

  Fields:
    configControllerConfig: Optional. Configuration for the Config Controller
      bundle.
  """

  configControllerConfig = _messages.MessageField('ConfigControllerConfig', 1)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CidrBlock(_messages.Message):
  r"""CidrBlock contains an optional name and one CIDR block.

  Fields:
    cidrBlock: Optional. cidr_block must be specified in CIDR notation when
      using master_authorized_networks_config. Currently, the user could still
      use the deprecated man_block field, so this field is currently optional,
      but will be required in the future.
    displayName: Optional. display_name is an optional field for users to
      identify CIDR blocks.
  """

  cidrBlock = _messages.StringField(1)
  displayName = _messages.StringField(2)


class ConfigControllerConfig(_messages.Message):
  r"""Configuration options for the Config Controller bundle.

  Fields:
    enabled: Optional. Whether the Config Controller bundle is enabled on the
      KrmApiHost.
  """

  enabled = _messages.BooleanField(1)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class FullManagementConfig(_messages.Message):
  r"""Configuration of the full (Autopilot) cluster management

  Fields:
    bootDiskKmsKey: Optional. The Customer Managed Encryption Key used to
      encrypt the boot disk attached to each node in the cluster. This should
      be of the form projects/[KEY_PROJECT_ID]/locations/[LOCATION]/keyRings/[
      RING_NAME]/cryptoKeys/[KEY_NAME]. For more information about protecting
      resources with Cloud KMS Keys please see:
      https://cloud.google.com/compute/docs/disks/customer-managed-encryption
    clusterCidrBlock: The IP address range for the cluster pod IPs. Set to
      blank to have a range chosen with the default size. Set to /netmask
      (e.g. /14) to have a range chosen with a specific netmask. Set to a CIDR
      notation (e.g. *********/14) from the RFC-1918 private networks (e.g.
      10.0.0.0/8, **********/12, ***********/16) to pick a specific range to
      use.
    clusterNamedRange: The name of the existing secondary range in the
      cluster's subnetwork to use for pod IP addresses. Alternatively,
      cluster_cidr_block can be used to automatically create a GKE-managed
      one.
    manBlock: Master Authorized Network. Allows access to the k8s master from
      this block. It cannot be set at the same time with the field
      master_authorized_networks_config.
    masterAuthorizedNetworksConfig: Master Authorized Network that supports
      multiple CIDR blocks. Allows access to the k8s master from multiple
      blocks. It cannot be set at the same time with the field man_block.
    masterIpv4CidrBlock: The /28 network that the masters will use.
    network: Existing VPC Network to put the GKE cluster and nodes in.
    servicesCidrBlock: The IP address range for the cluster service IPs. Set
      to blank to have a range chosen with the default size. Set to /netmask
      (e.g. /14) to have a range chosen with a specific netmask. Set to a CIDR
      notation (e.g. *********/14) from the RFC-1918 private networks (e.g.
      10.0.0.0/8, **********/12, ***********/16) to pick a specific range to
      use.
    servicesNamedRange: The name of the existing secondary range in the
      cluster's subnetwork to use for service ClusterIPs. Alternatively,
      services_cidr_block can be used to automatically create a GKE-managed
      one.
    subnet: Specifies the subnet that the interface will be part of. Network
      key must be specified and the subnet must be a subnetwork of the
      specified network.
  """

  bootDiskKmsKey = _messages.StringField(1)
  clusterCidrBlock = _messages.StringField(2)
  clusterNamedRange = _messages.StringField(3)
  manBlock = _messages.StringField(4)
  masterAuthorizedNetworksConfig = _messages.MessageField('MasterAuthorizedNetworksConfig', 5)
  masterIpv4CidrBlock = _messages.StringField(6)
  network = _messages.StringField(7)
  servicesCidrBlock = _messages.StringField(8)
  servicesNamedRange = _messages.StringField(9)
  subnet = _messages.StringField(10)


class KrmApiHost(_messages.Message):
  r"""A KrmApiHost represents a GKE cluster which is pre-installed with KRM
  resources of services currently supported by the KRM API Hosting API.

  Enums:
    StateValueValuesEnum: Output only. The current state of the internal state
      machine for the KrmApiHost.

  Messages:
    LabelsValue: Labels are used for additional information for a KrmApiHost.

  Fields:
    bundlesConfig: Required. Configuration for the bundles that are enabled on
      the KrmApiHost.
    gkeResourceLink: Output only. KrmApiHost GCP self link used for
      identifying the underlying endpoint (GKE cluster currently).
    labels: Labels are used for additional information for a KrmApiHost.
    managementConfig: Configuration of the cluster management
    name: Output only. The name of this KrmApiHost resource in the format: 'pr
      ojects/{project_id}/locations/{location}/krmApiHosts/{krm_api_host_id}'.
    state: Output only. The current state of the internal state machine for
      the KrmApiHost.
    usePrivateEndpoint: Only allow access to the master's private endpoint IP.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the internal state machine for the
    KrmApiHost.

    Values:
      STATE_UNSPECIFIED: Not set.
      CREATING: KrmApiHost is being created
      RUNNING: KrmApiHost is running
      DELETING: KrmApiHost is being deleted
      SUSPENDED: KrmApiHost is suspended, set on specific wipeout events
      READ_ONLY: KrmApiHost is read only, set on specific abuse & billing
        events
      UPDATING: KrmApiHost is being updated
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    RUNNING = 2
    DELETING = 3
    SUSPENDED = 4
    READ_ONLY = 5
    UPDATING = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels are used for additional information for a KrmApiHost.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  bundlesConfig = _messages.MessageField('BundlesConfig', 1)
  gkeResourceLink = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  managementConfig = _messages.MessageField('ManagementConfig', 4)
  name = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  usePrivateEndpoint = _messages.BooleanField(7)


class KrmapihostingProjectsLocationsGetRequest(_messages.Message):
  r"""A KrmapihostingProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class KrmapihostingProjectsLocationsKrmApiHostsCreateRequest(_messages.Message):
  r"""A KrmapihostingProjectsLocationsKrmApiHostsCreateRequest object.

  Fields:
    krmApiHost: A KrmApiHost resource to be passed as the request body.
    krmApiHostId: Required. Client chosen ID for the KrmApiHost.
    parent: Required. The parent in whose context the KrmApiHost is created.
      The parent value is in the format:
      'projects/{project_id}/locations/{location}'.
    requestId: Optional. A unique ID to identify requests. This is unique such
      that if the request is re-tried, the server will know to ignore the
      request if it has already been completed. The server will guarantee that
      for at least 60 minutes after the first request. The request ID must be
      a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  krmApiHost = _messages.MessageField('KrmApiHost', 1)
  krmApiHostId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class KrmapihostingProjectsLocationsKrmApiHostsDeleteRequest(_messages.Message):
  r"""A KrmapihostingProjectsLocationsKrmApiHostsDeleteRequest object.

  Fields:
    name: Required. The name of this service resource in the format: 'projects
      /{project_id}/locations/{location}/krmApiHosts/{krm_api_host_id}'.
    requestId: Optional. A unique ID to identify requests. This is unique such
      that if the request is re-tried, the server will know to ignore the
      request if it has already been completed. The server will guarantee that
      for at least 60 minutes after the first request. The request ID must be
      a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class KrmapihostingProjectsLocationsKrmApiHostsGetIamPolicyRequest(_messages.Message):
  r"""A KrmapihostingProjectsLocationsKrmApiHostsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class KrmapihostingProjectsLocationsKrmApiHostsGetRequest(_messages.Message):
  r"""A KrmapihostingProjectsLocationsKrmApiHostsGetRequest object.

  Fields:
    name: Required. The name of this service resource in the format: 'projects
      /{project_id}/locations/{location}/krmApiHosts/{krm_api_host_id}'.
  """

  name = _messages.StringField(1, required=True)


class KrmapihostingProjectsLocationsKrmApiHostsListRequest(_messages.Message):
  r"""A KrmapihostingProjectsLocationsKrmApiHostsListRequest object.

  Fields:
    filter: Lists the KrmApiHosts that match the filter expression. A filter
      expression filters the resources listed in the response. The expression
      must be of the form '{field} {operator} {value}' where operators: '<',
      '>', '<=', '>=', '!=', '=', ':' are supported (colon ':' represents a
      HAS operator which is roughly synonymous with equality). {field} can
      refer to a proto or JSON field, or a synthetic field. Field names can be
      camelCase or snake_case. Examples: - Filter by name: name =
      "projects/foo-proj/locations/us-central1/krmApiHosts/bar - Filter by
      labels: - Resources that have a key called 'foo' labels.foo:* -
      Resources that have a key called 'foo' whose value is 'bar' labels.foo =
      bar - Filter by state: - Members in CREATING state. state = CREATING
    orderBy: Field to use to sort the list.
    pageSize: When requesting a 'page' of resources, 'page_size' specifies
      number of resources to return. If unspecified or set to 0, all resources
      will be returned.
    pageToken: Token returned by previous call to 'ListKrmApiHosts' which
      specifies the position in the list from where to continue listing the
      resources.
    parent: Required. The parent in whose context the KrmApiHosts are listed.
      The parent value is in the format:
      'projects/{project_id}/locations/{location}'.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class KrmapihostingProjectsLocationsKrmApiHostsPatchRequest(_messages.Message):
  r"""A KrmapihostingProjectsLocationsKrmApiHostsPatchRequest object.

  Fields:
    krmApiHost: A KrmApiHost resource to be passed as the request body.
    name: Output only. The name of this KrmApiHost resource in the format: 'pr
      ojects/{project_id}/locations/{location}/krmApiHosts/{krm_api_host_id}'.
    requestId: Optional. A unique ID to identify requests. This is unique such
      that if the request is re-tried, the server will know to ignore the
      request if it has already been completed. The server will guarantee that
      for at least 60 minutes after the first request. The request ID must be
      a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the KrmApiHost resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. A request
      must specify at least one path in the field mask. Supported field mask
      values are: - `management_config.standard_management_config.man_block`
  """

  krmApiHost = _messages.MessageField('KrmApiHost', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class KrmapihostingProjectsLocationsKrmApiHostsSetIamPolicyRequest(_messages.Message):
  r"""A KrmapihostingProjectsLocationsKrmApiHostsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class KrmapihostingProjectsLocationsKrmApiHostsTestIamPermissionsRequest(_messages.Message):
  r"""A KrmapihostingProjectsLocationsKrmApiHostsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class KrmapihostingProjectsLocationsListRequest(_messages.Message):
  r"""A KrmapihostingProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class KrmapihostingProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A KrmapihostingProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class KrmapihostingProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A KrmapihostingProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class KrmapihostingProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A KrmapihostingProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class KrmapihostingProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A KrmapihostingProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class ListKrmApiHostsResponse(_messages.Message):
  r"""A ListKrmApiHostsResponse represents a List response for a set of
  KrmApiHosts in the service.

  Fields:
    krmApiHosts: The list of KrmApiHosts contained within the parent.
    nextPageToken: A token to request the next page of resources from the
      'ListApiEndpoints' method. The value of an empty string means that there
      are no more resources to return.
    unreachable: Locations that could not be reached.
  """

  krmApiHosts = _messages.MessageField('KrmApiHost', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class ManagementConfig(_messages.Message):
  r"""Configuration of the cluster management

  Fields:
    fullManagementConfig: Configuration of the full (Autopilot) cluster
      management. Full cluster management is a preview feature.
    standardManagementConfig: Configuration of the standard (GKE) cluster
      management
  """

  fullManagementConfig = _messages.MessageField('FullManagementConfig', 1)
  standardManagementConfig = _messages.MessageField('StandardManagementConfig', 2)


class MasterAuthorizedNetworksConfig(_messages.Message):
  r"""Configuration of the Master Authorized Network that support multiple
  CIDRs

  Fields:
    cidrBlocks: cidr_blocks define up to 50 external networks that could
      access Kubernetes master through HTTPS.
  """

  cidrBlocks = _messages.MessageField('CidrBlock', 1, repeated=True)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  statusMessage = _messages.StringField(4)
  target = _messages.StringField(5)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class StandardManagementConfig(_messages.Message):
  r"""Configuration of the standard (GKE) cluster management

  Fields:
    bootDiskKmsKey: Optional. The Customer Managed Encryption Key used to
      encrypt the boot disk attached to each node in the cluster. This should
      be of the form projects/[KEY_PROJECT_ID]/locations/[LOCATION]/keyRings/[
      RING_NAME]/cryptoKeys/[KEY_NAME]. For more information about protecting
      resources with Cloud KMS Keys please see:
      https://cloud.google.com/compute/docs/disks/customer-managed-encryption
    clusterCidrBlock: The IP address range for the cluster pod IPs. Set to
      blank to have a range chosen with the default size. Set to /netmask
      (e.g. /14) to have a range chosen with a specific netmask. Set to a CIDR
      notation (e.g. *********/14) from the RFC-1918 private networks (e.g.
      10.0.0.0/8, **********/12, ***********/16) to pick a specific range to
      use.
    clusterNamedRange: The name of the existing secondary range in the
      cluster's subnetwork to use for pod IP addresses. Alternatively,
      cluster_cidr_block can be used to automatically create a GKE-managed
      one.
    manBlock: Master Authorized Network. Allows access to the k8s master from
      this block. It cannot be set at the same time with the field
      master_authorized_networks_config.
    masterAuthorizedNetworksConfig: Master Authorized Network that supports
      multiple CIDR blocks. Allows access to the k8s master from multiple
      blocks. It cannot be set at the same time with the field man_block.
    masterIpv4CidrBlock: The /28 network that the masters will use.
    network: Existing VPC Network to put the GKE cluster and nodes in.
    servicesCidrBlock: The IP address range for the cluster service IPs. Set
      to blank to have a range chosen with the default size. Set to /netmask
      (e.g. /14) to have a range chosen with a specific netmask. Set to a CIDR
      notation (e.g. *********/14) from the RFC-1918 private networks (e.g.
      10.0.0.0/8, **********/12, ***********/16) to pick a specific range to
      use.
    servicesNamedRange: The name of the existing secondary range in the
      cluster's subnetwork to use for service ClusterIPs. Alternatively,
      services_cidr_block can be used to automatically create a GKE-managed
      one.
    subnet: Specifies the subnet that the interface will be part of. Network
      key must be specified and the subnet must be a subnetwork of the
      specified network.
  """

  bootDiskKmsKey = _messages.StringField(1)
  clusterCidrBlock = _messages.StringField(2)
  clusterNamedRange = _messages.StringField(3)
  manBlock = _messages.StringField(4)
  masterAuthorizedNetworksConfig = _messages.MessageField('MasterAuthorizedNetworksConfig', 5)
  masterIpv4CidrBlock = _messages.StringField(6)
  network = _messages.StringField(7)
  servicesCidrBlock = _messages.StringField(8)
  servicesNamedRange = _messages.StringField(9)
  subnet = _messages.StringField(10)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    KrmapihostingProjectsLocationsKrmApiHostsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
