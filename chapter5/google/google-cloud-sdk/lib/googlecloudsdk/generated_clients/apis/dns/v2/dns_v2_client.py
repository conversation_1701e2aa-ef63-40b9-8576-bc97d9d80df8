"""Generated client library for dns version v2."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.dns.v2 import dns_v2_messages as messages


class DnsV2(base_api.BaseApiClient):
  """Generated client library for service dns version v2."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://dns.googleapis.com/'
  MTLS_BASE_URL = 'https://dns.mtls.googleapis.com/'

  _PACKAGE = 'dns'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform', 'https://www.googleapis.com/auth/cloud-platform.read-only', 'https://www.googleapis.com/auth/ndev.clouddns.readonly', 'https://www.googleapis.com/auth/ndev.clouddns.readwrite']
  _VERSION = 'v2'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'DnsV2'
  _URL_VERSION = 'v2'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new dns handle."""
    url = url or self.BASE_URL
    super(DnsV2, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.changes = self.ChangesService(self)
    self.dnsKeys = self.DnsKeysService(self)
    self.managedZoneOperations = self.ManagedZoneOperationsService(self)
    self.managedZones = self.ManagedZonesService(self)
    self.policies = self.PoliciesService(self)
    self.projects = self.ProjectsService(self)
    self.resourceRecordSets = self.ResourceRecordSetsService(self)
    self.responsePolicies = self.ResponsePoliciesService(self)
    self.responsePolicyRules = self.ResponsePolicyRulesService(self)

  class ChangesService(base_api.BaseApiService):
    """Service class for the changes resource."""

    _NAME = 'changes'

    def __init__(self, client):
      super(DnsV2.ChangesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Atomically updates the ResourceRecordSet collection.

      Args:
        request: (DnsChangesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Change) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='dns.changes.create',
        ordered_params=['project', 'location', 'managedZone'],
        path_params=['location', 'managedZone', 'project'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}/changes',
        request_field='change',
        request_type_name='DnsChangesCreateRequest',
        response_type_name='Change',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Fetches the representation of an existing Change.

      Args:
        request: (DnsChangesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Change) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='dns.changes.get',
        ordered_params=['project', 'location', 'managedZone', 'changeId'],
        path_params=['changeId', 'location', 'managedZone', 'project'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}/changes/{changeId}',
        request_field='',
        request_type_name='DnsChangesGetRequest',
        response_type_name='Change',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Enumerates Changes to a ResourceRecordSet collection.

      Args:
        request: (DnsChangesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ChangesListResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='dns.changes.list',
        ordered_params=['project', 'location', 'managedZone'],
        path_params=['location', 'managedZone', 'project'],
        query_params=['maxResults', 'pageToken', 'sortBy', 'sortOrder'],
        relative_path='dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}/changes',
        request_field='',
        request_type_name='DnsChangesListRequest',
        response_type_name='ChangesListResponse',
        supports_download=False,
    )

  class DnsKeysService(base_api.BaseApiService):
    """Service class for the dnsKeys resource."""

    _NAME = 'dnsKeys'

    def __init__(self, client):
      super(DnsV2.DnsKeysService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Fetches the representation of an existing DnsKey.

      Args:
        request: (DnsDnsKeysGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DnsKey) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='dns.dnsKeys.get',
        ordered_params=['project', 'location', 'managedZone', 'dnsKeyId'],
        path_params=['dnsKeyId', 'location', 'managedZone', 'project'],
        query_params=['clientOperationId', 'digestType'],
        relative_path='dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}/dnsKeys/{dnsKeyId}',
        request_field='',
        request_type_name='DnsDnsKeysGetRequest',
        response_type_name='DnsKey',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Enumerates DnsKeys to a ResourceRecordSet collection.

      Args:
        request: (DnsDnsKeysListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DnsKeysListResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='dns.dnsKeys.list',
        ordered_params=['project', 'location', 'managedZone'],
        path_params=['location', 'managedZone', 'project'],
        query_params=['digestType', 'maxResults', 'pageToken'],
        relative_path='dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}/dnsKeys',
        request_field='',
        request_type_name='DnsDnsKeysListRequest',
        response_type_name='DnsKeysListResponse',
        supports_download=False,
    )

  class ManagedZoneOperationsService(base_api.BaseApiService):
    """Service class for the managedZoneOperations resource."""

    _NAME = 'managedZoneOperations'

    def __init__(self, client):
      super(DnsV2.ManagedZoneOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Fetches the representation of an existing Operation.

      Args:
        request: (DnsManagedZoneOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='dns.managedZoneOperations.get',
        ordered_params=['project', 'location', 'managedZone', 'operation'],
        path_params=['location', 'managedZone', 'operation', 'project'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}/operations/{operation}',
        request_field='',
        request_type_name='DnsManagedZoneOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Enumerates Operations for the given ManagedZone.

      Args:
        request: (DnsManagedZoneOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ManagedZoneOperationsListResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='dns.managedZoneOperations.list',
        ordered_params=['project', 'location', 'managedZone'],
        path_params=['location', 'managedZone', 'project'],
        query_params=['maxResults', 'pageToken', 'sortBy'],
        relative_path='dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}/operations',
        request_field='',
        request_type_name='DnsManagedZoneOperationsListRequest',
        response_type_name='ManagedZoneOperationsListResponse',
        supports_download=False,
    )

  class ManagedZonesService(base_api.BaseApiService):
    """Service class for the managedZones resource."""

    _NAME = 'managedZones'

    def __init__(self, client):
      super(DnsV2.ManagedZonesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new ManagedZone.

      Args:
        request: (DnsManagedZonesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ManagedZone) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='dns.managedZones.create',
        ordered_params=['project', 'location'],
        path_params=['location', 'project'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/managedZones',
        request_field='managedZone',
        request_type_name='DnsManagedZonesCreateRequest',
        response_type_name='ManagedZone',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a previously created ManagedZone.

      Args:
        request: (DnsManagedZonesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DnsManagedZonesDeleteResponse) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        http_method='DELETE',
        method_id='dns.managedZones.delete',
        ordered_params=['project', 'location', 'managedZone'],
        path_params=['location', 'managedZone', 'project'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}',
        request_field='',
        request_type_name='DnsManagedZonesDeleteRequest',
        response_type_name='DnsManagedZonesDeleteResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Fetches the representation of an existing ManagedZone.

      Args:
        request: (DnsManagedZonesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ManagedZone) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='dns.managedZones.get',
        ordered_params=['project', 'location', 'managedZone'],
        path_params=['location', 'managedZone', 'project'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}',
        request_field='',
        request_type_name='DnsManagedZonesGetRequest',
        response_type_name='ManagedZone',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (DnsManagedZonesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='dns/v2/projects/{projectsId}/locations/{locationsId}/managedZones/{managedZonesId}:getIamPolicy',
        http_method='POST',
        method_id='dns.managedZones.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='dns/v2/{+resource}:getIamPolicy',
        request_field='googleIamV1GetIamPolicyRequest',
        request_type_name='DnsManagedZonesGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Enumerates ManagedZones that have been created but not yet deleted.

      Args:
        request: (DnsManagedZonesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ManagedZonesListResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='dns.managedZones.list',
        ordered_params=['project', 'location'],
        path_params=['location', 'project'],
        query_params=['dnsName', 'maxResults', 'pageToken'],
        relative_path='dns/v2/projects/{project}/locations/{location}/managedZones',
        request_field='',
        request_type_name='DnsManagedZonesListRequest',
        response_type_name='ManagedZonesListResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Applies a partial update to an existing ManagedZone.

      Args:
        request: (DnsManagedZonesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PATCH',
        method_id='dns.managedZones.patch',
        ordered_params=['project', 'location', 'managedZone'],
        path_params=['location', 'managedZone', 'project'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}',
        request_field='managedZoneResource',
        request_type_name='DnsManagedZonesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (DnsManagedZonesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='dns/v2/projects/{projectsId}/locations/{locationsId}/managedZones/{managedZonesId}:setIamPolicy',
        http_method='POST',
        method_id='dns.managedZones.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='dns/v2/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='DnsManagedZonesSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this returns an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (DnsManagedZonesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='dns/v2/projects/{projectsId}/locations/{locationsId}/managedZones/{managedZonesId}:testIamPermissions',
        http_method='POST',
        method_id='dns.managedZones.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='dns/v2/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='DnsManagedZonesTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates an existing ManagedZone.

      Args:
        request: (DnsManagedZonesUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PUT',
        method_id='dns.managedZones.update',
        ordered_params=['project', 'location', 'managedZone'],
        path_params=['location', 'managedZone', 'project'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}',
        request_field='managedZoneResource',
        request_type_name='DnsManagedZonesUpdateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class PoliciesService(base_api.BaseApiService):
    """Service class for the policies resource."""

    _NAME = 'policies'

    def __init__(self, client):
      super(DnsV2.PoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Policy.

      Args:
        request: (DnsPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='dns.policies.create',
        ordered_params=['project', 'location'],
        path_params=['location', 'project'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/policies',
        request_field='policy',
        request_type_name='DnsPoliciesCreateRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a previously created Policy. Fails if the policy is still being referenced by a network.

      Args:
        request: (DnsPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DnsPoliciesDeleteResponse) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        http_method='DELETE',
        method_id='dns.policies.delete',
        ordered_params=['project', 'location', 'policy'],
        path_params=['location', 'policy', 'project'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/policies/{policy}',
        request_field='',
        request_type_name='DnsPoliciesDeleteRequest',
        response_type_name='DnsPoliciesDeleteResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Fetches the representation of an existing Policy.

      Args:
        request: (DnsPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='dns.policies.get',
        ordered_params=['project', 'location', 'policy'],
        path_params=['location', 'policy', 'project'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/policies/{policy}',
        request_field='',
        request_type_name='DnsPoliciesGetRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Enumerates all Policies associated with a project.

      Args:
        request: (DnsPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PoliciesListResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='dns.policies.list',
        ordered_params=['project', 'location'],
        path_params=['location', 'project'],
        query_params=['maxResults', 'pageToken'],
        relative_path='dns/v2/projects/{project}/locations/{location}/policies',
        request_field='',
        request_type_name='DnsPoliciesListRequest',
        response_type_name='PoliciesListResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Applies a partial update to an existing Policy.

      Args:
        request: (DnsPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PoliciesPatchResponse) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PATCH',
        method_id='dns.policies.patch',
        ordered_params=['project', 'location', 'policy'],
        path_params=['location', 'policy', 'project'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/policies/{policy}',
        request_field='policyResource',
        request_type_name='DnsPoliciesPatchRequest',
        response_type_name='PoliciesPatchResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates an existing Policy.

      Args:
        request: (DnsPoliciesUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PoliciesUpdateResponse) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PUT',
        method_id='dns.policies.update',
        ordered_params=['project', 'location', 'policy'],
        path_params=['location', 'policy', 'project'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/policies/{policy}',
        request_field='policyResource',
        request_type_name='DnsPoliciesUpdateRequest',
        response_type_name='PoliciesUpdateResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(DnsV2.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Fetches the representation of an existing Project.

      Args:
        request: (DnsProjectsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Project) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='dns.projects.get',
        ordered_params=['project', 'location'],
        path_params=['location', 'project'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}',
        request_field='',
        request_type_name='DnsProjectsGetRequest',
        response_type_name='Project',
        supports_download=False,
    )

  class ResourceRecordSetsService(base_api.BaseApiService):
    """Service class for the resourceRecordSets resource."""

    _NAME = 'resourceRecordSets'

    def __init__(self, client):
      super(DnsV2.ResourceRecordSetsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new ResourceRecordSet.

      Args:
        request: (DnsResourceRecordSetsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ResourceRecordSet) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='dns.resourceRecordSets.create',
        ordered_params=['project', 'location', 'managedZone'],
        path_params=['location', 'managedZone', 'project'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}/rrsets',
        request_field='resourceRecordSet',
        request_type_name='DnsResourceRecordSetsCreateRequest',
        response_type_name='ResourceRecordSet',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a previously created ResourceRecordSet.

      Args:
        request: (DnsResourceRecordSetsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DnsResourceRecordSetsDeleteResponse) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        http_method='DELETE',
        method_id='dns.resourceRecordSets.delete',
        ordered_params=['project', 'location', 'managedZone', 'name', 'type'],
        path_params=['location', 'managedZone', 'name', 'project', 'type'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}/rrsets/{name}/{type}',
        request_field='',
        request_type_name='DnsResourceRecordSetsDeleteRequest',
        response_type_name='DnsResourceRecordSetsDeleteResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Fetches the representation of an existing ResourceRecordSet.

      Args:
        request: (DnsResourceRecordSetsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ResourceRecordSet) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='dns.resourceRecordSets.get',
        ordered_params=['project', 'location', 'managedZone', 'name', 'type'],
        path_params=['location', 'managedZone', 'name', 'project', 'type'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}/rrsets/{name}/{type}',
        request_field='',
        request_type_name='DnsResourceRecordSetsGetRequest',
        response_type_name='ResourceRecordSet',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Enumerates ResourceRecordSets that you have created but not yet deleted.

      Args:
        request: (DnsResourceRecordSetsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ResourceRecordSetsListResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='dns.resourceRecordSets.list',
        ordered_params=['project', 'location', 'managedZone'],
        path_params=['location', 'managedZone', 'project'],
        query_params=['maxResults', 'name', 'pageToken', 'type'],
        relative_path='dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}/rrsets',
        request_field='',
        request_type_name='DnsResourceRecordSetsListRequest',
        response_type_name='ResourceRecordSetsListResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Applies a partial update to an existing ResourceRecordSet.

      Args:
        request: (DnsResourceRecordSetsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ResourceRecordSet) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PATCH',
        method_id='dns.resourceRecordSets.patch',
        ordered_params=['project', 'location', 'managedZone', 'name', 'type'],
        path_params=['location', 'managedZone', 'name', 'project', 'type'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/managedZones/{managedZone}/rrsets/{name}/{type}',
        request_field='resourceRecordSet',
        request_type_name='DnsResourceRecordSetsPatchRequest',
        response_type_name='ResourceRecordSet',
        supports_download=False,
    )

  class ResponsePoliciesService(base_api.BaseApiService):
    """Service class for the responsePolicies resource."""

    _NAME = 'responsePolicies'

    def __init__(self, client):
      super(DnsV2.ResponsePoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Response Policy.

      Args:
        request: (DnsResponsePoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ResponsePolicy) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='dns.responsePolicies.create',
        ordered_params=['project', 'location'],
        path_params=['location', 'project'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/responsePolicies',
        request_field='responsePolicy',
        request_type_name='DnsResponsePoliciesCreateRequest',
        response_type_name='ResponsePolicy',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a previously created Response Policy. Fails if the response policy is non-empty or still being referenced by a network.

      Args:
        request: (DnsResponsePoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DnsResponsePoliciesDeleteResponse) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        http_method='DELETE',
        method_id='dns.responsePolicies.delete',
        ordered_params=['project', 'location', 'responsePolicy'],
        path_params=['location', 'project', 'responsePolicy'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/responsePolicies/{responsePolicy}',
        request_field='',
        request_type_name='DnsResponsePoliciesDeleteRequest',
        response_type_name='DnsResponsePoliciesDeleteResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Fetches the representation of an existing Response Policy.

      Args:
        request: (DnsResponsePoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ResponsePolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='dns.responsePolicies.get',
        ordered_params=['project', 'location', 'responsePolicy'],
        path_params=['location', 'project', 'responsePolicy'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/responsePolicies/{responsePolicy}',
        request_field='',
        request_type_name='DnsResponsePoliciesGetRequest',
        response_type_name='ResponsePolicy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Enumerates all Response Policies associated with a project.

      Args:
        request: (DnsResponsePoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ResponsePoliciesListResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='dns.responsePolicies.list',
        ordered_params=['project', 'location'],
        path_params=['location', 'project'],
        query_params=['maxResults', 'pageToken'],
        relative_path='dns/v2/projects/{project}/locations/{location}/responsePolicies',
        request_field='',
        request_type_name='DnsResponsePoliciesListRequest',
        response_type_name='ResponsePoliciesListResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Applies a partial update to an existing Response Policy.

      Args:
        request: (DnsResponsePoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ResponsePoliciesPatchResponse) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PATCH',
        method_id='dns.responsePolicies.patch',
        ordered_params=['project', 'location', 'responsePolicy'],
        path_params=['location', 'project', 'responsePolicy'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/responsePolicies/{responsePolicy}',
        request_field='responsePolicyResource',
        request_type_name='DnsResponsePoliciesPatchRequest',
        response_type_name='ResponsePoliciesPatchResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates an existing Response Policy.

      Args:
        request: (DnsResponsePoliciesUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ResponsePoliciesUpdateResponse) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PUT',
        method_id='dns.responsePolicies.update',
        ordered_params=['project', 'location', 'responsePolicy'],
        path_params=['location', 'project', 'responsePolicy'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/responsePolicies/{responsePolicy}',
        request_field='responsePolicyResource',
        request_type_name='DnsResponsePoliciesUpdateRequest',
        response_type_name='ResponsePoliciesUpdateResponse',
        supports_download=False,
    )

  class ResponsePolicyRulesService(base_api.BaseApiService):
    """Service class for the responsePolicyRules resource."""

    _NAME = 'responsePolicyRules'

    def __init__(self, client):
      super(DnsV2.ResponsePolicyRulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Response Policy Rule.

      Args:
        request: (DnsResponsePolicyRulesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ResponsePolicyRule) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='dns.responsePolicyRules.create',
        ordered_params=['project', 'location', 'responsePolicy'],
        path_params=['location', 'project', 'responsePolicy'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/responsePolicies/{responsePolicy}/rules',
        request_field='responsePolicyRule',
        request_type_name='DnsResponsePolicyRulesCreateRequest',
        response_type_name='ResponsePolicyRule',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a previously created Response Policy Rule.

      Args:
        request: (DnsResponsePolicyRulesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DnsResponsePolicyRulesDeleteResponse) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        http_method='DELETE',
        method_id='dns.responsePolicyRules.delete',
        ordered_params=['project', 'location', 'responsePolicy', 'responsePolicyRule'],
        path_params=['location', 'project', 'responsePolicy', 'responsePolicyRule'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/responsePolicies/{responsePolicy}/rules/{responsePolicyRule}',
        request_field='',
        request_type_name='DnsResponsePolicyRulesDeleteRequest',
        response_type_name='DnsResponsePolicyRulesDeleteResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Fetches the representation of an existing Response Policy Rule.

      Args:
        request: (DnsResponsePolicyRulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ResponsePolicyRule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='dns.responsePolicyRules.get',
        ordered_params=['project', 'location', 'responsePolicy', 'responsePolicyRule'],
        path_params=['location', 'project', 'responsePolicy', 'responsePolicyRule'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/responsePolicies/{responsePolicy}/rules/{responsePolicyRule}',
        request_field='',
        request_type_name='DnsResponsePolicyRulesGetRequest',
        response_type_name='ResponsePolicyRule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Enumerates all Response Policy Rules associated with a project.

      Args:
        request: (DnsResponsePolicyRulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ResponsePolicyRulesListResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='dns.responsePolicyRules.list',
        ordered_params=['project', 'location', 'responsePolicy'],
        path_params=['location', 'project', 'responsePolicy'],
        query_params=['maxResults', 'pageToken'],
        relative_path='dns/v2/projects/{project}/locations/{location}/responsePolicies/{responsePolicy}/rules',
        request_field='',
        request_type_name='DnsResponsePolicyRulesListRequest',
        response_type_name='ResponsePolicyRulesListResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Applies a partial update to an existing Response Policy Rule.

      Args:
        request: (DnsResponsePolicyRulesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ResponsePolicyRulesPatchResponse) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PATCH',
        method_id='dns.responsePolicyRules.patch',
        ordered_params=['project', 'location', 'responsePolicy', 'responsePolicyRule'],
        path_params=['location', 'project', 'responsePolicy', 'responsePolicyRule'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/responsePolicies/{responsePolicy}/rules/{responsePolicyRule}',
        request_field='responsePolicyRuleResource',
        request_type_name='DnsResponsePolicyRulesPatchRequest',
        response_type_name='ResponsePolicyRulesPatchResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates an existing Response Policy Rule.

      Args:
        request: (DnsResponsePolicyRulesUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ResponsePolicyRulesUpdateResponse) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        http_method='PUT',
        method_id='dns.responsePolicyRules.update',
        ordered_params=['project', 'location', 'responsePolicy', 'responsePolicyRule'],
        path_params=['location', 'project', 'responsePolicy', 'responsePolicyRule'],
        query_params=['clientOperationId'],
        relative_path='dns/v2/projects/{project}/locations/{location}/responsePolicies/{responsePolicy}/rules/{responsePolicyRule}',
        request_field='responsePolicyRuleResource',
        request_type_name='DnsResponsePolicyRulesUpdateRequest',
        response_type_name='ResponsePolicyRulesUpdateResponse',
        supports_download=False,
    )
