"""Generated message classes for memcache version v1.

Google Cloud Memorystore for Memcached API is used for creating and managing
Memcached instances in GCP.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'memcache'


class ApplyParametersRequest(_messages.Message):
  r"""Request for ApplyParameters.

  Fields:
    applyAll: Whether to apply instance-level parameter group to all nodes. If
      set to true, users are restricted from specifying individual nodes, and
      `ApplyParameters` updates all nodes within the instance.
    nodeIds: Nodes to which the instance-level parameter group is applied.
  """

  applyAll = _messages.BooleanField(1)
  nodeIds = _messages.StringField(2, repeated=True)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class DailyCycle(_messages.Message):
  r"""Time window specified for daily operations.

  Fields:
    duration: Output only. Duration of the time window, set by service
      producer.
    startTime: Time within the day to start the operations.
  """

  duration = _messages.StringField(1)
  startTime = _messages.MessageField('TimeOfDay', 2)


class Date(_messages.Message):
  r"""Represents a whole or partial calendar date, such as a birthday. The
  time of day and time zone are either specified elsewhere or are
  insignificant. The date is relative to the Gregorian Calendar. This can
  represent one of the following: * A full date, with non-zero year, month,
  and day values. * A month and day, with a zero year (for example, an
  anniversary). * A year on its own, with a zero month and a zero day. * A
  year and month, with a zero day (for example, a credit card expiration
  date). Related types: * google.type.TimeOfDay * google.type.DateTime *
  google.protobuf.Timestamp

  Fields:
    day: Day of a month. Must be from 1 to 31 and valid for the year and
      month, or 0 to specify a year by itself or a year and month where the
      day isn't significant.
    month: Month of a year. Must be from 1 to 12, or 0 to specify a year
      without a month and day.
    year: Year of the date. Must be from 1 to 9999, or 0 to specify a date
      without a year.
  """

  day = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  month = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  year = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class DenyMaintenancePeriod(_messages.Message):
  r"""DenyMaintenancePeriod definition. Maintenance is forbidden within the
  deny period. The start_date must be less than the end_date.

  Fields:
    endDate: Deny period end date. This can be: * A full date, with non-zero
      year, month and day values. * A month and day value, with a zero year.
      Allows recurring deny periods each year. Date matching this period will
      have to be before the end.
    startDate: Deny period start date. This can be: * A full date, with non-
      zero year, month and day values. * A month and day value, with a zero
      year. Allows recurring deny periods each year. Date matching this period
      will have to be the same or after the start.
    time: Time in UTC when the Blackout period starts on start_date and ends
      on end_date. This can be: * Full time. * All zeros for 00:00:00 UTC
  """

  endDate = _messages.MessageField('Date', 1)
  startDate = _messages.MessageField('Date', 2)
  time = _messages.MessageField('TimeOfDay', 3)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class GetTagsRequest(_messages.Message):
  r"""Request message for GetTags.

  Fields:
    name: Required. The full One Platform resource name of the service
      resource.
  """

  name = _messages.StringField(1)


class GetTagsResponse(_messages.Message):
  r"""Response message for GetTags.

  Messages:
    TagsValue: Required. Tag keys/values directly bound to this resource. Each
      item in the map must be expressed as " : ". For example:
      "123/environment" : "production", "123/costCenter" : "marketing"

  Fields:
    name: Required. The full One Platform resource name of the service
      resource.
    tags: Required. Tag keys/values directly bound to this resource. Each item
      in the map must be expressed as " : ". For example: "123/environment" :
      "production", "123/costCenter" : "marketing"
    tagsEtag: A checksum based on the current bindings. This field is always
      set in server responses.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""Required. Tag keys/values directly bound to this resource. Each item
    in the map must be expressed as " : ". For example: "123/environment" :
    "production", "123/costCenter" : "marketing"

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  name = _messages.StringField(1)
  tags = _messages.MessageField('TagsValue', 2)
  tagsEtag = _messages.StringField(3)


class GoogleCloudMemcacheV1LocationMetadata(_messages.Message):
  r"""Metadata for the given google.cloud.location.Location.

  Messages:
    AvailableZonesValue: Output only. The set of available zones in the
      location. The map is keyed by the lowercase ID of each zone, as defined
      by GCE. These keys can be specified in the `zones` field when creating a
      Memcached instance.

  Fields:
    availableZones: Output only. The set of available zones in the location.
      The map is keyed by the lowercase ID of each zone, as defined by GCE.
      These keys can be specified in the `zones` field when creating a
      Memcached instance.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AvailableZonesValue(_messages.Message):
    r"""Output only. The set of available zones in the location. The map is
    keyed by the lowercase ID of each zone, as defined by GCE. These keys can
    be specified in the `zones` field when creating a Memcached instance.

    Messages:
      AdditionalProperty: An additional property for a AvailableZonesValue
        object.

    Fields:
      additionalProperties: Additional properties of type AvailableZonesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AvailableZonesValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudMemcacheV1ZoneMetadata attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudMemcacheV1ZoneMetadata', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  availableZones = _messages.MessageField('AvailableZonesValue', 1)


class GoogleCloudMemcacheV1MaintenancePolicy(_messages.Message):
  r"""Maintenance policy per instance.

  Fields:
    createTime: Output only. The time when the policy was created.
    description: Description of what this policy is for. Create/Update methods
      return INVALID_ARGUMENT if the length is greater than 512.
    updateTime: Output only. The time when the policy was updated.
    weeklyMaintenanceWindow: Required. Maintenance window that is applied to
      resources covered by this policy. Minimum 1. For the current version,
      the maximum number of weekly_maintenance_windows is expected to be one.
  """

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  updateTime = _messages.StringField(3)
  weeklyMaintenanceWindow = _messages.MessageField('WeeklyMaintenanceWindow', 4, repeated=True)


class GoogleCloudMemcacheV1OperationMetadata(_messages.Message):
  r"""Represents the metadata of a long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    cancelRequested: Output only. Identifies whether the user has requested
      cancellation of the operation. Operations that have successfully been
      cancelled have Operation.error value with a google.rpc.Status.code of 1,
      corresponding to `Code.CANCELLED`.
    createTime: Output only. Time when the operation was created.
    endTime: Output only. Time when the operation finished running.
    statusDetail: Output only. Human-readable status of the operation, if any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  cancelRequested = _messages.BooleanField(2)
  createTime = _messages.StringField(3)
  endTime = _messages.StringField(4)
  statusDetail = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudMemcacheV1UpgradeInstanceRequest(_messages.Message):
  r"""Request for UpgradeInstance.

  Enums:
    MemcacheVersionValueValuesEnum: Required. Specifies the target version of
      memcached engine to upgrade to.

  Fields:
    memcacheVersion: Required. Specifies the target version of memcached
      engine to upgrade to.
  """

  class MemcacheVersionValueValuesEnum(_messages.Enum):
    r"""Required. Specifies the target version of memcached engine to upgrade
    to.

    Values:
      MEMCACHE_VERSION_UNSPECIFIED: Memcache version is not specified by
        customer
      MEMCACHE_1_5: Memcached 1.5 version.
      MEMCACHE_1_6_15: Memcached 1.6.15 version.
    """
    MEMCACHE_VERSION_UNSPECIFIED = 0
    MEMCACHE_1_5 = 1
    MEMCACHE_1_6_15 = 2

  memcacheVersion = _messages.EnumField('MemcacheVersionValueValuesEnum', 1)


class GoogleCloudMemcacheV1ZoneMetadata(_messages.Message):
  r"""A GoogleCloudMemcacheV1ZoneMetadata object."""


class GoogleCloudSaasacceleratorManagementProvidersV1Instance(_messages.Message):
  r"""Instance represents the interface for SLM services to actuate the state
  of control plane resources. Example Instance in JSON, where consumer-
  project-number=123456, producer-project-id=cloud-sql: ```json Instance: {
  "name": "projects/123456/locations/us-east1/instances/prod-instance",
  "create_time": { "seconds": **********, }, "labels": { "env": "prod", "foo":
  "bar" }, "state": READY, "software_versions": { "software_update": "cloud-
  sql-09-28-2018", }, "maintenance_policy_names": { "UpdatePolicy":
  "projects/123456/locations/us-east1/maintenancePolicies/prod-update-policy",
  } "tenant_project_id": "cloud-sql-test-tenant", "producer_metadata": {
  "cloud-sql-tier": "basic", "cloud-sql-instance-size": "1G", },
  "provisioned_resources": [ { "resource-type": "compute-instance", "resource-
  url": "https://www.googleapis.com/compute/v1/projects/cloud-sql/zones/us-
  east1-b/instances/vm-1", } ], "maintenance_schedules": { "csa_rollout": {
  "start_time": { "seconds": **********, }, "end_time": { "seconds":
  1535406431, }, }, "ncsa_rollout": { "start_time": { "seconds": **********,
  }, "end_time": { "seconds": 1535406431, }, } }, "consumer_defined_name":
  "my-sql-instance1", } ``` LINT.IfChange

  Enums:
    StateValueValuesEnum: Output only. Current lifecycle state of the resource
      (e.g. if it's being created or ready to use).

  Messages:
    LabelsValue: Optional. Resource labels to represent user provided
      metadata. Each label is a key-value pair, where both the key and the
      value are arbitrary strings provided by the user.
    MaintenancePolicyNamesValue: Optional. The MaintenancePolicies that have
      been attached to the instance. The key must be of the type name of the
      oneof policy name defined in MaintenancePolicy, and the referenced
      policy must define the same policy type. For details, please refer to
      go/mr-user-guide. Should not be set if
      maintenance_settings.maintenance_policies is set.
    MaintenanceSchedulesValue: The MaintenanceSchedule contains the scheduling
      information of published maintenance schedule with same key as
      software_versions.
    NotificationParametersValue: Optional. notification_parameter are
      information that service producers may like to include that is not
      relevant to Rollout. This parameter will only be passed to Gamma and
      Cloud Logging for notification/logging purpose.
    ProducerMetadataValue: Output only. Custom string attributes used
      primarily to expose producer-specific information in monitoring
      dashboards. See go/get-instance-metadata.
    SoftwareVersionsValue: Software versions that are used to deploy this
      instance. This can be mutated by rollout services.

  Fields:
    consumerDefinedName: consumer_defined_name is the name of the instance set
      by the service consumers. Generally this is different from the `name`
      field which reperesents the system-assigned id of the instance which the
      service consumers do not recognize. This is a required field for tenants
      onboarding to Maintenance Window notifications (go/slm-rollout-
      maintenance-policies#prerequisites).
    consumerProjectNumber: Optional. The consumer_project_number associated
      with this Apigee instance. This field is added specifically to support
      Apigee integration with SLM Rollout and UMM. It represents the numerical
      project ID of the GCP project that consumes this Apigee instance. It is
      used for SLM rollout notifications and UMM integration, enabling proper
      mapping to customer projects and log delivery for Apigee instances. This
      field complements consumer_project_id and may be used for specific
      Apigee scenarios where the numerical ID is required.
    createTime: Output only. Timestamp when the resource was created.
    instanceType: Optional. The instance_type of this instance of format: proj
      ects/{project_number}/locations/{location_id}/instanceTypes/{instance_ty
      pe_id}. Instance Type represents a high-level tier or SKU of the service
      that this instance belong to. When enabled(eg: Maintenance Rollout),
      Rollout uses 'instance_type' along with 'software_versions' to determine
      whether instance needs an update or not.
    labels: Optional. Resource labels to represent user provided metadata.
      Each label is a key-value pair, where both the key and the value are
      arbitrary strings provided by the user.
    maintenancePolicyNames: Optional. The MaintenancePolicies that have been
      attached to the instance. The key must be of the type name of the oneof
      policy name defined in MaintenancePolicy, and the referenced policy must
      define the same policy type. For details, please refer to go/mr-user-
      guide. Should not be set if maintenance_settings.maintenance_policies is
      set.
    maintenanceSchedules: The MaintenanceSchedule contains the scheduling
      information of published maintenance schedule with same key as
      software_versions.
    maintenanceSettings: Optional. The MaintenanceSettings associated with
      instance.
    name: Unique name of the resource. It uses the form: `projects/{project_nu
      mber}/locations/{location_id}/instances/{instance_id}` Note: This name
      is passed, stored and logged across the rollout system. So use of
      consumer project_id or any other consumer PII in the name is strongly
      discouraged for wipeout (go/wipeout) compliance. See
      go/elysium/project_ids#storage-guidance for more details.
    notificationParameters: Optional. notification_parameter are information
      that service producers may like to include that is not relevant to
      Rollout. This parameter will only be passed to Gamma and Cloud Logging
      for notification/logging purpose.
    producerMetadata: Output only. Custom string attributes used primarily to
      expose producer-specific information in monitoring dashboards. See
      go/get-instance-metadata.
    provisionedResources: Output only. The list of data plane resources
      provisioned for this instance, e.g. compute VMs. See go/get-instance-
      metadata.
    slmInstanceTemplate: Link to the SLM instance template. Only populated
      when updating SLM instances via SSA's Actuation service adaptor. Service
      producers with custom control plane (e.g. Cloud SQL) doesn't need to
      populate this field. Instead they should use software_versions.
    sloMetadata: Output only. SLO metadata for instance classification in the
      Standardized dataplane SLO platform. See go/cloud-ssa-standard-slo for
      feature description.
    softwareVersions: Software versions that are used to deploy this instance.
      This can be mutated by rollout services.
    state: Output only. Current lifecycle state of the resource (e.g. if it's
      being created or ready to use).
    tenantProjectId: Output only. ID of the associated GCP tenant project. See
      go/get-instance-metadata.
    updateTime: Output only. Timestamp when the resource was last modified.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current lifecycle state of the resource (e.g. if it's
    being created or ready to use).

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      CREATING: Instance is being created.
      READY: Instance has been created and is ready to use.
      UPDATING: Instance is being updated.
      REPAIRING: Instance is unheathy and under repair.
      DELETING: Instance is being deleted.
      ERROR: Instance encountered an error and is in indeterministic state.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    UPDATING = 3
    REPAIRING = 4
    DELETING = 5
    ERROR = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels to represent user provided metadata. Each
    label is a key-value pair, where both the key and the value are arbitrary
    strings provided by the user.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MaintenancePolicyNamesValue(_messages.Message):
    r"""Optional. The MaintenancePolicies that have been attached to the
    instance. The key must be of the type name of the oneof policy name
    defined in MaintenancePolicy, and the referenced policy must define the
    same policy type. For details, please refer to go/mr-user-guide. Should
    not be set if maintenance_settings.maintenance_policies is set.

    Messages:
      AdditionalProperty: An additional property for a
        MaintenancePolicyNamesValue object.

    Fields:
      additionalProperties: Additional properties of type
        MaintenancePolicyNamesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MaintenancePolicyNamesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MaintenanceSchedulesValue(_messages.Message):
    r"""The MaintenanceSchedule contains the scheduling information of
    published maintenance schedule with same key as software_versions.

    Messages:
      AdditionalProperty: An additional property for a
        MaintenanceSchedulesValue object.

    Fields:
      additionalProperties: Additional properties of type
        MaintenanceSchedulesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MaintenanceSchedulesValue object.

      Fields:
        key: Name of the additional property.
        value: A
          GoogleCloudSaasacceleratorManagementProvidersV1MaintenanceSchedule
          attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1MaintenanceSchedule', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class NotificationParametersValue(_messages.Message):
    r"""Optional. notification_parameter are information that service
    producers may like to include that is not relevant to Rollout. This
    parameter will only be passed to Gamma and Cloud Logging for
    notification/logging purpose.

    Messages:
      AdditionalProperty: An additional property for a
        NotificationParametersValue object.

    Fields:
      additionalProperties: Additional properties of type
        NotificationParametersValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a NotificationParametersValue object.

      Fields:
        key: Name of the additional property.
        value: A
          GoogleCloudSaasacceleratorManagementProvidersV1NotificationParameter
          attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1NotificationParameter', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ProducerMetadataValue(_messages.Message):
    r"""Output only. Custom string attributes used primarily to expose
    producer-specific information in monitoring dashboards. See go/get-
    instance-metadata.

    Messages:
      AdditionalProperty: An additional property for a ProducerMetadataValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        ProducerMetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ProducerMetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SoftwareVersionsValue(_messages.Message):
    r"""Software versions that are used to deploy this instance. This can be
    mutated by rollout services.

    Messages:
      AdditionalProperty: An additional property for a SoftwareVersionsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        SoftwareVersionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SoftwareVersionsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  consumerDefinedName = _messages.StringField(1)
  consumerProjectNumber = _messages.StringField(2)
  createTime = _messages.StringField(3)
  instanceType = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  maintenancePolicyNames = _messages.MessageField('MaintenancePolicyNamesValue', 6)
  maintenanceSchedules = _messages.MessageField('MaintenanceSchedulesValue', 7)
  maintenanceSettings = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1MaintenanceSettings', 8)
  name = _messages.StringField(9)
  notificationParameters = _messages.MessageField('NotificationParametersValue', 10)
  producerMetadata = _messages.MessageField('ProducerMetadataValue', 11)
  provisionedResources = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1ProvisionedResource', 12, repeated=True)
  slmInstanceTemplate = _messages.StringField(13)
  sloMetadata = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1SloMetadata', 14)
  softwareVersions = _messages.MessageField('SoftwareVersionsValue', 15)
  state = _messages.EnumField('StateValueValuesEnum', 16)
  tenantProjectId = _messages.StringField(17)
  updateTime = _messages.StringField(18)


class GoogleCloudSaasacceleratorManagementProvidersV1MaintenanceSchedule(_messages.Message):
  r"""Maintenance schedule which is exposed to customer and potentially end
  user, indicating published upcoming future maintenance schedule

  Fields:
    canReschedule: This field is deprecated, and will be always set to true
      since reschedule can happen multiple times now. This field should not be
      removed until all service producers remove this for their customers.
    endTime: The scheduled end time for the maintenance.
    rolloutManagementPolicy: The rollout management policy this maintenance
      schedule is associated with. When doing reschedule update request, the
      reschedule should be against this given policy.
    scheduleDeadlineTime: schedule_deadline_time is the time deadline any
      schedule start time cannot go beyond, including reschedule. It's
      normally the initial schedule start time plus maintenance window length
      (1 day or 1 week). Maintenance cannot be scheduled to start beyond this
      deadline.
    startTime: The scheduled start time for the maintenance.
  """

  canReschedule = _messages.BooleanField(1)
  endTime = _messages.StringField(2)
  rolloutManagementPolicy = _messages.StringField(3)
  scheduleDeadlineTime = _messages.StringField(4)
  startTime = _messages.StringField(5)


class GoogleCloudSaasacceleratorManagementProvidersV1MaintenanceSettings(_messages.Message):
  r"""Maintenance settings associated with instance. Allows service producers
  and end users to assign settings that controls maintenance on this instance.

  Messages:
    MaintenancePoliciesValue: Optional. The MaintenancePolicies that have been
      attached to the instance. The key must be of the type name of the oneof
      policy name defined in MaintenancePolicy, and the embedded policy must
      define the same policy type. For details, please refer to go/mr-user-
      guide. Should not be set if maintenance_policy_names is set. If only the
      name is needed, then only populate MaintenancePolicy.name.

  Fields:
    exclude: Optional. Exclude instance from maintenance. When true, rollout
      service will not attempt maintenance on the instance. Rollout service
      will include the instance in reported rollout progress as not attempted.
    isRollback: Optional. If the update call is triggered from rollback, set
      the value as true.
    maintenancePolicies: Optional. The MaintenancePolicies that have been
      attached to the instance. The key must be of the type name of the oneof
      policy name defined in MaintenancePolicy, and the embedded policy must
      define the same policy type. For details, please refer to go/mr-user-
      guide. Should not be set if maintenance_policy_names is set. If only the
      name is needed, then only populate MaintenancePolicy.name.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MaintenancePoliciesValue(_messages.Message):
    r"""Optional. The MaintenancePolicies that have been attached to the
    instance. The key must be of the type name of the oneof policy name
    defined in MaintenancePolicy, and the embedded policy must define the same
    policy type. For details, please refer to go/mr-user-guide. Should not be
    set if maintenance_policy_names is set. If only the name is needed, then
    only populate MaintenancePolicy.name.

    Messages:
      AdditionalProperty: An additional property for a
        MaintenancePoliciesValue object.

    Fields:
      additionalProperties: Additional properties of type
        MaintenancePoliciesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MaintenancePoliciesValue object.

      Fields:
        key: Name of the additional property.
        value: A MaintenancePolicy attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('MaintenancePolicy', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  exclude = _messages.BooleanField(1)
  isRollback = _messages.BooleanField(2)
  maintenancePolicies = _messages.MessageField('MaintenancePoliciesValue', 3)


class GoogleCloudSaasacceleratorManagementProvidersV1NodeSloMetadata(_messages.Message):
  r"""Node information for custom per-node SLO implementations. SSA does not
  support per-node SLO, but producers can populate per-node information in
  SloMetadata for custom precomputations. SSA Eligibility Exporter will emit
  per-node metric based on this information.

  Fields:
    location: The location of the node, if different from instance location.
    nodeId: The id of the node. This should be equal to
      SaasInstanceNode.node_id.
    perSliEligibility: If present, this will override eligibility for the node
      coming from instance or exclusions for specified SLIs.
  """

  location = _messages.StringField(1)
  nodeId = _messages.StringField(2)
  perSliEligibility = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1PerSliSloEligibility', 3)


class GoogleCloudSaasacceleratorManagementProvidersV1NotificationParameter(_messages.Message):
  r"""Contains notification related data.

  Fields:
    values: Optional. Array of string values. e.g. instance's replica
      information.
  """

  values = _messages.StringField(1, repeated=True)


class GoogleCloudSaasacceleratorManagementProvidersV1PerSliSloEligibility(_messages.Message):
  r"""PerSliSloEligibility is a mapping from an SLI name to eligibility.

  Messages:
    EligibilitiesValue: An entry in the eligibilities map specifies an
      eligibility for a particular SLI for the given instance. The SLI key in
      the name must be a valid SLI name specified in the Eligibility Exporter
      binary flags otherwise an error will be emitted by Eligibility Exporter
      and the oncaller will be alerted. If an SLI has been defined in the
      binary flags but the eligibilities map does not contain it, the
      corresponding SLI time series will not be emitted by the Eligibility
      Exporter. This ensures a smooth rollout and compatibility between the
      data produced by different versions of the Eligibility Exporters. If
      eligibilities map contains a key for an SLI which has not been declared
      in the binary flags, there will be an error message emitted in the
      Eligibility Exporter log and the metric for the SLI in question will not
      be emitted.

  Fields:
    eligibilities: An entry in the eligibilities map specifies an eligibility
      for a particular SLI for the given instance. The SLI key in the name
      must be a valid SLI name specified in the Eligibility Exporter binary
      flags otherwise an error will be emitted by Eligibility Exporter and the
      oncaller will be alerted. If an SLI has been defined in the binary flags
      but the eligibilities map does not contain it, the corresponding SLI
      time series will not be emitted by the Eligibility Exporter. This
      ensures a smooth rollout and compatibility between the data produced by
      different versions of the Eligibility Exporters. If eligibilities map
      contains a key for an SLI which has not been declared in the binary
      flags, there will be an error message emitted in the Eligibility
      Exporter log and the metric for the SLI in question will not be emitted.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EligibilitiesValue(_messages.Message):
    r"""An entry in the eligibilities map specifies an eligibility for a
    particular SLI for the given instance. The SLI key in the name must be a
    valid SLI name specified in the Eligibility Exporter binary flags
    otherwise an error will be emitted by Eligibility Exporter and the
    oncaller will be alerted. If an SLI has been defined in the binary flags
    but the eligibilities map does not contain it, the corresponding SLI time
    series will not be emitted by the Eligibility Exporter. This ensures a
    smooth rollout and compatibility between the data produced by different
    versions of the Eligibility Exporters. If eligibilities map contains a key
    for an SLI which has not been declared in the binary flags, there will be
    an error message emitted in the Eligibility Exporter log and the metric
    for the SLI in question will not be emitted.

    Messages:
      AdditionalProperty: An additional property for a EligibilitiesValue
        object.

    Fields:
      additionalProperties: Additional properties of type EligibilitiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EligibilitiesValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudSaasacceleratorManagementProvidersV1SloEligibility
          attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1SloEligibility', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  eligibilities = _messages.MessageField('EligibilitiesValue', 1)


class GoogleCloudSaasacceleratorManagementProvidersV1ProvisionedResource(_messages.Message):
  r"""Describes provisioned dataplane resources.

  Fields:
    resourceType: Type of the resource. This can be either a GCP resource or a
      custom one (e.g. another cloud provider's VM). For GCP compute resources
      use singular form of the names listed in GCP compute API documentation
      (https://cloud.google.com/compute/docs/reference/rest/v1/), prefixed
      with 'compute-', for example: 'compute-instance', 'compute-disk',
      'compute-autoscaler'.
    resourceUrl: URL identifying the resource, e.g.
      "https://www.googleapis.com/compute/v1/projects/...)".
  """

  resourceType = _messages.StringField(1)
  resourceUrl = _messages.StringField(2)


class GoogleCloudSaasacceleratorManagementProvidersV1SloEligibility(_messages.Message):
  r"""SloEligibility is a tuple containing eligibility value: true if an
  instance is eligible for SLO calculation or false if it should be excluded
  from all SLO-related calculations along with a user-defined reason.

  Fields:
    eligible: Whether an instance is eligible or ineligible.
    reason: User-defined reason for the current value of instance eligibility.
      Usually, this can be directly mapped to the internal state. An empty
      reason is allowed.
  """

  eligible = _messages.BooleanField(1)
  reason = _messages.StringField(2)


class GoogleCloudSaasacceleratorManagementProvidersV1SloMetadata(_messages.Message):
  r"""SloMetadata contains resources required for proper SLO classification of
  the instance.

  Fields:
    nodes: Optional. List of nodes. Some producers need to use per-node
      metadata to calculate SLO. This field allows such producers to publish
      per-node SLO meta data, which will be consumed by SSA Eligibility
      Exporter and published in the form of per node metric to Monarch.
    perSliEligibility: Optional. Multiple per-instance SLI eligibilities which
      apply for individual SLIs.
    tier: Name of the SLO tier the Instance belongs to. This name will be
      expected to match the tiers specified in the service SLO configuration.
      Field is mandatory and must not be empty.
  """

  nodes = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1NodeSloMetadata', 1, repeated=True)
  perSliEligibility = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1PerSliSloEligibility', 2)
  tier = _messages.StringField(3)


class Instance(_messages.Message):
  r"""A Memorystore for Memcached instance

  Enums:
    MemcacheVersionValueValuesEnum: The major version of Memcached software.
      If not provided, latest supported version will be used. Currently the
      latest supported major version is `MEMCACHE_1_5`. The minor version will
      be automatically determined by our system based on the latest supported
      minor version.
    StateValueValuesEnum: Output only. The state of this Memcached instance.

  Messages:
    LabelsValue: Resource labels to represent user-provided metadata. Refer to
      cloud documentation on labels for more details.
      https://cloud.google.com/compute/docs/labeling-resources
    TagsValue: Optional. Tag keys/values directly bound to this resource. For
      example: ``` "123/environment": "production", "123/costCenter":
      "marketing" ```

  Fields:
    authorizedNetwork: The full name of the Google Compute Engine
      [network](/compute/docs/networks-and-firewalls#networks) to which the
      instance is connected. If left unspecified, the `default` network will
      be used.
    createTime: Output only. The time the instance was created.
    discoveryEndpoint: Output only. Endpoint for the Discovery API.
    displayName: User provided name for the instance, which is only used for
      display purposes. Cannot be more than 80 characters.
    instanceMessages: List of messages that describe the current state of the
      Memcached instance.
    labels: Resource labels to represent user-provided metadata. Refer to
      cloud documentation on labels for more details.
      https://cloud.google.com/compute/docs/labeling-resources
    maintenancePolicy: The maintenance policy for the instance. If not
      provided, the maintenance event will be performed based on Memorystore
      internal rollout schedule.
    maintenanceSchedule: Output only. Published maintenance schedule.
    memcacheFullVersion: Output only. The full version of memcached server
      running on this instance. System automatically determines the full
      memcached version for an instance based on the input MemcacheVersion.
      The full version format will be "memcached-1.5.16".
    memcacheNodes: Output only. List of Memcached nodes. Refer to Node message
      for more details.
    memcacheVersion: The major version of Memcached software. If not provided,
      latest supported version will be used. Currently the latest supported
      major version is `MEMCACHE_1_5`. The minor version will be automatically
      determined by our system based on the latest supported minor version.
    name: Required. Unique name of the resource in this scope including
      project and location using the form:
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
      Note: Memcached instances are managed and addressed at the regional
      level so `location_id` here refers to a Google Cloud region; however,
      users may choose which zones Memcached nodes should be provisioned in
      within an instance. Refer to zones field for more details.
    nodeConfig: Required. Configuration for Memcached nodes.
    nodeCount: Required. Number of nodes in the Memcached instance.
    parameters: User defined parameters to apply to the memcached process on
      each node.
    reservedIpRangeId: Optional. Contains the id of allocated IP address
      ranges associated with the private service access connection for
      example, "test-default" associated with IP range 10.0.0.0/29.
    satisfiesPzi: Optional. Output only. Reserved for future use.
    satisfiesPzs: Optional. Output only. Reserved for future use.
    state: Output only. The state of this Memcached instance.
    tags: Optional. Tag keys/values directly bound to this resource. For
      example: ``` "123/environment": "production", "123/costCenter":
      "marketing" ```
    updateTime: Output only. The time the instance was updated.
    zones: Zones in which Memcached nodes should be provisioned. Memcached
      nodes will be equally distributed across these zones. If not provided,
      the service will by default create nodes in all zones in the region for
      the instance.
  """

  class MemcacheVersionValueValuesEnum(_messages.Enum):
    r"""The major version of Memcached software. If not provided, latest
    supported version will be used. Currently the latest supported major
    version is `MEMCACHE_1_5`. The minor version will be automatically
    determined by our system based on the latest supported minor version.

    Values:
      MEMCACHE_VERSION_UNSPECIFIED: Memcache version is not specified by
        customer
      MEMCACHE_1_5: Memcached 1.5 version.
      MEMCACHE_1_6_15: Memcached 1.6.15 version.
    """
    MEMCACHE_VERSION_UNSPECIFIED = 0
    MEMCACHE_1_5 = 1
    MEMCACHE_1_6_15 = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of this Memcached instance.

    Values:
      STATE_UNSPECIFIED: State not set.
      CREATING: Memcached instance is being created.
      READY: Memcached instance has been created and ready to be used.
      UPDATING: Memcached instance is updating configuration such as
        maintenance policy and schedule.
      DELETING: Memcached instance is being deleted.
      PERFORMING_MAINTENANCE: Memcached instance is going through maintenance,
        e.g. data plane rollout.
      MEMCACHE_VERSION_UPGRADING: Memcached instance is undergoing memcached
        engine version upgrade.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    UPDATING = 3
    DELETING = 4
    PERFORMING_MAINTENANCE = 5
    MEMCACHE_VERSION_UPGRADING = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Resource labels to represent user-provided metadata. Refer to cloud
    documentation on labels for more details.
    https://cloud.google.com/compute/docs/labeling-resources

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""Optional. Tag keys/values directly bound to this resource. For
    example: ``` "123/environment": "production", "123/costCenter":
    "marketing" ```

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  authorizedNetwork = _messages.StringField(1)
  createTime = _messages.StringField(2)
  discoveryEndpoint = _messages.StringField(3)
  displayName = _messages.StringField(4)
  instanceMessages = _messages.MessageField('InstanceMessage', 5, repeated=True)
  labels = _messages.MessageField('LabelsValue', 6)
  maintenancePolicy = _messages.MessageField('GoogleCloudMemcacheV1MaintenancePolicy', 7)
  maintenanceSchedule = _messages.MessageField('MaintenanceSchedule', 8)
  memcacheFullVersion = _messages.StringField(9)
  memcacheNodes = _messages.MessageField('Node', 10, repeated=True)
  memcacheVersion = _messages.EnumField('MemcacheVersionValueValuesEnum', 11)
  name = _messages.StringField(12)
  nodeConfig = _messages.MessageField('NodeConfig', 13)
  nodeCount = _messages.IntegerField(14, variant=_messages.Variant.INT32)
  parameters = _messages.MessageField('MemcacheParameters', 15)
  reservedIpRangeId = _messages.StringField(16, repeated=True)
  satisfiesPzi = _messages.BooleanField(17)
  satisfiesPzs = _messages.BooleanField(18)
  state = _messages.EnumField('StateValueValuesEnum', 19)
  tags = _messages.MessageField('TagsValue', 20)
  updateTime = _messages.StringField(21)
  zones = _messages.StringField(22, repeated=True)


class InstanceMessage(_messages.Message):
  r"""A InstanceMessage object.

  Enums:
    CodeValueValuesEnum: A code that correspond to one type of user-facing
      message.

  Fields:
    code: A code that correspond to one type of user-facing message.
    message: Message on memcached instance which will be exposed to users.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""A code that correspond to one type of user-facing message.

    Values:
      CODE_UNSPECIFIED: Message Code not set.
      ZONE_DISTRIBUTION_UNBALANCED: Memcached nodes are distributed unevenly.
    """
    CODE_UNSPECIFIED = 0
    ZONE_DISTRIBUTION_UNBALANCED = 1

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  message = _messages.StringField(2)


class ListInstancesResponse(_messages.Message):
  r"""Response for ListInstances.

  Fields:
    instances: A list of Memcached instances in the project in the specified
      location, or across all locations. If the `location_id` in the parent
      field of the request is "-", all regions available to the project are
      queried, and the results aggregated.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: Locations that could not be reached.
  """

  instances = _messages.MessageField('Instance', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class LocationMetadata(_messages.Message):
  r"""Metadata for the given google.cloud.location.Location.

  Messages:
    AvailableZonesValue: Output only. The set of available zones in the
      location. The map is keyed by the lowercase ID of each zone, as defined
      by GCE. These keys can be specified in the `zones` field when creating a
      Memcached instance.

  Fields:
    availableZones: Output only. The set of available zones in the location.
      The map is keyed by the lowercase ID of each zone, as defined by GCE.
      These keys can be specified in the `zones` field when creating a
      Memcached instance.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AvailableZonesValue(_messages.Message):
    r"""Output only. The set of available zones in the location. The map is
    keyed by the lowercase ID of each zone, as defined by GCE. These keys can
    be specified in the `zones` field when creating a Memcached instance.

    Messages:
      AdditionalProperty: An additional property for a AvailableZonesValue
        object.

    Fields:
      additionalProperties: Additional properties of type AvailableZonesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AvailableZonesValue object.

      Fields:
        key: Name of the additional property.
        value: A ZoneMetadata attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ZoneMetadata', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  availableZones = _messages.MessageField('AvailableZonesValue', 1)


class MaintenancePolicy(_messages.Message):
  r"""Defines policies to service maintenance events.

  Enums:
    StateValueValuesEnum: Optional. The state of the policy.

  Messages:
    LabelsValue: Optional. Resource labels to represent user provided
      metadata. Each label is a key-value pair, where both the key and the
      value are arbitrary strings provided by the user.

  Fields:
    createTime: Output only. The time when the resource was created.
    description: Optional. Description of what this policy is for.
      Create/Update methods return INVALID_ARGUMENT if the length is greater
      than 512.
    labels: Optional. Resource labels to represent user provided metadata.
      Each label is a key-value pair, where both the key and the value are
      arbitrary strings provided by the user.
    name: Required. MaintenancePolicy name using the form: `projects/{project_
      id}/locations/{location_id}/maintenancePolicies/{maintenance_policy_id}`
      where {project_id} refers to a GCP consumer project ID, {location_id}
      refers to a GCP region/zone, {maintenance_policy_id} must be 1-63
      characters long and match the regular expression
      `[a-z0-9]([-a-z0-9]*[a-z0-9])?`.
    state: Optional. The state of the policy.
    updatePolicy: Maintenance policy applicable to instance update.
    updateTime: Output only. The time when the resource was updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Optional. The state of the policy.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      READY: Resource is ready to be used.
      DELETING: Resource is being deleted. It can no longer be attached to
        instances.
    """
    STATE_UNSPECIFIED = 0
    READY = 1
    DELETING = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels to represent user provided metadata. Each
    label is a key-value pair, where both the key and the value are arbitrary
    strings provided by the user.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)
  updatePolicy = _messages.MessageField('UpdatePolicy', 6)
  updateTime = _messages.StringField(7)


class MaintenanceSchedule(_messages.Message):
  r"""Upcoming maintenance schedule.

  Fields:
    endTime: Output only. The end time of any upcoming scheduled maintenance
      for this instance.
    scheduleDeadlineTime: Output only. The deadline that the maintenance
      schedule start time can not go beyond, including reschedule.
    startTime: Output only. The start time of any upcoming scheduled
      maintenance for this instance.
  """

  endTime = _messages.StringField(1)
  scheduleDeadlineTime = _messages.StringField(2)
  startTime = _messages.StringField(3)


class MaintenanceWindow(_messages.Message):
  r"""MaintenanceWindow definition.

  Fields:
    dailyCycle: Daily cycle.
    weeklyCycle: Weekly cycle.
  """

  dailyCycle = _messages.MessageField('DailyCycle', 1)
  weeklyCycle = _messages.MessageField('WeeklyCycle', 2)


class MemcacheParameters(_messages.Message):
  r"""A MemcacheParameters object.

  Messages:
    ParamsValue: User defined set of parameters to use in the memcached
      process.

  Fields:
    id: Output only. The unique ID associated with this set of parameters.
      Users can use this id to determine if the parameters associated with the
      instance differ from the parameters associated with the nodes. A
      discrepancy between parameter ids can inform users that they may need to
      take action to apply parameters on nodes.
    params: User defined set of parameters to use in the memcached process.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ParamsValue(_messages.Message):
    r"""User defined set of parameters to use in the memcached process.

    Messages:
      AdditionalProperty: An additional property for a ParamsValue object.

    Fields:
      additionalProperties: Additional properties of type ParamsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ParamsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  id = _messages.StringField(1)
  params = _messages.MessageField('ParamsValue', 2)


class MemcacheProjectsLocationsGetRequest(_messages.Message):
  r"""A MemcacheProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class MemcacheProjectsLocationsInstancesApplyParametersRequest(_messages.Message):
  r"""A MemcacheProjectsLocationsInstancesApplyParametersRequest object.

  Fields:
    applyParametersRequest: A ApplyParametersRequest resource to be passed as
      the request body.
    name: Required. Resource name of the Memcached instance for which
      parameter group updates should be applied.
  """

  applyParametersRequest = _messages.MessageField('ApplyParametersRequest', 1)
  name = _messages.StringField(2, required=True)


class MemcacheProjectsLocationsInstancesCreateRequest(_messages.Message):
  r"""A MemcacheProjectsLocationsInstancesCreateRequest object.

  Fields:
    instance: A Instance resource to be passed as the request body.
    instanceId: Required. The logical name of the Memcached instance in the
      user project with the following restrictions: * Must contain only
      lowercase letters, numbers, and hyphens. * Must start with a letter. *
      Must be between 1-40 characters. * Must end with a number or a letter. *
      Must be unique within the user project / location. If any of the above
      are not met, the API raises an invalid argument error.
    parent: Required. The resource name of the instance location using the
      form: `projects/{project_id}/locations/{location_id}` where
      `location_id` refers to a GCP region
  """

  instance = _messages.MessageField('Instance', 1)
  instanceId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class MemcacheProjectsLocationsInstancesDeleteRequest(_messages.Message):
  r"""A MemcacheProjectsLocationsInstancesDeleteRequest object.

  Fields:
    name: Required. Memcached instance resource name in the format:
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
      where `location_id` refers to a GCP region
  """

  name = _messages.StringField(1, required=True)


class MemcacheProjectsLocationsInstancesGetRequest(_messages.Message):
  r"""A MemcacheProjectsLocationsInstancesGetRequest object.

  Fields:
    name: Required. Memcached instance resource name in the format:
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
      where `location_id` refers to a GCP region
  """

  name = _messages.StringField(1, required=True)


class MemcacheProjectsLocationsInstancesListRequest(_messages.Message):
  r"""A MemcacheProjectsLocationsInstancesListRequest object.

  Fields:
    filter: List filter. For example, exclude all Memcached instances with
      name as my-instance by specifying `"name != my-instance"`.
    orderBy: Sort results. Supported values are "name", "name desc" or ""
      (unsorted).
    pageSize: The maximum number of items to return. If not specified, a
      default value of 1000 will be used by the service. Regardless of the
      `page_size` value, the response may include a partial list and a caller
      should only rely on response's `next_page_token` to determine if there
      are more instances left to be queried.
    pageToken: The `next_page_token` value returned from a previous List
      request, if any.
    parent: Required. The resource name of the instance location using the
      form: `projects/{project_id}/locations/{location_id}` where
      `location_id` refers to a GCP region
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class MemcacheProjectsLocationsInstancesPatchRequest(_messages.Message):
  r"""A MemcacheProjectsLocationsInstancesPatchRequest object.

  Fields:
    instance: A Instance resource to be passed as the request body.
    name: Required. Unique name of the resource in this scope including
      project and location using the form:
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
      Note: Memcached instances are managed and addressed at the regional
      level so `location_id` here refers to a Google Cloud region; however,
      users may choose which zones Memcached nodes should be provisioned in
      within an instance. Refer to zones field for more details.
    updateMask: Required. Mask of fields to update. * `displayName`
  """

  instance = _messages.MessageField('Instance', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class MemcacheProjectsLocationsInstancesRescheduleMaintenanceRequest(_messages.Message):
  r"""A MemcacheProjectsLocationsInstancesRescheduleMaintenanceRequest object.

  Fields:
    instance: Required. Memcache instance resource name using the form:
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
      where `location_id` refers to a GCP region.
    rescheduleMaintenanceRequest: A RescheduleMaintenanceRequest resource to
      be passed as the request body.
  """

  instance = _messages.StringField(1, required=True)
  rescheduleMaintenanceRequest = _messages.MessageField('RescheduleMaintenanceRequest', 2)


class MemcacheProjectsLocationsInstancesUpdateParametersRequest(_messages.Message):
  r"""A MemcacheProjectsLocationsInstancesUpdateParametersRequest object.

  Fields:
    name: Required. Resource name of the Memcached instance for which the
      parameters should be updated.
    updateParametersRequest: A UpdateParametersRequest resource to be passed
      as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateParametersRequest = _messages.MessageField('UpdateParametersRequest', 2)


class MemcacheProjectsLocationsInstancesUpgradeRequest(_messages.Message):
  r"""A MemcacheProjectsLocationsInstancesUpgradeRequest object.

  Fields:
    googleCloudMemcacheV1UpgradeInstanceRequest: A
      GoogleCloudMemcacheV1UpgradeInstanceRequest resource to be passed as the
      request body.
    name: Required. Memcache instance resource name using the form:
      `projects/{project}/locations/{location}/instances/{instance}` where
      `location_id` refers to a GCP region.
  """

  googleCloudMemcacheV1UpgradeInstanceRequest = _messages.MessageField('GoogleCloudMemcacheV1UpgradeInstanceRequest', 1)
  name = _messages.StringField(2, required=True)


class MemcacheProjectsLocationsListRequest(_messages.Message):
  r"""A MemcacheProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class MemcacheProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A MemcacheProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class MemcacheProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A MemcacheProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class MemcacheProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A MemcacheProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class MemcacheProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A MemcacheProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class Node(_messages.Message):
  r"""A Node object.

  Enums:
    MemcacheVersionValueValuesEnum: Output only. Major version of memcached
      server running on this node, e.g. MEMCACHE_1_5
    StateValueValuesEnum: Output only. Current state of the Memcached node.

  Fields:
    host: Output only. Hostname or IP address of the Memcached node used by
      the clients to connect to the Memcached server on this node.
    memcacheFullVersion: Output only. The full version of memcached server
      running on this node. e.g. - memcached-1.5.16
    memcacheVersion: Output only. Major version of memcached server running on
      this node, e.g. MEMCACHE_1_5
    nodeId: Output only. Identifier of the Memcached node. The node id does
      not include project or location like the Memcached instance name.
    parameters: User defined parameters currently applied to the node.
    port: Output only. The port number of the Memcached server on this node.
    state: Output only. Current state of the Memcached node.
    zone: Output only. Location (GCP Zone) for the Memcached node.
  """

  class MemcacheVersionValueValuesEnum(_messages.Enum):
    r"""Output only. Major version of memcached server running on this node,
    e.g. MEMCACHE_1_5

    Values:
      MEMCACHE_VERSION_UNSPECIFIED: Memcache version is not specified by
        customer
      MEMCACHE_1_5: Memcached 1.5 version.
      MEMCACHE_1_6_15: Memcached 1.6.15 version.
    """
    MEMCACHE_VERSION_UNSPECIFIED = 0
    MEMCACHE_1_5 = 1
    MEMCACHE_1_6_15 = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the Memcached node.

    Values:
      STATE_UNSPECIFIED: Node state is not set.
      CREATING: Node is being created.
      READY: Node has been created and ready to be used.
      DELETING: Node is being deleted.
      UPDATING: Node is being updated.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    DELETING = 3
    UPDATING = 4

  host = _messages.StringField(1)
  memcacheFullVersion = _messages.StringField(2)
  memcacheVersion = _messages.EnumField('MemcacheVersionValueValuesEnum', 3)
  nodeId = _messages.StringField(4)
  parameters = _messages.MessageField('MemcacheParameters', 5)
  port = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  zone = _messages.StringField(8)


class NodeConfig(_messages.Message):
  r"""Configuration for a Memcached Node.

  Fields:
    cpuCount: Required. Number of cpus per Memcached node.
    memorySizeMb: Required. Memory size in MiB for each Memcached node.
  """

  cpuCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  memorySizeMb = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of a long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    cancelRequested: Output only. Identifies whether the user has requested
      cancellation of the operation. Operations that have successfully been
      cancelled have Operation.error value with a google.rpc.Status.code of 1,
      corresponding to `Code.CANCELLED`.
    createTime: Output only. Time when the operation was created.
    endTime: Output only. Time when the operation finished running.
    statusDetail: Output only. Human-readable status of the operation, if any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  cancelRequested = _messages.BooleanField(2)
  createTime = _messages.StringField(3)
  endTime = _messages.StringField(4)
  statusDetail = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class RescheduleMaintenanceRequest(_messages.Message):
  r"""Request for RescheduleMaintenance.

  Enums:
    RescheduleTypeValueValuesEnum: Required. If reschedule type is
      SPECIFIC_TIME, must set up schedule_time as well.

  Fields:
    rescheduleType: Required. If reschedule type is SPECIFIC_TIME, must set up
      schedule_time as well.
    scheduleTime: Timestamp when the maintenance shall be rescheduled to if
      reschedule_type=SPECIFIC_TIME, in RFC 3339 format, for example
      `2012-11-15T16:19:00.094Z`.
  """

  class RescheduleTypeValueValuesEnum(_messages.Enum):
    r"""Required. If reschedule type is SPECIFIC_TIME, must set up
    schedule_time as well.

    Values:
      RESCHEDULE_TYPE_UNSPECIFIED: Not set.
      IMMEDIATE: If the user wants to schedule the maintenance to happen now.
      NEXT_AVAILABLE_WINDOW: If the user wants to use the existing maintenance
        policy to find the next available window.
      SPECIFIC_TIME: If the user wants to reschedule the maintenance to a
        specific time.
    """
    RESCHEDULE_TYPE_UNSPECIFIED = 0
    IMMEDIATE = 1
    NEXT_AVAILABLE_WINDOW = 2
    SPECIFIC_TIME = 3

  rescheduleType = _messages.EnumField('RescheduleTypeValueValuesEnum', 1)
  scheduleTime = _messages.StringField(2)


class Schedule(_messages.Message):
  r"""Configure the schedule.

  Enums:
    DayValueValuesEnum: Allows to define schedule that runs specified day of
      the week.

  Fields:
    day: Allows to define schedule that runs specified day of the week.
    duration: Output only. Duration of the time window, set by service
      producer.
    startTime: Time within the window to start the operations.
  """

  class DayValueValuesEnum(_messages.Enum):
    r"""Allows to define schedule that runs specified day of the week.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  day = _messages.EnumField('DayValueValuesEnum', 1)
  duration = _messages.StringField(2)
  startTime = _messages.MessageField('TimeOfDay', 3)


class SetTagsRequest(_messages.Message):
  r"""Request message for SetTags.

  Messages:
    TagsValue: Required. These bindings will override any bindings previously
      set and will be effective immediately. Each item in the map must be
      expressed as " : ". For example: "123/environment" : "production",
      "123/costCenter" : "marketing"

  Fields:
    name: Required. The full One Platform resource name of the service
      resource.
    requestId: Optional. A unique identifier for this request. Must be a valid
      UUID. This request is only idempotent if a `request_id` is provided.
    tags: Required. These bindings will override any bindings previously set
      and will be effective immediately. Each item in the map must be
      expressed as " : ". For example: "123/environment" : "production",
      "123/costCenter" : "marketing"
    tagsEtag: Optional. A checksum based on the current bindings which can be
      passed to prevent race conditions. If not passed, etag check would be
      skipped.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""Required. These bindings will override any bindings previously set and
    will be effective immediately. Each item in the map must be expressed as "
    : ". For example: "123/environment" : "production", "123/costCenter" :
    "marketing"

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  name = _messages.StringField(1)
  requestId = _messages.StringField(2)
  tags = _messages.MessageField('TagsValue', 3)
  tagsEtag = _messages.StringField(4)


class SetTagsResponse(_messages.Message):
  r"""Response message for SetTags.

  Messages:
    TagsValue: Required. Tag keys/values directly bound to this resource. Each
      item in the map must be expressed as " : ". For example:
      "123/environment" : "production", "123/costCenter" : "marketing"

  Fields:
    name: Required. The full One Platform resource name of the service
      resource.
    tags: Required. Tag keys/values directly bound to this resource. Each item
      in the map must be expressed as " : ". For example: "123/environment" :
      "production", "123/costCenter" : "marketing"
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""Required. Tag keys/values directly bound to this resource. Each item
    in the map must be expressed as " : ". For example: "123/environment" :
    "production", "123/costCenter" : "marketing"

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  name = _messages.StringField(1)
  tags = _messages.MessageField('TagsValue', 2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TimeOfDay(_messages.Message):
  r"""Represents a time of day. The date and time zone are either not
  significant or are specified elsewhere. An API may choose to allow leap
  seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.

  Fields:
    hours: Hours of a day in 24 hour format. Must be greater than or equal to
      0 and typically must be less than or equal to 23. An API may choose to
      allow the value "24:00:00" for scenarios like business closing time.
    minutes: Minutes of an hour. Must be greater than or equal to 0 and less
      than or equal to 59.
    nanos: Fractions of seconds, in nanoseconds. Must be greater than or equal
      to 0 and less than or equal to 999,999,999.
    seconds: Seconds of a minute. Must be greater than or equal to 0 and
      typically must be less than or equal to 59. An API may allow the value
      60 if it allows leap-seconds.
  """

  hours = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minutes = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  nanos = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  seconds = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class UpdateParametersRequest(_messages.Message):
  r"""Request for UpdateParameters.

  Fields:
    parameters: The parameters to apply to the instance.
    updateMask: Required. Mask of fields to update.
  """

  parameters = _messages.MessageField('MemcacheParameters', 1)
  updateMask = _messages.StringField(2)


class UpdatePolicy(_messages.Message):
  r"""Maintenance policy applicable to instance updates.

  Enums:
    ChannelValueValuesEnum: Optional. Relative scheduling channel applied to
      resource.

  Fields:
    channel: Optional. Relative scheduling channel applied to resource.
    denyMaintenancePeriods: Deny Maintenance Period that is applied to
      resource to indicate when maintenance is forbidden. The protocol
      supports zero-to-many such periods, but the current SLM Rollout
      implementation only supports zero-to-one.
    window: Optional. Maintenance window that is applied to resources covered
      by this policy.
  """

  class ChannelValueValuesEnum(_messages.Enum):
    r"""Optional. Relative scheduling channel applied to resource.

    Values:
      UPDATE_CHANNEL_UNSPECIFIED: Unspecified channel.
      EARLIER: Early channel within a customer project.
      LATER: Later channel within a customer project.
      WEEK1: ! ! The follow channels can ONLY be used if you adopt the new MW
        system! ! ! NOTE: all WEEK channels are assumed to be under a weekly
        window. ! There is currently no dedicated channel definitions for
        Daily windows. ! If you use Daily window, the system will assume a 1d
        (24Hours) advanced ! notification period b/w EARLY and LATER. ! We may
        consider support more flexible daily channel specifications in ! the
        future. WEEK1 == EARLIER with minimum 7d advanced notification. {7d,
        14d} The system will treat them equally and will use WEEK1 whenever it
        can. New customers are encouraged to use this channel annotation.
      WEEK2: WEEK2 == LATER with minimum 14d advanced notification {14d, 21d}.
      WEEK5: WEEK5 == 40d support. minimum 35d advanced notification {35d,
        42d}.
    """
    UPDATE_CHANNEL_UNSPECIFIED = 0
    EARLIER = 1
    LATER = 2
    WEEK1 = 3
    WEEK2 = 4
    WEEK5 = 5

  channel = _messages.EnumField('ChannelValueValuesEnum', 1)
  denyMaintenancePeriods = _messages.MessageField('DenyMaintenancePeriod', 2, repeated=True)
  window = _messages.MessageField('MaintenanceWindow', 3)


class WeeklyCycle(_messages.Message):
  r"""Time window specified for weekly operations.

  Fields:
    schedule: User can specify multiple windows in a week. Minimum of 1
      window.
  """

  schedule = _messages.MessageField('Schedule', 1, repeated=True)


class WeeklyMaintenanceWindow(_messages.Message):
  r"""Time window specified for weekly operations.

  Enums:
    DayValueValuesEnum: Required. Allows to define schedule that runs
      specified day of the week.

  Fields:
    day: Required. Allows to define schedule that runs specified day of the
      week.
    duration: Required. Duration of the time window.
    startTime: Required. Start time of the window in UTC.
  """

  class DayValueValuesEnum(_messages.Enum):
    r"""Required. Allows to define schedule that runs specified day of the
    week.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  day = _messages.EnumField('DayValueValuesEnum', 1)
  duration = _messages.StringField(2)
  startTime = _messages.MessageField('TimeOfDay', 3)


class ZoneMetadata(_messages.Message):
  r"""A ZoneMetadata object."""


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
