"""Generated message classes for run version v2.

Deploy and manage user provided container images that scale automatically
based on incoming requests. The Cloud Run Admin API v1 follows the Knative
Serving API specification, while v2 is aligned with Google Cloud AIP-based API
standards, as described in https://google.aip.dev/.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'run'


class GoogleCloudRunV2BinaryAuthorization(_messages.Message):
  r"""Settings for Binary Authorization feature.

  Fields:
    breakglassJustification: Optional. If present, indicates to use Breakglass
      using this justification. If use_default is False, then it must be
      empty. For more information on breakglass, see
      https://cloud.google.com/binary-authorization/docs/using-breakglass
    policy: Optional. The path to a binary authorization policy. Format:
      `projects/{project}/platforms/cloudRun/{policy-name}`
    useDefault: Optional. If True, indicates to use the default project's
      binary authorization policy. If False, binary authorization will be
      disabled.
  """

  breakglassJustification = _messages.StringField(1)
  policy = _messages.StringField(2)
  useDefault = _messages.BooleanField(3)


class GoogleCloudRunV2BuildConfig(_messages.Message):
  r"""Describes the Build step of the function that builds a container from
  the given source.

  Messages:
    EnvironmentVariablesValue: Optional. User-provided build-time environment
      variables for the function

  Fields:
    baseImage: Optional. The base image used to build the function.
    enableAutomaticUpdates: Optional. Sets whether the function will receive
      automatic base image updates.
    environmentVariables: Optional. User-provided build-time environment
      variables for the function
    functionTarget: Optional. The name of the function (as defined in source
      code) that will be executed. Defaults to the resource name suffix, if
      not specified. For backward compatibility, if function with given name
      is not found, then the system will try to use function named "function".
    imageUri: Optional. Artifact Registry URI to store the built image.
    name: Output only. The Cloud Build name of the latest successful
      deployment of the function.
    serviceAccount: Optional. Service account to be used for building the
      container. The format of this field is
      `projects/{projectId}/serviceAccounts/{serviceAccountEmail}`.
    sourceLocation: The Cloud Storage bucket URI where the function source
      code is located.
    workerPool: Optional. Name of the Cloud Build Custom Worker Pool that
      should be used to build the Cloud Run function. The format of this field
      is `projects/{project}/locations/{region}/workerPools/{workerPool}`
      where `{project}` and `{region}` are the project id and region
      respectively where the worker pool is defined and `{workerPool}` is the
      short name of the worker pool.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvironmentVariablesValue(_messages.Message):
    r"""Optional. User-provided build-time environment variables for the
    function

    Messages:
      AdditionalProperty: An additional property for a
        EnvironmentVariablesValue object.

    Fields:
      additionalProperties: Additional properties of type
        EnvironmentVariablesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvironmentVariablesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  baseImage = _messages.StringField(1)
  enableAutomaticUpdates = _messages.BooleanField(2)
  environmentVariables = _messages.MessageField('EnvironmentVariablesValue', 3)
  functionTarget = _messages.StringField(4)
  imageUri = _messages.StringField(5)
  name = _messages.StringField(6)
  serviceAccount = _messages.StringField(7)
  sourceLocation = _messages.StringField(8)
  workerPool = _messages.StringField(9)


class GoogleCloudRunV2BuildInfo(_messages.Message):
  r"""Build information of the image.

  Fields:
    functionTarget: Output only. Entry point of the function when the image is
      a Cloud Run function.
    sourceLocation: Output only. Source code location of the image.
  """

  functionTarget = _messages.StringField(1)
  sourceLocation = _messages.StringField(2)


class GoogleCloudRunV2BuildpacksBuild(_messages.Message):
  r"""Build the source using Buildpacks.

  Messages:
    EnvironmentVariablesValue: Optional. User-provided build-time environment
      variables.

  Fields:
    baseImage: Optional. The base image to use for the build.
    cacheImageUri: Optional. cache_image_uri is the GCR/AR URL where the cache
      image will be stored. cache_image_uri is optional and omitting it will
      disable caching. This URL must be stable across builds. It is used to
      derive a build-specific temporary URL by substituting the tag with the
      build ID. The build will clean up the temporary image on a best-effort
      basis.
    enableAutomaticUpdates: Optional. Whether or not the application container
      will be enrolled in automatic base image updates. When true, the
      application will be built on a scratch base image, so the base layers
      can be appended at run time.
    environmentVariables: Optional. User-provided build-time environment
      variables.
    functionTarget: Optional. Name of the function target if the source is a
      function source. Required for function builds.
    projectDescriptor: Optional. project_descriptor stores the path to the
      project descriptor file. When empty, it means that there is no project
      descriptor file in the source.
    runtime: The runtime name, e.g. 'go113'. Leave blank for generic builds.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvironmentVariablesValue(_messages.Message):
    r"""Optional. User-provided build-time environment variables.

    Messages:
      AdditionalProperty: An additional property for a
        EnvironmentVariablesValue object.

    Fields:
      additionalProperties: Additional properties of type
        EnvironmentVariablesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvironmentVariablesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  baseImage = _messages.StringField(1)
  cacheImageUri = _messages.StringField(2)
  enableAutomaticUpdates = _messages.BooleanField(3)
  environmentVariables = _messages.MessageField('EnvironmentVariablesValue', 4)
  functionTarget = _messages.StringField(5)
  projectDescriptor = _messages.StringField(6)
  runtime = _messages.StringField(7)


class GoogleCloudRunV2CancelExecutionRequest(_messages.Message):
  r"""Request message for deleting an Execution.

  Fields:
    etag: A system-generated fingerprint for this version of the resource.
      This may be used to detect modification conflict during updates.
    validateOnly: Indicates that the request should be validated without
      actually cancelling any resources.
  """

  etag = _messages.StringField(1)
  validateOnly = _messages.BooleanField(2)


class GoogleCloudRunV2CloudSqlInstance(_messages.Message):
  r"""Represents a set of Cloud SQL instances. Each one will be available
  under /cloudsql/[instance]. Visit
  https://cloud.google.com/sql/docs/mysql/connect-run for more information on
  how to connect Cloud SQL and Cloud Run.

  Fields:
    instances: The Cloud SQL instance connection names, as can be found in
      https://console.cloud.google.com/sql/instances. Visit
      https://cloud.google.com/sql/docs/mysql/connect-run for more information
      on how to connect Cloud SQL and Cloud Run. Format:
      {project}:{location}:{instance}
  """

  instances = _messages.StringField(1, repeated=True)


class GoogleCloudRunV2Condition(_messages.Message):
  r"""Defines a status condition for a resource.

  Enums:
    ExecutionReasonValueValuesEnum: Output only. A reason for the execution
      condition.
    ReasonValueValuesEnum: Output only. A common (service-level) reason for
      this condition.
    RevisionReasonValueValuesEnum: Output only. A reason for the revision
      condition.
    SeverityValueValuesEnum: How to interpret failures of this condition, one
      of Error, Warning, Info
    StateValueValuesEnum: State of the condition.

  Fields:
    executionReason: Output only. A reason for the execution condition.
    lastTransitionTime: Last time the condition transitioned from one status
      to another.
    message: Human readable message indicating details about the current
      status.
    reason: Output only. A common (service-level) reason for this condition.
    revisionReason: Output only. A reason for the revision condition.
    severity: How to interpret failures of this condition, one of Error,
      Warning, Info
    state: State of the condition.
    type: type is used to communicate the status of the reconciliation
      process. See also:
      https://github.com/knative/serving/blob/main/docs/spec/errors.md#error-
      conditions-and-reporting Types common to all resources include: *
      "Ready": True when the Resource is ready.
  """

  class ExecutionReasonValueValuesEnum(_messages.Enum):
    r"""Output only. A reason for the execution condition.

    Values:
      EXECUTION_REASON_UNDEFINED: Default value.
      JOB_STATUS_SERVICE_POLLING_ERROR: Internal system error getting
        execution status. System will retry.
      NON_ZERO_EXIT_CODE: A task reached its retry limit and the last attempt
        failed due to the user container exiting with a non-zero exit code.
      CANCELLED: The execution was cancelled by users.
      CANCELLING: The execution is in the process of being cancelled.
      DELETED: The execution was deleted.
    """
    EXECUTION_REASON_UNDEFINED = 0
    JOB_STATUS_SERVICE_POLLING_ERROR = 1
    NON_ZERO_EXIT_CODE = 2
    CANCELLED = 3
    CANCELLING = 4
    DELETED = 5

  class ReasonValueValuesEnum(_messages.Enum):
    r"""Output only. A common (service-level) reason for this condition.

    Values:
      COMMON_REASON_UNDEFINED: Default value.
      UNKNOWN: Reason unknown. Further details will be in message.
      REVISION_FAILED: Revision creation process failed.
      PROGRESS_DEADLINE_EXCEEDED: Timed out waiting for completion.
      CONTAINER_MISSING: The container image path is incorrect.
      CONTAINER_PERMISSION_DENIED: Insufficient permissions on the container
        image.
      CONTAINER_IMAGE_UNAUTHORIZED: Container image is not authorized by
        policy.
      CONTAINER_IMAGE_AUTHORIZATION_CHECK_FAILED: Container image policy
        authorization check failed.
      ENCRYPTION_KEY_PERMISSION_DENIED: Insufficient permissions on encryption
        key.
      ENCRYPTION_KEY_CHECK_FAILED: Permission check on encryption key failed.
      SECRETS_ACCESS_CHECK_FAILED: At least one Access check on secrets
        failed.
      WAITING_FOR_OPERATION: Waiting for operation to complete.
      IMMEDIATE_RETRY: System will retry immediately.
      POSTPONED_RETRY: System will retry later; current attempt failed.
      INTERNAL: An internal error occurred. Further information may be in the
        message.
      VPC_NETWORK_NOT_FOUND: User-provided VPC network was not found.
    """
    COMMON_REASON_UNDEFINED = 0
    UNKNOWN = 1
    REVISION_FAILED = 2
    PROGRESS_DEADLINE_EXCEEDED = 3
    CONTAINER_MISSING = 4
    CONTAINER_PERMISSION_DENIED = 5
    CONTAINER_IMAGE_UNAUTHORIZED = 6
    CONTAINER_IMAGE_AUTHORIZATION_CHECK_FAILED = 7
    ENCRYPTION_KEY_PERMISSION_DENIED = 8
    ENCRYPTION_KEY_CHECK_FAILED = 9
    SECRETS_ACCESS_CHECK_FAILED = 10
    WAITING_FOR_OPERATION = 11
    IMMEDIATE_RETRY = 12
    POSTPONED_RETRY = 13
    INTERNAL = 14
    VPC_NETWORK_NOT_FOUND = 15

  class RevisionReasonValueValuesEnum(_messages.Enum):
    r"""Output only. A reason for the revision condition.

    Values:
      REVISION_REASON_UNDEFINED: Default value.
      PENDING: Revision in Pending state.
      RESERVE: Revision is in Reserve state.
      RETIRED: Revision is Retired.
      RETIRING: Revision is being retired.
      RECREATING: Revision is being recreated.
      HEALTH_CHECK_CONTAINER_ERROR: There was a health check error.
      CUSTOMIZED_PATH_RESPONSE_PENDING: Health check failed due to user error
        from customized path of the container. System will retry.
      MIN_INSTANCES_NOT_PROVISIONED: A revision with min_instance_count > 0
        was created and is reserved, but it was not configured to serve
        traffic, so it's not live. This can also happen momentarily during
        traffic migration.
      ACTIVE_REVISION_LIMIT_REACHED: The maximum allowed number of active
        revisions has been reached.
      NO_DEPLOYMENT: There was no deployment defined. This value is no longer
        used, but Services created in older versions of the API might contain
        this value.
      HEALTH_CHECK_SKIPPED: A revision's container has no port specified since
        the revision is of a manually scaled service with 0 instance count
      MIN_INSTANCES_WARMING: A revision with min_instance_count > 0 was
        created and is waiting for enough instances to begin a traffic
        migration.
    """
    REVISION_REASON_UNDEFINED = 0
    PENDING = 1
    RESERVE = 2
    RETIRED = 3
    RETIRING = 4
    RECREATING = 5
    HEALTH_CHECK_CONTAINER_ERROR = 6
    CUSTOMIZED_PATH_RESPONSE_PENDING = 7
    MIN_INSTANCES_NOT_PROVISIONED = 8
    ACTIVE_REVISION_LIMIT_REACHED = 9
    NO_DEPLOYMENT = 10
    HEALTH_CHECK_SKIPPED = 11
    MIN_INSTANCES_WARMING = 12

  class SeverityValueValuesEnum(_messages.Enum):
    r"""How to interpret failures of this condition, one of Error, Warning,
    Info

    Values:
      SEVERITY_UNSPECIFIED: Unspecified severity
      ERROR: Error severity.
      WARNING: Warning severity.
      INFO: Info severity.
    """
    SEVERITY_UNSPECIFIED = 0
    ERROR = 1
    WARNING = 2
    INFO = 3

  class StateValueValuesEnum(_messages.Enum):
    r"""State of the condition.

    Values:
      STATE_UNSPECIFIED: The default value. This value is used if the state is
        omitted.
      CONDITION_PENDING: Transient state: Reconciliation has not started yet.
      CONDITION_RECONCILING: Transient state: reconciliation is still in
        progress.
      CONDITION_FAILED: Terminal state: Reconciliation did not succeed.
      CONDITION_SUCCEEDED: Terminal state: Reconciliation completed
        successfully.
    """
    STATE_UNSPECIFIED = 0
    CONDITION_PENDING = 1
    CONDITION_RECONCILING = 2
    CONDITION_FAILED = 3
    CONDITION_SUCCEEDED = 4

  executionReason = _messages.EnumField('ExecutionReasonValueValuesEnum', 1)
  lastTransitionTime = _messages.StringField(2)
  message = _messages.StringField(3)
  reason = _messages.EnumField('ReasonValueValuesEnum', 4)
  revisionReason = _messages.EnumField('RevisionReasonValueValuesEnum', 5)
  severity = _messages.EnumField('SeverityValueValuesEnum', 6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  type = _messages.StringField(8)


class GoogleCloudRunV2Container(_messages.Message):
  r"""A single application container. This specifies both the container to
  run, the command to run in the container and the arguments to supply to it.
  Note that additional arguments can be supplied by the system to the
  container at runtime.

  Fields:
    args: Arguments to the entrypoint. The docker image's CMD is used if this
      is not provided.
    baseImageUri: Base image for this container. Only supported for services.
      If set, it indicates that the service is enrolled into automatic base
      image update.
    buildInfo: Output only. The build info of the container image.
    command: Entrypoint array. Not executed within a shell. The docker image's
      ENTRYPOINT is used if this is not provided.
    dependsOn: Names of the containers that must start before this container.
    env: List of environment variables to set in the container.
    image: Required. Name of the container image in Dockerhub, Google Artifact
      Registry, or Google Container Registry. If the host is not provided,
      Dockerhub is assumed.
    livenessProbe: Periodic probe of container liveness. Container will be
      restarted if the probe fails.
    name: Name of the container specified as a DNS_LABEL (RFC 1123).
    ports: List of ports to expose from the container. Only a single port can
      be specified. The specified ports must be listening on all interfaces
      (0.0.0.0) within the container to be accessible. If omitted, a port
      number will be chosen and passed to the container through the PORT
      environment variable for the container to listen on.
    resources: Compute Resource requirements by this container.
    startupProbe: Startup probe of application within the container. All other
      probes are disabled if a startup probe is provided, until it succeeds.
      Container will not be added to service endpoints if the probe fails.
    volumeMounts: Volume to mount into the container's filesystem.
    workingDir: Container's working directory. If not specified, the container
      runtime's default will be used, which might be configured in the
      container image.
  """

  args = _messages.StringField(1, repeated=True)
  baseImageUri = _messages.StringField(2)
  buildInfo = _messages.MessageField('GoogleCloudRunV2BuildInfo', 3)
  command = _messages.StringField(4, repeated=True)
  dependsOn = _messages.StringField(5, repeated=True)
  env = _messages.MessageField('GoogleCloudRunV2EnvVar', 6, repeated=True)
  image = _messages.StringField(7)
  livenessProbe = _messages.MessageField('GoogleCloudRunV2Probe', 8)
  name = _messages.StringField(9)
  ports = _messages.MessageField('GoogleCloudRunV2ContainerPort', 10, repeated=True)
  resources = _messages.MessageField('GoogleCloudRunV2ResourceRequirements', 11)
  startupProbe = _messages.MessageField('GoogleCloudRunV2Probe', 12)
  volumeMounts = _messages.MessageField('GoogleCloudRunV2VolumeMount', 13, repeated=True)
  workingDir = _messages.StringField(14)


class GoogleCloudRunV2ContainerOverride(_messages.Message):
  r"""Per-container override specification.

  Fields:
    args: Optional. Arguments to the entrypoint. Will replace existing args
      for override.
    clearArgs: Optional. True if the intention is to clear out existing args
      list.
    env: List of environment variables to set in the container. Will be merged
      with existing env for override.
    name: The name of the container specified as a DNS_LABEL.
  """

  args = _messages.StringField(1, repeated=True)
  clearArgs = _messages.BooleanField(2)
  env = _messages.MessageField('GoogleCloudRunV2EnvVar', 3, repeated=True)
  name = _messages.StringField(4)


class GoogleCloudRunV2ContainerPort(_messages.Message):
  r"""ContainerPort represents a network port in a single container.

  Fields:
    containerPort: Port number the container listens on. This must be a valid
      TCP port number, 0 < container_port < 65536.
    name: If specified, used to specify which protocol to use. Allowed values
      are "http1" and "h2c".
  """

  containerPort = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  name = _messages.StringField(2)


class GoogleCloudRunV2DockerBuild(_messages.Message):
  r"""Build the source using Docker. This means the source has a Dockerfile.
  """



class GoogleCloudRunV2EmptyDirVolumeSource(_messages.Message):
  r"""In memory (tmpfs) ephemeral storage. It is ephemeral in the sense that
  when the sandbox is taken down, the data is destroyed with it (it does not
  persist across sandbox runs).

  Enums:
    MediumValueValuesEnum: The medium on which the data is stored. Acceptable
      values today is only MEMORY or none. When none, the default will
      currently be backed by memory but could change over time. +optional

  Fields:
    medium: The medium on which the data is stored. Acceptable values today is
      only MEMORY or none. When none, the default will currently be backed by
      memory but could change over time. +optional
    sizeLimit: Limit on the storage usable by this EmptyDir volume. The size
      limit is also applicable for memory medium. The maximum usage on memory
      medium EmptyDir would be the minimum value between the SizeLimit
      specified here and the sum of memory limits of all containers. The
      default is nil which means that the limit is undefined. More info:
      https://cloud.google.com/run/docs/configuring/in-memory-
      volumes#configure-volume. Info in Kubernetes:
      https://kubernetes.io/docs/concepts/storage/volumes/#emptydir
  """

  class MediumValueValuesEnum(_messages.Enum):
    r"""The medium on which the data is stored. Acceptable values today is
    only MEMORY or none. When none, the default will currently be backed by
    memory but could change over time. +optional

    Values:
      MEDIUM_UNSPECIFIED: When not specified, falls back to the default
        implementation which is currently in memory (this may change over
        time).
      MEMORY: Explicitly set the EmptyDir to be in memory. Uses tmpfs.
    """
    MEDIUM_UNSPECIFIED = 0
    MEMORY = 1

  medium = _messages.EnumField('MediumValueValuesEnum', 1)
  sizeLimit = _messages.StringField(2)


class GoogleCloudRunV2EnvVar(_messages.Message):
  r"""EnvVar represents an environment variable present in a Container.

  Fields:
    name: Required. Name of the environment variable. Must not exceed 32768
      characters.
    value: Literal value of the environment variable. Defaults to "", and the
      maximum length is 32768 bytes. Variable references are not supported in
      Cloud Run.
    valueSource: Source for the environment variable's value.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)
  valueSource = _messages.MessageField('GoogleCloudRunV2EnvVarSource', 3)


class GoogleCloudRunV2EnvVarSource(_messages.Message):
  r"""EnvVarSource represents a source for the value of an EnvVar.

  Fields:
    secretKeyRef: Selects a secret and a specific version from Cloud Secret
      Manager.
  """

  secretKeyRef = _messages.MessageField('GoogleCloudRunV2SecretKeySelector', 1)


class GoogleCloudRunV2Execution(_messages.Message):
  r"""Execution represents the configuration of a single execution. A
  execution an immutable resource that references a container image which is
  run to completion.

  Enums:
    LaunchStageValueValuesEnum: The least stable launch stage needed to create
      this resource, as defined by [Google Cloud Platform Launch
      Stages](https://cloud.google.com/terms/launch-stages). Cloud Run
      supports `ALPHA`, `BETA`, and `GA`. Note that this value might not be
      what was used as input. For example, if ALPHA was provided as input in
      the parent resource, but only BETA and GA-level features are were, this
      field will be BETA.

  Messages:
    AnnotationsValue: Output only. Unstructured key value map that may be set
      by external tools to store and arbitrary metadata. They are not
      queryable and should be preserved when modifying objects.
    LabelsValue: Output only. Unstructured key value map that can be used to
      organize and categorize objects. User-provided labels are shared with
      Google's billing system, so they can be used to filter, or break down
      billing charges by team, component, environment, state, etc. For more
      information, visit https://cloud.google.com/resource-
      manager/docs/creating-managing-labels or
      https://cloud.google.com/run/docs/configuring/labels

  Fields:
    annotations: Output only. Unstructured key value map that may be set by
      external tools to store and arbitrary metadata. They are not queryable
      and should be preserved when modifying objects.
    cancelledCount: Output only. The number of tasks which reached phase
      Cancelled.
    completionTime: Output only. Represents time when the execution was
      completed. It is not guaranteed to be set in happens-before order across
      separate operations.
    conditions: Output only. The Condition of this Execution, containing its
      readiness status, and detailed error information in case it did not
      reach the desired state.
    createTime: Output only. Represents time when the execution was
      acknowledged by the execution controller. It is not guaranteed to be set
      in happens-before order across separate operations.
    creator: Output only. Email address of the authenticated creator.
    deleteTime: Output only. For a deleted resource, the deletion time. It is
      only populated as a response to a Delete request.
    etag: Output only. A system-generated fingerprint for this version of the
      resource. May be used to detect modification conflict during updates.
    expireTime: Output only. For a deleted resource, the time after which it
      will be permamently deleted. It is only populated as a response to a
      Delete request.
    failedCount: Output only. The number of tasks which reached phase Failed.
    generation: Output only. A number that monotonically increases every time
      the user modifies the desired state.
    job: Output only. The name of the parent Job.
    labels: Output only. Unstructured key value map that can be used to
      organize and categorize objects. User-provided labels are shared with
      Google's billing system, so they can be used to filter, or break down
      billing charges by team, component, environment, state, etc. For more
      information, visit https://cloud.google.com/resource-
      manager/docs/creating-managing-labels or
      https://cloud.google.com/run/docs/configuring/labels
    launchStage: The least stable launch stage needed to create this resource,
      as defined by [Google Cloud Platform Launch
      Stages](https://cloud.google.com/terms/launch-stages). Cloud Run
      supports `ALPHA`, `BETA`, and `GA`. Note that this value might not be
      what was used as input. For example, if ALPHA was provided as input in
      the parent resource, but only BETA and GA-level features are were, this
      field will be BETA.
    logUri: Output only. URI where logs for this execution can be found in
      Cloud Console.
    name: Output only. The unique name of this Execution.
    observedGeneration: Output only. The generation of this Execution. See
      comments in `reconciling` for additional information on reconciliation
      process in Cloud Run.
    parallelism: Output only. Specifies the maximum desired number of tasks
      the execution should run at any given time. Must be <= task_count. The
      actual number of tasks running in steady state will be less than this
      number when ((.spec.task_count - .status.successful) <
      .spec.parallelism), i.e. when the work left to do is less than max
      parallelism.
    reconciling: Output only. Indicates whether the resource's reconciliation
      is still in progress. See comments in `Job.reconciling` for additional
      information on reconciliation process in Cloud Run.
    retriedCount: Output only. The number of tasks which have retried at least
      once.
    runningCount: Output only. The number of actively running tasks.
    satisfiesPzs: Output only. Reserved for future use.
    startTime: Output only. Represents time when the execution started to run.
      It is not guaranteed to be set in happens-before order across separate
      operations.
    succeededCount: Output only. The number of tasks which reached phase
      Succeeded.
    taskCount: Output only. Specifies the desired number of tasks the
      execution should run. Setting to 1 means that parallelism is limited to
      1 and the success of that task signals the success of the execution.
    template: Output only. The template used to create tasks for this
      execution.
    uid: Output only. Server assigned unique identifier for the Execution. The
      value is a UUID4 string and guaranteed to remain unchanged until the
      resource is deleted.
    updateTime: Output only. The last-modified time.
  """

  class LaunchStageValueValuesEnum(_messages.Enum):
    r"""The least stable launch stage needed to create this resource, as
    defined by [Google Cloud Platform Launch
    Stages](https://cloud.google.com/terms/launch-stages). Cloud Run supports
    `ALPHA`, `BETA`, and `GA`. Note that this value might not be what was used
    as input. For example, if ALPHA was provided as input in the parent
    resource, but only BETA and GA-level features are were, this field will be
    BETA.

    Values:
      LAUNCH_STAGE_UNSPECIFIED: Do not use this default value.
      UNIMPLEMENTED: The feature is not yet implemented. Users can not use it.
      PRELAUNCH: Prelaunch features are hidden from users and are only visible
        internally.
      EARLY_ACCESS: Early Access features are limited to a closed group of
        testers. To use these features, you must sign up in advance and sign a
        Trusted Tester agreement (which includes confidentiality provisions).
        These features may be unstable, changed in backward-incompatible ways,
        and are not guaranteed to be released.
      ALPHA: Alpha is a limited availability test for releases before they are
        cleared for widespread use. By Alpha, all significant design issues
        are resolved and we are in the process of verifying functionality.
        Alpha customers need to apply for access, agree to applicable terms,
        and have their projects allowlisted. Alpha releases don't have to be
        feature complete, no SLAs are provided, and there are no technical
        support obligations, but they will be far enough along that customers
        can actually use them in test environments or for limited-use tests --
        just like they would in normal production cases.
      BETA: Beta is the point at which we are ready to open a release for any
        customer to use. There are no SLA or technical support obligations in
        a Beta release. Products will be complete from a feature perspective,
        but may have some open outstanding issues. Beta releases are suitable
        for limited production use cases.
      GA: GA features are open to all developers and are considered stable and
        fully qualified for production use.
      DEPRECATED: Deprecated features are scheduled to be shut down and
        removed. For more information, see the "Deprecation Policy" section of
        our [Terms of Service](https://cloud.google.com/terms/) and the
        [Google Cloud Platform Subject to the Deprecation
        Policy](https://cloud.google.com/terms/deprecation) documentation.
    """
    LAUNCH_STAGE_UNSPECIFIED = 0
    UNIMPLEMENTED = 1
    PRELAUNCH = 2
    EARLY_ACCESS = 3
    ALPHA = 4
    BETA = 5
    GA = 6
    DEPRECATED = 7

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Output only. Unstructured key value map that may be set by external
    tools to store and arbitrary metadata. They are not queryable and should
    be preserved when modifying objects.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Output only. Unstructured key value map that can be used to organize
    and categorize objects. User-provided labels are shared with Google's
    billing system, so they can be used to filter, or break down billing
    charges by team, component, environment, state, etc. For more information,
    visit https://cloud.google.com/resource-manager/docs/creating-managing-
    labels or https://cloud.google.com/run/docs/configuring/labels

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  cancelledCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  completionTime = _messages.StringField(3)
  conditions = _messages.MessageField('GoogleCloudRunV2Condition', 4, repeated=True)
  createTime = _messages.StringField(5)
  creator = _messages.StringField(6)
  deleteTime = _messages.StringField(7)
  etag = _messages.StringField(8)
  expireTime = _messages.StringField(9)
  failedCount = _messages.IntegerField(10, variant=_messages.Variant.INT32)
  generation = _messages.IntegerField(11)
  job = _messages.StringField(12)
  labels = _messages.MessageField('LabelsValue', 13)
  launchStage = _messages.EnumField('LaunchStageValueValuesEnum', 14)
  logUri = _messages.StringField(15)
  name = _messages.StringField(16)
  observedGeneration = _messages.IntegerField(17)
  parallelism = _messages.IntegerField(18, variant=_messages.Variant.INT32)
  reconciling = _messages.BooleanField(19)
  retriedCount = _messages.IntegerField(20, variant=_messages.Variant.INT32)
  runningCount = _messages.IntegerField(21, variant=_messages.Variant.INT32)
  satisfiesPzs = _messages.BooleanField(22)
  startTime = _messages.StringField(23)
  succeededCount = _messages.IntegerField(24, variant=_messages.Variant.INT32)
  taskCount = _messages.IntegerField(25, variant=_messages.Variant.INT32)
  template = _messages.MessageField('GoogleCloudRunV2TaskTemplate', 26)
  uid = _messages.StringField(27)
  updateTime = _messages.StringField(28)


class GoogleCloudRunV2ExecutionReference(_messages.Message):
  r"""Reference to an Execution. Use /Executions.GetExecution with the given
  name to get full execution including the latest status.

  Enums:
    CompletionStatusValueValuesEnum: Status for the execution completion.

  Fields:
    completionStatus: Status for the execution completion.
    completionTime: Creation timestamp of the execution.
    createTime: Creation timestamp of the execution.
    deleteTime: The deletion time of the execution. It is only populated as a
      response to a Delete request.
    name: Name of the execution.
  """

  class CompletionStatusValueValuesEnum(_messages.Enum):
    r"""Status for the execution completion.

    Values:
      COMPLETION_STATUS_UNSPECIFIED: The default value. This value is used if
        the state is omitted.
      EXECUTION_SUCCEEDED: Job execution has succeeded.
      EXECUTION_FAILED: Job execution has failed.
      EXECUTION_RUNNING: Job execution is running normally.
      EXECUTION_PENDING: Waiting for backing resources to be provisioned.
      EXECUTION_CANCELLED: Job execution has been cancelled by the user.
    """
    COMPLETION_STATUS_UNSPECIFIED = 0
    EXECUTION_SUCCEEDED = 1
    EXECUTION_FAILED = 2
    EXECUTION_RUNNING = 3
    EXECUTION_PENDING = 4
    EXECUTION_CANCELLED = 5

  completionStatus = _messages.EnumField('CompletionStatusValueValuesEnum', 1)
  completionTime = _messages.StringField(2)
  createTime = _messages.StringField(3)
  deleteTime = _messages.StringField(4)
  name = _messages.StringField(5)


class GoogleCloudRunV2ExecutionTemplate(_messages.Message):
  r"""ExecutionTemplate describes the data an execution should have when
  created from a template.

  Messages:
    AnnotationsValue: Unstructured key value map that may be set by external
      tools to store and arbitrary metadata. They are not queryable and should
      be preserved when modifying objects. Cloud Run API v2 does not support
      annotations with `run.googleapis.com`, `cloud.googleapis.com`,
      `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they
      will be rejected. All system annotations in v1 now have a corresponding
      field in v2 ExecutionTemplate. This field follows Kubernetes
      annotations' namespacing, limits, and rules.
    LabelsValue: Unstructured key value map that can be used to organize and
      categorize objects. User-provided labels are shared with Google's
      billing system, so they can be used to filter, or break down billing
      charges by team, component, environment, state, etc. For more
      information, visit https://cloud.google.com/resource-
      manager/docs/creating-managing-labels or
      https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2
      does not support labels with `run.googleapis.com`,
      `cloud.googleapis.com`, `serving.knative.dev`, or
      `autoscaling.knative.dev` namespaces, and they will be rejected. All
      system labels in v1 now have a corresponding field in v2
      ExecutionTemplate.

  Fields:
    annotations: Unstructured key value map that may be set by external tools
      to store and arbitrary metadata. They are not queryable and should be
      preserved when modifying objects. Cloud Run API v2 does not support
      annotations with `run.googleapis.com`, `cloud.googleapis.com`,
      `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they
      will be rejected. All system annotations in v1 now have a corresponding
      field in v2 ExecutionTemplate. This field follows Kubernetes
      annotations' namespacing, limits, and rules.
    labels: Unstructured key value map that can be used to organize and
      categorize objects. User-provided labels are shared with Google's
      billing system, so they can be used to filter, or break down billing
      charges by team, component, environment, state, etc. For more
      information, visit https://cloud.google.com/resource-
      manager/docs/creating-managing-labels or
      https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2
      does not support labels with `run.googleapis.com`,
      `cloud.googleapis.com`, `serving.knative.dev`, or
      `autoscaling.knative.dev` namespaces, and they will be rejected. All
      system labels in v1 now have a corresponding field in v2
      ExecutionTemplate.
    parallelism: Optional. Specifies the maximum desired number of tasks the
      execution should run at given time. When the job is run, if this field
      is 0 or unset, the maximum possible value will be used for that
      execution. The actual number of tasks running in steady state will be
      less than this number when there are fewer tasks waiting to be completed
      remaining, i.e. when the work left to do is less than max parallelism.
    taskCount: Specifies the desired number of tasks the execution should run.
      Setting to 1 means that parallelism is limited to 1 and the success of
      that task signals the success of the execution. Defaults to 1.
    template: Required. Describes the task(s) that will be created when
      executing an execution.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Unstructured key value map that may be set by external tools to store
    and arbitrary metadata. They are not queryable and should be preserved
    when modifying objects. Cloud Run API v2 does not support annotations with
    `run.googleapis.com`, `cloud.googleapis.com`, `serving.knative.dev`, or
    `autoscaling.knative.dev` namespaces, and they will be rejected. All
    system annotations in v1 now have a corresponding field in v2
    ExecutionTemplate. This field follows Kubernetes annotations' namespacing,
    limits, and rules.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Unstructured key value map that can be used to organize and categorize
    objects. User-provided labels are shared with Google's billing system, so
    they can be used to filter, or break down billing charges by team,
    component, environment, state, etc. For more information, visit
    https://cloud.google.com/resource-manager/docs/creating-managing-labels or
    https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2
    does not support labels with `run.googleapis.com`, `cloud.googleapis.com`,
    `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they
    will be rejected. All system labels in v1 now have a corresponding field
    in v2 ExecutionTemplate.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  labels = _messages.MessageField('LabelsValue', 2)
  parallelism = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  taskCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  template = _messages.MessageField('GoogleCloudRunV2TaskTemplate', 5)


class GoogleCloudRunV2ExportImageRequest(_messages.Message):
  r"""Request message for exporting Cloud Run image.

  Fields:
    destinationRepo: Required. The export destination url (the Artifact
      Registry repo).
  """

  destinationRepo = _messages.StringField(1)


class GoogleCloudRunV2ExportImageResponse(_messages.Message):
  r"""ExportImageResponse contains an operation Id to track the image export
  operation.

  Fields:
    operationId: An operation ID used to track the status of image exports
      tied to the original pod ID in the request.
  """

  operationId = _messages.StringField(1)


class GoogleCloudRunV2ExportStatusResponse(_messages.Message):
  r"""ExportStatusResponse contains the status of image export operation, with
  the status of each image export job.

  Enums:
    OperationStateValueValuesEnum: Output only. The state of the overall
      export operation.

  Fields:
    imageExportStatuses: The status of each image export job.
    operationId: The operation id.
    operationState: Output only. The state of the overall export operation.
  """

  class OperationStateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the overall export operation.

    Values:
      OPERATION_STATE_UNSPECIFIED: State unspecified.
      IN_PROGRESS: Operation still in progress.
      FINISHED: Operation finished.
    """
    OPERATION_STATE_UNSPECIFIED = 0
    IN_PROGRESS = 1
    FINISHED = 2

  imageExportStatuses = _messages.MessageField('GoogleCloudRunV2ImageExportStatus', 1, repeated=True)
  operationId = _messages.StringField(2)
  operationState = _messages.EnumField('OperationStateValueValuesEnum', 3)


class GoogleCloudRunV2GCSVolumeSource(_messages.Message):
  r"""Represents a volume backed by a Cloud Storage bucket using Cloud Storage
  FUSE.

  Fields:
    bucket: Cloud Storage Bucket name.
    mountOptions: A list of additional flags to pass to the gcsfuse CLI.
      Options should be specified without the leading "--".
    readOnly: If true, the volume will be mounted as read only for all mounts.
  """

  bucket = _messages.StringField(1)
  mountOptions = _messages.StringField(2, repeated=True)
  readOnly = _messages.BooleanField(3)


class GoogleCloudRunV2GRPCAction(_messages.Message):
  r"""GRPCAction describes an action involving a GRPC port.

  Fields:
    port: Optional. Port number of the gRPC service. Number must be in the
      range 1 to 65535. If not specified, defaults to the exposed port of the
      container, which is the value of container.ports[0].containerPort.
    service: Optional. Service is the name of the service to place in the gRPC
      HealthCheckRequest (see
      https://github.com/grpc/grpc/blob/master/doc/health-checking.md ). If
      this is not specified, the default behavior is defined by gRPC.
  """

  port = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  service = _messages.StringField(2)


class GoogleCloudRunV2HTTPGetAction(_messages.Message):
  r"""HTTPGetAction describes an action based on HTTP Get requests.

  Fields:
    httpHeaders: Optional. Custom headers to set in the request. HTTP allows
      repeated headers.
    path: Optional. Path to access on the HTTP server. Defaults to '/'.
    port: Optional. Port number to access on the container. Must be in the
      range 1 to 65535. If not specified, defaults to the exposed port of the
      container, which is the value of container.ports[0].containerPort.
  """

  httpHeaders = _messages.MessageField('GoogleCloudRunV2HTTPHeader', 1, repeated=True)
  path = _messages.StringField(2)
  port = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class GoogleCloudRunV2HTTPHeader(_messages.Message):
  r"""HTTPHeader describes a custom header to be used in HTTP probes

  Fields:
    name: Required. The header field name
    value: Optional. The header field value
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class GoogleCloudRunV2ImageExportStatus(_messages.Message):
  r"""The status of an image export job.

  Enums:
    ExportJobStateValueValuesEnum: Output only. Has the image export job
      finished (regardless of successful or failure).

  Fields:
    exportJobState: Output only. Has the image export job finished (regardless
      of successful or failure).
    exportedImageDigest: The exported image ID as it will appear in Artifact
      Registry.
    status: The status of the export task if done.
    tag: The image tag as it will appear in Artifact Registry.
  """

  class ExportJobStateValueValuesEnum(_messages.Enum):
    r"""Output only. Has the image export job finished (regardless of
    successful or failure).

    Values:
      EXPORT_JOB_STATE_UNSPECIFIED: State unspecified.
      IN_PROGRESS: Job still in progress.
      FINISHED: Job finished.
    """
    EXPORT_JOB_STATE_UNSPECIFIED = 0
    IN_PROGRESS = 1
    FINISHED = 2

  exportJobState = _messages.EnumField('ExportJobStateValueValuesEnum', 1)
  exportedImageDigest = _messages.StringField(2)
  status = _messages.MessageField('UtilStatusProto', 3)
  tag = _messages.StringField(4)


class GoogleCloudRunV2InstanceSplit(_messages.Message):
  r"""Holds a single instance split entry for the Worker. Allocations can be
  done to a specific Revision name, or pointing to the latest Ready Revision.

  Enums:
    TypeValueValuesEnum: The allocation type for this instance split.

  Fields:
    percent: Specifies percent of the instance split to this Revision. This
      defaults to zero if unspecified.
    revision: Revision to which to assign this portion of instances, if split
      allocation is by revision.
    type: The allocation type for this instance split.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The allocation type for this instance split.

    Values:
      INSTANCE_SPLIT_ALLOCATION_TYPE_UNSPECIFIED: Unspecified instance
        allocation type.
      INSTANCE_SPLIT_ALLOCATION_TYPE_LATEST: Allocates instances to the
        Service's latest ready Revision.
      INSTANCE_SPLIT_ALLOCATION_TYPE_REVISION: Allocates instances to a
        Revision by name.
    """
    INSTANCE_SPLIT_ALLOCATION_TYPE_UNSPECIFIED = 0
    INSTANCE_SPLIT_ALLOCATION_TYPE_LATEST = 1
    INSTANCE_SPLIT_ALLOCATION_TYPE_REVISION = 2

  percent = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  revision = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class GoogleCloudRunV2InstanceSplitStatus(_messages.Message):
  r"""Represents the observed state of a single `InstanceSplit` entry.

  Enums:
    TypeValueValuesEnum: The allocation type for this instance split.

  Fields:
    percent: Specifies percent of the instance split to this Revision.
    revision: Revision to which this instance split is assigned.
    type: The allocation type for this instance split.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The allocation type for this instance split.

    Values:
      INSTANCE_SPLIT_ALLOCATION_TYPE_UNSPECIFIED: Unspecified instance
        allocation type.
      INSTANCE_SPLIT_ALLOCATION_TYPE_LATEST: Allocates instances to the
        Service's latest ready Revision.
      INSTANCE_SPLIT_ALLOCATION_TYPE_REVISION: Allocates instances to a
        Revision by name.
    """
    INSTANCE_SPLIT_ALLOCATION_TYPE_UNSPECIFIED = 0
    INSTANCE_SPLIT_ALLOCATION_TYPE_LATEST = 1
    INSTANCE_SPLIT_ALLOCATION_TYPE_REVISION = 2

  percent = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  revision = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class GoogleCloudRunV2Job(_messages.Message):
  r"""Job represents the configuration of a single job, which references a
  container image that is run to completion.

  Enums:
    LaunchStageValueValuesEnum: The launch stage as defined by [Google Cloud
      Platform Launch Stages](https://cloud.google.com/terms/launch-stages).
      Cloud Run supports `ALPHA`, `BETA`, and `GA`. If no value is specified,
      GA is assumed. Set the launch stage to a preview stage on input to allow
      use of preview features in that stage. On read (or output), describes
      whether the resource uses preview features. For example, if ALPHA is
      provided as input, but only BETA and GA-level features are used, this
      field will be BETA on output.

  Messages:
    AnnotationsValue: Unstructured key value map that may be set by external
      tools to store and arbitrary metadata. They are not queryable and should
      be preserved when modifying objects. Cloud Run API v2 does not support
      annotations with `run.googleapis.com`, `cloud.googleapis.com`,
      `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they
      will be rejected on new resources. All system annotations in v1 now have
      a corresponding field in v2 Job. This field follows Kubernetes
      annotations' namespacing, limits, and rules.
    LabelsValue: Unstructured key value map that can be used to organize and
      categorize objects. User-provided labels are shared with Google's
      billing system, so they can be used to filter, or break down billing
      charges by team, component, environment, state, etc. For more
      information, visit https://cloud.google.com/resource-
      manager/docs/creating-managing-labels or
      https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2
      does not support labels with `run.googleapis.com`,
      `cloud.googleapis.com`, `serving.knative.dev`, or
      `autoscaling.knative.dev` namespaces, and they will be rejected. All
      system labels in v1 now have a corresponding field in v2 Job.

  Fields:
    annotations: Unstructured key value map that may be set by external tools
      to store and arbitrary metadata. They are not queryable and should be
      preserved when modifying objects. Cloud Run API v2 does not support
      annotations with `run.googleapis.com`, `cloud.googleapis.com`,
      `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they
      will be rejected on new resources. All system annotations in v1 now have
      a corresponding field in v2 Job. This field follows Kubernetes
      annotations' namespacing, limits, and rules.
    binaryAuthorization: Settings for the Binary Authorization feature.
    client: Arbitrary identifier for the API client.
    clientVersion: Arbitrary version identifier for the API client.
    conditions: Output only. The Conditions of all other associated sub-
      resources. They contain additional diagnostics information in case the
      Job does not reach its desired state. See comments in `reconciling` for
      additional information on reconciliation process in Cloud Run.
    createTime: Output only. The creation time.
    creator: Output only. Email address of the authenticated creator.
    deleteTime: Output only. The deletion time. It is only populated as a
      response to a Delete request.
    etag: Optional. A system-generated fingerprint for this version of the
      resource. May be used to detect modification conflict during updates.
    executionCount: Output only. Number of executions created for this job.
    expireTime: Output only. For a deleted resource, the time after which it
      will be permamently deleted.
    generation: Output only. A number that monotonically increases every time
      the user modifies the desired state.
    labels: Unstructured key value map that can be used to organize and
      categorize objects. User-provided labels are shared with Google's
      billing system, so they can be used to filter, or break down billing
      charges by team, component, environment, state, etc. For more
      information, visit https://cloud.google.com/resource-
      manager/docs/creating-managing-labels or
      https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2
      does not support labels with `run.googleapis.com`,
      `cloud.googleapis.com`, `serving.knative.dev`, or
      `autoscaling.knative.dev` namespaces, and they will be rejected. All
      system labels in v1 now have a corresponding field in v2 Job.
    lastModifier: Output only. Email address of the last authenticated
      modifier.
    latestCreatedExecution: Output only. Name of the last created execution.
    launchStage: The launch stage as defined by [Google Cloud Platform Launch
      Stages](https://cloud.google.com/terms/launch-stages). Cloud Run
      supports `ALPHA`, `BETA`, and `GA`. If no value is specified, GA is
      assumed. Set the launch stage to a preview stage on input to allow use
      of preview features in that stage. On read (or output), describes
      whether the resource uses preview features. For example, if ALPHA is
      provided as input, but only BETA and GA-level features are used, this
      field will be BETA on output.
    name: The fully qualified name of this Job. Format:
      projects/{project}/locations/{location}/jobs/{job}
    observedGeneration: Output only. The generation of this Job. See comments
      in `reconciling` for additional information on reconciliation process in
      Cloud Run.
    reconciling: Output only. Returns true if the Job is currently being acted
      upon by the system to bring it into the desired state. When a new Job is
      created, or an existing one is updated, Cloud Run will asynchronously
      perform all necessary steps to bring the Job to the desired state. This
      process is called reconciliation. While reconciliation is in process,
      `observed_generation` and `latest_succeeded_execution`, will have
      transient values that might mismatch the intended state: Once
      reconciliation is over (and this field is false), there are two possible
      outcomes: reconciliation succeeded and the state matches the Job, or
      there was an error, and reconciliation failed. This state can be found
      in `terminal_condition.state`. If reconciliation succeeded, the
      following fields will match: `observed_generation` and `generation`,
      `latest_succeeded_execution` and `latest_created_execution`. If
      reconciliation failed, `observed_generation` and
      `latest_succeeded_execution` will have the state of the last succeeded
      execution or empty for newly created Job. Additional information on the
      failure can be found in `terminal_condition` and `conditions`.
    runExecutionToken: A unique string used as a suffix for creating a new
      execution. The Job will become ready when the execution is successfully
      completed. The sum of job name and token length must be fewer than 63
      characters.
    satisfiesPzs: Output only. Reserved for future use.
    startExecutionToken: A unique string used as a suffix creating a new
      execution. The Job will become ready when the execution is successfully
      started. The sum of job name and token length must be fewer than 63
      characters.
    template: Required. The template used to create executions for this Job.
    terminalCondition: Output only. The Condition of this Job, containing its
      readiness status, and detailed error information in case it did not
      reach the desired state.
    uid: Output only. Server assigned unique identifier for the Execution. The
      value is a UUID4 string and guaranteed to remain unchanged until the
      resource is deleted.
    updateTime: Output only. The last-modified time.
  """

  class LaunchStageValueValuesEnum(_messages.Enum):
    r"""The launch stage as defined by [Google Cloud Platform Launch
    Stages](https://cloud.google.com/terms/launch-stages). Cloud Run supports
    `ALPHA`, `BETA`, and `GA`. If no value is specified, GA is assumed. Set
    the launch stage to a preview stage on input to allow use of preview
    features in that stage. On read (or output), describes whether the
    resource uses preview features. For example, if ALPHA is provided as
    input, but only BETA and GA-level features are used, this field will be
    BETA on output.

    Values:
      LAUNCH_STAGE_UNSPECIFIED: Do not use this default value.
      UNIMPLEMENTED: The feature is not yet implemented. Users can not use it.
      PRELAUNCH: Prelaunch features are hidden from users and are only visible
        internally.
      EARLY_ACCESS: Early Access features are limited to a closed group of
        testers. To use these features, you must sign up in advance and sign a
        Trusted Tester agreement (which includes confidentiality provisions).
        These features may be unstable, changed in backward-incompatible ways,
        and are not guaranteed to be released.
      ALPHA: Alpha is a limited availability test for releases before they are
        cleared for widespread use. By Alpha, all significant design issues
        are resolved and we are in the process of verifying functionality.
        Alpha customers need to apply for access, agree to applicable terms,
        and have their projects allowlisted. Alpha releases don't have to be
        feature complete, no SLAs are provided, and there are no technical
        support obligations, but they will be far enough along that customers
        can actually use them in test environments or for limited-use tests --
        just like they would in normal production cases.
      BETA: Beta is the point at which we are ready to open a release for any
        customer to use. There are no SLA or technical support obligations in
        a Beta release. Products will be complete from a feature perspective,
        but may have some open outstanding issues. Beta releases are suitable
        for limited production use cases.
      GA: GA features are open to all developers and are considered stable and
        fully qualified for production use.
      DEPRECATED: Deprecated features are scheduled to be shut down and
        removed. For more information, see the "Deprecation Policy" section of
        our [Terms of Service](https://cloud.google.com/terms/) and the
        [Google Cloud Platform Subject to the Deprecation
        Policy](https://cloud.google.com/terms/deprecation) documentation.
    """
    LAUNCH_STAGE_UNSPECIFIED = 0
    UNIMPLEMENTED = 1
    PRELAUNCH = 2
    EARLY_ACCESS = 3
    ALPHA = 4
    BETA = 5
    GA = 6
    DEPRECATED = 7

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Unstructured key value map that may be set by external tools to store
    and arbitrary metadata. They are not queryable and should be preserved
    when modifying objects. Cloud Run API v2 does not support annotations with
    `run.googleapis.com`, `cloud.googleapis.com`, `serving.knative.dev`, or
    `autoscaling.knative.dev` namespaces, and they will be rejected on new
    resources. All system annotations in v1 now have a corresponding field in
    v2 Job. This field follows Kubernetes annotations' namespacing, limits,
    and rules.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Unstructured key value map that can be used to organize and categorize
    objects. User-provided labels are shared with Google's billing system, so
    they can be used to filter, or break down billing charges by team,
    component, environment, state, etc. For more information, visit
    https://cloud.google.com/resource-manager/docs/creating-managing-labels or
    https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2
    does not support labels with `run.googleapis.com`, `cloud.googleapis.com`,
    `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they
    will be rejected. All system labels in v1 now have a corresponding field
    in v2 Job.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  binaryAuthorization = _messages.MessageField('GoogleCloudRunV2BinaryAuthorization', 2)
  client = _messages.StringField(3)
  clientVersion = _messages.StringField(4)
  conditions = _messages.MessageField('GoogleCloudRunV2Condition', 5, repeated=True)
  createTime = _messages.StringField(6)
  creator = _messages.StringField(7)
  deleteTime = _messages.StringField(8)
  etag = _messages.StringField(9)
  executionCount = _messages.IntegerField(10, variant=_messages.Variant.INT32)
  expireTime = _messages.StringField(11)
  generation = _messages.IntegerField(12)
  labels = _messages.MessageField('LabelsValue', 13)
  lastModifier = _messages.StringField(14)
  latestCreatedExecution = _messages.MessageField('GoogleCloudRunV2ExecutionReference', 15)
  launchStage = _messages.EnumField('LaunchStageValueValuesEnum', 16)
  name = _messages.StringField(17)
  observedGeneration = _messages.IntegerField(18)
  reconciling = _messages.BooleanField(19)
  runExecutionToken = _messages.StringField(20)
  satisfiesPzs = _messages.BooleanField(21)
  startExecutionToken = _messages.StringField(22)
  template = _messages.MessageField('GoogleCloudRunV2ExecutionTemplate', 23)
  terminalCondition = _messages.MessageField('GoogleCloudRunV2Condition', 24)
  uid = _messages.StringField(25)
  updateTime = _messages.StringField(26)


class GoogleCloudRunV2ListExecutionsResponse(_messages.Message):
  r"""Response message containing a list of Executions.

  Fields:
    executions: The resulting list of Executions.
    nextPageToken: A token indicating there are more items than page_size. Use
      it in the next ListExecutions request to continue.
  """

  executions = _messages.MessageField('GoogleCloudRunV2Execution', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudRunV2ListJobsResponse(_messages.Message):
  r"""Response message containing a list of Jobs.

  Fields:
    jobs: The resulting list of Jobs.
    nextPageToken: A token indicating there are more items than page_size. Use
      it in the next ListJobs request to continue.
  """

  jobs = _messages.MessageField('GoogleCloudRunV2Job', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudRunV2ListRevisionsResponse(_messages.Message):
  r"""Response message containing a list of Revisions.

  Fields:
    nextPageToken: A token indicating there are more items than page_size. Use
      it in the next ListRevisions request to continue.
    revisions: The resulting list of Revisions.
  """

  nextPageToken = _messages.StringField(1)
  revisions = _messages.MessageField('GoogleCloudRunV2Revision', 2, repeated=True)


class GoogleCloudRunV2ListServicesResponse(_messages.Message):
  r"""Response message containing a list of Services.

  Fields:
    nextPageToken: A token indicating there are more items than page_size. Use
      it in the next ListServices request to continue.
    services: The resulting list of Services.
  """

  nextPageToken = _messages.StringField(1)
  services = _messages.MessageField('GoogleCloudRunV2Service', 2, repeated=True)


class GoogleCloudRunV2ListTasksResponse(_messages.Message):
  r"""Response message containing a list of Tasks.

  Fields:
    nextPageToken: A token indicating there are more items than page_size. Use
      it in the next ListTasks request to continue.
    tasks: The resulting list of Tasks.
  """

  nextPageToken = _messages.StringField(1)
  tasks = _messages.MessageField('GoogleCloudRunV2Task', 2, repeated=True)


class GoogleCloudRunV2ListWorkerPoolsResponse(_messages.Message):
  r"""Response message containing a list of WorkerPools.

  Fields:
    nextPageToken: A token indicating there are more items than page_size. Use
      it in the next ListWorkerPools request to continue.
    workerPools: The resulting list of WorkerPools.
  """

  nextPageToken = _messages.StringField(1)
  workerPools = _messages.MessageField('GoogleCloudRunV2WorkerPool', 2, repeated=True)


class GoogleCloudRunV2Metadata(_messages.Message):
  r"""Metadata represents the JSON encoded generated customer metadata.

  Fields:
    metadata: JSON encoded Google-generated Customer Metadata for a given
      resource/project.
  """

  metadata = _messages.StringField(1)


class GoogleCloudRunV2MultiRegionSettings(_messages.Message):
  r"""Settings for multi-region deployment.

  Fields:
    multiRegionId: Optional. System-generated unique id for the multi-region
      Service.
    regions: Required. List of regions to deploy to, including primary region.
  """

  multiRegionId = _messages.StringField(1)
  regions = _messages.StringField(2, repeated=True)


class GoogleCloudRunV2NFSVolumeSource(_messages.Message):
  r"""Represents an NFS mount.

  Fields:
    path: Path that is exported by the NFS server.
    readOnly: If true, the volume will be mounted as read only for all mounts.
    server: Hostname or IP address of the NFS server
  """

  path = _messages.StringField(1)
  readOnly = _messages.BooleanField(2)
  server = _messages.StringField(3)


class GoogleCloudRunV2NetworkInterface(_messages.Message):
  r"""Direct VPC egress settings.

  Fields:
    network: Optional. The VPC network that the Cloud Run resource will be
      able to send traffic to. At least one of network or subnetwork must be
      specified. If both network and subnetwork are specified, the given VPC
      subnetwork must belong to the given VPC network. If network is not
      specified, it will be looked up from the subnetwork.
    subnetwork: Optional. The VPC subnetwork that the Cloud Run resource will
      get IPs from. At least one of network or subnetwork must be specified.
      If both network and subnetwork are specified, the given VPC subnetwork
      must belong to the given VPC network. If subnetwork is not specified,
      the subnetwork with the same name with the network will be used.
    tags: Optional. Network tags applied to this Cloud Run resource.
  """

  network = _messages.StringField(1)
  subnetwork = _messages.StringField(2)
  tags = _messages.StringField(3, repeated=True)


class GoogleCloudRunV2NodeSelector(_messages.Message):
  r"""Hardware constraints configuration.

  Fields:
    accelerator: Required. GPU accelerator type to attach to an instance.
  """

  accelerator = _messages.StringField(1)


class GoogleCloudRunV2Overrides(_messages.Message):
  r"""RunJob Overrides that contains Execution fields to be overridden.

  Fields:
    containerOverrides: Per container override specification.
    taskCount: Optional. The desired number of tasks the execution should run.
      Will replace existing task_count value.
    timeout: Duration in seconds the task may be active before the system will
      actively try to mark it failed and kill associated containers. Will
      replace existing timeout_seconds value.
  """

  containerOverrides = _messages.MessageField('GoogleCloudRunV2ContainerOverride', 1, repeated=True)
  taskCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  timeout = _messages.StringField(3)


class GoogleCloudRunV2Probe(_messages.Message):
  r"""Probe describes a health check to be performed against a container to
  determine whether it is alive or ready to receive traffic.

  Fields:
    failureThreshold: Optional. Minimum consecutive failures for the probe to
      be considered failed after having succeeded. Defaults to 3. Minimum
      value is 1.
    grpc: Optional. GRPC specifies an action involving a gRPC port. Exactly
      one of httpGet, tcpSocket, or grpc must be specified.
    httpGet: Optional. HTTPGet specifies the http request to perform. Exactly
      one of httpGet, tcpSocket, or grpc must be specified.
    initialDelaySeconds: Optional. Number of seconds after the container has
      started before the probe is initiated. Defaults to 0 seconds. Minimum
      value is 0. Maximum value for liveness probe is 3600. Maximum value for
      startup probe is 240.
    periodSeconds: Optional. How often (in seconds) to perform the probe.
      Default to 10 seconds. Minimum value is 1. Maximum value for liveness
      probe is 3600. Maximum value for startup probe is 240. Must be greater
      or equal than timeout_seconds.
    tcpSocket: Optional. TCPSocket specifies an action involving a TCP port.
      Exactly one of httpGet, tcpSocket, or grpc must be specified.
    timeoutSeconds: Optional. Number of seconds after which the probe times
      out. Defaults to 1 second. Minimum value is 1. Maximum value is 3600.
      Must be smaller than period_seconds.
  """

  failureThreshold = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  grpc = _messages.MessageField('GoogleCloudRunV2GRPCAction', 2)
  httpGet = _messages.MessageField('GoogleCloudRunV2HTTPGetAction', 3)
  initialDelaySeconds = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  periodSeconds = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  tcpSocket = _messages.MessageField('GoogleCloudRunV2TCPSocketAction', 6)
  timeoutSeconds = _messages.IntegerField(7, variant=_messages.Variant.INT32)


class GoogleCloudRunV2ResourceRequirements(_messages.Message):
  r"""ResourceRequirements describes the compute resource requirements.

  Messages:
    LimitsValue: Only `memory` and `cpu` keys in the map are supported. Notes:
      * The only supported values for CPU are '1', '2', '4', and '8'. Setting
      4 CPU requires at least 2Gi of memory. For more information, go to
      https://cloud.google.com/run/docs/configuring/cpu. * For supported
      'memory' values and syntax, go to
      https://cloud.google.com/run/docs/configuring/memory-limits

  Fields:
    cpuIdle: Determines whether CPU is only allocated during requests (true by
      default). However, if ResourceRequirements is set, the caller must
      explicitly set this field to true to preserve the default behavior.
    limits: Only `memory` and `cpu` keys in the map are supported. Notes: *
      The only supported values for CPU are '1', '2', '4', and '8'. Setting 4
      CPU requires at least 2Gi of memory. For more information, go to
      https://cloud.google.com/run/docs/configuring/cpu. * For supported
      'memory' values and syntax, go to
      https://cloud.google.com/run/docs/configuring/memory-limits
    startupCpuBoost: Determines whether CPU should be boosted on startup of a
      new container instance above the requested CPU threshold, this can help
      reduce cold-start latency.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LimitsValue(_messages.Message):
    r"""Only `memory` and `cpu` keys in the map are supported. Notes: * The
    only supported values for CPU are '1', '2', '4', and '8'. Setting 4 CPU
    requires at least 2Gi of memory. For more information, go to
    https://cloud.google.com/run/docs/configuring/cpu. * For supported
    'memory' values and syntax, go to
    https://cloud.google.com/run/docs/configuring/memory-limits

    Messages:
      AdditionalProperty: An additional property for a LimitsValue object.

    Fields:
      additionalProperties: Additional properties of type LimitsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LimitsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  cpuIdle = _messages.BooleanField(1)
  limits = _messages.MessageField('LimitsValue', 2)
  startupCpuBoost = _messages.BooleanField(3)


class GoogleCloudRunV2Revision(_messages.Message):
  r"""A Revision is an immutable snapshot of code and configuration. A
  Revision references a container image. Revisions are only created by updates
  to its parent Service.

  Enums:
    EncryptionKeyRevocationActionValueValuesEnum: The action to take if the
      encryption key is revoked.
    ExecutionEnvironmentValueValuesEnum: The execution environment being used
      to host this Revision.
    LaunchStageValueValuesEnum: The least stable launch stage needed to create
      this resource, as defined by [Google Cloud Platform Launch
      Stages](https://cloud.google.com/terms/launch-stages). Cloud Run
      supports `ALPHA`, `BETA`, and `GA`. Note that this value might not be
      what was used as input. For example, if ALPHA was provided as input in
      the parent resource, but only BETA and GA-level features are were, this
      field will be BETA.

  Messages:
    AnnotationsValue: Output only. Unstructured key value map that may be set
      by external tools to store and arbitrary metadata. They are not
      queryable and should be preserved when modifying objects.
    LabelsValue: Output only. Unstructured key value map that can be used to
      organize and categorize objects. User-provided labels are shared with
      Google's billing system, so they can be used to filter, or break down
      billing charges by team, component, environment, state, etc. For more
      information, visit https://cloud.google.com/resource-
      manager/docs/creating-managing-labels or
      https://cloud.google.com/run/docs/configuring/labels.

  Fields:
    annotations: Output only. Unstructured key value map that may be set by
      external tools to store and arbitrary metadata. They are not queryable
      and should be preserved when modifying objects.
    conditions: Output only. The Condition of this Revision, containing its
      readiness status, and detailed error information in case it did not
      reach a serving state.
    containers: Holds the single container that defines the unit of execution
      for this Revision.
    createTime: Output only. The creation time.
    creator: Output only. Email address of the authenticated creator.
    deleteTime: Output only. For a deleted resource, the deletion time. It is
      only populated as a response to a Delete request.
    encryptionKey: A reference to a customer managed encryption key (CMEK) to
      use to encrypt this container image. For more information, go to
      https://cloud.google.com/run/docs/securing/using-cmek
    encryptionKeyRevocationAction: The action to take if the encryption key is
      revoked.
    encryptionKeyShutdownDuration: If encryption_key_revocation_action is
      SHUTDOWN, the duration before shutting down all instances. The minimum
      increment is 1 hour.
    etag: Output only. A system-generated fingerprint for this version of the
      resource. May be used to detect modification conflict during updates.
    executionEnvironment: The execution environment being used to host this
      Revision.
    expireTime: Output only. For a deleted resource, the time after which it
      will be permamently deleted. It is only populated as a response to a
      Delete request.
    generation: Output only. A number that monotonically increases every time
      the user modifies the desired state.
    gpuZonalRedundancyDisabled: Optional. Output only. True if GPU zonal
      redundancy is disabled on this revision.
    labels: Output only. Unstructured key value map that can be used to
      organize and categorize objects. User-provided labels are shared with
      Google's billing system, so they can be used to filter, or break down
      billing charges by team, component, environment, state, etc. For more
      information, visit https://cloud.google.com/resource-
      manager/docs/creating-managing-labels or
      https://cloud.google.com/run/docs/configuring/labels.
    launchStage: The least stable launch stage needed to create this resource,
      as defined by [Google Cloud Platform Launch
      Stages](https://cloud.google.com/terms/launch-stages). Cloud Run
      supports `ALPHA`, `BETA`, and `GA`. Note that this value might not be
      what was used as input. For example, if ALPHA was provided as input in
      the parent resource, but only BETA and GA-level features are were, this
      field will be BETA.
    logUri: Output only. The Google Console URI to obtain logs for the
      Revision.
    maxInstanceRequestConcurrency: Sets the maximum number of requests that
      each serving instance can receive.
    name: Output only. The unique name of this Revision.
    nodeSelector: The node selector for the revision.
    observedGeneration: Output only. The generation of this Revision currently
      serving traffic. See comments in `reconciling` for additional
      information on reconciliation process in Cloud Run.
    reconciling: Output only. Indicates whether the resource's reconciliation
      is still in progress. See comments in `Service.reconciling` for
      additional information on reconciliation process in Cloud Run.
    satisfiesPzs: Output only. Reserved for future use.
    scaling: Scaling settings for this revision.
    scalingStatus: Output only. The current effective scaling settings for the
      revision.
    service: Output only. The name of the parent service.
    serviceAccount: Email address of the IAM service account associated with
      the revision of the service. The service account represents the identity
      of the running revision, and determines what permissions the revision
      has.
    serviceMesh: Enables service mesh connectivity.
    sessionAffinity: Enable session affinity.
    timeout: Max allowed time for an instance to respond to a request.
    uid: Output only. Server assigned unique identifier for the Revision. The
      value is a UUID4 string and guaranteed to remain unchanged until the
      resource is deleted.
    updateTime: Output only. The last-modified time.
    volumes: A list of Volumes to make available to containers.
    vpcAccess: VPC Access configuration for this Revision. For more
      information, visit
      https://cloud.google.com/run/docs/configuring/connecting-vpc.
  """

  class EncryptionKeyRevocationActionValueValuesEnum(_messages.Enum):
    r"""The action to take if the encryption key is revoked.

    Values:
      ENCRYPTION_KEY_REVOCATION_ACTION_UNSPECIFIED: Unspecified
      PREVENT_NEW: Prevents the creation of new instances.
      SHUTDOWN: Shuts down existing instances, and prevents creation of new
        ones.
    """
    ENCRYPTION_KEY_REVOCATION_ACTION_UNSPECIFIED = 0
    PREVENT_NEW = 1
    SHUTDOWN = 2

  class ExecutionEnvironmentValueValuesEnum(_messages.Enum):
    r"""The execution environment being used to host this Revision.

    Values:
      EXECUTION_ENVIRONMENT_UNSPECIFIED: Unspecified
      EXECUTION_ENVIRONMENT_GEN1: Uses the First Generation environment.
      EXECUTION_ENVIRONMENT_GEN2: Uses Second Generation environment.
    """
    EXECUTION_ENVIRONMENT_UNSPECIFIED = 0
    EXECUTION_ENVIRONMENT_GEN1 = 1
    EXECUTION_ENVIRONMENT_GEN2 = 2

  class LaunchStageValueValuesEnum(_messages.Enum):
    r"""The least stable launch stage needed to create this resource, as
    defined by [Google Cloud Platform Launch
    Stages](https://cloud.google.com/terms/launch-stages). Cloud Run supports
    `ALPHA`, `BETA`, and `GA`. Note that this value might not be what was used
    as input. For example, if ALPHA was provided as input in the parent
    resource, but only BETA and GA-level features are were, this field will be
    BETA.

    Values:
      LAUNCH_STAGE_UNSPECIFIED: Do not use this default value.
      UNIMPLEMENTED: The feature is not yet implemented. Users can not use it.
      PRELAUNCH: Prelaunch features are hidden from users and are only visible
        internally.
      EARLY_ACCESS: Early Access features are limited to a closed group of
        testers. To use these features, you must sign up in advance and sign a
        Trusted Tester agreement (which includes confidentiality provisions).
        These features may be unstable, changed in backward-incompatible ways,
        and are not guaranteed to be released.
      ALPHA: Alpha is a limited availability test for releases before they are
        cleared for widespread use. By Alpha, all significant design issues
        are resolved and we are in the process of verifying functionality.
        Alpha customers need to apply for access, agree to applicable terms,
        and have their projects allowlisted. Alpha releases don't have to be
        feature complete, no SLAs are provided, and there are no technical
        support obligations, but they will be far enough along that customers
        can actually use them in test environments or for limited-use tests --
        just like they would in normal production cases.
      BETA: Beta is the point at which we are ready to open a release for any
        customer to use. There are no SLA or technical support obligations in
        a Beta release. Products will be complete from a feature perspective,
        but may have some open outstanding issues. Beta releases are suitable
        for limited production use cases.
      GA: GA features are open to all developers and are considered stable and
        fully qualified for production use.
      DEPRECATED: Deprecated features are scheduled to be shut down and
        removed. For more information, see the "Deprecation Policy" section of
        our [Terms of Service](https://cloud.google.com/terms/) and the
        [Google Cloud Platform Subject to the Deprecation
        Policy](https://cloud.google.com/terms/deprecation) documentation.
    """
    LAUNCH_STAGE_UNSPECIFIED = 0
    UNIMPLEMENTED = 1
    PRELAUNCH = 2
    EARLY_ACCESS = 3
    ALPHA = 4
    BETA = 5
    GA = 6
    DEPRECATED = 7

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Output only. Unstructured key value map that may be set by external
    tools to store and arbitrary metadata. They are not queryable and should
    be preserved when modifying objects.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Output only. Unstructured key value map that can be used to organize
    and categorize objects. User-provided labels are shared with Google's
    billing system, so they can be used to filter, or break down billing
    charges by team, component, environment, state, etc. For more information,
    visit https://cloud.google.com/resource-manager/docs/creating-managing-
    labels or https://cloud.google.com/run/docs/configuring/labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  conditions = _messages.MessageField('GoogleCloudRunV2Condition', 2, repeated=True)
  containers = _messages.MessageField('GoogleCloudRunV2Container', 3, repeated=True)
  createTime = _messages.StringField(4)
  creator = _messages.StringField(5)
  deleteTime = _messages.StringField(6)
  encryptionKey = _messages.StringField(7)
  encryptionKeyRevocationAction = _messages.EnumField('EncryptionKeyRevocationActionValueValuesEnum', 8)
  encryptionKeyShutdownDuration = _messages.StringField(9)
  etag = _messages.StringField(10)
  executionEnvironment = _messages.EnumField('ExecutionEnvironmentValueValuesEnum', 11)
  expireTime = _messages.StringField(12)
  generation = _messages.IntegerField(13)
  gpuZonalRedundancyDisabled = _messages.BooleanField(14)
  labels = _messages.MessageField('LabelsValue', 15)
  launchStage = _messages.EnumField('LaunchStageValueValuesEnum', 16)
  logUri = _messages.StringField(17)
  maxInstanceRequestConcurrency = _messages.IntegerField(18, variant=_messages.Variant.INT32)
  name = _messages.StringField(19)
  nodeSelector = _messages.MessageField('GoogleCloudRunV2NodeSelector', 20)
  observedGeneration = _messages.IntegerField(21)
  reconciling = _messages.BooleanField(22)
  satisfiesPzs = _messages.BooleanField(23)
  scaling = _messages.MessageField('GoogleCloudRunV2RevisionScaling', 24)
  scalingStatus = _messages.MessageField('GoogleCloudRunV2RevisionScalingStatus', 25)
  service = _messages.StringField(26)
  serviceAccount = _messages.StringField(27)
  serviceMesh = _messages.MessageField('GoogleCloudRunV2ServiceMesh', 28)
  sessionAffinity = _messages.BooleanField(29)
  timeout = _messages.StringField(30)
  uid = _messages.StringField(31)
  updateTime = _messages.StringField(32)
  volumes = _messages.MessageField('GoogleCloudRunV2Volume', 33, repeated=True)
  vpcAccess = _messages.MessageField('GoogleCloudRunV2VpcAccess', 34)


class GoogleCloudRunV2RevisionScaling(_messages.Message):
  r"""Settings for revision-level scaling settings.

  Fields:
    maxInstanceCount: Optional. Maximum number of serving instances that this
      resource should have. When unspecified, the field is set to the server
      default value of 100. For more information see
      https://cloud.google.com/run/docs/configuring/max-instances
    minInstanceCount: Optional. Minimum number of serving instances that this
      resource should have.
  """

  maxInstanceCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minInstanceCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleCloudRunV2RevisionScalingStatus(_messages.Message):
  r"""Effective settings for the current revision

  Fields:
    desiredMinInstanceCount: The current number of min instances provisioned
      for this revision.
  """

  desiredMinInstanceCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class GoogleCloudRunV2RevisionTemplate(_messages.Message):
  r"""RevisionTemplate describes the data a revision should have when created
  from a template.

  Enums:
    EncryptionKeyRevocationActionValueValuesEnum: Optional. The action to take
      if the encryption key is revoked.
    ExecutionEnvironmentValueValuesEnum: Optional. The sandbox environment to
      host this Revision.

  Messages:
    AnnotationsValue: Optional. Unstructured key value map that may be set by
      external tools to store and arbitrary metadata. They are not queryable
      and should be preserved when modifying objects. Cloud Run API v2 does
      not support annotations with `run.googleapis.com`,
      `cloud.googleapis.com`, `serving.knative.dev`, or
      `autoscaling.knative.dev` namespaces, and they will be rejected. All
      system annotations in v1 now have a corresponding field in v2
      RevisionTemplate. This field follows Kubernetes annotations'
      namespacing, limits, and rules.
    LabelsValue: Optional. Unstructured key value map that can be used to
      organize and categorize objects. User-provided labels are shared with
      Google's billing system, so they can be used to filter, or break down
      billing charges by team, component, environment, state, etc. For more
      information, visit https://cloud.google.com/resource-
      manager/docs/creating-managing-labels or
      https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2
      does not support labels with `run.googleapis.com`,
      `cloud.googleapis.com`, `serving.knative.dev`, or
      `autoscaling.knative.dev` namespaces, and they will be rejected. All
      system labels in v1 now have a corresponding field in v2
      RevisionTemplate.

  Fields:
    annotations: Optional. Unstructured key value map that may be set by
      external tools to store and arbitrary metadata. They are not queryable
      and should be preserved when modifying objects. Cloud Run API v2 does
      not support annotations with `run.googleapis.com`,
      `cloud.googleapis.com`, `serving.knative.dev`, or
      `autoscaling.knative.dev` namespaces, and they will be rejected. All
      system annotations in v1 now have a corresponding field in v2
      RevisionTemplate. This field follows Kubernetes annotations'
      namespacing, limits, and rules.
    containers: Holds the single container that defines the unit of execution
      for this Revision.
    encryptionKey: A reference to a customer managed encryption key (CMEK) to
      use to encrypt this container image. For more information, go to
      https://cloud.google.com/run/docs/securing/using-cmek
    encryptionKeyRevocationAction: Optional. The action to take if the
      encryption key is revoked.
    encryptionKeyShutdownDuration: Optional. If
      encryption_key_revocation_action is SHUTDOWN, the duration before
      shutting down all instances. The minimum increment is 1 hour.
    executionEnvironment: Optional. The sandbox environment to host this
      Revision.
    gpuZonalRedundancyDisabled: Optional. True if GPU zonal redundancy is
      disabled on this revision.
    healthCheckDisabled: Optional. Disables health checking containers during
      deployment.
    labels: Optional. Unstructured key value map that can be used to organize
      and categorize objects. User-provided labels are shared with Google's
      billing system, so they can be used to filter, or break down billing
      charges by team, component, environment, state, etc. For more
      information, visit https://cloud.google.com/resource-
      manager/docs/creating-managing-labels or
      https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2
      does not support labels with `run.googleapis.com`,
      `cloud.googleapis.com`, `serving.knative.dev`, or
      `autoscaling.knative.dev` namespaces, and they will be rejected. All
      system labels in v1 now have a corresponding field in v2
      RevisionTemplate.
    maxInstanceRequestConcurrency: Optional. Sets the maximum number of
      requests that each serving instance can receive. If not specified or 0,
      concurrency defaults to 80 when requested `CPU >= 1` and defaults to 1
      when requested `CPU < 1`.
    nodeSelector: Optional. The node selector for the revision template.
    revision: Optional. The unique name for the revision. If this field is
      omitted, it will be automatically generated based on the Service name.
    scaling: Optional. Scaling settings for this Revision.
    serviceAccount: Optional. Email address of the IAM service account
      associated with the revision of the service. The service account
      represents the identity of the running revision, and determines what
      permissions the revision has. If not provided, the revision will use the
      project's default service account.
    serviceMesh: Optional. Enables service mesh connectivity.
    sessionAffinity: Optional. Enable session affinity.
    timeout: Optional. Max allowed time for an instance to respond to a
      request.
    volumes: Optional. A list of Volumes to make available to containers.
    vpcAccess: Optional. VPC Access configuration to use for this Revision.
      For more information, visit
      https://cloud.google.com/run/docs/configuring/connecting-vpc.
  """

  class EncryptionKeyRevocationActionValueValuesEnum(_messages.Enum):
    r"""Optional. The action to take if the encryption key is revoked.

    Values:
      ENCRYPTION_KEY_REVOCATION_ACTION_UNSPECIFIED: Unspecified
      PREVENT_NEW: Prevents the creation of new instances.
      SHUTDOWN: Shuts down existing instances, and prevents creation of new
        ones.
    """
    ENCRYPTION_KEY_REVOCATION_ACTION_UNSPECIFIED = 0
    PREVENT_NEW = 1
    SHUTDOWN = 2

  class ExecutionEnvironmentValueValuesEnum(_messages.Enum):
    r"""Optional. The sandbox environment to host this Revision.

    Values:
      EXECUTION_ENVIRONMENT_UNSPECIFIED: Unspecified
      EXECUTION_ENVIRONMENT_GEN1: Uses the First Generation environment.
      EXECUTION_ENVIRONMENT_GEN2: Uses Second Generation environment.
    """
    EXECUTION_ENVIRONMENT_UNSPECIFIED = 0
    EXECUTION_ENVIRONMENT_GEN1 = 1
    EXECUTION_ENVIRONMENT_GEN2 = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Unstructured key value map that may be set by external tools
    to store and arbitrary metadata. They are not queryable and should be
    preserved when modifying objects. Cloud Run API v2 does not support
    annotations with `run.googleapis.com`, `cloud.googleapis.com`,
    `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they
    will be rejected. All system annotations in v1 now have a corresponding
    field in v2 RevisionTemplate. This field follows Kubernetes annotations'
    namespacing, limits, and rules.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Unstructured key value map that can be used to organize and
    categorize objects. User-provided labels are shared with Google's billing
    system, so they can be used to filter, or break down billing charges by
    team, component, environment, state, etc. For more information, visit
    https://cloud.google.com/resource-manager/docs/creating-managing-labels or
    https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2
    does not support labels with `run.googleapis.com`, `cloud.googleapis.com`,
    `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they
    will be rejected. All system labels in v1 now have a corresponding field
    in v2 RevisionTemplate.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  containers = _messages.MessageField('GoogleCloudRunV2Container', 2, repeated=True)
  encryptionKey = _messages.StringField(3)
  encryptionKeyRevocationAction = _messages.EnumField('EncryptionKeyRevocationActionValueValuesEnum', 4)
  encryptionKeyShutdownDuration = _messages.StringField(5)
  executionEnvironment = _messages.EnumField('ExecutionEnvironmentValueValuesEnum', 6)
  gpuZonalRedundancyDisabled = _messages.BooleanField(7)
  healthCheckDisabled = _messages.BooleanField(8)
  labels = _messages.MessageField('LabelsValue', 9)
  maxInstanceRequestConcurrency = _messages.IntegerField(10, variant=_messages.Variant.INT32)
  nodeSelector = _messages.MessageField('GoogleCloudRunV2NodeSelector', 11)
  revision = _messages.StringField(12)
  scaling = _messages.MessageField('GoogleCloudRunV2RevisionScaling', 13)
  serviceAccount = _messages.StringField(14)
  serviceMesh = _messages.MessageField('GoogleCloudRunV2ServiceMesh', 15)
  sessionAffinity = _messages.BooleanField(16)
  timeout = _messages.StringField(17)
  volumes = _messages.MessageField('GoogleCloudRunV2Volume', 18, repeated=True)
  vpcAccess = _messages.MessageField('GoogleCloudRunV2VpcAccess', 19)


class GoogleCloudRunV2RunJobRequest(_messages.Message):
  r"""Request message to create a new Execution of a Job.

  Fields:
    etag: A system-generated fingerprint for this version of the resource. May
      be used to detect modification conflict during updates.
    overrides: Overrides specification for a given execution of a job. If
      provided, overrides will be applied to update the execution or task
      spec.
    validateOnly: Indicates that the request should be validated without
      actually deleting any resources.
  """

  etag = _messages.StringField(1)
  overrides = _messages.MessageField('GoogleCloudRunV2Overrides', 2)
  validateOnly = _messages.BooleanField(3)


class GoogleCloudRunV2SecretKeySelector(_messages.Message):
  r"""SecretEnvVarSource represents a source for the value of an EnvVar.

  Fields:
    secret: Required. The name of the secret in Cloud Secret Manager. Format:
      {secret_name} if the secret is in the same project.
      projects/{project}/secrets/{secret_name} if the secret is in a different
      project.
    version: The Cloud Secret Manager secret version. Can be 'latest' for the
      latest version, an integer for a specific version, or a version alias.
  """

  secret = _messages.StringField(1)
  version = _messages.StringField(2)


class GoogleCloudRunV2SecretVolumeSource(_messages.Message):
  r"""The secret's value will be presented as the content of a file whose name
  is defined in the item path. If no items are defined, the name of the file
  is the secret.

  Fields:
    defaultMode: Integer representation of mode bits to use on created files
      by default. Must be a value between 0000 and 0777 (octal), defaulting to
      0444. Directories within the path are not affected by this setting.
      Notes * Internally, a umask of 0222 will be applied to any non-zero
      value. * This is an integer representation of the mode bits. So, the
      octal integer value should look exactly as the chmod numeric notation
      with a leading zero. Some examples: for chmod 640 (u=rw,g=r), set to
      0640 (octal) or 416 (base-10). For chmod 755 (u=rwx,g=rx,o=rx), set to
      0755 (octal) or 493 (base-10). * This might be in conflict with other
      options that affect the file mode, like fsGroup, and the result can be
      other mode bits set. This might be in conflict with other options that
      affect the file mode, like fsGroup, and as a result, other mode bits
      could be set.
    items: If unspecified, the volume will expose a file whose name is the
      secret, relative to VolumeMount.mount_path. If specified, the key will
      be used as the version to fetch from Cloud Secret Manager and the path
      will be the name of the file exposed in the volume. When items are
      defined, they must specify a path and a version.
    secret: Required. The name of the secret in Cloud Secret Manager. Format:
      {secret} if the secret is in the same project.
      projects/{project}/secrets/{secret} if the secret is in a different
      project.
  """

  defaultMode = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  items = _messages.MessageField('GoogleCloudRunV2VersionToPath', 2, repeated=True)
  secret = _messages.StringField(3)


class GoogleCloudRunV2Service(_messages.Message):
  r"""Service acts as a top-level container that manages a set of
  configurations and revision templates which implement a network service.
  Service exists to provide a singular abstraction which can be access
  controlled, reasoned about, and which encapsulates software lifecycle
  decisions such as rollout policy and team resource ownership.

  Enums:
    IngressValueValuesEnum: Optional. Provides the ingress settings for this
      Service. On output, returns the currently observed ingress settings, or
      INGRESS_TRAFFIC_UNSPECIFIED if no revision is active.
    LaunchStageValueValuesEnum: Optional. The launch stage as defined by
      [Google Cloud Platform Launch
      Stages](https://cloud.google.com/terms/launch-stages). Cloud Run
      supports `ALPHA`, `BETA`, and `GA`. If no value is specified, GA is
      assumed. Set the launch stage to a preview stage on input to allow use
      of preview features in that stage. On read (or output), describes
      whether the resource uses preview features. For example, if ALPHA is
      provided as input, but only BETA and GA-level features are used, this
      field will be BETA on output.

  Messages:
    AnnotationsValue: Optional. Unstructured key value map that may be set by
      external tools to store and arbitrary metadata. They are not queryable
      and should be preserved when modifying objects. Cloud Run API v2 does
      not support annotations with `run.googleapis.com`,
      `cloud.googleapis.com`, `serving.knative.dev`, or
      `autoscaling.knative.dev` namespaces, and they will be rejected in new
      resources. All system annotations in v1 now have a corresponding field
      in v2 Service. This field follows Kubernetes annotations' namespacing,
      limits, and rules.
    LabelsValue: Optional. Unstructured key value map that can be used to
      organize and categorize objects. User-provided labels are shared with
      Google's billing system, so they can be used to filter, or break down
      billing charges by team, component, environment, state, etc. For more
      information, visit https://cloud.google.com/resource-
      manager/docs/creating-managing-labels or
      https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2
      does not support labels with `run.googleapis.com`,
      `cloud.googleapis.com`, `serving.knative.dev`, or
      `autoscaling.knative.dev` namespaces, and they will be rejected. All
      system labels in v1 now have a corresponding field in v2 Service.

  Fields:
    annotations: Optional. Unstructured key value map that may be set by
      external tools to store and arbitrary metadata. They are not queryable
      and should be preserved when modifying objects. Cloud Run API v2 does
      not support annotations with `run.googleapis.com`,
      `cloud.googleapis.com`, `serving.knative.dev`, or
      `autoscaling.knative.dev` namespaces, and they will be rejected in new
      resources. All system annotations in v1 now have a corresponding field
      in v2 Service. This field follows Kubernetes annotations' namespacing,
      limits, and rules.
    binaryAuthorization: Optional. Settings for the Binary Authorization
      feature.
    buildConfig: Optional. Configuration for building a Cloud Run function.
    client: Arbitrary identifier for the API client.
    clientVersion: Arbitrary version identifier for the API client.
    conditions: Output only. The Conditions of all other associated sub-
      resources. They contain additional diagnostics information in case the
      Service does not reach its Serving state. See comments in `reconciling`
      for additional information on reconciliation process in Cloud Run.
    createTime: Output only. The creation time.
    creator: Output only. Email address of the authenticated creator.
    customAudiences: One or more custom audiences that you want this service
      to support. Specify each custom audience as the full URL in a string.
      The custom audiences are encoded in the token and used to authenticate
      requests. For more information, see
      https://cloud.google.com/run/docs/configuring/custom-audiences.
    defaultUriDisabled: Optional. Disables public resolution of the default
      URI of this service.
    deleteTime: Output only. The deletion time. It is only populated as a
      response to a Delete request.
    description: User-provided description of the Service. This field
      currently has a 512-character limit.
    etag: Optional. A system-generated fingerprint for this version of the
      resource. May be used to detect modification conflict during updates.
    expireTime: Output only. For a deleted resource, the time after which it
      will be permanently deleted.
    generation: Output only. A number that monotonically increases every time
      the user modifies the desired state. Please note that unlike v1, this is
      an int64 value. As with most Google APIs, its JSON representation will
      be a `string` instead of an `integer`.
    iapEnabled: Optional. IAP settings on the Service.
    ingress: Optional. Provides the ingress settings for this Service. On
      output, returns the currently observed ingress settings, or
      INGRESS_TRAFFIC_UNSPECIFIED if no revision is active.
    invokerIamDisabled: Optional. Disables IAM permission check for
      run.routes.invoke for callers of this service. For more information,
      visit https://cloud.google.com/run/docs/securing/managing-
      access#invoker_check.
    labels: Optional. Unstructured key value map that can be used to organize
      and categorize objects. User-provided labels are shared with Google's
      billing system, so they can be used to filter, or break down billing
      charges by team, component, environment, state, etc. For more
      information, visit https://cloud.google.com/resource-
      manager/docs/creating-managing-labels or
      https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2
      does not support labels with `run.googleapis.com`,
      `cloud.googleapis.com`, `serving.knative.dev`, or
      `autoscaling.knative.dev` namespaces, and they will be rejected. All
      system labels in v1 now have a corresponding field in v2 Service.
    lastModifier: Output only. Email address of the last authenticated
      modifier.
    latestCreatedRevision: Output only. Name of the last created revision. See
      comments in `reconciling` for additional information on reconciliation
      process in Cloud Run.
    latestReadyRevision: Output only. Name of the latest revision that is
      serving traffic. See comments in `reconciling` for additional
      information on reconciliation process in Cloud Run.
    launchStage: Optional. The launch stage as defined by [Google Cloud
      Platform Launch Stages](https://cloud.google.com/terms/launch-stages).
      Cloud Run supports `ALPHA`, `BETA`, and `GA`. If no value is specified,
      GA is assumed. Set the launch stage to a preview stage on input to allow
      use of preview features in that stage. On read (or output), describes
      whether the resource uses preview features. For example, if ALPHA is
      provided as input, but only BETA and GA-level features are used, this
      field will be BETA on output.
    multiRegionSettings: Optional. Settings for multi-region deployment.
    name: The fully qualified name of this Service. In CreateServiceRequest,
      this field is ignored, and instead composed from
      CreateServiceRequest.parent and CreateServiceRequest.service_id. Format:
      projects/{project}/locations/{location}/services/{service_id}
    observedGeneration: Output only. The generation of this Service currently
      serving traffic. See comments in `reconciling` for additional
      information on reconciliation process in Cloud Run. Please note that
      unlike v1, this is an int64 value. As with most Google APIs, its JSON
      representation will be a `string` instead of an `integer`.
    reconciling: Output only. Returns true if the Service is currently being
      acted upon by the system to bring it into the desired state. When a new
      Service is created, or an existing one is updated, Cloud Run will
      asynchronously perform all necessary steps to bring the Service to the
      desired serving state. This process is called reconciliation. While
      reconciliation is in process, `observed_generation`,
      `latest_ready_revision`, `traffic_statuses`, and `uri` will have
      transient values that might mismatch the intended state: Once
      reconciliation is over (and this field is false), there are two possible
      outcomes: reconciliation succeeded and the serving state matches the
      Service, or there was an error, and reconciliation failed. This state
      can be found in `terminal_condition.state`. If reconciliation succeeded,
      the following fields will match: `traffic` and `traffic_statuses`,
      `observed_generation` and `generation`, `latest_ready_revision` and
      `latest_created_revision`. If reconciliation failed, `traffic_statuses`,
      `observed_generation`, and `latest_ready_revision` will have the state
      of the last serving revision, or empty for newly created Services.
      Additional information on the failure can be found in
      `terminal_condition` and `conditions`.
    satisfiesPzs: Output only. Reserved for future use.
    scaling: Optional. Specifies service-level scaling settings
    template: Required. The template used to create revisions for this
      Service.
    terminalCondition: Output only. The Condition of this Service, containing
      its readiness status, and detailed error information in case it did not
      reach a serving state. See comments in `reconciling` for additional
      information on reconciliation process in Cloud Run.
    threatDetectionEnabled: Output only. True if Cloud Run Threat Detection
      monitoring is enabled for the parent project of this Service.
    traffic: Optional. Specifies how to distribute traffic over a collection
      of Revisions belonging to the Service. If traffic is empty or not
      provided, defaults to 100% traffic to the latest `Ready` Revision.
    trafficStatuses: Output only. Detailed status information for
      corresponding traffic targets. See comments in `reconciling` for
      additional information on reconciliation process in Cloud Run.
    uid: Output only. Server assigned unique identifier for the trigger. The
      value is a UUID4 string and guaranteed to remain unchanged until the
      resource is deleted.
    updateTime: Output only. The last-modified time.
    uri: Output only. The main URI in which this Service is serving traffic.
    urls: Output only. All URLs serving traffic for this Service.
  """

  class IngressValueValuesEnum(_messages.Enum):
    r"""Optional. Provides the ingress settings for this Service. On output,
    returns the currently observed ingress settings, or
    INGRESS_TRAFFIC_UNSPECIFIED if no revision is active.

    Values:
      INGRESS_TRAFFIC_UNSPECIFIED: Unspecified
      INGRESS_TRAFFIC_ALL: All inbound traffic is allowed.
      INGRESS_TRAFFIC_INTERNAL_ONLY: Only internal traffic is allowed.
      INGRESS_TRAFFIC_INTERNAL_LOAD_BALANCER: Both internal and Google Cloud
        Load Balancer traffic is allowed.
      INGRESS_TRAFFIC_NONE: No ingress traffic is allowed.
    """
    INGRESS_TRAFFIC_UNSPECIFIED = 0
    INGRESS_TRAFFIC_ALL = 1
    INGRESS_TRAFFIC_INTERNAL_ONLY = 2
    INGRESS_TRAFFIC_INTERNAL_LOAD_BALANCER = 3
    INGRESS_TRAFFIC_NONE = 4

  class LaunchStageValueValuesEnum(_messages.Enum):
    r"""Optional. The launch stage as defined by [Google Cloud Platform Launch
    Stages](https://cloud.google.com/terms/launch-stages). Cloud Run supports
    `ALPHA`, `BETA`, and `GA`. If no value is specified, GA is assumed. Set
    the launch stage to a preview stage on input to allow use of preview
    features in that stage. On read (or output), describes whether the
    resource uses preview features. For example, if ALPHA is provided as
    input, but only BETA and GA-level features are used, this field will be
    BETA on output.

    Values:
      LAUNCH_STAGE_UNSPECIFIED: Do not use this default value.
      UNIMPLEMENTED: The feature is not yet implemented. Users can not use it.
      PRELAUNCH: Prelaunch features are hidden from users and are only visible
        internally.
      EARLY_ACCESS: Early Access features are limited to a closed group of
        testers. To use these features, you must sign up in advance and sign a
        Trusted Tester agreement (which includes confidentiality provisions).
        These features may be unstable, changed in backward-incompatible ways,
        and are not guaranteed to be released.
      ALPHA: Alpha is a limited availability test for releases before they are
        cleared for widespread use. By Alpha, all significant design issues
        are resolved and we are in the process of verifying functionality.
        Alpha customers need to apply for access, agree to applicable terms,
        and have their projects allowlisted. Alpha releases don't have to be
        feature complete, no SLAs are provided, and there are no technical
        support obligations, but they will be far enough along that customers
        can actually use them in test environments or for limited-use tests --
        just like they would in normal production cases.
      BETA: Beta is the point at which we are ready to open a release for any
        customer to use. There are no SLA or technical support obligations in
        a Beta release. Products will be complete from a feature perspective,
        but may have some open outstanding issues. Beta releases are suitable
        for limited production use cases.
      GA: GA features are open to all developers and are considered stable and
        fully qualified for production use.
      DEPRECATED: Deprecated features are scheduled to be shut down and
        removed. For more information, see the "Deprecation Policy" section of
        our [Terms of Service](https://cloud.google.com/terms/) and the
        [Google Cloud Platform Subject to the Deprecation
        Policy](https://cloud.google.com/terms/deprecation) documentation.
    """
    LAUNCH_STAGE_UNSPECIFIED = 0
    UNIMPLEMENTED = 1
    PRELAUNCH = 2
    EARLY_ACCESS = 3
    ALPHA = 4
    BETA = 5
    GA = 6
    DEPRECATED = 7

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Unstructured key value map that may be set by external tools
    to store and arbitrary metadata. They are not queryable and should be
    preserved when modifying objects. Cloud Run API v2 does not support
    annotations with `run.googleapis.com`, `cloud.googleapis.com`,
    `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they
    will be rejected in new resources. All system annotations in v1 now have a
    corresponding field in v2 Service. This field follows Kubernetes
    annotations' namespacing, limits, and rules.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Unstructured key value map that can be used to organize and
    categorize objects. User-provided labels are shared with Google's billing
    system, so they can be used to filter, or break down billing charges by
    team, component, environment, state, etc. For more information, visit
    https://cloud.google.com/resource-manager/docs/creating-managing-labels or
    https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2
    does not support labels with `run.googleapis.com`, `cloud.googleapis.com`,
    `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they
    will be rejected. All system labels in v1 now have a corresponding field
    in v2 Service.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  binaryAuthorization = _messages.MessageField('GoogleCloudRunV2BinaryAuthorization', 2)
  buildConfig = _messages.MessageField('GoogleCloudRunV2BuildConfig', 3)
  client = _messages.StringField(4)
  clientVersion = _messages.StringField(5)
  conditions = _messages.MessageField('GoogleCloudRunV2Condition', 6, repeated=True)
  createTime = _messages.StringField(7)
  creator = _messages.StringField(8)
  customAudiences = _messages.StringField(9, repeated=True)
  defaultUriDisabled = _messages.BooleanField(10)
  deleteTime = _messages.StringField(11)
  description = _messages.StringField(12)
  etag = _messages.StringField(13)
  expireTime = _messages.StringField(14)
  generation = _messages.IntegerField(15)
  iapEnabled = _messages.BooleanField(16)
  ingress = _messages.EnumField('IngressValueValuesEnum', 17)
  invokerIamDisabled = _messages.BooleanField(18)
  labels = _messages.MessageField('LabelsValue', 19)
  lastModifier = _messages.StringField(20)
  latestCreatedRevision = _messages.StringField(21)
  latestReadyRevision = _messages.StringField(22)
  launchStage = _messages.EnumField('LaunchStageValueValuesEnum', 23)
  multiRegionSettings = _messages.MessageField('GoogleCloudRunV2MultiRegionSettings', 24)
  name = _messages.StringField(25)
  observedGeneration = _messages.IntegerField(26)
  reconciling = _messages.BooleanField(27)
  satisfiesPzs = _messages.BooleanField(28)
  scaling = _messages.MessageField('GoogleCloudRunV2ServiceScaling', 29)
  template = _messages.MessageField('GoogleCloudRunV2RevisionTemplate', 30)
  terminalCondition = _messages.MessageField('GoogleCloudRunV2Condition', 31)
  threatDetectionEnabled = _messages.BooleanField(32)
  traffic = _messages.MessageField('GoogleCloudRunV2TrafficTarget', 33, repeated=True)
  trafficStatuses = _messages.MessageField('GoogleCloudRunV2TrafficTargetStatus', 34, repeated=True)
  uid = _messages.StringField(35)
  updateTime = _messages.StringField(36)
  uri = _messages.StringField(37)
  urls = _messages.StringField(38, repeated=True)


class GoogleCloudRunV2ServiceMesh(_messages.Message):
  r"""Settings for Cloud Service Mesh. For more information see
  https://cloud.google.com/service-mesh/docs/overview.

  Fields:
    mesh: The Mesh resource name. Format:
      `projects/{project}/locations/global/meshes/{mesh}`, where `{project}`
      can be project id or number.
  """

  mesh = _messages.StringField(1)


class GoogleCloudRunV2ServiceScaling(_messages.Message):
  r"""Scaling settings applied at the service level rather than at the
  revision level.

  Enums:
    ScalingModeValueValuesEnum: Optional. The scaling mode for the service.

  Fields:
    manualInstanceCount: Optional. total instance count for the service in
      manual scaling mode. This number of instances is divided among all
      revisions with specified traffic based on the percent of traffic they
      are receiving.
    maxInstanceCount: Optional. total max instances for the service. This
      number of instances is divided among all revisions with specified
      traffic based on the percent of traffic they are receiving.
    minInstanceCount: Optional. total min instances for the service. This
      number of instances is divided among all revisions with specified
      traffic based on the percent of traffic they are receiving.
    scalingMode: Optional. The scaling mode for the service.
  """

  class ScalingModeValueValuesEnum(_messages.Enum):
    r"""Optional. The scaling mode for the service.

    Values:
      SCALING_MODE_UNSPECIFIED: Unspecified.
      AUTOMATIC: Scale based on traffic between min and max instances.
      MANUAL: Scale to exactly min instances and ignore max instances.
    """
    SCALING_MODE_UNSPECIFIED = 0
    AUTOMATIC = 1
    MANUAL = 2

  manualInstanceCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  maxInstanceCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  minInstanceCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  scalingMode = _messages.EnumField('ScalingModeValueValuesEnum', 4)


class GoogleCloudRunV2StorageSource(_messages.Message):
  r"""Location of the source in an archive file in Google Cloud Storage.

  Fields:
    bucket: Required. Google Cloud Storage bucket containing the source (see
      [Bucket Name Requirements](https://cloud.google.com/storage/docs/bucket-
      naming#requirements)).
    generation: Optional. Google Cloud Storage generation for the object. If
      the generation is omitted, the latest generation will be used.
    object: Required. Google Cloud Storage object containing the source. This
      object must be a gzipped archive file (`.tar.gz`) containing source to
      build.
  """

  bucket = _messages.StringField(1)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3)


class GoogleCloudRunV2SubmitBuildRequest(_messages.Message):
  r"""Request message for submitting a Build.

  Fields:
    buildpackBuild: Build the source using Buildpacks.
    dockerBuild: Build the source using Docker. This means the source has a
      Dockerfile.
    imageUri: Required. Artifact Registry URI to store the built image.
    machineType: Optional. The machine type from default pool to use for the
      build. If left blank, cloudbuild will use a sensible default. Currently
      only E2_HIGHCPU_8 is supported. If worker_pool is set, this field will
      be ignored.
    serviceAccount: Optional. The service account to use for the build. If not
      set, the default Cloud Build service account for the project will be
      used.
    storageSource: Required. Source for the build.
    tags: Optional. Additional tags to annotate the build.
    workerPool: Optional. Name of the Cloud Build Custom Worker Pool that
      should be used to build the function. The format of this field is
      `projects/{project}/locations/{region}/workerPools/{workerPool}` where
      `{project}` and `{region}` are the project id and region respectively
      where the worker pool is defined and `{workerPool}` is the short name of
      the worker pool.
  """

  buildpackBuild = _messages.MessageField('GoogleCloudRunV2BuildpacksBuild', 1)
  dockerBuild = _messages.MessageField('GoogleCloudRunV2DockerBuild', 2)
  imageUri = _messages.StringField(3)
  machineType = _messages.StringField(4)
  serviceAccount = _messages.StringField(5)
  storageSource = _messages.MessageField('GoogleCloudRunV2StorageSource', 6)
  tags = _messages.StringField(7, repeated=True)
  workerPool = _messages.StringField(8)


class GoogleCloudRunV2SubmitBuildResponse(_messages.Message):
  r"""Response message for submitting a Build.

  Fields:
    baseImageUri: URI of the base builder image in Artifact Registry being
      used in the build. Used to opt into automatic base image updates.
    baseImageWarning: Warning message for the base image.
    buildOperation: Cloud Build operation to be polled via CloudBuild API.
  """

  baseImageUri = _messages.StringField(1)
  baseImageWarning = _messages.StringField(2)
  buildOperation = _messages.MessageField('GoogleLongrunningOperation', 3)


class GoogleCloudRunV2TCPSocketAction(_messages.Message):
  r"""TCPSocketAction describes an action based on opening a socket

  Fields:
    port: Optional. Port number to access on the container. Must be in the
      range 1 to 65535. If not specified, defaults to the exposed port of the
      container, which is the value of container.ports[0].containerPort.
  """

  port = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class GoogleCloudRunV2Task(_messages.Message):
  r"""Task represents a single run of a container to completion.

  Enums:
    ExecutionEnvironmentValueValuesEnum: The execution environment being used
      to host this Task.

  Messages:
    AnnotationsValue: Output only. Unstructured key value map that may be set
      by external tools to store and arbitrary metadata. They are not
      queryable and should be preserved when modifying objects.
    LabelsValue: Output only. Unstructured key value map that can be used to
      organize and categorize objects. User-provided labels are shared with
      Google's billing system, so they can be used to filter, or break down
      billing charges by team, component, environment, state, etc. For more
      information, visit https://cloud.google.com/resource-
      manager/docs/creating-managing-labels or
      https://cloud.google.com/run/docs/configuring/labels

  Fields:
    annotations: Output only. Unstructured key value map that may be set by
      external tools to store and arbitrary metadata. They are not queryable
      and should be preserved when modifying objects.
    completionTime: Output only. Represents time when the Task was completed.
      It is not guaranteed to be set in happens-before order across separate
      operations.
    conditions: Output only. The Condition of this Task, containing its
      readiness status, and detailed error information in case it did not
      reach the desired state.
    containers: Holds the single container that defines the unit of execution
      for this task.
    createTime: Output only. Represents time when the task was created by the
      system. It is not guaranteed to be set in happens-before order across
      separate operations.
    deleteTime: Output only. For a deleted resource, the deletion time. It is
      only populated as a response to a Delete request.
    encryptionKey: Output only. A reference to a customer managed encryption
      key (CMEK) to use to encrypt this container image. For more information,
      go to https://cloud.google.com/run/docs/securing/using-cmek
    etag: Output only. A system-generated fingerprint for this version of the
      resource. May be used to detect modification conflict during updates.
    execution: Output only. The name of the parent Execution.
    executionEnvironment: The execution environment being used to host this
      Task.
    expireTime: Output only. For a deleted resource, the time after which it
      will be permamently deleted. It is only populated as a response to a
      Delete request.
    generation: Output only. A number that monotonically increases every time
      the user modifies the desired state.
    gpuZonalRedundancyDisabled: Optional. Output only. True if GPU zonal
      redundancy is disabled on this task.
    index: Output only. Index of the Task, unique per execution, and beginning
      at 0.
    job: Output only. The name of the parent Job.
    labels: Output only. Unstructured key value map that can be used to
      organize and categorize objects. User-provided labels are shared with
      Google's billing system, so they can be used to filter, or break down
      billing charges by team, component, environment, state, etc. For more
      information, visit https://cloud.google.com/resource-
      manager/docs/creating-managing-labels or
      https://cloud.google.com/run/docs/configuring/labels
    lastAttemptResult: Output only. Result of the last attempt of this Task.
    logUri: Output only. URI where logs for this execution can be found in
      Cloud Console.
    maxRetries: Number of retries allowed per Task, before marking this Task
      failed.
    name: Output only. The unique name of this Task.
    nodeSelector: Output only. The node selector for the task.
    observedGeneration: Output only. The generation of this Task. See comments
      in `Job.reconciling` for additional information on reconciliation
      process in Cloud Run.
    reconciling: Output only. Indicates whether the resource's reconciliation
      is still in progress. See comments in `Job.reconciling` for additional
      information on reconciliation process in Cloud Run.
    retried: Output only. The number of times this Task was retried. Tasks are
      retried when they fail up to the maxRetries limit.
    satisfiesPzs: Output only. Reserved for future use.
    scheduledTime: Output only. Represents time when the task was scheduled to
      run by the system. It is not guaranteed to be set in happens-before
      order across separate operations.
    serviceAccount: Email address of the IAM service account associated with
      the Task of a Job. The service account represents the identity of the
      running task, and determines what permissions the task has. If not
      provided, the task will use the project's default service account.
    startTime: Output only. Represents time when the task started to run. It
      is not guaranteed to be set in happens-before order across separate
      operations.
    timeout: Max allowed time duration the Task may be active before the
      system will actively try to mark it failed and kill associated
      containers. This applies per attempt of a task, meaning each retry can
      run for the full timeout.
    uid: Output only. Server assigned unique identifier for the Task. The
      value is a UUID4 string and guaranteed to remain unchanged until the
      resource is deleted.
    updateTime: Output only. The last-modified time.
    volumes: A list of Volumes to make available to containers.
    vpcAccess: Output only. VPC Access configuration to use for this Task. For
      more information, visit
      https://cloud.google.com/run/docs/configuring/connecting-vpc.
  """

  class ExecutionEnvironmentValueValuesEnum(_messages.Enum):
    r"""The execution environment being used to host this Task.

    Values:
      EXECUTION_ENVIRONMENT_UNSPECIFIED: Unspecified
      EXECUTION_ENVIRONMENT_GEN1: Uses the First Generation environment.
      EXECUTION_ENVIRONMENT_GEN2: Uses Second Generation environment.
    """
    EXECUTION_ENVIRONMENT_UNSPECIFIED = 0
    EXECUTION_ENVIRONMENT_GEN1 = 1
    EXECUTION_ENVIRONMENT_GEN2 = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Output only. Unstructured key value map that may be set by external
    tools to store and arbitrary metadata. They are not queryable and should
    be preserved when modifying objects.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Output only. Unstructured key value map that can be used to organize
    and categorize objects. User-provided labels are shared with Google's
    billing system, so they can be used to filter, or break down billing
    charges by team, component, environment, state, etc. For more information,
    visit https://cloud.google.com/resource-manager/docs/creating-managing-
    labels or https://cloud.google.com/run/docs/configuring/labels

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  completionTime = _messages.StringField(2)
  conditions = _messages.MessageField('GoogleCloudRunV2Condition', 3, repeated=True)
  containers = _messages.MessageField('GoogleCloudRunV2Container', 4, repeated=True)
  createTime = _messages.StringField(5)
  deleteTime = _messages.StringField(6)
  encryptionKey = _messages.StringField(7)
  etag = _messages.StringField(8)
  execution = _messages.StringField(9)
  executionEnvironment = _messages.EnumField('ExecutionEnvironmentValueValuesEnum', 10)
  expireTime = _messages.StringField(11)
  generation = _messages.IntegerField(12)
  gpuZonalRedundancyDisabled = _messages.BooleanField(13)
  index = _messages.IntegerField(14, variant=_messages.Variant.INT32)
  job = _messages.StringField(15)
  labels = _messages.MessageField('LabelsValue', 16)
  lastAttemptResult = _messages.MessageField('GoogleCloudRunV2TaskAttemptResult', 17)
  logUri = _messages.StringField(18)
  maxRetries = _messages.IntegerField(19, variant=_messages.Variant.INT32)
  name = _messages.StringField(20)
  nodeSelector = _messages.MessageField('GoogleCloudRunV2NodeSelector', 21)
  observedGeneration = _messages.IntegerField(22)
  reconciling = _messages.BooleanField(23)
  retried = _messages.IntegerField(24, variant=_messages.Variant.INT32)
  satisfiesPzs = _messages.BooleanField(25)
  scheduledTime = _messages.StringField(26)
  serviceAccount = _messages.StringField(27)
  startTime = _messages.StringField(28)
  timeout = _messages.StringField(29)
  uid = _messages.StringField(30)
  updateTime = _messages.StringField(31)
  volumes = _messages.MessageField('GoogleCloudRunV2Volume', 32, repeated=True)
  vpcAccess = _messages.MessageField('GoogleCloudRunV2VpcAccess', 33)


class GoogleCloudRunV2TaskAttemptResult(_messages.Message):
  r"""Result of a task attempt.

  Fields:
    exitCode: Output only. The exit code of this attempt. This may be unset if
      the container was unable to exit cleanly with a code due to some other
      failure. See status field for possible failure details. At most one of
      exit_code or term_signal will be set.
    status: Output only. The status of this attempt. If the status code is OK,
      then the attempt succeeded.
    termSignal: Output only. Termination signal of the container. This is set
      to non-zero if the container is terminated by the system. At most one of
      exit_code or term_signal will be set.
  """

  exitCode = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  status = _messages.MessageField('GoogleRpcStatus', 2)
  termSignal = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class GoogleCloudRunV2TaskTemplate(_messages.Message):
  r"""TaskTemplate describes the data a task should have when created from a
  template.

  Enums:
    ExecutionEnvironmentValueValuesEnum: Optional. The execution environment
      being used to host this Task.

  Fields:
    containers: Holds the single container that defines the unit of execution
      for this task.
    encryptionKey: A reference to a customer managed encryption key (CMEK) to
      use to encrypt this container image. For more information, go to
      https://cloud.google.com/run/docs/securing/using-cmek
    executionEnvironment: Optional. The execution environment being used to
      host this Task.
    gpuZonalRedundancyDisabled: Optional. True if GPU zonal redundancy is
      disabled on this task template.
    maxRetries: Number of retries allowed per Task, before marking this Task
      failed. Defaults to 3.
    nodeSelector: Optional. The node selector for the task template.
    serviceAccount: Optional. Email address of the IAM service account
      associated with the Task of a Job. The service account represents the
      identity of the running task, and determines what permissions the task
      has. If not provided, the task will use the project's default service
      account.
    timeout: Optional. Max allowed time duration the Task may be active before
      the system will actively try to mark it failed and kill associated
      containers. This applies per attempt of a task, meaning each retry can
      run for the full timeout. Defaults to 600 seconds.
    volumes: Optional. A list of Volumes to make available to containers.
    vpcAccess: Optional. VPC Access configuration to use for this Task. For
      more information, visit
      https://cloud.google.com/run/docs/configuring/connecting-vpc.
  """

  class ExecutionEnvironmentValueValuesEnum(_messages.Enum):
    r"""Optional. The execution environment being used to host this Task.

    Values:
      EXECUTION_ENVIRONMENT_UNSPECIFIED: Unspecified
      EXECUTION_ENVIRONMENT_GEN1: Uses the First Generation environment.
      EXECUTION_ENVIRONMENT_GEN2: Uses Second Generation environment.
    """
    EXECUTION_ENVIRONMENT_UNSPECIFIED = 0
    EXECUTION_ENVIRONMENT_GEN1 = 1
    EXECUTION_ENVIRONMENT_GEN2 = 2

  containers = _messages.MessageField('GoogleCloudRunV2Container', 1, repeated=True)
  encryptionKey = _messages.StringField(2)
  executionEnvironment = _messages.EnumField('ExecutionEnvironmentValueValuesEnum', 3)
  gpuZonalRedundancyDisabled = _messages.BooleanField(4)
  maxRetries = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  nodeSelector = _messages.MessageField('GoogleCloudRunV2NodeSelector', 6)
  serviceAccount = _messages.StringField(7)
  timeout = _messages.StringField(8)
  volumes = _messages.MessageField('GoogleCloudRunV2Volume', 9, repeated=True)
  vpcAccess = _messages.MessageField('GoogleCloudRunV2VpcAccess', 10)


class GoogleCloudRunV2TrafficTarget(_messages.Message):
  r"""Holds a single traffic routing entry for the Service. Allocations can be
  done to a specific Revision name, or pointing to the latest Ready Revision.

  Enums:
    TypeValueValuesEnum: The allocation type for this traffic target.

  Fields:
    percent: Specifies percent of the traffic to this Revision. This defaults
      to zero if unspecified.
    revision: Revision to which to send this portion of traffic, if traffic
      allocation is by revision.
    tag: Indicates a string to be part of the URI to exclusively reference
      this target.
    type: The allocation type for this traffic target.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The allocation type for this traffic target.

    Values:
      TRAFFIC_TARGET_ALLOCATION_TYPE_UNSPECIFIED: Unspecified instance
        allocation type.
      TRAFFIC_TARGET_ALLOCATION_TYPE_LATEST: Allocates instances to the
        Service's latest ready Revision.
      TRAFFIC_TARGET_ALLOCATION_TYPE_REVISION: Allocates instances to a
        Revision by name.
    """
    TRAFFIC_TARGET_ALLOCATION_TYPE_UNSPECIFIED = 0
    TRAFFIC_TARGET_ALLOCATION_TYPE_LATEST = 1
    TRAFFIC_TARGET_ALLOCATION_TYPE_REVISION = 2

  percent = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  revision = _messages.StringField(2)
  tag = _messages.StringField(3)
  type = _messages.EnumField('TypeValueValuesEnum', 4)


class GoogleCloudRunV2TrafficTargetStatus(_messages.Message):
  r"""Represents the observed state of a single `TrafficTarget` entry.

  Enums:
    TypeValueValuesEnum: The allocation type for this traffic target.

  Fields:
    percent: Specifies percent of the traffic to this Revision.
    revision: Revision to which this traffic is sent.
    tag: Indicates the string used in the URI to exclusively reference this
      target.
    type: The allocation type for this traffic target.
    uri: Displays the target URI.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The allocation type for this traffic target.

    Values:
      TRAFFIC_TARGET_ALLOCATION_TYPE_UNSPECIFIED: Unspecified instance
        allocation type.
      TRAFFIC_TARGET_ALLOCATION_TYPE_LATEST: Allocates instances to the
        Service's latest ready Revision.
      TRAFFIC_TARGET_ALLOCATION_TYPE_REVISION: Allocates instances to a
        Revision by name.
    """
    TRAFFIC_TARGET_ALLOCATION_TYPE_UNSPECIFIED = 0
    TRAFFIC_TARGET_ALLOCATION_TYPE_LATEST = 1
    TRAFFIC_TARGET_ALLOCATION_TYPE_REVISION = 2

  percent = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  revision = _messages.StringField(2)
  tag = _messages.StringField(3)
  type = _messages.EnumField('TypeValueValuesEnum', 4)
  uri = _messages.StringField(5)


class GoogleCloudRunV2VersionToPath(_messages.Message):
  r"""VersionToPath maps a specific version of a secret to a relative file to
  mount to, relative to VolumeMount's mount_path.

  Fields:
    mode: Integer octal mode bits to use on this file, must be a value between
      01 and 0777 (octal). If 0 or not set, the Volume's default mode will be
      used. Notes * Internally, a umask of 0222 will be applied to any non-
      zero value. * This is an integer representation of the mode bits. So,
      the octal integer value should look exactly as the chmod numeric
      notation with a leading zero. Some examples: for chmod 640 (u=rw,g=r),
      set to 0640 (octal) or 416 (base-10). For chmod 755 (u=rwx,g=rx,o=rx),
      set to 0755 (octal) or 493 (base-10). * This might be in conflict with
      other options that affect the file mode, like fsGroup, and the result
      can be other mode bits set.
    path: Required. The relative path of the secret in the container.
    version: The Cloud Secret Manager secret version. Can be 'latest' for the
      latest value, or an integer or a secret alias for a specific version.
  """

  mode = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  path = _messages.StringField(2)
  version = _messages.StringField(3)


class GoogleCloudRunV2Volume(_messages.Message):
  r"""Volume represents a named volume in a container.

  Fields:
    cloudSqlInstance: For Cloud SQL volumes, contains the specific instances
      that should be mounted. Visit
      https://cloud.google.com/sql/docs/mysql/connect-run for more information
      on how to connect Cloud SQL and Cloud Run.
    emptyDir: Ephemeral storage used as a shared volume.
    gcs: Persistent storage backed by a Google Cloud Storage bucket.
    name: Required. Volume's name.
    nfs: For NFS Voumes, contains the path to the nfs Volume
    secret: Secret represents a secret that should populate this volume.
  """

  cloudSqlInstance = _messages.MessageField('GoogleCloudRunV2CloudSqlInstance', 1)
  emptyDir = _messages.MessageField('GoogleCloudRunV2EmptyDirVolumeSource', 2)
  gcs = _messages.MessageField('GoogleCloudRunV2GCSVolumeSource', 3)
  name = _messages.StringField(4)
  nfs = _messages.MessageField('GoogleCloudRunV2NFSVolumeSource', 5)
  secret = _messages.MessageField('GoogleCloudRunV2SecretVolumeSource', 6)


class GoogleCloudRunV2VolumeMount(_messages.Message):
  r"""VolumeMount describes a mounting of a Volume within a container.

  Fields:
    mountPath: Required. Path within the container at which the volume should
      be mounted. Must not contain ':'. For Cloud SQL volumes, it can be left
      empty, or must otherwise be `/cloudsql`. All instances defined in the
      Volume will be available as `/cloudsql/[instance]`. For more information
      on Cloud SQL volumes, visit
      https://cloud.google.com/sql/docs/mysql/connect-run
    name: Required. This must match the Name of a Volume.
  """

  mountPath = _messages.StringField(1)
  name = _messages.StringField(2)


class GoogleCloudRunV2VpcAccess(_messages.Message):
  r"""VPC Access settings. For more information on sending traffic to a VPC
  network, visit https://cloud.google.com/run/docs/configuring/connecting-vpc.

  Enums:
    EgressValueValuesEnum: Optional. Traffic VPC egress settings. If not
      provided, it defaults to PRIVATE_RANGES_ONLY.

  Fields:
    connector: VPC Access connector name. Format:
      `projects/{project}/locations/{location}/connectors/{connector}`, where
      `{project}` can be project id or number. For more information on sending
      traffic to a VPC network via a connector, visit
      https://cloud.google.com/run/docs/configuring/vpc-connectors.
    egress: Optional. Traffic VPC egress settings. If not provided, it
      defaults to PRIVATE_RANGES_ONLY.
    networkInterfaces: Optional. Direct VPC egress settings. Currently only
      single network interface is supported.
  """

  class EgressValueValuesEnum(_messages.Enum):
    r"""Optional. Traffic VPC egress settings. If not provided, it defaults to
    PRIVATE_RANGES_ONLY.

    Values:
      VPC_EGRESS_UNSPECIFIED: Unspecified
      ALL_TRAFFIC: All outbound traffic is routed through the VPC connector.
      PRIVATE_RANGES_ONLY: Only private IP ranges are routed through the VPC
        connector.
    """
    VPC_EGRESS_UNSPECIFIED = 0
    ALL_TRAFFIC = 1
    PRIVATE_RANGES_ONLY = 2

  connector = _messages.StringField(1)
  egress = _messages.EnumField('EgressValueValuesEnum', 2)
  networkInterfaces = _messages.MessageField('GoogleCloudRunV2NetworkInterface', 3, repeated=True)


class GoogleCloudRunV2WorkerPool(_messages.Message):
  r"""WorkerPool acts as a top-level container that manages a set of
  configurations and revision templates which implement a pull-based workload.
  WorkerPool exists to provide a singular abstraction which can be access
  controlled, reasoned about, and which encapsulates software lifecycle
  decisions such as rollout policy and team resource ownership.

  Enums:
    LaunchStageValueValuesEnum: Optional. The launch stage as defined by
      [Google Cloud Platform Launch
      Stages](https://cloud.google.com/terms/launch-stages). Cloud Run
      supports `ALPHA`, `BETA`, and `GA`. If no value is specified, GA is
      assumed. Set the launch stage to a preview stage on input to allow use
      of preview features in that stage. On read (or output), describes
      whether the resource uses preview features. For example, if ALPHA is
      provided as input, but only BETA and GA-level features are used, this
      field will be BETA on output.

  Messages:
    AnnotationsValue: Optional. Unstructured key value map that may be set by
      external tools to store and arbitrary metadata. They are not queryable
      and should be preserved when modifying objects. Cloud Run API v2 does
      not support annotations with `run.googleapis.com`,
      `cloud.googleapis.com`, `serving.knative.dev`, or
      `autoscaling.knative.dev` namespaces, and they will be rejected in new
      resources. All system annotations in v1 now have a corresponding field
      in v2 WorkerPool. This field follows Kubernetes annotations'
      namespacing, limits, and rules.
    LabelsValue: Optional. Unstructured key value map that can be used to
      organize and categorize objects. User-provided labels are shared with
      Google's billing system, so they can be used to filter, or break down
      billing charges by team, component, environment, state, etc. For more
      information, visit https://cloud.google.com/resource-
      manager/docs/creating-managing-labels or
      https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2
      does not support labels with `run.googleapis.com`,
      `cloud.googleapis.com`, `serving.knative.dev`, or
      `autoscaling.knative.dev` namespaces, and they will be rejected. All
      system labels in v1 now have a corresponding field in v2 WorkerPool.

  Fields:
    annotations: Optional. Unstructured key value map that may be set by
      external tools to store and arbitrary metadata. They are not queryable
      and should be preserved when modifying objects. Cloud Run API v2 does
      not support annotations with `run.googleapis.com`,
      `cloud.googleapis.com`, `serving.knative.dev`, or
      `autoscaling.knative.dev` namespaces, and they will be rejected in new
      resources. All system annotations in v1 now have a corresponding field
      in v2 WorkerPool. This field follows Kubernetes annotations'
      namespacing, limits, and rules.
    binaryAuthorization: Optional. Settings for the Binary Authorization
      feature.
    client: Arbitrary identifier for the API client.
    clientVersion: Arbitrary version identifier for the API client.
    conditions: Output only. The Conditions of all other associated sub-
      resources. They contain additional diagnostics information in case the
      WorkerPool does not reach its Serving state. See comments in
      `reconciling` for additional information on reconciliation process in
      Cloud Run.
    createTime: Output only. The creation time.
    creator: Output only. Email address of the authenticated creator.
    customAudiences: One or more custom audiences that you want this worker
      pool to support. Specify each custom audience as the full URL in a
      string. The custom audiences are encoded in the token and used to
      authenticate requests. For more information, see
      https://cloud.google.com/run/docs/configuring/custom-audiences.
    deleteTime: Output only. The deletion time. It is only populated as a
      response to a Delete request.
    description: User-provided description of the WorkerPool. This field
      currently has a 512-character limit.
    etag: Optional. A system-generated fingerprint for this version of the
      resource. May be used to detect modification conflict during updates.
    expireTime: Output only. For a deleted resource, the time after which it
      will be permamently deleted.
    generation: Output only. A number that monotonically increases every time
      the user modifies the desired state. Please note that unlike v1, this is
      an int64 value. As with most Google APIs, its JSON representation will
      be a `string` instead of an `integer`.
    instanceSplitStatuses: Output only. Detailed status information for
      corresponding instance splits. See comments in `reconciling` for
      additional information on reconciliation process in Cloud Run.
    instanceSplits: Optional. Specifies how to distribute instances over a
      collection of Revisions belonging to the WorkerPool. If instance split
      is empty or not provided, defaults to 100% instances assigned to the
      latest `Ready` Revision.
    labels: Optional. Unstructured key value map that can be used to organize
      and categorize objects. User-provided labels are shared with Google's
      billing system, so they can be used to filter, or break down billing
      charges by team, component, environment, state, etc. For more
      information, visit https://cloud.google.com/resource-
      manager/docs/creating-managing-labels or
      https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2
      does not support labels with `run.googleapis.com`,
      `cloud.googleapis.com`, `serving.knative.dev`, or
      `autoscaling.knative.dev` namespaces, and they will be rejected. All
      system labels in v1 now have a corresponding field in v2 WorkerPool.
    lastModifier: Output only. Email address of the last authenticated
      modifier.
    latestCreatedRevision: Output only. Name of the last created revision. See
      comments in `reconciling` for additional information on reconciliation
      process in Cloud Run.
    latestReadyRevision: Output only. Name of the latest revision that is
      serving workloads. See comments in `reconciling` for additional
      information on reconciliation process in Cloud Run.
    launchStage: Optional. The launch stage as defined by [Google Cloud
      Platform Launch Stages](https://cloud.google.com/terms/launch-stages).
      Cloud Run supports `ALPHA`, `BETA`, and `GA`. If no value is specified,
      GA is assumed. Set the launch stage to a preview stage on input to allow
      use of preview features in that stage. On read (or output), describes
      whether the resource uses preview features. For example, if ALPHA is
      provided as input, but only BETA and GA-level features are used, this
      field will be BETA on output.
    name: The fully qualified name of this WorkerPool. In
      CreateWorkerPoolRequest, this field is ignored, and instead composed
      from CreateWorkerPoolRequest.parent and
      CreateWorkerPoolRequest.worker_id. Format:
      `projects/{project}/locations/{location}/workerPools/{worker_id}`
    observedGeneration: Output only. The generation of this WorkerPool
      currently serving workloads. See comments in `reconciling` for
      additional information on reconciliation process in Cloud Run. Please
      note that unlike v1, this is an int64 value. As with most Google APIs,
      its JSON representation will be a `string` instead of an `integer`.
    reconciling: Output only. Returns true if the WorkerPool is currently
      being acted upon by the system to bring it into the desired state. When
      a new WorkerPool is created, or an existing one is updated, Cloud Run
      will asynchronously perform all necessary steps to bring the WorkerPool
      to the desired serving state. This process is called reconciliation.
      While reconciliation is in process, `observed_generation`,
      `latest_ready_revison`, `instance_split_statuses`, and `uri` will have
      transient values that might mismatch the intended state: Once
      reconciliation is over (and this field is false), there are two possible
      outcomes: reconciliation succeeded and the serving state matches the
      WorkerPool, or there was an error, and reconciliation failed. This state
      can be found in `terminal_condition.state`. If reconciliation succeeded,
      the following fields will match: `instance_splits` and
      `instance_split_statuses`, `observed_generation` and `generation`,
      `latest_ready_revision` and `latest_created_revision`. If reconciliation
      failed, `instance_split_statuses`, `observed_generation`, and
      `latest_ready_revision` will have the state of the last serving
      revision, or empty for newly created WorkerPools. Additional information
      on the failure can be found in `terminal_condition` and `conditions`.
    satisfiesPzs: Output only. Reserved for future use.
    scaling: Optional. Specifies worker-pool-level scaling settings
    template: Required. The template used to create revisions for this
      WorkerPool.
    terminalCondition: Output only. The Condition of this WorkerPool,
      containing its readiness status, and detailed error information in case
      it did not reach a serving state. See comments in `reconciling` for
      additional information on reconciliation process in Cloud Run.
    uid: Output only. Server assigned unique identifier for the trigger. The
      value is a UUID4 string and guaranteed to remain unchanged until the
      resource is deleted.
    updateTime: Output only. The last-modified time.
  """

  class LaunchStageValueValuesEnum(_messages.Enum):
    r"""Optional. The launch stage as defined by [Google Cloud Platform Launch
    Stages](https://cloud.google.com/terms/launch-stages). Cloud Run supports
    `ALPHA`, `BETA`, and `GA`. If no value is specified, GA is assumed. Set
    the launch stage to a preview stage on input to allow use of preview
    features in that stage. On read (or output), describes whether the
    resource uses preview features. For example, if ALPHA is provided as
    input, but only BETA and GA-level features are used, this field will be
    BETA on output.

    Values:
      LAUNCH_STAGE_UNSPECIFIED: Do not use this default value.
      UNIMPLEMENTED: The feature is not yet implemented. Users can not use it.
      PRELAUNCH: Prelaunch features are hidden from users and are only visible
        internally.
      EARLY_ACCESS: Early Access features are limited to a closed group of
        testers. To use these features, you must sign up in advance and sign a
        Trusted Tester agreement (which includes confidentiality provisions).
        These features may be unstable, changed in backward-incompatible ways,
        and are not guaranteed to be released.
      ALPHA: Alpha is a limited availability test for releases before they are
        cleared for widespread use. By Alpha, all significant design issues
        are resolved and we are in the process of verifying functionality.
        Alpha customers need to apply for access, agree to applicable terms,
        and have their projects allowlisted. Alpha releases don't have to be
        feature complete, no SLAs are provided, and there are no technical
        support obligations, but they will be far enough along that customers
        can actually use them in test environments or for limited-use tests --
        just like they would in normal production cases.
      BETA: Beta is the point at which we are ready to open a release for any
        customer to use. There are no SLA or technical support obligations in
        a Beta release. Products will be complete from a feature perspective,
        but may have some open outstanding issues. Beta releases are suitable
        for limited production use cases.
      GA: GA features are open to all developers and are considered stable and
        fully qualified for production use.
      DEPRECATED: Deprecated features are scheduled to be shut down and
        removed. For more information, see the "Deprecation Policy" section of
        our [Terms of Service](https://cloud.google.com/terms/) and the
        [Google Cloud Platform Subject to the Deprecation
        Policy](https://cloud.google.com/terms/deprecation) documentation.
    """
    LAUNCH_STAGE_UNSPECIFIED = 0
    UNIMPLEMENTED = 1
    PRELAUNCH = 2
    EARLY_ACCESS = 3
    ALPHA = 4
    BETA = 5
    GA = 6
    DEPRECATED = 7

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Unstructured key value map that may be set by external tools
    to store and arbitrary metadata. They are not queryable and should be
    preserved when modifying objects. Cloud Run API v2 does not support
    annotations with `run.googleapis.com`, `cloud.googleapis.com`,
    `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they
    will be rejected in new resources. All system annotations in v1 now have a
    corresponding field in v2 WorkerPool. This field follows Kubernetes
    annotations' namespacing, limits, and rules.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Unstructured key value map that can be used to organize and
    categorize objects. User-provided labels are shared with Google's billing
    system, so they can be used to filter, or break down billing charges by
    team, component, environment, state, etc. For more information, visit
    https://cloud.google.com/resource-manager/docs/creating-managing-labels or
    https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2
    does not support labels with `run.googleapis.com`, `cloud.googleapis.com`,
    `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they
    will be rejected. All system labels in v1 now have a corresponding field
    in v2 WorkerPool.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  binaryAuthorization = _messages.MessageField('GoogleCloudRunV2BinaryAuthorization', 2)
  client = _messages.StringField(3)
  clientVersion = _messages.StringField(4)
  conditions = _messages.MessageField('GoogleCloudRunV2Condition', 5, repeated=True)
  createTime = _messages.StringField(6)
  creator = _messages.StringField(7)
  customAudiences = _messages.StringField(8, repeated=True)
  deleteTime = _messages.StringField(9)
  description = _messages.StringField(10)
  etag = _messages.StringField(11)
  expireTime = _messages.StringField(12)
  generation = _messages.IntegerField(13)
  instanceSplitStatuses = _messages.MessageField('GoogleCloudRunV2InstanceSplitStatus', 14, repeated=True)
  instanceSplits = _messages.MessageField('GoogleCloudRunV2InstanceSplit', 15, repeated=True)
  labels = _messages.MessageField('LabelsValue', 16)
  lastModifier = _messages.StringField(17)
  latestCreatedRevision = _messages.StringField(18)
  latestReadyRevision = _messages.StringField(19)
  launchStage = _messages.EnumField('LaunchStageValueValuesEnum', 20)
  name = _messages.StringField(21)
  observedGeneration = _messages.IntegerField(22)
  reconciling = _messages.BooleanField(23)
  satisfiesPzs = _messages.BooleanField(24)
  scaling = _messages.MessageField('GoogleCloudRunV2WorkerPoolScaling', 25)
  template = _messages.MessageField('GoogleCloudRunV2WorkerPoolRevisionTemplate', 26)
  terminalCondition = _messages.MessageField('GoogleCloudRunV2Condition', 27)
  uid = _messages.StringField(28)
  updateTime = _messages.StringField(29)


class GoogleCloudRunV2WorkerPoolRevisionTemplate(_messages.Message):
  r"""WorkerPoolRevisionTemplate describes the data a worker pool revision
  should have when created from a template.

  Enums:
    EncryptionKeyRevocationActionValueValuesEnum: Optional. The action to take
      if the encryption key is revoked.

  Messages:
    AnnotationsValue: Optional. Unstructured key value map that may be set by
      external tools to store and arbitrary metadata. They are not queryable
      and should be preserved when modifying objects. Cloud Run API v2 does
      not support annotations with `run.googleapis.com`,
      `cloud.googleapis.com`, `serving.knative.dev`, or
      `autoscaling.knative.dev` namespaces, and they will be rejected. All
      system annotations in v1 now have a corresponding field in v2
      WorkerPoolRevisionTemplate. This field follows Kubernetes annotations'
      namespacing, limits, and rules.
    LabelsValue: Optional. Unstructured key value map that can be used to
      organize and categorize objects. User-provided labels are shared with
      Google's billing system, so they can be used to filter, or break down
      billing charges by team, component, environment, state, etc. For more
      information, visit https://cloud.google.com/resource-
      manager/docs/creating-managing-labels or
      https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2
      does not support labels with `run.googleapis.com`,
      `cloud.googleapis.com`, `serving.knative.dev`, or
      `autoscaling.knative.dev` namespaces, and they will be rejected. All
      system labels in v1 now have a corresponding field in v2
      WorkerPoolRevisionTemplate.

  Fields:
    annotations: Optional. Unstructured key value map that may be set by
      external tools to store and arbitrary metadata. They are not queryable
      and should be preserved when modifying objects. Cloud Run API v2 does
      not support annotations with `run.googleapis.com`,
      `cloud.googleapis.com`, `serving.knative.dev`, or
      `autoscaling.knative.dev` namespaces, and they will be rejected. All
      system annotations in v1 now have a corresponding field in v2
      WorkerPoolRevisionTemplate. This field follows Kubernetes annotations'
      namespacing, limits, and rules.
    containers: Holds list of the containers that defines the unit of
      execution for this Revision.
    encryptionKey: A reference to a customer managed encryption key (CMEK) to
      use to encrypt this container image. For more information, go to
      https://cloud.google.com/run/docs/securing/using-cmek
    encryptionKeyRevocationAction: Optional. The action to take if the
      encryption key is revoked.
    encryptionKeyShutdownDuration: Optional. If
      encryption_key_revocation_action is SHUTDOWN, the duration before
      shutting down all instances. The minimum increment is 1 hour.
    gpuZonalRedundancyDisabled: Optional. True if GPU zonal redundancy is
      disabled on this worker pool.
    labels: Optional. Unstructured key value map that can be used to organize
      and categorize objects. User-provided labels are shared with Google's
      billing system, so they can be used to filter, or break down billing
      charges by team, component, environment, state, etc. For more
      information, visit https://cloud.google.com/resource-
      manager/docs/creating-managing-labels or
      https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2
      does not support labels with `run.googleapis.com`,
      `cloud.googleapis.com`, `serving.knative.dev`, or
      `autoscaling.knative.dev` namespaces, and they will be rejected. All
      system labels in v1 now have a corresponding field in v2
      WorkerPoolRevisionTemplate.
    nodeSelector: Optional. The node selector for the revision template.
    revision: Optional. The unique name for the revision. If this field is
      omitted, it will be automatically generated based on the WorkerPool
      name.
    serviceAccount: Optional. Email address of the IAM service account
      associated with the revision of the service. The service account
      represents the identity of the running revision, and determines what
      permissions the revision has. If not provided, the revision will use the
      project's default service account.
    serviceMesh: Optional. Enables service mesh connectivity.
    volumes: Optional. A list of Volumes to make available to containers.
    vpcAccess: Optional. VPC Access configuration to use for this Revision.
      For more information, visit
      https://cloud.google.com/run/docs/configuring/connecting-vpc.
  """

  class EncryptionKeyRevocationActionValueValuesEnum(_messages.Enum):
    r"""Optional. The action to take if the encryption key is revoked.

    Values:
      ENCRYPTION_KEY_REVOCATION_ACTION_UNSPECIFIED: Unspecified
      PREVENT_NEW: Prevents the creation of new instances.
      SHUTDOWN: Shuts down existing instances, and prevents creation of new
        ones.
    """
    ENCRYPTION_KEY_REVOCATION_ACTION_UNSPECIFIED = 0
    PREVENT_NEW = 1
    SHUTDOWN = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Unstructured key value map that may be set by external tools
    to store and arbitrary metadata. They are not queryable and should be
    preserved when modifying objects. Cloud Run API v2 does not support
    annotations with `run.googleapis.com`, `cloud.googleapis.com`,
    `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they
    will be rejected. All system annotations in v1 now have a corresponding
    field in v2 WorkerPoolRevisionTemplate. This field follows Kubernetes
    annotations' namespacing, limits, and rules.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Unstructured key value map that can be used to organize and
    categorize objects. User-provided labels are shared with Google's billing
    system, so they can be used to filter, or break down billing charges by
    team, component, environment, state, etc. For more information, visit
    https://cloud.google.com/resource-manager/docs/creating-managing-labels or
    https://cloud.google.com/run/docs/configuring/labels. Cloud Run API v2
    does not support labels with `run.googleapis.com`, `cloud.googleapis.com`,
    `serving.knative.dev`, or `autoscaling.knative.dev` namespaces, and they
    will be rejected. All system labels in v1 now have a corresponding field
    in v2 WorkerPoolRevisionTemplate.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  containers = _messages.MessageField('GoogleCloudRunV2Container', 2, repeated=True)
  encryptionKey = _messages.StringField(3)
  encryptionKeyRevocationAction = _messages.EnumField('EncryptionKeyRevocationActionValueValuesEnum', 4)
  encryptionKeyShutdownDuration = _messages.StringField(5)
  gpuZonalRedundancyDisabled = _messages.BooleanField(6)
  labels = _messages.MessageField('LabelsValue', 7)
  nodeSelector = _messages.MessageField('GoogleCloudRunV2NodeSelector', 8)
  revision = _messages.StringField(9)
  serviceAccount = _messages.StringField(10)
  serviceMesh = _messages.MessageField('GoogleCloudRunV2ServiceMesh', 11)
  volumes = _messages.MessageField('GoogleCloudRunV2Volume', 12, repeated=True)
  vpcAccess = _messages.MessageField('GoogleCloudRunV2VpcAccess', 13)


class GoogleCloudRunV2WorkerPoolScaling(_messages.Message):
  r"""Worker pool scaling settings.

  Enums:
    ScalingModeValueValuesEnum: Optional. The scaling mode for the worker
      pool.

  Fields:
    manualInstanceCount: Optional. The total number of instances in manual
      scaling mode.
    maxInstanceCount: Optional. The maximum count of instances distributed
      among revisions based on the specified instance split percentages.
    maxSurge: Optional. A maximum percentage of instances that will be moved
      in each step of traffic split changes. When set to a positive value, the
      server will bring up, at most, that percentage of new instances at a
      time before moving traffic to them. After moving traffic, the server
      will bring down instances of the old revision. This can reduce a spike
      of total active instances during changes from one revision to another
      but specifying how many extra instances can be brought up at a time.
    maxUnavailable: Optional. A maximum percentage of instances that may be
      unavailable during changes from one revision to another. When set to a
      positive value, the server may bring down instances before bringing up
      new instances. This can prevent a spike of total active instances during
      changes from one revision by reducing the pool of instances before
      bringing up new ones. Some requests may be slow or fail to serve during
      the transition.
    minInstanceCount: Optional. The minimum count of instances distributed
      among revisions based on the specified instance split percentages.
    scalingMode: Optional. The scaling mode for the worker pool.
  """

  class ScalingModeValueValuesEnum(_messages.Enum):
    r"""Optional. The scaling mode for the worker pool.

    Values:
      SCALING_MODE_UNSPECIFIED: Unspecified.
      AUTOMATIC: Automatically scale between min and max instances.
      MANUAL: Scale to manual instance count.
    """
    SCALING_MODE_UNSPECIFIED = 0
    AUTOMATIC = 1
    MANUAL = 2

  manualInstanceCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  maxInstanceCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  maxSurge = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  maxUnavailable = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  minInstanceCount = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  scalingMode = _messages.EnumField('ScalingModeValueValuesEnum', 6)


class GoogleDevtoolsCloudbuildV1ApprovalConfig(_messages.Message):
  r"""ApprovalConfig describes configuration for manual approval of a build.

  Fields:
    approvalRequired: Whether or not approval is needed. If this is set on a
      build, it will become pending when created, and will need to be
      explicitly approved to start.
  """

  approvalRequired = _messages.BooleanField(1)


class GoogleDevtoolsCloudbuildV1ApprovalResult(_messages.Message):
  r"""ApprovalResult describes the decision and associated metadata of a
  manual approval of a build.

  Enums:
    DecisionValueValuesEnum: Required. The decision of this manual approval.

  Fields:
    approvalTime: Output only. The time when the approval decision was made.
    approverAccount: Output only. Email of the user that called the
      ApproveBuild API to approve or reject a build at the time that the API
      was called.
    comment: Optional. An optional comment for this manual approval result.
    decision: Required. The decision of this manual approval.
    url: Optional. An optional URL tied to this manual approval result. This
      field is essentially the same as comment, except that it will be
      rendered by the UI differently. An example use case is a link to an
      external job that approved this Build.
  """

  class DecisionValueValuesEnum(_messages.Enum):
    r"""Required. The decision of this manual approval.

    Values:
      DECISION_UNSPECIFIED: Default enum type. This should not be used.
      APPROVED: Build is approved.
      REJECTED: Build is rejected.
    """
    DECISION_UNSPECIFIED = 0
    APPROVED = 1
    REJECTED = 2

  approvalTime = _messages.StringField(1)
  approverAccount = _messages.StringField(2)
  comment = _messages.StringField(3)
  decision = _messages.EnumField('DecisionValueValuesEnum', 4)
  url = _messages.StringField(5)


class GoogleDevtoolsCloudbuildV1ArtifactObjects(_messages.Message):
  r"""Files in the workspace to upload to Cloud Storage upon successful
  completion of all build steps.

  Fields:
    location: Cloud Storage bucket and optional object path, in the form
      "gs://bucket/path/to/somewhere/". (see [Bucket Name
      Requirements](https://cloud.google.com/storage/docs/bucket-
      naming#requirements)). Files in the workspace matching any path pattern
      will be uploaded to Cloud Storage with this location as a prefix.
    paths: Path globs used to match files in the build's workspace.
    timing: Output only. Stores timing information for pushing all artifact
      objects.
  """

  location = _messages.StringField(1)
  paths = _messages.StringField(2, repeated=True)
  timing = _messages.MessageField('GoogleDevtoolsCloudbuildV1TimeSpan', 3)


class GoogleDevtoolsCloudbuildV1Artifacts(_messages.Message):
  r"""Artifacts produced by a build that should be uploaded upon successful
  completion of all build steps.

  Fields:
    goModules: Optional. A list of Go modules to be uploaded to Artifact
      Registry upon successful completion of all build steps. If any objects
      fail to be pushed, the build is marked FAILURE.
    images: A list of images to be pushed upon the successful completion of
      all build steps. The images will be pushed using the builder service
      account's credentials. The digests of the pushed images will be stored
      in the Build resource's results field. If any of the images fail to be
      pushed, the build is marked FAILURE.
    mavenArtifacts: A list of Maven artifacts to be uploaded to Artifact
      Registry upon successful completion of all build steps. Artifacts in the
      workspace matching specified paths globs will be uploaded to the
      specified Artifact Registry repository using the builder service
      account's credentials. If any artifacts fail to be pushed, the build is
      marked FAILURE.
    npmPackages: A list of npm packages to be uploaded to Artifact Registry
      upon successful completion of all build steps. Npm packages in the
      specified paths will be uploaded to the specified Artifact Registry
      repository using the builder service account's credentials. If any
      packages fail to be pushed, the build is marked FAILURE.
    objects: A list of objects to be uploaded to Cloud Storage upon successful
      completion of all build steps. Files in the workspace matching specified
      paths globs will be uploaded to the specified Cloud Storage location
      using the builder service account's credentials. The location and
      generation of the uploaded objects will be stored in the Build
      resource's results field. If any objects fail to be pushed, the build is
      marked FAILURE.
    pythonPackages: A list of Python packages to be uploaded to Artifact
      Registry upon successful completion of all build steps. The build
      service account credentials will be used to perform the upload. If any
      objects fail to be pushed, the build is marked FAILURE.
  """

  goModules = _messages.MessageField('GoogleDevtoolsCloudbuildV1GoModule', 1, repeated=True)
  images = _messages.StringField(2, repeated=True)
  mavenArtifacts = _messages.MessageField('GoogleDevtoolsCloudbuildV1MavenArtifact', 3, repeated=True)
  npmPackages = _messages.MessageField('GoogleDevtoolsCloudbuildV1NpmPackage', 4, repeated=True)
  objects = _messages.MessageField('GoogleDevtoolsCloudbuildV1ArtifactObjects', 5)
  pythonPackages = _messages.MessageField('GoogleDevtoolsCloudbuildV1PythonPackage', 6, repeated=True)


class GoogleDevtoolsCloudbuildV1Build(_messages.Message):
  r"""A build resource in the Cloud Build API. At a high level, a `Build`
  describes where to find source code, how to build it (for example, the
  builder image to run on the source), and where to store the built artifacts.
  Fields can include the following variables, which will be expanded when the
  build is created: - $PROJECT_ID: the project ID of the build. -
  $PROJECT_NUMBER: the project number of the build. - $LOCATION: the
  location/region of the build. - $BUILD_ID: the autogenerated ID of the
  build. - $REPO_NAME: the source repository name specified by RepoSource. -
  $BRANCH_NAME: the branch name specified by RepoSource. - $TAG_NAME: the tag
  name specified by RepoSource. - $REVISION_ID or $COMMIT_SHA: the commit SHA
  specified by RepoSource or resolved from the specified branch or tag. -
  $SHORT_SHA: first 7 characters of $REVISION_ID or $COMMIT_SHA.

  Enums:
    StatusValueValuesEnum: Output only. Status of the build.

  Messages:
    SubstitutionsValue: Substitutions data for `Build` resource.
    TimingValue: Output only. Stores timing information for phases of the
      build. Valid keys are: * BUILD: time to execute all build steps. * PUSH:
      time to push all artifacts including docker images and non docker
      artifacts. * FETCHSOURCE: time to fetch source. * SETUPBUILD: time to
      set up build. If the build does not specify source or images, these keys
      will not be included.

  Fields:
    approval: Output only. Describes this build's approval configuration,
      status, and result.
    artifacts: Artifacts produced by the build that should be uploaded upon
      successful completion of all build steps.
    availableSecrets: Secrets and secret environment variables.
    buildTriggerId: Output only. The ID of the `BuildTrigger` that triggered
      this build, if it was triggered automatically.
    createTime: Output only. Time at which the request to create the build was
      received.
    dependencies: Optional. Dependencies that the Cloud Build worker will
      fetch before executing user steps.
    failureInfo: Output only. Contains information about the build when
      status=FAILURE.
    finishTime: Output only. Time at which execution of the build was
      finished. The difference between finish_time and start_time is the
      duration of the build's execution.
    gitConfig: Optional. Configuration for git operations.
    id: Output only. Unique identifier of the build.
    images: A list of images to be pushed upon the successful completion of
      all build steps. The images are pushed using the builder service
      account's credentials. The digests of the pushed images will be stored
      in the `Build` resource's results field. If any of the images fail to be
      pushed, the build status is marked `FAILURE`.
    logUrl: Output only. URL to logs for this build in Google Cloud Console.
    logsBucket: Cloud Storage bucket where logs should be written (see [Bucket
      Name Requirements](https://cloud.google.com/storage/docs/bucket-
      naming#requirements)). Logs file names will be of the format
      `${logs_bucket}/log-${build_id}.txt`.
    name: Output only. The 'Build' name with format:
      `projects/{project}/locations/{location}/builds/{build}`, where {build}
      is a unique identifier generated by the service.
    options: Special options for this build.
    projectId: Output only. ID of the project.
    queueTtl: TTL in queue for this build. If provided and the build is
      enqueued longer than this value, the build will expire and the build
      status will be `EXPIRED`. The TTL starts ticking from create_time.
    results: Output only. Results of the build.
    secrets: Secrets to decrypt using Cloud Key Management Service. Note:
      Secret Manager is the recommended technique for managing sensitive data
      with Cloud Build. Use `available_secrets` to configure builds to access
      secrets from Secret Manager. For instructions, see:
      https://cloud.google.com/cloud-build/docs/securing-builds/use-secrets
    serviceAccount: IAM service account whose credentials will be used at
      build runtime. Must be of the format
      `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}`. ACCOUNT can be email
      address or uniqueId of the service account.
    source: Optional. The location of the source files to build.
    sourceProvenance: Output only. A permanent fixed identifier for source.
    startTime: Output only. Time at which execution of the build was started.
    status: Output only. Status of the build.
    statusDetail: Output only. Customer-readable message about the current
      status.
    steps: Required. The operations to be performed on the workspace.
    substitutions: Substitutions data for `Build` resource.
    tags: Tags for annotation of a `Build`. These are not docker tags.
    timeout: Amount of time that this build should be allowed to run, to
      second granularity. If this amount of time elapses, work on the build
      will cease and the build status will be `TIMEOUT`. `timeout` starts
      ticking from `startTime`. Default time is 60 minutes.
    timing: Output only. Stores timing information for phases of the build.
      Valid keys are: * BUILD: time to execute all build steps. * PUSH: time
      to push all artifacts including docker images and non docker artifacts.
      * FETCHSOURCE: time to fetch source. * SETUPBUILD: time to set up build.
      If the build does not specify source or images, these keys will not be
      included.
    warnings: Output only. Non-fatal problems encountered during the execution
      of the build.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""Output only. Status of the build.

    Values:
      STATUS_UNKNOWN: Status of the build is unknown.
      PENDING: Build has been created and is pending execution and queuing. It
        has not been queued.
      QUEUED: Build or step is queued; work has not yet begun.
      WORKING: Build or step is being executed.
      SUCCESS: Build or step finished successfully.
      FAILURE: Build or step failed to complete successfully.
      INTERNAL_ERROR: Build or step failed due to an internal cause.
      TIMEOUT: Build or step took longer than was allowed.
      CANCELLED: Build or step was canceled by a user.
      EXPIRED: Build was enqueued for longer than the value of `queue_ttl`.
    """
    STATUS_UNKNOWN = 0
    PENDING = 1
    QUEUED = 2
    WORKING = 3
    SUCCESS = 4
    FAILURE = 5
    INTERNAL_ERROR = 6
    TIMEOUT = 7
    CANCELLED = 8
    EXPIRED = 9

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SubstitutionsValue(_messages.Message):
    r"""Substitutions data for `Build` resource.

    Messages:
      AdditionalProperty: An additional property for a SubstitutionsValue
        object.

    Fields:
      additionalProperties: Additional properties of type SubstitutionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SubstitutionsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TimingValue(_messages.Message):
    r"""Output only. Stores timing information for phases of the build. Valid
    keys are: * BUILD: time to execute all build steps. * PUSH: time to push
    all artifacts including docker images and non docker artifacts. *
    FETCHSOURCE: time to fetch source. * SETUPBUILD: time to set up build. If
    the build does not specify source or images, these keys will not be
    included.

    Messages:
      AdditionalProperty: An additional property for a TimingValue object.

    Fields:
      additionalProperties: Additional properties of type TimingValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TimingValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleDevtoolsCloudbuildV1TimeSpan attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleDevtoolsCloudbuildV1TimeSpan', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  approval = _messages.MessageField('GoogleDevtoolsCloudbuildV1BuildApproval', 1)
  artifacts = _messages.MessageField('GoogleDevtoolsCloudbuildV1Artifacts', 2)
  availableSecrets = _messages.MessageField('GoogleDevtoolsCloudbuildV1Secrets', 3)
  buildTriggerId = _messages.StringField(4)
  createTime = _messages.StringField(5)
  dependencies = _messages.MessageField('GoogleDevtoolsCloudbuildV1Dependency', 6, repeated=True)
  failureInfo = _messages.MessageField('GoogleDevtoolsCloudbuildV1FailureInfo', 7)
  finishTime = _messages.StringField(8)
  gitConfig = _messages.MessageField('GoogleDevtoolsCloudbuildV1GitConfig', 9)
  id = _messages.StringField(10)
  images = _messages.StringField(11, repeated=True)
  logUrl = _messages.StringField(12)
  logsBucket = _messages.StringField(13)
  name = _messages.StringField(14)
  options = _messages.MessageField('GoogleDevtoolsCloudbuildV1BuildOptions', 15)
  projectId = _messages.StringField(16)
  queueTtl = _messages.StringField(17)
  results = _messages.MessageField('GoogleDevtoolsCloudbuildV1Results', 18)
  secrets = _messages.MessageField('GoogleDevtoolsCloudbuildV1Secret', 19, repeated=True)
  serviceAccount = _messages.StringField(20)
  source = _messages.MessageField('GoogleDevtoolsCloudbuildV1Source', 21)
  sourceProvenance = _messages.MessageField('GoogleDevtoolsCloudbuildV1SourceProvenance', 22)
  startTime = _messages.StringField(23)
  status = _messages.EnumField('StatusValueValuesEnum', 24)
  statusDetail = _messages.StringField(25)
  steps = _messages.MessageField('GoogleDevtoolsCloudbuildV1BuildStep', 26, repeated=True)
  substitutions = _messages.MessageField('SubstitutionsValue', 27)
  tags = _messages.StringField(28, repeated=True)
  timeout = _messages.StringField(29)
  timing = _messages.MessageField('TimingValue', 30)
  warnings = _messages.MessageField('GoogleDevtoolsCloudbuildV1Warning', 31, repeated=True)


class GoogleDevtoolsCloudbuildV1BuildApproval(_messages.Message):
  r"""BuildApproval describes a build's approval configuration, state, and
  result.

  Enums:
    StateValueValuesEnum: Output only. The state of this build's approval.

  Fields:
    config: Output only. Configuration for manual approval of this build.
    result: Output only. Result of manual approval for this Build.
    state: Output only. The state of this build's approval.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of this build's approval.

    Values:
      STATE_UNSPECIFIED: Default enum type. This should not be used.
      PENDING: Build approval is pending.
      APPROVED: Build approval has been approved.
      REJECTED: Build approval has been rejected.
      CANCELLED: Build was cancelled while it was still pending approval.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    APPROVED = 2
    REJECTED = 3
    CANCELLED = 4

  config = _messages.MessageField('GoogleDevtoolsCloudbuildV1ApprovalConfig', 1)
  result = _messages.MessageField('GoogleDevtoolsCloudbuildV1ApprovalResult', 2)
  state = _messages.EnumField('StateValueValuesEnum', 3)


class GoogleDevtoolsCloudbuildV1BuildOperationMetadata(_messages.Message):
  r"""Metadata for build operations.

  Fields:
    build: The build that the operation is tracking.
  """

  build = _messages.MessageField('GoogleDevtoolsCloudbuildV1Build', 1)


class GoogleDevtoolsCloudbuildV1BuildOptions(_messages.Message):
  r"""Optional arguments to enable specific features of builds.

  Enums:
    DefaultLogsBucketBehaviorValueValuesEnum: Optional. Option to specify how
      default logs buckets are setup.
    LogStreamingOptionValueValuesEnum: Option to define build log streaming
      behavior to Cloud Storage.
    LoggingValueValuesEnum: Option to specify the logging mode, which
      determines if and where build logs are stored.
    MachineTypeValueValuesEnum: Compute Engine machine type on which to run
      the build.
    RequestedVerifyOptionValueValuesEnum: Requested verifiability options.
    SourceProvenanceHashValueListEntryValuesEnum:
    SubstitutionOptionValueValuesEnum: Option to specify behavior when there
      is an error in the substitution checks. NOTE: this is always set to
      ALLOW_LOOSE for triggered builds and cannot be overridden in the build
      configuration file.

  Fields:
    automapSubstitutions: Option to include built-in and custom substitutions
      as env variables for all build steps.
    defaultLogsBucketBehavior: Optional. Option to specify how default logs
      buckets are setup.
    diskSizeGb: Requested disk size for the VM that runs the build. Note that
      this is *NOT* "disk free"; some of the space will be used by the
      operating system and build utilities. Also note that this is the minimum
      disk size that will be allocated for the build -- the build may run with
      a larger disk than requested. At present, the maximum disk size is
      4000GB; builds that request more than the maximum are rejected with an
      error.
    dynamicSubstitutions: Option to specify whether or not to apply bash style
      string operations to the substitutions. NOTE: this is always enabled for
      triggered builds and cannot be overridden in the build configuration
      file.
    enableStructuredLogging: Optional. Option to specify whether structured
      logging is enabled. If true, JSON-formatted logs are parsed as
      structured logs.
    env: A list of global environment variable definitions that will exist for
      all build steps in this build. If a variable is defined in both globally
      and in a build step, the variable will use the build step value. The
      elements are of the form "KEY=VALUE" for the environment variable "KEY"
      being given the value "VALUE".
    logStreamingOption: Option to define build log streaming behavior to Cloud
      Storage.
    logging: Option to specify the logging mode, which determines if and where
      build logs are stored.
    machineType: Compute Engine machine type on which to run the build.
    pool: Optional. Specification for execution on a `WorkerPool`. See
      [running builds in a private
      pool](https://cloud.google.com/build/docs/private-pools/run-builds-in-
      private-pool) for more information.
    pubsubTopic: Optional. Option to specify the Pub/Sub topic to receive
      build status updates.
    requestedVerifyOption: Requested verifiability options.
    secretEnv: A list of global environment variables, which are encrypted
      using a Cloud Key Management Service crypto key. These values must be
      specified in the build's `Secret`. These variables will be available to
      all build steps in this build.
    sourceProvenanceHash: Requested hash for SourceProvenance.
    substitutionOption: Option to specify behavior when there is an error in
      the substitution checks. NOTE: this is always set to ALLOW_LOOSE for
      triggered builds and cannot be overridden in the build configuration
      file.
    volumes: Global list of volumes to mount for ALL build steps Each volume
      is created as an empty volume prior to starting the build process. Upon
      completion of the build, volumes and their contents are discarded.
      Global volume names and paths cannot conflict with the volumes defined a
      build step. Using a global volume in a build with only one step is not
      valid as it is indicative of a build request with an incorrect
      configuration.
    workerPool: This field deprecated; please use `pool.name` instead.
  """

  class DefaultLogsBucketBehaviorValueValuesEnum(_messages.Enum):
    r"""Optional. Option to specify how default logs buckets are setup.

    Values:
      DEFAULT_LOGS_BUCKET_BEHAVIOR_UNSPECIFIED: Unspecified.
      REGIONAL_USER_OWNED_BUCKET: Bucket is located in user-owned project in
        the same region as the build. The builder service account must have
        access to create and write to Cloud Storage buckets in the build
        project.
      LEGACY_BUCKET: Bucket is located in a Google-owned project and is not
        regionalized.
    """
    DEFAULT_LOGS_BUCKET_BEHAVIOR_UNSPECIFIED = 0
    REGIONAL_USER_OWNED_BUCKET = 1
    LEGACY_BUCKET = 2

  class LogStreamingOptionValueValuesEnum(_messages.Enum):
    r"""Option to define build log streaming behavior to Cloud Storage.

    Values:
      STREAM_DEFAULT: Service may automatically determine build log streaming
        behavior.
      STREAM_ON: Build logs should be streamed to Cloud Storage.
      STREAM_OFF: Build logs should not be streamed to Cloud Storage; they
        will be written when the build is completed.
    """
    STREAM_DEFAULT = 0
    STREAM_ON = 1
    STREAM_OFF = 2

  class LoggingValueValuesEnum(_messages.Enum):
    r"""Option to specify the logging mode, which determines if and where
    build logs are stored.

    Values:
      LOGGING_UNSPECIFIED: The service determines the logging mode. The
        default is `LEGACY`. Do not rely on the default logging behavior as it
        may change in the future.
      LEGACY: Build logs are stored in Cloud Logging and Cloud Storage.
      GCS_ONLY: Build logs are stored in Cloud Storage.
      STACKDRIVER_ONLY: This option is the same as CLOUD_LOGGING_ONLY.
      CLOUD_LOGGING_ONLY: Build logs are stored in Cloud Logging. Selecting
        this option will not allow [logs
        streaming](https://cloud.google.com/sdk/gcloud/reference/builds/log).
      NONE: Turn off all logging. No build logs will be captured.
    """
    LOGGING_UNSPECIFIED = 0
    LEGACY = 1
    GCS_ONLY = 2
    STACKDRIVER_ONLY = 3
    CLOUD_LOGGING_ONLY = 4
    NONE = 5

  class MachineTypeValueValuesEnum(_messages.Enum):
    r"""Compute Engine machine type on which to run the build.

    Values:
      UNSPECIFIED: Standard machine type.
      N1_HIGHCPU_8: Highcpu machine with 8 CPUs.
      N1_HIGHCPU_32: Highcpu machine with 32 CPUs.
      E2_HIGHCPU_8: Highcpu e2 machine with 8 CPUs.
      E2_HIGHCPU_32: Highcpu e2 machine with 32 CPUs.
      E2_MEDIUM: E2 machine with 1 CPU.
    """
    UNSPECIFIED = 0
    N1_HIGHCPU_8 = 1
    N1_HIGHCPU_32 = 2
    E2_HIGHCPU_8 = 3
    E2_HIGHCPU_32 = 4
    E2_MEDIUM = 5

  class RequestedVerifyOptionValueValuesEnum(_messages.Enum):
    r"""Requested verifiability options.

    Values:
      NOT_VERIFIED: Not a verifiable build (the default).
      VERIFIED: Build must be verified.
    """
    NOT_VERIFIED = 0
    VERIFIED = 1

  class SourceProvenanceHashValueListEntryValuesEnum(_messages.Enum):
    r"""SourceProvenanceHashValueListEntryValuesEnum enum type.

    Values:
      NONE: No hash requested.
      SHA256: Use a sha256 hash.
      MD5: Use a md5 hash.
      GO_MODULE_H1: Dirhash of a Go module's source code which is then hex-
        encoded.
      SHA512: Use a sha512 hash.
    """
    NONE = 0
    SHA256 = 1
    MD5 = 2
    GO_MODULE_H1 = 3
    SHA512 = 4

  class SubstitutionOptionValueValuesEnum(_messages.Enum):
    r"""Option to specify behavior when there is an error in the substitution
    checks. NOTE: this is always set to ALLOW_LOOSE for triggered builds and
    cannot be overridden in the build configuration file.

    Values:
      MUST_MATCH: Fails the build if error in substitutions checks, like
        missing a substitution in the template or in the map.
      ALLOW_LOOSE: Do not fail the build if error in substitutions checks.
    """
    MUST_MATCH = 0
    ALLOW_LOOSE = 1

  automapSubstitutions = _messages.BooleanField(1)
  defaultLogsBucketBehavior = _messages.EnumField('DefaultLogsBucketBehaviorValueValuesEnum', 2)
  diskSizeGb = _messages.IntegerField(3)
  dynamicSubstitutions = _messages.BooleanField(4)
  enableStructuredLogging = _messages.BooleanField(5)
  env = _messages.StringField(6, repeated=True)
  logStreamingOption = _messages.EnumField('LogStreamingOptionValueValuesEnum', 7)
  logging = _messages.EnumField('LoggingValueValuesEnum', 8)
  machineType = _messages.EnumField('MachineTypeValueValuesEnum', 9)
  pool = _messages.MessageField('GoogleDevtoolsCloudbuildV1PoolOption', 10)
  pubsubTopic = _messages.StringField(11)
  requestedVerifyOption = _messages.EnumField('RequestedVerifyOptionValueValuesEnum', 12)
  secretEnv = _messages.StringField(13, repeated=True)
  sourceProvenanceHash = _messages.EnumField('SourceProvenanceHashValueListEntryValuesEnum', 14, repeated=True)
  substitutionOption = _messages.EnumField('SubstitutionOptionValueValuesEnum', 15)
  volumes = _messages.MessageField('GoogleDevtoolsCloudbuildV1Volume', 16, repeated=True)
  workerPool = _messages.StringField(17)


class GoogleDevtoolsCloudbuildV1BuildStep(_messages.Message):
  r"""A step in the build pipeline.

  Enums:
    StatusValueValuesEnum: Output only. Status of the build step. At this
      time, build step status is only updated on build completion; step status
      is not updated in real-time as the build progresses.

  Fields:
    allowExitCodes: Allow this build step to fail without failing the entire
      build if and only if the exit code is one of the specified codes. If
      allow_failure is also specified, this field will take precedence.
    allowFailure: Allow this build step to fail without failing the entire
      build. If false, the entire build will fail if this step fails.
      Otherwise, the build will succeed, but this step will still have a
      failure status. Error information will be reported in the failure_detail
      field.
    args: A list of arguments that will be presented to the step when it is
      started. If the image used to run the step's container has an
      entrypoint, the `args` are used as arguments to that entrypoint. If the
      image does not define an entrypoint, the first element in args is used
      as the entrypoint, and the remainder will be used as arguments.
    automapSubstitutions: Option to include built-in and custom substitutions
      as env variables for this build step. This option will override the
      global option in BuildOption.
    dir: Working directory to use when running this step's container. If this
      value is a relative path, it is relative to the build's working
      directory. If this value is absolute, it may be outside the build's
      working directory, in which case the contents of the path may not be
      persisted across build step executions, unless a `volume` for that path
      is specified. If the build specifies a `RepoSource` with `dir` and a
      step with a `dir`, which specifies an absolute path, the `RepoSource`
      `dir` is ignored for the step's execution.
    entrypoint: Entrypoint to be used instead of the build step image's
      default entrypoint. If unset, the image's default entrypoint is used.
    env: A list of environment variable definitions to be used when running a
      step. The elements are of the form "KEY=VALUE" for the environment
      variable "KEY" being given the value "VALUE".
    exitCode: Output only. Return code from running the step.
    id: Unique identifier for this build step, used in `wait_for` to reference
      this build step as a dependency.
    name: Required. The name of the container image that will run this
      particular build step. If the image is available in the host's Docker
      daemon's cache, it will be run directly. If not, the host will attempt
      to pull the image first, using the builder service account's credentials
      if necessary. The Docker daemon's cache will already have the latest
      versions of all of the officially supported build steps
      ([https://github.com/GoogleCloudPlatform/cloud-
      builders](https://github.com/GoogleCloudPlatform/cloud-builders)). The
      Docker daemon will also have cached many of the layers for some popular
      images, like "ubuntu", "debian", but they will be refreshed at the time
      you attempt to use them. If you built an image in a previous build step,
      it will be stored in the host's Docker daemon's cache and is available
      to use as the name for a later build step.
    pullTiming: Output only. Stores timing information for pulling this build
      step's builder image only.
    results: Declaration of results for this build step.
    script: A shell script to be executed in the step. When script is
      provided, the user cannot specify the entrypoint or args.
    secretEnv: A list of environment variables which are encrypted using a
      Cloud Key Management Service crypto key. These values must be specified
      in the build's `Secret`.
    status: Output only. Status of the build step. At this time, build step
      status is only updated on build completion; step status is not updated
      in real-time as the build progresses.
    timeout: Time limit for executing this build step. If not defined, the
      step has no time limit and will be allowed to continue to run until
      either it completes or the build itself times out.
    timing: Output only. Stores timing information for executing this build
      step.
    volumes: List of volumes to mount into the build step. Each volume is
      created as an empty volume prior to execution of the build step. Upon
      completion of the build, volumes and their contents are discarded. Using
      a named volume in only one step is not valid as it is indicative of a
      build request with an incorrect configuration.
    waitFor: The ID(s) of the step(s) that this build step depends on. This
      build step will not start until all the build steps in `wait_for` have
      completed successfully. If `wait_for` is empty, this build step will
      start when all previous build steps in the `Build.Steps` list have
      completed successfully.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""Output only. Status of the build step. At this time, build step status
    is only updated on build completion; step status is not updated in real-
    time as the build progresses.

    Values:
      STATUS_UNKNOWN: Status of the build is unknown.
      PENDING: Build has been created and is pending execution and queuing. It
        has not been queued.
      QUEUED: Build or step is queued; work has not yet begun.
      WORKING: Build or step is being executed.
      SUCCESS: Build or step finished successfully.
      FAILURE: Build or step failed to complete successfully.
      INTERNAL_ERROR: Build or step failed due to an internal cause.
      TIMEOUT: Build or step took longer than was allowed.
      CANCELLED: Build or step was canceled by a user.
      EXPIRED: Build was enqueued for longer than the value of `queue_ttl`.
    """
    STATUS_UNKNOWN = 0
    PENDING = 1
    QUEUED = 2
    WORKING = 3
    SUCCESS = 4
    FAILURE = 5
    INTERNAL_ERROR = 6
    TIMEOUT = 7
    CANCELLED = 8
    EXPIRED = 9

  allowExitCodes = _messages.IntegerField(1, repeated=True, variant=_messages.Variant.INT32)
  allowFailure = _messages.BooleanField(2)
  args = _messages.StringField(3, repeated=True)
  automapSubstitutions = _messages.BooleanField(4)
  dir = _messages.StringField(5)
  entrypoint = _messages.StringField(6)
  env = _messages.StringField(7, repeated=True)
  exitCode = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  id = _messages.StringField(9)
  name = _messages.StringField(10)
  pullTiming = _messages.MessageField('GoogleDevtoolsCloudbuildV1TimeSpan', 11)
  results = _messages.MessageField('GoogleDevtoolsCloudbuildV1StepResult', 12, repeated=True)
  script = _messages.StringField(13)
  secretEnv = _messages.StringField(14, repeated=True)
  status = _messages.EnumField('StatusValueValuesEnum', 15)
  timeout = _messages.StringField(16)
  timing = _messages.MessageField('GoogleDevtoolsCloudbuildV1TimeSpan', 17)
  volumes = _messages.MessageField('GoogleDevtoolsCloudbuildV1Volume', 18, repeated=True)
  waitFor = _messages.StringField(19, repeated=True)


class GoogleDevtoolsCloudbuildV1BuiltImage(_messages.Message):
  r"""An image built by the pipeline.

  Fields:
    digest: Docker Registry 2.0 digest.
    name: Name used to push the container image to Google Container Registry,
      as presented to `docker push`.
    pushTiming: Output only. Stores timing information for pushing the
      specified image.
  """

  digest = _messages.StringField(1)
  name = _messages.StringField(2)
  pushTiming = _messages.MessageField('GoogleDevtoolsCloudbuildV1TimeSpan', 3)


class GoogleDevtoolsCloudbuildV1ConnectedRepository(_messages.Message):
  r"""Location of the source in a 2nd-gen Google Cloud Build repository
  resource.

  Fields:
    dir: Optional. Directory, relative to the source root, in which to run the
      build.
    repository: Required. Name of the Google Cloud Build repository, formatted
      as `projects/*/locations/*/connections/*/repositories/*`.
    revision: Required. The revision to fetch from the Git repository such as
      a branch, a tag, a commit SHA, or any Git ref.
  """

  dir = _messages.StringField(1)
  repository = _messages.StringField(2)
  revision = _messages.StringField(3)


class GoogleDevtoolsCloudbuildV1Dependency(_messages.Message):
  r"""A dependency that the Cloud Build worker will fetch before executing
  user steps.

  Fields:
    empty: If set to true disable all dependency fetching (ignoring the
      default source as well).
    gitSource: Represents a git repository as a build dependency.
  """

  empty = _messages.BooleanField(1)
  gitSource = _messages.MessageField('GoogleDevtoolsCloudbuildV1GitSourceDependency', 2)


class GoogleDevtoolsCloudbuildV1DeveloperConnectConfig(_messages.Message):
  r"""This config defines the location of a source through Developer Connect.

  Fields:
    dir: Required. Directory, relative to the source root, in which to run the
      build.
    gitRepositoryLink: Required. The Developer Connect Git repository link,
      formatted as `projects/*/locations/*/connections/*/gitRepositoryLink/*`.
    revision: Required. The revision to fetch from the Git repository such as
      a branch, a tag, a commit SHA, or any Git ref.
  """

  dir = _messages.StringField(1)
  gitRepositoryLink = _messages.StringField(2)
  revision = _messages.StringField(3)


class GoogleDevtoolsCloudbuildV1FailureInfo(_messages.Message):
  r"""A fatal problem encountered during the execution of the build.

  Enums:
    TypeValueValuesEnum: The name of the failure.

  Fields:
    detail: Explains the failure issue in more detail using hard-coded text.
    type: The name of the failure.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The name of the failure.

    Values:
      FAILURE_TYPE_UNSPECIFIED: Type unspecified
      PUSH_FAILED: Unable to push the image to the repository.
      PUSH_IMAGE_NOT_FOUND: Final image not found.
      PUSH_NOT_AUTHORIZED: Unauthorized push of the final image.
      LOGGING_FAILURE: Backend logging failures. Should retry.
      USER_BUILD_STEP: A build step has failed.
      FETCH_SOURCE_FAILED: The source fetching has failed.
    """
    FAILURE_TYPE_UNSPECIFIED = 0
    PUSH_FAILED = 1
    PUSH_IMAGE_NOT_FOUND = 2
    PUSH_NOT_AUTHORIZED = 3
    LOGGING_FAILURE = 4
    USER_BUILD_STEP = 5
    FETCH_SOURCE_FAILED = 6

  detail = _messages.StringField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class GoogleDevtoolsCloudbuildV1FileHashes(_messages.Message):
  r"""Container message for hashes of byte content of files, used in
  SourceProvenance messages to verify integrity of source input to the build.

  Fields:
    fileHash: Collection of file hashes.
  """

  fileHash = _messages.MessageField('GoogleDevtoolsCloudbuildV1Hash', 1, repeated=True)


class GoogleDevtoolsCloudbuildV1GitConfig(_messages.Message):
  r"""GitConfig is a configuration for git operations.

  Fields:
    http: Configuration for HTTP related git operations.
  """

  http = _messages.MessageField('GoogleDevtoolsCloudbuildV1HttpConfig', 1)


class GoogleDevtoolsCloudbuildV1GitSource(_messages.Message):
  r"""Location of the source in any accessible Git repository.

  Fields:
    dir: Optional. Directory, relative to the source root, in which to run the
      build. This must be a relative path. If a step's `dir` is specified and
      is an absolute path, this value is ignored for that step's execution.
    revision: Optional. The revision to fetch from the Git repository such as
      a branch, a tag, a commit SHA, or any Git ref. Cloud Build uses `git
      fetch` to fetch the revision from the Git repository; therefore make
      sure that the string you provide for `revision` is parsable by the
      command. For information on string values accepted by `git fetch`, see
      https://git-scm.com/docs/gitrevisions#_specifying_revisions. For
      information on `git fetch`, see https://git-scm.com/docs/git-fetch.
    url: Required. Location of the Git repo to build. This will be used as a
      `git remote`, see https://git-scm.com/docs/git-remote.
  """

  dir = _messages.StringField(1)
  revision = _messages.StringField(2)
  url = _messages.StringField(3)


class GoogleDevtoolsCloudbuildV1GitSourceDependency(_messages.Message):
  r"""Represents a git repository as a build dependency.

  Fields:
    depth: Optional. How much history should be fetched for the build (default
      1, -1 for all history).
    destPath: Required. Where should the files be placed on the worker.
    recurseSubmodules: Optional. True if submodules should be fetched too
      (default false).
    repository: Required. The kind of repo (url or dev connect).
    revision: Required. The revision that we will fetch the repo at.
  """

  depth = _messages.IntegerField(1)
  destPath = _messages.StringField(2)
  recurseSubmodules = _messages.BooleanField(3)
  repository = _messages.MessageField('GoogleDevtoolsCloudbuildV1GitSourceRepository', 4)
  revision = _messages.StringField(5)


class GoogleDevtoolsCloudbuildV1GitSourceRepository(_messages.Message):
  r"""A repository for a git source.

  Fields:
    developerConnect: The Developer Connect Git repository link formatted as
      `projects/*/locations/*/connections/*/gitRepositoryLink/*`
    url: Location of the Git repository.
  """

  developerConnect = _messages.StringField(1)
  url = _messages.StringField(2)


class GoogleDevtoolsCloudbuildV1GoModule(_messages.Message):
  r"""Go module to upload to Artifact Registry upon successful completion of
  all build steps. A module refers to all dependencies in a go.mod file.

  Fields:
    modulePath: Optional. The Go module's "module path". e.g.
      example.com/foo/v2
    moduleVersion: Optional. The Go module's semantic version in the form
      vX.Y.Z. e.g. v0.1.1 Pre-release identifiers can also be added by
      appending a dash and dot separated ASCII alphanumeric characters and
      hyphens. e.g. v0.2.3-alpha.x.12m.5
    repositoryLocation: Optional. Location of the Artifact Registry
      repository. i.e. us-east1 Defaults to the build's location.
    repositoryName: Optional. Artifact Registry repository name. Specified Go
      modules will be zipped and uploaded to Artifact Registry with this
      location as a prefix. e.g. my-go-repo
    repositoryProjectId: Optional. Project ID of the Artifact Registry
      repository. Defaults to the build project.
    sourcePath: Optional. Source path of the go.mod file in the build's
      workspace. If not specified, this will default to the current directory.
      e.g. ~/code/go/mypackage
  """

  modulePath = _messages.StringField(1)
  moduleVersion = _messages.StringField(2)
  repositoryLocation = _messages.StringField(3)
  repositoryName = _messages.StringField(4)
  repositoryProjectId = _messages.StringField(5)
  sourcePath = _messages.StringField(6)


class GoogleDevtoolsCloudbuildV1Hash(_messages.Message):
  r"""Container message for hash values.

  Enums:
    TypeValueValuesEnum: The type of hash that was performed.

  Fields:
    type: The type of hash that was performed.
    value: The hash value.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of hash that was performed.

    Values:
      NONE: No hash requested.
      SHA256: Use a sha256 hash.
      MD5: Use a md5 hash.
      GO_MODULE_H1: Dirhash of a Go module's source code which is then hex-
        encoded.
      SHA512: Use a sha512 hash.
    """
    NONE = 0
    SHA256 = 1
    MD5 = 2
    GO_MODULE_H1 = 3
    SHA512 = 4

  type = _messages.EnumField('TypeValueValuesEnum', 1)
  value = _messages.BytesField(2)


class GoogleDevtoolsCloudbuildV1HttpConfig(_messages.Message):
  r"""HttpConfig is a configuration for HTTP related git operations.

  Fields:
    proxySecretVersionName: SecretVersion resource of the HTTP proxy URL. The
      Service Account used in the build (either the default Service Account or
      user-specified Service Account) should have
      `secretmanager.versions.access` permissions on this secret. The proxy
      URL should be in format `protocol://@]proxyhost[:port]`.
  """

  proxySecretVersionName = _messages.StringField(1)


class GoogleDevtoolsCloudbuildV1InlineSecret(_messages.Message):
  r"""Pairs a set of secret environment variables mapped to encrypted values
  with the Cloud KMS key to use to decrypt the value.

  Messages:
    EnvMapValue: Map of environment variable name to its encrypted value.
      Secret environment variables must be unique across all of a build's
      secrets, and must be used by at least one build step. Values can be at
      most 64 KB in size. There can be at most 100 secret values across all of
      a build's secrets.

  Fields:
    envMap: Map of environment variable name to its encrypted value. Secret
      environment variables must be unique across all of a build's secrets,
      and must be used by at least one build step. Values can be at most 64 KB
      in size. There can be at most 100 secret values across all of a build's
      secrets.
    kmsKeyName: Resource name of Cloud KMS crypto key to decrypt the encrypted
      value. In format: projects/*/locations/*/keyRings/*/cryptoKeys/*
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvMapValue(_messages.Message):
    r"""Map of environment variable name to its encrypted value. Secret
    environment variables must be unique across all of a build's secrets, and
    must be used by at least one build step. Values can be at most 64 KB in
    size. There can be at most 100 secret values across all of a build's
    secrets.

    Messages:
      AdditionalProperty: An additional property for a EnvMapValue object.

    Fields:
      additionalProperties: Additional properties of type EnvMapValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvMapValue object.

      Fields:
        key: Name of the additional property.
        value: A byte attribute.
      """

      key = _messages.StringField(1)
      value = _messages.BytesField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  envMap = _messages.MessageField('EnvMapValue', 1)
  kmsKeyName = _messages.StringField(2)


class GoogleDevtoolsCloudbuildV1MavenArtifact(_messages.Message):
  r"""A Maven artifact to upload to Artifact Registry upon successful
  completion of all build steps.

  Fields:
    artifactId: Maven `artifactId` value used when uploading the artifact to
      Artifact Registry.
    groupId: Maven `groupId` value used when uploading the artifact to
      Artifact Registry.
    path: Optional. Path to an artifact in the build's workspace to be
      uploaded to Artifact Registry. This can be either an absolute path, e.g.
      /workspace/my-app/target/my-app-1.0.SNAPSHOT.jar or a relative path from
      /workspace, e.g. my-app/target/my-app-1.0.SNAPSHOT.jar.
    repository: Artifact Registry repository, in the form "https://$REGION-
      maven.pkg.dev/$PROJECT/$REPOSITORY" Artifact in the workspace specified
      by path will be uploaded to Artifact Registry with this location as a
      prefix.
    version: Maven `version` value used when uploading the artifact to
      Artifact Registry.
  """

  artifactId = _messages.StringField(1)
  groupId = _messages.StringField(2)
  path = _messages.StringField(3)
  repository = _messages.StringField(4)
  version = _messages.StringField(5)


class GoogleDevtoolsCloudbuildV1NpmPackage(_messages.Message):
  r"""Npm package to upload to Artifact Registry upon successful completion of
  all build steps.

  Fields:
    packagePath: Path to the package.json. e.g. workspace/path/to/package
    repository: Artifact Registry repository, in the form "https://$REGION-
      npm.pkg.dev/$PROJECT/$REPOSITORY" Npm package in the workspace specified
      by path will be zipped and uploaded to Artifact Registry with this
      location as a prefix.
  """

  packagePath = _messages.StringField(1)
  repository = _messages.StringField(2)


class GoogleDevtoolsCloudbuildV1PoolOption(_messages.Message):
  r"""Details about how a build should be executed on a `WorkerPool`. See
  [running builds in a private
  pool](https://cloud.google.com/build/docs/private-pools/run-builds-in-
  private-pool) for more information.

  Fields:
    name: The `WorkerPool` resource to execute the build on. You must have
      `cloudbuild.workerpools.use` on the project hosting the WorkerPool.
      Format
      projects/{project}/locations/{location}/workerPools/{workerPoolId}
  """

  name = _messages.StringField(1)


class GoogleDevtoolsCloudbuildV1PythonPackage(_messages.Message):
  r"""Python package to upload to Artifact Registry upon successful completion
  of all build steps. A package can encapsulate multiple objects to be
  uploaded to a single repository.

  Fields:
    paths: Path globs used to match files in the build's workspace. For
      Python/ Twine, this is usually `dist/*`, and sometimes additionally an
      `.asc` file.
    repository: Artifact Registry repository, in the form "https://$REGION-
      python.pkg.dev/$PROJECT/$REPOSITORY" Files in the workspace matching any
      path pattern will be uploaded to Artifact Registry with this location as
      a prefix.
  """

  paths = _messages.StringField(1, repeated=True)
  repository = _messages.StringField(2)


class GoogleDevtoolsCloudbuildV1RepoSource(_messages.Message):
  r"""Location of the source in a Google Cloud Source Repository.

  Messages:
    SubstitutionsValue: Optional. Substitutions to use in a triggered build.
      Should only be used with RunBuildTrigger

  Fields:
    branchName: Regex matching branches to build. The syntax of the regular
      expressions accepted is the syntax accepted by RE2 and described at
      https://github.com/google/re2/wiki/Syntax
    commitSha: Explicit commit SHA to build.
    dir: Optional. Directory, relative to the source root, in which to run the
      build. This must be a relative path. If a step's `dir` is specified and
      is an absolute path, this value is ignored for that step's execution.
    invertRegex: Optional. Only trigger a build if the revision regex does NOT
      match the revision regex.
    projectId: Optional. ID of the project that owns the Cloud Source
      Repository. If omitted, the project ID requesting the build is assumed.
    repoName: Required. Name of the Cloud Source Repository.
    substitutions: Optional. Substitutions to use in a triggered build. Should
      only be used with RunBuildTrigger
    tagName: Regex matching tags to build. The syntax of the regular
      expressions accepted is the syntax accepted by RE2 and described at
      https://github.com/google/re2/wiki/Syntax
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SubstitutionsValue(_messages.Message):
    r"""Optional. Substitutions to use in a triggered build. Should only be
    used with RunBuildTrigger

    Messages:
      AdditionalProperty: An additional property for a SubstitutionsValue
        object.

    Fields:
      additionalProperties: Additional properties of type SubstitutionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SubstitutionsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  branchName = _messages.StringField(1)
  commitSha = _messages.StringField(2)
  dir = _messages.StringField(3)
  invertRegex = _messages.BooleanField(4)
  projectId = _messages.StringField(5)
  repoName = _messages.StringField(6)
  substitutions = _messages.MessageField('SubstitutionsValue', 7)
  tagName = _messages.StringField(8)


class GoogleDevtoolsCloudbuildV1Results(_messages.Message):
  r"""Artifacts created by the build pipeline.

  Fields:
    artifactManifest: Path to the artifact manifest for non-container
      artifacts uploaded to Cloud Storage. Only populated when artifacts are
      uploaded to Cloud Storage.
    artifactTiming: Time to push all non-container artifacts to Cloud Storage.
    buildStepImages: List of build step digests, in the order corresponding to
      build step indices.
    buildStepOutputs: List of build step outputs, produced by builder images,
      in the order corresponding to build step indices. [Cloud
      Builders](https://cloud.google.com/cloud-build/docs/cloud-builders) can
      produce this output by writing to `$BUILDER_OUTPUT/output`. Only the
      first 50KB of data is stored. Note that the `$BUILDER_OUTPUT` variable
      is read-only and can't be substituted.
    goModules: Optional. Go module artifacts uploaded to Artifact Registry at
      the end of the build.
    images: Container images that were built as a part of the build.
    mavenArtifacts: Maven artifacts uploaded to Artifact Registry at the end
      of the build.
    npmPackages: Npm packages uploaded to Artifact Registry at the end of the
      build.
    numArtifacts: Number of non-container artifacts uploaded to Cloud Storage.
      Only populated when artifacts are uploaded to Cloud Storage.
    pythonPackages: Python artifacts uploaded to Artifact Registry at the end
      of the build.
  """

  artifactManifest = _messages.StringField(1)
  artifactTiming = _messages.MessageField('GoogleDevtoolsCloudbuildV1TimeSpan', 2)
  buildStepImages = _messages.StringField(3, repeated=True)
  buildStepOutputs = _messages.BytesField(4, repeated=True)
  goModules = _messages.MessageField('GoogleDevtoolsCloudbuildV1UploadedGoModule', 5, repeated=True)
  images = _messages.MessageField('GoogleDevtoolsCloudbuildV1BuiltImage', 6, repeated=True)
  mavenArtifacts = _messages.MessageField('GoogleDevtoolsCloudbuildV1UploadedMavenArtifact', 7, repeated=True)
  npmPackages = _messages.MessageField('GoogleDevtoolsCloudbuildV1UploadedNpmPackage', 8, repeated=True)
  numArtifacts = _messages.IntegerField(9)
  pythonPackages = _messages.MessageField('GoogleDevtoolsCloudbuildV1UploadedPythonPackage', 10, repeated=True)


class GoogleDevtoolsCloudbuildV1Secret(_messages.Message):
  r"""Pairs a set of secret environment variables containing encrypted values
  with the Cloud KMS key to use to decrypt the value. Note: Use `kmsKeyName`
  with `available_secrets` instead of using `kmsKeyName` with `secret`. For
  instructions see: https://cloud.google.com/cloud-build/docs/securing-
  builds/use-encrypted-credentials.

  Messages:
    SecretEnvValue: Map of environment variable name to its encrypted value.
      Secret environment variables must be unique across all of a build's
      secrets, and must be used by at least one build step. Values can be at
      most 64 KB in size. There can be at most 100 secret values across all of
      a build's secrets.

  Fields:
    kmsKeyName: Cloud KMS key name to use to decrypt these envs.
    secretEnv: Map of environment variable name to its encrypted value. Secret
      environment variables must be unique across all of a build's secrets,
      and must be used by at least one build step. Values can be at most 64 KB
      in size. There can be at most 100 secret values across all of a build's
      secrets.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SecretEnvValue(_messages.Message):
    r"""Map of environment variable name to its encrypted value. Secret
    environment variables must be unique across all of a build's secrets, and
    must be used by at least one build step. Values can be at most 64 KB in
    size. There can be at most 100 secret values across all of a build's
    secrets.

    Messages:
      AdditionalProperty: An additional property for a SecretEnvValue object.

    Fields:
      additionalProperties: Additional properties of type SecretEnvValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SecretEnvValue object.

      Fields:
        key: Name of the additional property.
        value: A byte attribute.
      """

      key = _messages.StringField(1)
      value = _messages.BytesField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  kmsKeyName = _messages.StringField(1)
  secretEnv = _messages.MessageField('SecretEnvValue', 2)


class GoogleDevtoolsCloudbuildV1SecretManagerSecret(_messages.Message):
  r"""Pairs a secret environment variable with a SecretVersion in Secret
  Manager.

  Fields:
    env: Environment variable name to associate with the secret. Secret
      environment variables must be unique across all of a build's secrets,
      and must be used by at least one build step.
    versionName: Resource name of the SecretVersion. In format:
      projects/*/secrets/*/versions/*
  """

  env = _messages.StringField(1)
  versionName = _messages.StringField(2)


class GoogleDevtoolsCloudbuildV1Secrets(_messages.Message):
  r"""Secrets and secret environment variables.

  Fields:
    inline: Secrets encrypted with KMS key and the associated secret
      environment variable.
    secretManager: Secrets in Secret Manager and associated secret environment
      variable.
  """

  inline = _messages.MessageField('GoogleDevtoolsCloudbuildV1InlineSecret', 1, repeated=True)
  secretManager = _messages.MessageField('GoogleDevtoolsCloudbuildV1SecretManagerSecret', 2, repeated=True)


class GoogleDevtoolsCloudbuildV1Source(_messages.Message):
  r"""Location of the source in a supported storage service.

  Fields:
    buildConfigFileName: Path, from the source root, to the build
      configuration file (i.e. cloudbuild.yaml).
    connectedRepository: Optional. If provided, get the source from this 2nd-
      gen Google Cloud Build repository resource.
    developerConnectConfig: If provided, get the source from this Developer
      Connect config.
    gitSource: If provided, get the source from this Git repository.
    repoSource: If provided, get the source from this location in a Cloud
      Source Repository.
    storageSource: If provided, get the source from this location in Cloud
      Storage.
    storageSourceManifest: If provided, get the source from this manifest in
      Cloud Storage. This feature is in Preview; see description
      [here](https://github.com/GoogleCloudPlatform/cloud-
      builders/tree/master/gcs-fetcher).
  """

  buildConfigFileName = _messages.StringField(1)
  connectedRepository = _messages.MessageField('GoogleDevtoolsCloudbuildV1ConnectedRepository', 2)
  developerConnectConfig = _messages.MessageField('GoogleDevtoolsCloudbuildV1DeveloperConnectConfig', 3)
  gitSource = _messages.MessageField('GoogleDevtoolsCloudbuildV1GitSource', 4)
  repoSource = _messages.MessageField('GoogleDevtoolsCloudbuildV1RepoSource', 5)
  storageSource = _messages.MessageField('GoogleDevtoolsCloudbuildV1StorageSource', 6)
  storageSourceManifest = _messages.MessageField('GoogleDevtoolsCloudbuildV1StorageSourceManifest', 7)


class GoogleDevtoolsCloudbuildV1SourceProvenance(_messages.Message):
  r"""Provenance of the source. Ways to find the original source, or verify
  that some source was used for this build.

  Messages:
    FileHashesValue: Output only. Hash(es) of the build source, which can be
      used to verify that the original source integrity was maintained in the
      build. Note that `FileHashes` will only be populated if `BuildOptions`
      has requested a `SourceProvenanceHash`. The keys to this map are file
      paths used as build source and the values contain the hash values for
      those files. If the build source came in a single package such as a
      gzipped tarfile (`.tar.gz`), the `FileHash` will be for the single path
      to that file.

  Fields:
    fileHashes: Output only. Hash(es) of the build source, which can be used
      to verify that the original source integrity was maintained in the
      build. Note that `FileHashes` will only be populated if `BuildOptions`
      has requested a `SourceProvenanceHash`. The keys to this map are file
      paths used as build source and the values contain the hash values for
      those files. If the build source came in a single package such as a
      gzipped tarfile (`.tar.gz`), the `FileHash` will be for the single path
      to that file.
    resolvedConnectedRepository: Output only. A copy of the build's
      `source.connected_repository`, if exists, with any revisions resolved.
    resolvedGitSource: Output only. A copy of the build's `source.git_source`,
      if exists, with any revisions resolved.
    resolvedRepoSource: A copy of the build's `source.repo_source`, if exists,
      with any revisions resolved.
    resolvedStorageSource: A copy of the build's `source.storage_source`, if
      exists, with any generations resolved.
    resolvedStorageSourceManifest: A copy of the build's
      `source.storage_source_manifest`, if exists, with any revisions
      resolved. This feature is in Preview.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FileHashesValue(_messages.Message):
    r"""Output only. Hash(es) of the build source, which can be used to verify
    that the original source integrity was maintained in the build. Note that
    `FileHashes` will only be populated if `BuildOptions` has requested a
    `SourceProvenanceHash`. The keys to this map are file paths used as build
    source and the values contain the hash values for those files. If the
    build source came in a single package such as a gzipped tarfile
    (`.tar.gz`), the `FileHash` will be for the single path to that file.

    Messages:
      AdditionalProperty: An additional property for a FileHashesValue object.

    Fields:
      additionalProperties: Additional properties of type FileHashesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FileHashesValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleDevtoolsCloudbuildV1FileHashes attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleDevtoolsCloudbuildV1FileHashes', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  fileHashes = _messages.MessageField('FileHashesValue', 1)
  resolvedConnectedRepository = _messages.MessageField('GoogleDevtoolsCloudbuildV1ConnectedRepository', 2)
  resolvedGitSource = _messages.MessageField('GoogleDevtoolsCloudbuildV1GitSource', 3)
  resolvedRepoSource = _messages.MessageField('GoogleDevtoolsCloudbuildV1RepoSource', 4)
  resolvedStorageSource = _messages.MessageField('GoogleDevtoolsCloudbuildV1StorageSource', 5)
  resolvedStorageSourceManifest = _messages.MessageField('GoogleDevtoolsCloudbuildV1StorageSourceManifest', 6)


class GoogleDevtoolsCloudbuildV1StepResult(_messages.Message):
  r"""StepResult is the declaration of a result for a build step.

  Fields:
    attestationContent: Optional. The content of the attestation to be
      generated.
    attestationType: Optional. The type of attestation to be generated.
    name: Required. The name of the result.
  """

  attestationContent = _messages.StringField(1)
  attestationType = _messages.StringField(2)
  name = _messages.StringField(3)


class GoogleDevtoolsCloudbuildV1StorageSource(_messages.Message):
  r"""Location of the source in an archive file in Cloud Storage.

  Enums:
    SourceFetcherValueValuesEnum: Optional. Option to specify the tool to
      fetch the source file for the build.

  Fields:
    bucket: Cloud Storage bucket containing the source (see [Bucket Name
      Requirements](https://cloud.google.com/storage/docs/bucket-
      naming#requirements)).
    generation: Optional. Cloud Storage generation for the object. If the
      generation is omitted, the latest generation will be used.
    object: Required. Cloud Storage object containing the source. This object
      must be a zipped (`.zip`) or gzipped archive file (`.tar.gz`) containing
      source to build.
    sourceFetcher: Optional. Option to specify the tool to fetch the source
      file for the build.
  """

  class SourceFetcherValueValuesEnum(_messages.Enum):
    r"""Optional. Option to specify the tool to fetch the source file for the
    build.

    Values:
      SOURCE_FETCHER_UNSPECIFIED: Unspecified defaults to GSUTIL.
      GSUTIL: Use the "gsutil" tool to download the source file.
      GCS_FETCHER: Use the Cloud Storage Fetcher tool to download the source
        file.
    """
    SOURCE_FETCHER_UNSPECIFIED = 0
    GSUTIL = 1
    GCS_FETCHER = 2

  bucket = _messages.StringField(1)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3)
  sourceFetcher = _messages.EnumField('SourceFetcherValueValuesEnum', 4)


class GoogleDevtoolsCloudbuildV1StorageSourceManifest(_messages.Message):
  r"""Location of the source manifest in Cloud Storage. This feature is in
  Preview; see description
  [here](https://github.com/GoogleCloudPlatform/cloud-
  builders/tree/master/gcs-fetcher).

  Fields:
    bucket: Required. Cloud Storage bucket containing the source manifest (see
      [Bucket Name Requirements](https://cloud.google.com/storage/docs/bucket-
      naming#requirements)).
    generation: Cloud Storage generation for the object. If the generation is
      omitted, the latest generation will be used.
    object: Required. Cloud Storage object containing the source manifest.
      This object must be a JSON file.
  """

  bucket = _messages.StringField(1)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3)


class GoogleDevtoolsCloudbuildV1TimeSpan(_messages.Message):
  r"""Start and end times for a build execution phase.

  Fields:
    endTime: End of time span.
    startTime: Start of time span.
  """

  endTime = _messages.StringField(1)
  startTime = _messages.StringField(2)


class GoogleDevtoolsCloudbuildV1UploadedGoModule(_messages.Message):
  r"""A Go module artifact uploaded to Artifact Registry using the GoModule
  directive.

  Fields:
    fileHashes: Hash types and values of the Go Module Artifact.
    pushTiming: Output only. Stores timing information for pushing the
      specified artifact.
    uri: URI of the uploaded artifact.
  """

  fileHashes = _messages.MessageField('GoogleDevtoolsCloudbuildV1FileHashes', 1)
  pushTiming = _messages.MessageField('GoogleDevtoolsCloudbuildV1TimeSpan', 2)
  uri = _messages.StringField(3)


class GoogleDevtoolsCloudbuildV1UploadedMavenArtifact(_messages.Message):
  r"""A Maven artifact uploaded using the MavenArtifact directive.

  Fields:
    fileHashes: Hash types and values of the Maven Artifact.
    pushTiming: Output only. Stores timing information for pushing the
      specified artifact.
    uri: URI of the uploaded artifact.
  """

  fileHashes = _messages.MessageField('GoogleDevtoolsCloudbuildV1FileHashes', 1)
  pushTiming = _messages.MessageField('GoogleDevtoolsCloudbuildV1TimeSpan', 2)
  uri = _messages.StringField(3)


class GoogleDevtoolsCloudbuildV1UploadedNpmPackage(_messages.Message):
  r"""An npm package uploaded to Artifact Registry using the NpmPackage
  directive.

  Fields:
    fileHashes: Hash types and values of the npm package.
    pushTiming: Output only. Stores timing information for pushing the
      specified artifact.
    uri: URI of the uploaded npm package.
  """

  fileHashes = _messages.MessageField('GoogleDevtoolsCloudbuildV1FileHashes', 1)
  pushTiming = _messages.MessageField('GoogleDevtoolsCloudbuildV1TimeSpan', 2)
  uri = _messages.StringField(3)


class GoogleDevtoolsCloudbuildV1UploadedPythonPackage(_messages.Message):
  r"""Artifact uploaded using the PythonPackage directive.

  Fields:
    fileHashes: Hash types and values of the Python Artifact.
    pushTiming: Output only. Stores timing information for pushing the
      specified artifact.
    uri: URI of the uploaded artifact.
  """

  fileHashes = _messages.MessageField('GoogleDevtoolsCloudbuildV1FileHashes', 1)
  pushTiming = _messages.MessageField('GoogleDevtoolsCloudbuildV1TimeSpan', 2)
  uri = _messages.StringField(3)


class GoogleDevtoolsCloudbuildV1Volume(_messages.Message):
  r"""Volume describes a Docker container volume which is mounted into build
  steps in order to persist files across build step execution.

  Fields:
    name: Name of the volume to mount. Volume names must be unique per build
      step and must be valid names for Docker volumes. Each named volume must
      be used by at least two build steps.
    path: Path at which to mount the volume. Paths must be absolute and cannot
      conflict with other volume paths on the same build step or with certain
      reserved volume paths.
  """

  name = _messages.StringField(1)
  path = _messages.StringField(2)


class GoogleDevtoolsCloudbuildV1Warning(_messages.Message):
  r"""A non-fatal problem encountered during the execution of the build.

  Enums:
    PriorityValueValuesEnum: The priority for this warning.

  Fields:
    priority: The priority for this warning.
    text: Explanation of the warning generated.
  """

  class PriorityValueValuesEnum(_messages.Enum):
    r"""The priority for this warning.

    Values:
      PRIORITY_UNSPECIFIED: Should not be used.
      INFO: e.g. deprecation warnings and alternative feature highlights.
      WARNING: e.g. automated detection of possible issues with the build.
      ALERT: e.g. alerts that a feature used in the build is pending removal
    """
    PRIORITY_UNSPECIFIED = 0
    INFO = 1
    WARNING = 2
    ALERT = 3

  priority = _messages.EnumField('PriorityValueValuesEnum', 1)
  text = _messages.StringField(2)


class GoogleIamV1AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('GoogleIamV1AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class GoogleIamV1AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class GoogleIamV1Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('GoogleTypeExpr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class GoogleIamV1Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('GoogleIamV1AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('GoogleIamV1Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleIamV1SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('GoogleIamV1Policy', 1)
  updateMask = _messages.StringField(2)


class GoogleIamV1TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class GoogleIamV1TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class GoogleLongrunningListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('GoogleLongrunningOperation', 2, repeated=True)


class GoogleLongrunningOperation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('GoogleRpcStatus', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class GoogleLongrunningWaitOperationRequest(_messages.Message):
  r"""The request message for Operations.WaitOperation.

  Fields:
    timeout: The maximum duration to wait before timing out. If left blank,
      the wait will be at most the time permitted by the underlying HTTP/RPC
      protocol. If RPC context deadline is also specified, the shorter one
      will be used.
  """

  timeout = _messages.StringField(1)


class GoogleProtobufEmpty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class GoogleRpcStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class GoogleTypeExpr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class Proto2BridgeMessageSet(_messages.Message):
  r"""This is proto2's version of MessageSet. DEPRECATED: DO NOT USE FOR NEW
  FIELDS. If you are using editions or proto2, please make your own extendable
  messages for your use case. If you are using proto3, please use `Any`
  instead. MessageSet was the implementation of extensions for proto1. When
  proto2 was introduced, extensions were implemented as a first-class feature.
  This schema for MessageSet was meant to be a "bridge" solution to migrate
  MessageSet-bearing messages from proto1 to proto2. This schema has been
  open-sourced only to facilitate the migration of Google products with
  MessageSet-bearing messages to open-source environments.
  """



class RunProjectsLocationsBuildsSubmitRequest(_messages.Message):
  r"""A RunProjectsLocationsBuildsSubmitRequest object.

  Fields:
    googleCloudRunV2SubmitBuildRequest: A GoogleCloudRunV2SubmitBuildRequest
      resource to be passed as the request body.
    parent: Required. The project and location to build in. Location must be a
      region, e.g., 'us-central1' or 'global' if the global builder is to be
      used. Format: `projects/{project}/locations/{location}`
  """

  googleCloudRunV2SubmitBuildRequest = _messages.MessageField('GoogleCloudRunV2SubmitBuildRequest', 1)
  parent = _messages.StringField(2, required=True)


class RunProjectsLocationsExportImageMetadataRequest(_messages.Message):
  r"""A RunProjectsLocationsExportImageMetadataRequest object.

  Fields:
    name: Required. The name of the resource of which image metadata should be
      exported. Format: `projects/{project_id_or_number}/locations/{location}/
      services/{service}/revisions/{revision}` for Revision `projects/{project
      _id_or_number}/locations/{location}/jobs/{job}/executions/{execution}`
      for Execution
  """

  name = _messages.StringField(1, required=True)


class RunProjectsLocationsExportImageRequest(_messages.Message):
  r"""A RunProjectsLocationsExportImageRequest object.

  Fields:
    googleCloudRunV2ExportImageRequest: A GoogleCloudRunV2ExportImageRequest
      resource to be passed as the request body.
    name: Required. The name of the resource of which image metadata should be
      exported. Format: `projects/{project_id_or_number}/locations/{location}/
      services/{service}/revisions/{revision}` for Revision `projects/{project
      _id_or_number}/locations/{location}/jobs/{job}/executions/{execution}`
      for Execution
  """

  googleCloudRunV2ExportImageRequest = _messages.MessageField('GoogleCloudRunV2ExportImageRequest', 1)
  name = _messages.StringField(2, required=True)


class RunProjectsLocationsExportMetadataRequest(_messages.Message):
  r"""A RunProjectsLocationsExportMetadataRequest object.

  Fields:
    name: Required. The name of the resource of which metadata should be
      exported. Format: `projects/{project_id_or_number}/locations/{location}/
      services/{service}` for Service `projects/{project_id_or_number}/locatio
      ns/{location}/services/{service}/revisions/{revision}` for Revision `pro
      jects/{project_id_or_number}/locations/{location}/jobs/{job}/executions/
      {execution}` for Execution {project_id_or_number} may contains domain-
      scoped project IDs
  """

  name = _messages.StringField(1, required=True)


class RunProjectsLocationsExportProjectMetadataRequest(_messages.Message):
  r"""A RunProjectsLocationsExportProjectMetadataRequest object.

  Fields:
    name: Required. The name of the project of which metadata should be
      exported. Format: `projects/{project_id_or_number}/locations/{location}`
      for Project in a given location.
  """

  name = _messages.StringField(1, required=True)


class RunProjectsLocationsJobsCreateRequest(_messages.Message):
  r"""A RunProjectsLocationsJobsCreateRequest object.

  Fields:
    googleCloudRunV2Job: A GoogleCloudRunV2Job resource to be passed as the
      request body.
    jobId: Required. The unique identifier for the Job. The name of the job
      becomes {parent}/jobs/{job_id}.
    parent: Required. The location and project in which this Job should be
      created. Format: projects/{project}/locations/{location}, where
      {project} can be project id or number.
    validateOnly: Indicates that the request should be validated and default
      values populated, without persisting the request or creating any
      resources.
  """

  googleCloudRunV2Job = _messages.MessageField('GoogleCloudRunV2Job', 1)
  jobId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class RunProjectsLocationsJobsDeleteRequest(_messages.Message):
  r"""A RunProjectsLocationsJobsDeleteRequest object.

  Fields:
    etag: A system-generated fingerprint for this version of the resource. May
      be used to detect modification conflict during updates.
    name: Required. The full name of the Job. Format:
      projects/{project}/locations/{location}/jobs/{job}, where {project} can
      be project id or number.
    validateOnly: Indicates that the request should be validated without
      actually deleting any resources.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class RunProjectsLocationsJobsExecutionsCancelRequest(_messages.Message):
  r"""A RunProjectsLocationsJobsExecutionsCancelRequest object.

  Fields:
    googleCloudRunV2CancelExecutionRequest: A
      GoogleCloudRunV2CancelExecutionRequest resource to be passed as the
      request body.
    name: Required. The name of the Execution to cancel. Format: `projects/{pr
      oject}/locations/{location}/jobs/{job}/executions/{execution}`, where
      `{project}` can be project id or number.
  """

  googleCloudRunV2CancelExecutionRequest = _messages.MessageField('GoogleCloudRunV2CancelExecutionRequest', 1)
  name = _messages.StringField(2, required=True)


class RunProjectsLocationsJobsExecutionsDeleteRequest(_messages.Message):
  r"""A RunProjectsLocationsJobsExecutionsDeleteRequest object.

  Fields:
    etag: A system-generated fingerprint for this version of the resource.
      This may be used to detect modification conflict during updates.
    name: Required. The name of the Execution to delete. Format: `projects/{pr
      oject}/locations/{location}/jobs/{job}/executions/{execution}`, where
      `{project}` can be project id or number.
    validateOnly: Indicates that the request should be validated without
      actually deleting any resources.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class RunProjectsLocationsJobsExecutionsExportStatusRequest(_messages.Message):
  r"""A RunProjectsLocationsJobsExecutionsExportStatusRequest object.

  Fields:
    name: Required. The name of the resource of which image export operation
      status has to be fetched. Format: `projects/{project_id_or_number}/locat
      ions/{location}/services/{service}/revisions/{revision}` for Revision `p
      rojects/{project_id_or_number}/locations/{location}/jobs/{job}/execution
      s/{execution}` for Execution
    operationId: Required. The operation id returned from ExportImage.
  """

  name = _messages.StringField(1, required=True)
  operationId = _messages.StringField(2, required=True)


class RunProjectsLocationsJobsExecutionsGetRequest(_messages.Message):
  r"""A RunProjectsLocationsJobsExecutionsGetRequest object.

  Fields:
    name: Required. The full name of the Execution. Format: `projects/{project
      }/locations/{location}/jobs/{job}/executions/{execution}`, where
      `{project}` can be project id or number.
  """

  name = _messages.StringField(1, required=True)


class RunProjectsLocationsJobsExecutionsListRequest(_messages.Message):
  r"""A RunProjectsLocationsJobsExecutionsListRequest object.

  Fields:
    pageSize: Maximum number of Executions to return in this call.
    pageToken: A page token received from a previous call to ListExecutions.
      All other parameters must match.
    parent: Required. The Execution from which the Executions should be
      listed. To list all Executions across Jobs, use "-" instead of Job name.
      Format: `projects/{project}/locations/{location}/jobs/{job}`, where
      `{project}` can be project id or number.
    showDeleted: If true, returns deleted (but unexpired) resources along with
      active ones.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  showDeleted = _messages.BooleanField(4)


class RunProjectsLocationsJobsExecutionsTasksGetRequest(_messages.Message):
  r"""A RunProjectsLocationsJobsExecutionsTasksGetRequest object.

  Fields:
    name: Required. The full name of the Task. Format: projects/{project}/loca
      tions/{location}/jobs/{job}/executions/{execution}/tasks/{task}
  """

  name = _messages.StringField(1, required=True)


class RunProjectsLocationsJobsExecutionsTasksListRequest(_messages.Message):
  r"""A RunProjectsLocationsJobsExecutionsTasksListRequest object.

  Fields:
    pageSize: Maximum number of Tasks to return in this call.
    pageToken: A page token received from a previous call to ListTasks. All
      other parameters must match.
    parent: Required. The Execution from which the Tasks should be listed. To
      list all Tasks across Executions of a Job, use "-" instead of Execution
      name. To list all Tasks across Jobs, use "-" instead of Job name.
      Format: projects/{project}/locations/{location}/jobs/{job}/executions/{e
      xecution}
    showDeleted: If true, returns deleted (but unexpired) resources along with
      active ones.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  showDeleted = _messages.BooleanField(4)


class RunProjectsLocationsJobsGetIamPolicyRequest(_messages.Message):
  r"""A RunProjectsLocationsJobsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class RunProjectsLocationsJobsGetRequest(_messages.Message):
  r"""A RunProjectsLocationsJobsGetRequest object.

  Fields:
    name: Required. The full name of the Job. Format:
      projects/{project}/locations/{location}/jobs/{job}, where {project} can
      be project id or number.
  """

  name = _messages.StringField(1, required=True)


class RunProjectsLocationsJobsListRequest(_messages.Message):
  r"""A RunProjectsLocationsJobsListRequest object.

  Fields:
    pageSize: Maximum number of Jobs to return in this call.
    pageToken: A page token received from a previous call to ListJobs. All
      other parameters must match.
    parent: Required. The location and project to list resources on. Format:
      projects/{project}/locations/{location}, where {project} can be project
      id or number.
    showDeleted: If true, returns deleted (but unexpired) resources along with
      active ones.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  showDeleted = _messages.BooleanField(4)


class RunProjectsLocationsJobsPatchRequest(_messages.Message):
  r"""A RunProjectsLocationsJobsPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, and if the Job does not exist, it
      will create a new one. Caller must have both create and update
      permissions for this call if this is set to true.
    googleCloudRunV2Job: A GoogleCloudRunV2Job resource to be passed as the
      request body.
    name: The fully qualified name of this Job. Format:
      projects/{project}/locations/{location}/jobs/{job}
    validateOnly: Indicates that the request should be validated and default
      values populated, without persisting the request or updating any
      resources.
  """

  allowMissing = _messages.BooleanField(1)
  googleCloudRunV2Job = _messages.MessageField('GoogleCloudRunV2Job', 2)
  name = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class RunProjectsLocationsJobsRunRequest(_messages.Message):
  r"""A RunProjectsLocationsJobsRunRequest object.

  Fields:
    googleCloudRunV2RunJobRequest: A GoogleCloudRunV2RunJobRequest resource to
      be passed as the request body.
    name: Required. The full name of the Job. Format:
      projects/{project}/locations/{location}/jobs/{job}, where {project} can
      be project id or number.
  """

  googleCloudRunV2RunJobRequest = _messages.MessageField('GoogleCloudRunV2RunJobRequest', 1)
  name = _messages.StringField(2, required=True)


class RunProjectsLocationsJobsSetIamPolicyRequest(_messages.Message):
  r"""A RunProjectsLocationsJobsSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class RunProjectsLocationsJobsTestIamPermissionsRequest(_messages.Message):
  r"""A RunProjectsLocationsJobsTestIamPermissionsRequest object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class RunProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A RunProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class RunProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A RunProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class RunProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A RunProjectsLocationsOperationsListRequest object.

  Fields:
    filter: Optional. A filter for matching the completed or in-progress
      operations. The supported formats of *filter* are: To query for only
      completed operations: done:true To query for only ongoing operations:
      done:false Must be empty to query for all of the latest operations for
      the given parent project.
    name: Required. To query for all of the operations for a project.
    pageSize: The maximum number of records that should be returned. Requested
      page size cannot exceed 100. If not set or set to less than or equal to
      0, the default page size is 100. .
    pageToken: Token identifying which result to start with, which is returned
      by a previous list call.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class RunProjectsLocationsOperationsWaitRequest(_messages.Message):
  r"""A RunProjectsLocationsOperationsWaitRequest object.

  Fields:
    googleLongrunningWaitOperationRequest: A
      GoogleLongrunningWaitOperationRequest resource to be passed as the
      request body.
    name: The name of the operation resource to wait on.
  """

  googleLongrunningWaitOperationRequest = _messages.MessageField('GoogleLongrunningWaitOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class RunProjectsLocationsServicesCreateRequest(_messages.Message):
  r"""A RunProjectsLocationsServicesCreateRequest object.

  Fields:
    googleCloudRunV2Service: A GoogleCloudRunV2Service resource to be passed
      as the request body.
    parent: Required. The location and project in which this service should be
      created. Format: projects/{project}/locations/{location}, where
      {project} can be project id or number. Only lowercase characters,
      digits, and hyphens.
    serviceId: Required. The unique identifier for the Service. It must begin
      with letter, and cannot end with hyphen; must contain fewer than 50
      characters. The name of the service becomes
      {parent}/services/{service_id}.
    validateOnly: Indicates that the request should be validated and default
      values populated, without persisting the request or creating any
      resources.
  """

  googleCloudRunV2Service = _messages.MessageField('GoogleCloudRunV2Service', 1)
  parent = _messages.StringField(2, required=True)
  serviceId = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class RunProjectsLocationsServicesDeleteRequest(_messages.Message):
  r"""A RunProjectsLocationsServicesDeleteRequest object.

  Fields:
    etag: A system-generated fingerprint for this version of the resource. May
      be used to detect modification conflict during updates.
    name: Required. The full name of the Service. Format:
      projects/{project}/locations/{location}/services/{service}, where
      {project} can be project id or number.
    validateOnly: Indicates that the request should be validated without
      actually deleting any resources.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class RunProjectsLocationsServicesGetIamPolicyRequest(_messages.Message):
  r"""A RunProjectsLocationsServicesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class RunProjectsLocationsServicesGetRequest(_messages.Message):
  r"""A RunProjectsLocationsServicesGetRequest object.

  Fields:
    name: Required. The full name of the Service. Format:
      projects/{project}/locations/{location}/services/{service}, where
      {project} can be project id or number.
  """

  name = _messages.StringField(1, required=True)


class RunProjectsLocationsServicesListRequest(_messages.Message):
  r"""A RunProjectsLocationsServicesListRequest object.

  Fields:
    pageSize: Maximum number of Services to return in this call.
    pageToken: A page token received from a previous call to ListServices. All
      other parameters must match.
    parent: Required. The location and project to list resources on. Location
      must be a valid Google Cloud region, and cannot be the "-" wildcard.
      Format: projects/{project}/locations/{location}, where {project} can be
      project id or number.
    showDeleted: If true, returns deleted (but unexpired) resources along with
      active ones.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  showDeleted = _messages.BooleanField(4)


class RunProjectsLocationsServicesPatchRequest(_messages.Message):
  r"""A RunProjectsLocationsServicesPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, and if the Service does not exist,
      it will create a new one. The caller must have 'run.services.create'
      permissions if this is set to true and the Service does not exist.
    googleCloudRunV2Service: A GoogleCloudRunV2Service resource to be passed
      as the request body.
    name: The fully qualified name of this Service. In CreateServiceRequest,
      this field is ignored, and instead composed from
      CreateServiceRequest.parent and CreateServiceRequest.service_id. Format:
      projects/{project}/locations/{location}/services/{service_id}
    updateMask: Optional. The list of fields to be updated.
    validateOnly: Indicates that the request should be validated and default
      values populated, without persisting the request or updating any
      resources.
  """

  allowMissing = _messages.BooleanField(1)
  googleCloudRunV2Service = _messages.MessageField('GoogleCloudRunV2Service', 2)
  name = _messages.StringField(3, required=True)
  updateMask = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class RunProjectsLocationsServicesRevisionsDeleteRequest(_messages.Message):
  r"""A RunProjectsLocationsServicesRevisionsDeleteRequest object.

  Fields:
    etag: A system-generated fingerprint for this version of the resource.
      This may be used to detect modification conflict during updates.
    name: Required. The name of the Revision to delete. Format: projects/{proj
      ect}/locations/{location}/services/{service}/revisions/{revision}
    validateOnly: Indicates that the request should be validated without
      actually deleting any resources.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class RunProjectsLocationsServicesRevisionsExportStatusRequest(_messages.Message):
  r"""A RunProjectsLocationsServicesRevisionsExportStatusRequest object.

  Fields:
    name: Required. The name of the resource of which image export operation
      status has to be fetched. Format: `projects/{project_id_or_number}/locat
      ions/{location}/services/{service}/revisions/{revision}` for Revision `p
      rojects/{project_id_or_number}/locations/{location}/jobs/{job}/execution
      s/{execution}` for Execution
    operationId: Required. The operation id returned from ExportImage.
  """

  name = _messages.StringField(1, required=True)
  operationId = _messages.StringField(2, required=True)


class RunProjectsLocationsServicesRevisionsGetRequest(_messages.Message):
  r"""A RunProjectsLocationsServicesRevisionsGetRequest object.

  Fields:
    name: Required. The full name of the Revision. Format: projects/{project}/
      locations/{location}/services/{service}/revisions/{revision}
  """

  name = _messages.StringField(1, required=True)


class RunProjectsLocationsServicesRevisionsListRequest(_messages.Message):
  r"""A RunProjectsLocationsServicesRevisionsListRequest object.

  Fields:
    pageSize: Maximum number of revisions to return in this call.
    pageToken: A page token received from a previous call to ListRevisions.
      All other parameters must match.
    parent: Required. The Service from which the Revisions should be listed.
      To list all Revisions across Services, use "-" instead of Service name.
      Format: projects/{project}/locations/{location}/services/{service}
    showDeleted: If true, returns deleted (but unexpired) resources along with
      active ones.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  showDeleted = _messages.BooleanField(4)


class RunProjectsLocationsServicesSetIamPolicyRequest(_messages.Message):
  r"""A RunProjectsLocationsServicesSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class RunProjectsLocationsServicesTestIamPermissionsRequest(_messages.Message):
  r"""A RunProjectsLocationsServicesTestIamPermissionsRequest object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class RunProjectsLocationsWorkerPoolsCreateRequest(_messages.Message):
  r"""A RunProjectsLocationsWorkerPoolsCreateRequest object.

  Fields:
    googleCloudRunV2WorkerPool: A GoogleCloudRunV2WorkerPool resource to be
      passed as the request body.
    parent: Required. The location and project in which this worker pool
      should be created. Format: `projects/{project}/locations/{location}`,
      where `{project}` can be project id or number. Only lowercase
      characters, digits, and hyphens.
    validateOnly: Optional. Indicates that the request should be validated and
      default values populated, without persisting the request or creating any
      resources.
    workerPoolId: Required. The unique identifier for the WorkerPool. It must
      begin with letter, and cannot end with hyphen; must contain fewer than
      50 characters. The name of the worker pool becomes
      `{parent}/workerPools/{worker_pool_id}`.
  """

  googleCloudRunV2WorkerPool = _messages.MessageField('GoogleCloudRunV2WorkerPool', 1)
  parent = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)
  workerPoolId = _messages.StringField(4)


class RunProjectsLocationsWorkerPoolsDeleteRequest(_messages.Message):
  r"""A RunProjectsLocationsWorkerPoolsDeleteRequest object.

  Fields:
    etag: A system-generated fingerprint for this version of the resource. May
      be used to detect modification conflict during updates.
    name: Required. The full name of the WorkerPool. Format:
      `projects/{project}/locations/{location}/workerPools/{worker_pool}`,
      where `{project}` can be project id or number.
    validateOnly: Optional. Indicates that the request should be validated
      without actually deleting any resources.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class RunProjectsLocationsWorkerPoolsGetIamPolicyRequest(_messages.Message):
  r"""A RunProjectsLocationsWorkerPoolsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class RunProjectsLocationsWorkerPoolsGetRequest(_messages.Message):
  r"""A RunProjectsLocationsWorkerPoolsGetRequest object.

  Fields:
    name: Required. The full name of the WorkerPool. Format:
      `projects/{project}/locations/{location}/workerPools/{worker_pool}`,
      where `{project}` can be project id or number.
  """

  name = _messages.StringField(1, required=True)


class RunProjectsLocationsWorkerPoolsListRequest(_messages.Message):
  r"""A RunProjectsLocationsWorkerPoolsListRequest object.

  Fields:
    pageSize: Maximum number of WorkerPools to return in this call.
    pageToken: A page token received from a previous call to ListWorkerPools.
      All other parameters must match.
    parent: Required. The location and project to list resources on. Location
      must be a valid Google Cloud region, and cannot be the "-" wildcard.
      Format: `projects/{project}/locations/{location}`, where `{project}` can
      be project id or number.
    showDeleted: If true, returns deleted (but unexpired) resources along with
      active ones.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  showDeleted = _messages.BooleanField(4)


class RunProjectsLocationsWorkerPoolsPatchRequest(_messages.Message):
  r"""A RunProjectsLocationsWorkerPoolsPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, and if the WorkerPool does not
      exist, it will create a new one. The caller must have
      'run.workerpools.create' permissions if this is set to true and the
      WorkerPool does not exist.
    forceNewRevision: Optional. If set to true, a new revision will be created
      from the template even if the system doesn't detect any changes from the
      previously deployed revision. This may be useful for cases where the
      underlying resources need to be recreated or reinitialized. For example
      if the image is specified by label, but the underlying image digest has
      changed) or if the container performs deployment initialization work
      that needs to be performed again.
    googleCloudRunV2WorkerPool: A GoogleCloudRunV2WorkerPool resource to be
      passed as the request body.
    name: The fully qualified name of this WorkerPool. In
      CreateWorkerPoolRequest, this field is ignored, and instead composed
      from CreateWorkerPoolRequest.parent and
      CreateWorkerPoolRequest.worker_id. Format:
      `projects/{project}/locations/{location}/workerPools/{worker_id}`
    updateMask: Optional. The list of fields to be updated.
    validateOnly: Optional. Indicates that the request should be validated and
      default values populated, without persisting the request or updating any
      resources.
  """

  allowMissing = _messages.BooleanField(1)
  forceNewRevision = _messages.BooleanField(2)
  googleCloudRunV2WorkerPool = _messages.MessageField('GoogleCloudRunV2WorkerPool', 3)
  name = _messages.StringField(4, required=True)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class RunProjectsLocationsWorkerPoolsRevisionsDeleteRequest(_messages.Message):
  r"""A RunProjectsLocationsWorkerPoolsRevisionsDeleteRequest object.

  Fields:
    etag: A system-generated fingerprint for this version of the resource.
      This may be used to detect modification conflict during updates.
    name: Required. The name of the Revision to delete. Format: projects/{proj
      ect}/locations/{location}/services/{service}/revisions/{revision}
    validateOnly: Indicates that the request should be validated without
      actually deleting any resources.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class RunProjectsLocationsWorkerPoolsRevisionsGetRequest(_messages.Message):
  r"""A RunProjectsLocationsWorkerPoolsRevisionsGetRequest object.

  Fields:
    name: Required. The full name of the Revision. Format: projects/{project}/
      locations/{location}/services/{service}/revisions/{revision}
  """

  name = _messages.StringField(1, required=True)


class RunProjectsLocationsWorkerPoolsRevisionsListRequest(_messages.Message):
  r"""A RunProjectsLocationsWorkerPoolsRevisionsListRequest object.

  Fields:
    pageSize: Maximum number of revisions to return in this call.
    pageToken: A page token received from a previous call to ListRevisions.
      All other parameters must match.
    parent: Required. The Service from which the Revisions should be listed.
      To list all Revisions across Services, use "-" instead of Service name.
      Format: projects/{project}/locations/{location}/services/{service}
    showDeleted: If true, returns deleted (but unexpired) resources along with
      active ones.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  showDeleted = _messages.BooleanField(4)


class RunProjectsLocationsWorkerPoolsSetIamPolicyRequest(_messages.Message):
  r"""A RunProjectsLocationsWorkerPoolsSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class RunProjectsLocationsWorkerPoolsTestIamPermissionsRequest(_messages.Message):
  r"""A RunProjectsLocationsWorkerPoolsTestIamPermissionsRequest object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class UtilStatusProto(_messages.Message):
  r"""Wire-format for a Status object

  Fields:
    canonicalCode: copybara:strip_begin(b/383363683)
      copybara:strip_end_and_replace optional int32 canonical_code = 6;
    code: Numeric code drawn from the space specified below. Often, this is
      the canonical error space, and code is drawn from
      google3/util/task/codes.proto copybara:strip_begin(b/383363683)
      copybara:strip_end_and_replace optional int32 code = 1;
    message: Detail message copybara:strip_begin(b/383363683)
      copybara:strip_end_and_replace optional string message = 3;
    messageSet: message_set associates an arbitrary proto message with the
      status. copybara:strip_begin(b/383363683) copybara:strip_end_and_replace
      optional proto2.bridge.MessageSet message_set = 5;
    space: copybara:strip_begin(b/383363683) Space to which this status
      belongs copybara:strip_end_and_replace optional string space = 2; //
      Space to which this status belongs
  """

  canonicalCode = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  code = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  message = _messages.StringField(3)
  messageSet = _messages.MessageField('Proto2BridgeMessageSet', 4)
  space = _messages.StringField(5)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    RunProjectsLocationsJobsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    RunProjectsLocationsServicesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    RunProjectsLocationsWorkerPoolsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
