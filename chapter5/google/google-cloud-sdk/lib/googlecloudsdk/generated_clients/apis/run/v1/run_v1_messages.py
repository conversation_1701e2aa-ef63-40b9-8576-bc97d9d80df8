"""Generated message classes for run version v1.

Deploy and manage user provided container images that scale automatically
based on incoming requests. The Cloud Run Admin API v1 follows the Knative
Serving API specification, while v2 is aligned with Google Cloud AIP-based API
standards, as described in https://google.aip.dev/.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'run'


class Addressable(_messages.Message):
  r"""Information for connecting over HTTP(s).

  Fields:
    url: A string attribute.
  """

  url = _messages.StringField(1)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class AuthorizedDomain(_messages.Message):
  r"""A domain that a user has been authorized to administer. To authorize use
  of a domain, verify ownership via [Search
  Console](https://search.google.com/search-console/welcome).

  Fields:
    id: Relative name of the domain authorized for use. Example:
      `example.com`.
    name: Deprecated Read only. Full path to the `AuthorizedDomain` resource
      in the API. Example: `projects/myproject/authorizedDomains/example.com`.
  """

  id = _messages.StringField(1)
  name = _messages.StringField(2)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class CSIVolumeSource(_messages.Message):
  r"""Storage volume source using the Container Storage Interface.

  Messages:
    VolumeAttributesValue: stores driver specific attributes. For Google Cloud
      Storage volumes, the following attributes are supported: * bucketName:
      the name of the Cloud Storage bucket to mount. The Cloud Run Service
      identity must have access to this bucket.

  Fields:
    driver: name of the CSI driver for the requested storage system. Cloud Run
      supports the following drivers: * gcsfuse.run.googleapis.com : Mount a
      Cloud Storage Bucket as a volume.
    readOnly: If true, mount the volume as read only. Defaults to false.
    volumeAttributes: stores driver specific attributes. For Google Cloud
      Storage volumes, the following attributes are supported: * bucketName:
      the name of the Cloud Storage bucket to mount. The Cloud Run Service
      identity must have access to this bucket.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class VolumeAttributesValue(_messages.Message):
    r"""stores driver specific attributes. For Google Cloud Storage volumes,
    the following attributes are supported: * bucketName: the name of the
    Cloud Storage bucket to mount. The Cloud Run Service identity must have
    access to this bucket.

    Messages:
      AdditionalProperty: An additional property for a VolumeAttributesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        VolumeAttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a VolumeAttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  driver = _messages.StringField(1)
  readOnly = _messages.BooleanField(2)
  volumeAttributes = _messages.MessageField('VolumeAttributesValue', 3)


class CancelExecutionRequest(_messages.Message):
  r"""Request message for cancelling an execution."""


class ConfigMapEnvSource(_messages.Message):
  r"""Not supported by Cloud Run. ConfigMapEnvSource selects a ConfigMap to
  populate the environment variables with. The contents of the target
  ConfigMap's Data field will represent the key-value pairs as environment
  variables.

  Fields:
    localObjectReference: This field should not be used directly as it is
      meant to be inlined directly into the message. Use the "name" field
      instead.
    name: The ConfigMap to select from.
    optional: Specify whether the ConfigMap must be defined.
  """

  localObjectReference = _messages.MessageField('LocalObjectReference', 1)
  name = _messages.StringField(2)
  optional = _messages.BooleanField(3)


class ConfigMapKeySelector(_messages.Message):
  r"""Not supported by Cloud Run.

  Fields:
    key: Required. Not supported by Cloud Run.
    localObjectReference: Not supported by Cloud Run.
    name: Required. Not supported by Cloud Run.
    optional: Not supported by Cloud Run.
  """

  key = _messages.StringField(1)
  localObjectReference = _messages.MessageField('LocalObjectReference', 2)
  name = _messages.StringField(3)
  optional = _messages.BooleanField(4)


class ConfigMapVolumeSource(_messages.Message):
  r"""Not supported by Cloud Run. Adapts a ConfigMap into a volume. The
  contents of the target ConfigMap's Data field will be presented in a volume
  as files using the keys in the Data field as the file names, unless the
  items element is populated with specific mappings of keys to paths.

  Fields:
    defaultMode: (Optional) Integer representation of mode bits to use on
      created files by default. Must be a value between 01 and 0777 (octal).
      If 0 or not set, it will default to 0644. Directories within the path
      are not affected by this setting. Notes * Internally, a umask of 0222
      will be applied to any non-zero value. * This is an integer
      representation of the mode bits. So, the octal integer value should look
      exactly as the chmod numeric notation with a leading zero. Some
      examples: for chmod 777 (a=rwx), set to 0777 (octal) or 511 (base-10).
      For chmod 640 (u=rw,g=r), set to 0640 (octal) or 416 (base-10). For
      chmod 755 (u=rwx,g=rx,o=rx), set to 0755 (octal) or 493 (base-10). *
      This might be in conflict with other options that affect the file mode,
      like fsGroup, and the result can be other mode bits set.
    items: (Optional) If unspecified, each key-value pair in the Data field of
      the referenced Secret will be projected into the volume as a file whose
      name is the key and content is the value. If specified, the listed keys
      will be projected into the specified paths, and unlisted keys will not
      be present. If a key is specified that is not present in the Secret, the
      volume setup will error unless it is marked optional.
    name: Name of the config.
    optional: (Optional) Specify whether the Secret or its keys must be
      defined.
  """

  defaultMode = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  items = _messages.MessageField('KeyToPath', 2, repeated=True)
  name = _messages.StringField(3)
  optional = _messages.BooleanField(4)


class Configuration(_messages.Message):
  r"""Configuration represents the "floating HEAD" of a linear history of
  Revisions, and optionally how the containers those revisions reference are
  built. Users create new Revisions by updating the Configuration's spec. The
  "latest created" revision's name is available under status, as is the
  "latest ready" revision's name.

  Fields:
    apiVersion: The API version for this call such as
      "serving.knative.dev/v1".
    kind: The kind of resource, in this case always "Configuration".
    metadata: Metadata associated with this Configuration, including name,
      namespace, labels, and annotations.
    spec: Spec holds the desired state of the Configuration (from the client).
    status: Status communicates the observed state of the Configuration (from
      the controller).
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  metadata = _messages.MessageField('ObjectMeta', 3)
  spec = _messages.MessageField('ConfigurationSpec', 4)
  status = _messages.MessageField('ConfigurationStatus', 5)


class ConfigurationSpec(_messages.Message):
  r"""ConfigurationSpec holds the desired state of the Configuration (from the
  client).

  Fields:
    template: Template holds the latest specification for the Revision to be
      stamped out.
  """

  template = _messages.MessageField('RevisionTemplate', 1)


class ConfigurationStatus(_messages.Message):
  r"""ConfigurationStatus communicates the observed state of the Configuration
  (from the controller).

  Fields:
    conditions: Conditions communicate information about ongoing/complete
      reconciliation processes that bring the "spec" inline with the observed
      state of the world.
    latestCreatedRevisionName: LatestCreatedRevisionName is the last revision
      that was created from this Configuration. It might not be ready yet, so
      for the latest ready revision, use LatestReadyRevisionName.
    latestReadyRevisionName: LatestReadyRevisionName holds the name of the
      latest Revision stamped out from this Configuration that has had its
      "Ready" condition become "True".
    observedGeneration: ObservedGeneration is the 'Generation' of the
      Configuration that was last processed by the controller. The observed
      generation is updated even if the controller failed to process the spec
      and create the Revision. Clients polling for completed reconciliation
      should poll until observedGeneration = metadata.generation, and the
      Ready condition's status is True or False.
  """

  conditions = _messages.MessageField('GoogleCloudRunV1Condition', 1, repeated=True)
  latestCreatedRevisionName = _messages.StringField(2)
  latestReadyRevisionName = _messages.StringField(3)
  observedGeneration = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class Container(_messages.Message):
  r"""A single application container. This specifies both the container to
  run, the command to run in the container and the arguments to supply to it.
  Note that additional arguments may be supplied by the system to the
  container at runtime.

  Fields:
    args: Arguments to the entrypoint. The docker image's CMD is used if this
      is not provided. Variable references are not supported in Cloud Run.
    command: Entrypoint array. Not executed within a shell. The docker image's
      ENTRYPOINT is used if this is not provided. Variable references are not
      supported in Cloud Run.
    env: List of environment variables to set in the container. EnvVar with
      duplicate names are generally allowed; if referencing a secret, the name
      must be unique for the container. For non-secret EnvVar names, the
      Container will only get the last-declared one.
    envFrom: Not supported by Cloud Run.
    image: Required. Name of the container image in Dockerhub, Google Artifact
      Registry, or Google Container Registry. If the host is not provided,
      Dockerhub is assumed.
    imagePullPolicy: Image pull policy. One of Always, Never, IfNotPresent.
      Defaults to Always if :latest tag is specified, or IfNotPresent
      otherwise.
    livenessProbe: Periodic probe of container liveness. Container will be
      restarted if the probe fails.
    name: Name of the container specified as a DNS_LABEL (RFC 1123).
    ports: List of ports to expose from the container. Only a single port can
      be specified. The specified ports must be listening on all interfaces
      (0.0.0.0) within the container to be accessible. If omitted, a port
      number will be chosen and passed to the container through the PORT
      environment variable for the container to listen on.
    readinessProbe: Readiness probe to be used for health checks.
    resources: Compute Resources required by this container.
    securityContext: Not supported by Cloud Run.
    startupProbe: Startup probe of application within the container. All other
      probes are disabled if a startup probe is provided, until it succeeds.
      Container will not receive traffic if the probe fails. If not provided,
      a default startup probe with TCP socket action is used.
    terminationMessagePath: Path at which the file to which the container's
      termination message will be written is mounted into the container's
      filesystem. Message written is intended to be brief final status, such
      as an assertion failure message. Will be truncated by the node if
      greater than 4096 bytes. The total message length across all containers
      will be limited to 12kb. Defaults to /dev/termination-log.
    terminationMessagePolicy: Indicate how the termination message should be
      populated. File will use the contents of terminationMessagePath to
      populate the container status message on both success and failure.
      FallbackToLogsOnError will use the last chunk of container log output if
      the termination message file is empty and the container exited with an
      error. The log output is limited to 2048 bytes or 80 lines, whichever is
      smaller. Defaults to File. Cannot be updated.
    volumeMounts: Volume to mount into the container's filesystem. Only
      supports SecretVolumeSources. Pod volumes to mount into the container's
      filesystem.
    workingDir: Container's working directory. If not specified, the container
      runtime's default will be used, which might be configured in the
      container image.
  """

  args = _messages.StringField(1, repeated=True)
  command = _messages.StringField(2, repeated=True)
  env = _messages.MessageField('EnvVar', 3, repeated=True)
  envFrom = _messages.MessageField('EnvFromSource', 4, repeated=True)
  image = _messages.StringField(5)
  imagePullPolicy = _messages.StringField(6)
  livenessProbe = _messages.MessageField('Probe', 7)
  name = _messages.StringField(8)
  ports = _messages.MessageField('ContainerPort', 9, repeated=True)
  readinessProbe = _messages.MessageField('Probe', 10)
  resources = _messages.MessageField('ResourceRequirements', 11)
  securityContext = _messages.MessageField('SecurityContext', 12)
  startupProbe = _messages.MessageField('Probe', 13)
  terminationMessagePath = _messages.StringField(14)
  terminationMessagePolicy = _messages.StringField(15)
  volumeMounts = _messages.MessageField('VolumeMount', 16, repeated=True)
  workingDir = _messages.StringField(17)


class ContainerOverride(_messages.Message):
  r"""Per container override specification.

  Fields:
    args: Arguments to the entrypoint. The specified arguments replace and
      override any existing entrypoint arguments. Must be empty if
      `clear_args` is set to true.
    clearArgs: Optional. Set to True to clear all existing arguments.
    env: List of environment variables to set in the container. All specified
      environment variables are merged with existing environment variables.
      When the specified environment variables exist, these values override
      any existing values.
    name: The name of the container specified as a DNS_LABEL.
  """

  args = _messages.StringField(1, repeated=True)
  clearArgs = _messages.BooleanField(2)
  env = _messages.MessageField('EnvVar', 3, repeated=True)
  name = _messages.StringField(4)


class ContainerPort(_messages.Message):
  r"""ContainerPort represents a network port in a single container.

  Fields:
    containerPort: Port number the container listens on. If present, this must
      be a valid port number, 0 < x < 65536. If not present, it will default
      to port 8080. For more information, see
      https://cloud.google.com/run/docs/container-contract#port
    name: If specified, used to specify which protocol to use. Allowed values
      are "http1" and "h2c".
    protocol: Protocol for port. Must be "TCP". Defaults to "TCP".
  """

  containerPort = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  name = _messages.StringField(2)
  protocol = _messages.StringField(3)


class DomainMapping(_messages.Message):
  r"""Resource to hold the state and status of a user's domain mapping. NOTE:
  This resource is currently in Beta.

  Fields:
    apiVersion: The API version for this call such as
      "domains.cloudrun.com/v1".
    kind: The kind of resource, in this case "DomainMapping".
    metadata: Metadata associated with this BuildTemplate.
    spec: The spec for this DomainMapping.
    status: The current status of the DomainMapping.
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  metadata = _messages.MessageField('ObjectMeta', 3)
  spec = _messages.MessageField('DomainMappingSpec', 4)
  status = _messages.MessageField('DomainMappingStatus', 5)


class DomainMappingSpec(_messages.Message):
  r"""The desired state of the Domain Mapping.

  Enums:
    CertificateModeValueValuesEnum: The mode of the certificate.

  Fields:
    certificateMode: The mode of the certificate.
    forceOverride: If set, the mapping will override any mapping set before
      this spec was set. It is recommended that the user leaves this empty to
      receive an error warning about a potential conflict and only set it once
      the respective UI has given such a warning.
    routeName: The name of the Knative Route that this DomainMapping applies
      to. The route must exist.
  """

  class CertificateModeValueValuesEnum(_messages.Enum):
    r"""The mode of the certificate.

    Values:
      CERTIFICATE_MODE_UNSPECIFIED: <no description>
      NONE: Do not provision an HTTPS certificate.
      AUTOMATIC: Automatically provisions an HTTPS certificate via GoogleCA.
    """
    CERTIFICATE_MODE_UNSPECIFIED = 0
    NONE = 1
    AUTOMATIC = 2

  certificateMode = _messages.EnumField('CertificateModeValueValuesEnum', 1)
  forceOverride = _messages.BooleanField(2)
  routeName = _messages.StringField(3)


class DomainMappingStatus(_messages.Message):
  r"""The current state of the Domain Mapping.

  Fields:
    conditions: Array of observed DomainMappingConditions, indicating the
      current state of the DomainMapping.
    mappedRouteName: The name of the route that the mapping currently points
      to.
    observedGeneration: ObservedGeneration is the 'Generation' of the
      DomainMapping that was last processed by the controller. Clients polling
      for completed reconciliation should poll until observedGeneration =
      metadata.generation and the Ready condition's status is True or False.
    resourceRecords: The resource records required to configure this domain
      mapping. These records must be added to the domain's DNS configuration
      in order to serve the application via this domain mapping.
    url: Optional. Not supported by Cloud Run.
  """

  conditions = _messages.MessageField('GoogleCloudRunV1Condition', 1, repeated=True)
  mappedRouteName = _messages.StringField(2)
  observedGeneration = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  resourceRecords = _messages.MessageField('ResourceRecord', 4, repeated=True)
  url = _messages.StringField(5)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EmptyDirVolumeSource(_messages.Message):
  r"""In memory (tmpfs) ephemeral storage. It is ephemeral in the sense that
  when the sandbox is taken down, the data is destroyed with it (it does not
  persist across sandbox runs).

  Fields:
    medium: The medium on which the data is stored. The default is "" which
      means to use the node's default medium. Must be an empty string
      (default) or Memory. More info:
      https://kubernetes.io/docs/concepts/storage/volumes#emptydir
    sizeLimit: Limit on the storage usable by this EmptyDir volume. The size
      limit is also applicable for memory medium. The maximum usage on memory
      medium EmptyDir would be the minimum value between the SizeLimit
      specified here and the sum of memory limits of all containers. The
      default is nil which means that the limit is undefined. More info:
      https://cloud.google.com/run/docs/configuring/in-memory-
      volumes#configure-volume. Info in Kubernetes:
      https://kubernetes.io/docs/concepts/storage/volumes/#emptydir
  """

  medium = _messages.StringField(1)
  sizeLimit = _messages.StringField(2)


class EnvFromSource(_messages.Message):
  r"""Not supported by Cloud Run. EnvFromSource represents the source of a set
  of ConfigMaps

  Fields:
    configMapRef: The ConfigMap to select from
    prefix: An optional identifier to prepend to each key in the ConfigMap.
      Must be a C_IDENTIFIER.
    secretRef: The Secret to select from
  """

  configMapRef = _messages.MessageField('ConfigMapEnvSource', 1)
  prefix = _messages.StringField(2)
  secretRef = _messages.MessageField('SecretEnvSource', 3)


class EnvVar(_messages.Message):
  r"""EnvVar represents an environment variable present in a Container.

  Fields:
    name: Required. Name of the environment variable.
    value: Value of the environment variable. Defaults to "". Variable
      references are not supported in Cloud Run.
    valueFrom: Source for the environment variable's value. Only supports
      secret_key_ref. Cannot be used if value is not empty.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)
  valueFrom = _messages.MessageField('EnvVarSource', 3)


class EnvVarSource(_messages.Message):
  r"""EnvVarSource represents a source for the value of an EnvVar.

  Fields:
    configMapKeyRef: Not supported by Cloud Run. Not supported in Cloud Run.
    secretKeyRef: Selects a key (version) of a secret in Secret Manager.
  """

  configMapKeyRef = _messages.MessageField('ConfigMapKeySelector', 1)
  secretKeyRef = _messages.MessageField('SecretKeySelector', 2)


class ExecAction(_messages.Message):
  r"""Not supported by Cloud Run. ExecAction describes a "run in container"
  action.

  Fields:
    command: Command is the command line to execute inside the container, the
      working directory for the command is root ('/') in the container's
      filesystem. The command is simply exec'd, it is not run inside a shell,
      so traditional shell instructions ('|', etc) won't work. To use a shell,
      you need to explicitly call out to that shell. Exit status of 0 is
      treated as live/healthy and non-zero is unhealthy.
  """

  command = _messages.StringField(1, repeated=True)


class Execution(_messages.Message):
  r"""Execution represents the configuration of a single execution. An
  execution is an immutable resource that references a container image which
  is run to completion.

  Fields:
    apiVersion: Optional. APIVersion defines the versioned schema of this
      representation of an object. Servers should convert recognized schemas
      to the latest internal value, and may reject unrecognized values.
    kind: Optional. Kind is a string value representing the REST resource this
      object represents. Servers may infer this from the endpoint the client
      submits requests to. Cannot be updated. In CamelCase.
    metadata: Optional. Standard object's metadata.
    spec: Optional. Specification of the desired behavior of an execution.
    status: Output only. Current status of an execution.
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  metadata = _messages.MessageField('ObjectMeta', 3)
  spec = _messages.MessageField('ExecutionSpec', 4)
  status = _messages.MessageField('ExecutionStatus', 5)


class ExecutionReference(_messages.Message):
  r"""Reference to an Execution. Use /Executions.GetExecution with the given
  name to get full execution including the latest status.

  Enums:
    CompletionStatusValueValuesEnum: Optional. Status for the execution
      completion.

  Fields:
    completionStatus: Optional. Status for the execution completion.
    completionTimestamp: Optional. Completion timestamp of the execution.
    creationTimestamp: Optional. Creation timestamp of the execution.
    deletionTimestamp: Optional. The read-only soft deletion timestamp of the
      execution.
    name: Optional. Name of the execution.
  """

  class CompletionStatusValueValuesEnum(_messages.Enum):
    r"""Optional. Status for the execution completion.

    Values:
      COMPLETION_STATUS_UNSPECIFIED: The default value. This value is used if
        the state is omitted.
      EXECUTION_SUCCEEDED: Job execution has succeeded.
      EXECUTION_FAILED: Job execution has failed.
      EXECUTION_RUNNING: Job execution is running normally.
      EXECUTION_PENDING: Waiting for backing resources to be provisioned.
      EXECUTION_CANCELLED: Job execution has been cancelled by the user.
    """
    COMPLETION_STATUS_UNSPECIFIED = 0
    EXECUTION_SUCCEEDED = 1
    EXECUTION_FAILED = 2
    EXECUTION_RUNNING = 3
    EXECUTION_PENDING = 4
    EXECUTION_CANCELLED = 5

  completionStatus = _messages.EnumField('CompletionStatusValueValuesEnum', 1)
  completionTimestamp = _messages.StringField(2)
  creationTimestamp = _messages.StringField(3)
  deletionTimestamp = _messages.StringField(4)
  name = _messages.StringField(5)


class ExecutionSpec(_messages.Message):
  r"""ExecutionSpec describes how the execution will look.

  Fields:
    parallelism: Optional. Specifies the maximum desired number of tasks the
      execution should run at given time. When the job is run, if this field
      is 0 or unset, the maximum possible value will be used for that
      execution. The actual number of tasks running in steady state will be
      less than this number when there are fewer tasks waiting to be
      completed, i.e. when the work left to do is less than max parallelism.
    taskCount: Optional. Specifies the desired number of tasks the execution
      should run. Setting to 1 means that parallelism is limited to 1 and the
      success of that task signals the success of the execution. Defaults to
      1.
    template: Optional. The template used to create tasks for this execution.
  """

  parallelism = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  taskCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  template = _messages.MessageField('TaskTemplateSpec', 3)


class ExecutionStatus(_messages.Message):
  r"""ExecutionStatus represents the current state of an Execution.

  Fields:
    cancelledCount: Optional. The number of tasks which reached phase
      Cancelled.
    completionTime: Optional. Represents the time that the execution was
      completed. It is not guaranteed to be set in happens-before order across
      separate operations. It is represented in RFC3339 form and is in UTC.
      +optional
    conditions: Optional. Conditions communicate information about
      ongoing/complete reconciliation processes that bring the "spec" inline
      with the observed state of the world. Execution-specific conditions
      include: * `ResourcesAvailable`: `True` when underlying resources have
      been provisioned. * `Started`: `True` when the execution has started to
      execute. * `Completed`: `True` when the execution has succeeded. `False`
      when the execution has failed.
    failedCount: Optional. The number of tasks which reached phase Failed.
    logUri: Optional. URI where logs for this execution can be found in Cloud
      Console.
    observedGeneration: Optional. The 'generation' of the execution that was
      last processed by the controller.
    retriedCount: Optional. The number of tasks which have retried at least
      once.
    runningCount: Optional. The number of actively running tasks.
    startTime: Optional. Represents the time that the execution started to
      run. It is not guaranteed to be set in happens-before order across
      separate operations. It is represented in RFC3339 form and is in UTC.
    succeededCount: Optional. The number of tasks which reached phase
      Succeeded.
  """

  cancelledCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  completionTime = _messages.StringField(2)
  conditions = _messages.MessageField('GoogleCloudRunV1Condition', 3, repeated=True)
  failedCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  logUri = _messages.StringField(5)
  observedGeneration = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  retriedCount = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  runningCount = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  startTime = _messages.StringField(9)
  succeededCount = _messages.IntegerField(10, variant=_messages.Variant.INT32)


class ExecutionTemplateSpec(_messages.Message):
  r"""ExecutionTemplateSpec describes the metadata and spec an Execution
  should have when created from a job.

  Fields:
    metadata: Optional. Optional metadata for this Execution, including labels
      and annotations. The following annotation keys set properties of the
      created execution: * `run.googleapis.com/cloudsql-instances` sets Cloud
      SQL connections. Multiple values should be comma separated. *
      `run.googleapis.com/vpc-access-connector` sets a Serverless VPC Access
      connector. * `run.googleapis.com/vpc-access-egress` sets VPC egress.
      Supported values are `all-traffic`, `all` (deprecated), and `private-
      ranges-only`. `all-traffic` and `all` provide the same functionality.
      `all` is deprecated but will continue to be supported. Prefer `all-
      traffic`.
    spec: Required. ExecutionSpec holds the desired configuration for
      executions of this job.
  """

  metadata = _messages.MessageField('ObjectMeta', 1)
  spec = _messages.MessageField('ExecutionSpec', 2)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GRPCAction(_messages.Message):
  r"""GRPCAction describes an action involving a GRPC port.

  Fields:
    port: Port number of the gRPC service. Number must be in the range 1 to
      65535.
    service: Service is the name of the service to place in the gRPC
      HealthCheckRequest. If this is not specified, the default behavior is
      defined by gRPC.
  """

  port = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  service = _messages.StringField(2)


class GoogleCloudRunV1Condition(_messages.Message):
  r"""Conditions show the status of reconciliation progress on a given
  resource. Most resource use a top-level condition type "Ready" or
  "Completed" to show overall status with other conditions to checkpoint each
  stage of reconciliation. Note that if metadata.Generation does not equal
  status.ObservedGeneration, the conditions shown may not be relevant for the
  current spec.

  Fields:
    lastTransitionTime: Optional. Last time the condition transitioned from
      one status to another.
    message: Optional. Human readable message indicating details about the
      current status.
    reason: Optional. One-word CamelCase reason for the condition's last
      transition. These are intended to be stable, unique values which the
      client may use to trigger error handling logic, whereas messages which
      may be changed later by the server.
    severity: Optional. How to interpret this condition. One of Error,
      Warning, or Info. Conditions of severity Info do not contribute to
      resource readiness.
    status: Status of the condition, one of True, False, Unknown.
    type: type is used to communicate the status of the reconciliation
      process. Types common to all resources include: * "Ready" or
      "Completed": True when the Resource is ready.
  """

  lastTransitionTime = _messages.StringField(1)
  message = _messages.StringField(2)
  reason = _messages.StringField(3)
  severity = _messages.StringField(4)
  status = _messages.StringField(5)
  type = _messages.StringField(6)


class GoogleDevtoolsCloudbuildV1ApprovalConfig(_messages.Message):
  r"""ApprovalConfig describes configuration for manual approval of a build.

  Fields:
    approvalRequired: Whether or not approval is needed. If this is set on a
      build, it will become pending when created, and will need to be
      explicitly approved to start.
  """

  approvalRequired = _messages.BooleanField(1)


class GoogleDevtoolsCloudbuildV1ApprovalResult(_messages.Message):
  r"""ApprovalResult describes the decision and associated metadata of a
  manual approval of a build.

  Enums:
    DecisionValueValuesEnum: Required. The decision of this manual approval.

  Fields:
    approvalTime: Output only. The time when the approval decision was made.
    approverAccount: Output only. Email of the user that called the
      ApproveBuild API to approve or reject a build at the time that the API
      was called.
    comment: Optional. An optional comment for this manual approval result.
    decision: Required. The decision of this manual approval.
    url: Optional. An optional URL tied to this manual approval result. This
      field is essentially the same as comment, except that it will be
      rendered by the UI differently. An example use case is a link to an
      external job that approved this Build.
  """

  class DecisionValueValuesEnum(_messages.Enum):
    r"""Required. The decision of this manual approval.

    Values:
      DECISION_UNSPECIFIED: Default enum type. This should not be used.
      APPROVED: Build is approved.
      REJECTED: Build is rejected.
    """
    DECISION_UNSPECIFIED = 0
    APPROVED = 1
    REJECTED = 2

  approvalTime = _messages.StringField(1)
  approverAccount = _messages.StringField(2)
  comment = _messages.StringField(3)
  decision = _messages.EnumField('DecisionValueValuesEnum', 4)
  url = _messages.StringField(5)


class GoogleDevtoolsCloudbuildV1ArtifactObjects(_messages.Message):
  r"""Files in the workspace to upload to Cloud Storage upon successful
  completion of all build steps.

  Fields:
    location: Cloud Storage bucket and optional object path, in the form
      "gs://bucket/path/to/somewhere/". (see [Bucket Name
      Requirements](https://cloud.google.com/storage/docs/bucket-
      naming#requirements)). Files in the workspace matching any path pattern
      will be uploaded to Cloud Storage with this location as a prefix.
    paths: Path globs used to match files in the build's workspace.
    timing: Output only. Stores timing information for pushing all artifact
      objects.
  """

  location = _messages.StringField(1)
  paths = _messages.StringField(2, repeated=True)
  timing = _messages.MessageField('GoogleDevtoolsCloudbuildV1TimeSpan', 3)


class GoogleDevtoolsCloudbuildV1Artifacts(_messages.Message):
  r"""Artifacts produced by a build that should be uploaded upon successful
  completion of all build steps.

  Fields:
    goModules: Optional. A list of Go modules to be uploaded to Artifact
      Registry upon successful completion of all build steps. If any objects
      fail to be pushed, the build is marked FAILURE.
    images: A list of images to be pushed upon the successful completion of
      all build steps. The images will be pushed using the builder service
      account's credentials. The digests of the pushed images will be stored
      in the Build resource's results field. If any of the images fail to be
      pushed, the build is marked FAILURE.
    mavenArtifacts: A list of Maven artifacts to be uploaded to Artifact
      Registry upon successful completion of all build steps. Artifacts in the
      workspace matching specified paths globs will be uploaded to the
      specified Artifact Registry repository using the builder service
      account's credentials. If any artifacts fail to be pushed, the build is
      marked FAILURE.
    npmPackages: A list of npm packages to be uploaded to Artifact Registry
      upon successful completion of all build steps. Npm packages in the
      specified paths will be uploaded to the specified Artifact Registry
      repository using the builder service account's credentials. If any
      packages fail to be pushed, the build is marked FAILURE.
    objects: A list of objects to be uploaded to Cloud Storage upon successful
      completion of all build steps. Files in the workspace matching specified
      paths globs will be uploaded to the specified Cloud Storage location
      using the builder service account's credentials. The location and
      generation of the uploaded objects will be stored in the Build
      resource's results field. If any objects fail to be pushed, the build is
      marked FAILURE.
    pythonPackages: A list of Python packages to be uploaded to Artifact
      Registry upon successful completion of all build steps. The build
      service account credentials will be used to perform the upload. If any
      objects fail to be pushed, the build is marked FAILURE.
  """

  goModules = _messages.MessageField('GoogleDevtoolsCloudbuildV1GoModule', 1, repeated=True)
  images = _messages.StringField(2, repeated=True)
  mavenArtifacts = _messages.MessageField('GoogleDevtoolsCloudbuildV1MavenArtifact', 3, repeated=True)
  npmPackages = _messages.MessageField('GoogleDevtoolsCloudbuildV1NpmPackage', 4, repeated=True)
  objects = _messages.MessageField('GoogleDevtoolsCloudbuildV1ArtifactObjects', 5)
  pythonPackages = _messages.MessageField('GoogleDevtoolsCloudbuildV1PythonPackage', 6, repeated=True)


class GoogleDevtoolsCloudbuildV1Build(_messages.Message):
  r"""A build resource in the Cloud Build API. At a high level, a `Build`
  describes where to find source code, how to build it (for example, the
  builder image to run on the source), and where to store the built artifacts.
  Fields can include the following variables, which will be expanded when the
  build is created: - $PROJECT_ID: the project ID of the build. -
  $PROJECT_NUMBER: the project number of the build. - $LOCATION: the
  location/region of the build. - $BUILD_ID: the autogenerated ID of the
  build. - $REPO_NAME: the source repository name specified by RepoSource. -
  $BRANCH_NAME: the branch name specified by RepoSource. - $TAG_NAME: the tag
  name specified by RepoSource. - $REVISION_ID or $COMMIT_SHA: the commit SHA
  specified by RepoSource or resolved from the specified branch or tag. -
  $SHORT_SHA: first 7 characters of $REVISION_ID or $COMMIT_SHA.

  Enums:
    StatusValueValuesEnum: Output only. Status of the build.

  Messages:
    SubstitutionsValue: Substitutions data for `Build` resource.
    TimingValue: Output only. Stores timing information for phases of the
      build. Valid keys are: * BUILD: time to execute all build steps. * PUSH:
      time to push all artifacts including docker images and non docker
      artifacts. * FETCHSOURCE: time to fetch source. * SETUPBUILD: time to
      set up build. If the build does not specify source or images, these keys
      will not be included.

  Fields:
    approval: Output only. Describes this build's approval configuration,
      status, and result.
    artifacts: Artifacts produced by the build that should be uploaded upon
      successful completion of all build steps.
    availableSecrets: Secrets and secret environment variables.
    buildTriggerId: Output only. The ID of the `BuildTrigger` that triggered
      this build, if it was triggered automatically.
    createTime: Output only. Time at which the request to create the build was
      received.
    dependencies: Optional. Dependencies that the Cloud Build worker will
      fetch before executing user steps.
    failureInfo: Output only. Contains information about the build when
      status=FAILURE.
    finishTime: Output only. Time at which execution of the build was
      finished. The difference between finish_time and start_time is the
      duration of the build's execution.
    gitConfig: Optional. Configuration for git operations.
    id: Output only. Unique identifier of the build.
    images: A list of images to be pushed upon the successful completion of
      all build steps. The images are pushed using the builder service
      account's credentials. The digests of the pushed images will be stored
      in the `Build` resource's results field. If any of the images fail to be
      pushed, the build status is marked `FAILURE`.
    logUrl: Output only. URL to logs for this build in Google Cloud Console.
    logsBucket: Cloud Storage bucket where logs should be written (see [Bucket
      Name Requirements](https://cloud.google.com/storage/docs/bucket-
      naming#requirements)). Logs file names will be of the format
      `${logs_bucket}/log-${build_id}.txt`.
    name: Output only. The 'Build' name with format:
      `projects/{project}/locations/{location}/builds/{build}`, where {build}
      is a unique identifier generated by the service.
    options: Special options for this build.
    projectId: Output only. ID of the project.
    queueTtl: TTL in queue for this build. If provided and the build is
      enqueued longer than this value, the build will expire and the build
      status will be `EXPIRED`. The TTL starts ticking from create_time.
    results: Output only. Results of the build.
    secrets: Secrets to decrypt using Cloud Key Management Service. Note:
      Secret Manager is the recommended technique for managing sensitive data
      with Cloud Build. Use `available_secrets` to configure builds to access
      secrets from Secret Manager. For instructions, see:
      https://cloud.google.com/cloud-build/docs/securing-builds/use-secrets
    serviceAccount: IAM service account whose credentials will be used at
      build runtime. Must be of the format
      `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}`. ACCOUNT can be email
      address or uniqueId of the service account.
    source: Optional. The location of the source files to build.
    sourceProvenance: Output only. A permanent fixed identifier for source.
    startTime: Output only. Time at which execution of the build was started.
    status: Output only. Status of the build.
    statusDetail: Output only. Customer-readable message about the current
      status.
    steps: Required. The operations to be performed on the workspace.
    substitutions: Substitutions data for `Build` resource.
    tags: Tags for annotation of a `Build`. These are not docker tags.
    timeout: Amount of time that this build should be allowed to run, to
      second granularity. If this amount of time elapses, work on the build
      will cease and the build status will be `TIMEOUT`. `timeout` starts
      ticking from `startTime`. Default time is 60 minutes.
    timing: Output only. Stores timing information for phases of the build.
      Valid keys are: * BUILD: time to execute all build steps. * PUSH: time
      to push all artifacts including docker images and non docker artifacts.
      * FETCHSOURCE: time to fetch source. * SETUPBUILD: time to set up build.
      If the build does not specify source or images, these keys will not be
      included.
    warnings: Output only. Non-fatal problems encountered during the execution
      of the build.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""Output only. Status of the build.

    Values:
      STATUS_UNKNOWN: Status of the build is unknown.
      PENDING: Build has been created and is pending execution and queuing. It
        has not been queued.
      QUEUED: Build or step is queued; work has not yet begun.
      WORKING: Build or step is being executed.
      SUCCESS: Build or step finished successfully.
      FAILURE: Build or step failed to complete successfully.
      INTERNAL_ERROR: Build or step failed due to an internal cause.
      TIMEOUT: Build or step took longer than was allowed.
      CANCELLED: Build or step was canceled by a user.
      EXPIRED: Build was enqueued for longer than the value of `queue_ttl`.
    """
    STATUS_UNKNOWN = 0
    PENDING = 1
    QUEUED = 2
    WORKING = 3
    SUCCESS = 4
    FAILURE = 5
    INTERNAL_ERROR = 6
    TIMEOUT = 7
    CANCELLED = 8
    EXPIRED = 9

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SubstitutionsValue(_messages.Message):
    r"""Substitutions data for `Build` resource.

    Messages:
      AdditionalProperty: An additional property for a SubstitutionsValue
        object.

    Fields:
      additionalProperties: Additional properties of type SubstitutionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SubstitutionsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TimingValue(_messages.Message):
    r"""Output only. Stores timing information for phases of the build. Valid
    keys are: * BUILD: time to execute all build steps. * PUSH: time to push
    all artifacts including docker images and non docker artifacts. *
    FETCHSOURCE: time to fetch source. * SETUPBUILD: time to set up build. If
    the build does not specify source or images, these keys will not be
    included.

    Messages:
      AdditionalProperty: An additional property for a TimingValue object.

    Fields:
      additionalProperties: Additional properties of type TimingValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TimingValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleDevtoolsCloudbuildV1TimeSpan attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleDevtoolsCloudbuildV1TimeSpan', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  approval = _messages.MessageField('GoogleDevtoolsCloudbuildV1BuildApproval', 1)
  artifacts = _messages.MessageField('GoogleDevtoolsCloudbuildV1Artifacts', 2)
  availableSecrets = _messages.MessageField('GoogleDevtoolsCloudbuildV1Secrets', 3)
  buildTriggerId = _messages.StringField(4)
  createTime = _messages.StringField(5)
  dependencies = _messages.MessageField('GoogleDevtoolsCloudbuildV1Dependency', 6, repeated=True)
  failureInfo = _messages.MessageField('GoogleDevtoolsCloudbuildV1FailureInfo', 7)
  finishTime = _messages.StringField(8)
  gitConfig = _messages.MessageField('GoogleDevtoolsCloudbuildV1GitConfig', 9)
  id = _messages.StringField(10)
  images = _messages.StringField(11, repeated=True)
  logUrl = _messages.StringField(12)
  logsBucket = _messages.StringField(13)
  name = _messages.StringField(14)
  options = _messages.MessageField('GoogleDevtoolsCloudbuildV1BuildOptions', 15)
  projectId = _messages.StringField(16)
  queueTtl = _messages.StringField(17)
  results = _messages.MessageField('GoogleDevtoolsCloudbuildV1Results', 18)
  secrets = _messages.MessageField('GoogleDevtoolsCloudbuildV1Secret', 19, repeated=True)
  serviceAccount = _messages.StringField(20)
  source = _messages.MessageField('GoogleDevtoolsCloudbuildV1Source', 21)
  sourceProvenance = _messages.MessageField('GoogleDevtoolsCloudbuildV1SourceProvenance', 22)
  startTime = _messages.StringField(23)
  status = _messages.EnumField('StatusValueValuesEnum', 24)
  statusDetail = _messages.StringField(25)
  steps = _messages.MessageField('GoogleDevtoolsCloudbuildV1BuildStep', 26, repeated=True)
  substitutions = _messages.MessageField('SubstitutionsValue', 27)
  tags = _messages.StringField(28, repeated=True)
  timeout = _messages.StringField(29)
  timing = _messages.MessageField('TimingValue', 30)
  warnings = _messages.MessageField('GoogleDevtoolsCloudbuildV1Warning', 31, repeated=True)


class GoogleDevtoolsCloudbuildV1BuildApproval(_messages.Message):
  r"""BuildApproval describes a build's approval configuration, state, and
  result.

  Enums:
    StateValueValuesEnum: Output only. The state of this build's approval.

  Fields:
    config: Output only. Configuration for manual approval of this build.
    result: Output only. Result of manual approval for this Build.
    state: Output only. The state of this build's approval.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of this build's approval.

    Values:
      STATE_UNSPECIFIED: Default enum type. This should not be used.
      PENDING: Build approval is pending.
      APPROVED: Build approval has been approved.
      REJECTED: Build approval has been rejected.
      CANCELLED: Build was cancelled while it was still pending approval.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    APPROVED = 2
    REJECTED = 3
    CANCELLED = 4

  config = _messages.MessageField('GoogleDevtoolsCloudbuildV1ApprovalConfig', 1)
  result = _messages.MessageField('GoogleDevtoolsCloudbuildV1ApprovalResult', 2)
  state = _messages.EnumField('StateValueValuesEnum', 3)


class GoogleDevtoolsCloudbuildV1BuildOperationMetadata(_messages.Message):
  r"""Metadata for build operations.

  Fields:
    build: The build that the operation is tracking.
  """

  build = _messages.MessageField('GoogleDevtoolsCloudbuildV1Build', 1)


class GoogleDevtoolsCloudbuildV1BuildOptions(_messages.Message):
  r"""Optional arguments to enable specific features of builds.

  Enums:
    DefaultLogsBucketBehaviorValueValuesEnum: Optional. Option to specify how
      default logs buckets are setup.
    LogStreamingOptionValueValuesEnum: Option to define build log streaming
      behavior to Cloud Storage.
    LoggingValueValuesEnum: Option to specify the logging mode, which
      determines if and where build logs are stored.
    MachineTypeValueValuesEnum: Compute Engine machine type on which to run
      the build.
    RequestedVerifyOptionValueValuesEnum: Requested verifiability options.
    SourceProvenanceHashValueListEntryValuesEnum:
    SubstitutionOptionValueValuesEnum: Option to specify behavior when there
      is an error in the substitution checks. NOTE: this is always set to
      ALLOW_LOOSE for triggered builds and cannot be overridden in the build
      configuration file.

  Fields:
    automapSubstitutions: Option to include built-in and custom substitutions
      as env variables for all build steps.
    defaultLogsBucketBehavior: Optional. Option to specify how default logs
      buckets are setup.
    diskSizeGb: Requested disk size for the VM that runs the build. Note that
      this is *NOT* "disk free"; some of the space will be used by the
      operating system and build utilities. Also note that this is the minimum
      disk size that will be allocated for the build -- the build may run with
      a larger disk than requested. At present, the maximum disk size is
      4000GB; builds that request more than the maximum are rejected with an
      error.
    dynamicSubstitutions: Option to specify whether or not to apply bash style
      string operations to the substitutions. NOTE: this is always enabled for
      triggered builds and cannot be overridden in the build configuration
      file.
    enableStructuredLogging: Optional. Option to specify whether structured
      logging is enabled. If true, JSON-formatted logs are parsed as
      structured logs.
    env: A list of global environment variable definitions that will exist for
      all build steps in this build. If a variable is defined in both globally
      and in a build step, the variable will use the build step value. The
      elements are of the form "KEY=VALUE" for the environment variable "KEY"
      being given the value "VALUE".
    logStreamingOption: Option to define build log streaming behavior to Cloud
      Storage.
    logging: Option to specify the logging mode, which determines if and where
      build logs are stored.
    machineType: Compute Engine machine type on which to run the build.
    pool: Optional. Specification for execution on a `WorkerPool`. See
      [running builds in a private
      pool](https://cloud.google.com/build/docs/private-pools/run-builds-in-
      private-pool) for more information.
    pubsubTopic: Optional. Option to specify the Pub/Sub topic to receive
      build status updates.
    requestedVerifyOption: Requested verifiability options.
    secretEnv: A list of global environment variables, which are encrypted
      using a Cloud Key Management Service crypto key. These values must be
      specified in the build's `Secret`. These variables will be available to
      all build steps in this build.
    sourceProvenanceHash: Requested hash for SourceProvenance.
    substitutionOption: Option to specify behavior when there is an error in
      the substitution checks. NOTE: this is always set to ALLOW_LOOSE for
      triggered builds and cannot be overridden in the build configuration
      file.
    volumes: Global list of volumes to mount for ALL build steps Each volume
      is created as an empty volume prior to starting the build process. Upon
      completion of the build, volumes and their contents are discarded.
      Global volume names and paths cannot conflict with the volumes defined a
      build step. Using a global volume in a build with only one step is not
      valid as it is indicative of a build request with an incorrect
      configuration.
    workerPool: This field deprecated; please use `pool.name` instead.
  """

  class DefaultLogsBucketBehaviorValueValuesEnum(_messages.Enum):
    r"""Optional. Option to specify how default logs buckets are setup.

    Values:
      DEFAULT_LOGS_BUCKET_BEHAVIOR_UNSPECIFIED: Unspecified.
      REGIONAL_USER_OWNED_BUCKET: Bucket is located in user-owned project in
        the same region as the build. The builder service account must have
        access to create and write to Cloud Storage buckets in the build
        project.
      LEGACY_BUCKET: Bucket is located in a Google-owned project and is not
        regionalized.
    """
    DEFAULT_LOGS_BUCKET_BEHAVIOR_UNSPECIFIED = 0
    REGIONAL_USER_OWNED_BUCKET = 1
    LEGACY_BUCKET = 2

  class LogStreamingOptionValueValuesEnum(_messages.Enum):
    r"""Option to define build log streaming behavior to Cloud Storage.

    Values:
      STREAM_DEFAULT: Service may automatically determine build log streaming
        behavior.
      STREAM_ON: Build logs should be streamed to Cloud Storage.
      STREAM_OFF: Build logs should not be streamed to Cloud Storage; they
        will be written when the build is completed.
    """
    STREAM_DEFAULT = 0
    STREAM_ON = 1
    STREAM_OFF = 2

  class LoggingValueValuesEnum(_messages.Enum):
    r"""Option to specify the logging mode, which determines if and where
    build logs are stored.

    Values:
      LOGGING_UNSPECIFIED: The service determines the logging mode. The
        default is `LEGACY`. Do not rely on the default logging behavior as it
        may change in the future.
      LEGACY: Build logs are stored in Cloud Logging and Cloud Storage.
      GCS_ONLY: Build logs are stored in Cloud Storage.
      STACKDRIVER_ONLY: This option is the same as CLOUD_LOGGING_ONLY.
      CLOUD_LOGGING_ONLY: Build logs are stored in Cloud Logging. Selecting
        this option will not allow [logs
        streaming](https://cloud.google.com/sdk/gcloud/reference/builds/log).
      NONE: Turn off all logging. No build logs will be captured.
    """
    LOGGING_UNSPECIFIED = 0
    LEGACY = 1
    GCS_ONLY = 2
    STACKDRIVER_ONLY = 3
    CLOUD_LOGGING_ONLY = 4
    NONE = 5

  class MachineTypeValueValuesEnum(_messages.Enum):
    r"""Compute Engine machine type on which to run the build.

    Values:
      UNSPECIFIED: Standard machine type.
      N1_HIGHCPU_8: Highcpu machine with 8 CPUs.
      N1_HIGHCPU_32: Highcpu machine with 32 CPUs.
      E2_HIGHCPU_8: Highcpu e2 machine with 8 CPUs.
      E2_HIGHCPU_32: Highcpu e2 machine with 32 CPUs.
      E2_MEDIUM: E2 machine with 1 CPU.
    """
    UNSPECIFIED = 0
    N1_HIGHCPU_8 = 1
    N1_HIGHCPU_32 = 2
    E2_HIGHCPU_8 = 3
    E2_HIGHCPU_32 = 4
    E2_MEDIUM = 5

  class RequestedVerifyOptionValueValuesEnum(_messages.Enum):
    r"""Requested verifiability options.

    Values:
      NOT_VERIFIED: Not a verifiable build (the default).
      VERIFIED: Build must be verified.
    """
    NOT_VERIFIED = 0
    VERIFIED = 1

  class SourceProvenanceHashValueListEntryValuesEnum(_messages.Enum):
    r"""SourceProvenanceHashValueListEntryValuesEnum enum type.

    Values:
      NONE: No hash requested.
      SHA256: Use a sha256 hash.
      MD5: Use a md5 hash.
      GO_MODULE_H1: Dirhash of a Go module's source code which is then hex-
        encoded.
      SHA512: Use a sha512 hash.
    """
    NONE = 0
    SHA256 = 1
    MD5 = 2
    GO_MODULE_H1 = 3
    SHA512 = 4

  class SubstitutionOptionValueValuesEnum(_messages.Enum):
    r"""Option to specify behavior when there is an error in the substitution
    checks. NOTE: this is always set to ALLOW_LOOSE for triggered builds and
    cannot be overridden in the build configuration file.

    Values:
      MUST_MATCH: Fails the build if error in substitutions checks, like
        missing a substitution in the template or in the map.
      ALLOW_LOOSE: Do not fail the build if error in substitutions checks.
    """
    MUST_MATCH = 0
    ALLOW_LOOSE = 1

  automapSubstitutions = _messages.BooleanField(1)
  defaultLogsBucketBehavior = _messages.EnumField('DefaultLogsBucketBehaviorValueValuesEnum', 2)
  diskSizeGb = _messages.IntegerField(3)
  dynamicSubstitutions = _messages.BooleanField(4)
  enableStructuredLogging = _messages.BooleanField(5)
  env = _messages.StringField(6, repeated=True)
  logStreamingOption = _messages.EnumField('LogStreamingOptionValueValuesEnum', 7)
  logging = _messages.EnumField('LoggingValueValuesEnum', 8)
  machineType = _messages.EnumField('MachineTypeValueValuesEnum', 9)
  pool = _messages.MessageField('GoogleDevtoolsCloudbuildV1PoolOption', 10)
  pubsubTopic = _messages.StringField(11)
  requestedVerifyOption = _messages.EnumField('RequestedVerifyOptionValueValuesEnum', 12)
  secretEnv = _messages.StringField(13, repeated=True)
  sourceProvenanceHash = _messages.EnumField('SourceProvenanceHashValueListEntryValuesEnum', 14, repeated=True)
  substitutionOption = _messages.EnumField('SubstitutionOptionValueValuesEnum', 15)
  volumes = _messages.MessageField('GoogleDevtoolsCloudbuildV1Volume', 16, repeated=True)
  workerPool = _messages.StringField(17)


class GoogleDevtoolsCloudbuildV1BuildStep(_messages.Message):
  r"""A step in the build pipeline.

  Enums:
    StatusValueValuesEnum: Output only. Status of the build step. At this
      time, build step status is only updated on build completion; step status
      is not updated in real-time as the build progresses.

  Fields:
    allowExitCodes: Allow this build step to fail without failing the entire
      build if and only if the exit code is one of the specified codes. If
      allow_failure is also specified, this field will take precedence.
    allowFailure: Allow this build step to fail without failing the entire
      build. If false, the entire build will fail if this step fails.
      Otherwise, the build will succeed, but this step will still have a
      failure status. Error information will be reported in the failure_detail
      field.
    args: A list of arguments that will be presented to the step when it is
      started. If the image used to run the step's container has an
      entrypoint, the `args` are used as arguments to that entrypoint. If the
      image does not define an entrypoint, the first element in args is used
      as the entrypoint, and the remainder will be used as arguments.
    automapSubstitutions: Option to include built-in and custom substitutions
      as env variables for this build step. This option will override the
      global option in BuildOption.
    dir: Working directory to use when running this step's container. If this
      value is a relative path, it is relative to the build's working
      directory. If this value is absolute, it may be outside the build's
      working directory, in which case the contents of the path may not be
      persisted across build step executions, unless a `volume` for that path
      is specified. If the build specifies a `RepoSource` with `dir` and a
      step with a `dir`, which specifies an absolute path, the `RepoSource`
      `dir` is ignored for the step's execution.
    entrypoint: Entrypoint to be used instead of the build step image's
      default entrypoint. If unset, the image's default entrypoint is used.
    env: A list of environment variable definitions to be used when running a
      step. The elements are of the form "KEY=VALUE" for the environment
      variable "KEY" being given the value "VALUE".
    exitCode: Output only. Return code from running the step.
    id: Unique identifier for this build step, used in `wait_for` to reference
      this build step as a dependency.
    name: Required. The name of the container image that will run this
      particular build step. If the image is available in the host's Docker
      daemon's cache, it will be run directly. If not, the host will attempt
      to pull the image first, using the builder service account's credentials
      if necessary. The Docker daemon's cache will already have the latest
      versions of all of the officially supported build steps
      ([https://github.com/GoogleCloudPlatform/cloud-
      builders](https://github.com/GoogleCloudPlatform/cloud-builders)). The
      Docker daemon will also have cached many of the layers for some popular
      images, like "ubuntu", "debian", but they will be refreshed at the time
      you attempt to use them. If you built an image in a previous build step,
      it will be stored in the host's Docker daemon's cache and is available
      to use as the name for a later build step.
    pullTiming: Output only. Stores timing information for pulling this build
      step's builder image only.
    script: A shell script to be executed in the step. When script is
      provided, the user cannot specify the entrypoint or args.
    secretEnv: A list of environment variables which are encrypted using a
      Cloud Key Management Service crypto key. These values must be specified
      in the build's `Secret`.
    status: Output only. Status of the build step. At this time, build step
      status is only updated on build completion; step status is not updated
      in real-time as the build progresses.
    timeout: Time limit for executing this build step. If not defined, the
      step has no time limit and will be allowed to continue to run until
      either it completes or the build itself times out.
    timing: Output only. Stores timing information for executing this build
      step.
    volumes: List of volumes to mount into the build step. Each volume is
      created as an empty volume prior to execution of the build step. Upon
      completion of the build, volumes and their contents are discarded. Using
      a named volume in only one step is not valid as it is indicative of a
      build request with an incorrect configuration.
    waitFor: The ID(s) of the step(s) that this build step depends on. This
      build step will not start until all the build steps in `wait_for` have
      completed successfully. If `wait_for` is empty, this build step will
      start when all previous build steps in the `Build.Steps` list have
      completed successfully.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""Output only. Status of the build step. At this time, build step status
    is only updated on build completion; step status is not updated in real-
    time as the build progresses.

    Values:
      STATUS_UNKNOWN: Status of the build is unknown.
      PENDING: Build has been created and is pending execution and queuing. It
        has not been queued.
      QUEUED: Build or step is queued; work has not yet begun.
      WORKING: Build or step is being executed.
      SUCCESS: Build or step finished successfully.
      FAILURE: Build or step failed to complete successfully.
      INTERNAL_ERROR: Build or step failed due to an internal cause.
      TIMEOUT: Build or step took longer than was allowed.
      CANCELLED: Build or step was canceled by a user.
      EXPIRED: Build was enqueued for longer than the value of `queue_ttl`.
    """
    STATUS_UNKNOWN = 0
    PENDING = 1
    QUEUED = 2
    WORKING = 3
    SUCCESS = 4
    FAILURE = 5
    INTERNAL_ERROR = 6
    TIMEOUT = 7
    CANCELLED = 8
    EXPIRED = 9

  allowExitCodes = _messages.IntegerField(1, repeated=True, variant=_messages.Variant.INT32)
  allowFailure = _messages.BooleanField(2)
  args = _messages.StringField(3, repeated=True)
  automapSubstitutions = _messages.BooleanField(4)
  dir = _messages.StringField(5)
  entrypoint = _messages.StringField(6)
  env = _messages.StringField(7, repeated=True)
  exitCode = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  id = _messages.StringField(9)
  name = _messages.StringField(10)
  pullTiming = _messages.MessageField('GoogleDevtoolsCloudbuildV1TimeSpan', 11)
  script = _messages.StringField(12)
  secretEnv = _messages.StringField(13, repeated=True)
  status = _messages.EnumField('StatusValueValuesEnum', 14)
  timeout = _messages.StringField(15)
  timing = _messages.MessageField('GoogleDevtoolsCloudbuildV1TimeSpan', 16)
  volumes = _messages.MessageField('GoogleDevtoolsCloudbuildV1Volume', 17, repeated=True)
  waitFor = _messages.StringField(18, repeated=True)


class GoogleDevtoolsCloudbuildV1BuiltImage(_messages.Message):
  r"""An image built by the pipeline.

  Fields:
    digest: Docker Registry 2.0 digest.
    name: Name used to push the container image to Google Container Registry,
      as presented to `docker push`.
    pushTiming: Output only. Stores timing information for pushing the
      specified image.
  """

  digest = _messages.StringField(1)
  name = _messages.StringField(2)
  pushTiming = _messages.MessageField('GoogleDevtoolsCloudbuildV1TimeSpan', 3)


class GoogleDevtoolsCloudbuildV1ConnectedRepository(_messages.Message):
  r"""Location of the source in a 2nd-gen Google Cloud Build repository
  resource.

  Fields:
    dir: Optional. Directory, relative to the source root, in which to run the
      build.
    repository: Required. Name of the Google Cloud Build repository, formatted
      as `projects/*/locations/*/connections/*/repositories/*`.
    revision: Required. The revision to fetch from the Git repository such as
      a branch, a tag, a commit SHA, or any Git ref.
  """

  dir = _messages.StringField(1)
  repository = _messages.StringField(2)
  revision = _messages.StringField(3)


class GoogleDevtoolsCloudbuildV1Dependency(_messages.Message):
  r"""A dependency that the Cloud Build worker will fetch before executing
  user steps.

  Fields:
    empty: If set to true disable all dependency fetching (ignoring the
      default source as well).
    gitSource: Represents a git repository as a build dependency.
  """

  empty = _messages.BooleanField(1)
  gitSource = _messages.MessageField('GoogleDevtoolsCloudbuildV1GitSourceDependency', 2)


class GoogleDevtoolsCloudbuildV1DeveloperConnectConfig(_messages.Message):
  r"""This config defines the location of a source through Developer Connect.

  Fields:
    dir: Required. Directory, relative to the source root, in which to run the
      build.
    gitRepositoryLink: Required. The Developer Connect Git repository link,
      formatted as `projects/*/locations/*/connections/*/gitRepositoryLink/*`.
    revision: Required. The revision to fetch from the Git repository such as
      a branch, a tag, a commit SHA, or any Git ref.
  """

  dir = _messages.StringField(1)
  gitRepositoryLink = _messages.StringField(2)
  revision = _messages.StringField(3)


class GoogleDevtoolsCloudbuildV1FailureInfo(_messages.Message):
  r"""A fatal problem encountered during the execution of the build.

  Enums:
    TypeValueValuesEnum: The name of the failure.

  Fields:
    detail: Explains the failure issue in more detail using hard-coded text.
    type: The name of the failure.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The name of the failure.

    Values:
      FAILURE_TYPE_UNSPECIFIED: Type unspecified
      PUSH_FAILED: Unable to push the image to the repository.
      PUSH_IMAGE_NOT_FOUND: Final image not found.
      PUSH_NOT_AUTHORIZED: Unauthorized push of the final image.
      LOGGING_FAILURE: Backend logging failures. Should retry.
      USER_BUILD_STEP: A build step has failed.
      FETCH_SOURCE_FAILED: The source fetching has failed.
    """
    FAILURE_TYPE_UNSPECIFIED = 0
    PUSH_FAILED = 1
    PUSH_IMAGE_NOT_FOUND = 2
    PUSH_NOT_AUTHORIZED = 3
    LOGGING_FAILURE = 4
    USER_BUILD_STEP = 5
    FETCH_SOURCE_FAILED = 6

  detail = _messages.StringField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class GoogleDevtoolsCloudbuildV1FileHashes(_messages.Message):
  r"""Container message for hashes of byte content of files, used in
  SourceProvenance messages to verify integrity of source input to the build.

  Fields:
    fileHash: Collection of file hashes.
  """

  fileHash = _messages.MessageField('GoogleDevtoolsCloudbuildV1Hash', 1, repeated=True)


class GoogleDevtoolsCloudbuildV1GitConfig(_messages.Message):
  r"""GitConfig is a configuration for git operations.

  Fields:
    http: Configuration for HTTP related git operations.
  """

  http = _messages.MessageField('GoogleDevtoolsCloudbuildV1HttpConfig', 1)


class GoogleDevtoolsCloudbuildV1GitSource(_messages.Message):
  r"""Location of the source in any accessible Git repository.

  Fields:
    dir: Optional. Directory, relative to the source root, in which to run the
      build. This must be a relative path. If a step's `dir` is specified and
      is an absolute path, this value is ignored for that step's execution.
    revision: Optional. The revision to fetch from the Git repository such as
      a branch, a tag, a commit SHA, or any Git ref. Cloud Build uses `git
      fetch` to fetch the revision from the Git repository; therefore make
      sure that the string you provide for `revision` is parsable by the
      command. For information on string values accepted by `git fetch`, see
      https://git-scm.com/docs/gitrevisions#_specifying_revisions. For
      information on `git fetch`, see https://git-scm.com/docs/git-fetch.
    url: Required. Location of the Git repo to build. This will be used as a
      `git remote`, see https://git-scm.com/docs/git-remote.
  """

  dir = _messages.StringField(1)
  revision = _messages.StringField(2)
  url = _messages.StringField(3)


class GoogleDevtoolsCloudbuildV1GitSourceDependency(_messages.Message):
  r"""Represents a git repository as a build dependency.

  Fields:
    depth: Optional. How much history should be fetched for the build (default
      1, -1 for all history).
    destPath: Required. Where should the files be placed on the worker.
    recurseSubmodules: Optional. True if submodules should be fetched too
      (default false).
    repository: Required. The kind of repo (url or dev connect).
    revision: Required. The revision that we will fetch the repo at.
  """

  depth = _messages.IntegerField(1)
  destPath = _messages.StringField(2)
  recurseSubmodules = _messages.BooleanField(3)
  repository = _messages.MessageField('GoogleDevtoolsCloudbuildV1GitSourceRepository', 4)
  revision = _messages.StringField(5)


class GoogleDevtoolsCloudbuildV1GitSourceRepository(_messages.Message):
  r"""A repository for a git source.

  Fields:
    developerConnect: The Developer Connect Git repository link formatted as
      `projects/*/locations/*/connections/*/gitRepositoryLink/*`
    url: Location of the Git repository.
  """

  developerConnect = _messages.StringField(1)
  url = _messages.StringField(2)


class GoogleDevtoolsCloudbuildV1GoModule(_messages.Message):
  r"""Go module to upload to Artifact Registry upon successful completion of
  all build steps. A module refers to all dependencies in a go.mod file.

  Fields:
    modulePath: Optional. The Go module's "module path". e.g.
      example.com/foo/v2
    moduleVersion: Optional. The Go module's semantic version in the form
      vX.Y.Z. e.g. v0.1.1 Pre-release identifiers can also be added by
      appending a dash and dot separated ASCII alphanumeric characters and
      hyphens. e.g. v0.2.3-alpha.x.12m.5
    repositoryLocation: Optional. Location of the Artifact Registry
      repository. i.e. us-east1 Defaults to the build's location.
    repositoryName: Optional. Artifact Registry repository name. Specified Go
      modules will be zipped and uploaded to Artifact Registry with this
      location as a prefix. e.g. my-go-repo
    repositoryProjectId: Optional. Project ID of the Artifact Registry
      repository. Defaults to the build project.
    sourcePath: Optional. Source path of the go.mod file in the build's
      workspace. If not specified, this will default to the current directory.
      e.g. ~/code/go/mypackage
  """

  modulePath = _messages.StringField(1)
  moduleVersion = _messages.StringField(2)
  repositoryLocation = _messages.StringField(3)
  repositoryName = _messages.StringField(4)
  repositoryProjectId = _messages.StringField(5)
  sourcePath = _messages.StringField(6)


class GoogleDevtoolsCloudbuildV1Hash(_messages.Message):
  r"""Container message for hash values.

  Enums:
    TypeValueValuesEnum: The type of hash that was performed.

  Fields:
    type: The type of hash that was performed.
    value: The hash value.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of hash that was performed.

    Values:
      NONE: No hash requested.
      SHA256: Use a sha256 hash.
      MD5: Use a md5 hash.
      GO_MODULE_H1: Dirhash of a Go module's source code which is then hex-
        encoded.
      SHA512: Use a sha512 hash.
    """
    NONE = 0
    SHA256 = 1
    MD5 = 2
    GO_MODULE_H1 = 3
    SHA512 = 4

  type = _messages.EnumField('TypeValueValuesEnum', 1)
  value = _messages.BytesField(2)


class GoogleDevtoolsCloudbuildV1HttpConfig(_messages.Message):
  r"""HttpConfig is a configuration for HTTP related git operations.

  Fields:
    proxySecretVersionName: SecretVersion resource of the HTTP proxy URL. The
      Service Account used in the build (either the default Service Account or
      user-specified Service Account) should have
      `secretmanager.versions.access` permissions on this secret. The proxy
      URL should be in format `protocol://@]proxyhost[:port]`.
  """

  proxySecretVersionName = _messages.StringField(1)


class GoogleDevtoolsCloudbuildV1InlineSecret(_messages.Message):
  r"""Pairs a set of secret environment variables mapped to encrypted values
  with the Cloud KMS key to use to decrypt the value.

  Messages:
    EnvMapValue: Map of environment variable name to its encrypted value.
      Secret environment variables must be unique across all of a build's
      secrets, and must be used by at least one build step. Values can be at
      most 64 KB in size. There can be at most 100 secret values across all of
      a build's secrets.

  Fields:
    envMap: Map of environment variable name to its encrypted value. Secret
      environment variables must be unique across all of a build's secrets,
      and must be used by at least one build step. Values can be at most 64 KB
      in size. There can be at most 100 secret values across all of a build's
      secrets.
    kmsKeyName: Resource name of Cloud KMS crypto key to decrypt the encrypted
      value. In format: projects/*/locations/*/keyRings/*/cryptoKeys/*
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvMapValue(_messages.Message):
    r"""Map of environment variable name to its encrypted value. Secret
    environment variables must be unique across all of a build's secrets, and
    must be used by at least one build step. Values can be at most 64 KB in
    size. There can be at most 100 secret values across all of a build's
    secrets.

    Messages:
      AdditionalProperty: An additional property for a EnvMapValue object.

    Fields:
      additionalProperties: Additional properties of type EnvMapValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvMapValue object.

      Fields:
        key: Name of the additional property.
        value: A byte attribute.
      """

      key = _messages.StringField(1)
      value = _messages.BytesField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  envMap = _messages.MessageField('EnvMapValue', 1)
  kmsKeyName = _messages.StringField(2)


class GoogleDevtoolsCloudbuildV1MavenArtifact(_messages.Message):
  r"""A Maven artifact to upload to Artifact Registry upon successful
  completion of all build steps.

  Fields:
    artifactId: Maven `artifactId` value used when uploading the artifact to
      Artifact Registry.
    groupId: Maven `groupId` value used when uploading the artifact to
      Artifact Registry.
    path: Optional. Path to an artifact in the build's workspace to be
      uploaded to Artifact Registry. This can be either an absolute path, e.g.
      /workspace/my-app/target/my-app-1.0.SNAPSHOT.jar or a relative path from
      /workspace, e.g. my-app/target/my-app-1.0.SNAPSHOT.jar.
    repository: Artifact Registry repository, in the form "https://$REGION-
      maven.pkg.dev/$PROJECT/$REPOSITORY" Artifact in the workspace specified
      by path will be uploaded to Artifact Registry with this location as a
      prefix.
    version: Maven `version` value used when uploading the artifact to
      Artifact Registry.
  """

  artifactId = _messages.StringField(1)
  groupId = _messages.StringField(2)
  path = _messages.StringField(3)
  repository = _messages.StringField(4)
  version = _messages.StringField(5)


class GoogleDevtoolsCloudbuildV1NpmPackage(_messages.Message):
  r"""Npm package to upload to Artifact Registry upon successful completion of
  all build steps.

  Fields:
    packagePath: Path to the package.json. e.g. workspace/path/to/package
    repository: Artifact Registry repository, in the form "https://$REGION-
      npm.pkg.dev/$PROJECT/$REPOSITORY" Npm package in the workspace specified
      by path will be zipped and uploaded to Artifact Registry with this
      location as a prefix.
  """

  packagePath = _messages.StringField(1)
  repository = _messages.StringField(2)


class GoogleDevtoolsCloudbuildV1PoolOption(_messages.Message):
  r"""Details about how a build should be executed on a `WorkerPool`. See
  [running builds in a private
  pool](https://cloud.google.com/build/docs/private-pools/run-builds-in-
  private-pool) for more information.

  Fields:
    name: The `WorkerPool` resource to execute the build on. You must have
      `cloudbuild.workerpools.use` on the project hosting the WorkerPool.
      Format
      projects/{project}/locations/{location}/workerPools/{workerPoolId}
  """

  name = _messages.StringField(1)


class GoogleDevtoolsCloudbuildV1PythonPackage(_messages.Message):
  r"""Python package to upload to Artifact Registry upon successful completion
  of all build steps. A package can encapsulate multiple objects to be
  uploaded to a single repository.

  Fields:
    paths: Path globs used to match files in the build's workspace. For
      Python/ Twine, this is usually `dist/*`, and sometimes additionally an
      `.asc` file.
    repository: Artifact Registry repository, in the form "https://$REGION-
      python.pkg.dev/$PROJECT/$REPOSITORY" Files in the workspace matching any
      path pattern will be uploaded to Artifact Registry with this location as
      a prefix.
  """

  paths = _messages.StringField(1, repeated=True)
  repository = _messages.StringField(2)


class GoogleDevtoolsCloudbuildV1RepoSource(_messages.Message):
  r"""Location of the source in a Google Cloud Source Repository.

  Messages:
    SubstitutionsValue: Optional. Substitutions to use in a triggered build.
      Should only be used with RunBuildTrigger

  Fields:
    branchName: Regex matching branches to build. The syntax of the regular
      expressions accepted is the syntax accepted by RE2 and described at
      https://github.com/google/re2/wiki/Syntax
    commitSha: Explicit commit SHA to build.
    dir: Optional. Directory, relative to the source root, in which to run the
      build. This must be a relative path. If a step's `dir` is specified and
      is an absolute path, this value is ignored for that step's execution.
    invertRegex: Optional. Only trigger a build if the revision regex does NOT
      match the revision regex.
    projectId: Optional. ID of the project that owns the Cloud Source
      Repository. If omitted, the project ID requesting the build is assumed.
    repoName: Required. Name of the Cloud Source Repository.
    substitutions: Optional. Substitutions to use in a triggered build. Should
      only be used with RunBuildTrigger
    tagName: Regex matching tags to build. The syntax of the regular
      expressions accepted is the syntax accepted by RE2 and described at
      https://github.com/google/re2/wiki/Syntax
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SubstitutionsValue(_messages.Message):
    r"""Optional. Substitutions to use in a triggered build. Should only be
    used with RunBuildTrigger

    Messages:
      AdditionalProperty: An additional property for a SubstitutionsValue
        object.

    Fields:
      additionalProperties: Additional properties of type SubstitutionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SubstitutionsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  branchName = _messages.StringField(1)
  commitSha = _messages.StringField(2)
  dir = _messages.StringField(3)
  invertRegex = _messages.BooleanField(4)
  projectId = _messages.StringField(5)
  repoName = _messages.StringField(6)
  substitutions = _messages.MessageField('SubstitutionsValue', 7)
  tagName = _messages.StringField(8)


class GoogleDevtoolsCloudbuildV1Results(_messages.Message):
  r"""Artifacts created by the build pipeline.

  Fields:
    artifactManifest: Path to the artifact manifest for non-container
      artifacts uploaded to Cloud Storage. Only populated when artifacts are
      uploaded to Cloud Storage.
    artifactTiming: Time to push all non-container artifacts to Cloud Storage.
    buildStepImages: List of build step digests, in the order corresponding to
      build step indices.
    buildStepOutputs: List of build step outputs, produced by builder images,
      in the order corresponding to build step indices. [Cloud
      Builders](https://cloud.google.com/cloud-build/docs/cloud-builders) can
      produce this output by writing to `$BUILDER_OUTPUT/output`. Only the
      first 50KB of data is stored. Note that the `$BUILDER_OUTPUT` variable
      is read-only and can't be substituted.
    goModules: Optional. Go module artifacts uploaded to Artifact Registry at
      the end of the build.
    images: Container images that were built as a part of the build.
    mavenArtifacts: Maven artifacts uploaded to Artifact Registry at the end
      of the build.
    npmPackages: Npm packages uploaded to Artifact Registry at the end of the
      build.
    numArtifacts: Number of non-container artifacts uploaded to Cloud Storage.
      Only populated when artifacts are uploaded to Cloud Storage.
    pythonPackages: Python artifacts uploaded to Artifact Registry at the end
      of the build.
  """

  artifactManifest = _messages.StringField(1)
  artifactTiming = _messages.MessageField('GoogleDevtoolsCloudbuildV1TimeSpan', 2)
  buildStepImages = _messages.StringField(3, repeated=True)
  buildStepOutputs = _messages.BytesField(4, repeated=True)
  goModules = _messages.MessageField('GoogleDevtoolsCloudbuildV1UploadedGoModule', 5, repeated=True)
  images = _messages.MessageField('GoogleDevtoolsCloudbuildV1BuiltImage', 6, repeated=True)
  mavenArtifacts = _messages.MessageField('GoogleDevtoolsCloudbuildV1UploadedMavenArtifact', 7, repeated=True)
  npmPackages = _messages.MessageField('GoogleDevtoolsCloudbuildV1UploadedNpmPackage', 8, repeated=True)
  numArtifacts = _messages.IntegerField(9)
  pythonPackages = _messages.MessageField('GoogleDevtoolsCloudbuildV1UploadedPythonPackage', 10, repeated=True)


class GoogleDevtoolsCloudbuildV1Secret(_messages.Message):
  r"""Pairs a set of secret environment variables containing encrypted values
  with the Cloud KMS key to use to decrypt the value. Note: Use `kmsKeyName`
  with `available_secrets` instead of using `kmsKeyName` with `secret`. For
  instructions see: https://cloud.google.com/cloud-build/docs/securing-
  builds/use-encrypted-credentials.

  Messages:
    SecretEnvValue: Map of environment variable name to its encrypted value.
      Secret environment variables must be unique across all of a build's
      secrets, and must be used by at least one build step. Values can be at
      most 64 KB in size. There can be at most 100 secret values across all of
      a build's secrets.

  Fields:
    kmsKeyName: Cloud KMS key name to use to decrypt these envs.
    secretEnv: Map of environment variable name to its encrypted value. Secret
      environment variables must be unique across all of a build's secrets,
      and must be used by at least one build step. Values can be at most 64 KB
      in size. There can be at most 100 secret values across all of a build's
      secrets.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SecretEnvValue(_messages.Message):
    r"""Map of environment variable name to its encrypted value. Secret
    environment variables must be unique across all of a build's secrets, and
    must be used by at least one build step. Values can be at most 64 KB in
    size. There can be at most 100 secret values across all of a build's
    secrets.

    Messages:
      AdditionalProperty: An additional property for a SecretEnvValue object.

    Fields:
      additionalProperties: Additional properties of type SecretEnvValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SecretEnvValue object.

      Fields:
        key: Name of the additional property.
        value: A byte attribute.
      """

      key = _messages.StringField(1)
      value = _messages.BytesField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  kmsKeyName = _messages.StringField(1)
  secretEnv = _messages.MessageField('SecretEnvValue', 2)


class GoogleDevtoolsCloudbuildV1SecretManagerSecret(_messages.Message):
  r"""Pairs a secret environment variable with a SecretVersion in Secret
  Manager.

  Fields:
    env: Environment variable name to associate with the secret. Secret
      environment variables must be unique across all of a build's secrets,
      and must be used by at least one build step.
    versionName: Resource name of the SecretVersion. In format:
      projects/*/secrets/*/versions/*
  """

  env = _messages.StringField(1)
  versionName = _messages.StringField(2)


class GoogleDevtoolsCloudbuildV1Secrets(_messages.Message):
  r"""Secrets and secret environment variables.

  Fields:
    inline: Secrets encrypted with KMS key and the associated secret
      environment variable.
    secretManager: Secrets in Secret Manager and associated secret environment
      variable.
  """

  inline = _messages.MessageField('GoogleDevtoolsCloudbuildV1InlineSecret', 1, repeated=True)
  secretManager = _messages.MessageField('GoogleDevtoolsCloudbuildV1SecretManagerSecret', 2, repeated=True)


class GoogleDevtoolsCloudbuildV1Source(_messages.Message):
  r"""Location of the source in a supported storage service.

  Fields:
    connectedRepository: Optional. If provided, get the source from this 2nd-
      gen Google Cloud Build repository resource.
    developerConnectConfig: If provided, get the source from this Developer
      Connect config.
    gitSource: If provided, get the source from this Git repository.
    repoSource: If provided, get the source from this location in a Cloud
      Source Repository.
    storageSource: If provided, get the source from this location in Cloud
      Storage.
    storageSourceManifest: If provided, get the source from this manifest in
      Cloud Storage. This feature is in Preview; see description
      [here](https://github.com/GoogleCloudPlatform/cloud-
      builders/tree/master/gcs-fetcher).
  """

  connectedRepository = _messages.MessageField('GoogleDevtoolsCloudbuildV1ConnectedRepository', 1)
  developerConnectConfig = _messages.MessageField('GoogleDevtoolsCloudbuildV1DeveloperConnectConfig', 2)
  gitSource = _messages.MessageField('GoogleDevtoolsCloudbuildV1GitSource', 3)
  repoSource = _messages.MessageField('GoogleDevtoolsCloudbuildV1RepoSource', 4)
  storageSource = _messages.MessageField('GoogleDevtoolsCloudbuildV1StorageSource', 5)
  storageSourceManifest = _messages.MessageField('GoogleDevtoolsCloudbuildV1StorageSourceManifest', 6)


class GoogleDevtoolsCloudbuildV1SourceProvenance(_messages.Message):
  r"""Provenance of the source. Ways to find the original source, or verify
  that some source was used for this build.

  Messages:
    FileHashesValue: Output only. Hash(es) of the build source, which can be
      used to verify that the original source integrity was maintained in the
      build. Note that `FileHashes` will only be populated if `BuildOptions`
      has requested a `SourceProvenanceHash`. The keys to this map are file
      paths used as build source and the values contain the hash values for
      those files. If the build source came in a single package such as a
      gzipped tarfile (`.tar.gz`), the `FileHash` will be for the single path
      to that file.

  Fields:
    fileHashes: Output only. Hash(es) of the build source, which can be used
      to verify that the original source integrity was maintained in the
      build. Note that `FileHashes` will only be populated if `BuildOptions`
      has requested a `SourceProvenanceHash`. The keys to this map are file
      paths used as build source and the values contain the hash values for
      those files. If the build source came in a single package such as a
      gzipped tarfile (`.tar.gz`), the `FileHash` will be for the single path
      to that file.
    resolvedConnectedRepository: Output only. A copy of the build's
      `source.connected_repository`, if exists, with any revisions resolved.
    resolvedGitSource: Output only. A copy of the build's `source.git_source`,
      if exists, with any revisions resolved.
    resolvedRepoSource: A copy of the build's `source.repo_source`, if exists,
      with any revisions resolved.
    resolvedStorageSource: A copy of the build's `source.storage_source`, if
      exists, with any generations resolved.
    resolvedStorageSourceManifest: A copy of the build's
      `source.storage_source_manifest`, if exists, with any revisions
      resolved. This feature is in Preview.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FileHashesValue(_messages.Message):
    r"""Output only. Hash(es) of the build source, which can be used to verify
    that the original source integrity was maintained in the build. Note that
    `FileHashes` will only be populated if `BuildOptions` has requested a
    `SourceProvenanceHash`. The keys to this map are file paths used as build
    source and the values contain the hash values for those files. If the
    build source came in a single package such as a gzipped tarfile
    (`.tar.gz`), the `FileHash` will be for the single path to that file.

    Messages:
      AdditionalProperty: An additional property for a FileHashesValue object.

    Fields:
      additionalProperties: Additional properties of type FileHashesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FileHashesValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleDevtoolsCloudbuildV1FileHashes attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleDevtoolsCloudbuildV1FileHashes', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  fileHashes = _messages.MessageField('FileHashesValue', 1)
  resolvedConnectedRepository = _messages.MessageField('GoogleDevtoolsCloudbuildV1ConnectedRepository', 2)
  resolvedGitSource = _messages.MessageField('GoogleDevtoolsCloudbuildV1GitSource', 3)
  resolvedRepoSource = _messages.MessageField('GoogleDevtoolsCloudbuildV1RepoSource', 4)
  resolvedStorageSource = _messages.MessageField('GoogleDevtoolsCloudbuildV1StorageSource', 5)
  resolvedStorageSourceManifest = _messages.MessageField('GoogleDevtoolsCloudbuildV1StorageSourceManifest', 6)


class GoogleDevtoolsCloudbuildV1StorageSource(_messages.Message):
  r"""Location of the source in an archive file in Cloud Storage.

  Enums:
    SourceFetcherValueValuesEnum: Optional. Option to specify the tool to
      fetch the source file for the build.

  Fields:
    bucket: Cloud Storage bucket containing the source (see [Bucket Name
      Requirements](https://cloud.google.com/storage/docs/bucket-
      naming#requirements)).
    generation: Optional. Cloud Storage generation for the object. If the
      generation is omitted, the latest generation will be used.
    object: Required. Cloud Storage object containing the source. This object
      must be a zipped (`.zip`) or gzipped archive file (`.tar.gz`) containing
      source to build.
    sourceFetcher: Optional. Option to specify the tool to fetch the source
      file for the build.
  """

  class SourceFetcherValueValuesEnum(_messages.Enum):
    r"""Optional. Option to specify the tool to fetch the source file for the
    build.

    Values:
      SOURCE_FETCHER_UNSPECIFIED: Unspecified defaults to GSUTIL.
      GSUTIL: Use the "gsutil" tool to download the source file.
      GCS_FETCHER: Use the Cloud Storage Fetcher tool to download the source
        file.
    """
    SOURCE_FETCHER_UNSPECIFIED = 0
    GSUTIL = 1
    GCS_FETCHER = 2

  bucket = _messages.StringField(1)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3)
  sourceFetcher = _messages.EnumField('SourceFetcherValueValuesEnum', 4)


class GoogleDevtoolsCloudbuildV1StorageSourceManifest(_messages.Message):
  r"""Location of the source manifest in Cloud Storage. This feature is in
  Preview; see description
  [here](https://github.com/GoogleCloudPlatform/cloud-
  builders/tree/master/gcs-fetcher).

  Fields:
    bucket: Required. Cloud Storage bucket containing the source manifest (see
      [Bucket Name Requirements](https://cloud.google.com/storage/docs/bucket-
      naming#requirements)).
    generation: Cloud Storage generation for the object. If the generation is
      omitted, the latest generation will be used.
    object: Required. Cloud Storage object containing the source manifest.
      This object must be a JSON file.
  """

  bucket = _messages.StringField(1)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3)


class GoogleDevtoolsCloudbuildV1TimeSpan(_messages.Message):
  r"""Start and end times for a build execution phase.

  Fields:
    endTime: End of time span.
    startTime: Start of time span.
  """

  endTime = _messages.StringField(1)
  startTime = _messages.StringField(2)


class GoogleDevtoolsCloudbuildV1UploadedGoModule(_messages.Message):
  r"""A Go module artifact uploaded to Artifact Registry using the GoModule
  directive.

  Fields:
    fileHashes: Hash types and values of the Go Module Artifact.
    pushTiming: Output only. Stores timing information for pushing the
      specified artifact.
    uri: URI of the uploaded artifact.
  """

  fileHashes = _messages.MessageField('GoogleDevtoolsCloudbuildV1FileHashes', 1)
  pushTiming = _messages.MessageField('GoogleDevtoolsCloudbuildV1TimeSpan', 2)
  uri = _messages.StringField(3)


class GoogleDevtoolsCloudbuildV1UploadedMavenArtifact(_messages.Message):
  r"""A Maven artifact uploaded using the MavenArtifact directive.

  Fields:
    fileHashes: Hash types and values of the Maven Artifact.
    pushTiming: Output only. Stores timing information for pushing the
      specified artifact.
    uri: URI of the uploaded artifact.
  """

  fileHashes = _messages.MessageField('GoogleDevtoolsCloudbuildV1FileHashes', 1)
  pushTiming = _messages.MessageField('GoogleDevtoolsCloudbuildV1TimeSpan', 2)
  uri = _messages.StringField(3)


class GoogleDevtoolsCloudbuildV1UploadedNpmPackage(_messages.Message):
  r"""An npm package uploaded to Artifact Registry using the NpmPackage
  directive.

  Fields:
    fileHashes: Hash types and values of the npm package.
    pushTiming: Output only. Stores timing information for pushing the
      specified artifact.
    uri: URI of the uploaded npm package.
  """

  fileHashes = _messages.MessageField('GoogleDevtoolsCloudbuildV1FileHashes', 1)
  pushTiming = _messages.MessageField('GoogleDevtoolsCloudbuildV1TimeSpan', 2)
  uri = _messages.StringField(3)


class GoogleDevtoolsCloudbuildV1UploadedPythonPackage(_messages.Message):
  r"""Artifact uploaded using the PythonPackage directive.

  Fields:
    fileHashes: Hash types and values of the Python Artifact.
    pushTiming: Output only. Stores timing information for pushing the
      specified artifact.
    uri: URI of the uploaded artifact.
  """

  fileHashes = _messages.MessageField('GoogleDevtoolsCloudbuildV1FileHashes', 1)
  pushTiming = _messages.MessageField('GoogleDevtoolsCloudbuildV1TimeSpan', 2)
  uri = _messages.StringField(3)


class GoogleDevtoolsCloudbuildV1Volume(_messages.Message):
  r"""Volume describes a Docker container volume which is mounted into build
  steps in order to persist files across build step execution.

  Fields:
    name: Name of the volume to mount. Volume names must be unique per build
      step and must be valid names for Docker volumes. Each named volume must
      be used by at least two build steps.
    path: Path at which to mount the volume. Paths must be absolute and cannot
      conflict with other volume paths on the same build step or with certain
      reserved volume paths.
  """

  name = _messages.StringField(1)
  path = _messages.StringField(2)


class GoogleDevtoolsCloudbuildV1Warning(_messages.Message):
  r"""A non-fatal problem encountered during the execution of the build.

  Enums:
    PriorityValueValuesEnum: The priority for this warning.

  Fields:
    priority: The priority for this warning.
    text: Explanation of the warning generated.
  """

  class PriorityValueValuesEnum(_messages.Enum):
    r"""The priority for this warning.

    Values:
      PRIORITY_UNSPECIFIED: Should not be used.
      INFO: e.g. deprecation warnings and alternative feature highlights.
      WARNING: e.g. automated detection of possible issues with the build.
      ALERT: e.g. alerts that a feature used in the build is pending removal
    """
    PRIORITY_UNSPECIFIED = 0
    INFO = 1
    WARNING = 2
    ALERT = 3

  priority = _messages.EnumField('PriorityValueValuesEnum', 1)
  text = _messages.StringField(2)


class GoogleLongrunningListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('GoogleLongrunningOperation', 2, repeated=True)


class GoogleLongrunningOperation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('GoogleRpcStatus', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class GoogleLongrunningWaitOperationRequest(_messages.Message):
  r"""The request message for Operations.WaitOperation.

  Fields:
    timeout: The maximum duration to wait before timing out. If left blank,
      the wait will be at most the time permitted by the underlying HTTP/RPC
      protocol. If RPC context deadline is also specified, the shorter one
      will be used.
  """

  timeout = _messages.StringField(1)


class GoogleRpcStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class HTTPGetAction(_messages.Message):
  r"""HTTPGetAction describes an action based on HTTP Get requests.

  Fields:
    host: Not supported by Cloud Run.
    httpHeaders: Custom headers to set in the request. HTTP allows repeated
      headers.
    path: Path to access on the HTTP server.
    port: Port number to access on the container. Number must be in the range
      1 to 65535.
    scheme: Not supported by Cloud Run.
  """

  host = _messages.StringField(1)
  httpHeaders = _messages.MessageField('HTTPHeader', 2, repeated=True)
  path = _messages.StringField(3)
  port = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  scheme = _messages.StringField(5)


class HTTPHeader(_messages.Message):
  r"""HTTPHeader describes a custom header to be used in HTTP probes

  Fields:
    name: Required. The header field name
    value: The header field value
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class InstanceSplit(_messages.Message):
  r"""Holds a single instance split entry for the Worker. Allocations can be
  done to a specific Revision name, or pointing to the latest Ready Revision.

  Fields:
    latestRevision: Uses the "status.latestReadyRevisionName" to determine the
      instance split target. When it changes, workloads will automatically
      migrate from the prior "latest ready" revision to the new one.
    percent: Specifies percent of the instance split to this Revision. This
      defaults to zero if unspecified.
    revisionName: Revision to which to assign this portion of instances.
  """

  latestRevision = _messages.BooleanField(1)
  percent = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  revisionName = _messages.StringField(3)


class Job(_messages.Message):
  r"""Job represents the configuration of a single job, which references a
  container image which is run to completion.

  Fields:
    apiVersion: Optional. APIVersion defines the versioned schema of this
      representation of an object. Servers should convert recognized schemas
      to the latest internal value, and may reject unrecognized values.
    kind: Optional. Kind is a string value representing the REST resource this
      object represents. Servers may infer this from the endpoint the client
      submits requests to. Cannot be updated. In CamelCase.
    metadata: Optional. Standard object's metadata.
    spec: Optional. Specification of the desired behavior of a job.
    status: Output only. Current status of a job.
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  metadata = _messages.MessageField('ObjectMeta', 3)
  spec = _messages.MessageField('JobSpec', 4)
  status = _messages.MessageField('JobStatus', 5)


class JobSpec(_messages.Message):
  r"""JobSpec describes how the job will look.

  Fields:
    runExecutionToken: A unique string used as a suffix for creating a new
      execution. The Job will become ready when the execution is successfully
      completed. The sum of job name and token length must be fewer than 63
      characters.
    startExecutionToken: A unique string used as a suffix for creating a new
      execution. The Job will become ready when the execution is successfully
      started. The sum of job name and token length must be fewer than 63
      characters.
    template: Optional. Describes the execution that will be created when
      running a job.
  """

  runExecutionToken = _messages.StringField(1)
  startExecutionToken = _messages.StringField(2)
  template = _messages.MessageField('ExecutionTemplateSpec', 3)


class JobStatus(_messages.Message):
  r"""JobStatus represents the current state of a Job.

  Fields:
    conditions: Conditions communicate information about ongoing/complete
      reconciliation processes that bring the "spec" inline with the observed
      state of the world. Job-specific conditions include: * `Ready`: `True`
      when the job is ready to be executed.
    executionCount: Number of executions created for this job.
    latestCreatedExecution: A pointer to the most recently created execution
      for this job. This is set regardless of the eventual state of the
      execution.
    observedGeneration: The 'generation' of the job that was last processed by
      the controller.
  """

  conditions = _messages.MessageField('GoogleCloudRunV1Condition', 1, repeated=True)
  executionCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  latestCreatedExecution = _messages.MessageField('ExecutionReference', 3)
  observedGeneration = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class KeyToPath(_messages.Message):
  r"""Maps a string key to a path within a volume.

  Fields:
    key: The Cloud Secret Manager secret version. Can be 'latest' for the
      latest value, or an integer or a secret alias for a specific version.
      The key to project.
    mode: (Optional) Mode bits to use on this file, must be a value between 01
      and 0777 (octal). If 0 or not set, the Volume's default mode will be
      used. Notes * Internally, a umask of 0222 will be applied to any non-
      zero value. * This is an integer representation of the mode bits. So,
      the octal integer value should look exactly as the chmod numeric
      notation with a leading zero. Some examples: for chmod 777 (a=rwx), set
      to 0777 (octal) or 511 (base-10). For chmod 640 (u=rw,g=r), set to 0640
      (octal) or 416 (base-10). For chmod 755 (u=rwx,g=rx,o=rx), set to 0755
      (octal) or 493 (base-10). * This might be in conflict with other options
      that affect the file mode, like fsGroup, and the result can be other
      mode bits set.
    path: The relative path of the file to map the key to. May not be an
      absolute path. May not contain the path element '..'. May not start with
      the string '..'.
  """

  key = _messages.StringField(1)
  mode = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  path = _messages.StringField(3)


class ListAuthorizedDomainsResponse(_messages.Message):
  r"""A list of Authorized Domains.

  Fields:
    domains: The authorized domains belonging to the user.
    nextPageToken: Continuation token for fetching the next page of results.
  """

  domains = _messages.MessageField('AuthorizedDomain', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListConfigurationsResponse(_messages.Message):
  r"""ListConfigurationsResponse is a list of Configuration resources.

  Fields:
    apiVersion: The API version for this call such as
      "serving.knative.dev/v1".
    items: List of Configurations.
    kind: The kind of this resource, in this case "ConfigurationList".
    metadata: Metadata associated with this Configuration list.
    unreachable: Locations that could not be reached.
  """

  apiVersion = _messages.StringField(1)
  items = _messages.MessageField('Configuration', 2, repeated=True)
  kind = _messages.StringField(3)
  metadata = _messages.MessageField('ListMeta', 4)
  unreachable = _messages.StringField(5, repeated=True)


class ListDomainMappingsResponse(_messages.Message):
  r"""ListDomainMappingsResponse is a list of DomainMapping resources.

  Fields:
    apiVersion: The API version for this call such as
      "domains.cloudrun.com/v1".
    items: List of DomainMappings.
    kind: The kind of this resource, in this case "DomainMappingList".
    metadata: Metadata associated with this DomainMapping list.
    unreachable: Locations that could not be reached.
  """

  apiVersion = _messages.StringField(1)
  items = _messages.MessageField('DomainMapping', 2, repeated=True)
  kind = _messages.StringField(3)
  metadata = _messages.MessageField('ListMeta', 4)
  unreachable = _messages.StringField(5, repeated=True)


class ListExecutionsResponse(_messages.Message):
  r"""ListExecutionsResponse is a list of Executions resources.

  Fields:
    apiVersion: The API version for this call such as "run.googleapis.com/v1".
    items: List of Executions.
    kind: The kind of this resource, in this case "ExecutionsList".
    metadata: Metadata associated with this executions list.
    unreachable: Locations that could not be reached.
  """

  apiVersion = _messages.StringField(1)
  items = _messages.MessageField('Execution', 2, repeated=True)
  kind = _messages.StringField(3)
  metadata = _messages.MessageField('ListMeta', 4)
  unreachable = _messages.StringField(5, repeated=True)


class ListJobsResponse(_messages.Message):
  r"""ListJobsResponse is a list of Jobs resources.

  Fields:
    apiVersion: The API version for this call such as "run.googleapis.com/v1".
    items: List of Jobs.
    kind: The kind of this resource, in this case "JobsList".
    metadata: Metadata associated with this jobs list.
    unreachable: Locations that could not be reached.
  """

  apiVersion = _messages.StringField(1)
  items = _messages.MessageField('Job', 2, repeated=True)
  kind = _messages.StringField(3)
  metadata = _messages.MessageField('ListMeta', 4)
  unreachable = _messages.StringField(5, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListMeta(_messages.Message):
  r"""Metadata for synthetic resources like List. In Cloud Run, all List
  Resources Responses will have a ListMeta instead of ObjectMeta.

  Fields:
    continue_: Continuation token is a value emitted when the count of items
      is larger than the user/system limit. To retrieve the next page of
      items, pass the value of `continue` as the next request's `page_token`.
    resourceVersion: Opaque string that identifies the server's internal
      version of this object. It can be used by clients to determine when
      objects have changed. If the message is passed back to the server, it
      must be left unmodified.
    selfLink: URL representing this object.
  """

  continue_ = _messages.StringField(1)
  resourceVersion = _messages.StringField(2)
  selfLink = _messages.StringField(3)


class ListRevisionsResponse(_messages.Message):
  r"""ListRevisionsResponse is a list of Revision resources.

  Fields:
    apiVersion: The API version for this call such as
      "serving.knative.dev/v1".
    items: List of Revisions.
    kind: The kind of this resource, in this case "RevisionList".
    metadata: Metadata associated with this revision list.
    unreachable: Locations that could not be reached.
  """

  apiVersion = _messages.StringField(1)
  items = _messages.MessageField('Revision', 2, repeated=True)
  kind = _messages.StringField(3)
  metadata = _messages.MessageField('ListMeta', 4)
  unreachable = _messages.StringField(5, repeated=True)


class ListRoutesResponse(_messages.Message):
  r"""ListRoutesResponse is a list of Route resources.

  Fields:
    apiVersion: The API version for this call such as
      "serving.knative.dev/v1".
    items: List of Routes.
    kind: The kind of this resource, in this case always "RouteList".
    metadata: Metadata associated with this Route list.
    unreachable: Locations that could not be reached.
  """

  apiVersion = _messages.StringField(1)
  items = _messages.MessageField('Route', 2, repeated=True)
  kind = _messages.StringField(3)
  metadata = _messages.MessageField('ListMeta', 4)
  unreachable = _messages.StringField(5, repeated=True)


class ListServicesResponse(_messages.Message):
  r"""A list of Service resources.

  Fields:
    apiVersion: The API version for this call; returns
      "serving.knative.dev/v1".
    items: List of Services.
    kind: The kind of this resource; returns "ServiceList".
    metadata: Metadata associated with this Service list.
    unreachable: For calls against the global endpoint, returns the list of
      Cloud locations that could not be reached. For regional calls, this
      field is not used.
  """

  apiVersion = _messages.StringField(1)
  items = _messages.MessageField('Service', 2, repeated=True)
  kind = _messages.StringField(3)
  metadata = _messages.MessageField('ListMeta', 4)
  unreachable = _messages.StringField(5, repeated=True)


class ListTasksResponse(_messages.Message):
  r"""ListTasksResponse is a list of Tasks resources.

  Fields:
    apiVersion: The API version for this call such as "run.googleapis.com/v1".
    items: List of Tasks.
    kind: The kind of this resource, in this case "TasksList".
    metadata: Metadata associated with this tasks list.
    unreachable: Locations that could not be reached.
  """

  apiVersion = _messages.StringField(1)
  items = _messages.MessageField('Task', 2, repeated=True)
  kind = _messages.StringField(3)
  metadata = _messages.MessageField('ListMeta', 4)
  unreachable = _messages.StringField(5, repeated=True)


class ListWorkerPoolsResponse(_messages.Message):
  r"""A list of WorkerPool resources.

  Fields:
    apiVersion: The API version for this call; returns
      "run.googleapis.com/v1".
    items: List of WorkerPools.
    kind: The kind of this resource; returns "WorkerPoolList".
    metadata: Metadata associated with this WorkerPool list.
    unreachable: For calls against the global endpoint, returns the list of
      Cloud locations that could not be reached. For regional calls, this
      field is not used.
  """

  apiVersion = _messages.StringField(1)
  items = _messages.MessageField('WorkerPool', 2, repeated=True)
  kind = _messages.StringField(3)
  metadata = _messages.MessageField('ListMeta', 4)
  unreachable = _messages.StringField(5, repeated=True)


class LocalObjectReference(_messages.Message):
  r"""Not supported by Cloud Run. LocalObjectReference contains enough
  information to let you locate the referenced object inside the same
  namespace.

  Fields:
    name: Name of the referent.
  """

  name = _messages.StringField(1)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class NFSVolumeSource(_messages.Message):
  r"""Represents a persistent volume that will be mounted using NFS. This
  volume will be shared between all instances of the resource and data will
  not be deleted when the instance is shut down.

  Fields:
    path: Path that is exported by the NFS server.
    readOnly: If true, mount the NFS volume as read only. Defaults to false.
    server: Hostname or IP address of the NFS server.
  """

  path = _messages.StringField(1)
  readOnly = _messages.BooleanField(2)
  server = _messages.StringField(3)


class ObjectMeta(_messages.Message):
  r"""google.cloud.run.meta.v1.ObjectMeta is metadata that all persisted
  resources must have, which includes all objects users must create.

  Messages:
    AnnotationsValue: Unstructured key value map stored with a resource that
      may be set by external tools to store and retrieve arbitrary metadata.
      They are not queryable and should be preserved when modifying objects.
      In Cloud Run, annotations with 'run.googleapis.com/' and
      'autoscaling.knative.dev' are restricted, and the accepted annotations
      will be different depending on the resource type. *
      `autoscaling.knative.dev/maxScale`: Revision. *
      `autoscaling.knative.dev/minScale`: Revision. *
      `run.googleapis.com/base-images`: Service, Revision. *
      `run.googleapis.com/binary-authorization-breakglass`: Service, Job, *
      `run.googleapis.com/binary-authorization`: Service, Job, Execution. *
      `run.googleapis.com/build-base-image`: Service. *
      `run.googleapis.com/build-enable-automatic-updates`: Service. *
      `run.googleapis.com/build-environment-variables`: Service. *
      `run.googleapis.com/build-function-target`: Service. *
      `run.googleapis.com/build-id`: Service. * `run.googleapis.com/build-
      image-uri`: Service. * `run.googleapis.com/build-name`: Service. *
      `run.googleapis.com/build-service-account`: Service. *
      `run.googleapis.com/build-source-location`: Service. *
      `run.googleapis.com/build-worker-pool`: Service. *
      `run.googleapis.com/client-name`: All resources. *
      `run.googleapis.com/cloudsql-instances`: Revision, Execution. *
      `run.googleapis.com/container-dependencies`: Revision . *
      `run.googleapis.com/cpu-throttling`: Revision. *
      `run.googleapis.com/custom-audiences`: Service. *
      `run.googleapis.com/default-url-disabled`: Service. *
      `run.googleapis.com/description`: Service. *
      `run.googleapis.com/encryption-key-shutdown-hours`: Revision *
      `run.googleapis.com/encryption-key`: Revision, Execution. *
      `run.googleapis.com/execution-environment`: Revision, Execution. *
      `run.googleapis.com/gc-traffic-tags`: Service. *
      `run.googleapis.com/gpu-zonal-redundancy-disabled`: Revision. *
      `run.googleapis.com/health-check-disabled`: Revision. *
      `run.googleapis.com/ingress`: Service. * `run.googleapis.com/launch-
      stage`: Service, Job. * `run.googleapis.com/minScale`: Service *
      `run.googleapis.com/network-interfaces`: Revision, Execution. *
      `run.googleapis.com/post-key-revocation-action-type`: Revision. *
      `run.googleapis.com/secrets`: Revision, Execution. *
      `run.googleapis.com/secure-session-agent`: Revision. *
      `run.googleapis.com/sessionAffinity`: Revision. *
      `run.googleapis.com/startup-cpu-boost`: Revision. *
      `run.googleapis.com/vpc-access-connector`: Revision, Execution. *
      `run.googleapis.com/vpc-access-egress`: Revision, Execution.
    LabelsValue: Map of string keys and values that can be used to organize
      and categorize (scope and select) objects. May match selectors of
      replication controllers and routes.

  Fields:
    annotations: Unstructured key value map stored with a resource that may be
      set by external tools to store and retrieve arbitrary metadata. They are
      not queryable and should be preserved when modifying objects. In Cloud
      Run, annotations with 'run.googleapis.com/' and
      'autoscaling.knative.dev' are restricted, and the accepted annotations
      will be different depending on the resource type. *
      `autoscaling.knative.dev/maxScale`: Revision. *
      `autoscaling.knative.dev/minScale`: Revision. *
      `run.googleapis.com/base-images`: Service, Revision. *
      `run.googleapis.com/binary-authorization-breakglass`: Service, Job, *
      `run.googleapis.com/binary-authorization`: Service, Job, Execution. *
      `run.googleapis.com/build-base-image`: Service. *
      `run.googleapis.com/build-enable-automatic-updates`: Service. *
      `run.googleapis.com/build-environment-variables`: Service. *
      `run.googleapis.com/build-function-target`: Service. *
      `run.googleapis.com/build-id`: Service. * `run.googleapis.com/build-
      image-uri`: Service. * `run.googleapis.com/build-name`: Service. *
      `run.googleapis.com/build-service-account`: Service. *
      `run.googleapis.com/build-source-location`: Service. *
      `run.googleapis.com/build-worker-pool`: Service. *
      `run.googleapis.com/client-name`: All resources. *
      `run.googleapis.com/cloudsql-instances`: Revision, Execution. *
      `run.googleapis.com/container-dependencies`: Revision . *
      `run.googleapis.com/cpu-throttling`: Revision. *
      `run.googleapis.com/custom-audiences`: Service. *
      `run.googleapis.com/default-url-disabled`: Service. *
      `run.googleapis.com/description`: Service. *
      `run.googleapis.com/encryption-key-shutdown-hours`: Revision *
      `run.googleapis.com/encryption-key`: Revision, Execution. *
      `run.googleapis.com/execution-environment`: Revision, Execution. *
      `run.googleapis.com/gc-traffic-tags`: Service. *
      `run.googleapis.com/gpu-zonal-redundancy-disabled`: Revision. *
      `run.googleapis.com/health-check-disabled`: Revision. *
      `run.googleapis.com/ingress`: Service. * `run.googleapis.com/launch-
      stage`: Service, Job. * `run.googleapis.com/minScale`: Service *
      `run.googleapis.com/network-interfaces`: Revision, Execution. *
      `run.googleapis.com/post-key-revocation-action-type`: Revision. *
      `run.googleapis.com/secrets`: Revision, Execution. *
      `run.googleapis.com/secure-session-agent`: Revision. *
      `run.googleapis.com/sessionAffinity`: Revision. *
      `run.googleapis.com/startup-cpu-boost`: Revision. *
      `run.googleapis.com/vpc-access-connector`: Revision, Execution. *
      `run.googleapis.com/vpc-access-egress`: Revision, Execution.
    clusterName: Not supported by Cloud Run
    creationTimestamp: UTC timestamp representing the server time when this
      object was created.
    deletionGracePeriodSeconds: Not supported by Cloud Run
    deletionTimestamp: The read-only soft deletion timestamp for this
      resource. In Cloud Run, users are not able to set this field. Instead,
      they must call the corresponding Delete API.
    finalizers: Not supported by Cloud Run
    generateName: Not supported by Cloud Run
    generation: A system-provided sequence number representing a specific
      generation of the desired state.
    labels: Map of string keys and values that can be used to organize and
      categorize (scope and select) objects. May match selectors of
      replication controllers and routes.
    name: Required. The name of the resource. Name is required when creating
      top-level resources (Service, Job), must be unique within a Cloud Run
      project/region, and cannot be changed once created.
    namespace: Required. Defines the space within each name must be unique
      within a Cloud Run region. In Cloud Run, it must be project ID or
      number.
    ownerReferences: Not supported by Cloud Run
    resourceVersion: Opaque, system-generated value that represents the
      internal version of this object that can be used by clients to determine
      when objects have changed. May be used for optimistic concurrency,
      change detection, and the watch operation on a resource or set of
      resources. Clients must treat these values as opaque and passed
      unmodified back to the server or omit the value to disable conflict-
      detection.
    selfLink: URL representing this object.
    uid: Unique, system-generated identifier for this resource.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Unstructured key value map stored with a resource that may be set by
    external tools to store and retrieve arbitrary metadata. They are not
    queryable and should be preserved when modifying objects. In Cloud Run,
    annotations with 'run.googleapis.com/' and 'autoscaling.knative.dev' are
    restricted, and the accepted annotations will be different depending on
    the resource type. * `autoscaling.knative.dev/maxScale`: Revision. *
    `autoscaling.knative.dev/minScale`: Revision. * `run.googleapis.com/base-
    images`: Service, Revision. * `run.googleapis.com/binary-authorization-
    breakglass`: Service, Job, * `run.googleapis.com/binary-authorization`:
    Service, Job, Execution. * `run.googleapis.com/build-base-image`: Service.
    * `run.googleapis.com/build-enable-automatic-updates`: Service. *
    `run.googleapis.com/build-environment-variables`: Service. *
    `run.googleapis.com/build-function-target`: Service. *
    `run.googleapis.com/build-id`: Service. * `run.googleapis.com/build-image-
    uri`: Service. * `run.googleapis.com/build-name`: Service. *
    `run.googleapis.com/build-service-account`: Service. *
    `run.googleapis.com/build-source-location`: Service. *
    `run.googleapis.com/build-worker-pool`: Service. *
    `run.googleapis.com/client-name`: All resources. *
    `run.googleapis.com/cloudsql-instances`: Revision, Execution. *
    `run.googleapis.com/container-dependencies`: Revision . *
    `run.googleapis.com/cpu-throttling`: Revision. *
    `run.googleapis.com/custom-audiences`: Service. *
    `run.googleapis.com/default-url-disabled`: Service. *
    `run.googleapis.com/description`: Service. *
    `run.googleapis.com/encryption-key-shutdown-hours`: Revision *
    `run.googleapis.com/encryption-key`: Revision, Execution. *
    `run.googleapis.com/execution-environment`: Revision, Execution. *
    `run.googleapis.com/gc-traffic-tags`: Service. * `run.googleapis.com/gpu-
    zonal-redundancy-disabled`: Revision. * `run.googleapis.com/health-check-
    disabled`: Revision. * `run.googleapis.com/ingress`: Service. *
    `run.googleapis.com/launch-stage`: Service, Job. *
    `run.googleapis.com/minScale`: Service * `run.googleapis.com/network-
    interfaces`: Revision, Execution. * `run.googleapis.com/post-key-
    revocation-action-type`: Revision. * `run.googleapis.com/secrets`:
    Revision, Execution. * `run.googleapis.com/secure-session-agent`:
    Revision. * `run.googleapis.com/sessionAffinity`: Revision. *
    `run.googleapis.com/startup-cpu-boost`: Revision. *
    `run.googleapis.com/vpc-access-connector`: Revision, Execution. *
    `run.googleapis.com/vpc-access-egress`: Revision, Execution.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Map of string keys and values that can be used to organize and
    categorize (scope and select) objects. May match selectors of replication
    controllers and routes.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  clusterName = _messages.StringField(2)
  creationTimestamp = _messages.StringField(3)
  deletionGracePeriodSeconds = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  deletionTimestamp = _messages.StringField(5)
  finalizers = _messages.StringField(6, repeated=True)
  generateName = _messages.StringField(7)
  generation = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  labels = _messages.MessageField('LabelsValue', 9)
  name = _messages.StringField(10)
  namespace = _messages.StringField(11)
  ownerReferences = _messages.MessageField('OwnerReference', 12, repeated=True)
  resourceVersion = _messages.StringField(13)
  selfLink = _messages.StringField(14)
  uid = _messages.StringField(15)


class Overrides(_messages.Message):
  r"""RunJob Overrides that contains Execution fields to be overridden on the
  go.

  Fields:
    containerOverrides: Per container override specification.
    taskCount: The desired number of tasks the execution should run. Will
      replace existing task_count value.
    timeoutSeconds: Duration in seconds the task may be active before the
      system will actively try to mark it failed and kill associated
      containers. Will replace existing timeout_seconds value.
  """

  containerOverrides = _messages.MessageField('ContainerOverride', 1, repeated=True)
  taskCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  timeoutSeconds = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class OwnerReference(_messages.Message):
  r"""This is not supported or used by Cloud Run.

  Fields:
    apiVersion: This is not supported or used by Cloud Run.
    blockOwnerDeletion: This is not supported or used by Cloud Run.
    controller: This is not supported or used by Cloud Run.
    kind: This is not supported or used by Cloud Run.
    name: This is not supported or used by Cloud Run.
    uid: This is not supported or used by Cloud Run.
  """

  apiVersion = _messages.StringField(1)
  blockOwnerDeletion = _messages.BooleanField(2)
  controller = _messages.BooleanField(3)
  kind = _messages.StringField(4)
  name = _messages.StringField(5)
  uid = _messages.StringField(6)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class Probe(_messages.Message):
  r"""Probe describes a health check to be performed against a container to
  determine whether it is alive or ready to receive traffic.

  Fields:
    exec_: Not supported by Cloud Run.
    failureThreshold: Minimum consecutive failures for the probe to be
      considered failed after having succeeded. Defaults to 3. Minimum value
      is 1.
    grpc: GRPCAction specifies an action involving a GRPC port.
    httpGet: HTTPGet specifies the http request to perform.
    initialDelaySeconds: Number of seconds after the container has started
      before the probe is initiated. Defaults to 0 seconds. Minimum value is
      0. Maximum value for liveness probe is 3600. Maximum value for startup
      probe is 240.
    periodSeconds: How often (in seconds) to perform the probe. Default to 10
      seconds. Minimum value is 1. Maximum value for liveness probe is 3600.
      Maximum value for startup probe is 240. Must be greater or equal than
      timeout_seconds.
    successThreshold: Minimum consecutive successes for the probe to be
      considered successful after having failed. Must be 1 if set.
    tcpSocket: TCPSocket specifies an action involving a TCP port.
    timeoutSeconds: Number of seconds after which the probe times out.
      Defaults to 1 second. Minimum value is 1. Maximum value is 3600. Must be
      smaller than period_seconds; if period_seconds is not set, must be less
      or equal than 10.
  """

  exec_ = _messages.MessageField('ExecAction', 1)
  failureThreshold = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  grpc = _messages.MessageField('GRPCAction', 3)
  httpGet = _messages.MessageField('HTTPGetAction', 4)
  initialDelaySeconds = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  periodSeconds = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  successThreshold = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  tcpSocket = _messages.MessageField('TCPSocketAction', 8)
  timeoutSeconds = _messages.IntegerField(9, variant=_messages.Variant.INT32)


class ResourceRecord(_messages.Message):
  r"""A DNS resource record.

  Enums:
    TypeValueValuesEnum: Resource record type. Example: `AAAA`.

  Fields:
    name: Relative name of the object affected by this record. Only applicable
      for `CNAME` records. Example: 'www'.
    rrdata: Data for this record. Values vary by record type, as defined in
      RFC 1035 (section 5) and RFC 1034 (section 3.6.1).
    type: Resource record type. Example: `AAAA`.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Resource record type. Example: `AAAA`.

    Values:
      RECORD_TYPE_UNSPECIFIED: An unknown resource record.
      A: An A resource record. Data is an IPv4 address.
      AAAA: An AAAA resource record. Data is an IPv6 address.
      CNAME: A CNAME resource record. Data is a domain name to be aliased.
    """
    RECORD_TYPE_UNSPECIFIED = 0
    A = 1
    AAAA = 2
    CNAME = 3

  name = _messages.StringField(1)
  rrdata = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class ResourceRequirements(_messages.Message):
  r"""ResourceRequirements describes the compute resource requirements.

  Messages:
    LimitsValue: Limits describes the maximum amount of compute resources
      allowed. Only 'cpu' and 'memory' keys are supported. * For supported
      'cpu' values, go to https://cloud.google.com/run/docs/configuring/cpu. *
      For supported 'memory' values and syntax, go to
      https://cloud.google.com/run/docs/configuring/memory-limits
    RequestsValue: Requests describes the minimum amount of compute resources
      required. Only `cpu` and `memory` are supported. If Requests is omitted
      for a container, it defaults to Limits if that is explicitly specified,
      otherwise to an implementation-defined value. * For supported 'cpu'
      values, go to https://cloud.google.com/run/docs/configuring/cpu. * For
      supported 'memory' values and syntax, go to
      https://cloud.google.com/run/docs/configuring/memory-limits

  Fields:
    limits: Limits describes the maximum amount of compute resources allowed.
      Only 'cpu' and 'memory' keys are supported. * For supported 'cpu'
      values, go to https://cloud.google.com/run/docs/configuring/cpu. * For
      supported 'memory' values and syntax, go to
      https://cloud.google.com/run/docs/configuring/memory-limits
    requests: Requests describes the minimum amount of compute resources
      required. Only `cpu` and `memory` are supported. If Requests is omitted
      for a container, it defaults to Limits if that is explicitly specified,
      otherwise to an implementation-defined value. * For supported 'cpu'
      values, go to https://cloud.google.com/run/docs/configuring/cpu. * For
      supported 'memory' values and syntax, go to
      https://cloud.google.com/run/docs/configuring/memory-limits
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LimitsValue(_messages.Message):
    r"""Limits describes the maximum amount of compute resources allowed. Only
    'cpu' and 'memory' keys are supported. * For supported 'cpu' values, go to
    https://cloud.google.com/run/docs/configuring/cpu. * For supported
    'memory' values and syntax, go to
    https://cloud.google.com/run/docs/configuring/memory-limits

    Messages:
      AdditionalProperty: An additional property for a LimitsValue object.

    Fields:
      additionalProperties: Additional properties of type LimitsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LimitsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class RequestsValue(_messages.Message):
    r"""Requests describes the minimum amount of compute resources required.
    Only `cpu` and `memory` are supported. If Requests is omitted for a
    container, it defaults to Limits if that is explicitly specified,
    otherwise to an implementation-defined value. * For supported 'cpu'
    values, go to https://cloud.google.com/run/docs/configuring/cpu. * For
    supported 'memory' values and syntax, go to
    https://cloud.google.com/run/docs/configuring/memory-limits

    Messages:
      AdditionalProperty: An additional property for a RequestsValue object.

    Fields:
      additionalProperties: Additional properties of type RequestsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a RequestsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  limits = _messages.MessageField('LimitsValue', 1)
  requests = _messages.MessageField('RequestsValue', 2)


class Revision(_messages.Message):
  r"""Revision is an immutable snapshot of code and configuration. A revision
  references a container image. Revisions are created by updates to a
  Configuration. See also: https://github.com/knative/specs/blob/main/specs/se
  rving/overview.md#revision

  Fields:
    apiVersion: The API version for this call such as
      "serving.knative.dev/v1".
    kind: The kind of this resource, in this case "Revision".
    metadata: Metadata associated with this Revision, including name,
      namespace, labels, and annotations.
    spec: Spec holds the desired state of the Revision (from the client).
    status: Status communicates the observed state of the Revision (from the
      controller).
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  metadata = _messages.MessageField('ObjectMeta', 3)
  spec = _messages.MessageField('RevisionSpec', 4)
  status = _messages.MessageField('RevisionStatus', 5)


class RevisionSpec(_messages.Message):
  r"""RevisionSpec holds the desired state of the Revision (from the client).

  Messages:
    NodeSelectorValue: Optional. The Node Selector configuration. Map of
      selector key to a value which matches a node.

  Fields:
    containerConcurrency: ContainerConcurrency specifies the maximum allowed
      in-flight (concurrent) requests per container instance of the Revision.
      If not specified or 0, defaults to 80 when requested CPU >= 1 and
      defaults to 1 when requested CPU < 1.
    containers: Required. Containers holds the list which define the units of
      execution for this Revision. In the context of a Revision, we disallow a
      number of fields on this Container, including: name and lifecycle.
    enableServiceLinks: Not supported by Cloud Run.
    imagePullSecrets: Not supported by Cloud Run.
    nodeSelector: Optional. The Node Selector configuration. Map of selector
      key to a value which matches a node.
    runtimeClassName: Runtime. Leave unset for default.
    serviceAccountName: Email address of the IAM service account associated
      with the revision of the service. The service account represents the
      identity of the running revision, and determines what permissions the
      revision has. If not provided, the revision will use the project's
      default service account.
    timeoutSeconds: Optional. TimeoutSeconds holds the max duration the
      instance is allowed for responding to a request. Cloud Run: defaults to
      300 seconds (5 minutes). Maximum allowed value is 3600 seconds (1 hour).
    volumes: A Volume attribute.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class NodeSelectorValue(_messages.Message):
    r"""Optional. The Node Selector configuration. Map of selector key to a
    value which matches a node.

    Messages:
      AdditionalProperty: An additional property for a NodeSelectorValue
        object.

    Fields:
      additionalProperties: Additional properties of type NodeSelectorValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a NodeSelectorValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  containerConcurrency = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  containers = _messages.MessageField('Container', 2, repeated=True)
  enableServiceLinks = _messages.BooleanField(3)
  imagePullSecrets = _messages.MessageField('LocalObjectReference', 4, repeated=True)
  nodeSelector = _messages.MessageField('NodeSelectorValue', 5)
  runtimeClassName = _messages.StringField(6)
  serviceAccountName = _messages.StringField(7)
  timeoutSeconds = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  volumes = _messages.MessageField('Volume', 9, repeated=True)


class RevisionStatus(_messages.Message):
  r"""RevisionStatus communicates the observed state of the Revision (from the
  controller).

  Fields:
    conditions: Conditions communicate information about ongoing/complete
      reconciliation processes that bring the "spec" inline with the observed
      state of the world. As a Revision is being prepared, it will
      incrementally update conditions. Revision-specific conditions include: *
      `ResourcesAvailable`: `True` when underlying resources have been
      provisioned. * `ContainerHealthy`: `True` when the Revision readiness
      check completes. * `Active`: `True` when the Revision may receive
      traffic.
    desiredReplicas: Output only. The configured number of instances running
      this revision. For Cloud Run, this only includes instances provisioned
      using the minScale annotation. It does not include instances created by
      autoscaling.
    imageDigest: ImageDigest holds the resolved digest for the image specified
      within .Spec.Container.Image. The digest is resolved during the creation
      of Revision. This field holds the digest value regardless of whether a
      tag or digest was originally specified in the Container object.
    logUrl: Optional. Specifies the generated logging url for this particular
      revision based on the revision url template specified in the
      controller's config.
    observedGeneration: ObservedGeneration is the 'Generation' of the Revision
      that was last processed by the controller. Clients polling for completed
      reconciliation should poll until observedGeneration =
      metadata.generation, and the Ready condition's status is True or False.
    serviceName: Not currently used by Cloud Run.
  """

  conditions = _messages.MessageField('GoogleCloudRunV1Condition', 1, repeated=True)
  desiredReplicas = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  imageDigest = _messages.StringField(3)
  logUrl = _messages.StringField(4)
  observedGeneration = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  serviceName = _messages.StringField(6)


class RevisionTemplate(_messages.Message):
  r"""RevisionTemplateSpec describes the data a revision should have when
  created from a template.

  Fields:
    metadata: Optional metadata for this Revision, including labels and
      annotations. Name will be generated by the Configuration. The following
      annotation keys set properties of the created revision: *
      `autoscaling.knative.dev/minScale` sets the minimum number of instances.
      * `autoscaling.knative.dev/maxScale` sets the maximum number of
      instances. * `run.googleapis.com/cloudsql-instances` sets Cloud SQL
      connections. Multiple values should be comma separated. *
      `run.googleapis.com/health-check-disabled`: if true, deploy-time startup
      probes will not run for this revision. * `run.googleapis.com/vpc-access-
      connector` sets a Serverless VPC Access connector. *
      `run.googleapis.com/vpc-access-egress` sets VPC egress. Supported values
      are `all-traffic`, `all` (deprecated), and `private-ranges-only`. `all-
      traffic` and `all` provide the same functionality. `all` is deprecated
      but will continue to be supported. Prefer `all-traffic`.
    spec: RevisionSpec holds the desired state of the Revision (from the
      client).
  """

  metadata = _messages.MessageField('ObjectMeta', 1)
  spec = _messages.MessageField('RevisionSpec', 2)


class Route(_messages.Message):
  r"""Route is responsible for configuring ingress over a collection of
  Revisions. Some of the Revisions a Route distributes traffic over may be
  specified by referencing the Configuration responsible for creating them; in
  these cases the Route is additionally responsible for monitoring the
  Configuration for "latest ready" revision changes, and smoothly rolling out
  latest revisions. Cloud Run currently supports referencing a single
  Configuration to automatically deploy the "latest ready" Revision from that
  Configuration.

  Fields:
    apiVersion: The API version for this call such as
      "serving.knative.dev/v1".
    kind: The kind of this resource, in this case always "Route".
    metadata: Metadata associated with this Route, including name, namespace,
      labels, and annotations.
    spec: Spec holds the desired state of the Route (from the client).
    status: Status communicates the observed state of the Route (from the
      controller).
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  metadata = _messages.MessageField('ObjectMeta', 3)
  spec = _messages.MessageField('RouteSpec', 4)
  status = _messages.MessageField('RouteStatus', 5)


class RouteSpec(_messages.Message):
  r"""RouteSpec holds the desired state of the Route (from the client).

  Fields:
    traffic: Traffic specifies how to distribute traffic over a collection of
      Knative Revisions and Configurations. Cloud Run currently supports a
      single configurationName.
  """

  traffic = _messages.MessageField('TrafficTarget', 1, repeated=True)


class RouteStatus(_messages.Message):
  r"""RouteStatus communicates the observed state of the Route (from the
  controller).

  Fields:
    address: Similar to url, information on where the service is available on
      HTTP.
    conditions: Conditions communicates information about ongoing/complete
      reconciliation processes that bring the "spec" inline with the observed
      state of the world.
    observedGeneration: ObservedGeneration is the 'Generation' of the Route
      that was last processed by the controller. Clients polling for completed
      reconciliation should poll until observedGeneration =
      metadata.generation and the Ready condition's status is True or False.
      Note that providing a TrafficTarget that has latest_revision=True will
      result in a Route that does not increment either its metadata.generation
      or its observedGeneration, as new "latest ready" revisions from the
      Configuration are processed without an update to the Route's spec.
    traffic: Traffic holds the configured traffic distribution. These entries
      will always contain RevisionName references. When ConfigurationName
      appears in the spec, this will hold the LatestReadyRevisionName that was
      last observed.
    url: URL holds the url that will distribute traffic over the provided
      traffic targets. It generally has the form: `https://{route-
      hash}-{project-hash}-{cluster-level-suffix}.a.run.app`
  """

  address = _messages.MessageField('Addressable', 1)
  conditions = _messages.MessageField('GoogleCloudRunV1Condition', 2, repeated=True)
  observedGeneration = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  traffic = _messages.MessageField('TrafficTarget', 4, repeated=True)
  url = _messages.StringField(5)


class RunJobRequest(_messages.Message):
  r"""Request message for creating a new execution of a job.

  Fields:
    overrides: Optional. Overrides existing job configuration for one specific
      new job execution only, using the specified values to update the job
      configuration for the new execution.
  """

  overrides = _messages.MessageField('Overrides', 1)


class RunNamespacesAuthorizeddomainsListRequest(_messages.Message):
  r"""A RunNamespacesAuthorizeddomainsListRequest object.

  Fields:
    pageSize: Maximum results to return per page.
    pageToken: Continuation token for fetching the next page of results.
    parent: Name of the parent Project resource. Example:
      `projects/myproject`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class RunNamespacesConfigurationsGetRequest(_messages.Message):
  r"""A RunNamespacesConfigurationsGetRequest object.

  Fields:
    name: The name of the configuration to retrieve. For Cloud Run, replace
      {namespace_id} with the project ID or number.
  """

  name = _messages.StringField(1, required=True)


class RunNamespacesConfigurationsListRequest(_messages.Message):
  r"""A RunNamespacesConfigurationsListRequest object.

  Fields:
    continue_: Optional. Encoded string to continue paging.
    fieldSelector: Not supported by Cloud Run.
    includeUninitialized: Not supported by Cloud Run.
    labelSelector: Allows to filter resources based on a label. Supported
      operations are =, !=, exists, in, and notIn.
    limit: Optional. The maximum number of the records that should be
      returned.
    parent: The namespace from which the configurations should be listed. For
      Cloud Run, replace {namespace_id} with the project ID or number.
    resourceVersion: Not supported by Cloud Run.
    watch: Not supported by Cloud Run.
  """

  continue_ = _messages.StringField(1)
  fieldSelector = _messages.StringField(2)
  includeUninitialized = _messages.BooleanField(3)
  labelSelector = _messages.StringField(4)
  limit = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  parent = _messages.StringField(6, required=True)
  resourceVersion = _messages.StringField(7)
  watch = _messages.BooleanField(8)


class RunNamespacesDomainmappingsCreateRequest(_messages.Message):
  r"""A RunNamespacesDomainmappingsCreateRequest object.

  Fields:
    domainMapping: A DomainMapping resource to be passed as the request body.
    dryRun: Indicates that the server should validate the request and populate
      default values without persisting the request. Supported values: `all`
    parent: Required. The namespace in which the domain mapping should be
      created. For Cloud Run (fully managed), replace {namespace} with the
      project ID or number. It takes the form namespaces/{namespace}. For
      example: namespaces/PROJECT_ID
  """

  domainMapping = _messages.MessageField('DomainMapping', 1)
  dryRun = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class RunNamespacesDomainmappingsDeleteRequest(_messages.Message):
  r"""A RunNamespacesDomainmappingsDeleteRequest object.

  Fields:
    apiVersion: Cloud Run currently ignores this parameter.
    dryRun: Indicates that the server should validate the request and populate
      default values without persisting the request. Supported values: `all`
    kind: Cloud Run currently ignores this parameter.
    name: Required. The name of the domain mapping to delete. For Cloud Run
      (fully managed), replace {namespace} with the project ID or number. It
      takes the form namespaces/{namespace}. For example:
      namespaces/PROJECT_ID
    propagationPolicy: Specifies the propagation policy of delete. Cloud Run
      currently ignores this setting, and deletes in the background. Please
      see kubernetes.io/docs/concepts/architecture/garbage-collection/ for
      more information.
  """

  apiVersion = _messages.StringField(1)
  dryRun = _messages.StringField(2)
  kind = _messages.StringField(3)
  name = _messages.StringField(4, required=True)
  propagationPolicy = _messages.StringField(5)


class RunNamespacesDomainmappingsGetRequest(_messages.Message):
  r"""A RunNamespacesDomainmappingsGetRequest object.

  Fields:
    name: Required. The name of the domain mapping to retrieve. For Cloud Run
      (fully managed), replace {namespace} with the project ID or number. It
      takes the form namespaces/{namespace}. For example:
      namespaces/PROJECT_ID
  """

  name = _messages.StringField(1, required=True)


class RunNamespacesDomainmappingsListRequest(_messages.Message):
  r"""A RunNamespacesDomainmappingsListRequest object.

  Fields:
    continue_: Optional. Encoded string to continue paging.
    fieldSelector: Allows to filter resources based on a specific value for a
      field name. Send this in a query string format. i.e.
      'metadata.name%3Dlorem'. Not currently used by Cloud Run.
    includeUninitialized: Not currently used by Cloud Run.
    labelSelector: Allows to filter resources based on a label. Supported
      operations are =, !=, exists, in, and notIn.
    limit: Optional. The maximum number of records that should be returned.
    parent: Required. The namespace from which the domain mappings should be
      listed. For Cloud Run (fully managed), replace {namespace} with the
      project ID or number. It takes the form namespaces/{namespace}. For
      example: namespaces/PROJECT_ID
    resourceVersion: The baseline resource version from which the list or
      watch operation should start. Not currently used by Cloud Run.
    watch: Flag that indicates that the client expects to watch this resource
      as well. Not currently used by Cloud Run.
  """

  continue_ = _messages.StringField(1)
  fieldSelector = _messages.StringField(2)
  includeUninitialized = _messages.BooleanField(3)
  labelSelector = _messages.StringField(4)
  limit = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  parent = _messages.StringField(6, required=True)
  resourceVersion = _messages.StringField(7)
  watch = _messages.BooleanField(8)


class RunNamespacesExecutionsCancelRequest(_messages.Message):
  r"""A RunNamespacesExecutionsCancelRequest object.

  Fields:
    cancelExecutionRequest: A CancelExecutionRequest resource to be passed as
      the request body.
    name: Required. The name of the execution to cancel. Replace {namespace}
      with the project ID or number. It takes the form namespaces/{namespace}.
      For example: namespaces/PROJECT_ID
  """

  cancelExecutionRequest = _messages.MessageField('CancelExecutionRequest', 1)
  name = _messages.StringField(2, required=True)


class RunNamespacesExecutionsDeleteRequest(_messages.Message):
  r"""A RunNamespacesExecutionsDeleteRequest object.

  Fields:
    apiVersion: Optional. Cloud Run currently ignores this parameter.
    kind: Optional. Cloud Run currently ignores this parameter.
    name: Required. The name of the execution to delete. Replace {namespace}
      with the project ID or number. It takes the form namespaces/{namespace}.
      For example: namespaces/PROJECT_ID
    propagationPolicy: Optional. Specifies the propagation policy of delete.
      Cloud Run currently ignores this setting.
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  propagationPolicy = _messages.StringField(4)


class RunNamespacesExecutionsGetRequest(_messages.Message):
  r"""A RunNamespacesExecutionsGetRequest object.

  Fields:
    name: Required. The name of the execution to retrieve. Replace {namespace}
      with the project ID or number. It takes the form namespaces/{namespace}.
      For example: namespaces/PROJECT_ID
  """

  name = _messages.StringField(1, required=True)


class RunNamespacesExecutionsListRequest(_messages.Message):
  r"""A RunNamespacesExecutionsListRequest object.

  Fields:
    continue_: Optional. Optional encoded string to continue paging.
    fieldSelector: Optional. Not supported by Cloud Run.
    includeUninitialized: Optional. Not supported by Cloud Run.
    labelSelector: Optional. Allows to filter resources based on a label.
      Supported operations are =, !=, exists, in, and notIn.
    limit: Optional. The maximum number of the records that should be
      returned.
    parent: Required. The namespace from which the executions should be
      listed. Replace {namespace} with the project ID or number. It takes the
      form namespaces/{namespace}. For example: namespaces/PROJECT_ID
    resourceVersion: Optional. Not supported by Cloud Run.
    watch: Optional. Not supported by Cloud Run.
  """

  continue_ = _messages.StringField(1)
  fieldSelector = _messages.StringField(2)
  includeUninitialized = _messages.BooleanField(3)
  labelSelector = _messages.StringField(4)
  limit = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  parent = _messages.StringField(6, required=True)
  resourceVersion = _messages.StringField(7)
  watch = _messages.BooleanField(8)


class RunNamespacesJobsCreateRequest(_messages.Message):
  r"""A RunNamespacesJobsCreateRequest object.

  Fields:
    job: A Job resource to be passed as the request body.
    parent: Required. The namespace in which the job should be created.
      Replace {namespace} with the project ID or number. It takes the form
      namespaces/{namespace}. For example: namespaces/PROJECT_ID
  """

  job = _messages.MessageField('Job', 1)
  parent = _messages.StringField(2, required=True)


class RunNamespacesJobsDeleteRequest(_messages.Message):
  r"""A RunNamespacesJobsDeleteRequest object.

  Fields:
    apiVersion: Optional. Cloud Run currently ignores this parameter.
    force: If set to true, the Job and its Executions will be deleted no
      matter whether any Executions are still running or not. If set to false
      or unset, the Job and its Executions can only be deleted if there are no
      running Executions. Any running Execution will fail the deletion.
    kind: Optional. Cloud Run currently ignores this parameter.
    name: Required. The name of the job to delete. Replace {namespace} with
      the project ID or number. It takes the form namespaces/{namespace}. For
      example: namespaces/PROJECT_ID
    propagationPolicy: Optional. Specifies the propagation policy of delete.
      Cloud Run currently ignores this setting, and deletes in the background.
      Please see kubernetes.io/docs/concepts/workloads/controllers/garbage-
      collection/ for more information.
  """

  apiVersion = _messages.StringField(1)
  force = _messages.BooleanField(2)
  kind = _messages.StringField(3)
  name = _messages.StringField(4, required=True)
  propagationPolicy = _messages.StringField(5)


class RunNamespacesJobsGetRequest(_messages.Message):
  r"""A RunNamespacesJobsGetRequest object.

  Fields:
    name: Required. The name of the job to retrieve. Replace {namespace} with
      the project ID or number. It takes the form namespaces/{namespace}. For
      example: namespaces/PROJECT_ID
  """

  name = _messages.StringField(1, required=True)


class RunNamespacesJobsListRequest(_messages.Message):
  r"""A RunNamespacesJobsListRequest object.

  Fields:
    continue_: Optional. Optional encoded string to continue paging.
    fieldSelector: Optional. Not supported by Cloud Run.
    includeUninitialized: Optional. Not supported by Cloud Run.
    labelSelector: Optional. Allows to filter resources based on a label.
      Supported operations are =, !=, exists, in, and notIn.
    limit: Optional. The maximum number of records that should be returned.
    parent: Required. The namespace from which the jobs should be listed.
      Replace {namespace} with the project ID or number. It takes the form
      namespaces/{namespace}. For example: namespaces/PROJECT_ID
    resourceVersion: Optional. Not supported by Cloud Run.
    watch: Optional. Not supported by Cloud Run.
  """

  continue_ = _messages.StringField(1)
  fieldSelector = _messages.StringField(2)
  includeUninitialized = _messages.BooleanField(3)
  labelSelector = _messages.StringField(4)
  limit = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  parent = _messages.StringField(6, required=True)
  resourceVersion = _messages.StringField(7)
  watch = _messages.BooleanField(8)


class RunNamespacesJobsReplaceJobRequest(_messages.Message):
  r"""A RunNamespacesJobsReplaceJobRequest object.

  Fields:
    job: A Job resource to be passed as the request body.
    name: Required. The name of the job being replaced. Replace {namespace}
      with the project ID or number. It takes the form namespaces/{namespace}.
      For example: namespaces/PROJECT_ID
  """

  job = _messages.MessageField('Job', 1)
  name = _messages.StringField(2, required=True)


class RunNamespacesJobsRunRequest(_messages.Message):
  r"""A RunNamespacesJobsRunRequest object.

  Fields:
    name: Required. The name of the job to run. Replace {namespace} with the
      project ID or number. It takes the form namespaces/{namespace}. For
      example: namespaces/PROJECT_ID
    runJobRequest: A RunJobRequest resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  runJobRequest = _messages.MessageField('RunJobRequest', 2)


class RunNamespacesRevisionsDeleteRequest(_messages.Message):
  r"""A RunNamespacesRevisionsDeleteRequest object.

  Fields:
    apiVersion: Cloud Run currently ignores this parameter.
    dryRun: Indicates that the server should validate the request and populate
      default values without persisting the request. Supported values: `all`
    kind: Cloud Run currently ignores this parameter.
    name: The name of the revision to delete. For Cloud Run (fully managed),
      replace {namespace} with the project ID or number. It takes the form
      namespaces/{namespace}. For example: namespaces/PROJECT_ID
    propagationPolicy: Specifies the propagation policy of delete. Cloud Run
      currently ignores this setting, and deletes in the background.
  """

  apiVersion = _messages.StringField(1)
  dryRun = _messages.StringField(2)
  kind = _messages.StringField(3)
  name = _messages.StringField(4, required=True)
  propagationPolicy = _messages.StringField(5)


class RunNamespacesRevisionsGetRequest(_messages.Message):
  r"""A RunNamespacesRevisionsGetRequest object.

  Fields:
    name: The name of the revision to retrieve. For Cloud Run (fully managed),
      replace {namespace} with the project ID or number. It takes the form
      namespaces/{namespace}. For example: namespaces/PROJECT_ID
  """

  name = _messages.StringField(1, required=True)


class RunNamespacesRevisionsListRequest(_messages.Message):
  r"""A RunNamespacesRevisionsListRequest object.

  Fields:
    continue_: Optional. Encoded string to continue paging.
    fieldSelector: Allows to filter resources based on a specific value for a
      field name. Send this in a query string format. i.e.
      'metadata.name%3Dlorem'. Not currently used by Cloud Run.
    includeUninitialized: Not currently used by Cloud Run.
    labelSelector: Allows to filter resources based on a label. Supported
      operations are =, !=, exists, in, and notIn.
    limit: Optional. The maximum number of records that should be returned.
    parent: The namespace from which the revisions should be listed. For Cloud
      Run (fully managed), replace {namespace} with the project ID or number.
      It takes the form namespaces/{namespace}. For example:
      namespaces/PROJECT_ID
    resourceVersion: The baseline resource version from which the list or
      watch operation should start. Not currently used by Cloud Run.
    watch: Flag that indicates that the client expects to watch this resource
      as well. Not currently used by Cloud Run.
  """

  continue_ = _messages.StringField(1)
  fieldSelector = _messages.StringField(2)
  includeUninitialized = _messages.BooleanField(3)
  labelSelector = _messages.StringField(4)
  limit = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  parent = _messages.StringField(6, required=True)
  resourceVersion = _messages.StringField(7)
  watch = _messages.BooleanField(8)


class RunNamespacesRoutesGetRequest(_messages.Message):
  r"""A RunNamespacesRoutesGetRequest object.

  Fields:
    name: The name of the route to retrieve. For Cloud Run (fully managed),
      replace {namespace} with the project ID or number. It takes the form
      namespaces/{namespace}. For example: namespaces/PROJECT_ID
  """

  name = _messages.StringField(1, required=True)


class RunNamespacesRoutesListRequest(_messages.Message):
  r"""A RunNamespacesRoutesListRequest object.

  Fields:
    continue_: Optional. Encoded string to continue paging.
    fieldSelector: Allows to filter resources based on a specific value for a
      field name. Send this in a query string format. i.e.
      'metadata.name%3Dlorem'. Not currently used by Cloud Run.
    includeUninitialized: Not currently used by Cloud Run.
    labelSelector: Allows to filter resources based on a label. Supported
      operations are =, !=, exists, in, and notIn.
    limit: Optional. The maximum number of records that should be returned.
    parent: The namespace from which the routes should be listed. For Cloud
      Run (fully managed), replace {namespace} with the project ID or number.
      It takes the form namespaces/{namespace}. For example:
      namespaces/PROJECT_ID
    resourceVersion: The baseline resource version from which the list or
      watch operation should start. Not currently used by Cloud Run.
    watch: Flag that indicates that the client expects to watch this resource
      as well. Not currently used by Cloud Run.
  """

  continue_ = _messages.StringField(1)
  fieldSelector = _messages.StringField(2)
  includeUninitialized = _messages.BooleanField(3)
  labelSelector = _messages.StringField(4)
  limit = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  parent = _messages.StringField(6, required=True)
  resourceVersion = _messages.StringField(7)
  watch = _messages.BooleanField(8)


class RunNamespacesServicesCreateRequest(_messages.Message):
  r"""A RunNamespacesServicesCreateRequest object.

  Fields:
    dryRun: Indicates that the server should validate the request and populate
      default values without persisting the request. Supported values: `all`
    parent: Required. The resource's parent. In Cloud Run, it may be one of
      the following: * `{project_id_or_number}` *
      `namespaces/{project_id_or_number}` *
      `namespaces/{project_id_or_number}/services` *
      `projects/{project_id_or_number}/locations/{region}` *
      `projects/{project_id_or_number}/regions/{region}`
    service: A Service resource to be passed as the request body.
  """

  dryRun = _messages.StringField(1)
  parent = _messages.StringField(2, required=True)
  service = _messages.MessageField('Service', 3)


class RunNamespacesServicesDeleteRequest(_messages.Message):
  r"""A RunNamespacesServicesDeleteRequest object.

  Fields:
    apiVersion: Not supported, and ignored by Cloud Run.
    dryRun: Indicates that the server should validate the request and populate
      default values without persisting the request. Supported values: `all`
    kind: Not supported, and ignored by Cloud Run.
    name: Required. The fully qualified name of the service to delete. It can
      be any of the following forms: *
      `namespaces/{project_id_or_number}/services/{service_name}` (only when
      the `endpoint` is regional) * `projects/{project_id_or_number}/locations
      /{region}/services/{service_name}` * `projects/{project_id_or_number}/re
      gions/{region}/services/{service_name}`
    propagationPolicy: Not supported, and ignored by Cloud Run.
  """

  apiVersion = _messages.StringField(1)
  dryRun = _messages.StringField(2)
  kind = _messages.StringField(3)
  name = _messages.StringField(4, required=True)
  propagationPolicy = _messages.StringField(5)


class RunNamespacesServicesGetRequest(_messages.Message):
  r"""A RunNamespacesServicesGetRequest object.

  Fields:
    name: Required. The fully qualified name of the service to retrieve. It
      can be any of the following forms: *
      `namespaces/{project_id_or_number}/services/{service_name}` (only when
      the `endpoint` is regional) * `projects/{project_id_or_number}/locations
      /{region}/services/{service_name}` * `projects/{project_id_or_number}/re
      gions/{region}/services/{service_name}`
  """

  name = _messages.StringField(1, required=True)


class RunNamespacesServicesListRequest(_messages.Message):
  r"""A RunNamespacesServicesListRequest object.

  Fields:
    continue_: Encoded string to continue paging.
    fieldSelector: Not supported, and ignored by Cloud Run.
    includeUninitialized: Not supported, and ignored by Cloud Run.
    labelSelector: Allows to filter resources based on a label. Supported
      operations are =, !=, exists, in, and notIn.
    limit: The maximum number of records that should be returned.
    parent: Required. The parent from where the resources should be listed. In
      Cloud Run, it may be one of the following: * `{project_id_or_number}` *
      `namespaces/{project_id_or_number}` *
      `namespaces/{project_id_or_number}/services` *
      `projects/{project_id_or_number}/locations/{region}` *
      `projects/{project_id_or_number}/regions/{region}`
    resourceVersion: Not supported, and ignored by Cloud Run.
    watch: Not supported, and ignored by Cloud Run.
  """

  continue_ = _messages.StringField(1)
  fieldSelector = _messages.StringField(2)
  includeUninitialized = _messages.BooleanField(3)
  labelSelector = _messages.StringField(4)
  limit = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  parent = _messages.StringField(6, required=True)
  resourceVersion = _messages.StringField(7)
  watch = _messages.BooleanField(8)


class RunNamespacesServicesReplaceServiceRequest(_messages.Message):
  r"""A RunNamespacesServicesReplaceServiceRequest object.

  Fields:
    dryRun: Indicates that the server should validate the request and populate
      default values without persisting the request. Supported values: `all`
    name: Required. The fully qualified name of the service to replace. It can
      be any of the following forms: *
      `namespaces/{project_id_or_number}/services/{service_name}` (only when
      the `endpoint` is regional) * `projects/{project_id_or_number}/locations
      /{region}/services/{service_name}` * `projects/{project_id_or_number}/re
      gions/{region}/services/{service_name}`
    service: A Service resource to be passed as the request body.
  """

  dryRun = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  service = _messages.MessageField('Service', 3)


class RunNamespacesTasksGetRequest(_messages.Message):
  r"""A RunNamespacesTasksGetRequest object.

  Fields:
    name: Required. The name of the task to retrieve. Replace {namespace} with
      the project ID or number. It takes the form namespaces/{namespace}. For
      example: namespaces/PROJECT_ID
  """

  name = _messages.StringField(1, required=True)


class RunNamespacesTasksListRequest(_messages.Message):
  r"""A RunNamespacesTasksListRequest object.

  Fields:
    continue_: Optional. Optional encoded string to continue paging.
    fieldSelector: Optional. Not supported by Cloud Run.
    includeUninitialized: Optional. Not supported by Cloud Run.
    labelSelector: Optional. Allows to filter resources based on a label.
      Supported operations are =, !=, exists, in, and notIn. For example, to
      list all tasks of execution "foo" in succeeded state: `run.googleapis.co
      m/execution=foo,run.googleapis.com/runningState=Succeeded`. Supported
      states are: * `Pending`: Initial state of all tasks. The task has not
      yet started but eventually will. * `Running`: Container instances for
      this task are running or will be running shortly. * `Succeeded`: No more
      container instances to run for the task, and the last attempt succeeded.
      * `Failed`: No more container instances to run for the task, and the
      last attempt failed. This task has run out of retry attempts. *
      `Cancelled`: Task was running but got stopped because its parent
      execution has been aborted. * `Abandoned`: The task has not yet started
      and never will because its parent execution has been aborted.
    limit: Optional. The maximum number of records that should be returned.
    parent: Required. The namespace from which the tasks should be listed.
      Replace {namespace} with the project ID or number. It takes the form
      namespaces/{namespace}. For example: namespaces/PROJECT_ID
    resourceVersion: Optional. Not supported by Cloud Run.
    watch: Optional. Not supported by Cloud Run.
  """

  continue_ = _messages.StringField(1)
  fieldSelector = _messages.StringField(2)
  includeUninitialized = _messages.BooleanField(3)
  labelSelector = _messages.StringField(4)
  limit = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  parent = _messages.StringField(6, required=True)
  resourceVersion = _messages.StringField(7)
  watch = _messages.BooleanField(8)


class RunNamespacesWorkerpoolsCreateRequest(_messages.Message):
  r"""A RunNamespacesWorkerpoolsCreateRequest object.

  Fields:
    dryRun: Indicates that the server should validate the request and populate
      default values without persisting the request. Supported values: `all`
    parent: Required. The resource's parent. In Cloud Run, it may be one of
      the following: * `{project_id_or_number}` *
      `namespaces/{project_id_or_number}` *
      `namespaces/{project_id_or_number}/workerpools` *
      `projects/{project_id_or_number}/locations/{region}` *
      `projects/{project_id_or_number}/regions/{region}`
    workerPool: A WorkerPool resource to be passed as the request body.
  """

  dryRun = _messages.StringField(1)
  parent = _messages.StringField(2, required=True)
  workerPool = _messages.MessageField('WorkerPool', 3)


class RunNamespacesWorkerpoolsDeleteRequest(_messages.Message):
  r"""A RunNamespacesWorkerpoolsDeleteRequest object.

  Fields:
    dryRun: Indicates that the server should validate the request and populate
      default values without persisting the request. Supported values: `all`
    name: Required. The fully qualified name of the worker pool to delete. It
      can be any of the following forms: *
      `namespaces/{project_id_or_number}/workerpools/{worker_pool_name}` (only
      when the `endpoint` is regional) * `projects/{project_id_or_number}/loca
      tions/{region}/workerpools/{worker_pool_name}` * `projects/{project_id_o
      r_number}/regions/{region}/workerpools/{worker_pool_name}`
  """

  dryRun = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class RunNamespacesWorkerpoolsGetRequest(_messages.Message):
  r"""A RunNamespacesWorkerpoolsGetRequest object.

  Fields:
    name: Required. The fully qualified name of the worker pool to retrieve.
      It can be any of the following forms: *
      `namespaces/{project_id_or_number}/workerpools/{worker_pool_name}` (only
      when the `endpoint` is regional) * `projects/{project_id_or_number}/loca
      tions/{region}/workerpools/{worker_pool_name}` * `projects/{project_id_o
      r_number}/regions/{region}/workerpools/{worker_pool_name}`
  """

  name = _messages.StringField(1, required=True)


class RunNamespacesWorkerpoolsListRequest(_messages.Message):
  r"""A RunNamespacesWorkerpoolsListRequest object.

  Fields:
    continue_: Encoded string to continue paging.
    labelSelector: =, !=, exists, in, and notIn.
    limit: The maximum number of records that should be returned.
    parent: Required. The parent from where the resources should be listed. In
      Cloud Run, it may be one of the following: * `{project_id_or_number}` *
      `namespaces/{project_id_or_number}` *
      `namespaces/{project_id_or_number}/workerpools` *
      `projects/{project_id_or_number}/locations/{region}` *
      `projects/{project_id_or_number}/regions/{region}`
  """

  continue_ = _messages.StringField(1)
  labelSelector = _messages.StringField(2)
  limit = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  parent = _messages.StringField(4, required=True)


class RunNamespacesWorkerpoolsReplaceWorkerPoolRequest(_messages.Message):
  r"""A RunNamespacesWorkerpoolsReplaceWorkerPoolRequest object.

  Fields:
    dryRun: Indicates that the server should validate the request and populate
      default values without persisting the request. Supported values: `all`
    name: Required. The fully qualified name of the worker pool to replace. It
      can be any of the following forms: *
      `namespaces/{project_id_or_number}/workerpools/{worker_pool_name}` (only
      when the `endpoint` is regional) * `projects/{project_id_or_number}/loca
      tions/{region}/workerpools/{worker_pool_name}` * `projects/{project_id_o
      r_number}/regions/{region}/workerpools/{worker_pool_name}`
    workerPool: A WorkerPool resource to be passed as the request body.
  """

  dryRun = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  workerPool = _messages.MessageField('WorkerPool', 3)


class RunProjectsAuthorizeddomainsListRequest(_messages.Message):
  r"""A RunProjectsAuthorizeddomainsListRequest object.

  Fields:
    pageSize: Maximum results to return per page.
    pageToken: Continuation token for fetching the next page of results.
    parent: Name of the parent Project resource. Example:
      `projects/myproject`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class RunProjectsLocationsAuthorizeddomainsListRequest(_messages.Message):
  r"""A RunProjectsLocationsAuthorizeddomainsListRequest object.

  Fields:
    pageSize: Maximum results to return per page.
    pageToken: Continuation token for fetching the next page of results.
    parent: Name of the parent Project resource. Example:
      `projects/myproject`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class RunProjectsLocationsConfigurationsGetRequest(_messages.Message):
  r"""A RunProjectsLocationsConfigurationsGetRequest object.

  Fields:
    name: The name of the configuration to retrieve. For Cloud Run, replace
      {namespace_id} with the project ID or number.
  """

  name = _messages.StringField(1, required=True)


class RunProjectsLocationsConfigurationsListRequest(_messages.Message):
  r"""A RunProjectsLocationsConfigurationsListRequest object.

  Fields:
    continue_: Optional. Encoded string to continue paging.
    fieldSelector: Not supported by Cloud Run.
    includeUninitialized: Not supported by Cloud Run.
    labelSelector: Allows to filter resources based on a label. Supported
      operations are =, !=, exists, in, and notIn.
    limit: Optional. The maximum number of the records that should be
      returned.
    parent: The namespace from which the configurations should be listed. For
      Cloud Run, replace {namespace_id} with the project ID or number.
    resourceVersion: Not supported by Cloud Run.
    watch: Not supported by Cloud Run.
  """

  continue_ = _messages.StringField(1)
  fieldSelector = _messages.StringField(2)
  includeUninitialized = _messages.BooleanField(3)
  labelSelector = _messages.StringField(4)
  limit = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  parent = _messages.StringField(6, required=True)
  resourceVersion = _messages.StringField(7)
  watch = _messages.BooleanField(8)


class RunProjectsLocationsDomainmappingsCreateRequest(_messages.Message):
  r"""A RunProjectsLocationsDomainmappingsCreateRequest object.

  Fields:
    domainMapping: A DomainMapping resource to be passed as the request body.
    dryRun: Indicates that the server should validate the request and populate
      default values without persisting the request. Supported values: `all`
    parent: Required. The namespace in which the domain mapping should be
      created. For Cloud Run (fully managed), replace {namespace} with the
      project ID or number. It takes the form namespaces/{namespace}. For
      example: namespaces/PROJECT_ID
  """

  domainMapping = _messages.MessageField('DomainMapping', 1)
  dryRun = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class RunProjectsLocationsDomainmappingsDeleteRequest(_messages.Message):
  r"""A RunProjectsLocationsDomainmappingsDeleteRequest object.

  Fields:
    apiVersion: Cloud Run currently ignores this parameter.
    dryRun: Indicates that the server should validate the request and populate
      default values without persisting the request. Supported values: `all`
    kind: Cloud Run currently ignores this parameter.
    name: Required. The name of the domain mapping to delete. For Cloud Run
      (fully managed), replace {namespace} with the project ID or number. It
      takes the form namespaces/{namespace}. For example:
      namespaces/PROJECT_ID
    propagationPolicy: Specifies the propagation policy of delete. Cloud Run
      currently ignores this setting, and deletes in the background. Please
      see kubernetes.io/docs/concepts/architecture/garbage-collection/ for
      more information.
  """

  apiVersion = _messages.StringField(1)
  dryRun = _messages.StringField(2)
  kind = _messages.StringField(3)
  name = _messages.StringField(4, required=True)
  propagationPolicy = _messages.StringField(5)


class RunProjectsLocationsDomainmappingsGetRequest(_messages.Message):
  r"""A RunProjectsLocationsDomainmappingsGetRequest object.

  Fields:
    name: Required. The name of the domain mapping to retrieve. For Cloud Run
      (fully managed), replace {namespace} with the project ID or number. It
      takes the form namespaces/{namespace}. For example:
      namespaces/PROJECT_ID
  """

  name = _messages.StringField(1, required=True)


class RunProjectsLocationsDomainmappingsListRequest(_messages.Message):
  r"""A RunProjectsLocationsDomainmappingsListRequest object.

  Fields:
    continue_: Optional. Encoded string to continue paging.
    fieldSelector: Allows to filter resources based on a specific value for a
      field name. Send this in a query string format. i.e.
      'metadata.name%3Dlorem'. Not currently used by Cloud Run.
    includeUninitialized: Not currently used by Cloud Run.
    labelSelector: Allows to filter resources based on a label. Supported
      operations are =, !=, exists, in, and notIn.
    limit: Optional. The maximum number of records that should be returned.
    parent: Required. The namespace from which the domain mappings should be
      listed. For Cloud Run (fully managed), replace {namespace} with the
      project ID or number. It takes the form namespaces/{namespace}. For
      example: namespaces/PROJECT_ID
    resourceVersion: The baseline resource version from which the list or
      watch operation should start. Not currently used by Cloud Run.
    watch: Flag that indicates that the client expects to watch this resource
      as well. Not currently used by Cloud Run.
  """

  continue_ = _messages.StringField(1)
  fieldSelector = _messages.StringField(2)
  includeUninitialized = _messages.BooleanField(3)
  labelSelector = _messages.StringField(4)
  limit = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  parent = _messages.StringField(6, required=True)
  resourceVersion = _messages.StringField(7)
  watch = _messages.BooleanField(8)


class RunProjectsLocationsJobsGetIamPolicyRequest(_messages.Message):
  r"""A RunProjectsLocationsJobsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class RunProjectsLocationsJobsSetIamPolicyRequest(_messages.Message):
  r"""A RunProjectsLocationsJobsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class RunProjectsLocationsJobsTestIamPermissionsRequest(_messages.Message):
  r"""A RunProjectsLocationsJobsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class RunProjectsLocationsListRequest(_messages.Message):
  r"""A RunProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class RunProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A RunProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class RunProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A RunProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class RunProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A RunProjectsLocationsOperationsListRequest object.

  Fields:
    filter: Optional. A filter for matching the completed or in-progress
      operations. The supported formats of *filter* are: To query for only
      completed operations: done:true To query for only ongoing operations:
      done:false Must be empty to query for all of the latest operations for
      the given parent project.
    name: Required. To query for all of the operations for a project.
    pageSize: The maximum number of records that should be returned. Requested
      page size cannot exceed 100. If not set or set to less than or equal to
      0, the default page size is 100. .
    pageToken: Token identifying which result to start with, which is returned
      by a previous list call.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class RunProjectsLocationsOperationsWaitRequest(_messages.Message):
  r"""A RunProjectsLocationsOperationsWaitRequest object.

  Fields:
    googleLongrunningWaitOperationRequest: A
      GoogleLongrunningWaitOperationRequest resource to be passed as the
      request body.
    name: The name of the operation resource to wait on.
  """

  googleLongrunningWaitOperationRequest = _messages.MessageField('GoogleLongrunningWaitOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class RunProjectsLocationsRevisionsDeleteRequest(_messages.Message):
  r"""A RunProjectsLocationsRevisionsDeleteRequest object.

  Fields:
    apiVersion: Cloud Run currently ignores this parameter.
    dryRun: Indicates that the server should validate the request and populate
      default values without persisting the request. Supported values: `all`
    kind: Cloud Run currently ignores this parameter.
    name: The name of the revision to delete. For Cloud Run (fully managed),
      replace {namespace} with the project ID or number. It takes the form
      namespaces/{namespace}. For example: namespaces/PROJECT_ID
    propagationPolicy: Specifies the propagation policy of delete. Cloud Run
      currently ignores this setting, and deletes in the background.
  """

  apiVersion = _messages.StringField(1)
  dryRun = _messages.StringField(2)
  kind = _messages.StringField(3)
  name = _messages.StringField(4, required=True)
  propagationPolicy = _messages.StringField(5)


class RunProjectsLocationsRevisionsGetRequest(_messages.Message):
  r"""A RunProjectsLocationsRevisionsGetRequest object.

  Fields:
    name: The name of the revision to retrieve. For Cloud Run (fully managed),
      replace {namespace} with the project ID or number. It takes the form
      namespaces/{namespace}. For example: namespaces/PROJECT_ID
  """

  name = _messages.StringField(1, required=True)


class RunProjectsLocationsRevisionsListRequest(_messages.Message):
  r"""A RunProjectsLocationsRevisionsListRequest object.

  Fields:
    continue_: Optional. Encoded string to continue paging.
    fieldSelector: Allows to filter resources based on a specific value for a
      field name. Send this in a query string format. i.e.
      'metadata.name%3Dlorem'. Not currently used by Cloud Run.
    includeUninitialized: Not currently used by Cloud Run.
    labelSelector: Allows to filter resources based on a label. Supported
      operations are =, !=, exists, in, and notIn.
    limit: Optional. The maximum number of records that should be returned.
    parent: The namespace from which the revisions should be listed. For Cloud
      Run (fully managed), replace {namespace} with the project ID or number.
      It takes the form namespaces/{namespace}. For example:
      namespaces/PROJECT_ID
    resourceVersion: The baseline resource version from which the list or
      watch operation should start. Not currently used by Cloud Run.
    watch: Flag that indicates that the client expects to watch this resource
      as well. Not currently used by Cloud Run.
  """

  continue_ = _messages.StringField(1)
  fieldSelector = _messages.StringField(2)
  includeUninitialized = _messages.BooleanField(3)
  labelSelector = _messages.StringField(4)
  limit = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  parent = _messages.StringField(6, required=True)
  resourceVersion = _messages.StringField(7)
  watch = _messages.BooleanField(8)


class RunProjectsLocationsRoutesGetRequest(_messages.Message):
  r"""A RunProjectsLocationsRoutesGetRequest object.

  Fields:
    name: The name of the route to retrieve. For Cloud Run (fully managed),
      replace {namespace} with the project ID or number. It takes the form
      namespaces/{namespace}. For example: namespaces/PROJECT_ID
  """

  name = _messages.StringField(1, required=True)


class RunProjectsLocationsRoutesListRequest(_messages.Message):
  r"""A RunProjectsLocationsRoutesListRequest object.

  Fields:
    continue_: Optional. Encoded string to continue paging.
    fieldSelector: Allows to filter resources based on a specific value for a
      field name. Send this in a query string format. i.e.
      'metadata.name%3Dlorem'. Not currently used by Cloud Run.
    includeUninitialized: Not currently used by Cloud Run.
    labelSelector: Allows to filter resources based on a label. Supported
      operations are =, !=, exists, in, and notIn.
    limit: Optional. The maximum number of records that should be returned.
    parent: The namespace from which the routes should be listed. For Cloud
      Run (fully managed), replace {namespace} with the project ID or number.
      It takes the form namespaces/{namespace}. For example:
      namespaces/PROJECT_ID
    resourceVersion: The baseline resource version from which the list or
      watch operation should start. Not currently used by Cloud Run.
    watch: Flag that indicates that the client expects to watch this resource
      as well. Not currently used by Cloud Run.
  """

  continue_ = _messages.StringField(1)
  fieldSelector = _messages.StringField(2)
  includeUninitialized = _messages.BooleanField(3)
  labelSelector = _messages.StringField(4)
  limit = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  parent = _messages.StringField(6, required=True)
  resourceVersion = _messages.StringField(7)
  watch = _messages.BooleanField(8)


class RunProjectsLocationsServicesCreateRequest(_messages.Message):
  r"""A RunProjectsLocationsServicesCreateRequest object.

  Fields:
    dryRun: Indicates that the server should validate the request and populate
      default values without persisting the request. Supported values: `all`
    parent: Required. The resource's parent. In Cloud Run, it may be one of
      the following: * `{project_id_or_number}` *
      `namespaces/{project_id_or_number}` *
      `namespaces/{project_id_or_number}/services` *
      `projects/{project_id_or_number}/locations/{region}` *
      `projects/{project_id_or_number}/regions/{region}`
    service: A Service resource to be passed as the request body.
  """

  dryRun = _messages.StringField(1)
  parent = _messages.StringField(2, required=True)
  service = _messages.MessageField('Service', 3)


class RunProjectsLocationsServicesDeleteRequest(_messages.Message):
  r"""A RunProjectsLocationsServicesDeleteRequest object.

  Fields:
    apiVersion: Not supported, and ignored by Cloud Run.
    dryRun: Indicates that the server should validate the request and populate
      default values without persisting the request. Supported values: `all`
    kind: Not supported, and ignored by Cloud Run.
    name: Required. The fully qualified name of the service to delete. It can
      be any of the following forms: *
      `namespaces/{project_id_or_number}/services/{service_name}` (only when
      the `endpoint` is regional) * `projects/{project_id_or_number}/locations
      /{region}/services/{service_name}` * `projects/{project_id_or_number}/re
      gions/{region}/services/{service_name}`
    propagationPolicy: Not supported, and ignored by Cloud Run.
  """

  apiVersion = _messages.StringField(1)
  dryRun = _messages.StringField(2)
  kind = _messages.StringField(3)
  name = _messages.StringField(4, required=True)
  propagationPolicy = _messages.StringField(5)


class RunProjectsLocationsServicesGetIamPolicyRequest(_messages.Message):
  r"""A RunProjectsLocationsServicesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class RunProjectsLocationsServicesGetRequest(_messages.Message):
  r"""A RunProjectsLocationsServicesGetRequest object.

  Fields:
    name: Required. The fully qualified name of the service to retrieve. It
      can be any of the following forms: *
      `namespaces/{project_id_or_number}/services/{service_name}` (only when
      the `endpoint` is regional) * `projects/{project_id_or_number}/locations
      /{region}/services/{service_name}` * `projects/{project_id_or_number}/re
      gions/{region}/services/{service_name}`
  """

  name = _messages.StringField(1, required=True)


class RunProjectsLocationsServicesListRequest(_messages.Message):
  r"""A RunProjectsLocationsServicesListRequest object.

  Fields:
    continue_: Encoded string to continue paging.
    fieldSelector: Not supported, and ignored by Cloud Run.
    includeUninitialized: Not supported, and ignored by Cloud Run.
    labelSelector: Allows to filter resources based on a label. Supported
      operations are =, !=, exists, in, and notIn.
    limit: The maximum number of records that should be returned.
    parent: Required. The parent from where the resources should be listed. In
      Cloud Run, it may be one of the following: * `{project_id_or_number}` *
      `namespaces/{project_id_or_number}` *
      `namespaces/{project_id_or_number}/services` *
      `projects/{project_id_or_number}/locations/{region}` *
      `projects/{project_id_or_number}/regions/{region}`
    resourceVersion: Not supported, and ignored by Cloud Run.
    watch: Not supported, and ignored by Cloud Run.
  """

  continue_ = _messages.StringField(1)
  fieldSelector = _messages.StringField(2)
  includeUninitialized = _messages.BooleanField(3)
  labelSelector = _messages.StringField(4)
  limit = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  parent = _messages.StringField(6, required=True)
  resourceVersion = _messages.StringField(7)
  watch = _messages.BooleanField(8)


class RunProjectsLocationsServicesReplaceServiceRequest(_messages.Message):
  r"""A RunProjectsLocationsServicesReplaceServiceRequest object.

  Fields:
    dryRun: Indicates that the server should validate the request and populate
      default values without persisting the request. Supported values: `all`
    name: Required. The fully qualified name of the service to replace. It can
      be any of the following forms: *
      `namespaces/{project_id_or_number}/services/{service_name}` (only when
      the `endpoint` is regional) * `projects/{project_id_or_number}/locations
      /{region}/services/{service_name}` * `projects/{project_id_or_number}/re
      gions/{region}/services/{service_name}`
    service: A Service resource to be passed as the request body.
  """

  dryRun = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  service = _messages.MessageField('Service', 3)


class RunProjectsLocationsServicesSetIamPolicyRequest(_messages.Message):
  r"""A RunProjectsLocationsServicesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class RunProjectsLocationsServicesTestIamPermissionsRequest(_messages.Message):
  r"""A RunProjectsLocationsServicesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class RunProjectsLocationsWorkerpoolsGetIamPolicyRequest(_messages.Message):
  r"""A RunProjectsLocationsWorkerpoolsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class RunProjectsLocationsWorkerpoolsSetIamPolicyRequest(_messages.Message):
  r"""A RunProjectsLocationsWorkerpoolsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class RunProjectsLocationsWorkerpoolsTestIamPermissionsRequest(_messages.Message):
  r"""A RunProjectsLocationsWorkerpoolsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class SecretEnvSource(_messages.Message):
  r"""Not supported by Cloud Run. SecretEnvSource selects a Secret to populate
  the environment variables with. The contents of the target Secret's Data
  field will represent the key-value pairs as environment variables.

  Fields:
    localObjectReference: This field should not be used directly as it is
      meant to be inlined directly into the message. Use the "name" field
      instead.
    name: The Secret to select from.
    optional: Specify whether the Secret must be defined
  """

  localObjectReference = _messages.MessageField('LocalObjectReference', 1)
  name = _messages.StringField(2)
  optional = _messages.BooleanField(3)


class SecretKeySelector(_messages.Message):
  r"""SecretKeySelector selects a key of a Secret.

  Fields:
    key: Required. A Cloud Secret Manager secret version. Must be 'latest' for
      the latest version, an integer for a specific version, or a version
      alias. The key of the secret to select from. Must be a valid secret key.
    localObjectReference: This field should not be used directly as it is
      meant to be inlined directly into the message. Use the "name" field
      instead.
    name: The name of the secret in Cloud Secret Manager. By default, the
      secret is assumed to be in the same project. If the secret is in another
      project, you must define an alias. An alias definition has the form:
      :projects//secrets/. If multiple alias definitions are needed, they must
      be separated by commas. The alias definitions must be set on the
      run.googleapis.com/secrets annotation. The name of the secret in the
      pod's namespace to select from.
    optional: Specify whether the Secret or its key must be defined.
  """

  key = _messages.StringField(1)
  localObjectReference = _messages.MessageField('LocalObjectReference', 2)
  name = _messages.StringField(3)
  optional = _messages.BooleanField(4)


class SecretVolumeSource(_messages.Message):
  r"""A volume representing a secret stored in Google Secret Manager. The
  secret's value will be presented as the content of a file whose name is
  defined in the item path. If no items are defined, the name of the file is
  the secret_name. The contents of the target Secret's Data field will be
  presented in a volume as files using the keys in the Data field as the file
  names.

  Fields:
    defaultMode: Integer representation of mode bits to use on created files
      by default. Must be a value between 01 and 0777 (octal). If 0 or not
      set, it will default to 0444. Directories within the path are not
      affected by this setting. Notes * Internally, a umask of 0222 will be
      applied to any non-zero value. * This is an integer representation of
      the mode bits. So, the octal integer value should look exactly as the
      chmod numeric notation with a leading zero. Some examples: for chmod 777
      (a=rwx), set to 0777 (octal) or 511 (base-10). For chmod 640 (u=rw,g=r),
      set to 0640 (octal) or 416 (base-10). For chmod 755 (u=rwx,g=rx,o=rx),
      set to 0755 (octal) or 493 (base-10). * This might be in conflict with
      other options that affect the file mode, like fsGroup, and the result
      can be other mode bits set.
    items: A list of secret versions to mount in the volume. If no items are
      specified, the volume will expose a file with the same name as the
      secret name. The contents of the file will be the data in the latest
      version of the secret. If items are specified, the key will be used as
      the version to fetch from Cloud Secret Manager and the path will be the
      name of the file exposed in the volume. When items are defined, they
      must specify both a key and a path.
    optional: Not supported by Cloud Run.
    secretName: The name of the secret in Cloud Secret Manager. By default,
      the secret is assumed to be in the same project. If the secret is in
      another project, you must define an alias. An alias definition has the
      form: :projects//secrets/. If multiple alias definitions are needed,
      they must be separated by commas. The alias definitions must be set on
      the run.googleapis.com/secrets annotation. Name of the secret in the
      container's namespace to use.
  """

  defaultMode = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  items = _messages.MessageField('KeyToPath', 2, repeated=True)
  optional = _messages.BooleanField(3)
  secretName = _messages.StringField(4)


class SecurityContext(_messages.Message):
  r"""Not supported by Cloud Run. SecurityContext holds security configuration
  that will be applied to a container. Some fields are present in both
  SecurityContext and PodSecurityContext. When both are set, the values in
  SecurityContext take precedence.

  Fields:
    runAsUser: The UID to run the entrypoint of the container process.
      Defaults to user specified in image metadata if unspecified. May also be
      set in PodSecurityContext. If set in both SecurityContext and
      PodSecurityContext, the value specified in SecurityContext takes
      precedence.
  """

  runAsUser = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class Service(_messages.Message):
  r"""Service acts as a top-level container that manages a set of Routes and
  Configurations which implement a network service. Service exists to provide
  a singular abstraction which can be access controlled, reasoned about, and
  which encapsulates software lifecycle decisions such as rollout policy and
  team resource ownership. Service acts only as an orchestrator of the
  underlying Routes and Configurations (much as a kubernetes Deployment
  orchestrates ReplicaSets). The Service's controller will track the statuses
  of its owned Configuration and Route, reflecting their statuses and
  conditions as its own.

  Fields:
    apiVersion: The API version for this call. It must be
      "serving.knative.dev/v1".
    kind: The kind of resource. It must be "Service".
    metadata: Metadata associated with this Service, including name,
      namespace, labels, and annotations. In Cloud Run, annotations with
      'run.googleapis.com/' and 'autoscaling.knative.dev' are restricted, and
      the accepted annotations will be different depending on the resource
      type. The following Cloud Run-specific annotations are accepted in
      Service.metadata.annotations. * `run.googleapis.com/binary-
      authorization-breakglass` * `run.googleapis.com/binary-authorization` *
      `run.googleapis.com/client-name` * `run.googleapis.com/custom-audiences`
      * `run.googleapis.com/default-url-disabled` *
      `run.googleapis.com/description` * `run.googleapis.com/gc-traffic-tags`
      * `run.googleapis.com/ingress` * `run.googleapis.com/ingress` sets the
      ingress settings for the Service. See [the ingress settings
      documentation](/run/docs/securing/ingress) for details on configuring
      ingress settings. * `run.googleapis.com/ingress-status` is output-only
      and contains the currently active ingress settings for the Service.
      `run.googleapis.com/ingress-status` may differ from
      `run.googleapis.com/ingress` while the system is processing a change to
      `run.googleapis.com/ingress` or if the system failed to process a change
      to `run.googleapis.com/ingress`. When the system has processed all
      changes successfully `run.googleapis.com/ingress-status` and
      `run.googleapis.com/ingress` are equal.
    spec: Holds the desired state of the Service (from the client).
    status: Communicates the system-controlled state of the Service.
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  metadata = _messages.MessageField('ObjectMeta', 3)
  spec = _messages.MessageField('ServiceSpec', 4)
  status = _messages.MessageField('ServiceStatus', 5)


class ServiceSpec(_messages.Message):
  r"""ServiceSpec holds the desired state of the Route (from the client),
  which is used to manipulate the underlying Route and Configuration(s).

  Fields:
    template: Holds the latest specification for the Revision to be stamped
      out.
    traffic: Specifies how to distribute traffic over a collection of Knative
      Revisions and Configurations to the Service's main URL.
  """

  template = _messages.MessageField('RevisionTemplate', 1)
  traffic = _messages.MessageField('TrafficTarget', 2, repeated=True)


class ServiceStatus(_messages.Message):
  r"""The current state of the Service. Output only.

  Fields:
    address: Similar to url, information on where the service is available on
      HTTP.
    conditions: Conditions communicate information about ongoing/complete
      reconciliation processes that bring the `spec` inline with the observed
      state of the world. Service-specific conditions include: *
      `ConfigurationsReady`: `True` when the underlying Configuration is
      ready. * `RoutesReady`: `True` when the underlying Route is ready. *
      `Ready`: `True` when all underlying resources are ready.
    latestCreatedRevisionName: Name of the last revision that was created from
      this Service's Configuration. It might not be ready yet, for that use
      LatestReadyRevisionName.
    latestReadyRevisionName: Name of the latest Revision from this Service's
      Configuration that has had its `Ready` condition become `True`.
    observedGeneration: Returns the generation last seen by the system.
      Clients polling for completed reconciliation should poll until
      observedGeneration = metadata.generation and the Ready condition's
      status is True or False.
    traffic: Holds the configured traffic distribution. These entries will
      always contain RevisionName references. When ConfigurationName appears
      in the spec, this will hold the LatestReadyRevisionName that we last
      observed.
    url: URL that will distribute traffic over the provided traffic targets.
      It generally has the form `https://{route-hash}-{project-hash}-{cluster-
      level-suffix}.a.run.app`
  """

  address = _messages.MessageField('Addressable', 1)
  conditions = _messages.MessageField('GoogleCloudRunV1Condition', 2, repeated=True)
  latestCreatedRevisionName = _messages.StringField(3)
  latestReadyRevisionName = _messages.StringField(4)
  observedGeneration = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  traffic = _messages.MessageField('TrafficTarget', 6, repeated=True)
  url = _messages.StringField(7)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""Status is a return value for calls that don't return other objects.

  Fields:
    code: Suggested HTTP return code for this status, 0 if not set.
    details: Extended data associated with the reason. Each reason may define
      its own extended details. This field is optional and the data returned
      is not guaranteed to conform to any schema except that defined by the
      reason type.
    message: A human-readable description of the status of this operation.
    metadata: Standard list metadata.
    reason: A machine-readable description of why this operation is in the
      "Failure" status. If this value is empty there is no information
      available. A Reason clarifies an HTTP status code but does not override
      it.
    status: Status of the operation. One of: "Success" or "Failure".
  """

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('StatusDetails', 2)
  message = _messages.StringField(3)
  metadata = _messages.MessageField('ListMeta', 4)
  reason = _messages.StringField(5)
  status = _messages.StringField(6)


class StatusCause(_messages.Message):
  r"""StatusCause provides more information about an api.Status failure,
  including cases when multiple errors are encountered.

  Fields:
    field: The field of the resource that has caused this error, as named by
      its JSON serialization. May include dot and postfix notation for nested
      attributes. Arrays are zero-indexed. Fields may appear more than once in
      an array of causes due to fields having multiple errors. Examples:
      "name" - the field "name" on the current resource "items[0].name" - the
      field "name" on the first array entry in "items"
    message: A human-readable description of the cause of the error. This
      field may be presented as-is to a reader.
    reason: A machine-readable description of the cause of the error. If this
      value is empty there is no information available.
  """

  field = _messages.StringField(1)
  message = _messages.StringField(2)
  reason = _messages.StringField(3)


class StatusDetails(_messages.Message):
  r"""StatusDetails is a set of additional properties that MAY be set by the
  server to provide additional information about a response. The Reason field
  of a Status object defines what attributes will be set. Clients must ignore
  fields that do not match the defined type of each attribute, and should
  assume that any attribute may be empty, invalid, or under defined.

  Fields:
    causes: The Causes array includes more details associated with the
      StatusReason failure. Not all StatusReasons may provide detailed causes.
    group: The group attribute of the resource associated with the status
      StatusReason.
    kind: The kind attribute of the resource associated with the status
      StatusReason. On some operations may differ from the requested resource
      Kind.
    name: The name attribute of the resource associated with the status
      StatusReason (when there is a single name which can be described).
    retryAfterSeconds: If specified, the time in seconds before the operation
      should be retried. Some errors may indicate the client must take an
      alternate action - for those errors this field may indicate how long to
      wait before taking the alternate action.
    uid: UID of the resource. (when there is a single resource which can be
      described).
  """

  causes = _messages.MessageField('StatusCause', 1, repeated=True)
  group = _messages.StringField(2)
  kind = _messages.StringField(3)
  name = _messages.StringField(4)
  retryAfterSeconds = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  uid = _messages.StringField(6)


class TCPSocketAction(_messages.Message):
  r"""TCPSocketAction describes an action based on opening a socket

  Fields:
    host: Not supported by Cloud Run.
    port: Port number to access on the container. Number must be in the range
      1 to 65535.
  """

  host = _messages.StringField(1)
  port = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class Task(_messages.Message):
  r"""Task represents a single run of a container to completion.

  Fields:
    apiVersion: Optional. APIVersion defines the versioned schema of this
      representation of an object. Servers should convert recognized schemas
      to the latest internal value, and may reject unrecognized values.
    kind: Optional. Kind is a string value representing the REST resource this
      object represents. Servers may infer this from the endpoint the client
      submits requests to. Cannot be updated. In CamelCase.
    metadata: Optional. Standard object's metadata.
    spec: Optional. Specification of the desired behavior of a task.
    status: Output only. Current status of a task.
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  metadata = _messages.MessageField('ObjectMeta', 3)
  spec = _messages.MessageField('TaskSpec', 4)
  status = _messages.MessageField('TaskStatus', 5)


class TaskAttemptResult(_messages.Message):
  r"""Result of a task attempt.

  Fields:
    exitCode: Optional. The exit code of this attempt. This may be unset if
      the container was unable to exit cleanly with a code due to some other
      failure. See status field for possible failure details. At most one of
      exit_code or term_signal will be set.
    status: Optional. The status of this attempt. If the status code is OK,
      then the attempt succeeded.
    termSignal: Optional. Termination signal of the container. This is set to
      non-zero if the container is terminated by the system. At most one of
      exit_code or term_signal will be set.
  """

  exitCode = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  status = _messages.MessageField('GoogleRpcStatus', 2)
  termSignal = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class TaskSpec(_messages.Message):
  r"""TaskSpec is a description of a task.

  Messages:
    NodeSelectorValue: Optional. The Node Selector configuration. Map of
      selector key to a value which matches a node.

  Fields:
    containers: Optional. List of containers belonging to the task. We
      disallow a number of fields on this Container.
    maxRetries: Optional. Number of retries allowed per task, before marking
      this job failed. Defaults to 3.
    nodeSelector: Optional. The Node Selector configuration. Map of selector
      key to a value which matches a node.
    serviceAccountName: Optional. Email address of the IAM service account
      associated with the task of a job execution. The service account
      represents the identity of the running task, and determines what
      permissions the task has. If not provided, the task will use the
      project's default service account.
    timeoutSeconds: Optional. Duration in seconds the task may be active
      before the system will actively try to mark it failed and kill
      associated containers. This applies per attempt of a task, meaning each
      retry can run for the full timeout. Defaults to 600 seconds.
    volumes: Optional. List of volumes that can be mounted by containers
      belonging to the task.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class NodeSelectorValue(_messages.Message):
    r"""Optional. The Node Selector configuration. Map of selector key to a
    value which matches a node.

    Messages:
      AdditionalProperty: An additional property for a NodeSelectorValue
        object.

    Fields:
      additionalProperties: Additional properties of type NodeSelectorValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a NodeSelectorValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  containers = _messages.MessageField('Container', 1, repeated=True)
  maxRetries = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  nodeSelector = _messages.MessageField('NodeSelectorValue', 3)
  serviceAccountName = _messages.StringField(4)
  timeoutSeconds = _messages.IntegerField(5)
  volumes = _messages.MessageField('Volume', 6, repeated=True)


class TaskStatus(_messages.Message):
  r"""TaskStatus represents the status of a task.

  Fields:
    completionTime: Optional. Represents time when the task was completed. It
      is not guaranteed to be set in happens-before order across separate
      operations. It is represented in RFC3339 form and is in UTC.
    conditions: Optional. Conditions communicate information about
      ongoing/complete reconciliation processes that bring the "spec" inline
      with the observed state of the world. Task-specific conditions include:
      * `Started`: `True` when the task has started to execute. * `Completed`:
      `True` when the task has succeeded. `False` when the task has failed.
    index: Required. Index of the task, unique per execution, and beginning at
      0.
    lastAttemptResult: Optional. Result of the last attempt of this task.
    logUri: Optional. URI where logs for this task can be found in Cloud
      Console.
    observedGeneration: Optional. The 'generation' of the task that was last
      processed by the controller.
    retried: Optional. The number of times this task was retried. Instances
      are retried when they fail up to the maxRetries limit.
    startTime: Optional. Represents time when the task started to run. It is
      not guaranteed to be set in happens-before order across separate
      operations. It is represented in RFC3339 form and is in UTC.
  """

  completionTime = _messages.StringField(1)
  conditions = _messages.MessageField('GoogleCloudRunV1Condition', 2, repeated=True)
  index = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  lastAttemptResult = _messages.MessageField('TaskAttemptResult', 4)
  logUri = _messages.StringField(5)
  observedGeneration = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  retried = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  startTime = _messages.StringField(8)


class TaskTemplateSpec(_messages.Message):
  r"""TaskTemplateSpec describes the data a task should have when created from
  a template.

  Fields:
    spec: Optional. Specification of the desired behavior of the task.
  """

  spec = _messages.MessageField('TaskSpec', 1)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TrafficTarget(_messages.Message):
  r"""TrafficTarget holds a single entry of the routing table for a Route.

  Fields:
    configurationName: [Deprecated] Not supported in Cloud Run. It must be
      empty.
    latestRevision: Uses the "status.latestReadyRevisionName" of the Service
      to determine the traffic target. When it changes, traffic will
      automatically migrate from the prior "latest ready" revision to the new
      one. This field must be false if RevisionName is set. This field
      defaults to true otherwise. If the field is set to true on Status, this
      means that the Revision was resolved from the Service's latest ready
      revision.
    percent: Percent specifies percent of the traffic to this Revision or
      Configuration. This defaults to zero if unspecified.
    revisionName: Points this traffic target to a specific Revision. This
      field is mutually exclusive with latest_revision.
    tag: Tag is used to expose a dedicated url for referencing this target
      exclusively.
    url: Output only. URL displays the URL for accessing tagged traffic
      targets. URL is displayed in status, and is disallowed on spec. URL must
      contain a scheme (e.g. https://) and a hostname, but may not contain
      anything else (e.g. basic auth, url path, etc.)
  """

  configurationName = _messages.StringField(1)
  latestRevision = _messages.BooleanField(2)
  percent = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  revisionName = _messages.StringField(4)
  tag = _messages.StringField(5)
  url = _messages.StringField(6)


class Volume(_messages.Message):
  r"""Volume represents a named volume in a container.

  Fields:
    configMap: Not supported in Cloud Run.
    csi: Volume specified by the Container Storage Interface driver
    emptyDir: Ephemeral storage used as a shared volume.
    name: Volume's name. In Cloud Run Fully Managed, the name 'cloudsql' is
      reserved.
    nfs: A NFSVolumeSource attribute.
    secret: The secret's value will be presented as the content of a file
      whose name is defined in the item path. If no items are defined, the
      name of the file is the secretName.
  """

  configMap = _messages.MessageField('ConfigMapVolumeSource', 1)
  csi = _messages.MessageField('CSIVolumeSource', 2)
  emptyDir = _messages.MessageField('EmptyDirVolumeSource', 3)
  name = _messages.StringField(4)
  nfs = _messages.MessageField('NFSVolumeSource', 5)
  secret = _messages.MessageField('SecretVolumeSource', 6)


class VolumeMount(_messages.Message):
  r"""VolumeMount describes a mounting of a Volume within a container.

  Fields:
    mountPath: Required. Path within the container at which the volume should
      be mounted. Must not contain ':'.
    name: Required. The name of the volume. There must be a corresponding
      Volume with the same name.
    readOnly: Sets the mount to be read-only or read-write. Not used by Cloud
      Run.
    subPath: Path within the volume from which the container's volume should
      be mounted. Defaults to "" (volume's root).
  """

  mountPath = _messages.StringField(1)
  name = _messages.StringField(2)
  readOnly = _messages.BooleanField(3)
  subPath = _messages.StringField(4)


class WorkerPool(_messages.Message):
  r"""WorkerPool acts as a top-level container that manages a set instance
  splits among a set of Revisions and a template for creating new Revisions.

  Fields:
    apiVersion: The API version for this call. It must be
      "run.googleapis.com/v1".
    kind: The kind of resource. It must be "WorkerPool".
    metadata: Metadata associated with this WorkerPool, including name,
      namespace, labels, and annotations. In Cloud Run, annotations with
      'run.googleapis.com/' and 'autoscaling.knative.dev' are restricted, and
      the accepted annotations will be different depending on the resource
      type. The following Cloud Run-specific annotations are accepted in
      WorkerPool.metadata.annotations. * `run.googleapis.com/binary-
      authorization-breakglass` * `run.googleapis.com/binary-authorization` *
      `run.googleapis.com/client-name` * `run.googleapis.com/description`
    spec: Holds the desired state of the WorkerPool (from the client).
    status: Communicates the system-controlled state of the WorkerPool.
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)
  metadata = _messages.MessageField('ObjectMeta', 3)
  spec = _messages.MessageField('WorkerPoolSpec', 4)
  status = _messages.MessageField('WorkerPoolStatus', 5)


class WorkerPoolSpec(_messages.Message):
  r"""WorkerPoolSpec holds the desired state of the WorkerPool's template and
  instance splits.

  Fields:
    instanceSplits: Specifies how to distribute instances over a collection of
      Revisions.
    template: Holds the latest specification for the Revision to be stamped
      out.
  """

  instanceSplits = _messages.MessageField('InstanceSplit', 1, repeated=True)
  template = _messages.MessageField('RevisionTemplate', 2)


class WorkerPoolStatus(_messages.Message):
  r"""The current state of the WorkerPool. Output only.

  Fields:
    conditions: Conditions communicate information about ongoing/complete
      reconciliation processes that bring the `spec` inline with the observed
      state of the world. * `Ready`: `True` when all underlying resources are
      ready.
    instanceSplits: Holds the configured workload distribution. These entries
      will always contain RevisionName references. When ConfigurationName
      appears in the spec, this will hold the LatestReadyRevisionName that we
      last observed.
    latestCreatedRevisionName: Name of the last revision that was created from
      this WorkerPool's template. It might not be ready yet, for that use
      LatestReadyRevisionName.
    latestReadyRevisionName: Name of the latest Revision from this
      WorkerPool's template that has had its `Ready` condition become `True`.
    observedGeneration: Returns the generation last seen by the system.
      Clients polling for completed reconciliation should poll until
      observedGeneration = metadata.generation and the Ready condition's
      status is True or False.
  """

  conditions = _messages.MessageField('GoogleCloudRunV1Condition', 1, repeated=True)
  instanceSplits = _messages.MessageField('InstanceSplit', 2, repeated=True)
  latestCreatedRevisionName = _messages.StringField(3)
  latestReadyRevisionName = _messages.StringField(4)
  observedGeneration = _messages.IntegerField(5, variant=_messages.Variant.INT32)


encoding.AddCustomJsonFieldMapping(
    ListMeta, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    Probe, 'exec_', 'exec')
encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    RunNamespacesConfigurationsListRequest, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    RunNamespacesDomainmappingsListRequest, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    RunNamespacesExecutionsListRequest, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    RunNamespacesJobsListRequest, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    RunNamespacesRevisionsListRequest, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    RunNamespacesRoutesListRequest, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    RunNamespacesServicesListRequest, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    RunNamespacesTasksListRequest, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    RunNamespacesWorkerpoolsListRequest, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    RunProjectsLocationsConfigurationsListRequest, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    RunProjectsLocationsDomainmappingsListRequest, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    RunProjectsLocationsJobsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    RunProjectsLocationsRevisionsListRequest, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    RunProjectsLocationsRoutesListRequest, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    RunProjectsLocationsServicesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    RunProjectsLocationsServicesListRequest, 'continue_', 'continue')
encoding.AddCustomJsonFieldMapping(
    RunProjectsLocationsWorkerpoolsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
