"""Generated client library for run version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.run.v1 import run_v1_messages as messages


class RunV1(base_api.BaseApiClient):
  """Generated client library for service run version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://run.googleapis.com/'
  MTLS_BASE_URL = 'https://run.mtls.googleapis.com/'

  _PACKAGE = 'run'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'RunV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new run handle."""
    url = url or self.BASE_URL
    super(RunV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.namespaces_authorizeddomains = self.NamespacesAuthorizeddomainsService(self)
    self.namespaces_configurations = self.NamespacesConfigurationsService(self)
    self.namespaces_domainmappings = self.NamespacesDomainmappingsService(self)
    self.namespaces_executions = self.NamespacesExecutionsService(self)
    self.namespaces_jobs = self.NamespacesJobsService(self)
    self.namespaces_revisions = self.NamespacesRevisionsService(self)
    self.namespaces_routes = self.NamespacesRoutesService(self)
    self.namespaces_services = self.NamespacesServicesService(self)
    self.namespaces_tasks = self.NamespacesTasksService(self)
    self.namespaces_workerpools = self.NamespacesWorkerpoolsService(self)
    self.namespaces = self.NamespacesService(self)
    self.projects_authorizeddomains = self.ProjectsAuthorizeddomainsService(self)
    self.projects_locations_authorizeddomains = self.ProjectsLocationsAuthorizeddomainsService(self)
    self.projects_locations_configurations = self.ProjectsLocationsConfigurationsService(self)
    self.projects_locations_domainmappings = self.ProjectsLocationsDomainmappingsService(self)
    self.projects_locations_jobs = self.ProjectsLocationsJobsService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_revisions = self.ProjectsLocationsRevisionsService(self)
    self.projects_locations_routes = self.ProjectsLocationsRoutesService(self)
    self.projects_locations_services = self.ProjectsLocationsServicesService(self)
    self.projects_locations_workerpools = self.ProjectsLocationsWorkerpoolsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class NamespacesAuthorizeddomainsService(base_api.BaseApiService):
    """Service class for the namespaces_authorizeddomains resource."""

    _NAME = 'namespaces_authorizeddomains'

    def __init__(self, client):
      super(RunV1.NamespacesAuthorizeddomainsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List authorized domains.

      Args:
        request: (RunNamespacesAuthorizeddomainsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAuthorizedDomainsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/domains.cloudrun.com/v1/namespaces/{namespacesId}/authorizeddomains',
        http_method='GET',
        method_id='run.namespaces.authorizeddomains.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='apis/domains.cloudrun.com/v1/{+parent}/authorizeddomains',
        request_field='',
        request_type_name='RunNamespacesAuthorizeddomainsListRequest',
        response_type_name='ListAuthorizedDomainsResponse',
        supports_download=False,
    )

  class NamespacesConfigurationsService(base_api.BaseApiService):
    """Service class for the namespaces_configurations resource."""

    _NAME = 'namespaces_configurations'

    def __init__(self, client):
      super(RunV1.NamespacesConfigurationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get information about a configuration.

      Args:
        request: (RunNamespacesConfigurationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Configuration) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/serving.knative.dev/v1/namespaces/{namespacesId}/configurations/{configurationsId}',
        http_method='GET',
        method_id='run.namespaces.configurations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='apis/serving.knative.dev/v1/{+name}',
        request_field='',
        request_type_name='RunNamespacesConfigurationsGetRequest',
        response_type_name='Configuration',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List configurations. Results are sorted by creation time, descending.

      Args:
        request: (RunNamespacesConfigurationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListConfigurationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/serving.knative.dev/v1/namespaces/{namespacesId}/configurations',
        http_method='GET',
        method_id='run.namespaces.configurations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['continue_', 'fieldSelector', 'includeUninitialized', 'labelSelector', 'limit', 'resourceVersion', 'watch'],
        relative_path='apis/serving.knative.dev/v1/{+parent}/configurations',
        request_field='',
        request_type_name='RunNamespacesConfigurationsListRequest',
        response_type_name='ListConfigurationsResponse',
        supports_download=False,
    )

  class NamespacesDomainmappingsService(base_api.BaseApiService):
    """Service class for the namespaces_domainmappings resource."""

    _NAME = 'namespaces_domainmappings'

    def __init__(self, client):
      super(RunV1.NamespacesDomainmappingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new domain mapping.

      Args:
        request: (RunNamespacesDomainmappingsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DomainMapping) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/domains.cloudrun.com/v1/namespaces/{namespacesId}/domainmappings',
        http_method='POST',
        method_id='run.namespaces.domainmappings.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['dryRun'],
        relative_path='apis/domains.cloudrun.com/v1/{+parent}/domainmappings',
        request_field='domainMapping',
        request_type_name='RunNamespacesDomainmappingsCreateRequest',
        response_type_name='DomainMapping',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a domain mapping.

      Args:
        request: (RunNamespacesDomainmappingsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Status) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/domains.cloudrun.com/v1/namespaces/{namespacesId}/domainmappings/{domainmappingsId}',
        http_method='DELETE',
        method_id='run.namespaces.domainmappings.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['apiVersion', 'dryRun', 'kind', 'propagationPolicy'],
        relative_path='apis/domains.cloudrun.com/v1/{+name}',
        request_field='',
        request_type_name='RunNamespacesDomainmappingsDeleteRequest',
        response_type_name='Status',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get information about a domain mapping.

      Args:
        request: (RunNamespacesDomainmappingsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DomainMapping) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/domains.cloudrun.com/v1/namespaces/{namespacesId}/domainmappings/{domainmappingsId}',
        http_method='GET',
        method_id='run.namespaces.domainmappings.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='apis/domains.cloudrun.com/v1/{+name}',
        request_field='',
        request_type_name='RunNamespacesDomainmappingsGetRequest',
        response_type_name='DomainMapping',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List all domain mappings.

      Args:
        request: (RunNamespacesDomainmappingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDomainMappingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/domains.cloudrun.com/v1/namespaces/{namespacesId}/domainmappings',
        http_method='GET',
        method_id='run.namespaces.domainmappings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['continue_', 'fieldSelector', 'includeUninitialized', 'labelSelector', 'limit', 'resourceVersion', 'watch'],
        relative_path='apis/domains.cloudrun.com/v1/{+parent}/domainmappings',
        request_field='',
        request_type_name='RunNamespacesDomainmappingsListRequest',
        response_type_name='ListDomainMappingsResponse',
        supports_download=False,
    )

  class NamespacesExecutionsService(base_api.BaseApiService):
    """Service class for the namespaces_executions resource."""

    _NAME = 'namespaces_executions'

    def __init__(self, client):
      super(RunV1.NamespacesExecutionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancel an execution.

      Args:
        request: (RunNamespacesExecutionsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Execution) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/run.googleapis.com/v1/namespaces/{namespacesId}/executions/{executionsId}:cancel',
        http_method='POST',
        method_id='run.namespaces.executions.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='apis/run.googleapis.com/v1/{+name}:cancel',
        request_field='cancelExecutionRequest',
        request_type_name='RunNamespacesExecutionsCancelRequest',
        response_type_name='Execution',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete an execution.

      Args:
        request: (RunNamespacesExecutionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Status) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/run.googleapis.com/v1/namespaces/{namespacesId}/executions/{executionsId}',
        http_method='DELETE',
        method_id='run.namespaces.executions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['apiVersion', 'kind', 'propagationPolicy'],
        relative_path='apis/run.googleapis.com/v1/{+name}',
        request_field='',
        request_type_name='RunNamespacesExecutionsDeleteRequest',
        response_type_name='Status',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get information about an execution.

      Args:
        request: (RunNamespacesExecutionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Execution) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/run.googleapis.com/v1/namespaces/{namespacesId}/executions/{executionsId}',
        http_method='GET',
        method_id='run.namespaces.executions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='apis/run.googleapis.com/v1/{+name}',
        request_field='',
        request_type_name='RunNamespacesExecutionsGetRequest',
        response_type_name='Execution',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List executions. Results are sorted by creation time, descending.

      Args:
        request: (RunNamespacesExecutionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListExecutionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/run.googleapis.com/v1/namespaces/{namespacesId}/executions',
        http_method='GET',
        method_id='run.namespaces.executions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['continue_', 'fieldSelector', 'includeUninitialized', 'labelSelector', 'limit', 'resourceVersion', 'watch'],
        relative_path='apis/run.googleapis.com/v1/{+parent}/executions',
        request_field='',
        request_type_name='RunNamespacesExecutionsListRequest',
        response_type_name='ListExecutionsResponse',
        supports_download=False,
    )

  class NamespacesJobsService(base_api.BaseApiService):
    """Service class for the namespaces_jobs resource."""

    _NAME = 'namespaces_jobs'

    def __init__(self, client):
      super(RunV1.NamespacesJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a job.

      Args:
        request: (RunNamespacesJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Job) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/run.googleapis.com/v1/namespaces/{namespacesId}/jobs',
        http_method='POST',
        method_id='run.namespaces.jobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='apis/run.googleapis.com/v1/{+parent}/jobs',
        request_field='job',
        request_type_name='RunNamespacesJobsCreateRequest',
        response_type_name='Job',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a job.

      Args:
        request: (RunNamespacesJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Status) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/run.googleapis.com/v1/namespaces/{namespacesId}/jobs/{jobsId}',
        http_method='DELETE',
        method_id='run.namespaces.jobs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['apiVersion', 'force', 'kind', 'propagationPolicy'],
        relative_path='apis/run.googleapis.com/v1/{+name}',
        request_field='',
        request_type_name='RunNamespacesJobsDeleteRequest',
        response_type_name='Status',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get information about a job.

      Args:
        request: (RunNamespacesJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Job) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/run.googleapis.com/v1/namespaces/{namespacesId}/jobs/{jobsId}',
        http_method='GET',
        method_id='run.namespaces.jobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='apis/run.googleapis.com/v1/{+name}',
        request_field='',
        request_type_name='RunNamespacesJobsGetRequest',
        response_type_name='Job',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List jobs. Results are sorted by creation time, descending.

      Args:
        request: (RunNamespacesJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/run.googleapis.com/v1/namespaces/{namespacesId}/jobs',
        http_method='GET',
        method_id='run.namespaces.jobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['continue_', 'fieldSelector', 'includeUninitialized', 'labelSelector', 'limit', 'resourceVersion', 'watch'],
        relative_path='apis/run.googleapis.com/v1/{+parent}/jobs',
        request_field='',
        request_type_name='RunNamespacesJobsListRequest',
        response_type_name='ListJobsResponse',
        supports_download=False,
    )

    def ReplaceJob(self, request, global_params=None):
      r"""Replace a job. Only the spec and metadata labels and annotations are modifiable. After the Replace request, Cloud Run will work to make the 'status' match the requested 'spec'. May provide metadata.resourceVersion to enforce update from last read for optimistic concurrency control.

      Args:
        request: (RunNamespacesJobsReplaceJobRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Job) The response message.
      """
      config = self.GetMethodConfig('ReplaceJob')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReplaceJob.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/run.googleapis.com/v1/namespaces/{namespacesId}/jobs/{jobsId}',
        http_method='PUT',
        method_id='run.namespaces.jobs.replaceJob',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='apis/run.googleapis.com/v1/{+name}',
        request_field='job',
        request_type_name='RunNamespacesJobsReplaceJobRequest',
        response_type_name='Job',
        supports_download=False,
    )

    def Run(self, request, global_params=None):
      r"""Trigger creation of a new execution of this job.

      Args:
        request: (RunNamespacesJobsRunRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Execution) The response message.
      """
      config = self.GetMethodConfig('Run')
      return self._RunMethod(
          config, request, global_params=global_params)

    Run.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/run.googleapis.com/v1/namespaces/{namespacesId}/jobs/{jobsId}:run',
        http_method='POST',
        method_id='run.namespaces.jobs.run',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='apis/run.googleapis.com/v1/{+name}:run',
        request_field='runJobRequest',
        request_type_name='RunNamespacesJobsRunRequest',
        response_type_name='Execution',
        supports_download=False,
    )

  class NamespacesRevisionsService(base_api.BaseApiService):
    """Service class for the namespaces_revisions resource."""

    _NAME = 'namespaces_revisions'

    def __init__(self, client):
      super(RunV1.NamespacesRevisionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Delete a revision.

      Args:
        request: (RunNamespacesRevisionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Status) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/serving.knative.dev/v1/namespaces/{namespacesId}/revisions/{revisionsId}',
        http_method='DELETE',
        method_id='run.namespaces.revisions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['apiVersion', 'dryRun', 'kind', 'propagationPolicy'],
        relative_path='apis/serving.knative.dev/v1/{+name}',
        request_field='',
        request_type_name='RunNamespacesRevisionsDeleteRequest',
        response_type_name='Status',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get information about a revision.

      Args:
        request: (RunNamespacesRevisionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Revision) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/serving.knative.dev/v1/namespaces/{namespacesId}/revisions/{revisionsId}',
        http_method='GET',
        method_id='run.namespaces.revisions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='apis/serving.knative.dev/v1/{+name}',
        request_field='',
        request_type_name='RunNamespacesRevisionsGetRequest',
        response_type_name='Revision',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List revisions. Results are sorted by creation time, descending.

      Args:
        request: (RunNamespacesRevisionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRevisionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/serving.knative.dev/v1/namespaces/{namespacesId}/revisions',
        http_method='GET',
        method_id='run.namespaces.revisions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['continue_', 'fieldSelector', 'includeUninitialized', 'labelSelector', 'limit', 'resourceVersion', 'watch'],
        relative_path='apis/serving.knative.dev/v1/{+parent}/revisions',
        request_field='',
        request_type_name='RunNamespacesRevisionsListRequest',
        response_type_name='ListRevisionsResponse',
        supports_download=False,
    )

  class NamespacesRoutesService(base_api.BaseApiService):
    """Service class for the namespaces_routes resource."""

    _NAME = 'namespaces_routes'

    def __init__(self, client):
      super(RunV1.NamespacesRoutesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get information about a route.

      Args:
        request: (RunNamespacesRoutesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Route) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/serving.knative.dev/v1/namespaces/{namespacesId}/routes/{routesId}',
        http_method='GET',
        method_id='run.namespaces.routes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='apis/serving.knative.dev/v1/{+name}',
        request_field='',
        request_type_name='RunNamespacesRoutesGetRequest',
        response_type_name='Route',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List routes. Results are sorted by creation time, descending.

      Args:
        request: (RunNamespacesRoutesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRoutesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/serving.knative.dev/v1/namespaces/{namespacesId}/routes',
        http_method='GET',
        method_id='run.namespaces.routes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['continue_', 'fieldSelector', 'includeUninitialized', 'labelSelector', 'limit', 'resourceVersion', 'watch'],
        relative_path='apis/serving.knative.dev/v1/{+parent}/routes',
        request_field='',
        request_type_name='RunNamespacesRoutesListRequest',
        response_type_name='ListRoutesResponse',
        supports_download=False,
    )

  class NamespacesServicesService(base_api.BaseApiService):
    """Service class for the namespaces_services resource."""

    _NAME = 'namespaces_services'

    def __init__(self, client):
      super(RunV1.NamespacesServicesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Service. Service creation will trigger a new deployment. Use GetService, and check service.status to determine if the Service is ready.

      Args:
        request: (RunNamespacesServicesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Service) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/serving.knative.dev/v1/namespaces/{namespacesId}/services',
        http_method='POST',
        method_id='run.namespaces.services.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['dryRun'],
        relative_path='apis/serving.knative.dev/v1/{+parent}/services',
        request_field='service',
        request_type_name='RunNamespacesServicesCreateRequest',
        response_type_name='Service',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the provided service. This will cause the Service to stop serving traffic and will delete all associated Revisions.

      Args:
        request: (RunNamespacesServicesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Status) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/serving.knative.dev/v1/namespaces/{namespacesId}/services/{servicesId}',
        http_method='DELETE',
        method_id='run.namespaces.services.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['apiVersion', 'dryRun', 'kind', 'propagationPolicy'],
        relative_path='apis/serving.knative.dev/v1/{+name}',
        request_field='',
        request_type_name='RunNamespacesServicesDeleteRequest',
        response_type_name='Status',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about a service.

      Args:
        request: (RunNamespacesServicesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Service) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/serving.knative.dev/v1/namespaces/{namespacesId}/services/{servicesId}',
        http_method='GET',
        method_id='run.namespaces.services.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='apis/serving.knative.dev/v1/{+name}',
        request_field='',
        request_type_name='RunNamespacesServicesGetRequest',
        response_type_name='Service',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists services for the given project and region. Results are sorted by creation time, descending.

      Args:
        request: (RunNamespacesServicesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListServicesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/serving.knative.dev/v1/namespaces/{namespacesId}/services',
        http_method='GET',
        method_id='run.namespaces.services.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['continue_', 'fieldSelector', 'includeUninitialized', 'labelSelector', 'limit', 'resourceVersion', 'watch'],
        relative_path='apis/serving.knative.dev/v1/{+parent}/services',
        request_field='',
        request_type_name='RunNamespacesServicesListRequest',
        response_type_name='ListServicesResponse',
        supports_download=False,
    )

    def ReplaceService(self, request, global_params=None):
      r"""Replaces a service. Only the spec and metadata labels and annotations are modifiable. After the Update request, Cloud Run will work to make the 'status' match the requested 'spec'. May provide metadata.resourceVersion to enforce update from last read for optimistic concurrency control.

      Args:
        request: (RunNamespacesServicesReplaceServiceRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Service) The response message.
      """
      config = self.GetMethodConfig('ReplaceService')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReplaceService.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/serving.knative.dev/v1/namespaces/{namespacesId}/services/{servicesId}',
        http_method='PUT',
        method_id='run.namespaces.services.replaceService',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['dryRun'],
        relative_path='apis/serving.knative.dev/v1/{+name}',
        request_field='service',
        request_type_name='RunNamespacesServicesReplaceServiceRequest',
        response_type_name='Service',
        supports_download=False,
    )

  class NamespacesTasksService(base_api.BaseApiService):
    """Service class for the namespaces_tasks resource."""

    _NAME = 'namespaces_tasks'

    def __init__(self, client):
      super(RunV1.NamespacesTasksService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get information about a task.

      Args:
        request: (RunNamespacesTasksGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Task) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/run.googleapis.com/v1/namespaces/{namespacesId}/tasks/{tasksId}',
        http_method='GET',
        method_id='run.namespaces.tasks.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='apis/run.googleapis.com/v1/{+name}',
        request_field='',
        request_type_name='RunNamespacesTasksGetRequest',
        response_type_name='Task',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List tasks.

      Args:
        request: (RunNamespacesTasksListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListTasksResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/run.googleapis.com/v1/namespaces/{namespacesId}/tasks',
        http_method='GET',
        method_id='run.namespaces.tasks.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['continue_', 'fieldSelector', 'includeUninitialized', 'labelSelector', 'limit', 'resourceVersion', 'watch'],
        relative_path='apis/run.googleapis.com/v1/{+parent}/tasks',
        request_field='',
        request_type_name='RunNamespacesTasksListRequest',
        response_type_name='ListTasksResponse',
        supports_download=False,
    )

  class NamespacesWorkerpoolsService(base_api.BaseApiService):
    """Service class for the namespaces_workerpools resource."""

    _NAME = 'namespaces_workerpools'

    def __init__(self, client):
      super(RunV1.NamespacesWorkerpoolsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new WorkerPool. WorkerPool creation will trigger a new deployment. Use GetWorkerPool, and check worker_pool.status to determine if the WorkerPool is ready.

      Args:
        request: (RunNamespacesWorkerpoolsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkerPool) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/run.googleapis.com/v1/namespaces/{namespacesId}/workerpools',
        http_method='POST',
        method_id='run.namespaces.workerpools.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['dryRun'],
        relative_path='apis/run.googleapis.com/v1/{+parent}/workerpools',
        request_field='workerPool',
        request_type_name='RunNamespacesWorkerpoolsCreateRequest',
        response_type_name='WorkerPool',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the provided worker pool. This will cause the WorkerPool to stop all instances and will delete all associated WorkerPoolRevisions.

      Args:
        request: (RunNamespacesWorkerpoolsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Status) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/run.googleapis.com/v1/namespaces/{namespacesId}/workerpools/{workerpoolsId}',
        http_method='DELETE',
        method_id='run.namespaces.workerpools.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['dryRun'],
        relative_path='apis/run.googleapis.com/v1/{+name}',
        request_field='',
        request_type_name='RunNamespacesWorkerpoolsDeleteRequest',
        response_type_name='Status',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about a worker pool.

      Args:
        request: (RunNamespacesWorkerpoolsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkerPool) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/run.googleapis.com/v1/namespaces/{namespacesId}/workerpools/{workerpoolsId}',
        http_method='GET',
        method_id='run.namespaces.workerpools.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='apis/run.googleapis.com/v1/{+name}',
        request_field='',
        request_type_name='RunNamespacesWorkerpoolsGetRequest',
        response_type_name='WorkerPool',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists worker pools for the given project and region. Results are sorted by creation time, descending.

      Args:
        request: (RunNamespacesWorkerpoolsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWorkerPoolsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/run.googleapis.com/v1/namespaces/{namespacesId}/workerpools',
        http_method='GET',
        method_id='run.namespaces.workerpools.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['continue_', 'labelSelector', 'limit'],
        relative_path='apis/run.googleapis.com/v1/{+parent}/workerpools',
        request_field='',
        request_type_name='RunNamespacesWorkerpoolsListRequest',
        response_type_name='ListWorkerPoolsResponse',
        supports_download=False,
    )

    def ReplaceWorkerPool(self, request, global_params=None):
      r"""Replaces a worker pool. Only the spec and metadata labels and annotations are modifiable. After the Update request, Cloud Run will work to make the 'status' match the requested 'spec'. May provide metadata.resourceVersion to enforce update from last read for optimistic concurrency control.

      Args:
        request: (RunNamespacesWorkerpoolsReplaceWorkerPoolRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WorkerPool) The response message.
      """
      config = self.GetMethodConfig('ReplaceWorkerPool')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReplaceWorkerPool.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='apis/run.googleapis.com/v1/namespaces/{namespacesId}/workerpools/{workerpoolsId}',
        http_method='PUT',
        method_id='run.namespaces.workerpools.replaceWorkerPool',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['dryRun'],
        relative_path='apis/run.googleapis.com/v1/{+name}',
        request_field='workerPool',
        request_type_name='RunNamespacesWorkerpoolsReplaceWorkerPoolRequest',
        response_type_name='WorkerPool',
        supports_download=False,
    )

  class NamespacesService(base_api.BaseApiService):
    """Service class for the namespaces resource."""

    _NAME = 'namespaces'

    def __init__(self, client):
      super(RunV1.NamespacesService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsAuthorizeddomainsService(base_api.BaseApiService):
    """Service class for the projects_authorizeddomains resource."""

    _NAME = 'projects_authorizeddomains'

    def __init__(self, client):
      super(RunV1.ProjectsAuthorizeddomainsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List authorized domains.

      Args:
        request: (RunProjectsAuthorizeddomainsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAuthorizedDomainsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/authorizeddomains',
        http_method='GET',
        method_id='run.projects.authorizeddomains.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/authorizeddomains',
        request_field='',
        request_type_name='RunProjectsAuthorizeddomainsListRequest',
        response_type_name='ListAuthorizedDomainsResponse',
        supports_download=False,
    )

  class ProjectsLocationsAuthorizeddomainsService(base_api.BaseApiService):
    """Service class for the projects_locations_authorizeddomains resource."""

    _NAME = 'projects_locations_authorizeddomains'

    def __init__(self, client):
      super(RunV1.ProjectsLocationsAuthorizeddomainsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List authorized domains.

      Args:
        request: (RunProjectsLocationsAuthorizeddomainsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAuthorizedDomainsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/authorizeddomains',
        http_method='GET',
        method_id='run.projects.locations.authorizeddomains.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/authorizeddomains',
        request_field='',
        request_type_name='RunProjectsLocationsAuthorizeddomainsListRequest',
        response_type_name='ListAuthorizedDomainsResponse',
        supports_download=False,
    )

  class ProjectsLocationsConfigurationsService(base_api.BaseApiService):
    """Service class for the projects_locations_configurations resource."""

    _NAME = 'projects_locations_configurations'

    def __init__(self, client):
      super(RunV1.ProjectsLocationsConfigurationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get information about a configuration.

      Args:
        request: (RunProjectsLocationsConfigurationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Configuration) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/configurations/{configurationsId}',
        http_method='GET',
        method_id='run.projects.locations.configurations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='RunProjectsLocationsConfigurationsGetRequest',
        response_type_name='Configuration',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List configurations. Results are sorted by creation time, descending.

      Args:
        request: (RunProjectsLocationsConfigurationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListConfigurationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/configurations',
        http_method='GET',
        method_id='run.projects.locations.configurations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['continue_', 'fieldSelector', 'includeUninitialized', 'labelSelector', 'limit', 'resourceVersion', 'watch'],
        relative_path='v1/{+parent}/configurations',
        request_field='',
        request_type_name='RunProjectsLocationsConfigurationsListRequest',
        response_type_name='ListConfigurationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDomainmappingsService(base_api.BaseApiService):
    """Service class for the projects_locations_domainmappings resource."""

    _NAME = 'projects_locations_domainmappings'

    def __init__(self, client):
      super(RunV1.ProjectsLocationsDomainmappingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a new domain mapping.

      Args:
        request: (RunProjectsLocationsDomainmappingsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DomainMapping) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/domainmappings',
        http_method='POST',
        method_id='run.projects.locations.domainmappings.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['dryRun'],
        relative_path='v1/{+parent}/domainmappings',
        request_field='domainMapping',
        request_type_name='RunProjectsLocationsDomainmappingsCreateRequest',
        response_type_name='DomainMapping',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a domain mapping.

      Args:
        request: (RunProjectsLocationsDomainmappingsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Status) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/domainmappings/{domainmappingsId}',
        http_method='DELETE',
        method_id='run.projects.locations.domainmappings.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['apiVersion', 'dryRun', 'kind', 'propagationPolicy'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='RunProjectsLocationsDomainmappingsDeleteRequest',
        response_type_name='Status',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get information about a domain mapping.

      Args:
        request: (RunProjectsLocationsDomainmappingsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DomainMapping) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/domainmappings/{domainmappingsId}',
        http_method='GET',
        method_id='run.projects.locations.domainmappings.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='RunProjectsLocationsDomainmappingsGetRequest',
        response_type_name='DomainMapping',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List all domain mappings.

      Args:
        request: (RunProjectsLocationsDomainmappingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDomainMappingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/domainmappings',
        http_method='GET',
        method_id='run.projects.locations.domainmappings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['continue_', 'fieldSelector', 'includeUninitialized', 'labelSelector', 'limit', 'resourceVersion', 'watch'],
        relative_path='v1/{+parent}/domainmappings',
        request_field='',
        request_type_name='RunProjectsLocationsDomainmappingsListRequest',
        response_type_name='ListDomainMappingsResponse',
        supports_download=False,
    )

  class ProjectsLocationsJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_jobs resource."""

    _NAME = 'projects_locations_jobs'

    def __init__(self, client):
      super(RunV1.ProjectsLocationsJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def GetIamPolicy(self, request, global_params=None):
      r"""Get the IAM Access Control policy currently in effect for the given job. This result does not include any inherited policies.

      Args:
        request: (RunProjectsLocationsJobsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}:getIamPolicy',
        http_method='GET',
        method_id='run.projects.locations.jobs.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='RunProjectsLocationsJobsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the IAM Access control policy for the specified job. Overwrites any existing policy.

      Args:
        request: (RunProjectsLocationsJobsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}:setIamPolicy',
        http_method='POST',
        method_id='run.projects.locations.jobs.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='RunProjectsLocationsJobsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified job. There are no permissions required for making this API call.

      Args:
        request: (RunProjectsLocationsJobsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}:testIamPermissions',
        http_method='POST',
        method_id='run.projects.locations.jobs.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='RunProjectsLocationsJobsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(RunV1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (RunProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='run.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='RunProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (RunProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='run.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='RunProjectsLocationsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (RunProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='run.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='RunProjectsLocationsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (RunProjectsLocationsOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='run.projects.locations.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:wait',
        request_field='googleLongrunningWaitOperationRequest',
        request_type_name='RunProjectsLocationsOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsRevisionsService(base_api.BaseApiService):
    """Service class for the projects_locations_revisions resource."""

    _NAME = 'projects_locations_revisions'

    def __init__(self, client):
      super(RunV1.ProjectsLocationsRevisionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Delete a revision.

      Args:
        request: (RunProjectsLocationsRevisionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Status) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/revisions/{revisionsId}',
        http_method='DELETE',
        method_id='run.projects.locations.revisions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['apiVersion', 'dryRun', 'kind', 'propagationPolicy'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='RunProjectsLocationsRevisionsDeleteRequest',
        response_type_name='Status',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get information about a revision.

      Args:
        request: (RunProjectsLocationsRevisionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Revision) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/revisions/{revisionsId}',
        http_method='GET',
        method_id='run.projects.locations.revisions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='RunProjectsLocationsRevisionsGetRequest',
        response_type_name='Revision',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List revisions. Results are sorted by creation time, descending.

      Args:
        request: (RunProjectsLocationsRevisionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRevisionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/revisions',
        http_method='GET',
        method_id='run.projects.locations.revisions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['continue_', 'fieldSelector', 'includeUninitialized', 'labelSelector', 'limit', 'resourceVersion', 'watch'],
        relative_path='v1/{+parent}/revisions',
        request_field='',
        request_type_name='RunProjectsLocationsRevisionsListRequest',
        response_type_name='ListRevisionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsRoutesService(base_api.BaseApiService):
    """Service class for the projects_locations_routes resource."""

    _NAME = 'projects_locations_routes'

    def __init__(self, client):
      super(RunV1.ProjectsLocationsRoutesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get information about a route.

      Args:
        request: (RunProjectsLocationsRoutesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Route) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/routes/{routesId}',
        http_method='GET',
        method_id='run.projects.locations.routes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='RunProjectsLocationsRoutesGetRequest',
        response_type_name='Route',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List routes. Results are sorted by creation time, descending.

      Args:
        request: (RunProjectsLocationsRoutesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRoutesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/routes',
        http_method='GET',
        method_id='run.projects.locations.routes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['continue_', 'fieldSelector', 'includeUninitialized', 'labelSelector', 'limit', 'resourceVersion', 'watch'],
        relative_path='v1/{+parent}/routes',
        request_field='',
        request_type_name='RunProjectsLocationsRoutesListRequest',
        response_type_name='ListRoutesResponse',
        supports_download=False,
    )

  class ProjectsLocationsServicesService(base_api.BaseApiService):
    """Service class for the projects_locations_services resource."""

    _NAME = 'projects_locations_services'

    def __init__(self, client):
      super(RunV1.ProjectsLocationsServicesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Service. Service creation will trigger a new deployment. Use GetService, and check service.status to determine if the Service is ready.

      Args:
        request: (RunProjectsLocationsServicesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Service) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services',
        http_method='POST',
        method_id='run.projects.locations.services.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['dryRun'],
        relative_path='v1/{+parent}/services',
        request_field='service',
        request_type_name='RunProjectsLocationsServicesCreateRequest',
        response_type_name='Service',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the provided service. This will cause the Service to stop serving traffic and will delete all associated Revisions.

      Args:
        request: (RunProjectsLocationsServicesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Status) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}',
        http_method='DELETE',
        method_id='run.projects.locations.services.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['apiVersion', 'dryRun', 'kind', 'propagationPolicy'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='RunProjectsLocationsServicesDeleteRequest',
        response_type_name='Status',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about a service.

      Args:
        request: (RunProjectsLocationsServicesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Service) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}',
        http_method='GET',
        method_id='run.projects.locations.services.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='RunProjectsLocationsServicesGetRequest',
        response_type_name='Service',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the IAM Access Control policy currently in effect for the given Cloud Run service. This result does not include any inherited policies.

      Args:
        request: (RunProjectsLocationsServicesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:getIamPolicy',
        http_method='GET',
        method_id='run.projects.locations.services.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='RunProjectsLocationsServicesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists services for the given project and region. Results are sorted by creation time, descending.

      Args:
        request: (RunProjectsLocationsServicesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListServicesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services',
        http_method='GET',
        method_id='run.projects.locations.services.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['continue_', 'fieldSelector', 'includeUninitialized', 'labelSelector', 'limit', 'resourceVersion', 'watch'],
        relative_path='v1/{+parent}/services',
        request_field='',
        request_type_name='RunProjectsLocationsServicesListRequest',
        response_type_name='ListServicesResponse',
        supports_download=False,
    )

    def ReplaceService(self, request, global_params=None):
      r"""Replaces a service. Only the spec and metadata labels and annotations are modifiable. After the Update request, Cloud Run will work to make the 'status' match the requested 'spec'. May provide metadata.resourceVersion to enforce update from last read for optimistic concurrency control.

      Args:
        request: (RunProjectsLocationsServicesReplaceServiceRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Service) The response message.
      """
      config = self.GetMethodConfig('ReplaceService')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReplaceService.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}',
        http_method='PUT',
        method_id='run.projects.locations.services.replaceService',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['dryRun'],
        relative_path='v1/{+name}',
        request_field='service',
        request_type_name='RunProjectsLocationsServicesReplaceServiceRequest',
        response_type_name='Service',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the IAM Access control policy for the specified Service. Overwrites any existing policy.

      Args:
        request: (RunProjectsLocationsServicesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:setIamPolicy',
        http_method='POST',
        method_id='run.projects.locations.services.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='RunProjectsLocationsServicesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified Project. There are no permissions required for making this API call.

      Args:
        request: (RunProjectsLocationsServicesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:testIamPermissions',
        http_method='POST',
        method_id='run.projects.locations.services.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='RunProjectsLocationsServicesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsWorkerpoolsService(base_api.BaseApiService):
    """Service class for the projects_locations_workerpools resource."""

    _NAME = 'projects_locations_workerpools'

    def __init__(self, client):
      super(RunV1.ProjectsLocationsWorkerpoolsService, self).__init__(client)
      self._upload_configs = {
          }

    def GetIamPolicy(self, request, global_params=None):
      r"""Get the IAM Access Control policy currently in effect for the given worker pool. This result does not include any inherited policies.

      Args:
        request: (RunProjectsLocationsWorkerpoolsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workerpools/{workerpoolsId}:getIamPolicy',
        http_method='GET',
        method_id='run.projects.locations.workerpools.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='RunProjectsLocationsWorkerpoolsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the IAM Access control policy for the specified worker pool. Overwrites any existing policy.

      Args:
        request: (RunProjectsLocationsWorkerpoolsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workerpools/{workerpoolsId}:setIamPolicy',
        http_method='POST',
        method_id='run.projects.locations.workerpools.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='RunProjectsLocationsWorkerpoolsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified worker pool. There are no permissions required for making this API call.

      Args:
        request: (RunProjectsLocationsWorkerpoolsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/workerpools/{workerpoolsId}:testIamPermissions',
        http_method='POST',
        method_id='run.projects.locations.workerpools.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='RunProjectsLocationsWorkerpoolsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(RunV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (RunProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='run.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/locations',
        request_field='',
        request_type_name='RunProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(RunV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
