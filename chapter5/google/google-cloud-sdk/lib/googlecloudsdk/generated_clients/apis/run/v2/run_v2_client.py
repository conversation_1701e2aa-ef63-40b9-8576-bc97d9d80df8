"""Generated client library for run version v2."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.run.v2 import run_v2_messages as messages


class RunV2(base_api.BaseApiClient):
  """Generated client library for service run version v2."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://run.googleapis.com/'
  MTLS_BASE_URL = 'https://run.mtls.googleapis.com/'

  _PACKAGE = 'run'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v2'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'RunV2'
  _URL_VERSION = 'v2'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new run handle."""
    url = url or self.BASE_URL
    super(RunV2, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_builds = self.ProjectsLocationsBuildsService(self)
    self.projects_locations_jobs_executions_tasks = self.ProjectsLocationsJobsExecutionsTasksService(self)
    self.projects_locations_jobs_executions = self.ProjectsLocationsJobsExecutionsService(self)
    self.projects_locations_jobs = self.ProjectsLocationsJobsService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_services_revisions = self.ProjectsLocationsServicesRevisionsService(self)
    self.projects_locations_services = self.ProjectsLocationsServicesService(self)
    self.projects_locations_workerPools_revisions = self.ProjectsLocationsWorkerPoolsRevisionsService(self)
    self.projects_locations_workerPools = self.ProjectsLocationsWorkerPoolsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsBuildsService(base_api.BaseApiService):
    """Service class for the projects_locations_builds resource."""

    _NAME = 'projects_locations_builds'

    def __init__(self, client):
      super(RunV2.ProjectsLocationsBuildsService, self).__init__(client)
      self._upload_configs = {
          }

    def Submit(self, request, global_params=None):
      r"""Submits a build in a given project.

      Args:
        request: (RunProjectsLocationsBuildsSubmitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRunV2SubmitBuildResponse) The response message.
      """
      config = self.GetMethodConfig('Submit')
      return self._RunMethod(
          config, request, global_params=global_params)

    Submit.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/builds:submit',
        http_method='POST',
        method_id='run.projects.locations.builds.submit',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/builds:submit',
        request_field='googleCloudRunV2SubmitBuildRequest',
        request_type_name='RunProjectsLocationsBuildsSubmitRequest',
        response_type_name='GoogleCloudRunV2SubmitBuildResponse',
        supports_download=False,
    )

  class ProjectsLocationsJobsExecutionsTasksService(base_api.BaseApiService):
    """Service class for the projects_locations_jobs_executions_tasks resource."""

    _NAME = 'projects_locations_jobs_executions_tasks'

    def __init__(self, client):
      super(RunV2.ProjectsLocationsJobsExecutionsTasksService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a Task.

      Args:
        request: (RunProjectsLocationsJobsExecutionsTasksGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRunV2Task) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}/executions/{executionsId}/tasks/{tasksId}',
        http_method='GET',
        method_id='run.projects.locations.jobs.executions.tasks.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='RunProjectsLocationsJobsExecutionsTasksGetRequest',
        response_type_name='GoogleCloudRunV2Task',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Tasks from an Execution of a Job.

      Args:
        request: (RunProjectsLocationsJobsExecutionsTasksListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRunV2ListTasksResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}/executions/{executionsId}/tasks',
        http_method='GET',
        method_id='run.projects.locations.jobs.executions.tasks.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'showDeleted'],
        relative_path='v2/{+parent}/tasks',
        request_field='',
        request_type_name='RunProjectsLocationsJobsExecutionsTasksListRequest',
        response_type_name='GoogleCloudRunV2ListTasksResponse',
        supports_download=False,
    )

  class ProjectsLocationsJobsExecutionsService(base_api.BaseApiService):
    """Service class for the projects_locations_jobs_executions resource."""

    _NAME = 'projects_locations_jobs_executions'

    def __init__(self, client):
      super(RunV2.ProjectsLocationsJobsExecutionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancels an Execution.

      Args:
        request: (RunProjectsLocationsJobsExecutionsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}/executions/{executionsId}:cancel',
        http_method='POST',
        method_id='run.projects.locations.jobs.executions.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:cancel',
        request_field='googleCloudRunV2CancelExecutionRequest',
        request_type_name='RunProjectsLocationsJobsExecutionsCancelRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an Execution.

      Args:
        request: (RunProjectsLocationsJobsExecutionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}/executions/{executionsId}',
        http_method='DELETE',
        method_id='run.projects.locations.jobs.executions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'validateOnly'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='RunProjectsLocationsJobsExecutionsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def ExportStatus(self, request, global_params=None):
      r"""Read the status of an image export operation.

      Args:
        request: (RunProjectsLocationsJobsExecutionsExportStatusRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRunV2ExportStatusResponse) The response message.
      """
      config = self.GetMethodConfig('ExportStatus')
      return self._RunMethod(
          config, request, global_params=global_params)

    ExportStatus.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}/executions/{executionsId}/{executionsId1}:exportStatus',
        http_method='GET',
        method_id='run.projects.locations.jobs.executions.exportStatus',
        ordered_params=['name', 'operationId'],
        path_params=['name', 'operationId'],
        query_params=[],
        relative_path='v2/{+name}/{+operationId}:exportStatus',
        request_field='',
        request_type_name='RunProjectsLocationsJobsExecutionsExportStatusRequest',
        response_type_name='GoogleCloudRunV2ExportStatusResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about an Execution.

      Args:
        request: (RunProjectsLocationsJobsExecutionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRunV2Execution) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}/executions/{executionsId}',
        http_method='GET',
        method_id='run.projects.locations.jobs.executions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='RunProjectsLocationsJobsExecutionsGetRequest',
        response_type_name='GoogleCloudRunV2Execution',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Executions from a Job. Results are sorted by creation time, descending.

      Args:
        request: (RunProjectsLocationsJobsExecutionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRunV2ListExecutionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}/executions',
        http_method='GET',
        method_id='run.projects.locations.jobs.executions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'showDeleted'],
        relative_path='v2/{+parent}/executions',
        request_field='',
        request_type_name='RunProjectsLocationsJobsExecutionsListRequest',
        response_type_name='GoogleCloudRunV2ListExecutionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_jobs resource."""

    _NAME = 'projects_locations_jobs'

    def __init__(self, client):
      super(RunV2.ProjectsLocationsJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a Job.

      Args:
        request: (RunProjectsLocationsJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/jobs',
        http_method='POST',
        method_id='run.projects.locations.jobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['jobId', 'validateOnly'],
        relative_path='v2/{+parent}/jobs',
        request_field='googleCloudRunV2Job',
        request_type_name='RunProjectsLocationsJobsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Job.

      Args:
        request: (RunProjectsLocationsJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}',
        http_method='DELETE',
        method_id='run.projects.locations.jobs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'validateOnly'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='RunProjectsLocationsJobsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about a Job.

      Args:
        request: (RunProjectsLocationsJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRunV2Job) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}',
        http_method='GET',
        method_id='run.projects.locations.jobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='RunProjectsLocationsJobsGetRequest',
        response_type_name='GoogleCloudRunV2Job',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the IAM Access Control policy currently in effect for the given Job. This result does not include any inherited policies.

      Args:
        request: (RunProjectsLocationsJobsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}:getIamPolicy',
        http_method='GET',
        method_id='run.projects.locations.jobs.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v2/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='RunProjectsLocationsJobsGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Jobs. Results are sorted by creation time, descending.

      Args:
        request: (RunProjectsLocationsJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRunV2ListJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/jobs',
        http_method='GET',
        method_id='run.projects.locations.jobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'showDeleted'],
        relative_path='v2/{+parent}/jobs',
        request_field='',
        request_type_name='RunProjectsLocationsJobsListRequest',
        response_type_name='GoogleCloudRunV2ListJobsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a Job.

      Args:
        request: (RunProjectsLocationsJobsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}',
        http_method='PATCH',
        method_id='run.projects.locations.jobs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'validateOnly'],
        relative_path='v2/{+name}',
        request_field='googleCloudRunV2Job',
        request_type_name='RunProjectsLocationsJobsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Run(self, request, global_params=None):
      r"""Triggers creation of a new Execution of this Job.

      Args:
        request: (RunProjectsLocationsJobsRunRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Run')
      return self._RunMethod(
          config, request, global_params=global_params)

    Run.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}:run',
        http_method='POST',
        method_id='run.projects.locations.jobs.run',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:run',
        request_field='googleCloudRunV2RunJobRequest',
        request_type_name='RunProjectsLocationsJobsRunRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the IAM Access control policy for the specified Job. Overwrites any existing policy.

      Args:
        request: (RunProjectsLocationsJobsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}:setIamPolicy',
        http_method='POST',
        method_id='run.projects.locations.jobs.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='RunProjectsLocationsJobsSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified Project. There are no permissions required for making this API call.

      Args:
        request: (RunProjectsLocationsJobsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}:testIamPermissions',
        http_method='POST',
        method_id='run.projects.locations.jobs.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='RunProjectsLocationsJobsTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(RunV2.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (RunProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='run.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='RunProjectsLocationsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (RunProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='run.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='RunProjectsLocationsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (RunProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='run.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2/{+name}/operations',
        request_field='',
        request_type_name='RunProjectsLocationsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (RunProjectsLocationsOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='run.projects.locations.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:wait',
        request_field='googleLongrunningWaitOperationRequest',
        request_type_name='RunProjectsLocationsOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsServicesRevisionsService(base_api.BaseApiService):
    """Service class for the projects_locations_services_revisions resource."""

    _NAME = 'projects_locations_services_revisions'

    def __init__(self, client):
      super(RunV2.ProjectsLocationsServicesRevisionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a Revision.

      Args:
        request: (RunProjectsLocationsServicesRevisionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/revisions/{revisionsId}',
        http_method='DELETE',
        method_id='run.projects.locations.services.revisions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'validateOnly'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='RunProjectsLocationsServicesRevisionsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def ExportStatus(self, request, global_params=None):
      r"""Read the status of an image export operation.

      Args:
        request: (RunProjectsLocationsServicesRevisionsExportStatusRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRunV2ExportStatusResponse) The response message.
      """
      config = self.GetMethodConfig('ExportStatus')
      return self._RunMethod(
          config, request, global_params=global_params)

    ExportStatus.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/revisions/{revisionsId}/{revisionsId1}:exportStatus',
        http_method='GET',
        method_id='run.projects.locations.services.revisions.exportStatus',
        ordered_params=['name', 'operationId'],
        path_params=['name', 'operationId'],
        query_params=[],
        relative_path='v2/{+name}/{+operationId}:exportStatus',
        request_field='',
        request_type_name='RunProjectsLocationsServicesRevisionsExportStatusRequest',
        response_type_name='GoogleCloudRunV2ExportStatusResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about a Revision.

      Args:
        request: (RunProjectsLocationsServicesRevisionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRunV2Revision) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/revisions/{revisionsId}',
        http_method='GET',
        method_id='run.projects.locations.services.revisions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='RunProjectsLocationsServicesRevisionsGetRequest',
        response_type_name='GoogleCloudRunV2Revision',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Revisions from a given Service, or from a given location. Results are sorted by creation time, descending.

      Args:
        request: (RunProjectsLocationsServicesRevisionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRunV2ListRevisionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/services/{servicesId}/revisions',
        http_method='GET',
        method_id='run.projects.locations.services.revisions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'showDeleted'],
        relative_path='v2/{+parent}/revisions',
        request_field='',
        request_type_name='RunProjectsLocationsServicesRevisionsListRequest',
        response_type_name='GoogleCloudRunV2ListRevisionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsServicesService(base_api.BaseApiService):
    """Service class for the projects_locations_services resource."""

    _NAME = 'projects_locations_services'

    def __init__(self, client):
      super(RunV2.ProjectsLocationsServicesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Service in a given project and location.

      Args:
        request: (RunProjectsLocationsServicesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/services',
        http_method='POST',
        method_id='run.projects.locations.services.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['serviceId', 'validateOnly'],
        relative_path='v2/{+parent}/services',
        request_field='googleCloudRunV2Service',
        request_type_name='RunProjectsLocationsServicesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Service. This will cause the Service to stop serving traffic and will delete all revisions.

      Args:
        request: (RunProjectsLocationsServicesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/services/{servicesId}',
        http_method='DELETE',
        method_id='run.projects.locations.services.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'validateOnly'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='RunProjectsLocationsServicesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about a Service.

      Args:
        request: (RunProjectsLocationsServicesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRunV2Service) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/services/{servicesId}',
        http_method='GET',
        method_id='run.projects.locations.services.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='RunProjectsLocationsServicesGetRequest',
        response_type_name='GoogleCloudRunV2Service',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the IAM Access Control policy currently in effect for the given Cloud Run Service. This result does not include any inherited policies.

      Args:
        request: (RunProjectsLocationsServicesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:getIamPolicy',
        http_method='GET',
        method_id='run.projects.locations.services.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v2/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='RunProjectsLocationsServicesGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Services. Results are sorted by creation time, descending.

      Args:
        request: (RunProjectsLocationsServicesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRunV2ListServicesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/services',
        http_method='GET',
        method_id='run.projects.locations.services.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'showDeleted'],
        relative_path='v2/{+parent}/services',
        request_field='',
        request_type_name='RunProjectsLocationsServicesListRequest',
        response_type_name='GoogleCloudRunV2ListServicesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a Service.

      Args:
        request: (RunProjectsLocationsServicesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/services/{servicesId}',
        http_method='PATCH',
        method_id='run.projects.locations.services.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask', 'validateOnly'],
        relative_path='v2/{+name}',
        request_field='googleCloudRunV2Service',
        request_type_name='RunProjectsLocationsServicesPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the IAM Access control policy for the specified Service. Overwrites any existing policy.

      Args:
        request: (RunProjectsLocationsServicesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:setIamPolicy',
        http_method='POST',
        method_id='run.projects.locations.services.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='RunProjectsLocationsServicesSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified Project. There are no permissions required for making this API call.

      Args:
        request: (RunProjectsLocationsServicesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/services/{servicesId}:testIamPermissions',
        http_method='POST',
        method_id='run.projects.locations.services.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='RunProjectsLocationsServicesTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsWorkerPoolsRevisionsService(base_api.BaseApiService):
    """Service class for the projects_locations_workerPools_revisions resource."""

    _NAME = 'projects_locations_workerPools_revisions'

    def __init__(self, client):
      super(RunV2.ProjectsLocationsWorkerPoolsRevisionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a Revision.

      Args:
        request: (RunProjectsLocationsWorkerPoolsRevisionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/workerPools/{workerPoolsId}/revisions/{revisionsId}',
        http_method='DELETE',
        method_id='run.projects.locations.workerPools.revisions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'validateOnly'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='RunProjectsLocationsWorkerPoolsRevisionsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about a Revision.

      Args:
        request: (RunProjectsLocationsWorkerPoolsRevisionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRunV2Revision) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/workerPools/{workerPoolsId}/revisions/{revisionsId}',
        http_method='GET',
        method_id='run.projects.locations.workerPools.revisions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='RunProjectsLocationsWorkerPoolsRevisionsGetRequest',
        response_type_name='GoogleCloudRunV2Revision',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Revisions from a given Service, or from a given location. Results are sorted by creation time, descending.

      Args:
        request: (RunProjectsLocationsWorkerPoolsRevisionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRunV2ListRevisionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/workerPools/{workerPoolsId}/revisions',
        http_method='GET',
        method_id='run.projects.locations.workerPools.revisions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'showDeleted'],
        relative_path='v2/{+parent}/revisions',
        request_field='',
        request_type_name='RunProjectsLocationsWorkerPoolsRevisionsListRequest',
        response_type_name='GoogleCloudRunV2ListRevisionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsWorkerPoolsService(base_api.BaseApiService):
    """Service class for the projects_locations_workerPools resource."""

    _NAME = 'projects_locations_workerPools'

    def __init__(self, client):
      super(RunV2.ProjectsLocationsWorkerPoolsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new WorkerPool in a given project and location.

      Args:
        request: (RunProjectsLocationsWorkerPoolsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/workerPools',
        http_method='POST',
        method_id='run.projects.locations.workerPools.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['validateOnly', 'workerPoolId'],
        relative_path='v2/{+parent}/workerPools',
        request_field='googleCloudRunV2WorkerPool',
        request_type_name='RunProjectsLocationsWorkerPoolsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a WorkerPool.

      Args:
        request: (RunProjectsLocationsWorkerPoolsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/workerPools/{workerPoolsId}',
        http_method='DELETE',
        method_id='run.projects.locations.workerPools.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'validateOnly'],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='RunProjectsLocationsWorkerPoolsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about a WorkerPool.

      Args:
        request: (RunProjectsLocationsWorkerPoolsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRunV2WorkerPool) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/workerPools/{workerPoolsId}',
        http_method='GET',
        method_id='run.projects.locations.workerPools.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='RunProjectsLocationsWorkerPoolsGetRequest',
        response_type_name='GoogleCloudRunV2WorkerPool',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the IAM Access Control policy currently in effect for the given Cloud Run WorkerPool. This result does not include any inherited policies.

      Args:
        request: (RunProjectsLocationsWorkerPoolsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/workerPools/{workerPoolsId}:getIamPolicy',
        http_method='GET',
        method_id='run.projects.locations.workerPools.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v2/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='RunProjectsLocationsWorkerPoolsGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists WorkerPools. Results are sorted by creation time, descending.

      Args:
        request: (RunProjectsLocationsWorkerPoolsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRunV2ListWorkerPoolsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/workerPools',
        http_method='GET',
        method_id='run.projects.locations.workerPools.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'showDeleted'],
        relative_path='v2/{+parent}/workerPools',
        request_field='',
        request_type_name='RunProjectsLocationsWorkerPoolsListRequest',
        response_type_name='GoogleCloudRunV2ListWorkerPoolsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a WorkerPool.

      Args:
        request: (RunProjectsLocationsWorkerPoolsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/workerPools/{workerPoolsId}',
        http_method='PATCH',
        method_id='run.projects.locations.workerPools.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'forceNewRevision', 'updateMask', 'validateOnly'],
        relative_path='v2/{+name}',
        request_field='googleCloudRunV2WorkerPool',
        request_type_name='RunProjectsLocationsWorkerPoolsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the IAM Access control policy for the specified WorkerPool. Overwrites any existing policy.

      Args:
        request: (RunProjectsLocationsWorkerPoolsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/workerPools/{workerPoolsId}:setIamPolicy',
        http_method='POST',
        method_id='run.projects.locations.workerPools.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='RunProjectsLocationsWorkerPoolsSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified Project. There are no permissions required for making this API call.

      Args:
        request: (RunProjectsLocationsWorkerPoolsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/workerPools/{workerPoolsId}:testIamPermissions',
        http_method='POST',
        method_id='run.projects.locations.workerPools.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v2/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='RunProjectsLocationsWorkerPoolsTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(RunV2.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def ExportImage(self, request, global_params=None):
      r"""Export image for a given resource.

      Args:
        request: (RunProjectsLocationsExportImageRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRunV2ExportImageResponse) The response message.
      """
      config = self.GetMethodConfig('ExportImage')
      return self._RunMethod(
          config, request, global_params=global_params)

    ExportImage.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/{locationsId1}:exportImage',
        http_method='POST',
        method_id='run.projects.locations.exportImage',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:exportImage',
        request_field='googleCloudRunV2ExportImageRequest',
        request_type_name='RunProjectsLocationsExportImageRequest',
        response_type_name='GoogleCloudRunV2ExportImageResponse',
        supports_download=False,
    )

    def ExportImageMetadata(self, request, global_params=None):
      r"""Export image metadata for a given resource.

      Args:
        request: (RunProjectsLocationsExportImageMetadataRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRunV2Metadata) The response message.
      """
      config = self.GetMethodConfig('ExportImageMetadata')
      return self._RunMethod(
          config, request, global_params=global_params)

    ExportImageMetadata.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/{locationsId1}:exportImageMetadata',
        http_method='GET',
        method_id='run.projects.locations.exportImageMetadata',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:exportImageMetadata',
        request_field='',
        request_type_name='RunProjectsLocationsExportImageMetadataRequest',
        response_type_name='GoogleCloudRunV2Metadata',
        supports_download=False,
    )

    def ExportMetadata(self, request, global_params=None):
      r"""Export generated customer metadata for a given resource.

      Args:
        request: (RunProjectsLocationsExportMetadataRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRunV2Metadata) The response message.
      """
      config = self.GetMethodConfig('ExportMetadata')
      return self._RunMethod(
          config, request, global_params=global_params)

    ExportMetadata.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/{locationsId1}:exportMetadata',
        http_method='GET',
        method_id='run.projects.locations.exportMetadata',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:exportMetadata',
        request_field='',
        request_type_name='RunProjectsLocationsExportMetadataRequest',
        response_type_name='GoogleCloudRunV2Metadata',
        supports_download=False,
    )

    def ExportProjectMetadata(self, request, global_params=None):
      r"""Export generated customer metadata for a given project.

      Args:
        request: (RunProjectsLocationsExportProjectMetadataRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRunV2Metadata) The response message.
      """
      config = self.GetMethodConfig('ExportProjectMetadata')
      return self._RunMethod(
          config, request, global_params=global_params)

    ExportProjectMetadata.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}:exportProjectMetadata',
        http_method='GET',
        method_id='run.projects.locations.exportProjectMetadata',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:exportProjectMetadata',
        request_field='',
        request_type_name='RunProjectsLocationsExportProjectMetadataRequest',
        response_type_name='GoogleCloudRunV2Metadata',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(RunV2.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
