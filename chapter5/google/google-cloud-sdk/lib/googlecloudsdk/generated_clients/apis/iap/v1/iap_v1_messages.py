"""Generated message classes for iap version v1.

Controls access to cloud applications running on Google Cloud Platform.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'iap'


class AccessDeniedPageSettings(_messages.Message):
  r"""Custom content configuration for access denied page. IAP allows
  customers to define a custom URI to use as the error page when access is
  denied to users. If IAP prevents access to this page, the default IAP error
  page will be displayed instead.

  Fields:
    accessDeniedPageUri: The URI to be redirected to when access is denied.
    generateTroubleshootingUri: Whether to generate a troubleshooting URL on
      access denied events to this application.
    remediationTokenGenerationEnabled: Whether to generate remediation token
      on access denied events to this application.
  """

  accessDeniedPageUri = _messages.StringField(1)
  generateTroubleshootingUri = _messages.BooleanField(2)
  remediationTokenGenerationEnabled = _messages.BooleanField(3)


class AccessSettings(_messages.Message):
  r"""Access related settings for IAP protected apps.

  Enums:
    IdentitySourcesValueListEntryValuesEnum:

  Fields:
    allowedDomainsSettings: Optional. Settings to configure and enable allowed
      domains.
    corsSettings: Optional. Configuration to allow cross-origin requests via
      IAP.
    gcipSettings: Optional. GCIP claims and endpoint configurations for 3p
      identity providers.
    identitySources: Optional. Identity sources that IAP can use to
      authenticate the end user. Only one identity source can be configured.
    oauthSettings: Optional. Settings to configure IAP's OAuth behavior.
    policyDelegationSettings: Optional. Settings to allow google-internal
      teams to use IAP for apps hosted in a tenant project.
    reauthSettings: Optional. Settings to configure reauthentication policies
      in IAP.
    workforceIdentitySettings: Optional. Settings to configure the workforce
      identity federation, including workforce pools and OAuth 2.0 settings.
  """

  class IdentitySourcesValueListEntryValuesEnum(_messages.Enum):
    r"""IdentitySourcesValueListEntryValuesEnum enum type.

    Values:
      IDENTITY_SOURCE_UNSPECIFIED: IdentitySource Unspecified. When selected,
        IAP relies on which identity settings are fully configured to redirect
        the traffic to. The precedence order is WorkforceIdentitySettings >
        GcipSettings. If none is set, default to use Google identity.
      WORKFORCE_IDENTITY_FEDERATION: Use external identities set up on Google
        Cloud Workforce Identity Federation.
    """
    IDENTITY_SOURCE_UNSPECIFIED = 0
    WORKFORCE_IDENTITY_FEDERATION = 1

  allowedDomainsSettings = _messages.MessageField('AllowedDomainsSettings', 1)
  corsSettings = _messages.MessageField('CorsSettings', 2)
  gcipSettings = _messages.MessageField('GcipSettings', 3)
  identitySources = _messages.EnumField('IdentitySourcesValueListEntryValuesEnum', 4, repeated=True)
  oauthSettings = _messages.MessageField('OAuthSettings', 5)
  policyDelegationSettings = _messages.MessageField('PolicyDelegationSettings', 6)
  reauthSettings = _messages.MessageField('ReauthSettings', 7)
  workforceIdentitySettings = _messages.MessageField('WorkforceIdentitySettings', 8)


class AllowedDomainsSettings(_messages.Message):
  r"""Configuration for IAP allowed domains. Lets you to restrict access to an
  app and allow access to only the domains that you list.

  Fields:
    domains: Optional. List of trusted domains.
    enable: Optional. Configuration for customers to opt in for the feature.
  """

  domains = _messages.StringField(1, repeated=True)
  enable = _messages.BooleanField(2)


class ApplicationSettings(_messages.Message):
  r"""Wrapper over application specific settings for IAP.

  Fields:
    accessDeniedPageSettings: Optional. Customization for Access Denied page.
    attributePropagationSettings: Optional. Settings to configure attribute
      propagation.
    cookieDomain: The Domain value to set for cookies generated by IAP. This
      value is not validated by the API, but will be ignored at runtime if
      invalid.
    csmSettings: Optional. Settings to configure IAP's behavior for a service
      mesh.
  """

  accessDeniedPageSettings = _messages.MessageField('AccessDeniedPageSettings', 1)
  attributePropagationSettings = _messages.MessageField('AttributePropagationSettings', 2)
  cookieDomain = _messages.StringField(3)
  csmSettings = _messages.MessageField('CsmSettings', 4)


class AttributePropagationSettings(_messages.Message):
  r"""Configuration for propagating attributes to applications protected by
  IAP.

  Enums:
    OutputCredentialsValueListEntryValuesEnum:

  Fields:
    enable: Optional. Whether the provided attribute propagation settings
      should be evaluated on user requests. If set to true, attributes
      returned from the expression will be propagated in the set output
      credentials.
    expression: Optional. Raw string CEL expression. Must return a list of
      attributes. A maximum of 45 attributes can be selected. Expressions can
      select different attribute types from `attributes`:
      `attributes.saml_attributes`, `attributes.iap_attributes`. The following
      functions are supported: - filter `.filter(, )`: Returns a subset of ``
      where `` is true for every item. - in ` in `: Returns true if ``
      contains ``. - selectByName `.selectByName()`: Returns the attribute in
      `` with the given `` name, otherwise returns empty. - emitAs
      `.emitAs()`: Sets the `` name field to the given `` for propagation in
      selected output credentials. - strict `.strict()`: Ignores the `x-goog-
      iap-attr-` prefix for the provided `` when propagating with the `HEADER`
      output credential, such as request headers. - append `.append()` OR
      `.append()`: Appends the provided `` or `` to the end of ``. Example
      expression: `attributes.saml_attributes.filter(x, x.name in ['test']).ap
      pend(attributes.iap_attributes.selectByName('exact').emitAs('custom').st
      rict())`
    outputCredentials: Optional. Which output credentials attributes selected
      by the CEL expression should be propagated in. All attributes will be
      fully duplicated in each selected output credential.
  """

  class OutputCredentialsValueListEntryValuesEnum(_messages.Enum):
    r"""OutputCredentialsValueListEntryValuesEnum enum type.

    Values:
      OUTPUT_CREDENTIALS_UNSPECIFIED: An output credential is required.
      HEADER: Propagate attributes in the headers with "x-goog-iap-attr-"
        prefix.
      JWT: Propagate attributes in the JWT of the form: `"additional_claims":
        { "my_attribute": ["value1", "value2"] }`
      RCTOKEN: Propagate attributes in the RCToken of the form:
        `"additional_claims": { "my_attribute": ["value1", "value2"] }`
    """
    OUTPUT_CREDENTIALS_UNSPECIFIED = 0
    HEADER = 1
    JWT = 2
    RCTOKEN = 3

  enable = _messages.BooleanField(1)
  expression = _messages.StringField(2)
  outputCredentials = _messages.EnumField('OutputCredentialsValueListEntryValuesEnum', 3, repeated=True)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class Brand(_messages.Message):
  r"""OAuth brand data. NOTE: Only contains a portion of the data that
  describes a brand.

  Fields:
    applicationTitle: Application name displayed on OAuth consent screen.
    name: Output only. Identifier of the brand. NOTE: GCP project number
      achieves the same brand identification purpose as only one brand per
      project can be created.
    orgInternalOnly: Output only. Whether the brand is only intended for usage
      inside the G Suite organization only.
    supportEmail: Support email displayed on the OAuth consent screen.
  """

  applicationTitle = _messages.StringField(1)
  name = _messages.StringField(2)
  orgInternalOnly = _messages.BooleanField(3)
  supportEmail = _messages.StringField(4)


class CorsSettings(_messages.Message):
  r"""Allows customers to configure HTTP request paths that'll allow HTTP
  `OPTIONS` call to bypass authentication and authorization.

  Fields:
    allowHttpOptions: Configuration to allow HTTP `OPTIONS` calls to skip
      authentication and authorization. If undefined, IAP will not apply any
      special logic to `OPTIONS` requests.
  """

  allowHttpOptions = _messages.BooleanField(1)


class CsmSettings(_messages.Message):
  r"""Configuration for RCToken generated for service mesh workloads protected
  by IAP. RCToken are IAP generated JWTs that can be verified at the
  application. The RCToken is primarily used for service mesh deployments, and
  can be scoped to a single mesh by configuring the audience field
  accordingly.

  Fields:
    rctokenAud: Audience claim set in the generated RCToken. This value is not
      validated by IAP.
  """

  rctokenAud = _messages.StringField(1)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GcipSettings(_messages.Message):
  r"""Allows customers to configure tenant IDs for a Cloud Identity Platform
  (GCIP) instance for each application.

  Fields:
    loginPageUri: Login page URI associated with the GCIP tenants. Typically,
      all resources within the same project share the same login page, though
      it could be overridden at the sub resource level.
    tenantIds: Optional. GCIP tenant IDs that are linked to the IAP resource.
      `tenant_ids` could be a string beginning with a number character to
      indicate authenticating with GCIP tenant flow, or in the format of `_`
      to indicate authenticating with GCIP agent flow. If agent flow is used,
      `tenant_ids` should only contain one single element, while for tenant
      flow, `tenant_ids` can contain multiple elements.
  """

  loginPageUri = _messages.StringField(1)
  tenantIds = _messages.StringField(2, repeated=True)


class GetIamPolicyRequest(_messages.Message):
  r"""Request message for `GetIamPolicy` method.

  Fields:
    options: OPTIONAL: A `GetPolicyOptions` object for specifying options to
      `GetIamPolicy`.
  """

  options = _messages.MessageField('GetPolicyOptions', 1)


class GetPolicyOptions(_messages.Message):
  r"""Encapsulates settings provided to GetIamPolicy.

  Fields:
    requestedPolicyVersion: Optional. The maximum policy version that will be
      used to format the policy. Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected. Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset. The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1. To learn which resources support conditions in
      their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class IapGetIamPolicyRequest(_messages.Message):
  r"""A IapGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class IapGetIapSettingsRequest(_messages.Message):
  r"""A IapGetIapSettingsRequest object.

  Fields:
    name: Required. The resource name for which to retrieve the settings.
      Authorization: Requires the `getSettings` permission for the associated
      resource.
  """

  name = _messages.StringField(1, required=True)


class IapProjectsBrandsCreateRequest(_messages.Message):
  r"""A IapProjectsBrandsCreateRequest object.

  Fields:
    brand: A Brand resource to be passed as the request body.
    parent: Required. GCP Project number/id under which the brand is to be
      created. In the following format: projects/{project_number/id}.
  """

  brand = _messages.MessageField('Brand', 1)
  parent = _messages.StringField(2, required=True)


class IapProjectsBrandsGetRequest(_messages.Message):
  r"""A IapProjectsBrandsGetRequest object.

  Fields:
    name: Required. Name of the brand to be fetched. In the following format:
      projects/{project_number/id}/brands/{brand}.
  """

  name = _messages.StringField(1, required=True)


class IapProjectsBrandsIdentityAwareProxyClientsCreateRequest(_messages.Message):
  r"""A IapProjectsBrandsIdentityAwareProxyClientsCreateRequest object.

  Fields:
    identityAwareProxyClient: A IdentityAwareProxyClient resource to be passed
      as the request body.
    parent: Required. Path to create the client in. In the following format:
      projects/{project_number/id}/brands/{brand}. The project must belong to
      a G Suite account.
  """

  identityAwareProxyClient = _messages.MessageField('IdentityAwareProxyClient', 1)
  parent = _messages.StringField(2, required=True)


class IapProjectsBrandsIdentityAwareProxyClientsDeleteRequest(_messages.Message):
  r"""A IapProjectsBrandsIdentityAwareProxyClientsDeleteRequest object.

  Fields:
    name: Required. Name of the Identity Aware Proxy client to be deleted. In
      the following format: projects/{project_number/id}/brands/{brand}/identi
      tyAwareProxyClients/{client_id}.
  """

  name = _messages.StringField(1, required=True)


class IapProjectsBrandsIdentityAwareProxyClientsGetRequest(_messages.Message):
  r"""A IapProjectsBrandsIdentityAwareProxyClientsGetRequest object.

  Fields:
    name: Required. Name of the Identity Aware Proxy client to be fetched. In
      the following format: projects/{project_number/id}/brands/{brand}/identi
      tyAwareProxyClients/{client_id}.
  """

  name = _messages.StringField(1, required=True)


class IapProjectsBrandsIdentityAwareProxyClientsListRequest(_messages.Message):
  r"""A IapProjectsBrandsIdentityAwareProxyClientsListRequest object.

  Fields:
    pageSize: The maximum number of clients to return. The service may return
      fewer than this value. If unspecified, at most 100 clients will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: A page token, received from a previous
      `ListIdentityAwareProxyClients` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `ListIdentityAwareProxyClients` must match the call that provided the
      page token.
    parent: Required. Full brand path. In the following format:
      projects/{project_number/id}/brands/{brand}.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class IapProjectsBrandsIdentityAwareProxyClientsResetSecretRequest(_messages.Message):
  r"""A IapProjectsBrandsIdentityAwareProxyClientsResetSecretRequest object.

  Fields:
    name: Required. Name of the Identity Aware Proxy client to that will have
      its secret reset. In the following format: projects/{project_number/id}/
      brands/{brand}/identityAwareProxyClients/{client_id}.
    resetIdentityAwareProxyClientSecretRequest: A
      ResetIdentityAwareProxyClientSecretRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  resetIdentityAwareProxyClientSecretRequest = _messages.MessageField('ResetIdentityAwareProxyClientSecretRequest', 2)


class IapProjectsBrandsListRequest(_messages.Message):
  r"""A IapProjectsBrandsListRequest object.

  Fields:
    parent: Required. GCP Project number/id. In the following format:
      projects/{project_number/id}.
  """

  parent = _messages.StringField(1, required=True)


class IapProjectsIapTunnelLocationsDestGroupsCreateRequest(_messages.Message):
  r"""A IapProjectsIapTunnelLocationsDestGroupsCreateRequest object.

  Fields:
    parent: Required. Google Cloud Project ID and location. In the following
      format: `projects/{project_number/id}/iap_tunnel/locations/{location}`.
    tunnelDestGroup: A TunnelDestGroup resource to be passed as the request
      body.
    tunnelDestGroupId: Required. The ID to use for the TunnelDestGroup, which
      becomes the final component of the resource name. This value must be
      4-63 characters, and valid characters are `[a-z]-`.
  """

  parent = _messages.StringField(1, required=True)
  tunnelDestGroup = _messages.MessageField('TunnelDestGroup', 2)
  tunnelDestGroupId = _messages.StringField(3)


class IapProjectsIapTunnelLocationsDestGroupsDeleteRequest(_messages.Message):
  r"""A IapProjectsIapTunnelLocationsDestGroupsDeleteRequest object.

  Fields:
    name: Required. Name of the TunnelDestGroup to delete. In the following
      format: `projects/{project_number/id}/iap_tunnel/locations/{location}/de
      stGroups/{dest_group}`.
  """

  name = _messages.StringField(1, required=True)


class IapProjectsIapTunnelLocationsDestGroupsGetRequest(_messages.Message):
  r"""A IapProjectsIapTunnelLocationsDestGroupsGetRequest object.

  Fields:
    name: Required. Name of the TunnelDestGroup to be fetched. In the
      following format: `projects/{project_number/id}/iap_tunnel/locations/{lo
      cation}/destGroups/{dest_group}`.
  """

  name = _messages.StringField(1, required=True)


class IapProjectsIapTunnelLocationsDestGroupsListRequest(_messages.Message):
  r"""A IapProjectsIapTunnelLocationsDestGroupsListRequest object.

  Fields:
    pageSize: The maximum number of groups to return. The service might return
      fewer than this value. If unspecified, at most 100 groups are returned.
      The maximum value is 1000; values above 1000 are coerced to 1000.
    pageToken: A page token, received from a previous `ListTunnelDestGroups`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListTunnelDestGroups` must match the call
      that provided the page token.
    parent: Required. Google Cloud Project ID and location. In the following
      format: `projects/{project_number/id}/iap_tunnel/locations/{location}`.
      A `-` can be used for the location to group across all locations.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class IapProjectsIapTunnelLocationsDestGroupsPatchRequest(_messages.Message):
  r"""A IapProjectsIapTunnelLocationsDestGroupsPatchRequest object.

  Fields:
    name: Identifier. Identifier for the TunnelDestGroup. Must be unique
      within the project and contain only lower case letters (a-z) and dashes
      (-).
    tunnelDestGroup: A TunnelDestGroup resource to be passed as the request
      body.
    updateMask: A field mask that specifies which IAP settings to update. If
      omitted, then all of the settings are updated. See
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  name = _messages.StringField(1, required=True)
  tunnelDestGroup = _messages.MessageField('TunnelDestGroup', 2)
  updateMask = _messages.StringField(3)


class IapSetIamPolicyRequest(_messages.Message):
  r"""A IapSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class IapSettings(_messages.Message):
  r"""The IAP configurable settings.

  Fields:
    accessSettings: Optional. Top level wrapper for all access related setting
      in IAP
    applicationSettings: Optional. Top level wrapper for all application
      related settings in IAP
    name: Required. The resource name of the IAP protected resource.
  """

  accessSettings = _messages.MessageField('AccessSettings', 1)
  applicationSettings = _messages.MessageField('ApplicationSettings', 2)
  name = _messages.StringField(3)


class IapTestIamPermissionsRequest(_messages.Message):
  r"""A IapTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class IapUpdateIapSettingsRequest(_messages.Message):
  r"""A IapUpdateIapSettingsRequest object.

  Fields:
    iapSettings: A IapSettings resource to be passed as the request body.
    name: Required. The resource name of the IAP protected resource.
    updateMask: The field mask specifying which IAP settings should be
      updated. If omitted, then all of the settings are updated. See
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask. Note: All IAP reauth
      settings must always be set together, using the field mask:
      `iapSettings.accessSettings.reauthSettings`.
  """

  iapSettings = _messages.MessageField('IapSettings', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class IapValidateAttributeExpressionRequest(_messages.Message):
  r"""A IapValidateAttributeExpressionRequest object.

  Fields:
    expression: Required. User input string expression. Should be of the form
      `attributes.saml_attributes.filter(attribute, attribute.name in
      ['{attribute_name}', '{attribute_name}'])`
    name: Required. The resource name of the IAP protected resource.
  """

  expression = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class IdentityAwareProxyClient(_messages.Message):
  r"""Contains the data that describes an Identity Aware Proxy owned client.

  Fields:
    displayName: Human-friendly name given to the OAuth client.
    name: Output only. Unique identifier of the OAuth client.
    secret: Output only. Client secret of the OAuth client.
  """

  displayName = _messages.StringField(1)
  name = _messages.StringField(2)
  secret = _messages.StringField(3)


class ListBrandsResponse(_messages.Message):
  r"""Response message for ListBrands.

  Fields:
    brands: Brands existing in the project.
  """

  brands = _messages.MessageField('Brand', 1, repeated=True)


class ListIdentityAwareProxyClientsResponse(_messages.Message):
  r"""Response message for ListIdentityAwareProxyClients.

  Fields:
    identityAwareProxyClients: Clients existing in the brand.
    nextPageToken: A token, which can be send as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  identityAwareProxyClients = _messages.MessageField('IdentityAwareProxyClient', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListTunnelDestGroupsResponse(_messages.Message):
  r"""The response from ListTunnelDestGroups.

  Fields:
    nextPageToken: A token that you can send as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    tunnelDestGroups: TunnelDestGroup existing in the project.
  """

  nextPageToken = _messages.StringField(1)
  tunnelDestGroups = _messages.MessageField('TunnelDestGroup', 2, repeated=True)


class NextStateOfTags(_messages.Message):
  r"""Used for calculating the next state of tags on the resource being passed
  for the CheckCustomConstraints RPC call. The detail evaluation of each field
  is described in go/op-create-update-time-tags and go/tags-in-orgpolicy-
  requests.

  Fields:
    tagsFullState: A TagsFullState attribute.
    tagsFullStateForChildResource: A TagsFullStateForChildResource attribute.
    tagsPartialState: A TagsPartialState attribute.
  """

  tagsFullState = _messages.MessageField('TagsFullState', 1)
  tagsFullStateForChildResource = _messages.MessageField('TagsFullStateForChildResource', 2)
  tagsPartialState = _messages.MessageField('TagsPartialState', 3)


class OAuth2(_messages.Message):
  r"""The OAuth 2.0 Settings

  Fields:
    clientId: The OAuth 2.0 client ID registered in the workforce identity
      federation OAuth 2.0 Server.
    clientSecret: Input only. The OAuth 2.0 client secret created while
      registering the client ID.
    clientSecretSha256: Output only. SHA256 hash value for the client secret.
      This field is returned by IAP when the settings are retrieved.
  """

  clientId = _messages.StringField(1)
  clientSecret = _messages.StringField(2)
  clientSecretSha256 = _messages.StringField(3)


class OAuthSettings(_messages.Message):
  r"""Configuration for OAuth login&consent flow behavior as well as for OAuth
  Credentials.

  Fields:
    loginHint: Domain hint to send as hd=? parameter in OAuth request flow.
      Enables redirect to primary IDP by skipping Google's login screen.
      https://developers.google.com/identity/protocols/OpenIDConnect#hd-param
      Note: IAP does not verify that the id token's hd claim matches this
      value since access behavior is managed by IAM policies.
    programmaticClients: Optional. List of client ids allowed to use IAP
      programmatically.
  """

  loginHint = _messages.StringField(1)
  programmaticClients = _messages.StringField(2, repeated=True)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  bindings = _messages.MessageField('Binding', 1, repeated=True)
  etag = _messages.BytesField(2)
  version = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class PolicyDelegationSettings(_messages.Message):
  r"""PolicyDelegationConfig allows google-internal teams to use IAP for apps
  hosted in a tenant project. Using these settings, the app can delegate
  permission check to happen against the linked customer project. This is only
  ever supposed to be used by google internal teams, hence the restriction on
  the proto.

  Fields:
    iamPermission: Permission to check in IAM.
    iamServiceName: The DNS name of the service (e.g.
      "resourcemanager.googleapis.com"). This should be the domain name part
      of the full resource names (see https://aip.dev/122#full-resource-
      names), which is usually the same as IamServiceSpec.service of the
      service where the resource type is defined.
    policyName: Policy name to be checked
    resource: IAM resource to check permission on
  """

  iamPermission = _messages.StringField(1)
  iamServiceName = _messages.StringField(2)
  policyName = _messages.MessageField('PolicyName', 3)
  resource = _messages.MessageField('Resource', 4)


class PolicyName(_messages.Message):
  r"""An internal name for an IAM policy, based on the resource to which the
  policy applies. Not to be confused with a resource's external full resource
  name. For more information on this distinction, see go/iam-full-resource-
  names.

  Fields:
    id: Identifies an instance of the type. ID format varies by type. The ID
      format is defined in the IAM .service file that defines the type, either
      in path_mapping or in a comment.
    region: For Cloud IAM: The location of the Policy. Must be empty or
      "global" for Policies owned by global IAM. Must name a region from
      prodspec/cloud-iam-cloudspec for Regional IAM Policies, see go/iam-
      faq#where-is-iam-currently-deployed. For Local IAM: This field should be
      set to "local".
    type: Resource type. Types are defined in IAM's .service files. Valid
      values for type might be 'storage_buckets', 'compute_instances',
      'resourcemanager_customers', 'billing_accounts', etc.
  """

  id = _messages.StringField(1)
  region = _messages.StringField(2)
  type = _messages.StringField(3)


class ReauthSettings(_messages.Message):
  r"""Configuration for IAP reauthentication policies.

  Enums:
    MethodValueValuesEnum: Optional. Reauth method requested.
    PolicyTypeValueValuesEnum: Optional. How IAP determines the effective
      policy in cases of hierarchical policies. Policies are merged from
      higher in the hierarchy to lower in the hierarchy.

  Fields:
    maxAge: Optional. Reauth session lifetime, how long before a user has to
      reauthenticate again.
    method: Optional. Reauth method requested.
    policyType: Optional. How IAP determines the effective policy in cases of
      hierarchical policies. Policies are merged from higher in the hierarchy
      to lower in the hierarchy.
  """

  class MethodValueValuesEnum(_messages.Enum):
    r"""Optional. Reauth method requested.

    Values:
      METHOD_UNSPECIFIED: Reauthentication disabled.
      LOGIN: Prompts the user to log in again.
      PASSWORD: <no description>
      SECURE_KEY: User must use their secure key 2nd factor device.
      ENROLLED_SECOND_FACTORS: User can use any enabled 2nd factor.
    """
    METHOD_UNSPECIFIED = 0
    LOGIN = 1
    PASSWORD = 2
    SECURE_KEY = 3
    ENROLLED_SECOND_FACTORS = 4

  class PolicyTypeValueValuesEnum(_messages.Enum):
    r"""Optional. How IAP determines the effective policy in cases of
    hierarchical policies. Policies are merged from higher in the hierarchy to
    lower in the hierarchy.

    Values:
      POLICY_TYPE_UNSPECIFIED: Default value. This value is unused.
      MINIMUM: This policy acts as a minimum to other policies, lower in the
        hierarchy. Effective policy may only be the same or stricter.
      DEFAULT: This policy acts as a default if no other reauth policy is set.
    """
    POLICY_TYPE_UNSPECIFIED = 0
    MINIMUM = 1
    DEFAULT = 2

  maxAge = _messages.StringField(1)
  method = _messages.EnumField('MethodValueValuesEnum', 2)
  policyType = _messages.EnumField('PolicyTypeValueValuesEnum', 3)


class ResetIdentityAwareProxyClientSecretRequest(_messages.Message):
  r"""The request sent to ResetIdentityAwareProxyClientSecret."""


class Resource(_messages.Message):
  r"""A Resource object.

  Messages:
    ExpectedNextStateValue: The proto or JSON formatted expected next state of
      the resource, wrapped in a google.protobuf.Any proto, against which the
      policy rules are evaluated. Services not integrated with custom org
      policy can omit this field. Services integrated with custom org policy
      must populate this field for all requests where the API call changes the
      state of the resource. Custom org policy backend uses these attributes
      to enforce custom org policies. For create operations, GCP service is
      expected to pass resource from customer request as is. For update/patch
      operations, GCP service is expected to compute the next state with the
      patch provided by the user. See go/federated-custom-org-policy-
      integration-guide for additional details.
    LabelsValue: The service defined labels of the resource on which the
      conditions will be evaluated. The semantics - including the key names -
      are vague to IAM. If the effective condition has a reference to a
      `resource.labels[foo]` construct, IAM consults with this map to retrieve
      the values associated with `foo` key for Conditions evaluation. If the
      provided key is not found in the labels map, the condition would
      evaluate to false. This field is in limited use. If your intended use
      case is not expected to express resource.labels attribute in IAM
      Conditions, leave this field empty. Before planning on using this
      attribute please: * Read go/iam-conditions-labels-comm and ensure your
      service can meet the data availability and management requirements. *
      Talk to iam-conditions-eng@ about your use case.

  Fields:
    expectedNextState: The proto or JSON formatted expected next state of the
      resource, wrapped in a google.protobuf.Any proto, against which the
      policy rules are evaluated. Services not integrated with custom org
      policy can omit this field. Services integrated with custom org policy
      must populate this field for all requests where the API call changes the
      state of the resource. Custom org policy backend uses these attributes
      to enforce custom org policies. For create operations, GCP service is
      expected to pass resource from customer request as is. For update/patch
      operations, GCP service is expected to compute the next state with the
      patch provided by the user. See go/federated-custom-org-policy-
      integration-guide for additional details.
    labels: The service defined labels of the resource on which the conditions
      will be evaluated. The semantics - including the key names - are vague
      to IAM. If the effective condition has a reference to a
      `resource.labels[foo]` construct, IAM consults with this map to retrieve
      the values associated with `foo` key for Conditions evaluation. If the
      provided key is not found in the labels map, the condition would
      evaluate to false. This field is in limited use. If your intended use
      case is not expected to express resource.labels attribute in IAM
      Conditions, leave this field empty. Before planning on using this
      attribute please: * Read go/iam-conditions-labels-comm and ensure your
      service can meet the data availability and management requirements. *
      Talk to iam-conditions-eng@ about your use case.
    locations: The locations of the resource. This field is used to determine
      whether the request is compliant with Trust Boundaries. Usage: - If
      unset or empty, the location of authorization is used as the target
      location. - For global resources: use a single value of "global". - For
      regional/multi-regional resources: use name of the GCP region(s) where
      the resource exists (e.g., ["us-east1", "us-west1"]). For multi-regional
      resources specify the name of each GCP region in the resource's multi-
      region. NOTE: Only GCP cloud region names are supported - go/cloud-
      region-names.
    name: The **relative** name of the resource, which is the URI path of the
      resource without the leading "/". See
      https://cloud.google.com/iam/docs/conditions-resource-
      attributes#resource-name for examples used by other GCP Services. This
      field is **required** for services integrated with resource-attribute-
      based IAM conditions and/or CustomOrgPolicy. This field requires special
      handling for parents-only permissions such as `create` and `list`. See
      the document linked below for further details. See go/iam-conditions-
      sig-g3#populate-resource-attributes for specific details on populating
      this field.
    nextStateOfTags: Used for calculating the next state of tags on the
      resource being passed for Custom Org Policy enforcement. NOTE: Only one
      of the tags representations (i.e. numeric or namespaced) should be
      populated. The input tags will be converted to the same representation
      before the calculation. This behavior intentionally may differ from
      other tags related fields in CheckPolicy request, which may require both
      formats to be passed in. IMPORTANT: If tags are unchanged, this field
      should not be set.
    service: The name of the service this resource belongs to. It is
      configured using the official_service_name of the Service as defined in
      service configurations under //configs/cloud/resourcetypes. For example,
      the official_service_name of cloud resource manager service is set as
      'cloudresourcemanager.googleapis.com' according to
      //configs/cloud/resourcetypes/google/cloud/resourcemanager/prod.yaml
      This field is **required** for services integrated with resource-
      attribute-based IAM conditions and/or CustomOrgPolicy. This field
      requires special handling for parents-only permissions such as `create`
      and `list`. See the document linked below for further details. See
      go/iam-conditions-sig-g3#populate-resource-attributes for specific
      details on populating this field.
    type: The public resource type name of the resource. It is configured
      using the official_name of the ResourceType as defined in service
      configurations under //configs/cloud/resourcetypes. For example, the
      official_name for GCP projects is set as
      'cloudresourcemanager.googleapis.com/Project' according to
      //configs/cloud/resourcetypes/google/cloud/resourcemanager/prod.yaml
      This field is **required** for services integrated with resource-
      attribute-based IAM conditions and/or CustomOrgPolicy. This field
      requires special handling for parents-only permissions such as `create`
      and `list`. See the document linked below for further details. See
      go/iam-conditions-sig-g3#populate-resource-attributes for specific
      details on populating this field.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExpectedNextStateValue(_messages.Message):
    r"""The proto or JSON formatted expected next state of the resource,
    wrapped in a google.protobuf.Any proto, against which the policy rules are
    evaluated. Services not integrated with custom org policy can omit this
    field. Services integrated with custom org policy must populate this field
    for all requests where the API call changes the state of the resource.
    Custom org policy backend uses these attributes to enforce custom org
    policies. For create operations, GCP service is expected to pass resource
    from customer request as is. For update/patch operations, GCP service is
    expected to compute the next state with the patch provided by the user.
    See go/federated-custom-org-policy-integration-guide for additional
    details.

    Messages:
      AdditionalProperty: An additional property for a ExpectedNextStateValue
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExpectedNextStateValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The service defined labels of the resource on which the conditions
    will be evaluated. The semantics - including the key names - are vague to
    IAM. If the effective condition has a reference to a
    `resource.labels[foo]` construct, IAM consults with this map to retrieve
    the values associated with `foo` key for Conditions evaluation. If the
    provided key is not found in the labels map, the condition would evaluate
    to false. This field is in limited use. If your intended use case is not
    expected to express resource.labels attribute in IAM Conditions, leave
    this field empty. Before planning on using this attribute please: * Read
    go/iam-conditions-labels-comm and ensure your service can meet the data
    availability and management requirements. * Talk to iam-conditions-eng@
    about your use case.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  expectedNextState = _messages.MessageField('ExpectedNextStateValue', 1)
  labels = _messages.MessageField('LabelsValue', 2)
  locations = _messages.StringField(3, repeated=True)
  name = _messages.StringField(4)
  nextStateOfTags = _messages.MessageField('NextStateOfTags', 5)
  service = _messages.StringField(6)
  type = _messages.StringField(7)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
  """

  policy = _messages.MessageField('Policy', 1)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class TagsFullState(_messages.Message):
  r"""A TagsFullState object.

  Messages:
    TagsValue: If TagsFullState is initialized, the values in this field fully
      represent all the tags in the next state (the current tag values are not
      used). If tags.size() == 0, the next state of tags would be no tags for
      evaluation purposes. Only one type of tags reference (numeric or
      namespace) is required to be passed.

  Fields:
    tags: If TagsFullState is initialized, the values in this field fully
      represent all the tags in the next state (the current tag values are not
      used). If tags.size() == 0, the next state of tags would be no tags for
      evaluation purposes. Only one type of tags reference (numeric or
      namespace) is required to be passed.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""If TagsFullState is initialized, the values in this field fully
    represent all the tags in the next state (the current tag values are not
    used). If tags.size() == 0, the next state of tags would be no tags for
    evaluation purposes. Only one type of tags reference (numeric or
    namespace) is required to be passed.

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  tags = _messages.MessageField('TagsValue', 1)


class TagsFullStateForChildResource(_messages.Message):
  r"""A TagsFullStateForChildResource object.

  Messages:
    TagsValue: If TagsFullStateForChildResource is initialized, the values in
      this field represent all the tags in the next state for the child
      resource. Only one type of tags reference (numeric or namespace) is
      required to be passed. IMPORTANT: This field should only be used when
      the target resource IAM policy name is UNKNOWN and the resource's parent
      IAM policy name is being passed in the request.

  Fields:
    tags: If TagsFullStateForChildResource is initialized, the values in this
      field represent all the tags in the next state for the child resource.
      Only one type of tags reference (numeric or namespace) is required to be
      passed. IMPORTANT: This field should only be used when the target
      resource IAM policy name is UNKNOWN and the resource's parent IAM policy
      name is being passed in the request.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""If TagsFullStateForChildResource is initialized, the values in this
    field represent all the tags in the next state for the child resource.
    Only one type of tags reference (numeric or namespace) is required to be
    passed. IMPORTANT: This field should only be used when the target resource
    IAM policy name is UNKNOWN and the resource's parent IAM policy name is
    being passed in the request.

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  tags = _messages.MessageField('TagsValue', 1)


class TagsPartialState(_messages.Message):
  r"""A TagsPartialState object.

  Messages:
    TagsToUpsertValue: Tags that'll be updated or added to the current state
      of tags for evaluation purposes. If a key exists in both
      "tags_to_upsert" and "tag_keys_to_remove", the one in
      "tag_keys_to_remove" is ignored. Only one type of tags reference
      (numeric or namespace) is required to be passed.

  Fields:
    tagKeysToRemove: Keys of the tags that should be removed for evaluation
      purposes. IMPORTANT: Currently only numeric references are supported.
      Once support for namespace references is added, both the tag references
      (numeric and namespace) will be removed.
    tagsToUpsert: Tags that'll be updated or added to the current state of
      tags for evaluation purposes. If a key exists in both "tags_to_upsert"
      and "tag_keys_to_remove", the one in "tag_keys_to_remove" is ignored.
      Only one type of tags reference (numeric or namespace) is required to be
      passed.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsToUpsertValue(_messages.Message):
    r"""Tags that'll be updated or added to the current state of tags for
    evaluation purposes. If a key exists in both "tags_to_upsert" and
    "tag_keys_to_remove", the one in "tag_keys_to_remove" is ignored. Only one
    type of tags reference (numeric or namespace) is required to be passed.

    Messages:
      AdditionalProperty: An additional property for a TagsToUpsertValue
        object.

    Fields:
      additionalProperties: Additional properties of type TagsToUpsertValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsToUpsertValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  tagKeysToRemove = _messages.StringField(1, repeated=True)
  tagsToUpsert = _messages.MessageField('TagsToUpsertValue', 2)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TunnelDestGroup(_messages.Message):
  r"""A TunnelDestGroup.

  Fields:
    cidrs: Optional. Unordered list. List of CIDRs that this group applies to.
    fqdns: Optional. Unordered list. List of FQDNs that this group applies to.
    name: Identifier. Identifier for the TunnelDestGroup. Must be unique
      within the project and contain only lower case letters (a-z) and dashes
      (-).
  """

  cidrs = _messages.StringField(1, repeated=True)
  fqdns = _messages.StringField(2, repeated=True)
  name = _messages.StringField(3)


class ValidateIapAttributeExpressionResponse(_messages.Message):
  r"""IAP Expression Linter endpoint returns empty response body."""


class WorkforceIdentitySettings(_messages.Message):
  r"""WorkforceIdentitySettings allows customers to configure workforce pools
  and OAuth 2.0 settings to gate their applications using a third-party IdP
  with access control.

  Fields:
    oauth2: OAuth 2.0 settings for IAP to perform OIDC flow with workforce
      identity federation services.
    workforcePools: The workforce pool resources. Only one workforce pool is
      accepted.
  """

  oauth2 = _messages.MessageField('OAuth2', 1)
  workforcePools = _messages.StringField(2, repeated=True)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
