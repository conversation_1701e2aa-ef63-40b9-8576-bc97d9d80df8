"""Generated message classes for websecurityscanner version v1beta.

Scans your Compute and App Engine apps for common web vulnerabilities.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding


package = 'websecurityscanner'


class Authentication(_messages.Message):
  r"""Scan authentication configuration.

  Fields:
    customAccount: Authentication using a custom account.
    googleAccount: Authentication using a Google account.
    iapCredential: Authentication using Identity-Aware-Proxy (IAP).
  """

  customAccount = _messages.MessageField('CustomAccount', 1)
  googleAccount = _messages.MessageField('GoogleAccount', 2)
  iapCredential = _messages.MessageField('IapCredential', 3)


class CrawledUrl(_messages.Message):
  r"""A CrawledUrl resource represents a URL that was crawled during a
  ScanRun. Web Security Scanner Service crawls the web applications, following
  all links within the scope of sites, to find the URLs to test against.

  Fields:
    body: The body of the request that was used to visit the URL.
    httpMethod: The http method of the request that was used to visit the URL,
      in uppercase.
    url: The URL that was crawled.
  """

  body = _messages.StringField(1)
  httpMethod = _messages.StringField(2)
  url = _messages.StringField(3)


class CustomAccount(_messages.Message):
  r"""Describes authentication configuration that uses a custom account.

  Fields:
    loginUrl: Required. The login form URL of the website.
    password: Required. Input only. The password of the custom account. The
      credential is stored encrypted and not returned in any response nor
      included in audit logs.
    username: Required. The user name of the custom account.
  """

  loginUrl = _messages.StringField(1)
  password = _messages.StringField(2)
  username = _messages.StringField(3)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Finding(_messages.Message):
  r"""A Finding resource represents a vulnerability instance identified during
  a ScanRun.

  Enums:
    SeverityValueValuesEnum: The severity level of the reported vulnerability.

  Fields:
    body: The body of the request that triggered the vulnerability.
    description: The description of the vulnerability.
    finalUrl: The URL where the browser lands when the vulnerability is
      detected.
    findingType: The type of the Finding. Detailed and up-to-date information
      on findings can be found here: https://cloud.google.com/security-
      command-center/docs/how-to-remediate-web-security-scanner
    form: An addon containing information reported for a vulnerability with an
      HTML form, if any.
    frameUrl: If the vulnerability was originated from nested IFrame, the
      immediate parent IFrame is reported.
    fuzzedUrl: The URL produced by the server-side fuzzer and used in the
      request that triggered the vulnerability.
    httpMethod: The http method of the request that triggered the
      vulnerability, in uppercase.
    name: The resource name of the Finding. The name follows the format of 'pr
      ojects/{projectId}/scanConfigs/{scanConfigId}/scanruns/{scanRunId}/findi
      ngs/{findingId}'. The finding IDs are generated by the system.
    outdatedLibrary: An addon containing information about outdated libraries.
    reproductionUrl: The URL containing human-readable payload that user can
      leverage to reproduce the vulnerability.
    severity: The severity level of the reported vulnerability.
    trackingId: The tracking ID uniquely identifies a vulnerability instance
      across multiple ScanRuns.
    violatingResource: An addon containing detailed information regarding any
      resource causing the vulnerability such as JavaScript sources, image,
      audio files, etc.
    vulnerableHeaders: An addon containing information about vulnerable or
      missing HTTP headers.
    vulnerableParameters: An addon containing information about request
      parameters which were found to be vulnerable.
    xss: An addon containing information reported for an XSS, if any.
    xxe: An addon containing information reported for an XXE, if any.
  """

  class SeverityValueValuesEnum(_messages.Enum):
    r"""The severity level of the reported vulnerability.

    Values:
      SEVERITY_UNSPECIFIED: No severity specified. The default value.
      CRITICAL: Critical severity.
      HIGH: High severity.
      MEDIUM: Medium severity.
      LOW: Low severity.
    """
    SEVERITY_UNSPECIFIED = 0
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4

  body = _messages.StringField(1)
  description = _messages.StringField(2)
  finalUrl = _messages.StringField(3)
  findingType = _messages.StringField(4)
  form = _messages.MessageField('Form', 5)
  frameUrl = _messages.StringField(6)
  fuzzedUrl = _messages.StringField(7)
  httpMethod = _messages.StringField(8)
  name = _messages.StringField(9)
  outdatedLibrary = _messages.MessageField('OutdatedLibrary', 10)
  reproductionUrl = _messages.StringField(11)
  severity = _messages.EnumField('SeverityValueValuesEnum', 12)
  trackingId = _messages.StringField(13)
  violatingResource = _messages.MessageField('ViolatingResource', 14)
  vulnerableHeaders = _messages.MessageField('VulnerableHeaders', 15)
  vulnerableParameters = _messages.MessageField('VulnerableParameters', 16)
  xss = _messages.MessageField('Xss', 17)
  xxe = _messages.MessageField('Xxe', 18)


class FindingTypeStats(_messages.Message):
  r"""A FindingTypeStats resource represents stats regarding a specific
  FindingType of Findings under a given ScanRun.

  Fields:
    findingCount: The count of findings belonging to this finding type.
    findingType: The finding type associated with the stats.
  """

  findingCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  findingType = _messages.StringField(2)


class Form(_messages.Message):
  r"""! Information about a vulnerability with an HTML.

  Fields:
    actionUri: ! The URI where to send the form when it's submitted.
    fields: ! The names of form fields related to the vulnerability.
  """

  actionUri = _messages.StringField(1)
  fields = _messages.StringField(2, repeated=True)


class GoogleAccount(_messages.Message):
  r"""Describes authentication configuration that uses a Google account.

  Fields:
    password: Required. Input only. The password of the Google account. The
      credential is stored encrypted and not returned in any response nor
      included in audit logs.
    username: Required. The user name of the Google account.
  """

  password = _messages.StringField(1)
  username = _messages.StringField(2)


class Header(_messages.Message):
  r"""Describes a HTTP Header.

  Fields:
    name: Header name.
    value: Header value.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class IapCredential(_messages.Message):
  r"""Describes authentication configuration for Identity-Aware-Proxy (IAP).

  Fields:
    iapTestServiceAccountInfo: Authentication configuration when Web-Security-
      Scanner service account is added in Identity-Aware-Proxy (IAP) access
      policies.
  """

  iapTestServiceAccountInfo = _messages.MessageField('IapTestServiceAccountInfo', 1)


class IapTestServiceAccountInfo(_messages.Message):
  r"""Describes authentication configuration when Web-Security-Scanner service
  account is added in Identity-Aware-Proxy (IAP) access policies.

  Fields:
    targetAudienceClientId: Required. Describes OAuth2 Client ID of resources
      protected by Identity-Aware-Proxy(IAP).
  """

  targetAudienceClientId = _messages.StringField(1)


class ListCrawledUrlsResponse(_messages.Message):
  r"""Response for the `ListCrawledUrls` method.

  Fields:
    crawledUrls: The list of CrawledUrls returned.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  crawledUrls = _messages.MessageField('CrawledUrl', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListFindingTypeStatsResponse(_messages.Message):
  r"""Response for the `ListFindingTypeStats` method.

  Fields:
    findingTypeStats: The list of FindingTypeStats returned.
  """

  findingTypeStats = _messages.MessageField('FindingTypeStats', 1, repeated=True)


class ListFindingsResponse(_messages.Message):
  r"""Response for the `ListFindings` method.

  Fields:
    findings: The list of Findings returned.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  findings = _messages.MessageField('Finding', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListScanConfigsResponse(_messages.Message):
  r"""Response for the `ListScanConfigs` method.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    scanConfigs: The list of ScanConfigs returned.
  """

  nextPageToken = _messages.StringField(1)
  scanConfigs = _messages.MessageField('ScanConfig', 2, repeated=True)


class ListScanRunsResponse(_messages.Message):
  r"""Response for the `ListScanRuns` method.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    scanRuns: The list of ScanRuns returned.
  """

  nextPageToken = _messages.StringField(1)
  scanRuns = _messages.MessageField('ScanRun', 2, repeated=True)


class OutdatedLibrary(_messages.Message):
  r"""Information reported for an outdated library.

  Fields:
    learnMoreUrls: URLs to learn more information about the vulnerabilities in
      the library.
    libraryName: The name of the outdated library.
    version: The version number.
  """

  learnMoreUrls = _messages.StringField(1, repeated=True)
  libraryName = _messages.StringField(2)
  version = _messages.StringField(3)


class ScanConfig(_messages.Message):
  r"""A ScanConfig resource contains the configurations to launch a scan.

  Enums:
    ExportToSecurityCommandCenterValueValuesEnum: Controls export of scan
      configurations and results to Security Command Center.
    RiskLevelValueValuesEnum: The risk level selected for the scan
    TargetPlatformsValueListEntryValuesEnum:
    UserAgentValueValuesEnum: The user agent used during scanning.

  Fields:
    authentication: The authentication configuration. If specified, service
      will use the authentication configuration during scanning.
    blacklistPatterns: The excluded URL patterns as described in
      https://cloud.google.com/security-command-center/docs/how-to-use-web-
      security-scanner#excluding_urls
    displayName: Required. The user provided display name of the ScanConfig.
    exportToSecurityCommandCenter: Controls export of scan configurations and
      results to Security Command Center.
    ignoreHttpStatusErrors: Whether to keep scanning even if most requests
      return HTTP error codes.
    latestRun: Latest ScanRun if available.
    managedScan: Whether the scan config is managed by Web Security Scanner,
      output only.
    maxQps: The maximum QPS during scanning. A valid value ranges from 5 to 20
      inclusively. If the field is unspecified or its value is set 0, server
      will default to 15. Other values outside of [5, 20] range will be
      rejected with INVALID_ARGUMENT error.
    name: The resource name of the ScanConfig. The name follows the format of
      'projects/{projectId}/scanConfigs/{scanConfigId}'. The ScanConfig IDs
      are generated by the system.
    riskLevel: The risk level selected for the scan
    schedule: The schedule of the ScanConfig.
    startingUrls: Required. The starting URLs from which the scanner finds
      site pages.
    staticIpScan: Whether the scan configuration has enabled static IP address
      scan feature. If enabled, the scanner will access applications from
      static IP addresses.
    targetPlatforms: Set of Google Cloud platforms targeted by the scan. If
      empty, APP_ENGINE will be used as a default.
    userAgent: The user agent used during scanning.
  """

  class ExportToSecurityCommandCenterValueValuesEnum(_messages.Enum):
    r"""Controls export of scan configurations and results to Security Command
    Center.

    Values:
      EXPORT_TO_SECURITY_COMMAND_CENTER_UNSPECIFIED: Use default, which is
        ENABLED.
      ENABLED: Export results of this scan to Security Command Center.
      DISABLED: Do not export results of this scan to Security Command Center.
    """
    EXPORT_TO_SECURITY_COMMAND_CENTER_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  class RiskLevelValueValuesEnum(_messages.Enum):
    r"""The risk level selected for the scan

    Values:
      RISK_LEVEL_UNSPECIFIED: Use default, which is NORMAL.
      NORMAL: Normal scanning (Recommended)
      LOW: Lower impact scanning
    """
    RISK_LEVEL_UNSPECIFIED = 0
    NORMAL = 1
    LOW = 2

  class TargetPlatformsValueListEntryValuesEnum(_messages.Enum):
    r"""TargetPlatformsValueListEntryValuesEnum enum type.

    Values:
      TARGET_PLATFORM_UNSPECIFIED: The target platform is unknown. Requests
        with this enum value will be rejected with INVALID_ARGUMENT error.
      APP_ENGINE: Google App Engine service.
      COMPUTE: Google Compute Engine service.
      CLOUD_RUN: Google Cloud Run service.
      CLOUD_FUNCTIONS: Google Cloud Function service.
    """
    TARGET_PLATFORM_UNSPECIFIED = 0
    APP_ENGINE = 1
    COMPUTE = 2
    CLOUD_RUN = 3
    CLOUD_FUNCTIONS = 4

  class UserAgentValueValuesEnum(_messages.Enum):
    r"""The user agent used during scanning.

    Values:
      USER_AGENT_UNSPECIFIED: The user agent is unknown. Service will default
        to CHROME_LINUX.
      CHROME_LINUX: Chrome on Linux. This is the service default if
        unspecified.
      CHROME_ANDROID: Chrome on Android.
      SAFARI_IPHONE: Safari on IPhone.
    """
    USER_AGENT_UNSPECIFIED = 0
    CHROME_LINUX = 1
    CHROME_ANDROID = 2
    SAFARI_IPHONE = 3

  authentication = _messages.MessageField('Authentication', 1)
  blacklistPatterns = _messages.StringField(2, repeated=True)
  displayName = _messages.StringField(3)
  exportToSecurityCommandCenter = _messages.EnumField('ExportToSecurityCommandCenterValueValuesEnum', 4)
  ignoreHttpStatusErrors = _messages.BooleanField(5)
  latestRun = _messages.MessageField('ScanRun', 6)
  managedScan = _messages.BooleanField(7)
  maxQps = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  name = _messages.StringField(9)
  riskLevel = _messages.EnumField('RiskLevelValueValuesEnum', 10)
  schedule = _messages.MessageField('Schedule', 11)
  startingUrls = _messages.StringField(12, repeated=True)
  staticIpScan = _messages.BooleanField(13)
  targetPlatforms = _messages.EnumField('TargetPlatformsValueListEntryValuesEnum', 14, repeated=True)
  userAgent = _messages.EnumField('UserAgentValueValuesEnum', 15)


class ScanConfigError(_messages.Message):
  r"""Defines a custom error message used by CreateScanConfig and
  UpdateScanConfig APIs when scan configuration validation fails. It is also
  reported as part of a ScanRunErrorTrace message if scan validation fails due
  to a scan configuration error.

  Enums:
    CodeValueValuesEnum: Indicates the reason code for a configuration
      failure.

  Fields:
    code: Indicates the reason code for a configuration failure.
    fieldName: Indicates the full name of the ScanConfig field that triggers
      this error, for example "scan_config.max_qps". This field is provided
      for troubleshooting purposes only and its actual value can change in the
      future.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Indicates the reason code for a configuration failure.

    Values:
      CODE_UNSPECIFIED: There is no error.
      OK: There is no error.
      INTERNAL_ERROR: Indicates an internal server error. Please DO NOT USE
        THIS ERROR CODE unless the root cause is truly unknown.
      APPENGINE_API_BACKEND_ERROR: One of the seed URLs is an App Engine URL
        but we cannot validate the scan settings due to an App Engine API
        backend error.
      APPENGINE_API_NOT_ACCESSIBLE: One of the seed URLs is an App Engine URL
        but we cannot access the App Engine API to validate scan settings.
      APPENGINE_DEFAULT_HOST_MISSING: One of the seed URLs is an App Engine
        URL but the Default Host of the App Engine is not set.
      CANNOT_USE_GOOGLE_COM_ACCOUNT: Google corporate accounts can not be used
        for scanning.
      CANNOT_USE_OWNER_ACCOUNT: The account of the scan creator can not be
        used for scanning.
      COMPUTE_API_BACKEND_ERROR: This scan targets Compute Engine, but we
        cannot validate scan settings due to a Compute Engine API backend
        error.
      COMPUTE_API_NOT_ACCESSIBLE: This scan targets Compute Engine, but we
        cannot access the Compute Engine API to validate the scan settings.
      CUSTOM_LOGIN_URL_DOES_NOT_BELONG_TO_CURRENT_PROJECT: The Custom Login
        URL does not belong to the current project.
      CUSTOM_LOGIN_URL_MALFORMED: The Custom Login URL is malformed (can not
        be parsed).
      CUSTOM_LOGIN_URL_MAPPED_TO_NON_ROUTABLE_ADDRESS: The Custom Login URL is
        mapped to a non-routable IP address in DNS.
      CUSTOM_LOGIN_URL_MAPPED_TO_UNRESERVED_ADDRESS: The Custom Login URL is
        mapped to an IP address which is not reserved for the current project.
      CUSTOM_LOGIN_URL_HAS_NON_ROUTABLE_IP_ADDRESS: The Custom Login URL has a
        non-routable IP address.
      CUSTOM_LOGIN_URL_HAS_UNRESERVED_IP_ADDRESS: The Custom Login URL has an
        IP address which is not reserved for the current project.
      DUPLICATE_SCAN_NAME: Another scan with the same name (case-sensitive)
        already exists.
      INVALID_FIELD_VALUE: A field is set to an invalid value.
      FAILED_TO_AUTHENTICATE_TO_TARGET: There was an error trying to
        authenticate to the scan target.
      FINDING_TYPE_UNSPECIFIED: Finding type value is not specified in the
        list findings request.
      FORBIDDEN_TO_SCAN_COMPUTE: Scan targets Compute Engine, yet current
        project was not whitelisted for Google Compute Engine Scanning Alpha
        access.
      FORBIDDEN_UPDATE_TO_MANAGED_SCAN: User tries to update managed scan
      MALFORMED_FILTER: The supplied filter is malformed. For example, it can
        not be parsed, does not have a filter type in expression, or the same
        filter type appears more than once.
      MALFORMED_RESOURCE_NAME: The supplied resource name is malformed (can
        not be parsed).
      PROJECT_INACTIVE: The current project is not in an active state.
      REQUIRED_FIELD: A required field is not set.
      RESOURCE_NAME_INCONSISTENT: Project id, scanconfig id, scanrun id, or
        finding id are not consistent with each other in resource name.
      SCAN_ALREADY_RUNNING: The scan being requested to start is already
        running.
      SCAN_NOT_RUNNING: The scan that was requested to be stopped is not
        running.
      SEED_URL_DOES_NOT_BELONG_TO_CURRENT_PROJECT: One of the seed URLs does
        not belong to the current project.
      SEED_URL_MALFORMED: One of the seed URLs is malformed (can not be
        parsed).
      SEED_URL_MAPPED_TO_NON_ROUTABLE_ADDRESS: One of the seed URLs is mapped
        to a non-routable IP address in DNS.
      SEED_URL_MAPPED_TO_UNRESERVED_ADDRESS: One of the seed URLs is mapped to
        an IP address which is not reserved for the current project.
      SEED_URL_HAS_NON_ROUTABLE_IP_ADDRESS: One of the seed URLs has on-
        routable IP address.
      SEED_URL_HAS_UNRESERVED_IP_ADDRESS: One of the seed URLs has an IP
        address that is not reserved for the current project.
      SERVICE_ACCOUNT_NOT_CONFIGURED: The Web Security Scanner service account
        is not configured under the project.
      TOO_MANY_SCANS: A project has reached the maximum number of scans.
      UNABLE_TO_RESOLVE_PROJECT_INFO: Resolving the details of the current
        project fails.
      UNSUPPORTED_BLACKLIST_PATTERN_FORMAT: One or more blacklist patterns
        were in the wrong format.
      UNSUPPORTED_FILTER: The supplied filter is not supported.
      UNSUPPORTED_FINDING_TYPE: The supplied finding type is not supported.
        For example, we do not provide findings of the given finding type.
      UNSUPPORTED_URL_SCHEME: The URL scheme of one or more of the supplied
        URLs is not supported.
      CLOUD_ASSET_INVENTORY_ASSET_NOT_FOUND: CAI is not able to list assets.
    """
    CODE_UNSPECIFIED = 0
    OK = 1
    INTERNAL_ERROR = 2
    APPENGINE_API_BACKEND_ERROR = 3
    APPENGINE_API_NOT_ACCESSIBLE = 4
    APPENGINE_DEFAULT_HOST_MISSING = 5
    CANNOT_USE_GOOGLE_COM_ACCOUNT = 6
    CANNOT_USE_OWNER_ACCOUNT = 7
    COMPUTE_API_BACKEND_ERROR = 8
    COMPUTE_API_NOT_ACCESSIBLE = 9
    CUSTOM_LOGIN_URL_DOES_NOT_BELONG_TO_CURRENT_PROJECT = 10
    CUSTOM_LOGIN_URL_MALFORMED = 11
    CUSTOM_LOGIN_URL_MAPPED_TO_NON_ROUTABLE_ADDRESS = 12
    CUSTOM_LOGIN_URL_MAPPED_TO_UNRESERVED_ADDRESS = 13
    CUSTOM_LOGIN_URL_HAS_NON_ROUTABLE_IP_ADDRESS = 14
    CUSTOM_LOGIN_URL_HAS_UNRESERVED_IP_ADDRESS = 15
    DUPLICATE_SCAN_NAME = 16
    INVALID_FIELD_VALUE = 17
    FAILED_TO_AUTHENTICATE_TO_TARGET = 18
    FINDING_TYPE_UNSPECIFIED = 19
    FORBIDDEN_TO_SCAN_COMPUTE = 20
    FORBIDDEN_UPDATE_TO_MANAGED_SCAN = 21
    MALFORMED_FILTER = 22
    MALFORMED_RESOURCE_NAME = 23
    PROJECT_INACTIVE = 24
    REQUIRED_FIELD = 25
    RESOURCE_NAME_INCONSISTENT = 26
    SCAN_ALREADY_RUNNING = 27
    SCAN_NOT_RUNNING = 28
    SEED_URL_DOES_NOT_BELONG_TO_CURRENT_PROJECT = 29
    SEED_URL_MALFORMED = 30
    SEED_URL_MAPPED_TO_NON_ROUTABLE_ADDRESS = 31
    SEED_URL_MAPPED_TO_UNRESERVED_ADDRESS = 32
    SEED_URL_HAS_NON_ROUTABLE_IP_ADDRESS = 33
    SEED_URL_HAS_UNRESERVED_IP_ADDRESS = 34
    SERVICE_ACCOUNT_NOT_CONFIGURED = 35
    TOO_MANY_SCANS = 36
    UNABLE_TO_RESOLVE_PROJECT_INFO = 37
    UNSUPPORTED_BLACKLIST_PATTERN_FORMAT = 38
    UNSUPPORTED_FILTER = 39
    UNSUPPORTED_FINDING_TYPE = 40
    UNSUPPORTED_URL_SCHEME = 41
    CLOUD_ASSET_INVENTORY_ASSET_NOT_FOUND = 42

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  fieldName = _messages.StringField(2)


class ScanRun(_messages.Message):
  r"""A ScanRun is a output-only resource representing an actual run of the
  scan. Next id: 12

  Enums:
    ExecutionStateValueValuesEnum: The execution state of the ScanRun.
    ResultStateValueValuesEnum: The result state of the ScanRun. This field is
      only available after the execution state reaches "FINISHED".

  Fields:
    endTime: The time at which the ScanRun reached termination state - that
      the ScanRun is either finished or stopped by user.
    errorTrace: If result_state is an ERROR, this field provides the primary
      reason for scan's termination and more details, if such are available.
    executionState: The execution state of the ScanRun.
    hasVulnerabilities: Whether the scan run has found any vulnerabilities.
    name: The resource name of the ScanRun. The name follows the format of
      'projects/{projectId}/scanConfigs/{scanConfigId}/scanRuns/{scanRunId}'.
      The ScanRun IDs are generated by the system.
    progressPercent: The percentage of total completion ranging from 0 to 100.
      If the scan is in queue, the value is 0. If the scan is running, the
      value ranges from 0 to 100. If the scan is finished, the value is 100.
    resultState: The result state of the ScanRun. This field is only available
      after the execution state reaches "FINISHED".
    startTime: The time at which the ScanRun started.
    urlsCrawledCount: The number of URLs crawled during this ScanRun. If the
      scan is in progress, the value represents the number of URLs crawled up
      to now.
    urlsTestedCount: The number of URLs tested during this ScanRun. If the
      scan is in progress, the value represents the number of URLs tested up
      to now. The number of URLs tested is usually larger than the number URLS
      crawled because typically a crawled URL is tested with multiple test
      payloads.
    warningTraces: A list of warnings, if such are encountered during this
      scan run.
  """

  class ExecutionStateValueValuesEnum(_messages.Enum):
    r"""The execution state of the ScanRun.

    Values:
      EXECUTION_STATE_UNSPECIFIED: Represents an invalid state caused by
        internal server error. This value should never be returned.
      QUEUED: The scan is waiting in the queue.
      SCANNING: The scan is in progress.
      FINISHED: The scan is either finished or stopped by user.
    """
    EXECUTION_STATE_UNSPECIFIED = 0
    QUEUED = 1
    SCANNING = 2
    FINISHED = 3

  class ResultStateValueValuesEnum(_messages.Enum):
    r"""The result state of the ScanRun. This field is only available after
    the execution state reaches "FINISHED".

    Values:
      RESULT_STATE_UNSPECIFIED: Default value. This value is returned when the
        ScanRun is not yet finished.
      SUCCESS: The scan finished without errors.
      ERROR: The scan finished with errors.
      KILLED: The scan was terminated by user.
    """
    RESULT_STATE_UNSPECIFIED = 0
    SUCCESS = 1
    ERROR = 2
    KILLED = 3

  endTime = _messages.StringField(1)
  errorTrace = _messages.MessageField('ScanRunErrorTrace', 2)
  executionState = _messages.EnumField('ExecutionStateValueValuesEnum', 3)
  hasVulnerabilities = _messages.BooleanField(4)
  name = _messages.StringField(5)
  progressPercent = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  resultState = _messages.EnumField('ResultStateValueValuesEnum', 7)
  startTime = _messages.StringField(8)
  urlsCrawledCount = _messages.IntegerField(9)
  urlsTestedCount = _messages.IntegerField(10)
  warningTraces = _messages.MessageField('ScanRunWarningTrace', 11, repeated=True)


class ScanRunErrorTrace(_messages.Message):
  r"""Output only. Defines an error trace message for a ScanRun.

  Enums:
    CodeValueValuesEnum: Indicates the error reason code.

  Fields:
    code: Indicates the error reason code.
    mostCommonHttpErrorCode: If the scan encounters TOO_MANY_HTTP_ERRORS, this
      field indicates the most common HTTP error code, if such is available.
      For example, if this code is 404, the scan has encountered too many
      NOT_FOUND responses.
    scanConfigError: If the scan encounters SCAN_CONFIG_ISSUE error, this
      field has the error message encountered during scan configuration
      validation that is performed before each scan run.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Indicates the error reason code.

    Values:
      CODE_UNSPECIFIED: Default value is never used.
      INTERNAL_ERROR: Indicates that the scan run failed due to an internal
        server error.
      SCAN_CONFIG_ISSUE: Indicates a scan configuration error, usually due to
        outdated ScanConfig settings, such as starting_urls or the DNS
        configuration.
      AUTHENTICATION_CONFIG_ISSUE: Indicates an authentication error, usually
        due to outdated ScanConfig authentication settings.
      TIMED_OUT_WHILE_SCANNING: Indicates a scan operation timeout, usually
        caused by a very large site.
      TOO_MANY_REDIRECTS: Indicates that a scan encountered excessive
        redirects, either to authentication or some other page outside of the
        scan scope.
      TOO_MANY_HTTP_ERRORS: Indicates that a scan encountered numerous errors
        from the web site pages. When available, most_common_http_error_code
        field indicates the most common HTTP error code encountered during the
        scan.
      STARTING_URLS_CRAWL_HTTP_ERRORS: Indicates that some of the starting web
        urls returned HTTP errors during the scan.
    """
    CODE_UNSPECIFIED = 0
    INTERNAL_ERROR = 1
    SCAN_CONFIG_ISSUE = 2
    AUTHENTICATION_CONFIG_ISSUE = 3
    TIMED_OUT_WHILE_SCANNING = 4
    TOO_MANY_REDIRECTS = 5
    TOO_MANY_HTTP_ERRORS = 6
    STARTING_URLS_CRAWL_HTTP_ERRORS = 7

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  mostCommonHttpErrorCode = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  scanConfigError = _messages.MessageField('ScanConfigError', 3)


class ScanRunWarningTrace(_messages.Message):
  r"""Output only. Defines a warning trace message for ScanRun. Warning traces
  provide customers with useful information that helps make the scanning
  process more effective.

  Enums:
    CodeValueValuesEnum: Indicates the warning code.

  Fields:
    code: Indicates the warning code.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Indicates the warning code.

    Values:
      CODE_UNSPECIFIED: Default value is never used.
      INSUFFICIENT_CRAWL_RESULTS: Indicates that a scan discovered an
        unexpectedly low number of URLs. This is sometimes caused by complex
        navigation features or by using a single URL for numerous pages.
      TOO_MANY_CRAWL_RESULTS: Indicates that a scan discovered too many URLs
        to test, or excessive redundant URLs.
      TOO_MANY_FUZZ_TASKS: Indicates that too many tests have been generated
        for the scan. Customer should try reducing the number of starting
        URLs, increasing the QPS rate, or narrowing down the scope of the scan
        using the excluded patterns.
      BLOCKED_BY_IAP: Indicates that a scan is blocked by IAP.
      NO_STARTING_URL_FOUND_FOR_MANAGED_SCAN: Indicates that no seed is found
        for a scan
    """
    CODE_UNSPECIFIED = 0
    INSUFFICIENT_CRAWL_RESULTS = 1
    TOO_MANY_CRAWL_RESULTS = 2
    TOO_MANY_FUZZ_TASKS = 3
    BLOCKED_BY_IAP = 4
    NO_STARTING_URL_FOUND_FOR_MANAGED_SCAN = 5

  code = _messages.EnumField('CodeValueValuesEnum', 1)


class Schedule(_messages.Message):
  r"""Scan schedule configuration.

  Fields:
    intervalDurationDays: Required. The duration of time between executions in
      days.
    scheduleTime: A timestamp indicates when the next run will be scheduled.
      The value is refreshed by the server after each run. If unspecified, it
      will default to current server time, which means the scan will be
      scheduled to start immediately.
  """

  intervalDurationDays = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  scheduleTime = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StartScanRunRequest(_messages.Message):
  r"""Request for the `StartScanRun` method."""


class StopScanRunRequest(_messages.Message):
  r"""Request for the `StopScanRun` method."""


class ViolatingResource(_messages.Message):
  r"""Information regarding any resource causing the vulnerability such as
  JavaScript sources, image, audio files, etc.

  Fields:
    contentType: The MIME type of this resource.
    resourceUrl: URL of this violating resource.
  """

  contentType = _messages.StringField(1)
  resourceUrl = _messages.StringField(2)


class VulnerableHeaders(_messages.Message):
  r"""Information about vulnerable or missing HTTP Headers.

  Fields:
    headers: List of vulnerable headers.
    missingHeaders: List of missing headers.
  """

  headers = _messages.MessageField('Header', 1, repeated=True)
  missingHeaders = _messages.MessageField('Header', 2, repeated=True)


class VulnerableParameters(_messages.Message):
  r"""Information about vulnerable request parameters.

  Fields:
    parameterNames: The vulnerable parameter names.
  """

  parameterNames = _messages.StringField(1, repeated=True)


class WebsecurityscannerProjectsScanConfigsCreateRequest(_messages.Message):
  r"""A WebsecurityscannerProjectsScanConfigsCreateRequest object.

  Fields:
    parent: Required. The parent resource name where the scan is created,
      which should be a project resource name in the format
      'projects/{projectId}'.
    scanConfig: A ScanConfig resource to be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  scanConfig = _messages.MessageField('ScanConfig', 2)


class WebsecurityscannerProjectsScanConfigsDeleteRequest(_messages.Message):
  r"""A WebsecurityscannerProjectsScanConfigsDeleteRequest object.

  Fields:
    name: Required. The resource name of the ScanConfig to be deleted. The
      name follows the format of
      'projects/{projectId}/scanConfigs/{scanConfigId}'.
  """

  name = _messages.StringField(1, required=True)


class WebsecurityscannerProjectsScanConfigsGetRequest(_messages.Message):
  r"""A WebsecurityscannerProjectsScanConfigsGetRequest object.

  Fields:
    name: Required. The resource name of the ScanConfig to be returned. The
      name follows the format of
      'projects/{projectId}/scanConfigs/{scanConfigId}'.
  """

  name = _messages.StringField(1, required=True)


class WebsecurityscannerProjectsScanConfigsListRequest(_messages.Message):
  r"""A WebsecurityscannerProjectsScanConfigsListRequest object.

  Fields:
    pageSize: The maximum number of ScanConfigs to return, can be limited by
      server. If not specified or not positive, the implementation will select
      a reasonable value.
    pageToken: A token identifying a page of results to be returned. This
      should be a `next_page_token` value returned from a previous List
      request. If unspecified, the first page of results is returned.
    parent: Required. The parent resource name, which should be a project
      resource name in the format 'projects/{projectId}'.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class WebsecurityscannerProjectsScanConfigsPatchRequest(_messages.Message):
  r"""A WebsecurityscannerProjectsScanConfigsPatchRequest object.

  Fields:
    name: The resource name of the ScanConfig. The name follows the format of
      'projects/{projectId}/scanConfigs/{scanConfigId}'. The ScanConfig IDs
      are generated by the system.
    scanConfig: A ScanConfig resource to be passed as the request body.
    updateMask: Required. The update mask applies to the resource. For the
      `FieldMask` definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  name = _messages.StringField(1, required=True)
  scanConfig = _messages.MessageField('ScanConfig', 2)
  updateMask = _messages.StringField(3)


class WebsecurityscannerProjectsScanConfigsScanRunsCrawledUrlsListRequest(_messages.Message):
  r"""A WebsecurityscannerProjectsScanConfigsScanRunsCrawledUrlsListRequest
  object.

  Fields:
    pageSize: The maximum number of CrawledUrls to return, can be limited by
      server. If not specified or not positive, the implementation will select
      a reasonable value.
    pageToken: A token identifying a page of results to be returned. This
      should be a `next_page_token` value returned from a previous List
      request. If unspecified, the first page of results is returned.
    parent: Required. The parent resource name, which should be a scan run
      resource name in the format
      'projects/{projectId}/scanConfigs/{scanConfigId}/scanRuns/{scanRunId}'.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class WebsecurityscannerProjectsScanConfigsScanRunsFindingTypeStatsListRequest(_messages.Message):
  r"""A
  WebsecurityscannerProjectsScanConfigsScanRunsFindingTypeStatsListRequest
  object.

  Fields:
    parent: Required. The parent resource name, which should be a scan run
      resource name in the format
      'projects/{projectId}/scanConfigs/{scanConfigId}/scanRuns/{scanRunId}'.
  """

  parent = _messages.StringField(1, required=True)


class WebsecurityscannerProjectsScanConfigsScanRunsFindingsGetRequest(_messages.Message):
  r"""A WebsecurityscannerProjectsScanConfigsScanRunsFindingsGetRequest
  object.

  Fields:
    name: Required. The resource name of the Finding to be returned. The name
      follows the format of 'projects/{projectId}/scanConfigs/{scanConfigId}/s
      canRuns/{scanRunId}/findings/{findingId}'.
  """

  name = _messages.StringField(1, required=True)


class WebsecurityscannerProjectsScanConfigsScanRunsFindingsListRequest(_messages.Message):
  r"""A WebsecurityscannerProjectsScanConfigsScanRunsFindingsListRequest
  object.

  Fields:
    filter: Required. The filter expression. The expression must be in the
      format: . Supported field: 'finding_type'. Supported operator: '='.
    pageSize: The maximum number of Findings to return, can be limited by
      server. If not specified or not positive, the implementation will select
      a reasonable value.
    pageToken: A token identifying a page of results to be returned. This
      should be a `next_page_token` value returned from a previous List
      request. If unspecified, the first page of results is returned.
    parent: Required. The parent resource name, which should be a scan run
      resource name in the format
      'projects/{projectId}/scanConfigs/{scanConfigId}/scanRuns/{scanRunId}'.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class WebsecurityscannerProjectsScanConfigsScanRunsGetRequest(_messages.Message):
  r"""A WebsecurityscannerProjectsScanConfigsScanRunsGetRequest object.

  Fields:
    name: Required. The resource name of the ScanRun to be returned. The name
      follows the format of
      'projects/{projectId}/scanConfigs/{scanConfigId}/scanRuns/{scanRunId}'.
  """

  name = _messages.StringField(1, required=True)


class WebsecurityscannerProjectsScanConfigsScanRunsListRequest(_messages.Message):
  r"""A WebsecurityscannerProjectsScanConfigsScanRunsListRequest object.

  Fields:
    pageSize: The maximum number of ScanRuns to return, can be limited by
      server. If not specified or not positive, the implementation will select
      a reasonable value.
    pageToken: A token identifying a page of results to be returned. This
      should be a `next_page_token` value returned from a previous List
      request. If unspecified, the first page of results is returned.
    parent: Required. The parent resource name, which should be a scan
      resource name in the format
      'projects/{projectId}/scanConfigs/{scanConfigId}'.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class WebsecurityscannerProjectsScanConfigsScanRunsStopRequest(_messages.Message):
  r"""A WebsecurityscannerProjectsScanConfigsScanRunsStopRequest object.

  Fields:
    name: Required. The resource name of the ScanRun to be stopped. The name
      follows the format of
      'projects/{projectId}/scanConfigs/{scanConfigId}/scanRuns/{scanRunId}'.
    stopScanRunRequest: A StopScanRunRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  stopScanRunRequest = _messages.MessageField('StopScanRunRequest', 2)


class WebsecurityscannerProjectsScanConfigsStartRequest(_messages.Message):
  r"""A WebsecurityscannerProjectsScanConfigsStartRequest object.

  Fields:
    name: Required. The resource name of the ScanConfig to be used. The name
      follows the format of 'projects/{projectId}/scanConfigs/{scanConfigId}'.
    startScanRunRequest: A StartScanRunRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  startScanRunRequest = _messages.MessageField('StartScanRunRequest', 2)


class Xss(_messages.Message):
  r"""Information reported for an XSS.

  Fields:
    errorMessage: An error message generated by a javascript breakage.
    stackTraces: Stack traces leading to the point where the XSS occurred.
  """

  errorMessage = _messages.StringField(1)
  stackTraces = _messages.StringField(2, repeated=True)


class Xxe(_messages.Message):
  r"""Information reported for an XXE.

  Enums:
    PayloadLocationValueValuesEnum: Location within the request where the
      payload was placed.

  Fields:
    payloadLocation: Location within the request where the payload was placed.
    payloadValue: The XML string that triggered the XXE vulnerability. Non-
      payload values might be redacted.
  """

  class PayloadLocationValueValuesEnum(_messages.Enum):
    r"""Location within the request where the payload was placed.

    Values:
      LOCATION_UNSPECIFIED: Unknown Location.
      COMPLETE_REQUEST_BODY: The XML payload replaced the complete request
        body.
    """
    LOCATION_UNSPECIFIED = 0
    COMPLETE_REQUEST_BODY = 1

  payloadLocation = _messages.EnumField('PayloadLocationValueValuesEnum', 1)
  payloadValue = _messages.StringField(2)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
