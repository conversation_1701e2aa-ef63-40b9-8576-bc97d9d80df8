"""Generated client library for websecurityscanner version v1beta."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.websecurityscanner.v1beta import websecurityscanner_v1beta_messages as messages


class WebsecurityscannerV1beta(base_api.BaseApiClient):
  """Generated client library for service websecurityscanner version v1beta."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://websecurityscanner.googleapis.com/'
  MTLS_BASE_URL = 'https://websecurityscanner.mtls.googleapis.com/'

  _PACKAGE = 'websecurityscanner'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1beta'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'WebsecurityscannerV1beta'
  _URL_VERSION = 'v1beta'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new websecurityscanner handle."""
    url = url or self.BASE_URL
    super(WebsecurityscannerV1beta, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_scanConfigs_scanRuns_crawledUrls = self.ProjectsScanConfigsScanRunsCrawledUrlsService(self)
    self.projects_scanConfigs_scanRuns_findingTypeStats = self.ProjectsScanConfigsScanRunsFindingTypeStatsService(self)
    self.projects_scanConfigs_scanRuns_findings = self.ProjectsScanConfigsScanRunsFindingsService(self)
    self.projects_scanConfigs_scanRuns = self.ProjectsScanConfigsScanRunsService(self)
    self.projects_scanConfigs = self.ProjectsScanConfigsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsScanConfigsScanRunsCrawledUrlsService(base_api.BaseApiService):
    """Service class for the projects_scanConfigs_scanRuns_crawledUrls resource."""

    _NAME = 'projects_scanConfigs_scanRuns_crawledUrls'

    def __init__(self, client):
      super(WebsecurityscannerV1beta.ProjectsScanConfigsScanRunsCrawledUrlsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List CrawledUrls under a given ScanRun.

      Args:
        request: (WebsecurityscannerProjectsScanConfigsScanRunsCrawledUrlsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListCrawledUrlsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/scanConfigs/{scanConfigsId}/scanRuns/{scanRunsId}/crawledUrls',
        http_method='GET',
        method_id='websecurityscanner.projects.scanConfigs.scanRuns.crawledUrls.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1beta/{+parent}/crawledUrls',
        request_field='',
        request_type_name='WebsecurityscannerProjectsScanConfigsScanRunsCrawledUrlsListRequest',
        response_type_name='ListCrawledUrlsResponse',
        supports_download=False,
    )

  class ProjectsScanConfigsScanRunsFindingTypeStatsService(base_api.BaseApiService):
    """Service class for the projects_scanConfigs_scanRuns_findingTypeStats resource."""

    _NAME = 'projects_scanConfigs_scanRuns_findingTypeStats'

    def __init__(self, client):
      super(WebsecurityscannerV1beta.ProjectsScanConfigsScanRunsFindingTypeStatsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List all FindingTypeStats under a given ScanRun.

      Args:
        request: (WebsecurityscannerProjectsScanConfigsScanRunsFindingTypeStatsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFindingTypeStatsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/scanConfigs/{scanConfigsId}/scanRuns/{scanRunsId}/findingTypeStats',
        http_method='GET',
        method_id='websecurityscanner.projects.scanConfigs.scanRuns.findingTypeStats.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1beta/{+parent}/findingTypeStats',
        request_field='',
        request_type_name='WebsecurityscannerProjectsScanConfigsScanRunsFindingTypeStatsListRequest',
        response_type_name='ListFindingTypeStatsResponse',
        supports_download=False,
    )

  class ProjectsScanConfigsScanRunsFindingsService(base_api.BaseApiService):
    """Service class for the projects_scanConfigs_scanRuns_findings resource."""

    _NAME = 'projects_scanConfigs_scanRuns_findings'

    def __init__(self, client):
      super(WebsecurityscannerV1beta.ProjectsScanConfigsScanRunsFindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets a Finding.

      Args:
        request: (WebsecurityscannerProjectsScanConfigsScanRunsFindingsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Finding) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/scanConfigs/{scanConfigsId}/scanRuns/{scanRunsId}/findings/{findingsId}',
        http_method='GET',
        method_id='websecurityscanner.projects.scanConfigs.scanRuns.findings.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='WebsecurityscannerProjectsScanConfigsScanRunsFindingsGetRequest',
        response_type_name='Finding',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List Findings under a given ScanRun.

      Args:
        request: (WebsecurityscannerProjectsScanConfigsScanRunsFindingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFindingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/scanConfigs/{scanConfigsId}/scanRuns/{scanRunsId}/findings',
        http_method='GET',
        method_id='websecurityscanner.projects.scanConfigs.scanRuns.findings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta/{+parent}/findings',
        request_field='',
        request_type_name='WebsecurityscannerProjectsScanConfigsScanRunsFindingsListRequest',
        response_type_name='ListFindingsResponse',
        supports_download=False,
    )

  class ProjectsScanConfigsScanRunsService(base_api.BaseApiService):
    """Service class for the projects_scanConfigs_scanRuns resource."""

    _NAME = 'projects_scanConfigs_scanRuns'

    def __init__(self, client):
      super(WebsecurityscannerV1beta.ProjectsScanConfigsScanRunsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets a ScanRun.

      Args:
        request: (WebsecurityscannerProjectsScanConfigsScanRunsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ScanRun) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/scanConfigs/{scanConfigsId}/scanRuns/{scanRunsId}',
        http_method='GET',
        method_id='websecurityscanner.projects.scanConfigs.scanRuns.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='WebsecurityscannerProjectsScanConfigsScanRunsGetRequest',
        response_type_name='ScanRun',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ScanRuns under a given ScanConfig, in descending order of ScanRun stop time.

      Args:
        request: (WebsecurityscannerProjectsScanConfigsScanRunsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListScanRunsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/scanConfigs/{scanConfigsId}/scanRuns',
        http_method='GET',
        method_id='websecurityscanner.projects.scanConfigs.scanRuns.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1beta/{+parent}/scanRuns',
        request_field='',
        request_type_name='WebsecurityscannerProjectsScanConfigsScanRunsListRequest',
        response_type_name='ListScanRunsResponse',
        supports_download=False,
    )

    def Stop(self, request, global_params=None):
      r"""Stops a ScanRun. The stopped ScanRun is returned.

      Args:
        request: (WebsecurityscannerProjectsScanConfigsScanRunsStopRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ScanRun) The response message.
      """
      config = self.GetMethodConfig('Stop')
      return self._RunMethod(
          config, request, global_params=global_params)

    Stop.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/scanConfigs/{scanConfigsId}/scanRuns/{scanRunsId}:stop',
        http_method='POST',
        method_id='websecurityscanner.projects.scanConfigs.scanRuns.stop',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}:stop',
        request_field='stopScanRunRequest',
        request_type_name='WebsecurityscannerProjectsScanConfigsScanRunsStopRequest',
        response_type_name='ScanRun',
        supports_download=False,
    )

  class ProjectsScanConfigsService(base_api.BaseApiService):
    """Service class for the projects_scanConfigs resource."""

    _NAME = 'projects_scanConfigs'

    def __init__(self, client):
      super(WebsecurityscannerV1beta.ProjectsScanConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new ScanConfig.

      Args:
        request: (WebsecurityscannerProjectsScanConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ScanConfig) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/scanConfigs',
        http_method='POST',
        method_id='websecurityscanner.projects.scanConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1beta/{+parent}/scanConfigs',
        request_field='scanConfig',
        request_type_name='WebsecurityscannerProjectsScanConfigsCreateRequest',
        response_type_name='ScanConfig',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing ScanConfig and its child resources.

      Args:
        request: (WebsecurityscannerProjectsScanConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/scanConfigs/{scanConfigsId}',
        http_method='DELETE',
        method_id='websecurityscanner.projects.scanConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='WebsecurityscannerProjectsScanConfigsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a ScanConfig.

      Args:
        request: (WebsecurityscannerProjectsScanConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ScanConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/scanConfigs/{scanConfigsId}',
        http_method='GET',
        method_id='websecurityscanner.projects.scanConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}',
        request_field='',
        request_type_name='WebsecurityscannerProjectsScanConfigsGetRequest',
        response_type_name='ScanConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ScanConfigs under a given project.

      Args:
        request: (WebsecurityscannerProjectsScanConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListScanConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/scanConfigs',
        http_method='GET',
        method_id='websecurityscanner.projects.scanConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1beta/{+parent}/scanConfigs',
        request_field='',
        request_type_name='WebsecurityscannerProjectsScanConfigsListRequest',
        response_type_name='ListScanConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a ScanConfig. This method support partial update of a ScanConfig.

      Args:
        request: (WebsecurityscannerProjectsScanConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ScanConfig) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/scanConfigs/{scanConfigsId}',
        http_method='PATCH',
        method_id='websecurityscanner.projects.scanConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta/{+name}',
        request_field='scanConfig',
        request_type_name='WebsecurityscannerProjectsScanConfigsPatchRequest',
        response_type_name='ScanConfig',
        supports_download=False,
    )

    def Start(self, request, global_params=None):
      r"""Start a ScanRun according to the given ScanConfig.

      Args:
        request: (WebsecurityscannerProjectsScanConfigsStartRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ScanRun) The response message.
      """
      config = self.GetMethodConfig('Start')
      return self._RunMethod(
          config, request, global_params=global_params)

    Start.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta/projects/{projectsId}/scanConfigs/{scanConfigsId}:start',
        http_method='POST',
        method_id='websecurityscanner.projects.scanConfigs.start',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta/{+name}:start',
        request_field='startScanRunRequest',
        request_type_name='WebsecurityscannerProjectsScanConfigsStartRequest',
        response_type_name='ScanRun',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(WebsecurityscannerV1beta.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
