"""Generated client library for serviceusage version v2alpha."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.serviceusage.v2alpha import serviceusage_v2alpha_messages as messages


class ServiceusageV2alpha(base_api.BaseApiClient):
  """Generated client library for service serviceusage version v2alpha."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://serviceusage.googleapis.com/'
  MTLS_BASE_URL = 'https://serviceusage.mtls.googleapis.com/'

  _PACKAGE = 'serviceusage'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform', 'https://www.googleapis.com/auth/cloud-platform.read-only', 'https://www.googleapis.com/auth/service.management']
  _VERSION = 'v2alpha'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'ServiceusageV2alpha'
  _URL_VERSION = 'v2alpha'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new serviceusage handle."""
    url = url or self.BASE_URL
    super(ServiceusageV2alpha, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.categories_categoryServices = self.CategoriesCategoryServicesService(self)
    self.categories = self.CategoriesService(self)
    self.consumerPolicies = self.ConsumerPoliciesService(self)
    self.operations = self.OperationsService(self)
    self.services_ancestorGroups = self.ServicesAncestorGroupsService(self)
    self.services_apis = self.ServicesApisService(self)
    self.services_groups_descendantServices = self.ServicesGroupsDescendantServicesService(self)
    self.services_groups_members = self.ServicesGroupsMembersService(self)
    self.services_groups = self.ServicesGroupsService(self)
    self.services = self.ServicesService(self)
    self.v2alpha = self.V2alphaService(self)

  class CategoriesCategoryServicesService(base_api.BaseApiService):
    """Service class for the categories_categoryServices resource."""

    _NAME = 'categories_categoryServices'

    def __init__(self, client):
      super(ServiceusageV2alpha.CategoriesCategoryServicesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List the services that belong to a given category. The supported categories are: `categories/google` and `categories/partner`.

      Args:
        request: (ServiceusageCategoriesCategoryServicesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListCategoryServicesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/{v2alphaId}/{v2alphaId1}/categories/{categoriesId}/categoryServices',
        http_method='GET',
        method_id='serviceusage.categories.categoryServices.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2alpha/{+parent}/categoryServices',
        request_field='',
        request_type_name='ServiceusageCategoriesCategoryServicesListRequest',
        response_type_name='ListCategoryServicesResponse',
        supports_download=False,
    )

  class CategoriesService(base_api.BaseApiService):
    """Service class for the categories resource."""

    _NAME = 'categories'

    def __init__(self, client):
      super(ServiceusageV2alpha.CategoriesService, self).__init__(client)
      self._upload_configs = {
          }

  class ConsumerPoliciesService(base_api.BaseApiService):
    """Service class for the consumerPolicies resource."""

    _NAME = 'consumerPolicies'

    def __init__(self, client):
      super(ServiceusageV2alpha.ConsumerPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get the consumer policy of a resource.

      Args:
        request: (ServiceusageConsumerPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleApiServiceusageV2alphaConsumerPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/{v2alphaId}/{v2alphaId1}/consumerPolicies/{consumerPoliciesId}',
        http_method='GET',
        method_id='serviceusage.consumerPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2alpha/{+name}',
        request_field='',
        request_type_name='ServiceusageConsumerPoliciesGetRequest',
        response_type_name='GoogleApiServiceusageV2alphaConsumerPolicy',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update the consumer policy of a resource.

      Args:
        request: (ServiceusageConsumerPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/{v2alphaId}/{v2alphaId1}/consumerPolicies/{consumerPoliciesId}',
        http_method='PATCH',
        method_id='serviceusage.consumerPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force', 'validateOnly'],
        relative_path='v2alpha/{+name}',
        request_field='googleApiServiceusageV2alphaConsumerPolicy',
        request_type_name='ServiceusageConsumerPoliciesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class OperationsService(base_api.BaseApiService):
    """Service class for the operations resource."""

    _NAME = 'operations'

    def __init__(self, client):
      super(ServiceusageV2alpha.OperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (ServiceusageOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/operations/{operationsId}',
        http_method='GET',
        method_id='serviceusage.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2alpha/{+name}',
        request_field='',
        request_type_name='ServiceusageOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (ServiceusageOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='serviceusage.operations.list',
        ordered_params=[],
        path_params=[],
        query_params=['filter', 'name', 'pageSize', 'pageToken'],
        relative_path='v2alpha/operations',
        request_field='',
        request_type_name='ServiceusageOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ServicesAncestorGroupsService(base_api.BaseApiService):
    """Service class for the services_ancestorGroups resource."""

    _NAME = 'services_ancestorGroups'

    def __init__(self, client):
      super(ServiceusageV2alpha.ServicesAncestorGroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List the ancestor groups that depend on the service. This lists the groups that include the parent service directly or which include a group for which the specified service is a descendant service.

      Args:
        request: (ServiceusageServicesAncestorGroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAncestorGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/{v2alphaId}/{v2alphaId1}/services/{servicesId}/ancestorGroups',
        http_method='GET',
        method_id='serviceusage.services.ancestorGroups.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2alpha/{+name}/ancestorGroups',
        request_field='',
        request_type_name='ServiceusageServicesAncestorGroupsListRequest',
        response_type_name='ListAncestorGroupsResponse',
        supports_download=False,
    )

  class ServicesApisService(base_api.BaseApiService):
    """Service class for the services_apis resource."""

    _NAME = 'services_apis'

    def __init__(self, client):
      super(ServiceusageV2alpha.ServicesApisService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List APIs exposed by the given service.

      Args:
        request: (ServiceusageServicesApisListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListServiceApisResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/{v2alphaId}/{v2alphaId1}/services/{servicesId}/apis',
        http_method='GET',
        method_id='serviceusage.services.apis.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2alpha/{+parent}/apis',
        request_field='',
        request_type_name='ServiceusageServicesApisListRequest',
        response_type_name='ListServiceApisResponse',
        supports_download=False,
    )

  class ServicesGroupsDescendantServicesService(base_api.BaseApiService):
    """Service class for the services_groups_descendantServices resource."""

    _NAME = 'services_groups_descendantServices'

    def __init__(self, client):
      super(ServiceusageV2alpha.ServicesGroupsDescendantServicesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List the services that belong to a given service group or transitively to any of the groups that are members of the service group. The service group is a producer defined service group.

      Args:
        request: (ServiceusageServicesGroupsDescendantServicesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDescendantServicesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/{v2alphaId}/{v2alphaId1}/services/{servicesId}/groups/{groupsId}/descendantServices',
        http_method='GET',
        method_id='serviceusage.services.groups.descendantServices.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v2alpha/{+parent}/descendantServices',
        request_field='',
        request_type_name='ServiceusageServicesGroupsDescendantServicesListRequest',
        response_type_name='ListDescendantServicesResponse',
        supports_download=False,
    )

  class ServicesGroupsMembersService(base_api.BaseApiService):
    """Service class for the services_groups_members resource."""

    _NAME = 'services_groups_members'

    def __init__(self, client):
      super(ServiceusageV2alpha.ServicesGroupsMembersService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List members for the given service group. The service group is a producer defined service group.

      Args:
        request: (ServiceusageServicesGroupsMembersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGroupMembersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/{v2alphaId}/{v2alphaId1}/services/{servicesId}/groups/{groupsId}/members',
        http_method='GET',
        method_id='serviceusage.services.groups.members.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'view'],
        relative_path='v2alpha/{+parent}/members',
        request_field='',
        request_type_name='ServiceusageServicesGroupsMembersListRequest',
        response_type_name='ListGroupMembersResponse',
        supports_download=False,
    )

  class ServicesGroupsService(base_api.BaseApiService):
    """Service class for the services_groups resource."""

    _NAME = 'services_groups'

    def __init__(self, client):
      super(ServiceusageV2alpha.ServicesGroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List service groups owned by the given service.

      Args:
        request: (ServiceusageServicesGroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListServiceGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/{v2alphaId}/{v2alphaId1}/services/{servicesId}/groups',
        http_method='GET',
        method_id='serviceusage.services.groups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'view'],
        relative_path='v2alpha/{+parent}/groups',
        request_field='',
        request_type_name='ServiceusageServicesGroupsListRequest',
        response_type_name='ListServiceGroupsResponse',
        supports_download=False,
    )

  class ServicesService(base_api.BaseApiService):
    """Service class for the services resource."""

    _NAME = 'services'

    def __init__(self, client):
      super(ServiceusageV2alpha.ServicesService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchGet(self, request, global_params=None):
      r"""Get the details of a collection of services.

      Args:
        request: (ServiceusageServicesBatchGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BatchGetServicesResponse) The response message.
      """
      config = self.GetMethodConfig('BatchGet')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchGet.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/{v2alphaId}/{v2alphaId1}/services:batchGet',
        http_method='GET',
        method_id='serviceusage.services.batchGet',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['services', 'view'],
        relative_path='v2alpha/{+parent}/services:batchGet',
        request_field='',
        request_type_name='ServiceusageServicesBatchGetRequest',
        response_type_name='BatchGetServicesResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get the details of a service.

      Args:
        request: (ServiceusageServicesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServiceState) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/{v2alphaId}/{v2alphaId1}/services/{servicesId}',
        http_method='GET',
        method_id='serviceusage.services.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v2alpha/{+name}',
        request_field='',
        request_type_name='ServiceusageServicesGetRequest',
        response_type_name='ServiceState',
        supports_download=False,
    )

  class V2alphaService(base_api.BaseApiService):
    """Service class for the v2alpha resource."""

    _NAME = 'v2alpha'

    def __init__(self, client):
      super(ServiceusageV2alpha.V2alphaService, self).__init__(client)
      self._upload_configs = {
          }

    def GetEffectivePolicy(self, request, global_params=None):
      r"""Get effective consumer policy for a resource, which contains enable rule information of consumer policies from the resource hierarchy.

      Args:
        request: (ServiceusageGetEffectivePolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EffectivePolicy) The response message.
      """
      config = self.GetMethodConfig('GetEffectivePolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetEffectivePolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/{v2alphaId}/{v2alphaId1}/effectivePolicy',
        http_method='GET',
        method_id='serviceusage.getEffectivePolicy',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v2alpha/{+name}',
        request_field='',
        request_type_name='ServiceusageGetEffectivePolicyRequest',
        response_type_name='EffectivePolicy',
        supports_download=False,
    )

    def TestEnabled(self, request, global_params=None):
      r"""Tests a value against the result of merging consumer policies in the resource hierarchy. This operation is designed to be used for building policy-aware UIs and command-line tools, not for access checking.

      Args:
        request: (ServiceusageTestEnabledRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (State) The response message.
      """
      config = self.GetMethodConfig('TestEnabled')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestEnabled.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/{v2alphaId}/{v2alphaId1}:testEnabled',
        http_method='POST',
        method_id='serviceusage.testEnabled',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2alpha/{+name}:testEnabled',
        request_field='testEnabledRequest',
        request_type_name='ServiceusageTestEnabledRequest',
        response_type_name='State',
        supports_download=False,
    )
