"""Generated client library for serviceusage version v1beta1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.serviceusage.v1beta1 import serviceusage_v1beta1_messages as messages


class ServiceusageV1beta1(base_api.BaseApiClient):
  """Generated client library for service serviceusage version v1beta1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://serviceusage.googleapis.com/'
  MTLS_BASE_URL = 'https://serviceusage.mtls.googleapis.com/'

  _PACKAGE = 'serviceusage'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform', 'https://www.googleapis.com/auth/cloud-platform.read-only', 'https://www.googleapis.com/auth/service.management']
  _VERSION = 'v1beta1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'ServiceusageV1beta1'
  _URL_VERSION = 'v1beta1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new serviceusage handle."""
    url = url or self.BASE_URL
    super(ServiceusageV1beta1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.operations = self.OperationsService(self)
    self.services_consumerQuotaMetrics_limits_adminOverrides = self.ServicesConsumerQuotaMetricsLimitsAdminOverridesService(self)
    self.services_consumerQuotaMetrics_limits_consumerOverrides = self.ServicesConsumerQuotaMetricsLimitsConsumerOverridesService(self)
    self.services_consumerQuotaMetrics_limits = self.ServicesConsumerQuotaMetricsLimitsService(self)
    self.services_consumerQuotaMetrics = self.ServicesConsumerQuotaMetricsService(self)
    self.services = self.ServicesService(self)

  class OperationsService(base_api.BaseApiService):
    """Service class for the operations resource."""

    _NAME = 'operations'

    def __init__(self, client):
      super(ServiceusageV1beta1.OperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation.  Clients can use this.
method to poll the operation result at intervals as recommended by the API
service.

      Args:
        request: (ServiceusageOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/operations/{operationsId}',
        http_method='GET',
        method_id='serviceusage.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='ServiceusageOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the.
server doesn't support this method, it returns `UNIMPLEMENTED`.

NOTE: the `name` binding allows API services to override the binding
to use different resource name schemes, such as `users/*/operations`. To
override the binding, API services can add a binding such as
`"/v1/{name=users/*}/operations"` to their service configuration.
For backwards compatibility, the default name includes the operations
collection id, however overriding users must ensure the name binding
is the parent resource, without the operations collection id.

      Args:
        request: (ServiceusageOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='serviceusage.operations.list',
        ordered_params=[],
        path_params=[],
        query_params=['filter', 'name', 'pageSize', 'pageToken'],
        relative_path='v1beta1/operations',
        request_field='',
        request_type_name='ServiceusageOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ServicesConsumerQuotaMetricsLimitsAdminOverridesService(base_api.BaseApiService):
    """Service class for the services_consumerQuotaMetrics_limits_adminOverrides resource."""

    _NAME = 'services_consumerQuotaMetrics_limits_adminOverrides'

    def __init__(self, client):
      super(ServiceusageV1beta1.ServicesConsumerQuotaMetricsLimitsAdminOverridesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an admin override.
An admin override is applied by an administrator of a parent folder or
parent organization of the consumer receiving the override. An admin
override is intended to limit the amount of quota the consumer can use out
of the total quota pool allocated to all children of the folder or
organization.

      Args:
        request: (ServiceusageServicesConsumerQuotaMetricsLimitsAdminOverridesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/{v1beta1Id}/{v1beta1Id1}/services/{servicesId}/consumerQuotaMetrics/{consumerQuotaMetricsId}/limits/{limitsId}/adminOverrides',
        http_method='POST',
        method_id='serviceusage.services.consumerQuotaMetrics.limits.adminOverrides.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['force'],
        relative_path='v1beta1/{+parent}/adminOverrides',
        request_field='quotaOverride',
        request_type_name='ServiceusageServicesConsumerQuotaMetricsLimitsAdminOverridesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an admin override.

      Args:
        request: (ServiceusageServicesConsumerQuotaMetricsLimitsAdminOverridesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/{v1beta1Id}/{v1beta1Id1}/services/{servicesId}/consumerQuotaMetrics/{consumerQuotaMetricsId}/limits/{limitsId}/adminOverrides/{adminOverridesId}',
        http_method='DELETE',
        method_id='serviceusage.services.consumerQuotaMetrics.limits.adminOverrides.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='ServiceusageServicesConsumerQuotaMetricsLimitsAdminOverridesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all admin overrides on this limit.

      Args:
        request: (ServiceusageServicesConsumerQuotaMetricsLimitsAdminOverridesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAdminOverridesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/{v1beta1Id}/{v1beta1Id1}/services/{servicesId}/consumerQuotaMetrics/{consumerQuotaMetricsId}/limits/{limitsId}/adminOverrides',
        http_method='GET',
        method_id='serviceusage.services.consumerQuotaMetrics.limits.adminOverrides.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/adminOverrides',
        request_field='',
        request_type_name='ServiceusageServicesConsumerQuotaMetricsLimitsAdminOverridesListRequest',
        response_type_name='ListAdminOverridesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an admin override.

      Args:
        request: (ServiceusageServicesConsumerQuotaMetricsLimitsAdminOverridesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/{v1beta1Id}/{v1beta1Id1}/services/{servicesId}/consumerQuotaMetrics/{consumerQuotaMetricsId}/limits/{limitsId}/adminOverrides/{adminOverridesId}',
        http_method='PATCH',
        method_id='serviceusage.services.consumerQuotaMetrics.limits.adminOverrides.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force', 'updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='quotaOverride',
        request_type_name='ServiceusageServicesConsumerQuotaMetricsLimitsAdminOverridesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ServicesConsumerQuotaMetricsLimitsConsumerOverridesService(base_api.BaseApiService):
    """Service class for the services_consumerQuotaMetrics_limits_consumerOverrides resource."""

    _NAME = 'services_consumerQuotaMetrics_limits_consumerOverrides'

    def __init__(self, client):
      super(ServiceusageV1beta1.ServicesConsumerQuotaMetricsLimitsConsumerOverridesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a consumer override.
A consumer override is applied to the consumer on its own authority to
limit its own quota usage. Consumer overrides cannot be used to grant more
quota than would be allowed by admin overrides, producer overrides, or the
default limit of the service.

      Args:
        request: (ServiceusageServicesConsumerQuotaMetricsLimitsConsumerOverridesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/{v1beta1Id}/{v1beta1Id1}/services/{servicesId}/consumerQuotaMetrics/{consumerQuotaMetricsId}/limits/{limitsId}/consumerOverrides',
        http_method='POST',
        method_id='serviceusage.services.consumerQuotaMetrics.limits.consumerOverrides.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['force'],
        relative_path='v1beta1/{+parent}/consumerOverrides',
        request_field='quotaOverride',
        request_type_name='ServiceusageServicesConsumerQuotaMetricsLimitsConsumerOverridesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a consumer override.

      Args:
        request: (ServiceusageServicesConsumerQuotaMetricsLimitsConsumerOverridesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/{v1beta1Id}/{v1beta1Id1}/services/{servicesId}/consumerQuotaMetrics/{consumerQuotaMetricsId}/limits/{limitsId}/consumerOverrides/{consumerOverridesId}',
        http_method='DELETE',
        method_id='serviceusage.services.consumerQuotaMetrics.limits.consumerOverrides.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='ServiceusageServicesConsumerQuotaMetricsLimitsConsumerOverridesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all consumer overrides on this limit.

      Args:
        request: (ServiceusageServicesConsumerQuotaMetricsLimitsConsumerOverridesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListConsumerOverridesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/{v1beta1Id}/{v1beta1Id1}/services/{servicesId}/consumerQuotaMetrics/{consumerQuotaMetricsId}/limits/{limitsId}/consumerOverrides',
        http_method='GET',
        method_id='serviceusage.services.consumerQuotaMetrics.limits.consumerOverrides.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/consumerOverrides',
        request_field='',
        request_type_name='ServiceusageServicesConsumerQuotaMetricsLimitsConsumerOverridesListRequest',
        response_type_name='ListConsumerOverridesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a consumer override.

      Args:
        request: (ServiceusageServicesConsumerQuotaMetricsLimitsConsumerOverridesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/{v1beta1Id}/{v1beta1Id1}/services/{servicesId}/consumerQuotaMetrics/{consumerQuotaMetricsId}/limits/{limitsId}/consumerOverrides/{consumerOverridesId}',
        http_method='PATCH',
        method_id='serviceusage.services.consumerQuotaMetrics.limits.consumerOverrides.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force', 'updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='quotaOverride',
        request_type_name='ServiceusageServicesConsumerQuotaMetricsLimitsConsumerOverridesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ServicesConsumerQuotaMetricsLimitsService(base_api.BaseApiService):
    """Service class for the services_consumerQuotaMetrics_limits resource."""

    _NAME = 'services_consumerQuotaMetrics_limits'

    def __init__(self, client):
      super(ServiceusageV1beta1.ServicesConsumerQuotaMetricsLimitsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Retrieves a summary of quota information for a specific quota limit.

      Args:
        request: (ServiceusageServicesConsumerQuotaMetricsLimitsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ConsumerQuotaLimit) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/{v1beta1Id}/{v1beta1Id1}/services/{servicesId}/consumerQuotaMetrics/{consumerQuotaMetricsId}/limits/{limitsId}',
        http_method='GET',
        method_id='serviceusage.services.consumerQuotaMetrics.limits.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='ServiceusageServicesConsumerQuotaMetricsLimitsGetRequest',
        response_type_name='ConsumerQuotaLimit',
        supports_download=False,
    )

  class ServicesConsumerQuotaMetricsService(base_api.BaseApiService):
    """Service class for the services_consumerQuotaMetrics resource."""

    _NAME = 'services_consumerQuotaMetrics'

    def __init__(self, client):
      super(ServiceusageV1beta1.ServicesConsumerQuotaMetricsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Retrieves a summary of quota information for a specific quota metric.

      Args:
        request: (ServiceusageServicesConsumerQuotaMetricsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ConsumerQuotaMetric) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/{v1beta1Id}/{v1beta1Id1}/services/{servicesId}/consumerQuotaMetrics/{consumerQuotaMetricsId}',
        http_method='GET',
        method_id='serviceusage.services.consumerQuotaMetrics.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='ServiceusageServicesConsumerQuotaMetricsGetRequest',
        response_type_name='ConsumerQuotaMetric',
        supports_download=False,
    )

    def ImportConsumerOverrides(self, request, global_params=None):
      r"""Create or update multiple consumer overrides atomically, all on the.
same consumer, but on many different metrics or limits.
The name field in the quota override message should not be set.

      Args:
        request: (ServiceusageServicesConsumerQuotaMetricsImportConsumerOverridesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('ImportConsumerOverrides')
      return self._RunMethod(
          config, request, global_params=global_params)

    ImportConsumerOverrides.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/{v1beta1Id}/{v1beta1Id1}/services/{servicesId}/consumerQuotaMetrics:importConsumerOverrides',
        http_method='POST',
        method_id='serviceusage.services.consumerQuotaMetrics.importConsumerOverrides',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/consumerQuotaMetrics:importConsumerOverrides',
        request_field='importConsumerOverridesRequest',
        request_type_name='ServiceusageServicesConsumerQuotaMetricsImportConsumerOverridesRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Retrieves a summary of all quota information visible to the service.
consumer, organized by service metric. Each metric includes information
about all of its defined limits. Each limit includes the limit
configuration (quota unit, preciseness, default value), the current
effective limit value, and all of the overrides applied to the limit.

      Args:
        request: (ServiceusageServicesConsumerQuotaMetricsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListConsumerQuotaMetricsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/{v1beta1Id}/{v1beta1Id1}/services/{servicesId}/consumerQuotaMetrics',
        http_method='GET',
        method_id='serviceusage.services.consumerQuotaMetrics.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'view'],
        relative_path='v1beta1/{+parent}/consumerQuotaMetrics',
        request_field='',
        request_type_name='ServiceusageServicesConsumerQuotaMetricsListRequest',
        response_type_name='ListConsumerQuotaMetricsResponse',
        supports_download=False,
    )

  class ServicesService(base_api.BaseApiService):
    """Service class for the services resource."""

    _NAME = 'services'

    def __init__(self, client):
      super(ServiceusageV1beta1.ServicesService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchEnable(self, request, global_params=None):
      r"""Enable multiple services on a project. The operation is atomic: if enabling.
any service fails, then the entire batch fails, and no state changes occur.

Operation<response: google.protobuf.Empty>

      Args:
        request: (ServiceusageServicesBatchEnableRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('BatchEnable')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchEnable.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/{v1beta1Id}/{v1beta1Id1}/services:batchEnable',
        http_method='POST',
        method_id='serviceusage.services.batchEnable',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/services:batchEnable',
        request_field='batchEnableServicesRequest',
        request_type_name='ServiceusageServicesBatchEnableRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Disable(self, request, global_params=None):
      r"""Disable a service so that it can no longer be used with a project.
This prevents unintended usage that may cause unexpected billing
charges or security leaks.

It is not valid to call the disable method on a service that is not
currently enabled. Callers will receive a `FAILED_PRECONDITION` status if
the target service is not currently enabled.

Operation<response: google.protobuf.Empty>

      Args:
        request: (ServiceusageServicesDisableRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Disable')
      return self._RunMethod(
          config, request, global_params=global_params)

    Disable.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/{v1beta1Id}/{v1beta1Id1}/services/{servicesId}:disable',
        http_method='POST',
        method_id='serviceusage.services.disable',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:disable',
        request_field='disableServiceRequest',
        request_type_name='ServiceusageServicesDisableRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Enable(self, request, global_params=None):
      r"""Enable a service so that it can be used with a project.

Operation<response: google.protobuf.Empty>

      Args:
        request: (ServiceusageServicesEnableRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Enable')
      return self._RunMethod(
          config, request, global_params=global_params)

    Enable.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/{v1beta1Id}/{v1beta1Id1}/services/{servicesId}:enable',
        http_method='POST',
        method_id='serviceusage.services.enable',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:enable',
        request_field='enableServiceRequest',
        request_type_name='ServiceusageServicesEnableRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def GenerateServiceIdentity(self, request, global_params=None):
      r"""Generate service identity for service.

      Args:
        request: (ServiceusageServicesGenerateServiceIdentityRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('GenerateServiceIdentity')
      return self._RunMethod(
          config, request, global_params=global_params)

    GenerateServiceIdentity.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/{v1beta1Id}/{v1beta1Id1}/services/{servicesId}:generateServiceIdentity',
        http_method='POST',
        method_id='serviceusage.services.generateServiceIdentity',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}:generateServiceIdentity',
        request_field='',
        request_type_name='ServiceusageServicesGenerateServiceIdentityRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the service configuration and enabled state for a given service.

      Args:
        request: (ServiceusageServicesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Service) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/{v1beta1Id}/{v1beta1Id1}/services/{servicesId}',
        http_method='GET',
        method_id='serviceusage.services.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='ServiceusageServicesGetRequest',
        response_type_name='Service',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List all services available to the specified project, and the current.
state of those services with respect to the project. The list includes
all public services, all services for which the calling user has the
`servicemanagement.services.bind` permission, and all services that have
already been enabled on the project. The list can be filtered to
only include services in a specific state, for example to only include
services enabled on the project.

      Args:
        request: (ServiceusageServicesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListServicesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/{v1beta1Id}/{v1beta1Id1}/services',
        http_method='GET',
        method_id='serviceusage.services.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/services',
        request_field='',
        request_type_name='ServiceusageServicesListRequest',
        response_type_name='ListServicesResponse',
        supports_download=False,
    )
