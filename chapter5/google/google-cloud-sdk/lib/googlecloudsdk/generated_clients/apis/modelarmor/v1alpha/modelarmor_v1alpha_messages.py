"""Generated message classes for modelarmor version v1alpha.

Model Armor helps you protect against risks like prompt injection, harmful
content, and data leakage in generative AI applications by letting you define
policies that filter user prompts and model responses.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'modelarmor'


class ByteDataItem(_messages.Message):
  r"""Represents Byte Data item.

  Enums:
    ByteDataTypeValueValuesEnum: Required. The type of byte data

  Fields:
    byteData: Required. Bytes Data
    byteDataType: Required. The type of byte data
  """

  class ByteDataTypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of byte data

    Values:
      BYTE_ITEM_TYPE_UNSPECIFIED: Unused
      PLAINTEXT_UTF8: plain text
      PDF: PDF
      WORD_DOCUMENT: DOCX, DOCM, DOTX, DOTM
      EXCEL_DOCUMENT: XLSX, XLSM, XLTX, XLYM
      POWERPOINT_DOCUMENT: PPTX, PPTM, POTX, POTM, POT
      TXT: TXT
      CSV: CSV
    """
    BYTE_ITEM_TYPE_UNSPECIFIED = 0
    PLAINTEXT_UTF8 = 1
    PDF = 2
    WORD_DOCUMENT = 3
    EXCEL_DOCUMENT = 4
    POWERPOINT_DOCUMENT = 5
    TXT = 6
    CSV = 7

  byteData = _messages.BytesField(1)
  byteDataType = _messages.EnumField('ByteDataTypeValueValuesEnum', 2)


class CsamFilterResult(_messages.Message):
  r"""CSAM (Child Safety Abuse Material) Filter Result

  Enums:
    ExecutionStateValueValuesEnum: Output only. Reports whether the CSAM
      filter was successfully executed or not.
    MatchStateValueValuesEnum: Output only. Match state for CSAM.

  Fields:
    executionState: Output only. Reports whether the CSAM filter was
      successfully executed or not.
    matchState: Output only. Match state for CSAM.
    messageItems: Optional messages corresponding to the result. A message can
      provide warnings or error details. For example, if execution state is
      skipped then this field provides related reason/explanation.
  """

  class ExecutionStateValueValuesEnum(_messages.Enum):
    r"""Output only. Reports whether the CSAM filter was successfully executed
    or not.

    Values:
      FILTER_EXECUTION_STATE_UNSPECIFIED: Unused
      EXECUTION_SUCCESS: Filter executed successfully
      EXECUTION_SKIPPED: Filter execution was skipped. This can happen due to
        server-side error or permission issue.
    """
    FILTER_EXECUTION_STATE_UNSPECIFIED = 0
    EXECUTION_SUCCESS = 1
    EXECUTION_SKIPPED = 2

  class MatchStateValueValuesEnum(_messages.Enum):
    r"""Output only. Match state for CSAM.

    Values:
      FILTER_MATCH_STATE_UNSPECIFIED: Unused
      NO_MATCH_FOUND: Matching criteria is not achieved for filters.
      MATCH_FOUND: Matching criteria is achieved for the filter.
    """
    FILTER_MATCH_STATE_UNSPECIFIED = 0
    NO_MATCH_FOUND = 1
    MATCH_FOUND = 2

  executionState = _messages.EnumField('ExecutionStateValueValuesEnum', 1)
  matchState = _messages.EnumField('MatchStateValueValuesEnum', 2)
  messageItems = _messages.MessageField('MessageItem', 3, repeated=True)


class DataItem(_messages.Message):
  r"""Represents Data item

  Fields:
    byteItem: Data provided in the form of bytes.
    text: Plaintext string data for sanitization.
  """

  byteItem = _messages.MessageField('ByteDataItem', 1)
  text = _messages.StringField(2)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class FilterConfig(_messages.Message):
  r"""Filters configuration.

  Fields:
    maliciousUriFilterSettings: Optional. Malicious URI filter settings.
    piAndJailbreakFilterSettings: Optional. Prompt injection and Jailbreak
      filter settings.
    raiSettings: Optional. Responsible AI settings.
    sdpSettings: Optional. Sensitive Data Protection settings.
  """

  maliciousUriFilterSettings = _messages.MessageField('MaliciousUriFilterSettings', 1)
  piAndJailbreakFilterSettings = _messages.MessageField('PiAndJailbreakFilterSettings', 2)
  raiSettings = _messages.MessageField('RaiFilterSettings', 3)
  sdpSettings = _messages.MessageField('SdpFilterSettings', 4)


class FilterResult(_messages.Message):
  r"""Filter Result obtained after Sanitization operations.

  Fields:
    csamFilterFilterResult: CSAM filter results.
    maliciousUriFilterResult: Malicious URI filter results.
    piAndJailbreakFilterResult: Prompt injection and Jailbreak filter results.
    raiFilterResult: Responsible AI filter results.
    sdpFilterResult: Sensitive Data Protection results.
    virusScanFilterResult: Virus scan results.
  """

  csamFilterFilterResult = _messages.MessageField('CsamFilterResult', 1)
  maliciousUriFilterResult = _messages.MessageField('MaliciousUriFilterResult', 2)
  piAndJailbreakFilterResult = _messages.MessageField('PiAndJailbreakFilterResult', 3)
  raiFilterResult = _messages.MessageField('RaiFilterResult', 4)
  sdpFilterResult = _messages.MessageField('SdpFilterResult', 5)
  virusScanFilterResult = _messages.MessageField('VirusScanFilterResult', 6)


class FloorSetting(_messages.Message):
  r"""Message describing FloorSetting resource

  Fields:
    createTime: Output only. [Output only] Create timestamp
    enableFloorSettingEnforcement: Optional. Floor Settings enforcement
      status.
    filterConfig: Required. ModelArmor filter configuration.
    floorSettingMetadata: Optional. Metadata for FloorSetting
    name: Identifier. The resource name.
    updateTime: Output only. [Output only] Update timestamp
  """

  createTime = _messages.StringField(1)
  enableFloorSettingEnforcement = _messages.BooleanField(2)
  filterConfig = _messages.MessageField('FilterConfig', 3)
  floorSettingMetadata = _messages.MessageField('FloorSettingMetadata', 4)
  name = _messages.StringField(5)
  updateTime = _messages.StringField(6)


class FloorSettingMetadata(_messages.Message):
  r"""message describing FloorSetting Metadata"""


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListTemplatesResponse(_messages.Message):
  r"""Message for response to listing Templates

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    templates: The list of Template
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  templates = _messages.MessageField('Template', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class MaliciousUriFilterResult(_messages.Message):
  r"""Malicious URI Filter Result.

  Enums:
    ExecutionStateValueValuesEnum: Output only. Reports whether Malicious URI
      filter was successfully executed or not.
    MatchStateValueValuesEnum: Output only. Match state for this Malicious
      URI. Value is MATCH_FOUND if at least one Malicious URI is found.

  Fields:
    executionState: Output only. Reports whether Malicious URI filter was
      successfully executed or not.
    maliciousUriMatchedItems: List of Malicious URIs found in data.
    matchState: Output only. Match state for this Malicious URI. Value is
      MATCH_FOUND if at least one Malicious URI is found.
    messageItems: Optional messages corresponding to the result. A message can
      provide warnings or error details. For example, if execution state is
      skipped then this field provides related reason/explanation.
  """

  class ExecutionStateValueValuesEnum(_messages.Enum):
    r"""Output only. Reports whether Malicious URI filter was successfully
    executed or not.

    Values:
      FILTER_EXECUTION_STATE_UNSPECIFIED: Unused
      EXECUTION_SUCCESS: Filter executed successfully
      EXECUTION_SKIPPED: Filter execution was skipped. This can happen due to
        server-side error or permission issue.
    """
    FILTER_EXECUTION_STATE_UNSPECIFIED = 0
    EXECUTION_SUCCESS = 1
    EXECUTION_SKIPPED = 2

  class MatchStateValueValuesEnum(_messages.Enum):
    r"""Output only. Match state for this Malicious URI. Value is MATCH_FOUND
    if at least one Malicious URI is found.

    Values:
      FILTER_MATCH_STATE_UNSPECIFIED: Unused
      NO_MATCH_FOUND: Matching criteria is not achieved for filters.
      MATCH_FOUND: Matching criteria is achieved for the filter.
    """
    FILTER_MATCH_STATE_UNSPECIFIED = 0
    NO_MATCH_FOUND = 1
    MATCH_FOUND = 2

  executionState = _messages.EnumField('ExecutionStateValueValuesEnum', 1)
  maliciousUriMatchedItems = _messages.MessageField('MaliciousUriMatchedItem', 2, repeated=True)
  matchState = _messages.EnumField('MatchStateValueValuesEnum', 3)
  messageItems = _messages.MessageField('MessageItem', 4, repeated=True)


class MaliciousUriFilterSettings(_messages.Message):
  r"""Malicious URI filter settings.

  Enums:
    FilterEnforcementValueValuesEnum: Optional. Tells whether the Malicious
      URI filter is enabled or disabled.

  Fields:
    filterEnforcement: Optional. Tells whether the Malicious URI filter is
      enabled or disabled.
  """

  class FilterEnforcementValueValuesEnum(_messages.Enum):
    r"""Optional. Tells whether the Malicious URI filter is enabled or
    disabled.

    Values:
      MALICIOUS_URI_FILTER_ENFORCEMENT_UNSPECIFIED: Same as Disabled
      ENABLED: Enabled
      DISABLED: Disabled
    """
    MALICIOUS_URI_FILTER_ENFORCEMENT_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  filterEnforcement = _messages.EnumField('FilterEnforcementValueValuesEnum', 1)


class MaliciousUriMatchedItem(_messages.Message):
  r"""Information regarding malicious URI and its location within the input
  content.

  Fields:
    locations: List of locations where Malicious URI is identified. The
      `locations` field is supported only for plaintext content i.e.
      ByteItemType.PLAINTEXT_UTF8
    uri: Malicious URI.
  """

  locations = _messages.MessageField('RangeInfo', 1, repeated=True)
  uri = _messages.StringField(2)


class MessageItem(_messages.Message):
  r"""Message item to report information, warning or error messages.

  Enums:
    MessageTypeValueValuesEnum: Type of message.

  Fields:
    message: The message content.
    messageType: Type of message.
  """

  class MessageTypeValueValuesEnum(_messages.Enum):
    r"""Type of message.

    Values:
      MESSAGE_TYPE_UNSPECIFIED: Unused
      INFO: Information related message.
      WARNING: Warning related message.
      ERROR: Error message.
    """
    MESSAGE_TYPE_UNSPECIFIED = 0
    INFO = 1
    WARNING = 2
    ERROR = 3

  message = _messages.StringField(1)
  messageType = _messages.EnumField('MessageTypeValueValuesEnum', 2)


class ModelarmorFoldersLocationsGetFloorSettingRequest(_messages.Message):
  r"""A ModelarmorFoldersLocationsGetFloorSettingRequest object.

  Fields:
    name: Required. The name of the floor setting to get, example
      projects/123/floorsetting.
  """

  name = _messages.StringField(1, required=True)


class ModelarmorFoldersLocationsUpdateFloorSettingRequest(_messages.Message):
  r"""A ModelarmorFoldersLocationsUpdateFloorSettingRequest object.

  Fields:
    floorSetting: A FloorSetting resource to be passed as the request body.
    name: Identifier. The resource name.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the FloorSetting resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  floorSetting = _messages.MessageField('FloorSetting', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ModelarmorOrganizationsLocationsGetFloorSettingRequest(_messages.Message):
  r"""A ModelarmorOrganizationsLocationsGetFloorSettingRequest object.

  Fields:
    name: Required. The name of the floor setting to get, example
      projects/123/floorsetting.
  """

  name = _messages.StringField(1, required=True)


class ModelarmorOrganizationsLocationsUpdateFloorSettingRequest(_messages.Message):
  r"""A ModelarmorOrganizationsLocationsUpdateFloorSettingRequest object.

  Fields:
    floorSetting: A FloorSetting resource to be passed as the request body.
    name: Identifier. The resource name.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the FloorSetting resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  floorSetting = _messages.MessageField('FloorSetting', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ModelarmorProjectsLocationsGetFloorSettingRequest(_messages.Message):
  r"""A ModelarmorProjectsLocationsGetFloorSettingRequest object.

  Fields:
    name: Required. The name of the floor setting to get, example
      projects/123/floorsetting.
  """

  name = _messages.StringField(1, required=True)


class ModelarmorProjectsLocationsGetRequest(_messages.Message):
  r"""A ModelarmorProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class ModelarmorProjectsLocationsListRequest(_messages.Message):
  r"""A ModelarmorProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class ModelarmorProjectsLocationsTemplatesCreateRequest(_messages.Message):
  r"""A ModelarmorProjectsLocationsTemplatesCreateRequest object.

  Fields:
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server stores the request ID for 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    template: A Template resource to be passed as the request body.
    templateId: Required. Id of the requesting object If auto-generating Id
      server-side, remove this field and template_id from the method_signature
      of Create RPC
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  template = _messages.MessageField('Template', 3)
  templateId = _messages.StringField(4)


class ModelarmorProjectsLocationsTemplatesDeleteRequest(_messages.Message):
  r"""A ModelarmorProjectsLocationsTemplatesDeleteRequest object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server stores the request ID for 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class ModelarmorProjectsLocationsTemplatesGetRequest(_messages.Message):
  r"""A ModelarmorProjectsLocationsTemplatesGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class ModelarmorProjectsLocationsTemplatesListRequest(_messages.Message):
  r"""A ModelarmorProjectsLocationsTemplatesListRequest object.

  Fields:
    filter: Optional. Filtering results
    orderBy: Optional. Hint for how to order the results
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListTemplatesRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ModelarmorProjectsLocationsTemplatesPatchRequest(_messages.Message):
  r"""A ModelarmorProjectsLocationsTemplatesPatchRequest object.

  Fields:
    name: Identifier. name of resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server stores the request ID for 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    template: A Template resource to be passed as the request body.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Template resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  template = _messages.MessageField('Template', 3)
  updateMask = _messages.StringField(4)


class ModelarmorProjectsLocationsTemplatesSanitizeModelResponseRequest(_messages.Message):
  r"""A ModelarmorProjectsLocationsTemplatesSanitizeModelResponseRequest
  object.

  Fields:
    name: Required. Represents resource name of template e.g.
      name=projects/sample-project/locations/us-central1/templates/templ01
    sanitizeModelResponseRequest: A SanitizeModelResponseRequest resource to
      be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  sanitizeModelResponseRequest = _messages.MessageField('SanitizeModelResponseRequest', 2)


class ModelarmorProjectsLocationsTemplatesSanitizeUserPromptRequest(_messages.Message):
  r"""A ModelarmorProjectsLocationsTemplatesSanitizeUserPromptRequest object.

  Fields:
    name: Required. Represents resource name of template e.g.
      name=projects/sample-project/locations/us-central1/templates/templ01
    sanitizeUserPromptRequest: A SanitizeUserPromptRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  sanitizeUserPromptRequest = _messages.MessageField('SanitizeUserPromptRequest', 2)


class ModelarmorProjectsLocationsUpdateFloorSettingRequest(_messages.Message):
  r"""A ModelarmorProjectsLocationsUpdateFloorSettingRequest object.

  Fields:
    floorSetting: A FloorSetting resource to be passed as the request body.
    name: Identifier. The resource name.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the FloorSetting resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  floorSetting = _messages.MessageField('FloorSetting', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class MultiLanguageDetection(_messages.Message):
  r"""Metadata to enable multi language detection via template.

  Fields:
    enableMultiLanguageDetection: Required. If true, multi language detection
      will be enabled.
  """

  enableMultiLanguageDetection = _messages.BooleanField(1)


class MultiLanguageDetectionMetadata(_messages.Message):
  r"""Message for Enabling Multi Language Detection.

  Fields:
    enableMultiLanguageDetection: Optional. Enable detection of multi-language
      prompts and responses.
    sourceLanguage: Optional. Optional Source language of the user prompt. If
      multi-language detection is enabled but language is not set in that case
      we would automatically detect the source language.
  """

  enableMultiLanguageDetection = _messages.BooleanField(1)
  sourceLanguage = _messages.StringField(2)


class PiAndJailbreakFilterResult(_messages.Message):
  r"""Prompt injection and Jailbreak Filter Result.

  Enums:
    ConfidenceLevelValueValuesEnum: Confidence level identified for Prompt
      injection and Jailbreak.
    ExecutionStateValueValuesEnum: Output only. Reports whether Prompt
      injection and Jailbreak filter was successfully executed or not.
    MatchStateValueValuesEnum: Output only. Match state for Prompt injection
      and Jailbreak.

  Fields:
    confidenceLevel: Confidence level identified for Prompt injection and
      Jailbreak.
    executionState: Output only. Reports whether Prompt injection and
      Jailbreak filter was successfully executed or not.
    matchState: Output only. Match state for Prompt injection and Jailbreak.
    messageItems: Optional messages corresponding to the result. A message can
      provide warnings or error details. For example, if execution state is
      skipped then this field provides related reason/explanation.
  """

  class ConfidenceLevelValueValuesEnum(_messages.Enum):
    r"""Confidence level identified for Prompt injection and Jailbreak.

    Values:
      DETECTION_CONFIDENCE_LEVEL_UNSPECIFIED: Same as LOW_AND_ABOVE.
      LOW_AND_ABOVE: Highest chance of a false positive.
      MEDIUM_AND_ABOVE: Some chance of false positives.
      HIGH: Low chance of false positives.
    """
    DETECTION_CONFIDENCE_LEVEL_UNSPECIFIED = 0
    LOW_AND_ABOVE = 1
    MEDIUM_AND_ABOVE = 2
    HIGH = 3

  class ExecutionStateValueValuesEnum(_messages.Enum):
    r"""Output only. Reports whether Prompt injection and Jailbreak filter was
    successfully executed or not.

    Values:
      FILTER_EXECUTION_STATE_UNSPECIFIED: Unused
      EXECUTION_SUCCESS: Filter executed successfully
      EXECUTION_SKIPPED: Filter execution was skipped. This can happen due to
        server-side error or permission issue.
    """
    FILTER_EXECUTION_STATE_UNSPECIFIED = 0
    EXECUTION_SUCCESS = 1
    EXECUTION_SKIPPED = 2

  class MatchStateValueValuesEnum(_messages.Enum):
    r"""Output only. Match state for Prompt injection and Jailbreak.

    Values:
      FILTER_MATCH_STATE_UNSPECIFIED: Unused
      NO_MATCH_FOUND: Matching criteria is not achieved for filters.
      MATCH_FOUND: Matching criteria is achieved for the filter.
    """
    FILTER_MATCH_STATE_UNSPECIFIED = 0
    NO_MATCH_FOUND = 1
    MATCH_FOUND = 2

  confidenceLevel = _messages.EnumField('ConfidenceLevelValueValuesEnum', 1)
  executionState = _messages.EnumField('ExecutionStateValueValuesEnum', 2)
  matchState = _messages.EnumField('MatchStateValueValuesEnum', 3)
  messageItems = _messages.MessageField('MessageItem', 4, repeated=True)


class PiAndJailbreakFilterSettings(_messages.Message):
  r"""Prompt injection and Jailbreak Filter settings.

  Enums:
    ConfidenceLevelValueValuesEnum: Optional. Confidence level for this
      filter. Confidence level is used to determine the threshold for the
      filter. If detection confidence is equal to or greater than the
      specified level, a positive match is reported. Confidence level will
      only be used if the filter is enabled.
    FilterEnforcementValueValuesEnum: Optional. Tells whether Prompt injection
      and Jailbreak filter is enabled or disabled.

  Fields:
    confidenceLevel: Optional. Confidence level for this filter. Confidence
      level is used to determine the threshold for the filter. If detection
      confidence is equal to or greater than the specified level, a positive
      match is reported. Confidence level will only be used if the filter is
      enabled.
    filterEnforcement: Optional. Tells whether Prompt injection and Jailbreak
      filter is enabled or disabled.
  """

  class ConfidenceLevelValueValuesEnum(_messages.Enum):
    r"""Optional. Confidence level for this filter. Confidence level is used
    to determine the threshold for the filter. If detection confidence is
    equal to or greater than the specified level, a positive match is
    reported. Confidence level will only be used if the filter is enabled.

    Values:
      DETECTION_CONFIDENCE_LEVEL_UNSPECIFIED: Same as LOW_AND_ABOVE.
      LOW_AND_ABOVE: Highest chance of a false positive.
      MEDIUM_AND_ABOVE: Some chance of false positives.
      HIGH: Low chance of false positives.
    """
    DETECTION_CONFIDENCE_LEVEL_UNSPECIFIED = 0
    LOW_AND_ABOVE = 1
    MEDIUM_AND_ABOVE = 2
    HIGH = 3

  class FilterEnforcementValueValuesEnum(_messages.Enum):
    r"""Optional. Tells whether Prompt injection and Jailbreak filter is
    enabled or disabled.

    Values:
      PI_AND_JAILBREAK_FILTER_ENFORCEMENT_UNSPECIFIED: Same as Disabled
      ENABLED: Enabled
      DISABLED: Enabled
    """
    PI_AND_JAILBREAK_FILTER_ENFORCEMENT_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  confidenceLevel = _messages.EnumField('ConfidenceLevelValueValuesEnum', 1)
  filterEnforcement = _messages.EnumField('FilterEnforcementValueValuesEnum', 2)


class RaiFilter(_messages.Message):
  r"""Responsible AI filter.

  Enums:
    ConfidenceLevelValueValuesEnum: Optional. Confidence level for this RAI
      filter. During data sanitization, if data is classified under this
      filter with a confidence level equal to or greater than the specified
      level, a positive match is reported. If the confidence level is
      unspecified (i.e., 0), the system will use a reasonable default level
      based on the `filter_type`.
    FilterTypeValueValuesEnum: Required. Type of responsible AI filter.

  Fields:
    confidenceLevel: Optional. Confidence level for this RAI filter. During
      data sanitization, if data is classified under this filter with a
      confidence level equal to or greater than the specified level, a
      positive match is reported. If the confidence level is unspecified
      (i.e., 0), the system will use a reasonable default level based on the
      `filter_type`.
    filterType: Required. Type of responsible AI filter.
  """

  class ConfidenceLevelValueValuesEnum(_messages.Enum):
    r"""Optional. Confidence level for this RAI filter. During data
    sanitization, if data is classified under this filter with a confidence
    level equal to or greater than the specified level, a positive match is
    reported. If the confidence level is unspecified (i.e., 0), the system
    will use a reasonable default level based on the `filter_type`.

    Values:
      DETECTION_CONFIDENCE_LEVEL_UNSPECIFIED: Same as LOW_AND_ABOVE.
      LOW_AND_ABOVE: Highest chance of a false positive.
      MEDIUM_AND_ABOVE: Some chance of false positives.
      HIGH: Low chance of false positives.
    """
    DETECTION_CONFIDENCE_LEVEL_UNSPECIFIED = 0
    LOW_AND_ABOVE = 1
    MEDIUM_AND_ABOVE = 2
    HIGH = 3

  class FilterTypeValueValuesEnum(_messages.Enum):
    r"""Required. Type of responsible AI filter.

    Values:
      RAI_FILTER_TYPE_UNSPECIFIED: Unspecified filter type.
      SEXUALLY_EXPLICIT: Sexually Explicit.
      HATE_SPEECH: Hate Speech.
      HARASSMENT: Harassment.
      DANGEROUS: Danger
    """
    RAI_FILTER_TYPE_UNSPECIFIED = 0
    SEXUALLY_EXPLICIT = 1
    HATE_SPEECH = 2
    HARASSMENT = 3
    DANGEROUS = 4

  confidenceLevel = _messages.EnumField('ConfidenceLevelValueValuesEnum', 1)
  filterType = _messages.EnumField('FilterTypeValueValuesEnum', 2)


class RaiFilterResult(_messages.Message):
  r"""Responsible AI Result.

  Enums:
    ExecutionStateValueValuesEnum: Output only. Reports whether the RAI filter
      was successfully executed or not.
    MatchStateValueValuesEnum: Output only. Overall filter match state for
      RAI. Value is MATCH_FOUND if at least one RAI filter confidence level is
      equal to or higher than the confidence level defined in configuration.

  Messages:
    RaiFilterTypeResultsValue: The map of RAI filter results where key is RAI
      filter type - either of "sexually_explicit", "hate_speech",
      "harassment", "dangerous".

  Fields:
    executionState: Output only. Reports whether the RAI filter was
      successfully executed or not.
    matchState: Output only. Overall filter match state for RAI. Value is
      MATCH_FOUND if at least one RAI filter confidence level is equal to or
      higher than the confidence level defined in configuration.
    messageItems: Optional messages corresponding to the result. A message can
      provide warnings or error details. For example, if execution state is
      skipped then this field provides related reason/explanation.
    raiFilterTypeResults: The map of RAI filter results where key is RAI
      filter type - either of "sexually_explicit", "hate_speech",
      "harassment", "dangerous".
  """

  class ExecutionStateValueValuesEnum(_messages.Enum):
    r"""Output only. Reports whether the RAI filter was successfully executed
    or not.

    Values:
      FILTER_EXECUTION_STATE_UNSPECIFIED: Unused
      EXECUTION_SUCCESS: Filter executed successfully
      EXECUTION_SKIPPED: Filter execution was skipped. This can happen due to
        server-side error or permission issue.
    """
    FILTER_EXECUTION_STATE_UNSPECIFIED = 0
    EXECUTION_SUCCESS = 1
    EXECUTION_SKIPPED = 2

  class MatchStateValueValuesEnum(_messages.Enum):
    r"""Output only. Overall filter match state for RAI. Value is MATCH_FOUND
    if at least one RAI filter confidence level is equal to or higher than the
    confidence level defined in configuration.

    Values:
      FILTER_MATCH_STATE_UNSPECIFIED: Unused
      NO_MATCH_FOUND: Matching criteria is not achieved for filters.
      MATCH_FOUND: Matching criteria is achieved for the filter.
    """
    FILTER_MATCH_STATE_UNSPECIFIED = 0
    NO_MATCH_FOUND = 1
    MATCH_FOUND = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class RaiFilterTypeResultsValue(_messages.Message):
    r"""The map of RAI filter results where key is RAI filter type - either of
    "sexually_explicit", "hate_speech", "harassment", "dangerous".

    Messages:
      AdditionalProperty: An additional property for a
        RaiFilterTypeResultsValue object.

    Fields:
      additionalProperties: Additional properties of type
        RaiFilterTypeResultsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a RaiFilterTypeResultsValue object.

      Fields:
        key: Name of the additional property.
        value: A RaiFilterTypeResult attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('RaiFilterTypeResult', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  executionState = _messages.EnumField('ExecutionStateValueValuesEnum', 1)
  matchState = _messages.EnumField('MatchStateValueValuesEnum', 2)
  messageItems = _messages.MessageField('MessageItem', 3, repeated=True)
  raiFilterTypeResults = _messages.MessageField('RaiFilterTypeResultsValue', 4)


class RaiFilterSettings(_messages.Message):
  r"""Responsible AI Filter settings.

  Fields:
    raiFilters: Required. List of Responsible AI filters enabled for template.
  """

  raiFilters = _messages.MessageField('RaiFilter', 1, repeated=True)


class RaiFilterTypeResult(_messages.Message):
  r"""Detailed Filter result for each of the responsible AI Filter Types.

  Enums:
    ConfidenceLevelValueValuesEnum: Confidence level identified for this RAI
      filter.
    FilterTypeValueValuesEnum: Type of responsible AI filter.
    MatchStateValueValuesEnum: Output only. Match state for this RAI filter.

  Fields:
    confidenceLevel: Confidence level identified for this RAI filter.
    filterType: Type of responsible AI filter.
    matchState: Output only. Match state for this RAI filter.
  """

  class ConfidenceLevelValueValuesEnum(_messages.Enum):
    r"""Confidence level identified for this RAI filter.

    Values:
      DETECTION_CONFIDENCE_LEVEL_UNSPECIFIED: Same as LOW_AND_ABOVE.
      LOW_AND_ABOVE: Highest chance of a false positive.
      MEDIUM_AND_ABOVE: Some chance of false positives.
      HIGH: Low chance of false positives.
    """
    DETECTION_CONFIDENCE_LEVEL_UNSPECIFIED = 0
    LOW_AND_ABOVE = 1
    MEDIUM_AND_ABOVE = 2
    HIGH = 3

  class FilterTypeValueValuesEnum(_messages.Enum):
    r"""Type of responsible AI filter.

    Values:
      RAI_FILTER_TYPE_UNSPECIFIED: Unspecified filter type.
      SEXUALLY_EXPLICIT: Sexually Explicit.
      HATE_SPEECH: Hate Speech.
      HARASSMENT: Harassment.
      DANGEROUS: Danger
    """
    RAI_FILTER_TYPE_UNSPECIFIED = 0
    SEXUALLY_EXPLICIT = 1
    HATE_SPEECH = 2
    HARASSMENT = 3
    DANGEROUS = 4

  class MatchStateValueValuesEnum(_messages.Enum):
    r"""Output only. Match state for this RAI filter.

    Values:
      FILTER_MATCH_STATE_UNSPECIFIED: Unused
      NO_MATCH_FOUND: Matching criteria is not achieved for filters.
      MATCH_FOUND: Matching criteria is achieved for the filter.
    """
    FILTER_MATCH_STATE_UNSPECIFIED = 0
    NO_MATCH_FOUND = 1
    MATCH_FOUND = 2

  confidenceLevel = _messages.EnumField('ConfidenceLevelValueValuesEnum', 1)
  filterType = _messages.EnumField('FilterTypeValueValuesEnum', 2)
  matchState = _messages.EnumField('MatchStateValueValuesEnum', 3)


class RangeInfo(_messages.Message):
  r"""Half-open range interval [start, end)

  Fields:
    end: Index of last character (exclusive).
    start: For proto3, value cannot be set to 0 unless the field is optional.
      Ref: https://protobuf.dev/programming-guides/proto3/#default Index of
      first character (inclusive).
  """

  end = _messages.IntegerField(1)
  start = _messages.IntegerField(2)


class SanitizationMetadata(_messages.Message):
  r"""Message describing Sanitization metadata.

  Fields:
    errorCode: Error code if any.
    errorMessage: Error message if any.
    ignorePartialInvocationFailures: Passthrough field defined in
      TemplateMetadata to indicate whether to ignore partial invocation
      failures.
  """

  errorCode = _messages.IntegerField(1)
  errorMessage = _messages.StringField(2)
  ignorePartialInvocationFailures = _messages.BooleanField(3)


class SanitizationResult(_messages.Message):
  r"""Sanitization result after applying all the filters on input content.

  Enums:
    FilterMatchStateValueValuesEnum: Output only. Overall filter match state
      for Sanitization. The state can have below two values. 1)
      NO_MATCH_FOUND: No filters in configuration satisfy matching criteria.
      In other words, input passed all filters. 2) MATCH_FOUND: At least one
      filter in configuration satisfies matching. In other words, input did
      not pass one or more filters.
    InvocationResultValueValuesEnum: Output only. A field indicating the
      outcome of the invocation, irrespective of match status. It can have the
      following three values: SUCCESS: All filters were executed successfully.
      PARTIAL: Some filters were skipped or failed execution. FAILURE: All
      filters were skipped or failed execution.

  Messages:
    FilterResultsValue: Output only. Results for all filters where the key is
      the filter name - either of "csam", "malicious_uris", "rai",
      "pi_and_jailbreak" ,"sdp".

  Fields:
    filterMatchState: Output only. Overall filter match state for
      Sanitization. The state can have below two values. 1) NO_MATCH_FOUND: No
      filters in configuration satisfy matching criteria. In other words,
      input passed all filters. 2) MATCH_FOUND: At least one filter in
      configuration satisfies matching. In other words, input did not pass one
      or more filters.
    filterResults: Output only. Results for all filters where the key is the
      filter name - either of "csam", "malicious_uris", "rai",
      "pi_and_jailbreak" ,"sdp".
    invocationResult: Output only. A field indicating the outcome of the
      invocation, irrespective of match status. It can have the following
      three values: SUCCESS: All filters were executed successfully. PARTIAL:
      Some filters were skipped or failed execution. FAILURE: All filters were
      skipped or failed execution.
    sanitizationMetadata: Output only. Metadata related to Sanitization.
  """

  class FilterMatchStateValueValuesEnum(_messages.Enum):
    r"""Output only. Overall filter match state for Sanitization. The state
    can have below two values. 1) NO_MATCH_FOUND: No filters in configuration
    satisfy matching criteria. In other words, input passed all filters. 2)
    MATCH_FOUND: At least one filter in configuration satisfies matching. In
    other words, input did not pass one or more filters.

    Values:
      FILTER_MATCH_STATE_UNSPECIFIED: Unused
      NO_MATCH_FOUND: Matching criteria is not achieved for filters.
      MATCH_FOUND: Matching criteria is achieved for the filter.
    """
    FILTER_MATCH_STATE_UNSPECIFIED = 0
    NO_MATCH_FOUND = 1
    MATCH_FOUND = 2

  class InvocationResultValueValuesEnum(_messages.Enum):
    r"""Output only. A field indicating the outcome of the invocation,
    irrespective of match status. It can have the following three values:
    SUCCESS: All filters were executed successfully. PARTIAL: Some filters
    were skipped or failed execution. FAILURE: All filters were skipped or
    failed execution.

    Values:
      INVOCATION_RESULT_UNSPECIFIED: Unused. Default value.
      SUCCESS: All filters were invoked successfully.
      PARTIAL: Some filters were skipped or failed.
      FAILURE: All filters were skipped or failed.
    """
    INVOCATION_RESULT_UNSPECIFIED = 0
    SUCCESS = 1
    PARTIAL = 2
    FAILURE = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FilterResultsValue(_messages.Message):
    r"""Output only. Results for all filters where the key is the filter name
    - either of "csam", "malicious_uris", "rai", "pi_and_jailbreak" ,"sdp".

    Messages:
      AdditionalProperty: An additional property for a FilterResultsValue
        object.

    Fields:
      additionalProperties: Additional properties of type FilterResultsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FilterResultsValue object.

      Fields:
        key: Name of the additional property.
        value: A FilterResult attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('FilterResult', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  filterMatchState = _messages.EnumField('FilterMatchStateValueValuesEnum', 1)
  filterResults = _messages.MessageField('FilterResultsValue', 2)
  invocationResult = _messages.EnumField('InvocationResultValueValuesEnum', 3)
  sanitizationMetadata = _messages.MessageField('SanitizationMetadata', 4)


class SanitizeModelResponseRequest(_messages.Message):
  r"""Sanitize Model Response request.

  Fields:
    modelResponseData: Required. Model response data to sanitize.
    multiLanguageDetectionMetadata: Optional. Metadata related for multi
      language detection.
    userPrompt: Optional. User Prompt associated with Model response.
  """

  modelResponseData = _messages.MessageField('DataItem', 1)
  multiLanguageDetectionMetadata = _messages.MessageField('MultiLanguageDetectionMetadata', 2)
  userPrompt = _messages.StringField(3)


class SanitizeModelResponseResponse(_messages.Message):
  r"""Sanitized Model Response Response.

  Fields:
    sanitizationResult: Output only. Sanitization Result.
  """

  sanitizationResult = _messages.MessageField('SanitizationResult', 1)


class SanitizeUserPromptRequest(_messages.Message):
  r"""Sanitize User Prompt request.

  Fields:
    multiLanguageDetectionMetadata: Optional. Metadata related to Multi
      Language Detection.
    userPromptData: Required. User prompt data to sanitize.
  """

  multiLanguageDetectionMetadata = _messages.MessageField('MultiLanguageDetectionMetadata', 1)
  userPromptData = _messages.MessageField('DataItem', 2)


class SanitizeUserPromptResponse(_messages.Message):
  r"""Sanitized User Prompt Response.

  Fields:
    sanitizationResult: Output only. Sanitization Result.
  """

  sanitizationResult = _messages.MessageField('SanitizationResult', 1)


class SdpAdvancedConfig(_messages.Message):
  r"""Sensitive Data Protection Advanced configuration.

  Fields:
    deidentifyTemplate: Optional. Optional Sensitive Data Protection
      Deidentify template resource name. If provided then DeidentifyContent
      action is performed during Sanitization using this template and inspect
      template. The De-identified data will be returned in
      SdpDeidentifyResult. Note that all info-types present in the deidentify
      template must be present in inspect template. e.g. `projects/{project}/l
      ocations/{location}/deidentifyTemplates/{deidentify_template}`
    inspectTemplate: Optional. Sensitive Data Protection inspect template
      resource name If only inspect template is provided (de-identify template
      not provided), then Sensitive Data Protection InspectContent action is
      performed during Sanitization. All Sensitive Data Protection findings
      identified during inspection will be returned as SdpFinding in
      SdpInsepctionResult. e.g. `projects/{project}/locations/{location}/inspe
      ctTemplates/{inspect_template}`
  """

  deidentifyTemplate = _messages.StringField(1)
  inspectTemplate = _messages.StringField(2)


class SdpBasicConfig(_messages.Message):
  r"""Sensitive Data Protection basic configuration.

  Enums:
    FilterEnforcementValueValuesEnum: Optional. Tells whether the Sensitive
      Data Protection basic config is enabled or disabled.

  Fields:
    filterEnforcement: Optional. Tells whether the Sensitive Data Protection
      basic config is enabled or disabled.
  """

  class FilterEnforcementValueValuesEnum(_messages.Enum):
    r"""Optional. Tells whether the Sensitive Data Protection basic config is
    enabled or disabled.

    Values:
      SDP_BASIC_CONFIG_ENFORCEMENT_UNSPECIFIED: Same as Disabled
      ENABLED: Enabled
      DISABLED: Disabled
    """
    SDP_BASIC_CONFIG_ENFORCEMENT_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  filterEnforcement = _messages.EnumField('FilterEnforcementValueValuesEnum', 1)


class SdpDeidentifyResult(_messages.Message):
  r"""Sensitive Data Protection Deidentification Result.

  Enums:
    ExecutionStateValueValuesEnum: Output only. Reports whether Sensitive Data
      Protection deidentification was successfully executed or not.
    MatchStateValueValuesEnum: Output only. Match state for Sensitive Data
      Protection Deidentification. Value is MATCH_FOUND if content is de-
      identified.

  Fields:
    data: De-identified data.
    executionState: Output only. Reports whether Sensitive Data Protection
      deidentification was successfully executed or not.
    infoTypes: List of Sensitive Data Protection info-types that were de-
      identified.
    matchState: Output only. Match state for Sensitive Data Protection
      Deidentification. Value is MATCH_FOUND if content is de-identified.
    messageItems: Optional messages corresponding to the result. A message can
      provide warnings or error details. For example, if execution state is
      skipped then this field provides related reason/explanation.
    transformedBytes: Total size in bytes that were transformed during
      deidentification.
  """

  class ExecutionStateValueValuesEnum(_messages.Enum):
    r"""Output only. Reports whether Sensitive Data Protection
    deidentification was successfully executed or not.

    Values:
      FILTER_EXECUTION_STATE_UNSPECIFIED: Unused
      EXECUTION_SUCCESS: Filter executed successfully
      EXECUTION_SKIPPED: Filter execution was skipped. This can happen due to
        server-side error or permission issue.
    """
    FILTER_EXECUTION_STATE_UNSPECIFIED = 0
    EXECUTION_SUCCESS = 1
    EXECUTION_SKIPPED = 2

  class MatchStateValueValuesEnum(_messages.Enum):
    r"""Output only. Match state for Sensitive Data Protection
    Deidentification. Value is MATCH_FOUND if content is de-identified.

    Values:
      FILTER_MATCH_STATE_UNSPECIFIED: Unused
      NO_MATCH_FOUND: Matching criteria is not achieved for filters.
      MATCH_FOUND: Matching criteria is achieved for the filter.
    """
    FILTER_MATCH_STATE_UNSPECIFIED = 0
    NO_MATCH_FOUND = 1
    MATCH_FOUND = 2

  data = _messages.MessageField('DataItem', 1)
  executionState = _messages.EnumField('ExecutionStateValueValuesEnum', 2)
  infoTypes = _messages.StringField(3, repeated=True)
  matchState = _messages.EnumField('MatchStateValueValuesEnum', 4)
  messageItems = _messages.MessageField('MessageItem', 5, repeated=True)
  transformedBytes = _messages.IntegerField(6)


class SdpFilterResult(_messages.Message):
  r"""Sensitive Data Protection filter result.

  Fields:
    deidentifyResult: Sensitive Data Protection Deidentification result if
      deidentification is performed.
    inspectResult: Sensitive Data Protection Inspection result if inspection
      is performed.
  """

  deidentifyResult = _messages.MessageField('SdpDeidentifyResult', 1)
  inspectResult = _messages.MessageField('SdpInspectResult', 2)


class SdpFilterSettings(_messages.Message):
  r"""Sensitive Data Protection settings.

  Fields:
    advancedConfig: Optional. Advanced Sensitive Data Protection configuration
      which enables use of Sensitive Data Protection templates. Supports both
      Sensitive Data Protection inspection and de-identification operations.
    basicConfig: Optional. Basic Sensitive Data Protection configuration
      inspects the content for sensitive data using a fixed set of six info-
      types. Sensitive Data Protection templates cannot be used with basic
      configuration. Only Sensitive Data Protection inspection operation is
      supported with basic configuration.
  """

  advancedConfig = _messages.MessageField('SdpAdvancedConfig', 1)
  basicConfig = _messages.MessageField('SdpBasicConfig', 2)


class SdpFinding(_messages.Message):
  r"""Finding corresponding to Sensitive Data Protection filter.

  Enums:
    LikelihoodValueValuesEnum: Identified confidence likelihood for
      `info_type`.

  Fields:
    infoType: Name of Sensitive Data Protection info type for this finding.
    likelihood: Identified confidence likelihood for `info_type`.
    location: Location for this finding.
  """

  class LikelihoodValueValuesEnum(_messages.Enum):
    r"""Identified confidence likelihood for `info_type`.

    Values:
      SDP_FINDING_LIKELIHOOD_UNSPECIFIED: Default value; same as POSSIBLE.
      VERY_UNLIKELY: Highest chance of a false positive.
      UNLIKELY: High chance of a false positive.
      POSSIBLE: Some matching signals. The default value.
      LIKELY: Low chance of a false positive.
      VERY_LIKELY: Confidence level is high. Lowest chance of a false
        positive.
    """
    SDP_FINDING_LIKELIHOOD_UNSPECIFIED = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  infoType = _messages.StringField(1)
  likelihood = _messages.EnumField('LikelihoodValueValuesEnum', 2)
  location = _messages.MessageField('SdpFindingLocation', 3)


class SdpFindingLocation(_messages.Message):
  r"""Location of this Sensitive Data Protection Finding within input content.

  Fields:
    byteRange: Zero-based byte offsets delimiting the finding. These are
      relative to the finding's containing element. Note that when the content
      is not textual, this references the UTF-8 encoded textual representation
      of the content.
    codepointRange: Unicode character offsets delimiting the finding. These
      are relative to the finding's containing element. Provided when the
      content is text.
  """

  byteRange = _messages.MessageField('RangeInfo', 1)
  codepointRange = _messages.MessageField('RangeInfo', 2)


class SdpInspectResult(_messages.Message):
  r"""Sensitive Data Protection Inspection Result.

  Enums:
    ExecutionStateValueValuesEnum: Output only. Reports whether Sensitive Data
      Protection inspection was successfully executed or not.
    MatchStateValueValuesEnum: Output only. Match state for SDP Inspection.
      Value is MATCH_FOUND if at least one Sensitive Data Protection finding
      is identified.

  Fields:
    executionState: Output only. Reports whether Sensitive Data Protection
      inspection was successfully executed or not.
    findings: List of Sensitive Data Protection findings.
    findingsTruncated: If true, then there is possibility that more findings
      were identified and the findings returned are a subset of all findings.
      The findings list might be truncated because the input items were too
      large, or because the server reached the maximum amount of resources
      allowed for a single API call.
    matchState: Output only. Match state for SDP Inspection. Value is
      MATCH_FOUND if at least one Sensitive Data Protection finding is
      identified.
    messageItems: Optional messages corresponding to the result. A message can
      provide warnings or error details. For example, if execution state is
      skipped then this field provides related reason/explanation.
  """

  class ExecutionStateValueValuesEnum(_messages.Enum):
    r"""Output only. Reports whether Sensitive Data Protection inspection was
    successfully executed or not.

    Values:
      FILTER_EXECUTION_STATE_UNSPECIFIED: Unused
      EXECUTION_SUCCESS: Filter executed successfully
      EXECUTION_SKIPPED: Filter execution was skipped. This can happen due to
        server-side error or permission issue.
    """
    FILTER_EXECUTION_STATE_UNSPECIFIED = 0
    EXECUTION_SUCCESS = 1
    EXECUTION_SKIPPED = 2

  class MatchStateValueValuesEnum(_messages.Enum):
    r"""Output only. Match state for SDP Inspection. Value is MATCH_FOUND if
    at least one Sensitive Data Protection finding is identified.

    Values:
      FILTER_MATCH_STATE_UNSPECIFIED: Unused
      NO_MATCH_FOUND: Matching criteria is not achieved for filters.
      MATCH_FOUND: Matching criteria is achieved for the filter.
    """
    FILTER_MATCH_STATE_UNSPECIFIED = 0
    NO_MATCH_FOUND = 1
    MATCH_FOUND = 2

  executionState = _messages.EnumField('ExecutionStateValueValuesEnum', 1)
  findings = _messages.MessageField('SdpFinding', 2, repeated=True)
  findingsTruncated = _messages.BooleanField(3)
  matchState = _messages.EnumField('MatchStateValueValuesEnum', 4)
  messageItems = _messages.MessageField('MessageItem', 5, repeated=True)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Template(_messages.Message):
  r"""Message describing Template resource

  Messages:
    LabelsValue: Optional. Labels as key value pairs

  Fields:
    createTime: Output only. [Output only] Create time stamp
    filterConfig: Required. filter configuration for this template
    labels: Optional. Labels as key value pairs
    name: Identifier. name of resource
    templateMetadata: Optional. metadata for this template
    updateTime: Output only. [Output only] Update time stamp
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  filterConfig = _messages.MessageField('FilterConfig', 2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  templateMetadata = _messages.MessageField('TemplateMetadata', 5)
  updateTime = _messages.StringField(6)


class TemplateMetadata(_messages.Message):
  r"""Message describing TemplateMetadata

  Enums:
    EnforcementTypeValueValuesEnum: Optional. Enforcement type for Model Armor
      filters.

  Fields:
    customLlmResponseSafetyErrorCode: Optional. Indicates the custom error
      code set by the user to be returned to the end user if the LLM response
      trips Model Armor filters.
    customLlmResponseSafetyErrorMessage: Optional. Indicates the custom error
      message set by the user to be returned to the end user if the LLM
      response trips Model Armor filters.
    customPromptSafetyErrorCode: Optional. Indicates the custom error code set
      by the user to be returned to the end user by the service extension if
      the prompt trips Model Armor filters.
    customPromptSafetyErrorMessage: Optional. Indicates the custom error
      message set by the user to be returned to the end user if the prompt
      trips Model Armor filters.
    enforcementType: Optional. Enforcement type for Model Armor filters.
    ignorePartialInvocationFailures: Optional. If true, partial detector
      failures should be ignored.
    logSanitizeOperations: Optional. If true, log sanitize operations.
    logTemplateOperations: Optional. If true, log template crud operations.
    multiLanguageDetection: Optional. Metadata for multi language detection.
  """

  class EnforcementTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Enforcement type for Model Armor filters.

    Values:
      ENFORCEMENT_TYPE_UNSPECIFIED: Default value. Same as INSPECT_AND_BLOCK.
      INSPECT_ONLY: Model Armor filters will run in inspect only mode. No
        action will be taken on the request.
      INSPECT_AND_BLOCK: Model Armor filters will run in inspect and block
        mode. Requests that trip Model Armor filters will be blocked.
    """
    ENFORCEMENT_TYPE_UNSPECIFIED = 0
    INSPECT_ONLY = 1
    INSPECT_AND_BLOCK = 2

  customLlmResponseSafetyErrorCode = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  customLlmResponseSafetyErrorMessage = _messages.StringField(2)
  customPromptSafetyErrorCode = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  customPromptSafetyErrorMessage = _messages.StringField(4)
  enforcementType = _messages.EnumField('EnforcementTypeValueValuesEnum', 5)
  ignorePartialInvocationFailures = _messages.BooleanField(6)
  logSanitizeOperations = _messages.BooleanField(7)
  logTemplateOperations = _messages.BooleanField(8)
  multiLanguageDetection = _messages.MessageField('MultiLanguageDetection', 9)


class VirusDetail(_messages.Message):
  r"""Details of an identified virus

  Enums:
    ThreatTypeValueValuesEnum: Threat type of the identified virus

  Fields:
    names: Names of this Virus.
    threatType: Threat type of the identified virus
    vendor: Name of vendor that produced this virus identification.
  """

  class ThreatTypeValueValuesEnum(_messages.Enum):
    r"""Threat type of the identified virus

    Values:
      THREAT_TYPE_UNSPECIFIED: Unused
      UNKNOWN: Unable to categorize threat
      VIRUS_OR_WORM: Virus or Worm threat.
      MALICIOUS_PROGRAM: Malicious program. E.g. Spyware, Trojan.
      POTENTIALLY_HARMFUL_CONTENT: Potentially harmful content. E.g. Injected
        code, Macro
      POTENTIALLY_UNWANTED_CONTENT: Potentially unwanted content. E.g. Adware.
    """
    THREAT_TYPE_UNSPECIFIED = 0
    UNKNOWN = 1
    VIRUS_OR_WORM = 2
    MALICIOUS_PROGRAM = 3
    POTENTIALLY_HARMFUL_CONTENT = 4
    POTENTIALLY_UNWANTED_CONTENT = 5

  names = _messages.StringField(1, repeated=True)
  threatType = _messages.EnumField('ThreatTypeValueValuesEnum', 2)
  vendor = _messages.StringField(3)


class VirusScanFilterResult(_messages.Message):
  r"""Virus scan results.

  Enums:
    ExecutionStateValueValuesEnum: Output only. Reports whether Virus Scan was
      successfully executed or not.
    MatchStateValueValuesEnum: Output only. Match status for Virus. Value is
      MATCH_FOUND if the data is infected with a virus.
    ScannedContentTypeValueValuesEnum: Type of content scanned.

  Fields:
    executionState: Output only. Reports whether Virus Scan was successfully
      executed or not.
    matchState: Output only. Match status for Virus. Value is MATCH_FOUND if
      the data is infected with a virus.
    messageItems: Optional messages corresponding to the result. A message can
      provide warnings or error details. For example, if execution status is
      skipped then this field provides related reason/explanation.
    scannedContentType: Type of content scanned.
    scannedSize: Size of scanned content in bytes.
    virusDetails: List of Viruses identified. This field will be empty if no
      virus was detected.
  """

  class ExecutionStateValueValuesEnum(_messages.Enum):
    r"""Output only. Reports whether Virus Scan was successfully executed or
    not.

    Values:
      FILTER_EXECUTION_STATE_UNSPECIFIED: Unused
      EXECUTION_SUCCESS: Filter executed successfully
      EXECUTION_SKIPPED: Filter execution was skipped. This can happen due to
        server-side error or permission issue.
    """
    FILTER_EXECUTION_STATE_UNSPECIFIED = 0
    EXECUTION_SUCCESS = 1
    EXECUTION_SKIPPED = 2

  class MatchStateValueValuesEnum(_messages.Enum):
    r"""Output only. Match status for Virus. Value is MATCH_FOUND if the data
    is infected with a virus.

    Values:
      FILTER_MATCH_STATE_UNSPECIFIED: Unused
      NO_MATCH_FOUND: Matching criteria is not achieved for filters.
      MATCH_FOUND: Matching criteria is achieved for the filter.
    """
    FILTER_MATCH_STATE_UNSPECIFIED = 0
    NO_MATCH_FOUND = 1
    MATCH_FOUND = 2

  class ScannedContentTypeValueValuesEnum(_messages.Enum):
    r"""Type of content scanned.

    Values:
      SCANNED_CONTENT_TYPE_UNSPECIFIED: Unused
      UNKNOWN: Unknown content
      PLAINTEXT: Plaintext
      PDF: PDF Scanning for only PDF is supported.
    """
    SCANNED_CONTENT_TYPE_UNSPECIFIED = 0
    UNKNOWN = 1
    PLAINTEXT = 2
    PDF = 3

  executionState = _messages.EnumField('ExecutionStateValueValuesEnum', 1)
  matchState = _messages.EnumField('MatchStateValueValuesEnum', 2)
  messageItems = _messages.MessageField('MessageItem', 3, repeated=True)
  scannedContentType = _messages.EnumField('ScannedContentTypeValueValuesEnum', 4)
  scannedSize = _messages.IntegerField(5)
  virusDetails = _messages.MessageField('VirusDetail', 6, repeated=True)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
