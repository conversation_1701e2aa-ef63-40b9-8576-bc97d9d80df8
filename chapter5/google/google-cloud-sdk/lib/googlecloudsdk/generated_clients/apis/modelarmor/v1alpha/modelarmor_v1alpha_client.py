"""Generated client library for modelarmor version v1alpha."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.modelarmor.v1alpha import modelarmor_v1alpha_messages as messages


class ModelarmorV1alpha(base_api.BaseApiClient):
  """Generated client library for service modelarmor version v1alpha."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://modelarmor.us.rep.googleapis.com/'
  MTLS_BASE_URL = 'https://modelarmor.us.rep.mtls.googleapis.com/'

  _PACKAGE = 'modelarmor'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1alpha'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'ModelarmorV1alpha'
  _URL_VERSION = 'v1alpha'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new modelarmor handle."""
    url = url or self.BASE_URL
    super(ModelarmorV1alpha, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.folders_locations = self.FoldersLocationsService(self)
    self.folders = self.FoldersService(self)
    self.organizations_locations = self.OrganizationsLocationsService(self)
    self.organizations = self.OrganizationsService(self)
    self.projects_locations_templates = self.ProjectsLocationsTemplatesService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class FoldersLocationsService(base_api.BaseApiService):
    """Service class for the folders_locations resource."""

    _NAME = 'folders_locations'

    def __init__(self, client):
      super(ModelarmorV1alpha.FoldersLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def GetFloorSetting(self, request, global_params=None):
      r"""Gets details of a single floor setting of a project.

      Args:
        request: (ModelarmorFoldersLocationsGetFloorSettingRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FloorSetting) The response message.
      """
      config = self.GetMethodConfig('GetFloorSetting')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetFloorSetting.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/floorSetting',
        http_method='GET',
        method_id='modelarmor.folders.locations.getFloorSetting',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='ModelarmorFoldersLocationsGetFloorSettingRequest',
        response_type_name='FloorSetting',
        supports_download=False,
    )

    def UpdateFloorSetting(self, request, global_params=None):
      r"""Updates the parameters of a single floor setting of a project.

      Args:
        request: (ModelarmorFoldersLocationsUpdateFloorSettingRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FloorSetting) The response message.
      """
      config = self.GetMethodConfig('UpdateFloorSetting')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateFloorSetting.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/folders/{foldersId}/locations/{locationsId}/floorSetting',
        http_method='PATCH',
        method_id='modelarmor.folders.locations.updateFloorSetting',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='floorSetting',
        request_type_name='ModelarmorFoldersLocationsUpdateFloorSettingRequest',
        response_type_name='FloorSetting',
        supports_download=False,
    )

  class FoldersService(base_api.BaseApiService):
    """Service class for the folders resource."""

    _NAME = 'folders'

    def __init__(self, client):
      super(ModelarmorV1alpha.FoldersService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsLocationsService(base_api.BaseApiService):
    """Service class for the organizations_locations resource."""

    _NAME = 'organizations_locations'

    def __init__(self, client):
      super(ModelarmorV1alpha.OrganizationsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def GetFloorSetting(self, request, global_params=None):
      r"""Gets details of a single floor setting of a project.

      Args:
        request: (ModelarmorOrganizationsLocationsGetFloorSettingRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FloorSetting) The response message.
      """
      config = self.GetMethodConfig('GetFloorSetting')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetFloorSetting.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/floorSetting',
        http_method='GET',
        method_id='modelarmor.organizations.locations.getFloorSetting',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='ModelarmorOrganizationsLocationsGetFloorSettingRequest',
        response_type_name='FloorSetting',
        supports_download=False,
    )

    def UpdateFloorSetting(self, request, global_params=None):
      r"""Updates the parameters of a single floor setting of a project.

      Args:
        request: (ModelarmorOrganizationsLocationsUpdateFloorSettingRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FloorSetting) The response message.
      """
      config = self.GetMethodConfig('UpdateFloorSetting')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateFloorSetting.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/organizations/{organizationsId}/locations/{locationsId}/floorSetting',
        http_method='PATCH',
        method_id='modelarmor.organizations.locations.updateFloorSetting',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='floorSetting',
        request_type_name='ModelarmorOrganizationsLocationsUpdateFloorSettingRequest',
        response_type_name='FloorSetting',
        supports_download=False,
    )

  class OrganizationsService(base_api.BaseApiService):
    """Service class for the organizations resource."""

    _NAME = 'organizations'

    def __init__(self, client):
      super(ModelarmorV1alpha.OrganizationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsTemplatesService(base_api.BaseApiService):
    """Service class for the projects_locations_templates resource."""

    _NAME = 'projects_locations_templates'

    def __init__(self, client):
      super(ModelarmorV1alpha.ProjectsLocationsTemplatesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Template in a given project and location.

      Args:
        request: (ModelarmorProjectsLocationsTemplatesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Template) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/templates',
        http_method='POST',
        method_id='modelarmor.projects.locations.templates.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'templateId'],
        relative_path='v1alpha/{+parent}/templates',
        request_field='template',
        request_type_name='ModelarmorProjectsLocationsTemplatesCreateRequest',
        response_type_name='Template',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Template.

      Args:
        request: (ModelarmorProjectsLocationsTemplatesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/templates/{templatesId}',
        http_method='DELETE',
        method_id='modelarmor.projects.locations.templates.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='ModelarmorProjectsLocationsTemplatesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Template.

      Args:
        request: (ModelarmorProjectsLocationsTemplatesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Template) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/templates/{templatesId}',
        http_method='GET',
        method_id='modelarmor.projects.locations.templates.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='ModelarmorProjectsLocationsTemplatesGetRequest',
        response_type_name='Template',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Templates in a given project and location.

      Args:
        request: (ModelarmorProjectsLocationsTemplatesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListTemplatesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/templates',
        http_method='GET',
        method_id='modelarmor.projects.locations.templates.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/templates',
        request_field='',
        request_type_name='ModelarmorProjectsLocationsTemplatesListRequest',
        response_type_name='ListTemplatesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Template.

      Args:
        request: (ModelarmorProjectsLocationsTemplatesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Template) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/templates/{templatesId}',
        http_method='PATCH',
        method_id='modelarmor.projects.locations.templates.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='template',
        request_type_name='ModelarmorProjectsLocationsTemplatesPatchRequest',
        response_type_name='Template',
        supports_download=False,
    )

    def SanitizeModelResponse(self, request, global_params=None):
      r"""Sanitizes Model Response.

      Args:
        request: (ModelarmorProjectsLocationsTemplatesSanitizeModelResponseRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SanitizeModelResponseResponse) The response message.
      """
      config = self.GetMethodConfig('SanitizeModelResponse')
      return self._RunMethod(
          config, request, global_params=global_params)

    SanitizeModelResponse.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/templates/{templatesId}:sanitizeModelResponse',
        http_method='POST',
        method_id='modelarmor.projects.locations.templates.sanitizeModelResponse',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:sanitizeModelResponse',
        request_field='sanitizeModelResponseRequest',
        request_type_name='ModelarmorProjectsLocationsTemplatesSanitizeModelResponseRequest',
        response_type_name='SanitizeModelResponseResponse',
        supports_download=False,
    )

    def SanitizeUserPrompt(self, request, global_params=None):
      r"""Sanitizes User Prompt.

      Args:
        request: (ModelarmorProjectsLocationsTemplatesSanitizeUserPromptRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SanitizeUserPromptResponse) The response message.
      """
      config = self.GetMethodConfig('SanitizeUserPrompt')
      return self._RunMethod(
          config, request, global_params=global_params)

    SanitizeUserPrompt.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/templates/{templatesId}:sanitizeUserPrompt',
        http_method='POST',
        method_id='modelarmor.projects.locations.templates.sanitizeUserPrompt',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:sanitizeUserPrompt',
        request_field='sanitizeUserPromptRequest',
        request_type_name='ModelarmorProjectsLocationsTemplatesSanitizeUserPromptRequest',
        response_type_name='SanitizeUserPromptResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(ModelarmorV1alpha.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (ModelarmorProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='modelarmor.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='ModelarmorProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def GetFloorSetting(self, request, global_params=None):
      r"""Gets details of a single floor setting of a project.

      Args:
        request: (ModelarmorProjectsLocationsGetFloorSettingRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FloorSetting) The response message.
      """
      config = self.GetMethodConfig('GetFloorSetting')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetFloorSetting.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/floorSetting',
        http_method='GET',
        method_id='modelarmor.projects.locations.getFloorSetting',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='ModelarmorProjectsLocationsGetFloorSettingRequest',
        response_type_name='FloorSetting',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (ModelarmorProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations',
        http_method='GET',
        method_id='modelarmor.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+name}/locations',
        request_field='',
        request_type_name='ModelarmorProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

    def UpdateFloorSetting(self, request, global_params=None):
      r"""Updates the parameters of a single floor setting of a project.

      Args:
        request: (ModelarmorProjectsLocationsUpdateFloorSettingRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FloorSetting) The response message.
      """
      config = self.GetMethodConfig('UpdateFloorSetting')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateFloorSetting.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/floorSetting',
        http_method='PATCH',
        method_id='modelarmor.projects.locations.updateFloorSetting',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='floorSetting',
        request_type_name='ModelarmorProjectsLocationsUpdateFloorSettingRequest',
        response_type_name='FloorSetting',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(ModelarmorV1alpha.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
