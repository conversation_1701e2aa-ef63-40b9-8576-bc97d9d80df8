"""Generated client library for gkehub version v2alpha."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.gkehub.v2alpha import gkehub_v2alpha_messages as messages


class GkehubV2alpha(base_api.BaseApiClient):
  """Generated client library for service gkehub version v2alpha."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://gkehub.googleapis.com/'
  MTLS_BASE_URL = 'https://gkehub.mtls.googleapis.com/'

  _PACKAGE = 'gkehub'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v2alpha'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'GkehubV2alpha'
  _URL_VERSION = 'v2alpha'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new gkehub handle."""
    url = url or self.BASE_URL
    super(GkehubV2alpha, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_featureConfigs = self.ProjectsLocationsFeatureConfigsService(self)
    self.projects_locations_memberships_features = self.ProjectsLocationsMembershipsFeaturesService(self)
    self.projects_locations_memberships = self.ProjectsLocationsMembershipsService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsFeatureConfigsService(base_api.BaseApiService):
    """Service class for the projects_locations_featureConfigs resource."""

    _NAME = 'projects_locations_featureConfigs'

    def __init__(self, client):
      super(GkehubV2alpha.ProjectsLocationsFeatureConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates FeatureConfig under a given parent.

      Args:
        request: (GkehubProjectsLocationsFeatureConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/projects/{projectsId}/locations/{locationsId}/featureConfigs',
        http_method='POST',
        method_id='gkehub.projects.locations.featureConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['featureConfigId', 'requestId'],
        relative_path='v2alpha/{+parent}/featureConfigs',
        request_field='featureConfig',
        request_type_name='GkehubProjectsLocationsFeatureConfigsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Removes a FeatureConfig.

      Args:
        request: (GkehubProjectsLocationsFeatureConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/projects/{projectsId}/locations/{locationsId}/featureConfigs/{featureConfigsId}',
        http_method='DELETE',
        method_id='gkehub.projects.locations.featureConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v2alpha/{+name}',
        request_field='',
        request_type_name='GkehubProjectsLocationsFeatureConfigsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""========= FeatureConfig Services ========= Gets details of a featureConfig.

      Args:
        request: (GkehubProjectsLocationsFeatureConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FeatureConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/projects/{projectsId}/locations/{locationsId}/featureConfigs/{featureConfigsId}',
        http_method='GET',
        method_id='gkehub.projects.locations.featureConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2alpha/{+name}',
        request_field='',
        request_type_name='GkehubProjectsLocationsFeatureConfigsGetRequest',
        response_type_name='FeatureConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all FeatureConfigs in a given project and location.

      Args:
        request: (GkehubProjectsLocationsFeatureConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFeatureConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/projects/{projectsId}/locations/{locationsId}/featureConfigs',
        http_method='GET',
        method_id='gkehub.projects.locations.featureConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2alpha/{+parent}/featureConfigs',
        request_field='',
        request_type_name='GkehubProjectsLocationsFeatureConfigsListRequest',
        response_type_name='ListFeatureConfigsResponse',
        supports_download=False,
    )

  class ProjectsLocationsMembershipsFeaturesService(base_api.BaseApiService):
    """Service class for the projects_locations_memberships_features resource."""

    _NAME = 'projects_locations_memberships_features'

    def __init__(self, client):
      super(GkehubV2alpha.ProjectsLocationsMembershipsFeaturesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates membershipFeature under a given parent.

      Args:
        request: (GkehubProjectsLocationsMembershipsFeaturesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/projects/{projectsId}/locations/{locationsId}/memberships/{membershipsId}/features',
        http_method='POST',
        method_id='gkehub.projects.locations.memberships.features.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['featureId', 'requestId'],
        relative_path='v2alpha/{+parent}/features',
        request_field='membershipFeature',
        request_type_name='GkehubProjectsLocationsMembershipsFeaturesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Removes a membershipFeature.

      Args:
        request: (GkehubProjectsLocationsMembershipsFeaturesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/projects/{projectsId}/locations/{locationsId}/memberships/{membershipsId}/features/{featuresId}',
        http_method='DELETE',
        method_id='gkehub.projects.locations.memberships.features.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v2alpha/{+name}',
        request_field='',
        request_type_name='GkehubProjectsLocationsMembershipsFeaturesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""========= MembershipFeature Services ========= Gets details of a membershipFeature.

      Args:
        request: (GkehubProjectsLocationsMembershipsFeaturesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MembershipFeature) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/projects/{projectsId}/locations/{locationsId}/memberships/{membershipsId}/features/{featuresId}',
        http_method='GET',
        method_id='gkehub.projects.locations.memberships.features.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2alpha/{+name}',
        request_field='',
        request_type_name='GkehubProjectsLocationsMembershipsFeaturesGetRequest',
        response_type_name='MembershipFeature',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists MembershipFeatures in a given project and location.

      Args:
        request: (GkehubProjectsLocationsMembershipsFeaturesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMembershipFeaturesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/projects/{projectsId}/locations/{locationsId}/memberships/{membershipsId}/features',
        http_method='GET',
        method_id='gkehub.projects.locations.memberships.features.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2alpha/{+parent}/features',
        request_field='',
        request_type_name='GkehubProjectsLocationsMembershipsFeaturesListRequest',
        response_type_name='ListMembershipFeaturesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing MembershipFeature.

      Args:
        request: (GkehubProjectsLocationsMembershipsFeaturesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/projects/{projectsId}/locations/{locationsId}/memberships/{membershipsId}/features/{featuresId}',
        http_method='PATCH',
        method_id='gkehub.projects.locations.memberships.features.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'requestId', 'updateMask'],
        relative_path='v2alpha/{+name}',
        request_field='membershipFeature',
        request_type_name='GkehubProjectsLocationsMembershipsFeaturesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsMembershipsService(base_api.BaseApiService):
    """Service class for the projects_locations_memberships resource."""

    _NAME = 'projects_locations_memberships'

    def __init__(self, client):
      super(GkehubV2alpha.ProjectsLocationsMembershipsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(GkehubV2alpha.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (GkehubProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='gkehub.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2alpha/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='GkehubProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (GkehubProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='gkehub.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2alpha/{+name}',
        request_field='',
        request_type_name='GkehubProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (GkehubProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='gkehub.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v2alpha/{+name}/operations',
        request_field='',
        request_type_name='GkehubProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(GkehubV2alpha.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (GkehubProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='gkehub.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2alpha/{+name}',
        request_field='',
        request_type_name='GkehubProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (GkehubProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2alpha/projects/{projectsId}/locations',
        http_method='GET',
        method_id='gkehub.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'includeUnrevealedLocations', 'pageSize', 'pageToken'],
        relative_path='v2alpha/{+name}/locations',
        request_field='',
        request_type_name='GkehubProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(GkehubV2alpha.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
