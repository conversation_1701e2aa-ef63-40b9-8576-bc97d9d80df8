"""Generated message classes for gkehub version v2.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'gkehub'


class AppDevExperienceState(_messages.Message):
  r"""State for App Dev Exp Feature.

  Fields:
    networkingInstallSucceeded: Status of subcomponent that detects configured
      Service Mesh resources.
  """

  networkingInstallSucceeded = _messages.MessageField('AppDevExperienceStatus', 1)


class AppDevExperienceStatus(_messages.Message):
  r"""Status specifies state for the subcomponent.

  Enums:
    CodeValueValuesEnum: Code specifies AppDevExperienceFeature's subcomponent
      ready state.

  Fields:
    code: Code specifies AppDevExperienceFeature's subcomponent ready state.
    description: Description is populated if Code is Failed, explaining why it
      has failed.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Code specifies AppDevExperienceFeature's subcomponent ready state.

    Values:
      CODE_UNSPECIFIED: Not set.
      OK: AppDevExperienceFeature's specified subcomponent is ready.
      FAILED: AppDevExperienceFeature's specified subcomponent ready state is
        false. This means AppDevExperienceFeature has encountered an issue
        that blocks all, or a portion, of its normal operation. See the
        `description` for more details.
      UNKNOWN: AppDevExperienceFeature's specified subcomponent has a pending
        or unknown state.
    """
    CODE_UNSPECIFIED = 0
    OK = 1
    FAILED = 2
    UNKNOWN = 3

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  description = _messages.StringField(2)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CloudBuildSpec(_messages.Message):
  r"""**Cloud Build**: Configurations for each Cloud Build enabled cluster.

  Enums:
    SecurityPolicyValueValuesEnum: Whether it is allowed to run the privileged
      builds on the cluster or not.

  Fields:
    securityPolicy: Whether it is allowed to run the privileged builds on the
      cluster or not.
    version: Version of the cloud build software on the cluster.
  """

  class SecurityPolicyValueValuesEnum(_messages.Enum):
    r"""Whether it is allowed to run the privileged builds on the cluster or
    not.

    Values:
      SECURITY_POLICY_UNSPECIFIED: Unspecified policy
      NON_PRIVILEGED: Privileged build pods are disallowed
      PRIVILEGED: Privileged build pods are allowed
    """
    SECURITY_POLICY_UNSPECIFIED = 0
    NON_PRIVILEGED = 1
    PRIVILEGED = 2

  securityPolicy = _messages.EnumField('SecurityPolicyValueValuesEnum', 1)
  version = _messages.StringField(2)


class ClusterUpgradeGKEUpgrade(_messages.Message):
  r"""GKEUpgrade represents a GKE provided upgrade, e.g., control plane
  upgrade.

  Fields:
    name: Name of the upgrade, e.g., "k8s_control_plane".
    version: Version of the upgrade, e.g., "1.22.1-gke.100".
  """

  name = _messages.StringField(1)
  version = _messages.StringField(2)


class ClusterUpgradeIgnoredMembership(_messages.Message):
  r"""IgnoredMembership represents a membership ignored by the feature. A
  membership can be ignored because it was manually upgraded to a newer
  version than RC default.

  Fields:
    ignoredTime: Time when the membership was first set to ignored.
    reason: Reason why the membership is ignored.
  """

  ignoredTime = _messages.StringField(1)
  reason = _messages.StringField(2)


class ClusterUpgradeMembershipGKEUpgradeState(_messages.Message):
  r"""MembershipGKEUpgradeState is a GKEUpgrade and its state per-membership.

  Fields:
    status: Status of the upgrade.
    upgrade: Which upgrade to track the state.
  """

  status = _messages.MessageField('ClusterUpgradeUpgradeStatus', 1)
  upgrade = _messages.MessageField('ClusterUpgradeGKEUpgrade', 2)


class ClusterUpgradeState(_messages.Message):
  r"""Per-membership state for this feature.

  Fields:
    ignored: Whether this membership is ignored by the feature. For example,
      manually upgraded clusters can be ignored if they are newer than the
      default versions of its release channel.
    upgrades: Actual upgrade state against desired.
  """

  ignored = _messages.MessageField('ClusterUpgradeIgnoredMembership', 1)
  upgrades = _messages.MessageField('ClusterUpgradeMembershipGKEUpgradeState', 2, repeated=True)


class ClusterUpgradeUpgradeStatus(_messages.Message):
  r"""UpgradeStatus provides status information for each upgrade.

  Enums:
    CodeValueValuesEnum: Status code of the upgrade.
    TypeValueValuesEnum: Type of the status.

  Fields:
    code: Status code of the upgrade.
    reason: Reason for this status.
    type: Type of the status.
    updateTime: Last timestamp the status was updated.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Status code of the upgrade.

    Values:
      CODE_UNSPECIFIED: Required by https://linter.aip.dev/126/unspecified.
      INELIGIBLE: The upgrade is ineligible. At the scope level, this means
        the upgrade is ineligible for all the clusters in the scope.
      PENDING: The upgrade is pending. At the scope level, this means the
        upgrade is pending for all the clusters in the scope.
      IN_PROGRESS: The upgrade is in progress. At the scope level, this means
        the upgrade is in progress for at least one cluster in the scope.
      SOAKING: The upgrade has finished and is soaking until the soaking time
        is up. At the scope level, this means at least one cluster is in
        soaking while the rest are either soaking or complete.
      FORCED_SOAKING: A cluster will be forced to enter soaking if an upgrade
        doesn't finish within a certain limit, despite it's actual status.
      COMPLETE: The upgrade has passed all post conditions (soaking). At the
        scope level, this means all eligible clusters are in COMPLETE status.
      PAUSED: The upgrade is paused. At the scope level, this means the
        upgrade is paused for all the clusters in the scope.
      FORCED_COMPLETE: The upgrade was forced into soaking and the soaking
        time has passed. This is the equivalent of COMPLETE status for
        upgrades that were forced into soaking.
    """
    CODE_UNSPECIFIED = 0
    INELIGIBLE = 1
    PENDING = 2
    IN_PROGRESS = 3
    SOAKING = 4
    FORCED_SOAKING = 5
    COMPLETE = 6
    PAUSED = 7
    FORCED_COMPLETE = 8

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of the status.

    Values:
      TYPE_UNSPECIFIED: Required by https://linter.aip.dev/126/unspecified.
      DISRUPTION_BUDGET: The upgrade is PAUSED due to the cluster's disruption
        budget. Cluster is out of disruption budget. Once the cluster is back
        in budget, the upgrade will resume.
      MAINTENANCE_POLICY: The upgrade is PAUSED due to the cluster's
        maintenance policy. The upgrade will resume once cluster's maintenance
        window is open and/or maintenance exclusion is over.
      SYSTEM_CONFIG: The upgrade is PAUSED due to the system config.
      CLUSTER_STATUS: The upgrade is INELIGIBLE due to the cluster's status.
      INCOMPATIBLE_VERSION: The upgrade is INELIGIBLE due to the cluster's
        current version being incompatible with the target version.
      DISABLED_BY_USER: The upgrade is INELIGIBLE due to the user disabling
        auto upgrades. Applies to node upgrades only.
    """
    TYPE_UNSPECIFIED = 0
    DISRUPTION_BUDGET = 1
    MAINTENANCE_POLICY = 2
    SYSTEM_CONFIG = 3
    CLUSTER_STATUS = 4
    INCOMPATIBLE_VERSION = 5
    DISABLED_BY_USER = 6

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  reason = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)
  updateTime = _messages.StringField(4)


class ConfigDeliveryArgoCDCondition(_messages.Message):
  r"""Condition contains details for one aspect of the current state of the
  reconciliation object.

  Enums:
    StatusValueValuesEnum: status of the condition, one of True, False,
      Unknown.
    TypeValueValuesEnum: type of condition in CamelCase.

  Fields:
    lastTransitionTime: lastTransitionTime is the last time the condition
      transitioned from one status to another
    message: message is a human readable message indicating details about the
      transition. This may be an empty string.
    reason: reason contains a programmatic identifier indicating the reason
      for the condition's last transition.
    status: status of the condition, one of True, False, Unknown.
    type: type of condition in CamelCase.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""status of the condition, one of True, False, Unknown.

    Values:
      CONDITION_STATUS_UNSPECIFIED: CONDITION_STATUS_UNSPECIFIED is the
        default unspecified conditionStatus.
      TRUE: TRUE means a resource is in the condition.
      FALSE: FALSE means a resource is not in the condition.
      UNKNOWN: UNKNOWN means kubernetes can't decide if a resource is in the
        condition or not.
    """
    CONDITION_STATUS_UNSPECIFIED = 0
    TRUE = 1
    FALSE = 2
    UNKNOWN = 3

  class TypeValueValuesEnum(_messages.Enum):
    r"""type of condition in CamelCase.

    Values:
      CONDITION_TYPE_UNSPECIFIED: CONDITION_TYPE_UNSPECIFIED is the default
        unspecified conditionType.
      READY: READY indicates the type of the configdeliveryargocd' status
        condtion is "READY". This is a normally used term in k8s which used as
        a specific "conditionType". The "conditionStatus" tells the value of
        "READY" (e.g. conditionStatus=true means not ready).
    """
    CONDITION_TYPE_UNSPECIFIED = 0
    READY = 1

  lastTransitionTime = _messages.StringField(1)
  message = _messages.StringField(2)
  reason = _messages.StringField(3)
  status = _messages.EnumField('StatusValueValuesEnum', 4)
  type = _messages.EnumField('TypeValueValuesEnum', 5)


class ConfigDeliveryArgoCDDeclarativeState(_messages.Message):
  r"""DeclarativeState summaries the state of all the deployable manifests.

  Fields:
    conditions: conditions provides a standard mechanism for higher-level
      status reporting from the moss reconciler.
    healthy: healthy tells whether the current state is healthy or not.
    version: version is the current in-use ArgoCD version. Users can only
      specify the channel and the margo/populas operator will decide which
      version is actually being used.
  """

  conditions = _messages.MessageField('ConfigDeliveryArgoCDCondition', 1, repeated=True)
  healthy = _messages.BooleanField(2)
  version = _messages.StringField(3)


class ConfigDeliveryArgoCDSpec(_messages.Message):
  r"""Spec defines the ConfigDeliveryArgoCD Feature specification.

  Enums:
    ChannelValueValuesEnum: Channel specifies a channel that can be used to
      resolve a specific addon. Margo will use the same release channel as the
      current cluster.

  Fields:
    channel: Channel specifies a channel that can be used to resolve a
      specific addon. Margo will use the same release channel as the current
      cluster.
    version: Version specifies the expected ArgoCD version to manage.
  """

  class ChannelValueValuesEnum(_messages.Enum):
    r"""Channel specifies a channel that can be used to resolve a specific
    addon. Margo will use the same release channel as the current cluster.

    Values:
      CHANNEL_UNSPECIFIED: CHANNEL_UNSPECIFIED is the default unspecified
        channel field.
      REGULAR: REGULAR refers to access the ConfigDeliveryArgoCD feature
        reasonably soon after they debut, but on a version that has been
        qualified over a longer period of time.
      RAPID: RAPID refers to get the latest ConfigDeliveryArgoCD release as
        early as possible, and be able to use new features the moment they go
        GA.
      STABLE: STABLE refers to prioritize stability over new functionality.
    """
    CHANNEL_UNSPECIFIED = 0
    REGULAR = 1
    RAPID = 2
    STABLE = 3

  channel = _messages.EnumField('ChannelValueValuesEnum', 1)
  version = _messages.StringField(2)


class ConfigDeliveryArgoCDState(_messages.Message):
  r"""State defines the state of the Margo reconciliation objects.

  Fields:
    cluster: The user-defined name for the cluster used by ClusterSelectors to
      group clusters together. This should match Membership's membership_name,
    margoState: This state describes the state of all the deployable ArgoCD
      manifests.
  """

  cluster = _messages.StringField(1)
  margoState = _messages.MessageField('ConfigDeliveryArgoCDDeclarativeState', 2)


class ConfigLifecycleState(_messages.Message):
  r"""ConfigLifecycleState describes the state of a FeatureConfig resource.

  Enums:
    StateValueValuesEnum: Output only. The current state of the FeatureConfig
      resource.

  Fields:
    state: Output only. The current state of the FeatureConfig resource.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the FeatureConfig resource.

    Values:
      STATE_UNSPECIFIED: The code is not set.
      CREATING: The FeatureConfig is being created.
      ACTIVE: The FeatureConfig has been created.
      DELETING: The FeatureConfig is being deleted.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3

  state = _messages.EnumField('StateValueValuesEnum', 1)


class ConfigManagementBinauthzConfig(_messages.Message):
  r"""Configuration for Binauthz.

  Fields:
    enabled: Whether binauthz is enabled in this cluster.
  """

  enabled = _messages.BooleanField(1)


class ConfigManagementBinauthzState(_messages.Message):
  r"""State for Binauthz.

  Enums:
    WebhookValueValuesEnum: The state of the binauthz webhook.

  Fields:
    version: The version of binauthz that is installed.
    webhook: The state of the binauthz webhook.
  """

  class WebhookValueValuesEnum(_messages.Enum):
    r"""The state of the binauthz webhook.

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined.
      NOT_INSTALLED: Deployment is not installed.
      INSTALLED: Deployment is installed.
      ERROR: Deployment was attempted to be installed, but has errors.
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  version = _messages.MessageField('ConfigManagementBinauthzVersion', 1)
  webhook = _messages.EnumField('WebhookValueValuesEnum', 2)


class ConfigManagementBinauthzVersion(_messages.Message):
  r"""The version of binauthz.

  Fields:
    webhookVersion: The version of the binauthz webhook.
  """

  webhookVersion = _messages.StringField(1)


class ConfigManagementConfigSync(_messages.Message):
  r"""Configuration for Config Sync

  Fields:
    deploymentOverrides: Optional. Configuration for deployment overrides.
    enabled: Optional. Enables the installation of ConfigSync. If set to true,
      ConfigSync resources will be created and the other ConfigSync fields
      will be applied if exist. If set to false, all other ConfigSync fields
      will be ignored, ConfigSync resources will be deleted. If omitted,
      ConfigSync resources will be managed depends on the presence of the git
      or oci field.
    git: Optional. Git repo configuration for the cluster.
    metricsGcpServiceAccountEmail: Optional. The Email of the Google Cloud
      Service Account (GSA) used for exporting Config Sync metrics to Cloud
      Monitoring and Cloud Monarch when Workload Identity is enabled. The GSA
      should have the Monitoring Metric Writer (roles/monitoring.metricWriter)
      IAM role. The Kubernetes ServiceAccount `default` in the namespace
      `config-management-monitoring` should be bound to the GSA. Deprecated:
      If Workload Identity Federation for GKE is enabled, Google Cloud Service
      Account is no longer needed for exporting Config Sync metrics:
      https://cloud.google.com/kubernetes-engine/enterprise/config-
      sync/docs/how-to/monitor-config-sync-cloud-monitoring#custom-monitoring.
    oci: Optional. OCI repo configuration for the cluster.
    preventDrift: Optional. Set to true to enable the Config Sync admission
      webhook to prevent drifts. If set to `false`, disables the Config Sync
      admission webhook and does not prevent drifts.
    sourceFormat: Optional. Specifies whether the Config Sync Repo is in
      "hierarchical" or "unstructured" mode.
    stopSyncing: Optional. Set to true to stop syncing configs for a single
      cluster. Default to false.
  """

  deploymentOverrides = _messages.MessageField('ConfigManagementDeploymentOverride', 1, repeated=True)
  enabled = _messages.BooleanField(2)
  git = _messages.MessageField('ConfigManagementGitConfig', 3)
  metricsGcpServiceAccountEmail = _messages.StringField(4)
  oci = _messages.MessageField('ConfigManagementOciConfig', 5)
  preventDrift = _messages.BooleanField(6)
  sourceFormat = _messages.StringField(7)
  stopSyncing = _messages.BooleanField(8)


class ConfigManagementConfigSyncDeploymentState(_messages.Message):
  r"""The state of ConfigSync's deployment on a cluster.

  Enums:
    AdmissionWebhookValueValuesEnum: Deployment state of admission-webhook.
    GitSyncValueValuesEnum: Deployment state of the git-sync pod.
    ImporterValueValuesEnum: Deployment state of the importer pod.
    MonitorValueValuesEnum: Deployment state of the monitor pod.
    OtelCollectorValueValuesEnum: Deployment state of otel-collector
    ReconcilerManagerValueValuesEnum: Deployment state of reconciler-manager
      pod.
    ResourceGroupControllerManagerValueValuesEnum: Deployment state of
      resource-group-controller-manager
    RootReconcilerValueValuesEnum: Deployment state of root-reconciler.
    SyncerValueValuesEnum: Deployment state of the syncer pod.

  Fields:
    admissionWebhook: Deployment state of admission-webhook.
    gitSync: Deployment state of the git-sync pod.
    importer: Deployment state of the importer pod.
    monitor: Deployment state of the monitor pod.
    otelCollector: Deployment state of otel-collector
    reconcilerManager: Deployment state of reconciler-manager pod.
    resourceGroupControllerManager: Deployment state of resource-group-
      controller-manager
    rootReconciler: Deployment state of root-reconciler.
    syncer: Deployment state of the syncer pod.
  """

  class AdmissionWebhookValueValuesEnum(_messages.Enum):
    r"""Deployment state of admission-webhook.

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined.
      NOT_INSTALLED: Deployment is not installed.
      INSTALLED: Deployment is installed.
      ERROR: Deployment was attempted to be installed, but has errors.
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class GitSyncValueValuesEnum(_messages.Enum):
    r"""Deployment state of the git-sync pod.

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined.
      NOT_INSTALLED: Deployment is not installed.
      INSTALLED: Deployment is installed.
      ERROR: Deployment was attempted to be installed, but has errors.
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class ImporterValueValuesEnum(_messages.Enum):
    r"""Deployment state of the importer pod.

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined.
      NOT_INSTALLED: Deployment is not installed.
      INSTALLED: Deployment is installed.
      ERROR: Deployment was attempted to be installed, but has errors.
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class MonitorValueValuesEnum(_messages.Enum):
    r"""Deployment state of the monitor pod.

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined.
      NOT_INSTALLED: Deployment is not installed.
      INSTALLED: Deployment is installed.
      ERROR: Deployment was attempted to be installed, but has errors.
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class OtelCollectorValueValuesEnum(_messages.Enum):
    r"""Deployment state of otel-collector

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined.
      NOT_INSTALLED: Deployment is not installed.
      INSTALLED: Deployment is installed.
      ERROR: Deployment was attempted to be installed, but has errors.
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class ReconcilerManagerValueValuesEnum(_messages.Enum):
    r"""Deployment state of reconciler-manager pod.

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined.
      NOT_INSTALLED: Deployment is not installed.
      INSTALLED: Deployment is installed.
      ERROR: Deployment was attempted to be installed, but has errors.
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class ResourceGroupControllerManagerValueValuesEnum(_messages.Enum):
    r"""Deployment state of resource-group-controller-manager

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined.
      NOT_INSTALLED: Deployment is not installed.
      INSTALLED: Deployment is installed.
      ERROR: Deployment was attempted to be installed, but has errors.
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class RootReconcilerValueValuesEnum(_messages.Enum):
    r"""Deployment state of root-reconciler.

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined.
      NOT_INSTALLED: Deployment is not installed.
      INSTALLED: Deployment is installed.
      ERROR: Deployment was attempted to be installed, but has errors.
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class SyncerValueValuesEnum(_messages.Enum):
    r"""Deployment state of the syncer pod.

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined.
      NOT_INSTALLED: Deployment is not installed.
      INSTALLED: Deployment is installed.
      ERROR: Deployment was attempted to be installed, but has errors.
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  admissionWebhook = _messages.EnumField('AdmissionWebhookValueValuesEnum', 1)
  gitSync = _messages.EnumField('GitSyncValueValuesEnum', 2)
  importer = _messages.EnumField('ImporterValueValuesEnum', 3)
  monitor = _messages.EnumField('MonitorValueValuesEnum', 4)
  otelCollector = _messages.EnumField('OtelCollectorValueValuesEnum', 5)
  reconcilerManager = _messages.EnumField('ReconcilerManagerValueValuesEnum', 6)
  resourceGroupControllerManager = _messages.EnumField('ResourceGroupControllerManagerValueValuesEnum', 7)
  rootReconciler = _messages.EnumField('RootReconcilerValueValuesEnum', 8)
  syncer = _messages.EnumField('SyncerValueValuesEnum', 9)


class ConfigManagementConfigSyncError(_messages.Message):
  r"""Errors pertaining to the installation of Config Sync

  Fields:
    errorMessage: A string representing the user facing error message
  """

  errorMessage = _messages.StringField(1)


class ConfigManagementConfigSyncState(_messages.Message):
  r"""State information for ConfigSync.

  Enums:
    ClusterLevelStopSyncingStateValueValuesEnum: Output only. Whether syncing
      resources to the cluster is stopped at the cluster level.
    ReposyncCrdValueValuesEnum: Output only. The state of the Reposync CRD
    RootsyncCrdValueValuesEnum: Output only. The state of the RootSync CRD
    StateValueValuesEnum: Output only. The state of CS This field summarizes
      the other fields in this message.

  Fields:
    clusterLevelStopSyncingState: Output only. Whether syncing resources to
      the cluster is stopped at the cluster level.
    crCount: Output only. The number of RootSync and RepoSync CRs in the
      cluster.
    deploymentState: Output only. Information about the deployment of
      ConfigSync, including the version. of the various Pods deployed
    errors: Output only. Errors pertaining to the installation of Config Sync.
    reposyncCrd: Output only. The state of the Reposync CRD
    rootsyncCrd: Output only. The state of the RootSync CRD
    state: Output only. The state of CS This field summarizes the other fields
      in this message.
    syncState: Output only. The state of ConfigSync's process to sync configs
      to a cluster.
    version: Output only. The version of ConfigSync deployed.
  """

  class ClusterLevelStopSyncingStateValueValuesEnum(_messages.Enum):
    r"""Output only. Whether syncing resources to the cluster is stopped at
    the cluster level.

    Values:
      STOP_SYNCING_STATE_UNSPECIFIED: State cannot be determined
      NOT_STOPPED: Syncing resources to the cluster is not stopped at the
        cluster level.
      PENDING: Some reconcilers stop syncing resources to the cluster, while
        others are still syncing.
      STOPPED: Syncing resources to the cluster is stopped at the cluster
        level.
    """
    STOP_SYNCING_STATE_UNSPECIFIED = 0
    NOT_STOPPED = 1
    PENDING = 2
    STOPPED = 3

  class ReposyncCrdValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the Reposync CRD

    Values:
      CRD_STATE_UNSPECIFIED: CRD's state cannot be determined
      NOT_INSTALLED: CRD is not installed
      INSTALLED: CRD is installed
      TERMINATING: CRD is terminating (i.e., it has been deleted and is
        cleaning up)
      INSTALLING: CRD is installing
    """
    CRD_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    TERMINATING = 3
    INSTALLING = 4

  class RootsyncCrdValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the RootSync CRD

    Values:
      CRD_STATE_UNSPECIFIED: CRD's state cannot be determined
      NOT_INSTALLED: CRD is not installed
      INSTALLED: CRD is installed
      TERMINATING: CRD is terminating (i.e., it has been deleted and is
        cleaning up)
      INSTALLING: CRD is installing
    """
    CRD_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    TERMINATING = 3
    INSTALLING = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of CS This field summarizes the other fields in
    this message.

    Values:
      STATE_UNSPECIFIED: CS's state cannot be determined.
      CONFIG_SYNC_NOT_INSTALLED: CS is not installed.
      CONFIG_SYNC_INSTALLED: The expected CS version is installed
        successfully.
      CONFIG_SYNC_ERROR: CS encounters errors.
      CONFIG_SYNC_PENDING: CS is installing or terminating.
    """
    STATE_UNSPECIFIED = 0
    CONFIG_SYNC_NOT_INSTALLED = 1
    CONFIG_SYNC_INSTALLED = 2
    CONFIG_SYNC_ERROR = 3
    CONFIG_SYNC_PENDING = 4

  clusterLevelStopSyncingState = _messages.EnumField('ClusterLevelStopSyncingStateValueValuesEnum', 1)
  crCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  deploymentState = _messages.MessageField('ConfigManagementConfigSyncDeploymentState', 3)
  errors = _messages.MessageField('ConfigManagementConfigSyncError', 4, repeated=True)
  reposyncCrd = _messages.EnumField('ReposyncCrdValueValuesEnum', 5)
  rootsyncCrd = _messages.EnumField('RootsyncCrdValueValuesEnum', 6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  syncState = _messages.MessageField('ConfigManagementSyncState', 8)
  version = _messages.MessageField('ConfigManagementConfigSyncVersion', 9)


class ConfigManagementConfigSyncVersion(_messages.Message):
  r"""Specific versioning information pertaining to ConfigSync's Pods.

  Fields:
    admissionWebhook: Version of the deployed admission-webhook pod.
    gitSync: Version of the deployed git-sync pod.
    importer: Version of the deployed importer pod.
    monitor: Version of the deployed monitor pod.
    otelCollector: Version of the deployed otel-collector pod
    reconcilerManager: Version of the deployed reconciler-manager pod.
    resourceGroupControllerManager: Version of the deployed resource-group-
      controller-manager pod
    rootReconciler: Version of the deployed reconciler container in root-
      reconciler pod.
    syncer: Version of the deployed syncer pod.
  """

  admissionWebhook = _messages.StringField(1)
  gitSync = _messages.StringField(2)
  importer = _messages.StringField(3)
  monitor = _messages.StringField(4)
  otelCollector = _messages.StringField(5)
  reconcilerManager = _messages.StringField(6)
  resourceGroupControllerManager = _messages.StringField(7)
  rootReconciler = _messages.StringField(8)
  syncer = _messages.StringField(9)


class ConfigManagementContainerOverride(_messages.Message):
  r"""Configuration for a container override.

  Fields:
    containerName: Required. The name of the container.
    cpuLimit: Optional. The cpu limit of the container.
    cpuRequest: Optional. The cpu request of the container.
    memoryLimit: Optional. The memory limit of the container.
    memoryRequest: Optional. The memory request of the container.
  """

  containerName = _messages.StringField(1)
  cpuLimit = _messages.StringField(2)
  cpuRequest = _messages.StringField(3)
  memoryLimit = _messages.StringField(4)
  memoryRequest = _messages.StringField(5)


class ConfigManagementDeploymentOverride(_messages.Message):
  r"""Configuration for a deployment override.

  Fields:
    containers: Optional. The containers of the deployment resource to be
      overridden.
    deploymentName: Required. The name of the deployment resource to be
      overridden.
    deploymentNamespace: Required. The namespace of the deployment resource to
      be overridden.
  """

  containers = _messages.MessageField('ConfigManagementContainerOverride', 1, repeated=True)
  deploymentName = _messages.StringField(2)
  deploymentNamespace = _messages.StringField(3)


class ConfigManagementErrorResource(_messages.Message):
  r"""Model for a config file in the git repo with an associated Sync error.

  Fields:
    resourceGvk: Group/version/kind of the resource that is causing an error
    resourceName: Metadata name of the resource that is causing an error
    resourceNamespace: Namespace of the resource that is causing an error
    sourcePath: Path in the git repo of the erroneous config
  """

  resourceGvk = _messages.MessageField('ConfigManagementGroupVersionKind', 1)
  resourceName = _messages.StringField(2)
  resourceNamespace = _messages.StringField(3)
  sourcePath = _messages.StringField(4)


class ConfigManagementGatekeeperDeploymentState(_messages.Message):
  r"""State of Policy Controller installation.

  Enums:
    GatekeeperAuditValueValuesEnum: Status of gatekeeper-audit deployment.
    GatekeeperControllerManagerStateValueValuesEnum: Status of gatekeeper-
      controller-manager pod.
    GatekeeperMutationValueValuesEnum: Status of the pod serving the mutation
      webhook.

  Fields:
    gatekeeperAudit: Status of gatekeeper-audit deployment.
    gatekeeperControllerManagerState: Status of gatekeeper-controller-manager
      pod.
    gatekeeperMutation: Status of the pod serving the mutation webhook.
  """

  class GatekeeperAuditValueValuesEnum(_messages.Enum):
    r"""Status of gatekeeper-audit deployment.

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined.
      NOT_INSTALLED: Deployment is not installed.
      INSTALLED: Deployment is installed.
      ERROR: Deployment was attempted to be installed, but has errors.
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class GatekeeperControllerManagerStateValueValuesEnum(_messages.Enum):
    r"""Status of gatekeeper-controller-manager pod.

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined.
      NOT_INSTALLED: Deployment is not installed.
      INSTALLED: Deployment is installed.
      ERROR: Deployment was attempted to be installed, but has errors.
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class GatekeeperMutationValueValuesEnum(_messages.Enum):
    r"""Status of the pod serving the mutation webhook.

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined.
      NOT_INSTALLED: Deployment is not installed.
      INSTALLED: Deployment is installed.
      ERROR: Deployment was attempted to be installed, but has errors.
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  gatekeeperAudit = _messages.EnumField('GatekeeperAuditValueValuesEnum', 1)
  gatekeeperControllerManagerState = _messages.EnumField('GatekeeperControllerManagerStateValueValuesEnum', 2)
  gatekeeperMutation = _messages.EnumField('GatekeeperMutationValueValuesEnum', 3)


class ConfigManagementGitConfig(_messages.Message):
  r"""Git repo configuration for a single cluster.

  Fields:
    gcpServiceAccountEmail: Optional. The Google Cloud Service Account Email
      used for auth when secret_type is gcpServiceAccount.
    httpsProxy: Optional. URL for the HTTPS proxy to be used when
      communicating with the Git repo.
    policyDir: Optional. The path within the Git repository that represents
      the top level of the repo to sync. Default: the root directory of the
      repository.
    secretType: Required. Type of secret configured for access to the Git
      repo. Must be one of ssh, cookiefile, gcenode, token, gcpserviceaccount,
      githubapp or none. The validation of this is case-sensitive.
    syncBranch: Optional. The branch of the repository to sync from. Default:
      master.
    syncRepo: Required. The URL of the Git repository to use as the source of
      truth.
    syncRev: Optional. Git revision (tag or hash) to check out. Default HEAD.
    syncWaitSecs: Optional. Period in seconds between consecutive syncs.
      Default: 15.
  """

  gcpServiceAccountEmail = _messages.StringField(1)
  httpsProxy = _messages.StringField(2)
  policyDir = _messages.StringField(3)
  secretType = _messages.StringField(4)
  syncBranch = _messages.StringField(5)
  syncRepo = _messages.StringField(6)
  syncRev = _messages.StringField(7)
  syncWaitSecs = _messages.IntegerField(8)


class ConfigManagementGroupVersionKind(_messages.Message):
  r"""A Kubernetes object's GVK.

  Fields:
    group: Kubernetes Group
    kind: Kubernetes Kind
    version: Kubernetes Version
  """

  group = _messages.StringField(1)
  kind = _messages.StringField(2)
  version = _messages.StringField(3)


class ConfigManagementHierarchyControllerConfig(_messages.Message):
  r"""Configuration for Hierarchy Controller.

  Fields:
    enableHierarchicalResourceQuota: Whether hierarchical resource quota is
      enabled in this cluster.
    enablePodTreeLabels: Whether pod tree labels are enabled in this cluster.
    enabled: Whether Hierarchy Controller is enabled in this cluster.
  """

  enableHierarchicalResourceQuota = _messages.BooleanField(1)
  enablePodTreeLabels = _messages.BooleanField(2)
  enabled = _messages.BooleanField(3)


class ConfigManagementHierarchyControllerDeploymentState(_messages.Message):
  r"""Deployment state for Hierarchy Controller

  Enums:
    ExtensionValueValuesEnum: The deployment state for Hierarchy Controller
      extension (e.g. v0.7.0-hc.1).
    HncValueValuesEnum: The deployment state for open source HNC (e.g.
      v0.7.0-hc.0).

  Fields:
    extension: The deployment state for Hierarchy Controller extension (e.g.
      v0.7.0-hc.1).
    hnc: The deployment state for open source HNC (e.g. v0.7.0-hc.0).
  """

  class ExtensionValueValuesEnum(_messages.Enum):
    r"""The deployment state for Hierarchy Controller extension (e.g.
    v0.7.0-hc.1).

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined.
      NOT_INSTALLED: Deployment is not installed.
      INSTALLED: Deployment is installed.
      ERROR: Deployment was attempted to be installed, but has errors.
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class HncValueValuesEnum(_messages.Enum):
    r"""The deployment state for open source HNC (e.g. v0.7.0-hc.0).

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined.
      NOT_INSTALLED: Deployment is not installed.
      INSTALLED: Deployment is installed.
      ERROR: Deployment was attempted to be installed, but has errors.
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  extension = _messages.EnumField('ExtensionValueValuesEnum', 1)
  hnc = _messages.EnumField('HncValueValuesEnum', 2)


class ConfigManagementHierarchyControllerState(_messages.Message):
  r"""State for Hierarchy Controller.

  Fields:
    state: The deployment state for Hierarchy Controller.
    version: The version for Hierarchy Controller.
  """

  state = _messages.MessageField('ConfigManagementHierarchyControllerDeploymentState', 1)
  version = _messages.MessageField('ConfigManagementHierarchyControllerVersion', 2)


class ConfigManagementHierarchyControllerVersion(_messages.Message):
  r"""Version for Hierarchy Controller.

  Fields:
    extension: Version for Hierarchy Controller extension.
    hnc: Version for open source HNC.
  """

  extension = _messages.StringField(1)
  hnc = _messages.StringField(2)


class ConfigManagementInstallError(_messages.Message):
  r"""Errors pertaining to the installation of ACM.

  Fields:
    errorMessage: A string representing the user facing error message.
  """

  errorMessage = _messages.StringField(1)


class ConfigManagementOciConfig(_messages.Message):
  r"""OCI repo configuration for a single cluster.

  Fields:
    gcpServiceAccountEmail: Optional. The Google Cloud Service Account Email
      used for auth when secret_type is gcpServiceAccount.
    policyDir: Optional. The absolute path of the directory that contains the
      local resources. Default: the root directory of the image.
    secretType: Required. Type of secret configured for access to the OCI
      repo. Must be one of gcenode, gcpserviceaccount, k8sserviceaccount or
      none. The validation of this is case-sensitive.
    syncRepo: Required. The OCI image repository URL for the package to sync
      from. e.g. `LOCATION-
      docker.pkg.dev/PROJECT_ID/REPOSITORY_NAME/PACKAGE_NAME`.
    syncWaitSecs: Optional. Period in seconds between consecutive syncs.
      Default: 15.
  """

  gcpServiceAccountEmail = _messages.StringField(1)
  policyDir = _messages.StringField(2)
  secretType = _messages.StringField(3)
  syncRepo = _messages.StringField(4)
  syncWaitSecs = _messages.IntegerField(5)


class ConfigManagementOperatorState(_messages.Message):
  r"""State information for an ACM's Operator.

  Enums:
    DeploymentStateValueValuesEnum: The state of the Operator's deployment.

  Fields:
    deploymentState: The state of the Operator's deployment.
    errors: Install errors.
    version: The semenatic version number of the operator.
  """

  class DeploymentStateValueValuesEnum(_messages.Enum):
    r"""The state of the Operator's deployment.

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined.
      NOT_INSTALLED: Deployment is not installed.
      INSTALLED: Deployment is installed.
      ERROR: Deployment was attempted to be installed, but has errors.
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  deploymentState = _messages.EnumField('DeploymentStateValueValuesEnum', 1)
  errors = _messages.MessageField('ConfigManagementInstallError', 2, repeated=True)
  version = _messages.StringField(3)


class ConfigManagementPolicyController(_messages.Message):
  r"""Configuration for Policy Controller

  Fields:
    auditIntervalSeconds: Sets the interval for Policy Controller Audit Scans
      (in seconds). When set to 0, this disables audit functionality
      altogether.
    enabled: Enables the installation of Policy Controller. If false, the rest
      of PolicyController fields take no effect.
    exemptableNamespaces: The set of namespaces that are excluded from Policy
      Controller checks. Namespaces do not need to currently exist on the
      cluster.
    logDeniesEnabled: Logs all denies and dry run failures.
    monitoring: Monitoring specifies the configuration of monitoring.
    mutationEnabled: Enable or disable mutation in policy controller. If true,
      mutation CRDs, webhook and controller deployment will be deployed to the
      cluster.
    referentialRulesEnabled: Enables the ability to use Constraint Templates
      that reference to objects other than the object currently being
      evaluated.
    templateLibraryInstalled: Installs the default template library along with
      Policy Controller.
    updateTime: Output only. Last time this membership spec was updated.
  """

  auditIntervalSeconds = _messages.IntegerField(1)
  enabled = _messages.BooleanField(2)
  exemptableNamespaces = _messages.StringField(3, repeated=True)
  logDeniesEnabled = _messages.BooleanField(4)
  monitoring = _messages.MessageField('ConfigManagementPolicyControllerMonitoring', 5)
  mutationEnabled = _messages.BooleanField(6)
  referentialRulesEnabled = _messages.BooleanField(7)
  templateLibraryInstalled = _messages.BooleanField(8)
  updateTime = _messages.StringField(9)


class ConfigManagementPolicyControllerMigration(_messages.Message):
  r"""State for the migration of PolicyController from ACM -> PoCo Hub.

  Enums:
    StageValueValuesEnum: Stage of the migration.

  Fields:
    copyTime: Last time this membership spec was copied to PoCo feature.
    stage: Stage of the migration.
  """

  class StageValueValuesEnum(_messages.Enum):
    r"""Stage of the migration.

    Values:
      STAGE_UNSPECIFIED: Unknown state of migration.
      ACM_MANAGED: ACM Hub/Operator manages policycontroller. No migration yet
        completed.
      POCO_MANAGED: All migrations steps complete; Poco Hub now manages
        policycontroller.
    """
    STAGE_UNSPECIFIED = 0
    ACM_MANAGED = 1
    POCO_MANAGED = 2

  copyTime = _messages.StringField(1)
  stage = _messages.EnumField('StageValueValuesEnum', 2)


class ConfigManagementPolicyControllerMonitoring(_messages.Message):
  r"""PolicyControllerMonitoring specifies the backends Policy Controller
  should export metrics to. For example, to specify metrics should be exported
  to Cloud Monitoring and Prometheus, specify backends: ["cloudmonitoring",
  "prometheus"]

  Enums:
    BackendsValueListEntryValuesEnum:

  Fields:
    backends: Specifies the list of backends Policy Controller will export to.
      An empty list would effectively disable metrics export.
  """

  class BackendsValueListEntryValuesEnum(_messages.Enum):
    r"""BackendsValueListEntryValuesEnum enum type.

    Values:
      MONITORING_BACKEND_UNSPECIFIED: Backend cannot be determined
      PROMETHEUS: Prometheus backend for monitoring
      CLOUD_MONITORING: Stackdriver/Cloud Monitoring backend for monitoring
    """
    MONITORING_BACKEND_UNSPECIFIED = 0
    PROMETHEUS = 1
    CLOUD_MONITORING = 2

  backends = _messages.EnumField('BackendsValueListEntryValuesEnum', 1, repeated=True)


class ConfigManagementPolicyControllerState(_messages.Message):
  r"""State for PolicyControllerState.

  Fields:
    deploymentState: The state about the policy controller installation.
    migration: Record state of ACM -> PoCo Hub migration for this feature.
    version: The version of Gatekeeper Policy Controller deployed.
  """

  deploymentState = _messages.MessageField('ConfigManagementGatekeeperDeploymentState', 1)
  migration = _messages.MessageField('ConfigManagementPolicyControllerMigration', 2)
  version = _messages.MessageField('ConfigManagementPolicyControllerVersion', 3)


class ConfigManagementPolicyControllerVersion(_messages.Message):
  r"""The build version of Gatekeeper Policy Controller is using.

  Fields:
    version: The gatekeeper image tag that is composed of ACM version, git
      tag, build number.
  """

  version = _messages.StringField(1)


class ConfigManagementSpec(_messages.Message):
  r"""**Anthos Config Management**: Configuration for a single cluster.
  Intended to parallel the ConfigManagement CR.

  Enums:
    ManagementValueValuesEnum: Optional. Enables automatic Feature management.

  Fields:
    binauthz: Optional. Binauthz conifguration for the cluster. Deprecated:
      This field will be ignored and should not be set.
    cluster: Optional. The user-specified cluster name used by Config Sync
      cluster-name-selector annotation or ClusterSelector, for applying
      configs to only a subset of clusters. Omit this field if the cluster's
      fleet membership name is used by Config Sync cluster-name-selector
      annotation or ClusterSelector. Set this field if a name different from
      the cluster's fleet membership name is used by Config Sync cluster-name-
      selector annotation or ClusterSelector.
    configSync: Optional. Config Sync configuration for the cluster.
    hierarchyController: Optional. Hierarchy Controller configuration for the
      cluster. Deprecated: Configuring Hierarchy Controller through the
      configmanagement feature is no longer recommended. Use
      https://github.com/kubernetes-sigs/hierarchical-namespaces instead.
    management: Optional. Enables automatic Feature management.
    policyController: Optional. Policy Controller configuration for the
      cluster. Deprecated: Configuring Policy Controller through the
      configmanagement feature is no longer recommended. Use the
      policycontroller feature instead.
    version: Optional. Version of ACM installed.
  """

  class ManagementValueValuesEnum(_messages.Enum):
    r"""Optional. Enables automatic Feature management.

    Values:
      MANAGEMENT_UNSPECIFIED: Unspecified
      MANAGEMENT_AUTOMATIC: Google will manage the Feature for the cluster.
      MANAGEMENT_MANUAL: User will manually manage the Feature for the
        cluster.
    """
    MANAGEMENT_UNSPECIFIED = 0
    MANAGEMENT_AUTOMATIC = 1
    MANAGEMENT_MANUAL = 2

  binauthz = _messages.MessageField('ConfigManagementBinauthzConfig', 1)
  cluster = _messages.StringField(2)
  configSync = _messages.MessageField('ConfigManagementConfigSync', 3)
  hierarchyController = _messages.MessageField('ConfigManagementHierarchyControllerConfig', 4)
  management = _messages.EnumField('ManagementValueValuesEnum', 5)
  policyController = _messages.MessageField('ConfigManagementPolicyController', 6)
  version = _messages.StringField(7)


class ConfigManagementState(_messages.Message):
  r"""**Anthos Config Management**: State for a single cluster.

  Fields:
    binauthzState: Output only. Binauthz status.
    clusterName: Output only. This field is set to the `cluster_name` field of
      the Membership Spec if it is not empty. Otherwise, it is set to the
      cluster's fleet membership name.
    configSyncState: Output only. Current sync status.
    hierarchyControllerState: Output only. Hierarchy Controller status.
    membershipSpec: Output only. Membership configuration in the cluster. This
      represents the actual state in the cluster, while the MembershipSpec in
      the FeatureSpec represents the intended state.
    operatorState: Output only. Current install status of ACM's Operator.
    policyControllerState: Output only. PolicyController status.
  """

  binauthzState = _messages.MessageField('ConfigManagementBinauthzState', 1)
  clusterName = _messages.StringField(2)
  configSyncState = _messages.MessageField('ConfigManagementConfigSyncState', 3)
  hierarchyControllerState = _messages.MessageField('ConfigManagementHierarchyControllerState', 4)
  membershipSpec = _messages.MessageField('ConfigManagementSpec', 5)
  operatorState = _messages.MessageField('ConfigManagementOperatorState', 6)
  policyControllerState = _messages.MessageField('ConfigManagementPolicyControllerState', 7)


class ConfigManagementSyncError(_messages.Message):
  r"""An ACM created error representing a problem syncing configurations.

  Fields:
    code: An ACM defined error code
    errorMessage: A description of the error
    errorResources: A list of config(s) associated with the error, if any
  """

  code = _messages.StringField(1)
  errorMessage = _messages.StringField(2)
  errorResources = _messages.MessageField('ConfigManagementErrorResource', 3, repeated=True)


class ConfigManagementSyncState(_messages.Message):
  r"""State indicating an ACM's progress syncing configurations to a cluster.

  Enums:
    CodeValueValuesEnum: Sync status code.

  Fields:
    code: Sync status code.
    errors: A list of errors resulting from problematic configs. This list
      will be truncated after 100 errors, although it is unlikely for that
      many errors to simultaneously exist.
    importToken: Token indicating the state of the importer.
    lastSync: Deprecated: use last_sync_time instead. Timestamp of when ACM
      last successfully synced the repo. The time format is specified in
      https://golang.org/pkg/time/#Time.String
    lastSyncTime: Timestamp type of when ACM last successfully synced the
      repo.
    sourceToken: Token indicating the state of the repo.
    syncToken: Token indicating the state of the syncer.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Sync status code.

    Values:
      SYNC_CODE_UNSPECIFIED: Config Sync cannot determine a sync code
      SYNCED: Config Sync successfully synced the git Repo with the cluster
      PENDING: Config Sync is in the progress of syncing a new change
      ERROR: Indicates an error configuring Config Sync, and user action is
        required
      NOT_CONFIGURED: Config Sync has been installed but not configured
      NOT_INSTALLED: Config Sync has not been installed
      UNAUTHORIZED: Error authorizing with the cluster
      UNREACHABLE: Cluster could not be reached
    """
    SYNC_CODE_UNSPECIFIED = 0
    SYNCED = 1
    PENDING = 2
    ERROR = 3
    NOT_CONFIGURED = 4
    NOT_INSTALLED = 5
    UNAUTHORIZED = 6
    UNREACHABLE = 7

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  errors = _messages.MessageField('ConfigManagementSyncError', 2, repeated=True)
  importToken = _messages.StringField(3)
  lastSync = _messages.StringField(4)
  lastSyncTime = _messages.StringField(5)
  sourceToken = _messages.StringField(6)
  syncToken = _messages.StringField(7)


class CreateReferenceRequest(_messages.Message):
  r"""The CreateReferenceRequest request.

  Fields:
    parent: Required. The parent resource name (target_resource of this
      reference). For example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}`.
    reference: Required. The reference to be created.
    referenceId: The unique id of this resource. Must be unique within a scope
      of a target resource, but does not have to be globally unique. Reference
      ID is part of resource name of the reference. Resource name is generated
      in the following way: {parent}/references/{reference_id}. Reference ID
      field is currently required but id auto generation might be added in the
      future. It can be any arbitrary string, either GUID or any other string,
      however CLHs can use preprocess callbacks to perform a custom
      validation.
    requestId: Optional. Request ID is an idempotency ID of the request. It
      must be a valid UUID. Zero UUID (00000000-0000-0000-0000-000000000000)
      is not supported.
  """

  parent = _messages.StringField(1)
  reference = _messages.MessageField('Reference', 2)
  referenceId = _messages.StringField(3)
  requestId = _messages.StringField(4)


class DeleteReferenceRequest(_messages.Message):
  r"""The DeleteReferenceRequest request.

  Fields:
    name: Required. Full resource name of the reference, in the following
      format:
      `//{targer_service}/{target_resource}/references/{reference_id}`. For
      example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}/references/{xyz}`.
    requestId: Optional. Request ID is an idempotency ID of the request. It
      must be a valid UUID. Zero UUID (00000000-0000-0000-0000-000000000000)
      is not supported.
  """

  name = _messages.StringField(1)
  requestId = _messages.StringField(2)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class FeatureConfig(_messages.Message):
  r"""FeatureConfig contains configurations for a Fleet feature. FeatureConfig
  can be applied to MembershipFeature(s) to setup per-membership FeatureSpec.

  Messages:
    LabelsValue: GCP labels for this FeatureConfig.

  Fields:
    createTime: Output only. When the FeatureConfig resource was created.
    deleteTime: Output only. When the FeatureConfig resource was deleted.
    labels: GCP labels for this FeatureConfig.
    name: Output only. Resource name of this FeatureConfig, in the format: `pr
      ojects/{project}/locations/global/FeatureConfigs/{feature_type}/{feature
      _config}`
    spec: Input only. Immutable. User input of feature spec. Note that this
      field is immutable. Must create a new FeatureConfig if a new feature
      spec is needed.
    state: Output only. Lifecycle information of the FeatureConfig resource.
    uniqueId: Output only. Google-generated UUID for this resource. This is
      unique across all FeatureConfig resources. If a Membership resource is
      deleted and another resource with the same name is created, it gets a
      different unique_id.
    updateTime: Output only. When the FeatureConfig resource was last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""GCP labels for this FeatureConfig.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  deleteTime = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  spec = _messages.MessageField('FeatureSpec', 5)
  state = _messages.MessageField('ConfigLifecycleState', 6)
  uniqueId = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class FeatureConfigRef(_messages.Message):
  r"""Information of the FeatureConfig applied on the MembershipFeature.

  Fields:
    config: Input only. Resource name of FeatureConfig, in the format:
      `projects/{project}/locations/global/featureConfigs/{feature_config}`.
    configUpdateTime: Output only. When the FeatureConfig was last applied and
      copied to FeatureSpec.
    uuid: Output only. An id that uniquely identify a FeatureConfig object.
  """

  config = _messages.StringField(1)
  configUpdateTime = _messages.StringField(2)
  uuid = _messages.StringField(3)


class FeatureSpec(_messages.Message):
  r"""FeatureSpec contains user input per-feature spec information.

  Fields:
    cloudbuild: Cloudbuild-specific FeatureSpec.
    configDeliveryArgoCd: Config Delivery ArgoCD FeatureSpec.
    configmanagement: Config Management FeatureSpec.
    helloworld: Helloworld-specific FeatureSpec.
    identityservice: IdentityService FeatureSpec.
    namespaceactuation: NamespaceActuation-specific FeatureSpec.
    origin: Whether this per-Feature spec was inherited from a fleet-level
      default. This field can be updated by users by either overriding a
      Feature config (updated to USER implicitly) or setting to FLEET
      explicitly.
    policycontroller: Policycontroller-specific FeatureSpec.
    rbacrolebindingactuation: Rbacrolebindingactuation-specific FeatureSpec.
    servicemesh: ServiceMesh Feature Spec.
    workloadcertificate: Workloadcertificate-specific FeatureSpec.
  """

  cloudbuild = _messages.MessageField('CloudBuildSpec', 1)
  configDeliveryArgoCd = _messages.MessageField('ConfigDeliveryArgoCDSpec', 2)
  configmanagement = _messages.MessageField('ConfigManagementSpec', 3)
  helloworld = _messages.MessageField('HelloWorldSpec', 4)
  identityservice = _messages.MessageField('IdentityServiceSpec', 5)
  namespaceactuation = _messages.MessageField('NamespaceActuationSpec', 6)
  origin = _messages.MessageField('Origin', 7)
  policycontroller = _messages.MessageField('PolicyControllerSpec', 8)
  rbacrolebindingactuation = _messages.MessageField('RBACRoleBindingActuationSpec', 9)
  servicemesh = _messages.MessageField('ServiceMeshSpec', 10)
  workloadcertificate = _messages.MessageField('WorkloadCertificateSpec', 11)


class FeatureState(_messages.Message):
  r"""FeatureState contains high-level state information and per-feature state
  information for this MembershipFeature.

  Fields:
    appdevexperience: Appdevexperience specific state.
    clusterupgrade: Cluster upgrade state.
    configDeliveryArgoCd: Config Delivery ArgoCD FeatureState.
    configmanagement: Config Management state
    helloworld: Helloworld-specific FeatureState.
    identityservice: Identity service state
    metering: Metering state
    namespaceactuation: RBAC Role Binding Actuation state
    policycontroller: Policy Controller state
    rbacrolebindingactuation: RBAC Role Binding Actuation state
    servicemesh: Service mesh state
    state: The high-level state of this MembershipFeature.
    workloadidentity: Workload Identity state
  """

  appdevexperience = _messages.MessageField('AppDevExperienceState', 1)
  clusterupgrade = _messages.MessageField('ClusterUpgradeState', 2)
  configDeliveryArgoCd = _messages.MessageField('ConfigDeliveryArgoCDState', 3)
  configmanagement = _messages.MessageField('ConfigManagementState', 4)
  helloworld = _messages.MessageField('HelloWorldState', 5)
  identityservice = _messages.MessageField('IdentityServiceState', 6)
  metering = _messages.MessageField('MeteringState', 7)
  namespaceactuation = _messages.MessageField('NamespaceActuationState', 8)
  policycontroller = _messages.MessageField('PolicyControllerState', 9)
  rbacrolebindingactuation = _messages.MessageField('RBACRoleBindingActuationState', 10)
  servicemesh = _messages.MessageField('ServiceMeshState', 11)
  state = _messages.MessageField('State', 12)
  workloadidentity = _messages.MessageField('WorkloadIdentityState', 13)


class GetReferenceRequest(_messages.Message):
  r"""The GetReferenceRequest request.

  Fields:
    name: Required. Full resource name of the reference, in the following
      format:
      `//{target_service}/{target_resource}/references/{reference_id}`. For
      example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}/references/{xyz}`.
  """

  name = _messages.StringField(1)


class GkehubProjectsLocationsFeatureConfigsCreateRequest(_messages.Message):
  r"""A GkehubProjectsLocationsFeatureConfigsCreateRequest object.

  Fields:
    featureConfig: A FeatureConfig resource to be passed as the request body.
    featureConfigId: The ID of the feature config to create.
    parent: Required. The name of parent where the FeatureConfig will be
      created. Specified in the format `projects/{project}/locations/global/`.
    requestId: Idempotent request UUID.
  """

  featureConfig = _messages.MessageField('FeatureConfig', 1)
  featureConfigId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class GkehubProjectsLocationsFeatureConfigsDeleteRequest(_messages.Message):
  r"""A GkehubProjectsLocationsFeatureConfigsDeleteRequest object.

  Fields:
    name: Required. The name of the membershipFeature to be deleted. Specified
      in the format `projects/*/locations/*/featureConfigs/**`.
    requestId: Idempotent request UUID.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class GkehubProjectsLocationsFeatureConfigsGetRequest(_messages.Message):
  r"""A GkehubProjectsLocationsFeatureConfigsGetRequest object.

  Fields:
    name: Required. The FeatureConfig resource name in the format
      `projects/*/locations/*/featureConfigs/**`.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsFeatureConfigsListRequest(_messages.Message):
  r"""A GkehubProjectsLocationsFeatureConfigsListRequest object.

  Fields:
    filter: Lists FeatureConfigs that match the filter expression, following
      the syntax outlined in https://google.aip.dev/160.
    orderBy: One or more fields to compare and use to sort the output. See
      https://google.aip.dev/132#ordering.
    pageSize: When requesting a 'page' of resources, `page_size` specifies
      number of resources to return. If unspecified or set to 0, all resources
      will be returned.
    pageToken: Token returned by previous call to `ListFeatures` which
      specifies the position in the list from where to continue listing the
      resources.
    parent: Required. The parent where the FeatureConfigs will be listed. In
      the format: `projects/*/locations/*`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class GkehubProjectsLocationsGetRequest(_messages.Message):
  r"""A GkehubProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsListRequest(_messages.Message):
  r"""A GkehubProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    includeUnrevealedLocations: If true, the returned list will include
      locations which are not yet revealed.
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  includeUnrevealedLocations = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  pageSize = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(6)


class GkehubProjectsLocationsMembershipsFeaturesCreateRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsFeaturesCreateRequest object.

  Fields:
    featureId: Required. The ID of the membership_feature to create.
    membershipFeature: A MembershipFeature resource to be passed as the
      request body.
    parent: Required. The name of parent where the MembershipFeature will be
      created. Specified in the format `projects/*/locations/*/memberships/*`.
    requestId: Idempotent request UUID.
  """

  featureId = _messages.StringField(1)
  membershipFeature = _messages.MessageField('MembershipFeature', 2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class GkehubProjectsLocationsMembershipsFeaturesDeleteRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsFeaturesDeleteRequest object.

  Fields:
    name: Required. The name of the membershipFeature to be deleted. Specified
      in the format `projects/*/locations/*/memberships/*/features/*`.
    requestId: Idempotent request UUID.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class GkehubProjectsLocationsMembershipsFeaturesGetRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsFeaturesGetRequest object.

  Fields:
    name: Required. The MembershipFeature resource name in the format
      `projects/*/locations/*/memberships/*/features/*`.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsMembershipsFeaturesListRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsFeaturesListRequest object.

  Fields:
    filter: Lists MembershipFeatures that match the filter expression,
      following the syntax outlined in https://google.aip.dev/160. Examples: -
      Feature with the name "helloworld" in project "foo-proj" and membership
      "member-bar": name = "projects/foo-
      proj/locations/global/memberships/member-bar/features/helloworld" -
      Features that have a label called `foo`: labels.foo:* - Features that
      have a label called `foo` whose value is `bar`: labels.foo = bar
    orderBy: One or more fields to compare and use to sort the output. See
      https://google.aip.dev/132#ordering.
    pageSize: When requesting a 'page' of resources, `page_size` specifies
      number of resources to return. If unspecified or set to 0, all resources
      will be returned.
    pageToken: Token returned by previous call to `ListFeatures` which
      specifies the position in the list from where to continue listing the
      resources.
    parent: Required. The parent where the MembershipFeature will be listed.
      In the format: `projects/*/locations/*/memberships/*`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class GkehubProjectsLocationsMembershipsFeaturesPatchRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsFeaturesPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, and the MembershipFeature is not
      found, a new MembershipFeature will be created. In this situation,
      `update_mask` is ignored.
    membershipFeature: A MembershipFeature resource to be passed as the
      request body.
    name: Output only. The resource name of the membershipFeature, in the
      format: `projects/{project}/locations/{location}/memberships/{membership
      }/features/{feature}`. Note that `membershipFeatures` is shortened to
      `features` in the resource name. (see http://go/aip/122#collection-
      identifiers)
    requestId: Idempotent request UUID.
    updateMask: Required. Mask of fields to update.
  """

  allowMissing = _messages.BooleanField(1)
  membershipFeature = _messages.MessageField('MembershipFeature', 2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)


class GkehubProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A GkehubProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class GkehubProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A GkehubProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A GkehubProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class GoogleRpcStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class HelloWorldFeatureSample(_messages.Message):
  r"""FeatureSample holds fields in various types for testing purpose.

  Enums:
    ThirdValueValuesEnum:

  Messages:
    FifthValue: A FifthValue object.
    NinthValue: Map field.

  Fields:
    eighth: Repeated field.
    fifth: A FifthValue attribute.
    first: Singular scaler field.
    fourth: Singular Message fields.
    ninth: Map field.
    second: A integer attribute.
    seventh: A string attribute.
    sixth: A string attribute.
    third: A ThirdValueValuesEnum attribute.
  """

  class ThirdValueValuesEnum(_messages.Enum):
    r"""ThirdValueValuesEnum enum type.

    Values:
      BAR_UNSPECIFIED: <no description>
      FIRST: <no description>
      SECOND: <no description>
    """
    BAR_UNSPECIFIED = 0
    FIRST = 1
    SECOND = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FifthValue(_messages.Message):
    r"""A FifthValue object.

    Messages:
      AdditionalProperty: An additional property for a FifthValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FifthValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class NinthValue(_messages.Message):
    r"""Map field.

    Messages:
      AdditionalProperty: An additional property for a NinthValue object.

    Fields:
      additionalProperties: Additional properties of type NinthValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a NinthValue object.

      Fields:
        key: Name of the additional property.
        value: A HelloWorldFooBar attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('HelloWorldFooBar', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  eighth = _messages.MessageField('HelloWorldFooBar', 1, repeated=True)
  fifth = _messages.MessageField('FifthValue', 2)
  first = _messages.StringField(3)
  fourth = _messages.StringField(4)
  ninth = _messages.MessageField('NinthValue', 5)
  second = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  seventh = _messages.StringField(7)
  sixth = _messages.IntegerField(8)
  third = _messages.EnumField('ThirdValueValuesEnum', 9)


class HelloWorldFooBar(_messages.Message):
  r"""Nested Message.

  Fields:
    first: A string attribute.
    second: A integer attribute.
  """

  first = _messages.StringField(1)
  second = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class HelloWorldSpec(_messages.Message):
  r"""**Hello World**: Configuration for a single membership.

  Fields:
    customConfig: This should be a textpb string.
    featureSample: Fields for testing purpose.
  """

  customConfig = _messages.StringField(1)
  featureSample = _messages.MessageField('HelloWorldFeatureSample', 2)


class HelloWorldState(_messages.Message):
  r"""**Hello World**: State for a single membership, analyzed and reported by
  feature controller.
  """



class IdentityServiceAuthMethod(_messages.Message):
  r"""Configuration of an auth method for a member/cluster. Only one
  authentication method (e.g., OIDC and LDAP) can be set per AuthMethod.

  Fields:
    azureadConfig: AzureAD specific Configuration.
    googleConfig: GoogleConfig specific configuration
    ldapConfig: LDAP specific configuration.
    name: Identifier for auth config.
    oidcConfig: OIDC specific configuration.
    proxy: Proxy server address to use for auth method.
    samlConfig: SAML specific configuration.
  """

  azureadConfig = _messages.MessageField('IdentityServiceAzureADConfig', 1)
  googleConfig = _messages.MessageField('IdentityServiceGoogleConfig', 2)
  ldapConfig = _messages.MessageField('IdentityServiceLdapConfig', 3)
  name = _messages.StringField(4)
  oidcConfig = _messages.MessageField('IdentityServiceOidcConfig', 5)
  proxy = _messages.StringField(6)
  samlConfig = _messages.MessageField('IdentityServiceSamlConfig', 7)


class IdentityServiceAzureADConfig(_messages.Message):
  r"""Configuration for the AzureAD Auth flow.

  Fields:
    clientId: ID for the registered client application that makes
      authentication requests to the Azure AD identity provider.
    clientSecret: Input only. Unencrypted AzureAD client secret will be passed
      to the GKE Hub CLH.
    encryptedClientSecret: Output only. Encrypted AzureAD client secret.
    groupFormat: Optional. Format of the AzureAD groups that the client wants
      for auth.
    kubectlRedirectUri: The redirect URL that kubectl uses for authorization.
    tenant: Kind of Azure AD account to be authenticated. Supported values are
      or for accounts belonging to a specific tenant.
    userClaim: Optional. Claim in the AzureAD ID Token that holds the user
      details.
  """

  clientId = _messages.StringField(1)
  clientSecret = _messages.StringField(2)
  encryptedClientSecret = _messages.BytesField(3)
  groupFormat = _messages.StringField(4)
  kubectlRedirectUri = _messages.StringField(5)
  tenant = _messages.StringField(6)
  userClaim = _messages.StringField(7)


class IdentityServiceDiagnosticInterface(_messages.Message):
  r"""Configuration options for the AIS diagnostic interface.

  Fields:
    enabled: Determines whether to enable the diagnostic interface.
    expirationTime: Determines the expiration time of the diagnostic interface
      enablement. When reached, requests to the interface would be
      automatically rejected.
  """

  enabled = _messages.BooleanField(1)
  expirationTime = _messages.StringField(2)


class IdentityServiceGoogleConfig(_messages.Message):
  r"""Configuration for the Google Plugin Auth flow.

  Fields:
    disable: Disable automatic configuration of Google Plugin on supported
      platforms.
  """

  disable = _messages.BooleanField(1)


class IdentityServiceGroupConfig(_messages.Message):
  r"""Contains the properties for locating and authenticating groups in the
  directory.

  Fields:
    baseDn: Required. The location of the subtree in the LDAP directory to
      search for group entries.
    filter: Optional. Optional filter to be used when searching for groups a
      user belongs to. This can be used to explicitly match only certain
      groups in order to reduce the amount of groups returned for each user.
      This defaults to "(objectClass=Group)".
    idAttribute: Optional. The identifying name of each group a user belongs
      to. For example, if this is set to "distinguishedName" then RBACs and
      other group expectations should be written as full DNs. This defaults to
      "distinguishedName".
  """

  baseDn = _messages.StringField(1)
  filter = _messages.StringField(2)
  idAttribute = _messages.StringField(3)


class IdentityServiceIdentityServiceOptions(_messages.Message):
  r"""Holds non-protocol-related configuration options.

  Fields:
    diagnosticInterface: Configuration options for the AIS diagnostic
      interface.
    sessionDuration: Determines the lifespan of STS tokens issued by Anthos
      Identity Service.
  """

  diagnosticInterface = _messages.MessageField('IdentityServiceDiagnosticInterface', 1)
  sessionDuration = _messages.StringField(2)


class IdentityServiceLdapConfig(_messages.Message):
  r"""Configuration for the LDAP Auth flow.

  Fields:
    group: Optional. Contains the properties for locating and authenticating
      groups in the directory.
    server: Required. Server settings for the external LDAP server.
    serviceAccount: Required. Contains the credentials of the service account
      which is authorized to perform the LDAP search in the directory. The
      credentials can be supplied by the combination of the DN and password or
      the client certificate.
    user: Required. Defines where users exist in the LDAP directory.
  """

  group = _messages.MessageField('IdentityServiceGroupConfig', 1)
  server = _messages.MessageField('IdentityServiceServerConfig', 2)
  serviceAccount = _messages.MessageField('IdentityServiceServiceAccountConfig', 3)
  user = _messages.MessageField('IdentityServiceUserConfig', 4)


class IdentityServiceOidcConfig(_messages.Message):
  r"""Configuration for OIDC Auth flow.

  Fields:
    certificateAuthorityData: PEM-encoded CA for OIDC provider.
    clientId: ID for OIDC client application.
    clientSecret: Input only. Unencrypted OIDC client secret will be passed to
      the GKE Hub CLH.
    deployCloudConsoleProxy: Flag to denote if reverse proxy is used to
      connect to auth provider. This flag should be set to true when provider
      is not reachable by Google Cloud Console.
    enableAccessToken: Enable access token.
    encryptedClientSecret: Output only. Encrypted OIDC Client secret
    extraParams: Comma-separated list of key-value pairs.
    groupPrefix: Prefix to prepend to group name.
    groupsClaim: Claim in OIDC ID token that holds group information.
    issuerUri: URI for the OIDC provider. This should point to the level below
      .well-known/openid-configuration.
    kubectlRedirectUri: Registered redirect uri to redirect users going
      through OAuth flow using kubectl plugin.
    scopes: Comma-separated list of identifiers.
    userClaim: Claim in OIDC ID token that holds username.
    userPrefix: Prefix to prepend to user name.
  """

  certificateAuthorityData = _messages.StringField(1)
  clientId = _messages.StringField(2)
  clientSecret = _messages.StringField(3)
  deployCloudConsoleProxy = _messages.BooleanField(4)
  enableAccessToken = _messages.BooleanField(5)
  encryptedClientSecret = _messages.BytesField(6)
  extraParams = _messages.StringField(7)
  groupPrefix = _messages.StringField(8)
  groupsClaim = _messages.StringField(9)
  issuerUri = _messages.StringField(10)
  kubectlRedirectUri = _messages.StringField(11)
  scopes = _messages.StringField(12)
  userClaim = _messages.StringField(13)
  userPrefix = _messages.StringField(14)


class IdentityServiceSamlConfig(_messages.Message):
  r"""Configuration for the SAML Auth flow.

  Messages:
    AttributeMappingValue: Optional. The mapping of additional user attributes
      like nickname, birthday and address etc.. `key` is the name of this
      additional attribute. `value` is a string presenting as CEL(common
      expression language, go/cel) used for getting the value from the
      resources. Take nickname as an example, in this case, `key` is
      "attribute.nickname" and `value` is "assertion.nickname".

  Fields:
    attributeMapping: Optional. The mapping of additional user attributes like
      nickname, birthday and address etc.. `key` is the name of this
      additional attribute. `value` is a string presenting as CEL(common
      expression language, go/cel) used for getting the value from the
      resources. Take nickname as an example, in this case, `key` is
      "attribute.nickname" and `value` is "assertion.nickname".
    groupPrefix: Optional. Prefix to prepend to group name.
    groupsAttribute: Optional. The SAML attribute to read groups from. This
      value is expected to be a string and will be passed along as-is (with
      the option of being prefixed by the `group_prefix`).
    identityProviderCertificates: Required. The list of IdP certificates to
      validate the SAML response against.
    identityProviderId: Required. The entity ID of the SAML IdP.
    identityProviderSsoUri: Required. The URI where the SAML IdP exposes the
      SSO service.
    userAttribute: Optional. The SAML attribute to read username from. If
      unspecified, the username will be read from the NameID element of the
      assertion in SAML response. This value is expected to be a string and
      will be passed along as-is (with the option of being prefixed by the
      `user_prefix`).
    userPrefix: Optional. Prefix to prepend to user name.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AttributeMappingValue(_messages.Message):
    r"""Optional. The mapping of additional user attributes like nickname,
    birthday and address etc.. `key` is the name of this additional attribute.
    `value` is a string presenting as CEL(common expression language, go/cel)
    used for getting the value from the resources. Take nickname as an
    example, in this case, `key` is "attribute.nickname" and `value` is
    "assertion.nickname".

    Messages:
      AdditionalProperty: An additional property for a AttributeMappingValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        AttributeMappingValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AttributeMappingValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attributeMapping = _messages.MessageField('AttributeMappingValue', 1)
  groupPrefix = _messages.StringField(2)
  groupsAttribute = _messages.StringField(3)
  identityProviderCertificates = _messages.StringField(4, repeated=True)
  identityProviderId = _messages.StringField(5)
  identityProviderSsoUri = _messages.StringField(6)
  userAttribute = _messages.StringField(7)
  userPrefix = _messages.StringField(8)


class IdentityServiceServerConfig(_messages.Message):
  r"""Server settings for the external LDAP server.

  Fields:
    certificateAuthorityData: Optional. Contains a Base64 encoded, PEM
      formatted certificate authority certificate for the LDAP server. This
      must be provided for the "ldaps" and "startTLS" connections.
    connectionType: Optional. Defines the connection type to communicate with
      the LDAP server. If `starttls` or `ldaps` is specified, the
      certificate_authority_data should not be empty.
    host: Required. Defines the hostname or IP of the LDAP server. Port is
      optional and will default to 389, if unspecified. For example,
      "ldap.server.example" or "***********:389".
  """

  certificateAuthorityData = _messages.BytesField(1)
  connectionType = _messages.StringField(2)
  host = _messages.StringField(3)


class IdentityServiceServiceAccountConfig(_messages.Message):
  r"""Contains the credentials of the service account which is authorized to
  perform the LDAP search in the directory. The credentials can be supplied by
  the combination of the DN and password or the client certificate.

  Fields:
    simpleBindCredentials: Credentials for basic auth.
  """

  simpleBindCredentials = _messages.MessageField('IdentityServiceSimpleBindCredentials', 1)


class IdentityServiceSimpleBindCredentials(_messages.Message):
  r"""The structure holds the LDAP simple binding credential.

  Fields:
    dn: Required. The distinguished name(DN) of the service account
      object/user.
    encryptedPassword: Output only. The encrypted password of the service
      account object/user.
    password: Required. Input only. The password of the service account
      object/user.
  """

  dn = _messages.StringField(1)
  encryptedPassword = _messages.BytesField(2)
  password = _messages.StringField(3)


class IdentityServiceSpec(_messages.Message):
  r"""**IdentityService**: Configuration for a single membership.

  Fields:
    authMethods: A member may support multiple auth methods.
    identityServiceOptions: Optional. non-protocol-related configuration
      options.
  """

  authMethods = _messages.MessageField('IdentityServiceAuthMethod', 1, repeated=True)
  identityServiceOptions = _messages.MessageField('IdentityServiceIdentityServiceOptions', 2)


class IdentityServiceState(_messages.Message):
  r"""**IdentityService**: State for a single membership, analyzed and
  reported by feature controller.

  Enums:
    StateValueValuesEnum: Deployment state on this member

  Fields:
    failureReason: The reason of the failure.
    installedVersion: Installed AIS version. This is the AIS version installed
      on this member. The values makes sense iff state is OK.
    memberConfig: Last reconciled membership configuration
    state: Deployment state on this member
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Deployment state on this member

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Unspecified state
      OK: deployment succeeds
      ERROR: Failure with error.
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    OK = 1
    ERROR = 2

  failureReason = _messages.StringField(1)
  installedVersion = _messages.StringField(2)
  memberConfig = _messages.MessageField('IdentityServiceSpec', 3)
  state = _messages.EnumField('StateValueValuesEnum', 4)


class IdentityServiceUserConfig(_messages.Message):
  r"""Defines where users exist in the LDAP directory.

  Fields:
    baseDn: Required. The location of the subtree in the LDAP directory to
      search for user entries.
    filter: Optional. Filter to apply when searching for the user. This can be
      used to further restrict the user accounts which are allowed to login.
      This defaults to "(objectClass=User)".
    idAttribute: Optional. Determines which attribute to use as the user's
      identity after they are authenticated. This is distinct from the
      loginAttribute field to allow users to login with a username, but then
      have their actual identifier be an email address or full Distinguished
      Name (DN). For example, setting loginAttribute to "sAMAccountName" and
      identifierAttribute to "userPrincipalName" would allow a user to login
      as "bsmith", but actual RBAC policies for the user would be written as
      "<EMAIL>". Using "userPrincipalName" is recommended since
      this will be unique for each user. This defaults to "userPrincipalName".
    loginAttribute: Optional. The name of the attribute which matches against
      the input username. This is used to find the user in the LDAP database
      e.g. "(=)" and is combined with the optional filter field. This defaults
      to "userPrincipalName".
  """

  baseDn = _messages.StringField(1)
  filter = _messages.StringField(2)
  idAttribute = _messages.StringField(3)
  loginAttribute = _messages.StringField(4)


class LifecycleState(_messages.Message):
  r"""LifecycleState describes the state of a MembershipFeature *resource* in
  the GkeHub API. See `FeatureState` for the "running state" of the
  MembershipFeature.

  Enums:
    StateValueValuesEnum: Output only. The current state of the Feature
      resource in the Hub API.

  Fields:
    state: Output only. The current state of the Feature resource in the Hub
      API.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the Feature resource in the Hub API.

    Values:
      STATE_UNSPECIFIED: State is unknown or not set.
      ENABLING: The MembershipFeature is being enabled, and the
        MembershipFeature resource is being created. Once complete, the
        corresponding MembershipFeature will be enabled in this Hub.
      ACTIVE: The MembershipFeature is enabled in this Hub, and the
        MembershipFeature resource is fully available.
      DISABLING: The MembershipFeature is being disabled in this Hub, and the
        MembershipFeature resource is being deleted.
      UPDATING: The MembershipFeature resource is being updated.
      SERVICE_UPDATING: The MembershipFeature resource is being updated by the
        Hub Service.
    """
    STATE_UNSPECIFIED = 0
    ENABLING = 1
    ACTIVE = 2
    DISABLING = 3
    UPDATING = 4
    SERVICE_UPDATING = 5

  state = _messages.EnumField('StateValueValuesEnum', 1)


class ListFeatureConfigsResponse(_messages.Message):
  r"""Response message for the `GkeHubFeature.ListFeatureConfigs` method.

  Fields:
    featureConfigs: The list of FeatureConfig(s).
    nextPageToken: A token to request the next page of resources from the
      `ListFeatureConfigs` method. The value of an empty string means that
      there are no more resources to return.
  """

  featureConfigs = _messages.MessageField('FeatureConfig', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListMembershipFeaturesResponse(_messages.Message):
  r"""Response message for the `GkeHubFeature.ListMembershipFeatures` method.

  Fields:
    membershipFeatures: The list of matching MembershipFeatures.
    nextPageToken: A token to request the next page of resources from the
      `ListMembershipFeatures` method. The value of an empty string means that
      there are no more resources to return.
    unreachable: List of locations that could not be reached while fetching
      this list.
  """

  membershipFeatures = _messages.MessageField('MembershipFeature', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListReferencesRequest(_messages.Message):
  r"""The ListResourceMetadataRequest request.

  Fields:
    pageSize: The maximum number of items to return. If unspecified, server
      will pick an appropriate default. Server may return fewer items than
      requested. A caller should only rely on response's next_page_token to
      determine if there are more References left to be queried.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: Required. The parent resource name (target_resource of this
      reference). For example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3)


class ListReferencesResponse(_messages.Message):
  r"""The ListReferencesResponse response.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    references: The list of references.
  """

  nextPageToken = _messages.StringField(1)
  references = _messages.MessageField('Reference', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class MembershipFeature(_messages.Message):
  r"""MembershipFeature represents the settings and status of a Fleet Feature
  enabled on a single Fleet Membership.

  Messages:
    LabelsValue: GCP labels for this MembershipFeature.

  Fields:
    createTime: Output only. When the MembershipFeature resource was created.
    deleteTime: Output only. When the MembershipFeature resource was deleted.
    featureConfigRef: Reference information for a FeatureConfig applied on the
      MembershipFeature.
    labels: GCP labels for this MembershipFeature.
    lifecycleState: Output only. Lifecycle information of the resource itself.
    name: Output only. The resource name of the membershipFeature, in the
      format: `projects/{project}/locations/{location}/memberships/{membership
      }/features/{feature}`. Note that `membershipFeatures` is shortened to
      `features` in the resource name. (see http://go/aip/122#collection-
      identifiers)
    spec: Optional. Spec of this membershipFeature.
    state: Output only. State of the this membershipFeature.
    updateTime: Output only. When the MembershipFeature resource was last
      updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""GCP labels for this MembershipFeature.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  deleteTime = _messages.StringField(2)
  featureConfigRef = _messages.MessageField('FeatureConfigRef', 3)
  labels = _messages.MessageField('LabelsValue', 4)
  lifecycleState = _messages.MessageField('LifecycleState', 5)
  name = _messages.StringField(6)
  spec = _messages.MessageField('FeatureSpec', 7)
  state = _messages.MessageField('FeatureState', 8)
  updateTime = _messages.StringField(9)


class MeteringState(_messages.Message):
  r"""**Metering**: State for a single membership, analyzed and reported by
  feature controller.

  Fields:
    lastMeasurementTime: The time stamp of the most recent measurement of the
      number of vCPUs in the cluster.
    preciseLastMeasuredClusterVcpuCapacity: The vCPUs capacity in the cluster
      according to the most recent measurement (1/1000 precision).
  """

  lastMeasurementTime = _messages.StringField(1)
  preciseLastMeasuredClusterVcpuCapacity = _messages.FloatField(2, variant=_messages.Variant.FLOAT)


class NamespaceActuationSpec(_messages.Message):
  r"""Spec for FNS actuation feature."""


class NamespaceActuationState(_messages.Message):
  r"""FNS actuation Feature state."""


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('GoogleRpcStatus', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    cancelRequested: Output only. Identifies whether the user has requested
      cancellation of the operation. Operations that have successfully been
      cancelled have Operation.error value with a google.rpc.Status.code of 1,
      corresponding to `Code.CANCELLED`.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    statusDetail: Output only. Human-readable status of the operation, if any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  cancelRequested = _messages.BooleanField(2)
  createTime = _messages.StringField(3)
  endTime = _messages.StringField(4)
  statusDetail = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Origin(_messages.Message):
  r"""Origin defines where this FeatureSpec originated from.

  Enums:
    TypeValueValuesEnum: Type specifies which type of origin is set.

  Fields:
    type: Type specifies which type of origin is set.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type specifies which type of origin is set.

    Values:
      TYPE_UNSPECIFIED: Type is unknown or not set.
      FLEET: Per-Feature spec was inherited from the fleet-level default.
      FLEET_OUT_OF_SYNC: Per-Feature spec was inherited from the fleet-level
        default but is now out of sync with the current default.
      USER: Per-Feature spec was inherited from a user specification.
    """
    TYPE_UNSPECIFIED = 0
    FLEET = 1
    FLEET_OUT_OF_SYNC = 2
    USER = 3

  type = _messages.EnumField('TypeValueValuesEnum', 1)


class PolicyControllerBundleInstallSpec(_messages.Message):
  r"""BundleInstallSpec is the specification configuration for a single
  managed bundle.

  Fields:
    exemptedNamespaces: the set of namespaces to be exempted from the bundle
  """

  exemptedNamespaces = _messages.StringField(1, repeated=True)


class PolicyControllerHubConfig(_messages.Message):
  r"""Configuration for Policy Controller

  Enums:
    InstallSpecValueValuesEnum: The install_spec represents the intended state
      specified by the latest request that mutated install_spec in the feature
      spec, not the lifecycle state of the feature observed by the Hub feature
      controller that is reported in the feature state.

  Messages:
    DeploymentConfigsValue: Map of deployment configs to deployments
      ("admission", "audit", "mutation").

  Fields:
    auditIntervalSeconds: Sets the interval for Policy Controller Audit Scans
      (in seconds). When set to 0, this disables audit functionality
      altogether.
    constraintViolationLimit: The maximum number of audit violations to be
      stored in a constraint. If not set, the internal default (currently 20)
      will be used.
    deploymentConfigs: Map of deployment configs to deployments ("admission",
      "audit", "mutation").
    exemptableNamespaces: The set of namespaces that are excluded from Policy
      Controller checks. Namespaces do not need to currently exist on the
      cluster.
    installSpec: The install_spec represents the intended state specified by
      the latest request that mutated install_spec in the feature spec, not
      the lifecycle state of the feature observed by the Hub feature
      controller that is reported in the feature state.
    logDeniesEnabled: Logs all denies and dry run failures.
    monitoring: Monitoring specifies the configuration of monitoring.
    mutationEnabled: Enables the ability to mutate resources using Policy
      Controller.
    policyContent: Specifies the desired policy content on the cluster
    referentialRulesEnabled: Enables the ability to use Constraint Templates
      that reference to objects other than the object currently being
      evaluated.
  """

  class InstallSpecValueValuesEnum(_messages.Enum):
    r"""The install_spec represents the intended state specified by the latest
    request that mutated install_spec in the feature spec, not the lifecycle
    state of the feature observed by the Hub feature controller that is
    reported in the feature state.

    Values:
      INSTALL_SPEC_UNSPECIFIED: Spec is unknown.
      INSTALL_SPEC_NOT_INSTALLED: Request to uninstall Policy Controller.
      INSTALL_SPEC_ENABLED: Request to install and enable Policy Controller.
      INSTALL_SPEC_SUSPENDED: Request to suspend Policy Controller i.e. its
        webhooks. If Policy Controller is not installed, it will be installed
        but suspended.
      INSTALL_SPEC_DETACHED: Request to stop all reconciliation actions by
        PoCo Hub controller. This is a breakglass mechanism to stop PoCo Hub
        from affecting cluster resources.
    """
    INSTALL_SPEC_UNSPECIFIED = 0
    INSTALL_SPEC_NOT_INSTALLED = 1
    INSTALL_SPEC_ENABLED = 2
    INSTALL_SPEC_SUSPENDED = 3
    INSTALL_SPEC_DETACHED = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DeploymentConfigsValue(_messages.Message):
    r"""Map of deployment configs to deployments ("admission", "audit",
    "mutation").

    Messages:
      AdditionalProperty: An additional property for a DeploymentConfigsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        DeploymentConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DeploymentConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A PolicyControllerPolicyControllerDeploymentConfig attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('PolicyControllerPolicyControllerDeploymentConfig', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  auditIntervalSeconds = _messages.IntegerField(1)
  constraintViolationLimit = _messages.IntegerField(2)
  deploymentConfigs = _messages.MessageField('DeploymentConfigsValue', 3)
  exemptableNamespaces = _messages.StringField(4, repeated=True)
  installSpec = _messages.EnumField('InstallSpecValueValuesEnum', 5)
  logDeniesEnabled = _messages.BooleanField(6)
  monitoring = _messages.MessageField('PolicyControllerMonitoringConfig', 7)
  mutationEnabled = _messages.BooleanField(8)
  policyContent = _messages.MessageField('PolicyControllerPolicyContentSpec', 9)
  referentialRulesEnabled = _messages.BooleanField(10)


class PolicyControllerMonitoringConfig(_messages.Message):
  r"""MonitoringConfig specifies the backends Policy Controller should export
  metrics to. For example, to specify metrics should be exported to Cloud
  Monitoring and Prometheus, specify backends: ["cloudmonitoring",
  "prometheus"]

  Enums:
    BackendsValueListEntryValuesEnum:

  Fields:
    backends: Specifies the list of backends Policy Controller will export to.
      An empty list would effectively disable metrics export.
  """

  class BackendsValueListEntryValuesEnum(_messages.Enum):
    r"""BackendsValueListEntryValuesEnum enum type.

    Values:
      MONITORING_BACKEND_UNSPECIFIED: Backend cannot be determined
      PROMETHEUS: Prometheus backend for monitoring
      CLOUD_MONITORING: Stackdriver/Cloud Monitoring backend for monitoring
    """
    MONITORING_BACKEND_UNSPECIFIED = 0
    PROMETHEUS = 1
    CLOUD_MONITORING = 2

  backends = _messages.EnumField('BackendsValueListEntryValuesEnum', 1, repeated=True)


class PolicyControllerOnClusterState(_messages.Message):
  r"""OnClusterState represents the state of a sub-component of Policy
  Controller.

  Enums:
    StateValueValuesEnum: The lifecycle state of this component.

  Fields:
    details: Surface potential errors or information logs.
    state: The lifecycle state of this component.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The lifecycle state of this component.

    Values:
      LIFECYCLE_STATE_UNSPECIFIED: The lifecycle state is unspecified.
      NOT_INSTALLED: The PC does not exist on the given cluster, and no k8s
        resources of any type that are associated with the PC should exist
        there. The cluster does not possess a membership with the PCH.
      INSTALLING: The PCH possesses a Membership, however the PC is not fully
        installed on the cluster. In this state the hub can be expected to be
        taking actions to install the PC on the cluster.
      ACTIVE: The PC is fully installed on the cluster and in an operational
        mode. In this state PCH will be reconciling state with the PC, and the
        PC will be performing it's operational tasks per that software.
        Entering a READY state requires that the hub has confirmed the PC is
        installed and its pods are operational with the version of the PC the
        PCH expects.
      UPDATING: The PC is fully installed, but in the process of changing the
        configuration (including changing the version of PC either up and
        down, or modifying the manifests of PC) of the resources running on
        the cluster. The PCH has a Membership, is aware of the version the
        cluster should be running in, but has not confirmed for itself that
        the PC is running with that version.
      DECOMMISSIONING: The PC may have resources on the cluster, but the PCH
        wishes to remove the Membership. The Membership still exists.
      CLUSTER_ERROR: The PC is not operational, and the PCH is unable to act
        to make it operational. Entering a CLUSTER_ERROR state happens
        automatically when the PCH determines that a PC installed on the
        cluster is non-operative or that the cluster does not meet
        requirements set for the PCH to administer the cluster but has
        nevertheless been given an instruction to do so (such as 'install').
      HUB_ERROR: In this state, the PC may still be operational, and only the
        PCH is unable to act. The hub should not issue instructions to change
        the PC state, or otherwise interfere with the on-cluster resources.
        Entering a HUB_ERROR state happens automatically when the PCH
        determines the hub is in an unhealthy state and it wishes to 'take
        hands off' to avoid corrupting the PC or other data.
      SUSPENDED: Policy Controller (PC) is installed but suspended. This means
        that the policies are not enforced, but violations are still recorded
        (through audit).
      DETACHED: PoCo Hub is not taking any action to reconcile cluster
        objects. Changes to those objects will not be overwritten by PoCo Hub.
    """
    LIFECYCLE_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLING = 2
    ACTIVE = 3
    UPDATING = 4
    DECOMMISSIONING = 5
    CLUSTER_ERROR = 6
    HUB_ERROR = 7
    SUSPENDED = 8
    DETACHED = 9

  details = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class PolicyControllerPolicyContentSpec(_messages.Message):
  r"""PolicyContentSpec defines the user's desired content configuration on
  the cluster.

  Messages:
    BundlesValue: map of bundle name to BundleInstallSpec. The bundle name
      maps to the `bundleName` key in the
      `policycontroller.gke.io/constraintData` annotation on a constraint.

  Fields:
    bundles: map of bundle name to BundleInstallSpec. The bundle name maps to
      the `bundleName` key in the `policycontroller.gke.io/constraintData`
      annotation on a constraint.
    templateLibrary: Configures the installation of the Template Library.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class BundlesValue(_messages.Message):
    r"""map of bundle name to BundleInstallSpec. The bundle name maps to the
    `bundleName` key in the `policycontroller.gke.io/constraintData`
    annotation on a constraint.

    Messages:
      AdditionalProperty: An additional property for a BundlesValue object.

    Fields:
      additionalProperties: Additional properties of type BundlesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a BundlesValue object.

      Fields:
        key: Name of the additional property.
        value: A PolicyControllerBundleInstallSpec attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('PolicyControllerBundleInstallSpec', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  bundles = _messages.MessageField('BundlesValue', 1)
  templateLibrary = _messages.MessageField('PolicyControllerTemplateLibraryConfig', 2)


class PolicyControllerPolicyContentState(_messages.Message):
  r"""The state of the policy controller policy content

  Messages:
    BundleStatesValue: The state of the any bundles included in the chosen
      version of the manifest

  Fields:
    bundleStates: The state of the any bundles included in the chosen version
      of the manifest
    referentialSyncConfigState: The state of the referential data sync
      configuration. This could represent the state of either the syncSet
      object(s) or the config object, depending on the version of PoCo
      configured by the user.
    templateLibraryState: The state of the template library
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class BundleStatesValue(_messages.Message):
    r"""The state of the any bundles included in the chosen version of the
    manifest

    Messages:
      AdditionalProperty: An additional property for a BundleStatesValue
        object.

    Fields:
      additionalProperties: Additional properties of type BundleStatesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a BundleStatesValue object.

      Fields:
        key: Name of the additional property.
        value: A PolicyControllerOnClusterState attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('PolicyControllerOnClusterState', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  bundleStates = _messages.MessageField('BundleStatesValue', 1)
  referentialSyncConfigState = _messages.MessageField('PolicyControllerOnClusterState', 2)
  templateLibraryState = _messages.MessageField('PolicyControllerOnClusterState', 3)


class PolicyControllerPolicyControllerDeploymentConfig(_messages.Message):
  r"""Deployment-specific configuration.

  Enums:
    PodAffinityValueValuesEnum: Pod affinity configuration.

  Fields:
    containerResources: Container resource requirements.
    podAffinity: Pod affinity configuration.
    podAntiAffinity: Pod anti-affinity enablement. Deprecated: use
      `pod_affinity` instead.
    podTolerations: Pod tolerations of node taints.
    replicaCount: Pod replica count.
  """

  class PodAffinityValueValuesEnum(_messages.Enum):
    r"""Pod affinity configuration.

    Values:
      AFFINITY_UNSPECIFIED: No affinity configuration has been specified.
      NO_AFFINITY: Affinity configurations will be removed from the
        deployment.
      ANTI_AFFINITY: Anti-affinity configuration will be applied to this
        deployment. Default for admissions deployment.
    """
    AFFINITY_UNSPECIFIED = 0
    NO_AFFINITY = 1
    ANTI_AFFINITY = 2

  containerResources = _messages.MessageField('PolicyControllerResourceRequirements', 1)
  podAffinity = _messages.EnumField('PodAffinityValueValuesEnum', 2)
  podAntiAffinity = _messages.BooleanField(3)
  podTolerations = _messages.MessageField('PolicyControllerToleration', 4, repeated=True)
  replicaCount = _messages.IntegerField(5)


class PolicyControllerResourceList(_messages.Message):
  r"""ResourceList contains container resource requirements.

  Fields:
    cpu: CPU requirement expressed in Kubernetes resource units.
    memory: Memory requirement expressed in Kubernetes resource units.
  """

  cpu = _messages.StringField(1)
  memory = _messages.StringField(2)


class PolicyControllerResourceRequirements(_messages.Message):
  r"""ResourceRequirements describes the compute resource requirements.

  Fields:
    limits: Limits describes the maximum amount of compute resources allowed
      for use by the running container.
    requests: Requests describes the amount of compute resources reserved for
      the container by the kube-scheduler.
  """

  limits = _messages.MessageField('PolicyControllerResourceList', 1)
  requests = _messages.MessageField('PolicyControllerResourceList', 2)


class PolicyControllerSpec(_messages.Message):
  r"""**Policy Controller**: Configuration for a single cluster. Intended to
  parallel the PolicyController CR.

  Fields:
    policyControllerHubConfig: Policy Controller configuration for the
      cluster.
    version: Version of Policy Controller installed.
  """

  policyControllerHubConfig = _messages.MessageField('PolicyControllerHubConfig', 1)
  version = _messages.StringField(2)


class PolicyControllerState(_messages.Message):
  r"""**Policy Controller**: State for a single cluster.

  Enums:
    StateValueValuesEnum: The overall Policy Controller lifecycle state
      observed by the Hub Feature controller.

  Messages:
    ComponentStatesValue: Currently these include (also serving as map keys):
      1. "admission" 2. "audit" 3. "mutation"

  Fields:
    componentStates: Currently these include (also serving as map keys): 1.
      "admission" 2. "audit" 3. "mutation"
    policyContentState: The overall content state observed by the Hub Feature
      controller.
    state: The overall Policy Controller lifecycle state observed by the Hub
      Feature controller.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The overall Policy Controller lifecycle state observed by the Hub
    Feature controller.

    Values:
      LIFECYCLE_STATE_UNSPECIFIED: The lifecycle state is unspecified.
      NOT_INSTALLED: The PC does not exist on the given cluster, and no k8s
        resources of any type that are associated with the PC should exist
        there. The cluster does not possess a membership with the PCH.
      INSTALLING: The PCH possesses a Membership, however the PC is not fully
        installed on the cluster. In this state the hub can be expected to be
        taking actions to install the PC on the cluster.
      ACTIVE: The PC is fully installed on the cluster and in an operational
        mode. In this state PCH will be reconciling state with the PC, and the
        PC will be performing it's operational tasks per that software.
        Entering a READY state requires that the hub has confirmed the PC is
        installed and its pods are operational with the version of the PC the
        PCH expects.
      UPDATING: The PC is fully installed, but in the process of changing the
        configuration (including changing the version of PC either up and
        down, or modifying the manifests of PC) of the resources running on
        the cluster. The PCH has a Membership, is aware of the version the
        cluster should be running in, but has not confirmed for itself that
        the PC is running with that version.
      DECOMMISSIONING: The PC may have resources on the cluster, but the PCH
        wishes to remove the Membership. The Membership still exists.
      CLUSTER_ERROR: The PC is not operational, and the PCH is unable to act
        to make it operational. Entering a CLUSTER_ERROR state happens
        automatically when the PCH determines that a PC installed on the
        cluster is non-operative or that the cluster does not meet
        requirements set for the PCH to administer the cluster but has
        nevertheless been given an instruction to do so (such as 'install').
      HUB_ERROR: In this state, the PC may still be operational, and only the
        PCH is unable to act. The hub should not issue instructions to change
        the PC state, or otherwise interfere with the on-cluster resources.
        Entering a HUB_ERROR state happens automatically when the PCH
        determines the hub is in an unhealthy state and it wishes to 'take
        hands off' to avoid corrupting the PC or other data.
      SUSPENDED: Policy Controller (PC) is installed but suspended. This means
        that the policies are not enforced, but violations are still recorded
        (through audit).
      DETACHED: PoCo Hub is not taking any action to reconcile cluster
        objects. Changes to those objects will not be overwritten by PoCo Hub.
    """
    LIFECYCLE_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLING = 2
    ACTIVE = 3
    UPDATING = 4
    DECOMMISSIONING = 5
    CLUSTER_ERROR = 6
    HUB_ERROR = 7
    SUSPENDED = 8
    DETACHED = 9

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ComponentStatesValue(_messages.Message):
    r"""Currently these include (also serving as map keys): 1. "admission" 2.
    "audit" 3. "mutation"

    Messages:
      AdditionalProperty: An additional property for a ComponentStatesValue
        object.

    Fields:
      additionalProperties: Additional properties of type ComponentStatesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ComponentStatesValue object.

      Fields:
        key: Name of the additional property.
        value: A PolicyControllerOnClusterState attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('PolicyControllerOnClusterState', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  componentStates = _messages.MessageField('ComponentStatesValue', 1)
  policyContentState = _messages.MessageField('PolicyControllerPolicyContentState', 2)
  state = _messages.EnumField('StateValueValuesEnum', 3)


class PolicyControllerTemplateLibraryConfig(_messages.Message):
  r"""The config specifying which default library templates to install.

  Enums:
    InstallationValueValuesEnum: Configures the manner in which the template
      library is installed on the cluster.

  Fields:
    installation: Configures the manner in which the template library is
      installed on the cluster.
  """

  class InstallationValueValuesEnum(_messages.Enum):
    r"""Configures the manner in which the template library is installed on
    the cluster.

    Values:
      INSTALLATION_UNSPECIFIED: No installation strategy has been specified.
      NOT_INSTALLED: Do not install the template library.
      ALL: Install the entire template library.
    """
    INSTALLATION_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    ALL = 2

  installation = _messages.EnumField('InstallationValueValuesEnum', 1)


class PolicyControllerToleration(_messages.Message):
  r"""Toleration of a node taint.

  Fields:
    effect: Matches a taint effect.
    key: Matches a taint key (not necessarily unique).
    operator: Matches a taint operator.
    value: Matches a taint value.
  """

  effect = _messages.StringField(1)
  key = _messages.StringField(2)
  operator = _messages.StringField(3)
  value = _messages.StringField(4)


class RBACRoleBindingActuationRBACRoleBindingState(_messages.Message):
  r"""RBACRoleBindingState is the status of an RBACRoleBinding which exists on
  a membership.

  Enums:
    StateValueValuesEnum: Output only. The state of the RBACRoleBinding.

  Fields:
    description: The reason for the failure.
    state: Output only. The state of the RBACRoleBinding.
    updateTime: The time the RBACRoleBinding status was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the RBACRoleBinding.

    Values:
      ROLE_BINDING_STATE_UNSPECIFIED: Unspecified state.
      OK: RBACRoleBinding is created properly on the cluster.
      CUSTOM_ROLE_MISSING_FROM_CLUSTER: The RBACRoleBinding was created on the
        cluster but the specified custom role does not exist on the cluster,
        hence the RBACRoleBinding has no effect.
    """
    ROLE_BINDING_STATE_UNSPECIFIED = 0
    OK = 1
    CUSTOM_ROLE_MISSING_FROM_CLUSTER = 2

  description = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)
  updateTime = _messages.StringField(3)


class RBACRoleBindingActuationSpec(_messages.Message):
  r"""**RBAC RoleBinding Actuation**: The membership-specific input for
  RBACRoleBindingActuation feature.
  """



class RBACRoleBindingActuationState(_messages.Message):
  r"""**RBAC RoleBinding Actuation**: A membership-specific Feature state for
  the RBACRoleBindingActuation fleet feature.

  Messages:
    RbacrolebindingStatesValue: Output only. The state of RBACRoleBindings
      using custom roles that exist on the cluster, keyed by RBACRoleBinding
      resource name with format: projects/{project}/locations/{location}/scope
      s/{scope}/rbacrolebindings/{rbacrolebinding}.

  Fields:
    rbacrolebindingStates: Output only. The state of RBACRoleBindings using
      custom roles that exist on the cluster, keyed by RBACRoleBinding
      resource name with format: projects/{project}/locations/{location}/scope
      s/{scope}/rbacrolebindings/{rbacrolebinding}.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class RbacrolebindingStatesValue(_messages.Message):
    r"""Output only. The state of RBACRoleBindings using custom roles that
    exist on the cluster, keyed by RBACRoleBinding resource name with format:
    projects/{project}/locations/{location}/scopes/{scope}/rbacrolebindings/{r
    bacrolebinding}.

    Messages:
      AdditionalProperty: An additional property for a
        RbacrolebindingStatesValue object.

    Fields:
      additionalProperties: Additional properties of type
        RbacrolebindingStatesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a RbacrolebindingStatesValue object.

      Fields:
        key: Name of the additional property.
        value: A RBACRoleBindingActuationRBACRoleBindingState attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('RBACRoleBindingActuationRBACRoleBindingState', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  rbacrolebindingStates = _messages.MessageField('RbacrolebindingStatesValue', 1)


class Reference(_messages.Message):
  r"""Represents a reference to a resource.

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    createTime: Output only. The creation time.
    details: Details of the reference type with no implied semantics.
      Cumulative size of the field must not be more than 1KiB.
    name: Output only. Relative resource name of the reference. Includes
      target resource as a parent and reference uid
      `{target_resource}/references/{reference_id}`. For example,
      `projects/{my-project}/locations/{location}/instances/{my-
      instance}/references/{xyz}`.
    sourceResource: Required. Full resource name of the resource which refers
      the target resource. For example:
      //tpu.googleapis.com/projects/myproject/nodes/mynode
    targetUniqueId: Output only. The unique_id of the target resource. Example
      1: (For arcus resource) A-1-0-2-387420123-13-913517247483640811
      unique_id format defined in go/m11n-unique-id-as-resource-id Example 2:
      (For CCFE resource) 123e4567-e89b-12d3-a456-************
    type: Required. Type of the reference. A service might impose limits on
      number of references of a specific type. Note: It's recommended to use
      CAPITALS_WITH_UNDERSCORES style for a type name.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  name = _messages.StringField(3)
  sourceResource = _messages.StringField(4)
  targetUniqueId = _messages.StringField(5)
  type = _messages.StringField(6)


class ServiceMeshAnalysisMessage(_messages.Message):
  r"""AnalysisMessage is a single message produced by an analyzer, and it used
  to communicate to the end user about the state of their Service Mesh
  configuration.

  Messages:
    ArgsValue: A UI can combine these args with a template (based on
      message_base.type) to produce an internationalized message.

  Fields:
    args: A UI can combine these args with a template (based on
      message_base.type) to produce an internationalized message.
    description: A human readable description of what the error means. It is
      suitable for non-internationalize display purposes.
    messageBase: Details common to all types of Istio and ServiceMesh analysis
      messages.
    resourcePaths: A list of strings specifying the resource identifiers that
      were the cause of message generation. A "path" here may be: *
      MEMBERSHIP_ID if the cause is a specific member cluster *
      MEMBERSHIP_ID/(NAMESPACE\/)?RESOURCETYPE/NAME if the cause is a resource
      in a cluster
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ArgsValue(_messages.Message):
    r"""A UI can combine these args with a template (based on
    message_base.type) to produce an internationalized message.

    Messages:
      AdditionalProperty: An additional property for a ArgsValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ArgsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  args = _messages.MessageField('ArgsValue', 1)
  description = _messages.StringField(2)
  messageBase = _messages.MessageField('ServiceMeshAnalysisMessageBase', 3)
  resourcePaths = _messages.StringField(4, repeated=True)


class ServiceMeshAnalysisMessageBase(_messages.Message):
  r"""AnalysisMessageBase describes some common information that is needed for
  all messages.

  Enums:
    LevelValueValuesEnum: Represents how severe a message is.

  Fields:
    documentationUrl: A url pointing to the Service Mesh or Istio
      documentation for this specific error type.
    level: Represents how severe a message is.
    type: Represents the specific type of a message.
  """

  class LevelValueValuesEnum(_messages.Enum):
    r"""Represents how severe a message is.

    Values:
      LEVEL_UNSPECIFIED: Illegal. Same
        istio.analysis.v1alpha1.AnalysisMessageBase.Level.UNKNOWN.
      ERROR: ERROR represents a misconfiguration that must be fixed.
      WARNING: WARNING represents a misconfiguration that should be fixed.
      INFO: INFO represents an informational finding.
    """
    LEVEL_UNSPECIFIED = 0
    ERROR = 1
    WARNING = 2
    INFO = 3

  documentationUrl = _messages.StringField(1)
  level = _messages.EnumField('LevelValueValuesEnum', 2)
  type = _messages.MessageField('ServiceMeshType', 3)


class ServiceMeshCondition(_messages.Message):
  r"""Condition being reported.

  Enums:
    CodeValueValuesEnum: Unique identifier of the condition which describes
      the condition recognizable to the user.
    SeverityValueValuesEnum: Severity level of the condition.

  Fields:
    code: Unique identifier of the condition which describes the condition
      recognizable to the user.
    details: A short summary about the issue.
    documentationLink: Links contains actionable information.
    severity: Severity level of the condition.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Unique identifier of the condition which describes the condition
    recognizable to the user.

    Values:
      CODE_UNSPECIFIED: Default Unspecified code
      MESH_IAM_PERMISSION_DENIED: Mesh IAM permission denied error code
      MESH_IAM_CROSS_PROJECT_PERMISSION_DENIED: Permission denied error code
        for cross-project
      CNI_CONFIG_UNSUPPORTED: CNI config unsupported error code
      GKE_SANDBOX_UNSUPPORTED: GKE sandbox unsupported error code
      NODEPOOL_WORKLOAD_IDENTITY_FEDERATION_REQUIRED: Nodepool workload
        identity federation required error code
      CNI_INSTALLATION_FAILED: CNI installation failed error code
      CNI_POD_UNSCHEDULABLE: CNI pod unschedulable error code
      CLUSTER_HAS_ZERO_NODES: Cluster has zero node code
      CANONICAL_SERVICE_ERROR: Failure to reconcile CanonicalServices
      UNSUPPORTED_MULTIPLE_CONTROL_PLANES: Multiple control planes unsupported
        error code
      VPCSC_GA_SUPPORTED: VPC-SC GA is supported for this control plane.
      DEPRECATED_SPEC_CONTROL_PLANE_MANAGEMENT: User is using deprecated
        ControlPlaneManagement and they have not yet set Management.
      DEPRECATED_SPEC_CONTROL_PLANE_MANAGEMENT_SAFE: User is using deprecated
        ControlPlaneManagement and they have already set Management.
      CONFIG_APPLY_INTERNAL_ERROR: Configuration (Istio/k8s resources) failed
        to apply due to internal error.
      CONFIG_VALIDATION_ERROR: Configuration failed to be applied due to being
        invalid.
      CONFIG_VALIDATION_WARNING: Encountered configuration(s) with possible
        unintended behavior or invalid configuration. These configs may not
        have been applied.
      QUOTA_EXCEEDED_BACKEND_SERVICES: BackendService quota exceeded error
        code.
      QUOTA_EXCEEDED_HEALTH_CHECKS: HealthCheck quota exceeded error code.
      QUOTA_EXCEEDED_HTTP_ROUTES: HTTPRoute quota exceeded error code.
      QUOTA_EXCEEDED_TCP_ROUTES: TCPRoute quota exceeded error code.
      QUOTA_EXCEEDED_TLS_ROUTES: TLS routes quota exceeded error code.
      QUOTA_EXCEEDED_TRAFFIC_POLICIES: TrafficPolicy quota exceeded error
        code.
      QUOTA_EXCEEDED_ENDPOINT_POLICIES: EndpointPolicy quota exceeded error
        code.
      QUOTA_EXCEEDED_GATEWAYS: Gateway quota exceeded error code.
      QUOTA_EXCEEDED_MESHES: Mesh quota exceeded error code.
      QUOTA_EXCEEDED_SERVER_TLS_POLICIES: ServerTLSPolicy quota exceeded error
        code.
      QUOTA_EXCEEDED_CLIENT_TLS_POLICIES: ClientTLSPolicy quota exceeded error
        code.
      QUOTA_EXCEEDED_SERVICE_LB_POLICIES: ServiceLBPolicy quota exceeded error
        code.
      QUOTA_EXCEEDED_HTTP_FILTERS: HTTPFilter quota exceeded error code.
      QUOTA_EXCEEDED_TCP_FILTERS: TCPFilter quota exceeded error code.
      QUOTA_EXCEEDED_NETWORK_ENDPOINT_GROUPS: NetworkEndpointGroup quota
        exceeded error code.
      LEGACY_MC_SECRETS: Legacy istio secrets found for multicluster error
        code
      WORKLOAD_IDENTITY_REQUIRED: Workload identity required error code
      NON_STANDARD_BINARY_USAGE: Non-standard binary usage error code
      UNSUPPORTED_GATEWAY_CLASS: Unsupported gateway class error code
      MANAGED_CNI_NOT_ENABLED: Managed CNI not enabled error code
      MODERNIZATION_SCHEDULED: Modernization is scheduled for a cluster.
      MODERNIZATION_IN_PROGRESS: Modernization is in progress for a cluster.
      MODERNIZATION_COMPLETED: Modernization is completed for a cluster.
      MODERNIZATION_ABORTED: Modernization is aborted for a cluster.
      MODERNIZATION_WILL_BE_SCHEDULED: Modernization will be scheduled for a
        fleet.
    """
    CODE_UNSPECIFIED = 0
    MESH_IAM_PERMISSION_DENIED = 1
    MESH_IAM_CROSS_PROJECT_PERMISSION_DENIED = 2
    CNI_CONFIG_UNSUPPORTED = 3
    GKE_SANDBOX_UNSUPPORTED = 4
    NODEPOOL_WORKLOAD_IDENTITY_FEDERATION_REQUIRED = 5
    CNI_INSTALLATION_FAILED = 6
    CNI_POD_UNSCHEDULABLE = 7
    CLUSTER_HAS_ZERO_NODES = 8
    CANONICAL_SERVICE_ERROR = 9
    UNSUPPORTED_MULTIPLE_CONTROL_PLANES = 10
    VPCSC_GA_SUPPORTED = 11
    DEPRECATED_SPEC_CONTROL_PLANE_MANAGEMENT = 12
    DEPRECATED_SPEC_CONTROL_PLANE_MANAGEMENT_SAFE = 13
    CONFIG_APPLY_INTERNAL_ERROR = 14
    CONFIG_VALIDATION_ERROR = 15
    CONFIG_VALIDATION_WARNING = 16
    QUOTA_EXCEEDED_BACKEND_SERVICES = 17
    QUOTA_EXCEEDED_HEALTH_CHECKS = 18
    QUOTA_EXCEEDED_HTTP_ROUTES = 19
    QUOTA_EXCEEDED_TCP_ROUTES = 20
    QUOTA_EXCEEDED_TLS_ROUTES = 21
    QUOTA_EXCEEDED_TRAFFIC_POLICIES = 22
    QUOTA_EXCEEDED_ENDPOINT_POLICIES = 23
    QUOTA_EXCEEDED_GATEWAYS = 24
    QUOTA_EXCEEDED_MESHES = 25
    QUOTA_EXCEEDED_SERVER_TLS_POLICIES = 26
    QUOTA_EXCEEDED_CLIENT_TLS_POLICIES = 27
    QUOTA_EXCEEDED_SERVICE_LB_POLICIES = 28
    QUOTA_EXCEEDED_HTTP_FILTERS = 29
    QUOTA_EXCEEDED_TCP_FILTERS = 30
    QUOTA_EXCEEDED_NETWORK_ENDPOINT_GROUPS = 31
    LEGACY_MC_SECRETS = 32
    WORKLOAD_IDENTITY_REQUIRED = 33
    NON_STANDARD_BINARY_USAGE = 34
    UNSUPPORTED_GATEWAY_CLASS = 35
    MANAGED_CNI_NOT_ENABLED = 36
    MODERNIZATION_SCHEDULED = 37
    MODERNIZATION_IN_PROGRESS = 38
    MODERNIZATION_COMPLETED = 39
    MODERNIZATION_ABORTED = 40
    MODERNIZATION_WILL_BE_SCHEDULED = 41

  class SeverityValueValuesEnum(_messages.Enum):
    r"""Severity level of the condition.

    Values:
      SEVERITY_UNSPECIFIED: Unspecified severity
      ERROR: Indicates an issue that prevents the mesh from operating
        correctly
      WARNING: Indicates a setting is likely wrong, but the mesh is still able
        to operate
      INFO: An informational message, not requiring any action
    """
    SEVERITY_UNSPECIFIED = 0
    ERROR = 1
    WARNING = 2
    INFO = 3

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  details = _messages.StringField(2)
  documentationLink = _messages.StringField(3)
  severity = _messages.EnumField('SeverityValueValuesEnum', 4)


class ServiceMeshControlPlaneManagement(_messages.Message):
  r"""Status of control plane management.

  Enums:
    ImplementationValueValuesEnum: Output only. Implementation of managed
      control plane.
    StateValueValuesEnum: LifecycleState of control plane management.

  Fields:
    details: Explanation of state.
    implementation: Output only. Implementation of managed control plane.
    state: LifecycleState of control plane management.
  """

  class ImplementationValueValuesEnum(_messages.Enum):
    r"""Output only. Implementation of managed control plane.

    Values:
      IMPLEMENTATION_UNSPECIFIED: Unspecified
      ISTIOD: A Google build of istiod is used for the managed control plane.
      TRAFFIC_DIRECTOR: Traffic director is used for the managed control
        plane.
      UPDATING: The control plane implementation is being updated.
    """
    IMPLEMENTATION_UNSPECIFIED = 0
    ISTIOD = 1
    TRAFFIC_DIRECTOR = 2
    UPDATING = 3

  class StateValueValuesEnum(_messages.Enum):
    r"""LifecycleState of control plane management.

    Values:
      LIFECYCLE_STATE_UNSPECIFIED: Unspecified
      DISABLED: DISABLED means that the component is not enabled.
      FAILED_PRECONDITION: FAILED_PRECONDITION means that provisioning cannot
        proceed because of some characteristic of the member cluster.
      PROVISIONING: PROVISIONING means that provisioning is in progress.
      ACTIVE: ACTIVE means that the component is ready for use.
      STALLED: STALLED means that provisioning could not be done.
      NEEDS_ATTENTION: NEEDS_ATTENTION means that the component is ready, but
        some user intervention is required. (For example that the user should
        migrate workloads to a new control plane revision.)
      DEGRADED: DEGRADED means that the component is ready, but operating in a
        degraded state.
      DEPROVISIONING: DEPROVISIONING means that deprovisioning is in progress.
    """
    LIFECYCLE_STATE_UNSPECIFIED = 0
    DISABLED = 1
    FAILED_PRECONDITION = 2
    PROVISIONING = 3
    ACTIVE = 4
    STALLED = 5
    NEEDS_ATTENTION = 6
    DEGRADED = 7
    DEPROVISIONING = 8

  details = _messages.MessageField('ServiceMeshStatusDetails', 1, repeated=True)
  implementation = _messages.EnumField('ImplementationValueValuesEnum', 2)
  state = _messages.EnumField('StateValueValuesEnum', 3)


class ServiceMeshControlPlaneRevision(_messages.Message):
  r"""Status of a control plane revision that is intended to be available for
  use in the cluster.

  Enums:
    ChannelValueValuesEnum: Release Channel the managed control plane revision
      is subscribed to.
    StateValueValuesEnum: State of the control plane revision.
      LIFECYCLE_STATE_UNSPECIFIED, FAILED_PRECONDITION, PROVISIONING, ACTIVE,
      and STALLED are applicable here.
    TypeValueValuesEnum: Type of the control plane revision.

  Fields:
    channel: Release Channel the managed control plane revision is subscribed
      to.
    details: Explanation of the state.
    owner: Owner of the control plane revision.
    revision: Unique name of the control plane revision.
    state: State of the control plane revision. LIFECYCLE_STATE_UNSPECIFIED,
      FAILED_PRECONDITION, PROVISIONING, ACTIVE, and STALLED are applicable
      here.
    type: Type of the control plane revision.
    version: Static version of the control plane revision.
  """

  class ChannelValueValuesEnum(_messages.Enum):
    r"""Release Channel the managed control plane revision is subscribed to.

    Values:
      CHANNEL_UNSPECIFIED: Unspecified
      RAPID: RAPID channel is offered on an early access basis for customers
        who want to test new releases.
      REGULAR: REGULAR channel is intended for production users who want to
        take advantage of new features.
      STABLE: STABLE channel includes versions that are known to be stable and
        reliable in production.
    """
    CHANNEL_UNSPECIFIED = 0
    RAPID = 1
    REGULAR = 2
    STABLE = 3

  class StateValueValuesEnum(_messages.Enum):
    r"""State of the control plane revision. LIFECYCLE_STATE_UNSPECIFIED,
    FAILED_PRECONDITION, PROVISIONING, ACTIVE, and STALLED are applicable
    here.

    Values:
      LIFECYCLE_STATE_UNSPECIFIED: Unspecified
      DISABLED: DISABLED means that the component is not enabled.
      FAILED_PRECONDITION: FAILED_PRECONDITION means that provisioning cannot
        proceed because of some characteristic of the member cluster.
      PROVISIONING: PROVISIONING means that provisioning is in progress.
      ACTIVE: ACTIVE means that the component is ready for use.
      STALLED: STALLED means that provisioning could not be done.
      NEEDS_ATTENTION: NEEDS_ATTENTION means that the component is ready, but
        some user intervention is required. (For example that the user should
        migrate workloads to a new control plane revision.)
      DEGRADED: DEGRADED means that the component is ready, but operating in a
        degraded state.
      DEPROVISIONING: DEPROVISIONING means that deprovisioning is in progress.
    """
    LIFECYCLE_STATE_UNSPECIFIED = 0
    DISABLED = 1
    FAILED_PRECONDITION = 2
    PROVISIONING = 3
    ACTIVE = 4
    STALLED = 5
    NEEDS_ATTENTION = 6
    DEGRADED = 7
    DEPROVISIONING = 8

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of the control plane revision.

    Values:
      CONTROL_PLANE_REVISION_TYPE_UNSPECIFIED: Unspecified.
      UNMANAGED: User-installed in-cluster control plane revision.
      MANAGED_SERVICE: Google-managed service running outside the cluster.
        Note: Google-managed control planes are independent per-cluster,
        regardless of whether the revision name is the same or not.
      MANAGED_LOCAL: Google-managed local control plane revision.
    """
    CONTROL_PLANE_REVISION_TYPE_UNSPECIFIED = 0
    UNMANAGED = 1
    MANAGED_SERVICE = 2
    MANAGED_LOCAL = 3

  channel = _messages.EnumField('ChannelValueValuesEnum', 1)
  details = _messages.MessageField('ServiceMeshStatusDetails', 2, repeated=True)
  owner = _messages.StringField(3)
  revision = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)
  type = _messages.EnumField('TypeValueValuesEnum', 6)
  version = _messages.StringField(7)


class ServiceMeshDataPlaneManagement(_messages.Message):
  r"""Status of data plane management. Only reported per-member.

  Enums:
    StateValueValuesEnum: Lifecycle status of data plane management.

  Fields:
    details: Explanation of the status.
    state: Lifecycle status of data plane management.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Lifecycle status of data plane management.

    Values:
      LIFECYCLE_STATE_UNSPECIFIED: Unspecified
      DISABLED: DISABLED means that the component is not enabled.
      FAILED_PRECONDITION: FAILED_PRECONDITION means that provisioning cannot
        proceed because of some characteristic of the member cluster.
      PROVISIONING: PROVISIONING means that provisioning is in progress.
      ACTIVE: ACTIVE means that the component is ready for use.
      STALLED: STALLED means that provisioning could not be done.
      NEEDS_ATTENTION: NEEDS_ATTENTION means that the component is ready, but
        some user intervention is required. (For example that the user should
        migrate workloads to a new control plane revision.)
      DEGRADED: DEGRADED means that the component is ready, but operating in a
        degraded state.
      DEPROVISIONING: DEPROVISIONING means that deprovisioning is in progress.
    """
    LIFECYCLE_STATE_UNSPECIFIED = 0
    DISABLED = 1
    FAILED_PRECONDITION = 2
    PROVISIONING = 3
    ACTIVE = 4
    STALLED = 5
    NEEDS_ATTENTION = 6
    DEGRADED = 7
    DEPROVISIONING = 8

  details = _messages.MessageField('ServiceMeshStatusDetails', 1, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class ServiceMeshMeshConnectivity(_messages.Message):
  r"""Status of cross cluster load balancing between other clusters in the
  mesh.

  Enums:
    StateValueValuesEnum: LifecycleState of multicluster load balancing.

  Fields:
    details: Explanation of state.
    state: LifecycleState of multicluster load balancing.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""LifecycleState of multicluster load balancing.

    Values:
      LIFECYCLE_STATE_UNSPECIFIED: Unspecified
      DISABLED: DISABLED means that the component is not enabled.
      FAILED_PRECONDITION: FAILED_PRECONDITION means that provisioning cannot
        proceed because of some characteristic of the member cluster.
      PROVISIONING: PROVISIONING means that provisioning is in progress.
      ACTIVE: ACTIVE means that the component is ready for use.
      STALLED: STALLED means that provisioning could not be done.
      NEEDS_ATTENTION: NEEDS_ATTENTION means that the component is ready, but
        some user intervention is required. (For example that the user should
        migrate workloads to a new control plane revision.)
      DEGRADED: DEGRADED means that the component is ready, but operating in a
        degraded state.
      DEPROVISIONING: DEPROVISIONING means that deprovisioning is in progress.
    """
    LIFECYCLE_STATE_UNSPECIFIED = 0
    DISABLED = 1
    FAILED_PRECONDITION = 2
    PROVISIONING = 3
    ACTIVE = 4
    STALLED = 5
    NEEDS_ATTENTION = 6
    DEGRADED = 7
    DEPROVISIONING = 8

  details = _messages.MessageField('ServiceMeshStatusDetails', 1, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class ServiceMeshSpec(_messages.Message):
  r"""**Service Mesh**: Spec for a single Membership for the servicemesh
  feature

  Enums:
    ConfigApiValueValuesEnum: Optional. Specifies the API that will be used
      for configuring the mesh workloads.
    ControlPlaneValueValuesEnum: Deprecated: use `management` instead Enables
      automatic control plane management.
    DataPlaneValueValuesEnum: Enables automatic data plane management.
    DefaultChannelValueValuesEnum: Determines which release channel to use for
      default injection and service mesh APIs.
    ManagementValueValuesEnum: Optional. Enables automatic Service Mesh
      management.

  Fields:
    configApi: Optional. Specifies the API that will be used for configuring
      the mesh workloads.
    controlPlane: Deprecated: use `management` instead Enables automatic
      control plane management.
    dataPlane: Enables automatic data plane management.
    defaultChannel: Determines which release channel to use for default
      injection and service mesh APIs.
    management: Optional. Enables automatic Service Mesh management.
  """

  class ConfigApiValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies the API that will be used for configuring the mesh
    workloads.

    Values:
      CONFIG_API_UNSPECIFIED: Unspecified
      CONFIG_API_ISTIO: Use the Istio API for configuration.
      CONFIG_API_GATEWAY: Use the K8s Gateway API for configuration.
    """
    CONFIG_API_UNSPECIFIED = 0
    CONFIG_API_ISTIO = 1
    CONFIG_API_GATEWAY = 2

  class ControlPlaneValueValuesEnum(_messages.Enum):
    r"""Deprecated: use `management` instead Enables automatic control plane
    management.

    Values:
      CONTROL_PLANE_MANAGEMENT_UNSPECIFIED: Unspecified
      AUTOMATIC: Google should provision a control plane revision and make it
        available in the cluster. Google will enroll this revision in a
        release channel and keep it up to date. The control plane revision may
        be a managed service, or a managed install.
      MANUAL: User will manually configure the control plane (e.g. via CLI, or
        via the ControlPlaneRevision KRM API)
    """
    CONTROL_PLANE_MANAGEMENT_UNSPECIFIED = 0
    AUTOMATIC = 1
    MANUAL = 2

  class DataPlaneValueValuesEnum(_messages.Enum):
    r"""Enables automatic data plane management.

    Values:
      DATA_PLANE_MANAGEMENT_UNSPECIFIED: Unspecified
      DATA_PLANE_MANAGEMENT_AUTOMATIC: Enables Google-managed data plane that
        provides L7 service mesh capabilities. Data plane management is
        enabled at the cluster level. Users can exclude individual workloads
        or namespaces.
      DATA_PLANE_MANAGEMENT_MANUAL: User will manage their L7 data plane.
    """
    DATA_PLANE_MANAGEMENT_UNSPECIFIED = 0
    DATA_PLANE_MANAGEMENT_AUTOMATIC = 1
    DATA_PLANE_MANAGEMENT_MANUAL = 2

  class DefaultChannelValueValuesEnum(_messages.Enum):
    r"""Determines which release channel to use for default injection and
    service mesh APIs.

    Values:
      CHANNEL_UNSPECIFIED: Unspecified
      RAPID: RAPID channel is offered on an early access basis for customers
        who want to test new releases.
      REGULAR: REGULAR channel is intended for production users who want to
        take advantage of new features.
      STABLE: STABLE channel includes versions that are known to be stable and
        reliable in production.
    """
    CHANNEL_UNSPECIFIED = 0
    RAPID = 1
    REGULAR = 2
    STABLE = 3

  class ManagementValueValuesEnum(_messages.Enum):
    r"""Optional. Enables automatic Service Mesh management.

    Values:
      MANAGEMENT_UNSPECIFIED: Unspecified
      MANAGEMENT_AUTOMATIC: Google should manage my Service Mesh for the
        cluster.
      MANAGEMENT_MANUAL: User will manually configure their service mesh
        components.
      MANAGEMENT_NOT_INSTALLED: Google should remove any managed Service Mesh
        components from this cluster and deprovision any resources.
    """
    MANAGEMENT_UNSPECIFIED = 0
    MANAGEMENT_AUTOMATIC = 1
    MANAGEMENT_MANUAL = 2
    MANAGEMENT_NOT_INSTALLED = 3

  configApi = _messages.EnumField('ConfigApiValueValuesEnum', 1)
  controlPlane = _messages.EnumField('ControlPlaneValueValuesEnum', 2)
  dataPlane = _messages.EnumField('DataPlaneValueValuesEnum', 3)
  defaultChannel = _messages.EnumField('DefaultChannelValueValuesEnum', 4)
  management = _messages.EnumField('ManagementValueValuesEnum', 5)


class ServiceMeshState(_messages.Message):
  r"""**Service Mesh**: State for a single Membership, as analyzed by the
  Service Mesh Hub Controller.

  Enums:
    DefaultChannelValueValuesEnum: Release channel to use for default
      injection and service mesh APIs.

  Fields:
    analysisMessages: Output only. Results of running Service Mesh analyzers.
    conditions: Output only. List of conditions reported for this membership.
    configApiVersion: The API version (i.e. Istio CRD version) for configuring
      service mesh in this cluster. This version is influenced by the
      `default_channel` field.
    controlPlaneManagement: Output only. Status of control plane management
    controlPlaneRevisions: Output only. State of all control plane revisions
      that are available in the cluster.
    dataPlaneManagement: Output only. Status of data plane management.
    defaultChannel: Release channel to use for default injection and service
      mesh APIs.
    meshConnectivity: Output only. Status of cross cluster load balancing
      between other clusters in the mesh.
  """

  class DefaultChannelValueValuesEnum(_messages.Enum):
    r"""Release channel to use for default injection and service mesh APIs.

    Values:
      CHANNEL_UNSPECIFIED: Unspecified
      RAPID: RAPID channel is offered on an early access basis for customers
        who want to test new releases.
      REGULAR: REGULAR channel is intended for production users who want to
        take advantage of new features.
      STABLE: STABLE channel includes versions that are known to be stable and
        reliable in production.
    """
    CHANNEL_UNSPECIFIED = 0
    RAPID = 1
    REGULAR = 2
    STABLE = 3

  analysisMessages = _messages.MessageField('ServiceMeshAnalysisMessage', 1, repeated=True)
  conditions = _messages.MessageField('ServiceMeshCondition', 2, repeated=True)
  configApiVersion = _messages.StringField(3)
  controlPlaneManagement = _messages.MessageField('ServiceMeshControlPlaneManagement', 4)
  controlPlaneRevisions = _messages.MessageField('ServiceMeshControlPlaneRevision', 5, repeated=True)
  dataPlaneManagement = _messages.MessageField('ServiceMeshDataPlaneManagement', 6)
  defaultChannel = _messages.EnumField('DefaultChannelValueValuesEnum', 7)
  meshConnectivity = _messages.MessageField('ServiceMeshMeshConnectivity', 8)


class ServiceMeshStatusDetails(_messages.Message):
  r"""Structured and human-readable details for a status.

  Fields:
    code: A machine-readable code that further describes a broad status.
    details: Human-readable explanation of code.
  """

  code = _messages.StringField(1)
  details = _messages.StringField(2)


class ServiceMeshType(_messages.Message):
  r"""A unique identifier for the type of message. Display_name is intended to
  be human-readable, code is intended to be machine readable. There should be
  a one-to-one mapping between display_name and code. (i.e. do not re-use
  display_names or codes between message types.) See
  istio.analysis.v1alpha1.AnalysisMessageBase.Type

  Fields:
    code: A 7 character code matching `^IST[0-9]{4}$` or `^ASM[0-9]{4}$`,
      intended to uniquely identify the message type. (e.g. "IST0001" is
      mapped to the "InternalError" message type.)
    displayName: A human-readable name for the message type. e.g.
      "InternalError", "PodMissingProxy". This should be the same for all
      messages of the same type. (This corresponds to the `name` field in
      open-source Istio.)
  """

  code = _messages.StringField(1)
  displayName = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class State(_messages.Message):
  r"""High-level state of a MembershipFeature.

  Enums:
    CodeValueValuesEnum: The high-level, machine-readable status of this
      MembershipFeature.

  Fields:
    code: The high-level, machine-readable status of this MembershipFeature.
    description: A human-readable description of the current status.
    updateTime: The time this status and any related Feature-specific details
      were updated.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""The high-level, machine-readable status of this MembershipFeature.

    Values:
      CODE_UNSPECIFIED: Unknown or not set.
      OK: The MembershipFeature is operating normally.
      WARNING: The MembershipFeature has encountered an issue, and is
        operating in a degraded state. The MembershipFeature may need
        intervention to return to normal operation. See the description and
        any associated MembershipFeature-specific details for more
        information.
      ERROR: The MembershipFeature is not operating or is in a severely
        degraded state. The MembershipFeature may need intervention to return
        to normal operation. See the description and any associated
        MembershipFeature-specific details for more information.
    """
    CODE_UNSPECIFIED = 0
    OK = 1
    WARNING = 2
    ERROR = 3

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  description = _messages.StringField(2)
  updateTime = _messages.StringField(3)


class WorkloadCertificateSpec(_messages.Message):
  r"""**WorkloadCertificate**: The membership-specific input for
  WorkloadCertificate feature.

  Enums:
    CertificateManagementValueValuesEnum: CertificateManagement specifies
      workload certificate management.

  Fields:
    certificateManagement: CertificateManagement specifies workload
      certificate management.
  """

  class CertificateManagementValueValuesEnum(_messages.Enum):
    r"""CertificateManagement specifies workload certificate management.

    Values:
      CERTIFICATE_MANAGEMENT_UNSPECIFIED: Disable workload certificate
        feature.
      DISABLED: Disable workload certificate feature.
      ENABLED: Enable workload certificate feature.
    """
    CERTIFICATE_MANAGEMENT_UNSPECIFIED = 0
    DISABLED = 1
    ENABLED = 2

  certificateManagement = _messages.EnumField('CertificateManagementValueValuesEnum', 1)


class WorkloadIdentityState(_messages.Message):
  r"""**WorkloadIdentity**: The membership-specific state for WorkloadIdentity
  feature.

  Fields:
    description: Deprecated, this field will be erased after code is changed
      to use the new field.
  """

  description = _messages.StringField(1)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
