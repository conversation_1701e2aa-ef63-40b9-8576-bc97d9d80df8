"""Generated message classes for gkehub version v1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'gkehub'


class AppDevExperienceFeatureSpec(_messages.Message):
  r"""Spec for App Dev Experience Feature."""


class AppDevExperienceFeatureState(_messages.Message):
  r"""State for App Dev Exp Feature.

  Fields:
    networkingInstallSucceeded: Status of subcomponent that detects configured
      Service Mesh resources.
  """

  networkingInstallSucceeded = _messages.MessageField('Status', 1)


class ApplianceCluster(_messages.Message):
  r"""ApplianceCluster contains information specific to GDC Edge Appliance
  Clusters.

  Fields:
    resourceLink: Immutable. Self-link of the Google Cloud resource for the
      Appliance Cluster. For example:
      //transferappliance.googleapis.com/projects/my-project/locations/us-
      west1-a/appliances/my-appliance
  """

  resourceLink = _messages.StringField(1)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    ignoreChildExemptions: A boolean attribute.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  ignoreChildExemptions = _messages.BooleanField(2)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 3)


class Authority(_messages.Message):
  r"""Authority encodes how Google will recognize identities from this
  Membership. See the workload identity documentation for more details:
  https://cloud.google.com/kubernetes-engine/docs/how-to/workload-identity

  Fields:
    identityProvider: Output only. An identity provider that reflects the
      `issuer` in the workload identity pool.
    issuer: Optional. A JSON Web Token (JWT) issuer URI. `issuer` must start
      with `https://` and be a valid URL with length <2000 characters, it must
      use `location` rather than `zone` for GKE clusters. If set, then Google
      will allow valid OIDC tokens from this issuer to authenticate within the
      workload_identity_pool. OIDC discovery will be performed on this URI to
      validate tokens from the issuer. Clearing `issuer` disables Workload
      Identity. `issuer` cannot be directly modified; it must be cleared (and
      Workload Identity disabled) before using a new issuer (and re-enabling
      Workload Identity).
    oidcJwks: Optional. OIDC verification keys for this Membership in JWKS
      format (RFC 7517). When this field is set, OIDC discovery will NOT be
      performed on `issuer`, and instead OIDC tokens will be validated using
      this field.
    scopeTenancyIdentityProvider: Optional. Output only. The identity provider
      for the scope-tenancy workload identity pool.
    scopeTenancyWorkloadIdentityPool: Optional. Output only. The name of the
      scope-tenancy workload identity pool. This pool is set in the fleet-
      level feature.
    workloadIdentityPool: Output only. The name of the workload identity pool
      in which `issuer` will be recognized. There is a single Workload
      Identity Pool per Hub that is shared between all Memberships that belong
      to that Hub. For a Hub hosted in {PROJECT_ID}, the workload pool format
      is `{PROJECT_ID}.hub.id.goog`, although this is subject to change in
      newer versions of this API.
  """

  identityProvider = _messages.StringField(1)
  issuer = _messages.StringField(2)
  oidcJwks = _messages.BytesField(3)
  scopeTenancyIdentityProvider = _messages.StringField(4)
  scopeTenancyWorkloadIdentityPool = _messages.StringField(5)
  workloadIdentityPool = _messages.StringField(6)


class AuthorizationLoggingOptions(_messages.Message):
  r"""Authorization-related information used by Cloud Audit Logging.

  Enums:
    PermissionTypeValueValuesEnum: The type of the permission that was
      checked.

  Fields:
    permissionType: The type of the permission that was checked.
  """

  class PermissionTypeValueValuesEnum(_messages.Enum):
    r"""The type of the permission that was checked.

    Values:
      PERMISSION_TYPE_UNSPECIFIED: Default. Should not be used.
      ADMIN_READ: A read of admin (meta) data.
      ADMIN_WRITE: A write of admin (meta) data.
      DATA_READ: A read of standard data.
      DATA_WRITE: A write of standard data.
    """
    PERMISSION_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    ADMIN_WRITE = 2
    DATA_READ = 3
    DATA_WRITE = 4

  permissionType = _messages.EnumField('PermissionTypeValueValuesEnum', 1)


class BinaryAuthorizationConfig(_messages.Message):
  r"""BinaryAuthorizationConfig defines the fleet level configuration of
  binary authorization feature.

  Enums:
    EvaluationModeValueValuesEnum: Optional. Mode of operation for binauthz
      policy evaluation.

  Fields:
    evaluationMode: Optional. Mode of operation for binauthz policy
      evaluation.
    policyBindings: Optional. Binauthz policies that apply to this cluster.
  """

  class EvaluationModeValueValuesEnum(_messages.Enum):
    r"""Optional. Mode of operation for binauthz policy evaluation.

    Values:
      EVALUATION_MODE_UNSPECIFIED: Default value
      DISABLED: Disable BinaryAuthorization
      POLICY_BINDINGS: Use Binary Authorization with the policies specified in
        policy_bindings.
    """
    EVALUATION_MODE_UNSPECIFIED = 0
    DISABLED = 1
    POLICY_BINDINGS = 2

  evaluationMode = _messages.EnumField('EvaluationModeValueValuesEnum', 1)
  policyBindings = _messages.MessageField('PolicyBinding', 2, repeated=True)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    bindingId: A string attribute.
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  bindingId = _messages.StringField(1)
  condition = _messages.MessageField('Expr', 2)
  members = _messages.StringField(3, repeated=True)
  role = _messages.StringField(4)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CloudAuditOptions(_messages.Message):
  r"""Write a Cloud Audit log

  Enums:
    LogNameValueValuesEnum: The log_name to populate in the Cloud Audit
      Record.
    PermissionTypeValueValuesEnum: The type associated with the permission.

  Fields:
    authorizationLoggingOptions: Information used by the Cloud Audit Logging
      pipeline. Will be deprecated once the migration to PermissionType is
      complete (b/201806118).
    logName: The log_name to populate in the Cloud Audit Record.
    permissionType: The type associated with the permission.
  """

  class LogNameValueValuesEnum(_messages.Enum):
    r"""The log_name to populate in the Cloud Audit Record.

    Values:
      UNSPECIFIED_LOG_NAME: Default. Should not be used.
      ADMIN_ACTIVITY: Corresponds to "cloudaudit.googleapis.com/activity"
      DATA_ACCESS: Corresponds to "cloudaudit.googleapis.com/data_access"
    """
    UNSPECIFIED_LOG_NAME = 0
    ADMIN_ACTIVITY = 1
    DATA_ACCESS = 2

  class PermissionTypeValueValuesEnum(_messages.Enum):
    r"""The type associated with the permission.

    Values:
      PERMISSION_TYPE_UNSPECIFIED: Default. Should not be used.
      ADMIN_READ: Permissions that gate reading resource configuration or
        metadata.
      ADMIN_WRITE: Permissions that gate modification of resource
        configuration or metadata.
      DATA_READ: Permissions that gate reading user-provided data.
      DATA_WRITE: Permissions that gate writing user-provided data.
    """
    PERMISSION_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    ADMIN_WRITE = 2
    DATA_READ = 3
    DATA_WRITE = 4

  authorizationLoggingOptions = _messages.MessageField('AuthorizationLoggingOptions', 1)
  logName = _messages.EnumField('LogNameValueValuesEnum', 2)
  permissionType = _messages.EnumField('PermissionTypeValueValuesEnum', 3)


class ClusterUpgradeFleetSpec(_messages.Message):
  r"""**ClusterUpgrade**: The configuration for the fleet-level ClusterUpgrade
  feature.

  Fields:
    gkeUpgradeOverrides: Allow users to override some properties of each GKE
      upgrade.
    postConditions: Required. Post conditions to evaluate to mark an upgrade
      COMPLETE. Required.
    upstreamFleets: This fleet consumes upgrades that have COMPLETE status
      code in the upstream fleets. See UpgradeStatus.Code for code
      definitions. The fleet name should be either fleet project number or id.
      This is defined as repeated for future proof reasons. Initial
      implementation will enforce at most one upstream fleet.
  """

  gkeUpgradeOverrides = _messages.MessageField('ClusterUpgradeGKEUpgradeOverride', 1, repeated=True)
  postConditions = _messages.MessageField('ClusterUpgradePostConditions', 2)
  upstreamFleets = _messages.StringField(3, repeated=True)


class ClusterUpgradeFleetState(_messages.Message):
  r"""**ClusterUpgrade**: The state for the fleet-level ClusterUpgrade
  feature.

  Messages:
    IgnoredValue: A list of memberships ignored by the feature. For example,
      manually upgraded clusters can be ignored if they are newer than the
      default versions of its release channel. The membership resource is in
      the format: `projects/{p}/locations/{l}/membership/{m}`.

  Fields:
    downstreamFleets: This fleets whose upstream_fleets contain the current
      fleet. The fleet name should be either fleet project number or id.
    gkeState: Feature state for GKE clusters.
    ignored: A list of memberships ignored by the feature. For example,
      manually upgraded clusters can be ignored if they are newer than the
      default versions of its release channel. The membership resource is in
      the format: `projects/{p}/locations/{l}/membership/{m}`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class IgnoredValue(_messages.Message):
    r"""A list of memberships ignored by the feature. For example, manually
    upgraded clusters can be ignored if they are newer than the default
    versions of its release channel. The membership resource is in the format:
    `projects/{p}/locations/{l}/membership/{m}`.

    Messages:
      AdditionalProperty: An additional property for a IgnoredValue object.

    Fields:
      additionalProperties: Additional properties of type IgnoredValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a IgnoredValue object.

      Fields:
        key: Name of the additional property.
        value: A ClusterUpgradeIgnoredMembership attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ClusterUpgradeIgnoredMembership', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  downstreamFleets = _messages.StringField(1, repeated=True)
  gkeState = _messages.MessageField('ClusterUpgradeGKEUpgradeFeatureState', 2)
  ignored = _messages.MessageField('IgnoredValue', 3)


class ClusterUpgradeGKEUpgrade(_messages.Message):
  r"""GKEUpgrade represents a GKE provided upgrade, e.g., control plane
  upgrade.

  Fields:
    name: Name of the upgrade, e.g., "k8s_control_plane". It should be a valid
      upgrade name. It must not exceet 99 characters.
    version: Version of the upgrade, e.g., "1.22.1-gke.100". It should be a
      valid version. It must not exceet 99 characters.
  """

  name = _messages.StringField(1)
  version = _messages.StringField(2)


class ClusterUpgradeGKEUpgradeFeatureCondition(_messages.Message):
  r"""GKEUpgradeFeatureCondition describes the condition of the feature for
  GKE clusters at a certain point of time.

  Fields:
    reason: Reason why the feature is in this status.
    status: Status of the condition, one of True, False, Unknown.
    type: Type of the condition, for example, "ready".
    updateTime: Last timestamp the condition was updated.
  """

  reason = _messages.StringField(1)
  status = _messages.StringField(2)
  type = _messages.StringField(3)
  updateTime = _messages.StringField(4)


class ClusterUpgradeGKEUpgradeFeatureState(_messages.Message):
  r"""GKEUpgradeFeatureState contains feature states for GKE clusters in the
  scope.

  Fields:
    conditions: Current conditions of the feature.
    upgradeState: Upgrade state. It will eventually replace `state`.
  """

  conditions = _messages.MessageField('ClusterUpgradeGKEUpgradeFeatureCondition', 1, repeated=True)
  upgradeState = _messages.MessageField('ClusterUpgradeGKEUpgradeState', 2, repeated=True)


class ClusterUpgradeGKEUpgradeOverride(_messages.Message):
  r"""Properties of a GKE upgrade that can be overridden by the user. For
  example, a user can skip soaking by overriding the soaking to 0.

  Fields:
    postConditions: Required. Post conditions to override for the specified
      upgrade (name + version). Required.
    upgrade: Required. Which upgrade to override. Required.
  """

  postConditions = _messages.MessageField('ClusterUpgradePostConditions', 1)
  upgrade = _messages.MessageField('ClusterUpgradeGKEUpgrade', 2)


class ClusterUpgradeGKEUpgradeState(_messages.Message):
  r"""GKEUpgradeState is a GKEUpgrade and its state at the scope and fleet
  level.

  Messages:
    StatsValue: Number of GKE clusters in each status code.

  Fields:
    stats: Number of GKE clusters in each status code.
    status: Status of the upgrade.
    upgrade: Which upgrade to track the state.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class StatsValue(_messages.Message):
    r"""Number of GKE clusters in each status code.

    Messages:
      AdditionalProperty: An additional property for a StatsValue object.

    Fields:
      additionalProperties: Additional properties of type StatsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a StatsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.IntegerField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  stats = _messages.MessageField('StatsValue', 1)
  status = _messages.MessageField('ClusterUpgradeUpgradeStatus', 2)
  upgrade = _messages.MessageField('ClusterUpgradeGKEUpgrade', 3)


class ClusterUpgradeIgnoredMembership(_messages.Message):
  r"""IgnoredMembership represents a membership ignored by the feature. A
  membership can be ignored because it was manually upgraded to a newer
  version than RC default.

  Fields:
    ignoredTime: Time when the membership was first set to ignored.
    reason: Reason why the membership is ignored.
  """

  ignoredTime = _messages.StringField(1)
  reason = _messages.StringField(2)


class ClusterUpgradeMembershipGKEUpgradeState(_messages.Message):
  r"""ScopeGKEUpgradeState is a GKEUpgrade and its state per-membership.

  Fields:
    status: Status of the upgrade.
    upgrade: Which upgrade to track the state.
  """

  status = _messages.MessageField('ClusterUpgradeUpgradeStatus', 1)
  upgrade = _messages.MessageField('ClusterUpgradeGKEUpgrade', 2)


class ClusterUpgradeMembershipState(_messages.Message):
  r"""Per-membership state for this feature.

  Fields:
    ignored: Whether this membership is ignored by the feature. For example,
      manually upgraded clusters can be ignored if they are newer than the
      default versions of its release channel.
    upgrades: Actual upgrade state against desired.
  """

  ignored = _messages.MessageField('ClusterUpgradeIgnoredMembership', 1)
  upgrades = _messages.MessageField('ClusterUpgradeMembershipGKEUpgradeState', 2, repeated=True)


class ClusterUpgradePostConditions(_messages.Message):
  r"""Post conditional checks after an upgrade has been applied on all
  eligible clusters.

  Fields:
    soaking: Required. Amount of time to "soak" after a rollout has been
      finished before marking it COMPLETE. Cannot exceed 30 days. Required.
  """

  soaking = _messages.StringField(1)


class ClusterUpgradeUpgradeStatus(_messages.Message):
  r"""UpgradeStatus provides status information for each upgrade.

  Enums:
    CodeValueValuesEnum: Status code of the upgrade.
    TypeValueValuesEnum: Type of the status.

  Fields:
    code: Status code of the upgrade.
    reason: Reason for this status.
    type: Type of the status.
    updateTime: Last timestamp the status was updated.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Status code of the upgrade.

    Values:
      CODE_UNSPECIFIED: Required by https://linter.aip.dev/126/unspecified.
      INELIGIBLE: The upgrade is ineligible. At the scope level, this means
        the upgrade is ineligible for all the clusters in the scope.
      PENDING: The upgrade is pending. At the scope level, this means the
        upgrade is pending for all the clusters in the scope.
      IN_PROGRESS: The upgrade is in progress. At the scope level, this means
        the upgrade is in progress for at least one cluster in the scope.
      SOAKING: The upgrade has finished and is soaking until the soaking time
        is up. At the scope level, this means at least one cluster is in
        soaking while the rest are either soaking or complete.
      FORCED_SOAKING: A cluster will be forced to enter soaking if an upgrade
        doesn't finish within a certain limit, despite it's actual status.
      COMPLETE: The upgrade has passed all post conditions (soaking). At the
        scope level, this means all eligible clusters are in COMPLETE status.
      PAUSED: The upgrade is paused. At the scope level, this means the
        upgrade is paused for all the clusters in the scope.
      FORCED_COMPLETE: The upgrade was forced into soaking and the soaking
        time has passed. This is the equivalent of COMPLETE status for
        upgrades that were forced into soaking.
    """
    CODE_UNSPECIFIED = 0
    INELIGIBLE = 1
    PENDING = 2
    IN_PROGRESS = 3
    SOAKING = 4
    FORCED_SOAKING = 5
    COMPLETE = 6
    PAUSED = 7
    FORCED_COMPLETE = 8

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of the status.

    Values:
      TYPE_UNSPECIFIED: Required by https://linter.aip.dev/126/unspecified.
      DISRUPTION_BUDGET: The upgrade is PAUSED due to the cluster's disruption
        budget. Cluster is out of disruption budget. Once the cluster is back
        in budget, the upgrade will resume.
      MAINTENANCE_POLICY: The upgrade is PAUSED due to the cluster's
        maintenance policy. The upgrade will resume once cluster's maintenance
        window is open and/or maintenance exclusion is over.
      SYSTEM_CONFIG: The upgrade is PAUSED due to the system config.
      CLUSTER_STATUS: The upgrade is INELIGIBLE due to the cluster's status.
      INCOMPATIBLE_VERSION: The upgrade is INELIGIBLE due to the cluster's
        current version being incompatible with the target version.
      DISABLED_BY_USER: The upgrade is INELIGIBLE due to the user disabling
        auto upgrades. Applies to node upgrades only.
    """
    TYPE_UNSPECIFIED = 0
    DISRUPTION_BUDGET = 1
    MAINTENANCE_POLICY = 2
    SYSTEM_CONFIG = 3
    CLUSTER_STATUS = 4
    INCOMPATIBLE_VERSION = 5
    DISABLED_BY_USER = 6

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  reason = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)
  updateTime = _messages.StringField(4)


class CommonFeatureSpec(_messages.Message):
  r"""CommonFeatureSpec contains Fleet-wide configuration information

  Fields:
    appdevexperience: Appdevexperience specific spec.
    clusterupgrade: ClusterUpgrade (fleet-based) feature spec.
    dataplanev2: DataplaneV2 feature spec.
    fleetobservability: FleetObservability feature spec.
    helloworld: Hello World-specific spec.
    multiclusteringress: Multicluster Ingress-specific spec.
    rbacrolebindingactuation: RBAC Role Binding Actuation feature spec
    workloadmigration: The specification for WorkloadMigration feature.
  """

  appdevexperience = _messages.MessageField('AppDevExperienceFeatureSpec', 1)
  clusterupgrade = _messages.MessageField('ClusterUpgradeFleetSpec', 2)
  dataplanev2 = _messages.MessageField('DataplaneV2FeatureSpec', 3)
  fleetobservability = _messages.MessageField('FleetObservabilityFeatureSpec', 4)
  helloworld = _messages.MessageField('HelloWorldFeatureSpec', 5)
  multiclusteringress = _messages.MessageField('MultiClusterIngressFeatureSpec', 6)
  rbacrolebindingactuation = _messages.MessageField('RBACRoleBindingActuationFeatureSpec', 7)
  workloadmigration = _messages.MessageField('WorkloadMigrationFeatureSpec', 8)


class CommonFeatureState(_messages.Message):
  r"""CommonFeatureState contains Fleet-wide Feature status information.

  Fields:
    appdevexperience: Appdevexperience specific state.
    clusterupgrade: ClusterUpgrade fleet-level state.
    fleetobservability: FleetObservability feature state.
    helloworld: Hello World-specific state.
    rbacrolebindingactuation: RBAC Role Binding Actuation feature state
    state: Output only. The "running state" of the Feature in this Fleet.
  """

  appdevexperience = _messages.MessageField('AppDevExperienceFeatureState', 1)
  clusterupgrade = _messages.MessageField('ClusterUpgradeFleetState', 2)
  fleetobservability = _messages.MessageField('FleetObservabilityFeatureState', 3)
  helloworld = _messages.MessageField('HelloWorldFeatureState', 4)
  rbacrolebindingactuation = _messages.MessageField('RBACRoleBindingActuationFeatureState', 5)
  state = _messages.MessageField('FeatureState', 6)


class CommonFleetDefaultMemberConfigSpec(_messages.Message):
  r"""CommonFleetDefaultMemberConfigSpec contains default configuration
  information for memberships of a fleet

  Fields:
    configmanagement: Config Management-specific spec.
    helloworld: Hello World-specific spec.
    identityservice: Identity Service-specific spec.
    mesh: Anthos Service Mesh-specific spec
    policycontroller: Policy Controller spec.
  """

  configmanagement = _messages.MessageField('ConfigManagementMembershipSpec', 1)
  helloworld = _messages.MessageField('HelloWorldMembershipSpec', 2)
  identityservice = _messages.MessageField('IdentityServiceMembershipSpec', 3)
  mesh = _messages.MessageField('ServiceMeshMembershipSpec', 4)
  policycontroller = _messages.MessageField('PolicyControllerMembershipSpec', 5)


class CompliancePostureConfig(_messages.Message):
  r"""CompliancePostureConfig defines the settings needed to enable/disable
  features for the Compliance Posture.

  Enums:
    ModeValueValuesEnum: Defines the enablement mode for Compliance Posture.

  Fields:
    complianceStandards: List of enabled compliance standards.
    mode: Defines the enablement mode for Compliance Posture.
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""Defines the enablement mode for Compliance Posture.

    Values:
      MODE_UNSPECIFIED: Default value not specified.
      DISABLED: Disables Compliance Posture features on the cluster.
      ENABLED: Enables Compliance Posture features on the cluster.
    """
    MODE_UNSPECIFIED = 0
    DISABLED = 1
    ENABLED = 2

  complianceStandards = _messages.MessageField('ComplianceStandard', 1, repeated=True)
  mode = _messages.EnumField('ModeValueValuesEnum', 2)


class ComplianceStandard(_messages.Message):
  r"""A ComplianceStandard object.

  Fields:
    standard: Name of the compliance standard.
  """

  standard = _messages.StringField(1)


class Condition(_messages.Message):
  r"""A condition to be met.

  Enums:
    IamValueValuesEnum: Trusted attributes supplied by the IAM system.
    OpValueValuesEnum: An operator to apply the subject with.
    SysValueValuesEnum: Trusted attributes supplied by any service that owns
      resources and uses the IAM system for access control.

  Fields:
    iam: Trusted attributes supplied by the IAM system.
    op: An operator to apply the subject with.
    svc: Trusted attributes discharged by the service.
    sys: Trusted attributes supplied by any service that owns resources and
      uses the IAM system for access control.
    values: The objects of the condition.
  """

  class IamValueValuesEnum(_messages.Enum):
    r"""Trusted attributes supplied by the IAM system.

    Values:
      NO_ATTR: Default non-attribute.
      AUTHORITY: Either principal or (if present) authority selector.
      ATTRIBUTION: The principal (even if an authority selector is present),
        which must only be used for attribution, not authorization.
      SECURITY_REALM: Any of the security realms in the IAMContext
        (go/security-realms). When used with IN, the condition indicates "any
        of the request's realms match one of the given values; with NOT_IN,
        "none of the realms match any of the given values". Note that a value
        can be: - 'self:campus' (i.e., clients that are in the same campus) -
        'self:metro' (i.e., clients that are in the same metro) - 'self:cloud-
        region' (i.e., allow connections from clients that are in the same
        cloud region) - 'self:prod-region' (i.e., allow connections from
        clients that are in the same prod region) - 'guardians' (i.e., allow
        connections from its guardian realms. See go/security-realms-
        glossary#guardian for more information.) - 'cryto_core_guardians'
        (i.e., allow connections from its crypto core guardian realms. See
        go/security-realms-glossary#guardian for more information.) Crypto
        Core coverage is a super-set of Default coverage, containing
        information about coverage between higher tier data centers (e.g.,
        YAWNs). Most services should use Default coverage and only use Crypto
        Core coverage if the service is involved in greenfield turnup of new
        higher tier data centers (e.g., credential infrastructure, machine/job
        management systems, etc.). - 'self' [DEPRECATED] (i.e., allow
        connections from clients that are in the same security realm, which is
        currently but not guaranteed to be campus-sized) - a realm (e.g.,
        'campus-abc') - a realm group (e.g., 'realms-for-borg-cell-xx', see:
        go/realm-groups) A match is determined by a realm group membership
        check performed by a RealmAclRep object (go/realm-acl-howto). It is
        not permitted to grant access based on the *absence* of a realm, so
        realm conditions can only be used in a "positive" context (e.g.,
        ALLOW/IN or DENY/NOT_IN).
      APPROVER: An approver (distinct from the requester) that has authorized
        this request. When used with IN, the condition indicates that one of
        the approvers associated with the request matches the specified
        principal, or is a member of the specified group. Approvers can only
        grant additional access, and are thus only used in a strictly positive
        context (e.g. ALLOW/IN or DENY/NOT_IN).
      JUSTIFICATION_TYPE: What types of justifications have been supplied with
        this request. String values should match enum names from
        security.credentials.JustificationType, e.g. "MANUAL_STRING". It is
        not permitted to grant access based on the *absence* of a
        justification, so justification conditions can only be used in a
        "positive" context (e.g., ALLOW/IN or DENY/NOT_IN). Multiple
        justifications, e.g., a Buganizer ID and a manually-entered reason,
        are normal and supported.
      CREDENTIALS_TYPE: What type of credentials have been supplied with this
        request. String values should match enum names from
        security_loas_l2.CredentialsType - currently, only
        CREDS_TYPE_EMERGENCY is supported. It is not permitted to grant access
        based on the *absence* of a credentials type, so the conditions can
        only be used in a "positive" context (e.g., ALLOW/IN or DENY/NOT_IN).
      CREDS_ASSERTION: Properties of the credentials supplied with this
        request. See http://go/rpcsp-credential-assertions?polyglot=rpcsp-v1-0
        The conditions can only be used in a "positive" context (e.g.,
        ALLOW/IN or DENY/NOT_IN).
    """
    NO_ATTR = 0
    AUTHORITY = 1
    ATTRIBUTION = 2
    SECURITY_REALM = 3
    APPROVER = 4
    JUSTIFICATION_TYPE = 5
    CREDENTIALS_TYPE = 6
    CREDS_ASSERTION = 7

  class OpValueValuesEnum(_messages.Enum):
    r"""An operator to apply the subject with.

    Values:
      NO_OP: Default no-op.
      EQUALS: DEPRECATED. Use IN instead.
      NOT_EQUALS: DEPRECATED. Use NOT_IN instead.
      IN: The condition is true if the subject (or any element of it if it is
        a set) matches any of the supplied values.
      NOT_IN: The condition is true if the subject (or every element of it if
        it is a set) matches none of the supplied values.
      DISCHARGED: Subject is discharged
    """
    NO_OP = 0
    EQUALS = 1
    NOT_EQUALS = 2
    IN = 3
    NOT_IN = 4
    DISCHARGED = 5

  class SysValueValuesEnum(_messages.Enum):
    r"""Trusted attributes supplied by any service that owns resources and
    uses the IAM system for access control.

    Values:
      NO_ATTR: Default non-attribute type
      REGION: Region of the resource
      SERVICE: Service name
      NAME: Resource name
      IP: IP address of the caller
    """
    NO_ATTR = 0
    REGION = 1
    SERVICE = 2
    NAME = 3
    IP = 4

  iam = _messages.EnumField('IamValueValuesEnum', 1)
  op = _messages.EnumField('OpValueValuesEnum', 2)
  svc = _messages.StringField(3)
  sys = _messages.EnumField('SysValueValuesEnum', 4)
  values = _messages.StringField(5, repeated=True)


class ConfigManagementConfigSync(_messages.Message):
  r"""Configuration for Config Sync

  Fields:
    deploymentOverrides: Optional. Configuration for deployment overrides.
    enabled: Optional. Enables the installation of ConfigSync. If set to true,
      ConfigSync resources will be created and the other ConfigSync fields
      will be applied if exist. If set to false, all other ConfigSync fields
      will be ignored, ConfigSync resources will be deleted. If omitted,
      ConfigSync resources will be managed depends on the presence of the git
      or oci field.
    git: Optional. Git repo configuration for the cluster.
    metricsGcpServiceAccountEmail: Optional. The Email of the Google Cloud
      Service Account (GSA) used for exporting Config Sync metrics to Cloud
      Monitoring and Cloud Monarch when Workload Identity is enabled. The GSA
      should have the Monitoring Metric Writer (roles/monitoring.metricWriter)
      IAM role. The Kubernetes ServiceAccount `default` in the namespace
      `config-management-monitoring` should be bound to the GSA. Deprecated:
      If Workload Identity Federation for GKE is enabled, Google Cloud Service
      Account is no longer needed for exporting Config Sync metrics:
      https://cloud.google.com/kubernetes-engine/enterprise/config-
      sync/docs/how-to/monitor-config-sync-cloud-monitoring#custom-monitoring.
    oci: Optional. OCI repo configuration for the cluster
    preventDrift: Optional. Set to true to enable the Config Sync admission
      webhook to prevent drifts. If set to `false`, disables the Config Sync
      admission webhook and does not prevent drifts.
    sourceFormat: Optional. Specifies whether the Config Sync Repo is in
      "hierarchical" or "unstructured" mode.
    stopSyncing: Optional. Set to true to stop syncing configs for a single
      cluster. Default to false.
  """

  deploymentOverrides = _messages.MessageField('ConfigManagementDeploymentOverride', 1, repeated=True)
  enabled = _messages.BooleanField(2)
  git = _messages.MessageField('ConfigManagementGitConfig', 3)
  metricsGcpServiceAccountEmail = _messages.StringField(4)
  oci = _messages.MessageField('ConfigManagementOciConfig', 5)
  preventDrift = _messages.BooleanField(6)
  sourceFormat = _messages.StringField(7)
  stopSyncing = _messages.BooleanField(8)


class ConfigManagementConfigSyncDeploymentState(_messages.Message):
  r"""The state of ConfigSync's deployment on a cluster

  Enums:
    AdmissionWebhookValueValuesEnum: Deployment state of admission-webhook
    GitSyncValueValuesEnum: Deployment state of the git-sync pod
    ImporterValueValuesEnum: Deployment state of the importer pod
    MonitorValueValuesEnum: Deployment state of the monitor pod
    OtelCollectorValueValuesEnum: Deployment state of otel-collector
    ReconcilerManagerValueValuesEnum: Deployment state of reconciler-manager
      pod
    ResourceGroupControllerManagerValueValuesEnum: Deployment state of
      resource-group-controller-manager
    RootReconcilerValueValuesEnum: Deployment state of root-reconciler
    SyncerValueValuesEnum: Deployment state of the syncer pod

  Fields:
    admissionWebhook: Deployment state of admission-webhook
    gitSync: Deployment state of the git-sync pod
    importer: Deployment state of the importer pod
    monitor: Deployment state of the monitor pod
    otelCollector: Deployment state of otel-collector
    reconcilerManager: Deployment state of reconciler-manager pod
    resourceGroupControllerManager: Deployment state of resource-group-
      controller-manager
    rootReconciler: Deployment state of root-reconciler
    syncer: Deployment state of the syncer pod
  """

  class AdmissionWebhookValueValuesEnum(_messages.Enum):
    r"""Deployment state of admission-webhook

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class GitSyncValueValuesEnum(_messages.Enum):
    r"""Deployment state of the git-sync pod

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class ImporterValueValuesEnum(_messages.Enum):
    r"""Deployment state of the importer pod

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class MonitorValueValuesEnum(_messages.Enum):
    r"""Deployment state of the monitor pod

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class OtelCollectorValueValuesEnum(_messages.Enum):
    r"""Deployment state of otel-collector

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class ReconcilerManagerValueValuesEnum(_messages.Enum):
    r"""Deployment state of reconciler-manager pod

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class ResourceGroupControllerManagerValueValuesEnum(_messages.Enum):
    r"""Deployment state of resource-group-controller-manager

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class RootReconcilerValueValuesEnum(_messages.Enum):
    r"""Deployment state of root-reconciler

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class SyncerValueValuesEnum(_messages.Enum):
    r"""Deployment state of the syncer pod

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  admissionWebhook = _messages.EnumField('AdmissionWebhookValueValuesEnum', 1)
  gitSync = _messages.EnumField('GitSyncValueValuesEnum', 2)
  importer = _messages.EnumField('ImporterValueValuesEnum', 3)
  monitor = _messages.EnumField('MonitorValueValuesEnum', 4)
  otelCollector = _messages.EnumField('OtelCollectorValueValuesEnum', 5)
  reconcilerManager = _messages.EnumField('ReconcilerManagerValueValuesEnum', 6)
  resourceGroupControllerManager = _messages.EnumField('ResourceGroupControllerManagerValueValuesEnum', 7)
  rootReconciler = _messages.EnumField('RootReconcilerValueValuesEnum', 8)
  syncer = _messages.EnumField('SyncerValueValuesEnum', 9)


class ConfigManagementConfigSyncError(_messages.Message):
  r"""Errors pertaining to the installation of Config Sync

  Fields:
    errorMessage: A string representing the user facing error message
  """

  errorMessage = _messages.StringField(1)


class ConfigManagementConfigSyncState(_messages.Message):
  r"""State information for ConfigSync

  Enums:
    ClusterLevelStopSyncingStateValueValuesEnum: Output only. Whether syncing
      resources to the cluster is stopped at the cluster level.
    ReposyncCrdValueValuesEnum: Output only. The state of the Reposync CRD
    RootsyncCrdValueValuesEnum: Output only. The state of the RootSync CRD
    StateValueValuesEnum: Output only. The state of CS This field summarizes
      the other fields in this message.

  Fields:
    clusterLevelStopSyncingState: Output only. Whether syncing resources to
      the cluster is stopped at the cluster level.
    crCount: Output only. The number of RootSync and RepoSync CRs in the
      cluster.
    deploymentState: Output only. Information about the deployment of
      ConfigSync, including the version of the various Pods deployed
    errors: Output only. Errors pertaining to the installation of Config Sync.
    reposyncCrd: Output only. The state of the Reposync CRD
    rootsyncCrd: Output only. The state of the RootSync CRD
    state: Output only. The state of CS This field summarizes the other fields
      in this message.
    syncState: Output only. The state of ConfigSync's process to sync configs
      to a cluster
    version: Output only. The version of ConfigSync deployed
  """

  class ClusterLevelStopSyncingStateValueValuesEnum(_messages.Enum):
    r"""Output only. Whether syncing resources to the cluster is stopped at
    the cluster level.

    Values:
      STOP_SYNCING_STATE_UNSPECIFIED: State cannot be determined
      NOT_STOPPED: Syncing resources to the cluster is not stopped at the
        cluster level.
      PENDING: Some reconcilers stop syncing resources to the cluster, while
        others are still syncing.
      STOPPED: Syncing resources to the cluster is stopped at the cluster
        level.
    """
    STOP_SYNCING_STATE_UNSPECIFIED = 0
    NOT_STOPPED = 1
    PENDING = 2
    STOPPED = 3

  class ReposyncCrdValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the Reposync CRD

    Values:
      CRD_STATE_UNSPECIFIED: CRD's state cannot be determined
      NOT_INSTALLED: CRD is not installed
      INSTALLED: CRD is installed
      TERMINATING: CRD is terminating (i.e., it has been deleted and is
        cleaning up)
      INSTALLING: CRD is installing
    """
    CRD_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    TERMINATING = 3
    INSTALLING = 4

  class RootsyncCrdValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the RootSync CRD

    Values:
      CRD_STATE_UNSPECIFIED: CRD's state cannot be determined
      NOT_INSTALLED: CRD is not installed
      INSTALLED: CRD is installed
      TERMINATING: CRD is terminating (i.e., it has been deleted and is
        cleaning up)
      INSTALLING: CRD is installing
    """
    CRD_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    TERMINATING = 3
    INSTALLING = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of CS This field summarizes the other fields in
    this message.

    Values:
      STATE_UNSPECIFIED: CS's state cannot be determined.
      CONFIG_SYNC_NOT_INSTALLED: CS is not installed.
      CONFIG_SYNC_INSTALLED: The expected CS version is installed
        successfully.
      CONFIG_SYNC_ERROR: CS encounters errors.
      CONFIG_SYNC_PENDING: CS is installing or terminating.
    """
    STATE_UNSPECIFIED = 0
    CONFIG_SYNC_NOT_INSTALLED = 1
    CONFIG_SYNC_INSTALLED = 2
    CONFIG_SYNC_ERROR = 3
    CONFIG_SYNC_PENDING = 4

  clusterLevelStopSyncingState = _messages.EnumField('ClusterLevelStopSyncingStateValueValuesEnum', 1)
  crCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  deploymentState = _messages.MessageField('ConfigManagementConfigSyncDeploymentState', 3)
  errors = _messages.MessageField('ConfigManagementConfigSyncError', 4, repeated=True)
  reposyncCrd = _messages.EnumField('ReposyncCrdValueValuesEnum', 5)
  rootsyncCrd = _messages.EnumField('RootsyncCrdValueValuesEnum', 6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  syncState = _messages.MessageField('ConfigManagementSyncState', 8)
  version = _messages.MessageField('ConfigManagementConfigSyncVersion', 9)


class ConfigManagementConfigSyncVersion(_messages.Message):
  r"""Specific versioning information pertaining to ConfigSync's Pods

  Fields:
    admissionWebhook: Version of the deployed admission-webhook pod
    gitSync: Version of the deployed git-sync pod
    importer: Version of the deployed importer pod
    monitor: Version of the deployed monitor pod
    otelCollector: Version of the deployed otel-collector pod
    reconcilerManager: Version of the deployed reconciler-manager pod
    resourceGroupControllerManager: Version of the deployed resource-group-
      controller-manager pod
    rootReconciler: Version of the deployed reconciler container in root-
      reconciler pod
    syncer: Version of the deployed syncer pod
  """

  admissionWebhook = _messages.StringField(1)
  gitSync = _messages.StringField(2)
  importer = _messages.StringField(3)
  monitor = _messages.StringField(4)
  otelCollector = _messages.StringField(5)
  reconcilerManager = _messages.StringField(6)
  resourceGroupControllerManager = _messages.StringField(7)
  rootReconciler = _messages.StringField(8)
  syncer = _messages.StringField(9)


class ConfigManagementContainerOverride(_messages.Message):
  r"""Configuration for a container override.

  Fields:
    containerName: Required. The name of the container.
    cpuLimit: Optional. The cpu limit of the container.
    cpuRequest: Optional. The cpu request of the container.
    memoryLimit: Optional. The memory limit of the container.
    memoryRequest: Optional. The memory request of the container.
  """

  containerName = _messages.StringField(1)
  cpuLimit = _messages.StringField(2)
  cpuRequest = _messages.StringField(3)
  memoryLimit = _messages.StringField(4)
  memoryRequest = _messages.StringField(5)


class ConfigManagementDeploymentOverride(_messages.Message):
  r"""Configuration for a deployment override.

  Fields:
    containers: Optional. The containers of the deployment resource to be
      overridden.
    deploymentName: Required. The name of the deployment resource to be
      overridden.
    deploymentNamespace: Required. The namespace of the deployment resource to
      be overridden.
  """

  containers = _messages.MessageField('ConfigManagementContainerOverride', 1, repeated=True)
  deploymentName = _messages.StringField(2)
  deploymentNamespace = _messages.StringField(3)


class ConfigManagementErrorResource(_messages.Message):
  r"""Model for a config file in the git repo with an associated Sync error

  Fields:
    resourceGvk: Group/version/kind of the resource that is causing an error
    resourceName: Metadata name of the resource that is causing an error
    resourceNamespace: Namespace of the resource that is causing an error
    sourcePath: Path in the git repo of the erroneous config
  """

  resourceGvk = _messages.MessageField('ConfigManagementGroupVersionKind', 1)
  resourceName = _messages.StringField(2)
  resourceNamespace = _messages.StringField(3)
  sourcePath = _messages.StringField(4)


class ConfigManagementGatekeeperDeploymentState(_messages.Message):
  r"""State of Policy Controller installation.

  Enums:
    GatekeeperAuditValueValuesEnum: Status of gatekeeper-audit deployment.
    GatekeeperControllerManagerStateValueValuesEnum: Status of gatekeeper-
      controller-manager pod.
    GatekeeperMutationValueValuesEnum: Status of the pod serving the mutation
      webhook.

  Fields:
    gatekeeperAudit: Status of gatekeeper-audit deployment.
    gatekeeperControllerManagerState: Status of gatekeeper-controller-manager
      pod.
    gatekeeperMutation: Status of the pod serving the mutation webhook.
  """

  class GatekeeperAuditValueValuesEnum(_messages.Enum):
    r"""Status of gatekeeper-audit deployment.

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class GatekeeperControllerManagerStateValueValuesEnum(_messages.Enum):
    r"""Status of gatekeeper-controller-manager pod.

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class GatekeeperMutationValueValuesEnum(_messages.Enum):
    r"""Status of the pod serving the mutation webhook.

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  gatekeeperAudit = _messages.EnumField('GatekeeperAuditValueValuesEnum', 1)
  gatekeeperControllerManagerState = _messages.EnumField('GatekeeperControllerManagerStateValueValuesEnum', 2)
  gatekeeperMutation = _messages.EnumField('GatekeeperMutationValueValuesEnum', 3)


class ConfigManagementGitConfig(_messages.Message):
  r"""Git repo configuration for a single cluster.

  Fields:
    gcpServiceAccountEmail: Optional. The Google Cloud Service Account Email
      used for auth when secret_type is gcpServiceAccount.
    httpsProxy: Optional. URL for the HTTPS proxy to be used when
      communicating with the Git repo.
    policyDir: Optional. The path within the Git repository that represents
      the top level of the repo to sync. Default: the root directory of the
      repository.
    secretType: Required. Type of secret configured for access to the Git
      repo. Must be one of ssh, cookiefile, gcenode, token, gcpserviceaccount,
      githubapp or none. The validation of this is case-sensitive.
    syncBranch: Optional. The branch of the repository to sync from. Default:
      master.
    syncRepo: Required. The URL of the Git repository to use as the source of
      truth.
    syncRev: Optional. Git revision (tag or hash) to check out. Default HEAD.
    syncWaitSecs: Optional. Period in seconds between consecutive syncs.
      Default: 15.
  """

  gcpServiceAccountEmail = _messages.StringField(1)
  httpsProxy = _messages.StringField(2)
  policyDir = _messages.StringField(3)
  secretType = _messages.StringField(4)
  syncBranch = _messages.StringField(5)
  syncRepo = _messages.StringField(6)
  syncRev = _messages.StringField(7)
  syncWaitSecs = _messages.IntegerField(8)


class ConfigManagementGroupVersionKind(_messages.Message):
  r"""A Kubernetes object's GVK

  Fields:
    group: Kubernetes Group
    kind: Kubernetes Kind
    version: Kubernetes Version
  """

  group = _messages.StringField(1)
  kind = _messages.StringField(2)
  version = _messages.StringField(3)


class ConfigManagementHierarchyControllerConfig(_messages.Message):
  r"""Configuration for Hierarchy Controller

  Fields:
    enableHierarchicalResourceQuota: Whether hierarchical resource quota is
      enabled in this cluster.
    enablePodTreeLabels: Whether pod tree labels are enabled in this cluster.
    enabled: Whether Hierarchy Controller is enabled in this cluster.
  """

  enableHierarchicalResourceQuota = _messages.BooleanField(1)
  enablePodTreeLabels = _messages.BooleanField(2)
  enabled = _messages.BooleanField(3)


class ConfigManagementHierarchyControllerDeploymentState(_messages.Message):
  r"""Deployment state for Hierarchy Controller

  Enums:
    ExtensionValueValuesEnum: The deployment state for Hierarchy Controller
      extension (e.g. v0.7.0-hc.1)
    HncValueValuesEnum: The deployment state for open source HNC (e.g.
      v0.7.0-hc.0)

  Fields:
    extension: The deployment state for Hierarchy Controller extension (e.g.
      v0.7.0-hc.1)
    hnc: The deployment state for open source HNC (e.g. v0.7.0-hc.0)
  """

  class ExtensionValueValuesEnum(_messages.Enum):
    r"""The deployment state for Hierarchy Controller extension (e.g.
    v0.7.0-hc.1)

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class HncValueValuesEnum(_messages.Enum):
    r"""The deployment state for open source HNC (e.g. v0.7.0-hc.0)

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  extension = _messages.EnumField('ExtensionValueValuesEnum', 1)
  hnc = _messages.EnumField('HncValueValuesEnum', 2)


class ConfigManagementHierarchyControllerState(_messages.Message):
  r"""State for Hierarchy Controller

  Fields:
    state: The deployment state for Hierarchy Controller
    version: The version for Hierarchy Controller
  """

  state = _messages.MessageField('ConfigManagementHierarchyControllerDeploymentState', 1)
  version = _messages.MessageField('ConfigManagementHierarchyControllerVersion', 2)


class ConfigManagementHierarchyControllerVersion(_messages.Message):
  r"""Version for Hierarchy Controller

  Fields:
    extension: Version for Hierarchy Controller extension
    hnc: Version for open source HNC
  """

  extension = _messages.StringField(1)
  hnc = _messages.StringField(2)


class ConfigManagementInstallError(_messages.Message):
  r"""Errors pertaining to the installation of ACM

  Fields:
    errorMessage: A string representing the user facing error message
  """

  errorMessage = _messages.StringField(1)


class ConfigManagementMembershipSpec(_messages.Message):
  r"""**Anthos Config Management**: Configuration for a single cluster.
  Intended to parallel the ConfigManagement CR.

  Enums:
    ManagementValueValuesEnum: Optional. Enables automatic Feature management.

  Fields:
    cluster: Optional. The user-specified cluster name used by Config Sync
      cluster-name-selector annotation or ClusterSelector, for applying
      configs to only a subset of clusters. Omit this field if the cluster's
      fleet membership name is used by Config Sync cluster-name-selector
      annotation or ClusterSelector. Set this field if a name different from
      the cluster's fleet membership name is used by Config Sync cluster-name-
      selector annotation or ClusterSelector.
    configSync: Optional. Config Sync configuration for the cluster.
    hierarchyController: Optional. Hierarchy Controller configuration for the
      cluster. Deprecated: Configuring Hierarchy Controller through the
      configmanagement feature is no longer recommended. Use
      https://github.com/kubernetes-sigs/hierarchical-namespaces instead.
    management: Optional. Enables automatic Feature management.
    policyController: Optional. Policy Controller configuration for the
      cluster. Deprecated: Configuring Policy Controller through the
      configmanagement feature is no longer recommended. Use the
      policycontroller feature instead.
    version: Optional. Version of ACM installed.
  """

  class ManagementValueValuesEnum(_messages.Enum):
    r"""Optional. Enables automatic Feature management.

    Values:
      MANAGEMENT_UNSPECIFIED: Unspecified
      MANAGEMENT_AUTOMATIC: Google will manage the Feature for the cluster.
      MANAGEMENT_MANUAL: User will manually manage the Feature for the
        cluster.
    """
    MANAGEMENT_UNSPECIFIED = 0
    MANAGEMENT_AUTOMATIC = 1
    MANAGEMENT_MANUAL = 2

  cluster = _messages.StringField(1)
  configSync = _messages.MessageField('ConfigManagementConfigSync', 2)
  hierarchyController = _messages.MessageField('ConfigManagementHierarchyControllerConfig', 3)
  management = _messages.EnumField('ManagementValueValuesEnum', 4)
  policyController = _messages.MessageField('ConfigManagementPolicyController', 5)
  version = _messages.StringField(6)


class ConfigManagementMembershipState(_messages.Message):
  r"""**Anthos Config Management**: State for a single cluster.

  Fields:
    clusterName: Output only. This field is set to the `cluster_name` field of
      the Membership Spec if it is not empty. Otherwise, it is set to the
      cluster's fleet membership name.
    configSyncState: Output only. Current sync status
    hierarchyControllerState: Output only. Hierarchy Controller status
    membershipSpec: Output only. Membership configuration in the cluster. This
      represents the actual state in the cluster, while the MembershipSpec in
      the FeatureSpec represents the intended state
    operatorState: Output only. Current install status of ACM's Operator
    policyControllerState: Output only. PolicyController status
  """

  clusterName = _messages.StringField(1)
  configSyncState = _messages.MessageField('ConfigManagementConfigSyncState', 2)
  hierarchyControllerState = _messages.MessageField('ConfigManagementHierarchyControllerState', 3)
  membershipSpec = _messages.MessageField('ConfigManagementMembershipSpec', 4)
  operatorState = _messages.MessageField('ConfigManagementOperatorState', 5)
  policyControllerState = _messages.MessageField('ConfigManagementPolicyControllerState', 6)


class ConfigManagementOciConfig(_messages.Message):
  r"""OCI repo configuration for a single cluster

  Fields:
    gcpServiceAccountEmail: Optional. The Google Cloud Service Account Email
      used for auth when secret_type is gcpServiceAccount.
    policyDir: Optional. The absolute path of the directory that contains the
      local resources. Default: the root directory of the image.
    secretType: Required. Type of secret configured for access to the OCI
      repo. Must be one of gcenode, gcpserviceaccount, k8sserviceaccount or
      none. The validation of this is case-sensitive.
    syncRepo: Required. The OCI image repository URL for the package to sync
      from. e.g. `LOCATION-
      docker.pkg.dev/PROJECT_ID/REPOSITORY_NAME/PACKAGE_NAME`.
    syncWaitSecs: Optional. Period in seconds between consecutive syncs.
      Default: 15.
  """

  gcpServiceAccountEmail = _messages.StringField(1)
  policyDir = _messages.StringField(2)
  secretType = _messages.StringField(3)
  syncRepo = _messages.StringField(4)
  syncWaitSecs = _messages.IntegerField(5)


class ConfigManagementOperatorState(_messages.Message):
  r"""State information for an ACM's Operator

  Enums:
    DeploymentStateValueValuesEnum: The state of the Operator's deployment

  Fields:
    deploymentState: The state of the Operator's deployment
    errors: Install errors.
    version: The semenatic version number of the operator
  """

  class DeploymentStateValueValuesEnum(_messages.Enum):
    r"""The state of the Operator's deployment

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  deploymentState = _messages.EnumField('DeploymentStateValueValuesEnum', 1)
  errors = _messages.MessageField('ConfigManagementInstallError', 2, repeated=True)
  version = _messages.StringField(3)


class ConfigManagementPolicyController(_messages.Message):
  r"""Configuration for Policy Controller

  Fields:
    auditIntervalSeconds: Sets the interval for Policy Controller Audit Scans
      (in seconds). When set to 0, this disables audit functionality
      altogether.
    enabled: Enables the installation of Policy Controller. If false, the rest
      of PolicyController fields take no effect.
    exemptableNamespaces: The set of namespaces that are excluded from Policy
      Controller checks. Namespaces do not need to currently exist on the
      cluster.
    logDeniesEnabled: Logs all denies and dry run failures.
    monitoring: Monitoring specifies the configuration of monitoring.
    mutationEnabled: Enable or disable mutation in policy controller. If true,
      mutation CRDs, webhook and controller deployment will be deployed to the
      cluster.
    referentialRulesEnabled: Enables the ability to use Constraint Templates
      that reference to objects other than the object currently being
      evaluated.
    templateLibraryInstalled: Installs the default template library along with
      Policy Controller.
    updateTime: Output only. Last time this membership spec was updated.
  """

  auditIntervalSeconds = _messages.IntegerField(1)
  enabled = _messages.BooleanField(2)
  exemptableNamespaces = _messages.StringField(3, repeated=True)
  logDeniesEnabled = _messages.BooleanField(4)
  monitoring = _messages.MessageField('ConfigManagementPolicyControllerMonitoring', 5)
  mutationEnabled = _messages.BooleanField(6)
  referentialRulesEnabled = _messages.BooleanField(7)
  templateLibraryInstalled = _messages.BooleanField(8)
  updateTime = _messages.StringField(9)


class ConfigManagementPolicyControllerMigration(_messages.Message):
  r"""State for the migration of PolicyController from ACM -> PoCo Hub.

  Enums:
    StageValueValuesEnum: Stage of the migration.

  Fields:
    copyTime: Last time this membership spec was copied to PoCo feature.
    stage: Stage of the migration.
  """

  class StageValueValuesEnum(_messages.Enum):
    r"""Stage of the migration.

    Values:
      STAGE_UNSPECIFIED: Unknown state of migration.
      ACM_MANAGED: ACM Hub/Operator manages policycontroller. No migration yet
        completed.
      POCO_MANAGED: All migrations steps complete; Poco Hub now manages
        policycontroller.
    """
    STAGE_UNSPECIFIED = 0
    ACM_MANAGED = 1
    POCO_MANAGED = 2

  copyTime = _messages.StringField(1)
  stage = _messages.EnumField('StageValueValuesEnum', 2)


class ConfigManagementPolicyControllerMonitoring(_messages.Message):
  r"""PolicyControllerMonitoring specifies the backends Policy Controller
  should export metrics to. For example, to specify metrics should be exported
  to Cloud Monitoring and Prometheus, specify backends: ["cloudmonitoring",
  "prometheus"]

  Enums:
    BackendsValueListEntryValuesEnum:

  Fields:
    backends: Specifies the list of backends Policy Controller will export to.
      An empty list would effectively disable metrics export.
  """

  class BackendsValueListEntryValuesEnum(_messages.Enum):
    r"""BackendsValueListEntryValuesEnum enum type.

    Values:
      MONITORING_BACKEND_UNSPECIFIED: Backend cannot be determined
      PROMETHEUS: Prometheus backend for monitoring
      CLOUD_MONITORING: Stackdriver/Cloud Monitoring backend for monitoring
    """
    MONITORING_BACKEND_UNSPECIFIED = 0
    PROMETHEUS = 1
    CLOUD_MONITORING = 2

  backends = _messages.EnumField('BackendsValueListEntryValuesEnum', 1, repeated=True)


class ConfigManagementPolicyControllerState(_messages.Message):
  r"""State for PolicyControllerState.

  Fields:
    deploymentState: The state about the policy controller installation.
    migration: Record state of ACM -> PoCo Hub migration for this feature.
    version: The version of Gatekeeper Policy Controller deployed.
  """

  deploymentState = _messages.MessageField('ConfigManagementGatekeeperDeploymentState', 1)
  migration = _messages.MessageField('ConfigManagementPolicyControllerMigration', 2)
  version = _messages.MessageField('ConfigManagementPolicyControllerVersion', 3)


class ConfigManagementPolicyControllerVersion(_messages.Message):
  r"""The build version of Gatekeeper Policy Controller is using.

  Fields:
    version: The gatekeeper image tag that is composed of ACM version, git
      tag, build number.
  """

  version = _messages.StringField(1)


class ConfigManagementSyncError(_messages.Message):
  r"""An ACM created error representing a problem syncing configurations

  Fields:
    code: An ACM defined error code
    errorMessage: A description of the error
    errorResources: A list of config(s) associated with the error, if any
  """

  code = _messages.StringField(1)
  errorMessage = _messages.StringField(2)
  errorResources = _messages.MessageField('ConfigManagementErrorResource', 3, repeated=True)


class ConfigManagementSyncState(_messages.Message):
  r"""State indicating an ACM's progress syncing configurations to a cluster

  Enums:
    CodeValueValuesEnum: Sync status code

  Fields:
    code: Sync status code
    errors: A list of errors resulting from problematic configs. This list
      will be truncated after 100 errors, although it is unlikely for that
      many errors to simultaneously exist.
    importToken: Token indicating the state of the importer.
    lastSync: Deprecated: use last_sync_time instead. Timestamp of when ACM
      last successfully synced the repo The time format is specified in
      https://golang.org/pkg/time/#Time.String
    lastSyncTime: Timestamp type of when ACM last successfully synced the repo
    sourceToken: Token indicating the state of the repo.
    syncToken: Token indicating the state of the syncer.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Sync status code

    Values:
      SYNC_CODE_UNSPECIFIED: Config Sync cannot determine a sync code
      SYNCED: Config Sync successfully synced the git Repo with the cluster
      PENDING: Config Sync is in the progress of syncing a new change
      ERROR: Indicates an error configuring Config Sync, and user action is
        required
      NOT_CONFIGURED: Config Sync has been installed but not configured
      NOT_INSTALLED: Config Sync has not been installed
      UNAUTHORIZED: Error authorizing with the cluster
      UNREACHABLE: Cluster could not be reached
    """
    SYNC_CODE_UNSPECIFIED = 0
    SYNCED = 1
    PENDING = 2
    ERROR = 3
    NOT_CONFIGURED = 4
    NOT_INSTALLED = 5
    UNAUTHORIZED = 6
    UNREACHABLE = 7

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  errors = _messages.MessageField('ConfigManagementSyncError', 2, repeated=True)
  importToken = _messages.StringField(3)
  lastSync = _messages.StringField(4)
  lastSyncTime = _messages.StringField(5)
  sourceToken = _messages.StringField(6)
  syncToken = _messages.StringField(7)


class ConnectAgentResource(_messages.Message):
  r"""ConnectAgentResource represents a Kubernetes resource manifest for
  Connect Agent deployment.

  Fields:
    manifest: YAML manifest of the resource.
    type: Kubernetes type of the resource.
  """

  manifest = _messages.StringField(1)
  type = _messages.MessageField('TypeMeta', 2)


class CounterOptions(_messages.Message):
  r"""Increment a streamz counter with the specified metric and field names.
  Metric names should start with a '/', generally be lowercase-only, and end
  in "_count". Field names should not contain an initial slash. The actual
  exported metric names will have "/iam/policy" prepended. Field names
  correspond to IAM request parameters and field values are their respective
  values. Supported field names: - "authority", which is "[token]" if
  IAMContext.token is present, otherwise the value of
  IAMContext.authority_selector if present, and otherwise a representation of
  IAMContext.principal; or - "iam_principal", a representation of
  IAMContext.principal even if a token or authority selector is present; or -
  "" (empty string), resulting in a counter with no fields. Examples: counter
  { metric: "/debug_access_count" field: "iam_principal" } ==> increment
  counter /iam/policy/debug_access_count {iam_principal=[value of
  IAMContext.principal]}

  Fields:
    customFields: Custom fields.
    field: The field value to attribute.
    metric: The metric to update.
  """

  customFields = _messages.MessageField('CustomField', 1, repeated=True)
  field = _messages.StringField(2)
  metric = _messages.StringField(3)


class CreateReferenceRequest(_messages.Message):
  r"""The CreateReferenceRequest request.

  Fields:
    parent: Required. The parent resource name (target_resource of this
      reference). For example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}`.
    reference: Required. The reference to be created.
    referenceId: The unique id of this resource. Must be unique within a scope
      of a target resource, but does not have to be globally unique. Reference
      ID is part of resource name of the reference. Resource name is generated
      in the following way: {parent}/references/{reference_id}. Reference ID
      field is currently required but id auto generation might be added in the
      future. It can be any arbitrary string, either GUID or any other string,
      however CLHs can use preprocess callbacks to perform a custom
      validation.
    requestId: Optional. Request ID is an idempotency ID of the request. It
      must be a valid UUID. Zero UUID (00000000-0000-0000-0000-000000000000)
      is not supported.
  """

  parent = _messages.StringField(1)
  reference = _messages.MessageField('Reference', 2)
  referenceId = _messages.StringField(3)
  requestId = _messages.StringField(4)


class CustomField(_messages.Message):
  r"""Custom fields. These can be used to create a counter with arbitrary
  field/value pairs. See: go/rpcsp-custom-fields.

  Fields:
    name: Name is the field name.
    value: Value is the field value. It is important that in contrast to the
      CounterOptions.field, the value here is a constant that is not derived
      from the IAMContext.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class DataAccessOptions(_messages.Message):
  r"""Write a Data Access (Gin) log

  Enums:
    LogModeValueValuesEnum:

  Fields:
    isDirectAuth: Indicates that access was granted by a regular grant policy
    logMode: A LogModeValueValuesEnum attribute.
  """

  class LogModeValueValuesEnum(_messages.Enum):
    r"""LogModeValueValuesEnum enum type.

    Values:
      LOG_MODE_UNSPECIFIED: Client is not required to write a partial Gin log
        immediately after the authorization check. If client chooses to write
        one and it fails, client may either fail open (allow the operation to
        continue) or fail closed (handle as a DENY outcome).
      LOG_FAIL_CLOSED: The application's operation in the context of which
        this authorization check is being made may only be performed if it is
        successfully logged to Gin. For instance, the authorization library
        may satisfy this obligation by emitting a partial log entry at
        authorization check time and only returning ALLOW to the application
        if it succeeds. If a matching Rule has this directive, but the client
        has not indicated that it will honor such requirements, then the IAM
        check will result in authorization failure by setting
        CheckPolicyResponse.success=false.
    """
    LOG_MODE_UNSPECIFIED = 0
    LOG_FAIL_CLOSED = 1

  isDirectAuth = _messages.BooleanField(1)
  logMode = _messages.EnumField('LogModeValueValuesEnum', 2)


class DataplaneV2FeatureSpec(_messages.Message):
  r"""**Dataplane V2**: Spec

  Fields:
    enableEncryption: Enable dataplane-v2 based encryption for multiple
      clusters.
  """

  enableEncryption = _messages.BooleanField(1)


class DefaultClusterConfig(_messages.Message):
  r"""DefaultClusterConfig describes the default cluster configurations to be
  applied to all clusters born-in-fleet.

  Fields:
    binaryAuthorizationConfig: Optional. Enable/Disable binary authorization
      features for the cluster.
    compliancePostureConfig: Optional. Enable/Disable Compliance Posture
      features for the cluster. Note that on UpdateFleet, only full
      replacement of this field is allowed. Users are not allowed for partial
      updates through field mask.
    securityPostureConfig: Enable/Disable Security Posture features for the
      cluster.
  """

  binaryAuthorizationConfig = _messages.MessageField('BinaryAuthorizationConfig', 1)
  compliancePostureConfig = _messages.MessageField('CompliancePostureConfig', 2)
  securityPostureConfig = _messages.MessageField('SecurityPostureConfig', 3)


class DeleteReferenceRequest(_messages.Message):
  r"""The DeleteReferenceRequest request.

  Fields:
    name: Required. Full resource name of the reference, in the following
      format:
      `//{targer_service}/{target_resource}/references/{reference_id}`. For
      example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}/references/{xyz}`.
    requestId: Optional. Request ID is an idempotency ID of the request. It
      must be a valid UUID. Zero UUID (00000000-0000-0000-0000-000000000000)
      is not supported.
  """

  name = _messages.StringField(1)
  requestId = _messages.StringField(2)


class EdgeCluster(_messages.Message):
  r"""EdgeCluster contains information specific to Google Edge Clusters.

  Fields:
    clusterVersion: Output only. The product version of the Edge Cluster, e.g.
      "1.8.0".
    resourceLink: Immutable. Self-link of the Google Cloud resource for the
      Edge Cluster. For example: //edgecontainer.googleapis.com/projects/my-
      project/locations/us-west1-a/clusters/my-cluster
  """

  clusterVersion = _messages.StringField(1)
  resourceLink = _messages.StringField(2)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class Feature(_messages.Message):
  r"""Feature represents the settings and status of any Fleet Feature.

  Messages:
    LabelsValue: Labels for this Feature.
    MembershipSpecsValue: Optional. Membership-specific configuration for this
      Feature. If this Feature does not support any per-Membership
      configuration, this field may be unused. The keys indicate which
      Membership the configuration is for, in the form:
      `projects/{p}/locations/{l}/memberships/{m}` Where {p} is the project,
      {l} is a valid location and {m} is a valid Membership in this project at
      that location. {p} WILL match the Feature's project. {p} will always be
      returned as the project number, but the project ID is also accepted
      during input. If the same Membership is specified in the map twice
      (using the project ID form, and the project number form), exactly ONE of
      the entries will be saved, with no guarantees as to which. For this
      reason, it is recommended the same format be used for all entries when
      mutating a Feature.
    MembershipStatesValue: Output only. Membership-specific Feature status. If
      this Feature does report any per-Membership status, this field may be
      unused. The keys indicate which Membership the state is for, in the
      form: `projects/{p}/locations/{l}/memberships/{m}` Where {p} is the
      project number, {l} is a valid location and {m} is a valid Membership in
      this project at that location. {p} MUST match the Feature's project
      number.
    ScopeSpecsValue: Optional. Scope-specific configuration for this Feature.
      If this Feature does not support any per-Scope configuration, this field
      may be unused. The keys indicate which Scope the configuration is for,
      in the form: `projects/{p}/locations/global/scopes/{s}` Where {p} is the
      project, {s} is a valid Scope in this project. {p} WILL match the
      Feature's project. {p} will always be returned as the project number,
      but the project ID is also accepted during input. If the same Scope is
      specified in the map twice (using the project ID form, and the project
      number form), exactly ONE of the entries will be saved, with no
      guarantees as to which. For this reason, it is recommended the same
      format be used for all entries when mutating a Feature.
    ScopeStatesValue: Output only. Scope-specific Feature status. If this
      Feature does report any per-Scope status, this field may be unused. The
      keys indicate which Scope the state is for, in the form:
      `projects/{p}/locations/global/scopes/{s}` Where {p} is the project, {s}
      is a valid Scope in this project. {p} WILL match the Feature's project.

  Fields:
    createTime: Output only. When the Feature resource was created.
    deleteTime: Output only. When the Feature resource was deleted.
    etag: Optional. This checksum is computed by the server based on the value
      of other fields, and may be sent on update and delete requests to ensure
      the client has an up-to-date value before proceeding.
    fleetDefaultMemberConfig: Optional. Feature configuration applicable to
      all memberships of the fleet.
    labels: Labels for this Feature.
    membershipSpecs: Optional. Membership-specific configuration for this
      Feature. If this Feature does not support any per-Membership
      configuration, this field may be unused. The keys indicate which
      Membership the configuration is for, in the form:
      `projects/{p}/locations/{l}/memberships/{m}` Where {p} is the project,
      {l} is a valid location and {m} is a valid Membership in this project at
      that location. {p} WILL match the Feature's project. {p} will always be
      returned as the project number, but the project ID is also accepted
      during input. If the same Membership is specified in the map twice
      (using the project ID form, and the project number form), exactly ONE of
      the entries will be saved, with no guarantees as to which. For this
      reason, it is recommended the same format be used for all entries when
      mutating a Feature.
    membershipStates: Output only. Membership-specific Feature status. If this
      Feature does report any per-Membership status, this field may be unused.
      The keys indicate which Membership the state is for, in the form:
      `projects/{p}/locations/{l}/memberships/{m}` Where {p} is the project
      number, {l} is a valid location and {m} is a valid Membership in this
      project at that location. {p} MUST match the Feature's project number.
    name: Output only. The full, unique name of this Feature resource in the
      format `projects/*/locations/*/features/*`.
    resourceState: Output only. State of the Feature resource itself.
    scopeSpecs: Optional. Scope-specific configuration for this Feature. If
      this Feature does not support any per-Scope configuration, this field
      may be unused. The keys indicate which Scope the configuration is for,
      in the form: `projects/{p}/locations/global/scopes/{s}` Where {p} is the
      project, {s} is a valid Scope in this project. {p} WILL match the
      Feature's project. {p} will always be returned as the project number,
      but the project ID is also accepted during input. If the same Scope is
      specified in the map twice (using the project ID form, and the project
      number form), exactly ONE of the entries will be saved, with no
      guarantees as to which. For this reason, it is recommended the same
      format be used for all entries when mutating a Feature.
    scopeStates: Output only. Scope-specific Feature status. If this Feature
      does report any per-Scope status, this field may be unused. The keys
      indicate which Scope the state is for, in the form:
      `projects/{p}/locations/global/scopes/{s}` Where {p} is the project, {s}
      is a valid Scope in this project. {p} WILL match the Feature's project.
    spec: Optional. Fleet-wide Feature configuration. If this Feature does not
      support any Fleet-wide configuration, this field may be unused.
    state: Output only. The Fleet-wide Feature state.
    unreachable: Output only. List of locations that could not be reached
      while fetching this feature.
    updateTime: Output only. When the Feature resource was last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels for this Feature.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MembershipSpecsValue(_messages.Message):
    r"""Optional. Membership-specific configuration for this Feature. If this
    Feature does not support any per-Membership configuration, this field may
    be unused. The keys indicate which Membership the configuration is for, in
    the form: `projects/{p}/locations/{l}/memberships/{m}` Where {p} is the
    project, {l} is a valid location and {m} is a valid Membership in this
    project at that location. {p} WILL match the Feature's project. {p} will
    always be returned as the project number, but the project ID is also
    accepted during input. If the same Membership is specified in the map
    twice (using the project ID form, and the project number form), exactly
    ONE of the entries will be saved, with no guarantees as to which. For this
    reason, it is recommended the same format be used for all entries when
    mutating a Feature.

    Messages:
      AdditionalProperty: An additional property for a MembershipSpecsValue
        object.

    Fields:
      additionalProperties: Additional properties of type MembershipSpecsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MembershipSpecsValue object.

      Fields:
        key: Name of the additional property.
        value: A MembershipFeatureSpec attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('MembershipFeatureSpec', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MembershipStatesValue(_messages.Message):
    r"""Output only. Membership-specific Feature status. If this Feature does
    report any per-Membership status, this field may be unused. The keys
    indicate which Membership the state is for, in the form:
    `projects/{p}/locations/{l}/memberships/{m}` Where {p} is the project
    number, {l} is a valid location and {m} is a valid Membership in this
    project at that location. {p} MUST match the Feature's project number.

    Messages:
      AdditionalProperty: An additional property for a MembershipStatesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        MembershipStatesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MembershipStatesValue object.

      Fields:
        key: Name of the additional property.
        value: A MembershipFeatureState attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('MembershipFeatureState', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ScopeSpecsValue(_messages.Message):
    r"""Optional. Scope-specific configuration for this Feature. If this
    Feature does not support any per-Scope configuration, this field may be
    unused. The keys indicate which Scope the configuration is for, in the
    form: `projects/{p}/locations/global/scopes/{s}` Where {p} is the project,
    {s} is a valid Scope in this project. {p} WILL match the Feature's
    project. {p} will always be returned as the project number, but the
    project ID is also accepted during input. If the same Scope is specified
    in the map twice (using the project ID form, and the project number form),
    exactly ONE of the entries will be saved, with no guarantees as to which.
    For this reason, it is recommended the same format be used for all entries
    when mutating a Feature.

    Messages:
      AdditionalProperty: An additional property for a ScopeSpecsValue object.

    Fields:
      additionalProperties: Additional properties of type ScopeSpecsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ScopeSpecsValue object.

      Fields:
        key: Name of the additional property.
        value: A ScopeFeatureSpec attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ScopeFeatureSpec', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ScopeStatesValue(_messages.Message):
    r"""Output only. Scope-specific Feature status. If this Feature does
    report any per-Scope status, this field may be unused. The keys indicate
    which Scope the state is for, in the form:
    `projects/{p}/locations/global/scopes/{s}` Where {p} is the project, {s}
    is a valid Scope in this project. {p} WILL match the Feature's project.

    Messages:
      AdditionalProperty: An additional property for a ScopeStatesValue
        object.

    Fields:
      additionalProperties: Additional properties of type ScopeStatesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ScopeStatesValue object.

      Fields:
        key: Name of the additional property.
        value: A ScopeFeatureState attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ScopeFeatureState', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  deleteTime = _messages.StringField(2)
  etag = _messages.StringField(3)
  fleetDefaultMemberConfig = _messages.MessageField('CommonFleetDefaultMemberConfigSpec', 4)
  labels = _messages.MessageField('LabelsValue', 5)
  membershipSpecs = _messages.MessageField('MembershipSpecsValue', 6)
  membershipStates = _messages.MessageField('MembershipStatesValue', 7)
  name = _messages.StringField(8)
  resourceState = _messages.MessageField('FeatureResourceState', 9)
  scopeSpecs = _messages.MessageField('ScopeSpecsValue', 10)
  scopeStates = _messages.MessageField('ScopeStatesValue', 11)
  spec = _messages.MessageField('CommonFeatureSpec', 12)
  state = _messages.MessageField('CommonFeatureState', 13)
  unreachable = _messages.StringField(14, repeated=True)
  updateTime = _messages.StringField(15)


class FeatureResourceState(_messages.Message):
  r"""FeatureResourceState describes the state of a Feature *resource* in the
  GkeHub API. See `FeatureState` for the "running state" of the Feature in the
  Fleet and across Memberships.

  Enums:
    StateValueValuesEnum: The current state of the Feature resource in the Hub
      API.

  Fields:
    state: The current state of the Feature resource in the Hub API.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The current state of the Feature resource in the Hub API.

    Values:
      STATE_UNSPECIFIED: State is unknown or not set.
      ENABLING: The Feature is being enabled, and the Feature resource is
        being created. Once complete, the corresponding Feature will be
        enabled in this Fleet.
      ACTIVE: The Feature is enabled in this Fleet, and the Feature resource
        is fully available.
      DISABLING: The Feature is being disabled in this Fleet, and the Feature
        resource is being deleted.
      UPDATING: The Feature resource is being updated.
      SERVICE_UPDATING: The Feature resource is being updated by the Hub
        Service.
    """
    STATE_UNSPECIFIED = 0
    ENABLING = 1
    ACTIVE = 2
    DISABLING = 3
    UPDATING = 4
    SERVICE_UPDATING = 5

  state = _messages.EnumField('StateValueValuesEnum', 1)


class FeatureState(_messages.Message):
  r"""FeatureState describes the high-level state of a Feature. It may be used
  to describe a Feature's state at the environ-level, or per-membershop,
  depending on the context.

  Enums:
    CodeValueValuesEnum: The high-level, machine-readable status of this
      Feature.

  Fields:
    code: The high-level, machine-readable status of this Feature.
    description: A human-readable description of the current status.
    updateTime: The time this status and any related Feature-specific details
      were updated.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""The high-level, machine-readable status of this Feature.

    Values:
      CODE_UNSPECIFIED: Unknown or not set.
      OK: The Feature is operating normally.
      WARNING: The Feature has encountered an issue, and is operating in a
        degraded state. The Feature may need intervention to return to normal
        operation. See the description and any associated Feature-specific
        details for more information.
      ERROR: The Feature is not operating or is in a severely degraded state.
        The Feature may need intervention to return to normal operation. See
        the description and any associated Feature-specific details for more
        information.
    """
    CODE_UNSPECIFIED = 0
    OK = 1
    WARNING = 2
    ERROR = 3

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  description = _messages.StringField(2)
  updateTime = _messages.StringField(3)


class Fleet(_messages.Message):
  r"""Fleet contains the Fleet-wide metadata and configuration.

  Messages:
    LabelsValue: Optional. Labels for this Fleet.

  Fields:
    createTime: Output only. When the Fleet was created.
    defaultClusterConfig: Optional. The default cluster configurations to
      apply across the fleet.
    deleteTime: Output only. When the Fleet was deleted.
    displayName: Optional. A user-assigned display name of the Fleet. When
      present, it must be between 4 to 30 characters. Allowed characters are:
      lowercase and uppercase letters, numbers, hyphen, single-quote, double-
      quote, space, and exclamation point. Example: `Production Fleet`
    labels: Optional. Labels for this Fleet.
    name: Output only. The full, unique resource name of this fleet in the
      format of `projects/{project}/locations/{location}/fleets/{fleet}`. Each
      Google Cloud project can have at most one fleet resource, named
      "default".
    state: Output only. State of the namespace resource.
    uid: Output only. Google-generated UUID for this resource. This is unique
      across all Fleet resources. If a Fleet resource is deleted and another
      resource with the same name is created, it gets a different uid.
    updateTime: Output only. When the Fleet was last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels for this Fleet.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  defaultClusterConfig = _messages.MessageField('DefaultClusterConfig', 2)
  deleteTime = _messages.StringField(3)
  displayName = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  state = _messages.MessageField('FleetLifecycleState', 7)
  uid = _messages.StringField(8)
  updateTime = _messages.StringField(9)


class FleetLifecycleState(_messages.Message):
  r"""FleetLifecycleState describes the state of a Fleet resource.

  Enums:
    CodeValueValuesEnum: Output only. The current state of the Fleet resource.

  Fields:
    code: Output only. The current state of the Fleet resource.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the Fleet resource.

    Values:
      CODE_UNSPECIFIED: The code is not set.
      CREATING: The fleet is being created.
      READY: The fleet active.
      DELETING: The fleet is being deleted.
      UPDATING: The fleet is being updated.
    """
    CODE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    DELETING = 3
    UPDATING = 4

  code = _messages.EnumField('CodeValueValuesEnum', 1)


class FleetObservabilityFeatureError(_messages.Message):
  r"""All error details of the fleet observability feature.

  Fields:
    code: The code of the error.
    description: A human-readable description of the current status.
  """

  code = _messages.StringField(1)
  description = _messages.StringField(2)


class FleetObservabilityFeatureSpec(_messages.Message):
  r"""**Fleet Observability**: The Hub-wide input for the FleetObservability
  feature.

  Fields:
    loggingConfig: Specified if fleet logging feature is enabled for the
      entire fleet. If UNSPECIFIED, fleet logging feature is disabled for the
      entire fleet.
  """

  loggingConfig = _messages.MessageField('FleetObservabilityLoggingConfig', 1)


class FleetObservabilityFeatureState(_messages.Message):
  r"""**FleetObservability**: Hub-wide Feature for FleetObservability feature.
  state.

  Fields:
    logging: The feature state of default logging.
    monitoring: The feature state of fleet monitoring.
  """

  logging = _messages.MessageField('FleetObservabilityFleetObservabilityLoggingState', 1)
  monitoring = _messages.MessageField('FleetObservabilityFleetObservabilityMonitoringState', 2)


class FleetObservabilityFleetObservabilityBaseFeatureState(_messages.Message):
  r"""Base state for fleet observability feature.

  Enums:
    CodeValueValuesEnum: The high-level, machine-readable status of this
      Feature.

  Fields:
    code: The high-level, machine-readable status of this Feature.
    errors: Errors after reconciling the monitoring and logging feature if the
      code is not OK.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""The high-level, machine-readable status of this Feature.

    Values:
      CODE_UNSPECIFIED: Unknown or not set.
      OK: The Feature is operating normally.
      ERROR: The Feature is encountering errors in the reconciliation. The
        Feature may need intervention to return to normal operation. See the
        description and any associated Feature-specific details for more
        information.
    """
    CODE_UNSPECIFIED = 0
    OK = 1
    ERROR = 2

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  errors = _messages.MessageField('FleetObservabilityFeatureError', 2, repeated=True)


class FleetObservabilityFleetObservabilityLoggingState(_messages.Message):
  r"""Feature state for logging feature.

  Fields:
    defaultLog: The base feature state of fleet default log.
    scopeLog: The base feature state of fleet scope log.
  """

  defaultLog = _messages.MessageField('FleetObservabilityFleetObservabilityBaseFeatureState', 1)
  scopeLog = _messages.MessageField('FleetObservabilityFleetObservabilityBaseFeatureState', 2)


class FleetObservabilityFleetObservabilityMonitoringState(_messages.Message):
  r"""Feature state for monitoring feature.

  Fields:
    state: The base feature state of fleet monitoring feature.
  """

  state = _messages.MessageField('FleetObservabilityFleetObservabilityBaseFeatureState', 1)


class FleetObservabilityLoggingConfig(_messages.Message):
  r"""LoggingConfig defines the configuration for different types of logs.

  Fields:
    defaultConfig: Specified if applying the default routing config to logs
      not specified in other configs.
    fleetScopeLogsConfig: Specified if applying the routing config to all logs
      for all fleet scopes.
  """

  defaultConfig = _messages.MessageField('FleetObservabilityRoutingConfig', 1)
  fleetScopeLogsConfig = _messages.MessageField('FleetObservabilityRoutingConfig', 2)


class FleetObservabilityMembershipSpec(_messages.Message):
  r"""**FleetObservability**: The membership-specific input for
  FleetObservability feature.
  """



class FleetObservabilityMembershipState(_messages.Message):
  r"""**FleetObservability**: Membership-specific Feature state for
  fleetobservability.
  """



class FleetObservabilityRoutingConfig(_messages.Message):
  r"""RoutingConfig configures the behaviour of fleet logging feature.

  Enums:
    ModeValueValuesEnum: mode configures the logs routing mode.

  Fields:
    mode: mode configures the logs routing mode.
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""mode configures the logs routing mode.

    Values:
      MODE_UNSPECIFIED: If UNSPECIFIED, fleet logging feature is disabled.
      COPY: logs will be copied to the destination project.
      MOVE: logs will be moved to the destination project.
    """
    MODE_UNSPECIFIED = 0
    COPY = 1
    MOVE = 2

  mode = _messages.EnumField('ModeValueValuesEnum', 1)


class GenerateConnectManifestResponse(_messages.Message):
  r"""GenerateConnectManifestResponse contains manifest information for
  installing/upgrading a Connect agent.

  Fields:
    manifest: The ordered list of Kubernetes resources that need to be applied
      to the cluster for GKE Connect agent installation/upgrade.
  """

  manifest = _messages.MessageField('ConnectAgentResource', 1, repeated=True)


class GenerateMembershipRBACRoleBindingYAMLResponse(_messages.Message):
  r"""Response for GenerateRBACRoleBindingYAML.

  Fields:
    roleBindingsYaml: a yaml text blob including the RBAC policies.
  """

  roleBindingsYaml = _messages.StringField(1)


class GetReferenceRequest(_messages.Message):
  r"""The GetReferenceRequest request.

  Fields:
    name: Required. Full resource name of the reference, in the following
      format:
      `//{target_service}/{target_resource}/references/{reference_id}`. For
      example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}/references/{xyz}`.
  """

  name = _messages.StringField(1)


class GkeCluster(_messages.Message):
  r"""GkeCluster contains information specific to GKE clusters.

  Fields:
    clusterMissing: Output only. If cluster_missing is set then it denotes
      that the GKE cluster no longer exists in the GKE Control Plane.
    resourceLink: Immutable. Self-link of the Google Cloud resource for the
      GKE cluster. For example: //container.googleapis.com/projects/my-
      project/locations/us-west1-a/clusters/my-cluster Zonal clusters are also
      supported.
  """

  clusterMissing = _messages.BooleanField(1)
  resourceLink = _messages.StringField(2)


class GkehubOrganizationsLocationsFleetsListRequest(_messages.Message):
  r"""A GkehubOrganizationsLocationsFleetsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of fleets to return. The service
      may return fewer than this value. If unspecified, at most 200 fleets
      will be returned. The maximum value is 1000; values above 1000 will be
      coerced to 1000.
    pageToken: Optional. A page token, received from a previous `ListFleets`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListFleets` must match the call that
      provided the page token.
    parent: Required. The organization or project to list for Fleets under, in
      the format `organizations/*/locations/*` or `projects/*/locations/*`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class GkehubProjectsLocationsFeaturesCreateRequest(_messages.Message):
  r"""A GkehubProjectsLocationsFeaturesCreateRequest object.

  Fields:
    feature: A Feature resource to be passed as the request body.
    featureId: The ID of the feature to create.
    parent: Required. The parent (project and location) where the Feature will
      be created. Specified in the format `projects/*/locations/*`.
    requestId: A request ID to identify requests. Specify a unique request ID
      so that if you must retry your request, the server will know to ignore
      the request if it has already been completed. The server will guarantee
      that for at least 60 minutes after the first request. For example,
      consider a situation where you make an initial request and the request
      times out. If you make the request again with the same request ID, the
      server can check if original operation with the same request ID was
      received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  feature = _messages.MessageField('Feature', 1)
  featureId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class GkehubProjectsLocationsFeaturesDeleteRequest(_messages.Message):
  r"""A GkehubProjectsLocationsFeaturesDeleteRequest object.

  Fields:
    force: If set to true, the delete will ignore any outstanding resources
      for this Feature (that is, `FeatureState.has_resources` is set to true).
      These resources will NOT be cleaned up or modified in any way.
    name: Required. The Feature resource name in the format
      `projects/*/locations/*/features/*`.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class GkehubProjectsLocationsFeaturesGetIamPolicyRequest(_messages.Message):
  r"""A GkehubProjectsLocationsFeaturesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class GkehubProjectsLocationsFeaturesGetRequest(_messages.Message):
  r"""A GkehubProjectsLocationsFeaturesGetRequest object.

  Fields:
    name: Required. The Feature resource name in the format
      `projects/*/locations/*/features/*`
    returnPartialSuccess: Optional. If set to true, the response will return
      partial results when some regions are unreachable and the unreachable
      field in Feature proto will be populated. If set to false, the request
      will fail when some regions are unreachable.
  """

  name = _messages.StringField(1, required=True)
  returnPartialSuccess = _messages.BooleanField(2)


class GkehubProjectsLocationsFeaturesListRequest(_messages.Message):
  r"""A GkehubProjectsLocationsFeaturesListRequest object.

  Fields:
    filter: Lists Features that match the filter expression, following the
      syntax outlined in https://google.aip.dev/160. Examples: - Feature with
      the name "servicemesh" in project "foo-proj": name = "projects/foo-
      proj/locations/global/features/servicemesh" - Features that have a label
      called `foo`: labels.foo:* - Features that have a label called `foo`
      whose value is `bar`: labels.foo = bar
    orderBy: One or more fields to compare and use to sort the output. See
      https://google.aip.dev/132#ordering.
    pageSize: When requesting a 'page' of resources, `page_size` specifies
      number of resources to return. If unspecified or set to 0, all resources
      will be returned.
    pageToken: Token returned by previous call to `ListFeatures` which
      specifies the position in the list from where to continue listing the
      resources.
    parent: Required. The parent (project and location) where the Features
      will be listed. Specified in the format `projects/*/locations/*`.
    returnPartialSuccess: Optional. If set to true, the response will return
      partial results when some regions are unreachable and the unreachable
      field in Feature proto will be populated. If set to false, the request
      will fail when some regions are unreachable.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)
  returnPartialSuccess = _messages.BooleanField(6)


class GkehubProjectsLocationsFeaturesPatchRequest(_messages.Message):
  r"""A GkehubProjectsLocationsFeaturesPatchRequest object.

  Fields:
    feature: A Feature resource to be passed as the request body.
    name: Required. The Feature resource name in the format
      `projects/*/locations/*/features/*`.
    requestId: A request ID to identify requests. Specify a unique request ID
      so that if you must retry your request, the server will know to ignore
      the request if it has already been completed. The server will guarantee
      that for at least 60 minutes after the first request. For example,
      consider a situation where you make an initial request and the request
      times out. If you make the request again with the same request ID, the
      server can check if original operation with the same request ID was
      received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Mask of fields to update.
  """

  feature = _messages.MessageField('Feature', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class GkehubProjectsLocationsFeaturesSetIamPolicyRequest(_messages.Message):
  r"""A GkehubProjectsLocationsFeaturesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class GkehubProjectsLocationsFeaturesTestIamPermissionsRequest(_messages.Message):
  r"""A GkehubProjectsLocationsFeaturesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class GkehubProjectsLocationsFleetsCreateRequest(_messages.Message):
  r"""A GkehubProjectsLocationsFleetsCreateRequest object.

  Fields:
    fleet: A Fleet resource to be passed as the request body.
    parent: Required. The parent (project and location) where the Fleet will
      be created. Specified in the format `projects/*/locations/*`.
  """

  fleet = _messages.MessageField('Fleet', 1)
  parent = _messages.StringField(2, required=True)


class GkehubProjectsLocationsFleetsDeleteRequest(_messages.Message):
  r"""A GkehubProjectsLocationsFleetsDeleteRequest object.

  Fields:
    name: Required. The Fleet resource name in the format
      `projects/*/locations/*/fleets/*`.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsFleetsGetRequest(_messages.Message):
  r"""A GkehubProjectsLocationsFleetsGetRequest object.

  Fields:
    name: Required. The Fleet resource name in the format
      `projects/*/locations/*/fleets/*`.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsFleetsListRequest(_messages.Message):
  r"""A GkehubProjectsLocationsFleetsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of fleets to return. The service
      may return fewer than this value. If unspecified, at most 200 fleets
      will be returned. The maximum value is 1000; values above 1000 will be
      coerced to 1000.
    pageToken: Optional. A page token, received from a previous `ListFleets`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListFleets` must match the call that
      provided the page token.
    parent: Required. The organization or project to list for Fleets under, in
      the format `organizations/*/locations/*` or `projects/*/locations/*`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class GkehubProjectsLocationsFleetsPatchRequest(_messages.Message):
  r"""A GkehubProjectsLocationsFleetsPatchRequest object.

  Fields:
    fleet: A Fleet resource to be passed as the request body.
    name: Output only. The full, unique resource name of this fleet in the
      format of `projects/{project}/locations/{location}/fleets/{fleet}`. Each
      Google Cloud project can have at most one fleet resource, named
      "default".
    updateMask: Required. The fields to be updated;
  """

  fleet = _messages.MessageField('Fleet', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class GkehubProjectsLocationsGetRequest(_messages.Message):
  r"""A GkehubProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsListRequest(_messages.Message):
  r"""A GkehubProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    includeUnrevealedLocations: If true, the returned list will include
      locations which are not yet revealed.
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  includeUnrevealedLocations = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  pageSize = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(6)


class GkehubProjectsLocationsMembershipsBindingsCreateRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsBindingsCreateRequest object.

  Fields:
    membershipBinding: A MembershipBinding resource to be passed as the
      request body.
    membershipBindingId: Required. The ID to use for the MembershipBinding.
    parent: Required. The parent (project and location) where the
      MembershipBinding will be created. Specified in the format
      `projects/*/locations/*/memberships/*`.
  """

  membershipBinding = _messages.MessageField('MembershipBinding', 1)
  membershipBindingId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class GkehubProjectsLocationsMembershipsBindingsDeleteRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsBindingsDeleteRequest object.

  Fields:
    name: Required. The MembershipBinding resource name in the format
      `projects/*/locations/*/memberships/*/bindings/*`.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsMembershipsBindingsGetRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsBindingsGetRequest object.

  Fields:
    name: Required. The MembershipBinding resource name in the format
      `projects/*/locations/*/memberships/*/bindings/*`.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsMembershipsBindingsListRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsBindingsListRequest object.

  Fields:
    filter: Optional. Lists MembershipBindings that match the filter
      expression, following the syntax outlined in https://google.aip.dev/160.
    pageSize: Optional. When requesting a 'page' of resources, `page_size`
      specifies number of resources to return. If unspecified or set to 0, all
      resources will be returned.
    pageToken: Optional. Token returned by previous call to
      `ListMembershipBindings` which specifies the position in the list from
      where to continue listing the resources.
    parent: Required. The parent Membership for which the MembershipBindings
      will be listed. Specified in the format
      `projects/*/locations/*/memberships/*`.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class GkehubProjectsLocationsMembershipsBindingsPatchRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsBindingsPatchRequest object.

  Fields:
    membershipBinding: A MembershipBinding resource to be passed as the
      request body.
    name: The resource name for the membershipbinding itself `projects/{projec
      t}/locations/{location}/memberships/{membership}/bindings/{membershipbin
      ding}`
    updateMask: Required. The fields to be updated.
  """

  membershipBinding = _messages.MessageField('MembershipBinding', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class GkehubProjectsLocationsMembershipsCreateRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsCreateRequest object.

  Fields:
    membership: A Membership resource to be passed as the request body.
    membershipId: Required. Client chosen ID for the membership.
      `membership_id` must be a valid RFC 1123 compliant DNS label: 1. At most
      63 characters in length 2. It must consist of lower case alphanumeric
      characters or `-` 3. It must start and end with an alphanumeric
      character Which can be expressed as the regex:
      `[a-z0-9]([-a-z0-9]*[a-z0-9])?`, with a maximum length of 63 characters.
    parent: Required. The parent (project and location) where the Memberships
      will be created. Specified in the format `projects/*/locations/*`.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  membership = _messages.MessageField('Membership', 1)
  membershipId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class GkehubProjectsLocationsMembershipsDeleteRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsDeleteRequest object.

  Fields:
    force: Optional. If set to true, any subresource from this Membership will
      also be deleted. Otherwise, the request will only work if the Membership
      has no subresource.
    name: Required. The Membership resource name in the format
      `projects/*/locations/*/memberships/*`.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class GkehubProjectsLocationsMembershipsGenerateConnectManifestRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsGenerateConnectManifestRequest
  object.

  Fields:
    cpuRequest: Optional. To customize the CPU requested for the Connect Agent
      resource.
    imagePullSecretContent: Optional. The image pull secret content for the
      registry, if not public.
    isUpgrade: Optional. If true, generate the resources for upgrade only.
      Some resources generated only for installation (e.g. secrets) will be
      excluded.
    memLimit: Optional. To customize the memory limit for the Connect Agent
      resource.
    memRequest: Optional. To customize the memory requested for the Connect
      Agent resource.
    name: Required. The Membership resource name the Agent will associate
      with, in the format `projects/*/locations/*/memberships/*`.
    namespace: Optional. Namespace for GKE Connect agent resources. Defaults
      to `gke-connect`. The Connect Agent is authorized automatically when run
      in the default namespace. Otherwise, explicit authorization must be
      granted with an additional IAM binding.
    proxy: Optional. URI of a proxy if connectivity from the agent to
      gkeconnect.googleapis.com requires the use of a proxy. Format must be in
      the form `http(s)://{proxy_address}`, depending on the HTTP/HTTPS
      protocol supported by the proxy. This will direct the connect agent's
      outbound traffic through a HTTP(S) proxy.
    registry: Optional. The registry to fetch the connect agent image from.
      Defaults to gcr.io/gkeconnect.
    version: Optional. The Connect agent version to use. Defaults to the most
      current version.
  """

  cpuRequest = _messages.StringField(1)
  imagePullSecretContent = _messages.BytesField(2)
  isUpgrade = _messages.BooleanField(3)
  memLimit = _messages.StringField(4)
  memRequest = _messages.StringField(5)
  name = _messages.StringField(6, required=True)
  namespace = _messages.StringField(7)
  proxy = _messages.BytesField(8)
  registry = _messages.StringField(9)
  version = _messages.StringField(10)


class GkehubProjectsLocationsMembershipsGetIamPolicyRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class GkehubProjectsLocationsMembershipsGetRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsGetRequest object.

  Fields:
    name: Required. The Membership resource name in the format
      `projects/*/locations/*/memberships/*`.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsMembershipsListRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsListRequest object.

  Fields:
    filter: Optional. Lists Memberships that match the filter expression,
      following the syntax outlined in https://google.aip.dev/160. Examples: -
      Name is `bar` in project `foo-proj` and location `global`: name =
      "projects/foo-proj/locations/global/membership/bar" - Memberships that
      have a label called `foo`: labels.foo:* - Memberships that have a label
      called `foo` whose value is `bar`: labels.foo = bar - Memberships in the
      CREATING state: state = CREATING
    orderBy: Optional. One or more fields to compare and use to sort the
      output. See https://google.aip.dev/132#ordering.
    pageSize: Optional. When requesting a 'page' of resources, `page_size`
      specifies number of resources to return. If unspecified or set to 0, all
      resources will be returned.
    pageToken: Optional. Token returned by previous call to `ListMemberships`
      which specifies the position in the list from where to continue listing
      the resources.
    parent: Required. The parent (project and location) where the Memberships
      will be listed. Specified in the format `projects/*/locations/*`.
      `projects/*/locations/-` list memberships in all the regions.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class GkehubProjectsLocationsMembershipsPatchRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsPatchRequest object.

  Fields:
    membership: A Membership resource to be passed as the request body.
    name: Required. The Membership resource name in the format
      `projects/*/locations/*/memberships/*`.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Mask of fields to update.
  """

  membership = _messages.MessageField('Membership', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class GkehubProjectsLocationsMembershipsRbacrolebindingsCreateRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsRbacrolebindingsCreateRequest
  object.

  Fields:
    parent: Required. The parent (project and location) where the
      RBACRoleBinding will be created. Specified in the format
      `projects/*/locations/*/memberships/*`.
    rBACRoleBinding: A RBACRoleBinding resource to be passed as the request
      body.
    rbacrolebindingId: Required. Client chosen ID for the RBACRoleBinding.
      `rbacrolebinding_id` must be a valid RFC 1123 compliant DNS label: 1. At
      most 63 characters in length 2. It must consist of lower case
      alphanumeric characters or `-` 3. It must start and end with an
      alphanumeric character Which can be expressed as the regex:
      `[a-z0-9]([-a-z0-9]*[a-z0-9])?`, with a maximum length of 63 characters.
  """

  parent = _messages.StringField(1, required=True)
  rBACRoleBinding = _messages.MessageField('RBACRoleBinding', 2)
  rbacrolebindingId = _messages.StringField(3)


class GkehubProjectsLocationsMembershipsRbacrolebindingsDeleteRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsRbacrolebindingsDeleteRequest
  object.

  Fields:
    name: Required. The RBACRoleBinding resource name in the format
      `projects/*/locations/*/memberships/*/rbacrolebindings/*`.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsMembershipsRbacrolebindingsGenerateMembershipRBACRoleBindingYAMLRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsRbacrolebindingsGenerateMembershipRB
  ACRoleBindingYAMLRequest object.

  Fields:
    parent: Required. The parent (project and location) where the
      RBACRoleBinding will be created. Specified in the format
      `projects/*/locations/*/memberships/*`.
    rBACRoleBinding: A RBACRoleBinding resource to be passed as the request
      body.
    rbacrolebindingId: Required. Client chosen ID for the RBACRoleBinding.
      `rbacrolebinding_id` must be a valid RFC 1123 compliant DNS label: 1. At
      most 63 characters in length 2. It must consist of lower case
      alphanumeric characters or `-` 3. It must start and end with an
      alphanumeric character Which can be expressed as the regex:
      `[a-z0-9]([-a-z0-9]*[a-z0-9])?`, with a maximum length of 63 characters.
  """

  parent = _messages.StringField(1, required=True)
  rBACRoleBinding = _messages.MessageField('RBACRoleBinding', 2)
  rbacrolebindingId = _messages.StringField(3)


class GkehubProjectsLocationsMembershipsRbacrolebindingsGetRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsRbacrolebindingsGetRequest object.

  Fields:
    name: Required. The RBACRoleBinding resource name in the format
      `projects/*/locations/*/memberships/*/rbacrolebindings/*`.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsMembershipsRbacrolebindingsListRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsRbacrolebindingsListRequest object.

  Fields:
    pageSize: Optional. When requesting a 'page' of resources, `page_size`
      specifies number of resources to return. If unspecified or set to 0, all
      resources will be returned.
    pageToken: Optional. Token returned by previous call to
      `ListMembershipRBACRoleBindings` which specifies the position in the
      list from where to continue listing the resources.
    parent: Required. The parent (project and location) where the Features
      will be listed. Specified in the format
      `projects/*/locations/*/memberships/*`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class GkehubProjectsLocationsMembershipsRbacrolebindingsPatchRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsRbacrolebindingsPatchRequest object.

  Fields:
    name: The resource name for the rbacrolebinding `projects/{project}/locati
      ons/{location}/scopes/{scope}/rbacrolebindings/{rbacrolebinding}` or `pr
      ojects/{project}/locations/{location}/memberships/{membership}/rbacroleb
      indings/{rbacrolebinding}`
    rBACRoleBinding: A RBACRoleBinding resource to be passed as the request
      body.
    updateMask: Required. The fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  rBACRoleBinding = _messages.MessageField('RBACRoleBinding', 2)
  updateMask = _messages.StringField(3)


class GkehubProjectsLocationsMembershipsSetIamPolicyRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class GkehubProjectsLocationsMembershipsTestIamPermissionsRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class GkehubProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A GkehubProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class GkehubProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A GkehubProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A GkehubProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A GkehubProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class GkehubProjectsLocationsScopesCreateRequest(_messages.Message):
  r"""A GkehubProjectsLocationsScopesCreateRequest object.

  Fields:
    parent: Required. The parent (project and location) where the Scope will
      be created. Specified in the format `projects/*/locations/*`.
    scope: A Scope resource to be passed as the request body.
    scopeId: Required. Client chosen ID for the Scope. `scope_id` must be a
      ????
  """

  parent = _messages.StringField(1, required=True)
  scope = _messages.MessageField('Scope', 2)
  scopeId = _messages.StringField(3)


class GkehubProjectsLocationsScopesDeleteRequest(_messages.Message):
  r"""A GkehubProjectsLocationsScopesDeleteRequest object.

  Fields:
    name: Required. The Scope resource name in the format
      `projects/*/locations/*/scopes/*`.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsScopesGetIamPolicyRequest(_messages.Message):
  r"""A GkehubProjectsLocationsScopesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class GkehubProjectsLocationsScopesGetRequest(_messages.Message):
  r"""A GkehubProjectsLocationsScopesGetRequest object.

  Fields:
    name: Required. The Scope resource name in the format
      `projects/*/locations/*/scopes/*`.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsScopesListMembershipsRequest(_messages.Message):
  r"""A GkehubProjectsLocationsScopesListMembershipsRequest object.

  Fields:
    filter: Optional. Lists Memberships that match the filter expression,
      following the syntax outlined in https://google.aip.dev/160. Currently,
      filtering can be done only based on Memberships's `name`, `labels`,
      `create_time`, `update_time`, and `unique_id`.
    pageSize: Optional. When requesting a 'page' of resources, `page_size`
      specifies number of resources to return. If unspecified or set to 0, all
      resources will be returned. Pagination is currently not supported;
      therefore, setting this field does not have any impact for now.
    pageToken: Optional. Token returned by previous call to
      `ListBoundMemberships` which specifies the position in the list from
      where to continue listing the resources.
    scopeName: Required. Name of the Scope, in the format
      `projects/*/locations/global/scopes/*`, to which the Memberships are
      bound.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  scopeName = _messages.StringField(4, required=True)


class GkehubProjectsLocationsScopesListPermittedRequest(_messages.Message):
  r"""A GkehubProjectsLocationsScopesListPermittedRequest object.

  Fields:
    pageSize: Optional. When requesting a 'page' of resources, `page_size`
      specifies number of resources to return. If unspecified or set to 0, all
      resources will be returned.
    pageToken: Optional. Token returned by previous call to
      `ListPermittedScopes` which specifies the position in the list from
      where to continue listing the resources.
    parent: Required. The parent (project and location) where the Scope will
      be listed. Specified in the format `projects/*/locations/*`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class GkehubProjectsLocationsScopesListRequest(_messages.Message):
  r"""A GkehubProjectsLocationsScopesListRequest object.

  Fields:
    pageSize: Optional. When requesting a 'page' of resources, `page_size`
      specifies number of resources to return. If unspecified or set to 0, all
      resources will be returned.
    pageToken: Optional. Token returned by previous call to `ListScopes` which
      specifies the position in the list from where to continue listing the
      resources.
    parent: Required. The parent (project and location) where the Scope will
      be listed. Specified in the format `projects/*/locations/*`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class GkehubProjectsLocationsScopesNamespacesCreateRequest(_messages.Message):
  r"""A GkehubProjectsLocationsScopesNamespacesCreateRequest object.

  Fields:
    namespace: A Namespace resource to be passed as the request body.
    parent: Required. The parent (project and location) where the Namespace
      will be created. Specified in the format
      `projects/*/locations/*/scopes/*`.
    scopeNamespaceId: Required. Client chosen ID for the Namespace.
      `namespace_id` must be a valid RFC 1123 compliant DNS label: 1. At most
      63 characters in length 2. It must consist of lower case alphanumeric
      characters or `-` 3. It must start and end with an alphanumeric
      character Which can be expressed as the regex:
      `[a-z0-9]([-a-z0-9]*[a-z0-9])?`, with a maximum length of 63 characters.
  """

  namespace = _messages.MessageField('Namespace', 1)
  parent = _messages.StringField(2, required=True)
  scopeNamespaceId = _messages.StringField(3)


class GkehubProjectsLocationsScopesNamespacesDeleteRequest(_messages.Message):
  r"""A GkehubProjectsLocationsScopesNamespacesDeleteRequest object.

  Fields:
    name: Required. The Namespace resource name in the format
      `projects/*/locations/*/scopes/*/namespaces/*`.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsScopesNamespacesGetRequest(_messages.Message):
  r"""A GkehubProjectsLocationsScopesNamespacesGetRequest object.

  Fields:
    name: Required. The Namespace resource name in the format
      `projects/*/locations/*/scopes/*/namespaces/*`.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsScopesNamespacesListRequest(_messages.Message):
  r"""A GkehubProjectsLocationsScopesNamespacesListRequest object.

  Fields:
    pageSize: Optional. When requesting a 'page' of resources, `page_size`
      specifies number of resources to return. If unspecified or set to 0, all
      resources will be returned.
    pageToken: Optional. Token returned by previous call to `ListFeatures`
      which specifies the position in the list from where to continue listing
      the resources.
    parent: Required. The parent (project and location) where the Features
      will be listed. Specified in the format
      `projects/*/locations/*/scopes/*`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class GkehubProjectsLocationsScopesNamespacesPatchRequest(_messages.Message):
  r"""A GkehubProjectsLocationsScopesNamespacesPatchRequest object.

  Fields:
    name: The resource name for the namespace
      `projects/{project}/locations/{location}/namespaces/{namespace}`
    namespace: A Namespace resource to be passed as the request body.
    updateMask: Required. The fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  namespace = _messages.MessageField('Namespace', 2)
  updateMask = _messages.StringField(3)


class GkehubProjectsLocationsScopesPatchRequest(_messages.Message):
  r"""A GkehubProjectsLocationsScopesPatchRequest object.

  Fields:
    name: The resource name for the scope
      `projects/{project}/locations/{location}/scopes/{scope}`
    scope: A Scope resource to be passed as the request body.
    updateMask: Required. The fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  scope = _messages.MessageField('Scope', 2)
  updateMask = _messages.StringField(3)


class GkehubProjectsLocationsScopesRbacrolebindingsCreateRequest(_messages.Message):
  r"""A GkehubProjectsLocationsScopesRbacrolebindingsCreateRequest object.

  Fields:
    parent: Required. The parent (project and location) where the
      RBACRoleBinding will be created. Specified in the format
      `projects/*/locations/*/scopes/*`.
    rBACRoleBinding: A RBACRoleBinding resource to be passed as the request
      body.
    rbacrolebindingId: Required. Client chosen ID for the RBACRoleBinding.
      `rbacrolebinding_id` must be a valid RFC 1123 compliant DNS label: 1. At
      most 63 characters in length 2. It must consist of lower case
      alphanumeric characters or `-` 3. It must start and end with an
      alphanumeric character Which can be expressed as the regex:
      `[a-z0-9]([-a-z0-9]*[a-z0-9])?`, with a maximum length of 63 characters.
  """

  parent = _messages.StringField(1, required=True)
  rBACRoleBinding = _messages.MessageField('RBACRoleBinding', 2)
  rbacrolebindingId = _messages.StringField(3)


class GkehubProjectsLocationsScopesRbacrolebindingsDeleteRequest(_messages.Message):
  r"""A GkehubProjectsLocationsScopesRbacrolebindingsDeleteRequest object.

  Fields:
    name: Required. The RBACRoleBinding resource name in the format
      `projects/*/locations/*/scopes/*/rbacrolebindings/*`.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsScopesRbacrolebindingsGetRequest(_messages.Message):
  r"""A GkehubProjectsLocationsScopesRbacrolebindingsGetRequest object.

  Fields:
    name: Required. The RBACRoleBinding resource name in the format
      `projects/*/locations/*/scopes/*/rbacrolebindings/*`.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsScopesRbacrolebindingsListRequest(_messages.Message):
  r"""A GkehubProjectsLocationsScopesRbacrolebindingsListRequest object.

  Fields:
    pageSize: Optional. When requesting a 'page' of resources, `page_size`
      specifies number of resources to return. If unspecified or set to 0, all
      resources will be returned.
    pageToken: Optional. Token returned by previous call to
      `ListScopeRBACRoleBindings` which specifies the position in the list
      from where to continue listing the resources.
    parent: Required. The parent (project and location) where the Features
      will be listed. Specified in the format
      `projects/*/locations/*/scopes/*`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class GkehubProjectsLocationsScopesRbacrolebindingsPatchRequest(_messages.Message):
  r"""A GkehubProjectsLocationsScopesRbacrolebindingsPatchRequest object.

  Fields:
    name: The resource name for the rbacrolebinding `projects/{project}/locati
      ons/{location}/scopes/{scope}/rbacrolebindings/{rbacrolebinding}` or `pr
      ojects/{project}/locations/{location}/memberships/{membership}/rbacroleb
      indings/{rbacrolebinding}`
    rBACRoleBinding: A RBACRoleBinding resource to be passed as the request
      body.
    updateMask: Required. The fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  rBACRoleBinding = _messages.MessageField('RBACRoleBinding', 2)
  updateMask = _messages.StringField(3)


class GkehubProjectsLocationsScopesSetIamPolicyRequest(_messages.Message):
  r"""A GkehubProjectsLocationsScopesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class GkehubProjectsLocationsScopesTestIamPermissionsRequest(_messages.Message):
  r"""A GkehubProjectsLocationsScopesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class GoogleRpcStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class HelloWorldFeatureSample(_messages.Message):
  r"""Represents message used in feature e2e create/mutate testing.

  Enums:
    ThirdValueValuesEnum:

  Messages:
    FifthValue: A FifthValue object.
    NinthValue: Map field.

  Fields:
    eighth: Repeated field.
    fifth: A FifthValue attribute.
    first: Singular scaler field.
    fourth: Singular Message fields.
    ninth: Map field.
    second: Singular scaler field.
    seventh: A string attribute.
    sixth: A string attribute.
    third: A ThirdValueValuesEnum attribute.
  """

  class ThirdValueValuesEnum(_messages.Enum):
    r"""ThirdValueValuesEnum enum type.

    Values:
      BAR_UNSPECIFIED: <no description>
      FIRST: <no description>
      SECOND: <no description>
    """
    BAR_UNSPECIFIED = 0
    FIRST = 1
    SECOND = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FifthValue(_messages.Message):
    r"""A FifthValue object.

    Messages:
      AdditionalProperty: An additional property for a FifthValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FifthValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class NinthValue(_messages.Message):
    r"""Map field.

    Messages:
      AdditionalProperty: An additional property for a NinthValue object.

    Fields:
      additionalProperties: Additional properties of type NinthValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a NinthValue object.

      Fields:
        key: Name of the additional property.
        value: A HelloWorldFooBar attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('HelloWorldFooBar', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  eighth = _messages.MessageField('HelloWorldFooBar', 1, repeated=True)
  fifth = _messages.MessageField('FifthValue', 2)
  first = _messages.StringField(3)
  fourth = _messages.StringField(4)
  ninth = _messages.MessageField('NinthValue', 5)
  second = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  seventh = _messages.StringField(7)
  sixth = _messages.IntegerField(8)
  third = _messages.EnumField('ThirdValueValuesEnum', 9)


class HelloWorldFeatureSpec(_messages.Message):
  r"""**Hello World**: The Hub-wide input for the HelloWorld feature.

  Fields:
    customConfig: Custom config for the HelloWorld controller codelab. This
      should be a textpb string.
    featureSample: Message to hold fields to use in feature e2e create/mutate
      testing.
  """

  customConfig = _messages.StringField(1)
  featureSample = _messages.MessageField('HelloWorldFeatureSample', 2)


class HelloWorldFeatureState(_messages.Message):
  r"""**Hello World**: An empty state left as an example Hub-wide Feature
  state.
  """



class HelloWorldFooBar(_messages.Message):
  r"""Nested Message.

  Fields:
    first: A string attribute.
    second: A integer attribute.
  """

  first = _messages.StringField(1)
  second = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class HelloWorldMembershipSpec(_messages.Message):
  r"""**Hello World**: The membership-specific input for HelloWorld feature.

  Fields:
    customConfig: Custom config for individual memberships. This should be a
      textpb string.
    featureSample: Message to hold fields to use in feature e2e create/mutate
      testing.
  """

  customConfig = _messages.StringField(1)
  featureSample = _messages.MessageField('HelloWorldFeatureSample', 2)


class HelloWorldMembershipState(_messages.Message):
  r"""**Hello World**: An empty state left as an example membership-specific
  Feature state.
  """



class HelloWorldScopeSpec(_messages.Message):
  r"""**Hello World**: The scope-specific input for HelloWorld feature.

  Fields:
    customConfig: Custom config for individual memberships. This should be a
      textpb string.
    featureSample: Message to hold fields to use in feature e2e create/mutate
      testing.
  """

  customConfig = _messages.StringField(1)
  featureSample = _messages.MessageField('HelloWorldFeatureSample', 2)


class HelloWorldScopeState(_messages.Message):
  r"""**Hello World**: An empty state left as an example scope-specific
  Feature state.
  """



class IdentityServiceAuthMethod(_messages.Message):
  r"""Configuration of an auth method for a member/cluster. Only one
  authentication method (e.g., OIDC and LDAP) can be set per AuthMethod.

  Fields:
    azureadConfig: AzureAD specific Configuration.
    googleConfig: GoogleConfig specific configuration.
    ldapConfig: LDAP specific configuration.
    name: Identifier for auth config.
    oidcConfig: OIDC specific configuration.
    proxy: Proxy server address to use for auth method.
    samlConfig: SAML specific configuration.
  """

  azureadConfig = _messages.MessageField('IdentityServiceAzureADConfig', 1)
  googleConfig = _messages.MessageField('IdentityServiceGoogleConfig', 2)
  ldapConfig = _messages.MessageField('IdentityServiceLdapConfig', 3)
  name = _messages.StringField(4)
  oidcConfig = _messages.MessageField('IdentityServiceOidcConfig', 5)
  proxy = _messages.StringField(6)
  samlConfig = _messages.MessageField('IdentityServiceSamlConfig', 7)


class IdentityServiceAzureADConfig(_messages.Message):
  r"""Configuration for the AzureAD Auth flow.

  Fields:
    clientId: ID for the registered client application that makes
      authentication requests to the Azure AD identity provider.
    clientSecret: Input only. Unencrypted AzureAD client secret will be passed
      to the GKE Hub CLH.
    encryptedClientSecret: Output only. Encrypted AzureAD client secret.
    groupFormat: Optional. Format of the AzureAD groups that the client wants
      for auth.
    kubectlRedirectUri: The redirect URL that kubectl uses for authorization.
    tenant: Kind of Azure AD account to be authenticated. Supported values are
      or for accounts belonging to a specific tenant.
    userClaim: Optional. Claim in the AzureAD ID Token that holds the user
      details.
  """

  clientId = _messages.StringField(1)
  clientSecret = _messages.StringField(2)
  encryptedClientSecret = _messages.BytesField(3)
  groupFormat = _messages.StringField(4)
  kubectlRedirectUri = _messages.StringField(5)
  tenant = _messages.StringField(6)
  userClaim = _messages.StringField(7)


class IdentityServiceDiagnosticInterface(_messages.Message):
  r"""Configuration options for the AIS diagnostic interface.

  Fields:
    enabled: Determines whether to enable the diagnostic interface.
    expirationTime: Determines the expiration time of the diagnostic interface
      enablement. When reached, requests to the interface would be
      automatically rejected.
  """

  enabled = _messages.BooleanField(1)
  expirationTime = _messages.StringField(2)


class IdentityServiceGoogleConfig(_messages.Message):
  r"""Configuration for the Google Plugin Auth flow.

  Fields:
    disable: Disable automatic configuration of Google Plugin on supported
      platforms.
  """

  disable = _messages.BooleanField(1)


class IdentityServiceGroupConfig(_messages.Message):
  r"""Contains the properties for locating and authenticating groups in the
  directory.

  Fields:
    baseDn: Required. The location of the subtree in the LDAP directory to
      search for group entries.
    filter: Optional. Optional filter to be used when searching for groups a
      user belongs to. This can be used to explicitly match only certain
      groups in order to reduce the amount of groups returned for each user.
      This defaults to "(objectClass=Group)".
    idAttribute: Optional. The identifying name of each group a user belongs
      to. For example, if this is set to "distinguishedName" then RBACs and
      other group expectations should be written as full DNs. This defaults to
      "distinguishedName".
  """

  baseDn = _messages.StringField(1)
  filter = _messages.StringField(2)
  idAttribute = _messages.StringField(3)


class IdentityServiceIdentityServiceOptions(_messages.Message):
  r"""Holds non-protocol-related configuration options.

  Fields:
    diagnosticInterface: Configuration options for the AIS diagnostic
      interface.
    sessionDuration: Determines the lifespan of STS tokens issued by Anthos
      Identity Service.
  """

  diagnosticInterface = _messages.MessageField('IdentityServiceDiagnosticInterface', 1)
  sessionDuration = _messages.StringField(2)


class IdentityServiceLdapConfig(_messages.Message):
  r"""Configuration for the LDAP Auth flow.

  Fields:
    group: Optional. Contains the properties for locating and authenticating
      groups in the directory.
    server: Required. Server settings for the external LDAP server.
    serviceAccount: Required. Contains the credentials of the service account
      which is authorized to perform the LDAP search in the directory. The
      credentials can be supplied by the combination of the DN and password or
      the client certificate.
    user: Required. Defines where users exist in the LDAP directory.
  """

  group = _messages.MessageField('IdentityServiceGroupConfig', 1)
  server = _messages.MessageField('IdentityServiceServerConfig', 2)
  serviceAccount = _messages.MessageField('IdentityServiceServiceAccountConfig', 3)
  user = _messages.MessageField('IdentityServiceUserConfig', 4)


class IdentityServiceMembershipSpec(_messages.Message):
  r"""**Anthos Identity Service**: Configuration for a single Membership.

  Fields:
    authMethods: A member may support multiple auth methods.
    identityServiceOptions: Optional. non-protocol-related configuration
      options.
  """

  authMethods = _messages.MessageField('IdentityServiceAuthMethod', 1, repeated=True)
  identityServiceOptions = _messages.MessageField('IdentityServiceIdentityServiceOptions', 2)


class IdentityServiceMembershipState(_messages.Message):
  r"""**Anthos Identity Service**: State for a single Membership.

  Enums:
    StateValueValuesEnum: Deployment state on this member

  Fields:
    failureReason: The reason of the failure.
    installedVersion: Installed AIS version. This is the AIS version installed
      on this member. The values makes sense iff state is OK.
    memberConfig: Last reconciled membership configuration
    state: Deployment state on this member
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Deployment state on this member

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Unspecified state
      OK: deployment succeeds
      ERROR: Failure with error.
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    OK = 1
    ERROR = 2

  failureReason = _messages.StringField(1)
  installedVersion = _messages.StringField(2)
  memberConfig = _messages.MessageField('IdentityServiceMembershipSpec', 3)
  state = _messages.EnumField('StateValueValuesEnum', 4)


class IdentityServiceOidcConfig(_messages.Message):
  r"""Configuration for OIDC Auth flow.

  Fields:
    certificateAuthorityData: PEM-encoded CA for OIDC provider.
    clientId: ID for OIDC client application.
    clientSecret: Input only. Unencrypted OIDC client secret will be passed to
      the GKE Hub CLH.
    deployCloudConsoleProxy: Flag to denote if reverse proxy is used to
      connect to auth provider. This flag should be set to true when provider
      is not reachable by Google Cloud Console.
    enableAccessToken: Enable access token.
    encryptedClientSecret: Output only. Encrypted OIDC Client secret
    extraParams: Comma-separated list of key-value pairs.
    groupPrefix: Prefix to prepend to group name.
    groupsClaim: Claim in OIDC ID token that holds group information.
    issuerUri: URI for the OIDC provider. This should point to the level below
      .well-known/openid-configuration.
    kubectlRedirectUri: Registered redirect uri to redirect users going
      through OAuth flow using kubectl plugin.
    scopes: Comma-separated list of identifiers.
    userClaim: Claim in OIDC ID token that holds username.
    userPrefix: Prefix to prepend to user name.
  """

  certificateAuthorityData = _messages.StringField(1)
  clientId = _messages.StringField(2)
  clientSecret = _messages.StringField(3)
  deployCloudConsoleProxy = _messages.BooleanField(4)
  enableAccessToken = _messages.BooleanField(5)
  encryptedClientSecret = _messages.BytesField(6)
  extraParams = _messages.StringField(7)
  groupPrefix = _messages.StringField(8)
  groupsClaim = _messages.StringField(9)
  issuerUri = _messages.StringField(10)
  kubectlRedirectUri = _messages.StringField(11)
  scopes = _messages.StringField(12)
  userClaim = _messages.StringField(13)
  userPrefix = _messages.StringField(14)


class IdentityServiceSamlConfig(_messages.Message):
  r"""Configuration for the SAML Auth flow.

  Messages:
    AttributeMappingValue: Optional. The mapping of additional user attributes
      like nickname, birthday and address etc.. `key` is the name of this
      additional attribute. `value` is a string presenting as CEL(common
      expression language, go/cel) used for getting the value from the
      resources. Take nickname as an example, in this case, `key` is
      "attribute.nickname" and `value` is "assertion.nickname".

  Fields:
    attributeMapping: Optional. The mapping of additional user attributes like
      nickname, birthday and address etc.. `key` is the name of this
      additional attribute. `value` is a string presenting as CEL(common
      expression language, go/cel) used for getting the value from the
      resources. Take nickname as an example, in this case, `key` is
      "attribute.nickname" and `value` is "assertion.nickname".
    groupPrefix: Optional. Prefix to prepend to group name.
    groupsAttribute: Optional. The SAML attribute to read groups from. This
      value is expected to be a string and will be passed along as-is (with
      the option of being prefixed by the `group_prefix`).
    identityProviderCertificates: Required. The list of IdP certificates to
      validate the SAML response against.
    identityProviderId: Required. The entity ID of the SAML IdP.
    identityProviderSsoUri: Required. The URI where the SAML IdP exposes the
      SSO service.
    userAttribute: Optional. The SAML attribute to read username from. If
      unspecified, the username will be read from the NameID element of the
      assertion in SAML response. This value is expected to be a string and
      will be passed along as-is (with the option of being prefixed by the
      `user_prefix`).
    userPrefix: Optional. Prefix to prepend to user name.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AttributeMappingValue(_messages.Message):
    r"""Optional. The mapping of additional user attributes like nickname,
    birthday and address etc.. `key` is the name of this additional attribute.
    `value` is a string presenting as CEL(common expression language, go/cel)
    used for getting the value from the resources. Take nickname as an
    example, in this case, `key` is "attribute.nickname" and `value` is
    "assertion.nickname".

    Messages:
      AdditionalProperty: An additional property for a AttributeMappingValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        AttributeMappingValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AttributeMappingValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attributeMapping = _messages.MessageField('AttributeMappingValue', 1)
  groupPrefix = _messages.StringField(2)
  groupsAttribute = _messages.StringField(3)
  identityProviderCertificates = _messages.StringField(4, repeated=True)
  identityProviderId = _messages.StringField(5)
  identityProviderSsoUri = _messages.StringField(6)
  userAttribute = _messages.StringField(7)
  userPrefix = _messages.StringField(8)


class IdentityServiceServerConfig(_messages.Message):
  r"""Server settings for the external LDAP server.

  Fields:
    certificateAuthorityData: Optional. Contains a Base64 encoded, PEM
      formatted certificate authority certificate for the LDAP server. This
      must be provided for the "ldaps" and "startTLS" connections.
    connectionType: Optional. Defines the connection type to communicate with
      the LDAP server. If `starttls` or `ldaps` is specified, the
      certificate_authority_data should not be empty.
    host: Required. Defines the hostname or IP of the LDAP server. Port is
      optional and will default to 389, if unspecified. For example,
      "ldap.server.example" or "***********:389".
  """

  certificateAuthorityData = _messages.BytesField(1)
  connectionType = _messages.StringField(2)
  host = _messages.StringField(3)


class IdentityServiceServiceAccountConfig(_messages.Message):
  r"""Contains the credentials of the service account which is authorized to
  perform the LDAP search in the directory. The credentials can be supplied by
  the combination of the DN and password or the client certificate.

  Fields:
    simpleBindCredentials: Credentials for basic auth.
  """

  simpleBindCredentials = _messages.MessageField('IdentityServiceSimpleBindCredentials', 1)


class IdentityServiceSimpleBindCredentials(_messages.Message):
  r"""The structure holds the LDAP simple binding credential.

  Fields:
    dn: Required. The distinguished name(DN) of the service account
      object/user.
    encryptedPassword: Output only. The encrypted password of the service
      account object/user.
    password: Required. Input only. The password of the service account
      object/user.
  """

  dn = _messages.StringField(1)
  encryptedPassword = _messages.BytesField(2)
  password = _messages.StringField(3)


class IdentityServiceUserConfig(_messages.Message):
  r"""Defines where users exist in the LDAP directory.

  Fields:
    baseDn: Required. The location of the subtree in the LDAP directory to
      search for user entries.
    filter: Optional. Filter to apply when searching for the user. This can be
      used to further restrict the user accounts which are allowed to login.
      This defaults to "(objectClass=User)".
    idAttribute: Optional. Determines which attribute to use as the user's
      identity after they are authenticated. This is distinct from the
      loginAttribute field to allow users to login with a username, but then
      have their actual identifier be an email address or full Distinguished
      Name (DN). For example, setting loginAttribute to "sAMAccountName" and
      identifierAttribute to "userPrincipalName" would allow a user to login
      as "bsmith", but actual RBAC policies for the user would be written as
      "<EMAIL>". Using "userPrincipalName" is recommended since
      this will be unique for each user. This defaults to "userPrincipalName".
    loginAttribute: Optional. The name of the attribute which matches against
      the input username. This is used to find the user in the LDAP database
      e.g. "(=)" and is combined with the optional filter field. This defaults
      to "userPrincipalName".
  """

  baseDn = _messages.StringField(1)
  filter = _messages.StringField(2)
  idAttribute = _messages.StringField(3)
  loginAttribute = _messages.StringField(4)


class KubernetesMetadata(_messages.Message):
  r"""KubernetesMetadata provides informational metadata for Memberships
  representing Kubernetes clusters.

  Fields:
    kubernetesApiServerVersion: Output only. Kubernetes API server version
      string as reported by `/version`.
    memoryMb: Output only. The total memory capacity as reported by the sum of
      all Kubernetes nodes resources, defined in MB.
    nodeCount: Output only. Node count as reported by Kubernetes nodes
      resources.
    nodeProviderId: Output only. Node providerID as reported by the first node
      in the list of nodes on the Kubernetes endpoint. On Kubernetes platforms
      that support zero-node clusters (like GKE-on-GCP), the node_count will
      be zero and the node_provider_id will be empty.
    updateTime: Output only. The time at which these details were last
      updated. This update_time is different from the Membership-level
      update_time since EndpointDetails are updated internally for API
      consumers.
    vcpuCount: Output only. vCPU count as reported by Kubernetes nodes
      resources.
  """

  kubernetesApiServerVersion = _messages.StringField(1)
  memoryMb = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  nodeCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  nodeProviderId = _messages.StringField(4)
  updateTime = _messages.StringField(5)
  vcpuCount = _messages.IntegerField(6, variant=_messages.Variant.INT32)


class KubernetesResource(_messages.Message):
  r"""KubernetesResource contains the YAML manifests and configuration for
  Membership Kubernetes resources in the cluster. After CreateMembership or
  UpdateMembership, these resources should be re-applied in the cluster.

  Fields:
    connectResources: Output only. The Kubernetes resources for installing the
      GKE Connect agent This field is only populated in the Membership
      returned from a successful long-running operation from CreateMembership
      or UpdateMembership. It is not populated during normal GetMembership or
      ListMemberships requests. To get the resource manifest after the initial
      registration, the caller should make a UpdateMembership call with an
      empty field mask.
    membershipCrManifest: Input only. The YAML representation of the
      Membership CR. This field is ignored for GKE clusters where Hub can read
      the CR directly. Callers should provide the CR that is currently present
      in the cluster during CreateMembership or UpdateMembership, or leave
      this field empty if none exists. The CR manifest is used to validate the
      cluster has not been registered with another Membership.
    membershipResources: Output only. Additional Kubernetes resources that
      need to be applied to the cluster after Membership creation, and after
      every update. This field is only populated in the Membership returned
      from a successful long-running operation from CreateMembership or
      UpdateMembership. It is not populated during normal GetMembership or
      ListMemberships requests. To get the resource manifest after the initial
      registration, the caller should make a UpdateMembership call with an
      empty field mask.
    resourceOptions: Optional. Options for Kubernetes resource generation.
  """

  connectResources = _messages.MessageField('ResourceManifest', 1, repeated=True)
  membershipCrManifest = _messages.StringField(2)
  membershipResources = _messages.MessageField('ResourceManifest', 3, repeated=True)
  resourceOptions = _messages.MessageField('ResourceOptions', 4)


class ListBoundMembershipsResponse(_messages.Message):
  r"""List of Memberships bound to a Scope.

  Fields:
    memberships: The list of Memberships bound to the given Scope.
    nextPageToken: A token to request the next page of resources from the
      `ListBoundMemberships` method. The value of an empty string means that
      there are no more resources to return.
    unreachable: List of locations that could not be reached while fetching
      this list.
  """

  memberships = _messages.MessageField('Membership', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListFeaturesResponse(_messages.Message):
  r"""Response message for the `GkeHub.ListFeatures` method.

  Fields:
    nextPageToken: A token to request the next page of resources from the
      `ListFeatures` method. The value of an empty string means that there are
      no more resources to return.
    resources: The list of matching Features
  """

  nextPageToken = _messages.StringField(1)
  resources = _messages.MessageField('Feature', 2, repeated=True)


class ListFleetsResponse(_messages.Message):
  r"""Response message for the `GkeHub.ListFleetsResponse` method.

  Fields:
    fleets: The list of matching fleets.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages. The
      token is only valid for 1h.
  """

  fleets = _messages.MessageField('Fleet', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListMembershipBindingsResponse(_messages.Message):
  r"""List of MembershipBindings.

  Fields:
    membershipBindings: The list of membership_bindings
    nextPageToken: A token to request the next page of resources from the
      `ListMembershipBindings` method. The value of an empty string means that
      there are no more resources to return.
    unreachable: List of locations that could not be reached while fetching
      this list.
  """

  membershipBindings = _messages.MessageField('MembershipBinding', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListMembershipRBACRoleBindingsResponse(_messages.Message):
  r"""List of Membership RBACRoleBindings.

  Fields:
    nextPageToken: A token to request the next page of resources from the
      `ListMembershipRBACRoleBindings` method. The value of an empty string
      means that there are no more resources to return.
    rbacrolebindings: The list of Membership RBACRoleBindings.
    unreachable: List of locations that could not be reached while fetching
      this list.
  """

  nextPageToken = _messages.StringField(1)
  rbacrolebindings = _messages.MessageField('RBACRoleBinding', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListMembershipsResponse(_messages.Message):
  r"""Response message for the `GkeHub.ListMemberships` method.

  Fields:
    nextPageToken: A token to request the next page of resources from the
      `ListMemberships` method. The value of an empty string means that there
      are no more resources to return.
    resources: The list of matching Memberships.
    unreachable: List of locations that could not be reached while fetching
      this list.
  """

  nextPageToken = _messages.StringField(1)
  resources = _messages.MessageField('Membership', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListPermittedScopesResponse(_messages.Message):
  r"""List of permitted Scopes.

  Fields:
    nextPageToken: A token to request the next page of resources from the
      `ListPermittedScopes` method. The value of an empty string means that
      there are no more resources to return.
    scopes: The list of permitted Scopes
  """

  nextPageToken = _messages.StringField(1)
  scopes = _messages.MessageField('Scope', 2, repeated=True)


class ListReferencesRequest(_messages.Message):
  r"""The ListResourceMetadataRequest request.

  Fields:
    pageSize: The maximum number of items to return. If unspecified, server
      will pick an appropriate default. Server may return fewer items than
      requested. A caller should only rely on response's next_page_token to
      determine if there are more References left to be queried.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: Required. The parent resource name (target_resource of this
      reference). For example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3)


class ListReferencesResponse(_messages.Message):
  r"""The ListReferencesResponse response.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    references: The list of references.
  """

  nextPageToken = _messages.StringField(1)
  references = _messages.MessageField('Reference', 2, repeated=True)


class ListScopeNamespacesResponse(_messages.Message):
  r"""List of fleet namespaces.

  Fields:
    nextPageToken: A token to request the next page of resources from the
      `ListNamespaces` method. The value of an empty string means that there
      are no more resources to return.
    scopeNamespaces: The list of fleet namespaces
  """

  nextPageToken = _messages.StringField(1)
  scopeNamespaces = _messages.MessageField('Namespace', 2, repeated=True)


class ListScopeRBACRoleBindingsResponse(_messages.Message):
  r"""List of Scope RBACRoleBindings.

  Fields:
    nextPageToken: A token to request the next page of resources from the
      `ListScopeRBACRoleBindings` method. The value of an empty string means
      that there are no more resources to return.
    rbacrolebindings: The list of Scope RBACRoleBindings.
  """

  nextPageToken = _messages.StringField(1)
  rbacrolebindings = _messages.MessageField('RBACRoleBinding', 2, repeated=True)


class ListScopesResponse(_messages.Message):
  r"""List of Scopes.

  Fields:
    nextPageToken: A token to request the next page of resources from the
      `ListScopes` method. The value of an empty string means that there are
      no more resources to return.
    scopes: The list of Scopes
  """

  nextPageToken = _messages.StringField(1)
  scopes = _messages.MessageField('Scope', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class LogConfig(_messages.Message):
  r"""Specifies what kind of log the caller must write

  Fields:
    cloudAudit: Cloud audit options.
    counter: Counter options.
    dataAccess: Data access options.
  """

  cloudAudit = _messages.MessageField('CloudAuditOptions', 1)
  counter = _messages.MessageField('CounterOptions', 2)
  dataAccess = _messages.MessageField('DataAccessOptions', 3)


class Membership(_messages.Message):
  r"""Membership contains information about a member cluster.

  Enums:
    ClusterTierValueValuesEnum: Output only. The tier of the cluster.
    InfrastructureTypeValueValuesEnum: Optional. The infrastructure type this
      Membership is running on.
    MembershipTypeValueValuesEnum: Output only. The type of the membership.

  Messages:
    LabelsValue: Optional. Labels for this membership. These labels are not
      leveraged by multi-cluster features, instead, we prefer cluster labels,
      which can be set on GKE cluster or other cluster types.
    PlatformLabelsValue: Output only. The labels of the cluster, coming from
      the platform api For example, a GKE cluster object labels are replicated
      here. This field is used by multi-cluster features as the source of
      labels and they ignore the membership labels (the `labels` field)

  Fields:
    authority: Optional. How to identify workloads from this Membership. See
      the documentation on Workload Identity for more details:
      https://cloud.google.com/kubernetes-engine/docs/how-to/workload-identity
    clusterTier: Output only. The tier of the cluster.
    createTime: Output only. When the Membership was created.
    deleteTime: Output only. When the Membership was deleted.
    description: Output only. Description of this membership, limited to 63
      characters. Must match the regex: `a-zA-Z0-9*` This field is present for
      legacy purposes.
    endpoint: Optional. Endpoint information to reach this member.
    externalId: Optional. An externally-generated and managed ID for this
      Membership. This ID may be modified after creation, but this is not
      recommended. The ID must match the regex: `a-zA-Z0-9*` If this
      Membership represents a Kubernetes cluster, this value should be set to
      the UID of the `kube-system` namespace object.
    infrastructureType: Optional. The infrastructure type this Membership is
      running on.
    labels: Optional. Labels for this membership. These labels are not
      leveraged by multi-cluster features, instead, we prefer cluster labels,
      which can be set on GKE cluster or other cluster types.
    lastConnectionTime: Output only. For clusters using Connect, the timestamp
      of the most recent connection established with Google Cloud. This time
      is updated every several minutes, not continuously. For clusters that do
      not use GKE Connect, or that have never connected successfully, this
      field will be unset.
    membershipType: Output only. The type of the membership.
    monitoringConfig: Optional. The monitoring config information for this
      membership.
    name: Output only. The full, unique name of this Membership resource in
      the format `projects/*/locations/*/memberships/{membership_id}`, set
      during creation. `membership_id` must be a valid RFC 1123 compliant DNS
      label: 1. At most 63 characters in length 2. It must consist of lower
      case alphanumeric characters or `-` 3. It must start and end with an
      alphanumeric character Which can be expressed as the regex:
      `[a-z0-9]([-a-z0-9]*[a-z0-9])?`, with a maximum length of 63 characters.
    platformLabels: Output only. The labels of the cluster, coming from the
      platform api For example, a GKE cluster object labels are replicated
      here. This field is used by multi-cluster features as the source of
      labels and they ignore the membership labels (the `labels` field)
    state: Output only. State of the Membership resource.
    uniqueId: Output only. Google-generated UUID for this resource. This is
      unique across all Membership resources. If a Membership resource is
      deleted and another resource with the same name is created, it gets a
      different unique_id.
    updateTime: Output only. When the Membership was last updated.
  """

  class ClusterTierValueValuesEnum(_messages.Enum):
    r"""Output only. The tier of the cluster.

    Values:
      CLUSTER_TIER_UNSPECIFIED: The ClusterTier is not set.
      STANDARD: The ClusterTier is standard.
      ENTERPRISE: The ClusterTier is enterprise.
    """
    CLUSTER_TIER_UNSPECIFIED = 0
    STANDARD = 1
    ENTERPRISE = 2

  class InfrastructureTypeValueValuesEnum(_messages.Enum):
    r"""Optional. The infrastructure type this Membership is running on.

    Values:
      INFRASTRUCTURE_TYPE_UNSPECIFIED: No type was specified. Some Hub
        functionality may require a type be specified, and will not support
        Memberships with this value.
      ON_PREM: Private infrastructure that is owned or operated by customer.
        This includes GKE distributions such as GKE-OnPrem and GKE-
        OnBareMetal.
      MULTI_CLOUD: Public cloud infrastructure.
    """
    INFRASTRUCTURE_TYPE_UNSPECIFIED = 0
    ON_PREM = 1
    MULTI_CLOUD = 2

  class MembershipTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the membership.

    Values:
      MEMBERSHIP_TYPE_UNSPECIFIED: The MembershipType is not set.
      LIGHTWEIGHT: The membership supports only lightweight compatible
        features.
    """
    MEMBERSHIP_TYPE_UNSPECIFIED = 0
    LIGHTWEIGHT = 1

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels for this membership. These labels are not leveraged
    by multi-cluster features, instead, we prefer cluster labels, which can be
    set on GKE cluster or other cluster types.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PlatformLabelsValue(_messages.Message):
    r"""Output only. The labels of the cluster, coming from the platform api
    For example, a GKE cluster object labels are replicated here. This field
    is used by multi-cluster features as the source of labels and they ignore
    the membership labels (the `labels` field)

    Messages:
      AdditionalProperty: An additional property for a PlatformLabelsValue
        object.

    Fields:
      additionalProperties: Additional properties of type PlatformLabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PlatformLabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  authority = _messages.MessageField('Authority', 1)
  clusterTier = _messages.EnumField('ClusterTierValueValuesEnum', 2)
  createTime = _messages.StringField(3)
  deleteTime = _messages.StringField(4)
  description = _messages.StringField(5)
  endpoint = _messages.MessageField('MembershipEndpoint', 6)
  externalId = _messages.StringField(7)
  infrastructureType = _messages.EnumField('InfrastructureTypeValueValuesEnum', 8)
  labels = _messages.MessageField('LabelsValue', 9)
  lastConnectionTime = _messages.StringField(10)
  membershipType = _messages.EnumField('MembershipTypeValueValuesEnum', 11)
  monitoringConfig = _messages.MessageField('MonitoringConfig', 12)
  name = _messages.StringField(13)
  platformLabels = _messages.MessageField('PlatformLabelsValue', 14)
  state = _messages.MessageField('MembershipState', 15)
  uniqueId = _messages.StringField(16)
  updateTime = _messages.StringField(17)


class MembershipBinding(_messages.Message):
  r"""MembershipBinding is a subresource of a Membership, representing what
  Fleet Scopes (or other, future Fleet resources) a Membership is bound to.

  Messages:
    LabelsValue: Optional. Labels for this MembershipBinding.

  Fields:
    createTime: Output only. When the membership binding was created.
    deleteTime: Output only. When the membership binding was deleted.
    labels: Optional. Labels for this MembershipBinding.
    name: The resource name for the membershipbinding itself `projects/{projec
      t}/locations/{location}/memberships/{membership}/bindings/{membershipbin
      ding}`
    scope: A Scope resource name in the format
      `projects/*/locations/*/scopes/*`.
    state: Output only. State of the membership binding resource.
    uid: Output only. Google-generated UUID for this resource. This is unique
      across all membershipbinding resources. If a membershipbinding resource
      is deleted and another resource with the same name is created, it gets a
      different uid.
    updateTime: Output only. When the membership binding was last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels for this MembershipBinding.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  deleteTime = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  scope = _messages.StringField(5)
  state = _messages.MessageField('MembershipBindingLifecycleState', 6)
  uid = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class MembershipBindingLifecycleState(_messages.Message):
  r"""MembershipBindingLifecycleState describes the state of a Binding
  resource.

  Enums:
    CodeValueValuesEnum: Output only. The current state of the
      MembershipBinding resource.

  Fields:
    code: Output only. The current state of the MembershipBinding resource.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the MembershipBinding resource.

    Values:
      CODE_UNSPECIFIED: The code is not set.
      CREATING: The membershipbinding is being created.
      READY: The membershipbinding active.
      DELETING: The membershipbinding is being deleted.
      UPDATING: The membershipbinding is being updated.
    """
    CODE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    DELETING = 3
    UPDATING = 4

  code = _messages.EnumField('CodeValueValuesEnum', 1)


class MembershipEndpoint(_messages.Message):
  r"""MembershipEndpoint contains information needed to contact a Kubernetes
  API, endpoint and any additional Kubernetes metadata.

  Fields:
    applianceCluster: Optional. Specific information for a GDC Edge Appliance
      cluster.
    edgeCluster: Optional. Specific information for a Google Edge cluster.
    gkeCluster: Optional. Specific information for a GKE-on-GCP cluster.
    googleManaged: Output only. Whether the lifecycle of this membership is
      managed by a google cluster platform service.
    kubernetesMetadata: Output only. Useful Kubernetes-specific metadata.
    kubernetesResource: Optional. The in-cluster Kubernetes Resources that
      should be applied for a correctly registered cluster, in the steady
      state. These resources: * Ensure that the cluster is exclusively
      registered to one and only one Hub Membership. * Propagate Workload Pool
      Information available in the Membership Authority field. * Ensure proper
      initial configuration of default Hub Features.
    multiCloudCluster: Optional. Specific information for a GKE Multi-Cloud
      cluster.
    onPremCluster: Optional. Specific information for a GKE On-Prem cluster.
      An onprem user-cluster who has no resourceLink is not allowed to use
      this field, it should have a nil "type" instead.
  """

  applianceCluster = _messages.MessageField('ApplianceCluster', 1)
  edgeCluster = _messages.MessageField('EdgeCluster', 2)
  gkeCluster = _messages.MessageField('GkeCluster', 3)
  googleManaged = _messages.BooleanField(4)
  kubernetesMetadata = _messages.MessageField('KubernetesMetadata', 5)
  kubernetesResource = _messages.MessageField('KubernetesResource', 6)
  multiCloudCluster = _messages.MessageField('MultiCloudCluster', 7)
  onPremCluster = _messages.MessageField('OnPremCluster', 8)


class MembershipFeatureSpec(_messages.Message):
  r"""MembershipFeatureSpec contains configuration information for a single
  Membership.

  Fields:
    configmanagement: Config Management-specific spec.
    fleetobservability: Fleet observability membership spec
    helloworld: Hello World-specific spec.
    identityservice: Identity Service-specific spec.
    mesh: Anthos Service Mesh-specific spec
    origin: Whether this per-Membership spec was inherited from a fleet-level
      default. This field can be updated by users by either overriding a
      Membership config (updated to USER implicitly) or setting to FLEET
      explicitly.
    policycontroller: Policy Controller spec.
  """

  configmanagement = _messages.MessageField('ConfigManagementMembershipSpec', 1)
  fleetobservability = _messages.MessageField('FleetObservabilityMembershipSpec', 2)
  helloworld = _messages.MessageField('HelloWorldMembershipSpec', 3)
  identityservice = _messages.MessageField('IdentityServiceMembershipSpec', 4)
  mesh = _messages.MessageField('ServiceMeshMembershipSpec', 5)
  origin = _messages.MessageField('Origin', 6)
  policycontroller = _messages.MessageField('PolicyControllerMembershipSpec', 7)


class MembershipFeatureState(_messages.Message):
  r"""MembershipFeatureState contains Feature status information for a single
  Membership.

  Fields:
    appdevexperience: Appdevexperience specific state.
    clusterupgrade: ClusterUpgrade state.
    configmanagement: Config Management-specific state.
    fleetobservability: Fleet observability membership state.
    helloworld: Hello World-specific state.
    identityservice: Identity Service-specific state.
    policycontroller: Policycontroller-specific state.
    servicemesh: Service Mesh-specific state.
    state: The high-level state of this Feature for a single membership.
  """

  appdevexperience = _messages.MessageField('AppDevExperienceFeatureState', 1)
  clusterupgrade = _messages.MessageField('ClusterUpgradeMembershipState', 2)
  configmanagement = _messages.MessageField('ConfigManagementMembershipState', 3)
  fleetobservability = _messages.MessageField('FleetObservabilityMembershipState', 4)
  helloworld = _messages.MessageField('HelloWorldMembershipState', 5)
  identityservice = _messages.MessageField('IdentityServiceMembershipState', 6)
  policycontroller = _messages.MessageField('PolicyControllerMembershipState', 7)
  servicemesh = _messages.MessageField('ServiceMeshMembershipState', 8)
  state = _messages.MessageField('FeatureState', 9)


class MembershipState(_messages.Message):
  r"""MembershipState describes the state of a Membership resource.

  Enums:
    CodeValueValuesEnum: Output only. The current state of the Membership
      resource.

  Fields:
    code: Output only. The current state of the Membership resource.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the Membership resource.

    Values:
      CODE_UNSPECIFIED: The code is not set.
      CREATING: The cluster is being registered.
      READY: The cluster is registered.
      DELETING: The cluster is being unregistered.
      UPDATING: The Membership is being updated.
      SERVICE_UPDATING: The Membership is being updated by the Hub Service.
    """
    CODE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    DELETING = 3
    UPDATING = 4
    SERVICE_UPDATING = 5

  code = _messages.EnumField('CodeValueValuesEnum', 1)


class MonitoringConfig(_messages.Message):
  r"""MonitoringConfig informs Fleet-based applications/services/UIs how the
  metrics for the underlying cluster is reported to cloud monitoring services.
  It can be set from empty to non-empty, but can't be mutated directly to
  prevent accidentally breaking the constinousty of metrics.

  Fields:
    cluster: Optional. Cluster name used to report metrics. For Anthos on
      VMWare/Baremetal/MultiCloud clusters, it would be in format
      {cluster_type}/{cluster_name}, e.g., "awsClusters/cluster_1".
    clusterHash: Optional. For GKE and Multicloud clusters, this is the UUID
      of the cluster resource. For VMWare and Baremetal clusters, this is the
      kube-system UID.
    kubernetesMetricsPrefix: Optional. Kubernetes system metrics, if
      available, are written to this prefix. This defaults to kubernetes.io
      for GKE, and kubernetes.io/anthos for Anthos eventually. Noted: Anthos
      MultiCloud will have kubernetes.io prefix today but will migration to be
      under kubernetes.io/anthos.
    location: Optional. Location used to report Metrics
    projectId: Optional. Project used to report Metrics
  """

  cluster = _messages.StringField(1)
  clusterHash = _messages.StringField(2)
  kubernetesMetricsPrefix = _messages.StringField(3)
  location = _messages.StringField(4)
  projectId = _messages.StringField(5)


class MultiCloudCluster(_messages.Message):
  r"""MultiCloudCluster contains information specific to GKE Multi-Cloud
  clusters.

  Fields:
    clusterMissing: Output only. If cluster_missing is set then it denotes
      that API(gkemulticloud.googleapis.com) resource for this GKE Multi-Cloud
      cluster no longer exists.
    resourceLink: Immutable. Self-link of the Google Cloud resource for the
      GKE Multi-Cloud cluster. For example:
      //gkemulticloud.googleapis.com/projects/my-project/locations/us-
      west1-a/awsClusters/my-cluster
      //gkemulticloud.googleapis.com/projects/my-project/locations/us-
      west1-a/azureClusters/my-cluster
      //gkemulticloud.googleapis.com/projects/my-project/locations/us-
      west1-a/attachedClusters/my-cluster
  """

  clusterMissing = _messages.BooleanField(1)
  resourceLink = _messages.StringField(2)


class MultiClusterIngressFeatureSpec(_messages.Message):
  r"""**Multi-cluster Ingress**: The configuration for the MultiClusterIngress
  feature.

  Fields:
    configMembership: Fully-qualified Membership name which hosts the
      MultiClusterIngress CRD. Example: `projects/foo-
      proj/locations/global/memberships/bar`
  """

  configMembership = _messages.StringField(1)


class Namespace(_messages.Message):
  r"""Namespace represents a namespace across the Fleet

  Messages:
    LabelsValue: Optional. Labels for this Namespace.
    NamespaceLabelsValue: Optional. Namespace-level cluster namespace labels.
      These labels are applied to the related namespace of the member clusters
      bound to the parent Scope. Scope-level labels (`namespace_labels` in the
      Fleet Scope resource) take precedence over Namespace-level labels if
      they share a key. Keys and values must be Kubernetes-conformant.

  Fields:
    createTime: Output only. When the namespace was created.
    deleteTime: Output only. When the namespace was deleted.
    labels: Optional. Labels for this Namespace.
    name: The resource name for the namespace
      `projects/{project}/locations/{location}/namespaces/{namespace}`
    namespaceLabels: Optional. Namespace-level cluster namespace labels. These
      labels are applied to the related namespace of the member clusters bound
      to the parent Scope. Scope-level labels (`namespace_labels` in the Fleet
      Scope resource) take precedence over Namespace-level labels if they
      share a key. Keys and values must be Kubernetes-conformant.
    scope: Required. Scope associated with the namespace
    state: Output only. State of the namespace resource.
    tenancyProject: Tenancy Project associated with the namespace
    uid: Output only. Google-generated UUID for this resource. This is unique
      across all namespace resources. If a namespace resource is deleted and
      another resource with the same name is created, it gets a different uid.
    updateTime: Output only. When the namespace was last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels for this Namespace.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class NamespaceLabelsValue(_messages.Message):
    r"""Optional. Namespace-level cluster namespace labels. These labels are
    applied to the related namespace of the member clusters bound to the
    parent Scope. Scope-level labels (`namespace_labels` in the Fleet Scope
    resource) take precedence over Namespace-level labels if they share a key.
    Keys and values must be Kubernetes-conformant.

    Messages:
      AdditionalProperty: An additional property for a NamespaceLabelsValue
        object.

    Fields:
      additionalProperties: Additional properties of type NamespaceLabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a NamespaceLabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  deleteTime = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  namespaceLabels = _messages.MessageField('NamespaceLabelsValue', 5)
  scope = _messages.StringField(6)
  state = _messages.MessageField('NamespaceLifecycleState', 7)
  tenancyProject = _messages.IntegerField(8)
  uid = _messages.StringField(9)
  updateTime = _messages.StringField(10)


class NamespaceLifecycleState(_messages.Message):
  r"""NamespaceLifecycleState describes the state of a Namespace resource.

  Enums:
    CodeValueValuesEnum: Output only. The current state of the Namespace
      resource.

  Fields:
    code: Output only. The current state of the Namespace resource.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the Namespace resource.

    Values:
      CODE_UNSPECIFIED: The code is not set.
      CREATING: The namespace is being created.
      READY: The namespace active.
      DELETING: The namespace is being deleted.
      UPDATING: The namespace is being updated.
    """
    CODE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    DELETING = 3
    UPDATING = 4

  code = _messages.EnumField('CodeValueValuesEnum', 1)


class OnPremCluster(_messages.Message):
  r"""OnPremCluster contains information specific to GKE On-Prem clusters.

  Enums:
    ClusterTypeValueValuesEnum: Immutable. The on prem cluster's type.

  Fields:
    adminCluster: Immutable. Whether the cluster is an admin cluster.
    clusterMissing: Output only. If cluster_missing is set then it denotes
      that API(gkeonprem.googleapis.com) resource for this GKE On-Prem cluster
      no longer exists.
    clusterType: Immutable. The on prem cluster's type.
    resourceLink: Immutable. Self-link of the Google Cloud resource for the
      GKE On-Prem cluster. For example:
      //gkeonprem.googleapis.com/projects/my-project/locations/us-
      west1-a/vmwareClusters/my-cluster
      //gkeonprem.googleapis.com/projects/my-project/locations/us-
      west1-a/bareMetalClusters/my-cluster
  """

  class ClusterTypeValueValuesEnum(_messages.Enum):
    r"""Immutable. The on prem cluster's type.

    Values:
      CLUSTERTYPE_UNSPECIFIED: The ClusterType is not set.
      BOOTSTRAP: The ClusterType is bootstrap cluster.
      HYBRID: The ClusterType is baremetal hybrid cluster.
      STANDALONE: The ClusterType is baremetal standalone cluster.
      USER: The ClusterType is user cluster.
    """
    CLUSTERTYPE_UNSPECIFIED = 0
    BOOTSTRAP = 1
    HYBRID = 2
    STANDALONE = 3
    USER = 4

  adminCluster = _messages.BooleanField(1)
  clusterMissing = _messages.BooleanField(2)
  clusterType = _messages.EnumField('ClusterTypeValueValuesEnum', 3)
  resourceLink = _messages.StringField(4)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('GoogleRpcStatus', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    cancelRequested: Output only. Identifies whether the user has requested
      cancellation of the operation. Operations that have successfully been
      cancelled have google.longrunning.Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    statusDetail: Output only. Human-readable status of the operation, if any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  cancelRequested = _messages.BooleanField(2)
  createTime = _messages.StringField(3)
  endTime = _messages.StringField(4)
  statusDetail = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Origin(_messages.Message):
  r"""Origin defines where this MembershipFeatureSpec originated from.

  Enums:
    TypeValueValuesEnum: Type specifies which type of origin is set.

  Fields:
    type: Type specifies which type of origin is set.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type specifies which type of origin is set.

    Values:
      TYPE_UNSPECIFIED: Type is unknown or not set.
      FLEET: Per-Membership spec was inherited from the fleet-level default.
      FLEET_OUT_OF_SYNC: Per-Membership spec was inherited from the fleet-
        level default but is now out of sync with the current default.
      USER: Per-Membership spec was inherited from a user specification.
    """
    TYPE_UNSPECIFIED = 0
    FLEET = 1
    FLEET_OUT_OF_SYNC = 2
    USER = 3

  type = _messages.EnumField('TypeValueValuesEnum', 1)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    rules: If more than one rule is specified, the rules are applied in the
      following manner: - All matching LOG rules are always applied. - If any
      DENY/DENY_WITH_LOG rule matches, permission is denied. Logging will be
      applied if one or more matching rule requires logging. - Otherwise, if
      any ALLOW/ALLOW_WITH_LOG rule matches, permission is granted. Logging
      will be applied if one or more matching rule requires logging. -
      Otherwise, if no rule applies, permission is denied.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  rules = _messages.MessageField('Rule', 4, repeated=True)
  version = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class PolicyBinding(_messages.Message):
  r"""Binauthz policy that applies to this cluster.

  Fields:
    name: The relative resource name of the binauthz platform policy to audit.
      GKE platform policies have the following format:
      `projects/{project_number}/platforms/gke/policies/{policy_id}`.
  """

  name = _messages.StringField(1)


class PolicyControllerBundleInstallSpec(_messages.Message):
  r"""BundleInstallSpec is the specification configuration for a single
  managed bundle.

  Fields:
    exemptedNamespaces: The set of namespaces to be exempted from the bundle.
  """

  exemptedNamespaces = _messages.StringField(1, repeated=True)


class PolicyControllerHubConfig(_messages.Message):
  r"""Configuration for Policy Controller

  Enums:
    InstallSpecValueValuesEnum: The install_spec represents the intended state
      specified by the latest request that mutated install_spec in the feature
      spec, not the lifecycle state of the feature observed by the Hub feature
      controller that is reported in the feature state.

  Messages:
    DeploymentConfigsValue: Map of deployment configs to deployments
      ("admission", "audit", "mutation').

  Fields:
    auditIntervalSeconds: Sets the interval for Policy Controller Audit Scans
      (in seconds). When set to 0, this disables audit functionality
      altogether.
    constraintViolationLimit: The maximum number of audit violations to be
      stored in a constraint. If not set, the internal default (currently 20)
      will be used.
    deploymentConfigs: Map of deployment configs to deployments ("admission",
      "audit", "mutation').
    exemptableNamespaces: The set of namespaces that are excluded from Policy
      Controller checks. Namespaces do not need to currently exist on the
      cluster.
    installSpec: The install_spec represents the intended state specified by
      the latest request that mutated install_spec in the feature spec, not
      the lifecycle state of the feature observed by the Hub feature
      controller that is reported in the feature state.
    logDeniesEnabled: Logs all denies and dry run failures.
    monitoring: Monitoring specifies the configuration of monitoring.
    mutationEnabled: Enables the ability to mutate resources using Policy
      Controller.
    policyContent: Specifies the desired policy content on the cluster
    referentialRulesEnabled: Enables the ability to use Constraint Templates
      that reference to objects other than the object currently being
      evaluated.
  """

  class InstallSpecValueValuesEnum(_messages.Enum):
    r"""The install_spec represents the intended state specified by the latest
    request that mutated install_spec in the feature spec, not the lifecycle
    state of the feature observed by the Hub feature controller that is
    reported in the feature state.

    Values:
      INSTALL_SPEC_UNSPECIFIED: Spec is unknown.
      INSTALL_SPEC_NOT_INSTALLED: Request to uninstall Policy Controller.
      INSTALL_SPEC_ENABLED: Request to install and enable Policy Controller.
      INSTALL_SPEC_SUSPENDED: Request to suspend Policy Controller i.e. its
        webhooks. If Policy Controller is not installed, it will be installed
        but suspended.
      INSTALL_SPEC_DETACHED: Request to stop all reconciliation actions by
        PoCo Hub controller. This is a breakglass mechanism to stop PoCo Hub
        from affecting cluster resources.
    """
    INSTALL_SPEC_UNSPECIFIED = 0
    INSTALL_SPEC_NOT_INSTALLED = 1
    INSTALL_SPEC_ENABLED = 2
    INSTALL_SPEC_SUSPENDED = 3
    INSTALL_SPEC_DETACHED = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DeploymentConfigsValue(_messages.Message):
    r"""Map of deployment configs to deployments ("admission", "audit",
    "mutation').

    Messages:
      AdditionalProperty: An additional property for a DeploymentConfigsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        DeploymentConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DeploymentConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A PolicyControllerPolicyControllerDeploymentConfig attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('PolicyControllerPolicyControllerDeploymentConfig', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  auditIntervalSeconds = _messages.IntegerField(1)
  constraintViolationLimit = _messages.IntegerField(2)
  deploymentConfigs = _messages.MessageField('DeploymentConfigsValue', 3)
  exemptableNamespaces = _messages.StringField(4, repeated=True)
  installSpec = _messages.EnumField('InstallSpecValueValuesEnum', 5)
  logDeniesEnabled = _messages.BooleanField(6)
  monitoring = _messages.MessageField('PolicyControllerMonitoringConfig', 7)
  mutationEnabled = _messages.BooleanField(8)
  policyContent = _messages.MessageField('PolicyControllerPolicyContentSpec', 9)
  referentialRulesEnabled = _messages.BooleanField(10)


class PolicyControllerMembershipSpec(_messages.Message):
  r"""**Policy Controller**: Configuration for a single cluster. Intended to
  parallel the PolicyController CR.

  Fields:
    policyControllerHubConfig: Policy Controller configuration for the
      cluster.
    version: Version of Policy Controller installed.
  """

  policyControllerHubConfig = _messages.MessageField('PolicyControllerHubConfig', 1)
  version = _messages.StringField(2)


class PolicyControllerMembershipState(_messages.Message):
  r"""**Policy Controller**: State for a single cluster.

  Enums:
    StateValueValuesEnum: The overall Policy Controller lifecycle state
      observed by the Hub Feature controller.

  Messages:
    ComponentStatesValue: Currently these include (also serving as map keys):
      1. "admission" 2. "audit" 3. "mutation"

  Fields:
    componentStates: Currently these include (also serving as map keys): 1.
      "admission" 2. "audit" 3. "mutation"
    policyContentState: The overall content state observed by the Hub Feature
      controller.
    state: The overall Policy Controller lifecycle state observed by the Hub
      Feature controller.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The overall Policy Controller lifecycle state observed by the Hub
    Feature controller.

    Values:
      LIFECYCLE_STATE_UNSPECIFIED: The lifecycle state is unspecified.
      NOT_INSTALLED: The PC does not exist on the given cluster, and no k8s
        resources of any type that are associated with the PC should exist
        there. The cluster does not possess a membership with the PCH.
      INSTALLING: The PCH possesses a Membership, however the PC is not fully
        installed on the cluster. In this state the hub can be expected to be
        taking actions to install the PC on the cluster.
      ACTIVE: The PC is fully installed on the cluster and in an operational
        mode. In this state PCH will be reconciling state with the PC, and the
        PC will be performing it's operational tasks per that software.
        Entering a READY state requires that the hub has confirmed the PC is
        installed and its pods are operational with the version of the PC the
        PCH expects.
      UPDATING: The PC is fully installed, but in the process of changing the
        configuration (including changing the version of PC either up and
        down, or modifying the manifests of PC) of the resources running on
        the cluster. The PCH has a Membership, is aware of the version the
        cluster should be running in, but has not confirmed for itself that
        the PC is running with that version.
      DECOMMISSIONING: The PC may have resources on the cluster, but the PCH
        wishes to remove the Membership. The Membership still exists.
      CLUSTER_ERROR: The PC is not operational, and the PCH is unable to act
        to make it operational. Entering a CLUSTER_ERROR state happens
        automatically when the PCH determines that a PC installed on the
        cluster is non-operative or that the cluster does not meet
        requirements set for the PCH to administer the cluster but has
        nevertheless been given an instruction to do so (such as 'install').
      HUB_ERROR: In this state, the PC may still be operational, and only the
        PCH is unable to act. The hub should not issue instructions to change
        the PC state, or otherwise interfere with the on-cluster resources.
        Entering a HUB_ERROR state happens automatically when the PCH
        determines the hub is in an unhealthy state and it wishes to 'take
        hands off' to avoid corrupting the PC or other data.
      SUSPENDED: Policy Controller (PC) is installed but suspended. This means
        that the policies are not enforced, but violations are still recorded
        (through audit).
      DETACHED: PoCo Hub is not taking any action to reconcile cluster
        objects. Changes to those objects will not be overwritten by PoCo Hub.
    """
    LIFECYCLE_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLING = 2
    ACTIVE = 3
    UPDATING = 4
    DECOMMISSIONING = 5
    CLUSTER_ERROR = 6
    HUB_ERROR = 7
    SUSPENDED = 8
    DETACHED = 9

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ComponentStatesValue(_messages.Message):
    r"""Currently these include (also serving as map keys): 1. "admission" 2.
    "audit" 3. "mutation"

    Messages:
      AdditionalProperty: An additional property for a ComponentStatesValue
        object.

    Fields:
      additionalProperties: Additional properties of type ComponentStatesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ComponentStatesValue object.

      Fields:
        key: Name of the additional property.
        value: A PolicyControllerOnClusterState attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('PolicyControllerOnClusterState', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  componentStates = _messages.MessageField('ComponentStatesValue', 1)
  policyContentState = _messages.MessageField('PolicyControllerPolicyContentState', 2)
  state = _messages.EnumField('StateValueValuesEnum', 3)


class PolicyControllerMonitoringConfig(_messages.Message):
  r"""MonitoringConfig specifies the backends Policy Controller should export
  metrics to. For example, to specify metrics should be exported to Cloud
  Monitoring and Prometheus, specify backends: ["cloudmonitoring",
  "prometheus"]

  Enums:
    BackendsValueListEntryValuesEnum:

  Fields:
    backends: Specifies the list of backends Policy Controller will export to.
      An empty list would effectively disable metrics export.
  """

  class BackendsValueListEntryValuesEnum(_messages.Enum):
    r"""BackendsValueListEntryValuesEnum enum type.

    Values:
      MONITORING_BACKEND_UNSPECIFIED: Backend cannot be determined
      PROMETHEUS: Prometheus backend for monitoring
      CLOUD_MONITORING: Stackdriver/Cloud Monitoring backend for monitoring
    """
    MONITORING_BACKEND_UNSPECIFIED = 0
    PROMETHEUS = 1
    CLOUD_MONITORING = 2

  backends = _messages.EnumField('BackendsValueListEntryValuesEnum', 1, repeated=True)


class PolicyControllerOnClusterState(_messages.Message):
  r"""OnClusterState represents the state of a sub-component of Policy
  Controller.

  Enums:
    StateValueValuesEnum: The lifecycle state of this component.

  Fields:
    details: Surface potential errors or information logs.
    state: The lifecycle state of this component.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The lifecycle state of this component.

    Values:
      LIFECYCLE_STATE_UNSPECIFIED: The lifecycle state is unspecified.
      NOT_INSTALLED: The PC does not exist on the given cluster, and no k8s
        resources of any type that are associated with the PC should exist
        there. The cluster does not possess a membership with the PCH.
      INSTALLING: The PCH possesses a Membership, however the PC is not fully
        installed on the cluster. In this state the hub can be expected to be
        taking actions to install the PC on the cluster.
      ACTIVE: The PC is fully installed on the cluster and in an operational
        mode. In this state PCH will be reconciling state with the PC, and the
        PC will be performing it's operational tasks per that software.
        Entering a READY state requires that the hub has confirmed the PC is
        installed and its pods are operational with the version of the PC the
        PCH expects.
      UPDATING: The PC is fully installed, but in the process of changing the
        configuration (including changing the version of PC either up and
        down, or modifying the manifests of PC) of the resources running on
        the cluster. The PCH has a Membership, is aware of the version the
        cluster should be running in, but has not confirmed for itself that
        the PC is running with that version.
      DECOMMISSIONING: The PC may have resources on the cluster, but the PCH
        wishes to remove the Membership. The Membership still exists.
      CLUSTER_ERROR: The PC is not operational, and the PCH is unable to act
        to make it operational. Entering a CLUSTER_ERROR state happens
        automatically when the PCH determines that a PC installed on the
        cluster is non-operative or that the cluster does not meet
        requirements set for the PCH to administer the cluster but has
        nevertheless been given an instruction to do so (such as 'install').
      HUB_ERROR: In this state, the PC may still be operational, and only the
        PCH is unable to act. The hub should not issue instructions to change
        the PC state, or otherwise interfere with the on-cluster resources.
        Entering a HUB_ERROR state happens automatically when the PCH
        determines the hub is in an unhealthy state and it wishes to 'take
        hands off' to avoid corrupting the PC or other data.
      SUSPENDED: Policy Controller (PC) is installed but suspended. This means
        that the policies are not enforced, but violations are still recorded
        (through audit).
      DETACHED: PoCo Hub is not taking any action to reconcile cluster
        objects. Changes to those objects will not be overwritten by PoCo Hub.
    """
    LIFECYCLE_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLING = 2
    ACTIVE = 3
    UPDATING = 4
    DECOMMISSIONING = 5
    CLUSTER_ERROR = 6
    HUB_ERROR = 7
    SUSPENDED = 8
    DETACHED = 9

  details = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class PolicyControllerPolicyContentSpec(_messages.Message):
  r"""PolicyContentSpec defines the user's desired content configuration on
  the cluster.

  Messages:
    BundlesValue: map of bundle name to BundleInstallSpec. The bundle name
      maps to the `bundleName` key in the
      `policycontroller.gke.io/constraintData` annotation on a constraint.

  Fields:
    bundles: map of bundle name to BundleInstallSpec. The bundle name maps to
      the `bundleName` key in the `policycontroller.gke.io/constraintData`
      annotation on a constraint.
    templateLibrary: Configures the installation of the Template Library.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class BundlesValue(_messages.Message):
    r"""map of bundle name to BundleInstallSpec. The bundle name maps to the
    `bundleName` key in the `policycontroller.gke.io/constraintData`
    annotation on a constraint.

    Messages:
      AdditionalProperty: An additional property for a BundlesValue object.

    Fields:
      additionalProperties: Additional properties of type BundlesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a BundlesValue object.

      Fields:
        key: Name of the additional property.
        value: A PolicyControllerBundleInstallSpec attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('PolicyControllerBundleInstallSpec', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  bundles = _messages.MessageField('BundlesValue', 1)
  templateLibrary = _messages.MessageField('PolicyControllerTemplateLibraryConfig', 2)


class PolicyControllerPolicyContentState(_messages.Message):
  r"""The state of the policy controller policy content

  Messages:
    BundleStatesValue: The state of the any bundles included in the chosen
      version of the manifest

  Fields:
    bundleStates: The state of the any bundles included in the chosen version
      of the manifest
    referentialSyncConfigState: The state of the referential data sync
      configuration. This could represent the state of either the syncSet
      object(s) or the config object, depending on the version of PoCo
      configured by the user.
    templateLibraryState: The state of the template library
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class BundleStatesValue(_messages.Message):
    r"""The state of the any bundles included in the chosen version of the
    manifest

    Messages:
      AdditionalProperty: An additional property for a BundleStatesValue
        object.

    Fields:
      additionalProperties: Additional properties of type BundleStatesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a BundleStatesValue object.

      Fields:
        key: Name of the additional property.
        value: A PolicyControllerOnClusterState attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('PolicyControllerOnClusterState', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  bundleStates = _messages.MessageField('BundleStatesValue', 1)
  referentialSyncConfigState = _messages.MessageField('PolicyControllerOnClusterState', 2)
  templateLibraryState = _messages.MessageField('PolicyControllerOnClusterState', 3)


class PolicyControllerPolicyControllerDeploymentConfig(_messages.Message):
  r"""Deployment-specific configuration.

  Enums:
    PodAffinityValueValuesEnum: Pod affinity configuration.

  Fields:
    containerResources: Container resource requirements.
    podAffinity: Pod affinity configuration.
    podAntiAffinity: Pod anti-affinity enablement. Deprecated: use
      `pod_affinity` instead.
    podTolerations: Pod tolerations of node taints.
    replicaCount: Pod replica count.
  """

  class PodAffinityValueValuesEnum(_messages.Enum):
    r"""Pod affinity configuration.

    Values:
      AFFINITY_UNSPECIFIED: No affinity configuration has been specified.
      NO_AFFINITY: Affinity configurations will be removed from the
        deployment.
      ANTI_AFFINITY: Anti-affinity configuration will be applied to this
        deployment. Default for admissions deployment.
    """
    AFFINITY_UNSPECIFIED = 0
    NO_AFFINITY = 1
    ANTI_AFFINITY = 2

  containerResources = _messages.MessageField('PolicyControllerResourceRequirements', 1)
  podAffinity = _messages.EnumField('PodAffinityValueValuesEnum', 2)
  podAntiAffinity = _messages.BooleanField(3)
  podTolerations = _messages.MessageField('PolicyControllerToleration', 4, repeated=True)
  replicaCount = _messages.IntegerField(5)


class PolicyControllerResourceList(_messages.Message):
  r"""ResourceList contains container resource requirements.

  Fields:
    cpu: CPU requirement expressed in Kubernetes resource units.
    memory: Memory requirement expressed in Kubernetes resource units.
  """

  cpu = _messages.StringField(1)
  memory = _messages.StringField(2)


class PolicyControllerResourceRequirements(_messages.Message):
  r"""ResourceRequirements describes the compute resource requirements.

  Fields:
    limits: Limits describes the maximum amount of compute resources allowed
      for use by the running container.
    requests: Requests describes the amount of compute resources reserved for
      the container by the kube-scheduler.
  """

  limits = _messages.MessageField('PolicyControllerResourceList', 1)
  requests = _messages.MessageField('PolicyControllerResourceList', 2)


class PolicyControllerTemplateLibraryConfig(_messages.Message):
  r"""The config specifying which default library templates to install.

  Enums:
    InstallationValueValuesEnum: Configures the manner in which the template
      library is installed on the cluster.

  Fields:
    installation: Configures the manner in which the template library is
      installed on the cluster.
  """

  class InstallationValueValuesEnum(_messages.Enum):
    r"""Configures the manner in which the template library is installed on
    the cluster.

    Values:
      INSTALLATION_UNSPECIFIED: No installation strategy has been specified.
      NOT_INSTALLED: Do not install the template library.
      ALL: Install the entire template library.
    """
    INSTALLATION_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    ALL = 2

  installation = _messages.EnumField('InstallationValueValuesEnum', 1)


class PolicyControllerToleration(_messages.Message):
  r"""Toleration of a node taint.

  Fields:
    effect: Matches a taint effect.
    key: Matches a taint key (not necessarily unique).
    operator: Matches a taint operator.
    value: Matches a taint value.
  """

  effect = _messages.StringField(1)
  key = _messages.StringField(2)
  operator = _messages.StringField(3)
  value = _messages.StringField(4)


class RBACRoleBinding(_messages.Message):
  r"""RBACRoleBinding represents a rbacrolebinding across the Fleet

  Messages:
    LabelsValue: Optional. Labels for this RBACRolebinding.

  Fields:
    createTime: Output only. When the rbacrolebinding was created.
    deleteTime: Output only. When the rbacrolebinding was deleted.
    group: group is the group, as seen by the kubernetes cluster.
    labels: Optional. Labels for this RBACRolebinding.
    name: The resource name for the rbacrolebinding `projects/{project}/locati
      ons/{location}/scopes/{scope}/rbacrolebindings/{rbacrolebinding}` or `pr
      ojects/{project}/locations/{location}/memberships/{membership}/rbacroleb
      indings/{rbacrolebinding}`
    role: Required. Role to bind to the principal
    state: Output only. State of the rbacrolebinding resource.
    uid: Output only. Google-generated UUID for this resource. This is unique
      across all rbacrolebinding resources. If a rbacrolebinding resource is
      deleted and another resource with the same name is created, it gets a
      different uid.
    updateTime: Output only. When the rbacrolebinding was last updated.
    user: user is the name of the user as seen by the kubernetes cluster,
      example "alice" or "<EMAIL>"
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels for this RBACRolebinding.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  deleteTime = _messages.StringField(2)
  group = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  role = _messages.MessageField('Role', 6)
  state = _messages.MessageField('RBACRoleBindingLifecycleState', 7)
  uid = _messages.StringField(8)
  updateTime = _messages.StringField(9)
  user = _messages.StringField(10)


class RBACRoleBindingActuationFeatureSpec(_messages.Message):
  r"""**RBAC RoleBinding Actuation**: The Hub-wide input for the
  RBACRoleBindingActuation feature.

  Fields:
    allowedCustomRoles: The list of allowed custom roles (ClusterRoles). If a
      ClusterRole is not part of this list, it cannot be used in a Scope
      RBACRoleBinding. If a ClusterRole in this list is in use, it cannot be
      removed from the list.
  """

  allowedCustomRoles = _messages.StringField(1, repeated=True)


class RBACRoleBindingActuationFeatureState(_messages.Message):
  r"""**RBAC RoleBinding Actuation**: An empty state left as an example Hub-
  wide Feature state.
  """



class RBACRoleBindingLifecycleState(_messages.Message):
  r"""RBACRoleBindingLifecycleState describes the state of a RbacRoleBinding
  resource.

  Enums:
    CodeValueValuesEnum: Output only. The current state of the rbacrolebinding
      resource.

  Fields:
    code: Output only. The current state of the rbacrolebinding resource.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the rbacrolebinding resource.

    Values:
      CODE_UNSPECIFIED: The code is not set.
      CREATING: The rbacrolebinding is being created.
      READY: The rbacrolebinding active.
      DELETING: The rbacrolebinding is being deleted.
      UPDATING: The rbacrolebinding is being updated.
    """
    CODE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    DELETING = 3
    UPDATING = 4

  code = _messages.EnumField('CodeValueValuesEnum', 1)


class Reference(_messages.Message):
  r"""Represents a reference to a resource.

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    createTime: Output only. The creation time.
    details: Details of the reference type with no implied semantics.
      Cumulative size of the field must not be more than 1KiB.
    name: Output only. Relative resource name of the reference. Includes
      target resource as a parent and reference uid
      `{target_resource}/references/{reference_id}`. For example,
      `projects/{my-project}/locations/{location}/instances/{my-
      instance}/references/{xyz}`.
    sourceResource: Required. Full resource name of the resource which refers
      the target resource. For example:
      //tpu.googleapis.com/projects/myproject/nodes/mynode
    targetUniqueId: Output only. The unique_id of the target resource. Example
      1: (For arcus resource) A-1-0-2-387420123-13-913517247483640811
      unique_id format defined in go/m11n-unique-id-as-resource-id Example 2:
      (For CCFE resource) 123e4567-e89b-12d3-a456-************
    type: Required. Type of the reference. A service might impose limits on
      number of references of a specific type. Note: It's recommended to use
      CAPITALS_WITH_UNDERSCORES style for a type name.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  name = _messages.StringField(3)
  sourceResource = _messages.StringField(4)
  targetUniqueId = _messages.StringField(5)
  type = _messages.StringField(6)


class ResourceManifest(_messages.Message):
  r"""ResourceManifest represents a single Kubernetes resource to be applied
  to the cluster.

  Fields:
    clusterScoped: Output only. Whether the resource provided in the manifest
      is `cluster_scoped`. If unset, the manifest is assumed to be namespace
      scoped. This field is used for REST mapping when applying the resource
      in a cluster.
    manifest: Output only. YAML manifest of the resource.
  """

  clusterScoped = _messages.BooleanField(1)
  manifest = _messages.StringField(2)


class ResourceOptions(_messages.Message):
  r"""ResourceOptions represent options for Kubernetes resource generation.

  Fields:
    connectVersion: Optional. The Connect agent version to use for
      connect_resources. Defaults to the latest GKE Connect version. The
      version must be a currently supported version, obsolete versions will be
      rejected.
    k8sVersion: Optional. Major version of the Kubernetes cluster. This is
      only used to determine which version to use for the
      CustomResourceDefinition resources, `apiextensions/v1beta1`
      or`apiextensions/v1`.
    v1beta1Crd: Optional. Use `apiextensions/v1beta1` instead of
      `apiextensions/v1` for CustomResourceDefinition resources. This option
      should be set for clusters with Kubernetes apiserver versions <1.16.
  """

  connectVersion = _messages.StringField(1)
  k8sVersion = _messages.StringField(2)
  v1beta1Crd = _messages.BooleanField(3)


class Role(_messages.Message):
  r"""Role is the type for Kubernetes roles

  Enums:
    PredefinedRoleValueValuesEnum: predefined_role is the Kubernetes default
      role to use

  Fields:
    customRole: Optional. custom_role is the name of a custom
      KubernetesClusterRole to use.
    predefinedRole: predefined_role is the Kubernetes default role to use
  """

  class PredefinedRoleValueValuesEnum(_messages.Enum):
    r"""predefined_role is the Kubernetes default role to use

    Values:
      UNKNOWN: UNKNOWN
      ADMIN: ADMIN has EDIT and RBAC permissions
      EDIT: EDIT can edit all resources except RBAC
      VIEW: VIEW can only read resources
      ANTHOS_SUPPORT: ANTHOS_SUPPORT gives Google Support read-only access to
        a number of cluster resources.
    """
    UNKNOWN = 0
    ADMIN = 1
    EDIT = 2
    VIEW = 3
    ANTHOS_SUPPORT = 4

  customRole = _messages.StringField(1)
  predefinedRole = _messages.EnumField('PredefinedRoleValueValuesEnum', 2)


class Rule(_messages.Message):
  r"""A rule to be applied in a Policy.

  Enums:
    ActionValueValuesEnum: Required

  Fields:
    action: Required
    conditions: Additional restrictions that must be met. All conditions must
      pass for the rule to match.
    description: Human-readable description of the rule.
    in_: If one or more 'in' clauses are specified, the rule matches if the
      PRINCIPAL/AUTHORITY_SELECTOR is in at least one of these entries.
    logConfig: The config returned to callers of CheckPolicy for any entries
      that match the LOG action.
    notIn: If one or more 'not_in' clauses are specified, the rule matches if
      the PRINCIPAL/AUTHORITY_SELECTOR is in none of the entries. The format
      for in and not_in entries can be found at in the Local IAM documentation
      (see go/local-iam#features).
    permissions: A permission is a string of form `..` (e.g.,
      'storage.buckets.list'). A value of '*' matches all permissions, and a
      verb part of '*' (e.g., 'storage.buckets.*') matches all verbs.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required

    Values:
      NO_ACTION: Default no action.
      ALLOW: Matching 'Entries' grant access.
      ALLOW_WITH_LOG: Matching 'Entries' grant access and the caller promises
        to log the request per the returned log_configs.
      DENY: Matching 'Entries' deny access.
      DENY_WITH_LOG: Matching 'Entries' deny access and the caller promises to
        log the request per the returned log_configs.
      LOG: Matching 'Entries' tell IAM.Check callers to generate logs.
    """
    NO_ACTION = 0
    ALLOW = 1
    ALLOW_WITH_LOG = 2
    DENY = 3
    DENY_WITH_LOG = 4
    LOG = 5

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  conditions = _messages.MessageField('Condition', 2, repeated=True)
  description = _messages.StringField(3)
  in_ = _messages.StringField(4, repeated=True)
  logConfig = _messages.MessageField('LogConfig', 5, repeated=True)
  notIn = _messages.StringField(6, repeated=True)
  permissions = _messages.StringField(7, repeated=True)


class Scope(_messages.Message):
  r"""Scope represents a Scope in a Fleet.

  Messages:
    LabelsValue: Optional. Labels for this Scope.
    NamespaceLabelsValue: Optional. Scope-level cluster namespace labels. For
      the member clusters bound to the Scope, these labels are applied to each
      namespace under the Scope. Scope-level labels take precedence over
      Namespace-level labels (`namespace_labels` in the Fleet Namespace
      resource) if they share a key. Keys and values must be Kubernetes-
      conformant.

  Fields:
    createTime: Output only. When the scope was created.
    deleteTime: Output only. When the scope was deleted.
    labels: Optional. Labels for this Scope.
    name: The resource name for the scope
      `projects/{project}/locations/{location}/scopes/{scope}`
    namespaceLabels: Optional. Scope-level cluster namespace labels. For the
      member clusters bound to the Scope, these labels are applied to each
      namespace under the Scope. Scope-level labels take precedence over
      Namespace-level labels (`namespace_labels` in the Fleet Namespace
      resource) if they share a key. Keys and values must be Kubernetes-
      conformant.
    state: Output only. State of the scope resource.
    uid: Output only. Google-generated UUID for this resource. This is unique
      across all scope resources. If a scope resource is deleted and another
      resource with the same name is created, it gets a different uid.
    updateTime: Output only. When the scope was last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels for this Scope.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class NamespaceLabelsValue(_messages.Message):
    r"""Optional. Scope-level cluster namespace labels. For the member
    clusters bound to the Scope, these labels are applied to each namespace
    under the Scope. Scope-level labels take precedence over Namespace-level
    labels (`namespace_labels` in the Fleet Namespace resource) if they share
    a key. Keys and values must be Kubernetes-conformant.

    Messages:
      AdditionalProperty: An additional property for a NamespaceLabelsValue
        object.

    Fields:
      additionalProperties: Additional properties of type NamespaceLabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a NamespaceLabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  deleteTime = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  namespaceLabels = _messages.MessageField('NamespaceLabelsValue', 5)
  state = _messages.MessageField('ScopeLifecycleState', 6)
  uid = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class ScopeFeatureSpec(_messages.Message):
  r"""ScopeFeatureSpec contains feature specs for a fleet scope.

  Fields:
    helloworld: Spec for the HelloWorld feature at the scope level
  """

  helloworld = _messages.MessageField('HelloWorldScopeSpec', 1)


class ScopeFeatureState(_messages.Message):
  r"""ScopeFeatureState contains Scope-wide Feature status information.

  Fields:
    helloworld: State for the HelloWorld feature at the scope level
    state: Output only. The "running state" of the Feature in this Scope.
  """

  helloworld = _messages.MessageField('HelloWorldScopeState', 1)
  state = _messages.MessageField('FeatureState', 2)


class ScopeLifecycleState(_messages.Message):
  r"""ScopeLifecycleState describes the state of a Scope resource.

  Enums:
    CodeValueValuesEnum: Output only. The current state of the scope resource.

  Fields:
    code: Output only. The current state of the scope resource.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the scope resource.

    Values:
      CODE_UNSPECIFIED: The code is not set.
      CREATING: The scope is being created.
      READY: The scope active.
      DELETING: The scope is being deleted.
      UPDATING: The scope is being updated.
    """
    CODE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    DELETING = 3
    UPDATING = 4

  code = _messages.EnumField('CodeValueValuesEnum', 1)


class SecurityPostureConfig(_messages.Message):
  r"""SecurityPostureConfig defines the flags needed to enable/disable
  features for the Security Posture API.

  Enums:
    ModeValueValuesEnum: Sets which mode to use for Security Posture features.
    VulnerabilityModeValueValuesEnum: Sets which mode to use for vulnerability
      scanning.

  Fields:
    mode: Sets which mode to use for Security Posture features.
    vulnerabilityMode: Sets which mode to use for vulnerability scanning.
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""Sets which mode to use for Security Posture features.

    Values:
      MODE_UNSPECIFIED: Default value not specified.
      DISABLED: Disables Security Posture features on the cluster.
      BASIC: Applies Security Posture features on the cluster.
      ENTERPRISE: Applies the Security Posture off cluster Enterprise level
        features.
    """
    MODE_UNSPECIFIED = 0
    DISABLED = 1
    BASIC = 2
    ENTERPRISE = 3

  class VulnerabilityModeValueValuesEnum(_messages.Enum):
    r"""Sets which mode to use for vulnerability scanning.

    Values:
      VULNERABILITY_MODE_UNSPECIFIED: Default value not specified.
      VULNERABILITY_DISABLED: Disables vulnerability scanning on the cluster.
      VULNERABILITY_BASIC: Applies basic vulnerability scanning on the
        cluster.
      VULNERABILITY_ENTERPRISE: Applies the Security Posture's vulnerability
        on cluster Enterprise level features.
    """
    VULNERABILITY_MODE_UNSPECIFIED = 0
    VULNERABILITY_DISABLED = 1
    VULNERABILITY_BASIC = 2
    VULNERABILITY_ENTERPRISE = 3

  mode = _messages.EnumField('ModeValueValuesEnum', 1)
  vulnerabilityMode = _messages.EnumField('VulnerabilityModeValueValuesEnum', 2)


class ServiceMeshCondition(_messages.Message):
  r"""Condition being reported.

  Enums:
    CodeValueValuesEnum: Unique identifier of the condition which describes
      the condition recognizable to the user.
    SeverityValueValuesEnum: Severity level of the condition.

  Fields:
    code: Unique identifier of the condition which describes the condition
      recognizable to the user.
    details: A short summary about the issue.
    documentationLink: Links contains actionable information.
    severity: Severity level of the condition.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Unique identifier of the condition which describes the condition
    recognizable to the user.

    Values:
      CODE_UNSPECIFIED: Default Unspecified code
      MESH_IAM_PERMISSION_DENIED: Mesh IAM permission denied error code
      MESH_IAM_CROSS_PROJECT_PERMISSION_DENIED: Permission denied error code
        for cross-project
      CNI_CONFIG_UNSUPPORTED: CNI config unsupported error code
      GKE_SANDBOX_UNSUPPORTED: GKE sandbox unsupported error code
      NODEPOOL_WORKLOAD_IDENTITY_FEDERATION_REQUIRED: Nodepool workload
        identity federation required error code
      CNI_INSTALLATION_FAILED: CNI installation failed error code
      CNI_POD_UNSCHEDULABLE: CNI pod unschedulable error code
      CLUSTER_HAS_ZERO_NODES: Cluster has zero node code
      CANONICAL_SERVICE_ERROR: Failure to reconcile CanonicalServices
      UNSUPPORTED_MULTIPLE_CONTROL_PLANES: Multiple control planes unsupported
        error code
      VPCSC_GA_SUPPORTED: VPC-SC GA is supported for this control plane.
      DEPRECATED_SPEC_CONTROL_PLANE_MANAGEMENT: User is using deprecated
        ControlPlaneManagement and they have not yet set Management.
      DEPRECATED_SPEC_CONTROL_PLANE_MANAGEMENT_SAFE: User is using deprecated
        ControlPlaneManagement and they have already set Management.
      CONFIG_APPLY_INTERNAL_ERROR: Configuration (Istio/k8s resources) failed
        to apply due to internal error.
      CONFIG_VALIDATION_ERROR: Configuration failed to be applied due to being
        invalid.
      CONFIG_VALIDATION_WARNING: Encountered configuration(s) with possible
        unintended behavior or invalid configuration. These configs may not
        have been applied.
      QUOTA_EXCEEDED_BACKEND_SERVICES: BackendService quota exceeded error
        code.
      QUOTA_EXCEEDED_HEALTH_CHECKS: HealthCheck quota exceeded error code.
      QUOTA_EXCEEDED_HTTP_ROUTES: HTTPRoute quota exceeded error code.
      QUOTA_EXCEEDED_TCP_ROUTES: TCPRoute quota exceeded error code.
      QUOTA_EXCEEDED_TLS_ROUTES: TLS routes quota exceeded error code.
      QUOTA_EXCEEDED_TRAFFIC_POLICIES: TrafficPolicy quota exceeded error
        code.
      QUOTA_EXCEEDED_ENDPOINT_POLICIES: EndpointPolicy quota exceeded error
        code.
      QUOTA_EXCEEDED_GATEWAYS: Gateway quota exceeded error code.
      QUOTA_EXCEEDED_MESHES: Mesh quota exceeded error code.
      QUOTA_EXCEEDED_SERVER_TLS_POLICIES: ServerTLSPolicy quota exceeded error
        code.
      QUOTA_EXCEEDED_CLIENT_TLS_POLICIES: ClientTLSPolicy quota exceeded error
        code.
      QUOTA_EXCEEDED_SERVICE_LB_POLICIES: ServiceLBPolicy quota exceeded error
        code.
      QUOTA_EXCEEDED_HTTP_FILTERS: HTTPFilter quota exceeded error code.
      QUOTA_EXCEEDED_TCP_FILTERS: TCPFilter quota exceeded error code.
      QUOTA_EXCEEDED_NETWORK_ENDPOINT_GROUPS: NetworkEndpointGroup quota
        exceeded error code.
      LEGACY_MC_SECRETS: Legacy istio secrets found for multicluster error
        code
      WORKLOAD_IDENTITY_REQUIRED: Workload identity required error code
      NON_STANDARD_BINARY_USAGE: Non-standard binary usage error code
      UNSUPPORTED_GATEWAY_CLASS: Unsupported gateway class error code
      MANAGED_CNI_NOT_ENABLED: Managed CNI not enabled error code
      MODERNIZATION_SCHEDULED: Modernization is scheduled for a cluster.
      MODERNIZATION_IN_PROGRESS: Modernization is in progress for a cluster.
      MODERNIZATION_COMPLETED: Modernization is completed for a cluster.
      MODERNIZATION_ABORTED: Modernization is aborted for a cluster.
      MODERNIZATION_WILL_BE_SCHEDULED: Modernization will be scheduled for a
        fleet.
    """
    CODE_UNSPECIFIED = 0
    MESH_IAM_PERMISSION_DENIED = 1
    MESH_IAM_CROSS_PROJECT_PERMISSION_DENIED = 2
    CNI_CONFIG_UNSUPPORTED = 3
    GKE_SANDBOX_UNSUPPORTED = 4
    NODEPOOL_WORKLOAD_IDENTITY_FEDERATION_REQUIRED = 5
    CNI_INSTALLATION_FAILED = 6
    CNI_POD_UNSCHEDULABLE = 7
    CLUSTER_HAS_ZERO_NODES = 8
    CANONICAL_SERVICE_ERROR = 9
    UNSUPPORTED_MULTIPLE_CONTROL_PLANES = 10
    VPCSC_GA_SUPPORTED = 11
    DEPRECATED_SPEC_CONTROL_PLANE_MANAGEMENT = 12
    DEPRECATED_SPEC_CONTROL_PLANE_MANAGEMENT_SAFE = 13
    CONFIG_APPLY_INTERNAL_ERROR = 14
    CONFIG_VALIDATION_ERROR = 15
    CONFIG_VALIDATION_WARNING = 16
    QUOTA_EXCEEDED_BACKEND_SERVICES = 17
    QUOTA_EXCEEDED_HEALTH_CHECKS = 18
    QUOTA_EXCEEDED_HTTP_ROUTES = 19
    QUOTA_EXCEEDED_TCP_ROUTES = 20
    QUOTA_EXCEEDED_TLS_ROUTES = 21
    QUOTA_EXCEEDED_TRAFFIC_POLICIES = 22
    QUOTA_EXCEEDED_ENDPOINT_POLICIES = 23
    QUOTA_EXCEEDED_GATEWAYS = 24
    QUOTA_EXCEEDED_MESHES = 25
    QUOTA_EXCEEDED_SERVER_TLS_POLICIES = 26
    QUOTA_EXCEEDED_CLIENT_TLS_POLICIES = 27
    QUOTA_EXCEEDED_SERVICE_LB_POLICIES = 28
    QUOTA_EXCEEDED_HTTP_FILTERS = 29
    QUOTA_EXCEEDED_TCP_FILTERS = 30
    QUOTA_EXCEEDED_NETWORK_ENDPOINT_GROUPS = 31
    LEGACY_MC_SECRETS = 32
    WORKLOAD_IDENTITY_REQUIRED = 33
    NON_STANDARD_BINARY_USAGE = 34
    UNSUPPORTED_GATEWAY_CLASS = 35
    MANAGED_CNI_NOT_ENABLED = 36
    MODERNIZATION_SCHEDULED = 37
    MODERNIZATION_IN_PROGRESS = 38
    MODERNIZATION_COMPLETED = 39
    MODERNIZATION_ABORTED = 40
    MODERNIZATION_WILL_BE_SCHEDULED = 41

  class SeverityValueValuesEnum(_messages.Enum):
    r"""Severity level of the condition.

    Values:
      SEVERITY_UNSPECIFIED: Unspecified severity
      ERROR: Indicates an issue that prevents the mesh from operating
        correctly
      WARNING: Indicates a setting is likely wrong, but the mesh is still able
        to operate
      INFO: An informational message, not requiring any action
    """
    SEVERITY_UNSPECIFIED = 0
    ERROR = 1
    WARNING = 2
    INFO = 3

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  details = _messages.StringField(2)
  documentationLink = _messages.StringField(3)
  severity = _messages.EnumField('SeverityValueValuesEnum', 4)


class ServiceMeshControlPlaneManagement(_messages.Message):
  r"""Status of control plane management.

  Enums:
    ImplementationValueValuesEnum: Output only. Implementation of managed
      control plane.
    StateValueValuesEnum: LifecycleState of control plane management.

  Fields:
    details: Explanation of state.
    implementation: Output only. Implementation of managed control plane.
    state: LifecycleState of control plane management.
  """

  class ImplementationValueValuesEnum(_messages.Enum):
    r"""Output only. Implementation of managed control plane.

    Values:
      IMPLEMENTATION_UNSPECIFIED: Unspecified
      ISTIOD: A Google build of istiod is used for the managed control plane.
      TRAFFIC_DIRECTOR: Traffic director is used for the managed control
        plane.
      UPDATING: The control plane implementation is being updated.
    """
    IMPLEMENTATION_UNSPECIFIED = 0
    ISTIOD = 1
    TRAFFIC_DIRECTOR = 2
    UPDATING = 3

  class StateValueValuesEnum(_messages.Enum):
    r"""LifecycleState of control plane management.

    Values:
      LIFECYCLE_STATE_UNSPECIFIED: Unspecified
      DISABLED: DISABLED means that the component is not enabled.
      FAILED_PRECONDITION: FAILED_PRECONDITION means that provisioning cannot
        proceed because of some characteristic of the member cluster.
      PROVISIONING: PROVISIONING means that provisioning is in progress.
      ACTIVE: ACTIVE means that the component is ready for use.
      STALLED: STALLED means that provisioning could not be done.
      NEEDS_ATTENTION: NEEDS_ATTENTION means that the component is ready, but
        some user intervention is required. (For example that the user should
        migrate workloads to a new control plane revision.)
      DEGRADED: DEGRADED means that the component is ready, but operating in a
        degraded state.
      DEPROVISIONING: DEPROVISIONING means that deprovisioning is in progress.
    """
    LIFECYCLE_STATE_UNSPECIFIED = 0
    DISABLED = 1
    FAILED_PRECONDITION = 2
    PROVISIONING = 3
    ACTIVE = 4
    STALLED = 5
    NEEDS_ATTENTION = 6
    DEGRADED = 7
    DEPROVISIONING = 8

  details = _messages.MessageField('ServiceMeshStatusDetails', 1, repeated=True)
  implementation = _messages.EnumField('ImplementationValueValuesEnum', 2)
  state = _messages.EnumField('StateValueValuesEnum', 3)


class ServiceMeshDataPlaneManagement(_messages.Message):
  r"""Status of data plane management. Only reported per-member.

  Enums:
    StateValueValuesEnum: Lifecycle status of data plane management.

  Fields:
    details: Explanation of the status.
    state: Lifecycle status of data plane management.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Lifecycle status of data plane management.

    Values:
      LIFECYCLE_STATE_UNSPECIFIED: Unspecified
      DISABLED: DISABLED means that the component is not enabled.
      FAILED_PRECONDITION: FAILED_PRECONDITION means that provisioning cannot
        proceed because of some characteristic of the member cluster.
      PROVISIONING: PROVISIONING means that provisioning is in progress.
      ACTIVE: ACTIVE means that the component is ready for use.
      STALLED: STALLED means that provisioning could not be done.
      NEEDS_ATTENTION: NEEDS_ATTENTION means that the component is ready, but
        some user intervention is required. (For example that the user should
        migrate workloads to a new control plane revision.)
      DEGRADED: DEGRADED means that the component is ready, but operating in a
        degraded state.
      DEPROVISIONING: DEPROVISIONING means that deprovisioning is in progress.
    """
    LIFECYCLE_STATE_UNSPECIFIED = 0
    DISABLED = 1
    FAILED_PRECONDITION = 2
    PROVISIONING = 3
    ACTIVE = 4
    STALLED = 5
    NEEDS_ATTENTION = 6
    DEGRADED = 7
    DEPROVISIONING = 8

  details = _messages.MessageField('ServiceMeshStatusDetails', 1, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class ServiceMeshMembershipSpec(_messages.Message):
  r"""**Service Mesh**: Spec for a single Membership for the servicemesh
  feature

  Enums:
    ConfigApiValueValuesEnum: Optional. Specifies the API that will be used
      for configuring the mesh workloads.
    ControlPlaneValueValuesEnum: Deprecated: use `management` instead Enables
      automatic control plane management.
    ManagementValueValuesEnum: Optional. Enables automatic Service Mesh
      management.

  Fields:
    configApi: Optional. Specifies the API that will be used for configuring
      the mesh workloads.
    controlPlane: Deprecated: use `management` instead Enables automatic
      control plane management.
    management: Optional. Enables automatic Service Mesh management.
  """

  class ConfigApiValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies the API that will be used for configuring the mesh
    workloads.

    Values:
      CONFIG_API_UNSPECIFIED: Unspecified
      CONFIG_API_ISTIO: Use the Istio API for configuration.
      CONFIG_API_GATEWAY: Use the K8s Gateway API for configuration.
    """
    CONFIG_API_UNSPECIFIED = 0
    CONFIG_API_ISTIO = 1
    CONFIG_API_GATEWAY = 2

  class ControlPlaneValueValuesEnum(_messages.Enum):
    r"""Deprecated: use `management` instead Enables automatic control plane
    management.

    Values:
      CONTROL_PLANE_MANAGEMENT_UNSPECIFIED: Unspecified
      AUTOMATIC: Google should provision a control plane revision and make it
        available in the cluster. Google will enroll this revision in a
        release channel and keep it up to date. The control plane revision may
        be a managed service, or a managed install.
      MANUAL: User will manually configure the control plane (e.g. via CLI, or
        via the ControlPlaneRevision KRM API)
    """
    CONTROL_PLANE_MANAGEMENT_UNSPECIFIED = 0
    AUTOMATIC = 1
    MANUAL = 2

  class ManagementValueValuesEnum(_messages.Enum):
    r"""Optional. Enables automatic Service Mesh management.

    Values:
      MANAGEMENT_UNSPECIFIED: Unspecified
      MANAGEMENT_AUTOMATIC: Google should manage my Service Mesh for the
        cluster.
      MANAGEMENT_MANUAL: User will manually configure their service mesh
        components.
      MANAGEMENT_NOT_INSTALLED: Google should remove any managed Service Mesh
        components from this cluster and deprovision any resources.
    """
    MANAGEMENT_UNSPECIFIED = 0
    MANAGEMENT_AUTOMATIC = 1
    MANAGEMENT_MANUAL = 2
    MANAGEMENT_NOT_INSTALLED = 3

  configApi = _messages.EnumField('ConfigApiValueValuesEnum', 1)
  controlPlane = _messages.EnumField('ControlPlaneValueValuesEnum', 2)
  management = _messages.EnumField('ManagementValueValuesEnum', 3)


class ServiceMeshMembershipState(_messages.Message):
  r"""**Service Mesh**: State for a single Membership, as analyzed by the
  Service Mesh Hub Controller.

  Fields:
    conditions: Output only. List of conditions reported for this membership.
    controlPlaneManagement: Output only. Status of control plane management
    dataPlaneManagement: Output only. Status of data plane management.
  """

  conditions = _messages.MessageField('ServiceMeshCondition', 1, repeated=True)
  controlPlaneManagement = _messages.MessageField('ServiceMeshControlPlaneManagement', 2)
  dataPlaneManagement = _messages.MessageField('ServiceMeshDataPlaneManagement', 3)


class ServiceMeshStatusDetails(_messages.Message):
  r"""Structured and human-readable details for a status.

  Fields:
    code: A machine-readable code that further describes a broad status.
    details: Human-readable explanation of code.
  """

  code = _messages.StringField(1)
  details = _messages.StringField(2)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""Status specifies state for the subcomponent.

  Enums:
    CodeValueValuesEnum: Code specifies AppDevExperienceFeature's subcomponent
      ready state.

  Fields:
    code: Code specifies AppDevExperienceFeature's subcomponent ready state.
    description: Description is populated if Code is Failed, explaining why it
      has failed.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Code specifies AppDevExperienceFeature's subcomponent ready state.

    Values:
      CODE_UNSPECIFIED: Not set.
      OK: AppDevExperienceFeature's specified subcomponent is ready.
      FAILED: AppDevExperienceFeature's specified subcomponent ready state is
        false. This means AppDevExperienceFeature has encountered an issue
        that blocks all, or a portion, of its normal operation. See the
        `description` for more details.
      UNKNOWN: AppDevExperienceFeature's specified subcomponent has a pending
        or unknown state.
    """
    CODE_UNSPECIFIED = 0
    OK = 1
    FAILED = 2
    UNKNOWN = 3

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  description = _messages.StringField(2)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TypeMeta(_messages.Message):
  r"""TypeMeta is the type information needed for content unmarshalling of
  Kubernetes resources in the manifest.

  Fields:
    apiVersion: APIVersion of the resource (e.g. v1).
    kind: Kind of the resource (e.g. Deployment).
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)


class WorkloadMigrationFeatureSpec(_messages.Message):
  r"""**WorkloadMigration**: The Hub-wide input for the WorkloadMigration
  feature. This is currently empty, but is used to restrict API visibility.
  """



encoding.AddCustomJsonFieldMapping(
    Rule, 'in_', 'in')
encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    GkehubProjectsLocationsFeaturesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    GkehubProjectsLocationsMembershipsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    GkehubProjectsLocationsScopesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
