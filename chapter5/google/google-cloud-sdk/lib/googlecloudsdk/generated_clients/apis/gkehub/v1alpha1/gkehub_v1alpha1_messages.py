"""Generated message classes for gkehub version v1alpha1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'gkehub'


class AppDevExperienceFeatureSpec(_messages.Message):
  r"""Spec for App Dev Experience Feature."""


class AppDevExperienceFeatureState(_messages.Message):
  r"""State for App Dev Exp Feature.

  Fields:
    networkingInstallSucceeded: Status of subcomponent that detects configured
      Service Mesh resources.
  """

  networkingInstallSucceeded = _messages.MessageField('Status', 1)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    ignoreChildExemptions: A boolean attribute.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  ignoreChildExemptions = _messages.BooleanField(2)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 3)


class AuthMethod(_messages.Message):
  r"""Configuration of an auth method for a member/cluster. Only one
  authentication method (e.g., OIDC and LDAP) can be set per AuthMethod.

  Fields:
    azureadConfig: AzureAD specific configuration.
    googleConfig: GoogleConfig specific configuration
    ldapConfig: Optional. LDAP specific configuration.
    name: Identifier for auth config.
    oidcConfig: OIDC specific configuration.
    proxy: Proxy server address to use for auth method.
    samlConfig: Optional. SAML specific configuration.
  """

  azureadConfig = _messages.MessageField('AzureADConfig', 1)
  googleConfig = _messages.MessageField('GoogleConfig', 2)
  ldapConfig = _messages.MessageField('LdapConfig', 3)
  name = _messages.StringField(4)
  oidcConfig = _messages.MessageField('OidcConfig', 5)
  proxy = _messages.StringField(6)
  samlConfig = _messages.MessageField('SamlConfig', 7)


class AuthorizationLoggingOptions(_messages.Message):
  r"""Authorization-related information used by Cloud Audit Logging.

  Enums:
    PermissionTypeValueValuesEnum: The type of the permission that was
      checked.

  Fields:
    permissionType: The type of the permission that was checked.
  """

  class PermissionTypeValueValuesEnum(_messages.Enum):
    r"""The type of the permission that was checked.

    Values:
      PERMISSION_TYPE_UNSPECIFIED: Default. Should not be used.
      ADMIN_READ: A read of admin (meta) data.
      ADMIN_WRITE: A write of admin (meta) data.
      DATA_READ: A read of standard data.
      DATA_WRITE: A write of standard data.
    """
    PERMISSION_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    ADMIN_WRITE = 2
    DATA_READ = 3
    DATA_WRITE = 4

  permissionType = _messages.EnumField('PermissionTypeValueValuesEnum', 1)


class AuthorizerFeatureSpec(_messages.Message):
  r"""AuthorizerFeatureSpec contains options and specifications for the
  Authorizer Feature.
  """



class AuthorizerFeatureState(_messages.Message):
  r"""AuthorizerFeatureState contains the current detailed state of the
  Authorizer Feature.
  """



class AzureADConfig(_messages.Message):
  r"""Configuration for the AzureAD Auth flow.

  Fields:
    clientId: ID for the registered client application that makes
      authentication requests to the Azure AD identity provider.
    clientSecret: Input only. Unencrypted AzureAD client secret will be passed
      to the GKE Hub CLH.
    encryptedClientSecret: Output only. Encrypted AzureAD client secret.
    groupFormat: Optional. Format of the AzureAD groups that the client wants
      for auth.
    kubectlRedirectUri: The redirect URL that kubectl uses for authorization.
    tenant: Kind of Azure AD account to be authenticated. Supported values are
      or for accounts belonging to a specific tenant.
    userClaim: Optional. Claim in the AzureAD ID Token that holds the user
      details.
  """

  clientId = _messages.StringField(1)
  clientSecret = _messages.StringField(2)
  encryptedClientSecret = _messages.BytesField(3)
  groupFormat = _messages.StringField(4)
  kubectlRedirectUri = _messages.StringField(5)
  tenant = _messages.StringField(6)
  userClaim = _messages.StringField(7)


class BinauthzConfig(_messages.Message):
  r"""Configuration for Binauthz

  Fields:
    enabled: Whether binauthz is enabled in this cluster.
  """

  enabled = _messages.BooleanField(1)


class BinauthzState(_messages.Message):
  r"""State for Binauthz

  Enums:
    WebhookValueValuesEnum: The state of the binauthz webhook.

  Fields:
    version: The version of binauthz that is installed.
    webhook: The state of the binauthz webhook.
  """

  class WebhookValueValuesEnum(_messages.Enum):
    r"""The state of the binauthz webhook.

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  version = _messages.MessageField('BinauthzVersion', 1)
  webhook = _messages.EnumField('WebhookValueValuesEnum', 2)


class BinauthzVersion(_messages.Message):
  r"""The version of binauthz.

  Fields:
    webhookVersion: The version of the binauthz webhook.
  """

  webhookVersion = _messages.StringField(1)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    bindingId: A string attribute.
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  bindingId = _messages.StringField(1)
  condition = _messages.MessageField('Expr', 2)
  members = _messages.StringField(3, repeated=True)
  role = _messages.StringField(4)


class BundleInstallSpec(_messages.Message):
  r"""BundleInstallSpec is the specification configuration for a single
  managed bundle.

  Fields:
    exemptedNamespaces: The set of namespaces to be exempted from the bundle.
  """

  exemptedNamespaces = _messages.StringField(1, repeated=True)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CloudAuditLoggingFeatureSpec(_messages.Message):
  r"""Spec for Audit Logging Allowlisting.

  Fields:
    allowlistedServiceAccounts: Service account that should be allowlisted to
      send the audit logs; eg cloudauditlogging@gcp-
      project.iam.gserviceaccount.com. These accounts must already exist, but
      do not need to have any permissions granted to them. The customer's
      entitlements will be checked prior to allowlisting (i.e. the customer
      must be an Anthos customer.)
  """

  allowlistedServiceAccounts = _messages.StringField(1, repeated=True)


class CloudAuditLoggingFeatureState(_messages.Message):
  r"""An empty state for Audit Logging Allowlisting. This is required since
  FeatureStateDetails requires a state.
  """



class CloudAuditOptions(_messages.Message):
  r"""Write a Cloud Audit log

  Enums:
    LogNameValueValuesEnum: The log_name to populate in the Cloud Audit
      Record.
    PermissionTypeValueValuesEnum: The type associated with the permission.

  Fields:
    authorizationLoggingOptions: Information used by the Cloud Audit Logging
      pipeline. Will be deprecated once the migration to PermissionType is
      complete (b/*********).
    logName: The log_name to populate in the Cloud Audit Record.
    permissionType: The type associated with the permission.
  """

  class LogNameValueValuesEnum(_messages.Enum):
    r"""The log_name to populate in the Cloud Audit Record.

    Values:
      UNSPECIFIED_LOG_NAME: Default. Should not be used.
      ADMIN_ACTIVITY: Corresponds to "cloudaudit.googleapis.com/activity"
      DATA_ACCESS: Corresponds to "cloudaudit.googleapis.com/data_access"
    """
    UNSPECIFIED_LOG_NAME = 0
    ADMIN_ACTIVITY = 1
    DATA_ACCESS = 2

  class PermissionTypeValueValuesEnum(_messages.Enum):
    r"""The type associated with the permission.

    Values:
      PERMISSION_TYPE_UNSPECIFIED: Default. Should not be used.
      ADMIN_READ: Permissions that gate reading resource configuration or
        metadata.
      ADMIN_WRITE: Permissions that gate modification of resource
        configuration or metadata.
      DATA_READ: Permissions that gate reading user-provided data.
      DATA_WRITE: Permissions that gate writing user-provided data.
    """
    PERMISSION_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    ADMIN_WRITE = 2
    DATA_READ = 3
    DATA_WRITE = 4

  authorizationLoggingOptions = _messages.MessageField('AuthorizationLoggingOptions', 1)
  logName = _messages.EnumField('LogNameValueValuesEnum', 2)
  permissionType = _messages.EnumField('PermissionTypeValueValuesEnum', 3)


class CloudBuildFeatureSpec(_messages.Message):
  r"""Cloud Build for Anthos feature spec. This is required since Feature
  proto requires a spec.

  Messages:
    MembershipConfigsValue: The map from membership path (e.g. projects/foo-
      proj/locations/global/memberships/bar) to the CloudBuildMembershipConfig
      that is chosen for that member cluster. If CloudBuild feature is enabled
      for a hub and the membership path of a cluster in that hub exists in
      this map then it has Cloud Build hub feature enabled for that particular
      cluster.

  Fields:
    membershipConfigs: The map from membership path (e.g. projects/foo-
      proj/locations/global/memberships/bar) to the CloudBuildMembershipConfig
      that is chosen for that member cluster. If CloudBuild feature is enabled
      for a hub and the membership path of a cluster in that hub exists in
      this map then it has Cloud Build hub feature enabled for that particular
      cluster.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MembershipConfigsValue(_messages.Message):
    r"""The map from membership path (e.g. projects/foo-
    proj/locations/global/memberships/bar) to the CloudBuildMembershipConfig
    that is chosen for that member cluster. If CloudBuild feature is enabled
    for a hub and the membership path of a cluster in that hub exists in this
    map then it has Cloud Build hub feature enabled for that particular
    cluster.

    Messages:
      AdditionalProperty: An additional property for a MembershipConfigsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        MembershipConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MembershipConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A CloudBuildMembershipConfig attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('CloudBuildMembershipConfig', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  membershipConfigs = _messages.MessageField('MembershipConfigsValue', 1)


class CloudBuildMembershipConfig(_messages.Message):
  r"""Configurations for each Cloud Build enabled cluster.

  Enums:
    SecurityPolicyValueValuesEnum: Whether it is allowed to run the privileged
      builds on the cluster or not.

  Fields:
    securityPolicy: Whether it is allowed to run the privileged builds on the
      cluster or not.
    version: Version of the cloud build software on the cluster.
  """

  class SecurityPolicyValueValuesEnum(_messages.Enum):
    r"""Whether it is allowed to run the privileged builds on the cluster or
    not.

    Values:
      SECURITY_POLICY_UNSPECIFIED: Unspecified policy
      NON_PRIVILEGED: Privileged build pods are disallowed
      PRIVILEGED: Privileged build pods are allowed
    """
    SECURITY_POLICY_UNSPECIFIED = 0
    NON_PRIVILEGED = 1
    PRIVILEGED = 2

  securityPolicy = _messages.EnumField('SecurityPolicyValueValuesEnum', 1)
  version = _messages.StringField(2)


class Condition(_messages.Message):
  r"""Condition being reported.

  Enums:
    CodeValueValuesEnum: Unique identifier of the condition which describes
      the condition recognizable to the user.
    SeverityValueValuesEnum: Severity level of the condition.

  Fields:
    code: Unique identifier of the condition which describes the condition
      recognizable to the user.
    details: A short summary about the issue.
    documentationLink: Links contains actionable information.
    severity: Severity level of the condition.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Unique identifier of the condition which describes the condition
    recognizable to the user.

    Values:
      CODE_UNSPECIFIED: Default Unspecified code
      MESH_IAM_PERMISSION_DENIED: Mesh IAM permission denied error code
      MESH_IAM_CROSS_PROJECT_PERMISSION_DENIED: Permission denied error code
        for cross-project
      CNI_CONFIG_UNSUPPORTED: CNI config unsupported error code
      GKE_SANDBOX_UNSUPPORTED: GKE sandbox unsupported error code
      NODEPOOL_WORKLOAD_IDENTITY_FEDERATION_REQUIRED: Nodepool workload
        identity federation required error code
      CNI_INSTALLATION_FAILED: CNI installation failed error code
      CNI_POD_UNSCHEDULABLE: CNI pod unschedulable error code
      CLUSTER_HAS_ZERO_NODES: Cluster has zero node code
      CANONICAL_SERVICE_ERROR: Failure to reconcile CanonicalServices
      UNSUPPORTED_MULTIPLE_CONTROL_PLANES: Multiple control planes unsupported
        error code
      VPCSC_GA_SUPPORTED: VPC-SC GA is supported for this control plane.
      DEPRECATED_SPEC_CONTROL_PLANE_MANAGEMENT: User is using deprecated
        ControlPlaneManagement and they have not yet set Management.
      DEPRECATED_SPEC_CONTROL_PLANE_MANAGEMENT_SAFE: User is using deprecated
        ControlPlaneManagement and they have already set Management.
      CONFIG_APPLY_INTERNAL_ERROR: Configuration (Istio/k8s resources) failed
        to apply due to internal error.
      CONFIG_VALIDATION_ERROR: Configuration failed to be applied due to being
        invalid.
      CONFIG_VALIDATION_WARNING: Encountered configuration(s) with possible
        unintended behavior or invalid configuration. These configs may not
        have been applied.
      QUOTA_EXCEEDED_BACKEND_SERVICES: BackendService quota exceeded error
        code.
      QUOTA_EXCEEDED_HEALTH_CHECKS: HealthCheck quota exceeded error code.
      QUOTA_EXCEEDED_HTTP_ROUTES: HTTPRoute quota exceeded error code.
      QUOTA_EXCEEDED_TCP_ROUTES: TCPRoute quota exceeded error code.
      QUOTA_EXCEEDED_TLS_ROUTES: TLS routes quota exceeded error code.
      QUOTA_EXCEEDED_TRAFFIC_POLICIES: TrafficPolicy quota exceeded error
        code.
      QUOTA_EXCEEDED_ENDPOINT_POLICIES: EndpointPolicy quota exceeded error
        code.
      QUOTA_EXCEEDED_GATEWAYS: Gateway quota exceeded error code.
      QUOTA_EXCEEDED_MESHES: Mesh quota exceeded error code.
      QUOTA_EXCEEDED_SERVER_TLS_POLICIES: ServerTLSPolicy quota exceeded error
        code.
      QUOTA_EXCEEDED_CLIENT_TLS_POLICIES: ClientTLSPolicy quota exceeded error
        code.
      QUOTA_EXCEEDED_SERVICE_LB_POLICIES: ServiceLBPolicy quota exceeded error
        code.
      QUOTA_EXCEEDED_HTTP_FILTERS: HTTPFilter quota exceeded error code.
      QUOTA_EXCEEDED_TCP_FILTERS: TCPFilter quota exceeded error code.
      QUOTA_EXCEEDED_NETWORK_ENDPOINT_GROUPS: NetworkEndpointGroup quota
        exceeded error code.
      LEGACY_MC_SECRETS: Legacy istio secrets found for multicluster error
        code
      WORKLOAD_IDENTITY_REQUIRED: Workload identity required error code
      NON_STANDARD_BINARY_USAGE: Non-standard binary usage error code
      UNSUPPORTED_GATEWAY_CLASS: Unsupported gateway class error code
      MANAGED_CNI_NOT_ENABLED: Managed CNI not enabled error code
      MODERNIZATION_SCHEDULED: Modernization is scheduled for a cluster.
      MODERNIZATION_IN_PROGRESS: Modernization is in progress for a cluster.
      MODERNIZATION_COMPLETED: Modernization is completed for a cluster.
      MODERNIZATION_ABORTED: Modernization is aborted for a cluster.
      MODERNIZATION_WILL_BE_SCHEDULED: Modernization will be scheduled for a
        fleet.
    """
    CODE_UNSPECIFIED = 0
    MESH_IAM_PERMISSION_DENIED = 1
    MESH_IAM_CROSS_PROJECT_PERMISSION_DENIED = 2
    CNI_CONFIG_UNSUPPORTED = 3
    GKE_SANDBOX_UNSUPPORTED = 4
    NODEPOOL_WORKLOAD_IDENTITY_FEDERATION_REQUIRED = 5
    CNI_INSTALLATION_FAILED = 6
    CNI_POD_UNSCHEDULABLE = 7
    CLUSTER_HAS_ZERO_NODES = 8
    CANONICAL_SERVICE_ERROR = 9
    UNSUPPORTED_MULTIPLE_CONTROL_PLANES = 10
    VPCSC_GA_SUPPORTED = 11
    DEPRECATED_SPEC_CONTROL_PLANE_MANAGEMENT = 12
    DEPRECATED_SPEC_CONTROL_PLANE_MANAGEMENT_SAFE = 13
    CONFIG_APPLY_INTERNAL_ERROR = 14
    CONFIG_VALIDATION_ERROR = 15
    CONFIG_VALIDATION_WARNING = 16
    QUOTA_EXCEEDED_BACKEND_SERVICES = 17
    QUOTA_EXCEEDED_HEALTH_CHECKS = 18
    QUOTA_EXCEEDED_HTTP_ROUTES = 19
    QUOTA_EXCEEDED_TCP_ROUTES = 20
    QUOTA_EXCEEDED_TLS_ROUTES = 21
    QUOTA_EXCEEDED_TRAFFIC_POLICIES = 22
    QUOTA_EXCEEDED_ENDPOINT_POLICIES = 23
    QUOTA_EXCEEDED_GATEWAYS = 24
    QUOTA_EXCEEDED_MESHES = 25
    QUOTA_EXCEEDED_SERVER_TLS_POLICIES = 26
    QUOTA_EXCEEDED_CLIENT_TLS_POLICIES = 27
    QUOTA_EXCEEDED_SERVICE_LB_POLICIES = 28
    QUOTA_EXCEEDED_HTTP_FILTERS = 29
    QUOTA_EXCEEDED_TCP_FILTERS = 30
    QUOTA_EXCEEDED_NETWORK_ENDPOINT_GROUPS = 31
    LEGACY_MC_SECRETS = 32
    WORKLOAD_IDENTITY_REQUIRED = 33
    NON_STANDARD_BINARY_USAGE = 34
    UNSUPPORTED_GATEWAY_CLASS = 35
    MANAGED_CNI_NOT_ENABLED = 36
    MODERNIZATION_SCHEDULED = 37
    MODERNIZATION_IN_PROGRESS = 38
    MODERNIZATION_COMPLETED = 39
    MODERNIZATION_ABORTED = 40
    MODERNIZATION_WILL_BE_SCHEDULED = 41

  class SeverityValueValuesEnum(_messages.Enum):
    r"""Severity level of the condition.

    Values:
      SEVERITY_UNSPECIFIED: Unspecified severity
      ERROR: Indicates an issue that prevents the mesh from operating
        correctly
      WARNING: Indicates a setting is likely wrong, but the mesh is still able
        to operate
      INFO: An informational message, not requiring any action
    """
    SEVERITY_UNSPECIFIED = 0
    ERROR = 1
    WARNING = 2
    INFO = 3

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  details = _messages.StringField(2)
  documentationLink = _messages.StringField(3)
  severity = _messages.EnumField('SeverityValueValuesEnum', 4)


class ConfigManagementFeatureSpec(_messages.Message):
  r"""Spec for Anthos Config Management (ACM).

  Messages:
    MembershipConfigsValue: Map of Membership IDs to individual configs.

  Fields:
    membershipConfigs: Map of Membership IDs to individual configs.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MembershipConfigsValue(_messages.Message):
    r"""Map of Membership IDs to individual configs.

    Messages:
      AdditionalProperty: An additional property for a MembershipConfigsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        MembershipConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MembershipConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A MembershipConfig attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('MembershipConfig', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  membershipConfigs = _messages.MessageField('MembershipConfigsValue', 1)


class ConfigManagementFeatureState(_messages.Message):
  r"""State for Anthos Config Management

  Fields:
    binauthzState: Binauthz status
    clusterName: This field is set to the `cluster_name` field of the
      Membership Spec if it is not empty. Otherwise, it is set to the
      cluster's fleet membership name.
    configSyncState: Current sync status
    hierarchyControllerState: Hierarchy Controller status
    membershipConfig: Membership configuration in the cluster. This represents
      the actual state in the cluster, while the MembershipConfig in the
      FeatureSpec represents the intended state
    operatorState: Current install status of ACM's Operator
    policyControllerState: PolicyController status
  """

  binauthzState = _messages.MessageField('BinauthzState', 1)
  clusterName = _messages.StringField(2)
  configSyncState = _messages.MessageField('ConfigSyncState', 3)
  hierarchyControllerState = _messages.MessageField('HierarchyControllerState', 4)
  membershipConfig = _messages.MessageField('MembershipConfig', 5)
  operatorState = _messages.MessageField('OperatorState', 6)
  policyControllerState = _messages.MessageField('PolicyControllerState', 7)


class ConfigSync(_messages.Message):
  r"""Configuration for Config Sync

  Fields:
    deploymentOverrides: Optional. Configuration for deployment overrides.
    enabled: Enables the installation of ConfigSync. If set to true,
      ConfigSync resources will be created and the other ConfigSync fields
      will be applied if exist. If set to false, all other ConfigSync fields
      will be ignored, ConfigSync resources will be deleted. If omitted,
      ConfigSync resources will be managed depends on the presence of the git
      or oci field.
    git: Git repo configuration for the cluster.
    metricsGcpServiceAccountEmail: The Email of the Google Cloud Service
      Account (GSA) used for exporting Config Sync metrics to Cloud Monitoring
      and Cloud Monarch when Workload Identity is enabled. The GSA should have
      the Monitoring Metric Writer (roles/monitoring.metricWriter) IAM role.
      The Kubernetes ServiceAccount `default` in the namespace `config-
      management-monitoring` should be bound to the GSA. Deprecated: If
      Workload Identity Federation for GKE is enabled, Google Cloud Service
      Account is no longer needed for exporting Config Sync metrics:
      https://cloud.google.com/kubernetes-engine/enterprise/config-
      sync/docs/how-to/monitor-config-sync-cloud-monitoring#custom-monitoring.
    oci: OCI repo configuration for the cluster
    preventDrift: Set to true to enable the Config Sync admission webhook to
      prevent drifts. Defaults to false which disables the Config Sync
      admission webhook and does not prevent drifts.
    sourceFormat: Specifies whether the Config Sync Repo is in "hierarchical"
      or "unstructured" mode.
    stopSyncing: Set to true to stop syncing configs for a single cluster.
      Default to false.
  """

  deploymentOverrides = _messages.MessageField('DeploymentOverride', 1, repeated=True)
  enabled = _messages.BooleanField(2)
  git = _messages.MessageField('GitConfig', 3)
  metricsGcpServiceAccountEmail = _messages.StringField(4)
  oci = _messages.MessageField('OciConfig', 5)
  preventDrift = _messages.BooleanField(6)
  sourceFormat = _messages.StringField(7)
  stopSyncing = _messages.BooleanField(8)


class ConfigSyncDeploymentState(_messages.Message):
  r"""The state of ConfigSync's deployment on a cluster

  Enums:
    AdmissionWebhookValueValuesEnum: Deployment state of admission-webhook
    GitSyncValueValuesEnum: Deployment state of the git-sync pod
    ImporterValueValuesEnum: Deployment state of the importer pod
    MonitorValueValuesEnum: Deployment state of the monitor pod
    OtelCollectorValueValuesEnum: Deployment state of otel-collector
    ReconcilerManagerValueValuesEnum: Deployment state of reconciler-manager
      pod
    ResourceGroupControllerManagerValueValuesEnum: Deployment state of
      resource-group-controller-manager
    RootReconcilerValueValuesEnum: Deployment state of root-reconciler
    SyncerValueValuesEnum: Deployment state of the syncer pod

  Fields:
    admissionWebhook: Deployment state of admission-webhook
    gitSync: Deployment state of the git-sync pod
    importer: Deployment state of the importer pod
    monitor: Deployment state of the monitor pod
    otelCollector: Deployment state of otel-collector
    reconcilerManager: Deployment state of reconciler-manager pod
    resourceGroupControllerManager: Deployment state of resource-group-
      controller-manager
    rootReconciler: Deployment state of root-reconciler
    syncer: Deployment state of the syncer pod
  """

  class AdmissionWebhookValueValuesEnum(_messages.Enum):
    r"""Deployment state of admission-webhook

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class GitSyncValueValuesEnum(_messages.Enum):
    r"""Deployment state of the git-sync pod

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class ImporterValueValuesEnum(_messages.Enum):
    r"""Deployment state of the importer pod

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class MonitorValueValuesEnum(_messages.Enum):
    r"""Deployment state of the monitor pod

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class OtelCollectorValueValuesEnum(_messages.Enum):
    r"""Deployment state of otel-collector

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class ReconcilerManagerValueValuesEnum(_messages.Enum):
    r"""Deployment state of reconciler-manager pod

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class ResourceGroupControllerManagerValueValuesEnum(_messages.Enum):
    r"""Deployment state of resource-group-controller-manager

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class RootReconcilerValueValuesEnum(_messages.Enum):
    r"""Deployment state of root-reconciler

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class SyncerValueValuesEnum(_messages.Enum):
    r"""Deployment state of the syncer pod

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  admissionWebhook = _messages.EnumField('AdmissionWebhookValueValuesEnum', 1)
  gitSync = _messages.EnumField('GitSyncValueValuesEnum', 2)
  importer = _messages.EnumField('ImporterValueValuesEnum', 3)
  monitor = _messages.EnumField('MonitorValueValuesEnum', 4)
  otelCollector = _messages.EnumField('OtelCollectorValueValuesEnum', 5)
  reconcilerManager = _messages.EnumField('ReconcilerManagerValueValuesEnum', 6)
  resourceGroupControllerManager = _messages.EnumField('ResourceGroupControllerManagerValueValuesEnum', 7)
  rootReconciler = _messages.EnumField('RootReconcilerValueValuesEnum', 8)
  syncer = _messages.EnumField('SyncerValueValuesEnum', 9)


class ConfigSyncError(_messages.Message):
  r"""Errors pertaining to the installation of Config Sync

  Fields:
    errorMessage: A string representing the user facing error message
  """

  errorMessage = _messages.StringField(1)


class ConfigSyncState(_messages.Message):
  r"""State information for ConfigSync

  Enums:
    ClusterLevelStopSyncingStateValueValuesEnum: Whether syncing resources to
      the cluster is stopped at the cluster level.
    ReposyncCrdValueValuesEnum: The state of the Reposync CRD
    RootsyncCrdValueValuesEnum: The state of the RootSync CRD
    StateValueValuesEnum: The state of CS This field summarizes the other
      fields in this message.

  Fields:
    clusterLevelStopSyncingState: Whether syncing resources to the cluster is
      stopped at the cluster level.
    crCount: Output only. The number of RootSync and RepoSync CRs in the
      cluster.
    deploymentState: Information about the deployment of ConfigSync, including
      the version of the various Pods deployed
    errors: Errors pertaining to the installation of Config Sync.
    reposyncCrd: The state of the Reposync CRD
    rootsyncCrd: The state of the RootSync CRD
    state: The state of CS This field summarizes the other fields in this
      message.
    syncState: The state of ConfigSync's process to sync configs to a cluster
    version: The version of ConfigSync deployed
  """

  class ClusterLevelStopSyncingStateValueValuesEnum(_messages.Enum):
    r"""Whether syncing resources to the cluster is stopped at the cluster
    level.

    Values:
      STOP_SYNCING_STATE_UNSPECIFIED: State cannot be determined
      NOT_STOPPED: Syncing resources to the cluster is not stopped at the
        cluster level.
      PENDING: Some reconcilers stop syncing resources to the cluster, while
        others are still syncing.
      STOPPED: Syncing resources to the cluster is stopped at the cluster
        level.
    """
    STOP_SYNCING_STATE_UNSPECIFIED = 0
    NOT_STOPPED = 1
    PENDING = 2
    STOPPED = 3

  class ReposyncCrdValueValuesEnum(_messages.Enum):
    r"""The state of the Reposync CRD

    Values:
      CRD_STATE_UNSPECIFIED: CRD's state cannot be determined
      NOT_INSTALLED: CRD is not installed
      INSTALLED: CRD is installed
      TERMINATING: CRD is terminating (i.e., it has been deleted and is
        cleaning up)
      INSTALLING: CRD is installing
    """
    CRD_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    TERMINATING = 3
    INSTALLING = 4

  class RootsyncCrdValueValuesEnum(_messages.Enum):
    r"""The state of the RootSync CRD

    Values:
      CRD_STATE_UNSPECIFIED: CRD's state cannot be determined
      NOT_INSTALLED: CRD is not installed
      INSTALLED: CRD is installed
      TERMINATING: CRD is terminating (i.e., it has been deleted and is
        cleaning up)
      INSTALLING: CRD is installing
    """
    CRD_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    TERMINATING = 3
    INSTALLING = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of CS This field summarizes the other fields in this
    message.

    Values:
      STATE_UNSPECIFIED: CS's state cannot be determined.
      CONFIG_SYNC_NOT_INSTALLED: CS is not installed.
      CONFIG_SYNC_INSTALLED: The expected CS version is installed
        successfully.
      CONFIG_SYNC_ERROR: CS encounters errors.
      CONFIG_SYNC_PENDING: CS is installing or terminating.
    """
    STATE_UNSPECIFIED = 0
    CONFIG_SYNC_NOT_INSTALLED = 1
    CONFIG_SYNC_INSTALLED = 2
    CONFIG_SYNC_ERROR = 3
    CONFIG_SYNC_PENDING = 4

  clusterLevelStopSyncingState = _messages.EnumField('ClusterLevelStopSyncingStateValueValuesEnum', 1)
  crCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  deploymentState = _messages.MessageField('ConfigSyncDeploymentState', 3)
  errors = _messages.MessageField('ConfigSyncError', 4, repeated=True)
  reposyncCrd = _messages.EnumField('ReposyncCrdValueValuesEnum', 5)
  rootsyncCrd = _messages.EnumField('RootsyncCrdValueValuesEnum', 6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  syncState = _messages.MessageField('SyncState', 8)
  version = _messages.MessageField('ConfigSyncVersion', 9)


class ConfigSyncVersion(_messages.Message):
  r"""Specific versioning information pertaining to ConfigSync's Pods

  Fields:
    admissionWebhook: Version of the deployed admission-webhook pod
    gitSync: Version of the deployed git-sync pod
    importer: Version of the deployed importer pod
    monitor: Version of the deployed monitor pod
    otelCollector: Version of the deployed otel-collector pod
    reconcilerManager: Version of the deployed reconciler-manager pod
    resourceGroupControllerManager: Version of the deployed resource-group-
      controller-manager pod
    rootReconciler: Version of the deployed reconciler container in root-
      reconciler pod
    syncer: Version of the deployed syncer pod
  """

  admissionWebhook = _messages.StringField(1)
  gitSync = _messages.StringField(2)
  importer = _messages.StringField(3)
  monitor = _messages.StringField(4)
  otelCollector = _messages.StringField(5)
  reconcilerManager = _messages.StringField(6)
  resourceGroupControllerManager = _messages.StringField(7)
  rootReconciler = _messages.StringField(8)
  syncer = _messages.StringField(9)


class ContainerOverride(_messages.Message):
  r"""Configuration for a container override.

  Fields:
    containerName: Required. The name of the container.
    cpuLimit: The cpu limit of the container.
    cpuRequest: The cpu request of the container.
    memoryLimit: The memory limit of the container.
    memoryRequest: The memory request of the container.
  """

  containerName = _messages.StringField(1)
  cpuLimit = _messages.StringField(2)
  cpuRequest = _messages.StringField(3)
  memoryLimit = _messages.StringField(4)
  memoryRequest = _messages.StringField(5)


class ControlPlaneManagement(_messages.Message):
  r"""Status of control plane management. Only reported per-member.

  Enums:
    ImplementationValueValuesEnum: Output only. Implementation of managed
      control plane.
    StateValueValuesEnum: State of control plane management.

  Fields:
    details: Explanation of state.
    implementation: Output only. Implementation of managed control plane.
    state: State of control plane management.
  """

  class ImplementationValueValuesEnum(_messages.Enum):
    r"""Output only. Implementation of managed control plane.

    Values:
      IMPLEMENTATION_UNSPECIFIED: Unspecified
      ISTIOD: A Google build of istiod is used for the managed control plane.
      TRAFFIC_DIRECTOR: Traffic director is used for the managed control
        plane.
      UPDATING: The control plane implementation is being updated.
    """
    IMPLEMENTATION_UNSPECIFIED = 0
    ISTIOD = 1
    TRAFFIC_DIRECTOR = 2
    UPDATING = 3

  class StateValueValuesEnum(_messages.Enum):
    r"""State of control plane management.

    Values:
      LIFECYCLE_STATE_UNSPECIFIED: Unspecified
      DISABLED: DISABLED means that the component is not enabled.
      FAILED_PRECONDITION: FAILED_PRECONDITION means that provisioning cannot
        proceed because of some characteristic of the member cluster.
      PROVISIONING: PROVISIONING means that provisioning is in progress.
      ACTIVE: ACTIVE means that the component is ready for use.
      STALLED: STALLED means that provisioning could not be done.
      NEEDS_ATTENTION: NEEDS_ATTENTION means that the component is ready, but
        some user intervention is required. (For example that the user should
        migrate workloads to a new control plane revision.)
      DEGRADED: DEGRADED means that the component is ready, but operating in a
        degraded state.
      DEPROVISIONING: DEPROVISIONING means that deprovisioning is in progress.
    """
    LIFECYCLE_STATE_UNSPECIFIED = 0
    DISABLED = 1
    FAILED_PRECONDITION = 2
    PROVISIONING = 3
    ACTIVE = 4
    STALLED = 5
    NEEDS_ATTENTION = 6
    DEGRADED = 7
    DEPROVISIONING = 8

  details = _messages.MessageField('StatusDetails', 1, repeated=True)
  implementation = _messages.EnumField('ImplementationValueValuesEnum', 2)
  state = _messages.EnumField('StateValueValuesEnum', 3)


class ControlPlaneRevision(_messages.Message):
  r"""Status of a control plane revision that is intended to be available for
  use in the cluster.

  Enums:
    ChannelValueValuesEnum: Release Channel the managed control plane revision
      is subscribed to.
    StateValueValuesEnum: State of the control plane revision.
      LIFECYCLE_STATE_UNSPECIFIED, FAILED_PRECONDITION, PROVISIONING, ACTIVE,
      and STALLED are applicable here.
    TypeValueValuesEnum: Type of the control plane revision.

  Fields:
    channel: Release Channel the managed control plane revision is subscribed
      to.
    details: Explanation of the state.
    owner: Owner of the control plane revision.
    revision: Unique name of the control plane revision.
    state: State of the control plane revision. LIFECYCLE_STATE_UNSPECIFIED,
      FAILED_PRECONDITION, PROVISIONING, ACTIVE, and STALLED are applicable
      here.
    type: Type of the control plane revision.
    version: Static version of the control plane revision.
  """

  class ChannelValueValuesEnum(_messages.Enum):
    r"""Release Channel the managed control plane revision is subscribed to.

    Values:
      CHANNEL_UNSPECIFIED: Unspecified
      RAPID: RAPID channel is offered on an early access basis for customers
        who want to test new releases.
      REGULAR: REGULAR channel is intended for production users who want to
        take advantage of new features.
      STABLE: STABLE channel includes versions that are known to be stable and
        reliable in production.
    """
    CHANNEL_UNSPECIFIED = 0
    RAPID = 1
    REGULAR = 2
    STABLE = 3

  class StateValueValuesEnum(_messages.Enum):
    r"""State of the control plane revision. LIFECYCLE_STATE_UNSPECIFIED,
    FAILED_PRECONDITION, PROVISIONING, ACTIVE, and STALLED are applicable
    here.

    Values:
      LIFECYCLE_STATE_UNSPECIFIED: Unspecified
      DISABLED: DISABLED means that the component is not enabled.
      FAILED_PRECONDITION: FAILED_PRECONDITION means that provisioning cannot
        proceed because of some characteristic of the member cluster.
      PROVISIONING: PROVISIONING means that provisioning is in progress.
      ACTIVE: ACTIVE means that the component is ready for use.
      STALLED: STALLED means that provisioning could not be done.
      NEEDS_ATTENTION: NEEDS_ATTENTION means that the component is ready, but
        some user intervention is required. (For example that the user should
        migrate workloads to a new control plane revision.)
      DEGRADED: DEGRADED means that the component is ready, but operating in a
        degraded state.
      DEPROVISIONING: DEPROVISIONING means that deprovisioning is in progress.
    """
    LIFECYCLE_STATE_UNSPECIFIED = 0
    DISABLED = 1
    FAILED_PRECONDITION = 2
    PROVISIONING = 3
    ACTIVE = 4
    STALLED = 5
    NEEDS_ATTENTION = 6
    DEGRADED = 7
    DEPROVISIONING = 8

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of the control plane revision.

    Values:
      CONTROL_PLANE_REVISION_TYPE_UNSPECIFIED: Unspecified.
      UNMANAGED: User-installed in-cluster control plane revision.
      MANAGED_SERVICE: Google-managed service running outside the cluster.
        Note: Google-managed control planes are independent per-cluster,
        regardless of whether the revision name is the same or not.
      MANAGED_LOCAL: Google-managed local control plane revision.
    """
    CONTROL_PLANE_REVISION_TYPE_UNSPECIFIED = 0
    UNMANAGED = 1
    MANAGED_SERVICE = 2
    MANAGED_LOCAL = 3

  channel = _messages.EnumField('ChannelValueValuesEnum', 1)
  details = _messages.MessageField('StatusDetails', 2, repeated=True)
  owner = _messages.StringField(3)
  revision = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)
  type = _messages.EnumField('TypeValueValuesEnum', 6)
  version = _messages.StringField(7)


class CounterOptions(_messages.Message):
  r"""Increment a streamz counter with the specified metric and field names.
  Metric names should start with a '/', generally be lowercase-only, and end
  in "_count". Field names should not contain an initial slash. The actual
  exported metric names will have "/iam/policy" prepended. Field names
  correspond to IAM request parameters and field values are their respective
  values. Supported field names: - "authority", which is "[token]" if
  IAMContext.token is present, otherwise the value of
  IAMContext.authority_selector if present, and otherwise a representation of
  IAMContext.principal; or - "iam_principal", a representation of
  IAMContext.principal even if a token or authority selector is present; or -
  "" (empty string), resulting in a counter with no fields. Examples: counter
  { metric: "/debug_access_count" field: "iam_principal" } ==> increment
  counter /iam/policy/debug_access_count {iam_principal=[value of
  IAMContext.principal]}

  Fields:
    customFields: Custom fields.
    field: The field value to attribute.
    metric: The metric to update.
  """

  customFields = _messages.MessageField('CustomField', 1, repeated=True)
  field = _messages.StringField(2)
  metric = _messages.StringField(3)


class CreateReferenceRequest(_messages.Message):
  r"""The CreateReferenceRequest request.

  Fields:
    parent: Required. The parent resource name (target_resource of this
      reference). For example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}`.
    reference: Required. The reference to be created.
    referenceId: The unique id of this resource. Must be unique within a scope
      of a target resource, but does not have to be globally unique. Reference
      ID is part of resource name of the reference. Resource name is generated
      in the following way: {parent}/references/{reference_id}. Reference ID
      field is currently required but id auto generation might be added in the
      future. It can be any arbitrary string, either GUID or any other string,
      however CLHs can use preprocess callbacks to perform a custom
      validation.
    requestId: Optional. Request ID is an idempotency ID of the request. It
      must be a valid UUID. Zero UUID (00000000-0000-0000-0000-000000000000)
      is not supported.
  """

  parent = _messages.StringField(1)
  reference = _messages.MessageField('Reference', 2)
  referenceId = _messages.StringField(3)
  requestId = _messages.StringField(4)


class CustomField(_messages.Message):
  r"""Custom fields. These can be used to create a counter with arbitrary
  field/value pairs. See: go/rpcsp-custom-fields.

  Fields:
    name: Name is the field name.
    value: Value is the field value. It is important that in contrast to the
      CounterOptions.field, the value here is a constant that is not derived
      from the IAMContext.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class DataAccessOptions(_messages.Message):
  r"""Write a Data Access (Gin) log

  Enums:
    LogModeValueValuesEnum:

  Fields:
    isDirectAuth: Indicates that access was granted by a regular grant policy
    logMode: A LogModeValueValuesEnum attribute.
  """

  class LogModeValueValuesEnum(_messages.Enum):
    r"""LogModeValueValuesEnum enum type.

    Values:
      LOG_MODE_UNSPECIFIED: Client is not required to write a partial Gin log
        immediately after the authorization check. If client chooses to write
        one and it fails, client may either fail open (allow the operation to
        continue) or fail closed (handle as a DENY outcome).
      LOG_FAIL_CLOSED: The application's operation in the context of which
        this authorization check is being made may only be performed if it is
        successfully logged to Gin. For instance, the authorization library
        may satisfy this obligation by emitting a partial log entry at
        authorization check time and only returning ALLOW to the application
        if it succeeds. If a matching Rule has this directive, but the client
        has not indicated that it will honor such requirements, then the IAM
        check will result in authorization failure by setting
        CheckPolicyResponse.success=false.
    """
    LOG_MODE_UNSPECIFIED = 0
    LOG_FAIL_CLOSED = 1

  isDirectAuth = _messages.BooleanField(1)
  logMode = _messages.EnumField('LogModeValueValuesEnum', 2)


class DataPlaneManagement(_messages.Message):
  r"""Status of data plane management. Only reported per-member.

  Enums:
    StateValueValuesEnum: Lifecycle status of data plane management.

  Fields:
    details: Explanation of the status.
    state: Lifecycle status of data plane management.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Lifecycle status of data plane management.

    Values:
      LIFECYCLE_STATE_UNSPECIFIED: Unspecified
      DISABLED: DISABLED means that the component is not enabled.
      FAILED_PRECONDITION: FAILED_PRECONDITION means that provisioning cannot
        proceed because of some characteristic of the member cluster.
      PROVISIONING: PROVISIONING means that provisioning is in progress.
      ACTIVE: ACTIVE means that the component is ready for use.
      STALLED: STALLED means that provisioning could not be done.
      NEEDS_ATTENTION: NEEDS_ATTENTION means that the component is ready, but
        some user intervention is required. (For example that the user should
        migrate workloads to a new control plane revision.)
      DEGRADED: DEGRADED means that the component is ready, but operating in a
        degraded state.
      DEPROVISIONING: DEPROVISIONING means that deprovisioning is in progress.
    """
    LIFECYCLE_STATE_UNSPECIFIED = 0
    DISABLED = 1
    FAILED_PRECONDITION = 2
    PROVISIONING = 3
    ACTIVE = 4
    STALLED = 5
    NEEDS_ATTENTION = 6
    DEGRADED = 7
    DEPROVISIONING = 8

  details = _messages.MessageField('StatusDetails', 1, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class DataplaneV2FeatureSpec(_messages.Message):
  r"""Spec for multi-cluster dataplane-v2 feature. This is required since
  Feature proto requires a spec.

  Fields:
    enableEncryption: Enable dataplane-v2 based encryption for multiple
      clusters.
  """

  enableEncryption = _messages.BooleanField(1)


class DataplaneV2FeatureState(_messages.Message):
  r"""An empty state for multi-cluster dataplane-v2 feature. This is required
  since FeatureStateDetails requires a state.
  """



class DeleteReferenceRequest(_messages.Message):
  r"""The DeleteReferenceRequest request.

  Fields:
    name: Required. Full resource name of the reference, in the following
      format:
      `//{targer_service}/{target_resource}/references/{reference_id}`. For
      example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}/references/{xyz}`.
    requestId: Optional. Request ID is an idempotency ID of the request. It
      must be a valid UUID. Zero UUID (00000000-0000-0000-0000-000000000000)
      is not supported.
  """

  name = _messages.StringField(1)
  requestId = _messages.StringField(2)


class DeploymentOverride(_messages.Message):
  r"""Configuration for a deployment override.

  Fields:
    containers: Optional. The containers of the deployment resource to be
      overridden.
    deploymentName: Required. The name of the deployment resource to be
      overridden.
    deploymentNamespace: Required. The namespace of the deployment resource to
      be overridden.
  """

  containers = _messages.MessageField('ContainerOverride', 1, repeated=True)
  deploymentName = _messages.StringField(2)
  deploymentNamespace = _messages.StringField(3)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class ErrorResource(_messages.Message):
  r"""Model for a config file in the git repo with an associated Sync error

  Fields:
    resourceGvk: Group/version/kind of the resource that is causing an error
    resourceName: Metadata name of the resource that is causing an error
    resourceNamespace: Namespace of the resource that is causing an error
    sourcePath: Path in the git repo of the erroneous config
  """

  resourceGvk = _messages.MessageField('GroupVersionKind', 1)
  resourceName = _messages.StringField(2)
  resourceNamespace = _messages.StringField(3)
  sourcePath = _messages.StringField(4)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class Feature(_messages.Message):
  r"""Feature represents the settings and status of any feature.

  Messages:
    LabelsValue: Labels for this feature.

  Fields:
    appdevexperienceFeatureSpec: The specification for App Dev Experience.
    authorizerFeatureSpec: The specification for the Authorizer Feature.
    cloudauditloggingFeatureSpec: The specification for Anthos Cloud Audit
      Logging.
    cloudbuildFeatureSpec: The specification for Cloud Build for Anthos.
    configmanagementFeatureSpec: The specification for Anthos Config
      Management.
    createTime: Output only. When the Feature was created.
    dataplanev2FeatureSpec: The specification for multi-cluster dataplane-v2.
    deleteTime: Output only. When the Feature was deleted.
    description: Description of the feature, limited to 63 characters.
    featureState: Output only. State of the Feature resource itself.
    fleetDefaultMemberConfig: FleetDefaultMemberConfig describes the default
      member configuration at the fleet level.
    fleetobservabilityFeatureSpec: The specification for FleetObservability
      feature.
    helloworldFeatureSpec: A Hello World feature for codelab examples and
      testing.
    identityserviceFeatureSpec: The specification for Anthos Identity Service.
    labels: Labels for this feature.
    meteringFeatureSpec: The specification for the Metering feature.
    multiclusteringressFeatureSpec: The specification for Ingress for Anthos.
    multiclusterservicediscoveryFeatureSpec: The specification for GKE Multi-
      Cluster Service Discovery.
    name: Output only. The full, unique name of this Feature resource in the
      format `projects/*/locations/global/features/*`.
    namespaceactuationFeatureSpec: The specification for Fleet Namespace
      Actuation.
    policycontrollerFeatureSpec: The specification for Policy Controller.
    rbacrolebindingactuationFeatureSpec: The specification for RBAC Role
      Binding Actuation.
    servicedirectoryFeatureSpec: The specification for Service Directory.
    servicemeshFeatureSpec: The specification for the Service Mesh Feature.
    unreachable: Output only. List of locations that could not be reached
      while fetching this feature.
    updateTime: Output only. When the Feature was last updated.
    workloadcertificateFeatureSpec: The specification for Workload
      Certificate.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels for this feature.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  appdevexperienceFeatureSpec = _messages.MessageField('AppDevExperienceFeatureSpec', 1)
  authorizerFeatureSpec = _messages.MessageField('AuthorizerFeatureSpec', 2)
  cloudauditloggingFeatureSpec = _messages.MessageField('CloudAuditLoggingFeatureSpec', 3)
  cloudbuildFeatureSpec = _messages.MessageField('CloudBuildFeatureSpec', 4)
  configmanagementFeatureSpec = _messages.MessageField('ConfigManagementFeatureSpec', 5)
  createTime = _messages.StringField(6)
  dataplanev2FeatureSpec = _messages.MessageField('DataplaneV2FeatureSpec', 7)
  deleteTime = _messages.StringField(8)
  description = _messages.StringField(9)
  featureState = _messages.MessageField('FeatureState', 10)
  fleetDefaultMemberConfig = _messages.MessageField('FleetDefaultMemberConfig', 11)
  fleetobservabilityFeatureSpec = _messages.MessageField('FleetObservabilityFeatureSpec', 12)
  helloworldFeatureSpec = _messages.MessageField('HelloWorldFeatureSpec', 13)
  identityserviceFeatureSpec = _messages.MessageField('IdentityServiceFeatureSpec', 14)
  labels = _messages.MessageField('LabelsValue', 15)
  meteringFeatureSpec = _messages.MessageField('MeteringFeatureSpec', 16)
  multiclusteringressFeatureSpec = _messages.MessageField('MultiClusterIngressFeatureSpec', 17)
  multiclusterservicediscoveryFeatureSpec = _messages.MessageField('MultiClusterServiceDiscoveryFeatureSpec', 18)
  name = _messages.StringField(19)
  namespaceactuationFeatureSpec = _messages.MessageField('NamespaceActuationFeatureSpec', 20)
  policycontrollerFeatureSpec = _messages.MessageField('PolicyControllerFeatureSpec', 21)
  rbacrolebindingactuationFeatureSpec = _messages.MessageField('RBACRoleBindingActuationFeatureSpec', 22)
  servicedirectoryFeatureSpec = _messages.MessageField('ServiceDirectoryFeatureSpec', 23)
  servicemeshFeatureSpec = _messages.MessageField('ServiceMeshFeatureSpec', 24)
  unreachable = _messages.StringField(25, repeated=True)
  updateTime = _messages.StringField(26)
  workloadcertificateFeatureSpec = _messages.MessageField('WorkloadCertificateFeatureSpec', 27)


class FeatureError(_messages.Message):
  r"""All error details of the fleet observability feature.

  Fields:
    code: The code of the error.
    description: A human-readable description of the current status.
  """

  code = _messages.StringField(1)
  description = _messages.StringField(2)


class FeatureSample(_messages.Message):
  r"""Represents message used in feature e2e create/mutate testing.

  Enums:
    ThirdValueValuesEnum:

  Messages:
    FifthValue: A FifthValue object.
    NinthValue: Map field.

  Fields:
    eighth: Repeated field.
    fifth: A FifthValue attribute.
    first: Singular scaler field.
    fourth: Singular Message fields.
    ninth: Map field.
    second: Singular scaler field.
    seventh: A string attribute.
    sixth: A string attribute.
    third: A ThirdValueValuesEnum attribute.
  """

  class ThirdValueValuesEnum(_messages.Enum):
    r"""ThirdValueValuesEnum enum type.

    Values:
      BAR_UNSPECIFIED: <no description>
      FIRST: <no description>
      SECOND: <no description>
    """
    BAR_UNSPECIFIED = 0
    FIRST = 1
    SECOND = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FifthValue(_messages.Message):
    r"""A FifthValue object.

    Messages:
      AdditionalProperty: An additional property for a FifthValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FifthValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class NinthValue(_messages.Message):
    r"""Map field.

    Messages:
      AdditionalProperty: An additional property for a NinthValue object.

    Fields:
      additionalProperties: Additional properties of type NinthValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a NinthValue object.

      Fields:
        key: Name of the additional property.
        value: A FooBar attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('FooBar', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  eighth = _messages.MessageField('FooBar', 1, repeated=True)
  fifth = _messages.MessageField('FifthValue', 2)
  first = _messages.StringField(3)
  fourth = _messages.StringField(4)
  ninth = _messages.MessageField('NinthValue', 5)
  second = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  seventh = _messages.StringField(7)
  sixth = _messages.IntegerField(8)
  third = _messages.EnumField('ThirdValueValuesEnum', 9)


class FeatureState(_messages.Message):
  r"""FeatureState describes the state of a Feature resource.

  Enums:
    LifecycleStateValueValuesEnum: The current state of the Feature resource.

  Messages:
    DetailsByMembershipValue: FeatureState for each Membership. Keys are the
      fully-qualified Membership name in the format
      `projects/{NUMBER}/locations/*/memberships/*`.

  Fields:
    details: Aggregate status message of the feature.
    detailsByMembership: FeatureState for each Membership. Keys are the fully-
      qualified Membership name in the format
      `projects/{NUMBER}/locations/*/memberships/*`.
    hasResources: Whether this Feature has outstanding resources that need to
      be cleaned up before it can be disabled.
    lifecycleState: The current state of the Feature resource.
  """

  class LifecycleStateValueValuesEnum(_messages.Enum):
    r"""The current state of the Feature resource.

    Values:
      LIFECYCLE_STATE_UNSPECIFIED: State is unknown or not set.
      ENABLING: The Feature is being enabled.
      ENABLED: The Feature is active.
      DISABLING: The Feature is being disabled.
      UPDATING: The Feature is being updated.
      SERVICE_UPDATING: The Feature is being updated by the Hub Service.
    """
    LIFECYCLE_STATE_UNSPECIFIED = 0
    ENABLING = 1
    ENABLED = 2
    DISABLING = 3
    UPDATING = 4
    SERVICE_UPDATING = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsByMembershipValue(_messages.Message):
    r"""FeatureState for each Membership. Keys are the fully-qualified
    Membership name in the format
    `projects/{NUMBER}/locations/*/memberships/*`.

    Messages:
      AdditionalProperty: An additional property for a
        DetailsByMembershipValue object.

    Fields:
      additionalProperties: Additional properties of type
        DetailsByMembershipValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsByMembershipValue object.

      Fields:
        key: Name of the additional property.
        value: A FeatureStateDetails attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('FeatureStateDetails', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  details = _messages.MessageField('FeatureStateDetails', 1)
  detailsByMembership = _messages.MessageField('DetailsByMembershipValue', 2)
  hasResources = _messages.BooleanField(3)
  lifecycleState = _messages.EnumField('LifecycleStateValueValuesEnum', 4)


class FeatureStateDetails(_messages.Message):
  r"""FeatureStateDetails is a semi-structured status message for a
  declarative resource in the API.

  Enums:
    CodeValueValuesEnum: The code describes, at a high level, if the Feature
      is operating correctly. Non-`OK` codes should have details in the
      `description` describing what actions (if any) need to be taken to
      return the Feature to `OK`.

  Fields:
    appdevexperienceFeatureState: State for the AppDevExperience Feature.
    authorizerFeatureState: State for the Authorizer Feature.
    cloudauditloggingFeatureState: The state of the Anthos Cloud Audit Logging
      feature.
    code: The code describes, at a high level, if the Feature is operating
      correctly. Non-`OK` codes should have details in the `description`
      describing what actions (if any) need to be taken to return the Feature
      to `OK`.
    configmanagementFeatureState: State for the Config Management Feature.
    dataplanev2FeatureState: State for multi-cluster dataplane-v2 feature.
    description: Human readable description of the issue.
    fleetobservabilityFeatureState: State for the FleetObservability Feature.
    helloworldFeatureState: State for the Hello World Feature.
    identityserviceFeatureState: State for the AIS Feature.
    meteringFeatureState: State for the Metering Feature.
    multiclusteringressFeatureState: State for the Ingress for Anthos Feature.
    multiclusterservicediscoveryFeatureState: State for the Multi-cluster
      Service Discovery Feature.
    namespaceactuationFeatureState: State for Fleet Namespace Actuation.
    policycontrollerFeatureState: State for the Policy Controller Feature.
    rbacrolebindingactuationFeatureState: State for RBAC Role Binding
      Actuation.
    servicedirectoryFeatureState: State for the Service Directory Feature.
    servicemeshFeatureState: State for the Service Mesh Feature.
    updateTime: The last update time of this status by the controllers
    workloadcertificateFeatureState: State for the Workload Certificate
      Feature
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""The code describes, at a high level, if the Feature is operating
    correctly. Non-`OK` codes should have details in the `description`
    describing what actions (if any) need to be taken to return the Feature to
    `OK`.

    Values:
      CODE_UNSPECIFIED: Not set.
      OK: No error.
      FAILED: The Feature has encountered an issue that blocks all, or a
        significant portion, of its normal operation. See the `description`
        for more details.
      WARNING: The Feature is in a state, or has encountered an issue, that
        impacts its normal operation. This state may or may not require
        intervention to resolve, see the `description` for more details.
    """
    CODE_UNSPECIFIED = 0
    OK = 1
    FAILED = 2
    WARNING = 3

  appdevexperienceFeatureState = _messages.MessageField('AppDevExperienceFeatureState', 1)
  authorizerFeatureState = _messages.MessageField('AuthorizerFeatureState', 2)
  cloudauditloggingFeatureState = _messages.MessageField('CloudAuditLoggingFeatureState', 3)
  code = _messages.EnumField('CodeValueValuesEnum', 4)
  configmanagementFeatureState = _messages.MessageField('ConfigManagementFeatureState', 5)
  dataplanev2FeatureState = _messages.MessageField('DataplaneV2FeatureState', 6)
  description = _messages.StringField(7)
  fleetobservabilityFeatureState = _messages.MessageField('FleetObservabilityFeatureState', 8)
  helloworldFeatureState = _messages.MessageField('HelloWorldFeatureState', 9)
  identityserviceFeatureState = _messages.MessageField('IdentityServiceFeatureState', 10)
  meteringFeatureState = _messages.MessageField('MeteringFeatureState', 11)
  multiclusteringressFeatureState = _messages.MessageField('MultiClusterIngressFeatureState', 12)
  multiclusterservicediscoveryFeatureState = _messages.MessageField('MultiClusterServiceDiscoveryFeatureState', 13)
  namespaceactuationFeatureState = _messages.MessageField('NamespaceActuationFeatureState', 14)
  policycontrollerFeatureState = _messages.MessageField('PolicyControllerFeatureState', 15)
  rbacrolebindingactuationFeatureState = _messages.MessageField('RBACRoleBindingActuationFeatureState', 16)
  servicedirectoryFeatureState = _messages.MessageField('ServiceDirectoryFeatureState', 17)
  servicemeshFeatureState = _messages.MessageField('ServiceMeshFeatureState', 18)
  updateTime = _messages.StringField(19)
  workloadcertificateFeatureState = _messages.MessageField('WorkloadCertificateFeatureState', 20)


class FleetDefaultMemberConfig(_messages.Message):
  r"""FleetDefaultMemberConfig contains default configuration information for
  memberships of a fleet.

  Fields:
    identityService: Spec for IdentityService.
    serviceMesh: Spec for ServiceMesh.
  """

  identityService = _messages.MessageField('MemberConfig', 1)
  serviceMesh = _messages.MessageField('ServiceMeshMembershipSpec', 2)


class FleetObservabilityBaseFeatureState(_messages.Message):
  r"""Base state for fleet observability feature.

  Enums:
    CodeValueValuesEnum: The high-level, machine-readable status of this
      Feature.

  Fields:
    code: The high-level, machine-readable status of this Feature.
    errors: Errors after reconciling the monitoring and logging feature if the
      code is not OK.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""The high-level, machine-readable status of this Feature.

    Values:
      CODE_UNSPECIFIED: Unknown or not set.
      OK: The Feature is operating normally.
      ERROR: The Feature is encountering errors in the reconciliation. The
        Feature may need intervention to return to normal operation. See the
        description and any associated Feature-specific details for more
        information.
    """
    CODE_UNSPECIFIED = 0
    OK = 1
    ERROR = 2

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  errors = _messages.MessageField('FeatureError', 2, repeated=True)


class FleetObservabilityFeatureSpec(_messages.Message):
  r"""Spec for FleetObservability feature. This is required since Feature
  proto requires a spec.

  Fields:
    loggingConfig: Specified if fleet logging feature is enabled for the
      entire fleet. If UNSPECIFIED, fleet logging feature is disabled for the
      entire fleet.
  """

  loggingConfig = _messages.MessageField('LoggingConfig', 1)


class FleetObservabilityFeatureState(_messages.Message):
  r"""An empty state for FleetObservability feature. This is required since
  FeatureStateDetails requires a state.

  Fields:
    logging: The feature state of fleet logging.
    monitoring: The feature state of fleet monitoring.
  """

  logging = _messages.MessageField('FleetObservabilityLoggingState', 1)
  monitoring = _messages.MessageField('FleetObservabilityMonitoringState', 2)


class FleetObservabilityLoggingState(_messages.Message):
  r"""Feature state for logging feature.

  Fields:
    defaultLog: The base feature state of fleet default log.
    scopeLog: The base feature state of fleet scope log.
  """

  defaultLog = _messages.MessageField('FleetObservabilityBaseFeatureState', 1)
  scopeLog = _messages.MessageField('FleetObservabilityBaseFeatureState', 2)


class FleetObservabilityMonitoringState(_messages.Message):
  r"""Feature state for monitoring feature.

  Fields:
    state: The base feature state of fleet monitoring feature.
  """

  state = _messages.MessageField('FleetObservabilityBaseFeatureState', 1)


class FooBar(_messages.Message):
  r"""Nested Message.

  Fields:
    first: A string attribute.
    second: A integer attribute.
  """

  first = _messages.StringField(1)
  second = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GatekeeperDeploymentState(_messages.Message):
  r"""State of Policy Controller installation.

  Enums:
    GatekeeperAuditValueValuesEnum: Status of gatekeeper-audit deployment.
    GatekeeperControllerManagerStateValueValuesEnum: Status of gatekeeper-
      controller-manager pod.
    GatekeeperMutationValueValuesEnum: Status of the pod serving the mutation
      webhook.

  Fields:
    gatekeeperAudit: Status of gatekeeper-audit deployment.
    gatekeeperControllerManagerState: Status of gatekeeper-controller-manager
      pod.
    gatekeeperMutation: Status of the pod serving the mutation webhook.
  """

  class GatekeeperAuditValueValuesEnum(_messages.Enum):
    r"""Status of gatekeeper-audit deployment.

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class GatekeeperControllerManagerStateValueValuesEnum(_messages.Enum):
    r"""Status of gatekeeper-controller-manager pod.

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class GatekeeperMutationValueValuesEnum(_messages.Enum):
    r"""Status of the pod serving the mutation webhook.

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  gatekeeperAudit = _messages.EnumField('GatekeeperAuditValueValuesEnum', 1)
  gatekeeperControllerManagerState = _messages.EnumField('GatekeeperControllerManagerStateValueValuesEnum', 2)
  gatekeeperMutation = _messages.EnumField('GatekeeperMutationValueValuesEnum', 3)


class GetReferenceRequest(_messages.Message):
  r"""The GetReferenceRequest request.

  Fields:
    name: Required. Full resource name of the reference, in the following
      format:
      `//{target_service}/{target_resource}/references/{reference_id}`. For
      example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}/references/{xyz}`.
  """

  name = _messages.StringField(1)


class GitConfig(_messages.Message):
  r"""Git repo configuration for a single cluster.

  Fields:
    gcpServiceAccountEmail: The Google Cloud Service Account Email used for
      auth when secret_type is gcpServiceAccount.
    httpsProxy: URL for the HTTPS proxy to be used when communicating with the
      Git repo.
    policyDir: The path within the Git repository that represents the top
      level of the repo to sync. Default: the root directory of the
      repository.
    secretType: Required. Type of secret configured for access to the Git
      repo.
    syncBranch: The branch of the repository to sync from. Default: master.
    syncRepo: The URL of the Git repository to use as the source of truth.
    syncRev: Git revision (tag or hash) to check out. Default HEAD.
    syncWaitSecs: Period in seconds between consecutive syncs. Default: 15.
  """

  gcpServiceAccountEmail = _messages.StringField(1)
  httpsProxy = _messages.StringField(2)
  policyDir = _messages.StringField(3)
  secretType = _messages.StringField(4)
  syncBranch = _messages.StringField(5)
  syncRepo = _messages.StringField(6)
  syncRev = _messages.StringField(7)
  syncWaitSecs = _messages.IntegerField(8)


class GkehubProjectsLocationsFeaturesGetIamPolicyRequest(_messages.Message):
  r"""A GkehubProjectsLocationsFeaturesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class GkehubProjectsLocationsFeaturesSetIamPolicyRequest(_messages.Message):
  r"""A GkehubProjectsLocationsFeaturesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class GkehubProjectsLocationsFeaturesTestIamPermissionsRequest(_messages.Message):
  r"""A GkehubProjectsLocationsFeaturesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class GkehubProjectsLocationsGetRequest(_messages.Message):
  r"""A GkehubProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsGlobalFeaturesCreateRequest(_messages.Message):
  r"""A GkehubProjectsLocationsGlobalFeaturesCreateRequest object.

  Fields:
    feature: A Feature resource to be passed as the request body.
    featureId: The ID of the feature to create.
    parent: Required. The parent (project and location) where the Feature will
      be created. Specified in the format `projects/*/locations/global`.
  """

  feature = _messages.MessageField('Feature', 1)
  featureId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class GkehubProjectsLocationsGlobalFeaturesDeleteRequest(_messages.Message):
  r"""A GkehubProjectsLocationsGlobalFeaturesDeleteRequest object.

  Fields:
    force: If set to true, the delete will ignore any outstanding resources
      for this Feature (that is, `FeatureState.has_resources` is set to true).
      These resources will NOT be cleaned up or modified in any way.
    name: Required. The Feature resource name in the format
      `projects/*/locations/global/features/*`.
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class GkehubProjectsLocationsGlobalFeaturesGetRequest(_messages.Message):
  r"""A GkehubProjectsLocationsGlobalFeaturesGetRequest object.

  Fields:
    name: Required. The Feature resource name in the format
      `projects/*/locations/global/features/*`
    returnPartialSuccess: Optional. If set to true, the response will return
      partial results when some regions are unreachable and the unreachable
      field in Feature proto will be populated. If set to false, the request
      will fail when some regions are unreachable.
  """

  name = _messages.StringField(1, required=True)
  returnPartialSuccess = _messages.BooleanField(2)


class GkehubProjectsLocationsGlobalFeaturesListRequest(_messages.Message):
  r"""A GkehubProjectsLocationsGlobalFeaturesListRequest object.

  Fields:
    filter: Lists Features that match the filter expression, following the
      syntax outlined in https://google.aip.dev/160. Examples: - Feature with
      the name "servicemesh" in project "foo-proj": name = "projects/foo-
      proj/locations/global/features/servicemesh" - Service Mesh Feature with
      `mtls` enabled: servicemesh_feature_spec.mtls = true - Features that
      have a label called `foo`: labels.foo:* - Features that have a label
      called `foo` whose value is `bar`: labels.foo = bar
    orderBy: One or more fields to compare and use to sort the output. See
      https://google.aip.dev/132#ordering.
    pageSize: When requesting a 'page' of resources, `page_size` specifies
      number of resources to return. If unspecified or set to 0, all resources
      will be returned.
    pageToken: Token returned by previous call to `ListFeatures` which
      specifies the position in the list from where to continue listing the
      resources.
    parent: Required. The parent (project and location) where the Features
      will be listed. Specified in the format `projects/*/locations/global`.
    returnPartialSuccess: Optional. If set to true, the response will return
      partial results when some regions are unreachable and the unreachable
      field in Feature proto will be populated. If set to false, the request
      will fail when some regions are unreachable.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)
  returnPartialSuccess = _messages.BooleanField(6)


class GkehubProjectsLocationsGlobalFeaturesPatchRequest(_messages.Message):
  r"""A GkehubProjectsLocationsGlobalFeaturesPatchRequest object.

  Fields:
    feature: A Feature resource to be passed as the request body.
    name: Required. The Feature resource name in the format
      `projects/*/locations/global/features/*`.
    updateMask: Mask of fields to update.
  """

  feature = _messages.MessageField('Feature', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class GkehubProjectsLocationsListRequest(_messages.Message):
  r"""A GkehubProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    includeUnrevealedLocations: If true, the returned list will include
      locations which are not yet revealed.
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  includeUnrevealedLocations = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  pageSize = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(6)


class GkehubProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A GkehubProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class GkehubProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A GkehubProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A GkehubProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A GkehubProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class GoogleConfig(_messages.Message):
  r"""Configuration for the Google Plugin Auth flow.

  Fields:
    disable: Disable automatic configuration of Google Plugin on supported
      platforms.
  """

  disable = _messages.BooleanField(1)


class GoogleIamV1Condition(_messages.Message):
  r"""A condition to be met.

  Enums:
    IamValueValuesEnum: Trusted attributes supplied by the IAM system.
    OpValueValuesEnum: An operator to apply the subject with.
    SysValueValuesEnum: Trusted attributes supplied by any service that owns
      resources and uses the IAM system for access control.

  Fields:
    iam: Trusted attributes supplied by the IAM system.
    op: An operator to apply the subject with.
    svc: Trusted attributes discharged by the service.
    sys: Trusted attributes supplied by any service that owns resources and
      uses the IAM system for access control.
    values: The objects of the condition.
  """

  class IamValueValuesEnum(_messages.Enum):
    r"""Trusted attributes supplied by the IAM system.

    Values:
      NO_ATTR: Default non-attribute.
      AUTHORITY: Either principal or (if present) authority selector.
      ATTRIBUTION: The principal (even if an authority selector is present),
        which must only be used for attribution, not authorization.
      SECURITY_REALM: Any of the security realms in the IAMContext
        (go/security-realms). When used with IN, the condition indicates "any
        of the request's realms match one of the given values; with NOT_IN,
        "none of the realms match any of the given values". Note that a value
        can be: - 'self:campus' (i.e., clients that are in the same campus) -
        'self:metro' (i.e., clients that are in the same metro) - 'self:cloud-
        region' (i.e., allow connections from clients that are in the same
        cloud region) - 'self:prod-region' (i.e., allow connections from
        clients that are in the same prod region) - 'guardians' (i.e., allow
        connections from its guardian realms. See go/security-realms-
        glossary#guardian for more information.) - 'cryto_core_guardians'
        (i.e., allow connections from its crypto core guardian realms. See
        go/security-realms-glossary#guardian for more information.) Crypto
        Core coverage is a super-set of Default coverage, containing
        information about coverage between higher tier data centers (e.g.,
        YAWNs). Most services should use Default coverage and only use Crypto
        Core coverage if the service is involved in greenfield turnup of new
        higher tier data centers (e.g., credential infrastructure, machine/job
        management systems, etc.). - 'self' [DEPRECATED] (i.e., allow
        connections from clients that are in the same security realm, which is
        currently but not guaranteed to be campus-sized) - a realm (e.g.,
        'campus-abc') - a realm group (e.g., 'realms-for-borg-cell-xx', see:
        go/realm-groups) A match is determined by a realm group membership
        check performed by a RealmAclRep object (go/realm-acl-howto). It is
        not permitted to grant access based on the *absence* of a realm, so
        realm conditions can only be used in a "positive" context (e.g.,
        ALLOW/IN or DENY/NOT_IN).
      APPROVER: An approver (distinct from the requester) that has authorized
        this request. When used with IN, the condition indicates that one of
        the approvers associated with the request matches the specified
        principal, or is a member of the specified group. Approvers can only
        grant additional access, and are thus only used in a strictly positive
        context (e.g. ALLOW/IN or DENY/NOT_IN).
      JUSTIFICATION_TYPE: What types of justifications have been supplied with
        this request. String values should match enum names from
        security.credentials.JustificationType, e.g. "MANUAL_STRING". It is
        not permitted to grant access based on the *absence* of a
        justification, so justification conditions can only be used in a
        "positive" context (e.g., ALLOW/IN or DENY/NOT_IN). Multiple
        justifications, e.g., a Buganizer ID and a manually-entered reason,
        are normal and supported.
      CREDENTIALS_TYPE: What type of credentials have been supplied with this
        request. String values should match enum names from
        security_loas_l2.CredentialsType - currently, only
        CREDS_TYPE_EMERGENCY is supported. It is not permitted to grant access
        based on the *absence* of a credentials type, so the conditions can
        only be used in a "positive" context (e.g., ALLOW/IN or DENY/NOT_IN).
      CREDS_ASSERTION: Properties of the credentials supplied with this
        request. See http://go/rpcsp-credential-assertions?polyglot=rpcsp-v1-0
        The conditions can only be used in a "positive" context (e.g.,
        ALLOW/IN or DENY/NOT_IN).
    """
    NO_ATTR = 0
    AUTHORITY = 1
    ATTRIBUTION = 2
    SECURITY_REALM = 3
    APPROVER = 4
    JUSTIFICATION_TYPE = 5
    CREDENTIALS_TYPE = 6
    CREDS_ASSERTION = 7

  class OpValueValuesEnum(_messages.Enum):
    r"""An operator to apply the subject with.

    Values:
      NO_OP: Default no-op.
      EQUALS: DEPRECATED. Use IN instead.
      NOT_EQUALS: DEPRECATED. Use NOT_IN instead.
      IN: The condition is true if the subject (or any element of it if it is
        a set) matches any of the supplied values.
      NOT_IN: The condition is true if the subject (or every element of it if
        it is a set) matches none of the supplied values.
      DISCHARGED: Subject is discharged
    """
    NO_OP = 0
    EQUALS = 1
    NOT_EQUALS = 2
    IN = 3
    NOT_IN = 4
    DISCHARGED = 5

  class SysValueValuesEnum(_messages.Enum):
    r"""Trusted attributes supplied by any service that owns resources and
    uses the IAM system for access control.

    Values:
      NO_ATTR: Default non-attribute type
      REGION: Region of the resource
      SERVICE: Service name
      NAME: Resource name
      IP: IP address of the caller
    """
    NO_ATTR = 0
    REGION = 1
    SERVICE = 2
    NAME = 3
    IP = 4

  iam = _messages.EnumField('IamValueValuesEnum', 1)
  op = _messages.EnumField('OpValueValuesEnum', 2)
  svc = _messages.StringField(3)
  sys = _messages.EnumField('SysValueValuesEnum', 4)
  values = _messages.StringField(5, repeated=True)


class GoogleRpcStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class GroupConfig(_messages.Message):
  r"""Contains the properties for locating and authenticating groups in the
  directory.

  Fields:
    baseDn: Required. The location of the subtree in the LDAP directory to
      search for group entries.
    filter: Optional. Optional filter to be used when searching for groups a
      user belongs to. This can be used to explicitly match only certain
      groups in order to reduce the amount of groups returned for each user.
      This defaults to "(objectClass=Group)".
    idAttribute: Optional. The identifying name of each group a user belongs
      to. For example, if this is set to "distinguishedName" then RBACs and
      other group expectations should be written as full DNs. This defaults to
      "distinguishedName".
  """

  baseDn = _messages.StringField(1)
  filter = _messages.StringField(2)
  idAttribute = _messages.StringField(3)


class GroupVersionKind(_messages.Message):
  r"""A Kubernetes object's GVK

  Fields:
    group: Kubernetes Group
    kind: Kubernetes Kind
    version: Kubernetes Version
  """

  group = _messages.StringField(1)
  kind = _messages.StringField(2)
  version = _messages.StringField(3)


class HelloWorldFeatureSpec(_messages.Message):
  r"""HelloWorldFeatureSpec contains the input for the HelloWorld feature.

  Fields:
    customConfig: Custom config for the HelloWorld controller codelab. This
      should be a textpb string.
    featureSample: Message to hold fields to use in feature e2e create/mutate
      testing.
  """

  customConfig = _messages.StringField(1)
  featureSample = _messages.MessageField('FeatureSample', 2)


class HelloWorldFeatureState(_messages.Message):
  r"""An empty state for HelloWorld feature. This is required since
  FeatureStateDetails requires a state.
  """



class HierarchyControllerConfig(_messages.Message):
  r"""Configuration for Hierarchy Controller

  Fields:
    enableHierarchicalResourceQuota: Whether hierarchical resource quota is
      enabled in this cluster.
    enablePodTreeLabels: Whether pod tree labels are enabled in this cluster.
    enabled: Whether Hierarchy Controller is enabled in this cluster.
  """

  enableHierarchicalResourceQuota = _messages.BooleanField(1)
  enablePodTreeLabels = _messages.BooleanField(2)
  enabled = _messages.BooleanField(3)


class HierarchyControllerDeploymentState(_messages.Message):
  r"""Deployment state for Hierarchy Controller

  Enums:
    ExtensionValueValuesEnum: The deployment state for Hierarchy Controller
      extension (e.g. v0.7.0-hc.1)
    HncValueValuesEnum: The deployment state for open source HNC (e.g.
      v0.7.0-hc.0)

  Fields:
    extension: The deployment state for Hierarchy Controller extension (e.g.
      v0.7.0-hc.1)
    hnc: The deployment state for open source HNC (e.g. v0.7.0-hc.0)
  """

  class ExtensionValueValuesEnum(_messages.Enum):
    r"""The deployment state for Hierarchy Controller extension (e.g.
    v0.7.0-hc.1)

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  class HncValueValuesEnum(_messages.Enum):
    r"""The deployment state for open source HNC (e.g. v0.7.0-hc.0)

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  extension = _messages.EnumField('ExtensionValueValuesEnum', 1)
  hnc = _messages.EnumField('HncValueValuesEnum', 2)


class HierarchyControllerState(_messages.Message):
  r"""State for Hierarchy Controller

  Fields:
    state: The deployment state for Hierarchy Controller
    version: The version for Hierarchy Controller
  """

  state = _messages.MessageField('HierarchyControllerDeploymentState', 1)
  version = _messages.MessageField('HierarchyControllerVersion', 2)


class HierarchyControllerVersion(_messages.Message):
  r"""Version for Hierarchy Controller

  Fields:
    extension: Version for Hierarchy Controller extension
    hnc: Version for open source HNC
  """

  extension = _messages.StringField(1)
  hnc = _messages.StringField(2)


class IdentityServiceFeatureSpec(_messages.Message):
  r"""Spec for Annthos Identity Service.

  Messages:
    MemberConfigsValue: A map between member ids to their configurations. The
      ID needs to be the full path to the membership e.g.,
      /projects/p/locations/l/memberships/m.

  Fields:
    memberConfigs: A map between member ids to their configurations. The ID
      needs to be the full path to the membership e.g.,
      /projects/p/locations/l/memberships/m.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MemberConfigsValue(_messages.Message):
    r"""A map between member ids to their configurations. The ID needs to be
    the full path to the membership e.g.,
    /projects/p/locations/l/memberships/m.

    Messages:
      AdditionalProperty: An additional property for a MemberConfigsValue
        object.

    Fields:
      additionalProperties: Additional properties of type MemberConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MemberConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A MemberConfig attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('MemberConfig', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  memberConfigs = _messages.MessageField('MemberConfigsValue', 1)


class IdentityServiceFeatureState(_messages.Message):
  r"""State for Anthos Identity Service

  Enums:
    StateValueValuesEnum: Deployment state on this member

  Fields:
    failureReason: The reason of the failure.
    installedVersion: Installed AIS version. This is the AIS version installed
      on this member. The values makes sense iff state is OK.
    memberConfig: Membership config state on this member
    state: Deployment state on this member
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Deployment state on this member

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Unspecified state
      OK: deployment succeeds
      ERROR: Failure with error.
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    OK = 1
    ERROR = 2

  failureReason = _messages.StringField(1)
  installedVersion = _messages.StringField(2)
  memberConfig = _messages.MessageField('MemberConfig', 3)
  state = _messages.EnumField('StateValueValuesEnum', 4)


class IdentityServiceOptions(_messages.Message):
  r"""Holds non-protocol-related configuration options.

  Fields:
    sessionDuration: Optional. Determines the lifespan of STS tokens issued by
      Anthos Identity Service.
  """

  sessionDuration = _messages.StringField(1)


class InstallError(_messages.Message):
  r"""Errors pertaining to the installation of ACM

  Fields:
    errorMessage: A string representing the user facing error message
  """

  errorMessage = _messages.StringField(1)


class LdapConfig(_messages.Message):
  r"""Configuration for the LDAP Auth flow.

  Fields:
    group: Optional. Contains the properties for locating and authenticating
      groups in the directory.
    server: Required. Server settings for the external LDAP server.
    serviceAccount: Required. Contains the credentials of the service account
      which is authorized to perform the LDAP search in the directory. The
      credentials can be supplied by the combination of the DN and password or
      the client certificate.
    user: Required. Defines where users exist in the LDAP directory.
  """

  group = _messages.MessageField('GroupConfig', 1)
  server = _messages.MessageField('ServerConfig', 2)
  serviceAccount = _messages.MessageField('ServiceAccountConfig', 3)
  user = _messages.MessageField('UserConfig', 4)


class ListFeaturesResponse(_messages.Message):
  r"""Response message for the `GkeHubDomainFeatureService.ListFeatures`
  method.

  Fields:
    nextPageToken: A token to request the next page of resources from the
      `ListFeatures` method. The value of an empty string means that there are
      no more resources to return.
    resources: The list of matching Features
  """

  nextPageToken = _messages.StringField(1)
  resources = _messages.MessageField('Feature', 2, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListReferencesRequest(_messages.Message):
  r"""The ListResourceMetadataRequest request.

  Fields:
    pageSize: The maximum number of items to return. If unspecified, server
      will pick an appropriate default. Server may return fewer items than
      requested. A caller should only rely on response's next_page_token to
      determine if there are more References left to be queried.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: Required. The parent resource name (target_resource of this
      reference). For example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3)


class ListReferencesResponse(_messages.Message):
  r"""The ListReferencesResponse response.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    references: The list of references.
  """

  nextPageToken = _messages.StringField(1)
  references = _messages.MessageField('Reference', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class LogConfig(_messages.Message):
  r"""Specifies what kind of log the caller must write

  Fields:
    cloudAudit: Cloud audit options.
    counter: Counter options.
    dataAccess: Data access options.
  """

  cloudAudit = _messages.MessageField('CloudAuditOptions', 1)
  counter = _messages.MessageField('CounterOptions', 2)
  dataAccess = _messages.MessageField('DataAccessOptions', 3)


class LoggingConfig(_messages.Message):
  r"""LoggingConfig defines the configuration for different types of logs.

  Fields:
    defaultConfig: Specified if applying the default routing config to logs
      not specified in other configs.
    fleetScopeLogsConfig: Specified if applying the routing config to all logs
      for all fleet scopes.
  """

  defaultConfig = _messages.MessageField('RoutingConfig', 1)
  fleetScopeLogsConfig = _messages.MessageField('RoutingConfig', 2)


class MemberConfig(_messages.Message):
  r"""The configuration for a member/cluster

  Fields:
    authMethods: A member may support multiple auth methods.
    identityServiceOptions: Optional. non-protocol-related configuration
      options.
  """

  authMethods = _messages.MessageField('AuthMethod', 1, repeated=True)
  identityServiceOptions = _messages.MessageField('IdentityServiceOptions', 2)


class MembershipConfig(_messages.Message):
  r"""Configuration for a single cluster. Intended to parallel the
  ConfigManagement CR.

  Enums:
    ManagementValueValuesEnum: Enables automatic Feature management.

  Fields:
    binauthz: Binauthz conifguration for the cluster. Deprecated: This field
      will be ignored and should not be set.
    cluster: The user-specified cluster name used by Config Sync cluster-name-
      selector annotation or ClusterSelector, for applying configs to only a
      subset of clusters. Omit this field if the cluster's fleet membership
      name is used by Config Sync cluster-name-selector annotation or
      ClusterSelector. Set this field if a name different from the cluster's
      fleet membership name is used by Config Sync cluster-name-selector
      annotation or ClusterSelector.
    configSync: Config Sync configuration for the cluster.
    hierarchyController: Hierarchy Controller configuration for the cluster.
      Deprecated: Configuring Hierarchy Controller through the
      configmanagement feature is no longer recommended. Use
      https://github.com/kubernetes-sigs/hierarchical-namespaces instead.
    management: Enables automatic Feature management.
    policyController: Policy Controller configuration for the cluster.
      Deprecated: Configuring Policy Controller through the configmanagement
      feature is no longer recommended. Use the policycontroller feature
      instead.
    version: Version of ACM installed.
  """

  class ManagementValueValuesEnum(_messages.Enum):
    r"""Enables automatic Feature management.

    Values:
      MANAGEMENT_UNSPECIFIED: Unspecified
      MANAGEMENT_AUTOMATIC: Google will manage the Feature for the cluster.
      MANAGEMENT_MANUAL: User will manually manage the Feature for the
        cluster.
    """
    MANAGEMENT_UNSPECIFIED = 0
    MANAGEMENT_AUTOMATIC = 1
    MANAGEMENT_MANUAL = 2

  binauthz = _messages.MessageField('BinauthzConfig', 1)
  cluster = _messages.StringField(2)
  configSync = _messages.MessageField('ConfigSync', 3)
  hierarchyController = _messages.MessageField('HierarchyControllerConfig', 4)
  management = _messages.EnumField('ManagementValueValuesEnum', 5)
  policyController = _messages.MessageField('PolicyController', 6)
  version = _messages.StringField(7)


class MeshConnectivity(_messages.Message):
  r"""Status of cross cluster load balancing between other clusters in the
  mesh.

  Enums:
    StateValueValuesEnum: LifecycleState of multicluster load balancing.

  Fields:
    details: Explanation of state.
    state: LifecycleState of multicluster load balancing.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""LifecycleState of multicluster load balancing.

    Values:
      LIFECYCLE_STATE_UNSPECIFIED: Unspecified
      DISABLED: DISABLED means that the component is not enabled.
      FAILED_PRECONDITION: FAILED_PRECONDITION means that provisioning cannot
        proceed because of some characteristic of the member cluster.
      PROVISIONING: PROVISIONING means that provisioning is in progress.
      ACTIVE: ACTIVE means that the component is ready for use.
      STALLED: STALLED means that provisioning could not be done.
      NEEDS_ATTENTION: NEEDS_ATTENTION means that the component is ready, but
        some user intervention is required. (For example that the user should
        migrate workloads to a new control plane revision.)
      DEGRADED: DEGRADED means that the component is ready, but operating in a
        degraded state.
      DEPROVISIONING: DEPROVISIONING means that deprovisioning is in progress.
    """
    LIFECYCLE_STATE_UNSPECIFIED = 0
    DISABLED = 1
    FAILED_PRECONDITION = 2
    PROVISIONING = 3
    ACTIVE = 4
    STALLED = 5
    NEEDS_ATTENTION = 6
    DEGRADED = 7
    DEPROVISIONING = 8

  details = _messages.MessageField('StatusDetails', 1, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class MeteringFeatureSpec(_messages.Message):
  r"""An empty spec for metering feature. This is required since Feature proto
  requires a spec.
  """



class MeteringFeatureState(_messages.Message):
  r"""Metering Feature State.

  Fields:
    lastMeasurementTime: The time stamp of the most recent measurement of the
      number of vCPUs in the cluster.
    preciseLastMeasuredClusterVcpuCapacity: The vCPUs capacity in the cluster
      according to the most recent measurement (1/1000 precision).
  """

  lastMeasurementTime = _messages.StringField(1)
  preciseLastMeasuredClusterVcpuCapacity = _messages.FloatField(2, variant=_messages.Variant.FLOAT)


class MultiClusterIngressFeatureSpec(_messages.Message):
  r"""MultiClusterIngressFeatureSpec contains the input for the
  MultiClusterIngress feature.

  Enums:
    BillingValueValuesEnum: Deprecated: This field will be ignored and should
      not be set. Customer's billing structure

  Fields:
    billing: Deprecated: This field will be ignored and should not be set.
      Customer's billing structure
    configMembership: Fully-qualified member name which hosts the
      MultiClusterIngress CRD. Example member name: `projects/foo-
      proj/locations/global/memberships/bar`
  """

  class BillingValueValuesEnum(_messages.Enum):
    r"""Deprecated: This field will be ignored and should not be set.
    Customer's billing structure

    Values:
      BILLING_UNSPECIFIED: Unknown
      PAY_AS_YOU_GO: User pays a fee per-endpoint.
      ANTHOS_LICENSE: User is paying for Anthos as a whole.
    """
    BILLING_UNSPECIFIED = 0
    PAY_AS_YOU_GO = 1
    ANTHOS_LICENSE = 2

  billing = _messages.EnumField('BillingValueValuesEnum', 1)
  configMembership = _messages.StringField(2)


class MultiClusterIngressFeatureState(_messages.Message):
  r"""MultiClusterIngressFeatureState contains the status fields specific to
  the MultiClusterIngress feature. This is just a placeholder and more fields
  will be added when we have more state information to report for this
  feature.
  """



class MultiClusterServiceDiscoveryFeatureSpec(_messages.Message):
  r"""An empty spec for multi-cluster service discovery feature. This is
  required since Feature proto requires a spec.
  """



class MultiClusterServiceDiscoveryFeatureState(_messages.Message):
  r"""An empty state for multi-cluster service discovery feature. This is
  required since FeatureStateDetails requires a state.
  """



class NamespaceActuationFeatureSpec(_messages.Message):
  r"""An empty spec for FNS actuation feature.

  Enums:
    ActuationModeValueValuesEnum: actuation_mode controls the behavior of the
      controller

  Messages:
    MembershipConfigsValue: Map of Membership resource name to individual
      configs. Membership resource names are in the format of
      "projects/P/locations/L/memberships/M"

  Fields:
    actuationMode: actuation_mode controls the behavior of the controller
    membershipConfigs: Map of Membership resource name to individual configs.
      Membership resource names are in the format of
      "projects/P/locations/L/memberships/M"
  """

  class ActuationModeValueValuesEnum(_messages.Enum):
    r"""actuation_mode controls the behavior of the controller

    Values:
      ACTUATION_MODE_UNSPECIFIED: ACTUATION_MODE_UNSPECIFIED is similar to
        CREATE_AND_DELETE_IF_CREATED in the default controller behavior.
      ACTUATION_MODE_CREATE_AND_DELETE_IF_CREATED:
        ACTUATION_MODE_CREATE_AND_DELETE_IF_CREATED has the controller create
        cluster namespaces for each fleet namespace and it deletes only the
        ones it created, which are identified by a label.
      ACTUATION_MODE_ADD_AND_REMOVE_FLEET_LABELS:
        ACTUATION_MODE_ADD_AND_REMOVE_FLEET_LABELS has the controller only
        apply labels to cluster namespaces to signal fleet namespace
        enablement. It doesn't create or delete cluster namespaces.
    """
    ACTUATION_MODE_UNSPECIFIED = 0
    ACTUATION_MODE_CREATE_AND_DELETE_IF_CREATED = 1
    ACTUATION_MODE_ADD_AND_REMOVE_FLEET_LABELS = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MembershipConfigsValue(_messages.Message):
    r"""Map of Membership resource name to individual configs. Membership
    resource names are in the format of "projects/P/locations/L/memberships/M"

    Messages:
      AdditionalProperty: An additional property for a MembershipConfigsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        MembershipConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MembershipConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A NamespaceActuationMembershipSpec attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('NamespaceActuationMembershipSpec', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  actuationMode = _messages.EnumField('ActuationModeValueValuesEnum', 1)
  membershipConfigs = _messages.MessageField('MembershipConfigsValue', 2)


class NamespaceActuationFeatureState(_messages.Message):
  r"""NamespaceActuation Feature State."""


class NamespaceActuationMembershipSpec(_messages.Message):
  r"""NamespaceActuation Membership Feature Spec."""


class OciConfig(_messages.Message):
  r"""OCI repo configuration for a single cluster

  Fields:
    gcpServiceAccountEmail: The Google Cloud Service Account Email used for
      auth when secret_type is gcpServiceAccount.
    policyDir: The absolute path of the directory that contains the local
      resources. Default: the root directory of the image.
    secretType: Type of secret configured for access to the Git repo.
    syncRepo: The OCI image repository URL for the package to sync from. e.g.
      `LOCATION-docker.pkg.dev/PROJECT_ID/REPOSITORY_NAME/PACKAGE_NAME`.
    syncWaitSecs: Period in seconds between consecutive syncs. Default: 15.
  """

  gcpServiceAccountEmail = _messages.StringField(1)
  policyDir = _messages.StringField(2)
  secretType = _messages.StringField(3)
  syncRepo = _messages.StringField(4)
  syncWaitSecs = _messages.IntegerField(5)


class OidcConfig(_messages.Message):
  r"""Configuration for OIDC Auth flow.

  Fields:
    certificateAuthorityData: PEM-encoded CA for OIDC provider.
    clientId: ID for OIDC client application.
    clientSecret: Input only. Unencrypted OIDC client secret will be passed to
      the GKE Hub CLH.
    deployCloudConsoleProxy: Flag to denote if reverse proxy is used to
      connect to auth provider. This flag should be set to true when provider
      is not reachable by Google Cloud Console.
    enableAccessToken: Enable access token.
    encryptedClientSecret: Output only. Encrypted OIDC Client secret
    extraParams: Comma-separated list of key-value pairs.
    groupPrefix: Prefix to prepend to group name.
    groupsClaim: Claim in OIDC ID token that holds group information.
    issuerUri: URI for the OIDC provider. This should point to the level below
      .well-known/openid-configuration.
    kubectlRedirectUri: Registered redirect uri to redirect users going
      through OAuth flow using kubectl plugin.
    scopes: Comma-separated list of identifiers.
    userClaim: Claim in OIDC ID token that holds username.
    userPrefix: Prefix to prepend to user name.
  """

  certificateAuthorityData = _messages.StringField(1)
  clientId = _messages.StringField(2)
  clientSecret = _messages.StringField(3)
  deployCloudConsoleProxy = _messages.BooleanField(4)
  enableAccessToken = _messages.BooleanField(5)
  encryptedClientSecret = _messages.BytesField(6)
  extraParams = _messages.StringField(7)
  groupPrefix = _messages.StringField(8)
  groupsClaim = _messages.StringField(9)
  issuerUri = _messages.StringField(10)
  kubectlRedirectUri = _messages.StringField(11)
  scopes = _messages.StringField(12)
  userClaim = _messages.StringField(13)
  userPrefix = _messages.StringField(14)


class OnClusterState(_messages.Message):
  r"""OnClusterState represents the state of a sub-component of Policy
  Controller.

  Enums:
    StateValueValuesEnum: The lifecycle state of this component.

  Fields:
    details: Surface potential errors or information logs.
    state: The lifecycle state of this component.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The lifecycle state of this component.

    Values:
      LIFECYCLE_STATE_UNSPECIFIED: The lifecycle state is unspecified.
      NOT_INSTALLED: Policy Controller (PC) does not exist on the given
        cluster, and no k8s resources of any type that are associated with the
        PC should exist there. The cluster does not possess a membership with
        the Hub Feature controller.
      INSTALLING: The Hub Feature controller possesses a Membership, however
        Policy Controller is not fully installed on the cluster. In this state
        the hub can be expected to be taking actions to install the PC on the
        cluster.
      ACTIVE: Policy Controller (PC) is fully installed on the cluster and in
        an operational mode. In this state the Hub Feature controller will be
        reconciling state with the PC, and the PC will be performing it's
        operational tasks per that software. Entering a READY state requires
        that the hub has confirmed the PC is installed and its pods are
        operational with the version of the PC the Hub Feature controller
        expects.
      UPDATING: Policy Controller (PC) is fully installed, but in the process
        of changing the configuration (including changing the version of PC
        either up and down, or modifying the manifests of PC) of the resources
        running on the cluster. The Hub Feature controller has a Membership,
        is aware of the version the cluster should be running in, but has not
        confirmed for itself that the PC is running with that version.
      DECOMMISSIONING: Policy Controller (PC) may have resources on the
        cluster, but the Hub Feature controller wishes to remove the
        Membership. The Membership still exists.
      CLUSTER_ERROR: Policy Controller (PC) is not operational, and the Hub
        Feature controller is unable to act to make it operational. Entering a
        CLUSTER_ERROR state happens automatically when the PCH determines that
        a PC installed on the cluster is non-operative or that the cluster
        does not meet requirements set for the Hub Feature controller to
        administer the cluster but has nevertheless been given an instruction
        to do so (such as 'install').
      HUB_ERROR: In this state, the PC may still be operational, and only the
        Hub Feature controller is unable to act. The hub should not issue
        instructions to change the PC state, or otherwise interfere with the
        on-cluster resources. Entering a HUB_ERROR state happens automatically
        when the Hub Feature controller determines the hub is in an unhealthy
        state and it wishes to 'take hands off' to avoid corrupting the PC or
        other data.
      SUSPENDED: Policy Controller (PC) is installed but suspended. This means
        that the policies are not enforced, but violations are still recorded
        (through audit).
      DETACHED: PoCo Hub is not taking any action to reconcile cluster
        objects. Changes to those objects will not be overwritten by PoCo Hub.
    """
    LIFECYCLE_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLING = 2
    ACTIVE = 3
    UPDATING = 4
    DECOMMISSIONING = 5
    CLUSTER_ERROR = 6
    HUB_ERROR = 7
    SUSPENDED = 8
    DETACHED = 9

  details = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('GoogleRpcStatus', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    cancelRequested: Output only. Identifies whether the user has requested
      cancellation of the operation. Operations that have successfully been
      cancelled have Operation.error value with a google.rpc.Status.code of 1,
      corresponding to `Code.CANCELLED`.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    statusDetail: Output only. Human-readable status of the operation, if any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  cancelRequested = _messages.BooleanField(2)
  createTime = _messages.StringField(3)
  endTime = _messages.StringField(4)
  statusDetail = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class OperatorState(_messages.Message):
  r"""State information for an ACM's Operator

  Enums:
    DeploymentStateValueValuesEnum: The state of the Operator's deployment

  Fields:
    deploymentState: The state of the Operator's deployment
    errors: Install errors.
    version: The semenatic version number of the operator
  """

  class DeploymentStateValueValuesEnum(_messages.Enum):
    r"""The state of the Operator's deployment

    Values:
      DEPLOYMENT_STATE_UNSPECIFIED: Deployment's state cannot be determined
      NOT_INSTALLED: Deployment is not installed
      INSTALLED: Deployment is installed
      ERROR: Deployment was attempted to be installed, but has errors
      PENDING: Deployment is installing or terminating
    """
    DEPLOYMENT_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLED = 2
    ERROR = 3
    PENDING = 4

  deploymentState = _messages.EnumField('DeploymentStateValueValuesEnum', 1)
  errors = _messages.MessageField('InstallError', 2, repeated=True)
  version = _messages.StringField(3)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    rules: If more than one rule is specified, the rules are applied in the
      following manner: - All matching LOG rules are always applied. - If any
      DENY/DENY_WITH_LOG rule matches, permission is denied. Logging will be
      applied if one or more matching rule requires logging. - Otherwise, if
      any ALLOW/ALLOW_WITH_LOG rule matches, permission is granted. Logging
      will be applied if one or more matching rule requires logging. -
      Otherwise, if no rule applies, permission is denied.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  rules = _messages.MessageField('Rule', 4, repeated=True)
  version = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class PolicyContentSpec(_messages.Message):
  r"""PolicyContentSpec defines the user's desired content configuration on
  the cluster.

  Messages:
    BundlesValue: map of bundle name to BundleInstallSpec. The bundle name
      maps to the `bundleName` key in the
      `policycontroller.gke.io/constraintData` annotation on a constraint.

  Fields:
    bundles: map of bundle name to BundleInstallSpec. The bundle name maps to
      the `bundleName` key in the `policycontroller.gke.io/constraintData`
      annotation on a constraint.
    templateLibrary: Configures the installation of the Template Library.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class BundlesValue(_messages.Message):
    r"""map of bundle name to BundleInstallSpec. The bundle name maps to the
    `bundleName` key in the `policycontroller.gke.io/constraintData`
    annotation on a constraint.

    Messages:
      AdditionalProperty: An additional property for a BundlesValue object.

    Fields:
      additionalProperties: Additional properties of type BundlesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a BundlesValue object.

      Fields:
        key: Name of the additional property.
        value: A BundleInstallSpec attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('BundleInstallSpec', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  bundles = _messages.MessageField('BundlesValue', 1)
  templateLibrary = _messages.MessageField('TemplateLibraryConfig', 2)


class PolicyContentState(_messages.Message):
  r"""The state of the policy controller policy content

  Messages:
    BundleStatesValue: The state of the any bundles included in the chosen
      version of the manifest

  Fields:
    bundleStates: The state of the any bundles included in the chosen version
      of the manifest
    referentialSyncConfigState: The state of the referential data sync
      configuration. This could represent the state of either the syncSet
      object(s) or the config object, depending on the version of PoCo
      configured by the user.
    templateLibraryState: The state of the template library
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class BundleStatesValue(_messages.Message):
    r"""The state of the any bundles included in the chosen version of the
    manifest

    Messages:
      AdditionalProperty: An additional property for a BundleStatesValue
        object.

    Fields:
      additionalProperties: Additional properties of type BundleStatesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a BundleStatesValue object.

      Fields:
        key: Name of the additional property.
        value: A OnClusterState attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('OnClusterState', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  bundleStates = _messages.MessageField('BundleStatesValue', 1)
  referentialSyncConfigState = _messages.MessageField('OnClusterState', 2)
  templateLibraryState = _messages.MessageField('OnClusterState', 3)


class PolicyController(_messages.Message):
  r"""Configuration for Policy Controller

  Fields:
    auditIntervalSeconds: Sets the interval for Policy Controller Audit Scans
      (in seconds). When set to 0, this disables audit functionality
      altogether.
    enabled: Enables the installation of Policy Controller. If false, the rest
      of PolicyController fields take no effect.
    exemptableNamespaces: The set of namespaces that are excluded from Policy
      Controller checks. Namespaces do not need to currently exist on the
      cluster.
    logDeniesEnabled: Logs all denies and dry run failures.
    monitoring: Monitoring specifies the configuration of monitoring.
    mutationEnabled: Enable users to try out mutation for PolicyController.
    referentialRulesEnabled: Enables the ability to use Constraint Templates
      that reference to objects other than the object currently being
      evaluated.
    templateLibraryInstalled: Installs the default template library along with
      Policy Controller.
    updateTime: Output only. Last time this membership spec was updated.
  """

  auditIntervalSeconds = _messages.IntegerField(1)
  enabled = _messages.BooleanField(2)
  exemptableNamespaces = _messages.StringField(3, repeated=True)
  logDeniesEnabled = _messages.BooleanField(4)
  monitoring = _messages.MessageField('PolicyControllerMonitoring', 5)
  mutationEnabled = _messages.BooleanField(6)
  referentialRulesEnabled = _messages.BooleanField(7)
  templateLibraryInstalled = _messages.BooleanField(8)
  updateTime = _messages.StringField(9)


class PolicyControllerDeploymentConfig(_messages.Message):
  r"""Deployment-specific configuration.

  Enums:
    PodAffinityValueValuesEnum: Pod affinity configuration.

  Fields:
    containerResources: Container resource requirements.
    podAffinity: Pod affinity configuration.
    podAntiAffinity: Pod anti-affinity enablement. Deprecated: use
      `pod_affinity` instead.
    podTolerations: Pod tolerations of node taints.
    replicaCount: Pod replica count.
  """

  class PodAffinityValueValuesEnum(_messages.Enum):
    r"""Pod affinity configuration.

    Values:
      AFFINITY_UNSPECIFIED: No affinity configuration has been specified.
      NO_AFFINITY: Affinity configurations will be removed from the
        deployment.
      ANTI_AFFINITY: Anti-affinity configuration will be applied to this
        deployment. Default for admissions deployment.
    """
    AFFINITY_UNSPECIFIED = 0
    NO_AFFINITY = 1
    ANTI_AFFINITY = 2

  containerResources = _messages.MessageField('ResourceRequirements', 1)
  podAffinity = _messages.EnumField('PodAffinityValueValuesEnum', 2)
  podAntiAffinity = _messages.BooleanField(3)
  podTolerations = _messages.MessageField('Toleration', 4, repeated=True)
  replicaCount = _messages.IntegerField(5)


class PolicyControllerFeatureSpec(_messages.Message):
  r"""Spec for Policy Controller.

  Messages:
    MembershipSpecsValue: Map of Membership IDs to individual specs.

  Fields:
    membershipSpecs: Map of Membership IDs to individual specs.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MembershipSpecsValue(_messages.Message):
    r"""Map of Membership IDs to individual specs.

    Messages:
      AdditionalProperty: An additional property for a MembershipSpecsValue
        object.

    Fields:
      additionalProperties: Additional properties of type MembershipSpecsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MembershipSpecsValue object.

      Fields:
        key: Name of the additional property.
        value: A PolicyControllerMembershipSpec attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('PolicyControllerMembershipSpec', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  membershipSpecs = _messages.MessageField('MembershipSpecsValue', 1)


class PolicyControllerFeatureState(_messages.Message):
  r"""State for PolicyController

  Enums:
    StateValueValuesEnum: The overall Policy Controller lifecycle state
      observed by the Hub Feature controller.

  Messages:
    ComponentStatesValue: On-cluster states of the components we would like to
      track. Currently these include (also serving as map keys): 1.
      "admission" 2. "audit" 3. "mutation"

  Fields:
    componentStates: On-cluster states of the components we would like to
      track. Currently these include (also serving as map keys): 1.
      "admission" 2. "audit" 3. "mutation"
    policyContentState: The overall content state observed by the Hub Feature
      controller.
    state: The overall Policy Controller lifecycle state observed by the Hub
      Feature controller.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The overall Policy Controller lifecycle state observed by the Hub
    Feature controller.

    Values:
      LIFECYCLE_STATE_UNSPECIFIED: The lifecycle state is unspecified.
      NOT_INSTALLED: Policy Controller (PC) does not exist on the given
        cluster, and no k8s resources of any type that are associated with the
        PC should exist there. The cluster does not possess a membership with
        the Hub Feature controller.
      INSTALLING: The Hub Feature controller possesses a Membership, however
        Policy Controller is not fully installed on the cluster. In this state
        the hub can be expected to be taking actions to install the PC on the
        cluster.
      ACTIVE: Policy Controller (PC) is fully installed on the cluster and in
        an operational mode. In this state the Hub Feature controller will be
        reconciling state with the PC, and the PC will be performing it's
        operational tasks per that software. Entering a READY state requires
        that the hub has confirmed the PC is installed and its pods are
        operational with the version of the PC the Hub Feature controller
        expects.
      UPDATING: Policy Controller (PC) is fully installed, but in the process
        of changing the configuration (including changing the version of PC
        either up and down, or modifying the manifests of PC) of the resources
        running on the cluster. The Hub Feature controller has a Membership,
        is aware of the version the cluster should be running in, but has not
        confirmed for itself that the PC is running with that version.
      DECOMMISSIONING: Policy Controller (PC) may have resources on the
        cluster, but the Hub Feature controller wishes to remove the
        Membership. The Membership still exists.
      CLUSTER_ERROR: Policy Controller (PC) is not operational, and the Hub
        Feature controller is unable to act to make it operational. Entering a
        CLUSTER_ERROR state happens automatically when the PCH determines that
        a PC installed on the cluster is non-operative or that the cluster
        does not meet requirements set for the Hub Feature controller to
        administer the cluster but has nevertheless been given an instruction
        to do so (such as 'install').
      HUB_ERROR: In this state, the PC may still be operational, and only the
        Hub Feature controller is unable to act. The hub should not issue
        instructions to change the PC state, or otherwise interfere with the
        on-cluster resources. Entering a HUB_ERROR state happens automatically
        when the Hub Feature controller determines the hub is in an unhealthy
        state and it wishes to 'take hands off' to avoid corrupting the PC or
        other data.
      SUSPENDED: Policy Controller (PC) is installed but suspended. This means
        that the policies are not enforced, but violations are still recorded
        (through audit).
      DETACHED: PoCo Hub is not taking any action to reconcile cluster
        objects. Changes to those objects will not be overwritten by PoCo Hub.
    """
    LIFECYCLE_STATE_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    INSTALLING = 2
    ACTIVE = 3
    UPDATING = 4
    DECOMMISSIONING = 5
    CLUSTER_ERROR = 6
    HUB_ERROR = 7
    SUSPENDED = 8
    DETACHED = 9

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ComponentStatesValue(_messages.Message):
    r"""On-cluster states of the components we would like to track. Currently
    these include (also serving as map keys): 1. "admission" 2. "audit" 3.
    "mutation"

    Messages:
      AdditionalProperty: An additional property for a ComponentStatesValue
        object.

    Fields:
      additionalProperties: Additional properties of type ComponentStatesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ComponentStatesValue object.

      Fields:
        key: Name of the additional property.
        value: A OnClusterState attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('OnClusterState', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  componentStates = _messages.MessageField('ComponentStatesValue', 1)
  policyContentState = _messages.MessageField('PolicyContentState', 2)
  state = _messages.EnumField('StateValueValuesEnum', 3)


class PolicyControllerHubConfig(_messages.Message):
  r"""Configuration for Policy Controller.

  Enums:
    InstallSpecValueValuesEnum: The install_spec represents the intended state
      specified by the latest request that mutated install_spec in the feature
      spec, not the lifecycle state of the feature observed by the Hub feature
      controller that is reported in the feature state.

  Messages:
    DeploymentConfigsValue: Map of deployment configs to deployments
      ("admission", "audit", "mutation").

  Fields:
    auditIntervalSeconds: Sets the interval for Policy Controller Audit Scans
      (in seconds). When set to 0, this disables audit functionality
      altogether.
    constraintViolationLimit: The maximum number of audit violations to be
      stored in a constraint. If not set, the internal default (currently 20)
      will be used.
    deploymentConfigs: Map of deployment configs to deployments ("admission",
      "audit", "mutation").
    exemptableNamespaces: The set of namespaces that are excluded from Policy
      Controller checks. Namespaces do not need to currently exist on the
      cluster.
    installSpec: The install_spec represents the intended state specified by
      the latest request that mutated install_spec in the feature spec, not
      the lifecycle state of the feature observed by the Hub feature
      controller that is reported in the feature state.
    logDeniesEnabled: Logs all denies and dry run failures.
    monitoring: Monitoring specifies the configuration of monitoring.
    mutationEnabled: Enables the ability to mutate resources using Policy
      Controller.
    policyContent: Specifies the desired policy content on the cluster
    referentialRulesEnabled: Enables the ability to use Constraint Templates
      that reference to objects other than the object currently being
      evaluated.
  """

  class InstallSpecValueValuesEnum(_messages.Enum):
    r"""The install_spec represents the intended state specified by the latest
    request that mutated install_spec in the feature spec, not the lifecycle
    state of the feature observed by the Hub feature controller that is
    reported in the feature state.

    Values:
      INSTALL_SPEC_UNSPECIFIED: Spec is unknown.
      INSTALL_SPEC_NOT_INSTALLED: Request to uninstall Policy Controller.
      INSTALL_SPEC_ENABLED: Request to install and enable Policy Controller.
      INSTALL_SPEC_SUSPENDED: Request to suspend Policy Controller i.e. its
        webhooks. If Policy Controller is not installed, it will be installed
        but suspended.
      INSTALL_SPEC_DETACHED: Request to stop all reconciliation actions by
        PoCo Hub controller. This is a breakglass mechanism to stop PoCo Hub
        from affecting cluster resources.
    """
    INSTALL_SPEC_UNSPECIFIED = 0
    INSTALL_SPEC_NOT_INSTALLED = 1
    INSTALL_SPEC_ENABLED = 2
    INSTALL_SPEC_SUSPENDED = 3
    INSTALL_SPEC_DETACHED = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DeploymentConfigsValue(_messages.Message):
    r"""Map of deployment configs to deployments ("admission", "audit",
    "mutation").

    Messages:
      AdditionalProperty: An additional property for a DeploymentConfigsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        DeploymentConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DeploymentConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A PolicyControllerDeploymentConfig attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('PolicyControllerDeploymentConfig', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  auditIntervalSeconds = _messages.IntegerField(1)
  constraintViolationLimit = _messages.IntegerField(2)
  deploymentConfigs = _messages.MessageField('DeploymentConfigsValue', 3)
  exemptableNamespaces = _messages.StringField(4, repeated=True)
  installSpec = _messages.EnumField('InstallSpecValueValuesEnum', 5)
  logDeniesEnabled = _messages.BooleanField(6)
  monitoring = _messages.MessageField('PolicyControllerMonitoringConfig', 7)
  mutationEnabled = _messages.BooleanField(8)
  policyContent = _messages.MessageField('PolicyContentSpec', 9)
  referentialRulesEnabled = _messages.BooleanField(10)


class PolicyControllerMembershipSpec(_messages.Message):
  r"""Configuration for a single cluster. Intended to parallel the
  PolicyController CR.

  Fields:
    policyControllerHubConfig: Policy Controller configuration for the
      cluster, managed by the Policy Controller Hub Feature controller.
    version: The version of the Policy Controller Feature.
  """

  policyControllerHubConfig = _messages.MessageField('PolicyControllerHubConfig', 1)
  version = _messages.StringField(2)


class PolicyControllerMigration(_messages.Message):
  r"""State for the migration of PolicyController from ACM -> PoCo Hub.

  Enums:
    StageValueValuesEnum: Stage of the migration.

  Fields:
    copyTime: Last time this membership spec was copied to PoCo feature.
    stage: Stage of the migration.
  """

  class StageValueValuesEnum(_messages.Enum):
    r"""Stage of the migration.

    Values:
      STAGE_UNSPECIFIED: Unknown state of migration.
      ACM_MANAGED: ACM Hub/Operator manages policycontroller. No migration yet
        completed.
      POCO_MANAGED: All migrations steps complete; Poco Hub now manages
        policycontroller.
    """
    STAGE_UNSPECIFIED = 0
    ACM_MANAGED = 1
    POCO_MANAGED = 2

  copyTime = _messages.StringField(1)
  stage = _messages.EnumField('StageValueValuesEnum', 2)


class PolicyControllerMonitoring(_messages.Message):
  r"""PolicyControllerMonitoring specifies the backends Policy Controller
  should export metrics to. For example, to specify metrics should be exported
  to Cloud Monitoring and Prometheus, specify backends: ["cloudmonitoring",
  "prometheus"]

  Enums:
    BackendsValueListEntryValuesEnum:

  Fields:
    backends: Specifies the list of backends Policy Controller will export to.
      An empty list would effectively disable metrics export.
  """

  class BackendsValueListEntryValuesEnum(_messages.Enum):
    r"""BackendsValueListEntryValuesEnum enum type.

    Values:
      MONITORING_BACKEND_UNSPECIFIED: Backend cannot be determined
      PROMETHEUS: Prometheus backend for monitoring
      CLOUD_MONITORING: Stackdriver/Cloud Monitoring backend for monitoring
    """
    MONITORING_BACKEND_UNSPECIFIED = 0
    PROMETHEUS = 1
    CLOUD_MONITORING = 2

  backends = _messages.EnumField('BackendsValueListEntryValuesEnum', 1, repeated=True)


class PolicyControllerMonitoringConfig(_messages.Message):
  r"""PolicyControllerMonitoringConfig specifies the backends Policy
  Controller should export metrics to. For example, to specify metrics should
  be exported to Cloud Monitoring and Prometheus, specify backends:
  ["prometheus", "cloudmonitoring"]

  Enums:
    BackendsValueListEntryValuesEnum:

  Fields:
    backends: Specifies the list of backends Policy Controller will export to.
      An empty list would effectively disable metrics export.
  """

  class BackendsValueListEntryValuesEnum(_messages.Enum):
    r"""BackendsValueListEntryValuesEnum enum type.

    Values:
      MONITORING_BACKEND_UNSPECIFIED: Backend cannot be determined
      PROMETHEUS: Prometheus backend for monitoring
      CLOUD_MONITORING: Stackdriver/Cloud Monitoring backend for monitoring
    """
    MONITORING_BACKEND_UNSPECIFIED = 0
    PROMETHEUS = 1
    CLOUD_MONITORING = 2

  backends = _messages.EnumField('BackendsValueListEntryValuesEnum', 1, repeated=True)


class PolicyControllerState(_messages.Message):
  r"""State for PolicyControllerState.

  Fields:
    deploymentState: The state about the policy controller installation.
    migration: Record state of ACM -> PoCo Hub migration for this feature.
    version: The version of Gatekeeper Policy Controller deployed.
  """

  deploymentState = _messages.MessageField('GatekeeperDeploymentState', 1)
  migration = _messages.MessageField('PolicyControllerMigration', 2)
  version = _messages.MessageField('PolicyControllerVersion', 3)


class PolicyControllerVersion(_messages.Message):
  r"""The build version of Gatekeeper Policy Controller is using.

  Fields:
    version: The gatekeeper image tag that is composed of ACM version, git
      tag, build number.
  """

  version = _messages.StringField(1)


class RBACRoleBindingActuationFeatureSpec(_messages.Message):
  r"""**RBAC RoleBinding Actuation**: The Hub-wide input for the
  RBACRoleBindingActuation feature.

  Fields:
    actuationDisabled: A boolean attribute.
  """

  actuationDisabled = _messages.BooleanField(1)


class RBACRoleBindingActuationFeatureState(_messages.Message):
  r"""**RBAC RoleBinding Actuation**: An empty state left as an example Hub-
  wide Feature state.
  """



class Reference(_messages.Message):
  r"""Represents a reference to a resource.

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    createTime: Output only. The creation time.
    details: Details of the reference type with no implied semantics.
      Cumulative size of the field must not be more than 1KiB.
    name: Output only. Relative resource name of the reference. Includes
      target resource as a parent and reference uid
      `{target_resource}/references/{reference_id}`. For example,
      `projects/{my-project}/locations/{location}/instances/{my-
      instance}/references/{xyz}`.
    sourceResource: Required. Full resource name of the resource which refers
      the target resource. For example:
      //tpu.googleapis.com/projects/myproject/nodes/mynode
    targetUniqueId: Output only. The unique_id of the target resource. Example
      1: (For arcus resource) A-1-0-2-387420123-13-913517247483640811
      unique_id format defined in go/m11n-unique-id-as-resource-id Example 2:
      (For CCFE resource) 123e4567-e89b-12d3-a456-************
    type: Required. Type of the reference. A service might impose limits on
      number of references of a specific type. Note: It's recommended to use
      CAPITALS_WITH_UNDERSCORES style for a type name.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  name = _messages.StringField(3)
  sourceResource = _messages.StringField(4)
  targetUniqueId = _messages.StringField(5)
  type = _messages.StringField(6)


class ResourceList(_messages.Message):
  r"""ResourceList contains container resource requirements.

  Fields:
    cpu: CPU requirement expressed in Kubernetes resource units.
    memory: Memory requirement expressed in Kubernetes resource units.
  """

  cpu = _messages.StringField(1)
  memory = _messages.StringField(2)


class ResourceRequirements(_messages.Message):
  r"""ResourceRequirements describes the compute resource requirements.

  Fields:
    limits: Limits describes the maximum amount of compute resources allowed
      for use by the running container.
    requests: Requests describes the amount of compute resources reserved for
      the container by the kube-scheduler.
  """

  limits = _messages.MessageField('ResourceList', 1)
  requests = _messages.MessageField('ResourceList', 2)


class RoutingConfig(_messages.Message):
  r"""RoutingConfig configures the behaviour of fleet logging feature.

  Enums:
    ModeValueValuesEnum: mode configures the logs routing mode.

  Fields:
    mode: mode configures the logs routing mode.
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""mode configures the logs routing mode.

    Values:
      MODE_UNSPECIFIED: If UNSPECIFIED, fleet logging feature is disabled.
      COPY: logs will be copied to the destination project.
      MOVE: logs will be moved to the destination project.
    """
    MODE_UNSPECIFIED = 0
    COPY = 1
    MOVE = 2

  mode = _messages.EnumField('ModeValueValuesEnum', 1)


class Rule(_messages.Message):
  r"""A rule to be applied in a Policy.

  Enums:
    ActionValueValuesEnum: Required

  Fields:
    action: Required
    conditions: Additional restrictions that must be met. All conditions must
      pass for the rule to match.
    description: Human-readable description of the rule.
    in_: If one or more 'in' clauses are specified, the rule matches if the
      PRINCIPAL/AUTHORITY_SELECTOR is in at least one of these entries.
    logConfig: The config returned to callers of CheckPolicy for any entries
      that match the LOG action.
    notIn: If one or more 'not_in' clauses are specified, the rule matches if
      the PRINCIPAL/AUTHORITY_SELECTOR is in none of the entries. The format
      for in and not_in entries can be found at in the Local IAM documentation
      (see go/local-iam#features).
    permissions: A permission is a string of form `..` (e.g.,
      'storage.buckets.list'). A value of '*' matches all permissions, and a
      verb part of '*' (e.g., 'storage.buckets.*') matches all verbs.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required

    Values:
      NO_ACTION: Default no action.
      ALLOW: Matching 'Entries' grant access.
      ALLOW_WITH_LOG: Matching 'Entries' grant access and the caller promises
        to log the request per the returned log_configs.
      DENY: Matching 'Entries' deny access.
      DENY_WITH_LOG: Matching 'Entries' deny access and the caller promises to
        log the request per the returned log_configs.
      LOG: Matching 'Entries' tell IAM.Check callers to generate logs.
    """
    NO_ACTION = 0
    ALLOW = 1
    ALLOW_WITH_LOG = 2
    DENY = 3
    DENY_WITH_LOG = 4
    LOG = 5

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  conditions = _messages.MessageField('GoogleIamV1Condition', 2, repeated=True)
  description = _messages.StringField(3)
  in_ = _messages.StringField(4, repeated=True)
  logConfig = _messages.MessageField('LogConfig', 5, repeated=True)
  notIn = _messages.StringField(6, repeated=True)
  permissions = _messages.StringField(7, repeated=True)


class SamlConfig(_messages.Message):
  r"""Configuration for the SAML Auth flow.

  Messages:
    AttributeMappingValue: Optional. The mapping of additional user attributes
      like nickname, birthday and address etc.. `key` is the name of this
      additional attribute. `value` is a string presenting as CEL(common
      expression language, go/cel) used for getting the value from the
      resources. Take nickname as an example, in this case, `key` is
      "attribute.nickname" and `value` is "assertion.nickname".

  Fields:
    attributeMapping: Optional. The mapping of additional user attributes like
      nickname, birthday and address etc.. `key` is the name of this
      additional attribute. `value` is a string presenting as CEL(common
      expression language, go/cel) used for getting the value from the
      resources. Take nickname as an example, in this case, `key` is
      "attribute.nickname" and `value` is "assertion.nickname".
    groupPrefix: Optional. Prefix to prepend to group name.
    groupsAttribute: Optional. The SAML attribute to read groups from. This
      value is expected to be a string and will be passed along as-is (with
      the option of being prefixed by the `group_prefix`).
    identityProviderCertificates: Required. The list of IdP certificates to
      validate the SAML response against.
    identityProviderId: Required. The entity ID of the SAML IdP.
    identityProviderSsoUri: Required. The URI where the SAML IdP exposes the
      SSO service.
    userAttribute: Optional. The SAML attribute to read username from. If
      unspecified, the username will be read from the NameID element of the
      assertion in SAML response. This value is expected to be a string and
      will be passed along as-is (with the option of being prefixed by the
      `user_prefix`).
    userPrefix: Optional. Prefix to prepend to user name.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AttributeMappingValue(_messages.Message):
    r"""Optional. The mapping of additional user attributes like nickname,
    birthday and address etc.. `key` is the name of this additional attribute.
    `value` is a string presenting as CEL(common expression language, go/cel)
    used for getting the value from the resources. Take nickname as an
    example, in this case, `key` is "attribute.nickname" and `value` is
    "assertion.nickname".

    Messages:
      AdditionalProperty: An additional property for a AttributeMappingValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        AttributeMappingValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AttributeMappingValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attributeMapping = _messages.MessageField('AttributeMappingValue', 1)
  groupPrefix = _messages.StringField(2)
  groupsAttribute = _messages.StringField(3)
  identityProviderCertificates = _messages.StringField(4, repeated=True)
  identityProviderId = _messages.StringField(5)
  identityProviderSsoUri = _messages.StringField(6)
  userAttribute = _messages.StringField(7)
  userPrefix = _messages.StringField(8)


class ServerConfig(_messages.Message):
  r"""Server settings for the external LDAP server.

  Fields:
    certificateAuthorityData: Optional. Contains a Base64 encoded, PEM
      formatted certificate authority certificate for the LDAP server. This
      must be provided for the "ldaps" and "startTLS" connections.
    connectionType: Optional. Defines the connection type to communicate with
      the LDAP server. If `starttls` or `ldaps` is specified, the
      certificate_authority_data should not be empty.
    host: Required. Defines the hostname or IP of the LDAP server. Port is
      optional and will default to 389, if unspecified. For example,
      "ldap.server.example" or "***********:389".
  """

  certificateAuthorityData = _messages.BytesField(1)
  connectionType = _messages.StringField(2)
  host = _messages.StringField(3)


class ServiceAccountConfig(_messages.Message):
  r"""Contains the credentials of the service account which is authorized to
  perform the LDAP search in the directory. The credentials can be supplied by
  the combination of the DN and password or the client certificate.

  Fields:
    simpleBindCredentials: Credentials for basic auth.
  """

  simpleBindCredentials = _messages.MessageField('SimpleBindCredentials', 1)


class ServiceDirectoryFeatureSpec(_messages.Message):
  r"""An empty spec for service directory feature. This is required since
  Feature proto requires a spec.
  """



class ServiceDirectoryFeatureState(_messages.Message):
  r"""An empty state for service directory feature. This is rqeuired since
  FeatureStateDetails requires a state.
  """



class ServiceMeshAnalysisMessage(_messages.Message):
  r"""ServiceMeshAnalysisMessage is a single message produced by an analyzer,
  and it used to communicate to the end user about the state of their Service
  Mesh configuration.

  Messages:
    ArgsValue: A UI can combine these args with a template (based on
      message_base.type) to produce an internationalized message.

  Fields:
    args: A UI can combine these args with a template (based on
      message_base.type) to produce an internationalized message.
    description: A human readable description of what the error means. It is
      suitable for non-internationalize display purposes.
    messageBase: Details common to all types of Istio and ServiceMesh analysis
      messages.
    resourcePaths: A list of strings specifying the resource identifiers that
      were the cause of message generation. A "path" here may be: *
      MEMBERSHIP_ID if the cause is a specific member cluster *
      MEMBERSHIP_ID/(NAMESPACE\/)?RESOURCETYPE/NAME if the cause is a resource
      in a cluster
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ArgsValue(_messages.Message):
    r"""A UI can combine these args with a template (based on
    message_base.type) to produce an internationalized message.

    Messages:
      AdditionalProperty: An additional property for a ArgsValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ArgsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  args = _messages.MessageField('ArgsValue', 1)
  description = _messages.StringField(2)
  messageBase = _messages.MessageField('ServiceMeshAnalysisMessageBase', 3)
  resourcePaths = _messages.StringField(4, repeated=True)


class ServiceMeshAnalysisMessageBase(_messages.Message):
  r"""ServiceMeshAnalysisMessageBase describes some common information that is
  needed for all messages.

  Enums:
    LevelValueValuesEnum: Represents how severe a message is.

  Fields:
    documentationUrl: A url pointing to the Service Mesh or Istio
      documentation for this specific error type.
    level: Represents how severe a message is.
    type: Represents the specific type of a message.
  """

  class LevelValueValuesEnum(_messages.Enum):
    r"""Represents how severe a message is.

    Values:
      LEVEL_UNSPECIFIED: Illegal. Same
        istio.analysis.v1alpha1.AnalysisMessageBase.Level.UNKNOWN.
      ERROR: ERROR represents a misconfiguration that must be fixed.
      WARNING: WARNING represents a misconfiguration that should be fixed.
      INFO: INFO represents an informational finding.
    """
    LEVEL_UNSPECIFIED = 0
    ERROR = 1
    WARNING = 2
    INFO = 3

  documentationUrl = _messages.StringField(1)
  level = _messages.EnumField('LevelValueValuesEnum', 2)
  type = _messages.MessageField('Type', 3)


class ServiceMeshFeatureSpec(_messages.Message):
  r"""ServiceMeshFeatureSpec contains the input for the service mesh feature.

  Messages:
    MembershipSpecsValue: Optional. Map from full path to the membership, to
      its individual config.

  Fields:
    fleetDefaultMemberConfig: Optional. The default fleet configuration to be
      applied to all memberships.
    membershipSpecs: Optional. Map from full path to the membership, to its
      individual config.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MembershipSpecsValue(_messages.Message):
    r"""Optional. Map from full path to the membership, to its individual
    config.

    Messages:
      AdditionalProperty: An additional property for a MembershipSpecsValue
        object.

    Fields:
      additionalProperties: Additional properties of type MembershipSpecsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MembershipSpecsValue object.

      Fields:
        key: Name of the additional property.
        value: A ServiceMeshMembershipSpec attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ServiceMeshMembershipSpec', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  fleetDefaultMemberConfig = _messages.MessageField('ServiceMeshMembershipSpec', 1)
  membershipSpecs = _messages.MessageField('MembershipSpecsValue', 2)


class ServiceMeshFeatureState(_messages.Message):
  r"""ServiceMeshFeatureState describes the state of the Service Mesh hub
  feature as analyzed by the Service Mesh Hub Controller.

  Enums:
    DefaultChannelValueValuesEnum: Release channel to use for default
      injection and service mesh APIs.

  Fields:
    analysisMessages: Output only. Results of running Service Mesh analyzers
      against member clusters, or the entire mesh.
    conditions: Output only. List of conditions reported for this membership.
    configApiVersion: The API version (i.e. Istio CRD version) for configuring
      service mesh in this cluster. This version is influenced by the
      `default_channel` field.
    controlPlaneManagement: Output only. Status of control plane management
    controlPlaneRevisions: Output only. State of all control plane revisions
      that are available in the cluster.
    dataPlaneManagement: Output only. Status of data plane management.
    defaultChannel: Release channel to use for default injection and service
      mesh APIs.
    meshConnectivity: Output only. Status of cross cluster load balancing
      between other clusters in the mesh.
  """

  class DefaultChannelValueValuesEnum(_messages.Enum):
    r"""Release channel to use for default injection and service mesh APIs.

    Values:
      CHANNEL_UNSPECIFIED: Unspecified
      RAPID: RAPID channel is offered on an early access basis for customers
        who want to test new releases.
      REGULAR: REGULAR channel is intended for production users who want to
        take advantage of new features.
      STABLE: STABLE channel includes versions that are known to be stable and
        reliable in production.
    """
    CHANNEL_UNSPECIFIED = 0
    RAPID = 1
    REGULAR = 2
    STABLE = 3

  analysisMessages = _messages.MessageField('ServiceMeshAnalysisMessage', 1, repeated=True)
  conditions = _messages.MessageField('Condition', 2, repeated=True)
  configApiVersion = _messages.StringField(3)
  controlPlaneManagement = _messages.MessageField('ControlPlaneManagement', 4)
  controlPlaneRevisions = _messages.MessageField('ControlPlaneRevision', 5, repeated=True)
  dataPlaneManagement = _messages.MessageField('DataPlaneManagement', 6)
  defaultChannel = _messages.EnumField('DefaultChannelValueValuesEnum', 7)
  meshConnectivity = _messages.MessageField('MeshConnectivity', 8)


class ServiceMeshMembershipSpec(_messages.Message):
  r"""**Service Mesh**: Spec for a single Membership for the servicemesh
  feature

  Enums:
    ConfigApiValueValuesEnum: Optional. Specifies the API that will be used
      for configuring the mesh workloads.
    ControlPlaneValueValuesEnum: Deprecated: use `management` instead Enables
      automatic control plane management.
    DataPlaneValueValuesEnum: Enables automatic data plane management.
    DefaultChannelValueValuesEnum: Determines which release channel to use for
      default injection and service mesh APIs.
    ManagementValueValuesEnum: Optional. Enables automatic Service Mesh
      management.

  Fields:
    configApi: Optional. Specifies the API that will be used for configuring
      the mesh workloads.
    controlPlane: Deprecated: use `management` instead Enables automatic
      control plane management.
    dataPlane: Enables automatic data plane management.
    defaultChannel: Determines which release channel to use for default
      injection and service mesh APIs.
    management: Optional. Enables automatic Service Mesh management.
  """

  class ConfigApiValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies the API that will be used for configuring the mesh
    workloads.

    Values:
      CONFIG_API_UNSPECIFIED: Unspecified
      CONFIG_API_ISTIO: Use the Istio API for configuration.
      CONFIG_API_GATEWAY: Use the K8s Gateway API for configuration.
    """
    CONFIG_API_UNSPECIFIED = 0
    CONFIG_API_ISTIO = 1
    CONFIG_API_GATEWAY = 2

  class ControlPlaneValueValuesEnum(_messages.Enum):
    r"""Deprecated: use `management` instead Enables automatic control plane
    management.

    Values:
      CONTROL_PLANE_MANAGEMENT_UNSPECIFIED: Unspecified
      AUTOMATIC: Google should provision a control plane revision and make it
        available in the cluster. Google will enroll this revision in a
        release channel and keep it up to date. The control plane revision may
        be a managed service, or a managed install.
      MANUAL: User will manually configure the control plane (e.g. via CLI, or
        via the ControlPlaneRevision KRM API)
    """
    CONTROL_PLANE_MANAGEMENT_UNSPECIFIED = 0
    AUTOMATIC = 1
    MANUAL = 2

  class DataPlaneValueValuesEnum(_messages.Enum):
    r"""Enables automatic data plane management.

    Values:
      DATA_PLANE_MANAGEMENT_UNSPECIFIED: Unspecified
      DATA_PLANE_MANAGEMENT_AUTOMATIC: Enables Google-managed data plane that
        provides L7 service mesh capabilities. Data plane management is
        enabled at the cluster level. Users can exclude individual workloads
        or namespaces.
      DATA_PLANE_MANAGEMENT_MANUAL: User will manage their L7 data plane.
    """
    DATA_PLANE_MANAGEMENT_UNSPECIFIED = 0
    DATA_PLANE_MANAGEMENT_AUTOMATIC = 1
    DATA_PLANE_MANAGEMENT_MANUAL = 2

  class DefaultChannelValueValuesEnum(_messages.Enum):
    r"""Determines which release channel to use for default injection and
    service mesh APIs.

    Values:
      CHANNEL_UNSPECIFIED: Unspecified
      RAPID: RAPID channel is offered on an early access basis for customers
        who want to test new releases.
      REGULAR: REGULAR channel is intended for production users who want to
        take advantage of new features.
      STABLE: STABLE channel includes versions that are known to be stable and
        reliable in production.
    """
    CHANNEL_UNSPECIFIED = 0
    RAPID = 1
    REGULAR = 2
    STABLE = 3

  class ManagementValueValuesEnum(_messages.Enum):
    r"""Optional. Enables automatic Service Mesh management.

    Values:
      MANAGEMENT_UNSPECIFIED: Unspecified
      MANAGEMENT_AUTOMATIC: Google should manage my Service Mesh for the
        cluster.
      MANAGEMENT_MANUAL: User will manually configure their service mesh
        components.
      MANAGEMENT_NOT_INSTALLED: Google should remove any managed Service Mesh
        components from this cluster and deprovision any resources.
    """
    MANAGEMENT_UNSPECIFIED = 0
    MANAGEMENT_AUTOMATIC = 1
    MANAGEMENT_MANUAL = 2
    MANAGEMENT_NOT_INSTALLED = 3

  configApi = _messages.EnumField('ConfigApiValueValuesEnum', 1)
  controlPlane = _messages.EnumField('ControlPlaneValueValuesEnum', 2)
  dataPlane = _messages.EnumField('DataPlaneValueValuesEnum', 3)
  defaultChannel = _messages.EnumField('DefaultChannelValueValuesEnum', 4)
  management = _messages.EnumField('ManagementValueValuesEnum', 5)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class SimpleBindCredentials(_messages.Message):
  r"""The structure holds the LDAP simple binding credential.

  Fields:
    dn: Required. The distinguished name(DN) of the service account
      object/user.
    encryptedPassword: Output only. The encrypted password of the service
      account object/user.
    password: Required. Input only. The password of the service account
      object/user.
  """

  dn = _messages.StringField(1)
  encryptedPassword = _messages.BytesField(2)
  password = _messages.StringField(3)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""Status specifies state for the subcomponent.

  Enums:
    CodeValueValuesEnum: Code specifies AppDevExperienceFeature's subcomponent
      ready state.

  Fields:
    code: Code specifies AppDevExperienceFeature's subcomponent ready state.
    description: Description is populated if Code is Failed, explaining why it
      has failed.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Code specifies AppDevExperienceFeature's subcomponent ready state.

    Values:
      CODE_UNSPECIFIED: Not set.
      OK: AppDevExperienceFeature's specified subcomponent is ready.
      FAILED: AppDevExperienceFeature's specified subcomponent ready state is
        false. This means AppDevExperienceFeature has encountered an issue
        that blocks all, or a portion, of its normal operation. See the
        `description` for more details.
      UNKNOWN: AppDevExperienceFeature's specified subcomponent has a pending
        or unknown state.
    """
    CODE_UNSPECIFIED = 0
    OK = 1
    FAILED = 2
    UNKNOWN = 3

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  description = _messages.StringField(2)


class StatusDetails(_messages.Message):
  r"""Structured and human-readable details for a status.

  Fields:
    code: A machine-readable code that further describes a broad status.
    details: Human-readable explanation of code.
  """

  code = _messages.StringField(1)
  details = _messages.StringField(2)


class SyncError(_messages.Message):
  r"""An ACM created error representing a problem syncing configurations

  Fields:
    code: An ACM defined error code
    errorMessage: A description of the error
    errorResources: A list of config(s) associated with the error, if any
  """

  code = _messages.StringField(1)
  errorMessage = _messages.StringField(2)
  errorResources = _messages.MessageField('ErrorResource', 3, repeated=True)


class SyncState(_messages.Message):
  r"""State indicating an ACM's progress syncing configurations to a cluster

  Enums:
    CodeValueValuesEnum: Sync status code

  Fields:
    code: Sync status code
    errors: A list of errors resulting from problematic configs. This list
      will be truncated after 100 errors, although it is unlikely for that
      many errors to simultaneously exist.
    importToken: Token indicating the state of the importer.
    lastSync: Timestamp of when ACM last successfully synced the repo The time
      format is specified in https://golang.org/pkg/time/#Time.String This
      field is being deprecated. Use last_sync_time instead.
    lastSyncTime: Timestamp type of when ACM last successfully synced the repo
    sourceToken: Token indicating the state of the repo.
    syncToken: Token indicating the state of the syncer.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Sync status code

    Values:
      SYNC_CODE_UNSPECIFIED: Config Sync cannot determine a sync code
      SYNCED: Config Sync successfully synced the git Repo with the cluster
      PENDING: Config Sync is in the progress of syncing a new change
      ERROR: Indicates an error configuring Config Sync, and user action is
        required
      NOT_CONFIGURED: Config Sync has been installed but not configured
      NOT_INSTALLED: Config Sync has not been installed
      UNAUTHORIZED: Error authorizing with the cluster
      UNREACHABLE: Cluster could not be reached
    """
    SYNC_CODE_UNSPECIFIED = 0
    SYNCED = 1
    PENDING = 2
    ERROR = 3
    NOT_CONFIGURED = 4
    NOT_INSTALLED = 5
    UNAUTHORIZED = 6
    UNREACHABLE = 7

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  errors = _messages.MessageField('SyncError', 2, repeated=True)
  importToken = _messages.StringField(3)
  lastSync = _messages.StringField(4)
  lastSyncTime = _messages.StringField(5)
  sourceToken = _messages.StringField(6)
  syncToken = _messages.StringField(7)


class TemplateLibraryConfig(_messages.Message):
  r"""The config specifying which default library templates to install.

  Enums:
    InstallationValueValuesEnum: Configures the manner in which the template
      library is installed on the cluster.

  Fields:
    installation: Configures the manner in which the template library is
      installed on the cluster.
  """

  class InstallationValueValuesEnum(_messages.Enum):
    r"""Configures the manner in which the template library is installed on
    the cluster.

    Values:
      INSTALLATION_UNSPECIFIED: No installation strategy has been specified.
      NOT_INSTALLED: Do not install the template library.
      ALL: Install the entire template library.
    """
    INSTALLATION_UNSPECIFIED = 0
    NOT_INSTALLED = 1
    ALL = 2

  installation = _messages.EnumField('InstallationValueValuesEnum', 1)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class Toleration(_messages.Message):
  r"""Toleration of a node taint.

  Fields:
    effect: Matches a taint effect.
    key: Matches a taint key (not necessarily unique).
    operator: Matches a taint operator.
    value: Matches a taint value.
  """

  effect = _messages.StringField(1)
  key = _messages.StringField(2)
  operator = _messages.StringField(3)
  value = _messages.StringField(4)


class Type(_messages.Message):
  r"""A unique identifier for the type of message. Display_name is intended to
  be human-readable, code is intended to be machine readable. There should be
  a one-to-one mapping between display_name and code. (i.e. do not re-use
  display_names or codes between message types.) See
  istio.analysis.v1alpha1.AnalysisMessageBase.Type

  Fields:
    code: A 7 character code matching `^IST[0-9]{4}$` or `^ASM[0-9]{4}$`,
      intended to uniquely identify the message type. (e.g. "IST0001" is
      mapped to the "InternalError" message type.)
    displayName: A human-readable name for the message type. e.g.
      "InternalError", "PodMissingProxy". This should be the same for all
      messages of the same type. (This corresponds to the `name` field in
      open-source Istio.)
  """

  code = _messages.StringField(1)
  displayName = _messages.StringField(2)


class UserConfig(_messages.Message):
  r"""Defines where users exist in the LDAP directory.

  Fields:
    baseDn: Required. The location of the subtree in the LDAP directory to
      search for user entries.
    filter: Optional. Filter to apply when searching for the user. This can be
      used to further restrict the user accounts which are allowed to login.
      This defaults to "(objectClass=User)".
    idAttribute: Optional. Determines which attribute to use as the user's
      identity after they are authenticated. This is distinct from the
      loginAttribute field to allow users to login with a username, but then
      have their actual identifier be an email address or full Distinguished
      Name (DN). For example, setting loginAttribute to "sAMAccountName" and
      identifierAttribute to "userPrincipalName" would allow a user to login
      as "bsmith", but actual RBAC policies for the user would be written as
      "<EMAIL>". Using "userPrincipalName" is recommended since
      this will be unique for each user. This defaults to "userPrincipalName".
    loginAttribute: Optional. The name of the attribute which matches against
      the input username. This is used to find the user in the LDAP database
      e.g. "(=)" and is combined with the optional filter field. This defaults
      to "userPrincipalName".
  """

  baseDn = _messages.StringField(1)
  filter = _messages.StringField(2)
  idAttribute = _messages.StringField(3)
  loginAttribute = _messages.StringField(4)


class WorkloadCertificateFeatureSpec(_messages.Message):
  r"""WorkloadCertificateFeatureSpec contains the input for the workload
  identity platform feature. This is required since Feature proto requires a
  spec.

  Enums:
    ProvisionGoogleCaValueValuesEnum: Immutable. Specifies CA configuration.

  Messages:
    MemberConfigsValue: Per-member configuration of workload certificate.

  Fields:
    defaultConfig: Default membership spec. Users can override the default in
      the member_configs for each member.
    memberConfigs: Per-member configuration of workload certificate.
    provisionGoogleCa: Immutable. Specifies CA configuration.
  """

  class ProvisionGoogleCaValueValuesEnum(_messages.Enum):
    r"""Immutable. Specifies CA configuration.

    Values:
      GOOGLE_CA_PROVISIONING_UNSPECIFIED: Disable default Google managed CA.
      DISABLED: Disable default Google managed CA.
      ENABLED: Use default Google managed CA.
      ENABLED_WITH_MANAGED_CA: Workload certificate feature is enabled, and
        the entire certificate provisioning process is managed by Google with
        managed CAS which is more secure than the default CA.
      ENABLED_WITH_DEFAULT_CA: Workload certificate feature is enabled, and
        the entire certificate provisioning process is using the default CA
        which is free.
    """
    GOOGLE_CA_PROVISIONING_UNSPECIFIED = 0
    DISABLED = 1
    ENABLED = 2
    ENABLED_WITH_MANAGED_CA = 3
    ENABLED_WITH_DEFAULT_CA = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MemberConfigsValue(_messages.Message):
    r"""Per-member configuration of workload certificate.

    Messages:
      AdditionalProperty: An additional property for a MemberConfigsValue
        object.

    Fields:
      additionalProperties: Additional properties of type MemberConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MemberConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A WorkloadCertificateMembershipSpec attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('WorkloadCertificateMembershipSpec', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  defaultConfig = _messages.MessageField('WorkloadCertificateMembershipSpec', 1)
  memberConfigs = _messages.MessageField('MemberConfigsValue', 2)
  provisionGoogleCa = _messages.EnumField('ProvisionGoogleCaValueValuesEnum', 3)


class WorkloadCertificateFeatureState(_messages.Message):
  r"""WorkloadCertificateFeatureState describes the state of the workload
  certificate feature. This is required since FeatureStateDetails requires a
  state.
  """



class WorkloadCertificateMembershipSpec(_messages.Message):
  r"""WorkloadCertificateMembershipSpec contains the membership-specific input
  for WorkloadCertificate feature.

  Enums:
    CertificateManagementValueValuesEnum: Specifies workload certificate
      management.

  Fields:
    certificateManagement: Specifies workload certificate management.
  """

  class CertificateManagementValueValuesEnum(_messages.Enum):
    r"""Specifies workload certificate management.

    Values:
      CERTIFICATE_MANAGEMENT_UNSPECIFIED: Disable workload certificate
        feature.
      DISABLED: Disable workload certificate feature.
      ENABLED: Enable workload certificate feature.
    """
    CERTIFICATE_MANAGEMENT_UNSPECIFIED = 0
    DISABLED = 1
    ENABLED = 2

  certificateManagement = _messages.EnumField('CertificateManagementValueValuesEnum', 1)


encoding.AddCustomJsonFieldMapping(
    Rule, 'in_', 'in')
encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    GkehubProjectsLocationsFeaturesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
