"""Generated message classes for gkehub version v1beta1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'gkehub'


class ApplianceCluster(_messages.Message):
  r"""ApplianceCluster contains information specific to GDC Edge Appliance
  Clusters.

  Fields:
    resourceLink: Immutable. Self-link of the GCP resource for the Appliance
      Cluster. For example: //transferappliance.googleapis.com/projects/my-
      project/locations/us-west1-a/appliances/my-appliance
  """

  resourceLink = _messages.StringField(1)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    ignoreChildExemptions: A boolean attribute.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  ignoreChildExemptions = _messages.BooleanField(2)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 3)


class Authority(_messages.Message):
  r"""Authority encodes how Google will recognize identities from this
  Membership. See the workload identity documentation for more details:
  https://cloud.google.com/kubernetes-engine/docs/how-to/workload-identity

  Fields:
    identityProvider: Output only. An identity provider that reflects the
      `issuer` in the workload identity pool.
    issuer: Optional. A JSON Web Token (JWT) issuer URI. `issuer` must start
      with `https://` and be a valid URL with length <2000 characters. If set,
      then Google will allow valid OIDC tokens from this issuer to
      authenticate within the workload_identity_pool. OIDC discovery will be
      performed on this URI to validate tokens from the issuer. Clearing
      `issuer` disables Workload Identity. `issuer` cannot be directly
      modified; it must be cleared (and Workload Identity disabled) before
      using a new issuer (and re-enabling Workload Identity).
    oidcJwks: Optional. OIDC verification keys for this Membership in JWKS
      format (RFC 7517). When this field is set, OIDC discovery will NOT be
      performed on `issuer`, and instead OIDC tokens will be validated using
      this field.
    scopeTenancyIdentityProvider: Optional. Output only. The identity provider
      for the scope-tenancy workload identity pool.
    scopeTenancyWorkloadIdentityPool: Optional. Output only. The name of the
      scope-tenancy workload identity pool. This pool is set in the fleet-
      level feature.
    workloadIdentityPool: Output only. The name of the workload identity pool
      in which `issuer` will be recognized. There is a single Workload
      Identity Pool per Hub that is shared between all Memberships that belong
      to that Hub. For a Hub hosted in {PROJECT_ID}, the workload pool format
      is `{PROJECT_ID}.hub.id.goog`, although this is subject to change in
      newer versions of this API.
  """

  identityProvider = _messages.StringField(1)
  issuer = _messages.StringField(2)
  oidcJwks = _messages.BytesField(3)
  scopeTenancyIdentityProvider = _messages.StringField(4)
  scopeTenancyWorkloadIdentityPool = _messages.StringField(5)
  workloadIdentityPool = _messages.StringField(6)


class AuthorizationLoggingOptions(_messages.Message):
  r"""Authorization-related information used by Cloud Audit Logging.

  Enums:
    PermissionTypeValueValuesEnum: The type of the permission that was
      checked.

  Fields:
    permissionType: The type of the permission that was checked.
  """

  class PermissionTypeValueValuesEnum(_messages.Enum):
    r"""The type of the permission that was checked.

    Values:
      PERMISSION_TYPE_UNSPECIFIED: Default. Should not be used.
      ADMIN_READ: A read of admin (meta) data.
      ADMIN_WRITE: A write of admin (meta) data.
      DATA_READ: A read of standard data.
      DATA_WRITE: A write of standard data.
    """
    PERMISSION_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    ADMIN_WRITE = 2
    DATA_READ = 3
    DATA_WRITE = 4

  permissionType = _messages.EnumField('PermissionTypeValueValuesEnum', 1)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    bindingId: A string attribute.
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  bindingId = _messages.StringField(1)
  condition = _messages.MessageField('Expr', 2)
  members = _messages.StringField(3, repeated=True)
  role = _messages.StringField(4)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CloudAuditOptions(_messages.Message):
  r"""Write a Cloud Audit log

  Enums:
    LogNameValueValuesEnum: The log_name to populate in the Cloud Audit
      Record.
    PermissionTypeValueValuesEnum: The type associated with the permission.

  Fields:
    authorizationLoggingOptions: Information used by the Cloud Audit Logging
      pipeline. Will be deprecated once the migration to PermissionType is
      complete (b/201806118).
    logName: The log_name to populate in the Cloud Audit Record.
    permissionType: The type associated with the permission.
  """

  class LogNameValueValuesEnum(_messages.Enum):
    r"""The log_name to populate in the Cloud Audit Record.

    Values:
      UNSPECIFIED_LOG_NAME: Default. Should not be used.
      ADMIN_ACTIVITY: Corresponds to "cloudaudit.googleapis.com/activity"
      DATA_ACCESS: Corresponds to "cloudaudit.googleapis.com/data_access"
    """
    UNSPECIFIED_LOG_NAME = 0
    ADMIN_ACTIVITY = 1
    DATA_ACCESS = 2

  class PermissionTypeValueValuesEnum(_messages.Enum):
    r"""The type associated with the permission.

    Values:
      PERMISSION_TYPE_UNSPECIFIED: Default. Should not be used.
      ADMIN_READ: Permissions that gate reading resource configuration or
        metadata.
      ADMIN_WRITE: Permissions that gate modification of resource
        configuration or metadata.
      DATA_READ: Permissions that gate reading user-provided data.
      DATA_WRITE: Permissions that gate writing user-provided data.
    """
    PERMISSION_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    ADMIN_WRITE = 2
    DATA_READ = 3
    DATA_WRITE = 4

  authorizationLoggingOptions = _messages.MessageField('AuthorizationLoggingOptions', 1)
  logName = _messages.EnumField('LogNameValueValuesEnum', 2)
  permissionType = _messages.EnumField('PermissionTypeValueValuesEnum', 3)


class Condition(_messages.Message):
  r"""A condition to be met.

  Enums:
    IamValueValuesEnum: Trusted attributes supplied by the IAM system.
    OpValueValuesEnum: An operator to apply the subject with.
    SysValueValuesEnum: Trusted attributes supplied by any service that owns
      resources and uses the IAM system for access control.

  Fields:
    iam: Trusted attributes supplied by the IAM system.
    op: An operator to apply the subject with.
    svc: Trusted attributes discharged by the service.
    sys: Trusted attributes supplied by any service that owns resources and
      uses the IAM system for access control.
    values: The objects of the condition.
  """

  class IamValueValuesEnum(_messages.Enum):
    r"""Trusted attributes supplied by the IAM system.

    Values:
      NO_ATTR: Default non-attribute.
      AUTHORITY: Either principal or (if present) authority selector.
      ATTRIBUTION: The principal (even if an authority selector is present),
        which must only be used for attribution, not authorization.
      SECURITY_REALM: Any of the security realms in the IAMContext
        (go/security-realms). When used with IN, the condition indicates "any
        of the request's realms match one of the given values; with NOT_IN,
        "none of the realms match any of the given values". Note that a value
        can be: - 'self:campus' (i.e., clients that are in the same campus) -
        'self:metro' (i.e., clients that are in the same metro) - 'self:cloud-
        region' (i.e., allow connections from clients that are in the same
        cloud region) - 'self:prod-region' (i.e., allow connections from
        clients that are in the same prod region) - 'guardians' (i.e., allow
        connections from its guardian realms. See go/security-realms-
        glossary#guardian for more information.) - 'cryto_core_guardians'
        (i.e., allow connections from its crypto core guardian realms. See
        go/security-realms-glossary#guardian for more information.) Crypto
        Core coverage is a super-set of Default coverage, containing
        information about coverage between higher tier data centers (e.g.,
        YAWNs). Most services should use Default coverage and only use Crypto
        Core coverage if the service is involved in greenfield turnup of new
        higher tier data centers (e.g., credential infrastructure, machine/job
        management systems, etc.). - 'self' [DEPRECATED] (i.e., allow
        connections from clients that are in the same security realm, which is
        currently but not guaranteed to be campus-sized) - a realm (e.g.,
        'campus-abc') - a realm group (e.g., 'realms-for-borg-cell-xx', see:
        go/realm-groups) A match is determined by a realm group membership
        check performed by a RealmAclRep object (go/realm-acl-howto). It is
        not permitted to grant access based on the *absence* of a realm, so
        realm conditions can only be used in a "positive" context (e.g.,
        ALLOW/IN or DENY/NOT_IN).
      APPROVER: An approver (distinct from the requester) that has authorized
        this request. When used with IN, the condition indicates that one of
        the approvers associated with the request matches the specified
        principal, or is a member of the specified group. Approvers can only
        grant additional access, and are thus only used in a strictly positive
        context (e.g. ALLOW/IN or DENY/NOT_IN).
      JUSTIFICATION_TYPE: What types of justifications have been supplied with
        this request. String values should match enum names from
        security.credentials.JustificationType, e.g. "MANUAL_STRING". It is
        not permitted to grant access based on the *absence* of a
        justification, so justification conditions can only be used in a
        "positive" context (e.g., ALLOW/IN or DENY/NOT_IN). Multiple
        justifications, e.g., a Buganizer ID and a manually-entered reason,
        are normal and supported.
      CREDENTIALS_TYPE: What type of credentials have been supplied with this
        request. String values should match enum names from
        security_loas_l2.CredentialsType - currently, only
        CREDS_TYPE_EMERGENCY is supported. It is not permitted to grant access
        based on the *absence* of a credentials type, so the conditions can
        only be used in a "positive" context (e.g., ALLOW/IN or DENY/NOT_IN).
      CREDS_ASSERTION: Properties of the credentials supplied with this
        request. See http://go/rpcsp-credential-assertions?polyglot=rpcsp-v1-0
        The conditions can only be used in a "positive" context (e.g.,
        ALLOW/IN or DENY/NOT_IN).
    """
    NO_ATTR = 0
    AUTHORITY = 1
    ATTRIBUTION = 2
    SECURITY_REALM = 3
    APPROVER = 4
    JUSTIFICATION_TYPE = 5
    CREDENTIALS_TYPE = 6
    CREDS_ASSERTION = 7

  class OpValueValuesEnum(_messages.Enum):
    r"""An operator to apply the subject with.

    Values:
      NO_OP: Default no-op.
      EQUALS: DEPRECATED. Use IN instead.
      NOT_EQUALS: DEPRECATED. Use NOT_IN instead.
      IN: The condition is true if the subject (or any element of it if it is
        a set) matches any of the supplied values.
      NOT_IN: The condition is true if the subject (or every element of it if
        it is a set) matches none of the supplied values.
      DISCHARGED: Subject is discharged
    """
    NO_OP = 0
    EQUALS = 1
    NOT_EQUALS = 2
    IN = 3
    NOT_IN = 4
    DISCHARGED = 5

  class SysValueValuesEnum(_messages.Enum):
    r"""Trusted attributes supplied by any service that owns resources and
    uses the IAM system for access control.

    Values:
      NO_ATTR: Default non-attribute type
      REGION: Region of the resource
      SERVICE: Service name
      NAME: Resource name
      IP: IP address of the caller
    """
    NO_ATTR = 0
    REGION = 1
    SERVICE = 2
    NAME = 3
    IP = 4

  iam = _messages.EnumField('IamValueValuesEnum', 1)
  op = _messages.EnumField('OpValueValuesEnum', 2)
  svc = _messages.StringField(3)
  sys = _messages.EnumField('SysValueValuesEnum', 4)
  values = _messages.StringField(5, repeated=True)


class ConnectAgentResource(_messages.Message):
  r"""ConnectAgentResource represents a Kubernetes resource manifest for
  Connect Agent deployment.

  Fields:
    manifest: YAML manifest of the resource.
    type: Kubernetes type of the resource.
  """

  manifest = _messages.StringField(1)
  type = _messages.MessageField('TypeMeta', 2)


class CounterOptions(_messages.Message):
  r"""Increment a streamz counter with the specified metric and field names.
  Metric names should start with a '/', generally be lowercase-only, and end
  in "_count". Field names should not contain an initial slash. The actual
  exported metric names will have "/iam/policy" prepended. Field names
  correspond to IAM request parameters and field values are their respective
  values. Supported field names: - "authority", which is "[token]" if
  IAMContext.token is present, otherwise the value of
  IAMContext.authority_selector if present, and otherwise a representation of
  IAMContext.principal; or - "iam_principal", a representation of
  IAMContext.principal even if a token or authority selector is present; or -
  "" (empty string), resulting in a counter with no fields. Examples: counter
  { metric: "/debug_access_count" field: "iam_principal" } ==> increment
  counter /iam/policy/debug_access_count {iam_principal=[value of
  IAMContext.principal]}

  Fields:
    customFields: Custom fields.
    field: The field value to attribute.
    metric: The metric to update.
  """

  customFields = _messages.MessageField('CustomField', 1, repeated=True)
  field = _messages.StringField(2)
  metric = _messages.StringField(3)


class CreateReferenceRequest(_messages.Message):
  r"""The CreateReferenceRequest request.

  Fields:
    parent: Required. The parent resource name (target_resource of this
      reference). For example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}`.
    reference: Required. The reference to be created.
    referenceId: The unique id of this resource. Must be unique within a scope
      of a target resource, but does not have to be globally unique. Reference
      ID is part of resource name of the reference. Resource name is generated
      in the following way: {parent}/references/{reference_id}. Reference ID
      field is currently required but id auto generation might be added in the
      future. It can be any arbitrary string, either GUID or any other string,
      however CLHs can use preprocess callbacks to perform a custom
      validation.
    requestId: Optional. Request ID is an idempotency ID of the request. It
      must be a valid UUID. Zero UUID (00000000-0000-0000-0000-000000000000)
      is not supported.
  """

  parent = _messages.StringField(1)
  reference = _messages.MessageField('Reference', 2)
  referenceId = _messages.StringField(3)
  requestId = _messages.StringField(4)


class CustomField(_messages.Message):
  r"""Custom fields. These can be used to create a counter with arbitrary
  field/value pairs. See: go/rpcsp-custom-fields.

  Fields:
    name: Name is the field name.
    value: Value is the field value. It is important that in contrast to the
      CounterOptions.field, the value here is a constant that is not derived
      from the IAMContext.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class DataAccessOptions(_messages.Message):
  r"""Write a Data Access (Gin) log

  Enums:
    LogModeValueValuesEnum:

  Fields:
    isDirectAuth: Indicates that access was granted by a regular grant policy
    logMode: A LogModeValueValuesEnum attribute.
  """

  class LogModeValueValuesEnum(_messages.Enum):
    r"""LogModeValueValuesEnum enum type.

    Values:
      LOG_MODE_UNSPECIFIED: Client is not required to write a partial Gin log
        immediately after the authorization check. If client chooses to write
        one and it fails, client may either fail open (allow the operation to
        continue) or fail closed (handle as a DENY outcome).
      LOG_FAIL_CLOSED: The application's operation in the context of which
        this authorization check is being made may only be performed if it is
        successfully logged to Gin. For instance, the authorization library
        may satisfy this obligation by emitting a partial log entry at
        authorization check time and only returning ALLOW to the application
        if it succeeds. If a matching Rule has this directive, but the client
        has not indicated that it will honor such requirements, then the IAM
        check will result in authorization failure by setting
        CheckPolicyResponse.success=false.
    """
    LOG_MODE_UNSPECIFIED = 0
    LOG_FAIL_CLOSED = 1

  isDirectAuth = _messages.BooleanField(1)
  logMode = _messages.EnumField('LogModeValueValuesEnum', 2)


class DeleteReferenceRequest(_messages.Message):
  r"""The DeleteReferenceRequest request.

  Fields:
    name: Required. Full resource name of the reference, in the following
      format:
      `//{targer_service}/{target_resource}/references/{reference_id}`. For
      example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}/references/{xyz}`.
    requestId: Optional. Request ID is an idempotency ID of the request. It
      must be a valid UUID. Zero UUID (00000000-0000-0000-0000-000000000000)
      is not supported.
  """

  name = _messages.StringField(1)
  requestId = _messages.StringField(2)


class EdgeCluster(_messages.Message):
  r"""EdgeCluster contains information specific to Google Edge Clusters.

  Fields:
    clusterVersion: Output only. The product version of the Edge Cluster, e.g.
      "1.8.0".
    resourceLink: Immutable. Self-link of the GCP resource for the Edge
      Cluster. For example: //edgecontainer.googleapis.com/projects/my-
      project/locations/us-west1-a/clusters/my-cluster
  """

  clusterVersion = _messages.StringField(1)
  resourceLink = _messages.StringField(2)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GenerateConnectManifestResponse(_messages.Message):
  r"""GenerateConnectManifestResponse contains manifest information for
  installing/upgrading a Connect agent.

  Fields:
    manifest: The ordered list of Kubernetes resources that need to be applied
      to the cluster for GKE Connect agent installation/upgrade.
  """

  manifest = _messages.MessageField('ConnectAgentResource', 1, repeated=True)


class GenerateExclusivityManifestResponse(_messages.Message):
  r"""The response of the exclusivity artifacts manifests for the client to
  apply.

  Fields:
    crManifest: The YAML manifest of the membership CR to apply if a new
      version of the CR is available. Empty if no update needs to be applied.
    crdManifest: The YAML manifest of the membership CRD to apply if a newer
      version of the CRD is available. Empty if no update needs to be applied.
  """

  crManifest = _messages.StringField(1)
  crdManifest = _messages.StringField(2)


class GetReferenceRequest(_messages.Message):
  r"""The GetReferenceRequest request.

  Fields:
    name: Required. Full resource name of the reference, in the following
      format:
      `//{target_service}/{target_resource}/references/{reference_id}`. For
      example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}/references/{xyz}`.
  """

  name = _messages.StringField(1)


class GkeCluster(_messages.Message):
  r"""GkeCluster contains information specific to GKE clusters.

  Fields:
    clusterMissing: Output only. If cluster_missing is set then it denotes
      that the GKE cluster no longer exists in the GKE Control Plane.
    resourceLink: Immutable. Self-link of the GCP resource for the GKE
      cluster. For example: //container.googleapis.com/projects/my-
      project/locations/us-west1-a/clusters/my-cluster Zonal clusters are also
      supported.
  """

  clusterMissing = _messages.BooleanField(1)
  resourceLink = _messages.StringField(2)


class GkehubProjectsLocationsGetRequest(_messages.Message):
  r"""A GkehubProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsListRequest(_messages.Message):
  r"""A GkehubProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    includeUnrevealedLocations: If true, the returned list will include
      locations which are not yet revealed.
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  includeUnrevealedLocations = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  pageSize = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(6)


class GkehubProjectsLocationsMembershipsCreateRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsCreateRequest object.

  Fields:
    membership: A Membership resource to be passed as the request body.
    membershipId: Required. Client chosen ID for the membership.
      `membership_id` must be a valid RFC 1123 compliant DNS label: 1. At most
      63 characters in length 2. It must consist of lower case alphanumeric
      characters or `-` 3. It must start and end with an alphanumeric
      character Which can be expressed as the regex:
      `[a-z0-9]([-a-z0-9]*[a-z0-9])?`, with a maximum length of 63 characters.
    parent: Required. The parent (project and location) where the Memberships
      will be created. Specified in the format `projects/*/locations/*`.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  membership = _messages.MessageField('Membership', 1)
  membershipId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class GkehubProjectsLocationsMembershipsDeleteRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsDeleteRequest object.

  Fields:
    force: Optional. If set to true, any subresource from this Membership will
      also be deleted. Otherwise, the request will only work if the Membership
      has no subresource.
    name: Required. The Membership resource name in the format
      `projects/*/locations/*/memberships/*`.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class GkehubProjectsLocationsMembershipsGenerateConnectManifestRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsGenerateConnectManifestRequest
  object.

  Fields:
    connectAgent_name: Do not set.
    connectAgent_namespace: Optional. Namespace for GKE Connect agent
      resources. Defaults to `gke-connect`. The Connect Agent is authorized
      automatically when run in the default namespace. Otherwise, explicit
      authorization must be granted with an additional IAM binding.
    connectAgent_proxy: Optional. URI of a proxy if connectivity from the
      agent to gkeconnect.googleapis.com requires the use of a proxy. Format
      must be in the form `http(s)://{proxy_address}`, depending on the
      HTTP/HTTPS protocol supported by the proxy. This will direct the connect
      agent's outbound traffic through a HTTP(S) proxy.
    imagePullSecretContent: Optional. The image pull secret content for the
      registry, if not public.
    isUpgrade: Optional. If true, generate the resources for upgrade only.
      Some resources generated only for installation (e.g. secrets) will be
      excluded.
    name: Required. The Membership resource name the Agent will associate
      with, in the format `projects/*/locations/*/memberships/*`.
    registry: Optional. The registry to fetch the connect agent image from.
      Defaults to gcr.io/gkeconnect.
    version: Optional. The Connect agent version to use. Defaults to the most
      current version.
  """

  connectAgent_name = _messages.StringField(1)
  connectAgent_namespace = _messages.StringField(2)
  connectAgent_proxy = _messages.BytesField(3)
  imagePullSecretContent = _messages.BytesField(4)
  isUpgrade = _messages.BooleanField(5)
  name = _messages.StringField(6, required=True)
  registry = _messages.StringField(7)
  version = _messages.StringField(8)


class GkehubProjectsLocationsMembershipsGenerateExclusivityManifestRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsGenerateExclusivityManifestRequest
  object.

  Fields:
    crManifest: Optional. The YAML manifest of the membership CR retrieved by
      `kubectl get memberships membership`. Leave empty if the resource does
      not exist.
    crdManifest: Optional. The YAML manifest of the membership CRD retrieved
      by `kubectl get customresourcedefinitions membership`. Leave empty if
      the resource does not exist.
    name: Required. The Membership resource name in the format
      `projects/*/locations/*/memberships/*`.
  """

  crManifest = _messages.StringField(1)
  crdManifest = _messages.StringField(2)
  name = _messages.StringField(3, required=True)


class GkehubProjectsLocationsMembershipsGetIamPolicyRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class GkehubProjectsLocationsMembershipsGetRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsGetRequest object.

  Fields:
    name: Required. The Membership resource name in the format
      `projects/*/locations/*/memberships/*`.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsMembershipsListRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsListRequest object.

  Fields:
    filter: Optional. Lists Memberships that match the filter expression,
      following the syntax outlined in https://google.aip.dev/160. Examples: -
      Name is `bar` in project `foo-proj` and location `global`: name =
      "projects/foo-proj/locations/global/membership/bar" - Memberships that
      have a label called `foo`: labels.foo:* - Memberships that have a label
      called `foo` whose value is `bar`: labels.foo = bar - Memberships in the
      CREATING state: state = CREATING
    orderBy: Optional. One or more fields to compare and use to sort the
      output. See https://google.aip.dev/132#ordering.
    pageSize: Optional. When requesting a 'page' of resources, `page_size`
      specifies number of resources to return. If unspecified or set to 0, all
      resources will be returned.
    pageToken: Optional. Token returned by previous call to `ListMemberships`
      which specifies the position in the list from where to continue listing
      the resources.
    parent: Required. The parent (project and location) where the Memberships
      will be listed. Specified in the format `projects/*/locations/*`.
      `projects/*/locations/-` list memberships in all the regions.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class GkehubProjectsLocationsMembershipsPatchRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsPatchRequest object.

  Fields:
    membership: A Membership resource to be passed as the request body.
    name: Required. The membership resource name in the format:
      `projects/[project_id]/locations/global/memberships/[membership_id]`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Mask of fields to update. At least one field path
      must be specified in this mask.
  """

  membership = _messages.MessageField('Membership', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class GkehubProjectsLocationsMembershipsSetIamPolicyRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class GkehubProjectsLocationsMembershipsTestIamPermissionsRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class GkehubProjectsLocationsMembershipsValidateExclusivityRequest(_messages.Message):
  r"""A GkehubProjectsLocationsMembershipsValidateExclusivityRequest object.

  Fields:
    crManifest: Optional. The YAML of the membership CR in the cluster. Empty
      if the membership CR does not exist.
    intendedMembership: Required. The intended membership name under the
      `parent`. This method only does validation in anticipation of a
      CreateMembership call with the same name.
    parent: Required. The parent (project and location) where the Memberships
      will be created. Specified in the format `projects/*/locations/*`.
  """

  crManifest = _messages.StringField(1)
  intendedMembership = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class GkehubProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A GkehubProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class GkehubProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A GkehubProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A GkehubProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class GkehubProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A GkehubProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class GoogleRpcStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class KubernetesMetadata(_messages.Message):
  r"""KubernetesMetadata provides informational metadata for Memberships
  representing Kubernetes clusters.

  Fields:
    kubernetesApiServerVersion: Output only. Kubernetes API server version
      string as reported by '/version'.
    memoryMb: Output only. The total memory capacity as reported by the sum of
      all Kubernetes nodes resources, defined in MB.
    nodeCount: Output only. Node count as reported by Kubernetes nodes
      resources.
    nodeProviderId: Output only. Node providerID as reported by the first node
      in the list of nodes on the Kubernetes endpoint. On Kubernetes platforms
      that support zero-node clusters (like GKE-on-GCP), the node_count will
      be zero and the node_provider_id will be empty.
    updateTime: Output only. The time at which these details were last
      updated. This update_time is different from the Membership-level
      update_time since EndpointDetails are updated internally for API
      consumers.
    vcpuCount: Output only. vCPU count as reported by Kubernetes nodes
      resources.
  """

  kubernetesApiServerVersion = _messages.StringField(1)
  memoryMb = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  nodeCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  nodeProviderId = _messages.StringField(4)
  updateTime = _messages.StringField(5)
  vcpuCount = _messages.IntegerField(6, variant=_messages.Variant.INT32)


class KubernetesResource(_messages.Message):
  r"""KubernetesResource contains the YAML manifests and configuration for
  Membership Kubernetes resources in the cluster. After CreateMembership or
  UpdateMembership, these resources should be re-applied in the cluster.

  Fields:
    connectResources: Output only. The Kubernetes resources for installing the
      GKE Connect agent This field is only populated in the Membership
      returned from a successful long-running operation from CreateMembership
      or UpdateMembership. It is not populated during normal GetMembership or
      ListMemberships requests. To get the resource manifest after the initial
      registration, the caller should make a UpdateMembership call with an
      empty field mask.
    membershipCrManifest: Input only. The YAML representation of the
      Membership CR. This field is ignored for GKE clusters where Hub can read
      the CR directly. Callers should provide the CR that is currently present
      in the cluster during CreateMembership or UpdateMembership, or leave
      this field empty if none exists. The CR manifest is used to validate the
      cluster has not been registered with another Membership.
    membershipResources: Output only. Additional Kubernetes resources that
      need to be applied to the cluster after Membership creation, and after
      every update. This field is only populated in the Membership returned
      from a successful long-running operation from CreateMembership or
      UpdateMembership. It is not populated during normal GetMembership or
      ListMemberships requests. To get the resource manifest after the initial
      registration, the caller should make a UpdateMembership call with an
      empty field mask.
    resourceOptions: Optional. Options for Kubernetes resource generation.
  """

  connectResources = _messages.MessageField('ResourceManifest', 1, repeated=True)
  membershipCrManifest = _messages.StringField(2)
  membershipResources = _messages.MessageField('ResourceManifest', 3, repeated=True)
  resourceOptions = _messages.MessageField('ResourceOptions', 4)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListMembershipsResponse(_messages.Message):
  r"""Response message for the `GkeHubMembershipService.ListMemberships`
  method.

  Fields:
    nextPageToken: A token to request the next page of resources from the
      `ListMemberships` method. The value of an empty string means that there
      are no more resources to return.
    resources: The list of matching Memberships.
    unreachable: List of locations that could not be reached while fetching
      this list.
  """

  nextPageToken = _messages.StringField(1)
  resources = _messages.MessageField('Membership', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListReferencesRequest(_messages.Message):
  r"""The ListResourceMetadataRequest request.

  Fields:
    pageSize: The maximum number of items to return. If unspecified, server
      will pick an appropriate default. Server may return fewer items than
      requested. A caller should only rely on response's next_page_token to
      determine if there are more References left to be queried.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: Required. The parent resource name (target_resource of this
      reference). For example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3)


class ListReferencesResponse(_messages.Message):
  r"""The ListReferencesResponse response.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    references: The list of references.
  """

  nextPageToken = _messages.StringField(1)
  references = _messages.MessageField('Reference', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class LogConfig(_messages.Message):
  r"""Specifies what kind of log the caller must write

  Fields:
    cloudAudit: Cloud audit options.
    counter: Counter options.
    dataAccess: Data access options.
  """

  cloudAudit = _messages.MessageField('CloudAuditOptions', 1)
  counter = _messages.MessageField('CounterOptions', 2)
  dataAccess = _messages.MessageField('DataAccessOptions', 3)


class Membership(_messages.Message):
  r"""Membership contains information about a member cluster.

  Enums:
    ClusterTierValueValuesEnum: Output only. The tier of the cluster.
    InfrastructureTypeValueValuesEnum: Optional. The infrastructure type this
      Membership is running on.
    MembershipTypeValueValuesEnum: Output only. The type of the membership.

  Messages:
    LabelsValue: Optional. GCP labels for this membership. These labels are
      not leveraged by multi-cluster features, instead, we prefer cluster
      labels, which can be set on GKE cluster or other cluster types.
    PlatformLabelsValue: Output only. The labels of the cluster, coming from
      the platform api For example, a GKE cluster object labels are replicated
      here. This field is used by multi-cluster features as the source of
      labels and they ignore the membership labels (the `labels` field)

  Fields:
    authority: Optional. How to identify workloads from this Membership. See
      the documentation on Workload Identity for more details:
      https://cloud.google.com/kubernetes-engine/docs/how-to/workload-identity
    clusterTier: Output only. The tier of the cluster.
    createTime: Output only. When the Membership was created.
    deleteTime: Output only. When the Membership was deleted.
    description: Optional. Description of this membership, limited to 63
      characters. Must match the regex: `a-zA-Z0-9*`
    endpoint: Optional. Endpoint information to reach this member.
    externalId: Optional. An externally-generated and managed ID for this
      Membership. This ID may be modified after creation, but this is not
      recommended. For GKE clusters, external_id is managed by the Hub API and
      updates will be ignored. The ID must match the regex: `a-zA-Z0-9*` If
      this Membership represents a Kubernetes cluster, this value should be
      set to the UID of the `kube-system` namespace object.
    infrastructureType: Optional. The infrastructure type this Membership is
      running on.
    labels: Optional. GCP labels for this membership. These labels are not
      leveraged by multi-cluster features, instead, we prefer cluster labels,
      which can be set on GKE cluster or other cluster types.
    lastConnectionTime: Output only. For clusters using Connect, the timestamp
      of the most recent connection established with Google Cloud. This time
      is updated every several minutes, not continuously. For clusters that do
      not use GKE Connect, or that have never connected successfully, this
      field will be unset.
    membershipType: Output only. The type of the membership.
    monitoringConfig: Optional. The monitoring config information for this
      membership.
    name: Output only. The full, unique name of this Membership resource in
      the format `projects/*/locations/*/memberships/{membership_id}`, set
      during creation. `membership_id` must be a valid RFC 1123 compliant DNS
      label: 1. At most 63 characters in length 2. It must consist of lower
      case alphanumeric characters or `-` 3. It must start and end with an
      alphanumeric character Which can be expressed as the regex:
      `[a-z0-9]([-a-z0-9]*[a-z0-9])?`, with a maximum length of 63 characters.
    platformLabels: Output only. The labels of the cluster, coming from the
      platform api For example, a GKE cluster object labels are replicated
      here. This field is used by multi-cluster features as the source of
      labels and they ignore the membership labels (the `labels` field)
    state: Output only. State of the Membership resource.
    uniqueId: Output only. Google-generated UUID for this resource. This is
      unique across all Membership resources. If a Membership resource is
      deleted and another resource with the same name is created, it gets a
      different unique_id.
    updateTime: Output only. When the Membership was last updated.
  """

  class ClusterTierValueValuesEnum(_messages.Enum):
    r"""Output only. The tier of the cluster.

    Values:
      CLUSTER_TIER_UNSPECIFIED: The ClusterTier is not set.
      STANDARD: The ClusterTier is standard.
      ENTERPRISE: The ClusterTier is enterprise.
    """
    CLUSTER_TIER_UNSPECIFIED = 0
    STANDARD = 1
    ENTERPRISE = 2

  class InfrastructureTypeValueValuesEnum(_messages.Enum):
    r"""Optional. The infrastructure type this Membership is running on.

    Values:
      INFRASTRUCTURE_TYPE_UNSPECIFIED: No type was specified. Some Hub
        functionality may require a type be specified, and will not support
        Memberships with this value.
      ON_PREM: Private infrastructure that is owned or operated by customer.
        This includes GKE distributions such as GKE-OnPrem and GKE-
        OnBareMetal.
      MULTI_CLOUD: Public cloud infrastructure.
    """
    INFRASTRUCTURE_TYPE_UNSPECIFIED = 0
    ON_PREM = 1
    MULTI_CLOUD = 2

  class MembershipTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the membership.

    Values:
      MEMBERSHIP_TYPE_UNSPECIFIED: The MembershipType is not set.
      LIGHTWEIGHT: The membership supports only lightweight compatible
        features.
    """
    MEMBERSHIP_TYPE_UNSPECIFIED = 0
    LIGHTWEIGHT = 1

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. GCP labels for this membership. These labels are not
    leveraged by multi-cluster features, instead, we prefer cluster labels,
    which can be set on GKE cluster or other cluster types.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PlatformLabelsValue(_messages.Message):
    r"""Output only. The labels of the cluster, coming from the platform api
    For example, a GKE cluster object labels are replicated here. This field
    is used by multi-cluster features as the source of labels and they ignore
    the membership labels (the `labels` field)

    Messages:
      AdditionalProperty: An additional property for a PlatformLabelsValue
        object.

    Fields:
      additionalProperties: Additional properties of type PlatformLabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PlatformLabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  authority = _messages.MessageField('Authority', 1)
  clusterTier = _messages.EnumField('ClusterTierValueValuesEnum', 2)
  createTime = _messages.StringField(3)
  deleteTime = _messages.StringField(4)
  description = _messages.StringField(5)
  endpoint = _messages.MessageField('MembershipEndpoint', 6)
  externalId = _messages.StringField(7)
  infrastructureType = _messages.EnumField('InfrastructureTypeValueValuesEnum', 8)
  labels = _messages.MessageField('LabelsValue', 9)
  lastConnectionTime = _messages.StringField(10)
  membershipType = _messages.EnumField('MembershipTypeValueValuesEnum', 11)
  monitoringConfig = _messages.MessageField('MonitoringConfig', 12)
  name = _messages.StringField(13)
  platformLabels = _messages.MessageField('PlatformLabelsValue', 14)
  state = _messages.MessageField('MembershipState', 15)
  uniqueId = _messages.StringField(16)
  updateTime = _messages.StringField(17)


class MembershipEndpoint(_messages.Message):
  r"""MembershipEndpoint contains information needed to contact a Kubernetes
  API, endpoint and any additional Kubernetes metadata.

  Fields:
    applianceCluster: Optional. Specific information for a GDC Edge Appliance
      cluster.
    edgeCluster: Optional. Specific information for a Google Edge cluster.
    gkeCluster: Optional. Specific information for a GKE-on-GCP cluster.
    googleManaged: Output only. Whether the lifecycle of this membership is
      managed by a google cluster platform service.
    kubernetesMetadata: Output only. Useful Kubernetes-specific metadata.
    kubernetesResource: Optional. The in-cluster Kubernetes Resources that
      should be applied for a correctly registered cluster, in the steady
      state. These resources: * Ensure that the cluster is exclusively
      registered to one and only one Hub Membership. * Propagate Workload Pool
      Information available in the Membership Authority field. * Ensure proper
      initial configuration of default Hub Features.
    multiCloudCluster: Optional. Specific information for a GKE Multi-Cloud
      cluster.
    onPremCluster: Optional. Specific information for a GKE On-Prem cluster.
      An onprem user-cluster who has no resourceLink is not allowed to use
      this field, it should have a nil "type" instead.
  """

  applianceCluster = _messages.MessageField('ApplianceCluster', 1)
  edgeCluster = _messages.MessageField('EdgeCluster', 2)
  gkeCluster = _messages.MessageField('GkeCluster', 3)
  googleManaged = _messages.BooleanField(4)
  kubernetesMetadata = _messages.MessageField('KubernetesMetadata', 5)
  kubernetesResource = _messages.MessageField('KubernetesResource', 6)
  multiCloudCluster = _messages.MessageField('MultiCloudCluster', 7)
  onPremCluster = _messages.MessageField('OnPremCluster', 8)


class MembershipState(_messages.Message):
  r"""State of the Membership resource.

  Enums:
    CodeValueValuesEnum: Output only. The current state of the Membership
      resource.

  Fields:
    code: Output only. The current state of the Membership resource.
    description: This field is never set by the Hub Service.
    updateTime: This field is never set by the Hub Service.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the Membership resource.

    Values:
      CODE_UNSPECIFIED: The code is not set.
      CREATING: The cluster is being registered.
      READY: The cluster is registered.
      DELETING: The cluster is being unregistered.
      UPDATING: The Membership is being updated.
      SERVICE_UPDATING: The Membership is being updated by the Hub Service.
    """
    CODE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    DELETING = 3
    UPDATING = 4
    SERVICE_UPDATING = 5

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  description = _messages.StringField(2)
  updateTime = _messages.StringField(3)


class MonitoringConfig(_messages.Message):
  r"""MonitoringConfig informs Fleet-based applications/services/UIs how the
  metrics for the underlying cluster is reported to cloud monitoring services.
  It can be set from empty to non-empty, but can't be mutated directly to
  prevent accidentally breaking the constinousty of metrics.

  Fields:
    cluster: Optional. Cluster name used to report metrics. For Anthos on
      VMWare/Baremetal/MultiCloud clusters, it would be in format
      {cluster_type}/{cluster_name}, e.g., "awsClusters/cluster_1".
    clusterHash: Optional. For GKE and Multicloud clusters, this is the UUID
      of the cluster resource. For VMWare and Baremetal clusters, this is the
      kube-system UID.
    kubernetesMetricsPrefix: Optional. Kubernetes system metrics, if
      available, are written to this prefix. This defaults to kubernetes.io
      for GKE, and kubernetes.io/anthos for Anthos eventually. Noted: Anthos
      MultiCloud will have kubernetes.io prefix today but will migration to be
      under kubernetes.io/anthos.
    location: Optional. Location used to report Metrics
    projectId: Optional. Project used to report Metrics
  """

  cluster = _messages.StringField(1)
  clusterHash = _messages.StringField(2)
  kubernetesMetricsPrefix = _messages.StringField(3)
  location = _messages.StringField(4)
  projectId = _messages.StringField(5)


class MultiCloudCluster(_messages.Message):
  r"""MultiCloudCluster contains information specific to GKE Multi-Cloud
  clusters.

  Fields:
    clusterMissing: Output only. If cluster_missing is set then it denotes
      that API(gkemulticloud.googleapis.com) resource for this GKE Multi-Cloud
      cluster no longer exists.
    resourceLink: Immutable. Self-link of the GCP resource for the GKE Multi-
      Cloud cluster. For example: //gkemulticloud.googleapis.com/projects/my-
      project/locations/us-west1-a/awsClusters/my-cluster
      //gkemulticloud.googleapis.com/projects/my-project/locations/us-
      west1-a/azureClusters/my-cluster
      //gkemulticloud.googleapis.com/projects/my-project/locations/us-
      west1-a/attachedClusters/my-cluster
  """

  clusterMissing = _messages.BooleanField(1)
  resourceLink = _messages.StringField(2)


class OnPremCluster(_messages.Message):
  r"""OnPremCluster contains information specific to GKE On-Prem clusters.

  Enums:
    ClusterTypeValueValuesEnum: Immutable. The on prem cluster's type.

  Fields:
    adminCluster: Immutable. Whether the cluster is an admin cluster.
    clusterMissing: Output only. If cluster_missing is set then it denotes
      that API(gkeonprem.googleapis.com) resource for this GKE On-Prem cluster
      no longer exists.
    clusterType: Immutable. The on prem cluster's type.
    resourceLink: Immutable. Self-link of the GCP resource for the GKE On-Prem
      cluster. For example: //gkeonprem.googleapis.com/projects/my-
      project/locations/us-west1-a/vmwareClusters/my-cluster
      //gkeonprem.googleapis.com/projects/my-project/locations/us-
      west1-a/bareMetalClusters/my-cluster
  """

  class ClusterTypeValueValuesEnum(_messages.Enum):
    r"""Immutable. The on prem cluster's type.

    Values:
      CLUSTERTYPE_UNSPECIFIED: The ClusterType is not set.
      BOOTSTRAP: The ClusterType is bootstrap cluster.
      HYBRID: The ClusterType is baremetal hybrid cluster.
      STANDALONE: The ClusterType is baremetal standalone cluster.
      USER: The ClusterType is user cluster.
    """
    CLUSTERTYPE_UNSPECIFIED = 0
    BOOTSTRAP = 1
    HYBRID = 2
    STANDALONE = 3
    USER = 4

  adminCluster = _messages.BooleanField(1)
  clusterMissing = _messages.BooleanField(2)
  clusterType = _messages.EnumField('ClusterTypeValueValuesEnum', 3)
  resourceLink = _messages.StringField(4)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('GoogleRpcStatus', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    cancelRequested: Output only. Identifies whether the user has requested
      cancellation of the operation. Operations that have successfully been
      cancelled have Operation.error value with a google.rpc.Status.code of 1,
      corresponding to `Code.CANCELLED`.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    statusDetail: Output only. Human-readable status of the operation, if any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  cancelRequested = _messages.BooleanField(2)
  createTime = _messages.StringField(3)
  endTime = _messages.StringField(4)
  statusDetail = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    rules: If more than one rule is specified, the rules are applied in the
      following manner: - All matching LOG rules are always applied. - If any
      DENY/DENY_WITH_LOG rule matches, permission is denied. Logging will be
      applied if one or more matching rule requires logging. - Otherwise, if
      any ALLOW/ALLOW_WITH_LOG rule matches, permission is granted. Logging
      will be applied if one or more matching rule requires logging. -
      Otherwise, if no rule applies, permission is denied.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  rules = _messages.MessageField('Rule', 4, repeated=True)
  version = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class Reference(_messages.Message):
  r"""Represents a reference to a resource.

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    createTime: Output only. The creation time.
    details: Details of the reference type with no implied semantics.
      Cumulative size of the field must not be more than 1KiB.
    name: Output only. Relative resource name of the reference. Includes
      target resource as a parent and reference uid
      `{target_resource}/references/{reference_id}`. For example,
      `projects/{my-project}/locations/{location}/instances/{my-
      instance}/references/{xyz}`.
    sourceResource: Required. Full resource name of the resource which refers
      the target resource. For example:
      //tpu.googleapis.com/projects/myproject/nodes/mynode
    targetUniqueId: Output only. The unique_id of the target resource. Example
      1: (For arcus resource) A-1-0-2-387420123-13-913517247483640811
      unique_id format defined in go/m11n-unique-id-as-resource-id Example 2:
      (For CCFE resource) 123e4567-e89b-12d3-a456-************
    type: Required. Type of the reference. A service might impose limits on
      number of references of a specific type. Note: It's recommended to use
      CAPITALS_WITH_UNDERSCORES style for a type name.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  name = _messages.StringField(3)
  sourceResource = _messages.StringField(4)
  targetUniqueId = _messages.StringField(5)
  type = _messages.StringField(6)


class ResourceManifest(_messages.Message):
  r"""ResourceManifest represents a single Kubernetes resource to be applied
  to the cluster.

  Fields:
    clusterScoped: Whether the resource provided in the manifest is
      `cluster_scoped`. If unset, the manifest is assumed to be namespace
      scoped. This field is used for REST mapping when applying the resource
      in a cluster.
    manifest: YAML manifest of the resource.
  """

  clusterScoped = _messages.BooleanField(1)
  manifest = _messages.StringField(2)


class ResourceOptions(_messages.Message):
  r"""ResourceOptions represent options for Kubernetes resource generation.

  Fields:
    connectVersion: Optional. The Connect agent version to use for
      connect_resources. Defaults to the latest GKE Connect version. The
      version must be a currently supported version, obsolete versions will be
      rejected.
    k8sVersion: Optional. Major version of the Kubernetes cluster. This is
      only used to determine which version to use for the
      CustomResourceDefinition resources, `apiextensions/v1beta1`
      or`apiextensions/v1`.
    v1beta1Crd: Optional. Use `apiextensions/v1beta1` instead of
      `apiextensions/v1` for CustomResourceDefinition resources. This option
      should be set for clusters with Kubernetes apiserver versions <1.16.
  """

  connectVersion = _messages.StringField(1)
  k8sVersion = _messages.StringField(2)
  v1beta1Crd = _messages.BooleanField(3)


class Rule(_messages.Message):
  r"""A rule to be applied in a Policy.

  Enums:
    ActionValueValuesEnum: Required

  Fields:
    action: Required
    conditions: Additional restrictions that must be met. All conditions must
      pass for the rule to match.
    description: Human-readable description of the rule.
    in_: If one or more 'in' clauses are specified, the rule matches if the
      PRINCIPAL/AUTHORITY_SELECTOR is in at least one of these entries.
    logConfig: The config returned to callers of CheckPolicy for any entries
      that match the LOG action.
    notIn: If one or more 'not_in' clauses are specified, the rule matches if
      the PRINCIPAL/AUTHORITY_SELECTOR is in none of the entries. The format
      for in and not_in entries can be found at in the Local IAM documentation
      (see go/local-iam#features).
    permissions: A permission is a string of form `..` (e.g.,
      'storage.buckets.list'). A value of '*' matches all permissions, and a
      verb part of '*' (e.g., 'storage.buckets.*') matches all verbs.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required

    Values:
      NO_ACTION: Default no action.
      ALLOW: Matching 'Entries' grant access.
      ALLOW_WITH_LOG: Matching 'Entries' grant access and the caller promises
        to log the request per the returned log_configs.
      DENY: Matching 'Entries' deny access.
      DENY_WITH_LOG: Matching 'Entries' deny access and the caller promises to
        log the request per the returned log_configs.
      LOG: Matching 'Entries' tell IAM.Check callers to generate logs.
    """
    NO_ACTION = 0
    ALLOW = 1
    ALLOW_WITH_LOG = 2
    DENY = 3
    DENY_WITH_LOG = 4
    LOG = 5

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  conditions = _messages.MessageField('Condition', 2, repeated=True)
  description = _messages.StringField(3)
  in_ = _messages.StringField(4, repeated=True)
  logConfig = _messages.MessageField('LogConfig', 5, repeated=True)
  notIn = _messages.StringField(6, repeated=True)
  permissions = _messages.StringField(7, repeated=True)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TypeMeta(_messages.Message):
  r"""TypeMeta is the type information needed for content unmarshalling of
  Kubernetes resources in the manifest.

  Fields:
    apiVersion: APIVersion of the resource (e.g. v1).
    kind: Kind of the resource (e.g. Deployment).
  """

  apiVersion = _messages.StringField(1)
  kind = _messages.StringField(2)


class ValidateExclusivityResponse(_messages.Message):
  r"""The response of exclusivity artifacts validation result status.

  Fields:
    status: The validation result. * `OK` means that exclusivity is validated,
      assuming the manifest produced by GenerateExclusivityManifest is
      successfully applied. * `ALREADY_EXISTS` means that the Membership CRD
      is already owned by another Hub. See `status.message` for more
      information.
  """

  status = _messages.MessageField('GoogleRpcStatus', 1)


encoding.AddCustomJsonFieldMapping(
    Rule, 'in_', 'in')
encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    GkehubProjectsLocationsMembershipsGenerateConnectManifestRequest, 'connectAgent_name', 'connectAgent.name')
encoding.AddCustomJsonFieldMapping(
    GkehubProjectsLocationsMembershipsGenerateConnectManifestRequest, 'connectAgent_namespace', 'connectAgent.namespace')
encoding.AddCustomJsonFieldMapping(
    GkehubProjectsLocationsMembershipsGenerateConnectManifestRequest, 'connectAgent_proxy', 'connectAgent.proxy')
encoding.AddCustomJsonFieldMapping(
    GkehubProjectsLocationsMembershipsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
