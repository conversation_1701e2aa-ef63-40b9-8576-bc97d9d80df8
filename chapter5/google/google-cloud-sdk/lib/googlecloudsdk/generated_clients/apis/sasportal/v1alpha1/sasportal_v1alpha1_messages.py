"""Generated message classes for sasportal version v1alpha1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'sasportal'


class SasPortalAssignment(_messages.Message):
  r"""Associates `members` with a `role`.

  Fields:
    members: The identities the role is assigned to. It can have the following
      values: * `{user_email}`: An email address that represents a specific
      Google account. For example: `<EMAIL>`. * `{group_email}`: An
      email address that represents a Google group. For example,
      `<EMAIL>`.
    role: Required. Role that is assigned to `members`.
  """

  members = _messages.StringField(1, repeated=True)
  role = _messages.StringField(2)


class SasPortalChannelWithScore(_messages.Message):
  r"""The channel with score.

  Fields:
    frequencyRange: The frequency range of the channel.
    score: The channel score, normalized to be in the range [0,100].
  """

  frequencyRange = _messages.MessageField('SasPortalFrequencyRange', 1)
  score = _messages.FloatField(2)


class SasPortalCreateSignedDeviceRequest(_messages.Message):
  r"""Request for CreateSignedDevice.

  Fields:
    encodedDevice: Required. JSON Web Token signed using a CPI private key.
      Payload must be the JSON encoding of the device. The user_id field must
      be set.
    installerId: Required. Unique installer id (CPI ID) from the Certified
      Professional Installers database.
  """

  encodedDevice = _messages.BytesField(1)
  installerId = _messages.StringField(2)


class SasPortalCustomer(_messages.Message):
  r"""Entity representing a SAS customer.

  Fields:
    displayName: Required. Name of the organization that the customer entity
      represents.
    name: Output only. Resource name of the customer.
    sasUserIds: User IDs used by the devices belonging to this customer.
  """

  displayName = _messages.StringField(1)
  name = _messages.StringField(2)
  sasUserIds = _messages.StringField(3, repeated=True)


class SasPortalDeployment(_messages.Message):
  r"""The Deployment.

  Fields:
    displayName: The deployment's display name.
    frns: Output only. The FCC Registration Numbers (FRNs) copied from its
      direct parent.
    name: Output only. Resource name.
    sasUserIds: User ID used by the devices belonging to this deployment. Each
      deployment should be associated with one unique user ID.
  """

  displayName = _messages.StringField(1)
  frns = _messages.StringField(2, repeated=True)
  name = _messages.StringField(3)
  sasUserIds = _messages.StringField(4, repeated=True)


class SasPortalDeploymentAssociation(_messages.Message):
  r"""Association between a gcp project and a SAS user id.

  Fields:
    gcpProjectId: GCP project id of the associated project.
    userId: User id of the deployment.
  """

  gcpProjectId = _messages.StringField(1)
  userId = _messages.StringField(2)


class SasPortalDevice(_messages.Message):
  r"""A SasPortalDevice object.

  Enums:
    StateValueValuesEnum: Output only. Device state.

  Fields:
    activeConfig: Output only. Current configuration of the device as
      registered to the SAS.
    currentChannels: Output only. Current channels with scores.
    deviceMetadata: Device parameters that can be overridden by both SAS
      Portal and SAS registration requests.
    displayName: Device display name.
    fccId: The FCC identifier of the device. Refer to
      https://www.fcc.gov/oet/ea/fccid for FccID format. Accept underscores
      and periods because some test-SAS customers use them.
    grantRangeAllowlists: Only ranges that are within the allowlists are
      available for new grants.
    grants: Output only. Grants held by the device.
    name: Output only. The resource path name.
    preloadedConfig: Configuration of the device, as specified via SAS Portal
      API.
    serialNumber: A serial number assigned to the device by the device
      manufacturer.
    state: Output only. Device state.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Device state.

    Values:
      DEVICE_STATE_UNSPECIFIED: Unspecified state.
      RESERVED: Device created in the SAS Portal, however, not yet registered
        with SAS.
      REGISTERED: Device registered with SAS.
      DEREGISTERED: Device de-registered with SAS.
    """
    DEVICE_STATE_UNSPECIFIED = 0
    RESERVED = 1
    REGISTERED = 2
    DEREGISTERED = 3

  activeConfig = _messages.MessageField('SasPortalDeviceConfig', 1)
  currentChannels = _messages.MessageField('SasPortalChannelWithScore', 2, repeated=True)
  deviceMetadata = _messages.MessageField('SasPortalDeviceMetadata', 3)
  displayName = _messages.StringField(4)
  fccId = _messages.StringField(5)
  grantRangeAllowlists = _messages.MessageField('SasPortalFrequencyRange', 6, repeated=True)
  grants = _messages.MessageField('SasPortalDeviceGrant', 7, repeated=True)
  name = _messages.StringField(8)
  preloadedConfig = _messages.MessageField('SasPortalDeviceConfig', 9)
  serialNumber = _messages.StringField(10)
  state = _messages.EnumField('StateValueValuesEnum', 11)


class SasPortalDeviceAirInterface(_messages.Message):
  r"""Information about the device's air interface.

  Enums:
    RadioTechnologyValueValuesEnum: Conditional. This field specifies the
      radio access technology that is used for the CBSD.

  Fields:
    radioTechnology: Conditional. This field specifies the radio access
      technology that is used for the CBSD.
    supportedSpec: Optional. This field is related to the `radioTechnology`
      and provides the air interface specification that the CBSD is compliant
      with at the time of registration.
  """

  class RadioTechnologyValueValuesEnum(_messages.Enum):
    r"""Conditional. This field specifies the radio access technology that is
    used for the CBSD.

    Values:
      RADIO_TECHNOLOGY_UNSPECIFIED: <no description>
      E_UTRA: <no description>
      CAMBIUM_NETWORKS: <no description>
      FOUR_G_BBW_SAA_1: <no description>
      NR: <no description>
      DOODLE_CBRS: <no description>
      CW: <no description>
      REDLINE: <no description>
      TARANA_WIRELESS: <no description>
      FAROS: <no description>
    """
    RADIO_TECHNOLOGY_UNSPECIFIED = 0
    E_UTRA = 1
    CAMBIUM_NETWORKS = 2
    FOUR_G_BBW_SAA_1 = 3
    NR = 4
    DOODLE_CBRS = 5
    CW = 6
    REDLINE = 7
    TARANA_WIRELESS = 8
    FAROS = 9

  radioTechnology = _messages.EnumField('RadioTechnologyValueValuesEnum', 1)
  supportedSpec = _messages.StringField(2)


class SasPortalDeviceConfig(_messages.Message):
  r"""Information about the device configuration.

  Enums:
    CategoryValueValuesEnum: FCC category of the device.
    MeasurementCapabilitiesValueListEntryValuesEnum:
    StateValueValuesEnum: State of the configuration.

  Fields:
    airInterface: Information about this device's air interface.
    callSign: The call sign of the device operator.
    category: FCC category of the device.
    installationParams: Installation parameters for the device.
    isSigned: Output only. Whether the configuration has been signed by a CPI.
    measurementCapabilities: Measurement reporting capabilities of the device.
    model: Information about this device model.
    state: State of the configuration.
    updateTime: Output only. The last time the device configuration was
      edited.
    userId: The identifier of a device user.
  """

  class CategoryValueValuesEnum(_messages.Enum):
    r"""FCC category of the device.

    Values:
      DEVICE_CATEGORY_UNSPECIFIED: Unspecified device category.
      DEVICE_CATEGORY_A: Category A.
      DEVICE_CATEGORY_B: Category B.
    """
    DEVICE_CATEGORY_UNSPECIFIED = 0
    DEVICE_CATEGORY_A = 1
    DEVICE_CATEGORY_B = 2

  class MeasurementCapabilitiesValueListEntryValuesEnum(_messages.Enum):
    r"""MeasurementCapabilitiesValueListEntryValuesEnum enum type.

    Values:
      MEASUREMENT_CAPABILITY_UNSPECIFIED: <no description>
      MEASUREMENT_CAPABILITY_RECEIVED_POWER_WITH_GRANT: <no description>
      MEASUREMENT_CAPABILITY_RECEIVED_POWER_WITHOUT_GRANT: <no description>
    """
    MEASUREMENT_CAPABILITY_UNSPECIFIED = 0
    MEASUREMENT_CAPABILITY_RECEIVED_POWER_WITH_GRANT = 1
    MEASUREMENT_CAPABILITY_RECEIVED_POWER_WITHOUT_GRANT = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""State of the configuration.

    Values:
      DEVICE_CONFIG_STATE_UNSPECIFIED: <no description>
      DRAFT: <no description>
      FINAL: <no description>
    """
    DEVICE_CONFIG_STATE_UNSPECIFIED = 0
    DRAFT = 1
    FINAL = 2

  airInterface = _messages.MessageField('SasPortalDeviceAirInterface', 1)
  callSign = _messages.StringField(2)
  category = _messages.EnumField('CategoryValueValuesEnum', 3)
  installationParams = _messages.MessageField('SasPortalInstallationParams', 4)
  isSigned = _messages.BooleanField(5)
  measurementCapabilities = _messages.EnumField('MeasurementCapabilitiesValueListEntryValuesEnum', 6, repeated=True)
  model = _messages.MessageField('SasPortalDeviceModel', 7)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  updateTime = _messages.StringField(9)
  userId = _messages.StringField(10)


class SasPortalDeviceGrant(_messages.Message):
  r"""Device grant. It is an authorization provided by the Spectrum Access
  System to a device to transmit using specified operating parameters after a
  successful heartbeat by the device.

  Enums:
    ChannelTypeValueValuesEnum: Type of channel used.
    StateValueValuesEnum: State of the grant.

  Fields:
    channelType: Type of channel used.
    expireTime: The expiration time of the grant.
    frequencyRange: The transmission frequency range.
    grantId: Grant Id.
    lastHeartbeatTransmitExpireTime: The transmit expiration time of the last
      heartbeat.
    maxEirp: Maximum Equivalent Isotropically Radiated Power (EIRP) permitted
      by the grant. The maximum EIRP is in units of dBm/MHz. The value of
      `maxEirp` represents the average (RMS) EIRP that would be measured by
      the procedure defined in FCC part 96.41(e)(3).
    moveList: The DPA move lists on which this grant appears.
    state: State of the grant.
    suspensionReason: If the grant is suspended, the reason(s) for suspension.
  """

  class ChannelTypeValueValuesEnum(_messages.Enum):
    r"""Type of channel used.

    Values:
      CHANNEL_TYPE_UNSPECIFIED: <no description>
      CHANNEL_TYPE_GAA: <no description>
      CHANNEL_TYPE_PAL: <no description>
    """
    CHANNEL_TYPE_UNSPECIFIED = 0
    CHANNEL_TYPE_GAA = 1
    CHANNEL_TYPE_PAL = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""State of the grant.

    Values:
      GRANT_STATE_UNSPECIFIED: <no description>
      GRANT_STATE_GRANTED: The grant has been granted but the device is not
        heartbeating on it.
      GRANT_STATE_TERMINATED: The grant has been terminated by the SAS.
      GRANT_STATE_SUSPENDED: The grant has been suspended by the SAS.
      GRANT_STATE_AUTHORIZED: The device is currently transmitting.
      GRANT_STATE_EXPIRED: The grant has expired.
    """
    GRANT_STATE_UNSPECIFIED = 0
    GRANT_STATE_GRANTED = 1
    GRANT_STATE_TERMINATED = 2
    GRANT_STATE_SUSPENDED = 3
    GRANT_STATE_AUTHORIZED = 4
    GRANT_STATE_EXPIRED = 5

  channelType = _messages.EnumField('ChannelTypeValueValuesEnum', 1)
  expireTime = _messages.StringField(2)
  frequencyRange = _messages.MessageField('SasPortalFrequencyRange', 3)
  grantId = _messages.StringField(4)
  lastHeartbeatTransmitExpireTime = _messages.StringField(5)
  maxEirp = _messages.FloatField(6)
  moveList = _messages.MessageField('SasPortalDpaMoveList', 7, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  suspensionReason = _messages.StringField(9, repeated=True)


class SasPortalDeviceMetadata(_messages.Message):
  r"""Device data overridable by both SAS Portal and registration requests.

  Fields:
    antennaModel: If populated, the Antenna Model Pattern to use. Format is:
      `RecordCreatorId:PatternId`
    commonChannelGroup: Common Channel Group (CCG). A group of CBSDs in the
      same ICG requesting a common primary channel assignment. For more
      details, see [CBRSA-TS-2001 V3.0.0](https://ongoalliance.org/wp-
      content/uploads/2020/02/CBRSA-TS-2001-V3.0.0_Approved-for-
      publication.pdf).
    interferenceCoordinationGroup: Interference Coordination Group (ICG). A
      group of CBSDs that manage their own interference with the group. For
      more details, see [CBRSA-TS-2001 V3.0.0](https://ongoalliance.org/wp-
      content/uploads/2020/02/CBRSA-TS-2001-V3.0.0_Approved-for-
      publication.pdf).
    nrqzValidated: Output only. Set to `true` if a CPI has validated that they
      have coordinated with the National Quiet Zone office.
    nrqzValidation: Output only. National Radio Quiet Zone validation info.
  """

  antennaModel = _messages.StringField(1)
  commonChannelGroup = _messages.StringField(2)
  interferenceCoordinationGroup = _messages.StringField(3)
  nrqzValidated = _messages.BooleanField(4)
  nrqzValidation = _messages.MessageField('SasPortalNrqzValidation', 5)


class SasPortalDeviceModel(_messages.Message):
  r"""Information about the model of the device.

  Fields:
    firmwareVersion: The firmware version of the device.
    hardwareVersion: The hardware version of the device.
    name: The name of the device model.
    softwareVersion: The software version of the device.
    vendor: The name of the device vendor.
  """

  firmwareVersion = _messages.StringField(1)
  hardwareVersion = _messages.StringField(2)
  name = _messages.StringField(3)
  softwareVersion = _messages.StringField(4)
  vendor = _messages.StringField(5)


class SasPortalDpaMoveList(_messages.Message):
  r"""An entry in a DPA's move list.

  Fields:
    dpaId: The ID of the DPA.
    frequencyRange: The frequency range that the move list affects.
  """

  dpaId = _messages.StringField(1)
  frequencyRange = _messages.MessageField('SasPortalFrequencyRange', 2)


class SasPortalEmpty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class SasPortalFrequencyRange(_messages.Message):
  r"""Frequency range from `low_frequency` to `high_frequency`.

  Fields:
    highFrequencyMhz: The highest frequency of the frequency range in MHz.
    lowFrequencyMhz: The lowest frequency of the frequency range in MHz.
  """

  highFrequencyMhz = _messages.FloatField(1)
  lowFrequencyMhz = _messages.FloatField(2)


class SasPortalGcpProjectDeployment(_messages.Message):
  r"""Deployment associated with the GCP project. Includes whether SAS
  analytics has been enabled or not.

  Fields:
    deployment: Deployment associated with the GCP project.
    hasEnabledAnalytics: Whether SAS analytics has been enabled.
  """

  deployment = _messages.MessageField('SasPortalDeployment', 1)
  hasEnabledAnalytics = _messages.BooleanField(2)


class SasPortalGenerateSecretRequest(_messages.Message):
  r"""Request for GenerateSecret."""


class SasPortalGenerateSecretResponse(_messages.Message):
  r"""Response for GenerateSecret.

  Fields:
    secret: The secret generated by the string and used by ValidateInstaller.
  """

  secret = _messages.StringField(1)


class SasPortalGetPolicyRequest(_messages.Message):
  r"""Request message for `GetPolicy` method.

  Fields:
    resource: Required. The resource for which the policy is being requested.
  """

  resource = _messages.StringField(1)


class SasPortalInstallationParams(_messages.Message):
  r"""Information about the device installation parameters.

  Enums:
    HeightTypeValueValuesEnum: Specifies how the height is measured.

  Fields:
    antennaAzimuth: Boresight direction of the horizontal plane of the antenna
      in degrees with respect to true north. The value of this parameter is an
      integer with a value between 0 and 359 inclusive. A value of 0 degrees
      means true north; a value of 90 degrees means east. This parameter is
      optional for Category A devices and conditional for Category B devices.
    antennaBeamwidth: 3-dB antenna beamwidth of the antenna in the horizontal-
      plane in degrees. This parameter is an unsigned integer having a value
      between 0 and 360 (degrees) inclusive; it is optional for Category A
      devices and conditional for Category B devices.
    antennaDowntilt: Antenna downtilt in degrees and is an integer with a
      value between -90 and +90 inclusive; a negative value means the antenna
      is tilted up (above horizontal). This parameter is optional for Category
      A devices and conditional for Category B devices.
    antennaGain: Peak antenna gain in dBi. This parameter is a double with a
      value between -127 and +128 (dBi) inclusive. Part of Release 2 to
      support floating-point value
    antennaModel: If an external antenna is used, the antenna model is
      optionally provided in this field. The string has a maximum length of
      128 octets.
    cpeCbsdIndication: If present, this parameter specifies whether the CBSD
      is a CPE-CBSD or not.
    eirpCapability: This parameter is the maximum device EIRP in units of
      dBm/10MHz and is an integer with a value between -127 and +47 (dBm/10
      MHz) inclusive. If not included, SAS interprets it as maximum allowable
      EIRP in units of dBm/10MHz for device category.
    height: Device antenna height in meters. When the `heightType` parameter
      value is "AGL", the antenna height should be given relative to ground
      level. When the `heightType` parameter value is "AMSL", it is given with
      respect to WGS84 datum.
    heightType: Specifies how the height is measured.
    horizontalAccuracy: A positive number in meters to indicate accuracy of
      the device antenna horizontal location. This optional parameter should
      only be present if its value is less than the FCC requirement of 50
      meters.
    indoorDeployment: Whether the device antenna is indoor or not. `true`:
      indoor. `false`: outdoor.
    latitude: Latitude of the device antenna location in degrees relative to
      the WGS 84 datum. The allowed range is from -90.000000 to +90.000000.
      Positive values represent latitudes north of the equator; negative
      values south of the equator.
    longitude: Longitude of the device antenna location in degrees relative to
      the WGS 84 datum. The allowed range is from -180.000000 to +180.000000.
      Positive values represent longitudes east of the prime meridian;
      negative values west of the prime meridian.
    verticalAccuracy: A positive number in meters to indicate accuracy of the
      device antenna vertical location. This optional parameter should only be
      present if its value is less than the FCC requirement of 3 meters.
  """

  class HeightTypeValueValuesEnum(_messages.Enum):
    r"""Specifies how the height is measured.

    Values:
      HEIGHT_TYPE_UNSPECIFIED: Unspecified height type.
      HEIGHT_TYPE_AGL: AGL height is measured relative to the ground level.
      HEIGHT_TYPE_AMSL: AMSL height is measured relative to the mean sea
        level.
    """
    HEIGHT_TYPE_UNSPECIFIED = 0
    HEIGHT_TYPE_AGL = 1
    HEIGHT_TYPE_AMSL = 2

  antennaAzimuth = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  antennaBeamwidth = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  antennaDowntilt = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  antennaGain = _messages.FloatField(4)
  antennaModel = _messages.StringField(5)
  cpeCbsdIndication = _messages.BooleanField(6)
  eirpCapability = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  height = _messages.FloatField(8)
  heightType = _messages.EnumField('HeightTypeValueValuesEnum', 9)
  horizontalAccuracy = _messages.FloatField(10)
  indoorDeployment = _messages.BooleanField(11)
  latitude = _messages.FloatField(12)
  longitude = _messages.FloatField(13)
  verticalAccuracy = _messages.FloatField(14)


class SasPortalListCustomersResponse(_messages.Message):
  r"""Response for `ListCustomers`.

  Fields:
    customers: The list of customers that match the request.
    nextPageToken: A pagination token returned from a previous call to
      ListCustomers that indicates from where listing should continue. If the
      field is missing or empty, it means there are no more customers.
  """

  customers = _messages.MessageField('SasPortalCustomer', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class SasPortalListDeploymentsResponse(_messages.Message):
  r"""Response for ListDeployments.

  Fields:
    deployments: The deployments that match the request.
    nextPageToken: A pagination token returned from a previous call to
      ListDeployments that indicates from where listing should continue. If
      the field is missing or empty, it means there are no more deployments.
  """

  deployments = _messages.MessageField('SasPortalDeployment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class SasPortalListDevicesResponse(_messages.Message):
  r"""Response for ListDevices.

  Fields:
    devices: The devices that match the request.
    nextPageToken: A pagination token returned from a previous call to
      ListDevices that indicates from where listing should continue. If the
      field is missing or empty, it means there is no more devices.
  """

  devices = _messages.MessageField('SasPortalDevice', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class SasPortalListGcpProjectDeploymentsResponse(_messages.Message):
  r"""Response for [ListGcpProjectDeployments].

  Fields:
    deployments: Optional. Deployments associated with the GCP project
  """

  deployments = _messages.MessageField('SasPortalGcpProjectDeployment', 1, repeated=True)


class SasPortalListLegacyOrganizationsResponse(_messages.Message):
  r"""Response for [ListLegacyOrganizations].
  [spectrum.sas.portal.v1alpha1.Provisioning.ListLegacyOrganizations].

  Fields:
    organizations: Optional. Legacy SAS organizations.
  """

  organizations = _messages.MessageField('SasPortalOrganization', 1, repeated=True)


class SasPortalListNodesResponse(_messages.Message):
  r"""Response for ListNodes.

  Fields:
    nextPageToken: A pagination token returned from a previous call to
      ListNodes that indicates from where listing should continue. If the
      field is missing or empty, it means there is no more nodes.
    nodes: The nodes that match the request.
  """

  nextPageToken = _messages.StringField(1)
  nodes = _messages.MessageField('SasPortalNode', 2, repeated=True)


class SasPortalMigrateOrganizationMetadata(_messages.Message):
  r"""Long-running operation metadata message returned by the
  MigrateOrganization.

  Enums:
    OperationStateValueValuesEnum: Output only. Current operation state

  Fields:
    operationState: Output only. Current operation state
  """

  class OperationStateValueValuesEnum(_messages.Enum):
    r"""Output only. Current operation state

    Values:
      OPERATION_STATE_UNSPECIFIED: Unspecified.
      OPERATION_STATE_PENDING: Pending (Not started).
      OPERATION_STATE_RUNNING: In-progress.
      OPERATION_STATE_SUCCEEDED: Done successfully.
      OPERATION_STATE_FAILED: Done with errors.
    """
    OPERATION_STATE_UNSPECIFIED = 0
    OPERATION_STATE_PENDING = 1
    OPERATION_STATE_RUNNING = 2
    OPERATION_STATE_SUCCEEDED = 3
    OPERATION_STATE_FAILED = 4

  operationState = _messages.EnumField('OperationStateValueValuesEnum', 1)


class SasPortalMigrateOrganizationRequest(_messages.Message):
  r"""Request for [MigrateOrganization].
  [spectrum.sas.portal.v1alpha1.Provisioning.MigrateOrganization]. GCP
  Project, Organization Info, and caller's GAIA ID should be retrieved from
  the RPC handler, and used to check authorization on SAS Portal organization
  and to create GCP Projects.

  Fields:
    organizationId: Required. Id of the SAS organization to be migrated.
  """

  organizationId = _messages.IntegerField(1)


class SasPortalMigrateOrganizationResponse(_messages.Message):
  r"""Response for [MigrateOrganization].
  [spectrum.sas.portal.v1alpha1.Provisioning.MigrateOrganization].

  Fields:
    deploymentAssociation: Optional. A list of deployment association that
      were created for the migration, or current associations if they already
      exist.
  """

  deploymentAssociation = _messages.MessageField('SasPortalDeploymentAssociation', 1, repeated=True)


class SasPortalMoveDeploymentRequest(_messages.Message):
  r"""Request for MoveDeployment.

  Fields:
    destination: Required. The name of the new parent resource node or
      customer to reparent the deployment under.
  """

  destination = _messages.StringField(1)


class SasPortalMoveDeviceRequest(_messages.Message):
  r"""Request for MoveDevice.

  Fields:
    destination: Required. The name of the new parent resource node or
      customer to reparent the device under.
  """

  destination = _messages.StringField(1)


class SasPortalMoveNodeRequest(_messages.Message):
  r"""Request for MoveNode.

  Fields:
    destination: Required. The name of the new parent resource node or
      customer to reparent the node under.
  """

  destination = _messages.StringField(1)


class SasPortalNode(_messages.Message):
  r"""The Node.

  Fields:
    displayName: The node's display name.
    name: Output only. Resource name.
    sasUserIds: User ids used by the devices belonging to this node.
  """

  displayName = _messages.StringField(1)
  name = _messages.StringField(2)
  sasUserIds = _messages.StringField(3, repeated=True)


class SasPortalNrqzValidation(_messages.Message):
  r"""Information about National Radio Quiet Zone validation.

  Enums:
    StateValueValuesEnum: State of the NRQZ validation info.

  Fields:
    caseId: Validation case ID.
    cpiId: CPI who signed the validation.
    latitude: Device latitude that's associated with the validation.
    longitude: Device longitude that's associated with the validation.
    state: State of the NRQZ validation info.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""State of the NRQZ validation info.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      DRAFT: Draft state.
      FINAL: Final state.
    """
    STATE_UNSPECIFIED = 0
    DRAFT = 1
    FINAL = 2

  caseId = _messages.StringField(1)
  cpiId = _messages.StringField(2)
  latitude = _messages.FloatField(3)
  longitude = _messages.FloatField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)


class SasPortalOperation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('SasPortalStatus', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class SasPortalOrganization(_messages.Message):
  r"""Organization details.

  Fields:
    displayName: Name of organization
    id: Id of organization
  """

  displayName = _messages.StringField(1)
  id = _messages.IntegerField(2)


class SasPortalPolicy(_messages.Message):
  r"""Defines an access control policy to the resources.

  Fields:
    assignments: List of assignments
    etag: The etag is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the etag in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An etag is returned in the response to GetPolicy, and
      systems are expected to put that etag in the request to SetPolicy to
      ensure that their change will be applied to the same version of the
      policy. If no etag is provided in the call to GetPolicy, then the
      existing policy is overwritten blindly.
  """

  assignments = _messages.MessageField('SasPortalAssignment', 1, repeated=True)
  etag = _messages.BytesField(2)


class SasPortalProvisionDeploymentRequest(_messages.Message):
  r"""Request for [ProvisionDeployment].
  [spectrum.sas.portal.v1alpha1.Provisioning.ProvisionDeployment]. GCP
  Project, Organization Info, and caller's GAIA ID should be retrieved from
  the RPC handler, and used as inputs to create a new SAS organization (if not
  exists) and a new SAS deployment.

  Fields:
    newDeploymentDisplayName: Optional. If this field is set, and a new SAS
      Portal Deployment needs to be created, its display name will be set to
      the value of this field.
    newOrganizationDisplayName: Optional. If this field is set, and a new SAS
      Portal Organization needs to be created, its display name will be set to
      the value of this field.
    organizationId: Optional. If this field is set then a new deployment will
      be created under the organization specified by this id.
  """

  newDeploymentDisplayName = _messages.StringField(1)
  newOrganizationDisplayName = _messages.StringField(2)
  organizationId = _messages.IntegerField(3)


class SasPortalProvisionDeploymentResponse(_messages.Message):
  r"""Response for [ProvisionDeployment].
  [spectrum.sas.portal.v1alpha1.Provisioning.ProvisionDeployment].

  Fields:
    errorMessage: Optional. Optional error message if the provisioning request
      is not successful.
  """

  errorMessage = _messages.StringField(1)


class SasPortalSetPolicyRequest(_messages.Message):
  r"""Request message for `SetPolicy` method.

  Fields:
    disableNotification: Optional. Set the field as `true` to disable the
      onboarding notification.
    policy: Required. The policy to be applied to the `resource`.
    resource: Required. The resource for which the policy is being specified.
      This policy replaces any existing policy.
  """

  disableNotification = _messages.BooleanField(1)
  policy = _messages.MessageField('SasPortalPolicy', 2)
  resource = _messages.StringField(3)


class SasPortalSetupSasAnalyticsMetadata(_messages.Message):
  r"""Metadata returned by the long running operation for the
  SetupSasAnalytics rpc.
  """



class SasPortalSetupSasAnalyticsRequest(_messages.Message):
  r"""Request for the SetupSasAnalytics rpc.

  Fields:
    userId: Optional. User id to setup analytics for, if not provided the user
      id associated with the project is used. optional
  """

  userId = _messages.StringField(1)


class SasPortalSetupSasAnalyticsResponse(_messages.Message):
  r"""Response returned by the long running operation for the
  SetupSasAnalytics rpc.
  """



class SasPortalSignDeviceRequest(_messages.Message):
  r"""Request for SignDevice.

  Fields:
    device: Required. The device to sign. The device fields name, fcc_id and
      serial_number must be set. The user_id field must be set.
  """

  device = _messages.MessageField('SasPortalDevice', 1)


class SasPortalStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class SasPortalTestPermissionsRequest(_messages.Message):
  r"""Request message for `TestPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
    resource: Required. The resource for which the permissions are being
      requested.
  """

  permissions = _messages.StringField(1, repeated=True)
  resource = _messages.StringField(2)


class SasPortalTestPermissionsResponse(_messages.Message):
  r"""Response message for `TestPermissions` method.

  Fields:
    permissions: A set of permissions that the caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class SasPortalUpdateSignedDeviceRequest(_messages.Message):
  r"""Request for UpdateSignedDevice.

  Fields:
    encodedDevice: Required. The JSON Web Token signed using a CPI private
      key. Payload must be the JSON encoding of the device. The user_id field
      must be set.
    installerId: Required. Unique installer ID (CPI ID) from the Certified
      Professional Installers database.
  """

  encodedDevice = _messages.BytesField(1)
  installerId = _messages.StringField(2)


class SasPortalValidateInstallerRequest(_messages.Message):
  r"""Request for ValidateInstaller.

  Fields:
    encodedSecret: Required. JSON Web Token signed using a CPI private key.
      Payload must include a "secret" claim whose value is the secret.
    installerId: Required. Unique installer id (CPI ID) from the Certified
      Professional Installers database.
    secret: Required. Secret returned by the GenerateSecret.
  """

  encodedSecret = _messages.StringField(1)
  installerId = _messages.StringField(2)
  secret = _messages.StringField(3)


class SasPortalValidateInstallerResponse(_messages.Message):
  r"""Response for ValidateInstaller."""


class SasportalCustomersDeploymentsCreateRequest(_messages.Message):
  r"""A SasportalCustomersDeploymentsCreateRequest object.

  Fields:
    parent: Required. The parent resource name where the deployment is to be
      created.
    sasPortalDeployment: A SasPortalDeployment resource to be passed as the
      request body.
  """

  parent = _messages.StringField(1, required=True)
  sasPortalDeployment = _messages.MessageField('SasPortalDeployment', 2)


class SasportalCustomersDeploymentsDeleteRequest(_messages.Message):
  r"""A SasportalCustomersDeploymentsDeleteRequest object.

  Fields:
    name: Required. The name of the deployment.
  """

  name = _messages.StringField(1, required=True)


class SasportalCustomersDeploymentsDevicesCreateRequest(_messages.Message):
  r"""A SasportalCustomersDeploymentsDevicesCreateRequest object.

  Fields:
    parent: Required. The name of the parent resource.
    sasPortalDevice: A SasPortalDevice resource to be passed as the request
      body.
  """

  parent = _messages.StringField(1, required=True)
  sasPortalDevice = _messages.MessageField('SasPortalDevice', 2)


class SasportalCustomersDeploymentsDevicesCreateSignedRequest(_messages.Message):
  r"""A SasportalCustomersDeploymentsDevicesCreateSignedRequest object.

  Fields:
    parent: Required. The name of the parent resource.
    sasPortalCreateSignedDeviceRequest: A SasPortalCreateSignedDeviceRequest
      resource to be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  sasPortalCreateSignedDeviceRequest = _messages.MessageField('SasPortalCreateSignedDeviceRequest', 2)


class SasportalCustomersDeploymentsDevicesListRequest(_messages.Message):
  r"""A SasportalCustomersDeploymentsDevicesListRequest object.

  Fields:
    filter: The filter expression. The filter should have one of the following
      formats: "sn=123454" or "display_name=MyDevice". sn corresponds to
      serial number of the device. The filter is case insensitive.
    pageSize: The maximum number of devices to return in the response. If
      empty or zero, all devices will be listed. Must be in the range [0,
      1000].
    pageToken: A pagination token returned from a previous call to ListDevices
      that indicates where this listing should continue from.
    parent: Required. The name of the parent resource.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SasportalCustomersDeploymentsGetRequest(_messages.Message):
  r"""A SasportalCustomersDeploymentsGetRequest object.

  Fields:
    name: Required. The name of the deployment.
  """

  name = _messages.StringField(1, required=True)


class SasportalCustomersDeploymentsListRequest(_messages.Message):
  r"""A SasportalCustomersDeploymentsListRequest object.

  Fields:
    filter: The filter expression. The filter should have the following
      format: "DIRECT_CHILDREN" or format: "direct_children". The filter is
      case insensitive. If empty, then no deployments are filtered.
    pageSize: The maximum number of deployments to return in the response.
    pageToken: A pagination token returned from a previous call to
      ListDeployments that indicates where this listing should continue from.
    parent: Required. The parent resource name, for example, "nodes/1",
      customer/1/nodes/2.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SasportalCustomersDeploymentsMoveRequest(_messages.Message):
  r"""A SasportalCustomersDeploymentsMoveRequest object.

  Fields:
    name: Required. The name of the deployment to move.
    sasPortalMoveDeploymentRequest: A SasPortalMoveDeploymentRequest resource
      to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  sasPortalMoveDeploymentRequest = _messages.MessageField('SasPortalMoveDeploymentRequest', 2)


class SasportalCustomersDeploymentsPatchRequest(_messages.Message):
  r"""A SasportalCustomersDeploymentsPatchRequest object.

  Fields:
    name: Output only. Resource name.
    sasPortalDeployment: A SasPortalDeployment resource to be passed as the
      request body.
    updateMask: Fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  sasPortalDeployment = _messages.MessageField('SasPortalDeployment', 2)
  updateMask = _messages.StringField(3)


class SasportalCustomersDevicesCreateRequest(_messages.Message):
  r"""A SasportalCustomersDevicesCreateRequest object.

  Fields:
    parent: Required. The name of the parent resource.
    sasPortalDevice: A SasPortalDevice resource to be passed as the request
      body.
  """

  parent = _messages.StringField(1, required=True)
  sasPortalDevice = _messages.MessageField('SasPortalDevice', 2)


class SasportalCustomersDevicesCreateSignedRequest(_messages.Message):
  r"""A SasportalCustomersDevicesCreateSignedRequest object.

  Fields:
    parent: Required. The name of the parent resource.
    sasPortalCreateSignedDeviceRequest: A SasPortalCreateSignedDeviceRequest
      resource to be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  sasPortalCreateSignedDeviceRequest = _messages.MessageField('SasPortalCreateSignedDeviceRequest', 2)


class SasportalCustomersDevicesDeleteRequest(_messages.Message):
  r"""A SasportalCustomersDevicesDeleteRequest object.

  Fields:
    name: Required. The name of the device.
  """

  name = _messages.StringField(1, required=True)


class SasportalCustomersDevicesGetRequest(_messages.Message):
  r"""A SasportalCustomersDevicesGetRequest object.

  Fields:
    name: Required. The name of the device.
  """

  name = _messages.StringField(1, required=True)


class SasportalCustomersDevicesListRequest(_messages.Message):
  r"""A SasportalCustomersDevicesListRequest object.

  Fields:
    filter: The filter expression. The filter should have one of the following
      formats: "sn=123454" or "display_name=MyDevice". sn corresponds to
      serial number of the device. The filter is case insensitive.
    pageSize: The maximum number of devices to return in the response. If
      empty or zero, all devices will be listed. Must be in the range [0,
      1000].
    pageToken: A pagination token returned from a previous call to ListDevices
      that indicates where this listing should continue from.
    parent: Required. The name of the parent resource.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SasportalCustomersDevicesMoveRequest(_messages.Message):
  r"""A SasportalCustomersDevicesMoveRequest object.

  Fields:
    name: Required. The name of the device to move.
    sasPortalMoveDeviceRequest: A SasPortalMoveDeviceRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  sasPortalMoveDeviceRequest = _messages.MessageField('SasPortalMoveDeviceRequest', 2)


class SasportalCustomersDevicesPatchRequest(_messages.Message):
  r"""A SasportalCustomersDevicesPatchRequest object.

  Fields:
    name: Output only. The resource path name.
    sasPortalDevice: A SasPortalDevice resource to be passed as the request
      body.
    updateMask: Fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  sasPortalDevice = _messages.MessageField('SasPortalDevice', 2)
  updateMask = _messages.StringField(3)


class SasportalCustomersDevicesSignDeviceRequest(_messages.Message):
  r"""A SasportalCustomersDevicesSignDeviceRequest object.

  Fields:
    name: Output only. The resource path name.
    sasPortalSignDeviceRequest: A SasPortalSignDeviceRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  sasPortalSignDeviceRequest = _messages.MessageField('SasPortalSignDeviceRequest', 2)


class SasportalCustomersDevicesUpdateSignedRequest(_messages.Message):
  r"""A SasportalCustomersDevicesUpdateSignedRequest object.

  Fields:
    name: Required. The name of the device to update.
    sasPortalUpdateSignedDeviceRequest: A SasPortalUpdateSignedDeviceRequest
      resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  sasPortalUpdateSignedDeviceRequest = _messages.MessageField('SasPortalUpdateSignedDeviceRequest', 2)


class SasportalCustomersGetRequest(_messages.Message):
  r"""A SasportalCustomersGetRequest object.

  Fields:
    name: Required. The name of the customer.
  """

  name = _messages.StringField(1, required=True)


class SasportalCustomersListGcpProjectDeploymentsRequest(_messages.Message):
  r"""A SasportalCustomersListGcpProjectDeploymentsRequest object."""


class SasportalCustomersListLegacyOrganizationsRequest(_messages.Message):
  r"""A SasportalCustomersListLegacyOrganizationsRequest object."""


class SasportalCustomersListRequest(_messages.Message):
  r"""A SasportalCustomersListRequest object.

  Fields:
    pageSize: The maximum number of customers to return in the response.
    pageToken: A pagination token returned from a previous call to
      ListCustomers that indicates where this listing should continue from.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)


class SasportalCustomersNodesCreateRequest(_messages.Message):
  r"""A SasportalCustomersNodesCreateRequest object.

  Fields:
    parent: Required. The parent resource name where the node is to be
      created.
    sasPortalNode: A SasPortalNode resource to be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  sasPortalNode = _messages.MessageField('SasPortalNode', 2)


class SasportalCustomersNodesDeleteRequest(_messages.Message):
  r"""A SasportalCustomersNodesDeleteRequest object.

  Fields:
    name: Required. The name of the node.
  """

  name = _messages.StringField(1, required=True)


class SasportalCustomersNodesDeploymentsCreateRequest(_messages.Message):
  r"""A SasportalCustomersNodesDeploymentsCreateRequest object.

  Fields:
    parent: Required. The parent resource name where the deployment is to be
      created.
    sasPortalDeployment: A SasPortalDeployment resource to be passed as the
      request body.
  """

  parent = _messages.StringField(1, required=True)
  sasPortalDeployment = _messages.MessageField('SasPortalDeployment', 2)


class SasportalCustomersNodesDeploymentsListRequest(_messages.Message):
  r"""A SasportalCustomersNodesDeploymentsListRequest object.

  Fields:
    filter: The filter expression. The filter should have the following
      format: "DIRECT_CHILDREN" or format: "direct_children". The filter is
      case insensitive. If empty, then no deployments are filtered.
    pageSize: The maximum number of deployments to return in the response.
    pageToken: A pagination token returned from a previous call to
      ListDeployments that indicates where this listing should continue from.
    parent: Required. The parent resource name, for example, "nodes/1",
      customer/1/nodes/2.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SasportalCustomersNodesDevicesCreateRequest(_messages.Message):
  r"""A SasportalCustomersNodesDevicesCreateRequest object.

  Fields:
    parent: Required. The name of the parent resource.
    sasPortalDevice: A SasPortalDevice resource to be passed as the request
      body.
  """

  parent = _messages.StringField(1, required=True)
  sasPortalDevice = _messages.MessageField('SasPortalDevice', 2)


class SasportalCustomersNodesDevicesCreateSignedRequest(_messages.Message):
  r"""A SasportalCustomersNodesDevicesCreateSignedRequest object.

  Fields:
    parent: Required. The name of the parent resource.
    sasPortalCreateSignedDeviceRequest: A SasPortalCreateSignedDeviceRequest
      resource to be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  sasPortalCreateSignedDeviceRequest = _messages.MessageField('SasPortalCreateSignedDeviceRequest', 2)


class SasportalCustomersNodesDevicesListRequest(_messages.Message):
  r"""A SasportalCustomersNodesDevicesListRequest object.

  Fields:
    filter: The filter expression. The filter should have one of the following
      formats: "sn=123454" or "display_name=MyDevice". sn corresponds to
      serial number of the device. The filter is case insensitive.
    pageSize: The maximum number of devices to return in the response. If
      empty or zero, all devices will be listed. Must be in the range [0,
      1000].
    pageToken: A pagination token returned from a previous call to ListDevices
      that indicates where this listing should continue from.
    parent: Required. The name of the parent resource.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SasportalCustomersNodesGetRequest(_messages.Message):
  r"""A SasportalCustomersNodesGetRequest object.

  Fields:
    name: Required. The name of the node.
  """

  name = _messages.StringField(1, required=True)


class SasportalCustomersNodesListRequest(_messages.Message):
  r"""A SasportalCustomersNodesListRequest object.

  Fields:
    filter: The filter expression. The filter should have the following
      format: "DIRECT_CHILDREN" or format: "direct_children". The filter is
      case insensitive. If empty, then no nodes are filtered.
    pageSize: The maximum number of nodes to return in the response.
    pageToken: A pagination token returned from a previous call to ListNodes
      that indicates where this listing should continue from.
    parent: Required. The parent resource name, for example, "nodes/1".
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SasportalCustomersNodesMoveRequest(_messages.Message):
  r"""A SasportalCustomersNodesMoveRequest object.

  Fields:
    name: Required. The name of the node to move.
    sasPortalMoveNodeRequest: A SasPortalMoveNodeRequest resource to be passed
      as the request body.
  """

  name = _messages.StringField(1, required=True)
  sasPortalMoveNodeRequest = _messages.MessageField('SasPortalMoveNodeRequest', 2)


class SasportalCustomersNodesNodesCreateRequest(_messages.Message):
  r"""A SasportalCustomersNodesNodesCreateRequest object.

  Fields:
    parent: Required. The parent resource name where the node is to be
      created.
    sasPortalNode: A SasPortalNode resource to be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  sasPortalNode = _messages.MessageField('SasPortalNode', 2)


class SasportalCustomersNodesNodesListRequest(_messages.Message):
  r"""A SasportalCustomersNodesNodesListRequest object.

  Fields:
    filter: The filter expression. The filter should have the following
      format: "DIRECT_CHILDREN" or format: "direct_children". The filter is
      case insensitive. If empty, then no nodes are filtered.
    pageSize: The maximum number of nodes to return in the response.
    pageToken: A pagination token returned from a previous call to ListNodes
      that indicates where this listing should continue from.
    parent: Required. The parent resource name, for example, "nodes/1".
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SasportalCustomersNodesPatchRequest(_messages.Message):
  r"""A SasportalCustomersNodesPatchRequest object.

  Fields:
    name: Output only. Resource name.
    sasPortalNode: A SasPortalNode resource to be passed as the request body.
    updateMask: Fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  sasPortalNode = _messages.MessageField('SasPortalNode', 2)
  updateMask = _messages.StringField(3)


class SasportalCustomersPatchRequest(_messages.Message):
  r"""A SasportalCustomersPatchRequest object.

  Fields:
    name: Output only. Resource name of the customer.
    sasPortalCustomer: A SasPortalCustomer resource to be passed as the
      request body.
    updateMask: Fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  sasPortalCustomer = _messages.MessageField('SasPortalCustomer', 2)
  updateMask = _messages.StringField(3)


class SasportalDeploymentsDevicesDeleteRequest(_messages.Message):
  r"""A SasportalDeploymentsDevicesDeleteRequest object.

  Fields:
    name: Required. The name of the device.
  """

  name = _messages.StringField(1, required=True)


class SasportalDeploymentsDevicesGetRequest(_messages.Message):
  r"""A SasportalDeploymentsDevicesGetRequest object.

  Fields:
    name: Required. The name of the device.
  """

  name = _messages.StringField(1, required=True)


class SasportalDeploymentsDevicesMoveRequest(_messages.Message):
  r"""A SasportalDeploymentsDevicesMoveRequest object.

  Fields:
    name: Required. The name of the device to move.
    sasPortalMoveDeviceRequest: A SasPortalMoveDeviceRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  sasPortalMoveDeviceRequest = _messages.MessageField('SasPortalMoveDeviceRequest', 2)


class SasportalDeploymentsDevicesPatchRequest(_messages.Message):
  r"""A SasportalDeploymentsDevicesPatchRequest object.

  Fields:
    name: Output only. The resource path name.
    sasPortalDevice: A SasPortalDevice resource to be passed as the request
      body.
    updateMask: Fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  sasPortalDevice = _messages.MessageField('SasPortalDevice', 2)
  updateMask = _messages.StringField(3)


class SasportalDeploymentsDevicesSignDeviceRequest(_messages.Message):
  r"""A SasportalDeploymentsDevicesSignDeviceRequest object.

  Fields:
    name: Output only. The resource path name.
    sasPortalSignDeviceRequest: A SasPortalSignDeviceRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  sasPortalSignDeviceRequest = _messages.MessageField('SasPortalSignDeviceRequest', 2)


class SasportalDeploymentsDevicesUpdateSignedRequest(_messages.Message):
  r"""A SasportalDeploymentsDevicesUpdateSignedRequest object.

  Fields:
    name: Required. The name of the device to update.
    sasPortalUpdateSignedDeviceRequest: A SasPortalUpdateSignedDeviceRequest
      resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  sasPortalUpdateSignedDeviceRequest = _messages.MessageField('SasPortalUpdateSignedDeviceRequest', 2)


class SasportalDeploymentsGetRequest(_messages.Message):
  r"""A SasportalDeploymentsGetRequest object.

  Fields:
    name: Required. The name of the deployment.
  """

  name = _messages.StringField(1, required=True)


class SasportalNodesDeploymentsDeleteRequest(_messages.Message):
  r"""A SasportalNodesDeploymentsDeleteRequest object.

  Fields:
    name: Required. The name of the deployment.
  """

  name = _messages.StringField(1, required=True)


class SasportalNodesDeploymentsDevicesCreateRequest(_messages.Message):
  r"""A SasportalNodesDeploymentsDevicesCreateRequest object.

  Fields:
    parent: Required. The name of the parent resource.
    sasPortalDevice: A SasPortalDevice resource to be passed as the request
      body.
  """

  parent = _messages.StringField(1, required=True)
  sasPortalDevice = _messages.MessageField('SasPortalDevice', 2)


class SasportalNodesDeploymentsDevicesCreateSignedRequest(_messages.Message):
  r"""A SasportalNodesDeploymentsDevicesCreateSignedRequest object.

  Fields:
    parent: Required. The name of the parent resource.
    sasPortalCreateSignedDeviceRequest: A SasPortalCreateSignedDeviceRequest
      resource to be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  sasPortalCreateSignedDeviceRequest = _messages.MessageField('SasPortalCreateSignedDeviceRequest', 2)


class SasportalNodesDeploymentsDevicesListRequest(_messages.Message):
  r"""A SasportalNodesDeploymentsDevicesListRequest object.

  Fields:
    filter: The filter expression. The filter should have one of the following
      formats: "sn=123454" or "display_name=MyDevice". sn corresponds to
      serial number of the device. The filter is case insensitive.
    pageSize: The maximum number of devices to return in the response. If
      empty or zero, all devices will be listed. Must be in the range [0,
      1000].
    pageToken: A pagination token returned from a previous call to ListDevices
      that indicates where this listing should continue from.
    parent: Required. The name of the parent resource.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SasportalNodesDeploymentsGetRequest(_messages.Message):
  r"""A SasportalNodesDeploymentsGetRequest object.

  Fields:
    name: Required. The name of the deployment.
  """

  name = _messages.StringField(1, required=True)


class SasportalNodesDeploymentsListRequest(_messages.Message):
  r"""A SasportalNodesDeploymentsListRequest object.

  Fields:
    filter: The filter expression. The filter should have the following
      format: "DIRECT_CHILDREN" or format: "direct_children". The filter is
      case insensitive. If empty, then no deployments are filtered.
    pageSize: The maximum number of deployments to return in the response.
    pageToken: A pagination token returned from a previous call to
      ListDeployments that indicates where this listing should continue from.
    parent: Required. The parent resource name, for example, "nodes/1",
      customer/1/nodes/2.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SasportalNodesDeploymentsMoveRequest(_messages.Message):
  r"""A SasportalNodesDeploymentsMoveRequest object.

  Fields:
    name: Required. The name of the deployment to move.
    sasPortalMoveDeploymentRequest: A SasPortalMoveDeploymentRequest resource
      to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  sasPortalMoveDeploymentRequest = _messages.MessageField('SasPortalMoveDeploymentRequest', 2)


class SasportalNodesDeploymentsPatchRequest(_messages.Message):
  r"""A SasportalNodesDeploymentsPatchRequest object.

  Fields:
    name: Output only. Resource name.
    sasPortalDeployment: A SasPortalDeployment resource to be passed as the
      request body.
    updateMask: Fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  sasPortalDeployment = _messages.MessageField('SasPortalDeployment', 2)
  updateMask = _messages.StringField(3)


class SasportalNodesDevicesCreateRequest(_messages.Message):
  r"""A SasportalNodesDevicesCreateRequest object.

  Fields:
    parent: Required. The name of the parent resource.
    sasPortalDevice: A SasPortalDevice resource to be passed as the request
      body.
  """

  parent = _messages.StringField(1, required=True)
  sasPortalDevice = _messages.MessageField('SasPortalDevice', 2)


class SasportalNodesDevicesCreateSignedRequest(_messages.Message):
  r"""A SasportalNodesDevicesCreateSignedRequest object.

  Fields:
    parent: Required. The name of the parent resource.
    sasPortalCreateSignedDeviceRequest: A SasPortalCreateSignedDeviceRequest
      resource to be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  sasPortalCreateSignedDeviceRequest = _messages.MessageField('SasPortalCreateSignedDeviceRequest', 2)


class SasportalNodesDevicesDeleteRequest(_messages.Message):
  r"""A SasportalNodesDevicesDeleteRequest object.

  Fields:
    name: Required. The name of the device.
  """

  name = _messages.StringField(1, required=True)


class SasportalNodesDevicesGetRequest(_messages.Message):
  r"""A SasportalNodesDevicesGetRequest object.

  Fields:
    name: Required. The name of the device.
  """

  name = _messages.StringField(1, required=True)


class SasportalNodesDevicesListRequest(_messages.Message):
  r"""A SasportalNodesDevicesListRequest object.

  Fields:
    filter: The filter expression. The filter should have one of the following
      formats: "sn=123454" or "display_name=MyDevice". sn corresponds to
      serial number of the device. The filter is case insensitive.
    pageSize: The maximum number of devices to return in the response. If
      empty or zero, all devices will be listed. Must be in the range [0,
      1000].
    pageToken: A pagination token returned from a previous call to ListDevices
      that indicates where this listing should continue from.
    parent: Required. The name of the parent resource.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SasportalNodesDevicesMoveRequest(_messages.Message):
  r"""A SasportalNodesDevicesMoveRequest object.

  Fields:
    name: Required. The name of the device to move.
    sasPortalMoveDeviceRequest: A SasPortalMoveDeviceRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  sasPortalMoveDeviceRequest = _messages.MessageField('SasPortalMoveDeviceRequest', 2)


class SasportalNodesDevicesPatchRequest(_messages.Message):
  r"""A SasportalNodesDevicesPatchRequest object.

  Fields:
    name: Output only. The resource path name.
    sasPortalDevice: A SasPortalDevice resource to be passed as the request
      body.
    updateMask: Fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  sasPortalDevice = _messages.MessageField('SasPortalDevice', 2)
  updateMask = _messages.StringField(3)


class SasportalNodesDevicesSignDeviceRequest(_messages.Message):
  r"""A SasportalNodesDevicesSignDeviceRequest object.

  Fields:
    name: Output only. The resource path name.
    sasPortalSignDeviceRequest: A SasPortalSignDeviceRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  sasPortalSignDeviceRequest = _messages.MessageField('SasPortalSignDeviceRequest', 2)


class SasportalNodesDevicesUpdateSignedRequest(_messages.Message):
  r"""A SasportalNodesDevicesUpdateSignedRequest object.

  Fields:
    name: Required. The name of the device to update.
    sasPortalUpdateSignedDeviceRequest: A SasPortalUpdateSignedDeviceRequest
      resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  sasPortalUpdateSignedDeviceRequest = _messages.MessageField('SasPortalUpdateSignedDeviceRequest', 2)


class SasportalNodesGetRequest(_messages.Message):
  r"""A SasportalNodesGetRequest object.

  Fields:
    name: Required. The name of the node.
  """

  name = _messages.StringField(1, required=True)


class SasportalNodesNodesCreateRequest(_messages.Message):
  r"""A SasportalNodesNodesCreateRequest object.

  Fields:
    parent: Required. The parent resource name where the node is to be
      created.
    sasPortalNode: A SasPortalNode resource to be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  sasPortalNode = _messages.MessageField('SasPortalNode', 2)


class SasportalNodesNodesDeleteRequest(_messages.Message):
  r"""A SasportalNodesNodesDeleteRequest object.

  Fields:
    name: Required. The name of the node.
  """

  name = _messages.StringField(1, required=True)


class SasportalNodesNodesDeploymentsCreateRequest(_messages.Message):
  r"""A SasportalNodesNodesDeploymentsCreateRequest object.

  Fields:
    parent: Required. The parent resource name where the deployment is to be
      created.
    sasPortalDeployment: A SasPortalDeployment resource to be passed as the
      request body.
  """

  parent = _messages.StringField(1, required=True)
  sasPortalDeployment = _messages.MessageField('SasPortalDeployment', 2)


class SasportalNodesNodesDeploymentsListRequest(_messages.Message):
  r"""A SasportalNodesNodesDeploymentsListRequest object.

  Fields:
    filter: The filter expression. The filter should have the following
      format: "DIRECT_CHILDREN" or format: "direct_children". The filter is
      case insensitive. If empty, then no deployments are filtered.
    pageSize: The maximum number of deployments to return in the response.
    pageToken: A pagination token returned from a previous call to
      ListDeployments that indicates where this listing should continue from.
    parent: Required. The parent resource name, for example, "nodes/1",
      customer/1/nodes/2.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SasportalNodesNodesDevicesCreateRequest(_messages.Message):
  r"""A SasportalNodesNodesDevicesCreateRequest object.

  Fields:
    parent: Required. The name of the parent resource.
    sasPortalDevice: A SasPortalDevice resource to be passed as the request
      body.
  """

  parent = _messages.StringField(1, required=True)
  sasPortalDevice = _messages.MessageField('SasPortalDevice', 2)


class SasportalNodesNodesDevicesCreateSignedRequest(_messages.Message):
  r"""A SasportalNodesNodesDevicesCreateSignedRequest object.

  Fields:
    parent: Required. The name of the parent resource.
    sasPortalCreateSignedDeviceRequest: A SasPortalCreateSignedDeviceRequest
      resource to be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  sasPortalCreateSignedDeviceRequest = _messages.MessageField('SasPortalCreateSignedDeviceRequest', 2)


class SasportalNodesNodesDevicesListRequest(_messages.Message):
  r"""A SasportalNodesNodesDevicesListRequest object.

  Fields:
    filter: The filter expression. The filter should have one of the following
      formats: "sn=123454" or "display_name=MyDevice". sn corresponds to
      serial number of the device. The filter is case insensitive.
    pageSize: The maximum number of devices to return in the response. If
      empty or zero, all devices will be listed. Must be in the range [0,
      1000].
    pageToken: A pagination token returned from a previous call to ListDevices
      that indicates where this listing should continue from.
    parent: Required. The name of the parent resource.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SasportalNodesNodesGetRequest(_messages.Message):
  r"""A SasportalNodesNodesGetRequest object.

  Fields:
    name: Required. The name of the node.
  """

  name = _messages.StringField(1, required=True)


class SasportalNodesNodesListRequest(_messages.Message):
  r"""A SasportalNodesNodesListRequest object.

  Fields:
    filter: The filter expression. The filter should have the following
      format: "DIRECT_CHILDREN" or format: "direct_children". The filter is
      case insensitive. If empty, then no nodes are filtered.
    pageSize: The maximum number of nodes to return in the response.
    pageToken: A pagination token returned from a previous call to ListNodes
      that indicates where this listing should continue from.
    parent: Required. The parent resource name, for example, "nodes/1".
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SasportalNodesNodesMoveRequest(_messages.Message):
  r"""A SasportalNodesNodesMoveRequest object.

  Fields:
    name: Required. The name of the node to move.
    sasPortalMoveNodeRequest: A SasPortalMoveNodeRequest resource to be passed
      as the request body.
  """

  name = _messages.StringField(1, required=True)
  sasPortalMoveNodeRequest = _messages.MessageField('SasPortalMoveNodeRequest', 2)


class SasportalNodesNodesNodesCreateRequest(_messages.Message):
  r"""A SasportalNodesNodesNodesCreateRequest object.

  Fields:
    parent: Required. The parent resource name where the node is to be
      created.
    sasPortalNode: A SasPortalNode resource to be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  sasPortalNode = _messages.MessageField('SasPortalNode', 2)


class SasportalNodesNodesNodesListRequest(_messages.Message):
  r"""A SasportalNodesNodesNodesListRequest object.

  Fields:
    filter: The filter expression. The filter should have the following
      format: "DIRECT_CHILDREN" or format: "direct_children". The filter is
      case insensitive. If empty, then no nodes are filtered.
    pageSize: The maximum number of nodes to return in the response.
    pageToken: A pagination token returned from a previous call to ListNodes
      that indicates where this listing should continue from.
    parent: Required. The parent resource name, for example, "nodes/1".
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SasportalNodesNodesPatchRequest(_messages.Message):
  r"""A SasportalNodesNodesPatchRequest object.

  Fields:
    name: Output only. Resource name.
    sasPortalNode: A SasPortalNode resource to be passed as the request body.
    updateMask: Fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  sasPortalNode = _messages.MessageField('SasPortalNode', 2)
  updateMask = _messages.StringField(3)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
