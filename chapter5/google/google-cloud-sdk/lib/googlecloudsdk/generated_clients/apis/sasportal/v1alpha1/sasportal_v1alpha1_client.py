"""Generated client library for sasportal version v1alpha1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.sasportal.v1alpha1 import sasportal_v1alpha1_messages as messages


class SasportalV1alpha1(base_api.BaseApiClient):
  """Generated client library for service sasportal version v1alpha1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://sasportal.googleapis.com/'
  MTLS_BASE_URL = 'https://sasportal.mtls.googleapis.com/'

  _PACKAGE = 'sasportal'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform', 'https://www.googleapis.com/auth/sasportal']
  _VERSION = 'v1alpha1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'SasportalV1alpha1'
  _URL_VERSION = 'v1alpha1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new sasportal handle."""
    url = url or self.BASE_URL
    super(SasportalV1alpha1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.customers_deployments_devices = self.CustomersDeploymentsDevicesService(self)
    self.customers_deployments = self.CustomersDeploymentsService(self)
    self.customers_devices = self.CustomersDevicesService(self)
    self.customers_nodes_deployments = self.CustomersNodesDeploymentsService(self)
    self.customers_nodes_devices = self.CustomersNodesDevicesService(self)
    self.customers_nodes_nodes = self.CustomersNodesNodesService(self)
    self.customers_nodes = self.CustomersNodesService(self)
    self.customers = self.CustomersService(self)
    self.deployments_devices = self.DeploymentsDevicesService(self)
    self.deployments = self.DeploymentsService(self)
    self.installer = self.InstallerService(self)
    self.nodes_deployments_devices = self.NodesDeploymentsDevicesService(self)
    self.nodes_deployments = self.NodesDeploymentsService(self)
    self.nodes_devices = self.NodesDevicesService(self)
    self.nodes_nodes_deployments = self.NodesNodesDeploymentsService(self)
    self.nodes_nodes_devices = self.NodesNodesDevicesService(self)
    self.nodes_nodes_nodes = self.NodesNodesNodesService(self)
    self.nodes_nodes = self.NodesNodesService(self)
    self.nodes = self.NodesService(self)
    self.policies = self.PoliciesService(self)

  class CustomersDeploymentsDevicesService(base_api.BaseApiService):
    """Service class for the customers_deployments_devices resource."""

    _NAME = 'customers_deployments_devices'

    def __init__(self, client):
      super(SasportalV1alpha1.CustomersDeploymentsDevicesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a device under a node or customer.

      Args:
        request: (SasportalCustomersDeploymentsDevicesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDevice) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/deployments/{deploymentsId}/devices',
        http_method='POST',
        method_id='sasportal.customers.deployments.devices.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/devices',
        request_field='sasPortalDevice',
        request_type_name='SasportalCustomersDeploymentsDevicesCreateRequest',
        response_type_name='SasPortalDevice',
        supports_download=False,
    )

    def CreateSigned(self, request, global_params=None):
      r"""Creates a signed device under a node or customer.

      Args:
        request: (SasportalCustomersDeploymentsDevicesCreateSignedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDevice) The response message.
      """
      config = self.GetMethodConfig('CreateSigned')
      return self._RunMethod(
          config, request, global_params=global_params)

    CreateSigned.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/deployments/{deploymentsId}/devices:createSigned',
        http_method='POST',
        method_id='sasportal.customers.deployments.devices.createSigned',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/devices:createSigned',
        request_field='sasPortalCreateSignedDeviceRequest',
        request_type_name='SasportalCustomersDeploymentsDevicesCreateSignedRequest',
        response_type_name='SasPortalDevice',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists devices under a node or customer.

      Args:
        request: (SasportalCustomersDeploymentsDevicesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalListDevicesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/deployments/{deploymentsId}/devices',
        http_method='GET',
        method_id='sasportal.customers.deployments.devices.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/devices',
        request_field='',
        request_type_name='SasportalCustomersDeploymentsDevicesListRequest',
        response_type_name='SasPortalListDevicesResponse',
        supports_download=False,
    )

  class CustomersDeploymentsService(base_api.BaseApiService):
    """Service class for the customers_deployments resource."""

    _NAME = 'customers_deployments'

    def __init__(self, client):
      super(SasportalV1alpha1.CustomersDeploymentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new deployment.

      Args:
        request: (SasportalCustomersDeploymentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDeployment) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/deployments',
        http_method='POST',
        method_id='sasportal.customers.deployments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/deployments',
        request_field='sasPortalDeployment',
        request_type_name='SasportalCustomersDeploymentsCreateRequest',
        response_type_name='SasPortalDeployment',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a deployment.

      Args:
        request: (SasportalCustomersDeploymentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/deployments/{deploymentsId}',
        http_method='DELETE',
        method_id='sasportal.customers.deployments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='SasportalCustomersDeploymentsDeleteRequest',
        response_type_name='SasPortalEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns a requested deployment.

      Args:
        request: (SasportalCustomersDeploymentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDeployment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/deployments/{deploymentsId}',
        http_method='GET',
        method_id='sasportal.customers.deployments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='SasportalCustomersDeploymentsGetRequest',
        response_type_name='SasPortalDeployment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists deployments.

      Args:
        request: (SasportalCustomersDeploymentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalListDeploymentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/deployments',
        http_method='GET',
        method_id='sasportal.customers.deployments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/deployments',
        request_field='',
        request_type_name='SasportalCustomersDeploymentsListRequest',
        response_type_name='SasPortalListDeploymentsResponse',
        supports_download=False,
    )

    def Move(self, request, global_params=None):
      r"""Moves a deployment under another node or customer.

      Args:
        request: (SasportalCustomersDeploymentsMoveRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalOperation) The response message.
      """
      config = self.GetMethodConfig('Move')
      return self._RunMethod(
          config, request, global_params=global_params)

    Move.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/deployments/{deploymentsId}:move',
        http_method='POST',
        method_id='sasportal.customers.deployments.move',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:move',
        request_field='sasPortalMoveDeploymentRequest',
        request_type_name='SasportalCustomersDeploymentsMoveRequest',
        response_type_name='SasPortalOperation',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing deployment.

      Args:
        request: (SasportalCustomersDeploymentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDeployment) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/deployments/{deploymentsId}',
        http_method='PATCH',
        method_id='sasportal.customers.deployments.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='sasPortalDeployment',
        request_type_name='SasportalCustomersDeploymentsPatchRequest',
        response_type_name='SasPortalDeployment',
        supports_download=False,
    )

  class CustomersDevicesService(base_api.BaseApiService):
    """Service class for the customers_devices resource."""

    _NAME = 'customers_devices'

    def __init__(self, client):
      super(SasportalV1alpha1.CustomersDevicesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a device under a node or customer.

      Args:
        request: (SasportalCustomersDevicesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDevice) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/devices',
        http_method='POST',
        method_id='sasportal.customers.devices.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/devices',
        request_field='sasPortalDevice',
        request_type_name='SasportalCustomersDevicesCreateRequest',
        response_type_name='SasPortalDevice',
        supports_download=False,
    )

    def CreateSigned(self, request, global_params=None):
      r"""Creates a signed device under a node or customer.

      Args:
        request: (SasportalCustomersDevicesCreateSignedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDevice) The response message.
      """
      config = self.GetMethodConfig('CreateSigned')
      return self._RunMethod(
          config, request, global_params=global_params)

    CreateSigned.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/devices:createSigned',
        http_method='POST',
        method_id='sasportal.customers.devices.createSigned',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/devices:createSigned',
        request_field='sasPortalCreateSignedDeviceRequest',
        request_type_name='SasportalCustomersDevicesCreateSignedRequest',
        response_type_name='SasPortalDevice',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a device.

      Args:
        request: (SasportalCustomersDevicesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/devices/{devicesId}',
        http_method='DELETE',
        method_id='sasportal.customers.devices.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='SasportalCustomersDevicesDeleteRequest',
        response_type_name='SasPortalEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details about a device.

      Args:
        request: (SasportalCustomersDevicesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDevice) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/devices/{devicesId}',
        http_method='GET',
        method_id='sasportal.customers.devices.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='SasportalCustomersDevicesGetRequest',
        response_type_name='SasPortalDevice',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists devices under a node or customer.

      Args:
        request: (SasportalCustomersDevicesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalListDevicesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/devices',
        http_method='GET',
        method_id='sasportal.customers.devices.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/devices',
        request_field='',
        request_type_name='SasportalCustomersDevicesListRequest',
        response_type_name='SasPortalListDevicesResponse',
        supports_download=False,
    )

    def Move(self, request, global_params=None):
      r"""Moves a device under another node or customer.

      Args:
        request: (SasportalCustomersDevicesMoveRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalOperation) The response message.
      """
      config = self.GetMethodConfig('Move')
      return self._RunMethod(
          config, request, global_params=global_params)

    Move.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/devices/{devicesId}:move',
        http_method='POST',
        method_id='sasportal.customers.devices.move',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:move',
        request_field='sasPortalMoveDeviceRequest',
        request_type_name='SasportalCustomersDevicesMoveRequest',
        response_type_name='SasPortalOperation',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a device.

      Args:
        request: (SasportalCustomersDevicesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDevice) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/devices/{devicesId}',
        http_method='PATCH',
        method_id='sasportal.customers.devices.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='sasPortalDevice',
        request_type_name='SasportalCustomersDevicesPatchRequest',
        response_type_name='SasPortalDevice',
        supports_download=False,
    )

    def SignDevice(self, request, global_params=None):
      r"""Signs a device.

      Args:
        request: (SasportalCustomersDevicesSignDeviceRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalEmpty) The response message.
      """
      config = self.GetMethodConfig('SignDevice')
      return self._RunMethod(
          config, request, global_params=global_params)

    SignDevice.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/devices/{devicesId}:signDevice',
        http_method='POST',
        method_id='sasportal.customers.devices.signDevice',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:signDevice',
        request_field='sasPortalSignDeviceRequest',
        request_type_name='SasportalCustomersDevicesSignDeviceRequest',
        response_type_name='SasPortalEmpty',
        supports_download=False,
    )

    def UpdateSigned(self, request, global_params=None):
      r"""Updates a signed device.

      Args:
        request: (SasportalCustomersDevicesUpdateSignedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDevice) The response message.
      """
      config = self.GetMethodConfig('UpdateSigned')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSigned.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/devices/{devicesId}:updateSigned',
        http_method='PATCH',
        method_id='sasportal.customers.devices.updateSigned',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:updateSigned',
        request_field='sasPortalUpdateSignedDeviceRequest',
        request_type_name='SasportalCustomersDevicesUpdateSignedRequest',
        response_type_name='SasPortalDevice',
        supports_download=False,
    )

  class CustomersNodesDeploymentsService(base_api.BaseApiService):
    """Service class for the customers_nodes_deployments resource."""

    _NAME = 'customers_nodes_deployments'

    def __init__(self, client):
      super(SasportalV1alpha1.CustomersNodesDeploymentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new deployment.

      Args:
        request: (SasportalCustomersNodesDeploymentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDeployment) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/nodes/{nodesId}/deployments',
        http_method='POST',
        method_id='sasportal.customers.nodes.deployments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/deployments',
        request_field='sasPortalDeployment',
        request_type_name='SasportalCustomersNodesDeploymentsCreateRequest',
        response_type_name='SasPortalDeployment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists deployments.

      Args:
        request: (SasportalCustomersNodesDeploymentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalListDeploymentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/nodes/{nodesId}/deployments',
        http_method='GET',
        method_id='sasportal.customers.nodes.deployments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/deployments',
        request_field='',
        request_type_name='SasportalCustomersNodesDeploymentsListRequest',
        response_type_name='SasPortalListDeploymentsResponse',
        supports_download=False,
    )

  class CustomersNodesDevicesService(base_api.BaseApiService):
    """Service class for the customers_nodes_devices resource."""

    _NAME = 'customers_nodes_devices'

    def __init__(self, client):
      super(SasportalV1alpha1.CustomersNodesDevicesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a device under a node or customer.

      Args:
        request: (SasportalCustomersNodesDevicesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDevice) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/nodes/{nodesId}/devices',
        http_method='POST',
        method_id='sasportal.customers.nodes.devices.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/devices',
        request_field='sasPortalDevice',
        request_type_name='SasportalCustomersNodesDevicesCreateRequest',
        response_type_name='SasPortalDevice',
        supports_download=False,
    )

    def CreateSigned(self, request, global_params=None):
      r"""Creates a signed device under a node or customer.

      Args:
        request: (SasportalCustomersNodesDevicesCreateSignedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDevice) The response message.
      """
      config = self.GetMethodConfig('CreateSigned')
      return self._RunMethod(
          config, request, global_params=global_params)

    CreateSigned.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/nodes/{nodesId}/devices:createSigned',
        http_method='POST',
        method_id='sasportal.customers.nodes.devices.createSigned',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/devices:createSigned',
        request_field='sasPortalCreateSignedDeviceRequest',
        request_type_name='SasportalCustomersNodesDevicesCreateSignedRequest',
        response_type_name='SasPortalDevice',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists devices under a node or customer.

      Args:
        request: (SasportalCustomersNodesDevicesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalListDevicesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/nodes/{nodesId}/devices',
        http_method='GET',
        method_id='sasportal.customers.nodes.devices.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/devices',
        request_field='',
        request_type_name='SasportalCustomersNodesDevicesListRequest',
        response_type_name='SasPortalListDevicesResponse',
        supports_download=False,
    )

  class CustomersNodesNodesService(base_api.BaseApiService):
    """Service class for the customers_nodes_nodes resource."""

    _NAME = 'customers_nodes_nodes'

    def __init__(self, client):
      super(SasportalV1alpha1.CustomersNodesNodesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new node.

      Args:
        request: (SasportalCustomersNodesNodesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalNode) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/nodes/{nodesId}/nodes',
        http_method='POST',
        method_id='sasportal.customers.nodes.nodes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/nodes',
        request_field='sasPortalNode',
        request_type_name='SasportalCustomersNodesNodesCreateRequest',
        response_type_name='SasPortalNode',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists nodes.

      Args:
        request: (SasportalCustomersNodesNodesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalListNodesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/nodes/{nodesId}/nodes',
        http_method='GET',
        method_id='sasportal.customers.nodes.nodes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/nodes',
        request_field='',
        request_type_name='SasportalCustomersNodesNodesListRequest',
        response_type_name='SasPortalListNodesResponse',
        supports_download=False,
    )

  class CustomersNodesService(base_api.BaseApiService):
    """Service class for the customers_nodes resource."""

    _NAME = 'customers_nodes'

    def __init__(self, client):
      super(SasportalV1alpha1.CustomersNodesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new node.

      Args:
        request: (SasportalCustomersNodesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalNode) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/nodes',
        http_method='POST',
        method_id='sasportal.customers.nodes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/nodes',
        request_field='sasPortalNode',
        request_type_name='SasportalCustomersNodesCreateRequest',
        response_type_name='SasPortalNode',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a node.

      Args:
        request: (SasportalCustomersNodesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/nodes/{nodesId}',
        http_method='DELETE',
        method_id='sasportal.customers.nodes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='SasportalCustomersNodesDeleteRequest',
        response_type_name='SasPortalEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns a requested node.

      Args:
        request: (SasportalCustomersNodesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalNode) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/nodes/{nodesId}',
        http_method='GET',
        method_id='sasportal.customers.nodes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='SasportalCustomersNodesGetRequest',
        response_type_name='SasPortalNode',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists nodes.

      Args:
        request: (SasportalCustomersNodesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalListNodesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/nodes',
        http_method='GET',
        method_id='sasportal.customers.nodes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/nodes',
        request_field='',
        request_type_name='SasportalCustomersNodesListRequest',
        response_type_name='SasPortalListNodesResponse',
        supports_download=False,
    )

    def Move(self, request, global_params=None):
      r"""Moves a node under another node or customer.

      Args:
        request: (SasportalCustomersNodesMoveRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalOperation) The response message.
      """
      config = self.GetMethodConfig('Move')
      return self._RunMethod(
          config, request, global_params=global_params)

    Move.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/nodes/{nodesId}:move',
        http_method='POST',
        method_id='sasportal.customers.nodes.move',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:move',
        request_field='sasPortalMoveNodeRequest',
        request_type_name='SasportalCustomersNodesMoveRequest',
        response_type_name='SasPortalOperation',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing node.

      Args:
        request: (SasportalCustomersNodesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalNode) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}/nodes/{nodesId}',
        http_method='PATCH',
        method_id='sasportal.customers.nodes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='sasPortalNode',
        request_type_name='SasportalCustomersNodesPatchRequest',
        response_type_name='SasPortalNode',
        supports_download=False,
    )

  class CustomersService(base_api.BaseApiService):
    """Service class for the customers resource."""

    _NAME = 'customers'

    def __init__(self, client):
      super(SasportalV1alpha1.CustomersService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Returns a requested customer.

      Args:
        request: (SasportalCustomersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalCustomer) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}',
        http_method='GET',
        method_id='sasportal.customers.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='SasportalCustomersGetRequest',
        response_type_name='SasPortalCustomer',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns a list of requested customers.

      Args:
        request: (SasportalCustomersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalListCustomersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='sasportal.customers.list',
        ordered_params=[],
        path_params=[],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/customers',
        request_field='',
        request_type_name='SasportalCustomersListRequest',
        response_type_name='SasPortalListCustomersResponse',
        supports_download=False,
    )

    def ListGcpProjectDeployments(self, request, global_params=None):
      r"""Returns a list of SAS deployments associated with current GCP project. Includes whether SAS analytics has been enabled or not.

      Args:
        request: (SasportalCustomersListGcpProjectDeploymentsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalListGcpProjectDeploymentsResponse) The response message.
      """
      config = self.GetMethodConfig('ListGcpProjectDeployments')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListGcpProjectDeployments.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='sasportal.customers.listGcpProjectDeployments',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v1alpha1/customers:listGcpProjectDeployments',
        request_field='',
        request_type_name='SasportalCustomersListGcpProjectDeploymentsRequest',
        response_type_name='SasPortalListGcpProjectDeploymentsResponse',
        supports_download=False,
    )

    def ListLegacyOrganizations(self, request, global_params=None):
      r"""Returns a list of legacy organizations.

      Args:
        request: (SasportalCustomersListLegacyOrganizationsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalListLegacyOrganizationsResponse) The response message.
      """
      config = self.GetMethodConfig('ListLegacyOrganizations')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListLegacyOrganizations.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='sasportal.customers.listLegacyOrganizations',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v1alpha1/customers:listLegacyOrganizations',
        request_field='',
        request_type_name='SasportalCustomersListLegacyOrganizationsRequest',
        response_type_name='SasPortalListLegacyOrganizationsResponse',
        supports_download=False,
    )

    def MigrateOrganization(self, request, global_params=None):
      r"""Migrates a SAS organization to the cloud. This will create GCP projects for each deployment and associate them. The SAS Organization is linked to the gcp project that called the command. go/sas-legacy-customer-migration.

      Args:
        request: (SasPortalMigrateOrganizationRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalOperation) The response message.
      """
      config = self.GetMethodConfig('MigrateOrganization')
      return self._RunMethod(
          config, request, global_params=global_params)

    MigrateOrganization.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sasportal.customers.migrateOrganization',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v1alpha1/customers:migrateOrganization',
        request_field='<request>',
        request_type_name='SasPortalMigrateOrganizationRequest',
        response_type_name='SasPortalOperation',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing customer.

      Args:
        request: (SasportalCustomersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalCustomer) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/customers/{customersId}',
        http_method='PATCH',
        method_id='sasportal.customers.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='sasPortalCustomer',
        request_type_name='SasportalCustomersPatchRequest',
        response_type_name='SasPortalCustomer',
        supports_download=False,
    )

    def ProvisionDeployment(self, request, global_params=None):
      r"""Creates a new SAS deployment through the GCP workflow. Creates a SAS organization if an organization match is not found.

      Args:
        request: (SasPortalProvisionDeploymentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalProvisionDeploymentResponse) The response message.
      """
      config = self.GetMethodConfig('ProvisionDeployment')
      return self._RunMethod(
          config, request, global_params=global_params)

    ProvisionDeployment.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sasportal.customers.provisionDeployment',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v1alpha1/customers:provisionDeployment',
        request_field='<request>',
        request_type_name='SasPortalProvisionDeploymentRequest',
        response_type_name='SasPortalProvisionDeploymentResponse',
        supports_download=False,
    )

    def SetupSasAnalytics(self, request, global_params=None):
      r"""Setups the a GCP Project to receive SAS Analytics messages via GCP Pub/Sub with a subscription to BigQuery. All the Pub/Sub topics and BigQuery tables are created automatically as part of this service.

      Args:
        request: (SasPortalSetupSasAnalyticsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalOperation) The response message.
      """
      config = self.GetMethodConfig('SetupSasAnalytics')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetupSasAnalytics.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sasportal.customers.setupSasAnalytics',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v1alpha1/customers:setupSasAnalytics',
        request_field='<request>',
        request_type_name='SasPortalSetupSasAnalyticsRequest',
        response_type_name='SasPortalOperation',
        supports_download=False,
    )

  class DeploymentsDevicesService(base_api.BaseApiService):
    """Service class for the deployments_devices resource."""

    _NAME = 'deployments_devices'

    def __init__(self, client):
      super(SasportalV1alpha1.DeploymentsDevicesService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a device.

      Args:
        request: (SasportalDeploymentsDevicesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/deployments/{deploymentsId}/devices/{devicesId}',
        http_method='DELETE',
        method_id='sasportal.deployments.devices.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='SasportalDeploymentsDevicesDeleteRequest',
        response_type_name='SasPortalEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details about a device.

      Args:
        request: (SasportalDeploymentsDevicesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDevice) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/deployments/{deploymentsId}/devices/{devicesId}',
        http_method='GET',
        method_id='sasportal.deployments.devices.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='SasportalDeploymentsDevicesGetRequest',
        response_type_name='SasPortalDevice',
        supports_download=False,
    )

    def Move(self, request, global_params=None):
      r"""Moves a device under another node or customer.

      Args:
        request: (SasportalDeploymentsDevicesMoveRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalOperation) The response message.
      """
      config = self.GetMethodConfig('Move')
      return self._RunMethod(
          config, request, global_params=global_params)

    Move.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/deployments/{deploymentsId}/devices/{devicesId}:move',
        http_method='POST',
        method_id='sasportal.deployments.devices.move',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:move',
        request_field='sasPortalMoveDeviceRequest',
        request_type_name='SasportalDeploymentsDevicesMoveRequest',
        response_type_name='SasPortalOperation',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a device.

      Args:
        request: (SasportalDeploymentsDevicesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDevice) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/deployments/{deploymentsId}/devices/{devicesId}',
        http_method='PATCH',
        method_id='sasportal.deployments.devices.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='sasPortalDevice',
        request_type_name='SasportalDeploymentsDevicesPatchRequest',
        response_type_name='SasPortalDevice',
        supports_download=False,
    )

    def SignDevice(self, request, global_params=None):
      r"""Signs a device.

      Args:
        request: (SasportalDeploymentsDevicesSignDeviceRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalEmpty) The response message.
      """
      config = self.GetMethodConfig('SignDevice')
      return self._RunMethod(
          config, request, global_params=global_params)

    SignDevice.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/deployments/{deploymentsId}/devices/{devicesId}:signDevice',
        http_method='POST',
        method_id='sasportal.deployments.devices.signDevice',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:signDevice',
        request_field='sasPortalSignDeviceRequest',
        request_type_name='SasportalDeploymentsDevicesSignDeviceRequest',
        response_type_name='SasPortalEmpty',
        supports_download=False,
    )

    def UpdateSigned(self, request, global_params=None):
      r"""Updates a signed device.

      Args:
        request: (SasportalDeploymentsDevicesUpdateSignedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDevice) The response message.
      """
      config = self.GetMethodConfig('UpdateSigned')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSigned.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/deployments/{deploymentsId}/devices/{devicesId}:updateSigned',
        http_method='PATCH',
        method_id='sasportal.deployments.devices.updateSigned',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:updateSigned',
        request_field='sasPortalUpdateSignedDeviceRequest',
        request_type_name='SasportalDeploymentsDevicesUpdateSignedRequest',
        response_type_name='SasPortalDevice',
        supports_download=False,
    )

  class DeploymentsService(base_api.BaseApiService):
    """Service class for the deployments resource."""

    _NAME = 'deployments'

    def __init__(self, client):
      super(SasportalV1alpha1.DeploymentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Returns a requested deployment.

      Args:
        request: (SasportalDeploymentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDeployment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/deployments/{deploymentsId}',
        http_method='GET',
        method_id='sasportal.deployments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='SasportalDeploymentsGetRequest',
        response_type_name='SasPortalDeployment',
        supports_download=False,
    )

  class InstallerService(base_api.BaseApiService):
    """Service class for the installer resource."""

    _NAME = 'installer'

    def __init__(self, client):
      super(SasportalV1alpha1.InstallerService, self).__init__(client)
      self._upload_configs = {
          }

    def GenerateSecret(self, request, global_params=None):
      r"""Generates a secret to be used with the ValidateInstaller.

      Args:
        request: (SasPortalGenerateSecretRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalGenerateSecretResponse) The response message.
      """
      config = self.GetMethodConfig('GenerateSecret')
      return self._RunMethod(
          config, request, global_params=global_params)

    GenerateSecret.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sasportal.installer.generateSecret',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v1alpha1/installer:generateSecret',
        request_field='<request>',
        request_type_name='SasPortalGenerateSecretRequest',
        response_type_name='SasPortalGenerateSecretResponse',
        supports_download=False,
    )

    def Validate(self, request, global_params=None):
      r"""Validates the identity of a Certified Professional Installer (CPI).

      Args:
        request: (SasPortalValidateInstallerRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalValidateInstallerResponse) The response message.
      """
      config = self.GetMethodConfig('Validate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Validate.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sasportal.installer.validate',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v1alpha1/installer:validate',
        request_field='<request>',
        request_type_name='SasPortalValidateInstallerRequest',
        response_type_name='SasPortalValidateInstallerResponse',
        supports_download=False,
    )

  class NodesDeploymentsDevicesService(base_api.BaseApiService):
    """Service class for the nodes_deployments_devices resource."""

    _NAME = 'nodes_deployments_devices'

    def __init__(self, client):
      super(SasportalV1alpha1.NodesDeploymentsDevicesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a device under a node or customer.

      Args:
        request: (SasportalNodesDeploymentsDevicesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDevice) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/deployments/{deploymentsId}/devices',
        http_method='POST',
        method_id='sasportal.nodes.deployments.devices.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/devices',
        request_field='sasPortalDevice',
        request_type_name='SasportalNodesDeploymentsDevicesCreateRequest',
        response_type_name='SasPortalDevice',
        supports_download=False,
    )

    def CreateSigned(self, request, global_params=None):
      r"""Creates a signed device under a node or customer.

      Args:
        request: (SasportalNodesDeploymentsDevicesCreateSignedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDevice) The response message.
      """
      config = self.GetMethodConfig('CreateSigned')
      return self._RunMethod(
          config, request, global_params=global_params)

    CreateSigned.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/deployments/{deploymentsId}/devices:createSigned',
        http_method='POST',
        method_id='sasportal.nodes.deployments.devices.createSigned',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/devices:createSigned',
        request_field='sasPortalCreateSignedDeviceRequest',
        request_type_name='SasportalNodesDeploymentsDevicesCreateSignedRequest',
        response_type_name='SasPortalDevice',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists devices under a node or customer.

      Args:
        request: (SasportalNodesDeploymentsDevicesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalListDevicesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/deployments/{deploymentsId}/devices',
        http_method='GET',
        method_id='sasportal.nodes.deployments.devices.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/devices',
        request_field='',
        request_type_name='SasportalNodesDeploymentsDevicesListRequest',
        response_type_name='SasPortalListDevicesResponse',
        supports_download=False,
    )

  class NodesDeploymentsService(base_api.BaseApiService):
    """Service class for the nodes_deployments resource."""

    _NAME = 'nodes_deployments'

    def __init__(self, client):
      super(SasportalV1alpha1.NodesDeploymentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a deployment.

      Args:
        request: (SasportalNodesDeploymentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/deployments/{deploymentsId}',
        http_method='DELETE',
        method_id='sasportal.nodes.deployments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='SasportalNodesDeploymentsDeleteRequest',
        response_type_name='SasPortalEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns a requested deployment.

      Args:
        request: (SasportalNodesDeploymentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDeployment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/deployments/{deploymentsId}',
        http_method='GET',
        method_id='sasportal.nodes.deployments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='SasportalNodesDeploymentsGetRequest',
        response_type_name='SasPortalDeployment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists deployments.

      Args:
        request: (SasportalNodesDeploymentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalListDeploymentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/deployments',
        http_method='GET',
        method_id='sasportal.nodes.deployments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/deployments',
        request_field='',
        request_type_name='SasportalNodesDeploymentsListRequest',
        response_type_name='SasPortalListDeploymentsResponse',
        supports_download=False,
    )

    def Move(self, request, global_params=None):
      r"""Moves a deployment under another node or customer.

      Args:
        request: (SasportalNodesDeploymentsMoveRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalOperation) The response message.
      """
      config = self.GetMethodConfig('Move')
      return self._RunMethod(
          config, request, global_params=global_params)

    Move.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/deployments/{deploymentsId}:move',
        http_method='POST',
        method_id='sasportal.nodes.deployments.move',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:move',
        request_field='sasPortalMoveDeploymentRequest',
        request_type_name='SasportalNodesDeploymentsMoveRequest',
        response_type_name='SasPortalOperation',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing deployment.

      Args:
        request: (SasportalNodesDeploymentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDeployment) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/deployments/{deploymentsId}',
        http_method='PATCH',
        method_id='sasportal.nodes.deployments.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='sasPortalDeployment',
        request_type_name='SasportalNodesDeploymentsPatchRequest',
        response_type_name='SasPortalDeployment',
        supports_download=False,
    )

  class NodesDevicesService(base_api.BaseApiService):
    """Service class for the nodes_devices resource."""

    _NAME = 'nodes_devices'

    def __init__(self, client):
      super(SasportalV1alpha1.NodesDevicesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a device under a node or customer.

      Args:
        request: (SasportalNodesDevicesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDevice) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/devices',
        http_method='POST',
        method_id='sasportal.nodes.devices.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/devices',
        request_field='sasPortalDevice',
        request_type_name='SasportalNodesDevicesCreateRequest',
        response_type_name='SasPortalDevice',
        supports_download=False,
    )

    def CreateSigned(self, request, global_params=None):
      r"""Creates a signed device under a node or customer.

      Args:
        request: (SasportalNodesDevicesCreateSignedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDevice) The response message.
      """
      config = self.GetMethodConfig('CreateSigned')
      return self._RunMethod(
          config, request, global_params=global_params)

    CreateSigned.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/devices:createSigned',
        http_method='POST',
        method_id='sasportal.nodes.devices.createSigned',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/devices:createSigned',
        request_field='sasPortalCreateSignedDeviceRequest',
        request_type_name='SasportalNodesDevicesCreateSignedRequest',
        response_type_name='SasPortalDevice',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a device.

      Args:
        request: (SasportalNodesDevicesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/devices/{devicesId}',
        http_method='DELETE',
        method_id='sasportal.nodes.devices.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='SasportalNodesDevicesDeleteRequest',
        response_type_name='SasPortalEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details about a device.

      Args:
        request: (SasportalNodesDevicesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDevice) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/devices/{devicesId}',
        http_method='GET',
        method_id='sasportal.nodes.devices.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='SasportalNodesDevicesGetRequest',
        response_type_name='SasPortalDevice',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists devices under a node or customer.

      Args:
        request: (SasportalNodesDevicesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalListDevicesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/devices',
        http_method='GET',
        method_id='sasportal.nodes.devices.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/devices',
        request_field='',
        request_type_name='SasportalNodesDevicesListRequest',
        response_type_name='SasPortalListDevicesResponse',
        supports_download=False,
    )

    def Move(self, request, global_params=None):
      r"""Moves a device under another node or customer.

      Args:
        request: (SasportalNodesDevicesMoveRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalOperation) The response message.
      """
      config = self.GetMethodConfig('Move')
      return self._RunMethod(
          config, request, global_params=global_params)

    Move.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/devices/{devicesId}:move',
        http_method='POST',
        method_id='sasportal.nodes.devices.move',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:move',
        request_field='sasPortalMoveDeviceRequest',
        request_type_name='SasportalNodesDevicesMoveRequest',
        response_type_name='SasPortalOperation',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a device.

      Args:
        request: (SasportalNodesDevicesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDevice) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/devices/{devicesId}',
        http_method='PATCH',
        method_id='sasportal.nodes.devices.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='sasPortalDevice',
        request_type_name='SasportalNodesDevicesPatchRequest',
        response_type_name='SasPortalDevice',
        supports_download=False,
    )

    def SignDevice(self, request, global_params=None):
      r"""Signs a device.

      Args:
        request: (SasportalNodesDevicesSignDeviceRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalEmpty) The response message.
      """
      config = self.GetMethodConfig('SignDevice')
      return self._RunMethod(
          config, request, global_params=global_params)

    SignDevice.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/devices/{devicesId}:signDevice',
        http_method='POST',
        method_id='sasportal.nodes.devices.signDevice',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:signDevice',
        request_field='sasPortalSignDeviceRequest',
        request_type_name='SasportalNodesDevicesSignDeviceRequest',
        response_type_name='SasPortalEmpty',
        supports_download=False,
    )

    def UpdateSigned(self, request, global_params=None):
      r"""Updates a signed device.

      Args:
        request: (SasportalNodesDevicesUpdateSignedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDevice) The response message.
      """
      config = self.GetMethodConfig('UpdateSigned')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSigned.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/devices/{devicesId}:updateSigned',
        http_method='PATCH',
        method_id='sasportal.nodes.devices.updateSigned',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:updateSigned',
        request_field='sasPortalUpdateSignedDeviceRequest',
        request_type_name='SasportalNodesDevicesUpdateSignedRequest',
        response_type_name='SasPortalDevice',
        supports_download=False,
    )

  class NodesNodesDeploymentsService(base_api.BaseApiService):
    """Service class for the nodes_nodes_deployments resource."""

    _NAME = 'nodes_nodes_deployments'

    def __init__(self, client):
      super(SasportalV1alpha1.NodesNodesDeploymentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new deployment.

      Args:
        request: (SasportalNodesNodesDeploymentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDeployment) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/nodes/{nodesId1}/deployments',
        http_method='POST',
        method_id='sasportal.nodes.nodes.deployments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/deployments',
        request_field='sasPortalDeployment',
        request_type_name='SasportalNodesNodesDeploymentsCreateRequest',
        response_type_name='SasPortalDeployment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists deployments.

      Args:
        request: (SasportalNodesNodesDeploymentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalListDeploymentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/nodes/{nodesId1}/deployments',
        http_method='GET',
        method_id='sasportal.nodes.nodes.deployments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/deployments',
        request_field='',
        request_type_name='SasportalNodesNodesDeploymentsListRequest',
        response_type_name='SasPortalListDeploymentsResponse',
        supports_download=False,
    )

  class NodesNodesDevicesService(base_api.BaseApiService):
    """Service class for the nodes_nodes_devices resource."""

    _NAME = 'nodes_nodes_devices'

    def __init__(self, client):
      super(SasportalV1alpha1.NodesNodesDevicesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a device under a node or customer.

      Args:
        request: (SasportalNodesNodesDevicesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDevice) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/nodes/{nodesId1}/devices',
        http_method='POST',
        method_id='sasportal.nodes.nodes.devices.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/devices',
        request_field='sasPortalDevice',
        request_type_name='SasportalNodesNodesDevicesCreateRequest',
        response_type_name='SasPortalDevice',
        supports_download=False,
    )

    def CreateSigned(self, request, global_params=None):
      r"""Creates a signed device under a node or customer.

      Args:
        request: (SasportalNodesNodesDevicesCreateSignedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalDevice) The response message.
      """
      config = self.GetMethodConfig('CreateSigned')
      return self._RunMethod(
          config, request, global_params=global_params)

    CreateSigned.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/nodes/{nodesId1}/devices:createSigned',
        http_method='POST',
        method_id='sasportal.nodes.nodes.devices.createSigned',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/devices:createSigned',
        request_field='sasPortalCreateSignedDeviceRequest',
        request_type_name='SasportalNodesNodesDevicesCreateSignedRequest',
        response_type_name='SasPortalDevice',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists devices under a node or customer.

      Args:
        request: (SasportalNodesNodesDevicesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalListDevicesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/nodes/{nodesId1}/devices',
        http_method='GET',
        method_id='sasportal.nodes.nodes.devices.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/devices',
        request_field='',
        request_type_name='SasportalNodesNodesDevicesListRequest',
        response_type_name='SasPortalListDevicesResponse',
        supports_download=False,
    )

  class NodesNodesNodesService(base_api.BaseApiService):
    """Service class for the nodes_nodes_nodes resource."""

    _NAME = 'nodes_nodes_nodes'

    def __init__(self, client):
      super(SasportalV1alpha1.NodesNodesNodesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new node.

      Args:
        request: (SasportalNodesNodesNodesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalNode) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/nodes/{nodesId1}/nodes',
        http_method='POST',
        method_id='sasportal.nodes.nodes.nodes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/nodes',
        request_field='sasPortalNode',
        request_type_name='SasportalNodesNodesNodesCreateRequest',
        response_type_name='SasPortalNode',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists nodes.

      Args:
        request: (SasportalNodesNodesNodesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalListNodesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/nodes/{nodesId1}/nodes',
        http_method='GET',
        method_id='sasportal.nodes.nodes.nodes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/nodes',
        request_field='',
        request_type_name='SasportalNodesNodesNodesListRequest',
        response_type_name='SasPortalListNodesResponse',
        supports_download=False,
    )

  class NodesNodesService(base_api.BaseApiService):
    """Service class for the nodes_nodes resource."""

    _NAME = 'nodes_nodes'

    def __init__(self, client):
      super(SasportalV1alpha1.NodesNodesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new node.

      Args:
        request: (SasportalNodesNodesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalNode) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/nodes',
        http_method='POST',
        method_id='sasportal.nodes.nodes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/nodes',
        request_field='sasPortalNode',
        request_type_name='SasportalNodesNodesCreateRequest',
        response_type_name='SasPortalNode',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a node.

      Args:
        request: (SasportalNodesNodesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/nodes/{nodesId1}',
        http_method='DELETE',
        method_id='sasportal.nodes.nodes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='SasportalNodesNodesDeleteRequest',
        response_type_name='SasPortalEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns a requested node.

      Args:
        request: (SasportalNodesNodesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalNode) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/nodes/{nodesId1}',
        http_method='GET',
        method_id='sasportal.nodes.nodes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='SasportalNodesNodesGetRequest',
        response_type_name='SasPortalNode',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists nodes.

      Args:
        request: (SasportalNodesNodesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalListNodesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/nodes',
        http_method='GET',
        method_id='sasportal.nodes.nodes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/nodes',
        request_field='',
        request_type_name='SasportalNodesNodesListRequest',
        response_type_name='SasPortalListNodesResponse',
        supports_download=False,
    )

    def Move(self, request, global_params=None):
      r"""Moves a node under another node or customer.

      Args:
        request: (SasportalNodesNodesMoveRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalOperation) The response message.
      """
      config = self.GetMethodConfig('Move')
      return self._RunMethod(
          config, request, global_params=global_params)

    Move.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/nodes/{nodesId1}:move',
        http_method='POST',
        method_id='sasportal.nodes.nodes.move',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:move',
        request_field='sasPortalMoveNodeRequest',
        request_type_name='SasportalNodesNodesMoveRequest',
        response_type_name='SasPortalOperation',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing node.

      Args:
        request: (SasportalNodesNodesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalNode) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}/nodes/{nodesId1}',
        http_method='PATCH',
        method_id='sasportal.nodes.nodes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='sasPortalNode',
        request_type_name='SasportalNodesNodesPatchRequest',
        response_type_name='SasPortalNode',
        supports_download=False,
    )

  class NodesService(base_api.BaseApiService):
    """Service class for the nodes resource."""

    _NAME = 'nodes'

    def __init__(self, client):
      super(SasportalV1alpha1.NodesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Returns a requested node.

      Args:
        request: (SasportalNodesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalNode) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/nodes/{nodesId}',
        http_method='GET',
        method_id='sasportal.nodes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='SasportalNodesGetRequest',
        response_type_name='SasPortalNode',
        supports_download=False,
    )

  class PoliciesService(base_api.BaseApiService):
    """Service class for the policies resource."""

    _NAME = 'policies'

    def __init__(self, client):
      super(SasportalV1alpha1.PoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (SasPortalGetPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sasportal.policies.get',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v1alpha1/policies:get',
        request_field='<request>',
        request_type_name='SasPortalGetPolicyRequest',
        response_type_name='SasPortalPolicy',
        supports_download=False,
    )

    def Set(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy.

      Args:
        request: (SasPortalSetPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalPolicy) The response message.
      """
      config = self.GetMethodConfig('Set')
      return self._RunMethod(
          config, request, global_params=global_params)

    Set.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sasportal.policies.set',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v1alpha1/policies:set',
        request_field='<request>',
        request_type_name='SasPortalSetPolicyRequest',
        response_type_name='SasPortalPolicy',
        supports_download=False,
    )

    def Test(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource.

      Args:
        request: (SasPortalTestPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SasPortalTestPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('Test')
      return self._RunMethod(
          config, request, global_params=global_params)

    Test.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='sasportal.policies.test',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v1alpha1/policies:test',
        request_field='<request>',
        request_type_name='SasPortalTestPermissionsRequest',
        response_type_name='SasPortalTestPermissionsResponse',
        supports_download=False,
    )
