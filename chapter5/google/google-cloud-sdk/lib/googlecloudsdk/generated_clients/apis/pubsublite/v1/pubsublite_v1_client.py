"""Generated client library for pubsublite version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.pubsublite.v1 import pubsublite_v1_messages as messages


class PubsubliteV1(base_api.BaseApiClient):
  """Generated client library for service pubsublite version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://pubsublite.googleapis.com/'
  MTLS_BASE_URL = 'https://pubsublite.mtls.googleapis.com/'

  _PACKAGE = 'pubsublite'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'PubsubliteV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new pubsublite handle."""
    url = url or self.BASE_URL
    super(PubsubliteV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.admin_projects_locations_operations = self.AdminProjectsLocationsOperationsService(self)
    self.admin_projects_locations_reservations_topics = self.AdminProjectsLocationsReservationsTopicsService(self)
    self.admin_projects_locations_reservations = self.AdminProjectsLocationsReservationsService(self)
    self.admin_projects_locations_subscriptions = self.AdminProjectsLocationsSubscriptionsService(self)
    self.admin_projects_locations_topics_subscriptions = self.AdminProjectsLocationsTopicsSubscriptionsService(self)
    self.admin_projects_locations_topics = self.AdminProjectsLocationsTopicsService(self)
    self.admin_projects_locations = self.AdminProjectsLocationsService(self)
    self.admin_projects = self.AdminProjectsService(self)
    self.admin = self.AdminService(self)
    self.cursor_projects_locations_subscriptions_cursors = self.CursorProjectsLocationsSubscriptionsCursorsService(self)
    self.cursor_projects_locations_subscriptions = self.CursorProjectsLocationsSubscriptionsService(self)
    self.cursor_projects_locations = self.CursorProjectsLocationsService(self)
    self.cursor_projects = self.CursorProjectsService(self)
    self.cursor = self.CursorService(self)
    self.topicStats_projects_locations_topics = self.TopicStatsProjectsLocationsTopicsService(self)
    self.topicStats_projects_locations = self.TopicStatsProjectsLocationsService(self)
    self.topicStats_projects = self.TopicStatsProjectsService(self)
    self.topicStats = self.TopicStatsService(self)

  class AdminProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the admin_projects_locations_operations resource."""

    _NAME = 'admin_projects_locations_operations'

    def __init__(self, client):
      super(PubsubliteV1.AdminProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (PubsubliteAdminProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/admin/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='pubsublite.admin.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/admin/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='PubsubliteAdminProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (PubsubliteAdminProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/admin/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='pubsublite.admin.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/admin/{+name}',
        request_field='',
        request_type_name='PubsubliteAdminProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (PubsubliteAdminProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/admin/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='pubsublite.admin.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/admin/{+name}',
        request_field='',
        request_type_name='PubsubliteAdminProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (PubsubliteAdminProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/admin/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='pubsublite.admin.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/admin/{+name}/operations',
        request_field='',
        request_type_name='PubsubliteAdminProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class AdminProjectsLocationsReservationsTopicsService(base_api.BaseApiService):
    """Service class for the admin_projects_locations_reservations_topics resource."""

    _NAME = 'admin_projects_locations_reservations_topics'

    def __init__(self, client):
      super(PubsubliteV1.AdminProjectsLocationsReservationsTopicsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the topics attached to the specified reservation.

      Args:
        request: (PubsubliteAdminProjectsLocationsReservationsTopicsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListReservationTopicsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/admin/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}/topics',
        http_method='GET',
        method_id='pubsublite.admin.projects.locations.reservations.topics.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/admin/{+name}/topics',
        request_field='',
        request_type_name='PubsubliteAdminProjectsLocationsReservationsTopicsListRequest',
        response_type_name='ListReservationTopicsResponse',
        supports_download=False,
    )

  class AdminProjectsLocationsReservationsService(base_api.BaseApiService):
    """Service class for the admin_projects_locations_reservations resource."""

    _NAME = 'admin_projects_locations_reservations'

    def __init__(self, client):
      super(PubsubliteV1.AdminProjectsLocationsReservationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new reservation.

      Args:
        request: (PubsubliteAdminProjectsLocationsReservationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Reservation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/admin/projects/{projectsId}/locations/{locationsId}/reservations',
        http_method='POST',
        method_id='pubsublite.admin.projects.locations.reservations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['reservationId'],
        relative_path='v1/admin/{+parent}/reservations',
        request_field='reservation',
        request_type_name='PubsubliteAdminProjectsLocationsReservationsCreateRequest',
        response_type_name='Reservation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified reservation.

      Args:
        request: (PubsubliteAdminProjectsLocationsReservationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/admin/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}',
        http_method='DELETE',
        method_id='pubsublite.admin.projects.locations.reservations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/admin/{+name}',
        request_field='',
        request_type_name='PubsubliteAdminProjectsLocationsReservationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the reservation configuration.

      Args:
        request: (PubsubliteAdminProjectsLocationsReservationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Reservation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/admin/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}',
        http_method='GET',
        method_id='pubsublite.admin.projects.locations.reservations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/admin/{+name}',
        request_field='',
        request_type_name='PubsubliteAdminProjectsLocationsReservationsGetRequest',
        response_type_name='Reservation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of reservations for the given project.

      Args:
        request: (PubsubliteAdminProjectsLocationsReservationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListReservationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/admin/projects/{projectsId}/locations/{locationsId}/reservations',
        http_method='GET',
        method_id='pubsublite.admin.projects.locations.reservations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/admin/{+parent}/reservations',
        request_field='',
        request_type_name='PubsubliteAdminProjectsLocationsReservationsListRequest',
        response_type_name='ListReservationsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates properties of the specified reservation.

      Args:
        request: (PubsubliteAdminProjectsLocationsReservationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Reservation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/admin/projects/{projectsId}/locations/{locationsId}/reservations/{reservationsId}',
        http_method='PATCH',
        method_id='pubsublite.admin.projects.locations.reservations.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/admin/{+name}',
        request_field='reservation',
        request_type_name='PubsubliteAdminProjectsLocationsReservationsPatchRequest',
        response_type_name='Reservation',
        supports_download=False,
    )

  class AdminProjectsLocationsSubscriptionsService(base_api.BaseApiService):
    """Service class for the admin_projects_locations_subscriptions resource."""

    _NAME = 'admin_projects_locations_subscriptions'

    def __init__(self, client):
      super(PubsubliteV1.AdminProjectsLocationsSubscriptionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new subscription.

      Args:
        request: (PubsubliteAdminProjectsLocationsSubscriptionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Subscription) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/admin/projects/{projectsId}/locations/{locationsId}/subscriptions',
        http_method='POST',
        method_id='pubsublite.admin.projects.locations.subscriptions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['skipBacklog', 'subscriptionId'],
        relative_path='v1/admin/{+parent}/subscriptions',
        request_field='subscription',
        request_type_name='PubsubliteAdminProjectsLocationsSubscriptionsCreateRequest',
        response_type_name='Subscription',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified subscription.

      Args:
        request: (PubsubliteAdminProjectsLocationsSubscriptionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/admin/projects/{projectsId}/locations/{locationsId}/subscriptions/{subscriptionsId}',
        http_method='DELETE',
        method_id='pubsublite.admin.projects.locations.subscriptions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/admin/{+name}',
        request_field='',
        request_type_name='PubsubliteAdminProjectsLocationsSubscriptionsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the subscription configuration.

      Args:
        request: (PubsubliteAdminProjectsLocationsSubscriptionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Subscription) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/admin/projects/{projectsId}/locations/{locationsId}/subscriptions/{subscriptionsId}',
        http_method='GET',
        method_id='pubsublite.admin.projects.locations.subscriptions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/admin/{+name}',
        request_field='',
        request_type_name='PubsubliteAdminProjectsLocationsSubscriptionsGetRequest',
        response_type_name='Subscription',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of subscriptions for the given project.

      Args:
        request: (PubsubliteAdminProjectsLocationsSubscriptionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSubscriptionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/admin/projects/{projectsId}/locations/{locationsId}/subscriptions',
        http_method='GET',
        method_id='pubsublite.admin.projects.locations.subscriptions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/admin/{+parent}/subscriptions',
        request_field='',
        request_type_name='PubsubliteAdminProjectsLocationsSubscriptionsListRequest',
        response_type_name='ListSubscriptionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates properties of the specified subscription.

      Args:
        request: (PubsubliteAdminProjectsLocationsSubscriptionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Subscription) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/admin/projects/{projectsId}/locations/{locationsId}/subscriptions/{subscriptionsId}',
        http_method='PATCH',
        method_id='pubsublite.admin.projects.locations.subscriptions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/admin/{+name}',
        request_field='subscription',
        request_type_name='PubsubliteAdminProjectsLocationsSubscriptionsPatchRequest',
        response_type_name='Subscription',
        supports_download=False,
    )

    def Seek(self, request, global_params=None):
      r"""Performs an out-of-band seek for a subscription to a specified target, which may be timestamps or named positions within the message backlog. Seek translates these targets to cursors for each partition and orchestrates subscribers to start consuming messages from these seek cursors. If an operation is returned, the seek has been registered and subscribers will eventually receive messages from the seek cursors (i.e. eventual consistency), as long as they are using a minimum supported client library version and not a system that tracks cursors independently of Pub/Sub Lite (e.g. Apache Beam, Dataflow, Spark). The seek operation will fail for unsupported clients. If clients would like to know when subscribers react to the seek (or not), they can poll the operation. The seek operation will succeed and complete once subscribers are ready to receive messages from the seek cursors for all partitions of the topic. This means that the seek operation will not complete until all subscribers come online. If the previous seek operation has not yet completed, it will be aborted and the new invocation of seek will supersede it.

      Args:
        request: (PubsubliteAdminProjectsLocationsSubscriptionsSeekRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Seek')
      return self._RunMethod(
          config, request, global_params=global_params)

    Seek.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/admin/projects/{projectsId}/locations/{locationsId}/subscriptions/{subscriptionsId}:seek',
        http_method='POST',
        method_id='pubsublite.admin.projects.locations.subscriptions.seek',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/admin/{+name}:seek',
        request_field='seekSubscriptionRequest',
        request_type_name='PubsubliteAdminProjectsLocationsSubscriptionsSeekRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class AdminProjectsLocationsTopicsSubscriptionsService(base_api.BaseApiService):
    """Service class for the admin_projects_locations_topics_subscriptions resource."""

    _NAME = 'admin_projects_locations_topics_subscriptions'

    def __init__(self, client):
      super(PubsubliteV1.AdminProjectsLocationsTopicsSubscriptionsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the subscriptions attached to the specified topic.

      Args:
        request: (PubsubliteAdminProjectsLocationsTopicsSubscriptionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListTopicSubscriptionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/admin/projects/{projectsId}/locations/{locationsId}/topics/{topicsId}/subscriptions',
        http_method='GET',
        method_id='pubsublite.admin.projects.locations.topics.subscriptions.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/admin/{+name}/subscriptions',
        request_field='',
        request_type_name='PubsubliteAdminProjectsLocationsTopicsSubscriptionsListRequest',
        response_type_name='ListTopicSubscriptionsResponse',
        supports_download=False,
    )

  class AdminProjectsLocationsTopicsService(base_api.BaseApiService):
    """Service class for the admin_projects_locations_topics resource."""

    _NAME = 'admin_projects_locations_topics'

    def __init__(self, client):
      super(PubsubliteV1.AdminProjectsLocationsTopicsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new topic.

      Args:
        request: (PubsubliteAdminProjectsLocationsTopicsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Topic) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/admin/projects/{projectsId}/locations/{locationsId}/topics',
        http_method='POST',
        method_id='pubsublite.admin.projects.locations.topics.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['topicId'],
        relative_path='v1/admin/{+parent}/topics',
        request_field='topic',
        request_type_name='PubsubliteAdminProjectsLocationsTopicsCreateRequest',
        response_type_name='Topic',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified topic.

      Args:
        request: (PubsubliteAdminProjectsLocationsTopicsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/admin/projects/{projectsId}/locations/{locationsId}/topics/{topicsId}',
        http_method='DELETE',
        method_id='pubsublite.admin.projects.locations.topics.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/admin/{+name}',
        request_field='',
        request_type_name='PubsubliteAdminProjectsLocationsTopicsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the topic configuration.

      Args:
        request: (PubsubliteAdminProjectsLocationsTopicsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Topic) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/admin/projects/{projectsId}/locations/{locationsId}/topics/{topicsId}',
        http_method='GET',
        method_id='pubsublite.admin.projects.locations.topics.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/admin/{+name}',
        request_field='',
        request_type_name='PubsubliteAdminProjectsLocationsTopicsGetRequest',
        response_type_name='Topic',
        supports_download=False,
    )

    def GetPartitions(self, request, global_params=None):
      r"""Returns the partition information for the requested topic.

      Args:
        request: (PubsubliteAdminProjectsLocationsTopicsGetPartitionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TopicPartitions) The response message.
      """
      config = self.GetMethodConfig('GetPartitions')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetPartitions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/admin/projects/{projectsId}/locations/{locationsId}/topics/{topicsId}/partitions',
        http_method='GET',
        method_id='pubsublite.admin.projects.locations.topics.getPartitions',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/admin/{+name}/partitions',
        request_field='',
        request_type_name='PubsubliteAdminProjectsLocationsTopicsGetPartitionsRequest',
        response_type_name='TopicPartitions',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns the list of topics for the given project.

      Args:
        request: (PubsubliteAdminProjectsLocationsTopicsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListTopicsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/admin/projects/{projectsId}/locations/{locationsId}/topics',
        http_method='GET',
        method_id='pubsublite.admin.projects.locations.topics.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/admin/{+parent}/topics',
        request_field='',
        request_type_name='PubsubliteAdminProjectsLocationsTopicsListRequest',
        response_type_name='ListTopicsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates properties of the specified topic.

      Args:
        request: (PubsubliteAdminProjectsLocationsTopicsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Topic) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/admin/projects/{projectsId}/locations/{locationsId}/topics/{topicsId}',
        http_method='PATCH',
        method_id='pubsublite.admin.projects.locations.topics.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/admin/{+name}',
        request_field='topic',
        request_type_name='PubsubliteAdminProjectsLocationsTopicsPatchRequest',
        response_type_name='Topic',
        supports_download=False,
    )

  class AdminProjectsLocationsService(base_api.BaseApiService):
    """Service class for the admin_projects_locations resource."""

    _NAME = 'admin_projects_locations'

    def __init__(self, client):
      super(PubsubliteV1.AdminProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class AdminProjectsService(base_api.BaseApiService):
    """Service class for the admin_projects resource."""

    _NAME = 'admin_projects'

    def __init__(self, client):
      super(PubsubliteV1.AdminProjectsService, self).__init__(client)
      self._upload_configs = {
          }

  class AdminService(base_api.BaseApiService):
    """Service class for the admin resource."""

    _NAME = 'admin'

    def __init__(self, client):
      super(PubsubliteV1.AdminService, self).__init__(client)
      self._upload_configs = {
          }

  class CursorProjectsLocationsSubscriptionsCursorsService(base_api.BaseApiService):
    """Service class for the cursor_projects_locations_subscriptions_cursors resource."""

    _NAME = 'cursor_projects_locations_subscriptions_cursors'

    def __init__(self, client):
      super(PubsubliteV1.CursorProjectsLocationsSubscriptionsCursorsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Returns all committed cursor information for a subscription.

      Args:
        request: (PubsubliteCursorProjectsLocationsSubscriptionsCursorsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPartitionCursorsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/cursor/projects/{projectsId}/locations/{locationsId}/subscriptions/{subscriptionsId}/cursors',
        http_method='GET',
        method_id='pubsublite.cursor.projects.locations.subscriptions.cursors.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/cursor/{+parent}/cursors',
        request_field='',
        request_type_name='PubsubliteCursorProjectsLocationsSubscriptionsCursorsListRequest',
        response_type_name='ListPartitionCursorsResponse',
        supports_download=False,
    )

  class CursorProjectsLocationsSubscriptionsService(base_api.BaseApiService):
    """Service class for the cursor_projects_locations_subscriptions resource."""

    _NAME = 'cursor_projects_locations_subscriptions'

    def __init__(self, client):
      super(PubsubliteV1.CursorProjectsLocationsSubscriptionsService, self).__init__(client)
      self._upload_configs = {
          }

    def CommitCursor(self, request, global_params=None):
      r"""Updates the committed cursor.

      Args:
        request: (PubsubliteCursorProjectsLocationsSubscriptionsCommitCursorRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CommitCursorResponse) The response message.
      """
      config = self.GetMethodConfig('CommitCursor')
      return self._RunMethod(
          config, request, global_params=global_params)

    CommitCursor.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/cursor/projects/{projectsId}/locations/{locationsId}/subscriptions/{subscriptionsId}:commitCursor',
        http_method='POST',
        method_id='pubsublite.cursor.projects.locations.subscriptions.commitCursor',
        ordered_params=['subscription'],
        path_params=['subscription'],
        query_params=[],
        relative_path='v1/cursor/{+subscription}:commitCursor',
        request_field='commitCursorRequest',
        request_type_name='PubsubliteCursorProjectsLocationsSubscriptionsCommitCursorRequest',
        response_type_name='CommitCursorResponse',
        supports_download=False,
    )

  class CursorProjectsLocationsService(base_api.BaseApiService):
    """Service class for the cursor_projects_locations resource."""

    _NAME = 'cursor_projects_locations'

    def __init__(self, client):
      super(PubsubliteV1.CursorProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class CursorProjectsService(base_api.BaseApiService):
    """Service class for the cursor_projects resource."""

    _NAME = 'cursor_projects'

    def __init__(self, client):
      super(PubsubliteV1.CursorProjectsService, self).__init__(client)
      self._upload_configs = {
          }

  class CursorService(base_api.BaseApiService):
    """Service class for the cursor resource."""

    _NAME = 'cursor'

    def __init__(self, client):
      super(PubsubliteV1.CursorService, self).__init__(client)
      self._upload_configs = {
          }

  class TopicStatsProjectsLocationsTopicsService(base_api.BaseApiService):
    """Service class for the topicStats_projects_locations_topics resource."""

    _NAME = 'topicStats_projects_locations_topics'

    def __init__(self, client):
      super(PubsubliteV1.TopicStatsProjectsLocationsTopicsService, self).__init__(client)
      self._upload_configs = {
          }

    def ComputeHeadCursor(self, request, global_params=None):
      r"""Compute the head cursor for the partition. The head cursor's offset is guaranteed to be less than or equal to all messages which have not yet been acknowledged as published, and greater than the offset of any message whose publish has already been acknowledged. It is zero if there have never been messages in the partition.

      Args:
        request: (PubsubliteTopicStatsProjectsLocationsTopicsComputeHeadCursorRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ComputeHeadCursorResponse) The response message.
      """
      config = self.GetMethodConfig('ComputeHeadCursor')
      return self._RunMethod(
          config, request, global_params=global_params)

    ComputeHeadCursor.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/topicStats/projects/{projectsId}/locations/{locationsId}/topics/{topicsId}:computeHeadCursor',
        http_method='POST',
        method_id='pubsublite.topicStats.projects.locations.topics.computeHeadCursor',
        ordered_params=['topic'],
        path_params=['topic'],
        query_params=[],
        relative_path='v1/topicStats/{+topic}:computeHeadCursor',
        request_field='computeHeadCursorRequest',
        request_type_name='PubsubliteTopicStatsProjectsLocationsTopicsComputeHeadCursorRequest',
        response_type_name='ComputeHeadCursorResponse',
        supports_download=False,
    )

    def ComputeMessageStats(self, request, global_params=None):
      r"""Compute statistics about a range of messages in a given topic and partition.

      Args:
        request: (PubsubliteTopicStatsProjectsLocationsTopicsComputeMessageStatsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ComputeMessageStatsResponse) The response message.
      """
      config = self.GetMethodConfig('ComputeMessageStats')
      return self._RunMethod(
          config, request, global_params=global_params)

    ComputeMessageStats.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/topicStats/projects/{projectsId}/locations/{locationsId}/topics/{topicsId}:computeMessageStats',
        http_method='POST',
        method_id='pubsublite.topicStats.projects.locations.topics.computeMessageStats',
        ordered_params=['topic'],
        path_params=['topic'],
        query_params=[],
        relative_path='v1/topicStats/{+topic}:computeMessageStats',
        request_field='computeMessageStatsRequest',
        request_type_name='PubsubliteTopicStatsProjectsLocationsTopicsComputeMessageStatsRequest',
        response_type_name='ComputeMessageStatsResponse',
        supports_download=False,
    )

    def ComputeTimeCursor(self, request, global_params=None):
      r"""Compute the corresponding cursor for a publish or event time in a topic partition.

      Args:
        request: (PubsubliteTopicStatsProjectsLocationsTopicsComputeTimeCursorRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ComputeTimeCursorResponse) The response message.
      """
      config = self.GetMethodConfig('ComputeTimeCursor')
      return self._RunMethod(
          config, request, global_params=global_params)

    ComputeTimeCursor.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/topicStats/projects/{projectsId}/locations/{locationsId}/topics/{topicsId}:computeTimeCursor',
        http_method='POST',
        method_id='pubsublite.topicStats.projects.locations.topics.computeTimeCursor',
        ordered_params=['topic'],
        path_params=['topic'],
        query_params=[],
        relative_path='v1/topicStats/{+topic}:computeTimeCursor',
        request_field='computeTimeCursorRequest',
        request_type_name='PubsubliteTopicStatsProjectsLocationsTopicsComputeTimeCursorRequest',
        response_type_name='ComputeTimeCursorResponse',
        supports_download=False,
    )

  class TopicStatsProjectsLocationsService(base_api.BaseApiService):
    """Service class for the topicStats_projects_locations resource."""

    _NAME = 'topicStats_projects_locations'

    def __init__(self, client):
      super(PubsubliteV1.TopicStatsProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class TopicStatsProjectsService(base_api.BaseApiService):
    """Service class for the topicStats_projects resource."""

    _NAME = 'topicStats_projects'

    def __init__(self, client):
      super(PubsubliteV1.TopicStatsProjectsService, self).__init__(client)
      self._upload_configs = {
          }

  class TopicStatsService(base_api.BaseApiService):
    """Service class for the topicStats resource."""

    _NAME = 'topicStats'

    def __init__(self, client):
      super(PubsubliteV1.TopicStatsService, self).__init__(client)
      self._upload_configs = {
          }
