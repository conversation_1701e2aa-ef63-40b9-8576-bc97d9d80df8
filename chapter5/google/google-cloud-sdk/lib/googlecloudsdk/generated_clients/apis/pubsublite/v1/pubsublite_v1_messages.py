"""Generated message classes for pubsublite version v1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'pubsublite'


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class Capacity(_messages.Message):
  r"""The throughput capacity configuration for each partition.

  Fields:
    publishMibPerSec: Publish throughput capacity per partition in MiB/s. Must
      be >= 4 and <= 16.
    subscribeMibPerSec: Subscribe throughput capacity per partition in MiB/s.
      Must be >= 4 and <= 32.
  """

  publishMibPerSec = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  subscribeMibPerSec = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class CommitCursorRequest(_messages.Message):
  r"""Request for CommitCursor.

  Fields:
    cursor: The new value for the committed cursor.
    partition: The partition for which to update the cursor. Partitions are
      zero indexed, so `partition` must be in the range [0,
      topic.num_partitions).
  """

  cursor = _messages.MessageField('Cursor', 1)
  partition = _messages.IntegerField(2)


class CommitCursorResponse(_messages.Message):
  r"""Response for CommitCursor."""


class ComputeHeadCursorRequest(_messages.Message):
  r"""Compute the current head cursor for a partition.

  Fields:
    partition: Required. The partition for which we should compute the head
      cursor.
  """

  partition = _messages.IntegerField(1)


class ComputeHeadCursorResponse(_messages.Message):
  r"""Response containing the head cursor for the requested topic and
  partition.

  Fields:
    headCursor: The head cursor.
  """

  headCursor = _messages.MessageField('Cursor', 1)


class ComputeMessageStatsRequest(_messages.Message):
  r"""Compute statistics about a range of messages in a given topic and
  partition.

  Fields:
    endCursor: The exclusive end of the range. The range is empty if
      end_cursor <= start_cursor. Specifying a start_cursor before the first
      message and an end_cursor after the last message will retrieve all
      messages.
    partition: Required. The partition for which we should compute message
      stats.
    startCursor: The inclusive start of the range.
  """

  endCursor = _messages.MessageField('Cursor', 1)
  partition = _messages.IntegerField(2)
  startCursor = _messages.MessageField('Cursor', 3)


class ComputeMessageStatsResponse(_messages.Message):
  r"""Response containing stats for messages in the requested topic and
  partition.

  Fields:
    messageBytes: The number of quota bytes accounted to these messages.
    messageCount: The count of messages.
    minimumEventTime: The minimum event timestamp across these messages. For
      the purposes of this computation, if a message does not have an event
      time, we use the publish time. The timestamp will be unset if there are
      no messages.
    minimumPublishTime: The minimum publish timestamp across these messages.
      Note that publish timestamps within a partition are not guaranteed to be
      non-decreasing. The timestamp will be unset if there are no messages.
  """

  messageBytes = _messages.IntegerField(1)
  messageCount = _messages.IntegerField(2)
  minimumEventTime = _messages.StringField(3)
  minimumPublishTime = _messages.StringField(4)


class ComputeTimeCursorRequest(_messages.Message):
  r"""Compute the corresponding cursor for a publish or event time in a topic
  partition.

  Fields:
    partition: Required. The partition for which we should compute the cursor.
    target: Required. The target publish or event time. Specifying a future
      time will return an unset cursor.
  """

  partition = _messages.IntegerField(1)
  target = _messages.MessageField('TimeTarget', 2)


class ComputeTimeCursorResponse(_messages.Message):
  r"""Response containing the cursor corresponding to a publish or event time
  in a topic partition.

  Fields:
    cursor: If present, the cursor references the first message with time
      greater than or equal to the specified target time. If such a message
      cannot be found, the cursor will be unset (i.e. `cursor` is not
      present).
  """

  cursor = _messages.MessageField('Cursor', 1)


class Cursor(_messages.Message):
  r"""A cursor that describes the position of a message within a topic
  partition.

  Fields:
    offset: The offset of a message within a topic partition. Must be greater
      than or equal 0.
  """

  offset = _messages.IntegerField(1)


class DeliveryConfig(_messages.Message):
  r"""The settings for a subscription's message delivery.

  Enums:
    DeliveryRequirementValueValuesEnum: The DeliveryRequirement for this
      subscription.

  Fields:
    deliveryRequirement: The DeliveryRequirement for this subscription.
  """

  class DeliveryRequirementValueValuesEnum(_messages.Enum):
    r"""The DeliveryRequirement for this subscription.

    Values:
      DELIVERY_REQUIREMENT_UNSPECIFIED: Default value. This value is unused.
      DELIVER_IMMEDIATELY: The server does not wait for a published message to
        be successfully written to storage before delivering it to
        subscribers.
      DELIVER_AFTER_STORED: The server will not deliver a published message to
        subscribers until the message has been successfully written to
        storage. This will result in higher end-to-end latency, but consistent
        delivery.
    """
    DELIVERY_REQUIREMENT_UNSPECIFIED = 0
    DELIVER_IMMEDIATELY = 1
    DELIVER_AFTER_STORED = 2

  deliveryRequirement = _messages.EnumField('DeliveryRequirementValueValuesEnum', 1)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class ExportConfig(_messages.Message):
  r"""Configuration for a Pub/Sub Lite subscription that writes messages to a
  destination. User subscriber clients must not connect to this subscription.

  Enums:
    CurrentStateValueValuesEnum: Output only. The current state of the export,
      which may be different to the desired state due to errors. This field is
      output only.
    DesiredStateValueValuesEnum: The desired state of this export. Setting
      this to values other than `ACTIVE` and `PAUSED` will result in an error.

  Fields:
    currentState: Output only. The current state of the export, which may be
      different to the desired state due to errors. This field is output only.
    deadLetterTopic: Optional. The name of an optional Pub/Sub Lite topic to
      publish messages that can not be exported to the destination. For
      example, the message can not be published to the Pub/Sub service because
      it does not satisfy the constraints documented at
      https://cloud.google.com/pubsub/docs/publisher. Structured like:
      projects/{project_number}/locations/{location}/topics/{topic_id}. Must
      be within the same project and location as the subscription. The topic
      may be changed or removed.
    desiredState: The desired state of this export. Setting this to values
      other than `ACTIVE` and `PAUSED` will result in an error.
    pubsubConfig: Messages are automatically written from the Pub/Sub Lite
      topic associated with this subscription to a Pub/Sub topic.
  """

  class CurrentStateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the export, which may be different
    to the desired state due to errors. This field is output only.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      ACTIVE: Messages are being exported.
      PAUSED: Exporting messages is suspended.
      PERMISSION_DENIED: Messages cannot be exported due to permission denied
        errors. Output only.
      NOT_FOUND: Messages cannot be exported due to missing resources. Output
        only.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    PAUSED = 2
    PERMISSION_DENIED = 3
    NOT_FOUND = 4

  class DesiredStateValueValuesEnum(_messages.Enum):
    r"""The desired state of this export. Setting this to values other than
    `ACTIVE` and `PAUSED` will result in an error.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      ACTIVE: Messages are being exported.
      PAUSED: Exporting messages is suspended.
      PERMISSION_DENIED: Messages cannot be exported due to permission denied
        errors. Output only.
      NOT_FOUND: Messages cannot be exported due to missing resources. Output
        only.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    PAUSED = 2
    PERMISSION_DENIED = 3
    NOT_FOUND = 4

  currentState = _messages.EnumField('CurrentStateValueValuesEnum', 1)
  deadLetterTopic = _messages.StringField(2)
  desiredState = _messages.EnumField('DesiredStateValueValuesEnum', 3)
  pubsubConfig = _messages.MessageField('PubSubConfig', 4)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListPartitionCursorsResponse(_messages.Message):
  r"""Response for ListPartitionCursors

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    partitionCursors: The partition cursors from this request.
  """

  nextPageToken = _messages.StringField(1)
  partitionCursors = _messages.MessageField('PartitionCursor', 2, repeated=True)


class ListReservationTopicsResponse(_messages.Message):
  r"""Response for ListReservationTopics.

  Fields:
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page of results. If this field is omitted, there are no more
      results.
    topics: The names of topics attached to the reservation. The order of the
      topics is unspecified.
  """

  nextPageToken = _messages.StringField(1)
  topics = _messages.StringField(2, repeated=True)


class ListReservationsResponse(_messages.Message):
  r"""Response for ListReservations.

  Fields:
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page of results. If this field is omitted, there are no more
      results.
    reservations: The list of reservation in the requested parent. The order
      of the reservations is unspecified.
  """

  nextPageToken = _messages.StringField(1)
  reservations = _messages.MessageField('Reservation', 2, repeated=True)


class ListSubscriptionsResponse(_messages.Message):
  r"""Response for ListSubscriptions.

  Fields:
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page of results. If this field is omitted, there are no more
      results.
    subscriptions: The list of subscriptions in the requested parent. The
      order of the subscriptions is unspecified.
  """

  nextPageToken = _messages.StringField(1)
  subscriptions = _messages.MessageField('Subscription', 2, repeated=True)


class ListTopicSubscriptionsResponse(_messages.Message):
  r"""Response for ListTopicSubscriptions.

  Fields:
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page of results. If this field is omitted, there are no more
      results.
    subscriptions: The names of subscriptions attached to the topic. The order
      of the subscriptions is unspecified.
  """

  nextPageToken = _messages.StringField(1)
  subscriptions = _messages.StringField(2, repeated=True)


class ListTopicsResponse(_messages.Message):
  r"""Response for ListTopics.

  Fields:
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page of results. If this field is omitted, there are no more
      results.
    topics: The list of topic in the requested parent. The order of the topics
      is unspecified.
  """

  nextPageToken = _messages.StringField(1)
  topics = _messages.MessageField('Topic', 2, repeated=True)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Metadata for long running operations.

  Fields:
    createTime: The time the operation was created.
    endTime: The time the operation finished running. Not set if the operation
      has not completed.
    target: Resource path for the target of the operation. For example,
      targets of seeks are subscription resources, structured like: projects/{
      project_number}/locations/{location}/subscriptions/{subscription_id}
    verb: Name of the verb executed by the operation.
  """

  createTime = _messages.StringField(1)
  endTime = _messages.StringField(2)
  target = _messages.StringField(3)
  verb = _messages.StringField(4)


class PartitionConfig(_messages.Message):
  r"""The settings for a topic's partitions.

  Fields:
    capacity: The capacity configuration.
    count: The number of partitions in the topic. Must be at least 1. Once a
      topic has been created the number of partitions can be increased but not
      decreased. Message ordering is not guaranteed across a topic resize. For
      more information see
      https://cloud.google.com/pubsub/lite/docs/topics#scaling_capacity
    scale: DEPRECATED: Use capacity instead which can express a superset of
      configurations. Every partition in the topic is allocated throughput
      equivalent to `scale` times the standard partition throughput (4 MiB/s).
      This is also reflected in the cost of this topic; a topic with `scale`
      of 2 and count of 10 is charged for 20 partitions. This value must be in
      the range [1,4].
  """

  capacity = _messages.MessageField('Capacity', 1)
  count = _messages.IntegerField(2)
  scale = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class PartitionCursor(_messages.Message):
  r"""A pair of a Cursor and the partition it is for.

  Fields:
    cursor: The value of the cursor.
    partition: The partition this is for.
  """

  cursor = _messages.MessageField('Cursor', 1)
  partition = _messages.IntegerField(2)


class PubSubConfig(_messages.Message):
  r"""Configuration for exporting to a Pub/Sub topic.

  Fields:
    topic: The name of the Pub/Sub topic. Structured like:
      projects/{project_number}/topics/{topic_id}. The topic may be changed.
  """

  topic = _messages.StringField(1)


class PubsubliteAdminProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A PubsubliteAdminProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class PubsubliteAdminProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A PubsubliteAdminProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class PubsubliteAdminProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A PubsubliteAdminProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class PubsubliteAdminProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A PubsubliteAdminProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class PubsubliteAdminProjectsLocationsReservationsCreateRequest(_messages.Message):
  r"""A PubsubliteAdminProjectsLocationsReservationsCreateRequest object.

  Fields:
    parent: Required. The parent location in which to create the reservation.
      Structured like `projects/{project_number}/locations/{location}`.
    reservation: A Reservation resource to be passed as the request body.
    reservationId: Required. The ID to use for the reservation, which will
      become the final component of the reservation's name. This value is
      structured like: `my-reservation-name`.
  """

  parent = _messages.StringField(1, required=True)
  reservation = _messages.MessageField('Reservation', 2)
  reservationId = _messages.StringField(3)


class PubsubliteAdminProjectsLocationsReservationsDeleteRequest(_messages.Message):
  r"""A PubsubliteAdminProjectsLocationsReservationsDeleteRequest object.

  Fields:
    name: Required. The name of the reservation to delete. Structured like: pr
      ojects/{project_number}/locations/{location}/reservations/{reservation_i
      d}
  """

  name = _messages.StringField(1, required=True)


class PubsubliteAdminProjectsLocationsReservationsGetRequest(_messages.Message):
  r"""A PubsubliteAdminProjectsLocationsReservationsGetRequest object.

  Fields:
    name: Required. The name of the reservation whose configuration to return.
      Structured like: projects/{project_number}/locations/{location}/reservat
      ions/{reservation_id}
  """

  name = _messages.StringField(1, required=True)


class PubsubliteAdminProjectsLocationsReservationsListRequest(_messages.Message):
  r"""A PubsubliteAdminProjectsLocationsReservationsListRequest object.

  Fields:
    pageSize: The maximum number of reservations to return. The service may
      return fewer than this value. If unset or zero, all reservations for the
      parent will be returned.
    pageToken: A page token, received from a previous `ListReservations` call.
      Provide this to retrieve the subsequent page. When paginating, all other
      parameters provided to `ListReservations` must match the call that
      provided the page token.
    parent: Required. The parent whose reservations are to be listed.
      Structured like `projects/{project_number}/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class PubsubliteAdminProjectsLocationsReservationsPatchRequest(_messages.Message):
  r"""A PubsubliteAdminProjectsLocationsReservationsPatchRequest object.

  Fields:
    name: The name of the reservation. Structured like: projects/{project_numb
      er}/locations/{location}/reservations/{reservation_id}
    reservation: A Reservation resource to be passed as the request body.
    updateMask: Required. A mask specifying the reservation fields to change.
  """

  name = _messages.StringField(1, required=True)
  reservation = _messages.MessageField('Reservation', 2)
  updateMask = _messages.StringField(3)


class PubsubliteAdminProjectsLocationsReservationsTopicsListRequest(_messages.Message):
  r"""A PubsubliteAdminProjectsLocationsReservationsTopicsListRequest object.

  Fields:
    name: Required. The name of the reservation whose topics to list.
      Structured like: projects/{project_number}/locations/{location}/reservat
      ions/{reservation_id}
    pageSize: The maximum number of topics to return. The service may return
      fewer than this value. If unset or zero, all topics for the given
      reservation will be returned.
    pageToken: A page token, received from a previous `ListReservationTopics`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListReservationTopics` must match the call
      that provided the page token.
  """

  name = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class PubsubliteAdminProjectsLocationsSubscriptionsCreateRequest(_messages.Message):
  r"""A PubsubliteAdminProjectsLocationsSubscriptionsCreateRequest object.

  Fields:
    parent: Required. The parent location in which to create the subscription.
      Structured like `projects/{project_number}/locations/{location}`.
    skipBacklog: If true, the newly created subscription will only receive
      messages published after the subscription was created. Otherwise, the
      entire message backlog will be received on the subscription. Defaults to
      false.
    subscription: A Subscription resource to be passed as the request body.
    subscriptionId: Required. The ID to use for the subscription, which will
      become the final component of the subscription's name. This value is
      structured like: `my-sub-name`.
  """

  parent = _messages.StringField(1, required=True)
  skipBacklog = _messages.BooleanField(2)
  subscription = _messages.MessageField('Subscription', 3)
  subscriptionId = _messages.StringField(4)


class PubsubliteAdminProjectsLocationsSubscriptionsDeleteRequest(_messages.Message):
  r"""A PubsubliteAdminProjectsLocationsSubscriptionsDeleteRequest object.

  Fields:
    name: Required. The name of the subscription to delete.
  """

  name = _messages.StringField(1, required=True)


class PubsubliteAdminProjectsLocationsSubscriptionsGetRequest(_messages.Message):
  r"""A PubsubliteAdminProjectsLocationsSubscriptionsGetRequest object.

  Fields:
    name: Required. The name of the subscription whose configuration to
      return.
  """

  name = _messages.StringField(1, required=True)


class PubsubliteAdminProjectsLocationsSubscriptionsListRequest(_messages.Message):
  r"""A PubsubliteAdminProjectsLocationsSubscriptionsListRequest object.

  Fields:
    pageSize: The maximum number of subscriptions to return. The service may
      return fewer than this value. If unset or zero, all subscriptions for
      the parent will be returned.
    pageToken: A page token, received from a previous `ListSubscriptions`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListSubscriptions` must match the call
      that provided the page token.
    parent: Required. The parent whose subscriptions are to be listed.
      Structured like `projects/{project_number}/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class PubsubliteAdminProjectsLocationsSubscriptionsPatchRequest(_messages.Message):
  r"""A PubsubliteAdminProjectsLocationsSubscriptionsPatchRequest object.

  Fields:
    name: The name of the subscription. Structured like: projects/{project_num
      ber}/locations/{location}/subscriptions/{subscription_id}
    subscription: A Subscription resource to be passed as the request body.
    updateMask: Required. A mask specifying the subscription fields to change.
  """

  name = _messages.StringField(1, required=True)
  subscription = _messages.MessageField('Subscription', 2)
  updateMask = _messages.StringField(3)


class PubsubliteAdminProjectsLocationsSubscriptionsSeekRequest(_messages.Message):
  r"""A PubsubliteAdminProjectsLocationsSubscriptionsSeekRequest object.

  Fields:
    name: Required. The name of the subscription to seek.
    seekSubscriptionRequest: A SeekSubscriptionRequest resource to be passed
      as the request body.
  """

  name = _messages.StringField(1, required=True)
  seekSubscriptionRequest = _messages.MessageField('SeekSubscriptionRequest', 2)


class PubsubliteAdminProjectsLocationsTopicsCreateRequest(_messages.Message):
  r"""A PubsubliteAdminProjectsLocationsTopicsCreateRequest object.

  Fields:
    parent: Required. The parent location in which to create the topic.
      Structured like `projects/{project_number}/locations/{location}`.
    topic: A Topic resource to be passed as the request body.
    topicId: Required. The ID to use for the topic, which will become the
      final component of the topic's name. This value is structured like: `my-
      topic-name`.
  """

  parent = _messages.StringField(1, required=True)
  topic = _messages.MessageField('Topic', 2)
  topicId = _messages.StringField(3)


class PubsubliteAdminProjectsLocationsTopicsDeleteRequest(_messages.Message):
  r"""A PubsubliteAdminProjectsLocationsTopicsDeleteRequest object.

  Fields:
    name: Required. The name of the topic to delete.
  """

  name = _messages.StringField(1, required=True)


class PubsubliteAdminProjectsLocationsTopicsGetPartitionsRequest(_messages.Message):
  r"""A PubsubliteAdminProjectsLocationsTopicsGetPartitionsRequest object.

  Fields:
    name: Required. The topic whose partition information to return.
  """

  name = _messages.StringField(1, required=True)


class PubsubliteAdminProjectsLocationsTopicsGetRequest(_messages.Message):
  r"""A PubsubliteAdminProjectsLocationsTopicsGetRequest object.

  Fields:
    name: Required. The name of the topic whose configuration to return.
  """

  name = _messages.StringField(1, required=True)


class PubsubliteAdminProjectsLocationsTopicsListRequest(_messages.Message):
  r"""A PubsubliteAdminProjectsLocationsTopicsListRequest object.

  Fields:
    pageSize: The maximum number of topics to return. The service may return
      fewer than this value. If unset or zero, all topics for the parent will
      be returned.
    pageToken: A page token, received from a previous `ListTopics` call.
      Provide this to retrieve the subsequent page. When paginating, all other
      parameters provided to `ListTopics` must match the call that provided
      the page token.
    parent: Required. The parent whose topics are to be listed. Structured
      like `projects/{project_number}/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class PubsubliteAdminProjectsLocationsTopicsPatchRequest(_messages.Message):
  r"""A PubsubliteAdminProjectsLocationsTopicsPatchRequest object.

  Fields:
    name: The name of the topic. Structured like:
      projects/{project_number}/locations/{location}/topics/{topic_id}
    topic: A Topic resource to be passed as the request body.
    updateMask: Required. A mask specifying the topic fields to change.
  """

  name = _messages.StringField(1, required=True)
  topic = _messages.MessageField('Topic', 2)
  updateMask = _messages.StringField(3)


class PubsubliteAdminProjectsLocationsTopicsSubscriptionsListRequest(_messages.Message):
  r"""A PubsubliteAdminProjectsLocationsTopicsSubscriptionsListRequest object.

  Fields:
    name: Required. The name of the topic whose subscriptions to list.
    pageSize: The maximum number of subscriptions to return. The service may
      return fewer than this value. If unset or zero, all subscriptions for
      the given topic will be returned.
    pageToken: A page token, received from a previous `ListTopicSubscriptions`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListTopicSubscriptions` must match the
      call that provided the page token.
  """

  name = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class PubsubliteCursorProjectsLocationsSubscriptionsCommitCursorRequest(_messages.Message):
  r"""A PubsubliteCursorProjectsLocationsSubscriptionsCommitCursorRequest
  object.

  Fields:
    commitCursorRequest: A CommitCursorRequest resource to be passed as the
      request body.
    subscription: The subscription for which to update the cursor.
  """

  commitCursorRequest = _messages.MessageField('CommitCursorRequest', 1)
  subscription = _messages.StringField(2, required=True)


class PubsubliteCursorProjectsLocationsSubscriptionsCursorsListRequest(_messages.Message):
  r"""A PubsubliteCursorProjectsLocationsSubscriptionsCursorsListRequest
  object.

  Fields:
    pageSize: The maximum number of cursors to return. The service may return
      fewer than this value. If unset or zero, all cursors for the parent will
      be returned.
    pageToken: A page token, received from a previous `ListPartitionCursors`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListPartitionCursors` must match the call
      that provided the page token.
    parent: Required. The subscription for which to retrieve cursors.
      Structured like `projects/{project_number}/locations/{location}/subscrip
      tions/{subscription_id}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class PubsubliteTopicStatsProjectsLocationsTopicsComputeHeadCursorRequest(_messages.Message):
  r"""A PubsubliteTopicStatsProjectsLocationsTopicsComputeHeadCursorRequest
  object.

  Fields:
    computeHeadCursorRequest: A ComputeHeadCursorRequest resource to be passed
      as the request body.
    topic: Required. The topic for which we should compute the head cursor.
  """

  computeHeadCursorRequest = _messages.MessageField('ComputeHeadCursorRequest', 1)
  topic = _messages.StringField(2, required=True)


class PubsubliteTopicStatsProjectsLocationsTopicsComputeMessageStatsRequest(_messages.Message):
  r"""A PubsubliteTopicStatsProjectsLocationsTopicsComputeMessageStatsRequest
  object.

  Fields:
    computeMessageStatsRequest: A ComputeMessageStatsRequest resource to be
      passed as the request body.
    topic: Required. The topic for which we should compute message stats.
  """

  computeMessageStatsRequest = _messages.MessageField('ComputeMessageStatsRequest', 1)
  topic = _messages.StringField(2, required=True)


class PubsubliteTopicStatsProjectsLocationsTopicsComputeTimeCursorRequest(_messages.Message):
  r"""A PubsubliteTopicStatsProjectsLocationsTopicsComputeTimeCursorRequest
  object.

  Fields:
    computeTimeCursorRequest: A ComputeTimeCursorRequest resource to be passed
      as the request body.
    topic: Required. The topic for which we should compute the cursor.
  """

  computeTimeCursorRequest = _messages.MessageField('ComputeTimeCursorRequest', 1)
  topic = _messages.StringField(2, required=True)


class Reservation(_messages.Message):
  r"""Metadata about a reservation resource.

  Fields:
    name: The name of the reservation. Structured like: projects/{project_numb
      er}/locations/{location}/reservations/{reservation_id}
    throughputCapacity: The reserved throughput capacity. Every unit of
      throughput capacity is equivalent to 1 MiB/s of published messages or 2
      MiB/s of subscribed messages. Any topics which are declared as using
      capacity from a Reservation will consume resources from this reservation
      instead of being charged individually.
  """

  name = _messages.StringField(1)
  throughputCapacity = _messages.IntegerField(2)


class ReservationConfig(_messages.Message):
  r"""The settings for this topic's Reservation usage.

  Fields:
    throughputReservation: The Reservation to use for this topic's throughput
      capacity. Structured like: projects/{project_number}/locations/{location
      }/reservations/{reservation_id}
  """

  throughputReservation = _messages.StringField(1)


class RetentionConfig(_messages.Message):
  r"""The settings for a topic's message retention.

  Fields:
    perPartitionBytes: The provisioned storage, in bytes, per partition. If
      the number of bytes stored in any of the topic's partitions grows beyond
      this value, older messages will be dropped to make room for newer ones,
      regardless of the value of `period`.
    period: How long a published message is retained. If unset, messages will
      be retained as long as the bytes retained for each partition is below
      `per_partition_bytes`.
  """

  perPartitionBytes = _messages.IntegerField(1)
  period = _messages.StringField(2)


class SeekSubscriptionRequest(_messages.Message):
  r"""Request for SeekSubscription.

  Enums:
    NamedTargetValueValuesEnum: Seek to a named position with respect to the
      message backlog.

  Fields:
    namedTarget: Seek to a named position with respect to the message backlog.
    timeTarget: Seek to the first message whose publish or event time is
      greater than or equal to the specified query time. If no such message
      can be located, will seek to the end of the message backlog.
  """

  class NamedTargetValueValuesEnum(_messages.Enum):
    r"""Seek to a named position with respect to the message backlog.

    Values:
      NAMED_TARGET_UNSPECIFIED: Unspecified named target. Do not use.
      TAIL: Seek to the oldest retained message.
      HEAD: Seek past all recently published messages, skipping the entire
        message backlog.
    """
    NAMED_TARGET_UNSPECIFIED = 0
    TAIL = 1
    HEAD = 2

  namedTarget = _messages.EnumField('NamedTargetValueValuesEnum', 1)
  timeTarget = _messages.MessageField('TimeTarget', 2)


class SeekSubscriptionResponse(_messages.Message):
  r"""Response for SeekSubscription long running operation."""


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class Subscription(_messages.Message):
  r"""Metadata about a subscription resource.

  Fields:
    deliveryConfig: The settings for this subscription's message delivery.
    exportConfig: If present, messages are automatically written from the
      Pub/Sub Lite topic associated with this subscription to a destination.
    name: The name of the subscription. Structured like: projects/{project_num
      ber}/locations/{location}/subscriptions/{subscription_id}
    topic: The name of the topic this subscription is attached to. Structured
      like: projects/{project_number}/locations/{location}/topics/{topic_id}
  """

  deliveryConfig = _messages.MessageField('DeliveryConfig', 1)
  exportConfig = _messages.MessageField('ExportConfig', 2)
  name = _messages.StringField(3)
  topic = _messages.StringField(4)


class TimeTarget(_messages.Message):
  r"""A target publish or event time. Can be used for seeking to or retrieving
  the corresponding cursor.

  Fields:
    eventTime: Request the cursor of the first message with event time greater
      than or equal to `event_time`. If messages are missing an event time,
      the publish time is used as a fallback. As event times are user
      supplied, subsequent messages may have event times less than
      `event_time` and should be filtered by the client, if necessary.
    publishTime: Request the cursor of the first message with publish time
      greater than or equal to `publish_time`. All messages thereafter are
      guaranteed to have publish times >= `publish_time`.
  """

  eventTime = _messages.StringField(1)
  publishTime = _messages.StringField(2)


class Topic(_messages.Message):
  r"""Metadata about a topic resource.

  Fields:
    name: The name of the topic. Structured like:
      projects/{project_number}/locations/{location}/topics/{topic_id}
    partitionConfig: The settings for this topic's partitions.
    reservationConfig: The settings for this topic's Reservation usage.
    retentionConfig: The settings for this topic's message retention.
  """

  name = _messages.StringField(1)
  partitionConfig = _messages.MessageField('PartitionConfig', 2)
  reservationConfig = _messages.MessageField('ReservationConfig', 3)
  retentionConfig = _messages.MessageField('RetentionConfig', 4)


class TopicPartitions(_messages.Message):
  r"""Response for GetTopicPartitions.

  Fields:
    partitionCount: The number of partitions in the topic.
  """

  partitionCount = _messages.IntegerField(1)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
