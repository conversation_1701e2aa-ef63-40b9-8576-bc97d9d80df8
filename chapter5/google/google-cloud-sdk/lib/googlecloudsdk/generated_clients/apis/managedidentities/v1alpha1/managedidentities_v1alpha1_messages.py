"""Generated message classes for managedidentities version v1alpha1.

The Managed Service for Microsoft Active Directory API is used for managing a
highly available, hardened service running Microsoft Active Directory (AD).
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'managedidentities'


class AttachTrustRequest(_messages.Message):
  r"""A AttachTrustRequest object.

  Fields:
    trust: The domain trust resource.
  """

  trust = _messages.MessageField('Trust', 1)


class Backup(_messages.Message):
  r"""Represents a Managed Microsoft Identities backup.

  Enums:
    StateValueValuesEnum: Output only. The current state of the backup.
    TypeValueValuesEnum: Output only. Indicates whether it's an on-demand
      backup or scheduled.

  Messages:
    LabelsValue: Optional. Resource labels to represent user provided
      metadata.

  Fields:
    createTime: Output only. The time the backups was created.
    description: Optional. A short description of the backup.
    labels: Optional. Resource labels to represent user provided metadata.
    name: Output only. The unique name of the Backup in the form of projects/{
      project_id}/locations/global/domains/{domain_name}/backups/{name}
    state: Output only. The current state of the backup.
    statusMessage: Output only. Additional information about the current
      status of this backup, if available.
    type: Output only. Indicates whether it's an on-demand backup or
      scheduled.
    updateTime: Output only. Last update time.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the backup.

    Values:
      STATE_UNSPECIFIED: Not set.
      CREATING: Backup is being created.
      ACTIVE: Backup has been created and validated.
      FAILED: Backup has been created but failed validation.
      DELETING: Backup is being deleted.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    FAILED = 3
    DELETING = 4

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. Indicates whether it's an on-demand backup or scheduled.

    Values:
      TYPE_UNSPECIFIED: Backup type not specified.
      ON_DEMAND: Backup was manually created.
      SCHEDULED: Backup was automatically created.
      SCHEMA_EXTENSION: Backup was taken as part of Schema Extension request.
    """
    TYPE_UNSPECIFIED = 0
    ON_DEMAND = 1
    SCHEDULED = 2
    SCHEMA_EXTENSION = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels to represent user provided metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)
  statusMessage = _messages.StringField(6)
  type = _messages.EnumField('TypeValueValuesEnum', 7)
  updateTime = _messages.StringField(8)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class Certificate(_messages.Message):
  r"""Certificate used to configure LDAPS.

  Fields:
    expireTime: The certificate expire time.
    issuingCertificate: The issuer of this certificate.
    subject: The certificate subject.
    subjectAlternativeName: The additional hostnames for the domain.
    thumbprint: The certificate thumbprint which uniquely identifies the
      certificate.
  """

  expireTime = _messages.StringField(1)
  issuingCertificate = _messages.MessageField('Certificate', 2)
  subject = _messages.StringField(3)
  subjectAlternativeName = _messages.StringField(4, repeated=True)
  thumbprint = _messages.StringField(5)


class CheckMigrationPermissionRequest(_messages.Message):
  r"""CheckMigrationPermissionRequest is the request message for
  CheckMigrationPermission method.
  """



class CheckMigrationPermissionResponse(_messages.Message):
  r"""CheckMigrationPermissionResponse is the response message for
  CheckMigrationPermission method.

  Enums:
    StateValueValuesEnum: The state of DomainMigration.

  Fields:
    onpremDomains: The state of SID filtering of all the domains which has
      trust established.
    state: The state of DomainMigration.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of DomainMigration.

    Values:
      STATE_UNSPECIFIED: DomainMigration is in unspecified state.
      DISABLED: Domain Migration is Disabled.
      ENABLED: Domain Migration is Enabled.
      NEEDS_MAINTENANCE: Domain Migration is not in valid state.
    """
    STATE_UNSPECIFIED = 0
    DISABLED = 1
    ENABLED = 2
    NEEDS_MAINTENANCE = 3

  onpremDomains = _messages.MessageField('OnPremDomainSIDDetails', 1, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class DailyCycle(_messages.Message):
  r"""Time window specified for daily operations.

  Fields:
    duration: Output only. Duration of the time window, set by service
      producer.
    startTime: Time within the day to start the operations.
  """

  duration = _messages.StringField(1)
  startTime = _messages.MessageField('TimeOfDay', 2)


class Date(_messages.Message):
  r"""Represents a whole or partial calendar date, such as a birthday. The
  time of day and time zone are either specified elsewhere or are
  insignificant. The date is relative to the Gregorian Calendar. This can
  represent one of the following: * A full date, with non-zero year, month,
  and day values. * A month and day, with a zero year (for example, an
  anniversary). * A year on its own, with a zero month and a zero day. * A
  year and month, with a zero day (for example, a credit card expiration
  date). Related types: * google.type.TimeOfDay * google.type.DateTime *
  google.protobuf.Timestamp

  Fields:
    day: Day of a month. Must be from 1 to 31 and valid for the year and
      month, or 0 to specify a year by itself or a year and month where the
      day isn't significant.
    month: Month of a year. Must be from 1 to 12, or 0 to specify a year
      without a month and day.
    year: Year of the date. Must be from 1 to 9999, or 0 to specify a date
      without a year.
  """

  day = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  month = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  year = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class DenyMaintenancePeriod(_messages.Message):
  r"""DenyMaintenancePeriod definition. Maintenance is forbidden within the
  deny period. The start_date must be less than the end_date.

  Fields:
    endDate: Deny period end date. This can be: * A full date, with non-zero
      year, month and day values. * A month and day value, with a zero year.
      Allows recurring deny periods each year. Date matching this period will
      have to be before the end.
    startDate: Deny period start date. This can be: * A full date, with non-
      zero year, month and day values. * A month and day value, with a zero
      year. Allows recurring deny periods each year. Date matching this period
      will have to be the same or after the start.
    time: Time in UTC when the Blackout period starts on start_date and ends
      on end_date. This can be: * Full time. * All zeros for 00:00:00 UTC
  """

  endDate = _messages.MessageField('Date', 1)
  startDate = _messages.MessageField('Date', 2)
  time = _messages.MessageField('TimeOfDay', 3)


class DetachTrustRequest(_messages.Message):
  r"""A DetachTrustRequest object.

  Fields:
    trust: The domain trust resource to removed.
  """

  trust = _messages.MessageField('Trust', 1)


class DisableMigrationRequest(_messages.Message):
  r"""DisableMigrationRequest is the request message for DisableMigration
  method.
  """



class Domain(_messages.Message):
  r"""If the domain is being changed, it will be placed into the UPDATING
  state, which indicates that the resource is being reconciled. At this point,
  Get will reflect an intermediate state.

  Enums:
    StateValueValuesEnum: Output only. The current state of this domain.

  Messages:
    LabelsValue: Optional. Resource labels to represent user provided metadata

  Fields:
    auditLogsEnabled: Optional. Configuration for audit logs. True if audit
      logs are enabled, else false. Default is audit logs disabled.
    authorizedNetworks: Optional. The full names of the Google Compute Engine
      [networks](/compute/docs/networks-and-firewalls#networks) to which the
      instance is connected. Network can be added using UpdateDomain later.
      Domain is only available on network part of authorized_networks. Caller
      needs to make sure that CIDR subnets do not overlap between networks,
      else domain creation will fail.
    createTime: Output only. The time the instance was created. Synthetic
      field is populated automatically by CCFE. go/ccfe-synthetic-field-user-
      guide
    fqdn: Output only. Fully-qualified domain name of the exposed domain used
      by clients to connect to the service. Similar to what would be chosen
      for an Active Directory that is set up on an internal network.
    labels: Optional. Resource labels to represent user provided metadata
    locations: Required. Locations where domain needs to be provisioned.
      regions e.g. us-west1 or us-east4 Service supports up to 4 locations at
      once. Each location will use a /26 block.
    managedIdentitiesAdminName: Optional. Name of customer-visible admin used
      to perform Active Directory operations. If not specified `setupadmin`
      would be used.
    name: Output only. Unique name of the domain in this scope including
      projects and location using the form:
      `projects/{project_id}/locations/global/domains/{domain_name}`.
    reservedIpRange: Required. The CIDR range of internal addresses that are
      reserved for this domain. Reserved networks must be /24 or larger.
      Ranges must be unique and non-overlapping with existing subnets in
      [Domain].[authorized_networks].
    state: Output only. The current state of this domain.
    statusMessage: Output only. Additional information about the current
      status of this domain, if available.
    trusts: Output only. The current trusts associated with the domain.
    updateTime: Output only. Last update time. Synthetic field is populated
      automatically by CCFE.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of this domain.

    Values:
      STATE_UNSPECIFIED: Not set.
      CREATING: ManagedIdentities domain is being created.
      READY: ManagedIdentities domain has been created and is fully usable.
      UPDATING: ManagedIdentities domain configuration is being updated.
      DELETING: ManagedIdentities domain is being deleted.
      REPAIRING: ManagedIdentities domain is being repaired and may be
        unusable. Details can be found in the `status_message` field.
      PERFORMING_MAINTENANCE: ManagedIdentities domain is undergoing
        maintenance.
      DOWN: ManagedIdentities domain is not serving customer requests.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    UPDATING = 3
    DELETING = 4
    REPAIRING = 5
    PERFORMING_MAINTENANCE = 6
    DOWN = 7

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels to represent user provided metadata

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  auditLogsEnabled = _messages.BooleanField(1)
  authorizedNetworks = _messages.StringField(2, repeated=True)
  createTime = _messages.StringField(3)
  fqdn = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  locations = _messages.StringField(6, repeated=True)
  managedIdentitiesAdminName = _messages.StringField(7)
  name = _messages.StringField(8)
  reservedIpRange = _messages.StringField(9)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  statusMessage = _messages.StringField(11)
  trusts = _messages.MessageField('Trust', 12, repeated=True)
  updateTime = _messages.StringField(13)


class DomainJoinMachineRequest(_messages.Message):
  r"""DomainJoinMachineRequest is the request message for DomainJoinMachine
  method

  Fields:
    force: Optional. force if True, forces domain join even if the computer
      account already exists.
    ouName: Optional. OU name where the VM needs to be domain joined
    vmIdToken: Required. Full instance id token of compute engine VM to verify
      instance identity. More about this:
      https://cloud.google.com/compute/docs/instances/verifying-instance-
      identity#request_signature
  """

  force = _messages.BooleanField(1)
  ouName = _messages.StringField(2)
  vmIdToken = _messages.StringField(3)


class DomainJoinMachineResponse(_messages.Message):
  r"""DomainJoinMachineResponse is the response message for DomainJoinMachine
  method

  Fields:
    domainJoinBlob: Offline domain join blob as the response
  """

  domainJoinBlob = _messages.StringField(1)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EnableMigrationRequest(_messages.Message):
  r"""EnableMigrationRequest is the request message for EnableMigration
  method.

  Fields:
    enableDuration: Optional. Period after which the migration would be auto
      disabled. If unspecified, a default timeout of 48h is used.
    migratingDomains: Required. List of the on-prem domains to be migrated.
  """

  enableDuration = _messages.StringField(1)
  migratingDomains = _messages.MessageField('OnPremDomainDetails', 2, repeated=True)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class ExtendSchemaRequest(_messages.Message):
  r"""ExtendSchemaRequest is the request message for ExtendSchema method.

  Fields:
    description: Required. Description for Schema Change.
    fileContents: File uploaded as a byte stream input.
    gcsPath: File stored in Cloud Storage bucket and represented in the form
      projects/{project_id}/buckets/{bucket_name}/objects/{object_name} File
      should be in the same project as the domain.
  """

  description = _messages.StringField(1)
  fileContents = _messages.BytesField(2)
  gcsPath = _messages.StringField(3)


class GoogleCloudManagedidentitiesV1OpMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  target = _messages.StringField(5)
  verb = _messages.StringField(6)


class GoogleCloudManagedidentitiesV1alpha1OpMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  target = _messages.StringField(5)
  verb = _messages.StringField(6)


class GoogleCloudManagedidentitiesV1beta1OpMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  target = _messages.StringField(5)
  verb = _messages.StringField(6)


class GoogleCloudSaasacceleratorManagementProvidersV1Instance(_messages.Message):
  r"""Instance represents the interface for SLM services to actuate the state
  of control plane resources. Example Instance in JSON, where consumer-
  project-number=123456, producer-project-id=cloud-sql: ```json Instance: {
  "name": "projects/123456/locations/us-east1/instances/prod-instance",
  "create_time": { "seconds": **********, }, "labels": { "env": "prod", "foo":
  "bar" }, "state": READY, "software_versions": { "software_update": "cloud-
  sql-09-28-2018", }, "maintenance_policy_names": { "UpdatePolicy":
  "projects/123456/locations/us-east1/maintenancePolicies/prod-update-policy",
  } "tenant_project_id": "cloud-sql-test-tenant", "producer_metadata": {
  "cloud-sql-tier": "basic", "cloud-sql-instance-size": "1G", },
  "provisioned_resources": [ { "resource-type": "compute-instance", "resource-
  url": "https://www.googleapis.com/compute/v1/projects/cloud-sql/zones/us-
  east1-b/instances/vm-1", } ], "maintenance_schedules": { "csa_rollout": {
  "start_time": { "seconds": **********, }, "end_time": { "seconds":
  1535406431, }, }, "ncsa_rollout": { "start_time": { "seconds": **********,
  }, "end_time": { "seconds": 1535406431, }, } }, "consumer_defined_name":
  "my-sql-instance1", } ``` LINT.IfChange

  Enums:
    StateValueValuesEnum: Output only. Current lifecycle state of the resource
      (e.g. if it's being created or ready to use).

  Messages:
    LabelsValue: Optional. Resource labels to represent user provided
      metadata. Each label is a key-value pair, where both the key and the
      value are arbitrary strings provided by the user.
    MaintenancePolicyNamesValue: Optional. The MaintenancePolicies that have
      been attached to the instance. The key must be of the type name of the
      oneof policy name defined in MaintenancePolicy, and the referenced
      policy must define the same policy type. For details, please refer to
      go/mr-user-guide. Should not be set if
      maintenance_settings.maintenance_policies is set.
    MaintenanceSchedulesValue: The MaintenanceSchedule contains the scheduling
      information of published maintenance schedule with same key as
      software_versions.
    NotificationParametersValue: Optional. notification_parameter are
      information that service producers may like to include that is not
      relevant to Rollout. This parameter will only be passed to Gamma and
      Cloud Logging for notification/logging purpose.
    ProducerMetadataValue: Output only. Custom string attributes used
      primarily to expose producer-specific information in monitoring
      dashboards. See go/get-instance-metadata.
    SoftwareVersionsValue: Software versions that are used to deploy this
      instance. This can be mutated by rollout services.

  Fields:
    consumerDefinedName: consumer_defined_name is the name of the instance set
      by the service consumers. Generally this is different from the `name`
      field which reperesents the system-assigned id of the instance which the
      service consumers do not recognize. This is a required field for tenants
      onboarding to Maintenance Window notifications (go/slm-rollout-
      maintenance-policies#prerequisites).
    consumerProjectNumber: Optional. The consumer_project_number associated
      with this Apigee instance. This field is added specifically to support
      Apigee integration with SLM Rollout and UMM. It represents the numerical
      project ID of the GCP project that consumes this Apigee instance. It is
      used for SLM rollout notifications and UMM integration, enabling proper
      mapping to customer projects and log delivery for Apigee instances. This
      field complements consumer_project_id and may be used for specific
      Apigee scenarios where the numerical ID is required.
    createTime: Output only. Timestamp when the resource was created.
    instanceType: Optional. The instance_type of this instance of format: proj
      ects/{project_number}/locations/{location_id}/instanceTypes/{instance_ty
      pe_id}. Instance Type represents a high-level tier or SKU of the service
      that this instance belong to. When enabled(eg: Maintenance Rollout),
      Rollout uses 'instance_type' along with 'software_versions' to determine
      whether instance needs an update or not.
    labels: Optional. Resource labels to represent user provided metadata.
      Each label is a key-value pair, where both the key and the value are
      arbitrary strings provided by the user.
    maintenancePolicyNames: Optional. The MaintenancePolicies that have been
      attached to the instance. The key must be of the type name of the oneof
      policy name defined in MaintenancePolicy, and the referenced policy must
      define the same policy type. For details, please refer to go/mr-user-
      guide. Should not be set if maintenance_settings.maintenance_policies is
      set.
    maintenanceSchedules: The MaintenanceSchedule contains the scheduling
      information of published maintenance schedule with same key as
      software_versions.
    maintenanceSettings: Optional. The MaintenanceSettings associated with
      instance.
    name: Unique name of the resource. It uses the form: `projects/{project_nu
      mber}/locations/{location_id}/instances/{instance_id}` Note: This name
      is passed, stored and logged across the rollout system. So use of
      consumer project_id or any other consumer PII in the name is strongly
      discouraged for wipeout (go/wipeout) compliance. See
      go/elysium/project_ids#storage-guidance for more details.
    notificationParameters: Optional. notification_parameter are information
      that service producers may like to include that is not relevant to
      Rollout. This parameter will only be passed to Gamma and Cloud Logging
      for notification/logging purpose.
    producerMetadata: Output only. Custom string attributes used primarily to
      expose producer-specific information in monitoring dashboards. See
      go/get-instance-metadata.
    provisionedResources: Output only. The list of data plane resources
      provisioned for this instance, e.g. compute VMs. See go/get-instance-
      metadata.
    slmInstanceTemplate: Link to the SLM instance template. Only populated
      when updating SLM instances via SSA's Actuation service adaptor. Service
      producers with custom control plane (e.g. Cloud SQL) doesn't need to
      populate this field. Instead they should use software_versions.
    sloMetadata: Output only. SLO metadata for instance classification in the
      Standardized dataplane SLO platform. See go/cloud-ssa-standard-slo for
      feature description.
    softwareVersions: Software versions that are used to deploy this instance.
      This can be mutated by rollout services.
    state: Output only. Current lifecycle state of the resource (e.g. if it's
      being created or ready to use).
    tenantProjectId: Output only. ID of the associated GCP tenant project. See
      go/get-instance-metadata.
    updateTime: Output only. Timestamp when the resource was last modified.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current lifecycle state of the resource (e.g. if it's
    being created or ready to use).

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      CREATING: Instance is being created.
      READY: Instance has been created and is ready to use.
      UPDATING: Instance is being updated.
      REPAIRING: Instance is unheathy and under repair.
      DELETING: Instance is being deleted.
      ERROR: Instance encountered an error and is in indeterministic state.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    UPDATING = 3
    REPAIRING = 4
    DELETING = 5
    ERROR = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels to represent user provided metadata. Each
    label is a key-value pair, where both the key and the value are arbitrary
    strings provided by the user.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MaintenancePolicyNamesValue(_messages.Message):
    r"""Optional. The MaintenancePolicies that have been attached to the
    instance. The key must be of the type name of the oneof policy name
    defined in MaintenancePolicy, and the referenced policy must define the
    same policy type. For details, please refer to go/mr-user-guide. Should
    not be set if maintenance_settings.maintenance_policies is set.

    Messages:
      AdditionalProperty: An additional property for a
        MaintenancePolicyNamesValue object.

    Fields:
      additionalProperties: Additional properties of type
        MaintenancePolicyNamesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MaintenancePolicyNamesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MaintenanceSchedulesValue(_messages.Message):
    r"""The MaintenanceSchedule contains the scheduling information of
    published maintenance schedule with same key as software_versions.

    Messages:
      AdditionalProperty: An additional property for a
        MaintenanceSchedulesValue object.

    Fields:
      additionalProperties: Additional properties of type
        MaintenanceSchedulesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MaintenanceSchedulesValue object.

      Fields:
        key: Name of the additional property.
        value: A
          GoogleCloudSaasacceleratorManagementProvidersV1MaintenanceSchedule
          attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1MaintenanceSchedule', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class NotificationParametersValue(_messages.Message):
    r"""Optional. notification_parameter are information that service
    producers may like to include that is not relevant to Rollout. This
    parameter will only be passed to Gamma and Cloud Logging for
    notification/logging purpose.

    Messages:
      AdditionalProperty: An additional property for a
        NotificationParametersValue object.

    Fields:
      additionalProperties: Additional properties of type
        NotificationParametersValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a NotificationParametersValue object.

      Fields:
        key: Name of the additional property.
        value: A
          GoogleCloudSaasacceleratorManagementProvidersV1NotificationParameter
          attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1NotificationParameter', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ProducerMetadataValue(_messages.Message):
    r"""Output only. Custom string attributes used primarily to expose
    producer-specific information in monitoring dashboards. See go/get-
    instance-metadata.

    Messages:
      AdditionalProperty: An additional property for a ProducerMetadataValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        ProducerMetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ProducerMetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SoftwareVersionsValue(_messages.Message):
    r"""Software versions that are used to deploy this instance. This can be
    mutated by rollout services.

    Messages:
      AdditionalProperty: An additional property for a SoftwareVersionsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        SoftwareVersionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SoftwareVersionsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  consumerDefinedName = _messages.StringField(1)
  consumerProjectNumber = _messages.StringField(2)
  createTime = _messages.StringField(3)
  instanceType = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  maintenancePolicyNames = _messages.MessageField('MaintenancePolicyNamesValue', 6)
  maintenanceSchedules = _messages.MessageField('MaintenanceSchedulesValue', 7)
  maintenanceSettings = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1MaintenanceSettings', 8)
  name = _messages.StringField(9)
  notificationParameters = _messages.MessageField('NotificationParametersValue', 10)
  producerMetadata = _messages.MessageField('ProducerMetadataValue', 11)
  provisionedResources = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1ProvisionedResource', 12, repeated=True)
  slmInstanceTemplate = _messages.StringField(13)
  sloMetadata = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1SloMetadata', 14)
  softwareVersions = _messages.MessageField('SoftwareVersionsValue', 15)
  state = _messages.EnumField('StateValueValuesEnum', 16)
  tenantProjectId = _messages.StringField(17)
  updateTime = _messages.StringField(18)


class GoogleCloudSaasacceleratorManagementProvidersV1MaintenanceSchedule(_messages.Message):
  r"""Maintenance schedule which is exposed to customer and potentially end
  user, indicating published upcoming future maintenance schedule

  Fields:
    canReschedule: This field is deprecated, and will be always set to true
      since reschedule can happen multiple times now. This field should not be
      removed until all service producers remove this for their customers.
    endTime: The scheduled end time for the maintenance.
    rolloutManagementPolicy: The rollout management policy this maintenance
      schedule is associated with. When doing reschedule update request, the
      reschedule should be against this given policy.
    scheduleDeadlineTime: schedule_deadline_time is the time deadline any
      schedule start time cannot go beyond, including reschedule. It's
      normally the initial schedule start time plus maintenance window length
      (1 day or 1 week). Maintenance cannot be scheduled to start beyond this
      deadline.
    startTime: The scheduled start time for the maintenance.
  """

  canReschedule = _messages.BooleanField(1)
  endTime = _messages.StringField(2)
  rolloutManagementPolicy = _messages.StringField(3)
  scheduleDeadlineTime = _messages.StringField(4)
  startTime = _messages.StringField(5)


class GoogleCloudSaasacceleratorManagementProvidersV1MaintenanceSettings(_messages.Message):
  r"""Maintenance settings associated with instance. Allows service producers
  and end users to assign settings that controls maintenance on this instance.

  Messages:
    MaintenancePoliciesValue: Optional. The MaintenancePolicies that have been
      attached to the instance. The key must be of the type name of the oneof
      policy name defined in MaintenancePolicy, and the embedded policy must
      define the same policy type. For details, please refer to go/mr-user-
      guide. Should not be set if maintenance_policy_names is set. If only the
      name is needed, then only populate MaintenancePolicy.name.

  Fields:
    exclude: Optional. Exclude instance from maintenance. When true, rollout
      service will not attempt maintenance on the instance. Rollout service
      will include the instance in reported rollout progress as not attempted.
    isRollback: Optional. If the update call is triggered from rollback, set
      the value as true.
    maintenancePolicies: Optional. The MaintenancePolicies that have been
      attached to the instance. The key must be of the type name of the oneof
      policy name defined in MaintenancePolicy, and the embedded policy must
      define the same policy type. For details, please refer to go/mr-user-
      guide. Should not be set if maintenance_policy_names is set. If only the
      name is needed, then only populate MaintenancePolicy.name.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MaintenancePoliciesValue(_messages.Message):
    r"""Optional. The MaintenancePolicies that have been attached to the
    instance. The key must be of the type name of the oneof policy name
    defined in MaintenancePolicy, and the embedded policy must define the same
    policy type. For details, please refer to go/mr-user-guide. Should not be
    set if maintenance_policy_names is set. If only the name is needed, then
    only populate MaintenancePolicy.name.

    Messages:
      AdditionalProperty: An additional property for a
        MaintenancePoliciesValue object.

    Fields:
      additionalProperties: Additional properties of type
        MaintenancePoliciesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MaintenancePoliciesValue object.

      Fields:
        key: Name of the additional property.
        value: A MaintenancePolicy attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('MaintenancePolicy', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  exclude = _messages.BooleanField(1)
  isRollback = _messages.BooleanField(2)
  maintenancePolicies = _messages.MessageField('MaintenancePoliciesValue', 3)


class GoogleCloudSaasacceleratorManagementProvidersV1NodeSloMetadata(_messages.Message):
  r"""Node information for custom per-node SLO implementations. SSA does not
  support per-node SLO, but producers can populate per-node information in
  SloMetadata for custom precomputations. SSA Eligibility Exporter will emit
  per-node metric based on this information.

  Fields:
    location: The location of the node, if different from instance location.
    nodeId: The id of the node. This should be equal to
      SaasInstanceNode.node_id.
    perSliEligibility: If present, this will override eligibility for the node
      coming from instance or exclusions for specified SLIs.
  """

  location = _messages.StringField(1)
  nodeId = _messages.StringField(2)
  perSliEligibility = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1PerSliSloEligibility', 3)


class GoogleCloudSaasacceleratorManagementProvidersV1NotificationParameter(_messages.Message):
  r"""Contains notification related data.

  Fields:
    values: Optional. Array of string values. e.g. instance's replica
      information.
  """

  values = _messages.StringField(1, repeated=True)


class GoogleCloudSaasacceleratorManagementProvidersV1PerSliSloEligibility(_messages.Message):
  r"""PerSliSloEligibility is a mapping from an SLI name to eligibility.

  Messages:
    EligibilitiesValue: An entry in the eligibilities map specifies an
      eligibility for a particular SLI for the given instance. The SLI key in
      the name must be a valid SLI name specified in the Eligibility Exporter
      binary flags otherwise an error will be emitted by Eligibility Exporter
      and the oncaller will be alerted. If an SLI has been defined in the
      binary flags but the eligibilities map does not contain it, the
      corresponding SLI time series will not be emitted by the Eligibility
      Exporter. This ensures a smooth rollout and compatibility between the
      data produced by different versions of the Eligibility Exporters. If
      eligibilities map contains a key for an SLI which has not been declared
      in the binary flags, there will be an error message emitted in the
      Eligibility Exporter log and the metric for the SLI in question will not
      be emitted.

  Fields:
    eligibilities: An entry in the eligibilities map specifies an eligibility
      for a particular SLI for the given instance. The SLI key in the name
      must be a valid SLI name specified in the Eligibility Exporter binary
      flags otherwise an error will be emitted by Eligibility Exporter and the
      oncaller will be alerted. If an SLI has been defined in the binary flags
      but the eligibilities map does not contain it, the corresponding SLI
      time series will not be emitted by the Eligibility Exporter. This
      ensures a smooth rollout and compatibility between the data produced by
      different versions of the Eligibility Exporters. If eligibilities map
      contains a key for an SLI which has not been declared in the binary
      flags, there will be an error message emitted in the Eligibility
      Exporter log and the metric for the SLI in question will not be emitted.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EligibilitiesValue(_messages.Message):
    r"""An entry in the eligibilities map specifies an eligibility for a
    particular SLI for the given instance. The SLI key in the name must be a
    valid SLI name specified in the Eligibility Exporter binary flags
    otherwise an error will be emitted by Eligibility Exporter and the
    oncaller will be alerted. If an SLI has been defined in the binary flags
    but the eligibilities map does not contain it, the corresponding SLI time
    series will not be emitted by the Eligibility Exporter. This ensures a
    smooth rollout and compatibility between the data produced by different
    versions of the Eligibility Exporters. If eligibilities map contains a key
    for an SLI which has not been declared in the binary flags, there will be
    an error message emitted in the Eligibility Exporter log and the metric
    for the SLI in question will not be emitted.

    Messages:
      AdditionalProperty: An additional property for a EligibilitiesValue
        object.

    Fields:
      additionalProperties: Additional properties of type EligibilitiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EligibilitiesValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudSaasacceleratorManagementProvidersV1SloEligibility
          attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1SloEligibility', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  eligibilities = _messages.MessageField('EligibilitiesValue', 1)


class GoogleCloudSaasacceleratorManagementProvidersV1ProvisionedResource(_messages.Message):
  r"""Describes provisioned dataplane resources.

  Fields:
    resourceType: Type of the resource. This can be either a GCP resource or a
      custom one (e.g. another cloud provider's VM). For GCP compute resources
      use singular form of the names listed in GCP compute API documentation
      (https://cloud.google.com/compute/docs/reference/rest/v1/), prefixed
      with 'compute-', for example: 'compute-instance', 'compute-disk',
      'compute-autoscaler'.
    resourceUrl: URL identifying the resource, e.g.
      "https://www.googleapis.com/compute/v1/projects/...)".
  """

  resourceType = _messages.StringField(1)
  resourceUrl = _messages.StringField(2)


class GoogleCloudSaasacceleratorManagementProvidersV1SloEligibility(_messages.Message):
  r"""SloEligibility is a tuple containing eligibility value: true if an
  instance is eligible for SLO calculation or false if it should be excluded
  from all SLO-related calculations along with a user-defined reason.

  Fields:
    eligible: Whether an instance is eligible or ineligible.
    reason: User-defined reason for the current value of instance eligibility.
      Usually, this can be directly mapped to the internal state. An empty
      reason is allowed.
  """

  eligible = _messages.BooleanField(1)
  reason = _messages.StringField(2)


class GoogleCloudSaasacceleratorManagementProvidersV1SloMetadata(_messages.Message):
  r"""SloMetadata contains resources required for proper SLO classification of
  the instance.

  Fields:
    nodes: Optional. List of nodes. Some producers need to use per-node
      metadata to calculate SLO. This field allows such producers to publish
      per-node SLO meta data, which will be consumed by SSA Eligibility
      Exporter and published in the form of per node metric to Monarch.
    perSliEligibility: Optional. Multiple per-instance SLI eligibilities which
      apply for individual SLIs.
    tier: Name of the SLO tier the Instance belongs to. This name will be
      expected to match the tiers specified in the service SLO configuration.
      Field is mandatory and must not be empty.
  """

  nodes = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1NodeSloMetadata', 1, repeated=True)
  perSliEligibility = _messages.MessageField('GoogleCloudSaasacceleratorManagementProvidersV1PerSliSloEligibility', 2)
  tier = _messages.StringField(3)


class LDAPSSettings(_messages.Message):
  r"""LDAPSSettings represents the ldaps settings for domain resource. LDAP is
  the Lightweight Directory Access Protocol, defined in
  https://tools.ietf.org/html/rfc4511. The settings object configures LDAP
  over SSL/TLS, whether it is over port 636 or the StartTLS operation. If
  LDAPSSettings is being changed, it will be placed into the UPDATING state,
  which indicates that the resource is being reconciled. At this point, Get
  will reflect an intermediate state.

  Enums:
    StateValueValuesEnum: Output only. The current state of this LDAPS
      settings.

  Fields:
    certificate: Output only. The certificate used to configure LDAPS.
      Certificates can be chained with a maximum length of 15.
    certificatePassword: Input only. The password used to encrypt the uploaded
      pfx certificate.
    certificatePfx: Input only. The uploaded PKCS12-formatted certificate to
      configure LDAPS with. It will enable the domain controllers in this
      domain to accept LDAPS connections (either LDAP over SSL/TLS or the
      StartTLS operation). A valid certificate chain must form a valid x.509
      certificate chain (or be comprised of a single self-signed certificate.
      It must be encrypted with either: 1) PBES2 + PBKDF2 + AES256 encryption
      and SHA256 PRF; or 2) pbeWithSHA1And3-KeyTripleDES-CBC Private key must
      be included for the leaf / single self-signed certificate. Note: For a
      fqdn your-example-domain.com, the wildcard fqdn is *.your-example-
      domain.com. Specifically the leaf certificate must have: - Either a
      blank subject or a subject with CN matching the wildcard fqdn. - Exactly
      two SANs - the fqdn and wildcard fqdn. - Encipherment and digital key
      signature key usages. - Server authentication extended key usage
      (OID=*******.*******.1) - Private key must be in one of the following
      formats: RSA, ECDSA, ED25519. - Private key must have appropriate key
      length: 2048 for RSA, 256 for ECDSA - Signature algorithm of the leaf
      certificate cannot be MD2, MD5 or SHA1.
    name: The resource name of the LDAPS settings. Uses the form:
      `projects/{project}/locations/{location}/domains/{domain}`.
    state: Output only. The current state of this LDAPS settings.
    updateTime: Output only. Last update time.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of this LDAPS settings.

    Values:
      STATE_UNSPECIFIED: Not Set
      UPDATING: The LDAPS setting is being updated.
      ACTIVE: The LDAPS setting is ready.
      FAILED: The LDAPS setting is not applied correctly.
    """
    STATE_UNSPECIFIED = 0
    UPDATING = 1
    ACTIVE = 2
    FAILED = 3

  certificate = _messages.MessageField('Certificate', 1)
  certificatePassword = _messages.StringField(2)
  certificatePfx = _messages.BytesField(3)
  name = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)
  updateTime = _messages.StringField(6)


class ListBackupsResponse(_messages.Message):
  r"""ListBackupsResponse is the response message for ListBackups method.

  Fields:
    backups: A list of Cloud AD backups in the domain.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: Locations that could not be reached.
  """

  backups = _messages.MessageField('Backup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListDomainsResponse(_messages.Message):
  r"""A ListDomainsResponse object.

  Fields:
    domains: A list of Managed Identities Service domains in the project.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: Locations that could not be reached.
  """

  domains = _messages.MessageField('Domain', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListPeeringsResponse(_messages.Message):
  r"""ListPeeringsResponse is the response message for ListPeerings method.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    peerings: A list of Managed Identities Service Peerings in the project.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  peerings = _messages.MessageField('Peering', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListSQLIntegrationsResponse(_messages.Message):
  r"""ListSQLIntegrationsResponse is the response message for
  ListSQLIntegrations method.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    sqlIntegrations: A list of SQLIntegrations of a domain.
    unreachable: A list of locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  sqlIntegrations = _messages.MessageField('SQLIntegration', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListSchemaExtensionsResponse(_messages.Message):
  r"""ListSchemaExtensionsResponse is the response message for
  ListSchemaExtensions method.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    schemaExtensions: A list of SchemaExtension of a domain.
    unreachable: A list of locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  schemaExtensions = _messages.MessageField('SchemaExtension', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class MaintenancePolicy(_messages.Message):
  r"""Defines policies to service maintenance events.

  Enums:
    StateValueValuesEnum: Optional. The state of the policy.

  Messages:
    LabelsValue: Optional. Resource labels to represent user provided
      metadata. Each label is a key-value pair, where both the key and the
      value are arbitrary strings provided by the user.

  Fields:
    createTime: Output only. The time when the resource was created.
    description: Optional. Description of what this policy is for.
      Create/Update methods return INVALID_ARGUMENT if the length is greater
      than 512.
    labels: Optional. Resource labels to represent user provided metadata.
      Each label is a key-value pair, where both the key and the value are
      arbitrary strings provided by the user.
    name: Required. MaintenancePolicy name using the form: `projects/{project_
      id}/locations/{location_id}/maintenancePolicies/{maintenance_policy_id}`
      where {project_id} refers to a GCP consumer project ID, {location_id}
      refers to a GCP region/zone, {maintenance_policy_id} must be 1-63
      characters long and match the regular expression
      `[a-z0-9]([-a-z0-9]*[a-z0-9])?`.
    state: Optional. The state of the policy.
    updatePolicy: Maintenance policy applicable to instance update.
    updateTime: Output only. The time when the resource was updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Optional. The state of the policy.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      READY: Resource is ready to be used.
      DELETING: Resource is being deleted. It can no longer be attached to
        instances.
    """
    STATE_UNSPECIFIED = 0
    READY = 1
    DELETING = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels to represent user provided metadata. Each
    label is a key-value pair, where both the key and the value are arbitrary
    strings provided by the user.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)
  updatePolicy = _messages.MessageField('UpdatePolicy', 6)
  updateTime = _messages.StringField(7)


class MaintenanceWindow(_messages.Message):
  r"""MaintenanceWindow definition.

  Fields:
    dailyCycle: Daily cycle.
    weeklyCycle: Weekly cycle.
  """

  dailyCycle = _messages.MessageField('DailyCycle', 1)
  weeklyCycle = _messages.MessageField('WeeklyCycle', 2)


class ManagedidentitiesProjectsLocationsGetRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class ManagedidentitiesProjectsLocationsGlobalDomainsAttachTrustRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalDomainsAttachTrustRequest
  object.

  Fields:
    attachTrustRequest: A AttachTrustRequest resource to be passed as the
      request body.
    name: The resource domain name, project name and location using the form:
      `projects/{project_id}/locations/global/domains/{domain_name}`
  """

  attachTrustRequest = _messages.MessageField('AttachTrustRequest', 1)
  name = _messages.StringField(2, required=True)


class ManagedidentitiesProjectsLocationsGlobalDomainsBackupsCreateRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalDomainsBackupsCreateRequest
  object.

  Fields:
    backup: A Backup resource to be passed as the request body.
    backupId: Required. Backup Id, unique name to identify the backups with
      the following restrictions: * Must be lowercase letters, numbers, and
      hyphens * Must start with a letter. * Must contain between 1-63
      characters. * Must end with a number or a letter. * Must be unique
      within the domain.
    parent: Required. The domain resource name using the form:
      `projects/{project_id}/locations/global/domains/{domain_name}`
  """

  backup = _messages.MessageField('Backup', 1)
  backupId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ManagedidentitiesProjectsLocationsGlobalDomainsBackupsDeleteRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalDomainsBackupsDeleteRequest
  object.

  Fields:
    name: Required. The backup resource name using the form: `projects/{projec
      t_id}/locations/global/domains/{domain_name}/backups/{backup_id}`
  """

  name = _messages.StringField(1, required=True)


class ManagedidentitiesProjectsLocationsGlobalDomainsBackupsGetIamPolicyRequest(_messages.Message):
  r"""A
  ManagedidentitiesProjectsLocationsGlobalDomainsBackupsGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class ManagedidentitiesProjectsLocationsGlobalDomainsBackupsGetRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalDomainsBackupsGetRequest
  object.

  Fields:
    name: Required. The backup resource name using the form: `projects/{projec
      t_id}/locations/global/domains/{domain_name}/backups/{backup_id}`
  """

  name = _messages.StringField(1, required=True)


class ManagedidentitiesProjectsLocationsGlobalDomainsBackupsListRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalDomainsBackupsListRequest
  object.

  Fields:
    filter: Optional. Filter specifying constraints of a list operation.
    orderBy: Optional. Specifies the ordering of results following syntax at
      https://cloud.google.com/apis/design/design_patterns#sorting_order.
    pageSize: Optional. The maximum number of items to return. If not
      specified, a default value of 1000 will be used by the service.
      Regardless of the page_size value, the response may include a partial
      list and a caller should only rely on response's next_page_token to
      determine if there are more instances left to be queried.
    pageToken: Optional. The next_page_token value returned from a previous
      List request, if any.
    parent: Required. The domain resource name using the form:
      `projects/{project_id}/locations/global/domains/{domain_name}`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ManagedidentitiesProjectsLocationsGlobalDomainsBackupsPatchRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalDomainsBackupsPatchRequest
  object.

  Fields:
    backup: A Backup resource to be passed as the request body.
    name: Output only. The unique name of the Backup in the form of projects/{
      project_id}/locations/global/domains/{domain_name}/backups/{name}
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field. The elements of the repeated paths field may
      only include these fields from Backup: * `labels`
  """

  backup = _messages.MessageField('Backup', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ManagedidentitiesProjectsLocationsGlobalDomainsBackupsSetIamPolicyRequest(_messages.Message):
  r"""A
  ManagedidentitiesProjectsLocationsGlobalDomainsBackupsSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class ManagedidentitiesProjectsLocationsGlobalDomainsBackupsTestIamPermissionsRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalDomainsBackupsTestIamPermissio
  nsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class ManagedidentitiesProjectsLocationsGlobalDomainsCheckMigrationPermissionRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalDomainsCheckMigrationPermissio
  nRequest object.

  Fields:
    checkMigrationPermissionRequest: A CheckMigrationPermissionRequest
      resource to be passed as the request body.
    domain: Required. The domain resource name using the form:
      `projects/{project_id}/locations/global/domains/{domain_name}`
  """

  checkMigrationPermissionRequest = _messages.MessageField('CheckMigrationPermissionRequest', 1)
  domain = _messages.StringField(2, required=True)


class ManagedidentitiesProjectsLocationsGlobalDomainsCreateRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalDomainsCreateRequest object.

  Fields:
    domain: A Domain resource to be passed as the request body.
    domainName: The fully qualified domain name. e.g.
      mydomain.myorganization.com, with the following restrictions: * Must
      contain only lowercase letters, numbers, periods and hyphens. * Must
      start with a letter. * Must contain between 2-64 characters. * Must end
      with a number or a letter. * Must not start with period. * Must be
      unique within the project. * First segment length (mydomain form example
      above) shouldn't exceed 15 chars. * The last segment cannot be fully
      numeric.
    parent: Resource project name and location using the form:
      `projects/{project_id}/locations/global`
  """

  domain = _messages.MessageField('Domain', 1)
  domainName = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ManagedidentitiesProjectsLocationsGlobalDomainsDeleteRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalDomainsDeleteRequest object.

  Fields:
    name: Domain resource name using the form:
      `projects/{project_id}/locations/global/domains/{domain_name}`
  """

  name = _messages.StringField(1, required=True)


class ManagedidentitiesProjectsLocationsGlobalDomainsDetachTrustRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalDomainsDetachTrustRequest
  object.

  Fields:
    detachTrustRequest: A DetachTrustRequest resource to be passed as the
      request body.
    name: The resource domain name, project name, and location using the form:
      `projects/{project_id}/locations/global/domains/{domain_name}`
  """

  detachTrustRequest = _messages.MessageField('DetachTrustRequest', 1)
  name = _messages.StringField(2, required=True)


class ManagedidentitiesProjectsLocationsGlobalDomainsDisableMigrationRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalDomainsDisableMigrationRequest
  object.

  Fields:
    disableMigrationRequest: A DisableMigrationRequest resource to be passed
      as the request body.
    domain: Required. The domain resource name using the form:
      `projects/{project_id}/locations/global/domains/{domain_name}`
  """

  disableMigrationRequest = _messages.MessageField('DisableMigrationRequest', 1)
  domain = _messages.StringField(2, required=True)


class ManagedidentitiesProjectsLocationsGlobalDomainsDomainJoinMachineRequest(_messages.Message):
  r"""A
  ManagedidentitiesProjectsLocationsGlobalDomainsDomainJoinMachineRequest
  object.

  Fields:
    domain: Required. The domain resource name using the form:
      projects/{project_id}/locations/global/domains/{domain_name}
    domainJoinMachineRequest: A DomainJoinMachineRequest resource to be passed
      as the request body.
  """

  domain = _messages.StringField(1, required=True)
  domainJoinMachineRequest = _messages.MessageField('DomainJoinMachineRequest', 2)


class ManagedidentitiesProjectsLocationsGlobalDomainsEnableMigrationRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalDomainsEnableMigrationRequest
  object.

  Fields:
    domain: Required. The domain resource name using the form:
      `projects/{project_id}/locations/global/domains/{domain_name}`
    enableMigrationRequest: A EnableMigrationRequest resource to be passed as
      the request body.
  """

  domain = _messages.StringField(1, required=True)
  enableMigrationRequest = _messages.MessageField('EnableMigrationRequest', 2)


class ManagedidentitiesProjectsLocationsGlobalDomainsExtendSchemaRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalDomainsExtendSchemaRequest
  object.

  Fields:
    domain: Required. The domain resource name using the form:
      `projects/{project_id}/locations/global/domains/{domain_name}`
    extendSchemaRequest: A ExtendSchemaRequest resource to be passed as the
      request body.
  """

  domain = _messages.StringField(1, required=True)
  extendSchemaRequest = _messages.MessageField('ExtendSchemaRequest', 2)


class ManagedidentitiesProjectsLocationsGlobalDomainsGetIamPolicyRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalDomainsGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class ManagedidentitiesProjectsLocationsGlobalDomainsGetLdapssettingsRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalDomainsGetLdapssettingsRequest
  object.

  Fields:
    name: Required. The domain resource name using the form:
      `projects/{project_id}/locations/global/domains/{domain_name}`
  """

  name = _messages.StringField(1, required=True)


class ManagedidentitiesProjectsLocationsGlobalDomainsGetRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalDomainsGetRequest object.

  Fields:
    name: Domain resource name using the form:
      `projects/{project_id}/locations/global/domains/{domain_name}`
  """

  name = _messages.StringField(1, required=True)


class ManagedidentitiesProjectsLocationsGlobalDomainsListRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalDomainsListRequest object.

  Fields:
    filter: Optional. Filter specifying constraints of a list operation. For
      example, `Domain.fqdn="mydomain.myorginization"`.
    orderBy: Optional. Specifies the ordering of results following syntax at
      https://cloud.google.com/apis/design/design_patterns#sorting_order.
    pageSize: If not specified, a default value of 1000 will be used by the
      service. Regardless of the page_size value, the response may include a
      partial list and a caller should only rely on response's next_page_token
      to determine if there are more instances left to be queried.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: Required. The resource name of the domain location using the form:
      `projects/{project_id}/locations/global`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ManagedidentitiesProjectsLocationsGlobalDomainsPatchRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalDomainsPatchRequest object.

  Fields:
    domain: A Domain resource to be passed as the request body.
    name: Output only. Unique name of the domain in this scope including
      projects and location using the form:
      `projects/{project_id}/locations/global/domains/{domain_name}`.
    updateMask: Mask of fields to update. At least one path must be supplied
      in this field. The elements of the repeated paths field may only include
      these fields from Domain: * `labels` * `locations` *
      `authorized_networks` * `audit_logs_enabled`
  """

  domain = _messages.MessageField('Domain', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ManagedidentitiesProjectsLocationsGlobalDomainsReconfigureTrustRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalDomainsReconfigureTrustRequest
  object.

  Fields:
    name: The resource domain name, project name and location using the form:
      `projects/{project_id}/locations/global/domains/{domain_name}`
    reconfigureTrustRequest: A ReconfigureTrustRequest resource to be passed
      as the request body.
  """

  name = _messages.StringField(1, required=True)
  reconfigureTrustRequest = _messages.MessageField('ReconfigureTrustRequest', 2)


class ManagedidentitiesProjectsLocationsGlobalDomainsResetAdminPasswordRequest(_messages.Message):
  r"""A
  ManagedidentitiesProjectsLocationsGlobalDomainsResetAdminPasswordRequest
  object.

  Fields:
    name: The domain resource name using the form:
      `projects/{project_id}/locations/global/domains/{domain_name}`
    resetAdminPasswordRequest: A ResetAdminPasswordRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  resetAdminPasswordRequest = _messages.MessageField('ResetAdminPasswordRequest', 2)


class ManagedidentitiesProjectsLocationsGlobalDomainsRestoreRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalDomainsRestoreRequest object.

  Fields:
    name: Required. resource name for the domain to which the backup belongs
    restoreDomainRequest: A RestoreDomainRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  restoreDomainRequest = _messages.MessageField('RestoreDomainRequest', 2)


class ManagedidentitiesProjectsLocationsGlobalDomainsSchemaExtensionsCreateRequest(_messages.Message):
  r"""A
  ManagedidentitiesProjectsLocationsGlobalDomainsSchemaExtensionsCreateRequest
  object.

  Fields:
    parent: Required. The domain resource name using the form:
      `projects/{project_id}/locations/global/domains/{domain_name}`
    schemaExtension: A SchemaExtension resource to be passed as the request
      body.
    schemaExtensionId: Required. Unique id of the Schema Extension Request.
      This value should be 4-63 characters, and valid characters are
      /A-Z[0-9]-/.
  """

  parent = _messages.StringField(1, required=True)
  schemaExtension = _messages.MessageField('SchemaExtension', 2)
  schemaExtensionId = _messages.StringField(3)


class ManagedidentitiesProjectsLocationsGlobalDomainsSchemaExtensionsGetRequest(_messages.Message):
  r"""A
  ManagedidentitiesProjectsLocationsGlobalDomainsSchemaExtensionsGetRequest
  object.

  Fields:
    name: Required. Managed AD Schema Extension resource name using the form:
      `projects/{project_id}/locations/global/domains/{domain_name}/schemaExte
      nsions/{schema_extension_id}`
  """

  name = _messages.StringField(1, required=True)


class ManagedidentitiesProjectsLocationsGlobalDomainsSchemaExtensionsListRequest(_messages.Message):
  r"""A
  ManagedidentitiesProjectsLocationsGlobalDomainsSchemaExtensionsListRequest
  object.

  Fields:
    filter: Optional. Filter specifying constraints of a list operation. For
      example, `SchemaExtension.name="projects/proj-
      test/locations/global/domains/test.com/schemaExtensions/s-123"`.
    orderBy: Optional. Specifies the ordering of results following syntax at
      https://cloud.google.com/apis/design/design_patterns#sorting_order.
    pageSize: Optional. The maximum number of items to return. The maximum
      value is 1000; values above 1000 will be coerced to 1000. If not
      specified, a default value of 1000 will be used by the service.
      Regardless of the page_size value, the response may include a partial
      list and a caller should only rely on response. next_page_token to
      determine if there are more instances left to be queried.
    pageToken: Optional. The next_page_token value returned from a previous
      List request, if any.
    parent: Required. The domain resource name using the form:
      `projects/{project_id}/locations/global/domains/{domain_name}`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ManagedidentitiesProjectsLocationsGlobalDomainsSetIamPolicyRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalDomainsSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class ManagedidentitiesProjectsLocationsGlobalDomainsSqlIntegrationsGetRequest(_messages.Message):
  r"""A
  ManagedidentitiesProjectsLocationsGlobalDomainsSqlIntegrationsGetRequest
  object.

  Fields:
    name: Required. MangedOU resource name using the form:
      `projects/{project_id}/locations/global/domains/*/sqlIntegrations/{name}
      `
  """

  name = _messages.StringField(1, required=True)


class ManagedidentitiesProjectsLocationsGlobalDomainsSqlIntegrationsListRequest(_messages.Message):
  r"""A
  ManagedidentitiesProjectsLocationsGlobalDomainsSqlIntegrationsListRequest
  object.

  Fields:
    filter: Optional. Filter specifying constraints of a list operation. For
      example, `SqlIntegration.name="sql"`.
    orderBy: Optional. Specifies the ordering of results following syntax at
      https://cloud.google.com/apis/design/design_patterns#sorting_order.
    pageSize: Optional. The maximum number of items to return. If not
      specified, a default value of 1000 will be used by the service.
      Regardless of the page_size value, the response may include a partial
      list and a caller should only rely on response'ANIZATIONs
      next_page_token to determine if there are more instances left to be
      queried.
    pageToken: Optional. The next_page_token value returned from a previous
      List request, if any.
    parent: Required. The resource name of the SqlIntegrations using the form:
      `projects/{project_id}/locations/global/domains/*`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ManagedidentitiesProjectsLocationsGlobalDomainsTestIamPermissionsRequest(_messages.Message):
  r"""A
  ManagedidentitiesProjectsLocationsGlobalDomainsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class ManagedidentitiesProjectsLocationsGlobalDomainsUpdateLdapssettingsRequest(_messages.Message):
  r"""A
  ManagedidentitiesProjectsLocationsGlobalDomainsUpdateLdapssettingsRequest
  object.

  Fields:
    lDAPSSettings: A LDAPSSettings resource to be passed as the request body.
    name: The resource name of the LDAPS settings. Uses the form:
      `projects/{project}/locations/{location}/domains/{domain}`.
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field. For the `FieldMask` definition, see
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  lDAPSSettings = _messages.MessageField('LDAPSSettings', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ManagedidentitiesProjectsLocationsGlobalDomainsValidateTrustRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalDomainsValidateTrustRequest
  object.

  Fields:
    name: The resource domain name, project name, and location using the form:
      `projects/{project_id}/locations/global/domains/{domain_name}`
    validateTrustRequest: A ValidateTrustRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  validateTrustRequest = _messages.MessageField('ValidateTrustRequest', 2)


class ManagedidentitiesProjectsLocationsGlobalOperationsCancelRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalOperationsCancelRequest
  object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class ManagedidentitiesProjectsLocationsGlobalOperationsDeleteRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalOperationsDeleteRequest
  object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class ManagedidentitiesProjectsLocationsGlobalOperationsGetRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class ManagedidentitiesProjectsLocationsGlobalOperationsListRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class ManagedidentitiesProjectsLocationsGlobalPeeringsCreateRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalPeeringsCreateRequest object.

  Fields:
    parent: Required. Resource project name and location using the form:
      `projects/{project_id}/locations/global`
    peering: A Peering resource to be passed as the request body.
    peeringId: Required. Peering Id, unique name to identify peering.
  """

  parent = _messages.StringField(1, required=True)
  peering = _messages.MessageField('Peering', 2)
  peeringId = _messages.StringField(3)


class ManagedidentitiesProjectsLocationsGlobalPeeringsDeleteRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalPeeringsDeleteRequest object.

  Fields:
    name: Required. Peering resource name using the form:
      `projects/{project_id}/locations/global/peerings/{peering_id}`
  """

  name = _messages.StringField(1, required=True)


class ManagedidentitiesProjectsLocationsGlobalPeeringsGetIamPolicyRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalPeeringsGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class ManagedidentitiesProjectsLocationsGlobalPeeringsGetRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalPeeringsGetRequest object.

  Fields:
    name: Required. Peering resource name using the form:
      `projects/{project_id}/locations/global/peerings/{peering_id}`
  """

  name = _messages.StringField(1, required=True)


class ManagedidentitiesProjectsLocationsGlobalPeeringsListRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalPeeringsListRequest object.

  Fields:
    filter: Optional. Filter specifying constraints of a list operation. For
      example, `peering.authoized_network ="/projects/myprojectid"`.
    orderBy: Optional. Specifies the ordering of results following syntax at
      https://cloud.google.com/apis/design/design_patterns#sorting_order.
    pageSize: Optional. The maximum number of items to return. If not
      specified, a default value of 1000 will be used by the service.
      Regardless of the page_size value, the response may include a partial
      list and a caller should only rely on response's next_page_token to
      determine if there are more instances left to be queried.
    pageToken: Optional. The next_page_token value returned from a previous
      List request, if any.
    parent: Required. The resource name of the domain location using the form:
      `projects/{project_id}/locations/global`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ManagedidentitiesProjectsLocationsGlobalPeeringsPatchRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalPeeringsPatchRequest object.

  Fields:
    name: Output only. Unique name of the peering in this scope including
      projects and location using the form:
      `projects/{project_id}/locations/global/peerings/{peering_id}`.
    peering: A Peering resource to be passed as the request body.
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field. The elements of the repeated paths field may
      only include these fields from Peering: * `labels`
  """

  name = _messages.StringField(1, required=True)
  peering = _messages.MessageField('Peering', 2)
  updateMask = _messages.StringField(3)


class ManagedidentitiesProjectsLocationsGlobalPeeringsSetIamPolicyRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsGlobalPeeringsSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class ManagedidentitiesProjectsLocationsGlobalPeeringsTestIamPermissionsRequest(_messages.Message):
  r"""A
  ManagedidentitiesProjectsLocationsGlobalPeeringsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class ManagedidentitiesProjectsLocationsListRequest(_messages.Message):
  r"""A ManagedidentitiesProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class OnPremDomainDetails(_messages.Message):
  r"""OnPremDomainDetails is the message which contains details of on-prem
  domain which is trusted and needs to be migrated.

  Fields:
    disableSidFiltering: Optional. Option to disable SID filtering.
    domainName: Required. FQDN of the on-prem domain being migrated.
  """

  disableSidFiltering = _messages.BooleanField(1)
  domainName = _messages.StringField(2)


class OnPremDomainSIDDetails(_messages.Message):
  r"""OnPremDomainDetails is the message which contains details of on-prem
  domain which is trusted and needs to be migrated.

  Enums:
    SidFilteringStateValueValuesEnum: Current SID filtering state.

  Fields:
    name: FQDN of the on-prem domain being migrated.
    sidFilteringState: Current SID filtering state.
  """

  class SidFilteringStateValueValuesEnum(_messages.Enum):
    r"""Current SID filtering state.

    Values:
      SID_FILTERING_STATE_UNSPECIFIED: SID Filtering is in unspecified state.
      ENABLED: SID Filtering is Enabled.
      DISABLED: SID Filtering is Disabled.
    """
    SID_FILTERING_STATE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  name = _messages.StringField(1)
  sidFilteringState = _messages.EnumField('SidFilteringStateValueValuesEnum', 2)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    cancelRequested: Output only. Identifies whether the user has requested
      cancellation of the operation. Operations that have been cancelled
      successfully have google.longrunning.Operation.error value with a
      google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    statusDetail: Output only. Human-readable status of the operation, if any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  cancelRequested = _messages.BooleanField(2)
  createTime = _messages.StringField(3)
  endTime = _messages.StringField(4)
  statusDetail = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Peering(_messages.Message):
  r"""Represents a Managed Microsoft Identities Peering.

  Enums:
    StateValueValuesEnum: Output only. The current state of this Peering.

  Messages:
    LabelsValue: Optional. Resource labels to represent user provided
      metadata.

  Fields:
    authorizedNetwork: Required. The full names of the Google Compute Engine
      [networks](/compute/docs/networks-and-firewalls#networks) to which the
      instance is connected. Caller needs to make sure that CIDR subnets do
      not overlap between networks, else peering creation will fail.
    createTime: Output only. The time the instance was created.
    domainResource: Required. Full domain resource path for the Managed AD
      Domain involved in peering. The resource path should be in the form:
      `projects/{project_id}/locations/global/domains/{domain_name}`
    labels: Optional. Resource labels to represent user provided metadata.
    name: Output only. Unique name of the peering in this scope including
      projects and location using the form:
      `projects/{project_id}/locations/global/peerings/{peering_id}`.
    state: Output only. The current state of this Peering.
    statusMessage: Output only. Additional information about the current
      status of this peering, if available.
    updateTime: Output only. Last update time.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of this Peering.

    Values:
      STATE_UNSPECIFIED: Not set.
      CREATING: Peering is being created.
      CONNECTED: Peering is connected.
      DISCONNECTED: Peering is disconnected.
      DELETING: Peering is being deleted.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    CONNECTED = 2
    DISCONNECTED = 3
    DELETING = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels to represent user provided metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  authorizedNetwork = _messages.StringField(1)
  createTime = _messages.StringField(2)
  domainResource = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  statusMessage = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  bindings = _messages.MessageField('Binding', 1, repeated=True)
  etag = _messages.BytesField(2)
  version = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class ReconfigureTrustRequest(_messages.Message):
  r"""A ReconfigureTrustRequest object.

  Fields:
    trust: The domain trust resource with updated dns conditional forwarder.
  """

  trust = _messages.MessageField('Trust', 1)


class ResetAdminPasswordRequest(_messages.Message):
  r"""A ResetAdminPasswordRequest object."""


class ResetAdminPasswordResponse(_messages.Message):
  r"""A ResetAdminPasswordResponse object.

  Fields:
    password: A random password. See admin for more information.
  """

  password = _messages.StringField(1)


class RestoreDomainRequest(_messages.Message):
  r"""RestoreDomainRequest is the request received by RestoreDomain rpc

  Fields:
    backupId: Required. ID of the backup to be restored
  """

  backupId = _messages.StringField(1)


class SQLIntegration(_messages.Message):
  r"""Represents the SQL instance integrated with AD.

  Enums:
    StateValueValuesEnum: Output only. The current state of the managed OU.

  Fields:
    createTime: Output only. The time the instance was created.
    name: The unique name of the sql integration in the form of `projects/{pro
      ject_id}/locations/global/domains/{domain_name}/sqlIntegrations/{sql_int
      egration}`
    sqlInstance: The full resource name of an integrated sql instance
    state: Output only. The current state of the managed OU.
    updateTime: Output only. Last update time for this SQL instance.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the managed OU.

    Values:
      STATE_UNSPECIFIED: Not Set
      CREATING: The sqlIntegration is being created.
      DELETING: The sqlIntegration is being deleted.
      READY: The sqlIntegration is ready.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    DELETING = 2
    READY = 3

  createTime = _messages.StringField(1)
  name = _messages.StringField(2)
  sqlInstance = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)
  updateTime = _messages.StringField(5)


class Schedule(_messages.Message):
  r"""Configure the schedule.

  Enums:
    DayValueValuesEnum: Allows to define schedule that runs specified day of
      the week.

  Fields:
    day: Allows to define schedule that runs specified day of the week.
    duration: Output only. Duration of the time window, set by service
      producer.
    startTime: Time within the window to start the operations.
  """

  class DayValueValuesEnum(_messages.Enum):
    r"""Allows to define schedule that runs specified day of the week.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  day = _messages.EnumField('DayValueValuesEnum', 1)
  duration = _messages.StringField(2)
  startTime = _messages.MessageField('TimeOfDay', 3)


class SchemaExtension(_messages.Message):
  r"""Represents a Managed Microsoft Identities Schema Extension.

  Enums:
    StateValueValuesEnum: Output only. The current state of the Schema
      Extension.

  Fields:
    backup: Output only. Id for backup taken before extending domain schema.
    createTime: Output only. The time the schema extension was created.
    description: Description for Schema Change.
    fileContents: File uploaded as a byte stream input.
    gcsPath: File stored in Cloud Storage bucket and represented in the form
      projects/{project_id}/buckets/{bucket_name}/objects/{object_name}
    name: The unique name of the Schema Extension in the form of projects/{pro
      ject_id}/locations/global/domains/{domain_name}/schemaExtensions/{schema
      _extension}
    state: Output only. The current state of the Schema Extension.
    statusMessage: Output only. Additional information about the current
      status of this Schema Extension, if available.
    updateTime: Output only. Last update time.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the Schema Extension.

    Values:
      STATE_UNSPECIFIED: Not set.
      CREATING: LDIF is currently getting applied on domain.
      COMPLETED: LDIF has been successfully applied on domain.
      FAILED: LDIF did not applied successfully.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    COMPLETED = 2
    FAILED = 3

  backup = _messages.StringField(1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  fileContents = _messages.BytesField(4)
  gcsPath = _messages.StringField(5)
  name = _messages.StringField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  statusMessage = _messages.StringField(8)
  updateTime = _messages.StringField(9)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
  """

  policy = _messages.MessageField('Policy', 1)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TimeOfDay(_messages.Message):
  r"""Represents a time of day. The date and time zone are either not
  significant or are specified elsewhere. An API may choose to allow leap
  seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.

  Fields:
    hours: Hours of a day in 24 hour format. Must be greater than or equal to
      0 and typically must be less than or equal to 23. An API may choose to
      allow the value "24:00:00" for scenarios like business closing time.
    minutes: Minutes of an hour. Must be greater than or equal to 0 and less
      than or equal to 59.
    nanos: Fractions of seconds, in nanoseconds. Must be greater than or equal
      to 0 and less than or equal to 999,999,999.
    seconds: Seconds of a minute. Must be greater than or equal to 0 and
      typically must be less than or equal to 59. An API may allow the value
      60 if it allows leap-seconds.
  """

  hours = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minutes = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  nanos = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  seconds = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class Trust(_messages.Message):
  r"""Represents a relationship between two domains which makes it possible
  for users in one domain to be authenticated by a dc in another domain. Refer
  https://docs.microsoft.com/en-us/previous-versions/windows/it-pro/windows-
  server-2008-R2-and-2008/cc731335(v%3dws.10) If the trust is being changed,
  it will be placed into the UPDATING state, which indicates that the resource
  is being reconciled. At this point, Get will reflect an intermediate state.

  Enums:
    StateValueValuesEnum: Output only. The current state of this trust.
    TrustDirectionValueValuesEnum: The trust direction decides the current
      domain is trusted, trusting or both.
    TrustTypeValueValuesEnum: The type of trust represented by the trust
      resource.

  Fields:
    createTime: Output only. The time the instance was created.
    lastKnownTrustConnectedHeartbeatTime: Output only. The last heartbeat time
      when the trust was known to be connected.
    selectiveAuthentication: The trust authentication type which decides
      whether the trusted side has forest/domain wide access or selective
      access to approved set of resources.
    state: Output only. The current state of this trust.
    stateDescription: Output only. Additional information about the current
      state of this trust, if available.
    targetDnsIpAddresses: The target dns server ip addresses which can resolve
      the remote domain involved in trust.
    targetDomainName: The fully qualified target domain name which will be in
      trust with current domain.
    trustDirection: The trust direction decides the current domain is trusted,
      trusting or both.
    trustHandshakeSecret: Input only, and will not be stored. The trust secret
      used for handshake with target domain.
    trustType: The type of trust represented by the trust resource.
    updateTime: Output only. Last update time.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of this trust.

    Values:
      STATE_UNSPECIFIED: Not Set
      CREATING: Domain trust is being created.
      UPDATING: Domain trust is being updated.
      DELETING: Domain trust is being deleted.
      CONNECTED: Domain trust is connected.
      DISCONNECTED: Domain trust is disconnected.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    UPDATING = 2
    DELETING = 3
    CONNECTED = 4
    DISCONNECTED = 5

  class TrustDirectionValueValuesEnum(_messages.Enum):
    r"""The trust direction decides the current domain is trusted, trusting or
    both.

    Values:
      TRUST_DIRECTION_UNSPECIFIED: Not Set
      INBOUND: The inbound direction represents the trusting side.
      OUTBOUND: The outboud direction represents the trusted side.
      BIDIRECTIONAL: The bidirectional direction represents the trusted /
        trusting side.
    """
    TRUST_DIRECTION_UNSPECIFIED = 0
    INBOUND = 1
    OUTBOUND = 2
    BIDIRECTIONAL = 3

  class TrustTypeValueValuesEnum(_messages.Enum):
    r"""The type of trust represented by the trust resource.

    Values:
      TRUST_TYPE_UNSPECIFIED: Not Set
      FOREST: The forest trust.
      EXTERNAL: The external domain trust.
    """
    TRUST_TYPE_UNSPECIFIED = 0
    FOREST = 1
    EXTERNAL = 2

  createTime = _messages.StringField(1)
  lastKnownTrustConnectedHeartbeatTime = _messages.StringField(2)
  selectiveAuthentication = _messages.BooleanField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)
  stateDescription = _messages.StringField(5)
  targetDnsIpAddresses = _messages.StringField(6, repeated=True)
  targetDomainName = _messages.StringField(7)
  trustDirection = _messages.EnumField('TrustDirectionValueValuesEnum', 8)
  trustHandshakeSecret = _messages.StringField(9)
  trustType = _messages.EnumField('TrustTypeValueValuesEnum', 10)
  updateTime = _messages.StringField(11)


class UpdatePolicy(_messages.Message):
  r"""Maintenance policy applicable to instance updates.

  Enums:
    ChannelValueValuesEnum: Optional. Relative scheduling channel applied to
      resource.

  Fields:
    channel: Optional. Relative scheduling channel applied to resource.
    denyMaintenancePeriods: Deny Maintenance Period that is applied to
      resource to indicate when maintenance is forbidden. The protocol
      supports zero-to-many such periods, but the current SLM Rollout
      implementation only supports zero-to-one.
    window: Optional. Maintenance window that is applied to resources covered
      by this policy.
  """

  class ChannelValueValuesEnum(_messages.Enum):
    r"""Optional. Relative scheduling channel applied to resource.

    Values:
      UPDATE_CHANNEL_UNSPECIFIED: Unspecified channel.
      EARLIER: Early channel within a customer project.
      LATER: Later channel within a customer project.
      WEEK1: ! ! The follow channels can ONLY be used if you adopt the new MW
        system! ! ! NOTE: all WEEK channels are assumed to be under a weekly
        window. ! There is currently no dedicated channel definitions for
        Daily windows. ! If you use Daily window, the system will assume a 1d
        (24Hours) advanced ! notification period b/w EARLY and LATER. ! We may
        consider support more flexible daily channel specifications in ! the
        future. WEEK1 == EARLIER with minimum 7d advanced notification. {7d,
        14d} The system will treat them equally and will use WEEK1 whenever it
        can. New customers are encouraged to use this channel annotation.
      WEEK2: WEEK2 == LATER with minimum 14d advanced notification {14d, 21d}.
      WEEK5: WEEK5 == 40d support. minimum 35d advanced notification {35d,
        42d}.
    """
    UPDATE_CHANNEL_UNSPECIFIED = 0
    EARLIER = 1
    LATER = 2
    WEEK1 = 3
    WEEK2 = 4
    WEEK5 = 5

  channel = _messages.EnumField('ChannelValueValuesEnum', 1)
  denyMaintenancePeriods = _messages.MessageField('DenyMaintenancePeriod', 2, repeated=True)
  window = _messages.MessageField('MaintenanceWindow', 3)


class ValidateTrustRequest(_messages.Message):
  r"""A ValidateTrustRequest object.

  Fields:
    trust: The domain trust to validate trust state for.
  """

  trust = _messages.MessageField('Trust', 1)


class WeeklyCycle(_messages.Message):
  r"""Time window specified for weekly operations.

  Fields:
    schedule: User can specify multiple windows in a week. Minimum of 1
      window.
  """

  schedule = _messages.MessageField('Schedule', 1, repeated=True)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    ManagedidentitiesProjectsLocationsGlobalDomainsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    ManagedidentitiesProjectsLocationsGlobalDomainsBackupsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    ManagedidentitiesProjectsLocationsGlobalPeeringsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
