"""Generated client library for managedidentities version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.managedidentities.v1 import managedidentities_v1_messages as messages


class ManagedidentitiesV1(base_api.BaseApiClient):
  """Generated client library for service managedidentities version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://managedidentities.googleapis.com/'
  MTLS_BASE_URL = 'https://managedidentities.mtls.googleapis.com/'

  _PACKAGE = 'managedidentities'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'ManagedidentitiesV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new managedidentities handle."""
    url = url or self.BASE_URL
    super(ManagedidentitiesV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_global_domains_backups = self.ProjectsLocationsGlobalDomainsBackupsService(self)
    self.projects_locations_global_domains_sqlIntegrations = self.ProjectsLocationsGlobalDomainsSqlIntegrationsService(self)
    self.projects_locations_global_domains = self.ProjectsLocationsGlobalDomainsService(self)
    self.projects_locations_global_operations = self.ProjectsLocationsGlobalOperationsService(self)
    self.projects_locations_global_peerings = self.ProjectsLocationsGlobalPeeringsService(self)
    self.projects_locations_global = self.ProjectsLocationsGlobalService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsGlobalDomainsBackupsService(base_api.BaseApiService):
    """Service class for the projects_locations_global_domains_backups resource."""

    _NAME = 'projects_locations_global_domains_backups'

    def __init__(self, client):
      super(ManagedidentitiesV1.ProjectsLocationsGlobalDomainsBackupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a Backup for a domain.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsBackupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}/backups',
        http_method='POST',
        method_id='managedidentities.projects.locations.global.domains.backups.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['backupId'],
        relative_path='v1/{+parent}/backups',
        request_field='backup',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsBackupsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes identified Backup.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsBackupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}/backups/{backupsId}',
        http_method='DELETE',
        method_id='managedidentities.projects.locations.global.domains.backups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsBackupsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Backup.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsBackupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Backup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}/backups/{backupsId}',
        http_method='GET',
        method_id='managedidentities.projects.locations.global.domains.backups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsBackupsGetRequest',
        response_type_name='Backup',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsBackupsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}/backups/{backupsId}:getIamPolicy',
        http_method='GET',
        method_id='managedidentities.projects.locations.global.domains.backups.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsBackupsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Backup in a given project.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsBackupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBackupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}/backups',
        http_method='GET',
        method_id='managedidentities.projects.locations.global.domains.backups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/backups',
        request_field='',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsBackupsListRequest',
        response_type_name='ListBackupsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the labels for specified Backup.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsBackupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}/backups/{backupsId}',
        http_method='PATCH',
        method_id='managedidentities.projects.locations.global.domains.backups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='backup',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsBackupsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsBackupsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}/backups/{backupsId}:setIamPolicy',
        http_method='POST',
        method_id='managedidentities.projects.locations.global.domains.backups.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsBackupsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsBackupsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}/backups/{backupsId}:testIamPermissions',
        http_method='POST',
        method_id='managedidentities.projects.locations.global.domains.backups.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsBackupsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsGlobalDomainsSqlIntegrationsService(base_api.BaseApiService):
    """Service class for the projects_locations_global_domains_sqlIntegrations resource."""

    _NAME = 'projects_locations_global_domains_sqlIntegrations'

    def __init__(self, client):
      super(ManagedidentitiesV1.ProjectsLocationsGlobalDomainsSqlIntegrationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details of a single sqlIntegration.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsSqlIntegrationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SqlIntegration) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}/sqlIntegrations/{sqlIntegrationsId}',
        http_method='GET',
        method_id='managedidentities.projects.locations.global.domains.sqlIntegrations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsSqlIntegrationsGetRequest',
        response_type_name='SqlIntegration',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists SqlIntegrations in a given domain.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsSqlIntegrationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSqlIntegrationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}/sqlIntegrations',
        http_method='GET',
        method_id='managedidentities.projects.locations.global.domains.sqlIntegrations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/sqlIntegrations',
        request_field='',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsSqlIntegrationsListRequest',
        response_type_name='ListSqlIntegrationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsGlobalDomainsService(base_api.BaseApiService):
    """Service class for the projects_locations_global_domains resource."""

    _NAME = 'projects_locations_global_domains'

    def __init__(self, client):
      super(ManagedidentitiesV1.ProjectsLocationsGlobalDomainsService, self).__init__(client)
      self._upload_configs = {
          }

    def AttachTrust(self, request, global_params=None):
      r"""Adds an AD trust to a domain.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsAttachTrustRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('AttachTrust')
      return self._RunMethod(
          config, request, global_params=global_params)

    AttachTrust.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}:attachTrust',
        http_method='POST',
        method_id='managedidentities.projects.locations.global.domains.attachTrust',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:attachTrust',
        request_field='attachTrustRequest',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsAttachTrustRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def CheckMigrationPermission(self, request, global_params=None):
      r"""CheckMigrationPermission API gets the current state of DomainMigration.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsCheckMigrationPermissionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CheckMigrationPermissionResponse) The response message.
      """
      config = self.GetMethodConfig('CheckMigrationPermission')
      return self._RunMethod(
          config, request, global_params=global_params)

    CheckMigrationPermission.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}:checkMigrationPermission',
        http_method='POST',
        method_id='managedidentities.projects.locations.global.domains.checkMigrationPermission',
        ordered_params=['domain'],
        path_params=['domain'],
        query_params=[],
        relative_path='v1/{+domain}:checkMigrationPermission',
        request_field='checkMigrationPermissionRequest',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsCheckMigrationPermissionRequest',
        response_type_name='CheckMigrationPermissionResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a Microsoft AD domain.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains',
        http_method='POST',
        method_id='managedidentities.projects.locations.global.domains.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['domainName'],
        relative_path='v1/{+parent}/domains',
        request_field='domain',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a domain.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}',
        http_method='DELETE',
        method_id='managedidentities.projects.locations.global.domains.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def DetachTrust(self, request, global_params=None):
      r"""Removes an AD trust.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsDetachTrustRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('DetachTrust')
      return self._RunMethod(
          config, request, global_params=global_params)

    DetachTrust.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}:detachTrust',
        http_method='POST',
        method_id='managedidentities.projects.locations.global.domains.detachTrust',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:detachTrust',
        request_field='detachTrustRequest',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsDetachTrustRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def DisableMigration(self, request, global_params=None):
      r"""Disable Domain Migration.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsDisableMigrationRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('DisableMigration')
      return self._RunMethod(
          config, request, global_params=global_params)

    DisableMigration.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}:disableMigration',
        http_method='POST',
        method_id='managedidentities.projects.locations.global.domains.disableMigration',
        ordered_params=['domain'],
        path_params=['domain'],
        query_params=[],
        relative_path='v1/{+domain}:disableMigration',
        request_field='disableMigrationRequest',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsDisableMigrationRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def DomainJoinMachine(self, request, global_params=None):
      r"""DomainJoinMachine API joins a Compute Engine VM to the domain.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsDomainJoinMachineRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DomainJoinMachineResponse) The response message.
      """
      config = self.GetMethodConfig('DomainJoinMachine')
      return self._RunMethod(
          config, request, global_params=global_params)

    DomainJoinMachine.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}:domainJoinMachine',
        http_method='POST',
        method_id='managedidentities.projects.locations.global.domains.domainJoinMachine',
        ordered_params=['domain'],
        path_params=['domain'],
        query_params=[],
        relative_path='v1/{+domain}:domainJoinMachine',
        request_field='domainJoinMachineRequest',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsDomainJoinMachineRequest',
        response_type_name='DomainJoinMachineResponse',
        supports_download=False,
    )

    def EnableMigration(self, request, global_params=None):
      r"""Enable Domain Migration.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsEnableMigrationRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('EnableMigration')
      return self._RunMethod(
          config, request, global_params=global_params)

    EnableMigration.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}:enableMigration',
        http_method='POST',
        method_id='managedidentities.projects.locations.global.domains.enableMigration',
        ordered_params=['domain'],
        path_params=['domain'],
        query_params=[],
        relative_path='v1/{+domain}:enableMigration',
        request_field='enableMigrationRequest',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsEnableMigrationRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def ExtendSchema(self, request, global_params=None):
      r"""Extend Schema for Domain.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsExtendSchemaRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('ExtendSchema')
      return self._RunMethod(
          config, request, global_params=global_params)

    ExtendSchema.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}:extendSchema',
        http_method='POST',
        method_id='managedidentities.projects.locations.global.domains.extendSchema',
        ordered_params=['domain'],
        path_params=['domain'],
        query_params=[],
        relative_path='v1/{+domain}:extendSchema',
        request_field='extendSchemaRequest',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsExtendSchemaRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets information about a domain.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Domain) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}',
        http_method='GET',
        method_id='managedidentities.projects.locations.global.domains.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsGetRequest',
        response_type_name='Domain',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}:getIamPolicy',
        http_method='GET',
        method_id='managedidentities.projects.locations.global.domains.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def GetLdapssettings(self, request, global_params=None):
      r"""Gets the domain ldaps settings.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsGetLdapssettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LDAPSSettings) The response message.
      """
      config = self.GetMethodConfig('GetLdapssettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetLdapssettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}/ldapssettings',
        http_method='GET',
        method_id='managedidentities.projects.locations.global.domains.getLdapssettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}/ldapssettings',
        request_field='',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsGetLdapssettingsRequest',
        response_type_name='LDAPSSettings',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists domains in a project.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDomainsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains',
        http_method='GET',
        method_id='managedidentities.projects.locations.global.domains.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/domains',
        request_field='',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsListRequest',
        response_type_name='ListDomainsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the metadata and configuration of a domain.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}',
        http_method='PATCH',
        method_id='managedidentities.projects.locations.global.domains.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='domain',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def ReconfigureTrust(self, request, global_params=None):
      r"""Updates the DNS conditional forwarder.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsReconfigureTrustRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('ReconfigureTrust')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReconfigureTrust.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}:reconfigureTrust',
        http_method='POST',
        method_id='managedidentities.projects.locations.global.domains.reconfigureTrust',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:reconfigureTrust',
        request_field='reconfigureTrustRequest',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsReconfigureTrustRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def ResetAdminPassword(self, request, global_params=None):
      r"""Resets a domain's administrator password.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsResetAdminPasswordRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ResetAdminPasswordResponse) The response message.
      """
      config = self.GetMethodConfig('ResetAdminPassword')
      return self._RunMethod(
          config, request, global_params=global_params)

    ResetAdminPassword.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}:resetAdminPassword',
        http_method='POST',
        method_id='managedidentities.projects.locations.global.domains.resetAdminPassword',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:resetAdminPassword',
        request_field='resetAdminPasswordRequest',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsResetAdminPasswordRequest',
        response_type_name='ResetAdminPasswordResponse',
        supports_download=False,
    )

    def Restore(self, request, global_params=None):
      r"""RestoreDomain restores domain backup mentioned in the RestoreDomainRequest.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsRestoreRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Restore')
      return self._RunMethod(
          config, request, global_params=global_params)

    Restore.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}:restore',
        http_method='POST',
        method_id='managedidentities.projects.locations.global.domains.restore',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:restore',
        request_field='restoreDomainRequest',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsRestoreRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}:setIamPolicy',
        http_method='POST',
        method_id='managedidentities.projects.locations.global.domains.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}:testIamPermissions',
        http_method='POST',
        method_id='managedidentities.projects.locations.global.domains.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def UpdateLdapssettings(self, request, global_params=None):
      r"""Patches a single ldaps settings.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsUpdateLdapssettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('UpdateLdapssettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateLdapssettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}/ldapssettings',
        http_method='PATCH',
        method_id='managedidentities.projects.locations.global.domains.updateLdapssettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}/ldapssettings',
        request_field='lDAPSSettings',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsUpdateLdapssettingsRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def ValidateTrust(self, request, global_params=None):
      r"""Validates a trust state, that the target domain is reachable, and that the target domain is able to accept incoming trust requests.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalDomainsValidateTrustRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('ValidateTrust')
      return self._RunMethod(
          config, request, global_params=global_params)

    ValidateTrust.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/domains/{domainsId}:validateTrust',
        http_method='POST',
        method_id='managedidentities.projects.locations.global.domains.validateTrust',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:validateTrust',
        request_field='validateTrustRequest',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalDomainsValidateTrustRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsGlobalOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_global_operations resource."""

    _NAME = 'projects_locations_global_operations'

    def __init__(self, client):
      super(ManagedidentitiesV1.ProjectsLocationsGlobalOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='managedidentities.projects.locations.global.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/operations/{operationsId}',
        http_method='DELETE',
        method_id='managedidentities.projects.locations.global.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/operations/{operationsId}',
        http_method='GET',
        method_id='managedidentities.projects.locations.global.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/operations',
        http_method='GET',
        method_id='managedidentities.projects.locations.global.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsGlobalPeeringsService(base_api.BaseApiService):
    """Service class for the projects_locations_global_peerings resource."""

    _NAME = 'projects_locations_global_peerings'

    def __init__(self, client):
      super(ManagedidentitiesV1.ProjectsLocationsGlobalPeeringsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a Peering for Managed AD instance.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalPeeringsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/peerings',
        http_method='POST',
        method_id='managedidentities.projects.locations.global.peerings.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['peeringId'],
        relative_path='v1/{+parent}/peerings',
        request_field='peering',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalPeeringsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes identified Peering.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalPeeringsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/peerings/{peeringsId}',
        http_method='DELETE',
        method_id='managedidentities.projects.locations.global.peerings.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalPeeringsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Peering.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalPeeringsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Peering) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/peerings/{peeringsId}',
        http_method='GET',
        method_id='managedidentities.projects.locations.global.peerings.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalPeeringsGetRequest',
        response_type_name='Peering',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalPeeringsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/peerings/{peeringsId}:getIamPolicy',
        http_method='GET',
        method_id='managedidentities.projects.locations.global.peerings.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalPeeringsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Peerings in a given project.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalPeeringsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPeeringsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/peerings',
        http_method='GET',
        method_id='managedidentities.projects.locations.global.peerings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/peerings',
        request_field='',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalPeeringsListRequest',
        response_type_name='ListPeeringsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the labels for specified Peering.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalPeeringsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/peerings/{peeringsId}',
        http_method='PATCH',
        method_id='managedidentities.projects.locations.global.peerings.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='peering',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalPeeringsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalPeeringsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/peerings/{peeringsId}:setIamPolicy',
        http_method='POST',
        method_id='managedidentities.projects.locations.global.peerings.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalPeeringsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (ManagedidentitiesProjectsLocationsGlobalPeeringsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/global/peerings/{peeringsId}:testIamPermissions',
        http_method='POST',
        method_id='managedidentities.projects.locations.global.peerings.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='ManagedidentitiesProjectsLocationsGlobalPeeringsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsGlobalService(base_api.BaseApiService):
    """Service class for the projects_locations_global resource."""

    _NAME = 'projects_locations_global'

    def __init__(self, client):
      super(ManagedidentitiesV1.ProjectsLocationsGlobalService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(ManagedidentitiesV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (ManagedidentitiesProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='managedidentities.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ManagedidentitiesProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (ManagedidentitiesProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='managedidentities.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/locations',
        request_field='',
        request_type_name='ManagedidentitiesProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(ManagedidentitiesV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
