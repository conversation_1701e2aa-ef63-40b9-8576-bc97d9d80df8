"""Generated client library for gkerecommender version v1alpha1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.gkerecommender.v1alpha1 import gkerecommender_v1alpha1_messages as messages


class GkerecommenderV1alpha1(base_api.BaseApiClient):
  """Generated client library for service gkerecommender version v1alpha1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://gkerecommender.googleapis.com/'
  MTLS_BASE_URL = 'https://gkerecommender.mtls.googleapis.com/'

  _PACKAGE = 'gkerecommender'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1alpha1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'GkerecommenderV1alpha1'
  _URL_VERSION = 'v1alpha1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new gkerecommender handle."""
    url = url or self.BASE_URL
    super(GkerecommenderV1alpha1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.accelerators = self.AcceleratorsService(self)
    self.modelServers_versions = self.ModelServersVersionsService(self)
    self.modelServers = self.ModelServersService(self)
    self.models = self.ModelsService(self)
    self.modelsAndServers = self.ModelsAndServersService(self)
    self.v1alpha1 = self.V1alpha1Service(self)

  class AcceleratorsService(base_api.BaseApiService):
    """Service class for the accelerators resource."""

    _NAME = 'accelerators'

    def __init__(self, client):
      super(GkerecommenderV1alpha1.AcceleratorsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List method for the accelerators service.

      Args:
        request: (GkerecommenderAcceleratorsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListCompatibleAcceleratorProfilesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='gkerecommender.accelerators.list',
        ordered_params=[],
        path_params=[],
        query_params=['modelName', 'modelServerName', 'modelServerVersion', 'performanceRequirements_cost_costPerMillionInputTokens_nanos', 'performanceRequirements_cost_costPerMillionInputTokens_units', 'performanceRequirements_cost_costPerMillionNormalizedOutputTokens_nanos', 'performanceRequirements_cost_costPerMillionNormalizedOutputTokens_units', 'performanceRequirements_cost_outputToInputCostRatio', 'performanceRequirements_cost_pricingModel', 'performanceRequirements_maxNtpotMilliseconds'],
        relative_path='v1alpha1/accelerators',
        request_field='',
        request_type_name='GkerecommenderAcceleratorsListRequest',
        response_type_name='ListCompatibleAcceleratorProfilesResponse',
        supports_download=False,
    )

  class ModelServersVersionsService(base_api.BaseApiService):
    """Service class for the modelServers_versions resource."""

    _NAME = 'modelServers_versions'

    def __init__(self, client):
      super(GkerecommenderV1alpha1.ModelServersVersionsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List method for the modelServers_versions service.

      Args:
        request: (GkerecommenderModelServersVersionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListModelServerVersionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='gkerecommender.modelServers.versions.list',
        ordered_params=['modelServerName'],
        path_params=['modelServerName'],
        query_params=['modelName'],
        relative_path='v1alpha1/modelServers/{modelServerName}/versions',
        request_field='',
        request_type_name='GkerecommenderModelServersVersionsListRequest',
        response_type_name='ListModelServerVersionsResponse',
        supports_download=False,
    )

  class ModelServersService(base_api.BaseApiService):
    """Service class for the modelServers resource."""

    _NAME = 'modelServers'

    def __init__(self, client):
      super(GkerecommenderV1alpha1.ModelServersService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List method for the modelServers service.

      Args:
        request: (GkerecommenderModelServersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListModelServersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='gkerecommender.modelServers.list',
        ordered_params=[],
        path_params=[],
        query_params=['modelName'],
        relative_path='v1alpha1/modelServers',
        request_field='',
        request_type_name='GkerecommenderModelServersListRequest',
        response_type_name='ListModelServersResponse',
        supports_download=False,
    )

  class ModelsService(base_api.BaseApiService):
    """Service class for the models resource."""

    _NAME = 'models'

    def __init__(self, client):
      super(GkerecommenderV1alpha1.ModelsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List method for the models service.

      Args:
        request: (GkerecommenderModelsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListModelsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='gkerecommender.models.list',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v1alpha1/models',
        request_field='',
        request_type_name='GkerecommenderModelsListRequest',
        response_type_name='ListModelsResponse',
        supports_download=False,
    )

  class ModelsAndServersService(base_api.BaseApiService):
    """Service class for the modelsAndServers resource."""

    _NAME = 'modelsAndServers'

    def __init__(self, client):
      super(GkerecommenderV1alpha1.ModelsAndServersService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List method for the modelsAndServers service.

      Args:
        request: (GkerecommenderModelsAndServersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListModelAndServerCombinationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='gkerecommender.modelsAndServers.list',
        ordered_params=[],
        path_params=[],
        query_params=['modelName', 'modelServerName', 'modelServerVersion'],
        relative_path='v1alpha1/modelsAndServers',
        request_field='',
        request_type_name='GkerecommenderModelsAndServersListRequest',
        response_type_name='ListModelAndServerCombinationsResponse',
        supports_download=False,
    )

  class V1alpha1Service(base_api.BaseApiService):
    """Service class for the v1alpha1 resource."""

    _NAME = 'v1alpha1'

    def __init__(self, client):
      super(GkerecommenderV1alpha1.V1alpha1Service, self).__init__(client)
      self._upload_configs = {
          }

    def OptimizedManifest(self, request, global_params=None):
      r"""OptimizedManifest method for the v1alpha1 service.

      Args:
        request: (GkerecommenderOptimizedManifestRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GenerateOptimizedManifestResponse) The response message.
      """
      config = self.GetMethodConfig('OptimizedManifest')
      return self._RunMethod(
          config, request, global_params=global_params)

    OptimizedManifest.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='gkerecommender.optimizedManifest',
        ordered_params=[],
        path_params=[],
        query_params=['acceleratorType', 'kubernetesNamespace', 'modelAndModelServerInfo_modelName', 'modelAndModelServerInfo_modelServerName', 'modelAndModelServerInfo_modelServerVersion', 'storageConfig_modelBucketUri', 'targetNtpotMilliseconds'],
        relative_path='v1alpha1/optimizedManifest',
        request_field='',
        request_type_name='GkerecommenderOptimizedManifestRequest',
        response_type_name='GenerateOptimizedManifestResponse',
        supports_download=False,
    )
