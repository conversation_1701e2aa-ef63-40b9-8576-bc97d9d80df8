"""Generated client library for gkerecommender version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.gkerecommender.v1 import gkerecommender_v1_messages as messages


class GkerecommenderV1(base_api.BaseApiClient):
  """Generated client library for service gkerecommender version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://gkerecommender.googleapis.com/'
  MTLS_BASE_URL = 'https://gkerecommender.mtls.googleapis.com/'

  _PACKAGE = 'gkerecommender'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'GkerecommenderV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new gkerecommender handle."""
    url = url or self.BASE_URL
    super(GkerecommenderV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.modelServers_versions = self.ModelServersVersionsService(self)
    self.modelServers = self.ModelServersService(self)
    self.models = self.ModelsService(self)
    self.profiles = self.ProfilesService(self)
    self.v1 = self.V1Service(self)

  class ModelServersVersionsService(base_api.BaseApiService):
    """Service class for the modelServers_versions resource."""

    _NAME = 'modelServers_versions'

    def __init__(self, client):
      super(GkerecommenderV1.ModelServersVersionsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List method for the modelServers_versions service.

      Args:
        request: (GkerecommenderModelServersVersionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListModelServerVersionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='gkerecommender.modelServers.versions.list',
        ordered_params=['modelServerName'],
        path_params=['modelServerName'],
        query_params=['modelName'],
        relative_path='v1/modelServers/{modelServerName}/versions',
        request_field='',
        request_type_name='GkerecommenderModelServersVersionsListRequest',
        response_type_name='ListModelServerVersionsResponse',
        supports_download=False,
    )

  class ModelServersService(base_api.BaseApiService):
    """Service class for the modelServers resource."""

    _NAME = 'modelServers'

    def __init__(self, client):
      super(GkerecommenderV1.ModelServersService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List method for the modelServers service.

      Args:
        request: (GkerecommenderModelServersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListModelServersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='gkerecommender.modelServers.list',
        ordered_params=[],
        path_params=[],
        query_params=['modelName'],
        relative_path='v1/modelServers',
        request_field='',
        request_type_name='GkerecommenderModelServersListRequest',
        response_type_name='ListModelServersResponse',
        supports_download=False,
    )

  class ModelsService(base_api.BaseApiService):
    """Service class for the models resource."""

    _NAME = 'models'

    def __init__(self, client):
      super(GkerecommenderV1.ModelsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List method for the models service.

      Args:
        request: (GkerecommenderModelsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListModelsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='gkerecommender.models.list',
        ordered_params=[],
        path_params=[],
        query_params=[],
        relative_path='v1/models',
        request_field='',
        request_type_name='GkerecommenderModelsListRequest',
        response_type_name='ListModelsResponse',
        supports_download=False,
    )

  class ProfilesService(base_api.BaseApiService):
    """Service class for the profiles resource."""

    _NAME = 'profiles'

    def __init__(self, client):
      super(GkerecommenderV1.ProfilesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""List method for the profiles service.

      Args:
        request: (GkerecommenderProfilesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListCompatibleProfilesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='gkerecommender.profiles.list',
        ordered_params=[],
        path_params=[],
        query_params=['modelName', 'modelServerName', 'modelServerVersion', 'performanceRequirements_targetCost_costPerMillionInputTokens_nanos', 'performanceRequirements_targetCost_costPerMillionInputTokens_units', 'performanceRequirements_targetCost_costPerMillionOutputTokens_nanos', 'performanceRequirements_targetCost_costPerMillionOutputTokens_units', 'performanceRequirements_targetCost_outputToInputCostRatio', 'performanceRequirements_targetCost_pricingModel', 'performanceRequirements_targetNtpotMilliseconds', 'performanceRequirements_targetTtftMilliseconds'],
        relative_path='v1/profiles',
        request_field='',
        request_type_name='GkerecommenderProfilesListRequest',
        response_type_name='ListCompatibleProfilesResponse',
        supports_download=False,
    )

  class V1Service(base_api.BaseApiService):
    """Service class for the v1 resource."""

    _NAME = 'v1'

    def __init__(self, client):
      super(GkerecommenderV1.V1Service, self).__init__(client)
      self._upload_configs = {
          }

    def GetBenchmarkingData(self, request, global_params=None):
      r"""GetBenchmarkingData method for the v1 service.

      Args:
        request: (GkerecommenderGetBenchmarkingDataRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GetBenchmarkingDataResponse) The response message.
      """
      config = self.GetMethodConfig('GetBenchmarkingData')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetBenchmarkingData.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='gkerecommender.getBenchmarkingData',
        ordered_params=[],
        path_params=[],
        query_params=['instanceType', 'modelAndModelServerInfo_modelName', 'modelAndModelServerInfo_modelServerName', 'modelAndModelServerInfo_modelServerVersion'],
        relative_path='v1/benchmarkingData',
        request_field='',
        request_type_name='GkerecommenderGetBenchmarkingDataRequest',
        response_type_name='GetBenchmarkingDataResponse',
        supports_download=False,
    )

    def OptimizedManifest(self, request, global_params=None):
      r"""OptimizedManifest method for the v1 service.

      Args:
        request: (GkerecommenderOptimizedManifestRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GenerateOptimizedManifestResponse) The response message.
      """
      config = self.GetMethodConfig('OptimizedManifest')
      return self._RunMethod(
          config, request, global_params=global_params)

    OptimizedManifest.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='gkerecommender.optimizedManifest',
        ordered_params=[],
        path_params=[],
        query_params=['acceleratorType', 'kubernetesNamespace', 'modelAndModelServerInfo_modelName', 'modelAndModelServerInfo_modelServerName', 'modelAndModelServerInfo_modelServerVersion', 'performanceRequirements_targetCost_costPerMillionInputTokens_nanos', 'performanceRequirements_targetCost_costPerMillionInputTokens_units', 'performanceRequirements_targetCost_costPerMillionOutputTokens_nanos', 'performanceRequirements_targetCost_costPerMillionOutputTokens_units', 'performanceRequirements_targetCost_outputToInputCostRatio', 'performanceRequirements_targetCost_pricingModel', 'performanceRequirements_targetNtpotMilliseconds', 'performanceRequirements_targetTtftMilliseconds', 'storageConfig_modelBucketUri', 'storageConfig_xlaCacheBucketUri'],
        relative_path='v1/optimizedManifest',
        request_field='',
        request_type_name='GkerecommenderOptimizedManifestRequest',
        response_type_name='GenerateOptimizedManifestResponse',
        supports_download=False,
    )
