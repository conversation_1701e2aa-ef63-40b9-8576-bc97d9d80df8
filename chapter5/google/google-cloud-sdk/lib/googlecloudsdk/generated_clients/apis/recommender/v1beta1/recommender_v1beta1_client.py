"""Generated client library for recommender version v1beta1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.recommender.v1beta1 import recommender_v1beta1_messages as messages


class RecommenderV1beta1(base_api.BaseApiClient):
  """Generated client library for service recommender version v1beta1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://recommender.googleapis.com/'
  MTLS_BASE_URL = 'https://recommender.mtls.googleapis.com/'

  _PACKAGE = 'recommender'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1beta1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'RecommenderV1beta1'
  _URL_VERSION = 'v1beta1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new recommender handle."""
    url = url or self.BASE_URL
    super(RecommenderV1beta1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.billingAccounts_locations_insightTypes_insights = self.BillingAccountsLocationsInsightTypesInsightsService(self)
    self.billingAccounts_locations_insightTypes = self.BillingAccountsLocationsInsightTypesService(self)
    self.billingAccounts_locations_recommenders_recommendations = self.BillingAccountsLocationsRecommendersRecommendationsService(self)
    self.billingAccounts_locations_recommenders = self.BillingAccountsLocationsRecommendersService(self)
    self.billingAccounts_locations = self.BillingAccountsLocationsService(self)
    self.billingAccounts = self.BillingAccountsService(self)
    self.folders_locations_insightTypes_insights = self.FoldersLocationsInsightTypesInsightsService(self)
    self.folders_locations_insightTypes = self.FoldersLocationsInsightTypesService(self)
    self.folders_locations_recommenders_recommendations = self.FoldersLocationsRecommendersRecommendationsService(self)
    self.folders_locations_recommenders = self.FoldersLocationsRecommendersService(self)
    self.folders_locations = self.FoldersLocationsService(self)
    self.folders = self.FoldersService(self)
    self.insightTypes = self.InsightTypesService(self)
    self.organizations_locations_insightTypes_insights = self.OrganizationsLocationsInsightTypesInsightsService(self)
    self.organizations_locations_insightTypes = self.OrganizationsLocationsInsightTypesService(self)
    self.organizations_locations_recommenders_recommendations = self.OrganizationsLocationsRecommendersRecommendationsService(self)
    self.organizations_locations_recommenders = self.OrganizationsLocationsRecommendersService(self)
    self.organizations_locations = self.OrganizationsLocationsService(self)
    self.organizations = self.OrganizationsService(self)
    self.projects_locations_insightTypes_insights = self.ProjectsLocationsInsightTypesInsightsService(self)
    self.projects_locations_insightTypes = self.ProjectsLocationsInsightTypesService(self)
    self.projects_locations_recommenders_recommendations = self.ProjectsLocationsRecommendersRecommendationsService(self)
    self.projects_locations_recommenders = self.ProjectsLocationsRecommendersService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)
    self.recommenders = self.RecommendersService(self)

  class BillingAccountsLocationsInsightTypesInsightsService(base_api.BaseApiService):
    """Service class for the billingAccounts_locations_insightTypes_insights resource."""

    _NAME = 'billingAccounts_locations_insightTypes_insights'

    def __init__(self, client):
      super(RecommenderV1beta1.BillingAccountsLocationsInsightTypesInsightsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the requested insight. Requires the recommender.*.get IAM permission for the specified insight type.

      Args:
        request: (RecommenderBillingAccountsLocationsInsightTypesInsightsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Insight) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/billingAccounts/{billingAccountsId}/locations/{locationsId}/insightTypes/{insightTypesId}/insights/{insightsId}',
        http_method='GET',
        method_id='recommender.billingAccounts.locations.insightTypes.insights.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='RecommenderBillingAccountsLocationsInsightTypesInsightsGetRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Insight',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists insights for the specified Cloud Resource. Requires the recommender.*.list IAM permission for the specified insight type.

      Args:
        request: (RecommenderBillingAccountsLocationsInsightTypesInsightsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1ListInsightsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/billingAccounts/{billingAccountsId}/locations/{locationsId}/insightTypes/{insightTypesId}/insights',
        http_method='GET',
        method_id='recommender.billingAccounts.locations.insightTypes.insights.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/insights',
        request_field='',
        request_type_name='RecommenderBillingAccountsLocationsInsightTypesInsightsListRequest',
        response_type_name='GoogleCloudRecommenderV1beta1ListInsightsResponse',
        supports_download=False,
    )

    def MarkAccepted(self, request, global_params=None):
      r"""Marks the Insight State as Accepted. Users can use this method to indicate to the Recommender API that they have applied some action based on the insight. This stops the insight content from being updated. MarkInsightAccepted can be applied to insights in ACTIVE state. Requires the recommender.*.update IAM permission for the specified insight.

      Args:
        request: (RecommenderBillingAccountsLocationsInsightTypesInsightsMarkAcceptedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Insight) The response message.
      """
      config = self.GetMethodConfig('MarkAccepted')
      return self._RunMethod(
          config, request, global_params=global_params)

    MarkAccepted.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/billingAccounts/{billingAccountsId}/locations/{locationsId}/insightTypes/{insightTypesId}/insights/{insightsId}:markAccepted',
        http_method='POST',
        method_id='recommender.billingAccounts.locations.insightTypes.insights.markAccepted',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:markAccepted',
        request_field='googleCloudRecommenderV1beta1MarkInsightAcceptedRequest',
        request_type_name='RecommenderBillingAccountsLocationsInsightTypesInsightsMarkAcceptedRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Insight',
        supports_download=False,
    )

  class BillingAccountsLocationsInsightTypesService(base_api.BaseApiService):
    """Service class for the billingAccounts_locations_insightTypes resource."""

    _NAME = 'billingAccounts_locations_insightTypes'

    def __init__(self, client):
      super(RecommenderV1beta1.BillingAccountsLocationsInsightTypesService, self).__init__(client)
      self._upload_configs = {
          }

    def GetConfig(self, request, global_params=None):
      r"""Gets the requested InsightTypeConfig. There is only one instance of the config for each InsightType.

      Args:
        request: (RecommenderBillingAccountsLocationsInsightTypesGetConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1InsightTypeConfig) The response message.
      """
      config = self.GetMethodConfig('GetConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/billingAccounts/{billingAccountsId}/locations/{locationsId}/insightTypes/{insightTypesId}/config',
        http_method='GET',
        method_id='recommender.billingAccounts.locations.insightTypes.getConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='RecommenderBillingAccountsLocationsInsightTypesGetConfigRequest',
        response_type_name='GoogleCloudRecommenderV1beta1InsightTypeConfig',
        supports_download=False,
    )

    def UpdateConfig(self, request, global_params=None):
      r"""Updates an InsightTypeConfig change. This will create a new revision of the config.

      Args:
        request: (RecommenderBillingAccountsLocationsInsightTypesUpdateConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1InsightTypeConfig) The response message.
      """
      config = self.GetMethodConfig('UpdateConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/billingAccounts/{billingAccountsId}/locations/{locationsId}/insightTypes/{insightTypesId}/config',
        http_method='PATCH',
        method_id='recommender.billingAccounts.locations.insightTypes.updateConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='googleCloudRecommenderV1beta1InsightTypeConfig',
        request_type_name='RecommenderBillingAccountsLocationsInsightTypesUpdateConfigRequest',
        response_type_name='GoogleCloudRecommenderV1beta1InsightTypeConfig',
        supports_download=False,
    )

  class BillingAccountsLocationsRecommendersRecommendationsService(base_api.BaseApiService):
    """Service class for the billingAccounts_locations_recommenders_recommendations resource."""

    _NAME = 'billingAccounts_locations_recommenders_recommendations'

    def __init__(self, client):
      super(RecommenderV1beta1.BillingAccountsLocationsRecommendersRecommendationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the requested recommendation. Requires the recommender.*.get IAM permission for the specified recommender.

      Args:
        request: (RecommenderBillingAccountsLocationsRecommendersRecommendationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Recommendation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/billingAccounts/{billingAccountsId}/locations/{locationsId}/recommenders/{recommendersId}/recommendations/{recommendationsId}',
        http_method='GET',
        method_id='recommender.billingAccounts.locations.recommenders.recommendations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='RecommenderBillingAccountsLocationsRecommendersRecommendationsGetRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Recommendation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists recommendations for the specified Cloud Resource. Requires the recommender.*.list IAM permission for the specified recommender.

      Args:
        request: (RecommenderBillingAccountsLocationsRecommendersRecommendationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1ListRecommendationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/billingAccounts/{billingAccountsId}/locations/{locationsId}/recommenders/{recommendersId}/recommendations',
        http_method='GET',
        method_id='recommender.billingAccounts.locations.recommenders.recommendations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/recommendations',
        request_field='',
        request_type_name='RecommenderBillingAccountsLocationsRecommendersRecommendationsListRequest',
        response_type_name='GoogleCloudRecommenderV1beta1ListRecommendationsResponse',
        supports_download=False,
    )

    def MarkClaimed(self, request, global_params=None):
      r"""Marks the Recommendation State as Claimed. Users can use this method to indicate to the Recommender API that they are starting to apply the recommendation themselves. This stops the recommendation content from being updated. Associated insights are frozen and placed in the ACCEPTED state. MarkRecommendationClaimed can be applied to recommendations in CLAIMED or ACTIVE state. Requires the recommender.*.update IAM permission for the specified recommender.

      Args:
        request: (RecommenderBillingAccountsLocationsRecommendersRecommendationsMarkClaimedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Recommendation) The response message.
      """
      config = self.GetMethodConfig('MarkClaimed')
      return self._RunMethod(
          config, request, global_params=global_params)

    MarkClaimed.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/billingAccounts/{billingAccountsId}/locations/{locationsId}/recommenders/{recommendersId}/recommendations/{recommendationsId}:markClaimed',
        http_method='POST',
        method_id='recommender.billingAccounts.locations.recommenders.recommendations.markClaimed',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:markClaimed',
        request_field='googleCloudRecommenderV1beta1MarkRecommendationClaimedRequest',
        request_type_name='RecommenderBillingAccountsLocationsRecommendersRecommendationsMarkClaimedRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Recommendation',
        supports_download=False,
    )

    def MarkDismissed(self, request, global_params=None):
      r"""Mark the Recommendation State as Dismissed. Users can use this method to indicate to the Recommender API that an ACTIVE recommendation has to be marked back as DISMISSED. MarkRecommendationDismissed can be applied to recommendations in ACTIVE state. Requires the recommender.*.update IAM permission for the specified recommender.

      Args:
        request: (RecommenderBillingAccountsLocationsRecommendersRecommendationsMarkDismissedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Recommendation) The response message.
      """
      config = self.GetMethodConfig('MarkDismissed')
      return self._RunMethod(
          config, request, global_params=global_params)

    MarkDismissed.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/billingAccounts/{billingAccountsId}/locations/{locationsId}/recommenders/{recommendersId}/recommendations/{recommendationsId}:markDismissed',
        http_method='POST',
        method_id='recommender.billingAccounts.locations.recommenders.recommendations.markDismissed',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:markDismissed',
        request_field='googleCloudRecommenderV1beta1MarkRecommendationDismissedRequest',
        request_type_name='RecommenderBillingAccountsLocationsRecommendersRecommendationsMarkDismissedRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Recommendation',
        supports_download=False,
    )

    def MarkFailed(self, request, global_params=None):
      r"""Marks the Recommendation State as Failed. Users can use this method to indicate to the Recommender API that they have applied the recommendation themselves, and the operation failed. This stops the recommendation content from being updated. Associated insights are frozen and placed in the ACCEPTED state. MarkRecommendationFailed can be applied to recommendations in ACTIVE, CLAIMED, SUCCEEDED, or FAILED state. Requires the recommender.*.update IAM permission for the specified recommender.

      Args:
        request: (RecommenderBillingAccountsLocationsRecommendersRecommendationsMarkFailedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Recommendation) The response message.
      """
      config = self.GetMethodConfig('MarkFailed')
      return self._RunMethod(
          config, request, global_params=global_params)

    MarkFailed.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/billingAccounts/{billingAccountsId}/locations/{locationsId}/recommenders/{recommendersId}/recommendations/{recommendationsId}:markFailed',
        http_method='POST',
        method_id='recommender.billingAccounts.locations.recommenders.recommendations.markFailed',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:markFailed',
        request_field='googleCloudRecommenderV1beta1MarkRecommendationFailedRequest',
        request_type_name='RecommenderBillingAccountsLocationsRecommendersRecommendationsMarkFailedRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Recommendation',
        supports_download=False,
    )

    def MarkSucceeded(self, request, global_params=None):
      r"""Marks the Recommendation State as Succeeded. Users can use this method to indicate to the Recommender API that they have applied the recommendation themselves, and the operation was successful. This stops the recommendation content from being updated. Associated insights are frozen and placed in the ACCEPTED state. MarkRecommendationSucceeded can be applied to recommendations in ACTIVE, CLAIMED, SUCCEEDED, or FAILED state. Requires the recommender.*.update IAM permission for the specified recommender.

      Args:
        request: (RecommenderBillingAccountsLocationsRecommendersRecommendationsMarkSucceededRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Recommendation) The response message.
      """
      config = self.GetMethodConfig('MarkSucceeded')
      return self._RunMethod(
          config, request, global_params=global_params)

    MarkSucceeded.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/billingAccounts/{billingAccountsId}/locations/{locationsId}/recommenders/{recommendersId}/recommendations/{recommendationsId}:markSucceeded',
        http_method='POST',
        method_id='recommender.billingAccounts.locations.recommenders.recommendations.markSucceeded',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:markSucceeded',
        request_field='googleCloudRecommenderV1beta1MarkRecommendationSucceededRequest',
        request_type_name='RecommenderBillingAccountsLocationsRecommendersRecommendationsMarkSucceededRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Recommendation',
        supports_download=False,
    )

  class BillingAccountsLocationsRecommendersService(base_api.BaseApiService):
    """Service class for the billingAccounts_locations_recommenders resource."""

    _NAME = 'billingAccounts_locations_recommenders'

    def __init__(self, client):
      super(RecommenderV1beta1.BillingAccountsLocationsRecommendersService, self).__init__(client)
      self._upload_configs = {
          }

    def GetConfig(self, request, global_params=None):
      r"""Gets the requested Recommender Config. There is only one instance of the config for each Recommender.

      Args:
        request: (RecommenderBillingAccountsLocationsRecommendersGetConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1RecommenderConfig) The response message.
      """
      config = self.GetMethodConfig('GetConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/billingAccounts/{billingAccountsId}/locations/{locationsId}/recommenders/{recommendersId}/config',
        http_method='GET',
        method_id='recommender.billingAccounts.locations.recommenders.getConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='RecommenderBillingAccountsLocationsRecommendersGetConfigRequest',
        response_type_name='GoogleCloudRecommenderV1beta1RecommenderConfig',
        supports_download=False,
    )

    def UpdateConfig(self, request, global_params=None):
      r"""Updates a Recommender Config. This will create a new revision of the config.

      Args:
        request: (RecommenderBillingAccountsLocationsRecommendersUpdateConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1RecommenderConfig) The response message.
      """
      config = self.GetMethodConfig('UpdateConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/billingAccounts/{billingAccountsId}/locations/{locationsId}/recommenders/{recommendersId}/config',
        http_method='PATCH',
        method_id='recommender.billingAccounts.locations.recommenders.updateConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='googleCloudRecommenderV1beta1RecommenderConfig',
        request_type_name='RecommenderBillingAccountsLocationsRecommendersUpdateConfigRequest',
        response_type_name='GoogleCloudRecommenderV1beta1RecommenderConfig',
        supports_download=False,
    )

  class BillingAccountsLocationsService(base_api.BaseApiService):
    """Service class for the billingAccounts_locations resource."""

    _NAME = 'billingAccounts_locations'

    def __init__(self, client):
      super(RecommenderV1beta1.BillingAccountsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists locations with recommendations or insights.

      Args:
        request: (RecommenderBillingAccountsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudLocationListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/billingAccounts/{billingAccountsId}/locations',
        http_method='GET',
        method_id='recommender.billingAccounts.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+name}/locations',
        request_field='',
        request_type_name='RecommenderBillingAccountsLocationsListRequest',
        response_type_name='GoogleCloudLocationListLocationsResponse',
        supports_download=False,
    )

  class BillingAccountsService(base_api.BaseApiService):
    """Service class for the billingAccounts resource."""

    _NAME = 'billingAccounts'

    def __init__(self, client):
      super(RecommenderV1beta1.BillingAccountsService, self).__init__(client)
      self._upload_configs = {
          }

  class FoldersLocationsInsightTypesInsightsService(base_api.BaseApiService):
    """Service class for the folders_locations_insightTypes_insights resource."""

    _NAME = 'folders_locations_insightTypes_insights'

    def __init__(self, client):
      super(RecommenderV1beta1.FoldersLocationsInsightTypesInsightsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the requested insight. Requires the recommender.*.get IAM permission for the specified insight type.

      Args:
        request: (RecommenderFoldersLocationsInsightTypesInsightsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Insight) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/folders/{foldersId}/locations/{locationsId}/insightTypes/{insightTypesId}/insights/{insightsId}',
        http_method='GET',
        method_id='recommender.folders.locations.insightTypes.insights.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='RecommenderFoldersLocationsInsightTypesInsightsGetRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Insight',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists insights for the specified Cloud Resource. Requires the recommender.*.list IAM permission for the specified insight type.

      Args:
        request: (RecommenderFoldersLocationsInsightTypesInsightsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1ListInsightsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/folders/{foldersId}/locations/{locationsId}/insightTypes/{insightTypesId}/insights',
        http_method='GET',
        method_id='recommender.folders.locations.insightTypes.insights.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/insights',
        request_field='',
        request_type_name='RecommenderFoldersLocationsInsightTypesInsightsListRequest',
        response_type_name='GoogleCloudRecommenderV1beta1ListInsightsResponse',
        supports_download=False,
    )

    def MarkAccepted(self, request, global_params=None):
      r"""Marks the Insight State as Accepted. Users can use this method to indicate to the Recommender API that they have applied some action based on the insight. This stops the insight content from being updated. MarkInsightAccepted can be applied to insights in ACTIVE state. Requires the recommender.*.update IAM permission for the specified insight.

      Args:
        request: (RecommenderFoldersLocationsInsightTypesInsightsMarkAcceptedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Insight) The response message.
      """
      config = self.GetMethodConfig('MarkAccepted')
      return self._RunMethod(
          config, request, global_params=global_params)

    MarkAccepted.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/folders/{foldersId}/locations/{locationsId}/insightTypes/{insightTypesId}/insights/{insightsId}:markAccepted',
        http_method='POST',
        method_id='recommender.folders.locations.insightTypes.insights.markAccepted',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:markAccepted',
        request_field='googleCloudRecommenderV1beta1MarkInsightAcceptedRequest',
        request_type_name='RecommenderFoldersLocationsInsightTypesInsightsMarkAcceptedRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Insight',
        supports_download=False,
    )

  class FoldersLocationsInsightTypesService(base_api.BaseApiService):
    """Service class for the folders_locations_insightTypes resource."""

    _NAME = 'folders_locations_insightTypes'

    def __init__(self, client):
      super(RecommenderV1beta1.FoldersLocationsInsightTypesService, self).__init__(client)
      self._upload_configs = {
          }

  class FoldersLocationsRecommendersRecommendationsService(base_api.BaseApiService):
    """Service class for the folders_locations_recommenders_recommendations resource."""

    _NAME = 'folders_locations_recommenders_recommendations'

    def __init__(self, client):
      super(RecommenderV1beta1.FoldersLocationsRecommendersRecommendationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the requested recommendation. Requires the recommender.*.get IAM permission for the specified recommender.

      Args:
        request: (RecommenderFoldersLocationsRecommendersRecommendationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Recommendation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/folders/{foldersId}/locations/{locationsId}/recommenders/{recommendersId}/recommendations/{recommendationsId}',
        http_method='GET',
        method_id='recommender.folders.locations.recommenders.recommendations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='RecommenderFoldersLocationsRecommendersRecommendationsGetRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Recommendation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists recommendations for the specified Cloud Resource. Requires the recommender.*.list IAM permission for the specified recommender.

      Args:
        request: (RecommenderFoldersLocationsRecommendersRecommendationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1ListRecommendationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/folders/{foldersId}/locations/{locationsId}/recommenders/{recommendersId}/recommendations',
        http_method='GET',
        method_id='recommender.folders.locations.recommenders.recommendations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/recommendations',
        request_field='',
        request_type_name='RecommenderFoldersLocationsRecommendersRecommendationsListRequest',
        response_type_name='GoogleCloudRecommenderV1beta1ListRecommendationsResponse',
        supports_download=False,
    )

    def MarkClaimed(self, request, global_params=None):
      r"""Marks the Recommendation State as Claimed. Users can use this method to indicate to the Recommender API that they are starting to apply the recommendation themselves. This stops the recommendation content from being updated. Associated insights are frozen and placed in the ACCEPTED state. MarkRecommendationClaimed can be applied to recommendations in CLAIMED or ACTIVE state. Requires the recommender.*.update IAM permission for the specified recommender.

      Args:
        request: (RecommenderFoldersLocationsRecommendersRecommendationsMarkClaimedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Recommendation) The response message.
      """
      config = self.GetMethodConfig('MarkClaimed')
      return self._RunMethod(
          config, request, global_params=global_params)

    MarkClaimed.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/folders/{foldersId}/locations/{locationsId}/recommenders/{recommendersId}/recommendations/{recommendationsId}:markClaimed',
        http_method='POST',
        method_id='recommender.folders.locations.recommenders.recommendations.markClaimed',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:markClaimed',
        request_field='googleCloudRecommenderV1beta1MarkRecommendationClaimedRequest',
        request_type_name='RecommenderFoldersLocationsRecommendersRecommendationsMarkClaimedRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Recommendation',
        supports_download=False,
    )

    def MarkDismissed(self, request, global_params=None):
      r"""Mark the Recommendation State as Dismissed. Users can use this method to indicate to the Recommender API that an ACTIVE recommendation has to be marked back as DISMISSED. MarkRecommendationDismissed can be applied to recommendations in ACTIVE state. Requires the recommender.*.update IAM permission for the specified recommender.

      Args:
        request: (RecommenderFoldersLocationsRecommendersRecommendationsMarkDismissedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Recommendation) The response message.
      """
      config = self.GetMethodConfig('MarkDismissed')
      return self._RunMethod(
          config, request, global_params=global_params)

    MarkDismissed.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/folders/{foldersId}/locations/{locationsId}/recommenders/{recommendersId}/recommendations/{recommendationsId}:markDismissed',
        http_method='POST',
        method_id='recommender.folders.locations.recommenders.recommendations.markDismissed',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:markDismissed',
        request_field='googleCloudRecommenderV1beta1MarkRecommendationDismissedRequest',
        request_type_name='RecommenderFoldersLocationsRecommendersRecommendationsMarkDismissedRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Recommendation',
        supports_download=False,
    )

    def MarkFailed(self, request, global_params=None):
      r"""Marks the Recommendation State as Failed. Users can use this method to indicate to the Recommender API that they have applied the recommendation themselves, and the operation failed. This stops the recommendation content from being updated. Associated insights are frozen and placed in the ACCEPTED state. MarkRecommendationFailed can be applied to recommendations in ACTIVE, CLAIMED, SUCCEEDED, or FAILED state. Requires the recommender.*.update IAM permission for the specified recommender.

      Args:
        request: (RecommenderFoldersLocationsRecommendersRecommendationsMarkFailedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Recommendation) The response message.
      """
      config = self.GetMethodConfig('MarkFailed')
      return self._RunMethod(
          config, request, global_params=global_params)

    MarkFailed.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/folders/{foldersId}/locations/{locationsId}/recommenders/{recommendersId}/recommendations/{recommendationsId}:markFailed',
        http_method='POST',
        method_id='recommender.folders.locations.recommenders.recommendations.markFailed',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:markFailed',
        request_field='googleCloudRecommenderV1beta1MarkRecommendationFailedRequest',
        request_type_name='RecommenderFoldersLocationsRecommendersRecommendationsMarkFailedRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Recommendation',
        supports_download=False,
    )

    def MarkSucceeded(self, request, global_params=None):
      r"""Marks the Recommendation State as Succeeded. Users can use this method to indicate to the Recommender API that they have applied the recommendation themselves, and the operation was successful. This stops the recommendation content from being updated. Associated insights are frozen and placed in the ACCEPTED state. MarkRecommendationSucceeded can be applied to recommendations in ACTIVE, CLAIMED, SUCCEEDED, or FAILED state. Requires the recommender.*.update IAM permission for the specified recommender.

      Args:
        request: (RecommenderFoldersLocationsRecommendersRecommendationsMarkSucceededRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Recommendation) The response message.
      """
      config = self.GetMethodConfig('MarkSucceeded')
      return self._RunMethod(
          config, request, global_params=global_params)

    MarkSucceeded.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/folders/{foldersId}/locations/{locationsId}/recommenders/{recommendersId}/recommendations/{recommendationsId}:markSucceeded',
        http_method='POST',
        method_id='recommender.folders.locations.recommenders.recommendations.markSucceeded',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:markSucceeded',
        request_field='googleCloudRecommenderV1beta1MarkRecommendationSucceededRequest',
        request_type_name='RecommenderFoldersLocationsRecommendersRecommendationsMarkSucceededRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Recommendation',
        supports_download=False,
    )

  class FoldersLocationsRecommendersService(base_api.BaseApiService):
    """Service class for the folders_locations_recommenders resource."""

    _NAME = 'folders_locations_recommenders'

    def __init__(self, client):
      super(RecommenderV1beta1.FoldersLocationsRecommendersService, self).__init__(client)
      self._upload_configs = {
          }

  class FoldersLocationsService(base_api.BaseApiService):
    """Service class for the folders_locations resource."""

    _NAME = 'folders_locations'

    def __init__(self, client):
      super(RecommenderV1beta1.FoldersLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists locations with recommendations or insights.

      Args:
        request: (RecommenderFoldersLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudLocationListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/folders/{foldersId}/locations',
        http_method='GET',
        method_id='recommender.folders.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+name}/locations',
        request_field='',
        request_type_name='RecommenderFoldersLocationsListRequest',
        response_type_name='GoogleCloudLocationListLocationsResponse',
        supports_download=False,
    )

  class FoldersService(base_api.BaseApiService):
    """Service class for the folders resource."""

    _NAME = 'folders'

    def __init__(self, client):
      super(RecommenderV1beta1.FoldersService, self).__init__(client)
      self._upload_configs = {
          }

  class InsightTypesService(base_api.BaseApiService):
    """Service class for the insightTypes resource."""

    _NAME = 'insightTypes'

    def __init__(self, client):
      super(RecommenderV1beta1.InsightTypesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists available InsightTypes. No IAM permissions are required.

      Args:
        request: (RecommenderInsightTypesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1ListInsightTypesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='recommender.insightTypes.list',
        ordered_params=[],
        path_params=[],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1beta1/insightTypes',
        request_field='',
        request_type_name='RecommenderInsightTypesListRequest',
        response_type_name='GoogleCloudRecommenderV1beta1ListInsightTypesResponse',
        supports_download=False,
    )

  class OrganizationsLocationsInsightTypesInsightsService(base_api.BaseApiService):
    """Service class for the organizations_locations_insightTypes_insights resource."""

    _NAME = 'organizations_locations_insightTypes_insights'

    def __init__(self, client):
      super(RecommenderV1beta1.OrganizationsLocationsInsightTypesInsightsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the requested insight. Requires the recommender.*.get IAM permission for the specified insight type.

      Args:
        request: (RecommenderOrganizationsLocationsInsightTypesInsightsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Insight) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/organizations/{organizationsId}/locations/{locationsId}/insightTypes/{insightTypesId}/insights/{insightsId}',
        http_method='GET',
        method_id='recommender.organizations.locations.insightTypes.insights.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='RecommenderOrganizationsLocationsInsightTypesInsightsGetRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Insight',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists insights for the specified Cloud Resource. Requires the recommender.*.list IAM permission for the specified insight type.

      Args:
        request: (RecommenderOrganizationsLocationsInsightTypesInsightsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1ListInsightsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/organizations/{organizationsId}/locations/{locationsId}/insightTypes/{insightTypesId}/insights',
        http_method='GET',
        method_id='recommender.organizations.locations.insightTypes.insights.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/insights',
        request_field='',
        request_type_name='RecommenderOrganizationsLocationsInsightTypesInsightsListRequest',
        response_type_name='GoogleCloudRecommenderV1beta1ListInsightsResponse',
        supports_download=False,
    )

    def MarkAccepted(self, request, global_params=None):
      r"""Marks the Insight State as Accepted. Users can use this method to indicate to the Recommender API that they have applied some action based on the insight. This stops the insight content from being updated. MarkInsightAccepted can be applied to insights in ACTIVE state. Requires the recommender.*.update IAM permission for the specified insight.

      Args:
        request: (RecommenderOrganizationsLocationsInsightTypesInsightsMarkAcceptedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Insight) The response message.
      """
      config = self.GetMethodConfig('MarkAccepted')
      return self._RunMethod(
          config, request, global_params=global_params)

    MarkAccepted.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/organizations/{organizationsId}/locations/{locationsId}/insightTypes/{insightTypesId}/insights/{insightsId}:markAccepted',
        http_method='POST',
        method_id='recommender.organizations.locations.insightTypes.insights.markAccepted',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:markAccepted',
        request_field='googleCloudRecommenderV1beta1MarkInsightAcceptedRequest',
        request_type_name='RecommenderOrganizationsLocationsInsightTypesInsightsMarkAcceptedRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Insight',
        supports_download=False,
    )

  class OrganizationsLocationsInsightTypesService(base_api.BaseApiService):
    """Service class for the organizations_locations_insightTypes resource."""

    _NAME = 'organizations_locations_insightTypes'

    def __init__(self, client):
      super(RecommenderV1beta1.OrganizationsLocationsInsightTypesService, self).__init__(client)
      self._upload_configs = {
          }

    def GetConfig(self, request, global_params=None):
      r"""Gets the requested InsightTypeConfig. There is only one instance of the config for each InsightType.

      Args:
        request: (RecommenderOrganizationsLocationsInsightTypesGetConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1InsightTypeConfig) The response message.
      """
      config = self.GetMethodConfig('GetConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/organizations/{organizationsId}/locations/{locationsId}/insightTypes/{insightTypesId}/config',
        http_method='GET',
        method_id='recommender.organizations.locations.insightTypes.getConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='RecommenderOrganizationsLocationsInsightTypesGetConfigRequest',
        response_type_name='GoogleCloudRecommenderV1beta1InsightTypeConfig',
        supports_download=False,
    )

    def UpdateConfig(self, request, global_params=None):
      r"""Updates an InsightTypeConfig change. This will create a new revision of the config.

      Args:
        request: (RecommenderOrganizationsLocationsInsightTypesUpdateConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1InsightTypeConfig) The response message.
      """
      config = self.GetMethodConfig('UpdateConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/organizations/{organizationsId}/locations/{locationsId}/insightTypes/{insightTypesId}/config',
        http_method='PATCH',
        method_id='recommender.organizations.locations.insightTypes.updateConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='googleCloudRecommenderV1beta1InsightTypeConfig',
        request_type_name='RecommenderOrganizationsLocationsInsightTypesUpdateConfigRequest',
        response_type_name='GoogleCloudRecommenderV1beta1InsightTypeConfig',
        supports_download=False,
    )

  class OrganizationsLocationsRecommendersRecommendationsService(base_api.BaseApiService):
    """Service class for the organizations_locations_recommenders_recommendations resource."""

    _NAME = 'organizations_locations_recommenders_recommendations'

    def __init__(self, client):
      super(RecommenderV1beta1.OrganizationsLocationsRecommendersRecommendationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the requested recommendation. Requires the recommender.*.get IAM permission for the specified recommender.

      Args:
        request: (RecommenderOrganizationsLocationsRecommendersRecommendationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Recommendation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/organizations/{organizationsId}/locations/{locationsId}/recommenders/{recommendersId}/recommendations/{recommendationsId}',
        http_method='GET',
        method_id='recommender.organizations.locations.recommenders.recommendations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='RecommenderOrganizationsLocationsRecommendersRecommendationsGetRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Recommendation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists recommendations for the specified Cloud Resource. Requires the recommender.*.list IAM permission for the specified recommender.

      Args:
        request: (RecommenderOrganizationsLocationsRecommendersRecommendationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1ListRecommendationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/organizations/{organizationsId}/locations/{locationsId}/recommenders/{recommendersId}/recommendations',
        http_method='GET',
        method_id='recommender.organizations.locations.recommenders.recommendations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/recommendations',
        request_field='',
        request_type_name='RecommenderOrganizationsLocationsRecommendersRecommendationsListRequest',
        response_type_name='GoogleCloudRecommenderV1beta1ListRecommendationsResponse',
        supports_download=False,
    )

    def MarkClaimed(self, request, global_params=None):
      r"""Marks the Recommendation State as Claimed. Users can use this method to indicate to the Recommender API that they are starting to apply the recommendation themselves. This stops the recommendation content from being updated. Associated insights are frozen and placed in the ACCEPTED state. MarkRecommendationClaimed can be applied to recommendations in CLAIMED or ACTIVE state. Requires the recommender.*.update IAM permission for the specified recommender.

      Args:
        request: (RecommenderOrganizationsLocationsRecommendersRecommendationsMarkClaimedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Recommendation) The response message.
      """
      config = self.GetMethodConfig('MarkClaimed')
      return self._RunMethod(
          config, request, global_params=global_params)

    MarkClaimed.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/organizations/{organizationsId}/locations/{locationsId}/recommenders/{recommendersId}/recommendations/{recommendationsId}:markClaimed',
        http_method='POST',
        method_id='recommender.organizations.locations.recommenders.recommendations.markClaimed',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:markClaimed',
        request_field='googleCloudRecommenderV1beta1MarkRecommendationClaimedRequest',
        request_type_name='RecommenderOrganizationsLocationsRecommendersRecommendationsMarkClaimedRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Recommendation',
        supports_download=False,
    )

    def MarkDismissed(self, request, global_params=None):
      r"""Mark the Recommendation State as Dismissed. Users can use this method to indicate to the Recommender API that an ACTIVE recommendation has to be marked back as DISMISSED. MarkRecommendationDismissed can be applied to recommendations in ACTIVE state. Requires the recommender.*.update IAM permission for the specified recommender.

      Args:
        request: (RecommenderOrganizationsLocationsRecommendersRecommendationsMarkDismissedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Recommendation) The response message.
      """
      config = self.GetMethodConfig('MarkDismissed')
      return self._RunMethod(
          config, request, global_params=global_params)

    MarkDismissed.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/organizations/{organizationsId}/locations/{locationsId}/recommenders/{recommendersId}/recommendations/{recommendationsId}:markDismissed',
        http_method='POST',
        method_id='recommender.organizations.locations.recommenders.recommendations.markDismissed',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:markDismissed',
        request_field='googleCloudRecommenderV1beta1MarkRecommendationDismissedRequest',
        request_type_name='RecommenderOrganizationsLocationsRecommendersRecommendationsMarkDismissedRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Recommendation',
        supports_download=False,
    )

    def MarkFailed(self, request, global_params=None):
      r"""Marks the Recommendation State as Failed. Users can use this method to indicate to the Recommender API that they have applied the recommendation themselves, and the operation failed. This stops the recommendation content from being updated. Associated insights are frozen and placed in the ACCEPTED state. MarkRecommendationFailed can be applied to recommendations in ACTIVE, CLAIMED, SUCCEEDED, or FAILED state. Requires the recommender.*.update IAM permission for the specified recommender.

      Args:
        request: (RecommenderOrganizationsLocationsRecommendersRecommendationsMarkFailedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Recommendation) The response message.
      """
      config = self.GetMethodConfig('MarkFailed')
      return self._RunMethod(
          config, request, global_params=global_params)

    MarkFailed.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/organizations/{organizationsId}/locations/{locationsId}/recommenders/{recommendersId}/recommendations/{recommendationsId}:markFailed',
        http_method='POST',
        method_id='recommender.organizations.locations.recommenders.recommendations.markFailed',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:markFailed',
        request_field='googleCloudRecommenderV1beta1MarkRecommendationFailedRequest',
        request_type_name='RecommenderOrganizationsLocationsRecommendersRecommendationsMarkFailedRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Recommendation',
        supports_download=False,
    )

    def MarkSucceeded(self, request, global_params=None):
      r"""Marks the Recommendation State as Succeeded. Users can use this method to indicate to the Recommender API that they have applied the recommendation themselves, and the operation was successful. This stops the recommendation content from being updated. Associated insights are frozen and placed in the ACCEPTED state. MarkRecommendationSucceeded can be applied to recommendations in ACTIVE, CLAIMED, SUCCEEDED, or FAILED state. Requires the recommender.*.update IAM permission for the specified recommender.

      Args:
        request: (RecommenderOrganizationsLocationsRecommendersRecommendationsMarkSucceededRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Recommendation) The response message.
      """
      config = self.GetMethodConfig('MarkSucceeded')
      return self._RunMethod(
          config, request, global_params=global_params)

    MarkSucceeded.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/organizations/{organizationsId}/locations/{locationsId}/recommenders/{recommendersId}/recommendations/{recommendationsId}:markSucceeded',
        http_method='POST',
        method_id='recommender.organizations.locations.recommenders.recommendations.markSucceeded',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:markSucceeded',
        request_field='googleCloudRecommenderV1beta1MarkRecommendationSucceededRequest',
        request_type_name='RecommenderOrganizationsLocationsRecommendersRecommendationsMarkSucceededRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Recommendation',
        supports_download=False,
    )

  class OrganizationsLocationsRecommendersService(base_api.BaseApiService):
    """Service class for the organizations_locations_recommenders resource."""

    _NAME = 'organizations_locations_recommenders'

    def __init__(self, client):
      super(RecommenderV1beta1.OrganizationsLocationsRecommendersService, self).__init__(client)
      self._upload_configs = {
          }

    def GetConfig(self, request, global_params=None):
      r"""Gets the requested Recommender Config. There is only one instance of the config for each Recommender.

      Args:
        request: (RecommenderOrganizationsLocationsRecommendersGetConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1RecommenderConfig) The response message.
      """
      config = self.GetMethodConfig('GetConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/organizations/{organizationsId}/locations/{locationsId}/recommenders/{recommendersId}/config',
        http_method='GET',
        method_id='recommender.organizations.locations.recommenders.getConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='RecommenderOrganizationsLocationsRecommendersGetConfigRequest',
        response_type_name='GoogleCloudRecommenderV1beta1RecommenderConfig',
        supports_download=False,
    )

    def UpdateConfig(self, request, global_params=None):
      r"""Updates a Recommender Config. This will create a new revision of the config.

      Args:
        request: (RecommenderOrganizationsLocationsRecommendersUpdateConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1RecommenderConfig) The response message.
      """
      config = self.GetMethodConfig('UpdateConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/organizations/{organizationsId}/locations/{locationsId}/recommenders/{recommendersId}/config',
        http_method='PATCH',
        method_id='recommender.organizations.locations.recommenders.updateConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='googleCloudRecommenderV1beta1RecommenderConfig',
        request_type_name='RecommenderOrganizationsLocationsRecommendersUpdateConfigRequest',
        response_type_name='GoogleCloudRecommenderV1beta1RecommenderConfig',
        supports_download=False,
    )

  class OrganizationsLocationsService(base_api.BaseApiService):
    """Service class for the organizations_locations resource."""

    _NAME = 'organizations_locations'

    def __init__(self, client):
      super(RecommenderV1beta1.OrganizationsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists locations with recommendations or insights.

      Args:
        request: (RecommenderOrganizationsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudLocationListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/organizations/{organizationsId}/locations',
        http_method='GET',
        method_id='recommender.organizations.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+name}/locations',
        request_field='',
        request_type_name='RecommenderOrganizationsLocationsListRequest',
        response_type_name='GoogleCloudLocationListLocationsResponse',
        supports_download=False,
    )

  class OrganizationsService(base_api.BaseApiService):
    """Service class for the organizations resource."""

    _NAME = 'organizations'

    def __init__(self, client):
      super(RecommenderV1beta1.OrganizationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsInsightTypesInsightsService(base_api.BaseApiService):
    """Service class for the projects_locations_insightTypes_insights resource."""

    _NAME = 'projects_locations_insightTypes_insights'

    def __init__(self, client):
      super(RecommenderV1beta1.ProjectsLocationsInsightTypesInsightsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the requested insight. Requires the recommender.*.get IAM permission for the specified insight type.

      Args:
        request: (RecommenderProjectsLocationsInsightTypesInsightsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Insight) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/insightTypes/{insightTypesId}/insights/{insightsId}',
        http_method='GET',
        method_id='recommender.projects.locations.insightTypes.insights.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='RecommenderProjectsLocationsInsightTypesInsightsGetRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Insight',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists insights for the specified Cloud Resource. Requires the recommender.*.list IAM permission for the specified insight type.

      Args:
        request: (RecommenderProjectsLocationsInsightTypesInsightsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1ListInsightsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/insightTypes/{insightTypesId}/insights',
        http_method='GET',
        method_id='recommender.projects.locations.insightTypes.insights.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/insights',
        request_field='',
        request_type_name='RecommenderProjectsLocationsInsightTypesInsightsListRequest',
        response_type_name='GoogleCloudRecommenderV1beta1ListInsightsResponse',
        supports_download=False,
    )

    def MarkAccepted(self, request, global_params=None):
      r"""Marks the Insight State as Accepted. Users can use this method to indicate to the Recommender API that they have applied some action based on the insight. This stops the insight content from being updated. MarkInsightAccepted can be applied to insights in ACTIVE state. Requires the recommender.*.update IAM permission for the specified insight.

      Args:
        request: (RecommenderProjectsLocationsInsightTypesInsightsMarkAcceptedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Insight) The response message.
      """
      config = self.GetMethodConfig('MarkAccepted')
      return self._RunMethod(
          config, request, global_params=global_params)

    MarkAccepted.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/insightTypes/{insightTypesId}/insights/{insightsId}:markAccepted',
        http_method='POST',
        method_id='recommender.projects.locations.insightTypes.insights.markAccepted',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:markAccepted',
        request_field='googleCloudRecommenderV1beta1MarkInsightAcceptedRequest',
        request_type_name='RecommenderProjectsLocationsInsightTypesInsightsMarkAcceptedRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Insight',
        supports_download=False,
    )

  class ProjectsLocationsInsightTypesService(base_api.BaseApiService):
    """Service class for the projects_locations_insightTypes resource."""

    _NAME = 'projects_locations_insightTypes'

    def __init__(self, client):
      super(RecommenderV1beta1.ProjectsLocationsInsightTypesService, self).__init__(client)
      self._upload_configs = {
          }

    def GetConfig(self, request, global_params=None):
      r"""Gets the requested InsightTypeConfig. There is only one instance of the config for each InsightType.

      Args:
        request: (RecommenderProjectsLocationsInsightTypesGetConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1InsightTypeConfig) The response message.
      """
      config = self.GetMethodConfig('GetConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/insightTypes/{insightTypesId}/config',
        http_method='GET',
        method_id='recommender.projects.locations.insightTypes.getConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='RecommenderProjectsLocationsInsightTypesGetConfigRequest',
        response_type_name='GoogleCloudRecommenderV1beta1InsightTypeConfig',
        supports_download=False,
    )

    def UpdateConfig(self, request, global_params=None):
      r"""Updates an InsightTypeConfig change. This will create a new revision of the config.

      Args:
        request: (RecommenderProjectsLocationsInsightTypesUpdateConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1InsightTypeConfig) The response message.
      """
      config = self.GetMethodConfig('UpdateConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/insightTypes/{insightTypesId}/config',
        http_method='PATCH',
        method_id='recommender.projects.locations.insightTypes.updateConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='googleCloudRecommenderV1beta1InsightTypeConfig',
        request_type_name='RecommenderProjectsLocationsInsightTypesUpdateConfigRequest',
        response_type_name='GoogleCloudRecommenderV1beta1InsightTypeConfig',
        supports_download=False,
    )

  class ProjectsLocationsRecommendersRecommendationsService(base_api.BaseApiService):
    """Service class for the projects_locations_recommenders_recommendations resource."""

    _NAME = 'projects_locations_recommenders_recommendations'

    def __init__(self, client):
      super(RecommenderV1beta1.ProjectsLocationsRecommendersRecommendationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the requested recommendation. Requires the recommender.*.get IAM permission for the specified recommender.

      Args:
        request: (RecommenderProjectsLocationsRecommendersRecommendationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Recommendation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/recommenders/{recommendersId}/recommendations/{recommendationsId}',
        http_method='GET',
        method_id='recommender.projects.locations.recommenders.recommendations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='RecommenderProjectsLocationsRecommendersRecommendationsGetRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Recommendation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists recommendations for the specified Cloud Resource. Requires the recommender.*.list IAM permission for the specified recommender.

      Args:
        request: (RecommenderProjectsLocationsRecommendersRecommendationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1ListRecommendationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/recommenders/{recommendersId}/recommendations',
        http_method='GET',
        method_id='recommender.projects.locations.recommenders.recommendations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/recommendations',
        request_field='',
        request_type_name='RecommenderProjectsLocationsRecommendersRecommendationsListRequest',
        response_type_name='GoogleCloudRecommenderV1beta1ListRecommendationsResponse',
        supports_download=False,
    )

    def MarkClaimed(self, request, global_params=None):
      r"""Marks the Recommendation State as Claimed. Users can use this method to indicate to the Recommender API that they are starting to apply the recommendation themselves. This stops the recommendation content from being updated. Associated insights are frozen and placed in the ACCEPTED state. MarkRecommendationClaimed can be applied to recommendations in CLAIMED or ACTIVE state. Requires the recommender.*.update IAM permission for the specified recommender.

      Args:
        request: (RecommenderProjectsLocationsRecommendersRecommendationsMarkClaimedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Recommendation) The response message.
      """
      config = self.GetMethodConfig('MarkClaimed')
      return self._RunMethod(
          config, request, global_params=global_params)

    MarkClaimed.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/recommenders/{recommendersId}/recommendations/{recommendationsId}:markClaimed',
        http_method='POST',
        method_id='recommender.projects.locations.recommenders.recommendations.markClaimed',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:markClaimed',
        request_field='googleCloudRecommenderV1beta1MarkRecommendationClaimedRequest',
        request_type_name='RecommenderProjectsLocationsRecommendersRecommendationsMarkClaimedRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Recommendation',
        supports_download=False,
    )

    def MarkDismissed(self, request, global_params=None):
      r"""Mark the Recommendation State as Dismissed. Users can use this method to indicate to the Recommender API that an ACTIVE recommendation has to be marked back as DISMISSED. MarkRecommendationDismissed can be applied to recommendations in ACTIVE state. Requires the recommender.*.update IAM permission for the specified recommender.

      Args:
        request: (RecommenderProjectsLocationsRecommendersRecommendationsMarkDismissedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Recommendation) The response message.
      """
      config = self.GetMethodConfig('MarkDismissed')
      return self._RunMethod(
          config, request, global_params=global_params)

    MarkDismissed.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/recommenders/{recommendersId}/recommendations/{recommendationsId}:markDismissed',
        http_method='POST',
        method_id='recommender.projects.locations.recommenders.recommendations.markDismissed',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:markDismissed',
        request_field='googleCloudRecommenderV1beta1MarkRecommendationDismissedRequest',
        request_type_name='RecommenderProjectsLocationsRecommendersRecommendationsMarkDismissedRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Recommendation',
        supports_download=False,
    )

    def MarkFailed(self, request, global_params=None):
      r"""Marks the Recommendation State as Failed. Users can use this method to indicate to the Recommender API that they have applied the recommendation themselves, and the operation failed. This stops the recommendation content from being updated. Associated insights are frozen and placed in the ACCEPTED state. MarkRecommendationFailed can be applied to recommendations in ACTIVE, CLAIMED, SUCCEEDED, or FAILED state. Requires the recommender.*.update IAM permission for the specified recommender.

      Args:
        request: (RecommenderProjectsLocationsRecommendersRecommendationsMarkFailedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Recommendation) The response message.
      """
      config = self.GetMethodConfig('MarkFailed')
      return self._RunMethod(
          config, request, global_params=global_params)

    MarkFailed.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/recommenders/{recommendersId}/recommendations/{recommendationsId}:markFailed',
        http_method='POST',
        method_id='recommender.projects.locations.recommenders.recommendations.markFailed',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:markFailed',
        request_field='googleCloudRecommenderV1beta1MarkRecommendationFailedRequest',
        request_type_name='RecommenderProjectsLocationsRecommendersRecommendationsMarkFailedRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Recommendation',
        supports_download=False,
    )

    def MarkSucceeded(self, request, global_params=None):
      r"""Marks the Recommendation State as Succeeded. Users can use this method to indicate to the Recommender API that they have applied the recommendation themselves, and the operation was successful. This stops the recommendation content from being updated. Associated insights are frozen and placed in the ACCEPTED state. MarkRecommendationSucceeded can be applied to recommendations in ACTIVE, CLAIMED, SUCCEEDED, or FAILED state. Requires the recommender.*.update IAM permission for the specified recommender.

      Args:
        request: (RecommenderProjectsLocationsRecommendersRecommendationsMarkSucceededRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1Recommendation) The response message.
      """
      config = self.GetMethodConfig('MarkSucceeded')
      return self._RunMethod(
          config, request, global_params=global_params)

    MarkSucceeded.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/recommenders/{recommendersId}/recommendations/{recommendationsId}:markSucceeded',
        http_method='POST',
        method_id='recommender.projects.locations.recommenders.recommendations.markSucceeded',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:markSucceeded',
        request_field='googleCloudRecommenderV1beta1MarkRecommendationSucceededRequest',
        request_type_name='RecommenderProjectsLocationsRecommendersRecommendationsMarkSucceededRequest',
        response_type_name='GoogleCloudRecommenderV1beta1Recommendation',
        supports_download=False,
    )

  class ProjectsLocationsRecommendersService(base_api.BaseApiService):
    """Service class for the projects_locations_recommenders resource."""

    _NAME = 'projects_locations_recommenders'

    def __init__(self, client):
      super(RecommenderV1beta1.ProjectsLocationsRecommendersService, self).__init__(client)
      self._upload_configs = {
          }

    def GetConfig(self, request, global_params=None):
      r"""Gets the requested Recommender Config. There is only one instance of the config for each Recommender.

      Args:
        request: (RecommenderProjectsLocationsRecommendersGetConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1RecommenderConfig) The response message.
      """
      config = self.GetMethodConfig('GetConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/recommenders/{recommendersId}/config',
        http_method='GET',
        method_id='recommender.projects.locations.recommenders.getConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='RecommenderProjectsLocationsRecommendersGetConfigRequest',
        response_type_name='GoogleCloudRecommenderV1beta1RecommenderConfig',
        supports_download=False,
    )

    def UpdateConfig(self, request, global_params=None):
      r"""Updates a Recommender Config. This will create a new revision of the config.

      Args:
        request: (RecommenderProjectsLocationsRecommendersUpdateConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1RecommenderConfig) The response message.
      """
      config = self.GetMethodConfig('UpdateConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/recommenders/{recommendersId}/config',
        http_method='PATCH',
        method_id='recommender.projects.locations.recommenders.updateConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v1beta1/{+name}',
        request_field='googleCloudRecommenderV1beta1RecommenderConfig',
        request_type_name='RecommenderProjectsLocationsRecommendersUpdateConfigRequest',
        response_type_name='GoogleCloudRecommenderV1beta1RecommenderConfig',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(RecommenderV1beta1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists locations with recommendations or insights.

      Args:
        request: (RecommenderProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudLocationListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='recommender.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+name}/locations',
        request_field='',
        request_type_name='RecommenderProjectsLocationsListRequest',
        response_type_name='GoogleCloudLocationListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(RecommenderV1beta1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

  class RecommendersService(base_api.BaseApiService):
    """Service class for the recommenders resource."""

    _NAME = 'recommenders'

    def __init__(self, client):
      super(RecommenderV1beta1.RecommendersService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists all available Recommenders. No IAM permissions are required.

      Args:
        request: (RecommenderRecommendersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudRecommenderV1beta1ListRecommendersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='recommender.recommenders.list',
        ordered_params=[],
        path_params=[],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1beta1/recommenders',
        request_field='',
        request_type_name='RecommenderRecommendersListRequest',
        response_type_name='GoogleCloudRecommenderV1beta1ListRecommendersResponse',
        supports_download=False,
    )
