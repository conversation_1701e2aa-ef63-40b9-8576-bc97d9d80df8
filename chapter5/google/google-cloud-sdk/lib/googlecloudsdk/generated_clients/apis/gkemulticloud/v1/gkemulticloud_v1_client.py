"""Generated client library for gkemulticloud version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.gkemulticloud.v1 import gkemulticloud_v1_messages as messages


class GkemulticloudV1(base_api.BaseApiClient):
  """Generated client library for service gkemulticloud version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://gkemulticloud.googleapis.com/'
  MTLS_BASE_URL = 'https://gkemulticloud.mtls.googleapis.com/'

  _PACKAGE = 'gkemulticloud'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'GkemulticloudV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new gkemulticloud handle."""
    url = url or self.BASE_URL
    super(GkemulticloudV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_attachedClusters = self.ProjectsLocationsAttachedClustersService(self)
    self.projects_locations_awsClusters_awsNodePools = self.ProjectsLocationsAwsClustersAwsNodePoolsService(self)
    self.projects_locations_awsClusters_well_known = self.ProjectsLocationsAwsClustersWellKnownService(self)
    self.projects_locations_awsClusters = self.ProjectsLocationsAwsClustersService(self)
    self.projects_locations_azureClients = self.ProjectsLocationsAzureClientsService(self)
    self.projects_locations_azureClusters_azureNodePools = self.ProjectsLocationsAzureClustersAzureNodePoolsService(self)
    self.projects_locations_azureClusters_well_known = self.ProjectsLocationsAzureClustersWellKnownService(self)
    self.projects_locations_azureClusters = self.ProjectsLocationsAzureClustersService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsAttachedClustersService(base_api.BaseApiService):
    """Service class for the projects_locations_attachedClusters resource."""

    _NAME = 'projects_locations_attachedClusters'

    def __init__(self, client):
      super(GkemulticloudV1.ProjectsLocationsAttachedClustersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new AttachedCluster resource on a given Google Cloud Platform project and region. If successful, the response contains a newly created Operation resource that can be described to track the status of the operation.

      Args:
        request: (GkemulticloudProjectsLocationsAttachedClustersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/attachedClusters',
        http_method='POST',
        method_id='gkemulticloud.projects.locations.attachedClusters.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['attachedClusterId', 'validateOnly'],
        relative_path='v1/{+parent}/attachedClusters',
        request_field='googleCloudGkemulticloudV1AttachedCluster',
        request_type_name='GkemulticloudProjectsLocationsAttachedClustersCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a specific AttachedCluster resource. If successful, the response contains a newly created Operation resource that can be described to track the status of the operation.

      Args:
        request: (GkemulticloudProjectsLocationsAttachedClustersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/attachedClusters/{attachedClustersId}',
        http_method='DELETE',
        method_id='gkemulticloud.projects.locations.attachedClusters.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'ignoreErrors', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsAttachedClustersDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def GenerateAttachedClusterAgentToken(self, request, global_params=None):
      r"""Generates an access token for a cluster agent.

      Args:
        request: (GkemulticloudProjectsLocationsAttachedClustersGenerateAttachedClusterAgentTokenRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudGkemulticloudV1GenerateAttachedClusterAgentTokenResponse) The response message.
      """
      config = self.GetMethodConfig('GenerateAttachedClusterAgentToken')
      return self._RunMethod(
          config, request, global_params=global_params)

    GenerateAttachedClusterAgentToken.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/attachedClusters/{attachedClustersId}:generateAttachedClusterAgentToken',
        http_method='POST',
        method_id='gkemulticloud.projects.locations.attachedClusters.generateAttachedClusterAgentToken',
        ordered_params=['attachedCluster'],
        path_params=['attachedCluster'],
        query_params=[],
        relative_path='v1/{+attachedCluster}:generateAttachedClusterAgentToken',
        request_field='googleCloudGkemulticloudV1GenerateAttachedClusterAgentTokenRequest',
        request_type_name='GkemulticloudProjectsLocationsAttachedClustersGenerateAttachedClusterAgentTokenRequest',
        response_type_name='GoogleCloudGkemulticloudV1GenerateAttachedClusterAgentTokenResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Describes a specific AttachedCluster resource.

      Args:
        request: (GkemulticloudProjectsLocationsAttachedClustersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudGkemulticloudV1AttachedCluster) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/attachedClusters/{attachedClustersId}',
        http_method='GET',
        method_id='gkemulticloud.projects.locations.attachedClusters.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsAttachedClustersGetRequest',
        response_type_name='GoogleCloudGkemulticloudV1AttachedCluster',
        supports_download=False,
    )

    def Import(self, request, global_params=None):
      r"""Imports creates a new AttachedCluster resource by importing an existing Fleet Membership resource. Attached Clusters created before the introduction of the Anthos Multi-Cloud API can be imported through this method. If successful, the response contains a newly created Operation resource that can be described to track the status of the operation.

      Args:
        request: (GkemulticloudProjectsLocationsAttachedClustersImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Import')
      return self._RunMethod(
          config, request, global_params=global_params)

    Import.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/attachedClusters:import',
        http_method='POST',
        method_id='gkemulticloud.projects.locations.attachedClusters.import',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/attachedClusters:import',
        request_field='googleCloudGkemulticloudV1ImportAttachedClusterRequest',
        request_type_name='GkemulticloudProjectsLocationsAttachedClustersImportRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all AttachedCluster resources on a given Google Cloud project and region.

      Args:
        request: (GkemulticloudProjectsLocationsAttachedClustersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudGkemulticloudV1ListAttachedClustersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/attachedClusters',
        http_method='GET',
        method_id='gkemulticloud.projects.locations.attachedClusters.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/attachedClusters',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsAttachedClustersListRequest',
        response_type_name='GoogleCloudGkemulticloudV1ListAttachedClustersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an AttachedCluster.

      Args:
        request: (GkemulticloudProjectsLocationsAttachedClustersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/attachedClusters/{attachedClustersId}',
        http_method='PATCH',
        method_id='gkemulticloud.projects.locations.attachedClusters.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='googleCloudGkemulticloudV1AttachedCluster',
        request_type_name='GkemulticloudProjectsLocationsAttachedClustersPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsAwsClustersAwsNodePoolsService(base_api.BaseApiService):
    """Service class for the projects_locations_awsClusters_awsNodePools resource."""

    _NAME = 'projects_locations_awsClusters_awsNodePools'

    def __init__(self, client):
      super(GkemulticloudV1.ProjectsLocationsAwsClustersAwsNodePoolsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new AwsNodePool, attached to a given AwsCluster. If successful, the response contains a newly created Operation resource that can be described to track the status of the operation.

      Args:
        request: (GkemulticloudProjectsLocationsAwsClustersAwsNodePoolsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/awsClusters/{awsClustersId}/awsNodePools',
        http_method='POST',
        method_id='gkemulticloud.projects.locations.awsClusters.awsNodePools.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['awsNodePoolId', 'validateOnly'],
        relative_path='v1/{+parent}/awsNodePools',
        request_field='googleCloudGkemulticloudV1AwsNodePool',
        request_type_name='GkemulticloudProjectsLocationsAwsClustersAwsNodePoolsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a specific AwsNodePool resource. If successful, the response contains a newly created Operation resource that can be described to track the status of the operation.

      Args:
        request: (GkemulticloudProjectsLocationsAwsClustersAwsNodePoolsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/awsClusters/{awsClustersId}/awsNodePools/{awsNodePoolsId}',
        http_method='DELETE',
        method_id='gkemulticloud.projects.locations.awsClusters.awsNodePools.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'ignoreErrors', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsAwsClustersAwsNodePoolsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Describes a specific AwsNodePool resource.

      Args:
        request: (GkemulticloudProjectsLocationsAwsClustersAwsNodePoolsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudGkemulticloudV1AwsNodePool) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/awsClusters/{awsClustersId}/awsNodePools/{awsNodePoolsId}',
        http_method='GET',
        method_id='gkemulticloud.projects.locations.awsClusters.awsNodePools.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsAwsClustersAwsNodePoolsGetRequest',
        response_type_name='GoogleCloudGkemulticloudV1AwsNodePool',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all AwsNodePool resources on a given AwsCluster.

      Args:
        request: (GkemulticloudProjectsLocationsAwsClustersAwsNodePoolsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudGkemulticloudV1ListAwsNodePoolsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/awsClusters/{awsClustersId}/awsNodePools',
        http_method='GET',
        method_id='gkemulticloud.projects.locations.awsClusters.awsNodePools.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/awsNodePools',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsAwsClustersAwsNodePoolsListRequest',
        response_type_name='GoogleCloudGkemulticloudV1ListAwsNodePoolsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an AwsNodePool.

      Args:
        request: (GkemulticloudProjectsLocationsAwsClustersAwsNodePoolsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/awsClusters/{awsClustersId}/awsNodePools/{awsNodePoolsId}',
        http_method='PATCH',
        method_id='gkemulticloud.projects.locations.awsClusters.awsNodePools.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='googleCloudGkemulticloudV1AwsNodePool',
        request_type_name='GkemulticloudProjectsLocationsAwsClustersAwsNodePoolsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Rollback(self, request, global_params=None):
      r"""Rolls back a previously aborted or failed AwsNodePool update request. Makes no changes if the last update request successfully finished. If an update request is in progress, you cannot rollback the update. You must first cancel or let it finish unsuccessfully before you can rollback.

      Args:
        request: (GkemulticloudProjectsLocationsAwsClustersAwsNodePoolsRollbackRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Rollback')
      return self._RunMethod(
          config, request, global_params=global_params)

    Rollback.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/awsClusters/{awsClustersId}/awsNodePools/{awsNodePoolsId}:rollback',
        http_method='POST',
        method_id='gkemulticloud.projects.locations.awsClusters.awsNodePools.rollback',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:rollback',
        request_field='googleCloudGkemulticloudV1RollbackAwsNodePoolUpdateRequest',
        request_type_name='GkemulticloudProjectsLocationsAwsClustersAwsNodePoolsRollbackRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsAwsClustersWellKnownService(base_api.BaseApiService):
    """Service class for the projects_locations_awsClusters_well_known resource."""

    _NAME = 'projects_locations_awsClusters_well_known'

    def __init__(self, client):
      super(GkemulticloudV1.ProjectsLocationsAwsClustersWellKnownService, self).__init__(client)
      self._upload_configs = {
          }

    def GetOpenid_configuration(self, request, global_params=None):
      r"""Gets the OIDC discovery document for the cluster. See the [OpenID Connect Discovery 1.0 specification](https://openid.net/specs/openid-connect-discovery-1_0.html) for details.

      Args:
        request: (GkemulticloudProjectsLocationsAwsClustersWellKnownGetOpenidConfigurationRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudGkemulticloudV1AwsOpenIdConfig) The response message.
      """
      config = self.GetMethodConfig('GetOpenid_configuration')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetOpenid_configuration.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/awsClusters/{awsClustersId}/.well-known/openid-configuration',
        http_method='GET',
        method_id='gkemulticloud.projects.locations.awsClusters.well-known.getOpenid-configuration',
        ordered_params=['awsCluster'],
        path_params=['awsCluster'],
        query_params=[],
        relative_path='v1/{+awsCluster}/.well-known/openid-configuration',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsAwsClustersWellKnownGetOpenidConfigurationRequest',
        response_type_name='GoogleCloudGkemulticloudV1AwsOpenIdConfig',
        supports_download=False,
    )

  class ProjectsLocationsAwsClustersService(base_api.BaseApiService):
    """Service class for the projects_locations_awsClusters resource."""

    _NAME = 'projects_locations_awsClusters'

    def __init__(self, client):
      super(GkemulticloudV1.ProjectsLocationsAwsClustersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new AwsCluster resource on a given Google Cloud Platform project and region. If successful, the response contains a newly created Operation resource that can be described to track the status of the operation.

      Args:
        request: (GkemulticloudProjectsLocationsAwsClustersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/awsClusters',
        http_method='POST',
        method_id='gkemulticloud.projects.locations.awsClusters.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['awsClusterId', 'validateOnly'],
        relative_path='v1/{+parent}/awsClusters',
        request_field='googleCloudGkemulticloudV1AwsCluster',
        request_type_name='GkemulticloudProjectsLocationsAwsClustersCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a specific AwsCluster resource. Fails if the cluster has one or more associated AwsNodePool resources. If successful, the response contains a newly created Operation resource that can be described to track the status of the operation.

      Args:
        request: (GkemulticloudProjectsLocationsAwsClustersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/awsClusters/{awsClustersId}',
        http_method='DELETE',
        method_id='gkemulticloud.projects.locations.awsClusters.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'ignoreErrors', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsAwsClustersDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def GenerateAwsAccessToken(self, request, global_params=None):
      r"""Generates a short-lived access token to authenticate to a given AwsCluster resource.

      Args:
        request: (GkemulticloudProjectsLocationsAwsClustersGenerateAwsAccessTokenRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudGkemulticloudV1GenerateAwsAccessTokenResponse) The response message.
      """
      config = self.GetMethodConfig('GenerateAwsAccessToken')
      return self._RunMethod(
          config, request, global_params=global_params)

    GenerateAwsAccessToken.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/awsClusters/{awsClustersId}:generateAwsAccessToken',
        http_method='GET',
        method_id='gkemulticloud.projects.locations.awsClusters.generateAwsAccessToken',
        ordered_params=['awsCluster'],
        path_params=['awsCluster'],
        query_params=[],
        relative_path='v1/{+awsCluster}:generateAwsAccessToken',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsAwsClustersGenerateAwsAccessTokenRequest',
        response_type_name='GoogleCloudGkemulticloudV1GenerateAwsAccessTokenResponse',
        supports_download=False,
    )

    def GenerateAwsClusterAgentToken(self, request, global_params=None):
      r"""Generates an access token for a cluster agent.

      Args:
        request: (GkemulticloudProjectsLocationsAwsClustersGenerateAwsClusterAgentTokenRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudGkemulticloudV1GenerateAwsClusterAgentTokenResponse) The response message.
      """
      config = self.GetMethodConfig('GenerateAwsClusterAgentToken')
      return self._RunMethod(
          config, request, global_params=global_params)

    GenerateAwsClusterAgentToken.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/awsClusters/{awsClustersId}:generateAwsClusterAgentToken',
        http_method='POST',
        method_id='gkemulticloud.projects.locations.awsClusters.generateAwsClusterAgentToken',
        ordered_params=['awsCluster'],
        path_params=['awsCluster'],
        query_params=[],
        relative_path='v1/{+awsCluster}:generateAwsClusterAgentToken',
        request_field='googleCloudGkemulticloudV1GenerateAwsClusterAgentTokenRequest',
        request_type_name='GkemulticloudProjectsLocationsAwsClustersGenerateAwsClusterAgentTokenRequest',
        response_type_name='GoogleCloudGkemulticloudV1GenerateAwsClusterAgentTokenResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Describes a specific AwsCluster resource.

      Args:
        request: (GkemulticloudProjectsLocationsAwsClustersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudGkemulticloudV1AwsCluster) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/awsClusters/{awsClustersId}',
        http_method='GET',
        method_id='gkemulticloud.projects.locations.awsClusters.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsAwsClustersGetRequest',
        response_type_name='GoogleCloudGkemulticloudV1AwsCluster',
        supports_download=False,
    )

    def GetJwks(self, request, global_params=None):
      r"""Gets the public component of the cluster signing keys in JSON Web Key format.

      Args:
        request: (GkemulticloudProjectsLocationsAwsClustersGetJwksRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudGkemulticloudV1AwsJsonWebKeys) The response message.
      """
      config = self.GetMethodConfig('GetJwks')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetJwks.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/awsClusters/{awsClustersId}/jwks',
        http_method='GET',
        method_id='gkemulticloud.projects.locations.awsClusters.getJwks',
        ordered_params=['awsCluster'],
        path_params=['awsCluster'],
        query_params=[],
        relative_path='v1/{+awsCluster}/jwks',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsAwsClustersGetJwksRequest',
        response_type_name='GoogleCloudGkemulticloudV1AwsJsonWebKeys',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all AwsCluster resources on a given Google Cloud project and region.

      Args:
        request: (GkemulticloudProjectsLocationsAwsClustersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudGkemulticloudV1ListAwsClustersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/awsClusters',
        http_method='GET',
        method_id='gkemulticloud.projects.locations.awsClusters.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/awsClusters',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsAwsClustersListRequest',
        response_type_name='GoogleCloudGkemulticloudV1ListAwsClustersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an AwsCluster.

      Args:
        request: (GkemulticloudProjectsLocationsAwsClustersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/awsClusters/{awsClustersId}',
        http_method='PATCH',
        method_id='gkemulticloud.projects.locations.awsClusters.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='googleCloudGkemulticloudV1AwsCluster',
        request_type_name='GkemulticloudProjectsLocationsAwsClustersPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsAzureClientsService(base_api.BaseApiService):
    """Service class for the projects_locations_azureClients resource."""

    _NAME = 'projects_locations_azureClients'

    def __init__(self, client):
      super(GkemulticloudV1.ProjectsLocationsAzureClientsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new AzureClient resource on a given Google Cloud project and region. `AzureClient` resources hold client authentication information needed by the Anthos Multicloud API to manage Azure resources on your Azure subscription on your behalf. If successful, the response contains a newly created Operation resource that can be described to track the status of the operation.

      Args:
        request: (GkemulticloudProjectsLocationsAzureClientsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/azureClients',
        http_method='POST',
        method_id='gkemulticloud.projects.locations.azureClients.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['azureClientId', 'validateOnly'],
        relative_path='v1/{+parent}/azureClients',
        request_field='googleCloudGkemulticloudV1AzureClient',
        request_type_name='GkemulticloudProjectsLocationsAzureClientsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a specific AzureClient resource. If the client is used by one or more clusters, deletion will fail and a `FAILED_PRECONDITION` error will be returned. If successful, the response contains a newly created Operation resource that can be described to track the status of the operation.

      Args:
        request: (GkemulticloudProjectsLocationsAzureClientsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/azureClients/{azureClientsId}',
        http_method='DELETE',
        method_id='gkemulticloud.projects.locations.azureClients.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsAzureClientsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Describes a specific AzureClient resource.

      Args:
        request: (GkemulticloudProjectsLocationsAzureClientsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudGkemulticloudV1AzureClient) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/azureClients/{azureClientsId}',
        http_method='GET',
        method_id='gkemulticloud.projects.locations.azureClients.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsAzureClientsGetRequest',
        response_type_name='GoogleCloudGkemulticloudV1AzureClient',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all AzureClient resources on a given Google Cloud project and region.

      Args:
        request: (GkemulticloudProjectsLocationsAzureClientsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudGkemulticloudV1ListAzureClientsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/azureClients',
        http_method='GET',
        method_id='gkemulticloud.projects.locations.azureClients.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/azureClients',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsAzureClientsListRequest',
        response_type_name='GoogleCloudGkemulticloudV1ListAzureClientsResponse',
        supports_download=False,
    )

  class ProjectsLocationsAzureClustersAzureNodePoolsService(base_api.BaseApiService):
    """Service class for the projects_locations_azureClusters_azureNodePools resource."""

    _NAME = 'projects_locations_azureClusters_azureNodePools'

    def __init__(self, client):
      super(GkemulticloudV1.ProjectsLocationsAzureClustersAzureNodePoolsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new AzureNodePool, attached to a given AzureCluster. If successful, the response contains a newly created Operation resource that can be described to track the status of the operation.

      Args:
        request: (GkemulticloudProjectsLocationsAzureClustersAzureNodePoolsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/azureClusters/{azureClustersId}/azureNodePools',
        http_method='POST',
        method_id='gkemulticloud.projects.locations.azureClusters.azureNodePools.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['azureNodePoolId', 'validateOnly'],
        relative_path='v1/{+parent}/azureNodePools',
        request_field='googleCloudGkemulticloudV1AzureNodePool',
        request_type_name='GkemulticloudProjectsLocationsAzureClustersAzureNodePoolsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a specific AzureNodePool resource. If successful, the response contains a newly created Operation resource that can be described to track the status of the operation.

      Args:
        request: (GkemulticloudProjectsLocationsAzureClustersAzureNodePoolsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/azureClusters/{azureClustersId}/azureNodePools/{azureNodePoolsId}',
        http_method='DELETE',
        method_id='gkemulticloud.projects.locations.azureClusters.azureNodePools.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'ignoreErrors', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsAzureClustersAzureNodePoolsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Describes a specific AzureNodePool resource.

      Args:
        request: (GkemulticloudProjectsLocationsAzureClustersAzureNodePoolsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudGkemulticloudV1AzureNodePool) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/azureClusters/{azureClustersId}/azureNodePools/{azureNodePoolsId}',
        http_method='GET',
        method_id='gkemulticloud.projects.locations.azureClusters.azureNodePools.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsAzureClustersAzureNodePoolsGetRequest',
        response_type_name='GoogleCloudGkemulticloudV1AzureNodePool',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all AzureNodePool resources on a given AzureCluster.

      Args:
        request: (GkemulticloudProjectsLocationsAzureClustersAzureNodePoolsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudGkemulticloudV1ListAzureNodePoolsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/azureClusters/{azureClustersId}/azureNodePools',
        http_method='GET',
        method_id='gkemulticloud.projects.locations.azureClusters.azureNodePools.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/azureNodePools',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsAzureClustersAzureNodePoolsListRequest',
        response_type_name='GoogleCloudGkemulticloudV1ListAzureNodePoolsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an AzureNodePool.

      Args:
        request: (GkemulticloudProjectsLocationsAzureClustersAzureNodePoolsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/azureClusters/{azureClustersId}/azureNodePools/{azureNodePoolsId}',
        http_method='PATCH',
        method_id='gkemulticloud.projects.locations.azureClusters.azureNodePools.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='googleCloudGkemulticloudV1AzureNodePool',
        request_type_name='GkemulticloudProjectsLocationsAzureClustersAzureNodePoolsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsAzureClustersWellKnownService(base_api.BaseApiService):
    """Service class for the projects_locations_azureClusters_well_known resource."""

    _NAME = 'projects_locations_azureClusters_well_known'

    def __init__(self, client):
      super(GkemulticloudV1.ProjectsLocationsAzureClustersWellKnownService, self).__init__(client)
      self._upload_configs = {
          }

    def GetOpenid_configuration(self, request, global_params=None):
      r"""Gets the OIDC discovery document for the cluster. See the [OpenID Connect Discovery 1.0 specification](https://openid.net/specs/openid-connect-discovery-1_0.html) for details.

      Args:
        request: (GkemulticloudProjectsLocationsAzureClustersWellKnownGetOpenidConfigurationRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudGkemulticloudV1AzureOpenIdConfig) The response message.
      """
      config = self.GetMethodConfig('GetOpenid_configuration')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetOpenid_configuration.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/azureClusters/{azureClustersId}/.well-known/openid-configuration',
        http_method='GET',
        method_id='gkemulticloud.projects.locations.azureClusters.well-known.getOpenid-configuration',
        ordered_params=['azureCluster'],
        path_params=['azureCluster'],
        query_params=[],
        relative_path='v1/{+azureCluster}/.well-known/openid-configuration',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsAzureClustersWellKnownGetOpenidConfigurationRequest',
        response_type_name='GoogleCloudGkemulticloudV1AzureOpenIdConfig',
        supports_download=False,
    )

  class ProjectsLocationsAzureClustersService(base_api.BaseApiService):
    """Service class for the projects_locations_azureClusters resource."""

    _NAME = 'projects_locations_azureClusters'

    def __init__(self, client):
      super(GkemulticloudV1.ProjectsLocationsAzureClustersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new AzureCluster resource on a given Google Cloud Platform project and region. If successful, the response contains a newly created Operation resource that can be described to track the status of the operation.

      Args:
        request: (GkemulticloudProjectsLocationsAzureClustersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/azureClusters',
        http_method='POST',
        method_id='gkemulticloud.projects.locations.azureClusters.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['azureClusterId', 'validateOnly'],
        relative_path='v1/{+parent}/azureClusters',
        request_field='googleCloudGkemulticloudV1AzureCluster',
        request_type_name='GkemulticloudProjectsLocationsAzureClustersCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a specific AzureCluster resource. Fails if the cluster has one or more associated AzureNodePool resources. If successful, the response contains a newly created Operation resource that can be described to track the status of the operation.

      Args:
        request: (GkemulticloudProjectsLocationsAzureClustersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/azureClusters/{azureClustersId}',
        http_method='DELETE',
        method_id='gkemulticloud.projects.locations.azureClusters.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'ignoreErrors', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsAzureClustersDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def GenerateAzureAccessToken(self, request, global_params=None):
      r"""Generates a short-lived access token to authenticate to a given AzureCluster resource.

      Args:
        request: (GkemulticloudProjectsLocationsAzureClustersGenerateAzureAccessTokenRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudGkemulticloudV1GenerateAzureAccessTokenResponse) The response message.
      """
      config = self.GetMethodConfig('GenerateAzureAccessToken')
      return self._RunMethod(
          config, request, global_params=global_params)

    GenerateAzureAccessToken.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/azureClusters/{azureClustersId}:generateAzureAccessToken',
        http_method='GET',
        method_id='gkemulticloud.projects.locations.azureClusters.generateAzureAccessToken',
        ordered_params=['azureCluster'],
        path_params=['azureCluster'],
        query_params=[],
        relative_path='v1/{+azureCluster}:generateAzureAccessToken',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsAzureClustersGenerateAzureAccessTokenRequest',
        response_type_name='GoogleCloudGkemulticloudV1GenerateAzureAccessTokenResponse',
        supports_download=False,
    )

    def GenerateAzureClusterAgentToken(self, request, global_params=None):
      r"""Generates an access token for a cluster agent.

      Args:
        request: (GkemulticloudProjectsLocationsAzureClustersGenerateAzureClusterAgentTokenRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudGkemulticloudV1GenerateAzureClusterAgentTokenResponse) The response message.
      """
      config = self.GetMethodConfig('GenerateAzureClusterAgentToken')
      return self._RunMethod(
          config, request, global_params=global_params)

    GenerateAzureClusterAgentToken.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/azureClusters/{azureClustersId}:generateAzureClusterAgentToken',
        http_method='POST',
        method_id='gkemulticloud.projects.locations.azureClusters.generateAzureClusterAgentToken',
        ordered_params=['azureCluster'],
        path_params=['azureCluster'],
        query_params=[],
        relative_path='v1/{+azureCluster}:generateAzureClusterAgentToken',
        request_field='googleCloudGkemulticloudV1GenerateAzureClusterAgentTokenRequest',
        request_type_name='GkemulticloudProjectsLocationsAzureClustersGenerateAzureClusterAgentTokenRequest',
        response_type_name='GoogleCloudGkemulticloudV1GenerateAzureClusterAgentTokenResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Describes a specific AzureCluster resource.

      Args:
        request: (GkemulticloudProjectsLocationsAzureClustersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudGkemulticloudV1AzureCluster) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/azureClusters/{azureClustersId}',
        http_method='GET',
        method_id='gkemulticloud.projects.locations.azureClusters.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsAzureClustersGetRequest',
        response_type_name='GoogleCloudGkemulticloudV1AzureCluster',
        supports_download=False,
    )

    def GetJwks(self, request, global_params=None):
      r"""Gets the public component of the cluster signing keys in JSON Web Key format.

      Args:
        request: (GkemulticloudProjectsLocationsAzureClustersGetJwksRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudGkemulticloudV1AzureJsonWebKeys) The response message.
      """
      config = self.GetMethodConfig('GetJwks')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetJwks.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/azureClusters/{azureClustersId}/jwks',
        http_method='GET',
        method_id='gkemulticloud.projects.locations.azureClusters.getJwks',
        ordered_params=['azureCluster'],
        path_params=['azureCluster'],
        query_params=[],
        relative_path='v1/{+azureCluster}/jwks',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsAzureClustersGetJwksRequest',
        response_type_name='GoogleCloudGkemulticloudV1AzureJsonWebKeys',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all AzureCluster resources on a given Google Cloud project and region.

      Args:
        request: (GkemulticloudProjectsLocationsAzureClustersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudGkemulticloudV1ListAzureClustersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/azureClusters',
        http_method='GET',
        method_id='gkemulticloud.projects.locations.azureClusters.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/azureClusters',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsAzureClustersListRequest',
        response_type_name='GoogleCloudGkemulticloudV1ListAzureClustersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an AzureCluster.

      Args:
        request: (GkemulticloudProjectsLocationsAzureClustersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/azureClusters/{azureClustersId}',
        http_method='PATCH',
        method_id='gkemulticloud.projects.locations.azureClusters.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='googleCloudGkemulticloudV1AzureCluster',
        request_type_name='GkemulticloudProjectsLocationsAzureClustersPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(GkemulticloudV1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (GkemulticloudProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='gkemulticloud.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='googleLongrunningCancelOperationRequest',
        request_type_name='GkemulticloudProjectsLocationsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (GkemulticloudProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='gkemulticloud.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (GkemulticloudProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='gkemulticloud.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (GkemulticloudProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='gkemulticloud.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(GkemulticloudV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def GenerateAttachedClusterInstallManifest(self, request, global_params=None):
      r"""Generates the install manifest to be installed on the target cluster.

      Args:
        request: (GkemulticloudProjectsLocationsGenerateAttachedClusterInstallManifestRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudGkemulticloudV1GenerateAttachedClusterInstallManifestResponse) The response message.
      """
      config = self.GetMethodConfig('GenerateAttachedClusterInstallManifest')
      return self._RunMethod(
          config, request, global_params=global_params)

    GenerateAttachedClusterInstallManifest.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}:generateAttachedClusterInstallManifest',
        http_method='GET',
        method_id='gkemulticloud.projects.locations.generateAttachedClusterInstallManifest',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['attachedClusterId', 'platformVersion', 'proxyConfig_kubernetesSecret_name', 'proxyConfig_kubernetesSecret_namespace'],
        relative_path='v1/{+parent}:generateAttachedClusterInstallManifest',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsGenerateAttachedClusterInstallManifestRequest',
        response_type_name='GoogleCloudGkemulticloudV1GenerateAttachedClusterInstallManifestResponse',
        supports_download=False,
    )

    def GetAttachedServerConfig(self, request, global_params=None):
      r"""Returns information, such as supported Kubernetes versions, on a given Google Cloud location.

      Args:
        request: (GkemulticloudProjectsLocationsGetAttachedServerConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudGkemulticloudV1AttachedServerConfig) The response message.
      """
      config = self.GetMethodConfig('GetAttachedServerConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetAttachedServerConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/attachedServerConfig',
        http_method='GET',
        method_id='gkemulticloud.projects.locations.getAttachedServerConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsGetAttachedServerConfigRequest',
        response_type_name='GoogleCloudGkemulticloudV1AttachedServerConfig',
        supports_download=False,
    )

    def GetAwsServerConfig(self, request, global_params=None):
      r"""Returns information, such as supported AWS regions and Kubernetes versions, on a given Google Cloud location.

      Args:
        request: (GkemulticloudProjectsLocationsGetAwsServerConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudGkemulticloudV1AwsServerConfig) The response message.
      """
      config = self.GetMethodConfig('GetAwsServerConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetAwsServerConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/awsServerConfig',
        http_method='GET',
        method_id='gkemulticloud.projects.locations.getAwsServerConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsGetAwsServerConfigRequest',
        response_type_name='GoogleCloudGkemulticloudV1AwsServerConfig',
        supports_download=False,
    )

    def GetAzureServerConfig(self, request, global_params=None):
      r"""Returns information, such as supported Azure regions and Kubernetes versions, on a given Google Cloud location.

      Args:
        request: (GkemulticloudProjectsLocationsGetAzureServerConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudGkemulticloudV1AzureServerConfig) The response message.
      """
      config = self.GetMethodConfig('GetAzureServerConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetAzureServerConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/azureServerConfig',
        http_method='GET',
        method_id='gkemulticloud.projects.locations.getAzureServerConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkemulticloudProjectsLocationsGetAzureServerConfigRequest',
        response_type_name='GoogleCloudGkemulticloudV1AzureServerConfig',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(GkemulticloudV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
