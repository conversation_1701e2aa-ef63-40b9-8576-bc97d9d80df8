"""Generated message classes for gkemulticloud version v1.

 GKE Multi-Cloud provides a way to manage Kubernetes clusters that run on AWS
and Azure infrastructure using the GKE Multi-Cloud API. Combined with Connect,
you can manage Kubernetes clusters on Google Cloud, AWS, and Azure from the
Google Cloud Console. When you create a cluster with GKE Multi-Cloud, Google
creates the resources needed and brings up a cluster on your behalf. You can
deploy workloads with the GKE Multi-Cloud API or the gcloud and kubectl
command-line tools.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'gkemulticloud'


class GkemulticloudProjectsLocationsAttachedClustersCreateRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAttachedClustersCreateRequest object.

  Fields:
    attachedClusterId: Required. A client provided ID the resource. Must be
      unique within the parent resource. The provided ID will be part of the
      AttachedCluster resource name formatted as
      `projects//locations//attachedClusters/`. Valid characters are `/a-z-/`.
      Cannot be longer than 63 characters.
    googleCloudGkemulticloudV1AttachedCluster: A
      GoogleCloudGkemulticloudV1AttachedCluster resource to be passed as the
      request body.
    parent: Required. The parent location where this AttachedCluster resource
      will be created. Location names are formatted as `projects//locations/`.
      See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud resource names.
    validateOnly: If set, only validate the request, but do not actually
      create the cluster.
  """

  attachedClusterId = _messages.StringField(1)
  googleCloudGkemulticloudV1AttachedCluster = _messages.MessageField('GoogleCloudGkemulticloudV1AttachedCluster', 2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class GkemulticloudProjectsLocationsAttachedClustersDeleteRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAttachedClustersDeleteRequest object.

  Fields:
    allowMissing: If set to true, and the AttachedCluster resource is not
      found, the request will succeed but no action will be taken on the
      server and a completed Operation will be returned. Useful for idempotent
      deletion.
    etag: The current etag of the AttachedCluster. Allows clients to perform
      deletions through optimistic concurrency control. If the provided etag
      does not match the current etag of the cluster, the request will fail
      and an ABORTED error will be returned.
    ignoreErrors: If set to true, the deletion of AttachedCluster resource
      will succeed even if errors occur during deleting in cluster resources.
      Using this parameter may result in orphaned resources in the cluster.
    name: Required. The resource name the AttachedCluster to delete.
      `AttachedCluster` names are formatted as
      `projects//locations//attachedClusters/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud Platform resource names.
    validateOnly: If set, only validate the request, but do not actually
      delete the resource.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  ignoreErrors = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  validateOnly = _messages.BooleanField(5)


class GkemulticloudProjectsLocationsAttachedClustersGenerateAttachedClusterAgentTokenRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAttachedClustersGenerateAttachedClusterA
  gentTokenRequest object.

  Fields:
    attachedCluster: Required.
    googleCloudGkemulticloudV1GenerateAttachedClusterAgentTokenRequest: A
      GoogleCloudGkemulticloudV1GenerateAttachedClusterAgentTokenRequest
      resource to be passed as the request body.
  """

  attachedCluster = _messages.StringField(1, required=True)
  googleCloudGkemulticloudV1GenerateAttachedClusterAgentTokenRequest = _messages.MessageField('GoogleCloudGkemulticloudV1GenerateAttachedClusterAgentTokenRequest', 2)


class GkemulticloudProjectsLocationsAttachedClustersGetRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAttachedClustersGetRequest object.

  Fields:
    name: Required. The name of the AttachedCluster resource to describe.
      `AttachedCluster` names are formatted as
      `projects//locations//attachedClusters/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud Platform resource names.
  """

  name = _messages.StringField(1, required=True)


class GkemulticloudProjectsLocationsAttachedClustersImportRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAttachedClustersImportRequest object.

  Fields:
    googleCloudGkemulticloudV1ImportAttachedClusterRequest: A
      GoogleCloudGkemulticloudV1ImportAttachedClusterRequest resource to be
      passed as the request body.
    parent: Required. The parent location where this AttachedCluster resource
      will be created. Location names are formatted as `projects//locations/`.
      See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud resource names.
  """

  googleCloudGkemulticloudV1ImportAttachedClusterRequest = _messages.MessageField('GoogleCloudGkemulticloudV1ImportAttachedClusterRequest', 1)
  parent = _messages.StringField(2, required=True)


class GkemulticloudProjectsLocationsAttachedClustersListRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAttachedClustersListRequest object.

  Fields:
    pageSize: The maximum number of items to return. If not specified, a
      default value of 50 will be used by the service. Regardless of the
      pageSize value, the response can include a partial list and a caller
      should only rely on response's nextPageToken to determine if there are
      more instances left to be queried.
    pageToken: The `nextPageToken` value returned from a previous
      attachedClusters.list request, if any.
    parent: Required. The parent location which owns this collection of
      AttachedCluster resources. Location names are formatted as
      `projects//locations/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud Platform resource names.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class GkemulticloudProjectsLocationsAttachedClustersPatchRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAttachedClustersPatchRequest object.

  Fields:
    googleCloudGkemulticloudV1AttachedCluster: A
      GoogleCloudGkemulticloudV1AttachedCluster resource to be passed as the
      request body.
    name: The name of this resource. Cluster names are formatted as
      `projects//locations//attachedClusters/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud Platform resource names.
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field. The elements of the repeated paths field can
      only include these fields from AttachedCluster: * `annotations`. *
      `authorization.admin_groups`. * `authorization.admin_users`. *
      `binary_authorization.evaluation_mode`. * `description`. *
      `logging_config.component_config.enable_components`. *
      `monitoring_config.managed_prometheus_config.enabled`. *
      `platform_version`. * `proxy_config.kubernetes_secret.name`. *
      `proxy_config.kubernetes_secret.namespace`. *
      `security_posture_config.vulnerability_mode` *
      `monitoring_config.cloud_monitoring_config.enabled`
    validateOnly: If set, only validate the request, but do not actually
      update the cluster.
  """

  googleCloudGkemulticloudV1AttachedCluster = _messages.MessageField('GoogleCloudGkemulticloudV1AttachedCluster', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class GkemulticloudProjectsLocationsAwsClustersAwsNodePoolsCreateRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAwsClustersAwsNodePoolsCreateRequest
  object.

  Fields:
    awsNodePoolId: Required. A client provided ID the resource. Must be unique
      within the parent resource. The provided ID will be part of the
      AwsNodePool resource name formatted as
      `projects//locations//awsClusters//awsNodePools/`. Valid characters are
      `/a-z-/`. Cannot be longer than 63 characters.
    googleCloudGkemulticloudV1AwsNodePool: A
      GoogleCloudGkemulticloudV1AwsNodePool resource to be passed as the
      request body.
    parent: Required. The AwsCluster resource where this node pool will be
      created. `AwsCluster` names are formatted as
      `projects//locations//awsClusters/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud resource names.
    validateOnly: If set, only validate the request, but do not actually
      create the node pool.
  """

  awsNodePoolId = _messages.StringField(1)
  googleCloudGkemulticloudV1AwsNodePool = _messages.MessageField('GoogleCloudGkemulticloudV1AwsNodePool', 2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class GkemulticloudProjectsLocationsAwsClustersAwsNodePoolsDeleteRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAwsClustersAwsNodePoolsDeleteRequest
  object.

  Fields:
    allowMissing: If set to true, and the AwsNodePool resource is not found,
      the request will succeed but no action will be taken on the server and a
      completed Operation will be returned. Useful for idempotent deletion.
    etag: The current ETag of the AwsNodePool. Allows clients to perform
      deletions through optimistic concurrency control. If the provided ETag
      does not match the current etag of the node pool, the request will fail
      and an ABORTED error will be returned.
    ignoreErrors: Optional. If set to true, the deletion of AwsNodePool
      resource will succeed even if errors occur during deleting in node pool
      resources. Using this parameter may result in orphaned resources in the
      node pool.
    name: Required. The resource name the AwsNodePool to delete. `AwsNodePool`
      names are formatted as
      `projects//locations//awsClusters//awsNodePools/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud resource names.
    validateOnly: If set, only validate the request, but do not actually
      delete the node pool.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  ignoreErrors = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  validateOnly = _messages.BooleanField(5)


class GkemulticloudProjectsLocationsAwsClustersAwsNodePoolsGetRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAwsClustersAwsNodePoolsGetRequest
  object.

  Fields:
    name: Required. The name of the AwsNodePool resource to describe.
      `AwsNodePool` names are formatted as
      `projects//locations//awsClusters//awsNodePools/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud resource names.
  """

  name = _messages.StringField(1, required=True)


class GkemulticloudProjectsLocationsAwsClustersAwsNodePoolsListRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAwsClustersAwsNodePoolsListRequest
  object.

  Fields:
    pageSize: The maximum number of items to return. If not specified, a
      default value of 50 will be used by the service. Regardless of the
      pageSize value, the response can include a partial list and a caller
      should only rely on response's nextPageToken to determine if there are
      more instances left to be queried.
    pageToken: The `nextPageToken` value returned from a previous
      awsNodePools.list request, if any.
    parent: Required. The parent `AwsCluster` which owns this collection of
      AwsNodePool resources. `AwsCluster` names are formatted as
      `projects//locations//awsClusters/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud resource names.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class GkemulticloudProjectsLocationsAwsClustersAwsNodePoolsPatchRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAwsClustersAwsNodePoolsPatchRequest
  object.

  Fields:
    googleCloudGkemulticloudV1AwsNodePool: A
      GoogleCloudGkemulticloudV1AwsNodePool resource to be passed as the
      request body.
    name: The name of this resource. Node pool names are formatted as
      `projects//locations//awsClusters//awsNodePools/`. For more details on
      Google Cloud resource names, see [Resource
      Names](https://cloud.google.com/apis/design/resource_names)
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field. The elements of the repeated paths field can
      only include these fields from AwsNodePool: * `annotations`. *
      `version`. * `autoscaling.min_node_count`. *
      `autoscaling.max_node_count`. * `config.config_encryption.kms_key_arn`.
      * `config.security_group_ids`. * `config.root_volume.iops`. *
      `config.root_volume.throughput`. * `config.root_volume.kms_key_arn`. *
      `config.root_volume.volume_type`. * `config.root_volume.size_gib`. *
      `config.proxy_config`. * `config.proxy_config.secret_arn`. *
      `config.proxy_config.secret_version`. * `config.ssh_config`. *
      `config.ssh_config.ec2_key_pair`. * `config.instance_placement.tenancy`.
      * `config.iam_instance_profile`. * `config.labels`. * `config.tags`. *
      `config.autoscaling_metrics_collection`. *
      `config.autoscaling_metrics_collection.granularity`. *
      `config.autoscaling_metrics_collection.metrics`. *
      `config.instance_type`. * `management.auto_repair`. * `management`. *
      `update_settings`. * `update_settings.surge_settings`. *
      `update_settings.surge_settings.max_surge`. *
      `update_settings.surge_settings.max_unavailable`.
    validateOnly: If set, only validate the request, but don't actually update
      the node pool.
  """

  googleCloudGkemulticloudV1AwsNodePool = _messages.MessageField('GoogleCloudGkemulticloudV1AwsNodePool', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class GkemulticloudProjectsLocationsAwsClustersAwsNodePoolsRollbackRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAwsClustersAwsNodePoolsRollbackRequest
  object.

  Fields:
    googleCloudGkemulticloudV1RollbackAwsNodePoolUpdateRequest: A
      GoogleCloudGkemulticloudV1RollbackAwsNodePoolUpdateRequest resource to
      be passed as the request body.
    name: Required. The name of the AwsNodePool resource to rollback.
      `AwsNodePool` names are formatted as
      `projects//locations//awsClusters//awsNodePools/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud resource names.
  """

  googleCloudGkemulticloudV1RollbackAwsNodePoolUpdateRequest = _messages.MessageField('GoogleCloudGkemulticloudV1RollbackAwsNodePoolUpdateRequest', 1)
  name = _messages.StringField(2, required=True)


class GkemulticloudProjectsLocationsAwsClustersCreateRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAwsClustersCreateRequest object.

  Fields:
    awsClusterId: Required. A client provided ID the resource. Must be unique
      within the parent resource. The provided ID will be part of the
      AwsCluster resource name formatted as
      `projects//locations//awsClusters/`. Valid characters are `/a-z-/`.
      Cannot be longer than 63 characters.
    googleCloudGkemulticloudV1AwsCluster: A
      GoogleCloudGkemulticloudV1AwsCluster resource to be passed as the
      request body.
    parent: Required. The parent location where this AwsCluster resource will
      be created. Location names are formatted as `projects//locations/`. See
      [Resource Names](https://cloud.google.com/apis/design/resource_names)
      for more details on Google Cloud resource names.
    validateOnly: If set, only validate the request, but do not actually
      create the cluster.
  """

  awsClusterId = _messages.StringField(1)
  googleCloudGkemulticloudV1AwsCluster = _messages.MessageField('GoogleCloudGkemulticloudV1AwsCluster', 2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class GkemulticloudProjectsLocationsAwsClustersDeleteRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAwsClustersDeleteRequest object.

  Fields:
    allowMissing: If set to true, and the AwsCluster resource is not found,
      the request will succeed but no action will be taken on the server and a
      completed Operation will be returned. Useful for idempotent deletion.
    etag: The current etag of the AwsCluster. Allows clients to perform
      deletions through optimistic concurrency control. If the provided etag
      does not match the current etag of the cluster, the request will fail
      and an ABORTED error will be returned.
    ignoreErrors: Optional. If set to true, the deletion of AwsCluster
      resource will succeed even if errors occur during deleting in cluster
      resources. Using this parameter may result in orphaned resources in the
      cluster.
    name: Required. The resource name the AwsCluster to delete. `AwsCluster`
      names are formatted as `projects//locations//awsClusters/`. See
      [Resource Names](https://cloud.google.com/apis/design/resource_names)
      for more details on Google Cloud Platform resource names.
    validateOnly: If set, only validate the request, but do not actually
      delete the resource.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  ignoreErrors = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  validateOnly = _messages.BooleanField(5)


class GkemulticloudProjectsLocationsAwsClustersGenerateAwsAccessTokenRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAwsClustersGenerateAwsAccessTokenRequest
  object.

  Fields:
    awsCluster: Required. The name of the AwsCluster resource to authenticate
      to. `AwsCluster` names are formatted as
      `projects//locations//awsClusters/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud resource names.
  """

  awsCluster = _messages.StringField(1, required=True)


class GkemulticloudProjectsLocationsAwsClustersGenerateAwsClusterAgentTokenRequest(_messages.Message):
  r"""A
  GkemulticloudProjectsLocationsAwsClustersGenerateAwsClusterAgentTokenRequest
  object.

  Fields:
    awsCluster: Required.
    googleCloudGkemulticloudV1GenerateAwsClusterAgentTokenRequest: A
      GoogleCloudGkemulticloudV1GenerateAwsClusterAgentTokenRequest resource
      to be passed as the request body.
  """

  awsCluster = _messages.StringField(1, required=True)
  googleCloudGkemulticloudV1GenerateAwsClusterAgentTokenRequest = _messages.MessageField('GoogleCloudGkemulticloudV1GenerateAwsClusterAgentTokenRequest', 2)


class GkemulticloudProjectsLocationsAwsClustersGetJwksRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAwsClustersGetJwksRequest object.

  Fields:
    awsCluster: Required. The AwsCluster, which owns the JsonWebKeys. Format:
      projects/{project}/locations/{location}/awsClusters/{cluster}
  """

  awsCluster = _messages.StringField(1, required=True)


class GkemulticloudProjectsLocationsAwsClustersGetRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAwsClustersGetRequest object.

  Fields:
    name: Required. The name of the AwsCluster resource to describe.
      `AwsCluster` names are formatted as `projects//locations//awsClusters/`.
      See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud Platform resource names.
  """

  name = _messages.StringField(1, required=True)


class GkemulticloudProjectsLocationsAwsClustersListRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAwsClustersListRequest object.

  Fields:
    pageSize: The maximum number of items to return. If not specified, a
      default value of 50 will be used by the service. Regardless of the
      pageSize value, the response can include a partial list and a caller
      should only rely on response's nextPageToken to determine if there are
      more instances left to be queried.
    pageToken: The `nextPageToken` value returned from a previous
      awsClusters.list request, if any.
    parent: Required. The parent location which owns this collection of
      AwsCluster resources. Location names are formatted as
      `projects//locations/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud Platform resource names.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class GkemulticloudProjectsLocationsAwsClustersPatchRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAwsClustersPatchRequest object.

  Fields:
    googleCloudGkemulticloudV1AwsCluster: A
      GoogleCloudGkemulticloudV1AwsCluster resource to be passed as the
      request body.
    name: The name of this resource. Cluster names are formatted as
      `projects//locations//awsClusters/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud Platform resource names.
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field. The elements of the repeated paths field can
      only include these fields from AwsCluster: * `description`. *
      `annotations`. * `control_plane.version`. * `authorization.admin_users`.
      * `authorization.admin_groups`. *
      `binary_authorization.evaluation_mode`. *
      `control_plane.aws_services_authentication.role_arn`. *
      `control_plane.aws_services_authentication.role_session_name`. *
      `control_plane.config_encryption.kms_key_arn`. *
      `control_plane.instance_type`. * `control_plane.security_group_ids`. *
      `control_plane.proxy_config`. * `control_plane.proxy_config.secret_arn`.
      * `control_plane.proxy_config.secret_version`. *
      `control_plane.root_volume.size_gib`. *
      `control_plane.root_volume.volume_type`. *
      `control_plane.root_volume.iops`. *
      `control_plane.root_volume.throughput`. *
      `control_plane.root_volume.kms_key_arn`. * `control_plane.ssh_config`. *
      `control_plane.ssh_config.ec2_key_pair`. *
      `control_plane.instance_placement.tenancy`. *
      `control_plane.iam_instance_profile`. *
      `logging_config.component_config.enable_components`. *
      `control_plane.tags`. *
      `monitoring_config.managed_prometheus_config.enabled`. *
      `networking.per_node_pool_sg_rules_disabled`.
    validateOnly: If set, only validate the request, but do not actually
      update the cluster.
  """

  googleCloudGkemulticloudV1AwsCluster = _messages.MessageField('GoogleCloudGkemulticloudV1AwsCluster', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class GkemulticloudProjectsLocationsAwsClustersWellKnownGetOpenidConfigurationRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAwsClustersWellKnownGetOpenidConfigurati
  onRequest object.

  Fields:
    awsCluster: Required. The AwsCluster, which owns the OIDC discovery
      document. Format:
      projects/{project}/locations/{location}/awsClusters/{cluster}
  """

  awsCluster = _messages.StringField(1, required=True)


class GkemulticloudProjectsLocationsAzureClientsCreateRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAzureClientsCreateRequest object.

  Fields:
    azureClientId: Required. A client provided ID the resource. Must be unique
      within the parent resource. The provided ID will be part of the
      AzureClient resource name formatted as
      `projects//locations//azureClients/`. Valid characters are `/a-z-/`.
      Cannot be longer than 63 characters.
    googleCloudGkemulticloudV1AzureClient: A
      GoogleCloudGkemulticloudV1AzureClient resource to be passed as the
      request body.
    parent: Required. The parent location where this AzureClient resource will
      be created. Location names are formatted as `projects//locations/`. See
      [Resource Names](https://cloud.google.com/apis/design/resource_names)
      for more details on Google Cloud resource names.
    validateOnly: If set, only validate the request, but do not actually
      create the client.
  """

  azureClientId = _messages.StringField(1)
  googleCloudGkemulticloudV1AzureClient = _messages.MessageField('GoogleCloudGkemulticloudV1AzureClient', 2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class GkemulticloudProjectsLocationsAzureClientsDeleteRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAzureClientsDeleteRequest object.

  Fields:
    allowMissing: If set to true, and the AzureClient resource is not found,
      the request will succeed but no action will be taken on the server and a
      completed Operation will be returned. Useful for idempotent deletion.
    name: Required. The resource name the AzureClient to delete. AzureClient
      names are formatted as `projects//locations//azureClients/`. See
      [Resource Names](https://cloud.google.com/apis/design/resource_names)
      for more details on Google Cloud resource names.
    validateOnly: If set, only validate the request, but do not actually
      delete the resource.
  """

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  validateOnly = _messages.BooleanField(3)


class GkemulticloudProjectsLocationsAzureClientsGetRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAzureClientsGetRequest object.

  Fields:
    name: Required. The name of the AzureClient resource to describe.
      AzureClient names are formatted as `projects//locations//azureClients/`.
      See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud resource names.
  """

  name = _messages.StringField(1, required=True)


class GkemulticloudProjectsLocationsAzureClientsListRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAzureClientsListRequest object.

  Fields:
    pageSize: The maximum number of items to return. If not specified, a
      default value of 50 will be used by the service. Regardless of the
      pageSize value, the response can include a partial list and a caller
      should only rely on response's nextPageToken to determine if there are
      more instances left to be queried.
    pageToken: The `nextPageToken` value returned from a previous
      azureClients.list request, if any.
    parent: Required. The parent location which owns this collection of
      AzureClient resources. Location names are formatted as
      `projects//locations/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud Platform resource names.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class GkemulticloudProjectsLocationsAzureClustersAzureNodePoolsCreateRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAzureClustersAzureNodePoolsCreateRequest
  object.

  Fields:
    azureNodePoolId: Required. A client provided ID the resource. Must be
      unique within the parent resource. The provided ID will be part of the
      AzureNodePool resource name formatted as
      `projects//locations//azureClusters//azureNodePools/`. Valid characters
      are `/a-z-/`. Cannot be longer than 63 characters.
    googleCloudGkemulticloudV1AzureNodePool: A
      GoogleCloudGkemulticloudV1AzureNodePool resource to be passed as the
      request body.
    parent: Required. The AzureCluster resource where this node pool will be
      created. `AzureCluster` names are formatted as
      `projects//locations//azureClusters/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud resource names.
    validateOnly: If set, only validate the request, but do not actually
      create the node pool.
  """

  azureNodePoolId = _messages.StringField(1)
  googleCloudGkemulticloudV1AzureNodePool = _messages.MessageField('GoogleCloudGkemulticloudV1AzureNodePool', 2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class GkemulticloudProjectsLocationsAzureClustersAzureNodePoolsDeleteRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAzureClustersAzureNodePoolsDeleteRequest
  object.

  Fields:
    allowMissing: If set to true, and the AzureNodePool resource is not found,
      the request will succeed but no action will be taken on the server and a
      completed Operation will be returned. Useful for idempotent deletion.
    etag: The current ETag of the AzureNodePool. Allows clients to perform
      deletions through optimistic concurrency control. If the provided ETag
      does not match the current etag of the node pool, the request will fail
      and an ABORTED error will be returned.
    ignoreErrors: Optional. If set to true, the deletion of AzureNodePool
      resource will succeed even if errors occur during deleting in node pool
      resources. Using this parameter may result in orphaned resources in the
      node pool.
    name: Required. The resource name the AzureNodePool to delete.
      `AzureNodePool` names are formatted as
      `projects//locations//azureClusters//azureNodePools/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud resource names.
    validateOnly: If set, only validate the request, but do not actually
      delete the node pool.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  ignoreErrors = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  validateOnly = _messages.BooleanField(5)


class GkemulticloudProjectsLocationsAzureClustersAzureNodePoolsGetRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAzureClustersAzureNodePoolsGetRequest
  object.

  Fields:
    name: Required. The name of the AzureNodePool resource to describe.
      `AzureNodePool` names are formatted as
      `projects//locations//azureClusters//azureNodePools/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud resource names.
  """

  name = _messages.StringField(1, required=True)


class GkemulticloudProjectsLocationsAzureClustersAzureNodePoolsListRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAzureClustersAzureNodePoolsListRequest
  object.

  Fields:
    pageSize: The maximum number of items to return. If not specified, a
      default value of 50 will be used by the service. Regardless of the
      pageSize value, the response can include a partial list and a caller
      should only rely on response's nextPageToken to determine if there are
      more instances left to be queried.
    pageToken: The `nextPageToken` value returned from a previous
      azureNodePools.list request, if any.
    parent: Required. The parent `AzureCluster` which owns this collection of
      AzureNodePool resources. `AzureCluster` names are formatted as
      `projects//locations//azureClusters/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud resource names.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class GkemulticloudProjectsLocationsAzureClustersAzureNodePoolsPatchRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAzureClustersAzureNodePoolsPatchRequest
  object.

  Fields:
    googleCloudGkemulticloudV1AzureNodePool: A
      GoogleCloudGkemulticloudV1AzureNodePool resource to be passed as the
      request body.
    name: The name of this resource. Node pool names are formatted as
      `projects//locations//azureClusters//azureNodePools/`. For more details
      on Google Cloud resource names, see [Resource
      Names](https://cloud.google.com/apis/design/resource_names)
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field. The elements of the repeated paths field can
      only include these fields from AzureNodePool: *. `annotations`. *
      `version`. * `autoscaling.min_node_count`. *
      `autoscaling.max_node_count`. * `config.ssh_config.authorized_key`. *
      `management.auto_repair`. * `management`.
    validateOnly: If set, only validate the request, but don't actually update
      the node pool.
  """

  googleCloudGkemulticloudV1AzureNodePool = _messages.MessageField('GoogleCloudGkemulticloudV1AzureNodePool', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class GkemulticloudProjectsLocationsAzureClustersCreateRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAzureClustersCreateRequest object.

  Fields:
    azureClusterId: Required. A client provided ID the resource. Must be
      unique within the parent resource. The provided ID will be part of the
      AzureCluster resource name formatted as
      `projects//locations//azureClusters/`. Valid characters are `/a-z-/`.
      Cannot be longer than 63 characters.
    googleCloudGkemulticloudV1AzureCluster: A
      GoogleCloudGkemulticloudV1AzureCluster resource to be passed as the
      request body.
    parent: Required. The parent location where this AzureCluster resource
      will be created. Location names are formatted as `projects//locations/`.
      See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud resource names.
    validateOnly: If set, only validate the request, but do not actually
      create the cluster.
  """

  azureClusterId = _messages.StringField(1)
  googleCloudGkemulticloudV1AzureCluster = _messages.MessageField('GoogleCloudGkemulticloudV1AzureCluster', 2)
  parent = _messages.StringField(3, required=True)
  validateOnly = _messages.BooleanField(4)


class GkemulticloudProjectsLocationsAzureClustersDeleteRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAzureClustersDeleteRequest object.

  Fields:
    allowMissing: If set to true, and the AzureCluster resource is not found,
      the request will succeed but no action will be taken on the server and a
      completed Operation will be returned. Useful for idempotent deletion.
    etag: The current etag of the AzureCluster. Allows clients to perform
      deletions through optimistic concurrency control. If the provided etag
      does not match the current etag of the cluster, the request will fail
      and an ABORTED error will be returned.
    ignoreErrors: Optional. If set to true, the deletion of AzureCluster
      resource will succeed even if errors occur during deleting in cluster
      resources. Using this parameter may result in orphaned resources in the
      cluster.
    name: Required. The resource name the AzureCluster to delete.
      `AzureCluster` names are formatted as
      `projects//locations//azureClusters/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud Platform resource names.
    validateOnly: If set, only validate the request, but do not actually
      delete the resource.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  ignoreErrors = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  validateOnly = _messages.BooleanField(5)


class GkemulticloudProjectsLocationsAzureClustersGenerateAzureAccessTokenRequest(_messages.Message):
  r"""A
  GkemulticloudProjectsLocationsAzureClustersGenerateAzureAccessTokenRequest
  object.

  Fields:
    azureCluster: Required. The name of the AzureCluster resource to
      authenticate to. `AzureCluster` names are formatted as
      `projects//locations//azureClusters/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud resource names.
  """

  azureCluster = _messages.StringField(1, required=True)


class GkemulticloudProjectsLocationsAzureClustersGenerateAzureClusterAgentTokenRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAzureClustersGenerateAzureClusterAgentTo
  kenRequest object.

  Fields:
    azureCluster: Required.
    googleCloudGkemulticloudV1GenerateAzureClusterAgentTokenRequest: A
      GoogleCloudGkemulticloudV1GenerateAzureClusterAgentTokenRequest resource
      to be passed as the request body.
  """

  azureCluster = _messages.StringField(1, required=True)
  googleCloudGkemulticloudV1GenerateAzureClusterAgentTokenRequest = _messages.MessageField('GoogleCloudGkemulticloudV1GenerateAzureClusterAgentTokenRequest', 2)


class GkemulticloudProjectsLocationsAzureClustersGetJwksRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAzureClustersGetJwksRequest object.

  Fields:
    azureCluster: Required. The AzureCluster, which owns the JsonWebKeys.
      Format: `projects//locations//azureClusters/`
  """

  azureCluster = _messages.StringField(1, required=True)


class GkemulticloudProjectsLocationsAzureClustersGetRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAzureClustersGetRequest object.

  Fields:
    name: Required. The name of the AzureCluster resource to describe.
      `AzureCluster` names are formatted as
      `projects//locations//azureClusters/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud Platform resource names.
  """

  name = _messages.StringField(1, required=True)


class GkemulticloudProjectsLocationsAzureClustersListRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAzureClustersListRequest object.

  Fields:
    pageSize: The maximum number of items to return. If not specified, a
      default value of 50 will be used by the service. Regardless of the
      pageSize value, the response can include a partial list and a caller
      should only rely on response's nextPageToken to determine if there are
      more instances left to be queried.
    pageToken: The `nextPageToken` value returned from a previous
      azureClusters.list request, if any.
    parent: Required. The parent location which owns this collection of
      AzureCluster resources. Location names are formatted as
      `projects//locations/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud Platform resource names.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class GkemulticloudProjectsLocationsAzureClustersPatchRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAzureClustersPatchRequest object.

  Fields:
    googleCloudGkemulticloudV1AzureCluster: A
      GoogleCloudGkemulticloudV1AzureCluster resource to be passed as the
      request body.
    name: The name of this resource. Cluster names are formatted as
      `projects//locations//azureClusters/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud Platform resource names.
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field. The elements of the repeated paths field can
      only include these fields from AzureCluster: * `description`. *
      `azureClient`. * `control_plane.version`. * `control_plane.vm_size`. *
      `annotations`. * `authorization.admin_users`. *
      `authorization.admin_groups`. * `control_plane.root_volume.size_gib`. *
      `azure_services_authentication`. *
      `azure_services_authentication.tenant_id`. *
      `azure_services_authentication.application_id`. *
      `control_plane.proxy_config`. *
      `control_plane.proxy_config.resource_group_id`. *
      `control_plane.proxy_config.secret_id`. *
      `control_plane.ssh_config.authorized_key`. *
      `logging_config.component_config.enable_components` *
      `monitoring_config.managed_prometheus_config.enabled`.
    validateOnly: If set, only validate the request, but do not actually
      update the cluster.
  """

  googleCloudGkemulticloudV1AzureCluster = _messages.MessageField('GoogleCloudGkemulticloudV1AzureCluster', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)
  validateOnly = _messages.BooleanField(4)


class GkemulticloudProjectsLocationsAzureClustersWellKnownGetOpenidConfigurationRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsAzureClustersWellKnownGetOpenidConfigura
  tionRequest object.

  Fields:
    azureCluster: Required. The AzureCluster, which owns the OIDC discovery
      document. Format: projects//locations//azureClusters/
  """

  azureCluster = _messages.StringField(1, required=True)


class GkemulticloudProjectsLocationsGenerateAttachedClusterInstallManifestRequest(_messages.Message):
  r"""A
  GkemulticloudProjectsLocationsGenerateAttachedClusterInstallManifestRequest
  object.

  Fields:
    attachedClusterId: Required. A client provided ID of the resource. Must be
      unique within the parent resource. The provided ID will be part of the
      AttachedCluster resource name formatted as
      `projects//locations//attachedClusters/`. Valid characters are `/a-z-/`.
      Cannot be longer than 63 characters. When generating an install manifest
      for importing an existing Membership resource, the attached_cluster_id
      field must be the Membership id. Membership names are formatted as
      `projects//locations//memberships/`.
    parent: Required. The parent location where this AttachedCluster resource
      will be created. Location names are formatted as `projects//locations/`.
      See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud resource names.
    platformVersion: Required. The platform version for the cluster (e.g.
      `1.19.0-gke.1000`). You can list all supported versions on a given
      Google Cloud region by calling GetAttachedServerConfig.
    proxyConfig_kubernetesSecret_name: Name of the kubernetes secret.
    proxyConfig_kubernetesSecret_namespace: Namespace in which the kubernetes
      secret is stored.
  """

  attachedClusterId = _messages.StringField(1)
  parent = _messages.StringField(2, required=True)
  platformVersion = _messages.StringField(3)
  proxyConfig_kubernetesSecret_name = _messages.StringField(4)
  proxyConfig_kubernetesSecret_namespace = _messages.StringField(5)


class GkemulticloudProjectsLocationsGetAttachedServerConfigRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsGetAttachedServerConfigRequest object.

  Fields:
    name: Required. The name of the AttachedServerConfig resource to describe.
      `AttachedServerConfig` names are formatted as
      `projects//locations//attachedServerConfig`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud resource names.
  """

  name = _messages.StringField(1, required=True)


class GkemulticloudProjectsLocationsGetAwsServerConfigRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsGetAwsServerConfigRequest object.

  Fields:
    name: Required. The name of the AwsServerConfig resource to describe.
      `AwsServerConfig` names are formatted as
      `projects//locations//awsServerConfig`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud resource names.
  """

  name = _messages.StringField(1, required=True)


class GkemulticloudProjectsLocationsGetAzureServerConfigRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsGetAzureServerConfigRequest object.

  Fields:
    name: Required. The name of the AzureServerConfig resource to describe.
      `AzureServerConfig` names are formatted as
      `projects//locations//azureServerConfig`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud resource names.
  """

  name = _messages.StringField(1, required=True)


class GkemulticloudProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsOperationsCancelRequest object.

  Fields:
    googleLongrunningCancelOperationRequest: A
      GoogleLongrunningCancelOperationRequest resource to be passed as the
      request body.
    name: The name of the operation resource to be cancelled.
  """

  googleLongrunningCancelOperationRequest = _messages.MessageField('GoogleLongrunningCancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class GkemulticloudProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class GkemulticloudProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class GkemulticloudProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A GkemulticloudProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class GoogleCloudGkemulticloudV1AttachedCluster(_messages.Message):
  r"""An Anthos cluster running on customer own infrastructure.

  Enums:
    StateValueValuesEnum: Output only. The current state of the cluster.

  Messages:
    AnnotationsValue: Optional. Annotations on the cluster. This field has the
      same restrictions as Kubernetes annotations. The total size of all keys
      and values combined is limited to 256k. Key can have 2 segments: prefix
      (optional) and name (required), separated by a slash (/). Prefix must be
      a DNS subdomain. Name must be 63 characters or less, begin and end with
      alphanumerics, with dashes (-), underscores (_), dots (.), and
      alphanumerics between.
    TagsValue: Optional. Input only. Tag keys and values directly bound to
      this resource. The tag key must be specified in the format `/,` where
      the tag namespace is the ID of the organization or name of the project
      that the tag key is defined in. The short name of a tag key or value can
      have a maximum length of 256 characters. The permitted character set for
      the short name includes UTF-8 encoded Unicode characters except single
      quotation marks (`'`), double quotation marks (`"`), backslashes (`\`),
      and forward slashes (`/`). See [Tags](https://cloud.google.com/resource-
      manager/docs/tags/tags-overview) for more details on Google Cloud
      Platform tags.

  Fields:
    annotations: Optional. Annotations on the cluster. This field has the same
      restrictions as Kubernetes annotations. The total size of all keys and
      values combined is limited to 256k. Key can have 2 segments: prefix
      (optional) and name (required), separated by a slash (/). Prefix must be
      a DNS subdomain. Name must be 63 characters or less, begin and end with
      alphanumerics, with dashes (-), underscores (_), dots (.), and
      alphanumerics between.
    authorization: Optional. Configuration related to the cluster RBAC
      settings.
    binaryAuthorization: Optional. Binary Authorization configuration for this
      cluster.
    clusterRegion: Output only. The region where this cluster runs. For EKS
      clusters, this is a AWS region. For AKS clusters, this is an Azure
      region.
    createTime: Output only. The time at which this cluster was registered.
    description: Optional. A human readable description of this cluster.
      Cannot be longer than 255 UTF-8 encoded bytes.
    distribution: Required. The Kubernetes distribution of the underlying
      attached cluster. Supported values: ["eks", "aks", "generic"].
    errors: Output only. A set of errors found in the cluster.
    etag: Allows clients to perform consistent read-modify-writes through
      optimistic concurrency control. Can be sent on update and delete
      requests to ensure the client has an up-to-date value before proceeding.
    fleet: Required. Fleet configuration.
    kubernetesVersion: Output only. The Kubernetes version of the cluster.
    loggingConfig: Optional. Logging configuration for this cluster.
    monitoringConfig: Optional. Monitoring configuration for this cluster.
    name: The name of this resource. Cluster names are formatted as
      `projects//locations//attachedClusters/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud Platform resource names.
    oidcConfig: Required. OpenID Connect (OIDC) configuration for the cluster.
    platformVersion: Required. The platform version for the cluster (e.g.
      `1.19.0-gke.1000`). You can list all supported versions on a given
      Google Cloud region by calling GetAttachedServerConfig.
    proxyConfig: Optional. Proxy configuration for outbound HTTP(S) traffic.
    reconciling: Output only. If set, there are currently changes in flight to
      the cluster.
    securityPostureConfig: Optional. Security Posture configuration for this
      cluster.
    state: Output only. The current state of the cluster.
    systemComponentsConfig: Optional. Kubernetes configurationss for auto-
      installed components on the cluster.
    tags: Optional. Input only. Tag keys and values directly bound to this
      resource. The tag key must be specified in the format `/,` where the tag
      namespace is the ID of the organization or name of the project that the
      tag key is defined in. The short name of a tag key or value can have a
      maximum length of 256 characters. The permitted character set for the
      short name includes UTF-8 encoded Unicode characters except single
      quotation marks (`'`), double quotation marks (`"`), backslashes (`\`),
      and forward slashes (`/`). See [Tags](https://cloud.google.com/resource-
      manager/docs/tags/tags-overview) for more details on Google Cloud
      Platform tags.
    uid: Output only. A globally unique identifier for the cluster.
    updateTime: Output only. The time at which this cluster was last updated.
    workloadIdentityConfig: Output only. Workload Identity settings.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the cluster.

    Values:
      STATE_UNSPECIFIED: Not set.
      PROVISIONING: The PROVISIONING state indicates the cluster is being
        registered.
      RUNNING: The RUNNING state indicates the cluster has been register and
        is fully usable.
      RECONCILING: The RECONCILING state indicates that some work is actively
        being done on the cluster, such as upgrading software components.
      STOPPING: The STOPPING state indicates the cluster is being de-
        registered.
      ERROR: The ERROR state indicates the cluster is in a broken
        unrecoverable state.
      DEGRADED: The DEGRADED state indicates the cluster requires user action
        to restore full functionality.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONING = 1
    RUNNING = 2
    RECONCILING = 3
    STOPPING = 4
    ERROR = 5
    DEGRADED = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Annotations on the cluster. This field has the same
    restrictions as Kubernetes annotations. The total size of all keys and
    values combined is limited to 256k. Key can have 2 segments: prefix
    (optional) and name (required), separated by a slash (/). Prefix must be a
    DNS subdomain. Name must be 63 characters or less, begin and end with
    alphanumerics, with dashes (-), underscores (_), dots (.), and
    alphanumerics between.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""Optional. Input only. Tag keys and values directly bound to this
    resource. The tag key must be specified in the format `/,` where the tag
    namespace is the ID of the organization or name of the project that the
    tag key is defined in. The short name of a tag key or value can have a
    maximum length of 256 characters. The permitted character set for the
    short name includes UTF-8 encoded Unicode characters except single
    quotation marks (`'`), double quotation marks (`"`), backslashes (`\`),
    and forward slashes (`/`). See [Tags](https://cloud.google.com/resource-
    manager/docs/tags/tags-overview) for more details on Google Cloud Platform
    tags.

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  authorization = _messages.MessageField('GoogleCloudGkemulticloudV1AttachedClustersAuthorization', 2)
  binaryAuthorization = _messages.MessageField('GoogleCloudGkemulticloudV1BinaryAuthorization', 3)
  clusterRegion = _messages.StringField(4)
  createTime = _messages.StringField(5)
  description = _messages.StringField(6)
  distribution = _messages.StringField(7)
  errors = _messages.MessageField('GoogleCloudGkemulticloudV1AttachedClusterError', 8, repeated=True)
  etag = _messages.StringField(9)
  fleet = _messages.MessageField('GoogleCloudGkemulticloudV1Fleet', 10)
  kubernetesVersion = _messages.StringField(11)
  loggingConfig = _messages.MessageField('GoogleCloudGkemulticloudV1LoggingConfig', 12)
  monitoringConfig = _messages.MessageField('GoogleCloudGkemulticloudV1MonitoringConfig', 13)
  name = _messages.StringField(14)
  oidcConfig = _messages.MessageField('GoogleCloudGkemulticloudV1AttachedOidcConfig', 15)
  platformVersion = _messages.StringField(16)
  proxyConfig = _messages.MessageField('GoogleCloudGkemulticloudV1AttachedProxyConfig', 17)
  reconciling = _messages.BooleanField(18)
  securityPostureConfig = _messages.MessageField('GoogleCloudGkemulticloudV1SecurityPostureConfig', 19)
  state = _messages.EnumField('StateValueValuesEnum', 20)
  systemComponentsConfig = _messages.MessageField('GoogleCloudGkemulticloudV1SystemComponentsConfig', 21)
  tags = _messages.MessageField('TagsValue', 22)
  uid = _messages.StringField(23)
  updateTime = _messages.StringField(24)
  workloadIdentityConfig = _messages.MessageField('GoogleCloudGkemulticloudV1WorkloadIdentityConfig', 25)


class GoogleCloudGkemulticloudV1AttachedClusterError(_messages.Message):
  r"""AttachedClusterError describes errors found on attached clusters.

  Fields:
    message: Human-friendly description of the error.
  """

  message = _messages.StringField(1)


class GoogleCloudGkemulticloudV1AttachedClusterGroup(_messages.Message):
  r"""Identities of a group-type subject for Attached clusters.

  Fields:
    group: Required. The name of the group, e.g. `<EMAIL>`.
  """

  group = _messages.StringField(1)


class GoogleCloudGkemulticloudV1AttachedClusterUser(_messages.Message):
  r"""Identities of a user-type subject for Attached clusters.

  Fields:
    username: Required. The name of the user, e.g. `<EMAIL>`.
  """

  username = _messages.StringField(1)


class GoogleCloudGkemulticloudV1AttachedClustersAuthorization(_messages.Message):
  r"""Configuration related to the cluster RBAC settings.

  Fields:
    adminGroups: Optional. Groups of users that can perform operations as a
      cluster admin. A managed ClusterRoleBinding will be created to grant the
      `cluster-admin` ClusterRole to the groups. Up to ten admin groups can be
      provided. For more info on RBAC, see
      https://kubernetes.io/docs/reference/access-authn-authz/rbac/#user-
      facing-roles
    adminUsers: Optional. Users that can perform operations as a cluster
      admin. A managed ClusterRoleBinding will be created to grant the
      `cluster-admin` ClusterRole to the users. Up to ten admin users can be
      provided. For more info on RBAC, see
      https://kubernetes.io/docs/reference/access-authn-authz/rbac/#user-
      facing-roles
  """

  adminGroups = _messages.MessageField('GoogleCloudGkemulticloudV1AttachedClusterGroup', 1, repeated=True)
  adminUsers = _messages.MessageField('GoogleCloudGkemulticloudV1AttachedClusterUser', 2, repeated=True)


class GoogleCloudGkemulticloudV1AttachedOidcConfig(_messages.Message):
  r"""OIDC discovery information of the target cluster. Kubernetes Service
  Account (KSA) tokens are JWT tokens signed by the cluster API server. This
  fields indicates how Google Cloud Platform services validate KSA tokens in
  order to allow system workloads (such as GKE Connect and telemetry agents)
  to authenticate back to Google Cloud Platform. Both clusters with public and
  private issuer URLs are supported. Clusters with public issuers only need to
  specify the `issuer_url` field while clusters with private issuers need to
  provide both `issuer_url` and `oidc_jwks`.

  Fields:
    issuerUrl: A JSON Web Token (JWT) issuer URI. `issuer` must start with
      `https://`.
    jwks: Optional. OIDC verification keys in JWKS format (RFC 7517). It
      contains a list of OIDC verification keys that can be used to verify
      OIDC JWTs. This field is required for cluster that doesn't have a
      publicly available discovery endpoint. When provided, it will be
      directly used to verify the OIDC JWT asserted by the IDP.
  """

  issuerUrl = _messages.StringField(1)
  jwks = _messages.BytesField(2)


class GoogleCloudGkemulticloudV1AttachedPlatformVersionInfo(_messages.Message):
  r"""Information about a supported Attached Clusters platform version.

  Fields:
    version: Platform version name.
  """

  version = _messages.StringField(1)


class GoogleCloudGkemulticloudV1AttachedProxyConfig(_messages.Message):
  r"""Details of a proxy config.

  Fields:
    kubernetesSecret: The Kubernetes Secret resource that contains the HTTP(S)
      proxy configuration. The secret must be a JSON encoded proxy
      configuration as described in https://cloud.google.com/kubernetes-
      engine/multi-cloud/docs/attached/eks/how-to/use-a-proxy#configure-proxy-
      support for EKS clusters and https://cloud.google.com/kubernetes-
      engine/multi-cloud/docs/attached/aks/how-to/use-a-proxy#configure-proxy-
      support for AKS clusters.
  """

  kubernetesSecret = _messages.MessageField('GoogleCloudGkemulticloudV1KubernetesSecret', 1)


class GoogleCloudGkemulticloudV1AttachedServerConfig(_messages.Message):
  r"""AttachedServerConfig provides information about supported Kubernetes
  versions

  Fields:
    name: The resource name of the config.
    validVersions: List of valid platform versions.
  """

  name = _messages.StringField(1)
  validVersions = _messages.MessageField('GoogleCloudGkemulticloudV1AttachedPlatformVersionInfo', 2, repeated=True)


class GoogleCloudGkemulticloudV1AwsAuthorization(_messages.Message):
  r"""Configuration related to the cluster RBAC settings.

  Fields:
    adminGroups: Optional. Groups of users that can perform operations as a
      cluster admin. A managed ClusterRoleBinding will be created to grant the
      `cluster-admin` ClusterRole to the groups. Up to ten admin groups can be
      provided. For more info on RBAC, see
      https://kubernetes.io/docs/reference/access-authn-authz/rbac/#user-
      facing-roles
    adminUsers: Optional. Users that can perform operations as a cluster
      admin. A managed ClusterRoleBinding will be created to grant the
      `cluster-admin` ClusterRole to the users. Up to ten admin users can be
      provided. For more info on RBAC, see
      https://kubernetes.io/docs/reference/access-authn-authz/rbac/#user-
      facing-roles
  """

  adminGroups = _messages.MessageField('GoogleCloudGkemulticloudV1AwsClusterGroup', 1, repeated=True)
  adminUsers = _messages.MessageField('GoogleCloudGkemulticloudV1AwsClusterUser', 2, repeated=True)


class GoogleCloudGkemulticloudV1AwsAutoscalingGroupMetricsCollection(_messages.Message):
  r"""Configuration related to CloudWatch metrics collection in an AWS Auto
  Scaling group.

  Fields:
    granularity: Required. The frequency at which EC2 Auto Scaling sends
      aggregated data to AWS CloudWatch. The only valid value is "1Minute".
    metrics: Optional. The metrics to enable. For a list of valid metrics, see
      https://docs.aws.amazon.com/autoscaling/ec2/APIReference/API_EnableMetri
      csCollection.html. If you specify Granularity and don't specify any
      metrics, all metrics are enabled.
  """

  granularity = _messages.StringField(1)
  metrics = _messages.StringField(2, repeated=True)


class GoogleCloudGkemulticloudV1AwsCluster(_messages.Message):
  r"""An Anthos cluster running on AWS.

  Enums:
    StateValueValuesEnum: Output only. The current state of the cluster.

  Messages:
    AnnotationsValue: Optional. Annotations on the cluster. This field has the
      same restrictions as Kubernetes annotations. The total size of all keys
      and values combined is limited to 256k. Key can have 2 segments: prefix
      (optional) and name (required), separated by a slash (/). Prefix must be
      a DNS subdomain. Name must be 63 characters or less, begin and end with
      alphanumerics, with dashes (-), underscores (_), dots (.), and
      alphanumerics between.

  Fields:
    annotations: Optional. Annotations on the cluster. This field has the same
      restrictions as Kubernetes annotations. The total size of all keys and
      values combined is limited to 256k. Key can have 2 segments: prefix
      (optional) and name (required), separated by a slash (/). Prefix must be
      a DNS subdomain. Name must be 63 characters or less, begin and end with
      alphanumerics, with dashes (-), underscores (_), dots (.), and
      alphanumerics between.
    authorization: Required. Configuration related to the cluster RBAC
      settings.
    awsRegion: Required. The AWS region where the cluster runs. Each Google
      Cloud region supports a subset of nearby AWS regions. You can call
      GetAwsServerConfig to list all supported AWS regions within a given
      Google Cloud region.
    binaryAuthorization: Optional. Binary Authorization configuration for this
      cluster.
    clusterCaCertificate: Output only. PEM encoded x509 certificate of the
      cluster root of trust.
    controlPlane: Required. Configuration related to the cluster control
      plane.
    createTime: Output only. The time at which this cluster was created.
    description: Optional. A human readable description of this cluster.
      Cannot be longer than 255 UTF-8 encoded bytes.
    endpoint: Output only. The endpoint of the cluster's API server.
    errors: Output only. A set of errors found in the cluster.
    etag: Allows clients to perform consistent read-modify-writes through
      optimistic concurrency control. Can be sent on update and delete
      requests to ensure the client has an up-to-date value before proceeding.
    fleet: Required. Fleet configuration.
    loggingConfig: Optional. Logging configuration for this cluster.
    monitoringConfig: Optional. Monitoring configuration for this cluster.
    name: The name of this resource. Cluster names are formatted as
      `projects//locations//awsClusters/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud Platform resource names.
    networking: Required. Cluster-wide networking configuration.
    reconciling: Output only. If set, there are currently changes in flight to
      the cluster.
    state: Output only. The current state of the cluster.
    uid: Output only. A globally unique identifier for the cluster.
    updateTime: Output only. The time at which this cluster was last updated.
    workloadIdentityConfig: Output only. Workload Identity settings.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the cluster.

    Values:
      STATE_UNSPECIFIED: Not set.
      PROVISIONING: The PROVISIONING state indicates the cluster is being
        created.
      RUNNING: The RUNNING state indicates the cluster has been created and is
        fully usable.
      RECONCILING: The RECONCILING state indicates that some work is actively
        being done on the cluster, such as upgrading the control plane
        replicas.
      STOPPING: The STOPPING state indicates the cluster is being deleted.
      ERROR: The ERROR state indicates the cluster is in a broken
        unrecoverable state.
      DEGRADED: The DEGRADED state indicates the cluster requires user action
        to restore full functionality.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONING = 1
    RUNNING = 2
    RECONCILING = 3
    STOPPING = 4
    ERROR = 5
    DEGRADED = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Annotations on the cluster. This field has the same
    restrictions as Kubernetes annotations. The total size of all keys and
    values combined is limited to 256k. Key can have 2 segments: prefix
    (optional) and name (required), separated by a slash (/). Prefix must be a
    DNS subdomain. Name must be 63 characters or less, begin and end with
    alphanumerics, with dashes (-), underscores (_), dots (.), and
    alphanumerics between.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  authorization = _messages.MessageField('GoogleCloudGkemulticloudV1AwsAuthorization', 2)
  awsRegion = _messages.StringField(3)
  binaryAuthorization = _messages.MessageField('GoogleCloudGkemulticloudV1BinaryAuthorization', 4)
  clusterCaCertificate = _messages.StringField(5)
  controlPlane = _messages.MessageField('GoogleCloudGkemulticloudV1AwsControlPlane', 6)
  createTime = _messages.StringField(7)
  description = _messages.StringField(8)
  endpoint = _messages.StringField(9)
  errors = _messages.MessageField('GoogleCloudGkemulticloudV1AwsClusterError', 10, repeated=True)
  etag = _messages.StringField(11)
  fleet = _messages.MessageField('GoogleCloudGkemulticloudV1Fleet', 12)
  loggingConfig = _messages.MessageField('GoogleCloudGkemulticloudV1LoggingConfig', 13)
  monitoringConfig = _messages.MessageField('GoogleCloudGkemulticloudV1MonitoringConfig', 14)
  name = _messages.StringField(15)
  networking = _messages.MessageField('GoogleCloudGkemulticloudV1AwsClusterNetworking', 16)
  reconciling = _messages.BooleanField(17)
  state = _messages.EnumField('StateValueValuesEnum', 18)
  uid = _messages.StringField(19)
  updateTime = _messages.StringField(20)
  workloadIdentityConfig = _messages.MessageField('GoogleCloudGkemulticloudV1WorkloadIdentityConfig', 21)


class GoogleCloudGkemulticloudV1AwsClusterError(_messages.Message):
  r"""AwsClusterError describes errors found on AWS clusters.

  Fields:
    message: Human-friendly description of the error.
  """

  message = _messages.StringField(1)


class GoogleCloudGkemulticloudV1AwsClusterGroup(_messages.Message):
  r"""Identities of a group-type subject for AWS clusters.

  Fields:
    group: Required. The name of the group, e.g. `<EMAIL>`.
  """

  group = _messages.StringField(1)


class GoogleCloudGkemulticloudV1AwsClusterNetworking(_messages.Message):
  r"""ClusterNetworking defines cluster-wide networking configuration. Anthos
  clusters on AWS run on a single VPC. This includes control plane replicas
  and node pool nodes.

  Fields:
    perNodePoolSgRulesDisabled: Optional. Disable the per node pool subnet
      security group rules on the control plane security group. When set to
      true, you must also provide one or more security groups that ensure node
      pools are able to send requests to the control plane on TCP/443 and
      TCP/8132. Failure to do so may result in unavailable node pools.
    podAddressCidrBlocks: Required. All pods in the cluster are assigned an
      IPv4 address from these ranges. Only a single range is supported. This
      field cannot be changed after creation.
    serviceAddressCidrBlocks: Required. All services in the cluster are
      assigned an IPv4 address from these ranges. Only a single range is
      supported. This field cannot be changed after creation.
    vpcId: Required. The VPC associated with the cluster. All component
      clusters (i.e. control plane and node pools) run on a single VPC. This
      field cannot be changed after creation.
  """

  perNodePoolSgRulesDisabled = _messages.BooleanField(1)
  podAddressCidrBlocks = _messages.StringField(2, repeated=True)
  serviceAddressCidrBlocks = _messages.StringField(3, repeated=True)
  vpcId = _messages.StringField(4)


class GoogleCloudGkemulticloudV1AwsClusterUser(_messages.Message):
  r"""Identities of a user-type subject for AWS clusters.

  Fields:
    username: Required. The name of the user, e.g. `<EMAIL>`.
  """

  username = _messages.StringField(1)


class GoogleCloudGkemulticloudV1AwsConfigEncryption(_messages.Message):
  r"""Config encryption for user data.

  Fields:
    kmsKeyArn: Required. The ARN of the AWS KMS key used to encrypt user data.
  """

  kmsKeyArn = _messages.StringField(1)


class GoogleCloudGkemulticloudV1AwsControlPlane(_messages.Message):
  r"""ControlPlane defines common parameters between control plane nodes.

  Messages:
    TagsValue: Optional. A set of AWS resource tags to propagate to all
      underlying managed AWS resources. Specify at most 50 pairs containing
      alphanumerics, spaces, and symbols (.+-=_:@/). Keys can be up to 127
      Unicode characters. Values can be up to 255 Unicode characters.

  Fields:
    awsServicesAuthentication: Required. Authentication configuration for
      management of AWS resources.
    configEncryption: Required. Config encryption for user data.
    databaseEncryption: Required. The ARN of the AWS KMS key used to encrypt
      cluster secrets.
    iamInstanceProfile: Required. The name or ARN of the AWS IAM instance
      profile to assign to each control plane replica.
    instancePlacement: Optional. The placement to use on control plane
      instances. When unspecified, the VPC's default tenancy will be used.
    instanceType: Optional. The AWS instance type. When unspecified, it uses a
      default based on the cluster's version.
    mainVolume: Optional. Configuration related to the main volume provisioned
      for each control plane replica. The main volume is in charge of storing
      all of the cluster's etcd state. Volumes will be provisioned in the
      availability zone associated with the corresponding subnet. When
      unspecified, it defaults to 8 GiB with the GP2 volume type.
    proxyConfig: Optional. Proxy configuration for outbound HTTP(S) traffic.
    rootVolume: Optional. Configuration related to the root volume provisioned
      for each control plane replica. Volumes will be provisioned in the
      availability zone associated with the corresponding subnet. When
      unspecified, it defaults to 32 GiB with the GP2 volume type.
    securityGroupIds: Optional. The IDs of additional security groups to add
      to control plane replicas. The Anthos Multi-Cloud API will automatically
      create and manage security groups with the minimum rules needed for a
      functioning cluster.
    sshConfig: Optional. SSH configuration for how to access the underlying
      control plane machines.
    subnetIds: Required. The list of subnets where control plane replicas will
      run. A replica will be provisioned on each subnet and up to three values
      can be provided. Each subnet must be in a different AWS Availability
      Zone (AZ).
    tags: Optional. A set of AWS resource tags to propagate to all underlying
      managed AWS resources. Specify at most 50 pairs containing
      alphanumerics, spaces, and symbols (.+-=_:@/). Keys can be up to 127
      Unicode characters. Values can be up to 255 Unicode characters.
    version: Required. The Kubernetes version to run on control plane replicas
      (e.g. `1.19.10-gke.1000`). You can list all supported versions on a
      given Google Cloud region by calling GetAwsServerConfig.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""Optional. A set of AWS resource tags to propagate to all underlying
    managed AWS resources. Specify at most 50 pairs containing alphanumerics,
    spaces, and symbols (.+-=_:@/). Keys can be up to 127 Unicode characters.
    Values can be up to 255 Unicode characters.

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  awsServicesAuthentication = _messages.MessageField('GoogleCloudGkemulticloudV1AwsServicesAuthentication', 1)
  configEncryption = _messages.MessageField('GoogleCloudGkemulticloudV1AwsConfigEncryption', 2)
  databaseEncryption = _messages.MessageField('GoogleCloudGkemulticloudV1AwsDatabaseEncryption', 3)
  iamInstanceProfile = _messages.StringField(4)
  instancePlacement = _messages.MessageField('GoogleCloudGkemulticloudV1AwsInstancePlacement', 5)
  instanceType = _messages.StringField(6)
  mainVolume = _messages.MessageField('GoogleCloudGkemulticloudV1AwsVolumeTemplate', 7)
  proxyConfig = _messages.MessageField('GoogleCloudGkemulticloudV1AwsProxyConfig', 8)
  rootVolume = _messages.MessageField('GoogleCloudGkemulticloudV1AwsVolumeTemplate', 9)
  securityGroupIds = _messages.StringField(10, repeated=True)
  sshConfig = _messages.MessageField('GoogleCloudGkemulticloudV1AwsSshConfig', 11)
  subnetIds = _messages.StringField(12, repeated=True)
  tags = _messages.MessageField('TagsValue', 13)
  version = _messages.StringField(14)


class GoogleCloudGkemulticloudV1AwsDatabaseEncryption(_messages.Message):
  r"""Configuration related to application-layer secrets encryption.

  Fields:
    kmsKeyArn: Required. The ARN of the AWS KMS key used to encrypt cluster
      secrets.
  """

  kmsKeyArn = _messages.StringField(1)


class GoogleCloudGkemulticloudV1AwsInstancePlacement(_messages.Message):
  r"""Details of placement information for an instance. Limitations for using
  the `host` tenancy: * T3 instances that use the unlimited CPU credit option
  don't support host tenancy.

  Enums:
    TenancyValueValuesEnum: Required. The tenancy for instance.

  Fields:
    tenancy: Required. The tenancy for instance.
  """

  class TenancyValueValuesEnum(_messages.Enum):
    r"""Required. The tenancy for instance.

    Values:
      TENANCY_UNSPECIFIED: Not set.
      DEFAULT: Use default VPC tenancy.
      DEDICATED: Run a dedicated instance.
      HOST: Launch this instance to a dedicated host.
    """
    TENANCY_UNSPECIFIED = 0
    DEFAULT = 1
    DEDICATED = 2
    HOST = 3

  tenancy = _messages.EnumField('TenancyValueValuesEnum', 1)


class GoogleCloudGkemulticloudV1AwsJsonWebKeys(_messages.Message):
  r"""AwsJsonWebKeys is a valid JSON Web Key Set as specififed in RFC 7517.

  Fields:
    keys: The public component of the keys used by the cluster to sign token
      requests.
  """

  keys = _messages.MessageField('GoogleCloudGkemulticloudV1Jwk', 1, repeated=True)


class GoogleCloudGkemulticloudV1AwsK8sVersionInfo(_messages.Message):
  r"""Kubernetes version information of GKE cluster on AWS.

  Fields:
    enabled: Optional. True if the version is available for cluster creation.
      If a version is enabled for creation, it can be used to create new
      clusters. Otherwise, cluster creation will fail. However, cluster
      upgrade operations may succeed, even if the version is not enabled.
    endOfLife: Optional. True if this cluster version belongs to a minor
      version that has reached its end of life and is no longer in scope to
      receive security and bug fixes.
    endOfLifeDate: Optional. The estimated date (in Pacific Time) when this
      cluster version will reach its end of life. Or if this version is no
      longer supported (the `end_of_life` field is true), this is the actual
      date (in Pacific time) when the version reached its end of life.
    releaseDate: Optional. The date (in Pacific Time) when the cluster version
      was released.
    version: Kubernetes version name.
  """

  enabled = _messages.BooleanField(1)
  endOfLife = _messages.BooleanField(2)
  endOfLifeDate = _messages.MessageField('GoogleTypeDate', 3)
  releaseDate = _messages.MessageField('GoogleTypeDate', 4)
  version = _messages.StringField(5)


class GoogleCloudGkemulticloudV1AwsNodeConfig(_messages.Message):
  r"""Parameters that describe the nodes in a cluster.

  Messages:
    LabelsValue: Optional. The initial labels assigned to nodes of this node
      pool. An object containing a list of "key": value pairs. Example: {
      "name": "wrench", "mass": "1.3kg", "count": "3" }.
    TagsValue: Optional. Key/value metadata to assign to each underlying AWS
      resource. Specify at most 50 pairs containing alphanumerics, spaces, and
      symbols (.+-=_:@/). Keys can be up to 127 Unicode characters. Values can
      be up to 255 Unicode characters.

  Fields:
    autoscalingMetricsCollection: Optional. Configuration related to
      CloudWatch metrics collection on the Auto Scaling group of the node
      pool. When unspecified, metrics collection is disabled.
    configEncryption: Required. Config encryption for user data.
    iamInstanceProfile: Required. The name or ARN of the AWS IAM instance
      profile to assign to nodes in the pool.
    imageType: Optional. The OS image type to use on node pool instances. Can
      be unspecified, or have a value of `ubuntu`. When unspecified, it
      defaults to `ubuntu`.
    instancePlacement: Optional. Placement related info for this node. When
      unspecified, the VPC's default tenancy will be used.
    instanceType: Optional. The EC2 instance type when creating on-Demand
      instances. If unspecified during node pool creation, a default will be
      chosen based on the node pool version, and assigned to this field.
    labels: Optional. The initial labels assigned to nodes of this node pool.
      An object containing a list of "key": value pairs. Example: { "name":
      "wrench", "mass": "1.3kg", "count": "3" }.
    proxyConfig: Optional. Proxy configuration for outbound HTTP(S) traffic.
    rootVolume: Optional. Template for the root volume provisioned for node
      pool nodes. Volumes will be provisioned in the availability zone
      assigned to the node pool subnet. When unspecified, it defaults to 32
      GiB with the GP2 volume type.
    securityGroupIds: Optional. The IDs of additional security groups to add
      to nodes in this pool. The manager will automatically create security
      groups with minimum rules needed for a functioning cluster.
    spotConfig: Optional. Configuration for provisioning EC2 Spot instances
      When specified, the node pool will provision Spot instances from the set
      of spot_config.instance_types. This field is mutually exclusive with
      `instance_type`.
    sshConfig: Optional. The SSH configuration.
    tags: Optional. Key/value metadata to assign to each underlying AWS
      resource. Specify at most 50 pairs containing alphanumerics, spaces, and
      symbols (.+-=_:@/). Keys can be up to 127 Unicode characters. Values can
      be up to 255 Unicode characters.
    taints: Optional. The initial taints assigned to nodes of this node pool.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The initial labels assigned to nodes of this node pool. An
    object containing a list of "key": value pairs. Example: { "name":
    "wrench", "mass": "1.3kg", "count": "3" }.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""Optional. Key/value metadata to assign to each underlying AWS
    resource. Specify at most 50 pairs containing alphanumerics, spaces, and
    symbols (.+-=_:@/). Keys can be up to 127 Unicode characters. Values can
    be up to 255 Unicode characters.

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  autoscalingMetricsCollection = _messages.MessageField('GoogleCloudGkemulticloudV1AwsAutoscalingGroupMetricsCollection', 1)
  configEncryption = _messages.MessageField('GoogleCloudGkemulticloudV1AwsConfigEncryption', 2)
  iamInstanceProfile = _messages.StringField(3)
  imageType = _messages.StringField(4)
  instancePlacement = _messages.MessageField('GoogleCloudGkemulticloudV1AwsInstancePlacement', 5)
  instanceType = _messages.StringField(6)
  labels = _messages.MessageField('LabelsValue', 7)
  proxyConfig = _messages.MessageField('GoogleCloudGkemulticloudV1AwsProxyConfig', 8)
  rootVolume = _messages.MessageField('GoogleCloudGkemulticloudV1AwsVolumeTemplate', 9)
  securityGroupIds = _messages.StringField(10, repeated=True)
  spotConfig = _messages.MessageField('GoogleCloudGkemulticloudV1SpotConfig', 11)
  sshConfig = _messages.MessageField('GoogleCloudGkemulticloudV1AwsSshConfig', 12)
  tags = _messages.MessageField('TagsValue', 13)
  taints = _messages.MessageField('GoogleCloudGkemulticloudV1NodeTaint', 14, repeated=True)


class GoogleCloudGkemulticloudV1AwsNodeManagement(_messages.Message):
  r"""AwsNodeManagement defines the set of node management features turned on
  for an AWS node pool.

  Fields:
    autoRepair: Optional. Whether or not the nodes will be automatically
      repaired. When set to true, the nodes in this node pool will be
      monitored and if they fail health checks consistently over a period of
      time, an automatic repair action will be triggered to replace them with
      new nodes.
  """

  autoRepair = _messages.BooleanField(1)


class GoogleCloudGkemulticloudV1AwsNodePool(_messages.Message):
  r"""An Anthos node pool running on AWS.

  Enums:
    StateValueValuesEnum: Output only. The lifecycle state of the node pool.

  Messages:
    AnnotationsValue: Optional. Annotations on the node pool. This field has
      the same restrictions as Kubernetes annotations. The total size of all
      keys and values combined is limited to 256k. Key can have 2 segments:
      prefix (optional) and name (required), separated by a slash (/). Prefix
      must be a DNS subdomain. Name must be 63 characters or less, begin and
      end with alphanumerics, with dashes (-), underscores (_), dots (.), and
      alphanumerics between.

  Fields:
    annotations: Optional. Annotations on the node pool. This field has the
      same restrictions as Kubernetes annotations. The total size of all keys
      and values combined is limited to 256k. Key can have 2 segments: prefix
      (optional) and name (required), separated by a slash (/). Prefix must be
      a DNS subdomain. Name must be 63 characters or less, begin and end with
      alphanumerics, with dashes (-), underscores (_), dots (.), and
      alphanumerics between.
    autoscaling: Required. Autoscaler configuration for this node pool.
    config: Required. The configuration of the node pool.
    createTime: Output only. The time at which this node pool was created.
    errors: Output only. A set of errors found in the node pool.
    etag: Allows clients to perform consistent read-modify-writes through
      optimistic concurrency control. Can be sent on update and delete
      requests to ensure the client has an up-to-date value before proceeding.
    kubeletConfig: Optional. Node kubelet configs.
    management: Optional. The Management configuration for this node pool.
    maxPodsConstraint: Required. The constraint on the maximum number of pods
      that can be run simultaneously on a node in the node pool.
    name: The name of this resource. Node pool names are formatted as
      `projects//locations//awsClusters//awsNodePools/`. For more details on
      Google Cloud resource names, see [Resource
      Names](https://cloud.google.com/apis/design/resource_names)
    reconciling: Output only. If set, there are currently changes in flight to
      the node pool.
    state: Output only. The lifecycle state of the node pool.
    subnetId: Required. The subnet where the node pool node run.
    uid: Output only. A globally unique identifier for the node pool.
    updateSettings: Optional. Update settings control the speed and disruption
      of the update.
    updateTime: Output only. The time at which this node pool was last
      updated.
    version: Required. The Kubernetes version to run on this node pool (e.g.
      `1.19.10-gke.1000`). You can list all supported versions on a given
      Google Cloud region by calling GetAwsServerConfig.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The lifecycle state of the node pool.

    Values:
      STATE_UNSPECIFIED: Not set.
      PROVISIONING: The PROVISIONING state indicates the node pool is being
        created.
      RUNNING: The RUNNING state indicates the node pool has been created and
        is fully usable.
      RECONCILING: The RECONCILING state indicates that the node pool is being
        reconciled.
      STOPPING: The STOPPING state indicates the node pool is being deleted.
      ERROR: The ERROR state indicates the node pool is in a broken
        unrecoverable state.
      DEGRADED: The DEGRADED state indicates the node pool requires user
        action to restore full functionality.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONING = 1
    RUNNING = 2
    RECONCILING = 3
    STOPPING = 4
    ERROR = 5
    DEGRADED = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Annotations on the node pool. This field has the same
    restrictions as Kubernetes annotations. The total size of all keys and
    values combined is limited to 256k. Key can have 2 segments: prefix
    (optional) and name (required), separated by a slash (/). Prefix must be a
    DNS subdomain. Name must be 63 characters or less, begin and end with
    alphanumerics, with dashes (-), underscores (_), dots (.), and
    alphanumerics between.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  autoscaling = _messages.MessageField('GoogleCloudGkemulticloudV1AwsNodePoolAutoscaling', 2)
  config = _messages.MessageField('GoogleCloudGkemulticloudV1AwsNodeConfig', 3)
  createTime = _messages.StringField(4)
  errors = _messages.MessageField('GoogleCloudGkemulticloudV1AwsNodePoolError', 5, repeated=True)
  etag = _messages.StringField(6)
  kubeletConfig = _messages.MessageField('GoogleCloudGkemulticloudV1NodeKubeletConfig', 7)
  management = _messages.MessageField('GoogleCloudGkemulticloudV1AwsNodeManagement', 8)
  maxPodsConstraint = _messages.MessageField('GoogleCloudGkemulticloudV1MaxPodsConstraint', 9)
  name = _messages.StringField(10)
  reconciling = _messages.BooleanField(11)
  state = _messages.EnumField('StateValueValuesEnum', 12)
  subnetId = _messages.StringField(13)
  uid = _messages.StringField(14)
  updateSettings = _messages.MessageField('GoogleCloudGkemulticloudV1UpdateSettings', 15)
  updateTime = _messages.StringField(16)
  version = _messages.StringField(17)


class GoogleCloudGkemulticloudV1AwsNodePoolAutoscaling(_messages.Message):
  r"""AwsNodePoolAutoscaling contains information required by cluster
  autoscaler to adjust the size of the node pool to the current cluster usage.

  Fields:
    maxNodeCount: Required. Maximum number of nodes in the node pool. Must be
      greater than or equal to min_node_count and less than or equal to 50.
    minNodeCount: Required. Minimum number of nodes in the node pool. Must be
      greater than or equal to 1 and less than or equal to max_node_count.
  """

  maxNodeCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minNodeCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleCloudGkemulticloudV1AwsNodePoolError(_messages.Message):
  r"""AwsNodePoolError describes errors found on AWS node pools.

  Fields:
    message: Human-friendly description of the error.
  """

  message = _messages.StringField(1)


class GoogleCloudGkemulticloudV1AwsOpenIdConfig(_messages.Message):
  r"""AwsOpenIdConfig is an OIDC discovery document for the cluster. See the
  OpenID Connect Discovery 1.0 specification for details.

  Fields:
    claims_supported: Supported claims.
    grant_types: Supported grant types.
    id_token_signing_alg_values_supported: supported ID Token signing
      Algorithms.
    issuer: OIDC Issuer.
    jwks_uri: JSON Web Key uri.
    response_types_supported: Supported response types.
    subject_types_supported: Supported subject types.
  """

  claims_supported = _messages.StringField(1, repeated=True)
  grant_types = _messages.StringField(2, repeated=True)
  id_token_signing_alg_values_supported = _messages.StringField(3, repeated=True)
  issuer = _messages.StringField(4)
  jwks_uri = _messages.StringField(5)
  response_types_supported = _messages.StringField(6, repeated=True)
  subject_types_supported = _messages.StringField(7, repeated=True)


class GoogleCloudGkemulticloudV1AwsProxyConfig(_messages.Message):
  r"""Details of a proxy config stored in AWS Secret Manager.

  Fields:
    secretArn: The ARN of the AWS Secret Manager secret that contains the
      HTTP(S) proxy configuration. The secret must be a JSON encoded proxy
      configuration as described in https://cloud.google.com/kubernetes-
      engine/multi-cloud/docs/aws/how-to/use-a-
      proxy#create_a_proxy_configuration_file
    secretVersion: The version string of the AWS Secret Manager secret that
      contains the HTTP(S) proxy configuration.
  """

  secretArn = _messages.StringField(1)
  secretVersion = _messages.StringField(2)


class GoogleCloudGkemulticloudV1AwsServerConfig(_messages.Message):
  r"""AwsServerConfig is the configuration of GKE cluster on AWS.

  Fields:
    name: The resource name of the config.
    supportedAwsRegions: The list of supported AWS regions.
    validVersions: List of all released Kubernetes versions, including ones
      which are end of life and can no longer be used. Filter by the `enabled`
      property to limit to currently available versions. Valid versions
      supported for both create and update operations
  """

  name = _messages.StringField(1)
  supportedAwsRegions = _messages.StringField(2, repeated=True)
  validVersions = _messages.MessageField('GoogleCloudGkemulticloudV1AwsK8sVersionInfo', 3, repeated=True)


class GoogleCloudGkemulticloudV1AwsServicesAuthentication(_messages.Message):
  r"""Authentication configuration for the management of AWS resources.

  Fields:
    roleArn: Required. The Amazon Resource Name (ARN) of the role that the
      Anthos Multi-Cloud API will assume when managing AWS resources on your
      account.
    roleSessionName: Optional. An identifier for the assumed role session.
      When unspecified, it defaults to `multicloud-service-agent`.
  """

  roleArn = _messages.StringField(1)
  roleSessionName = _messages.StringField(2)


class GoogleCloudGkemulticloudV1AwsSshConfig(_messages.Message):
  r"""SSH configuration for AWS resources.

  Fields:
    ec2KeyPair: Required. The name of the EC2 key pair used to login into
      cluster machines.
  """

  ec2KeyPair = _messages.StringField(1)


class GoogleCloudGkemulticloudV1AwsVolumeTemplate(_messages.Message):
  r"""Configuration template for AWS EBS volumes.

  Enums:
    VolumeTypeValueValuesEnum: Optional. Type of the EBS volume. When
      unspecified, it defaults to GP2 volume.

  Fields:
    iops: Optional. The number of I/O operations per second (IOPS) to
      provision for GP3 volume.
    kmsKeyArn: Optional. The Amazon Resource Name (ARN) of the Customer
      Managed Key (CMK) used to encrypt AWS EBS volumes. If not specified, the
      default Amazon managed key associated to the AWS region where this
      cluster runs will be used.
    sizeGib: Optional. The size of the volume, in GiBs. When unspecified, a
      default value is provided. See the specific reference in the parent
      resource.
    throughput: Optional. The throughput that the volume supports, in MiB/s.
      Only valid if volume_type is GP3. If the volume_type is GP3 and this is
      not specified, it defaults to 125.
    volumeType: Optional. Type of the EBS volume. When unspecified, it
      defaults to GP2 volume.
  """

  class VolumeTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Type of the EBS volume. When unspecified, it defaults to GP2
    volume.

    Values:
      VOLUME_TYPE_UNSPECIFIED: Not set.
      GP2: GP2 (General Purpose SSD volume type).
      GP3: GP3 (General Purpose SSD volume type).
    """
    VOLUME_TYPE_UNSPECIFIED = 0
    GP2 = 1
    GP3 = 2

  iops = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  kmsKeyArn = _messages.StringField(2)
  sizeGib = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  throughput = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  volumeType = _messages.EnumField('VolumeTypeValueValuesEnum', 5)


class GoogleCloudGkemulticloudV1AzureAuthorization(_messages.Message):
  r"""Configuration related to the cluster RBAC settings.

  Fields:
    adminGroups: Optional. Groups of users that can perform operations as a
      cluster admin. A managed ClusterRoleBinding will be created to grant the
      `cluster-admin` ClusterRole to the groups. Up to ten admin groups can be
      provided. For more info on RBAC, see
      https://kubernetes.io/docs/reference/access-authn-authz/rbac/#user-
      facing-roles
    adminUsers: Optional. Users that can perform operations as a cluster
      admin. A managed ClusterRoleBinding will be created to grant the
      `cluster-admin` ClusterRole to the users. Up to ten admin users can be
      provided. For more info on RBAC, see
      https://kubernetes.io/docs/reference/access-authn-authz/rbac/#user-
      facing-roles
  """

  adminGroups = _messages.MessageField('GoogleCloudGkemulticloudV1AzureClusterGroup', 1, repeated=True)
  adminUsers = _messages.MessageField('GoogleCloudGkemulticloudV1AzureClusterUser', 2, repeated=True)


class GoogleCloudGkemulticloudV1AzureClient(_messages.Message):
  r"""`AzureClient` resources hold client authentication information needed by
  the Anthos Multi-Cloud API to manage Azure resources on your Azure
  subscription. When an AzureCluster is created, an `AzureClient` resource
  needs to be provided and all operations on Azure resources associated to
  that cluster will authenticate to Azure services using the given client.
  `AzureClient` resources are immutable and cannot be modified upon creation.
  Each `AzureClient` resource is bound to a single Azure Active Directory
  Application and tenant.

  Messages:
    AnnotationsValue: Optional. Annotations on the resource. This field has
      the same restrictions as Kubernetes annotations. The total size of all
      keys and values combined is limited to 256k. Keys can have 2 segments:
      prefix (optional) and name (required), separated by a slash (/). Prefix
      must be a DNS subdomain. Name must be 63 characters or less, begin and
      end with alphanumerics, with dashes (-), underscores (_), dots (.), and
      alphanumerics between.

  Fields:
    annotations: Optional. Annotations on the resource. This field has the
      same restrictions as Kubernetes annotations. The total size of all keys
      and values combined is limited to 256k. Keys can have 2 segments: prefix
      (optional) and name (required), separated by a slash (/). Prefix must be
      a DNS subdomain. Name must be 63 characters or less, begin and end with
      alphanumerics, with dashes (-), underscores (_), dots (.), and
      alphanumerics between.
    applicationId: Required. The Azure Active Directory Application ID.
    createTime: Output only. The time at which this resource was created.
    name: The name of this resource. `AzureClient` resource names are
      formatted as `projects//locations//azureClients/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud resource names.
    pemCertificate: Output only. The PEM encoded x509 certificate.
    reconciling: Output only. If set, there are currently pending changes to
      the client.
    tenantId: Required. The Azure Active Directory Tenant ID.
    uid: Output only. A globally unique identifier for the client.
    updateTime: Output only. The time at which this client was last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Annotations on the resource. This field has the same
    restrictions as Kubernetes annotations. The total size of all keys and
    values combined is limited to 256k. Keys can have 2 segments: prefix
    (optional) and name (required), separated by a slash (/). Prefix must be a
    DNS subdomain. Name must be 63 characters or less, begin and end with
    alphanumerics, with dashes (-), underscores (_), dots (.), and
    alphanumerics between.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  applicationId = _messages.StringField(2)
  createTime = _messages.StringField(3)
  name = _messages.StringField(4)
  pemCertificate = _messages.StringField(5)
  reconciling = _messages.BooleanField(6)
  tenantId = _messages.StringField(7)
  uid = _messages.StringField(8)
  updateTime = _messages.StringField(9)


class GoogleCloudGkemulticloudV1AzureCluster(_messages.Message):
  r"""An Anthos cluster running on Azure.

  Enums:
    StateValueValuesEnum: Output only. The current state of the cluster.

  Messages:
    AnnotationsValue: Optional. Annotations on the cluster. This field has the
      same restrictions as Kubernetes annotations. The total size of all keys
      and values combined is limited to 256k. Keys can have 2 segments: prefix
      (optional) and name (required), separated by a slash (/). Prefix must be
      a DNS subdomain. Name must be 63 characters or less, begin and end with
      alphanumerics, with dashes (-), underscores (_), dots (.), and
      alphanumerics between.

  Fields:
    annotations: Optional. Annotations on the cluster. This field has the same
      restrictions as Kubernetes annotations. The total size of all keys and
      values combined is limited to 256k. Keys can have 2 segments: prefix
      (optional) and name (required), separated by a slash (/). Prefix must be
      a DNS subdomain. Name must be 63 characters or less, begin and end with
      alphanumerics, with dashes (-), underscores (_), dots (.), and
      alphanumerics between.
    authorization: Required. Configuration related to the cluster RBAC
      settings.
    azureClient: Optional. Name of the AzureClient that contains
      authentication configuration for how the Anthos Multi-Cloud API connects
      to Azure APIs. Either azure_client or azure_services_authentication
      should be provided. The `AzureClient` resource must reside on the same
      Google Cloud Platform project and region as the `AzureCluster`.
      `AzureClient` names are formatted as
      `projects//locations//azureClients/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud resource names.
    azureRegion: Required. The Azure region where the cluster runs. Each
      Google Cloud region supports a subset of nearby Azure regions. You can
      call GetAzureServerConfig to list all supported Azure regions within a
      given Google Cloud region.
    azureServicesAuthentication: Optional. Authentication configuration for
      management of Azure resources. Either azure_client or
      azure_services_authentication should be provided.
    clusterCaCertificate: Output only. PEM encoded x509 certificate of the
      cluster root of trust.
    controlPlane: Required. Configuration related to the cluster control
      plane.
    createTime: Output only. The time at which this cluster was created.
    description: Optional. A human readable description of this cluster.
      Cannot be longer than 255 UTF-8 encoded bytes.
    endpoint: Output only. The endpoint of the cluster's API server.
    errors: Output only. A set of errors found in the cluster.
    etag: Allows clients to perform consistent read-modify-writes through
      optimistic concurrency control. Can be sent on update and delete
      requests to ensure the client has an up-to-date value before proceeding.
    fleet: Required. Fleet configuration.
    loggingConfig: Optional. Logging configuration for this cluster.
    managedResources: Output only. Managed Azure resources for this cluster.
    monitoringConfig: Optional. Monitoring configuration for this cluster.
    name: The name of this resource. Cluster names are formatted as
      `projects//locations//azureClusters/`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud Platform resource names.
    networking: Required. Cluster-wide networking configuration.
    reconciling: Output only. If set, there are currently changes in flight to
      the cluster.
    resourceGroupId: Required. The ARM ID of the resource group where the
      cluster resources are deployed. For example:
      `/subscriptions//resourceGroups/`
    state: Output only. The current state of the cluster.
    uid: Output only. A globally unique identifier for the cluster.
    updateTime: Output only. The time at which this cluster was last updated.
    workloadIdentityConfig: Output only. Workload Identity settings.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the cluster.

    Values:
      STATE_UNSPECIFIED: Not set.
      PROVISIONING: The PROVISIONING state indicates the cluster is being
        created.
      RUNNING: The RUNNING state indicates the cluster has been created and is
        fully usable.
      RECONCILING: The RECONCILING state indicates that some work is actively
        being done on the cluster, such as upgrading the control plane
        replicas.
      STOPPING: The STOPPING state indicates the cluster is being deleted.
      ERROR: The ERROR state indicates the cluster is in a broken
        unrecoverable state.
      DEGRADED: The DEGRADED state indicates the cluster requires user action
        to restore full functionality.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONING = 1
    RUNNING = 2
    RECONCILING = 3
    STOPPING = 4
    ERROR = 5
    DEGRADED = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Annotations on the cluster. This field has the same
    restrictions as Kubernetes annotations. The total size of all keys and
    values combined is limited to 256k. Keys can have 2 segments: prefix
    (optional) and name (required), separated by a slash (/). Prefix must be a
    DNS subdomain. Name must be 63 characters or less, begin and end with
    alphanumerics, with dashes (-), underscores (_), dots (.), and
    alphanumerics between.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  authorization = _messages.MessageField('GoogleCloudGkemulticloudV1AzureAuthorization', 2)
  azureClient = _messages.StringField(3)
  azureRegion = _messages.StringField(4)
  azureServicesAuthentication = _messages.MessageField('GoogleCloudGkemulticloudV1AzureServicesAuthentication', 5)
  clusterCaCertificate = _messages.StringField(6)
  controlPlane = _messages.MessageField('GoogleCloudGkemulticloudV1AzureControlPlane', 7)
  createTime = _messages.StringField(8)
  description = _messages.StringField(9)
  endpoint = _messages.StringField(10)
  errors = _messages.MessageField('GoogleCloudGkemulticloudV1AzureClusterError', 11, repeated=True)
  etag = _messages.StringField(12)
  fleet = _messages.MessageField('GoogleCloudGkemulticloudV1Fleet', 13)
  loggingConfig = _messages.MessageField('GoogleCloudGkemulticloudV1LoggingConfig', 14)
  managedResources = _messages.MessageField('GoogleCloudGkemulticloudV1AzureClusterResources', 15)
  monitoringConfig = _messages.MessageField('GoogleCloudGkemulticloudV1MonitoringConfig', 16)
  name = _messages.StringField(17)
  networking = _messages.MessageField('GoogleCloudGkemulticloudV1AzureClusterNetworking', 18)
  reconciling = _messages.BooleanField(19)
  resourceGroupId = _messages.StringField(20)
  state = _messages.EnumField('StateValueValuesEnum', 21)
  uid = _messages.StringField(22)
  updateTime = _messages.StringField(23)
  workloadIdentityConfig = _messages.MessageField('GoogleCloudGkemulticloudV1WorkloadIdentityConfig', 24)


class GoogleCloudGkemulticloudV1AzureClusterError(_messages.Message):
  r"""AzureClusterError describes errors found on Azure clusters.

  Fields:
    message: Human-friendly description of the error.
  """

  message = _messages.StringField(1)


class GoogleCloudGkemulticloudV1AzureClusterGroup(_messages.Message):
  r"""Identities of a group-type subject for Azure clusters.

  Fields:
    group: Required. The name of the group, e.g. `<EMAIL>`.
  """

  group = _messages.StringField(1)


class GoogleCloudGkemulticloudV1AzureClusterNetworking(_messages.Message):
  r"""ClusterNetworking contains cluster-wide networking configuration.

  Fields:
    podAddressCidrBlocks: Required. The IP address range of the pods in this
      cluster, in CIDR notation (e.g. `*********/14`). All pods in the cluster
      get assigned a unique IPv4 address from these ranges. Only a single
      range is supported. This field cannot be changed after creation.
    serviceAddressCidrBlocks: Required. The IP address range for services in
      this cluster, in CIDR notation (e.g. `*********/14`). All services in
      the cluster get assigned a unique IPv4 address from these ranges. Only a
      single range is supported. This field cannot be changed after creating a
      cluster.
    serviceLoadBalancerSubnetId: Optional. The ARM ID of the subnet where
      Kubernetes private service type load balancers are deployed. When
      unspecified, it defaults to AzureControlPlane.subnet_id. Example: "/subs
      criptions/d00494d6-6f3c-4280-bbb2-
      899e163d1d30/resourceGroups/anthos_cluster_gkeust4/providers/Microsoft.N
      etwork/virtualNetworks/gke-vnet-gkeust4/subnets/subnetid456"
    virtualNetworkId: Required. The Azure Resource Manager (ARM) ID of the
      VNet associated with your cluster. All components in the cluster (i.e.
      control plane and node pools) run on a single VNet. Example: `/subscript
      ions//resourceGroups//providers/Microsoft.Network/virtualNetworks/` This
      field cannot be changed after creation.
  """

  podAddressCidrBlocks = _messages.StringField(1, repeated=True)
  serviceAddressCidrBlocks = _messages.StringField(2, repeated=True)
  serviceLoadBalancerSubnetId = _messages.StringField(3)
  virtualNetworkId = _messages.StringField(4)


class GoogleCloudGkemulticloudV1AzureClusterResources(_messages.Message):
  r"""Managed Azure resources for the cluster. The values could change and be
  empty, depending on the state of the cluster.

  Fields:
    controlPlaneApplicationSecurityGroupId: Output only. The ARM ID of the
      control plane application security group.
    networkSecurityGroupId: Output only. The ARM ID of the cluster network
      security group.
  """

  controlPlaneApplicationSecurityGroupId = _messages.StringField(1)
  networkSecurityGroupId = _messages.StringField(2)


class GoogleCloudGkemulticloudV1AzureClusterUser(_messages.Message):
  r"""Identities of a user-type subject for Azure clusters.

  Fields:
    username: Required. The name of the user, e.g. `<EMAIL>`.
  """

  username = _messages.StringField(1)


class GoogleCloudGkemulticloudV1AzureConfigEncryption(_messages.Message):
  r"""Configuration related to config data encryption. Azure VM bootstrap
  secret is envelope encrypted with the provided key vault key.

  Fields:
    keyId: Required. The ARM ID of the Azure Key Vault key to encrypt /
      decrypt config data. For example: `/subscriptions//resourceGroups//provi
      ders/Microsoft.KeyVault/vaults//keys/`
    publicKey: Optional. RSA key of the Azure Key Vault public key to use for
      encrypting the data. This key must be formatted as a PEM-encoded
      SubjectPublicKeyInfo (RFC 5280) in ASN.1 DER form. The string must be
      comprised of a single PEM block of type "PUBLIC KEY".
  """

  keyId = _messages.StringField(1)
  publicKey = _messages.StringField(2)


class GoogleCloudGkemulticloudV1AzureControlPlane(_messages.Message):
  r"""AzureControlPlane represents the control plane configurations.

  Messages:
    TagsValue: Optional. A set of tags to apply to all underlying control
      plane Azure resources.

  Fields:
    configEncryption: Optional. Configuration related to vm config encryption.
    databaseEncryption: Optional. Configuration related to application-layer
      secrets encryption.
    endpointSubnetId: Optional. The ARM ID of the subnet where the control
      plane load balancer is deployed. When unspecified, it defaults to
      AzureControlPlane.subnet_id. Example: "/subscriptions/d00494d6-6f3c-
      4280-bbb2-
      899e163d1d30/resourceGroups/anthos_cluster_gkeust4/providers/Microsoft.N
      etwork/virtualNetworks/gke-vnet-gkeust4/subnets/subnetid123"
    mainVolume: Optional. Configuration related to the main volume provisioned
      for each control plane replica. The main volume is in charge of storing
      all of the cluster's etcd state. When unspecified, it defaults to a
      8-GiB Azure Disk.
    proxyConfig: Optional. Proxy configuration for outbound HTTP(S) traffic.
    replicaPlacements: Optional. Configuration for where to place the control
      plane replicas. Up to three replica placement instances can be
      specified. If replica_placements is set, the replica placement instances
      will be applied to the three control plane replicas as evenly as
      possible.
    rootVolume: Optional. Configuration related to the root volume provisioned
      for each control plane replica. When unspecified, it defaults to 32-GiB
      Azure Disk.
    sshConfig: Required. SSH configuration for how to access the underlying
      control plane machines.
    subnetId: Optional. The ARM ID of the default subnet for the control
      plane. The control plane VMs are deployed in this subnet, unless
      `AzureControlPlane.replica_placements` is specified. This subnet will
      also be used as default for `AzureControlPlane.endpoint_subnet_id` if
      `AzureControlPlane.endpoint_subnet_id` is not specified. Similarly it
      will be used as default for
      `AzureClusterNetworking.service_load_balancer_subnet_id`. Example: `/sub
      scriptions//resourceGroups//providers/Microsoft.Network/virtualNetworks/
      /subnets/default`.
    tags: Optional. A set of tags to apply to all underlying control plane
      Azure resources.
    version: Required. The Kubernetes version to run on control plane replicas
      (e.g. `1.19.10-gke.1000`). You can list all supported versions on a
      given Google Cloud region by calling GetAzureServerConfig.
    vmSize: Optional. The Azure VM size name. Example: `Standard_DS2_v2`. For
      available VM sizes, see https://docs.microsoft.com/en-us/azure/virtual-
      machines/vm-naming-conventions. When unspecified, it defaults to
      `Standard_DS2_v2`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""Optional. A set of tags to apply to all underlying control plane Azure
    resources.

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  configEncryption = _messages.MessageField('GoogleCloudGkemulticloudV1AzureConfigEncryption', 1)
  databaseEncryption = _messages.MessageField('GoogleCloudGkemulticloudV1AzureDatabaseEncryption', 2)
  endpointSubnetId = _messages.StringField(3)
  mainVolume = _messages.MessageField('GoogleCloudGkemulticloudV1AzureDiskTemplate', 4)
  proxyConfig = _messages.MessageField('GoogleCloudGkemulticloudV1AzureProxyConfig', 5)
  replicaPlacements = _messages.MessageField('GoogleCloudGkemulticloudV1ReplicaPlacement', 6, repeated=True)
  rootVolume = _messages.MessageField('GoogleCloudGkemulticloudV1AzureDiskTemplate', 7)
  sshConfig = _messages.MessageField('GoogleCloudGkemulticloudV1AzureSshConfig', 8)
  subnetId = _messages.StringField(9)
  tags = _messages.MessageField('TagsValue', 10)
  version = _messages.StringField(11)
  vmSize = _messages.StringField(12)


class GoogleCloudGkemulticloudV1AzureDatabaseEncryption(_messages.Message):
  r"""Configuration related to application-layer secrets encryption. Anthos
  clusters on Azure encrypts your Kubernetes data at rest in etcd using Azure
  Key Vault.

  Fields:
    keyId: Required. The ARM ID of the Azure Key Vault key to encrypt /
      decrypt data. For example: `/subscriptions//resourceGroups//providers/Mi
      crosoft.KeyVault/vaults//keys/` Encryption will always take the latest
      version of the key and hence specific version is not supported.
  """

  keyId = _messages.StringField(1)


class GoogleCloudGkemulticloudV1AzureDiskTemplate(_messages.Message):
  r"""Configuration for Azure Disks.

  Fields:
    sizeGib: Optional. The size of the disk, in GiBs. When unspecified, a
      default value is provided. See the specific reference in the parent
      resource.
  """

  sizeGib = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class GoogleCloudGkemulticloudV1AzureJsonWebKeys(_messages.Message):
  r"""AzureJsonWebKeys is a valid JSON Web Key Set as specififed in RFC 7517.

  Fields:
    keys: The public component of the keys used by the cluster to sign token
      requests.
  """

  keys = _messages.MessageField('GoogleCloudGkemulticloudV1Jwk', 1, repeated=True)


class GoogleCloudGkemulticloudV1AzureK8sVersionInfo(_messages.Message):
  r"""Kubernetes version information of GKE cluster on Azure.

  Fields:
    enabled: Optional. True if the version is available for cluster creation.
      If a version is enabled for creation, it can be used to create new
      clusters. Otherwise, cluster creation will fail. However, cluster
      upgrade operations may succeed, even if the version is not enabled.
    endOfLife: Optional. True if this cluster version belongs to a minor
      version that has reached its end of life and is no longer in scope to
      receive security and bug fixes.
    endOfLifeDate: Optional. The estimated date (in Pacific Time) when this
      cluster version will reach its end of life. Or if this version is no
      longer supported (the `end_of_life` field is true), this is the actual
      date (in Pacific time) when the version reached its end of life.
    releaseDate: Optional. The date (in Pacific Time) when the cluster version
      was released.
    version: Kubernetes version name (for example, `1.19.10-gke.1000`)
  """

  enabled = _messages.BooleanField(1)
  endOfLife = _messages.BooleanField(2)
  endOfLifeDate = _messages.MessageField('GoogleTypeDate', 3)
  releaseDate = _messages.MessageField('GoogleTypeDate', 4)
  version = _messages.StringField(5)


class GoogleCloudGkemulticloudV1AzureNodeConfig(_messages.Message):
  r"""Parameters that describe the configuration of all node machines on a
  given node pool.

  Messages:
    LabelsValue: Optional. The initial labels assigned to nodes of this node
      pool. An object containing a list of "key": value pairs. Example: {
      "name": "wrench", "mass": "1.3kg", "count": "3" }.
    TagsValue: Optional. A set of tags to apply to all underlying Azure
      resources for this node pool. This currently only includes Virtual
      Machine Scale Sets. Specify at most 50 pairs containing alphanumerics,
      spaces, and symbols (.+-=_:@/). Keys can be up to 127 Unicode
      characters. Values can be up to 255 Unicode characters.

  Fields:
    configEncryption: Optional. Configuration related to vm config encryption.
    imageType: Optional. The OS image type to use on node pool instances. Can
      be unspecified, or have a value of `ubuntu`. When unspecified, it
      defaults to `ubuntu`.
    labels: Optional. The initial labels assigned to nodes of this node pool.
      An object containing a list of "key": value pairs. Example: { "name":
      "wrench", "mass": "1.3kg", "count": "3" }.
    proxyConfig: Optional. Proxy configuration for outbound HTTP(S) traffic.
    rootVolume: Optional. Configuration related to the root volume provisioned
      for each node pool machine. When unspecified, it defaults to a 32-GiB
      Azure Disk.
    sshConfig: Required. SSH configuration for how to access the node pool
      machines.
    tags: Optional. A set of tags to apply to all underlying Azure resources
      for this node pool. This currently only includes Virtual Machine Scale
      Sets. Specify at most 50 pairs containing alphanumerics, spaces, and
      symbols (.+-=_:@/). Keys can be up to 127 Unicode characters. Values can
      be up to 255 Unicode characters.
    taints: Optional. The initial taints assigned to nodes of this node pool.
    vmSize: Optional. The Azure VM size name. Example: `Standard_DS2_v2`. See
      [Supported VM sizes](/anthos/clusters/docs/azure/reference/supported-
      vms) for options. When unspecified, it defaults to `Standard_DS2_v2`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. The initial labels assigned to nodes of this node pool. An
    object containing a list of "key": value pairs. Example: { "name":
    "wrench", "mass": "1.3kg", "count": "3" }.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TagsValue(_messages.Message):
    r"""Optional. A set of tags to apply to all underlying Azure resources for
    this node pool. This currently only includes Virtual Machine Scale Sets.
    Specify at most 50 pairs containing alphanumerics, spaces, and symbols
    (.+-=_:@/). Keys can be up to 127 Unicode characters. Values can be up to
    255 Unicode characters.

    Messages:
      AdditionalProperty: An additional property for a TagsValue object.

    Fields:
      additionalProperties: Additional properties of type TagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  configEncryption = _messages.MessageField('GoogleCloudGkemulticloudV1AzureConfigEncryption', 1)
  imageType = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  proxyConfig = _messages.MessageField('GoogleCloudGkemulticloudV1AzureProxyConfig', 4)
  rootVolume = _messages.MessageField('GoogleCloudGkemulticloudV1AzureDiskTemplate', 5)
  sshConfig = _messages.MessageField('GoogleCloudGkemulticloudV1AzureSshConfig', 6)
  tags = _messages.MessageField('TagsValue', 7)
  taints = _messages.MessageField('GoogleCloudGkemulticloudV1NodeTaint', 8, repeated=True)
  vmSize = _messages.StringField(9)


class GoogleCloudGkemulticloudV1AzureNodeManagement(_messages.Message):
  r"""AzureNodeManagement defines the set of node management features turned
  on for an Azure node pool.

  Fields:
    autoRepair: Optional. Whether or not the nodes will be automatically
      repaired. When set to true, the nodes in this node pool will be
      monitored and if they fail health checks consistently over a period of
      time, an automatic repair action will be triggered to replace them with
      new nodes.
  """

  autoRepair = _messages.BooleanField(1)


class GoogleCloudGkemulticloudV1AzureNodePool(_messages.Message):
  r"""An Anthos node pool running on Azure.

  Enums:
    StateValueValuesEnum: Output only. The current state of the node pool.

  Messages:
    AnnotationsValue: Optional. Annotations on the node pool. This field has
      the same restrictions as Kubernetes annotations. The total size of all
      keys and values combined is limited to 256k. Keys can have 2 segments:
      prefix (optional) and name (required), separated by a slash (/). Prefix
      must be a DNS subdomain. Name must be 63 characters or less, begin and
      end with alphanumerics, with dashes (-), underscores (_), dots (.), and
      alphanumerics between.

  Fields:
    annotations: Optional. Annotations on the node pool. This field has the
      same restrictions as Kubernetes annotations. The total size of all keys
      and values combined is limited to 256k. Keys can have 2 segments: prefix
      (optional) and name (required), separated by a slash (/). Prefix must be
      a DNS subdomain. Name must be 63 characters or less, begin and end with
      alphanumerics, with dashes (-), underscores (_), dots (.), and
      alphanumerics between.
    autoscaling: Required. Autoscaler configuration for this node pool.
    azureAvailabilityZone: Optional. The Azure availability zone of the nodes
      in this nodepool. When unspecified, it defaults to `1`.
    config: Required. The node configuration of the node pool.
    createTime: Output only. The time at which this node pool was created.
    errors: Output only. A set of errors found in the node pool.
    etag: Allows clients to perform consistent read-modify-writes through
      optimistic concurrency control. Can be sent on update and delete
      requests to ensure the client has an up-to-date value before proceeding.
    management: Optional. The Management configuration for this node pool.
    maxPodsConstraint: Required. The constraint on the maximum number of pods
      that can be run simultaneously on a node in the node pool.
    name: The name of this resource. Node pool names are formatted as
      `projects//locations//azureClusters//azureNodePools/`. For more details
      on Google Cloud resource names, see [Resource
      Names](https://cloud.google.com/apis/design/resource_names)
    reconciling: Output only. If set, there are currently pending changes to
      the node pool.
    state: Output only. The current state of the node pool.
    subnetId: Required. The ARM ID of the subnet where the node pool VMs run.
      Make sure it's a subnet under the virtual network in the cluster
      configuration.
    uid: Output only. A globally unique identifier for the node pool.
    updateTime: Output only. The time at which this node pool was last
      updated.
    version: Required. The Kubernetes version (e.g. `1.19.10-gke.1000`)
      running on this node pool.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the node pool.

    Values:
      STATE_UNSPECIFIED: Not set.
      PROVISIONING: The PROVISIONING state indicates the node pool is being
        created.
      RUNNING: The RUNNING state indicates the node pool has been created and
        is fully usable.
      RECONCILING: The RECONCILING state indicates that the node pool is being
        reconciled.
      STOPPING: The STOPPING state indicates the node pool is being deleted.
      ERROR: The ERROR state indicates the node pool is in a broken
        unrecoverable state.
      DEGRADED: The DEGRADED state indicates the node pool requires user
        action to restore full functionality.
    """
    STATE_UNSPECIFIED = 0
    PROVISIONING = 1
    RUNNING = 2
    RECONCILING = 3
    STOPPING = 4
    ERROR = 5
    DEGRADED = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Annotations on the node pool. This field has the same
    restrictions as Kubernetes annotations. The total size of all keys and
    values combined is limited to 256k. Keys can have 2 segments: prefix
    (optional) and name (required), separated by a slash (/). Prefix must be a
    DNS subdomain. Name must be 63 characters or less, begin and end with
    alphanumerics, with dashes (-), underscores (_), dots (.), and
    alphanumerics between.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  autoscaling = _messages.MessageField('GoogleCloudGkemulticloudV1AzureNodePoolAutoscaling', 2)
  azureAvailabilityZone = _messages.StringField(3)
  config = _messages.MessageField('GoogleCloudGkemulticloudV1AzureNodeConfig', 4)
  createTime = _messages.StringField(5)
  errors = _messages.MessageField('GoogleCloudGkemulticloudV1AzureNodePoolError', 6, repeated=True)
  etag = _messages.StringField(7)
  management = _messages.MessageField('GoogleCloudGkemulticloudV1AzureNodeManagement', 8)
  maxPodsConstraint = _messages.MessageField('GoogleCloudGkemulticloudV1MaxPodsConstraint', 9)
  name = _messages.StringField(10)
  reconciling = _messages.BooleanField(11)
  state = _messages.EnumField('StateValueValuesEnum', 12)
  subnetId = _messages.StringField(13)
  uid = _messages.StringField(14)
  updateTime = _messages.StringField(15)
  version = _messages.StringField(16)


class GoogleCloudGkemulticloudV1AzureNodePoolAutoscaling(_messages.Message):
  r"""Configuration related to Kubernetes cluster autoscaler. The Kubernetes
  cluster autoscaler will automatically adjust the size of the node pool based
  on the cluster load.

  Fields:
    maxNodeCount: Required. Maximum number of nodes in the node pool. Must be
      greater than or equal to min_node_count and less than or equal to 50.
    minNodeCount: Required. Minimum number of nodes in the node pool. Must be
      greater than or equal to 1 and less than or equal to max_node_count.
  """

  maxNodeCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minNodeCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleCloudGkemulticloudV1AzureNodePoolError(_messages.Message):
  r"""AzureNodePoolError describes errors found on Azure node pools.

  Fields:
    message: Human-friendly description of the error.
  """

  message = _messages.StringField(1)


class GoogleCloudGkemulticloudV1AzureOpenIdConfig(_messages.Message):
  r"""AzureOpenIdConfig is an OIDC discovery document for the cluster. See the
  OpenID Connect Discovery 1.0 specification for details.

  Fields:
    claims_supported: Supported claims.
    grant_types: Supported grant types.
    id_token_signing_alg_values_supported: supported ID Token signing
      Algorithms.
    issuer: OIDC Issuer.
    jwks_uri: JSON Web Key uri.
    response_types_supported: Supported response types.
    subject_types_supported: Supported subject types.
  """

  claims_supported = _messages.StringField(1, repeated=True)
  grant_types = _messages.StringField(2, repeated=True)
  id_token_signing_alg_values_supported = _messages.StringField(3, repeated=True)
  issuer = _messages.StringField(4)
  jwks_uri = _messages.StringField(5)
  response_types_supported = _messages.StringField(6, repeated=True)
  subject_types_supported = _messages.StringField(7, repeated=True)


class GoogleCloudGkemulticloudV1AzureProxyConfig(_messages.Message):
  r"""Details of a proxy config stored in Azure Key Vault.

  Fields:
    resourceGroupId: The ARM ID the of the resource group containing proxy
      keyvault. Resource group ids are formatted as
      `/subscriptions//resourceGroups/`.
    secretId: The URL the of the proxy setting secret with its version. The
      secret must be a JSON encoded proxy configuration as described in
      https://cloud.google.com/kubernetes-engine/multi-cloud/docs/azure/how-
      to/use-a-proxy#create_a_proxy_configuration_file Secret ids are
      formatted as `https://.vault.azure.net/secrets//`.
  """

  resourceGroupId = _messages.StringField(1)
  secretId = _messages.StringField(2)


class GoogleCloudGkemulticloudV1AzureServerConfig(_messages.Message):
  r"""AzureServerConfig contains information about a Google Cloud location,
  such as supported Azure regions and Kubernetes versions.

  Fields:
    name: The `AzureServerConfig` resource name. `AzureServerConfig` names are
      formatted as `projects//locations//azureServerConfig`. See [Resource
      Names](https://cloud.google.com/apis/design/resource_names) for more
      details on Google Cloud Platform resource names.
    supportedAzureRegions: The list of supported Azure regions.
    validVersions: List of all released Kubernetes versions, including ones
      which are end of life and can no longer be used. Filter by the `enabled`
      property to limit to currently available versions. Valid versions
      supported for both create and update operations
  """

  name = _messages.StringField(1)
  supportedAzureRegions = _messages.StringField(2, repeated=True)
  validVersions = _messages.MessageField('GoogleCloudGkemulticloudV1AzureK8sVersionInfo', 3, repeated=True)


class GoogleCloudGkemulticloudV1AzureServicesAuthentication(_messages.Message):
  r"""Authentication configuration for the management of Azure resources.

  Fields:
    applicationId: Required. The Azure Active Directory Application ID.
    tenantId: Required. The Azure Active Directory Tenant ID.
  """

  applicationId = _messages.StringField(1)
  tenantId = _messages.StringField(2)


class GoogleCloudGkemulticloudV1AzureSshConfig(_messages.Message):
  r"""SSH configuration for Azure resources.

  Fields:
    authorizedKey: Required. The SSH public key data for VMs managed by
      Anthos. This accepts the authorized_keys file format used in OpenSSH
      according to the sshd(8) manual page.
  """

  authorizedKey = _messages.StringField(1)


class GoogleCloudGkemulticloudV1BinaryAuthorization(_messages.Message):
  r"""Configuration for Binary Authorization.

  Enums:
    EvaluationModeValueValuesEnum: Mode of operation for binauthz policy
      evaluation. If unspecified, defaults to DISABLED.

  Fields:
    evaluationMode: Mode of operation for binauthz policy evaluation. If
      unspecified, defaults to DISABLED.
  """

  class EvaluationModeValueValuesEnum(_messages.Enum):
    r"""Mode of operation for binauthz policy evaluation. If unspecified,
    defaults to DISABLED.

    Values:
      EVALUATION_MODE_UNSPECIFIED: Default value
      DISABLED: Disable BinaryAuthorization
      PROJECT_SINGLETON_POLICY_ENFORCE: Enforce Kubernetes admission requests
        with BinaryAuthorization using the project's singleton policy.
    """
    EVALUATION_MODE_UNSPECIFIED = 0
    DISABLED = 1
    PROJECT_SINGLETON_POLICY_ENFORCE = 2

  evaluationMode = _messages.EnumField('EvaluationModeValueValuesEnum', 1)


class GoogleCloudGkemulticloudV1CloudMonitoringConfig(_messages.Message):
  r"""CloudMonitoringConfig defines the configuration for built-in Cloud
  Logging and Monitoring. Only for Attached Clusters.

  Fields:
    enabled: Enable GKE-native logging and metrics. Only for Attached
      Clusters.
  """

  enabled = _messages.BooleanField(1)


class GoogleCloudGkemulticloudV1Fleet(_messages.Message):
  r"""Fleet related configuration. Fleets are a Google Cloud concept for
  logically organizing clusters, letting you use and manage multi-cluster
  capabilities and apply consistent policies across your systems. See [Anthos
  Fleets](https://cloud.google.com/anthos/multicluster-management/fleets) for
  more details on Anthos multi-cluster capabilities using Fleets.

  Fields:
    membership: Output only. The name of the managed Hub Membership resource
      associated to this cluster. Membership names are formatted as
      `projects//locations/global/membership/`.
    project: Required. The name of the Fleet host project where this cluster
      will be registered. Project names are formatted as `projects/`.
  """

  membership = _messages.StringField(1)
  project = _messages.StringField(2)


class GoogleCloudGkemulticloudV1GenerateAttachedClusterAgentTokenRequest(_messages.Message):
  r"""A GoogleCloudGkemulticloudV1GenerateAttachedClusterAgentTokenRequest
  object.

  Fields:
    audience: Optional.
    grantType: Optional.
    options: Optional.
    requestedTokenType: Optional.
    scope: Optional.
    subjectToken: Required.
    subjectTokenType: Required.
    version: Required.
  """

  audience = _messages.StringField(1)
  grantType = _messages.StringField(2)
  options = _messages.StringField(3)
  requestedTokenType = _messages.StringField(4)
  scope = _messages.StringField(5)
  subjectToken = _messages.StringField(6)
  subjectTokenType = _messages.StringField(7)
  version = _messages.StringField(8)


class GoogleCloudGkemulticloudV1GenerateAttachedClusterAgentTokenResponse(_messages.Message):
  r"""A GoogleCloudGkemulticloudV1GenerateAttachedClusterAgentTokenResponse
  object.

  Fields:
    access_token: A string attribute.
    expires_in: A integer attribute.
    token_type: A string attribute.
  """

  access_token = _messages.StringField(1)
  expires_in = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  token_type = _messages.StringField(3)


class GoogleCloudGkemulticloudV1GenerateAttachedClusterInstallManifestResponse(_messages.Message):
  r"""Response message for
  `AttachedClusters.GenerateAttachedClusterInstallManifest` method.

  Fields:
    manifest: A set of Kubernetes resources (in YAML format) to be applied to
      the cluster to be attached.
  """

  manifest = _messages.StringField(1)


class GoogleCloudGkemulticloudV1GenerateAwsAccessTokenResponse(_messages.Message):
  r"""Response message for `AwsClusters.GenerateAwsAccessToken` method.

  Fields:
    accessToken: Output only. Access token to authenticate to k8s api-server.
    expirationTime: Output only. Timestamp at which the token will expire.
  """

  accessToken = _messages.StringField(1)
  expirationTime = _messages.StringField(2)


class GoogleCloudGkemulticloudV1GenerateAwsClusterAgentTokenRequest(_messages.Message):
  r"""A GoogleCloudGkemulticloudV1GenerateAwsClusterAgentTokenRequest object.

  Fields:
    audience: Optional.
    grantType: Optional.
    nodePoolId: Optional.
    options: Optional.
    requestedTokenType: Optional.
    scope: Optional.
    subjectToken: Required.
    subjectTokenType: Required.
    version: Required.
  """

  audience = _messages.StringField(1)
  grantType = _messages.StringField(2)
  nodePoolId = _messages.StringField(3)
  options = _messages.StringField(4)
  requestedTokenType = _messages.StringField(5)
  scope = _messages.StringField(6)
  subjectToken = _messages.StringField(7)
  subjectTokenType = _messages.StringField(8)
  version = _messages.StringField(9)


class GoogleCloudGkemulticloudV1GenerateAwsClusterAgentTokenResponse(_messages.Message):
  r"""A GoogleCloudGkemulticloudV1GenerateAwsClusterAgentTokenResponse object.

  Fields:
    access_token: A string attribute.
    expires_in: A integer attribute.
    token_type: A string attribute.
  """

  access_token = _messages.StringField(1)
  expires_in = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  token_type = _messages.StringField(3)


class GoogleCloudGkemulticloudV1GenerateAzureAccessTokenResponse(_messages.Message):
  r"""Response message for `AzureClusters.GenerateAzureAccessToken` method.

  Fields:
    accessToken: Output only. Access token to authenticate to k8s api-server.
    expirationTime: Output only. Timestamp at which the token will expire.
  """

  accessToken = _messages.StringField(1)
  expirationTime = _messages.StringField(2)


class GoogleCloudGkemulticloudV1GenerateAzureClusterAgentTokenRequest(_messages.Message):
  r"""A GoogleCloudGkemulticloudV1GenerateAzureClusterAgentTokenRequest
  object.

  Fields:
    audience: Optional.
    grantType: Optional.
    nodePoolId: Optional.
    options: Optional.
    requestedTokenType: Optional.
    scope: Optional.
    subjectToken: Required.
    subjectTokenType: Required.
    version: Required.
  """

  audience = _messages.StringField(1)
  grantType = _messages.StringField(2)
  nodePoolId = _messages.StringField(3)
  options = _messages.StringField(4)
  requestedTokenType = _messages.StringField(5)
  scope = _messages.StringField(6)
  subjectToken = _messages.StringField(7)
  subjectTokenType = _messages.StringField(8)
  version = _messages.StringField(9)


class GoogleCloudGkemulticloudV1GenerateAzureClusterAgentTokenResponse(_messages.Message):
  r"""A GoogleCloudGkemulticloudV1GenerateAzureClusterAgentTokenResponse
  object.

  Fields:
    access_token: A string attribute.
    expires_in: A integer attribute.
    token_type: A string attribute.
  """

  access_token = _messages.StringField(1)
  expires_in = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  token_type = _messages.StringField(3)


class GoogleCloudGkemulticloudV1ImportAttachedClusterRequest(_messages.Message):
  r"""Request message for `AttachedClusters.ImportAttachedCluster` method.

  Fields:
    distribution: Required. The Kubernetes distribution of the underlying
      attached cluster. Supported values: ["eks", "aks", "generic"].
    fleetMembership: Required. The name of the fleet membership resource to
      import.
    platformVersion: Required. The platform version for the cluster (e.g.
      `1.19.0-gke.1000`). You can list all supported versions on a given
      Google Cloud region by calling GetAttachedServerConfig.
    proxyConfig: Optional. Proxy configuration for outbound HTTP(S) traffic.
    validateOnly: If set, only validate the request, but do not actually
      import the cluster.
  """

  distribution = _messages.StringField(1)
  fleetMembership = _messages.StringField(2)
  platformVersion = _messages.StringField(3)
  proxyConfig = _messages.MessageField('GoogleCloudGkemulticloudV1AttachedProxyConfig', 4)
  validateOnly = _messages.BooleanField(5)


class GoogleCloudGkemulticloudV1Jwk(_messages.Message):
  r"""Jwk is a JSON Web Key as specified in RFC 7517.

  Fields:
    alg: Algorithm.
    crv: Used for ECDSA keys.
    e: Used for RSA keys.
    kid: Key ID.
    kty: Key Type.
    n: Used for RSA keys.
    use: Permitted uses for the public keys.
    x: Used for ECDSA keys.
    y: Used for ECDSA keys.
  """

  alg = _messages.StringField(1)
  crv = _messages.StringField(2)
  e = _messages.StringField(3)
  kid = _messages.StringField(4)
  kty = _messages.StringField(5)
  n = _messages.StringField(6)
  use = _messages.StringField(7)
  x = _messages.StringField(8)
  y = _messages.StringField(9)


class GoogleCloudGkemulticloudV1KubernetesSecret(_messages.Message):
  r"""Information about a Kubernetes Secret

  Fields:
    name: Name of the kubernetes secret.
    namespace: Namespace in which the kubernetes secret is stored.
  """

  name = _messages.StringField(1)
  namespace = _messages.StringField(2)


class GoogleCloudGkemulticloudV1Label(_messages.Message):
  r"""Label defines the additional fields for labels for pods created by auto-
  installed components.

  Fields:
    key: This is the key of the label.
    value: This is the value of the label.
  """

  key = _messages.StringField(1)
  value = _messages.StringField(2)


class GoogleCloudGkemulticloudV1ListAttachedClustersResponse(_messages.Message):
  r"""Response message for `AttachedClusters.ListAttachedClusters` method.

  Fields:
    attachedClusters: A list of AttachedCluster resources in the specified
      Google Cloud Platform project and region region.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  attachedClusters = _messages.MessageField('GoogleCloudGkemulticloudV1AttachedCluster', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudGkemulticloudV1ListAwsClustersResponse(_messages.Message):
  r"""Response message for `AwsClusters.ListAwsClusters` method.

  Fields:
    awsClusters: A list of AwsCluster resources in the specified Google Cloud
      Platform project and region region.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  awsClusters = _messages.MessageField('GoogleCloudGkemulticloudV1AwsCluster', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudGkemulticloudV1ListAwsNodePoolsResponse(_messages.Message):
  r"""Response message for `AwsClusters.ListAwsNodePools` method.

  Fields:
    awsNodePools: A list of AwsNodePool resources in the specified
      `AwsCluster`.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  awsNodePools = _messages.MessageField('GoogleCloudGkemulticloudV1AwsNodePool', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudGkemulticloudV1ListAzureClientsResponse(_messages.Message):
  r"""Response message for `AzureClusters.ListAzureClients` method.

  Fields:
    azureClients: A list of AzureClient resources in the specified Google
      Cloud project and region region.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  azureClients = _messages.MessageField('GoogleCloudGkemulticloudV1AzureClient', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudGkemulticloudV1ListAzureClustersResponse(_messages.Message):
  r"""Response message for `AzureClusters.ListAzureClusters` method.

  Fields:
    azureClusters: A list of AzureCluster resources in the specified Google
      Cloud Platform project and region region.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  azureClusters = _messages.MessageField('GoogleCloudGkemulticloudV1AzureCluster', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudGkemulticloudV1ListAzureNodePoolsResponse(_messages.Message):
  r"""Response message for `AzureClusters.ListAzureNodePools` method.

  Fields:
    azureNodePools: A list of AzureNodePool resources in the specified
      `AzureCluster`.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  azureNodePools = _messages.MessageField('GoogleCloudGkemulticloudV1AzureNodePool', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudGkemulticloudV1LoggingComponentConfig(_messages.Message):
  r"""Parameters that describe the Logging component configuration in a
  cluster.

  Enums:
    EnableComponentsValueListEntryValuesEnum:

  Fields:
    enableComponents: The components to be enabled.
  """

  class EnableComponentsValueListEntryValuesEnum(_messages.Enum):
    r"""EnableComponentsValueListEntryValuesEnum enum type.

    Values:
      COMPONENT_UNSPECIFIED: No component is specified
      SYSTEM_COMPONENTS: This indicates that system logging components is
        enabled.
      WORKLOADS: This indicates that user workload logging component is
        enabled.
    """
    COMPONENT_UNSPECIFIED = 0
    SYSTEM_COMPONENTS = 1
    WORKLOADS = 2

  enableComponents = _messages.EnumField('EnableComponentsValueListEntryValuesEnum', 1, repeated=True)


class GoogleCloudGkemulticloudV1LoggingConfig(_messages.Message):
  r"""Parameters that describe the Logging configuration in a cluster.

  Fields:
    componentConfig: The configuration of the logging components;
  """

  componentConfig = _messages.MessageField('GoogleCloudGkemulticloudV1LoggingComponentConfig', 1)


class GoogleCloudGkemulticloudV1ManagedPrometheusConfig(_messages.Message):
  r"""ManagedPrometheusConfig defines the configuration for Google Cloud
  Managed Service for Prometheus.

  Fields:
    enabled: Enable Managed Collection.
  """

  enabled = _messages.BooleanField(1)


class GoogleCloudGkemulticloudV1MaxPodsConstraint(_messages.Message):
  r"""Constraints applied to pods.

  Fields:
    maxPodsPerNode: Required. The maximum number of pods to schedule on a
      single node.
  """

  maxPodsPerNode = _messages.IntegerField(1)


class GoogleCloudGkemulticloudV1MonitoringConfig(_messages.Message):
  r"""Parameters that describe the Monitoring configuration in a cluster.

  Fields:
    cloudMonitoringConfig: Optionally enable GKE metrics. Only for Attached
      Clusters.
    kubernetesMetadataEndpointOverride: Optional. Override of the default
      (prod) Kubernetes metadata endpoint. Only supported for Attached
      clusters now.
    managedPrometheusConfig: Enable Google Cloud Managed Service for
      Prometheus in the cluster.
  """

  cloudMonitoringConfig = _messages.MessageField('GoogleCloudGkemulticloudV1CloudMonitoringConfig', 1)
  kubernetesMetadataEndpointOverride = _messages.StringField(2)
  managedPrometheusConfig = _messages.MessageField('GoogleCloudGkemulticloudV1ManagedPrometheusConfig', 3)


class GoogleCloudGkemulticloudV1NodeKubeletConfig(_messages.Message):
  r"""Configuration for node pool kubelet options.

  Fields:
    cpuCfsQuota: Optional. Enable CPU CFS quota enforcement for containers
      that specify CPU limits. This option is enabled by default which makes
      kubelet use CFS quota
      (https://www.kernel.org/doc/Documentation/scheduler/sched-bwc.txt) to
      enforce container CPU limits. Otherwise, CPU limits will not be enforced
      at all. Disable this option to mitigate CPU throttling problems while
      still having your pods to be in Guaranteed QoS class by specifying the
      CPU limits. The default value is 'true' if unspecified.
    cpuCfsQuotaPeriod: Optional. Set the CPU CFS quota period value
      'cpu.cfs_period_us'. The string must be a sequence of decimal numbers,
      each with optional fraction and a unit suffix, such as "300ms". Valid
      time units are "ns", "us" (or "\xb5s"), "ms", "s", "m", "h". The value
      must be a positive duration. The default value is '100ms' if
      unspecified.
    cpuManagerPolicy: Optional. Control the CPU management policy on the node.
      See https://kubernetes.io/docs/tasks/administer-cluster/cpu-management-
      policies/ The following values are allowed. * "none": the default, which
      represents the existing scheduling behavior. * "static": allows pods
      with certain resource characteristics to be granted increased CPU
      affinity and exclusivity on the node. The default value is 'none' if
      unspecified.
    insecureKubeletReadonlyPortEnabled: Optional. Enable the insecure kubelet
      read only port.
    podPidsLimit: Optional. Set the Pod PID limits. See
      https://kubernetes.io/docs/concepts/policy/pid-limiting/#pod-pid-limits
      Controls the maximum number of processes allowed to run in a pod. The
      value must be greater than or equal to 1024 and less than 4194304.
  """

  cpuCfsQuota = _messages.BooleanField(1)
  cpuCfsQuotaPeriod = _messages.StringField(2)
  cpuManagerPolicy = _messages.StringField(3)
  insecureKubeletReadonlyPortEnabled = _messages.BooleanField(4)
  podPidsLimit = _messages.IntegerField(5)


class GoogleCloudGkemulticloudV1NodeTaint(_messages.Message):
  r"""The taint content for the node taint.

  Enums:
    EffectValueValuesEnum: Required. The taint effect.

  Fields:
    effect: Required. The taint effect.
    key: Required. Key for the taint.
    value: Required. Value for the taint.
  """

  class EffectValueValuesEnum(_messages.Enum):
    r"""Required. The taint effect.

    Values:
      EFFECT_UNSPECIFIED: Not set.
      NO_SCHEDULE: Do not allow new pods to schedule onto the node unless they
        tolerate the taint, but allow all pods submitted to Kubelet without
        going through the scheduler to start, and allow all already-running
        pods to continue running. Enforced by the scheduler.
      PREFER_NO_SCHEDULE: Like TaintEffectNoSchedule, but the scheduler tries
        not to schedule new pods onto the node, rather than prohibiting new
        pods from scheduling onto the node entirely. Enforced by the
        scheduler.
      NO_EXECUTE: Evict any already-running pods that do not tolerate the
        taint. Currently enforced by NodeController.
    """
    EFFECT_UNSPECIFIED = 0
    NO_SCHEDULE = 1
    PREFER_NO_SCHEDULE = 2
    NO_EXECUTE = 3

  effect = _messages.EnumField('EffectValueValuesEnum', 1)
  key = _messages.StringField(2)
  value = _messages.StringField(3)


class GoogleCloudGkemulticloudV1OperationMetadata(_messages.Message):
  r"""Metadata about a long-running operation.

  Fields:
    createTime: Output only. The time at which this operation was created.
    endTime: Output only. The time at which this operation was completed.
    errorDetail: Output only. Human-readable status of any error that occurred
      during the operation.
    requestedCancellation: Output only. Identifies whether cancellation has
      been requested for the operation. Operations that have successfully been
      cancelled have google.longrunning.Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusDetail: Output only. Human-readable status of the operation, if any.
    target: Output only. The name of the resource associated to this
      operation.
    verb: Output only. The verb associated with the API method which triggered
      this operation. Possible values are "create", "delete", "update" and
      "import".
  """

  createTime = _messages.StringField(1)
  endTime = _messages.StringField(2)
  errorDetail = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusDetail = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudGkemulticloudV1ReplicaPlacement(_messages.Message):
  r"""Configuration for the placement of a control plane replica.

  Fields:
    azureAvailabilityZone: Required. For a given replica, the Azure
      availability zone where to provision the control plane VM and the ETCD
      disk.
    subnetId: Required. For a given replica, the ARM ID of the subnet where
      the control plane VM is deployed. Make sure it's a subnet under the
      virtual network in the cluster configuration.
  """

  azureAvailabilityZone = _messages.StringField(1)
  subnetId = _messages.StringField(2)


class GoogleCloudGkemulticloudV1RollbackAwsNodePoolUpdateRequest(_messages.Message):
  r"""Request message for `AwsClusters.RollbackAwsNodePoolUpdate` method.

  Fields:
    respectPdb: Optional. Option for rollback to ignore the
      PodDisruptionBudget when draining the node pool nodes. Default value is
      false.
  """

  respectPdb = _messages.BooleanField(1)


class GoogleCloudGkemulticloudV1SecurityPostureConfig(_messages.Message):
  r"""SecurityPostureConfig defines the flags needed to enable/disable
  features for the Security Posture API.

  Enums:
    VulnerabilityModeValueValuesEnum: Sets which mode to use for vulnerability
      scanning.

  Fields:
    vulnerabilityMode: Sets which mode to use for vulnerability scanning.
  """

  class VulnerabilityModeValueValuesEnum(_messages.Enum):
    r"""Sets which mode to use for vulnerability scanning.

    Values:
      VULNERABILITY_MODE_UNSPECIFIED: Default value not specified.
      VULNERABILITY_DISABLED: Disables vulnerability scanning on the cluster.
      VULNERABILITY_ENTERPRISE: Applies the Security Posture's vulnerability
        on cluster Enterprise level features.
    """
    VULNERABILITY_MODE_UNSPECIFIED = 0
    VULNERABILITY_DISABLED = 1
    VULNERABILITY_ENTERPRISE = 2

  vulnerabilityMode = _messages.EnumField('VulnerabilityModeValueValuesEnum', 1)


class GoogleCloudGkemulticloudV1SpotConfig(_messages.Message):
  r"""SpotConfig has configuration info for Spot node.

  Fields:
    instanceTypes: Required. A list of instance types for creating spot node
      pool.
  """

  instanceTypes = _messages.StringField(1, repeated=True)


class GoogleCloudGkemulticloudV1SurgeSettings(_messages.Message):
  r"""SurgeSettings contains the parameters for Surge update.

  Fields:
    maxSurge: Optional. The maximum number of nodes that can be created beyond
      the current size of the node pool during the update process.
    maxUnavailable: Optional. The maximum number of nodes that can be
      simultaneously unavailable during the update process. A node is
      considered unavailable if its status is not Ready.
  """

  maxSurge = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  maxUnavailable = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleCloudGkemulticloudV1SystemComponentsConfig(_messages.Message):
  r"""SystemComponentsConfig defines the fields for customizing configurations
  for auto-installed components.

  Fields:
    labels: Sets custom labels for pods created by auto-installed components.
    tolerations: Sets custom tolerations for pods created by auto-installed
      components.
  """

  labels = _messages.MessageField('GoogleCloudGkemulticloudV1Label', 1, repeated=True)
  tolerations = _messages.MessageField('GoogleCloudGkemulticloudV1Toleration', 2, repeated=True)


class GoogleCloudGkemulticloudV1Toleration(_messages.Message):
  r"""Toleration defines the fields for tolerations for pods created by auto-
  installed components.

  Enums:
    EffectValueValuesEnum: Effect indicates the taint effect to match e.g.
      'NoSchedule'
    KeyOperatorValueValuesEnum: KeyOperator represents a key's relationship to
      the value e.g. 'Exist'.

  Fields:
    effect: Effect indicates the taint effect to match e.g. 'NoSchedule'
    key: Key is the taint key that the toleration applies to.
    keyOperator: KeyOperator represents a key's relationship to the value e.g.
      'Exist'.
    value: Value is the taint value that the toleration applies to.
  """

  class EffectValueValuesEnum(_messages.Enum):
    r"""Effect indicates the taint effect to match e.g. 'NoSchedule'

    Values:
      EFFECT_UNSPECIFIED: Effect is not specified.
      EFFECT_NO_SCHEDULE: Effect maps to 'NoSchedule'.
      EFFECT_PREFER_NO_SCHEDULE: Effect maps to 'PreferNoSchedule'.
      EFFECT_NO_EXECUTE: Effect maps to 'NoExecute'.
    """
    EFFECT_UNSPECIFIED = 0
    EFFECT_NO_SCHEDULE = 1
    EFFECT_PREFER_NO_SCHEDULE = 2
    EFFECT_NO_EXECUTE = 3

  class KeyOperatorValueValuesEnum(_messages.Enum):
    r"""KeyOperator represents a key's relationship to the value e.g. 'Exist'.

    Values:
      KEY_OPERATOR_UNSPECIFIED: Operator is not specified.
      KEY_OPERATOR_EQUAL: Operator maps to 'Equal'.
      KEY_OPERATOR_EXISTS: Operator maps to 'Exists'.
    """
    KEY_OPERATOR_UNSPECIFIED = 0
    KEY_OPERATOR_EQUAL = 1
    KEY_OPERATOR_EXISTS = 2

  effect = _messages.EnumField('EffectValueValuesEnum', 1)
  key = _messages.StringField(2)
  keyOperator = _messages.EnumField('KeyOperatorValueValuesEnum', 3)
  value = _messages.StringField(4)


class GoogleCloudGkemulticloudV1UpdateSettings(_messages.Message):
  r"""UpdateSettings control the level of parallelism and the level of
  disruption caused during the update of a node pool. These settings are
  applicable when the node pool update requires replacing the existing node
  pool nodes with the updated ones. UpdateSettings are optional. When
  UpdateSettings are not specified during the node pool creation, a default is
  chosen based on the parent cluster's version. For clusters with minor
  version 1.27 and later, a default surge_settings configuration with
  max_surge = 1 and max_unavailable = 0 is used. For clusters with older
  versions, node pool updates use the traditional rolling update mechanism of
  updating one node at a time in a "terminate before create" fashion and
  update_settings is not applicable. Set the surge_settings parameter to use
  the Surge Update mechanism for the rolling update of node pool nodes. 1.
  max_surge controls the number of additional nodes that can be created beyond
  the current size of the node pool temporarily for the time of the update to
  increase the number of available nodes. 2. max_unavailable controls the
  number of nodes that can be simultaneously unavailable during the update. 3.
  (max_surge + max_unavailable) determines the level of parallelism (i.e., the
  number of nodes being updated at the same time).

  Fields:
    surgeSettings: Optional. Settings for surge update.
  """

  surgeSettings = _messages.MessageField('GoogleCloudGkemulticloudV1SurgeSettings', 1)


class GoogleCloudGkemulticloudV1WorkloadIdentityConfig(_messages.Message):
  r"""Workload Identity settings.

  Fields:
    identityProvider: The ID of the OIDC Identity Provider (IdP) associated to
      the Workload Identity Pool.
    issuerUri: The OIDC issuer URL for this cluster.
    workloadPool: The Workload Identity Pool associated to the cluster.
  """

  identityProvider = _messages.StringField(1)
  issuerUri = _messages.StringField(2)
  workloadPool = _messages.StringField(3)


class GoogleLongrunningCancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class GoogleLongrunningListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('GoogleLongrunningOperation', 2, repeated=True)


class GoogleLongrunningOperation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('GoogleRpcStatus', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class GoogleProtobufEmpty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class GoogleRpcStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class GoogleTypeDate(_messages.Message):
  r"""Represents a whole or partial calendar date, such as a birthday. The
  time of day and time zone are either specified elsewhere or are
  insignificant. The date is relative to the Gregorian Calendar. This can
  represent one of the following: * A full date, with non-zero year, month,
  and day values. * A month and day, with a zero year (for example, an
  anniversary). * A year on its own, with a zero month and a zero day. * A
  year and month, with a zero day (for example, a credit card expiration
  date). Related types: * google.type.TimeOfDay * google.type.DateTime *
  google.protobuf.Timestamp

  Fields:
    day: Day of a month. Must be from 1 to 31 and valid for the year and
      month, or 0 to specify a year by itself or a year and month where the
      day isn't significant.
    month: Month of a year. Must be from 1 to 12, or 0 to specify a year
      without a month and day.
    year: Year of the date. Must be from 1 to 9999, or 0 to specify a date
      without a year.
  """

  day = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  month = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  year = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    GkemulticloudProjectsLocationsGenerateAttachedClusterInstallManifestRequest, 'proxyConfig_kubernetesSecret_name', 'proxyConfig.kubernetesSecret.name')
encoding.AddCustomJsonFieldMapping(
    GkemulticloudProjectsLocationsGenerateAttachedClusterInstallManifestRequest, 'proxyConfig_kubernetesSecret_namespace', 'proxyConfig.kubernetesSecret.namespace')
