"""Generated message classes for servicedirectory version v1.

Service Directory is a platform for discovering, publishing, and connecting
services.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'servicedirectory'


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Endpoint(_messages.Message):
  r"""An individual endpoint that provides a service. The service must already
  exist to create an endpoint.

  Messages:
    AnnotationsValue: Optional. Annotations for the endpoint. This data can be
      consumed by service clients. Restrictions: * The entire annotations
      dictionary may contain up to 512 characters, spread accoss all key-value
      pairs. Annotations that go beyond this limit are rejected * Valid
      annotation keys have two segments: an optional prefix and name,
      separated by a slash (/). The name segment is required and must be 63
      characters or less, beginning and ending with an alphanumeric character
      ([a-z0-9A-Z]) with dashes (-), underscores (_), dots (.), and
      alphanumerics between. The prefix is optional. If specified, the prefix
      must be a DNS subdomain: a series of DNS labels separated by dots (.),
      not longer than 253 characters in total, followed by a slash (/)
      Annotations that fails to meet these requirements are rejected. Note:
      This field is equivalent to the `metadata` field in the v1beta1 API.
      They have the same syntax and read/write to the same location in Service
      Directory.

  Fields:
    address: Optional. An IPv4 or IPv6 address. Service Directory rejects bad
      addresses like: * `8.8.8` * `*******:53` * `test:bad:address` * `[::1]`
      * `[::1]:8080` Limited to 45 characters.
    annotations: Optional. Annotations for the endpoint. This data can be
      consumed by service clients. Restrictions: * The entire annotations
      dictionary may contain up to 512 characters, spread accoss all key-value
      pairs. Annotations that go beyond this limit are rejected * Valid
      annotation keys have two segments: an optional prefix and name,
      separated by a slash (/). The name segment is required and must be 63
      characters or less, beginning and ending with an alphanumeric character
      ([a-z0-9A-Z]) with dashes (-), underscores (_), dots (.), and
      alphanumerics between. The prefix is optional. If specified, the prefix
      must be a DNS subdomain: a series of DNS labels separated by dots (.),
      not longer than 253 characters in total, followed by a slash (/)
      Annotations that fails to meet these requirements are rejected. Note:
      This field is equivalent to the `metadata` field in the v1beta1 API.
      They have the same syntax and read/write to the same location in Service
      Directory.
    name: Immutable. The resource name for the endpoint in the format
      `projects/*/locations/*/namespaces/*/services/*/endpoints/*`.
    network: Immutable. The Google Compute Engine network (VPC) of the
      endpoint in the format `projects//locations/global/networks/*`. The
      project must be specified by project number (project id is rejected).
      Incorrectly formatted networks are rejected, we also check to make sure
      that you have the servicedirectory.networks.attach permission on the
      project specified.
    port: Optional. Service Directory rejects values outside of `[0, 65535]`.
    uid: Output only. The globally unique identifier of the endpoint in the
      UUID4 format.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Annotations for the endpoint. This data can be consumed by
    service clients. Restrictions: * The entire annotations dictionary may
    contain up to 512 characters, spread accoss all key-value pairs.
    Annotations that go beyond this limit are rejected * Valid annotation keys
    have two segments: an optional prefix and name, separated by a slash (/).
    The name segment is required and must be 63 characters or less, beginning
    and ending with an alphanumeric character ([a-z0-9A-Z]) with dashes (-),
    underscores (_), dots (.), and alphanumerics between. The prefix is
    optional. If specified, the prefix must be a DNS subdomain: a series of
    DNS labels separated by dots (.), not longer than 253 characters in total,
    followed by a slash (/) Annotations that fails to meet these requirements
    are rejected. Note: This field is equivalent to the `metadata` field in
    the v1beta1 API. They have the same syntax and read/write to the same
    location in Service Directory.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  address = _messages.StringField(1)
  annotations = _messages.MessageField('AnnotationsValue', 2)
  name = _messages.StringField(3)
  network = _messages.StringField(4)
  port = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  uid = _messages.StringField(6)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GetIamPolicyRequest(_messages.Message):
  r"""Request message for `GetIamPolicy` method.

  Fields:
    options: OPTIONAL: A `GetPolicyOptions` object for specifying options to
      `GetIamPolicy`.
  """

  options = _messages.MessageField('GetPolicyOptions', 1)


class GetPolicyOptions(_messages.Message):
  r"""Encapsulates settings provided to GetIamPolicy.

  Fields:
    requestedPolicyVersion: Optional. The maximum policy version that will be
      used to format the policy. Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected. Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset. The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1. To learn which resources support conditions in
      their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class ListEndpointsResponse(_messages.Message):
  r"""The response message for RegistrationService.ListEndpoints.

  Fields:
    endpoints: The list of endpoints.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  endpoints = _messages.MessageField('Endpoint', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListNamespacesResponse(_messages.Message):
  r"""The response message for RegistrationService.ListNamespaces.

  Fields:
    namespaces: The list of namespaces.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  namespaces = _messages.MessageField('Namespace', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListServicesResponse(_messages.Message):
  r"""The response message for RegistrationService.ListServices.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    services: The list of services.
  """

  nextPageToken = _messages.StringField(1)
  services = _messages.MessageField('Service', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class Namespace(_messages.Message):
  r"""A container for services. Namespaces allow administrators to group
  services together and define permissions for a collection of services.

  Messages:
    LabelsValue: Optional. Resource labels associated with this namespace. No
      more than 64 user labels can be associated with a given resource. Label
      keys and values can be no longer than 63 characters.

  Fields:
    labels: Optional. Resource labels associated with this namespace. No more
      than 64 user labels can be associated with a given resource. Label keys
      and values can be no longer than 63 characters.
    name: Immutable. The resource name for the namespace in the format
      `projects/*/locations/*/namespaces/*`.
    uid: Output only. The globally unique identifier of the namespace in the
      UUID4 format.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels associated with this namespace. No more than
    64 user labels can be associated with a given resource. Label keys and
    values can be no longer than 63 characters.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)
  name = _messages.StringField(2)
  uid = _messages.StringField(3)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  bindings = _messages.MessageField('Binding', 1, repeated=True)
  etag = _messages.BytesField(2)
  version = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class ResolveServiceRequest(_messages.Message):
  r"""The request message for LookupService.ResolveService. Looks up a service
  by its name, returns the service and its endpoints.

  Fields:
    endpointFilter: Optional. The filter applied to the endpoints of the
      resolved service. General `filter` string syntax: ` ()` * `` can be
      `name`, `address`, `port`, or `annotations.` for map field * `` can be
      `<`, `>`, `<=`, `>=`, `!=`, `=`, `:`. Of which `:` means `HAS`, and is
      roughly the same as `=` * `` must be the same data type as field * ``
      can be `AND`, `OR`, `NOT` Examples of valid filters: *
      `annotations.owner` returns endpoints that have a annotation with the
      key `owner`, this is the same as `annotations:owner` *
      `annotations.protocol=gRPC` returns endpoints that have key/value
      `protocol=gRPC` * `address=*************` returns endpoints that have
      this address * `port>8080` returns endpoints that have port number
      larger than 8080 * `name>projects/my-project/locations/us-
      east1/namespaces/my-namespace/services/my-service/endpoints/endpoint-c`
      returns endpoints that have name that is alphabetically later than the
      string, so "endpoint-e" is returned but "endpoint-a" is not *
      `name=projects/my-project/locations/us-central1/namespaces/my-
      namespace/services/my-service/endpoints/ep-1` returns the endpoint that
      has an endpoint_id equal to `ep-1` * `annotations.owner!=sd AND
      annotations.foo=bar` returns endpoints that have `owner` in annotation
      key but value is not `sd` AND have key/value `foo=bar` *
      `doesnotexist.foo=bar` returns an empty list. Note that endpoint doesn't
      have a field called "doesnotexist". Since the filter does not match any
      endpoint, it returns no results For more information about filtering,
      see [API Filtering](https://aip.dev/160).
    maxEndpoints: Optional. The maximum number of endpoints to return.
      Defaults to 25. Maximum is 100. If a value less than one is specified,
      the Default is used. If a value greater than the Maximum is specified,
      the Maximum is used.
  """

  endpointFilter = _messages.StringField(1)
  maxEndpoints = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class ResolveServiceResponse(_messages.Message):
  r"""The response message for LookupService.ResolveService.

  Fields:
    service: A Service attribute.
  """

  service = _messages.MessageField('Service', 1)


class Service(_messages.Message):
  r"""An individual service. A service contains a name and optional metadata.
  A service must exist before endpoints can be added to it.

  Messages:
    AnnotationsValue: Optional. Annotations for the service. This data can be
      consumed by service clients. Restrictions: * The entire annotations
      dictionary may contain up to 2000 characters, spread accoss all key-
      value pairs. Annotations that go beyond this limit are rejected * Valid
      annotation keys have two segments: an optional prefix and name,
      separated by a slash (/). The name segment is required and must be 63
      characters or less, beginning and ending with an alphanumeric character
      ([a-z0-9A-Z]) with dashes (-), underscores (_), dots (.), and
      alphanumerics between. The prefix is optional. If specified, the prefix
      must be a DNS subdomain: a series of DNS labels separated by dots (.),
      not longer than 253 characters in total, followed by a slash (/).
      Annotations that fails to meet these requirements are rejected Note:
      This field is equivalent to the `metadata` field in the v1beta1 API.
      They have the same syntax and read/write to the same location in Service
      Directory.

  Fields:
    annotations: Optional. Annotations for the service. This data can be
      consumed by service clients. Restrictions: * The entire annotations
      dictionary may contain up to 2000 characters, spread accoss all key-
      value pairs. Annotations that go beyond this limit are rejected * Valid
      annotation keys have two segments: an optional prefix and name,
      separated by a slash (/). The name segment is required and must be 63
      characters or less, beginning and ending with an alphanumeric character
      ([a-z0-9A-Z]) with dashes (-), underscores (_), dots (.), and
      alphanumerics between. The prefix is optional. If specified, the prefix
      must be a DNS subdomain: a series of DNS labels separated by dots (.),
      not longer than 253 characters in total, followed by a slash (/).
      Annotations that fails to meet these requirements are rejected Note:
      This field is equivalent to the `metadata` field in the v1beta1 API.
      They have the same syntax and read/write to the same location in Service
      Directory.
    endpoints: Output only. Endpoints associated with this service. Returned
      on LookupService.ResolveService. Control plane clients should use
      RegistrationService.ListEndpoints.
    name: Immutable. The resource name for the service in the format
      `projects/*/locations/*/namespaces/*/services/*`.
    uid: Output only. The globally unique identifier of the service in the
      UUID4 format.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. Annotations for the service. This data can be consumed by
    service clients. Restrictions: * The entire annotations dictionary may
    contain up to 2000 characters, spread accoss all key-value pairs.
    Annotations that go beyond this limit are rejected * Valid annotation keys
    have two segments: an optional prefix and name, separated by a slash (/).
    The name segment is required and must be 63 characters or less, beginning
    and ending with an alphanumeric character ([a-z0-9A-Z]) with dashes (-),
    underscores (_), dots (.), and alphanumerics between. The prefix is
    optional. If specified, the prefix must be a DNS subdomain: a series of
    DNS labels separated by dots (.), not longer than 253 characters in total,
    followed by a slash (/). Annotations that fails to meet these requirements
    are rejected Note: This field is equivalent to the `metadata` field in the
    v1beta1 API. They have the same syntax and read/write to the same location
    in Service Directory.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  endpoints = _messages.MessageField('Endpoint', 2, repeated=True)
  name = _messages.StringField(3)
  uid = _messages.StringField(4)


class ServicedirectoryProjectsLocationsGetRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class ServicedirectoryProjectsLocationsListRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class ServicedirectoryProjectsLocationsNamespacesCreateRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesCreateRequest object.

  Fields:
    namespace: A Namespace resource to be passed as the request body.
    namespaceId: Required. The Resource ID must be 1-63 characters long, and
      comply with RFC1035. Specifically, the name must be 1-63 characters long
      and match the regular expression `[a-z](?:[-a-z0-9]{0,61}[a-z0-9])?`
      which means the first character must be a lowercase letter, and all
      following characters must be a dash, lowercase letter, or digit, except
      the last character, which cannot be a dash.
    parent: Required. The resource name of the project and location the
      namespace will be created in.
  """

  namespace = _messages.MessageField('Namespace', 1)
  namespaceId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ServicedirectoryProjectsLocationsNamespacesDeleteRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesDeleteRequest object.

  Fields:
    name: Required. The name of the namespace to delete.
  """

  name = _messages.StringField(1, required=True)


class ServicedirectoryProjectsLocationsNamespacesGetIamPolicyRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class ServicedirectoryProjectsLocationsNamespacesGetRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesGetRequest object.

  Fields:
    name: Required. The name of the namespace to retrieve.
  """

  name = _messages.StringField(1, required=True)


class ServicedirectoryProjectsLocationsNamespacesListRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesListRequest object.

  Fields:
    filter: Optional. The filter to list results by. General `filter` string
      syntax: ` ()` * `` can be `name` or `labels.` for map field * `` can be
      `<`, `>`, `<=`, `>=`, `!=`, `=`, `:`. Of which `:` means `HAS`, and is
      roughly the same as `=` * `` must be the same data type as field * ``
      can be `AND`, `OR`, `NOT` Examples of valid filters: * `labels.owner`
      returns namespaces that have a label with the key `owner`, this is the
      same as `labels:owner` * `labels.owner=sd` returns namespaces that have
      key/value `owner=sd` * `name>projects/my-project/locations/us-
      east1/namespaces/namespace-c` returns namespaces that have name that is
      alphabetically later than the string, so "namespace-e" is returned but
      "namespace-a" is not * `labels.owner!=sd AND labels.foo=bar` returns
      namespaces that have `owner` in label key but value is not `sd` AND have
      key/value `foo=bar` * `doesnotexist.foo=bar` returns an empty list. Note
      that namespace doesn't have a field called "doesnotexist". Since the
      filter does not match any namespaces, it returns no results For more
      information about filtering, see [API Filtering](https://aip.dev/160).
    orderBy: Optional. The order to list results by. General `order_by` string
      syntax: ` () (,)` * `` allows value: `name` * `` ascending or descending
      order by ``. If this is left blank, `asc` is used Note that an empty
      `order_by` string results in default order, which is order by `name` in
      ascending order.
    pageSize: Optional. The maximum number of items to return.
    pageToken: Optional. The next_page_token value returned from a previous
      List request, if any.
    parent: Required. The resource name of the project and location whose
      namespaces you'd like to list.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ServicedirectoryProjectsLocationsNamespacesPatchRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesPatchRequest object.

  Fields:
    name: Immutable. The resource name for the namespace in the format
      `projects/*/locations/*/namespaces/*`.
    namespace: A Namespace resource to be passed as the request body.
    updateMask: Required. List of fields to be updated in this request.
  """

  name = _messages.StringField(1, required=True)
  namespace = _messages.MessageField('Namespace', 2)
  updateMask = _messages.StringField(3)


class ServicedirectoryProjectsLocationsNamespacesServicesCreateRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesServicesCreateRequest
  object.

  Fields:
    parent: Required. The resource name of the namespace this service will
      belong to.
    service: A Service resource to be passed as the request body.
    serviceId: Required. The Resource ID must be 1-63 characters long, and
      comply with RFC1035. Specifically, the name must be 1-63 characters long
      and match the regular expression `[a-z](?:[-a-z0-9]{0,61}[a-z0-9])?`
      which means the first character must be a lowercase letter, and all
      following characters must be a dash, lowercase letter, or digit, except
      the last character, which cannot be a dash.
  """

  parent = _messages.StringField(1, required=True)
  service = _messages.MessageField('Service', 2)
  serviceId = _messages.StringField(3)


class ServicedirectoryProjectsLocationsNamespacesServicesDeleteRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesServicesDeleteRequest
  object.

  Fields:
    name: Required. The name of the service to delete.
  """

  name = _messages.StringField(1, required=True)


class ServicedirectoryProjectsLocationsNamespacesServicesEndpointsCreateRequest(_messages.Message):
  r"""A
  ServicedirectoryProjectsLocationsNamespacesServicesEndpointsCreateRequest
  object.

  Fields:
    endpoint: A Endpoint resource to be passed as the request body.
    endpointId: Required. The Resource ID must be 1-63 characters long, and
      comply with RFC1035. Specifically, the name must be 1-63 characters long
      and match the regular expression `[a-z](?:[-a-z0-9]{0,61}[a-z0-9])?`
      which means the first character must be a lowercase letter, and all
      following characters must be a dash, lowercase letter, or digit, except
      the last character, which cannot be a dash.
    parent: Required. The resource name of the service that this endpoint
      provides.
  """

  endpoint = _messages.MessageField('Endpoint', 1)
  endpointId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ServicedirectoryProjectsLocationsNamespacesServicesEndpointsDeleteRequest(_messages.Message):
  r"""A
  ServicedirectoryProjectsLocationsNamespacesServicesEndpointsDeleteRequest
  object.

  Fields:
    name: Required. The name of the endpoint to delete.
  """

  name = _messages.StringField(1, required=True)


class ServicedirectoryProjectsLocationsNamespacesServicesEndpointsGetRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesServicesEndpointsGetRequest
  object.

  Fields:
    name: Required. The name of the endpoint to get.
  """

  name = _messages.StringField(1, required=True)


class ServicedirectoryProjectsLocationsNamespacesServicesEndpointsListRequest(_messages.Message):
  r"""A
  ServicedirectoryProjectsLocationsNamespacesServicesEndpointsListRequest
  object.

  Fields:
    filter: Optional. The filter to list results by. General `filter` string
      syntax: ` ()` * `` can be `name`, `address`, `port`, or `annotations.`
      for map field * `` can be `<`, `>`, `<=`, `>=`, `!=`, `=`, `:`. Of which
      `:` means `HAS`, and is roughly the same as `=` * `` must be the same
      data type as field * `` can be `AND`, `OR`, `NOT` Examples of valid
      filters: * `annotations.owner` returns endpoints that have a annotation
      with the key `owner`, this is the same as `annotations:owner` *
      `annotations.protocol=gRPC` returns endpoints that have key/value
      `protocol=gRPC` * `address=*************` returns endpoints that have
      this address * `port>8080` returns endpoints that have port number
      larger than 8080 * `name>projects/my-project/locations/us-
      east1/namespaces/my-namespace/services/my-service/endpoints/endpoint-c`
      returns endpoints that have name that is alphabetically later than the
      string, so "endpoint-e" is returned but "endpoint-a" is not *
      `annotations.owner!=sd AND annotations.foo=bar` returns endpoints that
      have `owner` in annotation key but value is not `sd` AND have key/value
      `foo=bar` * `doesnotexist.foo=bar` returns an empty list. Note that
      endpoint doesn't have a field called "doesnotexist". Since the filter
      does not match any endpoints, it returns no results For more information
      about filtering, see [API Filtering](https://aip.dev/160).
    orderBy: Optional. The order to list results by. General `order_by` string
      syntax: ` () (,)` * `` allows values: `name`, `address`, `port` * ``
      ascending or descending order by ``. If this is left blank, `asc` is
      used Note that an empty `order_by` string results in default order,
      which is order by `name` in ascending order.
    pageSize: Optional. The maximum number of items to return.
    pageToken: Optional. The next_page_token value returned from a previous
      List request, if any.
    parent: Required. The resource name of the service whose endpoints you'd
      like to list.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ServicedirectoryProjectsLocationsNamespacesServicesEndpointsPatchRequest(_messages.Message):
  r"""A
  ServicedirectoryProjectsLocationsNamespacesServicesEndpointsPatchRequest
  object.

  Fields:
    endpoint: A Endpoint resource to be passed as the request body.
    name: Immutable. The resource name for the endpoint in the format
      `projects/*/locations/*/namespaces/*/services/*/endpoints/*`.
    updateMask: Required. List of fields to be updated in this request.
  """

  endpoint = _messages.MessageField('Endpoint', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ServicedirectoryProjectsLocationsNamespacesServicesGetIamPolicyRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesServicesGetIamPolicyRequest
  object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class ServicedirectoryProjectsLocationsNamespacesServicesGetRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesServicesGetRequest object.

  Fields:
    name: Required. The name of the service to get.
  """

  name = _messages.StringField(1, required=True)


class ServicedirectoryProjectsLocationsNamespacesServicesListRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesServicesListRequest object.

  Fields:
    filter: Optional. The filter to list results by. General `filter` string
      syntax: ` ()` * `` can be `name` or `annotations.` for map field * ``
      can be `<`, `>`, `<=`, `>=`, `!=`, `=`, `:`. Of which `:` means `HAS`,
      and is roughly the same as `=` * `` must be the same data type as field
      * `` can be `AND`, `OR`, `NOT` Examples of valid filters: *
      `annotations.owner` returns services that have a annotation with the key
      `owner`, this is the same as `annotations:owner` *
      `annotations.protocol=gRPC` returns services that have key/value
      `protocol=gRPC` * `name>projects/my-project/locations/us-
      east1/namespaces/my-namespace/services/service-c` returns services that
      have name that is alphabetically later than the string, so "service-e"
      is returned but "service-a" is not * `annotations.owner!=sd AND
      annotations.foo=bar` returns services that have `owner` in annotation
      key but value is not `sd` AND have key/value `foo=bar` *
      `doesnotexist.foo=bar` returns an empty list. Note that service doesn't
      have a field called "doesnotexist". Since the filter does not match any
      services, it returns no results For more information about filtering,
      see [API Filtering](https://aip.dev/160).
    orderBy: Optional. The order to list results by. General `order_by` string
      syntax: ` () (,)` * `` allows value: `name` * `` ascending or descending
      order by ``. If this is left blank, `asc` is used Note that an empty
      `order_by` string results in default order, which is order by `name` in
      ascending order.
    pageSize: Optional. The maximum number of items to return.
    pageToken: Optional. The next_page_token value returned from a previous
      List request, if any.
    parent: Required. The resource name of the namespace whose services you'd
      like to list.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ServicedirectoryProjectsLocationsNamespacesServicesPatchRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesServicesPatchRequest
  object.

  Fields:
    name: Immutable. The resource name for the service in the format
      `projects/*/locations/*/namespaces/*/services/*`.
    service: A Service resource to be passed as the request body.
    updateMask: Required. List of fields to be updated in this request.
  """

  name = _messages.StringField(1, required=True)
  service = _messages.MessageField('Service', 2)
  updateMask = _messages.StringField(3)


class ServicedirectoryProjectsLocationsNamespacesServicesResolveRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesServicesResolveRequest
  object.

  Fields:
    name: Required. The name of the service to resolve.
    resolveServiceRequest: A ResolveServiceRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  resolveServiceRequest = _messages.MessageField('ResolveServiceRequest', 2)


class ServicedirectoryProjectsLocationsNamespacesServicesSetIamPolicyRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesServicesSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class ServicedirectoryProjectsLocationsNamespacesServicesTestIamPermissionsRequest(_messages.Message):
  r"""A
  ServicedirectoryProjectsLocationsNamespacesServicesTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class ServicedirectoryProjectsLocationsNamespacesSetIamPolicyRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class ServicedirectoryProjectsLocationsNamespacesTestIamPermissionsRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
  """

  policy = _messages.MessageField('Policy', 1)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
