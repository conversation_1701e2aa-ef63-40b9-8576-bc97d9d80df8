"""Generated client library for mediaasset version v1alpha."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.mediaasset.v1alpha import mediaasset_v1alpha_messages as messages


class MediaassetV1alpha(base_api.BaseApiClient):
  """Generated client library for service mediaasset version v1alpha."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://mediaasset.googleapis.com/'
  MTLS_BASE_URL = 'https://mediaasset.mtls.googleapis.com/'

  _PACKAGE = 'mediaasset'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1alpha'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'MediaassetV1alpha'
  _URL_VERSION = 'v1alpha'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new mediaasset handle."""
    url = url or self.BASE_URL
    super(MediaassetV1alpha, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_assetTypes_assets_actions = self.ProjectsLocationsAssetTypesAssetsActionsService(self)
    self.projects_locations_assetTypes_assets_annotationSets_annotations = self.ProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsService(self)
    self.projects_locations_assetTypes_assets_annotationSets = self.ProjectsLocationsAssetTypesAssetsAnnotationSetsService(self)
    self.projects_locations_assetTypes_assets = self.ProjectsLocationsAssetTypesAssetsService(self)
    self.projects_locations_assetTypes_rules = self.ProjectsLocationsAssetTypesRulesService(self)
    self.projects_locations_assetTypes = self.ProjectsLocationsAssetTypesService(self)
    self.projects_locations_catalogs = self.ProjectsLocationsCatalogsService(self)
    self.projects_locations_complexTypes = self.ProjectsLocationsComplexTypesService(self)
    self.projects_locations_modules = self.ProjectsLocationsModulesService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_transformers = self.ProjectsLocationsTransformersService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsAssetTypesAssetsActionsService(base_api.BaseApiService):
    """Service class for the projects_locations_assetTypes_assets_actions resource."""

    _NAME = 'projects_locations_assetTypes_assets_actions'

    def __init__(self, client):
      super(MediaassetV1alpha.ProjectsLocationsAssetTypesAssetsActionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancel any pending invocations under this action.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesAssetsActionsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CancelActionResponse) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/assets/{assetsId}/actions/{actionsId}:cancel',
        http_method='POST',
        method_id='mediaasset.projects.locations.assetTypes.assets.actions.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:cancel',
        request_field='cancelActionRequest',
        request_type_name='MediaassetProjectsLocationsAssetTypesAssetsActionsCancelRequest',
        response_type_name='CancelActionResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single action.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesAssetsActionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Action) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/assets/{assetsId}/actions/{actionsId}',
        http_method='GET',
        method_id='mediaasset.projects.locations.assetTypes.assets.actions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='MediaassetProjectsLocationsAssetTypesAssetsActionsGetRequest',
        response_type_name='Action',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists actions in a given project and location.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesAssetsActionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListActionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/assets/{assetsId}/actions',
        http_method='GET',
        method_id='mediaasset.projects.locations.assetTypes.assets.actions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/actions',
        request_field='',
        request_type_name='MediaassetProjectsLocationsAssetTypesAssetsActionsListRequest',
        response_type_name='ListActionsResponse',
        supports_download=False,
    )

    def Trigger(self, request, global_params=None):
      r"""Trigger an invocation with the latest input state.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesAssetsActionsTriggerRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TriggerActionResponse) The response message.
      """
      config = self.GetMethodConfig('Trigger')
      return self._RunMethod(
          config, request, global_params=global_params)

    Trigger.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/assets/{assetsId}/actions/{actionsId}:trigger',
        http_method='POST',
        method_id='mediaasset.projects.locations.assetTypes.assets.actions.trigger',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:trigger',
        request_field='triggerActionRequest',
        request_type_name='MediaassetProjectsLocationsAssetTypesAssetsActionsTriggerRequest',
        response_type_name='TriggerActionResponse',
        supports_download=False,
    )

  class ProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsService(base_api.BaseApiService):
    """Service class for the projects_locations_assetTypes_assets_annotationSets_annotations resource."""

    _NAME = 'projects_locations_assetTypes_assets_annotationSets_annotations'

    def __init__(self, client):
      super(MediaassetV1alpha.ProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new annotation in a given project and location.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Annotation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/assets/{assetsId}/annotationSets/{annotationSetsId}/annotations',
        http_method='POST',
        method_id='mediaasset.projects.locations.assetTypes.assets.annotationSets.annotations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['annotationId'],
        relative_path='v1alpha/{+parent}/annotations',
        request_field='annotation',
        request_type_name='MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsCreateRequest',
        response_type_name='Annotation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single annotation.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/assets/{assetsId}/annotationSets/{annotationSetsId}/annotations/{annotationsId}',
        http_method='DELETE',
        method_id='mediaasset.projects.locations.assetTypes.assets.annotationSets.annotations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single annotation.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Annotation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/assets/{assetsId}/annotationSets/{annotationSetsId}/annotations/{annotationsId}',
        http_method='GET',
        method_id='mediaasset.projects.locations.assetTypes.assets.annotationSets.annotations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsGetRequest',
        response_type_name='Annotation',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/assets/{assetsId}/annotationSets/{annotationSetsId}/annotations/{annotationsId}:getIamPolicy',
        http_method='GET',
        method_id='mediaasset.projects.locations.assetTypes.assets.annotationSets.annotations.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists annotations in a given project and location.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAnnotationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/assets/{assetsId}/annotationSets/{annotationSetsId}/annotations',
        http_method='GET',
        method_id='mediaasset.projects.locations.assetTypes.assets.annotationSets.annotations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/annotations',
        request_field='',
        request_type_name='MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsListRequest',
        response_type_name='ListAnnotationsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single annotation.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Annotation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/assets/{assetsId}/annotationSets/{annotationSetsId}/annotations/{annotationsId}',
        http_method='PATCH',
        method_id='mediaasset.projects.locations.assetTypes.assets.annotationSets.annotations.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='annotation',
        request_type_name='MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsPatchRequest',
        response_type_name='Annotation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/assets/{assetsId}/annotationSets/{annotationSetsId}/annotations/{annotationsId}:setIamPolicy',
        http_method='POST',
        method_id='mediaasset.projects.locations.assetTypes.assets.annotationSets.annotations.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/assets/{assetsId}/annotationSets/{annotationSetsId}/annotations/{annotationsId}:testIamPermissions',
        http_method='POST',
        method_id='mediaasset.projects.locations.assetTypes.assets.annotationSets.annotations.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsAssetTypesAssetsAnnotationSetsService(base_api.BaseApiService):
    """Service class for the projects_locations_assetTypes_assets_annotationSets resource."""

    _NAME = 'projects_locations_assetTypes_assets_annotationSets'

    def __init__(self, client):
      super(MediaassetV1alpha.ProjectsLocationsAssetTypesAssetsAnnotationSetsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details of a single annotationSet.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AnnotationSet) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/assets/{assetsId}/annotationSets/{annotationSetsId}',
        http_method='GET',
        method_id='mediaasset.projects.locations.assetTypes.assets.annotationSets.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsGetRequest',
        response_type_name='AnnotationSet',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/assets/{assetsId}/annotationSets/{annotationSetsId}:getIamPolicy',
        http_method='GET',
        method_id='mediaasset.projects.locations.assetTypes.assets.annotationSets.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists annotationSets in a given project and location.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAnnotationSetsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/assets/{assetsId}/annotationSets',
        http_method='GET',
        method_id='mediaasset.projects.locations.assetTypes.assets.annotationSets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/annotationSets',
        request_field='',
        request_type_name='MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsListRequest',
        response_type_name='ListAnnotationSetsResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/assets/{assetsId}/annotationSets/{annotationSetsId}:setIamPolicy',
        http_method='POST',
        method_id='mediaasset.projects.locations.assetTypes.assets.annotationSets.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/assets/{assetsId}/annotationSets/{annotationSetsId}:testIamPermissions',
        http_method='POST',
        method_id='mediaasset.projects.locations.assetTypes.assets.annotationSets.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsAssetTypesAssetsService(base_api.BaseApiService):
    """Service class for the projects_locations_assetTypes_assets resource."""

    _NAME = 'projects_locations_assetTypes_assets'

    def __init__(self, client):
      super(MediaassetV1alpha.ProjectsLocationsAssetTypesAssetsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new asset in a given project and location.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesAssetsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/assets',
        http_method='POST',
        method_id='mediaasset.projects.locations.assetTypes.assets.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['assetId'],
        relative_path='v1alpha/{+parent}/assets',
        request_field='asset',
        request_type_name='MediaassetProjectsLocationsAssetTypesAssetsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single asset.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesAssetsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/assets/{assetsId}',
        http_method='DELETE',
        method_id='mediaasset.projects.locations.assetTypes.assets.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'requestId'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='MediaassetProjectsLocationsAssetTypesAssetsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single asset.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesAssetsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Asset) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/assets/{assetsId}',
        http_method='GET',
        method_id='mediaasset.projects.locations.assetTypes.assets.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['readMask'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='MediaassetProjectsLocationsAssetTypesAssetsGetRequest',
        response_type_name='Asset',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesAssetsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/assets/{assetsId}:getIamPolicy',
        http_method='GET',
        method_id='mediaasset.projects.locations.assetTypes.assets.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='MediaassetProjectsLocationsAssetTypesAssetsGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists assets in a given project and location.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesAssetsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAssetsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/assets',
        http_method='GET',
        method_id='mediaasset.projects.locations.assetTypes.assets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1alpha/{+parent}/assets',
        request_field='',
        request_type_name='MediaassetProjectsLocationsAssetTypesAssetsListRequest',
        response_type_name='ListAssetsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single asset.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesAssetsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/assets/{assetsId}',
        http_method='PATCH',
        method_id='mediaasset.projects.locations.assetTypes.assets.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='asset',
        request_type_name='MediaassetProjectsLocationsAssetTypesAssetsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesAssetsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/assets/{assetsId}:setIamPolicy',
        http_method='POST',
        method_id='mediaasset.projects.locations.assetTypes.assets.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='MediaassetProjectsLocationsAssetTypesAssetsSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesAssetsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/assets/{assetsId}:testIamPermissions',
        http_method='POST',
        method_id='mediaasset.projects.locations.assetTypes.assets.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='MediaassetProjectsLocationsAssetTypesAssetsTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsAssetTypesRulesService(base_api.BaseApiService):
    """Service class for the projects_locations_assetTypes_rules resource."""

    _NAME = 'projects_locations_assetTypes_rules'

    def __init__(self, client):
      super(MediaassetV1alpha.ProjectsLocationsAssetTypesRulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new rule in a given project and location.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesRulesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/rules',
        http_method='POST',
        method_id='mediaasset.projects.locations.assetTypes.rules.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['ruleId'],
        relative_path='v1alpha/{+parent}/rules',
        request_field='rule',
        request_type_name='MediaassetProjectsLocationsAssetTypesRulesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single rule.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesRulesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/rules/{rulesId}',
        http_method='DELETE',
        method_id='mediaasset.projects.locations.assetTypes.rules.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='MediaassetProjectsLocationsAssetTypesRulesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single rule.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesRulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Rule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/rules/{rulesId}',
        http_method='GET',
        method_id='mediaasset.projects.locations.assetTypes.rules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='MediaassetProjectsLocationsAssetTypesRulesGetRequest',
        response_type_name='Rule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists rules in a given project and location.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesRulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListRulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/rules',
        http_method='GET',
        method_id='mediaasset.projects.locations.assetTypes.rules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/rules',
        request_field='',
        request_type_name='MediaassetProjectsLocationsAssetTypesRulesListRequest',
        response_type_name='ListRulesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single rule.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesRulesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}/rules/{rulesId}',
        http_method='PATCH',
        method_id='mediaasset.projects.locations.assetTypes.rules.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='rule',
        request_type_name='MediaassetProjectsLocationsAssetTypesRulesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsAssetTypesService(base_api.BaseApiService):
    """Service class for the projects_locations_assetTypes resource."""

    _NAME = 'projects_locations_assetTypes'

    def __init__(self, client):
      super(MediaassetV1alpha.ProjectsLocationsAssetTypesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new AssetType in a given project and location.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes',
        http_method='POST',
        method_id='mediaasset.projects.locations.assetTypes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['assetTypeId', 'requestId'],
        relative_path='v1alpha/{+parent}/assetTypes',
        request_field='assetType',
        request_type_name='MediaassetProjectsLocationsAssetTypesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single AssetType.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}',
        http_method='DELETE',
        method_id='mediaasset.projects.locations.assetTypes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='MediaassetProjectsLocationsAssetTypesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single AssetType.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AssetType) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}',
        http_method='GET',
        method_id='mediaasset.projects.locations.assetTypes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='MediaassetProjectsLocationsAssetTypesGetRequest',
        response_type_name='AssetType',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}:getIamPolicy',
        http_method='GET',
        method_id='mediaasset.projects.locations.assetTypes.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='MediaassetProjectsLocationsAssetTypesGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists AssetTypes in a given project and location.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAssetTypesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes',
        http_method='GET',
        method_id='mediaasset.projects.locations.assetTypes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/assetTypes',
        request_field='',
        request_type_name='MediaassetProjectsLocationsAssetTypesListRequest',
        response_type_name='ListAssetTypesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single AssetType.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}',
        http_method='PATCH',
        method_id='mediaasset.projects.locations.assetTypes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='assetType',
        request_type_name='MediaassetProjectsLocationsAssetTypesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Search(self, request, global_params=None):
      r"""Search returns the resources (e.g., assets and annotations) under a Video Asset Type that match the given query. Search covers both media content and metadata.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesSearchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SearchAssetTypeResponse) The response message.
      """
      config = self.GetMethodConfig('Search')
      return self._RunMethod(
          config, request, global_params=global_params)

    Search.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}:search',
        http_method='POST',
        method_id='mediaasset.projects.locations.assetTypes.search',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:search',
        request_field='searchAssetTypeRequest',
        request_type_name='MediaassetProjectsLocationsAssetTypesSearchRequest',
        response_type_name='SearchAssetTypeResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}:setIamPolicy',
        http_method='POST',
        method_id='mediaasset.projects.locations.assetTypes.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='MediaassetProjectsLocationsAssetTypesSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (MediaassetProjectsLocationsAssetTypesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/assetTypes/{assetTypesId}:testIamPermissions',
        http_method='POST',
        method_id='mediaasset.projects.locations.assetTypes.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='MediaassetProjectsLocationsAssetTypesTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsCatalogsService(base_api.BaseApiService):
    """Service class for the projects_locations_catalogs resource."""

    _NAME = 'projects_locations_catalogs'

    def __init__(self, client):
      super(MediaassetV1alpha.ProjectsLocationsCatalogsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new catalog in a given project and location.

      Args:
        request: (MediaassetProjectsLocationsCatalogsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/catalogs',
        http_method='POST',
        method_id='mediaasset.projects.locations.catalogs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['catalogId'],
        relative_path='v1alpha/{+parent}/catalogs',
        request_field='catalog',
        request_type_name='MediaassetProjectsLocationsCatalogsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single catalog.

      Args:
        request: (MediaassetProjectsLocationsCatalogsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}',
        http_method='DELETE',
        method_id='mediaasset.projects.locations.catalogs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='MediaassetProjectsLocationsCatalogsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single catalog.

      Args:
        request: (MediaassetProjectsLocationsCatalogsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Catalog) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}',
        http_method='GET',
        method_id='mediaasset.projects.locations.catalogs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='MediaassetProjectsLocationsCatalogsGetRequest',
        response_type_name='Catalog',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists catalogs in a given project and location.

      Args:
        request: (MediaassetProjectsLocationsCatalogsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListCatalogsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/catalogs',
        http_method='GET',
        method_id='mediaasset.projects.locations.catalogs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/catalogs',
        request_field='',
        request_type_name='MediaassetProjectsLocationsCatalogsListRequest',
        response_type_name='ListCatalogsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single catalog.

      Args:
        request: (MediaassetProjectsLocationsCatalogsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}',
        http_method='PATCH',
        method_id='mediaasset.projects.locations.catalogs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='catalog',
        request_type_name='MediaassetProjectsLocationsCatalogsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Search(self, request, global_params=None):
      r"""Search returns the resources (e.g., assets and annotations) under a Catalog that match the given query. Search covers both media content and metadata.

      Args:
        request: (MediaassetProjectsLocationsCatalogsSearchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CatalogSearchResponse) The response message.
      """
      config = self.GetMethodConfig('Search')
      return self._RunMethod(
          config, request, global_params=global_params)

    Search.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/catalogs/{catalogsId}:search',
        http_method='POST',
        method_id='mediaasset.projects.locations.catalogs.search',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:search',
        request_field='catalogSearchRequest',
        request_type_name='MediaassetProjectsLocationsCatalogsSearchRequest',
        response_type_name='CatalogSearchResponse',
        supports_download=False,
    )

  class ProjectsLocationsComplexTypesService(base_api.BaseApiService):
    """Service class for the projects_locations_complexTypes resource."""

    _NAME = 'projects_locations_complexTypes'

    def __init__(self, client):
      super(MediaassetV1alpha.ProjectsLocationsComplexTypesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new complex type in a given project and location.

      Args:
        request: (MediaassetProjectsLocationsComplexTypesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/complexTypes',
        http_method='POST',
        method_id='mediaasset.projects.locations.complexTypes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['complexTypeId', 'requestId'],
        relative_path='v1alpha/{+parent}/complexTypes',
        request_field='complexType',
        request_type_name='MediaassetProjectsLocationsComplexTypesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single complex type.

      Args:
        request: (MediaassetProjectsLocationsComplexTypesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/complexTypes/{complexTypesId}',
        http_method='DELETE',
        method_id='mediaasset.projects.locations.complexTypes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='MediaassetProjectsLocationsComplexTypesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single complex type.

      Args:
        request: (MediaassetProjectsLocationsComplexTypesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ComplexType) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/complexTypes/{complexTypesId}',
        http_method='GET',
        method_id='mediaasset.projects.locations.complexTypes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='MediaassetProjectsLocationsComplexTypesGetRequest',
        response_type_name='ComplexType',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (MediaassetProjectsLocationsComplexTypesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/complexTypes/{complexTypesId}:getIamPolicy',
        http_method='GET',
        method_id='mediaasset.projects.locations.complexTypes.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='MediaassetProjectsLocationsComplexTypesGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists complex types in a given project and location.

      Args:
        request: (MediaassetProjectsLocationsComplexTypesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListComplexTypesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/complexTypes',
        http_method='GET',
        method_id='mediaasset.projects.locations.complexTypes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/complexTypes',
        request_field='',
        request_type_name='MediaassetProjectsLocationsComplexTypesListRequest',
        response_type_name='ListComplexTypesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single complex type.

      Args:
        request: (MediaassetProjectsLocationsComplexTypesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/complexTypes/{complexTypesId}',
        http_method='PATCH',
        method_id='mediaasset.projects.locations.complexTypes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='complexType',
        request_type_name='MediaassetProjectsLocationsComplexTypesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (MediaassetProjectsLocationsComplexTypesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/complexTypes/{complexTypesId}:setIamPolicy',
        http_method='POST',
        method_id='mediaasset.projects.locations.complexTypes.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='MediaassetProjectsLocationsComplexTypesSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (MediaassetProjectsLocationsComplexTypesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/complexTypes/{complexTypesId}:testIamPermissions',
        http_method='POST',
        method_id='mediaasset.projects.locations.complexTypes.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='MediaassetProjectsLocationsComplexTypesTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsModulesService(base_api.BaseApiService):
    """Service class for the projects_locations_modules resource."""

    _NAME = 'projects_locations_modules'

    def __init__(self, client):
      super(MediaassetV1alpha.ProjectsLocationsModulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new module in a given project and location.

      Args:
        request: (MediaassetProjectsLocationsModulesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/modules',
        http_method='POST',
        method_id='mediaasset.projects.locations.modules.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['moduleId'],
        relative_path='v1alpha/{+parent}/modules',
        request_field='module',
        request_type_name='MediaassetProjectsLocationsModulesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single module.

      Args:
        request: (MediaassetProjectsLocationsModulesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/modules/{modulesId}',
        http_method='DELETE',
        method_id='mediaasset.projects.locations.modules.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='MediaassetProjectsLocationsModulesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single module.

      Args:
        request: (MediaassetProjectsLocationsModulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Module) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/modules/{modulesId}',
        http_method='GET',
        method_id='mediaasset.projects.locations.modules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='MediaassetProjectsLocationsModulesGetRequest',
        response_type_name='Module',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists modules in a given project and location.

      Args:
        request: (MediaassetProjectsLocationsModulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListModulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/modules',
        http_method='GET',
        method_id='mediaasset.projects.locations.modules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/modules',
        request_field='',
        request_type_name='MediaassetProjectsLocationsModulesListRequest',
        response_type_name='ListModulesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single module.

      Args:
        request: (MediaassetProjectsLocationsModulesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/modules/{modulesId}',
        http_method='PATCH',
        method_id='mediaasset.projects.locations.modules.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='module',
        request_type_name='MediaassetProjectsLocationsModulesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(MediaassetV1alpha.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.

      Args:
        request: (MediaassetProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='mediaasset.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='MediaassetProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (MediaassetProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='mediaasset.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='MediaassetProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (MediaassetProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='mediaasset.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='MediaassetProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (MediaassetProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='mediaasset.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken', 'returnPartialSuccess'],
        relative_path='v1alpha/{+name}/operations',
        request_field='',
        request_type_name='MediaassetProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsTransformersService(base_api.BaseApiService):
    """Service class for the projects_locations_transformers resource."""

    _NAME = 'projects_locations_transformers'

    def __init__(self, client):
      super(MediaassetV1alpha.ProjectsLocationsTransformersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new transformer in a given project and location.

      Args:
        request: (MediaassetProjectsLocationsTransformersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/transformers',
        http_method='POST',
        method_id='mediaasset.projects.locations.transformers.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'transformerId'],
        relative_path='v1alpha/{+parent}/transformers',
        request_field='transformer',
        request_type_name='MediaassetProjectsLocationsTransformersCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single transformer.

      Args:
        request: (MediaassetProjectsLocationsTransformersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/transformers/{transformersId}',
        http_method='DELETE',
        method_id='mediaasset.projects.locations.transformers.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='MediaassetProjectsLocationsTransformersDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single transformer.

      Args:
        request: (MediaassetProjectsLocationsTransformersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Transformer) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/transformers/{transformersId}',
        http_method='GET',
        method_id='mediaasset.projects.locations.transformers.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='MediaassetProjectsLocationsTransformersGetRequest',
        response_type_name='Transformer',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (MediaassetProjectsLocationsTransformersGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/transformers/{transformersId}:getIamPolicy',
        http_method='GET',
        method_id='mediaasset.projects.locations.transformers.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='MediaassetProjectsLocationsTransformersGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists transformers in a given project and location.

      Args:
        request: (MediaassetProjectsLocationsTransformersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListTransformersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/transformers',
        http_method='GET',
        method_id='mediaasset.projects.locations.transformers.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+parent}/transformers',
        request_field='',
        request_type_name='MediaassetProjectsLocationsTransformersListRequest',
        response_type_name='ListTransformersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single transformer.

      Args:
        request: (MediaassetProjectsLocationsTransformersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/transformers/{transformersId}',
        http_method='PATCH',
        method_id='mediaasset.projects.locations.transformers.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha/{+name}',
        request_field='transformer',
        request_type_name='MediaassetProjectsLocationsTransformersPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (MediaassetProjectsLocationsTransformersSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/transformers/{transformersId}:setIamPolicy',
        http_method='POST',
        method_id='mediaasset.projects.locations.transformers.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='MediaassetProjectsLocationsTransformersSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (MediaassetProjectsLocationsTransformersTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}/transformers/{transformersId}:testIamPermissions',
        http_method='POST',
        method_id='mediaasset.projects.locations.transformers.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='MediaassetProjectsLocationsTransformersTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(MediaassetV1alpha.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (MediaassetProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='mediaasset.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha/{+name}',
        request_field='',
        request_type_name='MediaassetProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (MediaassetProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha/projects/{projectsId}/locations',
        http_method='GET',
        method_id='mediaasset.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['extraLocationTypes', 'filter', 'includeUnrevealedLocations', 'pageSize', 'pageToken'],
        relative_path='v1alpha/{+name}/locations',
        request_field='',
        request_type_name='MediaassetProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(MediaassetV1alpha.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
