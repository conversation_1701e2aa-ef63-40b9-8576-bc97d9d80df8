"""Generated message classes for mediaasset version v1alpha.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'mediaasset'


class Action(_messages.Message):
  r"""An action resource. Action is a read-only resource and is nested under
  an asset. It contains the status of the actions associated with an Asset.

  Enums:
    StateValueValuesEnum: Current state of the action.

  Messages:
    LabelsValue: The labels associated with this resource. Each label is a
      key-value pair.

  Fields:
    assetTransformation: Status of asset transformation, if this action is for
      asset transformation.
    createTime: Output only. The creation time.
    derivedAsset: Status of derived asset action.
    labels: The labels associated with this resource. Each label is a key-
      value pair.
    name: The resource name of the action, in the following form: `projects/{p
      roject}/locations/{location}/assetTypes/{type}/assets/{asset}/actions/{a
      ction}`.
    notification: Status of notification action.
    rule: Required. The resource name of the rule, in the following form:
      `projects/{project}/locations/{location}/assetTypes/{type}/rule/{rule}`.
    state: Current state of the action.
    updateTime: Output only. The last-modified time.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Current state of the action.

    Values:
      STATE_UNSPECIFIED: Transformation state is unspecified.
      QUEUED: Transformation is queued.
      RUNNING: Transformation is in running state.
      SUCCEEDED: Transformation has been completed successfully.
      FAILED: Transformation has been completed unsuccessfully.
      CANCELLED: Transformation has been cancelled.
      CANCELLING: Transformation is in cancelling state.
    """
    STATE_UNSPECIFIED = 0
    QUEUED = 1
    RUNNING = 2
    SUCCEEDED = 3
    FAILED = 4
    CANCELLED = 5
    CANCELLING = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels associated with this resource. Each label is a key-value
    pair.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  assetTransformation = _messages.MessageField('TransformationStatus', 1)
  createTime = _messages.StringField(2)
  derivedAsset = _messages.MessageField('DerivedAssetStatus', 3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  notification = _messages.MessageField('NotificationStatus', 6)
  rule = _messages.StringField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  updateTime = _messages.StringField(9)


class Annotation(_messages.Message):
  r"""An annotation resource is associated with an AnnotationSet and
  represents timed-metadata that can be modified and searched at a high
  throughput.

  Enums:
    StateValueValuesEnum: Output only. State of the annotation.

  Messages:
    DataValue: Required. Data held by the annotation. Data must have "start"
      and "end" fields. For "start" and "end": If the type is datetime, it
      must be a string that follows RFC3339. If the type is number or
      duration, it must be a valid int64.
    LabelsValue: The labels associated with this resource. Each label is a
      key-value pair.

  Fields:
    createTime: Output only. The creation time of the annotation.
    data: Required. Data held by the annotation. Data must have "start" and
      "end" fields. For "start" and "end": If the type is datetime, it must be
      a string that follows RFC3339. If the type is number or duration, it
      must be a valid int64.
    etag: Etag of the resource used in output and update requests.
    labels: The labels associated with this resource. Each label is a key-
      value pair.
    name: Output only. An automatically-generated resource name of the
      annotation `projects/{project}/locations/{location}/assetTypes/{asset_ty
      pe}/assets/{asset}/annotationSets/{annotation_set}/annotations/{annotati
      on}`.
    state: Output only. State of the annotation.
    updateTime: Output only. The latest update time of the annotation.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the annotation.

    Values:
      STATE_UNSPECIFIED: STATE_UNSPECIFIED means state of the annotation is
        unclear.
      ACTIVE: ACTIVE means the annotation is created and ready to be used.
      DELETING: DELETING means the annotation is being deleted. This state
        only shows up in the annotation in pub sub notification when
        DeleteAnnotation method is called.
      PROCESSING: PROCESSING means the annotation is being processed by the
        system. IMS Annotations under PROCESSING state will not show up as
        part of the search result.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    DELETING = 2
    PROCESSING = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DataValue(_messages.Message):
    r"""Required. Data held by the annotation. Data must have "start" and
    "end" fields. For "start" and "end": If the type is datetime, it must be a
    string that follows RFC3339. If the type is number or duration, it must be
    a valid int64.

    Messages:
      AdditionalProperty: An additional property for a DataValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels associated with this resource. Each label is a key-value
    pair.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  data = _messages.MessageField('DataValue', 2)
  etag = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  updateTime = _messages.StringField(7)


class AnnotationChange(_messages.Message):
  r"""Specifies the annotation changes that should trigger notifications.

  Fields:
    annotationSetId: Required. Notifies for changes to any annotation nested
      under the given annotationSet of the parent AssetType.
  """

  annotationSetId = _messages.StringField(1)


class AnnotationSet(_messages.Message):
  r"""An annotationSet resource is associated with an Asset and is a
  collection of timed-metadata that can be modified and searched at a high
  throughput.

  Messages:
    LabelsValue: The labels associated with this resource. Each label is a
      key-value pair.

  Fields:
    createTime: Output only. The creation time of the annotationSet.
    etag: Etag of the resource used in output and update requests.
    labels: The labels associated with this resource. Each label is a key-
      value pair.
    name: A user-specified resource name of the annotationSet `projects/{proje
      ct}/locations/{location}/assetTypes/{asset_type}/assets/{asset}/annotati
      onSets/{annotation_set}`. Here {annotation_set} is a resource id.
      Detailed rules for a resource id are: 1. 1 character minimum, 63
      characters maximum 2. only contains letters, digits, underscore and
      hyphen 3. starts with a letter if length == 1, starts with a letter or
      underscore if length > 1
    updateTime: Output only. The latest update time of the annotationSet.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels associated with this resource. Each label is a key-value
    pair.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  etag = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  updateTime = _messages.StringField(5)


class AnnotationSetConfig(_messages.Message):
  r"""Configuration of an annotationSet.

  Messages:
    IndexedFieldConfigsValue: List of indexed fields (e.g. "data.start") to
      make available in searches with their corresponding properties.

  Fields:
    complexType: Required. Reference to the complex type name, in the
      following form:
      `projects/{project}/locations/{location}/complexTypes/{name}`. Complex
      type of the annotation set config has the following requirements: 1.
      Must have two required fields named start and end. 2. Allowed types for
      start and end: Video asset type: timecode. 3. Start and end should have
      the same type.
    indexedFieldConfigs: List of indexed fields (e.g. "data.start") to make
      available in searches with their corresponding properties.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class IndexedFieldConfigsValue(_messages.Message):
    r"""List of indexed fields (e.g. "data.start") to make available in
    searches with their corresponding properties.

    Messages:
      AdditionalProperty: An additional property for a
        IndexedFieldConfigsValue object.

    Fields:
      additionalProperties: Additional properties of type
        IndexedFieldConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a IndexedFieldConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A IndexedFieldConfig attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('IndexedFieldConfig', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  complexType = _messages.StringField(1)
  indexedFieldConfigs = _messages.MessageField('IndexedFieldConfigsValue', 2)


class Asset(_messages.Message):
  r"""An asset resource. An asset is a core resource that represents a media
  entity. An Asset has an asset type associated with it which specifies the
  schema of the asset.

  Enums:
    StateValueValuesEnum: State of the asset

  Messages:
    LabelsValue: The labels associated with this resource. Each label is a
      key-value pair.
    LinkInfoValue: Information about the links.
    LinkSetsValue: Named collections of Link Sets, each having qualified
      links.
    LinksValue: Links to other assets.
    MetadataValue: List of metadata for this asset.
    MetadataInfoValue: Output only. System-generated information about the
      metadata fields. Includes update time and owner.

  Fields:
    assetId: Output only. The assetID of an asset.
    createTime: Output only. The creation time.
    etag: Etag of the resource used in output and update requests.
    expirationTime: The expiration time of the asset, asset will be deleted
      after this time.
    labels: The labels associated with this resource. Each label is a key-
      value pair.
    linkInfo: Information about the links.
    linkSets: Named collections of Link Sets, each having qualified links.
    links: Links to other assets.
    metadata: List of metadata for this asset.
    metadataInfo: Output only. System-generated information about the metadata
      fields. Includes update time and owner.
    name: The resource name of the asset, in the following form: `projects/{pr
      oject}/locations/{location}/assetTypes/{type}/assets/{asset}`. Here
      {asset} is a resource id. Detailed rules for a resource id are: 1. 1
      character minimum, 63 characters maximum 2. only contains letters,
      digits, underscore and hyphen 3. starts with a letter if length == 1,
      starts with a letter or underscore if length > 1
    owner: The owner of the asset, set by the system.
    state: State of the asset
    updateTime: Output only. Last update time.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""State of the asset

    Values:
      STATE_UNSPECIFIED: STATE_UNSPECIFIED maps with UNKNOWN or CREATING in
        AssetStatus_State in status.proto
      ACTIVE: ACTIVE maps with READY in AssetStatus_State in status.proto
      DELETING: DELETING maps with DELETING in AssetStatus_State in
        status.proto
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    DELETING = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels associated with this resource. Each label is a key-value
    pair.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LinkInfoValue(_messages.Message):
    r"""Information about the links.

    Messages:
      AdditionalProperty: An additional property for a LinkInfoValue object.

    Fields:
      additionalProperties: Additional properties of type LinkInfoValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LinkInfoValue object.

      Fields:
        key: Name of the additional property.
        value: A LinkInfo attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('LinkInfo', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LinkSetsValue(_messages.Message):
    r"""Named collections of Link Sets, each having qualified links.

    Messages:
      AdditionalProperty: An additional property for a LinkSetsValue object.

    Fields:
      additionalProperties: Additional properties of type LinkSetsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LinkSetsValue object.

      Fields:
        key: Name of the additional property.
        value: A LinkSet attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('LinkSet', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LinksValue(_messages.Message):
    r"""Links to other assets.

    Messages:
      AdditionalProperty: An additional property for a LinksValue object.

    Fields:
      additionalProperties: Additional properties of type LinksValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LinksValue object.

      Fields:
        key: Name of the additional property.
        value: A Asset attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Asset', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataInfoValue(_messages.Message):
    r"""Output only. System-generated information about the metadata fields.
    Includes update time and owner.

    Messages:
      AdditionalProperty: An additional property for a MetadataInfoValue
        object.

    Fields:
      additionalProperties: Additional properties of type MetadataInfoValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataInfoValue object.

      Fields:
        key: Name of the additional property.
        value: A MetadataInfo attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('MetadataInfo', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""List of metadata for this asset.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Additional properties of type MetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Messages:
        ValueValue: A ValueValue object.

      Fields:
        key: Name of the additional property.
        value: A ValueValue attribute.
      """

      @encoding.MapUnrecognizedFields('additionalProperties')
      class ValueValue(_messages.Message):
        r"""A ValueValue object.

        Messages:
          AdditionalProperty: An additional property for a ValueValue object.

        Fields:
          additionalProperties: Properties of the object.
        """

        class AdditionalProperty(_messages.Message):
          r"""An additional property for a ValueValue object.

          Fields:
            key: Name of the additional property.
            value: A extra_types.JsonValue attribute.
          """

          key = _messages.StringField(1)
          value = _messages.MessageField('extra_types.JsonValue', 2)

        additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

      key = _messages.StringField(1)
      value = _messages.MessageField('ValueValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  assetId = _messages.StringField(1)
  createTime = _messages.StringField(2)
  etag = _messages.StringField(3)
  expirationTime = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  linkInfo = _messages.MessageField('LinkInfoValue', 6)
  linkSets = _messages.MessageField('LinkSetsValue', 7)
  links = _messages.MessageField('LinksValue', 8)
  metadata = _messages.MessageField('MetadataValue', 9)
  metadataInfo = _messages.MessageField('MetadataInfoValue', 10)
  name = _messages.StringField(11)
  owner = _messages.MessageField('Owner', 12)
  state = _messages.EnumField('StateValueValuesEnum', 13)
  updateTime = _messages.StringField(14)


class AssetChange(_messages.Message):
  r"""Specifies the asset changes that should trigger notifications."""


class AssetType(_messages.Message):
  r"""An asset type resource. An asset type defines the schema for the
  assets.It specifies configuration of all the fields present on the asset.

  Enums:
    MediaTypeValueValuesEnum: Specifies the kind of media held by assets of
      this asset type.

  Messages:
    AnnotationSetConfigsValue: Mapping of annotationSet ID to its
      configuration. The annotationSet ID will be used as the resource ID when
      GCMA creates the AnnotationSet internally. Detailed rules for a resource
      id are: 1. 1 character minimum, 63 characters maximum 2. only contains
      letters, digits, underscore and hyphen 3. starts with a letter if length
      == 1, starts with a letter or underscore if length > 1
    FacetConfigsValue: Mapping of facet name to its configuration. To update
      facets, use either "*" or "facet_configs" update mask.
    IndexedFieldConfigsValue: List of indexed fields (e.g.
      "metadata.file.url") to make available in searches with their
      corresponding properties.
    LabelsValue: The labels associated with this resource. Each label is a
      key-value pair.
    LinkConfigsValue: Mapping of a link field name to its configuration.
    LinkSetConfigsValue: Mapping of a collection of link sets to the set
      configuration.
    MetadataConfigsValue: Mapping of field name to its configuration.

  Fields:
    annotationSetConfigs: Mapping of annotationSet ID to its configuration.
      The annotationSet ID will be used as the resource ID when GCMA creates
      the AnnotationSet internally. Detailed rules for a resource id are: 1. 1
      character minimum, 63 characters maximum 2. only contains letters,
      digits, underscore and hyphen 3. starts with a letter if length == 1,
      starts with a letter or underscore if length > 1
    assetTypeStats: asset_type_stats stores stats on this asset type.
    createTime: Output only. The creation time.
    facetConfigs: Mapping of facet name to its configuration. To update
      facets, use either "*" or "facet_configs" update mask.
    featureConfigs: Configuration for IMS features, including languages for
      speech transcription.
    indexedFieldConfigs: List of indexed fields (e.g. "metadata.file.url") to
      make available in searches with their corresponding properties.
    labels: The labels associated with this resource. Each label is a key-
      value pair.
    linkConfigs: Mapping of a link field name to its configuration.
    linkSetConfigs: Mapping of a collection of link sets to the set
      configuration.
    mediaType: Specifies the kind of media held by assets of this asset type.
    metadataConfigs: Mapping of field name to its configuration.
    name: The resource name of the asset type, in the following form:
      `projects/{project}/locations/{location}/assetTypes/{type}`. Here {type}
      is a resource id. Detailed rules for a resource id are: 1. 1 character
      minimum, 63 characters maximum 2. only contains letters, digits,
      underscore and hyphen 3. starts with a letter if length == 1, starts
      with a letter or underscore if length > 1
    sortOrder: Specifies sort order for all assets of the type. If not
      specified, assets are sorted in reverse create_time order (newest
      first).
    updateTime: Output only. The last-modified time.
  """

  class MediaTypeValueValuesEnum(_messages.Enum):
    r"""Specifies the kind of media held by assets of this asset type.

    Values:
      MEDIA_TYPE_UNSPECIFIED: AssetTypes with unspecified media types hold
        generic assets.
      VIDEO: AssetTypes with video media types have the following properties:
        1. Have a required and immutable metadata field called 'video_file' of
        type 'system:gcs-file', which is the path to a video file. 2. Support
        searching the content of the provided video asset.
    """
    MEDIA_TYPE_UNSPECIFIED = 0
    VIDEO = 1

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationSetConfigsValue(_messages.Message):
    r"""Mapping of annotationSet ID to its configuration. The annotationSet ID
    will be used as the resource ID when GCMA creates the AnnotationSet
    internally. Detailed rules for a resource id are: 1. 1 character minimum,
    63 characters maximum 2. only contains letters, digits, underscore and
    hyphen 3. starts with a letter if length == 1, starts with a letter or
    underscore if length > 1

    Messages:
      AdditionalProperty: An additional property for a
        AnnotationSetConfigsValue object.

    Fields:
      additionalProperties: Additional properties of type
        AnnotationSetConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationSetConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A AnnotationSetConfig attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('AnnotationSetConfig', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FacetConfigsValue(_messages.Message):
    r"""Mapping of facet name to its configuration. To update facets, use
    either "*" or "facet_configs" update mask.

    Messages:
      AdditionalProperty: An additional property for a FacetConfigsValue
        object.

    Fields:
      additionalProperties: Additional properties of type FacetConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FacetConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A FacetConfig attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('FacetConfig', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class IndexedFieldConfigsValue(_messages.Message):
    r"""List of indexed fields (e.g. "metadata.file.url") to make available in
    searches with their corresponding properties.

    Messages:
      AdditionalProperty: An additional property for a
        IndexedFieldConfigsValue object.

    Fields:
      additionalProperties: Additional properties of type
        IndexedFieldConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a IndexedFieldConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A IndexedFieldConfig attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('IndexedFieldConfig', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels associated with this resource. Each label is a key-value
    pair.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LinkConfigsValue(_messages.Message):
    r"""Mapping of a link field name to its configuration.

    Messages:
      AdditionalProperty: An additional property for a LinkConfigsValue
        object.

    Fields:
      additionalProperties: Additional properties of type LinkConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LinkConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A LinkConfig attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('LinkConfig', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LinkSetConfigsValue(_messages.Message):
    r"""Mapping of a collection of link sets to the set configuration.

    Messages:
      AdditionalProperty: An additional property for a LinkSetConfigsValue
        object.

    Fields:
      additionalProperties: Additional properties of type LinkSetConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LinkSetConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A LinkSetConfig attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('LinkSetConfig', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataConfigsValue(_messages.Message):
    r"""Mapping of field name to its configuration.

    Messages:
      AdditionalProperty: An additional property for a MetadataConfigsValue
        object.

    Fields:
      additionalProperties: Additional properties of type MetadataConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A MetadataConfig attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('MetadataConfig', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotationSetConfigs = _messages.MessageField('AnnotationSetConfigsValue', 1)
  assetTypeStats = _messages.MessageField('AssetTypeStats', 2)
  createTime = _messages.StringField(3)
  facetConfigs = _messages.MessageField('FacetConfigsValue', 4)
  featureConfigs = _messages.MessageField('FeatureConfigs', 5)
  indexedFieldConfigs = _messages.MessageField('IndexedFieldConfigsValue', 6)
  labels = _messages.MessageField('LabelsValue', 7)
  linkConfigs = _messages.MessageField('LinkConfigsValue', 8)
  linkSetConfigs = _messages.MessageField('LinkSetConfigsValue', 9)
  mediaType = _messages.EnumField('MediaTypeValueValuesEnum', 10)
  metadataConfigs = _messages.MessageField('MetadataConfigsValue', 11)
  name = _messages.StringField(12)
  sortOrder = _messages.MessageField('SortOrderConfig', 13)
  updateTime = _messages.StringField(14)


class AssetTypeConfig(_messages.Message):
  r"""Catalog search item that includes the asset type and it's configuration.

  Enums:
    StateValueValuesEnum: Output only. Current state of the asset type config.

  Messages:
    IndexedFieldConfigsValue: A map between user-defined key and
      IndexedFieldConfig, where the key is indexed with the value configured
      by IndexedFieldConfig and customers can use the key as search operator
      if the expression is nonempty. If the expression in
      CatalogIndexedFieldConfig is empty, the key must be "".

  Fields:
    assetType: The asset type name that this catalog item configured. Format:
      projects/{project}/locations/{location}/assetTypes/{assetType}
    indexedFieldConfigs: A map between user-defined key and
      IndexedFieldConfig, where the key is indexed with the value configured
      by IndexedFieldConfig and customers can use the key as search operator
      if the expression is nonempty. If the expression in
      CatalogIndexedFieldConfig is empty, the key must be "".
    state: Output only. Current state of the asset type config.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the asset type config.

    Values:
      STATE_UNSPECIFIED: Unknown state.
      CREATING: Asset type config is being created.
      ACTIVE: Asset type config is active.
      DELETING: Asset type config is being deleted and will be automatically
        removed from catalog when the background deletion is done.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class IndexedFieldConfigsValue(_messages.Message):
    r"""A map between user-defined key and IndexedFieldConfig, where the key
    is indexed with the value configured by IndexedFieldConfig and customers
    can use the key as search operator if the expression is nonempty. If the
    expression in CatalogIndexedFieldConfig is empty, the key must be "".

    Messages:
      AdditionalProperty: An additional property for a
        IndexedFieldConfigsValue object.

    Fields:
      additionalProperties: Additional properties of type
        IndexedFieldConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a IndexedFieldConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A CatalogIndexedFieldConfig attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('CatalogIndexedFieldConfig', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  assetType = _messages.StringField(1)
  indexedFieldConfigs = _messages.MessageField('IndexedFieldConfigsValue', 2)
  state = _messages.EnumField('StateValueValuesEnum', 3)


class AssetTypeStats(_messages.Message):
  r"""AssetTypeStats stores stats on this asset type.

  Messages:
    RuleStatsValue: A map from rule_id to RuleStats.

  Fields:
    ruleStats: A map from rule_id to RuleStats.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class RuleStatsValue(_messages.Message):
    r"""A map from rule_id to RuleStats.

    Messages:
      AdditionalProperty: An additional property for a RuleStatsValue object.

    Fields:
      additionalProperties: Additional properties of type RuleStatsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a RuleStatsValue object.

      Fields:
        key: Name of the additional property.
        value: A RuleStats attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('RuleStats', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  ruleStats = _messages.MessageField('RuleStatsValue', 1)


class AuthorizationLoggingOptions(_messages.Message):
  r"""Authorization-related information used by Cloud Audit Logging.

  Enums:
    PermissionTypeValueValuesEnum: The type of the permission that was
      checked.

  Fields:
    permissionType: The type of the permission that was checked.
  """

  class PermissionTypeValueValuesEnum(_messages.Enum):
    r"""The type of the permission that was checked.

    Values:
      PERMISSION_TYPE_UNSPECIFIED: Default. Should not be used.
      ADMIN_READ: A read of admin (meta) data.
      ADMIN_WRITE: A write of admin (meta) data.
      DATA_READ: A read of standard data.
      DATA_WRITE: A write of standard data.
    """
    PERMISSION_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    ADMIN_WRITE = 2
    DATA_READ = 3
    DATA_WRITE = 4

  permissionType = _messages.EnumField('PermissionTypeValueValuesEnum', 1)


class CancelActionRequest(_messages.Message):
  r"""Request message for ActionsService.CancelAction."""


class CancelActionResponse(_messages.Message):
  r"""Response message of ActionsService.CancelAction."""


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class Catalog(_messages.Message):
  r"""A catalog specifies a search index for a collection of assets.

  Messages:
    AssetTypeConfigsValue: A map between asset type name and its configuration
      within this catalog.
    FacetConfigsValue: A map between facet name and its configuration within
      this catalog.
    LabelsValue: The labels associated with this resource. Each label is a
      key-value pair.

  Fields:
    assetTypeConfigs: A map between asset type name and its configuration
      within this catalog.
    createTime: Output only. The creation time of the catalog.
    facetConfigs: A map between facet name and its configuration within this
      catalog.
    labels: The labels associated with this resource. Each label is a key-
      value pair.
    name: The resource name of the catalog, in the following form:
      `projects/{project}/locations/{location}/catalogs/{catalog}`.
    updateTime: Output only. The last-modified time of the catalog.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AssetTypeConfigsValue(_messages.Message):
    r"""A map between asset type name and its configuration within this
    catalog.

    Messages:
      AdditionalProperty: An additional property for a AssetTypeConfigsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        AssetTypeConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AssetTypeConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A AssetTypeConfig attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('AssetTypeConfig', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FacetConfigsValue(_messages.Message):
    r"""A map between facet name and its configuration within this catalog.

    Messages:
      AdditionalProperty: An additional property for a FacetConfigsValue
        object.

    Fields:
      additionalProperties: Additional properties of type FacetConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FacetConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A FacetConfig attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('FacetConfig', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels associated with this resource. Each label is a key-value
    pair.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  assetTypeConfigs = _messages.MessageField('AssetTypeConfigsValue', 1)
  createTime = _messages.StringField(2)
  facetConfigs = _messages.MessageField('FacetConfigsValue', 3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  updateTime = _messages.StringField(6)


class CatalogConfig(_messages.Message):
  r"""Configures the catalog of assets.

  Fields:
    catalog: Required. Reference to a catalog to populate with assets:
      `projects/{project}/locations/{location}/catalogs/{name}`.
  """

  catalog = _messages.StringField(1)


class CatalogIndexedFieldConfig(_messages.Message):
  r"""CatalogIndexedFieldConfig configures the asset type's metadata fields
  that need to be included in catalog's search result.

  Fields:
    expression: The expression that points to the indexed field in the asset.
      If it's empty, the default is metadata.key, and the key will not be used
      as search operator.
  """

  expression = _messages.StringField(1)


class CatalogSearchRequest(_messages.Message):
  r"""Request message for CatalogsService.SearchCatalog.

  Enums:
    TypeValueValuesEnum: By default, search at segment level.

  Fields:
    pageSize: The maximum number of items to return. If unspecified, server
      will pick an appropriate default. Server may return fewer items than
      requested. A caller should only rely on response's next_page_token to
      determine if there are more realms left to be queried.
    pageToken: The next_page_token value returned from a previous Search
      request, if any.
    query: Search query.
    type: By default, search at segment level.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""By default, search at segment level.

    Values:
      SEARCH_REQUEST_TYPE_UNSPECIFIED: Unspecified type.
      SEARCH_REQUEST_TYPE_ASSET: Video-level search. That is, search over
        videos and video-level metadata.
      SEARCH_REQUEST_TYPE_SEGMENT: Segment-level search. That is, search over
        segments within videos and annotations.
    """
    SEARCH_REQUEST_TYPE_UNSPECIFIED = 0
    SEARCH_REQUEST_TYPE_ASSET = 1
    SEARCH_REQUEST_TYPE_SEGMENT = 2

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  query = _messages.StringField(3)
  type = _messages.EnumField('TypeValueValuesEnum', 4)


class CatalogSearchResponse(_messages.Message):
  r"""Response message for CatalogsService.SearchCatalog.

  Fields:
    items: Returned search results.
    nextPageToken: The next-page continuation token.
  """

  items = _messages.MessageField('SearchResultItem', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ComplexFieldAllowedValues(_messages.Message):
  r"""A ComplexFieldAllowedValues object.

  Fields:
    values: A extra_types.JsonValue attribute.
  """

  values = _messages.MessageField('extra_types.JsonValue', 1, repeated=True)


class ComplexFieldType(_messages.Message):
  r"""A ComplexFieldType object.

  Fields:
    allowedValues: If present, metadata values of ComplexFieldType must be
      within the constraints of allowedValues.
    required: If true, a value of this complex type must contain this field.
    type: Required. Type for this field. The type can be one of: - Primitive
      types ("string", "number", "bool") - Custom complex type. Format:
      "p/p/l/l/complexTypes/*" - Collections of the above - list(), dict()
  """

  allowedValues = _messages.MessageField('ComplexFieldAllowedValues', 1)
  required = _messages.BooleanField(2)
  type = _messages.StringField(3)


class ComplexType(_messages.Message):
  r"""A complex type resource. A complex type describes the schema for asset
  metadata.

  Messages:
    FieldsValue: Mapping of a field name to its type.
    LabelsValue: The labels associated with this resource. Each label is a
      key-value pair.

  Fields:
    allowUndefinedFields: Allow fields that aren't in complex type schema as
      defined in complex type fields.
    createTime: Output only. The creation time.
    fields: Mapping of a field name to its type.
    labels: The labels associated with this resource. Each label is a key-
      value pair.
    name: The resource name of the complex type, in the following form:
      `projects/{project}/locations/{location}/complexTypes/{type}`. Here
      {type} is a resource id. Detailed rules for a resource id are: 1. 1
      character minimum, 63 characters maximum 2. only contains letters,
      digits, underscore and hyphen 3. starts with a letter if length == 1,
      starts with a letter or underscore if length > 1
    updateTime: Output only. The last-modified time.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FieldsValue(_messages.Message):
    r"""Mapping of a field name to its type.

    Messages:
      AdditionalProperty: An additional property for a FieldsValue object.

    Fields:
      additionalProperties: Additional properties of type FieldsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FieldsValue object.

      Fields:
        key: Name of the additional property.
        value: A ComplexFieldType attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ComplexFieldType', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels associated with this resource. Each label is a key-value
    pair.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  allowUndefinedFields = _messages.BooleanField(1)
  createTime = _messages.StringField(2)
  fields = _messages.MessageField('FieldsValue', 3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  updateTime = _messages.StringField(6)


class DerivedAssetChange(_messages.Message):
  r"""Specifies the derived asset changes that should trigger notifications.

  Fields:
    ruleId: Required. Notifies for changes to any transformer invocations
      triggered by the derived asset rule.
  """

  ruleId = _messages.StringField(1)


class DerivedAssetConfig(_messages.Message):
  r"""A DerivedAssetConfig configures the associated AssetType to manage the
  derived assets for its Assets. The assets under the associated AssetType are
  owners of the derived assets. The derived assets are linked to the owners
  via the links in the owner assets.

  Messages:
    MetadataValue: Required. Key-value pairs for how to set the metadata in
      the derived assets. The key maps to the metadata in the derived assets.
      The value is interpreted as a literal or a path within the owner asset
      if it's prefixed by "$asset.", e.g. "$asset.file.url".

  Fields:
    metadata: Required. Key-value pairs for how to set the metadata in the
      derived assets. The key maps to the metadata in the derived assets. The
      value is interpreted as a literal or a path within the owner asset if
      it's prefixed by "$asset.", e.g. "$asset.file.url".
    owningLink: Required. The link in the owner asset.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Required. Key-value pairs for how to set the metadata in the derived
    assets. The key maps to the metadata in the derived assets. The value is
    interpreted as a literal or a path within the owner asset if it's prefixed
    by "$asset.", e.g. "$asset.file.url".

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  metadata = _messages.MessageField('MetadataValue', 1)
  owningLink = _messages.StringField(2)


class DerivedAssetStatus(_messages.Message):
  r"""Status of derived asset action."""


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class Facet(_messages.Message):
  r"""Facet represents an aggregated buckets of search result items.

  Fields:
    key: Key of facet, equivalent to the key in FacetConfig in Video AssetType
      creation.
    values: List of facet values (buckets) representing the search results.
  """

  key = _messages.StringField(1)
  values = _messages.MessageField('FacetValue', 2, repeated=True)


class FacetConfig(_messages.Message):
  r"""FacetConfig allows for configuration of faceted search.

  Fields:
    maxValues: Maximum number of facet values to return in a facet. Default is
      10.
    operators: The list of search operators to include in a facet.
  """

  maxValues = _messages.IntegerField(1)
  operators = _messages.StringField(2, repeated=True)


class FacetValue(_messages.Message):
  r"""FacetValue represents a single value result in a facet.

  Fields:
    count: Output only. Number of videos or segments corresponding to the
      facet.
    stringValue: A string attribute.
  """

  count = _messages.IntegerField(1)
  stringValue = _messages.StringField(2)


class FeatureConfigs(_messages.Message):
  r"""FeatureConfigs configure different IMS properties.

  Fields:
    speechTranscriptionConfig: Configure transcription options for speech:
      keyword.
  """

  speechTranscriptionConfig = _messages.MessageField('SpeechTranscriptionConfig', 1)


class GoogleIamV1AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('GoogleIamV1AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class GoogleIamV1AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    ignoreChildExemptions: A boolean attribute.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  ignoreChildExemptions = _messages.BooleanField(2)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 3)


class GoogleIamV1Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    bindingId: A string attribute.
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. * `principal://iam.googleapis.com/locatio
      ns/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A
      single identity in a workforce identity pool. * `principalSet://iam.goog
      leapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`:
      All workforce identities in a group. * `principalSet://iam.googleapis.co
      m/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{
      attribute_value}`: All workforce identities with a specific attribute
      value. * `principalSet://iam.googleapis.com/locations/global/workforcePo
      ols/{pool_id}/*`: All identities in a workforce identity pool. * `princi
      pal://iam.googleapis.com/projects/{project_number}/locations/global/work
      loadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
      identity in a workload identity pool. * `principalSet://iam.googleapis.c
      om/projects/{project_number}/locations/global/workloadIdentityPools/{poo
      l_id}/group/{group_id}`: A workload identity pool group. * `principalSet
      ://iam.googleapis.com/projects/{project_number}/locations/global/workloa
      dIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`:
      All identities in a workload identity pool with a certain attribute. * `
      principalSet://iam.googleapis.com/projects/{project_number}/locations/gl
      obal/workloadIdentityPools/{pool_id}/*`: All identities in a workload
      identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email
      address (plus unique identifier) representing a user that has been
      recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the user is recovered,
      this value reverts to `user:{emailid}` and the recovered user retains
      the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding. * `deleted:principal://iam.google
      apis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attr
      ibute_value}`: Deleted single identity in a workforce identity pool. For
      example, `deleted:principal://iam.googleapis.com/locations/global/workfo
      rcePools/my-pool-id/subject/my-subject-attribute-value`.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an
      overview of the IAM roles and permissions, see the [IAM
      documentation](https://cloud.google.com/iam/docs/roles-overview). For a
      list of the available pre-defined roles, see
      [here](https://cloud.google.com/iam/docs/understanding-roles).
  """

  bindingId = _messages.StringField(1)
  condition = _messages.MessageField('Expr', 2)
  members = _messages.StringField(3, repeated=True)
  role = _messages.StringField(4)


class GoogleIamV1Condition(_messages.Message):
  r"""A condition to be met.

  Enums:
    IamValueValuesEnum: Trusted attributes supplied by the IAM system.
    OpValueValuesEnum: An operator to apply the subject with.
    SysValueValuesEnum: Trusted attributes supplied by any service that owns
      resources and uses the IAM system for access control.

  Fields:
    iam: Trusted attributes supplied by the IAM system.
    op: An operator to apply the subject with.
    svc: Trusted attributes discharged by the service.
    sys: Trusted attributes supplied by any service that owns resources and
      uses the IAM system for access control.
    values: The objects of the condition.
  """

  class IamValueValuesEnum(_messages.Enum):
    r"""Trusted attributes supplied by the IAM system.

    Values:
      NO_ATTR: Default non-attribute.
      AUTHORITY: Either principal or (if present) authority selector.
      ATTRIBUTION: The principal (even if an authority selector is present),
        which must only be used for attribution, not authorization.
      SECURITY_REALM: Any of the security realms in the IAMContext
        (go/security-realms). When used with IN, the condition indicates "any
        of the request's realms match one of the given values; with NOT_IN,
        "none of the realms match any of the given values". Note that a value
        can be: - 'self:campus' (i.e., clients that are in the same campus) -
        'self:metro' (i.e., clients that are in the same metro) - 'self:cloud-
        region' (i.e., allow connections from clients that are in the same
        cloud region) - 'self:prod-region' (i.e., allow connections from
        clients that are in the same prod region) - 'guardians' (i.e., allow
        connections from its guardian realms. See go/security-realms-
        glossary#guardian for more information.) - 'cryto_core_guardians'
        (i.e., allow connections from its crypto core guardian realms. See
        go/security-realms-glossary#guardian for more information.) Crypto
        Core coverage is a super-set of Default coverage, containing
        information about coverage between higher tier data centers (e.g.,
        YAWNs). Most services should use Default coverage and only use Crypto
        Core coverage if the service is involved in greenfield turnup of new
        higher tier data centers (e.g., credential infrastructure, machine/job
        management systems, etc.). - 'self' [DEPRECATED] (i.e., allow
        connections from clients that are in the same security realm, which is
        currently but not guaranteed to be campus-sized) - a realm (e.g.,
        'campus-abc') - a realm group (e.g., 'realms-for-borg-cell-xx', see:
        go/realm-groups) A match is determined by a realm group membership
        check performed by a RealmAclRep object (go/realm-acl-howto). It is
        not permitted to grant access based on the *absence* of a realm, so
        realm conditions can only be used in a "positive" context (e.g.,
        ALLOW/IN or DENY/NOT_IN).
      APPROVER: An approver (distinct from the requester) that has authorized
        this request. When used with IN, the condition indicates that one of
        the approvers associated with the request matches the specified
        principal, or is a member of the specified group. Approvers can only
        grant additional access, and are thus only used in a strictly positive
        context (e.g. ALLOW/IN or DENY/NOT_IN).
      JUSTIFICATION_TYPE: What types of justifications have been supplied with
        this request. String values should match enum names from
        security.credentials.JustificationType, e.g. "MANUAL_STRING". It is
        not permitted to grant access based on the *absence* of a
        justification, so justification conditions can only be used in a
        "positive" context (e.g., ALLOW/IN or DENY/NOT_IN). Multiple
        justifications, e.g., a Buganizer ID and a manually-entered reason,
        are normal and supported.
      CREDENTIALS_TYPE: What type of credentials have been supplied with this
        request. String values should match enum names from
        security_loas_l2.CredentialsType - currently, only
        CREDS_TYPE_EMERGENCY is supported. It is not permitted to grant access
        based on the *absence* of a credentials type, so the conditions can
        only be used in a "positive" context (e.g., ALLOW/IN or DENY/NOT_IN).
      CREDS_ASSERTION: Properties of the credentials supplied with this
        request. See http://go/rpcsp-credential-assertions?polyglot=rpcsp-v1-0
        The conditions can only be used in a "positive" context (e.g.,
        ALLOW/IN or DENY/NOT_IN).
    """
    NO_ATTR = 0
    AUTHORITY = 1
    ATTRIBUTION = 2
    SECURITY_REALM = 3
    APPROVER = 4
    JUSTIFICATION_TYPE = 5
    CREDENTIALS_TYPE = 6
    CREDS_ASSERTION = 7

  class OpValueValuesEnum(_messages.Enum):
    r"""An operator to apply the subject with.

    Values:
      NO_OP: Default no-op.
      EQUALS: DEPRECATED. Use IN instead.
      NOT_EQUALS: DEPRECATED. Use NOT_IN instead.
      IN: The condition is true if the subject (or any element of it if it is
        a set) matches any of the supplied values.
      NOT_IN: The condition is true if the subject (or every element of it if
        it is a set) matches none of the supplied values.
      DISCHARGED: Subject is discharged
    """
    NO_OP = 0
    EQUALS = 1
    NOT_EQUALS = 2
    IN = 3
    NOT_IN = 4
    DISCHARGED = 5

  class SysValueValuesEnum(_messages.Enum):
    r"""Trusted attributes supplied by any service that owns resources and
    uses the IAM system for access control.

    Values:
      NO_ATTR: Default non-attribute type
      REGION: Region of the resource
      SERVICE: Service name
      NAME: Resource name
      IP: IP address of the caller
    """
    NO_ATTR = 0
    REGION = 1
    SERVICE = 2
    NAME = 3
    IP = 4

  iam = _messages.EnumField('IamValueValuesEnum', 1)
  op = _messages.EnumField('OpValueValuesEnum', 2)
  svc = _messages.StringField(3)
  sys = _messages.EnumField('SysValueValuesEnum', 4)
  values = _messages.StringField(5, repeated=True)


class GoogleIamV1LogConfig(_messages.Message):
  r"""Specifies what kind of log the caller must write

  Fields:
    cloudAudit: Cloud audit options.
    counter: Counter options.
    dataAccess: Data access options.
  """

  cloudAudit = _messages.MessageField('GoogleIamV1LogConfigCloudAuditOptions', 1)
  counter = _messages.MessageField('GoogleIamV1LogConfigCounterOptions', 2)
  dataAccess = _messages.MessageField('GoogleIamV1LogConfigDataAccessOptions', 3)


class GoogleIamV1LogConfigCloudAuditOptions(_messages.Message):
  r"""Write a Cloud Audit log

  Enums:
    LogNameValueValuesEnum: The log_name to populate in the Cloud Audit
      Record.
    PermissionTypeValueValuesEnum: The type associated with the permission.

  Fields:
    authorizationLoggingOptions: Information used by the Cloud Audit Logging
      pipeline. Will be deprecated once the migration to PermissionType is
      complete (b/201806118).
    logName: The log_name to populate in the Cloud Audit Record.
    permissionType: The type associated with the permission.
  """

  class LogNameValueValuesEnum(_messages.Enum):
    r"""The log_name to populate in the Cloud Audit Record.

    Values:
      UNSPECIFIED_LOG_NAME: Default. Should not be used.
      ADMIN_ACTIVITY: Corresponds to "cloudaudit.googleapis.com/activity"
      DATA_ACCESS: Corresponds to "cloudaudit.googleapis.com/data_access"
    """
    UNSPECIFIED_LOG_NAME = 0
    ADMIN_ACTIVITY = 1
    DATA_ACCESS = 2

  class PermissionTypeValueValuesEnum(_messages.Enum):
    r"""The type associated with the permission.

    Values:
      PERMISSION_TYPE_UNSPECIFIED: Default. Should not be used.
      ADMIN_READ: Permissions that gate reading resource configuration or
        metadata.
      ADMIN_WRITE: Permissions that gate modification of resource
        configuration or metadata.
      DATA_READ: Permissions that gate reading user-provided data.
      DATA_WRITE: Permissions that gate writing user-provided data.
    """
    PERMISSION_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    ADMIN_WRITE = 2
    DATA_READ = 3
    DATA_WRITE = 4

  authorizationLoggingOptions = _messages.MessageField('AuthorizationLoggingOptions', 1)
  logName = _messages.EnumField('LogNameValueValuesEnum', 2)
  permissionType = _messages.EnumField('PermissionTypeValueValuesEnum', 3)


class GoogleIamV1LogConfigCounterOptions(_messages.Message):
  r"""Increment a streamz counter with the specified metric and field names.
  Metric names should start with a '/', generally be lowercase-only, and end
  in "_count". Field names should not contain an initial slash. The actual
  exported metric names will have "/iam/policy" prepended. Field names
  correspond to IAM request parameters and field values are their respective
  values. Supported field names: - "authority", which is "[token]" if
  IAMContext.token is present, otherwise the value of
  IAMContext.authority_selector if present, and otherwise a representation of
  IAMContext.principal; or - "iam_principal", a representation of
  IAMContext.principal even if a token or authority selector is present; or -
  "" (empty string), resulting in a counter with no fields. Examples: counter
  { metric: "/debug_access_count" field: "iam_principal" } ==> increment
  counter /iam/policy/debug_access_count {iam_principal=[value of
  IAMContext.principal]}

  Fields:
    customFields: Custom fields.
    field: The field value to attribute.
    metric: The metric to update.
  """

  customFields = _messages.MessageField('GoogleIamV1LogConfigCounterOptionsCustomField', 1, repeated=True)
  field = _messages.StringField(2)
  metric = _messages.StringField(3)


class GoogleIamV1LogConfigCounterOptionsCustomField(_messages.Message):
  r"""Custom fields. These can be used to create a counter with arbitrary
  field/value pairs. See: go/rpcsp-custom-fields.

  Fields:
    name: Name is the field name.
    value: Value is the field value. It is important that in contrast to the
      CounterOptions.field, the value here is a constant that is not derived
      from the IAMContext.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class GoogleIamV1LogConfigDataAccessOptions(_messages.Message):
  r"""Write a Data Access (Gin) log

  Enums:
    LogModeValueValuesEnum:

  Fields:
    isDirectAuth: Indicates that access was granted by a regular grant policy
    logMode: A LogModeValueValuesEnum attribute.
  """

  class LogModeValueValuesEnum(_messages.Enum):
    r"""LogModeValueValuesEnum enum type.

    Values:
      LOG_MODE_UNSPECIFIED: Client is not required to write a partial Gin log
        immediately after the authorization check. If client chooses to write
        one and it fails, client may either fail open (allow the operation to
        continue) or fail closed (handle as a DENY outcome).
      LOG_FAIL_CLOSED: The application's operation in the context of which
        this authorization check is being made may only be performed if it is
        successfully logged to Gin. For instance, the authorization library
        may satisfy this obligation by emitting a partial log entry at
        authorization check time and only returning ALLOW to the application
        if it succeeds. If a matching Rule has this directive, but the client
        has not indicated that it will honor such requirements, then the IAM
        check will result in authorization failure by setting
        CheckPolicyResponse.success=false.
    """
    LOG_MODE_UNSPECIFIED = 0
    LOG_FAIL_CLOSED = 1

  isDirectAuth = _messages.BooleanField(1)
  logMode = _messages.EnumField('LogModeValueValuesEnum', 2)


class GoogleIamV1Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    rules: If more than one rule is specified, the rules are applied in the
      following manner: - All matching LOG rules are always applied. - If any
      DENY/DENY_WITH_LOG rule matches, permission is denied. Logging will be
      applied if one or more matching rule requires logging. - Otherwise, if
      any ALLOW/ALLOW_WITH_LOG rule matches, permission is granted. Logging
      will be applied if one or more matching rule requires logging. -
      Otherwise, if no rule applies, permission is denied.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('GoogleIamV1AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('GoogleIamV1Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  rules = _messages.MessageField('GoogleIamV1Rule', 4, repeated=True)
  version = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class GoogleIamV1Rule(_messages.Message):
  r"""A rule to be applied in a Policy.

  Enums:
    ActionValueValuesEnum: Required

  Fields:
    action: Required
    conditions: Additional restrictions that must be met. All conditions must
      pass for the rule to match.
    description: Human-readable description of the rule.
    in_: If one or more 'in' clauses are specified, the rule matches if the
      PRINCIPAL/AUTHORITY_SELECTOR is in at least one of these entries.
    logConfig: The config returned to callers of CheckPolicy for any entries
      that match the LOG action.
    notIn: If one or more 'not_in' clauses are specified, the rule matches if
      the PRINCIPAL/AUTHORITY_SELECTOR is in none of the entries. The format
      for in and not_in entries can be found at in the Local IAM documentation
      (see go/local-iam#features).
    permissions: A permission is a string of form `..` (e.g.,
      'storage.buckets.list'). A value of '*' matches all permissions, and a
      verb part of '*' (e.g., 'storage.buckets.*') matches all verbs.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required

    Values:
      NO_ACTION: Default no action.
      ALLOW: Matching 'Entries' grant access.
      ALLOW_WITH_LOG: Matching 'Entries' grant access and the caller promises
        to log the request per the returned log_configs.
      DENY: Matching 'Entries' deny access.
      DENY_WITH_LOG: Matching 'Entries' deny access and the caller promises to
        log the request per the returned log_configs.
      LOG: Matching 'Entries' tell IAM.Check callers to generate logs.
    """
    NO_ACTION = 0
    ALLOW = 1
    ALLOW_WITH_LOG = 2
    DENY = 3
    DENY_WITH_LOG = 4
    LOG = 5

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  conditions = _messages.MessageField('GoogleIamV1Condition', 2, repeated=True)
  description = _messages.StringField(3)
  in_ = _messages.StringField(4, repeated=True)
  logConfig = _messages.MessageField('GoogleIamV1LogConfig', 5, repeated=True)
  notIn = _messages.StringField(6, repeated=True)
  permissions = _messages.StringField(7, repeated=True)


class GoogleIamV1SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('GoogleIamV1Policy', 1)
  updateMask = _messages.StringField(2)


class GoogleIamV1TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class GoogleIamV1TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class IndexedFieldConfig(_messages.Message):
  r"""A IndexedFieldConfig object.

  Enums:
    StateValueValuesEnum: Output only. State shows whether IndexedFieldConfig
      is ready to be used.
    TokenizationValueValuesEnum: String tokenization mode.

  Fields:
    expression: Expression to evaluate in the context of an indexed property
      defaults to the property name.
    fullTextSearch: Enable full text search - strings only. DEPRECATED, use
      tokenization
    state: Output only. State shows whether IndexedFieldConfig is ready to be
      used.
    tokenization: String tokenization mode.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State shows whether IndexedFieldConfig is ready to be
    used.

    Values:
      STATE_UNSPECIFIED: STATE_UNSPECIFIED is not an expected state.
      UPDATING: UPDATING means the config is still being updated and not
        filterable.
      ACTIVE: ACTIVE means the config is filterable.
    """
    STATE_UNSPECIFIED = 0
    UPDATING = 1
    ACTIVE = 2

  class TokenizationValueValuesEnum(_messages.Enum):
    r"""String tokenization mode.

    Values:
      TOKENIZATION_UNSPECIFIED: No tokenization - only exact string matches
        are supported.
      WORDS: Use word tokens.
      SUBSTRINGS_NGRAM_3: Uses 3-ngram tokens supporting efficient substring
        searches.
    """
    TOKENIZATION_UNSPECIFIED = 0
    WORDS = 1
    SUBSTRINGS_NGRAM_3 = 2

  expression = _messages.StringField(1)
  fullTextSearch = _messages.BooleanField(2)
  state = _messages.EnumField('StateValueValuesEnum', 3)
  tokenization = _messages.EnumField('TokenizationValueValuesEnum', 4)


class Input(_messages.Message):
  r"""Input describes an input of a transformer. The type could be one of: -
  Primitive types ("string", "number", "bool") - Custom complex type. Format:
  "p/p/l/l/complexTypes/*" - Collections of the above - list(), dict()

  Fields:
    required: A required field is for inputs which must be specified in the
      TransformationConfig rule.
    type: A string attribute.
  """

  required = _messages.BooleanField(1)
  type = _messages.StringField(2)


class LinkConfig(_messages.Message):
  r"""A LinkConfig object.

  Fields:
    assetType: Required. Reference to the asset type name of the linked asset,
      in the following form:
      `projects/{project}/locations/{location}/assetTypes/{name}`.
    owner: Output only. The owner of the link, if it's updated by the system.
    required: If true, this asset link is required during asset creation.
  """

  assetType = _messages.StringField(1)
  owner = _messages.StringField(2)
  required = _messages.BooleanField(3)


class LinkInfo(_messages.Message):
  r"""Additional link information.

  Fields:
    owner: Output only. The owner of the link, if it's updated by the system.
  """

  owner = _messages.StringField(1)


class LinkSet(_messages.Message):
  r"""A LinkSet object.

  Messages:
    LinksValue: Mapping of a qualifier to an asset link. Asset Link Format:
      "p/p/l/l/assets/"

  Fields:
    links: Mapping of a qualifier to an asset link. Asset Link Format:
      "p/p/l/l/assets/"
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LinksValue(_messages.Message):
    r"""Mapping of a qualifier to an asset link. Asset Link Format:
    "p/p/l/l/assets/"

    Messages:
      AdditionalProperty: An additional property for a LinksValue object.

    Fields:
      additionalProperties: Additional properties of type LinksValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LinksValue object.

      Fields:
        key: Name of the additional property.
        value: A Asset attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Asset', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  links = _messages.MessageField('LinksValue', 1)


class LinkSetConfig(_messages.Message):
  r"""A LinkSetConfig object.

  Fields:
    assetType: Required. Reference to the asset type name for the type of the
      assets in this set, in the following form:
      `projects/{project}/locations/{location}/assetTypes/{name}`.
  """

  assetType = _messages.StringField(1)


class ListActionsResponse(_messages.Message):
  r"""Response message for ActionsService.ListActions.

  Fields:
    actions: The list of actions.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  actions = _messages.MessageField('Action', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListAnnotationSetsResponse(_messages.Message):
  r"""Response message for AnnotationSetsService.ListAnnotationSets.

  Fields:
    annotationSets: The annotationSets from the specified asset.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  annotationSets = _messages.MessageField('AnnotationSet', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListAnnotationsResponse(_messages.Message):
  r"""Response message for AnnotationsService.ListAnnotations.

  Fields:
    annotations: The annotations from the specified asset.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  annotations = _messages.MessageField('Annotation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListAssetTypesResponse(_messages.Message):
  r"""Response message for AssetTypesService.ListAssetTypes.

  Fields:
    assetTypes: The list of asset types.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: Locations that could not be reached.
  """

  assetTypes = _messages.MessageField('AssetType', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListAssetsResponse(_messages.Message):
  r"""Response message for AssetsService.ListAssets.

  Fields:
    assets: The list of assets.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: Locations that could not be reached.
  """

  assets = _messages.MessageField('Asset', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListCatalogsResponse(_messages.Message):
  r"""Response message for CatalogsService.ListCatalogs.

  Fields:
    catalogs: The list of catalogs.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: Locations that could not be reached.
  """

  catalogs = _messages.MessageField('Catalog', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListComplexTypesResponse(_messages.Message):
  r"""Response message for ComplexTypesService.ListComplexTypes.

  Fields:
    complexTypes: The list of complex types.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: Locations that could not be reached.
  """

  complexTypes = _messages.MessageField('ComplexType', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListModulesResponse(_messages.Message):
  r"""Response message for ModulesService.ListModules.

  Fields:
    modules: The list of modules.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: Locations that could not be reached.
  """

  modules = _messages.MessageField('Module', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
    unreachable: Unordered list. Unreachable resources. Populated when the
      request sets `ListOperationsRequest.return_partial_success` and reads
      across collections e.g. when attempting to list all resources across all
      supported locations.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListRulesResponse(_messages.Message):
  r"""Response message for RulesService.ListRules.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    rules: The rules from the specified assetType.
  """

  nextPageToken = _messages.StringField(1)
  rules = _messages.MessageField('Rule', 2, repeated=True)


class ListTransformersResponse(_messages.Message):
  r"""Response message for TransformersService.ListTransformers.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    transformers: The list of transformers.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  transformers = _messages.MessageField('Transformer', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class MediaassetProjectsLocationsAssetTypesAssetsActionsCancelRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesAssetsActionsCancelRequest
  object.

  Fields:
    cancelActionRequest: A CancelActionRequest resource to be passed as the
      request body.
    name: Required. The name of the action to cancel. Format: `projects/{proje
      ct}/locations/{location}/assetTypes/{type}/assets/{asset}/actions/{actio
      n}`
  """

  cancelActionRequest = _messages.MessageField('CancelActionRequest', 1)
  name = _messages.StringField(2, required=True)


class MediaassetProjectsLocationsAssetTypesAssetsActionsGetRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesAssetsActionsGetRequest object.

  Fields:
    name: Required. The name of the action to retrieve, in the following form:
      `projects/{project}/locations/{location}/assetTypes/{type}/assets/{asset
      }/actions/{action}`.
  """

  name = _messages.StringField(1, required=True)


class MediaassetProjectsLocationsAssetTypesAssetsActionsListRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesAssetsActionsListRequest object.

  Fields:
    filter: The filter to apply to list results.
    orderBy: Specifies the ordering of results following syntax at
      https://cloud.google.com/apis/design/design_patterns#sorting_order.
    pageSize: The maximum number of items to return. If unspecified, server
      will pick an appropriate default. Server may return fewer items than
      requested. A caller should only rely on response's next_page_token to
      determine if there are more realms left to be queried.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: Required. The parent resource name, in the following form: `projec
      ts/{project}/locations/{location}/assetTypes/{type}/assets/{asset}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class MediaassetProjectsLocationsAssetTypesAssetsActionsTriggerRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesAssetsActionsTriggerRequest
  object.

  Fields:
    name: Required. The name of the action to Trigger. Format: `projects/{proj
      ect}/locations/{location}/assetTypes/{type}/assets/{asset}/actions/{acti
      on}`
    triggerActionRequest: A TriggerActionRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  triggerActionRequest = _messages.MessageField('TriggerActionRequest', 2)


class MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsCreateRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsCr
  eateRequest object.

  Fields:
    annotation: A Annotation resource to be passed as the request body.
    annotationId: Required. The ID of the annotation resource to be created.
    parent: Required. The parent resource where this Annotation will be
      created. Format: `projects/{project}/locations/{location}/assetTypes/{as
      set_type}/assets/{asset}/annotationSets/{annotation_set}`
  """

  annotation = _messages.MessageField('Annotation', 1)
  annotationId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsDeleteRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsDe
  leteRequest object.

  Fields:
    etag: The current etag of the annotation. If an etag is provided and does
      not match the current etag of the annotation, deletion will be blocked
      and a FAILED_PRECONDITION error will be returned.
    name: Required. The name of the annotation to delete. Format: `projects/{p
      roject}/locations/{location}/assetTypes/{asset_type}/assets/{asset}/anno
      tationSets/{annotation_set}/annotations/{annotation}`
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsGetIamPolicyRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsGe
  tIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsGetRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsGe
  tRequest object.

  Fields:
    name: Required. The name of the Annotation to retrieve. Format: `projects/
      {project}/locations/{location}/assetTypes/{asset_type}/assets/{asset}/an
      notationSets/{annotation_set}/annotations/{annotation}`
  """

  name = _messages.StringField(1, required=True)


class MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsListRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsLi
  stRequest object.

  Fields:
    filter: The filter to apply to list results. Valid field expressions are
      defined in assetType.annotationSetConfig.indexedFieldConfig. Format:
      https://cloud.google.com/logging/docs/view/advanced-queries
    pageSize: The maximum number of annotations to return. The service may
      return fewer than this value. If unspecified, at most 50 annotations
      will be returned. The maximum value is 100; values above 100 will be
      coerced to 100.
    pageToken: A page token, received from a previous `ListAnnotations` call.
      Provide this to retrieve the subsequent page. When paginating, all other
      parameters provided to `ListAnnotations` must match the call that
      provided the page token.
    parent: Required. The parent, which owns this collection of annotations.
      Format: `projects/{project}/locations/{location}/assetTypes/{asset_type}
      /assets/{asset}/annotationSets/{annotation_set}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsPatchRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsPa
  tchRequest object.

  Fields:
    annotation: A Annotation resource to be passed as the request body.
    name: Output only. An automatically-generated resource name of the
      annotation `projects/{project}/locations/{location}/assetTypes/{asset_ty
      pe}/assets/{asset}/annotationSets/{annotation_set}/annotations/{annotati
      on}`.
    updateMask: Required. Comma-separated list of fields to be updated.
  """

  annotation = _messages.MessageField('Annotation', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsSetIamPolicyRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsSe
  tIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsTestIamPermissionsRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsTe
  stIamPermissionsRequest object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsGetIamPolicyRequest(_messages.Message):
  r"""A
  MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsGetRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsGetRequest
  object.

  Fields:
    name: Required. The name of the AnnotationSet to retrieve. Format: `projec
      ts/{project}/locations/{location}/assetTypes/{asset_type}/assets/{asset}
      /annotationSets/{annotation_set}`
  """

  name = _messages.StringField(1, required=True)


class MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsListRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsListRequest
  object.

  Fields:
    filter: The filter to apply to list results.
    pageSize: The maximum number of annotationSets to return. The service may
      return fewer than this value. If unspecified, at most 50 annotationSets
      will be returned. The maximum value is 100; values above 100 will be
      coerced to 100.
    pageToken: A page token, received from a previous `ListAnnotationSets`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListAnnotationSets` must match the call
      that provided the page token.
    parent: Required. The name of the asset that owns this collection of
      annotationSets. Format: `projects/{project}/locations/{location}/assetTy
      pes/{asset_type}/assets/{asset}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsSetIamPolicyRequest(_messages.Message):
  r"""A
  MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsSetIamPolicyRequest
  object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsTestIamPermissionsRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsTestIamPermis
  sionsRequest object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class MediaassetProjectsLocationsAssetTypesAssetsCreateRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesAssetsCreateRequest object.

  Fields:
    asset: A Asset resource to be passed as the request body.
    assetId: Required. The ID of the asset resource to be created.
    parent: Required. The parent resource name, in the following form:
      `projects/{project}/locations/{location}/assetTypes/{type}`.
  """

  asset = _messages.MessageField('Asset', 1)
  assetId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class MediaassetProjectsLocationsAssetTypesAssetsDeleteRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesAssetsDeleteRequest object.

  Fields:
    etag: The current etag of the asset. If an etag is provided and does not
      match the current etag of the asset, deletion will be blocked and a
      FAILED_PRECONDITION error will be returned.
    name: Required. The name of the asset to delete, in the following form: `p
      rojects/{project}/locations/{location}/assetTypes/{type}/assets/{asset}`
      .
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class MediaassetProjectsLocationsAssetTypesAssetsGetIamPolicyRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesAssetsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class MediaassetProjectsLocationsAssetTypesAssetsGetRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesAssetsGetRequest object.

  Fields:
    name: Required. The name of the asset to retrieve, in the following form:
      `projects/{project}/locations/{location}/assetTypes/{type}/assets/{asset
      }`.
    readMask: Extra fields to be poplulated as part of the asset resource in
      the response. Currently, this only supports populating asset metadata
      (no wildcards and no contents of the entire asset).
  """

  name = _messages.StringField(1, required=True)
  readMask = _messages.StringField(2)


class MediaassetProjectsLocationsAssetTypesAssetsListRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesAssetsListRequest object.

  Fields:
    filter: The filter to apply to list results. Valid field expressions are
      defined in assetType.indexedFieldConfig. Format:
      https://cloud.google.com/logging/docs/view/advanced-queries
    pageSize: The maximum number of items to return. If unspecified, server
      will pick an appropriate default. Server may return fewer items than
      requested. A caller should only rely on response's next_page_token to
      determine if there are more realms left to be queried.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: Required. The parent resource name, in the following form:
      `projects/{project}/locations/{location}/assetTypes/{type}`.
    readMask: Extra fields to be poplulated as part of the asset resource in
      the response. Currently, this only supports populating asset metadata
      (no wildcards and no contents of the entire asset).
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  readMask = _messages.StringField(5)


class MediaassetProjectsLocationsAssetTypesAssetsPatchRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesAssetsPatchRequest object.

  Fields:
    asset: A Asset resource to be passed as the request body.
    name: The resource name of the asset, in the following form: `projects/{pr
      oject}/locations/{location}/assetTypes/{type}/assets/{asset}`. Here
      {asset} is a resource id. Detailed rules for a resource id are: 1. 1
      character minimum, 63 characters maximum 2. only contains letters,
      digits, underscore and hyphen 3. starts with a letter if length == 1,
      starts with a letter or underscore if length > 1
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Comma-separated list of fields is used to specify
      the fields to be overwritten in the Asset resource by the update. The
      fields specified in the update_mask are relative to the resource, not
      the full request. A field will be overwritten if it is in the mask. If
      the user does not provide a mask then all fields will be overwritten.
  """

  asset = _messages.MessageField('Asset', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class MediaassetProjectsLocationsAssetTypesAssetsSetIamPolicyRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesAssetsSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class MediaassetProjectsLocationsAssetTypesAssetsTestIamPermissionsRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesAssetsTestIamPermissionsRequest
  object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class MediaassetProjectsLocationsAssetTypesCreateRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesCreateRequest object.

  Fields:
    assetType: A AssetType resource to be passed as the request body.
    assetTypeId: Required. The ID of the asset type resource to be created.
    parent: Required. The parent resource name, in the following form:
      `projects/{project}/locations/{location}`.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  assetType = _messages.MessageField('AssetType', 1)
  assetTypeId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class MediaassetProjectsLocationsAssetTypesDeleteRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesDeleteRequest object.

  Fields:
    name: Required. The name of the asset type to delete, in the following
      form: `projects/{project}/locations/{location}/assetTypes/{type}`.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class MediaassetProjectsLocationsAssetTypesGetIamPolicyRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class MediaassetProjectsLocationsAssetTypesGetRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesGetRequest object.

  Fields:
    name: Required. The name of the asset type to retrieve, in the following
      form: `projects/{project}/locations/{location}/assetTypes/{type}`.
  """

  name = _messages.StringField(1, required=True)


class MediaassetProjectsLocationsAssetTypesListRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesListRequest object.

  Fields:
    filter: The filter to apply to list results.
    orderBy: Specifies the ordering of results following syntax at
      https://cloud.google.com/apis/design/design_patterns#sorting_order.
    pageSize: The maximum number of items to return. If unspecified, server
      will pick an appropriate default. Server may return fewer items than
      requested. A caller should only rely on response's next_page_token to
      determine if there are more realms left to be queried.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: Required. The parent resource name, in the following form:
      `projects/{project}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class MediaassetProjectsLocationsAssetTypesPatchRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesPatchRequest object.

  Fields:
    assetType: A AssetType resource to be passed as the request body.
    name: The resource name of the asset type, in the following form:
      `projects/{project}/locations/{location}/assetTypes/{type}`. Here {type}
      is a resource id. Detailed rules for a resource id are: 1. 1 character
      minimum, 63 characters maximum 2. only contains letters, digits,
      underscore and hyphen 3. starts with a letter if length == 1, starts
      with a letter or underscore if length > 1
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Comma-separated list of fields is used to specify
      the fields to be overwritten in the AssetType resource by the update.
      The fields specified in the update_mask are relative to the resource,
      not the full request. A field will be overwritten if it is in the mask.
      If the user does not provide a mask then all fields will be overwritten.
  """

  assetType = _messages.MessageField('AssetType', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class MediaassetProjectsLocationsAssetTypesRulesCreateRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesRulesCreateRequest object.

  Fields:
    parent: Required. The parent resource where this Rule will be created.
      Format: `projects/{project}/locations/{location}/assetTypes/{type}`
    rule: A Rule resource to be passed as the request body.
    ruleId: Required. The ID to use for the Rule, which will become the final
      component of the rule's resource name. This value should be 4-63
      characters, and valid characters are /a-z-/.
  """

  parent = _messages.StringField(1, required=True)
  rule = _messages.MessageField('Rule', 2)
  ruleId = _messages.StringField(3)


class MediaassetProjectsLocationsAssetTypesRulesDeleteRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesRulesDeleteRequest object.

  Fields:
    etag: The current etag of the rule. If an etag is provided and does not
      match the current etag of the rule, deletion will be blocked and a
      FAILED_PRECONDITION error will be returned.
    name: Required. The name of the rule to delete. Format:
      `projects/{project}/locations/{location}/assetTypes/{type}/rules/{rule}`
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class MediaassetProjectsLocationsAssetTypesRulesGetRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesRulesGetRequest object.

  Fields:
    name: Required. The name of the Rule to retrieve. Format:
      `projects/{project}/locations/{location}/assetTypes/{type}/rules/{rule}`
  """

  name = _messages.StringField(1, required=True)


class MediaassetProjectsLocationsAssetTypesRulesListRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesRulesListRequest object.

  Fields:
    pageSize: The maximum number of rules to return. The service may return
      fewer than this value. If unspecified, at most 50 books will be
      returned. The maximum value is 100; values above 100 will be coerced to
      100.
    pageToken: A page token, received from a previous `ListRules` call.
      Provide this to retrieve the subsequent page. When paginating, all other
      parameters provided to `ListRules` must match the call that provided the
      page token.
    parent: Required. The parent, which owns this collection of rules. Format:
      `projects/{project}/locations/{location}/assetTypes/{type}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class MediaassetProjectsLocationsAssetTypesRulesPatchRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesRulesPatchRequest object.

  Fields:
    name: A user-specified resource name of the rule
      `projects/{project}/locations/{location}/assetTypes/{type}/rules/{rule}`
      . Here {rule} is a resource id. Detailed rules for a resource id are: 1.
      1 character minimum, 63 characters maximum 2. only contains letters,
      digits, underscore and hyphen 3. starts with a letter if length == 1,
      starts with a letter or underscore if length > 1
    rule: A Rule resource to be passed as the request body.
    updateMask: Required. Comma-separated list of fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  rule = _messages.MessageField('Rule', 2)
  updateMask = _messages.StringField(3)


class MediaassetProjectsLocationsAssetTypesSearchRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesSearchRequest object.

  Fields:
    name: Required. The asset type resource name, in the following form:
      `projects/{project}/locations/{location}/assetTypes/{type}`.
    searchAssetTypeRequest: A SearchAssetTypeRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  searchAssetTypeRequest = _messages.MessageField('SearchAssetTypeRequest', 2)


class MediaassetProjectsLocationsAssetTypesSetIamPolicyRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class MediaassetProjectsLocationsAssetTypesTestIamPermissionsRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsAssetTypesTestIamPermissionsRequest object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class MediaassetProjectsLocationsCatalogsCreateRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsCatalogsCreateRequest object.

  Fields:
    catalog: A Catalog resource to be passed as the request body.
    catalogId: The ID of the catalog resource to be created.
    parent: Required. The parent resource name, in the following form:
      `projects/{project}/locations/{location}`.
  """

  catalog = _messages.MessageField('Catalog', 1)
  catalogId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class MediaassetProjectsLocationsCatalogsDeleteRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsCatalogsDeleteRequest object.

  Fields:
    name: Required. The name of the catalog to delete, in the following form:
      `projects/{project}/locations/{location}/catalogs/{catalog}`.
  """

  name = _messages.StringField(1, required=True)


class MediaassetProjectsLocationsCatalogsGetRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsCatalogsGetRequest object.

  Fields:
    name: Required. The name of the catalog to retrieve, in the following
      form: `projects/{project}/locations/{location}/catalogs/{catalog}`.
  """

  name = _messages.StringField(1, required=True)


class MediaassetProjectsLocationsCatalogsListRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsCatalogsListRequest object.

  Fields:
    filter: The filter to apply to list results.
    orderBy: Specifies the ordering of results following syntax at
      https://cloud.google.com/apis/design/design_patterns#sorting_order.
    pageSize: The maximum number of items to return. If unspecified, server
      will pick an appropriate default. Server may return fewer items than
      requested. A caller should only rely on response's next_page_token to
      determine if there are more realms left to be queried.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: Required. The parent resource name, in the following form:
      `projects/{project}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class MediaassetProjectsLocationsCatalogsPatchRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsCatalogsPatchRequest object.

  Fields:
    catalog: A Catalog resource to be passed as the request body.
    name: The resource name of the catalog, in the following form:
      `projects/{project}/locations/{location}/catalogs/{catalog}`.
    updateMask: update_mask is a comma separated fields used to specify
      changes in catalog. The fields specified in the update_mask are relative
      to the resource, not the full request. A field will be overwritten if it
      is in the mask. If the user does not provide a mask then all fields will
      be overwritten.
  """

  catalog = _messages.MessageField('Catalog', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class MediaassetProjectsLocationsCatalogsSearchRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsCatalogsSearchRequest object.

  Fields:
    catalogSearchRequest: A CatalogSearchRequest resource to be passed as the
      request body.
    name: Required. The catalog resource name, in the following form:
      `projects/{project}/locations/{location}/catalogs/{catalog}`.
  """

  catalogSearchRequest = _messages.MessageField('CatalogSearchRequest', 1)
  name = _messages.StringField(2, required=True)


class MediaassetProjectsLocationsComplexTypesCreateRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsComplexTypesCreateRequest object.

  Fields:
    complexType: A ComplexType resource to be passed as the request body.
    complexTypeId: Required. The ID of the complex type resource to be
      created.
    parent: Required. The parent resource name, in the following form:
      `projects/{project}/locations/{location}`.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  complexType = _messages.MessageField('ComplexType', 1)
  complexTypeId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class MediaassetProjectsLocationsComplexTypesDeleteRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsComplexTypesDeleteRequest object.

  Fields:
    name: Required. The name of the complex type to delete, in the following
      form: `projects/{project}/locations/{location}/complexTypes/{type}`.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class MediaassetProjectsLocationsComplexTypesGetIamPolicyRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsComplexTypesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class MediaassetProjectsLocationsComplexTypesGetRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsComplexTypesGetRequest object.

  Fields:
    name: Required. The name of the complex type to retrieve, in the following
      form: `projects/{project}/locations/{location}/complexTypes/{type}`.
  """

  name = _messages.StringField(1, required=True)


class MediaassetProjectsLocationsComplexTypesListRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsComplexTypesListRequest object.

  Fields:
    filter: The filter to apply to list results.
    orderBy: Specifies the ordering of results following syntax at
      https://cloud.google.com/apis/design/design_patterns#sorting_order.
    pageSize: The maximum number of items to return. If unspecified, server
      will pick an appropriate default. Server may return fewer items than
      requested. A caller should only rely on response's next_page_token to
      determine if there are more realms left to be queried
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: Required. The parent resource name, in the following form:
      `projects/{project}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class MediaassetProjectsLocationsComplexTypesPatchRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsComplexTypesPatchRequest object.

  Fields:
    complexType: A ComplexType resource to be passed as the request body.
    name: The resource name of the complex type, in the following form:
      `projects/{project}/locations/{location}/complexTypes/{type}`. Here
      {type} is a resource id. Detailed rules for a resource id are: 1. 1
      character minimum, 63 characters maximum 2. only contains letters,
      digits, underscore and hyphen 3. starts with a letter if length == 1,
      starts with a letter or underscore if length > 1
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Comma-separated list of fields is used to specify
      the fields to be overwritten in the ComplexType resource by the update.
      The fields specified in the update_mask are relative to the resource,
      not the full request. A field will be overwritten if it is in the mask.
      If the user does not provide a mask then all fields will be overwritten.
  """

  complexType = _messages.MessageField('ComplexType', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class MediaassetProjectsLocationsComplexTypesSetIamPolicyRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsComplexTypesSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class MediaassetProjectsLocationsComplexTypesTestIamPermissionsRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsComplexTypesTestIamPermissionsRequest
  object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class MediaassetProjectsLocationsGetRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class MediaassetProjectsLocationsListRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsListRequest object.

  Fields:
    extraLocationTypes: Optional. A list of extra location types that should
      be used as conditions for controlling the visibility of the locations.
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    includeUnrevealedLocations: If true, the returned list will include
      locations which are not yet revealed.
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  extraLocationTypes = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  includeUnrevealedLocations = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  pageSize = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(6)


class MediaassetProjectsLocationsModulesCreateRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsModulesCreateRequest object.

  Fields:
    module: A Module resource to be passed as the request body.
    moduleId: The ID of the module resource to be created.
    parent: Required. The parent resource name, in the following form:
      `projects/{project}/locations/{location}`.
  """

  module = _messages.MessageField('Module', 1)
  moduleId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class MediaassetProjectsLocationsModulesDeleteRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsModulesDeleteRequest object.

  Fields:
    name: Required. The name of the module to delete, in the following form:
      `projects/{project}/locations/{location}/modules/{module}`.
  """

  name = _messages.StringField(1, required=True)


class MediaassetProjectsLocationsModulesGetRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsModulesGetRequest object.

  Fields:
    name: Required. The name of the module to retrieve, in the following form:
      `projects/{project}/locations/{location}/modules/{module}`.
  """

  name = _messages.StringField(1, required=True)


class MediaassetProjectsLocationsModulesListRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsModulesListRequest object.

  Fields:
    filter: The filter to apply to list results.
    orderBy: Specifies the ordering of results following syntax at
      https://cloud.google.com/apis/design/design_patterns#sorting_order.
    pageSize: The maximum number of items to return. If unspecified, server
      will pick an appropriate default. Server may return fewer items than
      requested. A caller should only rely on response's next_page_token to
      determine if there are more realms left to be queried.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: Required. The parent resource name, in the following form:
      `projects/{project}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class MediaassetProjectsLocationsModulesPatchRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsModulesPatchRequest object.

  Fields:
    module: A Module resource to be passed as the request body.
    name: The resource name of the module, in the following form:
      `projects/{project}/locations/{location}/module/{module}`.
    updateMask: Field mask is used to specify the fields to be overwritten in
      the Module resource by the update. The fields specified in the
      update_mask are relative to the resource, not the full request. A field
      will be overwritten if it is in the mask. If the user does not provide a
      mask then all fields will be overwritten.
  """

  module = _messages.MessageField('Module', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class MediaassetProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class MediaassetProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class MediaassetProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class MediaassetProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
    returnPartialSuccess: When set to `true`, operations that are reachable
      are returned as normal, and those that are unreachable are returned in
      the [ListOperationsResponse.unreachable] field. This can only be `true`
      when reading across collections e.g. when `parent` is set to
      `"projects/example/locations/-"`. This field is not by default supported
      and will result in an `UNIMPLEMENTED` error if set unless explicitly
      documented otherwise in service or product specific documentation.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  returnPartialSuccess = _messages.BooleanField(5)


class MediaassetProjectsLocationsTransformersCreateRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsTransformersCreateRequest object.

  Fields:
    parent: Required. The parent resource name, in the following form:
      `projects/{project}/locations/{location}`.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    transformer: A Transformer resource to be passed as the request body.
    transformerId: Required. The ID of the transformer resource to be created.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  transformer = _messages.MessageField('Transformer', 3)
  transformerId = _messages.StringField(4)


class MediaassetProjectsLocationsTransformersDeleteRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsTransformersDeleteRequest object.

  Fields:
    name: Required. The name of the transformer to delete, in the following
      form: `projects/{project}/locations/{location}/transformer/{type}`.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class MediaassetProjectsLocationsTransformersGetIamPolicyRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsTransformersGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class MediaassetProjectsLocationsTransformersGetRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsTransformersGetRequest object.

  Fields:
    name: Required. The name of the transformer to retrieve, in the following
      form:
      `projects/{project}/locations/{location}/transformers/{transformer}`.
  """

  name = _messages.StringField(1, required=True)


class MediaassetProjectsLocationsTransformersListRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsTransformersListRequest object.

  Fields:
    filter: The filter to apply to list results.
    orderBy: Specifies the ordering of results following syntax at
      https://cloud.google.com/apis/design/design_patterns#sorting_order.
    pageSize: The maximum number of items to return. If unspecified, server
      will pick an appropriate default. Server may return fewer items than
      requested. A caller should only rely on response's next_page_token to
      determine if there are more realms left to be queried
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: Required. The parent resource name, in the following form:
      `projects/{project}/locations/{location}`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class MediaassetProjectsLocationsTransformersPatchRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsTransformersPatchRequest object.

  Fields:
    name: The resource name of the transformer, in the following form:
      `projects/{project}/locations/{location}/transformer/{transformer}`.
      Here {transformer} is a resource id. Detailed rules for a resource id
      are: 1. 1 character minimum, 63 characters maximum 2. only contains
      letters, digits, underscore and hyphen 3. starts with a letter if length
      == 1, starts with a letter or underscore if length > 1
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    transformer: A Transformer resource to be passed as the request body.
    updateMask: Required. Comma-separated list of fields is used to specify
      the fields to be overwritten in the Transformer resource by the update.
      The fields specified in the update_mask are relative to the resource,
      not the full request. A field will be overwritten if it is in the mask.
      If the user does not provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  transformer = _messages.MessageField('Transformer', 3)
  updateMask = _messages.StringField(4)


class MediaassetProjectsLocationsTransformersSetIamPolicyRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsTransformersSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class MediaassetProjectsLocationsTransformersTestIamPermissionsRequest(_messages.Message):
  r"""A MediaassetProjectsLocationsTransformersTestIamPermissionsRequest
  object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class MetadataConfig(_messages.Message):
  r"""A MetadataConfig object.

  Fields:
    complexType: Required. Reference to the complex type name, in the
      following form:
      `projects/{project}/locations/{location}/complexTypes/{name}`.
    moduleDefinition: Reference to the module definition name, in the
      following form:
      `projects/{project}/locations/{location}/modules/{name}#{definition}`.
    owner: Output only. The owner of the metadata, set by the system.
    required: If true, this asset metadata is required to be specified during
      asset creation.
  """

  complexType = _messages.StringField(1)
  moduleDefinition = _messages.StringField(2)
  owner = _messages.StringField(3)
  required = _messages.BooleanField(4)


class MetadataInfo(_messages.Message):
  r"""A MetadataInfo object.

  Fields:
    owner: Output only. The owner of the metadata, if it's updated by the
      system.
    updateTime: Output only. Time at which this field was updated.
  """

  owner = _messages.StringField(1)
  updateTime = _messages.StringField(2)


class Module(_messages.Message):
  r"""A module specifies the data types used in assets.

  Messages:
    LabelsValue: The labels associated with this resource. Each label is a
      key-value pair.
    SchemaValue: The Open API schema that defines this module.

  Fields:
    createTime: Output only. The creation time of the module.
    labels: The labels associated with this resource. Each label is a key-
      value pair.
    name: The resource name of the module, in the following form:
      `projects/{project}/locations/{location}/module/{module}`.
    schema: The Open API schema that defines this module.
    updateTime: Output only. The last-modified time of the module.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels associated with this resource. Each label is a key-value
    pair.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SchemaValue(_messages.Message):
    r"""The Open API schema that defines this module.

    Messages:
      AdditionalProperty: An additional property for a SchemaValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SchemaValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  name = _messages.StringField(3)
  schema = _messages.MessageField('SchemaValue', 4)
  updateTime = _messages.StringField(5)


class NotificationConfig(_messages.Message):
  r"""Configures what, when and where notifications should be sent.

  Fields:
    criteria: Required. Specifies what and when notifications should be sent.
    destination: Required. Specifies where notifications should be sent to.
  """

  criteria = _messages.MessageField('NotificationCriteria', 1)
  destination = _messages.MessageField('NotificationDestination', 2)


class NotificationCriteria(_messages.Message):
  r"""Specifies when a notification should be sent.

  Fields:
    annotationChange: Specifies the annotations nested under the parent
      AssetType that should trigger notifications.
    assetChange: Specifies the asset changes that should trigger
      notifications.
    derivedAssetChange: Specifies the transformer invocations triggered by the
      derived asset rule that should publish Pub/Sub messages on event
      changes.
    transformationChange: Specifies the transformer invocations triggered by
      the transformation rule that should publish Pub/Sub messages on event
      changes.
  """

  annotationChange = _messages.MessageField('AnnotationChange', 1)
  assetChange = _messages.MessageField('AssetChange', 2)
  derivedAssetChange = _messages.MessageField('DerivedAssetChange', 3)
  transformationChange = _messages.MessageField('TransformationChange', 4)


class NotificationDestination(_messages.Message):
  r"""Specifies the destination to send the notifications to.

  Fields:
    pubsub: Specifies the pub/sub destination notifications should be sent to.
  """

  pubsub = _messages.MessageField('PubSubDestination', 1)


class NotificationStatus(_messages.Message):
  r"""Status of notification action

  Fields:
    lastUpdateTime: Output only. Time at which the last pub/sub action
      occurred.
    status: Output only. Status of the last pub/sub action.
  """

  lastUpdateTime = _messages.StringField(1)
  status = _messages.MessageField('Status', 2)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Output(_messages.Message):
  r"""Output describes an output of a transformer.

  Fields:
    type: Required. The type could be one of: - Primitive types ("string",
      "number", "bool") - Custom complex type. Format:
      "p/p/l/l/complexTypes/*" - Collections of the above - list(), dict()
  """

  type = _messages.StringField(1)


class Owner(_messages.Message):
  r"""The owner of the asset, set by the system.

  Fields:
    linkId: The link ID in the owner asset.
    ownerAsset: The name of the owner asset.
  """

  linkId = _messages.StringField(1)
  ownerAsset = _messages.StringField(2)


class PubSubDestination(_messages.Message):
  r"""Specifies the pub/sub destination to send the notifications to.

  Fields:
    topic: Required. A Pub/Sub topic to which messages are sent by GCMA.
      https://cloud.google.com/pubsub/docs/overview
  """

  topic = _messages.StringField(1)


class Rule(_messages.Message):
  r"""A rule resource is associated with an AssetType and manages the workflow
  pipelines of a collection of Assets under that AssetType. NEXT_ID: 10

  Messages:
    LabelsValue: The labels associated with this resource. Each label is a
      key-value pair.

  Fields:
    catalog: Configures how to catalog assets.
    createTime: Output only. The creation time of the rule.
    derivedAsset: Configures the associated AssetType to manage the derived
      assets for its Assets.
    labels: The labels associated with this resource. Each label is a key-
      value pair.
    name: A user-specified resource name of the rule
      `projects/{project}/locations/{location}/assetTypes/{type}/rules/{rule}`
      . Here {rule} is a resource id. Detailed rules for a resource id are: 1.
      1 character minimum, 63 characters maximum 2. only contains letters,
      digits, underscore and hyphen 3. starts with a letter if length == 1,
      starts with a letter or underscore if length > 1
    notification: Configures notifications for changes to resources nested
      under the parent AssetType (e.g., assets, actions, and annotations).
    transformation: Configures the associated AssetType to invoke
      transformers.
    updateTime: Output only. The latest update time of the rule.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels associated with this resource. Each label is a key-value
    pair.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  catalog = _messages.MessageField('CatalogConfig', 1)
  createTime = _messages.StringField(2)
  derivedAsset = _messages.MessageField('DerivedAssetConfig', 3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  notification = _messages.MessageField('NotificationConfig', 6)
  transformation = _messages.MessageField('TransformationConfig', 7)
  updateTime = _messages.StringField(8)


class RuleStats(_messages.Message):
  r"""RuleStats stores information on actions related to a certain rule.

  Fields:
    active: The number of actions that already have the same version as the
      current rule.
    updating: The number of actions with version < `version of the current
      rule`.
  """

  active = _messages.IntegerField(1)
  updating = _messages.IntegerField(2)


class SearchAssetTypeRequest(_messages.Message):
  r"""Request message for AssetTypesService.Search.

  Enums:
    TypeValueValuesEnum: By default, search at segment level.

  Fields:
    facetSelections: Facets to be selected.
    pageSize: The maximum number of items to return. If unspecified, server
      will pick an appropriate default. Server may return fewer items than
      requested. A caller should only rely on response's next_page_token to
      determine if there are more realms left to be queried.
    pageToken: The next_page_token value returned from a previous Search
      request, if any.
    query: Search query.
    type: By default, search at segment level.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""By default, search at segment level.

    Values:
      SEARCH_REQUEST_TYPE_UNSPECIFIED: Unspecified type.
      SEARCH_REQUEST_TYPE_ASSET: Video-level search. That is, search over
        videos and video-level metadata.
      SEARCH_REQUEST_TYPE_SEGMENT: Segment-level search. That is, search over
        segments within videos and annotations.
    """
    SEARCH_REQUEST_TYPE_UNSPECIFIED = 0
    SEARCH_REQUEST_TYPE_ASSET = 1
    SEARCH_REQUEST_TYPE_SEGMENT = 2

  facetSelections = _messages.MessageField('Facet', 1, repeated=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  query = _messages.StringField(4)
  type = _messages.EnumField('TypeValueValuesEnum', 5)


class SearchAssetTypeResponse(_messages.Message):
  r"""Response message for AssetTypesService.Search.

  Fields:
    facets: Returned facets from the search results, showing the aggregated
      buckets.
    items: Returned search results.
    nextPageToken: The next-page continuation token.
  """

  facets = _messages.MessageField('Facet', 1, repeated=True)
  items = _messages.MessageField('SearchResultItem', 2, repeated=True)
  nextPageToken = _messages.StringField(3)


class SearchResultItem(_messages.Message):
  r"""Encapsulates each piece of data that matches the search query.

  Fields:
    asset: The resource name of the asset in the form:
      'projects/{project}/locations/{location}/assetTypes/{type}/assets/{asset
      }'
    segments: Matching segments within the above asset.
  """

  asset = _messages.StringField(1)
  segments = _messages.MessageField('Segment', 2, repeated=True)


class Segment(_messages.Message):
  r"""Represents a segment of a video asset that matches a search query.

  Fields:
    endOffset: Segment end offet timestamp.
    startOffset: Segment start offset timestamp.
  """

  endOffset = _messages.StringField(1)
  startOffset = _messages.StringField(2)


class SortOrderConfig(_messages.Message):
  r"""A SortOrderConfig object.

  Fields:
    descending: Sort in descending order.
    field: Required. Field to sort by. Must be: - required - numeric, datetime
      or duration field - within the asset itself and not in any linked
      assets.
  """

  descending = _messages.BooleanField(1)
  field = _messages.StringField(2)


class SpeechTranscriptionConfig(_messages.Message):
  r"""Configure transcription options for speech: keyword.

  Fields:
    languageCode: Language code to use for configuring `speech:` search
      keyword. If unset, the default language will be English (en-US). This
      language code will be validated under [BCP-47](https://www.rfc-
      editor.org/rfc/bcp/bcp47.txt). Example: "en-US". See [Language
      Support](https://cloud.google.com/speech/docs/languages) for a list of
      the currently supported language codes.
  """

  languageCode = _messages.StringField(1)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TransformationChange(_messages.Message):
  r"""Specifies the transformation changes that should trigger notifications.

  Fields:
    ruleId: Required. Notifies for changes to any transformer invocations
      triggered by the transformation rule.
  """

  ruleId = _messages.StringField(1)


class TransformationConfig(_messages.Message):
  r"""A TransformationConfig configures the associated AssetType to invoke
  transformers on its Assets.

  Messages:
    InputsValue: A InputsValue object.
    OutputsValue: Required. Key-value pairs representing output parameters
      from the transformers. The key maps to the transformer output parameter
      name. The value will be the path to the metadata in the asset to which
      this output should be assigned.

  Fields:
    inputs: A InputsValue attribute.
    outputs: Required. Key-value pairs representing output parameters from the
      transformers. The key maps to the transformer output parameter name. The
      value will be the path to the metadata in the asset to which this output
      should be assigned.
    transformer: Required. Reference to a transformer to execute, in the
      following form:
      `projects/{project}/locations/{location}/transformers/{name}`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class InputsValue(_messages.Message):
    r"""A InputsValue object.

    Messages:
      AdditionalProperty: An additional property for a InputsValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a InputsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class OutputsValue(_messages.Message):
    r"""Required. Key-value pairs representing output parameters from the
    transformers. The key maps to the transformer output parameter name. The
    value will be the path to the metadata in the asset to which this output
    should be assigned.

    Messages:
      AdditionalProperty: An additional property for a OutputsValue object.

    Fields:
      additionalProperties: Additional properties of type OutputsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a OutputsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  inputs = _messages.MessageField('InputsValue', 1)
  outputs = _messages.MessageField('OutputsValue', 2)
  transformer = _messages.StringField(3)


class TransformationStatus(_messages.Message):
  r"""Status of Asset transformation.

  Messages:
    ProgressReportValue: Output only. A struct that was provided by the
      Transformer as progress report.

  Fields:
    invocationId: A UUID of the asset transformation run.
    lastInvocationStatus: Status of the last invocation of the asset
      transformation.
    lastInvocationTime: Output only. Time at which the last invocation of the
      asset transformation occurred.
    progressReport: Output only. A struct that was provided by the Transformer
      as progress report.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ProgressReportValue(_messages.Message):
    r"""Output only. A struct that was provided by the Transformer as progress
    report.

    Messages:
      AdditionalProperty: An additional property for a ProgressReportValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ProgressReportValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  invocationId = _messages.StringField(1)
  lastInvocationStatus = _messages.MessageField('Status', 2)
  lastInvocationTime = _messages.StringField(3)
  progressReport = _messages.MessageField('ProgressReportValue', 4)


class Transformer(_messages.Message):
  r"""A transformer resource. Transformers are functions that process a set of
  input parameters and generate outputs. They provide the mechanism to enrich
  asset information by processing asset contents.

  Messages:
    InputValue: Mapping of the input parameter name to its input definition.
      Will be replacing inputs in future.
    LabelsValue: The labels associated with this resource. Each label is a
      key-value pair.
    OutputValue: Mapping of the output parameter name to its output
      definition. Will be replacing outputs in future.

  Fields:
    createTime: Output only. The creation time.
    endpoint: Required. URI endpoint that the Transformer is listening for its
      invocation through the documented Transformer protocol.
    input: Mapping of the input parameter name to its input definition. Will
      be replacing inputs in future.
    labels: The labels associated with this resource. Each label is a key-
      value pair.
    name: The resource name of the transformer, in the following form:
      `projects/{project}/locations/{location}/transformer/{transformer}`.
      Here {transformer} is a resource id. Detailed rules for a resource id
      are: 1. 1 character minimum, 63 characters maximum 2. only contains
      letters, digits, underscore and hyphen 3. starts with a letter if length
      == 1, starts with a letter or underscore if length > 1
    output: Mapping of the output parameter name to its output definition.
      Will be replacing outputs in future.
    updateTime: Output only. The last-modified time.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class InputValue(_messages.Message):
    r"""Mapping of the input parameter name to its input definition. Will be
    replacing inputs in future.

    Messages:
      AdditionalProperty: An additional property for a InputValue object.

    Fields:
      additionalProperties: Additional properties of type InputValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a InputValue object.

      Fields:
        key: Name of the additional property.
        value: A Input attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Input', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels associated with this resource. Each label is a key-value
    pair.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class OutputValue(_messages.Message):
    r"""Mapping of the output parameter name to its output definition. Will be
    replacing outputs in future.

    Messages:
      AdditionalProperty: An additional property for a OutputValue object.

    Fields:
      additionalProperties: Additional properties of type OutputValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a OutputValue object.

      Fields:
        key: Name of the additional property.
        value: A Output attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Output', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  endpoint = _messages.StringField(2)
  input = _messages.MessageField('InputValue', 3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  output = _messages.MessageField('OutputValue', 6)
  updateTime = _messages.StringField(7)


class TriggerActionRequest(_messages.Message):
  r"""Request message for ActionsService.TriggerAction."""


class TriggerActionResponse(_messages.Message):
  r"""Response message for ActionsService.TriggerAction."""


encoding.AddCustomJsonFieldMapping(
    GoogleIamV1Rule, 'in_', 'in')
encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
encoding.AddCustomJsonFieldMapping(
    MediaassetProjectsLocationsAssetTypesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    MediaassetProjectsLocationsAssetTypesAssetsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    MediaassetProjectsLocationsAssetTypesAssetsAnnotationSetsAnnotationsGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    MediaassetProjectsLocationsComplexTypesGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
encoding.AddCustomJsonFieldMapping(
    MediaassetProjectsLocationsTransformersGetIamPolicyRequest, 'options_requestedPolicyVersion', 'options.requestedPolicyVersion')
