"""Generated client library for dataprocgdc version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.dataprocgdc.v1 import dataprocgdc_v1_messages as messages


class DataprocgdcV1(base_api.BaseApiClient):
  """Generated client library for service dataprocgdc version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://dataprocgdc.googleapis.com/'
  MTLS_BASE_URL = 'https://dataprocgdc.mtls.googleapis.com/'

  _PACKAGE = 'dataprocgdc'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'DataprocgdcV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new dataprocgdc handle."""
    url = url or self.BASE_URL
    super(DataprocgdcV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_serviceInstances_applicationEnvironments = self.ProjectsLocationsServiceInstancesApplicationEnvironmentsService(self)
    self.projects_locations_serviceInstances_sparkApplications = self.ProjectsLocationsServiceInstancesSparkApplicationsService(self)
    self.projects_locations_serviceInstances = self.ProjectsLocationsServiceInstancesService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(DataprocgdcV1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (DataprocgdcProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='dataprocgdc.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='DataprocgdcProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (DataprocgdcProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='dataprocgdc.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocgdcProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (DataprocgdcProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='dataprocgdc.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocgdcProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (DataprocgdcProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='dataprocgdc.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='DataprocgdcProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsServiceInstancesApplicationEnvironmentsService(base_api.BaseApiService):
    """Service class for the projects_locations_serviceInstances_applicationEnvironments resource."""

    _NAME = 'projects_locations_serviceInstances_applicationEnvironments'

    def __init__(self, client):
      super(DataprocgdcV1.ProjectsLocationsServiceInstancesApplicationEnvironmentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an application environment associated with a Dataproc ServiceInstance.

      Args:
        request: (DataprocgdcProjectsLocationsServiceInstancesApplicationEnvironmentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ApplicationEnvironment) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceInstances/{serviceInstancesId}/applicationEnvironments',
        http_method='POST',
        method_id='dataprocgdc.projects.locations.serviceInstances.applicationEnvironments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['applicationEnvironmentId', 'requestId'],
        relative_path='v1/{+parent}/applicationEnvironments',
        request_field='applicationEnvironment',
        request_type_name='DataprocgdcProjectsLocationsServiceInstancesApplicationEnvironmentsCreateRequest',
        response_type_name='ApplicationEnvironment',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an application environment.

      Args:
        request: (DataprocgdcProjectsLocationsServiceInstancesApplicationEnvironmentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceInstances/{serviceInstancesId}/applicationEnvironments/{applicationEnvironmentsId}',
        http_method='DELETE',
        method_id='dataprocgdc.projects.locations.serviceInstances.applicationEnvironments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocgdcProjectsLocationsServiceInstancesApplicationEnvironmentsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an application environment.

      Args:
        request: (DataprocgdcProjectsLocationsServiceInstancesApplicationEnvironmentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ApplicationEnvironment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceInstances/{serviceInstancesId}/applicationEnvironments/{applicationEnvironmentsId}',
        http_method='GET',
        method_id='dataprocgdc.projects.locations.serviceInstances.applicationEnvironments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocgdcProjectsLocationsServiceInstancesApplicationEnvironmentsGetRequest',
        response_type_name='ApplicationEnvironment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists application environments in a location.

      Args:
        request: (DataprocgdcProjectsLocationsServiceInstancesApplicationEnvironmentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListApplicationEnvironmentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceInstances/{serviceInstancesId}/applicationEnvironments',
        http_method='GET',
        method_id='dataprocgdc.projects.locations.serviceInstances.applicationEnvironments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/applicationEnvironments',
        request_field='',
        request_type_name='DataprocgdcProjectsLocationsServiceInstancesApplicationEnvironmentsListRequest',
        response_type_name='ListApplicationEnvironmentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an application environment. Only supports updating state or labels.

      Args:
        request: (DataprocgdcProjectsLocationsServiceInstancesApplicationEnvironmentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ApplicationEnvironment) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceInstances/{serviceInstancesId}/applicationEnvironments/{applicationEnvironmentsId}',
        http_method='PATCH',
        method_id='dataprocgdc.projects.locations.serviceInstances.applicationEnvironments.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='applicationEnvironment',
        request_type_name='DataprocgdcProjectsLocationsServiceInstancesApplicationEnvironmentsPatchRequest',
        response_type_name='ApplicationEnvironment',
        supports_download=False,
    )

  class ProjectsLocationsServiceInstancesSparkApplicationsService(base_api.BaseApiService):
    """Service class for the projects_locations_serviceInstances_sparkApplications resource."""

    _NAME = 'projects_locations_serviceInstances_sparkApplications'

    def __init__(self, client):
      super(DataprocgdcV1.ProjectsLocationsServiceInstancesSparkApplicationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an application associated with a Dataproc operator.

      Args:
        request: (DataprocgdcProjectsLocationsServiceInstancesSparkApplicationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceInstances/{serviceInstancesId}/sparkApplications',
        http_method='POST',
        method_id='dataprocgdc.projects.locations.serviceInstances.sparkApplications.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'sparkApplicationId'],
        relative_path='v1/{+parent}/sparkApplications',
        request_field='sparkApplication',
        request_type_name='DataprocgdcProjectsLocationsServiceInstancesSparkApplicationsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a application.

      Args:
        request: (DataprocgdcProjectsLocationsServiceInstancesSparkApplicationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceInstances/{serviceInstancesId}/sparkApplications/{sparkApplicationsId}',
        http_method='DELETE',
        method_id='dataprocgdc.projects.locations.serviceInstances.sparkApplications.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocgdcProjectsLocationsServiceInstancesSparkApplicationsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a application.

      Args:
        request: (DataprocgdcProjectsLocationsServiceInstancesSparkApplicationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SparkApplication) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceInstances/{serviceInstancesId}/sparkApplications/{sparkApplicationsId}',
        http_method='GET',
        method_id='dataprocgdc.projects.locations.serviceInstances.sparkApplications.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocgdcProjectsLocationsServiceInstancesSparkApplicationsGetRequest',
        response_type_name='SparkApplication',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists applications in a location.

      Args:
        request: (DataprocgdcProjectsLocationsServiceInstancesSparkApplicationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSparkApplicationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceInstances/{serviceInstancesId}/sparkApplications',
        http_method='GET',
        method_id='dataprocgdc.projects.locations.serviceInstances.sparkApplications.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/sparkApplications',
        request_field='',
        request_type_name='DataprocgdcProjectsLocationsServiceInstancesSparkApplicationsListRequest',
        response_type_name='ListSparkApplicationsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a application. Only supports updating state or labels.

      Args:
        request: (DataprocgdcProjectsLocationsServiceInstancesSparkApplicationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceInstances/{serviceInstancesId}/sparkApplications/{sparkApplicationsId}',
        http_method='PATCH',
        method_id='dataprocgdc.projects.locations.serviceInstances.sparkApplications.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'requestId', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='sparkApplication',
        request_type_name='DataprocgdcProjectsLocationsServiceInstancesSparkApplicationsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsServiceInstancesService(base_api.BaseApiService):
    """Service class for the projects_locations_serviceInstances resource."""

    _NAME = 'projects_locations_serviceInstances'

    def __init__(self, client):
      super(DataprocgdcV1.ProjectsLocationsServiceInstancesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a service instance in a GDC cluster.

      Args:
        request: (DataprocgdcProjectsLocationsServiceInstancesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceInstances',
        http_method='POST',
        method_id='dataprocgdc.projects.locations.serviceInstances.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'serviceInstanceId'],
        relative_path='v1/{+parent}/serviceInstances',
        request_field='serviceInstance',
        request_type_name='DataprocgdcProjectsLocationsServiceInstancesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a service instance. Deleting will remove the service instance from the cluster, and deletes all Dataproc API objects from that cluster.

      Args:
        request: (DataprocgdcProjectsLocationsServiceInstancesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceInstances/{serviceInstancesId}',
        http_method='DELETE',
        method_id='dataprocgdc.projects.locations.serviceInstances.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'force', 'requestId'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocgdcProjectsLocationsServiceInstancesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a service instance.

      Args:
        request: (DataprocgdcProjectsLocationsServiceInstancesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServiceInstance) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceInstances/{serviceInstancesId}',
        http_method='GET',
        method_id='dataprocgdc.projects.locations.serviceInstances.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocgdcProjectsLocationsServiceInstancesGetRequest',
        response_type_name='ServiceInstance',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists serviceInstances in a location.

      Args:
        request: (DataprocgdcProjectsLocationsServiceInstancesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListServiceInstancesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/serviceInstances',
        http_method='GET',
        method_id='dataprocgdc.projects.locations.serviceInstances.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/serviceInstances',
        request_field='',
        request_type_name='DataprocgdcProjectsLocationsServiceInstancesListRequest',
        response_type_name='ListServiceInstancesResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(DataprocgdcV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (DataprocgdcProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='dataprocgdc.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='DataprocgdcProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (DataprocgdcProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='dataprocgdc.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/locations',
        request_field='',
        request_type_name='DataprocgdcProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(DataprocgdcV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }
